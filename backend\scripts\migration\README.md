# Database Migration Scripts

This directory contains scripts for migrating data, updating database schemas,
and performing system-wide changes.

## Scripts Overview

### 📊 User Role Migration

- **`migrate-user-roles.js`** - Migrates user roles from raw_user_meta_data to
  user_profiles table
  - Maps users to employee records based on email matching
  - <PERSON>les role validation and normalization
  - Generates detailed migration reports and rollback scripts
  - Part of the Hybrid RBAC implementation (Phase 2)

### 🧹 System Cleanup

- **`phase4-system-cleanup.js`** - Cleans up deprecated data and configurations
  - Removes old role dependencies from raw_user_meta_data
  - Validates migration completeness
  - Final phase of RBAC implementation

## Migration Process

### Phase 2: User Role Migration

The `migrate-user-roles.js` script performs comprehensive data migration:

1. **Data Extraction**: Retrieves user roles from Supabase auth metadata
2. **Role Mapping**: Maps raw roles to standardized UserRole enum values
3. **Employee Matching**: Links users to employee records via email matching
4. **Profile Creation**: Creates user_profiles records with proper relationships
5. **Reporting**: Generates detailed logs and rollback scripts

### Phase 4: System Cleanup

The cleanup script ensures:

- Removal of deprecated role data
- Validation of migration completeness
- System optimization and cleanup

## Usage Examples

### Run User Role Migration

```bash
# Ensure backend is built first
npm run build

# Run migration with full logging
node scripts/migration/migrate-user-roles.js
```

### Run System Cleanup

```bash
node scripts/migration/phase4-system-cleanup.js
```

## Safety Features

### Backup & Rollback

- **Automatic Rollback Scripts**: Generated during migration
- **Detailed Logging**: Comprehensive migration reports
- **Validation Checks**: Pre-migration validation
- **Error Handling**: Graceful error recovery

### Migration Reports

Generated reports include:

- Total users processed
- Successful migrations
- Skipped users (already migrated)
- Error details and affected users
- User-to-employee mappings
- Rollback SQL scripts

## Important Notes

⚠️ **Critical Warning**: These scripts modify database structure and data.

### Prerequisites

- **Database Backup**: Always backup before running migrations
- **Environment Check**: Verify you're targeting correct database
- **Service Role Key**: Supabase service role key required
- **Built Backend**: Run `npm run build` first

### Migration Validation

Before running migrations:

1. Verify environment variables
2. Check database connectivity
3. Ensure user_profiles table exists
4. Validate Employee table structure

### Post-Migration Steps

After successful migration:

1. Verify user profiles created correctly
2. Test authentication flows
3. Validate JWT claims injection
4. Run RBAC verification scripts
5. Archive migration logs

## Rollback Procedures

### Automatic Rollback

Each migration generates rollback scripts:

```sql
-- Example rollback script
DELETE FROM user_profiles WHERE id = 'user-uuid-here';
```

### Manual Rollback

If needed, manually reverse changes:

1. Use generated rollback scripts
2. Restore from database backup
3. Re-run verification scripts

## Troubleshooting

### Common Issues

- **Duplicate Users**: Check for existing user_profiles
- **Role Mapping Errors**: Verify role enum values
- **Employee Matching**: Check email format consistency
- **Permission Errors**: Verify service role key

### Debug Process

1. Check migration logs for specific errors
2. Verify database schema matches expectations
3. Test with single user first
4. Use auth verification scripts post-migration

## Migration History

Track completed migrations to prevent re-execution:

- Phase 2: User role migration (raw_user_meta_data → user_profiles)
- Phase 4: System cleanup and optimization

## Support

For migration issues:

1. Check generated log files in `/logs` directory
2. Review rollback scripts for recovery options
3. Use auth verification scripts to validate state
4. Contact development team for complex issues
