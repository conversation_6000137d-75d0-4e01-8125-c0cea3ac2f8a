// frontend/src/components/features/reporting/dashboard/widgets/SingleTaskWidget.tsx

import React from 'react';
import { BaseWidget } from './BaseWidget';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
// Import real task hook from existing API integration
import { useTask } from '@/lib/stores/queries/useTasks';
import {
  Calendar,
  User,
  AlertTriangle,
  Clock,
  CheckCircle,
  FileText,
  Download,
  ExternalLink,
  Flag,
} from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';

interface SingleTaskWidgetProps {
  taskId: string;
  title?: string;
  showActions?: boolean;
  onExport?: () => void;
  onViewDetails?: () => void;
}

/**
 * @component SingleTaskWidget
 * @description Widget for displaying detailed information about a single task
 *
 * Responsibilities:
 * - Shows comprehensive task details
 * - Displays progress and status information
 * - Provides quick actions for the task
 * - Handles loading and error states
 *
 * SOLID Principles Applied:
 * - SRP: Single responsibility of presenting single task data
 * - OCP: Open for extension via props
 * - DIP: Depends on task hook abstractions
 */
export const SingleTaskWidget: React.FC<SingleTaskWidgetProps> = ({
  taskId,
  title = 'Task Details',
  showActions = true,
  onExport,
  onViewDetails,
}) => {
  const { data: task, isLoading, error, refetch } = useTask(taskId);

  // Calculate task progress based on status
  const getProgress = () => {
    if (!task) return 0;

    const statusProgress = {
      PENDING: 0,
      IN_PROGRESS: 50,
      COMPLETED: 100,
      CANCELLED: 0,
      ON_HOLD: 25,
    };

    return statusProgress[task.status as keyof typeof statusProgress] || 0;
  };

  // Get status color
  const getStatusColor = (status: string) => {
    const colors = {
      PENDING: 'bg-gray-100 text-gray-800',
      IN_PROGRESS: 'bg-blue-100 text-blue-800',
      COMPLETED: 'bg-green-100 text-green-800',
      CANCELLED: 'bg-red-100 text-red-800',
      ON_HOLD: 'bg-yellow-100 text-yellow-800',
    };
    return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  // Get priority color
  const getPriorityColor = (priority: string) => {
    const colors = {
      LOW: 'bg-green-100 text-green-800',
      MEDIUM: 'bg-yellow-100 text-yellow-800',
      HIGH: 'bg-orange-100 text-orange-800',
      URGENT: 'bg-red-100 text-red-800',
    };
    return (
      colors[priority as keyof typeof colors] || 'bg-gray-100 text-gray-800'
    );
  };

  // Check if task is overdue
  const isOverdue = () => {
    if (!task?.deadline) return false; // Use deadline instead of dueDate
    return new Date(task.deadline) < new Date() && task.status !== 'Completed';
  };

  // Handle actions
  const handleExport = () => {
    if (onExport) {
      onExport();
    } else {
      console.log('Exporting task:', taskId);
    }
  };

  const handleViewDetails = () => {
    if (onViewDetails) {
      onViewDetails();
    } else {
      window.open(`/tasks/${taskId}`, '_blank');
    }
  };

  // Render actions
  const renderActions = () => {
    if (!showActions) return null;

    return (
      <div className="flex items-center gap-2">
        <Button variant="outline" size="sm" onClick={handleExport}>
          <Download className="h-4 w-4 mr-2" />
          Export
        </Button>
        <Button variant="outline" size="sm" onClick={handleViewDetails}>
          <ExternalLink className="h-4 w-4 mr-2" />
          View
        </Button>
      </div>
    );
  };

  // Render task info cards
  const renderInfoCards = () => {
    if (!task) return null;

    const cards = [
      {
        icon: Calendar,
        label: 'Due Date',
        value: task.deadline
          ? format(new Date(task.deadline), 'MMM d, yyyy')
          : 'Not set',
        isOverdue: isOverdue(),
      },
      {
        icon: User,
        label: 'Assignee',
        value:
          task.staffEmployee?.name || task.driverEmployee?.name || 'Unassigned',
      },
      {
        icon: Flag,
        label: 'Priority',
        value: task.priority || 'Not set',
        badge: true,
        badgeColor: getPriorityColor(task.priority || ''),
      },
      {
        icon: FileText,
        label: 'Location',
        value: task.location || 'No location',
      },
    ];

    return (
      <div className="grid grid-cols-1 gap-3">
        {cards.map((card, index) => {
          const Icon = card.icon;
          return (
            <div
              key={index}
              className="flex items-center gap-3 p-2 bg-muted/50 rounded"
            >
              <Icon
                className={cn(
                  'h-4 w-4',
                  card.isOverdue ? 'text-red-500' : 'text-muted-foreground'
                )}
              />
              <div className="flex-1">
                <p className="text-xs text-muted-foreground">{card.label}</p>
                {card.badge ? (
                  <Badge className={cn('text-xs', card.badgeColor)}>
                    {card.value}
                  </Badge>
                ) : (
                  <p
                    className={cn(
                      'text-sm font-medium',
                      card.isOverdue ? 'text-red-600' : ''
                    )}
                  >
                    {card.value}
                    {card.isOverdue && (
                      <span className="ml-1 text-red-500">(Overdue)</span>
                    )}
                  </p>
                )}
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  return (
    <BaseWidget
      title={title}
      loading={isLoading}
      error={error?.message}
      actions={renderActions()}
    >
      {task && (
        <div className="space-y-4">
          {/* Task Header */}
          <div className="space-y-2">
            <div className="flex items-start justify-between gap-2">
              <h3 className="font-semibold text-lg leading-tight">
                {task.description}
              </h3>
              <div className="flex flex-col gap-1">
                <Badge className={cn('text-xs', getStatusColor(task.status))}>
                  {task.status.replace('_', ' ')}
                </Badge>
                {isOverdue() && (
                  <Badge variant="destructive" className="text-xs">
                    <AlertTriangle className="h-3 w-3 mr-1" />
                    Overdue
                  </Badge>
                )}
              </div>
            </div>

            {task.notes && (
              <p className="text-sm text-muted-foreground line-clamp-3">
                {task.notes}
              </p>
            )}
          </div>

          {/* Progress */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Progress</span>
              <span className="text-sm text-muted-foreground">
                {getProgress()}%
              </span>
            </div>
            <Progress value={getProgress()} className="h-2" />
          </div>

          <Separator />

          {/* Info Cards */}
          {renderInfoCards()}

          {/* Time Tracking */}
          {task.estimatedDuration && (
            <>
              <Separator />
              <div className="space-y-2">
                <h4 className="font-medium flex items-center gap-2">
                  <Clock className="h-4 w-4" />
                  Time Tracking
                </h4>
                <div className="grid grid-cols-1 gap-2">
                  <div className="p-2 bg-blue-50 rounded text-center">
                    <p className="text-xs text-blue-600">Estimated Duration</p>
                    <p className="font-semibold text-blue-700">
                      {task.estimatedDuration || 0} minutes
                    </p>
                  </div>
                </div>
              </div>
            </>
          )}
        </div>
      )}
    </BaseWidget>
  );
};

export default SingleTaskWidget;
