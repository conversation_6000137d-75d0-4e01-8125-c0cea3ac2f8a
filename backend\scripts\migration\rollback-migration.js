#!/usr/bin/env node

/**
 * Rollback Script: Restore user_profiles Table
 *
 * This script rolls back the migration to auth.users-only architecture
 * by restoring the user_profiles table and reverting related changes.
 *
 * Usage: node rollback-migration.js [backup-file-path]
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables:');
  console.error('   SUPABASE_URL:', supabaseUrl ? '✅ Set' : '❌ Missing');
  console.error('   SUPABASE_SERVICE_ROLE_KEY:', supabaseServiceKey ? '✅ Set' : '❌ Missing');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function rollbackMigration(backupFilePath) {
  console.log('🔄 Starting rollback to user_profiles architecture...');

  try {
    // Step 1: Load backup data
    console.log('📋 Step 1: Loading backup data...');
    const backupData = await loadBackupData(backupFilePath);

    if (!backupData) {
      console.error('❌ Failed to load backup data');
      return false;
    }

    console.log(`✅ Loaded backup with ${backupData.user_profiles.length} profiles`);

    // Step 2: Recreate user_profiles table
    console.log('📋 Step 2: Recreating user_profiles table...');
    const tableCreated = await recreateUserProfilesTable();

    if (!tableCreated) {
      console.error('❌ Failed to recreate user_profiles table');
      return false;
    }

    console.log('✅ user_profiles table recreated');

    // Step 3: Restore user_profiles data
    console.log('📋 Step 3: Restoring user_profiles data...');
    const dataRestored = await restoreUserProfilesData(backupData.user_profiles);

    if (!dataRestored) {
      console.error('❌ Failed to restore user_profiles data');
      return false;
    }

    console.log('✅ user_profiles data restored');

    // Step 4: Restore original auth hook
    console.log('📋 Step 4: Restoring original auth hook...');
    const hookRestored = await restoreOriginalAuthHook();

    if (!hookRestored) {
      console.error('❌ Failed to restore auth hook');
      return false;
    }

    console.log('✅ Original auth hook restored');

    // Step 5: Clean user_metadata from auth.users (optional)
    console.log('📋 Step 5: Cleaning user_metadata from auth.users...');
    const metadataCleaned = await cleanUserMetadata(backupData.user_profiles);

    if (!metadataCleaned) {
      console.warn('⚠️  Failed to clean user_metadata (non-critical)');
    } else {
      console.log('✅ user_metadata cleaned');
    }

    // Step 6: Verify rollback
    console.log('📋 Step 6: Verifying rollback...');
    const verificationResult = await verifyRollback(backupData.user_profiles);

    if (!verificationResult.success) {
      console.error('❌ Rollback verification failed:', verificationResult.errors);
      return false;
    }

    console.log('✅ Rollback verification passed');

    return {
      success: true,
      restored: verificationResult.restored,
    };
  } catch (error) {
    console.error('❌ Rollback failed with error:', error);
    return false;
  }
}

async function loadBackupData(backupFilePath) {
  try {
    // If no path provided, find the latest backup
    if (!backupFilePath) {
      backupFilePath = findLatestBackup();
    }

    if (!backupFilePath || !fs.existsSync(backupFilePath)) {
      console.error('❌ Backup file not found:', backupFilePath);
      return null;
    }

    const backupContent = fs.readFileSync(backupFilePath, 'utf8');
    const backupData = JSON.parse(backupContent);

    // Validate backup structure
    if (!backupData.user_profiles || !Array.isArray(backupData.user_profiles)) {
      console.error('❌ Invalid backup file structure');
      return null;
    }

    console.log(`📊 Backup info:`);
    console.log(`   - Created: ${backupData.timestamp}`);
    console.log(`   - Version: ${backupData.version}`);
    console.log(`   - Profiles: ${backupData.user_profiles.length}`);

    return backupData;
  } catch (error) {
    console.error('❌ Error loading backup:', error);
    return null;
  }
}

function findLatestBackup() {
  const backupDir = path.join(process.cwd(), 'backups');

  if (!fs.existsSync(backupDir)) {
    return null;
  }

  const backupFiles = fs
    .readdirSync(backupDir)
    .filter(file => file.startsWith('user_profiles_backup_') && file.endsWith('.json'))
    .sort()
    .reverse();

  return backupFiles.length > 0 ? path.join(backupDir, backupFiles[0]) : null;
}

async function recreateUserProfilesTable() {
  try {
    // Drop existing table if it exists
    const dropResult = await supabase.rpc('exec_sql', {
      sql: 'DROP TABLE IF EXISTS public.user_profiles CASCADE;',
    });

    // Create user_profiles table
    const createTableSQL = `
      CREATE TABLE public.user_profiles (
          id UUID PRIMARY KEY,
          role TEXT DEFAULT 'USER' CHECK (role IN ('SUPER_ADMIN', 'ADMIN', 'MANAGER', 'USER', 'READONLY')),
          employee_id INTEGER UNIQUE,
          is_active BOOLEAN DEFAULT true,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
      
      -- Add foreign key constraint
      ALTER TABLE public.user_profiles 
      ADD CONSTRAINT user_profiles_id_fkey 
      FOREIGN KEY (id) REFERENCES auth.users(id) ON DELETE CASCADE;
      
      -- Enable RLS
      ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;
      
      -- Create policies
      CREATE POLICY "user_profiles_own_access" ON public.user_profiles
          FOR ALL USING (auth.uid() = id);
      
      CREATE POLICY "user_profiles_admin_view" ON public.user_profiles
          FOR SELECT USING (
              EXISTS (
                  SELECT 1 FROM public.user_profiles up 
                  WHERE up.id = auth.uid() 
                  AND up.role IN ('ADMIN', 'SUPER_ADMIN')
              )
          );
    `;

    const createResult = await supabase.rpc('exec_sql', { sql: createTableSQL });

    return !createResult.error;
  } catch (error) {
    console.error('Error recreating table:', error);
    return false;
  }
}

async function restoreUserProfilesData(userProfiles) {
  try {
    console.log(`🔄 Restoring ${userProfiles.length} user profiles...`);

    // Insert data in batches
    const batchSize = 50;
    let restored = 0;

    for (let i = 0; i < userProfiles.length; i += batchSize) {
      const batch = userProfiles.slice(i, i + batchSize);

      const { error } = await supabase.from('user_profiles').insert(batch);

      if (error) {
        console.error(`❌ Error inserting batch ${i / batchSize + 1}:`, error);
        return false;
      }

      restored += batch.length;
      console.log(`   ✅ Restored ${restored}/${userProfiles.length} profiles`);
    }

    return true;
  } catch (error) {
    console.error('Error restoring data:', error);
    return false;
  }
}

async function restoreOriginalAuthHook() {
  try {
    const originalAuthHookSQL = `
      CREATE OR REPLACE FUNCTION public.custom_access_token_hook(event jsonb)
      RETURNS jsonb AS $$
      DECLARE
          claims jsonb;
          user_role text;
          is_active boolean;
          employee_id integer;
      BEGIN
          claims := event->'claims';
          
          -- Fetch user role, status, and employee_id from user_profiles
          SELECT role, is_active, employee_id 
          INTO user_role, is_active, employee_id
          FROM public.user_profiles 
          WHERE id = (event->>'user_id')::uuid;
          
          -- Set custom claims
          IF user_role IS NOT NULL THEN
              claims := jsonb_set(claims, '{custom_claims}', jsonb_build_object(
                  'user_role', user_role,
                  'is_active', COALESCE(is_active, true),
                  'employee_id', employee_id
              ));
          ELSE
              -- Default claims for users without a profile
              claims := jsonb_set(claims, '{custom_claims}', jsonb_build_object(
                  'user_role', 'USER',
                  'is_active', true,
                  'employee_id', null
              ));
          END IF;
          
          RETURN jsonb_set(event, '{claims}', claims);
      END;
      $$ LANGUAGE plpgsql STABLE SECURITY DEFINER;
    `;

    const result = await supabase.rpc('exec_sql', { sql: originalAuthHookSQL });

    return !result.error;
  } catch (error) {
    console.error('Error restoring auth hook:', error);
    return false;
  }
}

async function cleanUserMetadata(userProfiles) {
  try {
    console.log('🧹 Cleaning user_metadata from auth.users...');

    const userIds = userProfiles.map(profile => profile.id);
    let cleaned = 0;

    for (const userId of userIds) {
      const { error } = await supabase.auth.admin.updateUserById(userId, {
        user_metadata: {},
      });

      if (!error) {
        cleaned++;
      }
    }

    console.log(`   ✅ Cleaned metadata for ${cleaned}/${userIds.length} users`);
    return true;
  } catch (error) {
    console.error('Error cleaning metadata:', error);
    return false;
  }
}

async function verifyRollback(originalProfiles) {
  try {
    const { data: restoredProfiles, error } = await supabase.from('user_profiles').select('*');

    if (error) {
      return {
        success: false,
        errors: [`Failed to fetch restored profiles: ${error.message}`],
      };
    }

    const errors = [];
    const restored = [];

    // Check count
    if (restoredProfiles.length !== originalProfiles.length) {
      errors.push(
        `Profile count mismatch: restored ${restoredProfiles.length}, expected ${originalProfiles.length}`,
      );
    }

    // Check each profile
    originalProfiles.forEach(original => {
      const restored_profile = restoredProfiles.find(p => p.id === original.id);

      if (!restored_profile) {
        errors.push(`Missing restored profile: ${original.id}`);
        return;
      }

      if (restored_profile.role !== original.role) {
        errors.push(
          `Role mismatch for ${original.id}: expected ${original.role}, got ${restored_profile.role}`,
        );
      }

      if (restored_profile.is_active !== original.is_active) {
        errors.push(`Active status mismatch for ${original.id}`);
      }

      if (restored_profile.employee_id !== original.employee_id) {
        errors.push(`Employee ID mismatch for ${original.id}`);
      }

      if (errors.length === 0) {
        restored.push(original.id);
      }
    });

    return {
      success: errors.length === 0,
      restored,
      errors,
    };
  } catch (error) {
    return {
      success: false,
      errors: [`Verification failed: ${error.message}`],
    };
  }
}

// Run rollback if this script is executed directly
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const isMainModule = process.argv[1] === __filename;

if (isMainModule) {
  const backupFilePath = process.argv[2];

  rollbackMigration(backupFilePath)
    .then(result => {
      if (result && result.success) {
        console.log('🎉 Rollback completed successfully!');
        console.log(`📊 Results: ${result.restored?.length || 0} profiles restored`);
        process.exit(0);
      } else {
        console.error('💥 Rollback failed!');
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('💥 Rollback failed with error:', error);
      process.exit(1);
    });
}

export { rollbackMigration };
