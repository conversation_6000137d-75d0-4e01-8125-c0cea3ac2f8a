/**
 * @file Delegation view renderer component
 * @module components/delegations/DelegationViewRenderer
 */

'use client';

import React from 'react';

import type { ViewMode } from '@/components/dashboard/types';
import type { Delegation } from '@/lib/types/domain';

import { cn } from '@/lib/utils';

import DelegationCalendar from '../calendar/DelegationCalendar';
import DelegationCard from '../common/DelegationCard';
import DelegationTable from './DelegationTable';

/**
 * Props for DelegationViewRenderer component
 */
interface DelegationViewRendererProps {
  className?: string;
  compactMode: boolean;
  delegations: Delegation[];
  gridColumns: number;
  viewMode: ViewMode;
}

/**
 * Delegation view renderer component that switches between different view modes.
 *
 * Supported view modes:
 * - cards: Grid of delegation cards
 * - table: Tabular view with sortable columns
 * - list: Vertical list of delegation cards
 * - calendar: Calendar view showing delegations by date
 *
 * @param props - Component props
 * @returns JSX element representing the delegations in the selected view mode
 */
export const DelegationViewRenderer: React.FC<DelegationViewRendererProps> = ({
  className = '',
  compactMode,
  delegations,
  gridColumns,
  viewMode,
}) => {
  // Render based on view mode
  switch (viewMode) {
    case 'calendar': {
      return (
        <DelegationCalendar className={className} delegations={delegations} />
      );
    }

    case 'list': {
      return (
        <div
          className={cn(
            'flex flex-col',
            compactMode ? 'gap-2' : 'gap-4',
            className
          )}
        >
          {delegations.map(delegation => (
            <DelegationCard delegation={delegation} key={delegation.id} />
          ))}
        </div>
      );
    }

    case 'table': {
      return (
        <DelegationTable className={className} delegations={delegations} />
      );
    }

    case 'cards':
    case 'grid':
    default: {
      return (
        <div
          className={cn(
            'grid grid-cols-1 gap-6',
            `md:grid-cols-2 lg:grid-cols-${gridColumns}`,
            compactMode && 'gap-3',
            className
          )}
        >
          {delegations.map(delegation => (
            <DelegationCard delegation={delegation} key={delegation.id} />
          ))}
        </div>
      );
    }
  }
};

export default DelegationViewRenderer;
