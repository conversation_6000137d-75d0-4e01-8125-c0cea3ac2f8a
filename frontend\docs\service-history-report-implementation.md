# Service History Report Implementation Plan

This document provides a detailed implementation plan for redesigning the
Service History Report page in the WorkHub application.

## Implementation Analysis

### Previous Implementation (Deprecated)

The Service History Report previously existed in two separate implementations:

1. **General Service History** (`/service-history`)

   - Used a `ServiceHistoryContainer` component for displaying service records
     (now deprecated)
   - Provided filtering by vehicle, service type, and search term
   - Used a simple table layout with basic styling
   - Had basic print functionality
   - No pagination for large data sets
   - No summary statistics

2. **Vehicle-Specific Service History** (embedded in `/vehicles/[id]/report`)
   - Showed service history for a specific vehicle
   - Included vehicle details section
   - Used a different table layout than the general view
   - Had print and export functionality via `ReportActions`
   - No pagination for large data sets
   - No summary statistics

### Current Implementation

The Service History Report has been redesigned with a unified approach using
shared components:

1. **General Service History** (`/service-history`)

   - Uses the new `EnhancedServiceHistoryContainer` component
   - Provides filtering by vehicle, service type, and search term
   - Includes summary statistics via `ServiceHistorySummary`
   - Features sortable columns via `ServiceHistoryTable`
   - Implements pagination for large data sets
   - Has improved print and export functionality via `ReportActions`

2. **Vehicle-Specific Service History**
   - Now available as a dedicated page (`/vehicles/[id]/report/service-history`)
   - Also embedded in the Vehicle Detail page (`/vehicles/[id]`)
   - Uses the same `EnhancedServiceHistoryContainer` component
   - Includes vehicle-specific summary statistics
   - Features sortable columns and pagination
   - Has consistent styling and behavior with the general view

## Implementation Details

The redesign has successfully consolidated these two implementations into a
unified, consistent approach while enhancing functionality and user experience.
The following components have been created and are now in production use:

### Phase 1: Create Shared Components

1. **Service History Summary Component**

```tsx
// components/reports/ServiceHistorySummary.tsx
import React from 'react';
import type { EnrichedServiceRecord } from '@/lib/types';

interface ServiceHistorySummaryProps {
  records: EnrichedServiceRecord[];
  vehicleSpecific?: boolean;
}

export function ServiceHistorySummary({
  records,
  vehicleSpecific = false,
}: ServiceHistorySummaryProps) {
  // Calculate statistics
  const totalRecords = records.length;
  const totalCost = records.reduce(
    (sum, record) => sum + (Number(record.cost) || 0),
    0
  );

  // Get date range
  const dates = records.map(record => new Date(record.date).getTime());
  const oldestDate = dates.length ? new Date(Math.min(...dates)) : null;
  const newestDate = dates.length ? new Date(Math.max(...dates)) : null;

  // Get service type counts
  const serviceTypeCounts = records.reduce(
    (acc, record) => {
      record.servicePerformed.forEach(service => {
        acc[service] = (acc[service] || 0) + 1;
      });
      return acc;
    },
    {} as Record<string, number>
  );

  // Get top services
  const topServices = Object.entries(serviceTypeCounts)
    .sort((a, b) => b[1] - a[1])
    .slice(0, 3);

  return (
    <div className="mt-4 grid grid-cols-2 sm:grid-cols-3 gap-2">
      <div className="bg-gray-50 p-2 rounded">
        <p className="text-2xl font-semibold">{totalRecords}</p>
        <p className="text-xs text-gray-500">Total Services</p>
      </div>

      <div className="bg-green-50 p-2 rounded">
        <p className="text-2xl font-semibold">${totalCost.toFixed(2)}</p>
        <p className="text-xs text-green-500">Total Cost</p>
      </div>

      {!vehicleSpecific && records.length > 0 && (
        <div className="bg-blue-50 p-2 rounded">
          <p className="text-2xl font-semibold">
            {new Set(records.map(r => r.vehicleId)).size}
          </p>
          <p className="text-xs text-blue-500">Vehicles Serviced</p>
        </div>
      )}

      {vehicleSpecific && records.length > 0 && (
        <div className="bg-blue-50 p-2 rounded">
          <p className="text-2xl font-semibold">
            {Math.max(...records.map(r => r.odometer)) -
              Math.min(...records.map(r => r.odometer))}
          </p>
          <p className="text-xs text-blue-500">Miles Between Services</p>
        </div>
      )}

      {records.length > 0 && (
        <div className="bg-purple-50 p-2 rounded col-span-2 sm:col-span-3">
          <p className="text-sm font-semibold">Date Range:</p>
          <p className="text-xs text-purple-500">
            {oldestDate?.toLocaleDateString()} -{' '}
            {newestDate?.toLocaleDateString()}
          </p>
        </div>
      )}

      {topServices.length > 0 && (
        <div className="bg-yellow-50 p-2 rounded col-span-2 sm:col-span-3">
          <p className="text-sm font-semibold">Top Services:</p>
          <div className="flex flex-wrap gap-1 mt-1">
            {topServices.map(([service, count]) => (
              <span
                key={service}
                className="text-xs bg-yellow-100 text-yellow-800 px-2 py-0.5 rounded"
              >
                {service} ({count})
              </span>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
```

2. **Enhanced Service History Table Component**

```tsx
// components/service-history/ServiceHistoryTable.tsx
import React from 'react';
import type { EnrichedServiceRecord } from '@/lib/types';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

interface ServiceHistoryTableProps {
  records: EnrichedServiceRecord[];
  showVehicleInfo?: boolean;
  sortField?: string;
  sortDirection?: 'asc' | 'desc';
  onSort?: (field: string) => void;
}

export function ServiceHistoryTable({
  records,
  showVehicleInfo = true,
  sortField,
  sortDirection,
  onSort,
}: ServiceHistoryTableProps) {
  const handleSort = (field: string) => {
    if (onSort) {
      onSort(field);
    }
  };

  return (
    <Table id="service-history-table">
      <TableHeader>
        <TableRow>
          <TableHead
            onClick={() => handleSort('date')}
            className={onSort ? 'cursor-pointer' : ''}
          >
            Date{' '}
            {sortField === 'date' && (
              <span>{sortDirection === 'asc' ? '↑' : '↓'}</span>
            )}
          </TableHead>

          {showVehicleInfo && (
            <TableHead
              onClick={() => handleSort('vehicleMake')}
              className={onSort ? 'cursor-pointer' : ''}
            >
              Vehicle{' '}
              {sortField === 'vehicleMake' && (
                <span>{sortDirection === 'asc' ? '↑' : '↓'}</span>
              )}
            </TableHead>
          )}

          <TableHead
            onClick={() => handleSort('servicePerformed')}
            className={onSort ? 'cursor-pointer' : ''}
          >
            Service(s){' '}
            {sortField === 'servicePerformed' && (
              <span>{sortDirection === 'asc' ? '↑' : '↓'}</span>
            )}
          </TableHead>

          <TableHead
            onClick={() => handleSort('odometer')}
            className={onSort ? 'cursor-pointer' : ''}
          >
            Odometer{' '}
            {sortField === 'odometer' && (
              <span>{sortDirection === 'asc' ? '↑' : '↓'}</span>
            )}
          </TableHead>

          <TableHead
            onClick={() => handleSort('cost')}
            className={`text-right ${onSort ? 'cursor-pointer' : ''}`}
          >
            Cost{' '}
            {sortField === 'cost' && (
              <span>{sortDirection === 'asc' ? '↑' : '↓'}</span>
            )}
          </TableHead>

          <TableHead className="print-notes-col">Notes</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {records.map(record => (
          <TableRow key={record.id}>
            <TableCell>{new Date(record.date).toLocaleDateString()}</TableCell>

            {showVehicleInfo && (
              <TableCell>
                {record.vehicleMake} {record.vehicleModel} ({record.vehicleYear}
                )
                {record.vehiclePlateNumber && (
                  <span className="block text-xs text-muted-foreground">
                    {record.vehiclePlateNumber}
                  </span>
                )}
              </TableCell>
            )}

            <TableCell
              className="max-w-xs truncate print-service-col"
              title={record.servicePerformed.join(', ')}
            >
              {record.servicePerformed.join(', ')}
            </TableCell>

            <TableCell>{record.odometer.toLocaleString()}</TableCell>

            <TableCell className="text-right">
              {record.cost ? `$${Number(record.cost).toFixed(2)}` : '-'}
            </TableCell>

            <TableCell
              className="max-w-xs truncate print-notes-col"
              title={record.notes}
            >
              {record.notes || '-'}
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
}
```

### Phase 2: Enhanced Service History Container

```tsx
// components/service-history/EnhancedServiceHistoryContainer.tsx
import React, { useState, useEffect, useMemo } from 'react';
import type { EnrichedServiceRecord, Vehicle } from '@/lib/types';
import { Card, CardContent } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Info } from 'lucide-react';
import { ServiceHistoryTable } from './ServiceHistoryTable';
import { ServiceHistorySummary } from '@/components/reports/ServiceHistorySummary';
import { PaginationControls } from '@/components/ui/pagination';

interface EnhancedServiceHistoryContainerProps {
  records: EnrichedServiceRecord[];
  isLoading: boolean;
  error: string | null;
  onRetry: () => void;
  showVehicleInfo?: boolean;
  vehicleSpecific?: boolean;
}

export function EnhancedServiceHistoryContainer({
  records,
  isLoading,
  error,
  onRetry,
  showVehicleInfo = true,
  vehicleSpecific = false,
}: EnhancedServiceHistoryContainerProps) {
  // Sorting state
  const [sortField, setSortField] = useState<string>('date');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);

  // Sort records
  const sortedRecords = useMemo(() => {
    return [...records].sort((a, b) => {
      let valueA, valueB;

      // Handle different field types
      switch (sortField) {
        case 'date':
          valueA = new Date(a.date).getTime();
          valueB = new Date(b.date).getTime();
          break;
        case 'cost':
          valueA = Number(a.cost) || 0;
          valueB = Number(b.cost) || 0;
          break;
        case 'odometer':
          valueA = a.odometer;
          valueB = b.odometer;
          break;
        case 'servicePerformed':
          valueA = a.servicePerformed.join(', ');
          valueB = b.servicePerformed.join(', ');
          break;
        case 'vehicleMake':
          valueA = `${a.vehicleMake} ${a.vehicleModel}`;
          valueB = `${b.vehicleMake} ${b.vehicleModel}`;
          break;
        default:
          valueA = a[sortField as keyof EnrichedServiceRecord];
          valueB = b[sortField as keyof EnrichedServiceRecord];
      }

      // Compare values based on direction
      if (valueA < valueB) return sortDirection === 'asc' ? -1 : 1;
      if (valueA > valueB) return sortDirection === 'asc' ? 1 : -1;
      return 0;
    });
  }, [records, sortField, sortDirection]);

  // Handle sort
  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Pagination logic
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const paginatedRecords = sortedRecords.slice(
    indexOfFirstItem,
    indexOfLastItem
  );
  const totalPages = Math.ceil(sortedRecords.length / itemsPerPage);

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Reset pagination when records change
  useEffect(() => {
    setCurrentPage(1);
  }, [records]);

  // Render error state
  if (error) {
    return (
      <Alert variant="destructive" className="mb-6">
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>{error}</AlertDescription>
        <Button variant="outline" size="sm" onClick={onRetry} className="mt-2">
          Try Again
        </Button>
      </Alert>
    );
  }

  // Render empty state
  if (!isLoading && records.length === 0) {
    return (
      <Alert variant="default" className="mb-6">
        <Info className="h-4 w-4" />
        <AlertTitle>No Service Records</AlertTitle>
        <AlertDescription>
          {vehicleSpecific
            ? 'No service records available for this vehicle.'
            : 'No service records match your current filters. Try adjusting your search or filter criteria.'}
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* Summary Statistics */}
      <ServiceHistorySummary
        records={sortedRecords}
        vehicleSpecific={vehicleSpecific}
      />

      {/* Service Records Table */}
      <Card className="shadow-md card-print">
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <ServiceHistoryTable
              records={paginatedRecords}
              showVehicleInfo={showVehicleInfo}
              sortField={sortField}
              sortDirection={sortDirection}
              onSort={handleSort}
            />
          </div>
        </CardContent>
      </Card>

      {/* Pagination */}
      {sortedRecords.length > itemsPerPage && (
        <div className="flex justify-center mt-4 no-print">
          <PaginationControls
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={handlePageChange}
          />
        </div>
      )}
    </div>
  );
}
```

### Phase 3: Update the General Service History Page

The general service history page will be updated to use the enhanced components
while maintaining its existing filtering capabilities.

### Phase 4: Update the Vehicle-Specific Service History Page

The vehicle-specific service history page will be updated to use the enhanced
components while maintaining its vehicle details section.

## Mobile Responsiveness Strategy

1. **Filter Section**

   - Stack filters vertically on mobile using grid-cols-1
   - Adjust padding and spacing for smaller screens

2. **Table Handling**

   - Add horizontal scrolling for the table on small screens
   - Adjust column widths to prioritize important information

3. **Summary Statistics**

   - Reduce from 3 columns to 2 columns on small screens
   - Adjust font sizes for better readability

4. **Pagination**
   - Simplify pagination on mobile (show fewer page numbers)
   - Increase touch target sizes for better usability

## Accessibility Considerations

1. **Semantic HTML**

   - Use proper heading hierarchy
   - Use appropriate ARIA roles for interactive elements

2. **Keyboard Navigation**

   - Ensure all interactive elements are keyboard accessible
   - Add focus styles for better visibility

3. **Screen Reader Support**

   - Add aria-labels to buttons and controls
   - Ensure table has proper headers and cell associations

4. **Color Contrast**
   - Ensure all text meets WCAG AA contrast requirements
   - Don't rely solely on color to convey information

## Implementation Status

The implementation has been completed with the following steps:

1. ✅ Created the shared components:

   - `ServiceHistorySummary.tsx` - Displays summary statistics for service
     records
   - `ServiceHistoryTable.tsx` - Displays service records in a sortable table
   - `EnhancedServiceHistoryContainer.tsx` - Manages sorting, pagination, and
     display of service records

2. ✅ Updated the general service history page (`/service-history`) to use the
   enhanced components

3. ✅ Created a dedicated vehicle-specific service history page
   (`/vehicles/[id]/report/service-history`)

4. ✅ Updated the vehicle detail page (`/vehicles/[id]`) to use the enhanced
   components

5. ✅ Deprecated the old `ServiceHistoryContainer` component with proper
   warnings and migration instructions

6. ✅ Updated documentation to reflect the new implementation

7. 🔄 Testing on different screen sizes and with assistive technologies (in
   progress)

8. 🔄 Refining based on testing feedback (in progress)

## Future Enhancements

1. **Advanced Filtering**:

   - Add date range filter
   - Add cost range filter
   - Save filter preferences

2. **Export Customization**:

   - Allow users to select which columns to include in exports
   - Add more export formats

3. **Service Insights**:
   - Add service frequency analysis
   - Add cost trend analysis
   - Add maintenance prediction based on service history
