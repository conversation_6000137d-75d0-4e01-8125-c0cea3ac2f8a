/**
 * Enhanced CORS Middleware
 *
 * Provides comprehensive CORS handling with enhanced origin detection,
 * special handling for localhost development environments, and proper
 * logging of CORS decisions.
 *
 * Features:
 * - Enhanced origin detection from referer headers
 * - Special handling for localhost development environments (including :3001)
 * - Protocol normalization for origin URLs
 * - Support for all HTTP methods including HEAD for health checks
 * - Comprehensive logging of CORS decisions
 * - Security-first approach with explicit allow/deny logging
 *
 * <AUTHOR> Development Team
 * @version 2.0 - Critical Issues Resolution with 2025 Best Practices
 */

import type { NextFunction, Request, Response } from 'express';

import cors from 'cors';

import logger from '../utils/logger.js';

/**
 * Origin detection result interface
 */
interface OriginDetectionResult {
  detected: string;
  normalized: string;
  source: 'fallback' | 'header' | 'host' | 'none' | 'referer';
}

/**
 * Normalize origin URL to ensure protocol consistency
 *
 * @param origin - Origin URL to normalize
 * @returns Normalized origin URL
 */
const normalizeOrigin = (origin: string): string => {
  if (!origin) return origin;

  // Handle localhost and 127.0.0.1 with proper protocol
  if (origin.includes('localhost') || origin.includes('127.0.0.1')) {
    // Ensure localhost origins have http:// protocol for development
    if (!origin.startsWith('http://') && !origin.startsWith('https://')) {
      return `http://${origin}`;
    }
  }

  return origin;
};

/**
 * Enhanced origin detection with multiple fallback strategies
 *
 * @param req - Express request object
 * @returns Origin detection result
 */
const detectOrigin = (req: Request): OriginDetectionResult => {
  // Primary: Check Origin header
  if (req.headers.origin) {
    const normalized = normalizeOrigin(req.headers.origin);
    return {
      detected: req.headers.origin,
      normalized,
      source: 'header',
    };
  }

  // Secondary: Check Referer header for health checks and API calls
  if (req.headers.referer) {
    try {
      const refererUrl = new URL(req.headers.referer);
      const origin = `${refererUrl.protocol}//${refererUrl.host}`;
      const normalized = normalizeOrigin(origin);
      return {
        detected: origin,
        normalized,
        source: 'referer',
      };
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.debug(`Failed to parse referer header: ${req.headers.referer} - ${errorMessage}`, {
        referer: req.headers.referer,
        service: 'cors-middleware',
      });
    }
  }

  // Tertiary: Check X-Forwarded-Host or Host hea ders
  const forwardedHost = req.headers['x-forwarded-host'] ?? req.headers.host;
  if (forwardedHost) {
    const hostValue = Array.isArray(forwardedHost) ? forwardedHost[0] : forwardedHost;
    const origin = `http://${hostValue}`; // Default to HTTP for host-based detection
    const normalized = normalizeOrigin(origin);
    return {
      detected: origin,
      normalized,
      source: 'host',
    };
  }

  // Fallback: Use environment default
  const defaultOrigin = process.env.FRONTEND_URL?.split(',')[0]?.trim() ?? 'http://localhost:3000';
  return {
    detected: defaultOrigin,
    normalized: normalizeOrigin(defaultOrigin),
    source: 'fallback',
  };
};

/**
 * Check if origin is allowed based on configuration
 *
 * @param origin - Origin to check
 * @param allowedOrigins - List of allowed origins
 * @returns True if origin is allowed
 */
const isOriginAllowed = (origin: string, allowedOrigins: string[]): boolean => {
  const normalizedOrigin = normalizeOrigin(origin);

  // Check exact matches
  if (allowedOrigins.includes(origin) || allowedOrigins.includes(normalizedOrigin)) {
    return true;
  }

  // Special handling for localhost development environments
  if (origin.includes('localhost') || origin.includes('127.0.0.1')) {
    const localhostPatterns = allowedOrigins.filter(
      allowed => allowed.includes('localhost') || allowed.includes('127.0.0.1'),
    );

    // Allow any localhost port if localhost is in allowed origins
    if (localhostPatterns.length > 0) {
      return true;
    }
  }

  return false;
};

/**
 * Create enhanced CORS configuration with localhost:3001 support
 *
 * @returns CORS configuration object
 */
export const createEnhancedCorsOptions = () => {
  // Parse allowed origins from environment variable
  // It is critical that FRONTEND_URL is set in production and development
  const allowedOrigins = process.env.FRONTEND_URL
    ? process.env.FRONTEND_URL.split(',').map(url => url.trim())
    : []; // Default to empty array if not set, forcing explicit configuration

  if (allowedOrigins.length === 0) {
    logger.warn(
      'CORS: FRONTEND_URL environment variable is not set or is empty. No origins will be allowed.',
    );
  }

  const corsConfig = {
    allowedHeaders: [
      'Content-Type',
      'Authorization',
      'X-Requested-With',
      'X-Cache',
      'X-Deduplication-Key',
      'Accept',
      'Origin',
      'User-Agent', // For health check tools
    ],
    allowedOrigins,
    credentials: true,
    exposedHeaders: [
      'X-Cache',
      'X-Deduplication-Key',
      'X-Rate-Limit-Remaining',
      'X-Rate-Limit-Reset',
      'X-Response-Time',
    ],
    maxAge: 86400, // 24 hours
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS', 'HEAD'], // CRITICAL: Include HEAD for health checks
  };

  logger.info('Enhanced CORS configuration initialized', {
    allowedHeaders: corsConfig.allowedHeaders.length,
    allowedOrigins: corsConfig.allowedOrigins,
    methods: corsConfig.methods,
    service: 'cors-middleware',
  });

  return {
    allowedHeaders: corsConfig.allowedHeaders,
    credentials: corsConfig.credentials,
    exposedHeaders: corsConfig.exposedHeaders,
    maxAge: corsConfig.maxAge,
    methods: corsConfig.methods,
    optionsSuccessStatus: 200, // For legacy browser support
    origin: (
      requestOrigin: string | undefined,
      callback: (err: Error | null, allow?: boolean) => void,
    ) => {
      const detectionResult = detectOrigin({ headers: { origin: requestOrigin } } as Request);

      logger.debug('CORS origin detection', {
        detected: detectionResult.detected,
        normalized: detectionResult.normalized,
        requestOrigin: requestOrigin ?? 'none',
        service: 'cors-middleware',
        source: detectionResult.source,
      });

      // Allow requests with no origin (mobile apps, curl, server-to-server)
      if (!requestOrigin) {
        logger.debug('CORS: No origin provided, allowing request', {
          reason: 'no-origin',
          service: 'cors-middleware',
        });
        callback(null, true);
        return;
      }

      // Check if origin is allowed
      const isAllowed = isOriginAllowed(requestOrigin, corsConfig.allowedOrigins);

      if (isAllowed) {
        logger.debug('CORS: Origin allowed', {
          normalized: normalizeOrigin(requestOrigin),
          origin: requestOrigin,
          service: 'cors-middleware',
        });
        callback(null, true);
      } else {
        logger.warn('CORS: Origin rejected', {
          allowedOrigins: corsConfig.allowedOrigins,
          origin: requestOrigin,
          service: 'cors-middleware',
        });
        callback(new Error(`CORS: Origin ${requestOrigin} not allowed`), false);
      }
    },
  };
};

/**
 * Enhanced origin detection middleware (to be used before CORS)
 *
 * @param req - Express request object
 * @param res - Express response object
 * @param next - Express next function
 */
export const enhancedOriginDetection = (req: Request, res: Response, next: NextFunction): void => {
  const detectionResult = detectOrigin(req);

  // Set origin header if detected from alternative sources
  if (
    detectionResult.source !== 'header' &&
    detectionResult.source !== 'none' &&
    detectionResult.detected
  ) {
    const originValue = detectionResult.detected;

    req.headers.origin = originValue;

    logger.debug('Enhanced origin detection: Origin set from alternative source', {
      detectedOrigin: originValue,
      service: 'cors-middleware',
      source: detectionResult.source,
    });
  }

  next();
};

/**
 * CORS logging middleware (to be used after CORS)
 *
 * @param req - Express request object
 * @param res - Express response object
 * @param next - Express next function
 */
export const corsLoggingMiddleware = (req: Request, res: Response, next: NextFunction): void => {
  // Log CORS headers set by the CORS middleware
  const corsHeaders = {
    'access-control-allow-credentials': res.getHeader('access-control-allow-credentials'),
    'access-control-allow-headers': res.getHeader('access-control-allow-headers'),
    'access-control-allow-methods': res.getHeader('access-control-allow-methods'),
    'access-control-allow-origin': res.getHeader('access-control-allow-origin'),
  };

  logger.debug('CORS headers applied', {
    corsHeaders,
    method: req.method,
    origin: req.headers.origin ?? 'none',
    path: req.path,
    service: 'cors-middleware',
  });

  next();
};

/**
 * Create complete enhanced CORS middleware stack
 *
 * @returns Array of middleware functions
 */
export const createEnhancedCorsMiddleware = () => {
  const corsOptions = createEnhancedCorsOptions();

  return [enhancedOriginDetection, cors(corsOptions), corsLoggingMiddleware];
};
