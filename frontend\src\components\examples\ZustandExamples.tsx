/**
 * @file Example components demonstrating Zustand store usage
 * @module components/examples/ZustandExamples
 */

'use client';

import { Bell, Menu, Moon, Settings, Sun, X } from 'lucide-react';
import React from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useAppStore } from '@/lib/stores/zustand/appStore';
import { useUiStore } from '@/lib/stores/zustand/uiStore';

/**
 * Theme toggle component using AppStore
 */
export const ThemeToggleExample: React.FC = () => {
  const { currentTheme, setTheme } = useAppStore();

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {currentTheme === 'light' ? (
            <Sun className="size-5" />
          ) : (
            <Moon className="size-5" />
          )}
          Theme Toggle
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex items-center justify-between">
          <span>Current theme: {currentTheme}</span>
          <Button
            onClick={() =>
              setTheme(currentTheme === 'light' ? 'dark' : 'light')
            }
            size="sm"
            variant="outline"
          >
            Switch to {currentTheme === 'light' ? 'dark' : 'light'}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

/**
 * Sidebar toggle component using AppStore
 */
export const SidebarToggleExample: React.FC = () => {
  const { sidebarOpen, toggleSidebar } = useAppStore();

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Menu className="size-5" />
          Sidebar Control
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex items-center justify-between">
          <span>Sidebar is {sidebarOpen ? 'open' : 'closed'}</span>
          <Button onClick={toggleSidebar} size="sm" variant="outline">
            {sidebarOpen ? (
              <X className="size-4" />
            ) : (
              <Menu className="size-4" />
            )}
            {sidebarOpen ? 'Close' : 'Open'}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

/**
 * Notification system example using AppStore
 */
export const NotificationExample: React.FC = () => {
  const {
    addNotification,
    clearAllNotifications,
    markNotificationAsRead,
    notifications,
    removeNotification,
    unreadNotificationCount,
  } = useAppStore();

  const showNotification = (type: 'error' | 'info' | 'success' | 'warning') => {
    const messages = {
      error: 'An error occurred. Please try again.',
      info: 'This is an informational message',
      success: 'Operation completed successfully!',
      warning: 'Please review this warning',
    };

    addNotification({
      message: messages[type],
      type,
    });
  };

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Bell className="size-5" />
          Notification System
          {unreadNotificationCount() > 0 && (
            <Badge variant="destructive">{unreadNotificationCount()}</Badge>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Notification Controls */}
        <div className="flex flex-wrap gap-2">
          <Button
            onClick={() => showNotification('info')}
            size="sm"
            variant="outline"
          >
            Info
          </Button>
          <Button
            onClick={() => showNotification('success')}
            size="sm"
            variant="outline"
          >
            Success
          </Button>
          <Button
            onClick={() => showNotification('warning')}
            size="sm"
            variant="outline"
          >
            Warning
          </Button>
          <Button
            onClick={() => showNotification('error')}
            size="sm"
            variant="outline"
          >
            Error
          </Button>
          {notifications.length > 0 && (
            <Button
              onClick={clearAllNotifications}
              size="sm"
              variant="destructive"
            >
              Clear All
            </Button>
          )}
        </div>

        {/* Notifications List */}
        <div className="max-h-64 space-y-2 overflow-y-auto">
          {notifications.length === 0 ? (
            <p className="py-4 text-center text-muted-foreground">
              No notifications
            </p>
          ) : (
            notifications.map(notification => (
              <div
                className={`rounded border p-3 ${
                  notification.read ? 'bg-muted' : 'bg-background'
                } ${
                  notification.type === 'error'
                    ? 'border-red-200'
                    : notification.type === 'warning'
                      ? 'border-yellow-200'
                      : notification.type === 'success'
                        ? 'border-green-200'
                        : 'border-blue-200'
                }`}
                key={notification.id}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <Badge
                        variant={
                          notification.type === 'error'
                            ? 'destructive'
                            : notification.type === 'warning'
                              ? 'secondary'
                              : notification.type === 'success'
                                ? 'default'
                                : 'outline'
                        }
                      >
                        {notification.type}
                      </Badge>
                      {!notification.read && (
                        <Badge variant="outline">New</Badge>
                      )}
                    </div>
                    <p className="mt-1 text-sm">{notification.message}</p>
                    <p className="mt-1 text-xs text-muted-foreground">
                      {new Date(notification.timestamp).toLocaleString()}
                    </p>
                  </div>
                  <div className="ml-2 flex gap-1">
                    {!notification.read && (
                      <Button
                        onClick={() => markNotificationAsRead(notification.id)}
                        size="sm"
                        variant="ghost"
                      >
                        Mark Read
                      </Button>
                    )}
                    <Button
                      onClick={() => removeNotification(notification.id)}
                      size="sm"
                      variant="ghost"
                    >
                      ×
                    </Button>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  );
};

/**
 * Font size control using UiStore
 */
export const FontSizeExample: React.FC = () => {
  const { fontSize, setFontSize } = useUiStore();

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle>Font Size Preference</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <label htmlFor="font-size">Font Size:</label>
            <select
              className="rounded border px-2 py-1"
              id="font-size"
              onChange={e =>
                setFontSize(e.target.value as 'large' | 'medium' | 'small')
              }
              value={fontSize}
            >
              <option value="small">Small</option>
              <option value="medium">Medium</option>
              <option value="large">Large</option>
            </select>
          </div>
          <div
            className={`rounded border p-3 ${
              fontSize === 'small'
                ? 'text-sm'
                : (fontSize === 'large'
                  ? 'text-lg'
                  : 'text-base')
            }`}
          >
            This text demonstrates the {fontSize} font size setting.
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

/**
 * Modal system example using UiStore
 */
export const ModalExample: React.FC = () => {
  const { closeModal, isModalOpen, modalContent, openModal } = useUiStore();

  return (
    <>
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="size-5" />
            Modal System
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            <Button
              onClick={() => openModal('login')}
              size="sm"
              variant="outline"
            >
              Login Modal
            </Button>
            <Button
              onClick={() => openModal('signup')}
              size="sm"
              variant="outline"
            >
              Signup Modal
            </Button>
            <Button
              onClick={() => openModal('settings')}
              size="sm"
              variant="outline"
            >
              Settings Modal
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Modal Overlay */}
      {isModalOpen && (
        <div
          className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
          onClick={closeModal}
        >
          <div
            className="mx-4 w-full max-w-md rounded-lg bg-white p-6"
            onClick={e => e.stopPropagation()}
          >
            <div className="mb-4 flex items-center justify-between">
              <h2 className="text-lg font-semibold capitalize">
                {modalContent} Modal
              </h2>
              <Button onClick={closeModal} size="sm" variant="ghost">
                ×
              </Button>
            </div>
            <div className="space-y-4">
              {modalContent === 'login' && (
                <div>
                  <p>Login form would go here...</p>
                  <div className="mt-4 space-y-2">
                    <input
                      className="w-full rounded border px-3 py-2"
                      placeholder="Email"
                      type="email"
                    />
                    <input
                      className="w-full rounded border px-3 py-2"
                      placeholder="Password"
                      type="password"
                    />
                  </div>
                </div>
              )}
              {modalContent === 'signup' && (
                <div>
                  <p>Signup form would go here...</p>
                  <div className="mt-4 space-y-2">
                    <input
                      className="w-full rounded border px-3 py-2"
                      placeholder="Full Name"
                      type="text"
                    />
                    <input
                      className="w-full rounded border px-3 py-2"
                      placeholder="Email"
                      type="email"
                    />
                    <input
                      className="w-full rounded border px-3 py-2"
                      placeholder="Password"
                      type="password"
                    />
                  </div>
                </div>
              )}
              {modalContent === 'settings' && (
                <div>
                  <p>Settings form would go here...</p>
                  <div className="mt-4 space-y-2">
                    <label className="flex items-center gap-2">
                      <input type="checkbox" />
                      Enable notifications
                    </label>
                    <label className="flex items-center gap-2">
                      <input type="checkbox" />
                      Dark mode
                    </label>
                  </div>
                </div>
              )}
              <div className="flex gap-2 pt-4">
                <Button
                  className="flex-1"
                  onClick={closeModal}
                  variant="outline"
                >
                  Cancel
                </Button>
                <Button className="flex-1">
                  {modalContent === 'login'
                    ? 'Login'
                    : (modalContent === 'signup'
                      ? 'Sign Up'
                      : 'Save')}
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

/**
 * Combined example showing all Zustand store features
 */
export const ZustandShowcase: React.FC = () => {
  return (
    <div className="space-y-6 p-6">
      <h1 className="text-2xl font-bold">Zustand Stores Showcase</h1>
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
        <ThemeToggleExample />
        <SidebarToggleExample />
        <FontSizeExample />
        <ModalExample />
      </div>
      <NotificationExample />
    </div>
  );
};
