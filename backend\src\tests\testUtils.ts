// testUtils.ts - Helper functions for testing with Jest and ESM modules

/**
 * Create Express request and response mocks for controller testing
 */
export function createExpressMocks() {
  const responseJson = jest.fn().mockReturnValue({ end: jest.fn() });
  const responseStatus = jest.fn().mockReturnThis();
  const responseEnd = jest.fn();

  const mockRequest = {
    body: {},
    cookies: {},
    headers: {},
    params: {},
    query: {},
  };

  const mockResponse = {
    clearCookie: jest.fn().mockReturnThis(),
    cookie: jest.fn().mockReturnThis(),
    end: responseEnd,
    json: responseJson,
    redirect: jest.fn().mockReturnThis(),
    send: jest.fn().mockReturnThis(),
    status: responseStatus,
  };

  return { mockRequest, mockResponse, responseJson, responseStatus };
}

/**
 * Creates a mock object with methods auto-setup as Jest spies.
 * Works with ESM modules where direct jest.mock() doesn't work properly.
 *
 * @param methods Methods to mock
 * @returns Object with all methods mocked as Jest spies
 */
export function createMock<T extends Record<string, any>>(
  methods: (keyof T)[] = [],
): Record<string, jest.Mock> {
  const mock = {} as Record<string, jest.Mock>;

  methods.forEach(method => {
    mock[method as string] = jest.fn();
  });

  return mock as any;
}

/**
 * Creates mock Express app for testing routes
 */
export function createMockExpressApp() {
  const routes: Record<string, any> = {};
  const handlers: Record<string, any> = {};

  // Create mock implementations for Express app methods
  const app: Record<string, any> = {
    delete: jest.fn((path, ...handlers) => {
      routes[`DELETE ${path}`] = handlers;
      return app;
    }),
    get: jest.fn((path, ...handlers) => {
      routes[`GET ${path}`] = handlers;
      return app;
    }),
    handlers,
    listen: jest.fn(),
    patch: jest.fn((path, ...handlers) => {
      routes[`PATCH ${path}`] = handlers;
      return app;
    }),
    post: jest.fn((path, ...handlers) => {
      routes[`POST ${path}`] = handlers;
      return app;
    }),
    put: jest.fn((path, ...handlers) => {
      routes[`PUT ${path}`] = handlers;
      return app;
    }),
    routes,
    use: jest.fn((path, handler) => {
      if (typeof path === 'function') {
        // Middleware without path
        handlers.middleware = handlers.middleware || [];
        handlers.middleware.push(path);
      } else {
        // Middleware or sub-route with path
        handlers[path] = handler;
      }
      return app;
    }),
  };

  return app;
}

/**
 * Mocks Prisma client for testing
 */
export function mockPrisma() {
  const prismaModelMethods = [
    'findUnique',
    'findFirst',
    'findMany',
    'create',
    'update',
    'upsert',
    'delete',
    'deleteMany',
    'count',
  ];

  // Create model mock generators
  const createModelMock = () => {
    const modelMock = {} as Record<string, jest.Mock>;

    prismaModelMethods.forEach(method => {
      modelMock[method] = jest.fn();
    });

    return modelMock;
  };

  // Create a mock Prisma client with common models
  const prisma = {
    $connect: jest.fn().mockResolvedValue(undefined),
    $disconnect: jest.fn().mockResolvedValue(undefined),
    $queryRawUnsafe: jest.fn(),
    $transaction: jest.fn().mockImplementation(callback => callback(prisma)),
    delegation: createModelMock(),
    employee: createModelMock(),
    employeeStatusEntry: createModelMock(),
    flight: createModelMock(),
    serviceRecord: createModelMock(),
    task: createModelMock(),
    vehicle: createModelMock(),
  };

  return prisma;
}
