# 🔐 SecureApiClient Usage Guide

## Overview

The `SecureApiClient` combines the best of both worlds:
- **ApiClient's** reliability, performance, and retry logic
- **useSecureApi's** comprehensive security features

## 🎯 Benefits

### ✅ **Security Features from useSecureApi:**
- **CSRF Protection** - Automatic CSRF tokens for state-changing operations
- **Input Sanitization** - Sanitizes all request data
- **Token Validation** - Validates tokens before requests
- **Automatic Token Refresh** - Refreshes expired tokens automatically
- **Auto Logout** - Signs out user on authentication failures
- **Authentication Checks** - Ensures user is authenticated

### ✅ **Performance Features from ApiClient:**
- **Retry Logic** - Exponential backoff retry mechanism
- **Error Handling** - Comprehensive error classification
- **Timeout Management** - Configurable request timeouts
- **HTTP Methods** - Full REST API support (GET, POST, PUT, PATCH, DELETE)

## 🚀 Usage Examples

### **Basic Usage**

```typescript
import { useSecureApiClient } from '@/hooks/api/useSecureApiClient';

function MyComponent() {
  const { secureApiClient, isAuthenticated, securityStatus } = useSecureApiClient();

  const fetchData = async () => {
    try {
      // All security features applied automatically:
      // ✅ Token validation & refresh
      // ✅ Input sanitization  
      // ✅ CSRF protection (for POST/PUT/PATCH/DELETE)
      // ✅ Auto logout on auth errors
      const result = await secureApiClient.get('/api/data');
      return result;
    } catch (error) {
      // Error already handled by SecureApiClient
      console.error('Request failed:', error);
    }
  };

  if (!isAuthenticated) {
    return <div>Please sign in</div>;
  }

  return <div>Secure component content</div>;
}
```

### **Advanced Configuration**

```typescript
import { useSecureApiClient } from '@/hooks/api/useSecureApiClient';

function AdvancedComponent() {
  const { secureApiClient } = useSecureApiClient({
    // Custom configuration
    baseURL: '/api/v2',
    timeout: 15000,
    retryAttempts: 5,
    
    // Security features (all enabled by default)
    enableCSRF: true,
    enableInputSanitization: true,
    enableTokenValidation: true,
    enableAutoLogout: true,
  });

  // Use as normal ApiClient
  const createUser = async (userData: any) => {
    // Automatically applies:
    // 1. Input sanitization on userData
    // 2. CSRF protection (POST request)
    // 3. Token validation/refresh
    // 4. Auto logout on 401 errors
    return await secureApiClient.post('/users', userData);
  };
}
```

### **React Query Integration**

```typescript
import { useQuery, useMutation } from '@tanstack/react-query';
import { useSecureApiClient } from '@/hooks/api/useSecureApiClient';

function SecureDataComponent() {
  const { secureApiClient, isAuthenticated, hasValidToken } = useSecureApiClient();

  // Secure query
  const { data, isLoading, error } = useQuery({
    queryKey: ['secure-data'],
    queryFn: () => secureApiClient.get('/secure-endpoint'),
    enabled: isAuthenticated && hasValidToken,
    retry: (failureCount, error: any) => {
      // Don't retry auth errors (SecureApiClient already handled logout)
      if (error?.status === 401) return false;
      return failureCount < 3;
    },
  });

  // Secure mutation
  const createMutation = useMutation({
    mutationFn: (data: any) => secureApiClient.post('/create', data),
    onError: (error) => {
      // SecureApiClient already handled auto-logout if needed
      console.error('Creation failed:', error);
    },
  });

  return (
    <div>
      {isLoading && <div>Loading...</div>}
      {data && <div>Data: {JSON.stringify(data)}</div>}
      <button onClick={() => createMutation.mutate({ name: 'test' })}>
        Create Item
      </button>
    </div>
  );
}
```

### **Manual Security Control**

```typescript
import { useSecureApiClientFactory } from '@/hooks/api/useSecureApiClient';

function ManualSecurityComponent() {
  const { createClient, securityFeatures } = useSecureApiClientFactory({
    enableCSRF: false, // Disable CSRF for this specific client
  });

  const handleSpecialRequest = async () => {
    const client = createClient();
    
    // Manually initialize security features when needed
    client.initializeSecurity({
      inputSanitization: securityFeatures.inputSanitization,
      tokenValidation: securityFeatures.tokenValidation,
      authValidation: securityFeatures.authValidation,
      // Skip CSRF for this specific use case
    });

    return await client.get('/special-endpoint');
  };
}
```

## 🔧 Migration from useSecureApi

### **Before (useSecureApi only):**
```typescript
function OldComponent() {
  const { secureRequest } = useSecureApi();

  const fetchData = async () => {
    const result = await secureRequest({
      url: '/api/data',
      method: 'GET',
    });
    return result.data;
  };
}
```

### **After (SecureApiClient):**
```typescript
function NewComponent() {
  const { secureApiClient } = useSecureApiClient();

  const fetchData = async () => {
    // Same security features + better performance & reliability
    const result = await secureApiClient.get('/data');
    return result;
  };
}
```

## 🔍 Security Status Monitoring

```typescript
function SecurityMonitorComponent() {
  const { securityStatus, isAuthenticated, hasValidToken } = useSecureApiClient();

  return (
    <div>
      <h3>Security Status</h3>
      <p>Authenticated: {isAuthenticated ? '✅' : '❌'}</p>
      <p>Valid Token: {hasValidToken ? '✅' : '❌'}</p>
      <p>Security Features:</p>
      <ul>
        <li>CSRF: {securityStatus.securityFeaturesEnabled.enableCSRF ? '✅' : '❌'}</li>
        <li>Sanitization: {securityStatus.securityFeaturesEnabled.enableInputSanitization ? '✅' : '❌'}</li>
        <li>Token Validation: {securityStatus.securityFeaturesEnabled.enableTokenValidation ? '✅' : '❌'}</li>
        <li>Auto Logout: {securityStatus.securityFeaturesEnabled.enableAutoLogout ? '✅' : '❌'}</li>
      </ul>
    </div>
  );
}
```

## 🎯 Best Practices

### ✅ **Do:**
- Use `useSecureApiClient` for all authenticated API calls
- Enable all security features by default
- Check `isAuthenticated` and `hasValidToken` before making requests
- Handle errors gracefully (SecureApiClient handles auth errors automatically)

### ❌ **Don't:**
- Mix `useSecureApi` and `apiClient` in the same component
- Disable security features unless absolutely necessary
- Retry authentication errors (SecureApiClient handles this)
- Manually handle token refresh (SecureApiClient does this automatically)

## 🔄 Token Refresh Handling

The SecureApiClient automatically handles token refresh:

```typescript
// This happens automatically inside SecureApiClient
const makeRequest = async () => {
  // 1. Check if token is valid/expired
  // 2. If expired, attempt refresh
  // 3. If refresh fails, auto logout
  // 4. If refresh succeeds, proceed with request
  // 5. Apply all other security features
  return await secureApiClient.get('/protected-endpoint');
};
```

## 🚀 Performance Benefits

- **Reduced Bundle Size**: Single client instead of multiple hooks
- **Better Caching**: Consistent request patterns for React Query
- **Fewer Re-renders**: Optimized dependency arrays
- **Retry Logic**: Built-in exponential backoff
- **Connection Pooling**: Reuses HTTP connections efficiently
