/**
 * @file Custom hook for notification management using Zustand AppStore
 * @module hooks/useNotifications
 */

import { useCallback } from 'react';

import { useAppStore } from '@/lib/stores/zustand/appStore';
import { undefinedToNull } from '../../lib/utils/typeHelpers';

/**
 * Custom hook for simplified notification management
 * Provides convenient methods for showing different types of notifications
 */
export const useNotifications = () => {
  const addNotification = useAppStore(state => state.addNotification);
  const removeNotification = useAppStore(state => state.removeNotification);
  const clearAllNotifications = useAppStore(
    state => state.clearAllNotifications
  );
  const unreadCount = useAppStore(state => state.unreadNotificationCount);

  /**
   * Show a success notification
   */
  const showSuccess = useCallback(
    (message: string) => {
      addNotification({
        message,
        type: 'success',
      });
    },
    [addNotification]
  );

  /**
   * Show an error notification
   */
  const showError = useCallback(
    (message: string) => {
      addNotification({
        message,
        type: 'error',
      });
    },
    [addNotification]
  );

  /**
   * Show a warning notification
   */
  const showWarning = useCallback(
    (message: string) => {
      addNotification({
        message,
        type: 'warning',
      });
    },
    [addNotification]
  );

  /**
   * Show an info notification
   */
  const showInfo = useCallback(
    (message: string) => {
      addNotification({
        message,
        type: 'info',
      });
    },
    [addNotification]
  );

  /**
   * Show a notification for API operation results
   */
  const showApiResult = useCallback(
    (success: boolean, successMessage: string, errorMessage: string) => {
      if (success) {
        showSuccess(successMessage);
      } else {
        showError(errorMessage);
      }
    },
    [showSuccess, showError]
  );

  /**
   * Show a notification with auto-dismiss after specified time
   */
  const showTemporary = useCallback(
    (
      type: 'error' | 'info' | 'success' | 'warning',
      message: string,
      dismissAfter = 5000
    ) => {
      addNotification({ message, type });

      // Auto-dismiss after specified time
      setTimeout(() => {
        // Note: This is a simplified approach. In a real implementation,
        // you might want to store the notification ID and remove specifically that one
        const notifications = useAppStore.getState().notifications;
        const latestNotification = notifications.at(-1);
        if (latestNotification && latestNotification.message === message) {
          removeNotification(latestNotification.id);
        }
      }, dismissAfter);
    },
    [addNotification, removeNotification]
  );

  /**
   * Show a loading notification that can be updated
   */
  const showLoading = useCallback(
    (message = 'Loading...') => {
      addNotification({
        message,
        type: 'info',
      });

      // Return the notification ID for potential updates
      const notifications = useAppStore.getState().notifications;
      return notifications.at(-1)?.id;
    },
    [addNotification]
  );

  /**
   * Update a loading notification to success or error
   */
  const updateLoadingNotification = useCallback(
    (notificationId: string, success: boolean, message: string) => {
      removeNotification(notificationId);
      if (success) {
        showSuccess(message);
      } else {
        showError(message);
      }
    },
    [removeNotification, showSuccess, showError]
  );

  return {
    clearAllNotifications,
    // Store methods
    removeNotification,
    // Advanced methods
    showApiResult,
    showError,

    showInfo,
    showLoading,
    // Basic notification methods
    showSuccess,
    showTemporary,

    showWarning,
    unreadCount,
    updateLoadingNotification,
  };
};

/**
 * Enhanced notification hook with WorkHub-specific notification types
 */
export const useWorkHubNotifications = () => {
  const {
    clearAllNotifications,
    removeNotification,
    showError,
    showInfo,
    showSuccess,
    showWarning,
    unreadCount,
  } = useNotifications();

  /**
   * Show delegation-related notifications
   */
  const showDelegationUpdate = useCallback(
    (message: string, actionUrl?: string) => {
      const addNotification = useAppStore.getState().addNotification;
      addNotification({
        ...(actionUrl && { actionUrl }),
        category: 'delegation',
        message,
        type: 'delegation-update',
      });
    },
    []
  );

  /**
   * Show vehicle maintenance notifications
   */
  const showVehicleMaintenance = useCallback(
    (message: string, actionUrl?: string) => {
      const addNotification = useAppStore.getState().addNotification;
      addNotification({
        ...(actionUrl && { actionUrl }),
        category: 'vehicle',
        message,
        type: 'vehicle-maintenance',
      });
    },
    []
  );

  /**
   * Show task assignment notifications
   */
  const showTaskAssigned = useCallback(
    (message: string, actionUrl?: string) => {
      const addNotification = useAppStore.getState().addNotification;
      addNotification({
        ...(actionUrl && { actionUrl }),
        category: 'task',
        message,
        type: 'task-assigned',
      });
    },
    []
  );

  /**
   * Show employee update notifications
   */
  const showEmployeeUpdate = useCallback(
    (message: string, actionUrl?: string) => {
      const addNotification = useAppStore.getState().addNotification;
      addNotification({
        ...(actionUrl && { actionUrl }),
        category: 'employee',
        message,
        type: 'employee-update',
      });
    },
    []
  );

  return {
    clearAllNotifications,
    // Management
    removeNotification,
    // WorkHub-specific notifications
    showDelegationUpdate,
    showEmployeeUpdate,

    showError,
    showInfo,
    // Basic notifications
    showSuccess,
    showTaskAssigned,

    showVehicleMaintenance,
    showWarning,
    unreadCount,
  };
};
