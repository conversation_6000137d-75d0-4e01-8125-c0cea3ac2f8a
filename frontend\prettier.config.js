/**
 * Prettier 2025 Configuration for WorkHub Frontend
 * Modern formatting rules optimized for React, Next.js, TypeScript, and Tailwind CSS
 */
export default {
  // Line formatting
  printWidth: 100, // Increased from 80 for modern wide screens
  tabWidth: 2,
  useTabs: false, // Spaces are more consistent across editors

  // Quotes and semicolons
  semi: true,
  singleQuote: true,
  quoteProps: 'as-needed',
  jsxSingleQuote: false, // Use double quotes in JSX for consistency with HTML

  // Trailing commas (ES2017+ support)
  trailingComma: 'all', // Better for git diffs and easier array/object manipulation

  // Brackets and spacing
  bracketSpacing: true,
  bracketSameLine: false, // Put > on new line for better readability

  // Arrow functions
  arrowParens: 'avoid', // Cleaner for single parameter functions

  // Line endings (consistent across platforms)
  endOfLine: 'lf',

  // Embedded language formatting
  embeddedLanguageFormatting: 'auto',

  // HTML whitespace sensitivity
  htmlWhitespaceSensitivity: 'css',

  // Prose wrapping (for markdown/comments)
  proseWrap: 'preserve',

  // JSX specific
  jsxBracketSameLine: false,

  // Override for specific file types
  overrides: [
    {
      files: '*.json',
      options: {
        printWidth: 120,
        tabWidth: 2,
      },
    },
    {
      files: '*.md',
      options: {
        printWidth: 80,
        proseWrap: 'always',
      },
    },
    {
      files: '*.yml',
      options: {
        tabWidth: 2,
        singleQuote: false,
      },
    },
    {
      files: '*.css',
      options: {
        singleQuote: false,
      },
    },
    {
      files: '*.scss',
      options: {
        singleQuote: false,
      },
    },
    {
      files: '*.html',
      options: {
        printWidth: 120,
        htmlWhitespaceSensitivity: 'ignore',
      },
    },
    {
      files: ['*.tsx', '*.jsx'],
      options: {
        // JSX specific overrides
        jsxSingleQuote: false,
        bracketSameLine: false,
      },
    },
  ],

  // Plugin configurations
  plugins: [
    // Tailwind CSS class sorting
    'prettier-plugin-tailwindcss',
  ],
};
