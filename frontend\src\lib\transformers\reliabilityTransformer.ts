/**
 * @file Data transformer for Reliability domain models.
 * @module transformers/reliabilityTransformer
 */

import type {
  AcknowledgeAlertRequest,
  AlertApiResponse,
  AlertHistoryApiResponse,
  AlertStatisticsApiResponse,
  CircuitBreakerStatusApiResponse,
  DeduplicationMetricsApiResponse,
  DependencyHealthApiResponse,
  DetailedHealthApiResponse,
  HealthCheckApiResponse,
  MetricsApiResponse,
  ResolveAlertRequest,
  TestAlertsApiResponse,
} from '../types/api';
import type {
  Alert,
  AlertHistory,
  AlertSeverity,
  AlertStatistics,
  AlertStatus,
  CircuitBreakerState,
  CircuitBreakerStatus,
  ComponentHealth,
  DeduplicationMetrics,
  DependencyHealth,
  DetailedHealthCheck,
  HealthCheck,
  HealthStatus,
  SystemMetrics,
  TestAlertsResult,
} from '../types/domain';

/**
 * Transforms reliability data between API response formats and frontend domain models.
 * Follows the established pattern of static transformation methods.
 */
export const ReliabilityTransformer = {
  /**
   * Converts a basic health check API response into a frontend domain model.
   * @param apiData - The raw health check data received from the API.
   * @returns The transformed HealthCheck domain model.
   */
  fromHealthApi(apiData: HealthCheckApiResponse): HealthCheck {
    return {
      status: this.transformHealthStatus(apiData.status),
      timestamp: apiData.timestamp,
      uptime: apiData.uptime,
      version: apiData.version,
      environment: apiData.environment,
    };
  },

  /**
   * Converts a detailed health check API response into a frontend domain model.
   * @param apiData - The raw detailed health check data received from the API.
   * @returns The transformed DetailedHealthCheck domain model.
   */
  fromDetailedHealthApi(
    apiData: DetailedHealthApiResponse
  ): DetailedHealthCheck {
    return {
      status: this.transformHealthStatus(apiData.status),
      timestamp: apiData.timestamp,
      uptime: apiData.uptime,
      version: apiData.version,
      environment: apiData.environment,
      checks: {
        database: this.transformComponentHealth(apiData.checks.database),
        supabase: this.transformComponentHealth(apiData.checks.supabase),
        cache: {
          ...this.transformComponentHealth(apiData.checks.cache),
          ...(apiData.checks.cache.details && {
            details: apiData.checks.cache.details,
          }),
        },
        circuitBreakers: {
          ...this.transformComponentHealth(apiData.checks.circuitBreakers),
          ...(apiData.checks.circuitBreakers.details && {
            details: apiData.checks.circuitBreakers.details,
          }),
        },
        systemResources: {
          ...this.transformComponentHealth(apiData.checks.systemResources),
          ...(apiData.checks.systemResources.details && {
            details: apiData.checks.systemResources.details,
          }),
        },
        businessLogic: this.transformComponentHealth(
          apiData.checks.businessLogic
        ),
      },
      summary: apiData.summary,
    };
  },

  /**
   * Converts a dependency health API response into a frontend domain model.
   * @param apiData - The raw dependency health data received from the API.
   * @returns The transformed DependencyHealth domain model.
   */
  fromDependencyHealthApi(
    apiData: DependencyHealthApiResponse
  ): DependencyHealth {
    return {
      dependencies: apiData.dependencies.map(dep => ({
        name: dep.name,
        status: this.transformHealthStatus(dep.status),
        responseTime: dep.responseTime ?? 0,
        lastChecked: dep.lastChecked,
        ...(dep.error && { error: dep.error }),
      })),
      summary: apiData.summary,
    };
  },

  /**
   * Converts a circuit breaker status API response into a frontend domain model.
   * @param apiData - The raw circuit breaker status data received from the API.
   * @returns The transformed CircuitBreakerStatus domain model.
   */
  fromCircuitBreakerApi(
    apiData: CircuitBreakerStatusApiResponse
  ): CircuitBreakerStatus {
    return {
      circuitBreakers: apiData.circuitBreakers.map(breaker => ({
        name: breaker.name,
        state: this.transformCircuitBreakerState(breaker.state),
        failureCount: breaker.failureCount,
        ...(breaker.lastFailureTime && {
          lastFailureTime: breaker.lastFailureTime,
        }),
        ...(breaker.nextAttempt && { nextAttempt: breaker.nextAttempt }),
        ...(breaker.successCount !== undefined && {
          successCount: breaker.successCount,
        }),
        ...(breaker.timeout !== undefined && { timeout: breaker.timeout }),
      })),
      summary: apiData.summary,
    };
  },

  /**
   * Converts a deduplication metrics API response into a frontend domain model.
   * @param apiData - The raw deduplication metrics data received from the API.
   * @returns The transformed DeduplicationMetrics domain model.
   */
  fromDeduplicationMetricsApi(
    apiData: DeduplicationMetricsApiResponse
  ): DeduplicationMetrics {
    return {
      totalRequests: apiData.totalRequests,
      cacheHits: apiData.cacheHits,
      cacheMisses: apiData.cacheMisses,
      hitRate: apiData.hitRate,
      errors: apiData.errors,
      lastReset: apiData.lastReset,
    };
  },

  /**
   * Converts a system metrics API response into a frontend domain model.
   * @param apiData - The raw system metrics data received from the API.
   * @returns The transformed SystemMetrics domain model.
   */
  fromMetricsApi(apiData: MetricsApiResponse): SystemMetrics {
    return {
      systemMetrics: apiData.systemMetrics,
      httpRequestMetrics: {
        name: apiData.httpRequestMetrics.name,
        help: apiData.httpRequestMetrics.help,
        type: apiData.httpRequestMetrics.type,
        values: apiData.httpRequestMetrics.values.map(value => ({
          labels: {
            method: value.labels.method,
            route: value.labels.route,
            statusCode: value.labels.status_code, // Transform snake_case to camelCase
            userRole: value.labels.user_role, // Transform snake_case to camelCase
          },
          value: value.value,
        })),
      },
      deduplicationMetrics: this.fromDeduplicationMetricsApi(
        apiData.deduplicationMetrics
      ),
    };
  },

  /**
   * Converts an alert API response into a frontend domain model.
   * @param apiData - The raw alert data received from the API.
   * @returns The transformed Alert domain model.
   */
  fromAlertApi(apiData: AlertApiResponse): Alert {
    return {
      id: apiData.id,
      type: apiData.type,
      severity: this.transformAlertSeverity(apiData.severity),
      message: apiData.message,
      details: apiData.details,
      timestamp: apiData.timestamp,
      status: this.transformAlertStatus(apiData.status),
      ...(apiData.acknowledgedAt && { acknowledgedAt: apiData.acknowledgedAt }),
      ...(apiData.acknowledgedBy && { acknowledgedBy: apiData.acknowledgedBy }),
      ...(apiData.resolvedAt && { resolvedAt: apiData.resolvedAt }),
      ...(apiData.resolvedBy && { resolvedBy: apiData.resolvedBy }),
      source: apiData.source,
    };
  },

  /**
   * Converts an alert statistics API response into a frontend domain model.
   * @param apiData - The raw alert statistics data received from the API.
   * @returns The transformed AlertStatistics domain model.
   */
  fromAlertStatisticsApi(apiData: AlertStatisticsApiResponse): AlertStatistics {
    return {
      total: apiData.total,
      active: apiData.active,
      acknowledged: apiData.acknowledged,
      resolved: apiData.resolved,
      bySeverity: apiData.bySeverity,
      averageResolutionTime: apiData.averageResolutionTime,
      recentTrends: apiData.recentTrends,
    };
  },

  /**
   * Converts an alert history API response into a frontend domain model.
   * @param apiData - The raw alert history data received from the API.
   * @returns The transformed AlertHistory domain model.
   */
  fromAlertHistoryApi(apiData: AlertHistoryApiResponse): AlertHistory {
    return {
      alerts: apiData.alerts.map(alert => this.fromAlertApi(alert)),
      pagination: apiData.pagination,
    };
  },

  /**
   * Converts a test alerts API response into a frontend domain model.
   * @param apiData - The raw test alerts data received from the API.
   * @returns The transformed TestAlertsResult domain model.
   */
  fromTestAlertsApi(apiData: TestAlertsApiResponse): TestAlertsResult {
    return {
      success: apiData.success,
      message: apiData.message,
      ...(apiData.testAlertId && { testAlertId: apiData.testAlertId }),
    };
  },

  /**
   * Converts frontend data for resolving an alert into an API request payload.
   * @param reason - Optional reason for resolving the alert.
   * @param resolvedBy - Optional identifier of who resolved the alert.
   * @returns The transformed payload, compatible with ResolveAlertRequest.
   */
  toResolveAlertRequest(
    reason?: string,
    resolvedBy?: string
  ): ResolveAlertRequest {
    return {
      ...(reason?.trim() && { reason: reason.trim() }),
      ...(resolvedBy?.trim() && { resolvedBy: resolvedBy.trim() }),
    };
  },

  /**
   * Converts frontend data for acknowledging an alert into an API request payload.
   * @param note - Optional note for acknowledging the alert.
   * @param acknowledgedBy - Optional identifier of who acknowledged the alert.
   * @returns The transformed payload, compatible with AcknowledgeAlertRequest.
   */
  toAcknowledgeAlertRequest(
    note?: string,
    acknowledgedBy?: string
  ): AcknowledgeAlertRequest {
    return {
      ...(note?.trim() && { note: note.trim() }),
      ...(acknowledgedBy?.trim() && { acknowledgedBy: acknowledgedBy.trim() }),
    };
  },

  // Helper transformation methods

  /**
   * Transforms API health status string to domain HealthStatus type.
   * @param status - The health status string from the API.
   * @returns The transformed HealthStatus.
   */
  transformHealthStatus(status: string): HealthStatus {
    switch (status) {
      case 'healthy':
        return 'healthy';
      case 'degraded':
        return 'degraded';
      case 'unhealthy':
        return 'unhealthy';
      default:
        // Defensive programming: default to unhealthy for unknown statuses
        return 'unhealthy';
    }
  },

  /**
   * Transforms API component health data to domain ComponentHealth type.
   * @param componentData - The component health data from the API.
   * @returns The transformed ComponentHealth.
   */
  transformComponentHealth(componentData: {
    status: string;
    responseTime?: number;
  }): ComponentHealth {
    return {
      status: this.transformHealthStatus(componentData.status),
      ...(componentData.responseTime !== undefined && {
        responseTime: componentData.responseTime,
      }),
    };
  },

  /**
   * Transforms API circuit breaker state string to domain CircuitBreakerState type.
   * @param state - The circuit breaker state string from the API.
   * @returns The transformed CircuitBreakerState.
   */
  transformCircuitBreakerState(state: string): CircuitBreakerState {
    switch (state) {
      case 'CLOSED':
        return 'CLOSED';
      case 'OPEN':
        return 'OPEN';
      case 'HALF_OPEN':
        return 'HALF_OPEN';
      default:
        // Defensive programming: default to OPEN for unknown states
        return 'OPEN';
    }
  },

  /**
   * Transforms API alert severity string to domain AlertSeverity type.
   * @param severity - The alert severity string from the API.
   * @returns The transformed AlertSeverity.
   */
  transformAlertSeverity(severity: string): AlertSeverity {
    switch (severity) {
      case 'low':
        return 'low';
      case 'medium':
        return 'medium';
      case 'high':
        return 'high';
      case 'critical':
        return 'critical';
      default:
        // Defensive programming: default to medium for unknown severities
        return 'medium';
    }
  },

  /**
   * Transforms API alert status string to domain AlertStatus type.
   * @param status - The alert status string from the API.
   * @returns The transformed AlertStatus.
   */
  transformAlertStatus(status: string): AlertStatus {
    switch (status) {
      case 'active':
        return 'active';
      case 'acknowledged':
        return 'acknowledged';
      case 'resolved':
        return 'resolved';
      default:
        // Defensive programming: default to active for unknown statuses
        return 'active';
    }
  },
};
