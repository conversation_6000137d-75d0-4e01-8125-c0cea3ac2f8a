/**
 * @file Vehicle Information Card Component
 * @description Reusable component for displaying vehicle information in service record contexts
 * Follows SOLID principles with single responsibility and DRY approach
 */

'use client';

import React from 'react';
import Link from 'next/link';
import { Car, ExternalLink } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { useVehicleInfo, type VehicleInfo } from '@/hooks/api/useVehicleInfo';

interface VehicleInfoCardProps {
  vehicleId: number | null | undefined;
  showViewButton?: boolean;
  className?: string;
}

interface VehicleInfoDisplayProps {
  vehicleInfo: VehicleInfo;
  showViewButton?: boolean;
}

/**
 * Component to display vehicle information
 */
function VehicleInfoDisplay({ vehicleInfo, showViewButton = true }: VehicleInfoDisplayProps) {
  return (
    <>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Car className="h-5 w-5" />
          Vehicle Information
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 gap-3">
          <div>
            <p className="text-sm font-medium text-muted-foreground">Make & Model</p>
            <p className="font-semibold">
              {vehicleInfo.make} {vehicleInfo.model}
            </p>
          </div>
          
          <div>
            <p className="text-sm font-medium text-muted-foreground">Year</p>
            <p className="font-semibold">{vehicleInfo.year}</p>
          </div>
          
          <div>
            <p className="text-sm font-medium text-muted-foreground">License Plate</p>
            <p className="font-semibold">{vehicleInfo.licensePlate}</p>
          </div>
          
          {vehicleInfo.color && (
            <div>
              <p className="text-sm font-medium text-muted-foreground">Color</p>
              <p className="font-semibold">{vehicleInfo.color}</p>
            </div>
          )}
        </div>
        
        {showViewButton && (
          <Button asChild variant="outline" className="w-full">
            <Link href={`/vehicles/${vehicleInfo.id}`}>
              <Car className="mr-2 h-4 w-4" />
              View Vehicle Details
              <ExternalLink className="ml-2 h-4 w-4" />
            </Link>
          </Button>
        )}
      </CardContent>
    </>
  );
}

/**
 * Loading state component
 */
function VehicleInfoLoading() {
  return (
    <>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Car className="h-5 w-5" />
          Vehicle Information
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 gap-3">
          <div>
            <p className="text-sm font-medium text-muted-foreground">Make & Model</p>
            <Skeleton className="h-6 w-32" />
          </div>
          <div>
            <p className="text-sm font-medium text-muted-foreground">Year</p>
            <Skeleton className="h-6 w-16" />
          </div>
          <div>
            <p className="text-sm font-medium text-muted-foreground">License Plate</p>
            <Skeleton className="h-6 w-24" />
          </div>
        </div>
        <Skeleton className="h-10 w-full" />
      </CardContent>
    </>
  );
}

/**
 * Error state component
 */
function VehicleInfoError() {
  return (
    <>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Car className="h-5 w-5" />
          Vehicle Information
        </CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-sm text-muted-foreground">
          Unable to load vehicle information
        </p>
      </CardContent>
    </>
  );
}

/**
 * Main Vehicle Information Card Component
 */
export function VehicleInfoCard({ 
  vehicleId, 
  showViewButton = true, 
  className 
}: VehicleInfoCardProps) {
  const { vehicleInfo, isLoading, error } = useVehicleInfo(vehicleId);

  return (
    <Card className={className}>
      {isLoading && <VehicleInfoLoading />}
      {error && <VehicleInfoError />}
      {vehicleInfo && (
        <VehicleInfoDisplay 
          vehicleInfo={vehicleInfo} 
          showViewButton={showViewButton} 
        />
      )}
    </Card>
  );
}

export default VehicleInfoCard;
