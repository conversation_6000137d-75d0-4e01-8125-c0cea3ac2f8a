/**
 * @file Secure API Example Component
 * @module api/security/examples/SecureApiExample
 * 
 * Phase 3: React Integration Layer Example
 * Demonstrates how to use the new secure API architecture with clear naming conventions
 */

'use client';

import React, { useState } from 'react';
import { SecurityConfigProvider } from '../providers/SecurityConfigProvider';
import { useSecureApiClient, useSecureApiReplacement } from '../hooks/useSecureApiClient';

/**
 * Example component showing the new useSecureApiClient hook
 */
function SecureApiClientExample() {
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Use the full-featured hook
  const {
    secureRequest,
    isAuthenticated,
    hasValidToken,
    securityStatus,
    refreshSecurityFeatures,
    isInitialized,
    client,
  } = useSecureApiClient({
    enableLogging: true,
    onSecurityError: (err) => {
      console.error('Security error:', err);
      setError(err.message);
    },
  });

  const handleApiCall = async () => {
    if (!isAuthenticated) {
      setError('Not authenticated');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await secureRequest({
        url: '/api/test',
        method: 'GET',
      });
      setData(response.data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Request failed');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-4 border rounded-lg">
      <h3 className="text-lg font-semibold mb-4">Secure API Client Example</h3>
      
      <div className="space-y-2 mb-4">
        <p>Authentication Status: {isAuthenticated ? '✅ Authenticated' : '❌ Not Authenticated'}</p>
        <p>Token Valid: {hasValidToken ? '✅ Valid' : '❌ Invalid'}</p>
        <p>Client Initialized: {isInitialized ? '✅ Ready' : '❌ Not Ready'}</p>
        <p>Threat Level: {securityStatus?.threatLevel || 'Unknown'}</p>
      </div>

      <div className="space-x-2 mb-4">
        <button
          onClick={handleApiCall}
          disabled={loading || !isAuthenticated}
          className="px-4 py-2 bg-blue-500 text-white rounded disabled:opacity-50"
        >
          {loading ? 'Loading...' : 'Make Secure API Call'}
        </button>
        
        <button
          onClick={refreshSecurityFeatures}
          className="px-4 py-2 bg-green-500 text-white rounded"
        >
          Refresh Security
        </button>
      </div>

      {error && (
        <div className="p-2 bg-red-100 border border-red-400 text-red-700 rounded">
          Error: {error}
        </div>
      )}

      {data && (
        <div className="p-2 bg-green-100 border border-green-400 text-green-700 rounded">
          <pre>{JSON.stringify(data, null, 2)}</pre>
        </div>
      )}
    </div>
  );
}

/**
 * Example component showing the backward-compatible hook
 */
function BackwardCompatibleExample() {
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  // Use the backward-compatible hook (drop-in replacement for useSecureApi)
  const {
    secureRequest,
    isAuthenticated,
    hasValidToken,
    sanitizeInput,
  } = useSecureApiReplacement();

  const handleLegacyApiCall = async () => {
    setLoading(true);
    try {
      // This works exactly like the old useSecureApi
      const response = await secureRequest({
        url: '/api/legacy-endpoint',
        method: 'POST',
        data: sanitizeInput({ message: 'Hello from new architecture!' }),
      });
      setData(response.data);
    } catch (error) {
      console.error('Legacy API call failed:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-4 border rounded-lg">
      <h3 className="text-lg font-semibold mb-4">Backward Compatible Example</h3>
      
      <p className="mb-4 text-sm text-gray-600">
        This example shows how the new architecture maintains backward compatibility
        with the existing useSecureApi interface.
      </p>

      <div className="space-y-2 mb-4">
        <p>Authenticated: {isAuthenticated ? 'Yes' : 'No'}</p>
        <p>Valid Token: {hasValidToken ? 'Yes' : 'No'}</p>
      </div>

      <button
        onClick={handleLegacyApiCall}
        disabled={loading || !isAuthenticated}
        className="px-4 py-2 bg-purple-500 text-white rounded disabled:opacity-50"
      >
        {loading ? 'Loading...' : 'Legacy API Call'}
      </button>

      {data && (
        <div className="mt-4 p-2 bg-blue-100 border border-blue-400 text-blue-700 rounded">
          <pre>{JSON.stringify(data, null, 2)}</pre>
        </div>
      )}
    </div>
  );
}

/**
 * Main example component with SecurityConfigProvider
 */
export function SecureApiExample() {
  return (
    <SecurityConfigProvider
      initialConfig={{
        // Custom security configuration
        csrf: {
          enabled: true,
          tokenHeader: 'X-CSRF-Token',
          excludePaths: ['/api/public'],
        },
        authentication: {
          enabled: true,
          autoLogout: true,
          redirectOnFailure: true,
        },
      }}
      configVersion="1.0.0"
      validateConfig={true}
      onConfigChange={(config) => {
        console.log('Security config changed:', config);
      }}
    >
      <div className="space-y-6 p-6">
        <h2 className="text-2xl font-bold">Phase 3: React Integration Layer Examples</h2>
        
        <div className="text-sm text-gray-600">
          <p>These examples demonstrate the new secure API architecture with:</p>
          <ul className="list-disc list-inside mt-2 space-y-1">
            <li>Enhanced SecureApiClient with SecurityComposer integration</li>
            <li>SecurityConfigProvider for centralized configuration</li>
            <li>Clear naming conventions (useSecureApiClient, useSecureApiReplacement)</li>
            <li>Backward compatibility with existing useSecureApi interface</li>
            <li>Enhanced security features and monitoring</li>
          </ul>
        </div>

        <SecureApiClientExample />
        <BackwardCompatibleExample />
      </div>
    </SecurityConfigProvider>
  );
}
