/**
 * @file CSV Export Service
 * @description Production-ready CSV export service for reporting data
 */

import type {
  ICSVExportService,
  CSVExportOptions,
  ExportResult,
  ExportMetadata,
  EntityType,
  ComprehensiveReportData,
} from '../../types/export';
import type { ReportingFilters } from '../../types/reporting';

export class CSVExportService implements ICSVExportService {
  private readonly defaultOptions: Partial<CSVExportOptions> = {
    delimiter: ',',
    encoding: 'utf-8',
    includeHeaders: true,
    flattenNested: true,
  };

  async generateComprehensiveReport(
    data: ComprehensiveReportData
  ): Promise<ExportResult> {
    try {
      const csvContent = this.generateComprehensiveCSV(data);
      const filename = `comprehensive-report-${Date.now()}.csv`;

      return this.createExportResult(csvContent, filename, data);
    } catch (error) {
      throw new Error(`Failed to generate comprehensive CSV report: ${error}`);
    }
  }

  async generateEntityReport(
    entityType: EntityType,
    data: any,
    options?: Partial<CSVExportOptions>
  ): Promise<ExportResult> {
    try {
      const mergedOptions = { ...this.defaultOptions, ...options };
      let csvContent: string;

      switch (entityType) {
        case 'delegation':
          csvContent = this.generateDelegationCSV(data, mergedOptions);
          break;
        case 'task':
          csvContent = this.generateTaskCSV(data, mergedOptions);
          break;
        case 'vehicle':
          csvContent = this.generateVehicleCSV(data, mergedOptions);
          break;
        case 'employee':
          csvContent = this.generateEmployeeCSV(data, mergedOptions);
          break;
        default:
          throw new Error(`Unsupported entity type: ${entityType}`);
      }

      const filename = `${entityType}-report-${Date.now()}.csv`;
      return this.createExportResult(csvContent, filename, data);
    } catch (error) {
      throw new Error(`Failed to generate ${entityType} CSV report: ${error}`);
    }
  }

  private generateComprehensiveCSV(data: ComprehensiveReportData): string {
    const sections: string[] = [];

    // Delegation summary
    if (data.delegations?.summary) {
      sections.push('DELEGATION SUMMARY');
      sections.push('Metric,Value');
      sections.push(
        `Total Delegations,${data.delegations.summary.totalDelegations || 0}`
      );
      sections.push(
        `Active Delegations,${data.delegations.summary.activeDelegations || 0}`
      );
      sections.push(
        `Completed Delegations,${data.delegations.summary.completedDelegations || 0}`
      );
      sections.push(
        `Completion Rate,${data.delegations.summary.completionRate?.toFixed(2) || 0}%`
      );
      sections.push('');
    }

    return sections.join('\n');
  }

  private generateDelegationCSV(
    data: any,
    options: Partial<CSVExportOptions>
  ): string {
    const delimiter = options.delimiter || ',';
    const headers = [
      'ID',
      'Status',
      'Created Date',
      'Due Date',
      'Assigned To',
      'Priority',
    ];
    const rows: string[] = [];

    if (options.includeHeaders) {
      rows.push(headers.join(delimiter));
    }

    if (Array.isArray(data)) {
      data.forEach((delegation: any) => {
        const row = [
          delegation.id || '',
          delegation.status || '',
          delegation.createdAt || '',
          delegation.dueDate || '',
          delegation.assignedTo || '',
          delegation.priority || '',
        ];
        rows.push(row.join(delimiter));
      });
    }

    return rows.join('\n');
  }

  private generateTaskCSV(
    data: any,
    options: Partial<CSVExportOptions>
  ): string {
    const delimiter = options.delimiter || ',';
    const headers = [
      'ID',
      'Title',
      'Status',
      'Priority',
      'Assigned To',
      'Due Date',
    ];
    const rows: string[] = [];

    if (options.includeHeaders) {
      rows.push(headers.join(delimiter));
    }

    if (Array.isArray(data)) {
      data.forEach((task: any) => {
        const row = [
          task.id || '',
          task.title || '',
          task.status || '',
          task.priority || '',
          task.assignedTo || '',
          task.dueDate || '',
        ];
        rows.push(row.join(delimiter));
      });
    }

    return rows.join('\n');
  }

  private generateVehicleCSV(
    data: any,
    options: Partial<CSVExportOptions>
  ): string {
    const delimiter = options.delimiter || ',';
    const headers = [
      'ID',
      'Make',
      'Model',
      'Year',
      'Status',
      'Utilization Rate',
    ];
    const rows: string[] = [];

    if (options.includeHeaders) {
      rows.push(headers.join(delimiter));
    }

    if (Array.isArray(data)) {
      data.forEach((vehicle: any) => {
        const row = [
          vehicle.id || '',
          vehicle.make || '',
          vehicle.model || '',
          vehicle.year || '',
          vehicle.status || '',
          vehicle.utilizationRate?.toFixed(2) || '0',
        ];
        rows.push(row.join(delimiter));
      });
    }

    return rows.join('\n');
  }

  private generateEmployeeCSV(
    data: any,
    options: Partial<CSVExportOptions>
  ): string {
    const delimiter = options.delimiter || ',';
    const headers = [
      'ID',
      'Name',
      'Department',
      'Role',
      'Tasks Completed',
      'Performance Score',
    ];
    const rows: string[] = [];

    if (options.includeHeaders) {
      rows.push(headers.join(delimiter));
    }

    if (Array.isArray(data)) {
      data.forEach((employee: any) => {
        const row = [
          employee.id || '',
          employee.name || '',
          employee.department || '',
          employee.role || '',
          employee.tasksCompleted || '0',
          employee.performanceScore?.toFixed(2) || '0',
        ];
        rows.push(row.join(delimiter));
      });
    }

    return rows.join('\n');
  }

  private createExportResult(
    csvContent: string,
    filename: string,
    data: any
  ): ExportResult {
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const downloadUrl = URL.createObjectURL(blob);

    const metadata: ExportMetadata = {
      generatedAt: new Date(),
      generatedBy: 'system',
      recordCount: Array.isArray(data) ? data.length : 1,
      entityTypes: [],
      filters: {
        dateRange: { from: new Date(), to: new Date() },
        status: [],
        locations: [],
        employees: [],
        vehicles: [],
      },
      processingTime: 0,
    };

    return {
      id: `csv-${Date.now()}`,
      filename,
      format: 'csv',
      fileSize: blob.size,
      downloadUrl,
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
      metadata,
    };
  }
}
