/**
 * @file Modal management hook using Zustand UiStore
 * @module hooks/useModal
 */

import { useCallback } from 'react';

import { useUiStore } from '@/lib/stores/zustand/uiStore';

/**
 * Modal content types for WorkHub
 */
export type ModalContent =
  | 'delegation-form'
  | 'employee-profile'
  | 'login'
  | 'settings'
  | 'signup'
  | 'task-assignment'
  | 'vehicle-details'
  | null;

/**
 * Hook for modal management
 * Provides convenient methods for modal state control
 */
export const useModal = () => {
  const isModalOpen = useUiStore(state => state.isModalOpen);
  const modalContent = useUiStore(state => state.modalContent);
  const openModal = useUiStore(state => state.openModal);
  const closeModal = useUiStore(state => state.closeModal);

  /**
   * Open login modal
   */
  const openLoginModal = useCallback(() => {
    openModal('login');
  }, [openModal]);

  /**
   * Open signup modal
   */
  const openSignupModal = useCallback(() => {
    openModal('signup');
  }, [openModal]);

  /**
   * Open settings modal
   */
  const openSettingsModal = useCallback(() => {
    openModal('settings');
  }, [openModal]);

  /**
   * Open delegation form modal
   */
  const openDelegationFormModal = useCallback(() => {
    openModal('delegation-form');
  }, [openModal]);

  /**
   * Open vehicle details modal
   */
  const openVehicleDetailsModal = useCallback(() => {
    openModal('vehicle-details');
  }, [openModal]);

  /**
   * Open task assignment modal
   */
  const openTaskAssignmentModal = useCallback(() => {
    openModal('task-assignment');
  }, [openModal]);

  /**
   * Open employee profile modal
   */
  const openEmployeeProfileModal = useCallback(() => {
    openModal('employee-profile');
  }, [openModal]);

  /**
   * Check if a specific modal is open
   */
  const isModalOfTypeOpen = useCallback(
    (type: ModalContent) => {
      return isModalOpen && modalContent === type;
    },
    [isModalOpen, modalContent]
  );

  /**
   * Get modal-specific CSS classes
   */
  const getModalClasses = useCallback(() => {
    return {
      backdrop: 'modal-backdrop',
      container: isModalOpen
        ? 'modal-container-visible'
        : 'modal-container-hidden',
      content: `modal-content modal-content-${modalContent || 'default'}`,
      overlay: isModalOpen ? 'modal-overlay-visible' : 'modal-overlay-hidden',
    };
  }, [isModalOpen, modalContent]);

  /**
   * Get modal accessibility attributes
   */
  const getModalAriaAttributes = useCallback(() => {
    return {
      'aria-describedby': modalContent
        ? `${modalContent}-modal-description`
        : undefined,
      'aria-hidden': !isModalOpen,
      'aria-labelledby': modalContent
        ? `${modalContent}-modal-title`
        : undefined,
      'aria-modal': isModalOpen,
      role: 'dialog',
    };
  }, [isModalOpen, modalContent]);

  /**
   * Handle escape key press to close modal
   */
  const handleEscapeKey = useCallback(
    (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isModalOpen) {
        closeModal();
      }
    },
    [isModalOpen, closeModal]
  );

  /**
   * Handle backdrop click to close modal
   */
  const handleBackdropClick = useCallback(
    (event: React.MouseEvent) => {
      if (event.target === event.currentTarget && isModalOpen) {
        closeModal();
      }
    },
    [isModalOpen, closeModal]
  );

  /**
   * Get modal title based on content type
   */
  const getModalTitle = useCallback(() => {
    switch (modalContent) {
      case 'delegation-form': {
        return 'Create Delegation';
      }
      case 'employee-profile': {
        return 'Employee Profile';
      }
      case 'login': {
        return 'Sign In';
      }
      case 'settings': {
        return 'Settings';
      }
      case 'signup': {
        return 'Create Account';
      }
      case 'task-assignment': {
        return 'Assign Task';
      }
      case 'vehicle-details': {
        return 'Vehicle Details';
      }
      default: {
        return 'Modal';
      }
    }
  }, [modalContent]);

  /**
   * Check if modal content is WorkHub-specific
   */
  const isWorkHubModal = useCallback(() => {
    return [
      'delegation-form',
      'employee-profile',
      'task-assignment',
      'vehicle-details',
    ].includes(modalContent || '');
  }, [modalContent]);

  return {
    closeModal,
    getModalAriaAttributes,
    getModalClasses,

    getModalTitle,
    handleBackdropClick,
    handleEscapeKey,
    // Utilities
    isModalOfTypeOpen,
    // State
    isModalOpen,
    isWorkHubModal: isWorkHubModal(),
    modalContent,
    openDelegationFormModal,
    openEmployeeProfileModal,

    openLoginModal,
    // Actions
    openModal,
    openSettingsModal,
    openSignupModal,
    openTaskAssignmentModal,
    openVehicleDetailsModal,
  };
};
