'use client';

import { format, parseISO } from 'date-fns';
import {
  <PERSON>ertTriangle,
  ArrowLeft,
  Briefcase,
  Building,
  CalendarDays,
  Car,
  CircleHelp,
  Clock,
  Edit,
  Info,
  Mail,
  MapPin,
  Phone,
  RefreshCw,
  ShieldCheck,
  Trash2,
  UserCircle2,
  <PERSON>Round,
  Wrench,
} from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { useParams, useRouter } from 'next/navigation';
import React from 'react';

import type { Employee } from '@/lib/types/domain';

import DriverInformation from '@/components/drivers/DriverInformation';
import { ActionButton } from '@/components/ui/action-button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AppBreadcrumb } from '@/components/ui/app-breadcrumb';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  <PERSON><PERSON><PERSON>er,
  CardTitle,
} from '@/components/ui/card';
import {
  DataLoader,
  ErrorDisplay,
  SkeletonLoader,
} from '@/components/ui/loading';
import { PageHeader } from '@/components/ui/PageHeader';
import { Separator } from '@/components/ui/separator';
import { employeeToast } from '@/lib/services/toastService';
import {
  useDeleteEmployee,
  useEmployee,
} from '@/lib/stores/queries/useEmployees';
import { cn } from '@/lib/utils';

const getStatusColor = (status?: Employee['status']) => {
  if (!status) return 'bg-gray-500/20 text-gray-700 border-gray-500/30';
  switch (status) {
    case 'Active': {
      return 'bg-green-500/20 text-green-700 border-green-500/30 dark:text-green-400 dark:bg-green-500/10 dark:border-green-500/20';
    }
    case 'On_Leave': {
      return 'bg-yellow-500/20 text-yellow-700 border-yellow-500/30 dark:text-yellow-400 dark:bg-yellow-500/10 dark:border-yellow-500/20';
    }
    case 'Terminated': {
      return 'bg-red-500/20 text-red-700 border-red-500/30 dark:text-red-400 dark:bg-red-500/10 dark:border-red-500/20';
    }
    default: {
      return 'bg-gray-500/20 text-gray-700 border-gray-500/30 dark:text-gray-400 dark:bg-gray-500/10 dark:border-gray-500/20';
    }
  }
};

const getAvailabilityColor = (availability?: Employee['availability']) => {
  if (!availability) return 'bg-gray-500/20 text-gray-700 border-gray-500/30';
  switch (availability) {
    case 'Busy': {
      return 'bg-red-500/20 text-red-700 border-red-500/30 dark:text-red-400 dark:bg-red-500/10 dark:border-red-500/20';
    }
    case 'Off_Shift': {
      return 'bg-gray-500/20 text-gray-700 border-gray-500/30 dark:text-gray-400 dark:bg-gray-500/10 dark:border-gray-500/20';
    }
    case 'On_Break': {
      return 'bg-yellow-500/20 text-yellow-700 border-yellow-500/30 dark:text-yellow-400 dark:bg-yellow-500/10 dark:border-yellow-500/20';
    }
    case 'On_Shift': {
      return 'bg-green-500/20 text-green-700 border-green-500/30 dark:text-green-400 dark:bg-green-500/10 dark:border-green-500/20';
    }
    default: {
      return 'bg-gray-500/20 text-gray-700 border-gray-500/30 dark:text-gray-400 dark:bg-gray-500/10 dark:border-gray-500/20';
    }
  }
};

const EmployeeDetailPage = () => {
  const router = useRouter();
  const params = useParams();

  const employeeIdStr = params?.id as string;
  const employeeId = Number.parseInt(employeeIdStr, 10);

  // Use React Query hooks for data fetching
  const {
    data: employee,
    error: employeeError,
    isLoading: isLoadingEmployee,
    refetch: refetchEmployee,
  } = useEmployee(employeeIdStr);

  const { isPending: isDeleting, mutate: deleteEmployeeMutation } =
    useDeleteEmployee();

  // Note: Vehicle assignments are now context-specific (per task/delegation)

  const handleDelete = async () => {
    if (isNaN(employeeId) || !employee) {
      employeeToast.entityDeletionError(
        'Invalid employee ID or employee data missing.'
      );
      return;
    }

    // Enhanced confirmation dialog with employee name
    const employeeName = employee.name || 'Employee';
    if (
      !globalThis.confirm(
        `Are you sure you want to permanently delete ${employeeName}?\n\nThis action cannot be undone and will remove all employee data from the system.`
      )
    ) {
      return;
    }

    // Use the mutate function from the hook
    deleteEmployeeMutation(employeeIdStr, {
      onError: (err: any) => {
        console.error('Failed to delete employee:', err);
        // Enhanced error message based on error type
        let errorMessage = 'Could not delete employee. Please try again.';
        if (err.message?.includes('Network error')) {
          errorMessage =
            'Network error. Please check your connection and try again.';
        } else if (err.message?.includes('404')) {
          errorMessage = 'Employee not found or already deleted.';
        } else if (err.message?.includes('403')) {
          errorMessage = 'You do not have permission to delete this employee.';
        } else if (err.message?.includes('500')) {
          errorMessage =
            'Server error. Please contact support if this persists.';
        } else if (err.response?.data?.error) {
          errorMessage = err.response.data.error;
        } else if (err.message) {
          errorMessage = err.message;
        }
        // setError(errorMessage); // No longer need local error state
        employeeToast.entityDeletionError(errorMessage);
      },
      onSuccess: () => {
        const deletedEmployee = { name: employeeName };
        employeeToast.entityDeleted(deletedEmployee);
        // Navigate back to employees list
        router.push('/employees');
      },
    });
  };

  const DetailItem: React.FC<{
    icon: React.ElementType;
    label: string;
    value?: null | number | React.ReactNode | string;
    valueClassName?: string;
  }> = ({ icon: Icon, label, value, valueClassName }) =>
    value || value === 0 ? (
      <div className="flex items-start space-x-3">
        <Icon className="mr-3 mt-0.5 size-5 shrink-0 text-accent" />
        <div>
          <p className="text-sm text-muted-foreground">{label}</p>
          <p
            className={cn(
              'text-base font-semibold text-foreground',
              valueClassName
            )}
          >
            {value}
          </p>
        </div>
      </div>
    ) : null;

  return (
    <div className="container mx-auto space-y-6 py-8">
      <AppBreadcrumb />
      <DataLoader
        data={employee}
        emptyComponent={
          <div className="py-10 text-center">
            <PageHeader icon={AlertTriangle} title="Employee Not Found" />
            <p className="mb-4">The requested employee could not be found.</p>
            <ActionButton
              actionType="primary"
              icon={<ArrowLeft className="size-4" />}
              onClick={() => router.push('/employees')}
            >
              Back to Employees
            </ActionButton>
          </div>
        }
        error={employeeError ? employeeError.message : null}
        isLoading={isLoadingEmployee}
        loadingComponent={
          <div className="space-y-6">
            <PageHeader icon={UsersRound} title="Loading Employee..." />
            <SkeletonLoader count={1} variant="card" />
          </div>
        }
        onRetry={() => {
          refetchEmployee();
        }}
      >
        {loadedEmployee => {
          const employeeName = loadedEmployee.name || 'Employee';
          return (
            <>
              <PageHeader
                description={`Details for Employee ID: ${loadedEmployee.id}`}
                icon={UsersRound}
                title={employeeName}
              />

              {employeeError && (
                <Alert variant="destructive">
                  <CircleHelp className="size-5" />
                  <AlertTitle>An Error Occurred</AlertTitle>
                  <AlertDescription>{employeeError.message}</AlertDescription>
                </Alert>
              )}

              <Card className="shadow-lg">
                <CardHeader className="p-5">
                  <div className="flex flex-col items-start justify-between gap-4 sm:flex-row sm:items-center">
                    <div className="flex items-center gap-4">
                      <div className="relative flex size-20 items-center justify-center overflow-hidden rounded-full bg-muted ring-2 ring-primary/30">
                        {loadedEmployee.profileImageUrl ? (
                          <Image
                            alt={employeeName}
                            data-ai-hint="employee profile"
                            layout="fill"
                            objectFit="cover"
                            src={loadedEmployee.profileImageUrl}
                          />
                        ) : (
                          <UserCircle2 className="size-12 text-muted-foreground" />
                        )}
                      </div>
                      <div>
                        <CardTitle className="text-2xl font-bold text-primary">
                          {employeeName}
                        </CardTitle>
                        <CardDescription className="text-sm">
                          {loadedEmployee.position} -{' '}
                          {loadedEmployee.department}
                        </CardDescription>
                        <div className="mt-1 flex items-center gap-2">
                          <Badge
                            className={cn(
                              'text-xs',
                              getStatusColor(loadedEmployee.status)
                            )}
                          >
                            {loadedEmployee.status}
                          </Badge>
                          {loadedEmployee.role === 'driver' &&
                            loadedEmployee.availability && (
                              <Badge
                                className={cn(
                                  'text-xs',
                                  getAvailabilityColor(
                                    loadedEmployee.availability
                                  )
                                )}
                              >
                                {loadedEmployee.availability?.replace('_', ' ')}
                              </Badge>
                            )}
                        </div>
                      </div>
                    </div>
                    <div className="flex flex-wrap gap-2 self-start sm:self-center">
                      <ActionButton
                        actionType="tertiary"
                        className="group relative"
                        disabled={isLoadingEmployee}
                        onClick={() => {
                          refetchEmployee();
                        }}
                        size="icon"
                        title="Refresh Employee Data"
                      >
                        <RefreshCw
                          className={cn(
                            'h-4 w-4',
                            isLoadingEmployee && 'animate-spin'
                          )}
                        />
                        <span className="sr-only">
                          {isLoadingEmployee
                            ? 'Refreshing...'
                            : 'Refresh employee data'}
                        </span>
                      </ActionButton>
                      <ActionButton
                        actionType="secondary"
                        asChild
                        className="group relative"
                        size="icon"
                        title="Edit Employee Details"
                      >
                        <Link
                          className="inline-flex items-center justify-center"
                          href={`/employees/${loadedEmployee.id}/edit`}
                        >
                          <Edit className="size-4" />
                          <span className="sr-only">Edit {employeeName}</span>
                        </Link>
                      </ActionButton>
                      <ActionButton
                        actionType="danger"
                        className="group relative"
                        disabled={isDeleting}
                        isLoading={isDeleting}
                        loadingText=""
                        onClick={handleDelete}
                        size="icon"
                        title={`Delete ${employeeName}`}
                      >
                        <Trash2 className="size-4" />
                        <span className="sr-only">
                          {isDeleting
                            ? 'Deleting...'
                            : `Delete ${employeeName}`}
                        </span>
                      </ActionButton>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="mt-2 space-y-8 p-5">
                  <section>
                    <h3 className="mb-3 flex items-center text-xl font-semibold text-primary">
                      <UserCircle2 className="mr-2 size-5 text-accent" />{' '}
                      Contact & Employment
                    </h3>
                    <div className="grid grid-cols-1 gap-x-6 gap-y-4 md:grid-cols-2 lg:grid-cols-3">
                      <DetailItem
                        icon={Mail}
                        label="Email / Primary Contact"
                        value={loadedEmployee.contactEmail}
                      />
                      {loadedEmployee.contactMobile && (
                        <DetailItem
                          icon={Phone}
                          label="Mobile"
                          value={loadedEmployee.contactMobile}
                        />
                      )}
                      {loadedEmployee.contactPhone &&
                        !loadedEmployee.contactMobile && (
                          <DetailItem
                            icon={Phone}
                            label="Phone"
                            value={loadedEmployee.contactPhone}
                          />
                        )}
                      <DetailItem
                        icon={CalendarDays}
                        label="Hire Date"
                        value={
                          loadedEmployee.hireDate
                            ? format(
                                parseISO(loadedEmployee.hireDate),
                                'MMMM d, yyyy'
                              )
                            : 'N/A'
                        }
                      />
                      <DetailItem
                        icon={Briefcase}
                        label="Role"
                        value={
                          loadedEmployee.role
                            ? loadedEmployee.role.charAt(0).toUpperCase() +
                              loadedEmployee.role.slice(1).replace('_', ' ')
                            : 'N/A'
                        }
                      />
                      <DetailItem
                        icon={Building}
                        label="Department"
                        value={loadedEmployee.department}
                      />
                      <DetailItem
                        icon={ShieldCheck}
                        label="Employee ID (System)"
                        value={loadedEmployee.id.toString()}
                      />
                    </div>
                  </section>

                  {(loadedEmployee.role === 'driver' ||
                    (loadedEmployee.skills &&
                      loadedEmployee.skills.length > 0) ||
                    loadedEmployee.shiftSchedule ||
                    (loadedEmployee.generalAssignments &&
                      loadedEmployee.generalAssignments.length > 0)) && (
                    <Separator className="my-4" />
                  )}

                  {loadedEmployee.role === 'driver' && (
                    <section>
                      <h3 className="mb-3 flex items-center text-xl font-semibold text-primary">
                        <Car className="mr-2 size-5 text-accent" /> Driver
                        Information
                      </h3>
                      <DriverInformation employee={loadedEmployee} />
                    </section>
                  )}

                  {(loadedEmployee.skills &&
                    loadedEmployee.skills.length > 0) ||
                  loadedEmployee.shiftSchedule ||
                  (loadedEmployee.generalAssignments &&
                    loadedEmployee.generalAssignments.length > 0) ? (
                    <section>
                      <h3 className="mb-3 flex items-center text-xl font-semibold text-primary">
                        <Wrench className="mr-2 size-5 text-accent" /> Work
                        Details
                      </h3>
                      <div className="grid grid-cols-1 gap-x-6 gap-y-4 md:grid-cols-2">
                        {loadedEmployee.skills &&
                          loadedEmployee.skills.length > 0 && (
                            <DetailItem
                              icon={Wrench}
                              label="Skills"
                              value={loadedEmployee.skills.join(', ')}
                            />
                          )}
                        <DetailItem
                          icon={Clock}
                          label="Shift Schedule"
                          value={loadedEmployee.shiftSchedule}
                        />
                        {loadedEmployee.generalAssignments &&
                          loadedEmployee.generalAssignments.length > 0 && (
                            <DetailItem
                              icon={Briefcase}
                              label="General Assignments"
                              value={loadedEmployee.generalAssignments.join(
                                ', '
                              )}
                            />
                          )}
                      </div>
                    </section>
                  ) : null}

                  {loadedEmployee.notes && (
                    <>
                      <Separator className="my-4" />
                      <section>
                        <h3 className="mb-2 flex items-center text-xl font-semibold text-primary">
                          <Info className="mr-2 size-5 text-accent" /> Notes
                        </h3>
                        <p className="whitespace-pre-wrap rounded-md bg-muted/50 p-3 text-sm text-foreground">
                          {loadedEmployee.notes}
                        </p>
                      </section>
                    </>
                  )}
                </CardContent>
                <CardFooter className="border-t p-5 pt-4 text-xs text-muted-foreground">
                  Registered:{' '}
                  {new Date(loadedEmployee.createdAt).toLocaleString()} | Last
                  updated: {new Date(loadedEmployee.updatedAt).toLocaleString()}
                </CardFooter>
              </Card>
            </>
          );
        }}
      </DataLoader>
    </div>
  );
};

export default EmployeeDetailPage;
