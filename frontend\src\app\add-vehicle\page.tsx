'use client';

import { Car } from 'lucide-react';
import { useRouter } from 'next/navigation';

import type { CreateVehicleData } from '@/lib/types/domain';

import { VehicleForm } from '@/components/features/vehicles/forms/vehicleForm';
import { useCreateVehicle } from '@/lib/stores/queries/useVehicles';

export default function AddVehiclePage() {
  const router = useRouter();
  const createVehicle = useCreateVehicle();

  const handleSubmit = async (data: CreateVehicleData) => {
    try {
      await createVehicle.mutateAsync(data);
      router.push('/vehicles');
    } catch (error) {
      console.error('Error adding vehicle:', error);
    }
  };

  return (
    <div className="space-y-6">
      <div className="mb-6 flex items-center space-x-2">
        <Car className="size-8 text-primary" />
        <div>
          <h1 className="text-3xl font-bold text-primary">Add New Vehicle</h1>
          <p className="text-muted-foreground">
            Enter the details of your new vehicle.
          </p>
        </div>
      </div>
      <VehicleForm onSubmit={handleSubmit} />
    </div>
  );
}
