'use client';

import { format, parseISO } from 'date-fns';
import {
  Calendar,
  Edit,
  Gift as GiftIcon,
  Heart,
  MoreHorizontal,
  Package,
  Trash2,
  User,
} from 'lucide-react';
import Link from 'next/link';
import { useState } from 'react';

import type { Gift } from '@/lib/types/domain';

import { ActionButton } from '@/components/ui/action-button';
import { Badge } from '@/components/ui/badge';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';

interface GiftCardProps {
  gift: Gift;
  onEdit?: (gift: Gift) => void;
  onDelete?: (gift: Gift) => void;
  showRecipient?: boolean;
  className?: string;
}

const getOccasionColor = (occasion: string | null) => {
  if (!occasion) return 'bg-gray-500/20 text-gray-700 border-gray-500/30 dark:text-gray-400 dark:bg-gray-500/10 dark:border-gray-500/20';
  
  const lowerOccasion = occasion.toLowerCase();
  
  if (lowerOccasion.includes('birthday')) {
    return 'bg-pink-500/20 text-pink-700 border-pink-500/30 dark:text-pink-400 dark:bg-pink-500/10 dark:border-pink-500/20';
  }
  if (lowerOccasion.includes('anniversary')) {
    return 'bg-purple-500/20 text-purple-700 border-purple-500/30 dark:text-purple-400 dark:bg-purple-500/10 dark:border-purple-500/20';
  }
  if (lowerOccasion.includes('christmas') || lowerOccasion.includes('holiday')) {
    return 'bg-red-500/20 text-red-700 border-red-500/30 dark:text-red-400 dark:bg-red-500/10 dark:border-red-500/20';
  }
  if (lowerOccasion.includes('graduation')) {
    return 'bg-blue-500/20 text-blue-700 border-blue-500/30 dark:text-blue-400 dark:bg-blue-500/10 dark:border-blue-500/20';
  }
  if (lowerOccasion.includes('wedding')) {
    return 'bg-yellow-500/20 text-yellow-700 border-yellow-500/30 dark:text-yellow-400 dark:bg-yellow-500/10 dark:border-yellow-500/20';
  }
  
  return 'bg-green-500/20 text-green-700 border-green-500/30 dark:text-green-400 dark:bg-green-500/10 dark:border-green-500/20';
};

export const GiftCard: React.FC<GiftCardProps> = ({
  gift,
  onEdit,
  onDelete,
  showRecipient = true,
  className,
}) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const handleEdit = () => {
    setIsMenuOpen(false);
    onEdit?.(gift);
  };

  const handleDelete = () => {
    setIsMenuOpen(false);
    onDelete?.(gift);
  };

  return (
    <Card className={cn('p-5 shadow-md hover:shadow-lg transition-shadow', className)}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-primary/10 rounded-lg">
              <GiftIcon className="h-5 w-5 text-primary" />
            </div>
            <div>
              <CardTitle className="text-lg font-semibold line-clamp-1">
                {gift.itemDescription}
              </CardTitle>
              <CardDescription className="text-sm text-muted-foreground">
                Sent by {gift.senderName}
              </CardDescription>
            </div>
          </div>
          
          {(onEdit || onDelete) && (
            <DropdownMenu open={isMenuOpen} onOpenChange={setIsMenuOpen}>
              <DropdownMenuTrigger asChild>
                <ActionButton
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0"
                  aria-label="Gift actions"
                >
                  <MoreHorizontal className="h-4 w-4" />
                </ActionButton>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                {onEdit && (
                  <DropdownMenuItem onClick={handleEdit}>
                    <Edit className="h-4 w-4 mr-2" />
                    Edit Gift
                  </DropdownMenuItem>
                )}
                {onEdit && onDelete && <DropdownMenuSeparator />}
                {onDelete && (
                  <DropdownMenuItem onClick={handleDelete} className="text-destructive">
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete Gift
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Date Sent */}
        <div className="flex items-center gap-2 text-sm">
          <Calendar className="h-4 w-4 text-muted-foreground" />
          <span className="text-muted-foreground">Sent on</span>
          <span className="font-medium">
            {format(parseISO(gift.dateSent), 'MMM dd, yyyy')}
          </span>
        </div>

        {/* Recipient */}
        {showRecipient && gift.recipient && (
          <div className="flex items-center gap-2 text-sm">
            <User className="h-4 w-4 text-muted-foreground" />
            <span className="text-muted-foreground">Recipient:</span>
            <Link 
              href={`/recipients/${gift.recipient.id}`}
              className="font-medium text-primary hover:underline"
            >
              {gift.recipient.name}
            </Link>
          </div>
        )}

        {/* Occasion */}
        {gift.occasion && (
          <div className="flex items-center gap-2">
            <Heart className="h-4 w-4 text-muted-foreground" />
            <Badge variant="outline" className={getOccasionColor(gift.occasion)}>
              {gift.occasion}
            </Badge>
          </div>
        )}

        {/* Notes */}
        {gift.notes && (
          <>
            <Separator />
            <div className="text-sm text-muted-foreground">
              <p className="line-clamp-2">{gift.notes}</p>
            </div>
          </>
        )}
      </CardContent>

      <CardFooter className="pt-4 text-xs text-muted-foreground">
        <div className="flex items-center justify-between w-full">
          <span>Created {format(parseISO(gift.createdAt), 'MMM dd, yyyy')}</span>
          {gift.updatedAt !== gift.createdAt && (
            <span>Updated {format(parseISO(gift.updatedAt), 'MMM dd, yyyy')}</span>
          )}
        </div>
      </CardFooter>
    </Card>
  );
};
