import type { Request, Response, NextFunction } from 'express';

import type { Prisma } from '../generated/prisma/index.js';
// Import event payload types
import type {
  VehicleCreatedPayload,
  VehicleDeletedPayload,
  VehicleUpdatedPayload,
} from '../types/websocketEvents.js'; // Added

import * as vehicleModel from '../models/vehicle.model.js';
// Removed: import { emitVehicleChange, SOCKET_EVENTS } from '../services/socketService.js';
import { getUnifiedWebSocketService } from '../services/UnifiedWebSocketService.js'; // Added
import { CRUD_EVENTS } from '../services/WebSocketEventManager.js'; // Added
import logger from '../utils/logger.js'; // Added logger for ws errors
import HttpError from '../utils/HttpError.js';

const CRUD_ROOM = 'entity-updates'; // General room for CRUD updates

export const createVehicle = async (
  req: Request,
  res: Response,
  next: NextFunction,
): Promise<void> => {
  try {
    const vehicleData = req.body;
    const createdVehicle = await vehicleModel.createVehicle(vehicleData);
    if (!createdVehicle) {
      next(new HttpError('Vehicle could not be created.', 400, 'VEHICLE_CREATION_FAILED'));
      return;
    }
    res.status(201).json(createdVehicle);
  } catch (error: any) {
    if (error.code === 'P2002') {
      next(new HttpError(error.message, 409, 'DUPLICATE_VEHICLE'));
    } else {
      next(
        new HttpError('Error creating vehicle', 500, 'VEHICLE_CREATION_ERROR', {
          details: error.message,
        }),
      );
    }
  }
};

export const getAllVehicles = async (
  req: Request,
  res: Response,
  next: NextFunction,
): Promise<void> => {
  try {
    const vehicles = await vehicleModel.getAllVehicles();
    res.status(200).json(vehicles);
  } catch (error: any) {
    next(
      new HttpError('Error fetching vehicles', 500, 'VEHICLE_FETCH_ERROR', {
        details: error.message,
      }),
    );
  }
};

export const getVehicleById = async (
  req: Request,
  res: Response,
  next: NextFunction,
): Promise<void> => {
  const { id } = req.params;
  const vehicleId = parseInt(id, 10);
  if (isNaN(vehicleId)) {
    next(new HttpError('Invalid vehicle ID format', 400, 'INVALID_VEHICLE_ID'));
    return;
  }
  try {
    const vehicle = await vehicleModel.getVehicleById(vehicleId);
    if (vehicle) {
      res.status(200).json(vehicle);
    } else {
      next(new HttpError('Vehicle not found', 404, 'VEHICLE_NOT_FOUND'));
    }
  } catch (error: any) {
    next(
      new HttpError('Error fetching vehicle', 500, 'VEHICLE_FETCH_ERROR', {
        details: error.message,
      }),
    );
  }
};

export const updateVehicle = async (
  req: Request,
  res: Response,
  next: NextFunction,
): Promise<void> => {
  const { id } = req.params;
  const vehicleId = parseInt(id, 10);
  if (isNaN(vehicleId)) {
    next(new HttpError('Invalid vehicle ID format', 400, 'INVALID_VEHICLE_ID'));
    return;
  }
  try {
    const vehicleData = req.body;
    const updatedVehicle = await vehicleModel.updateVehicle(vehicleId, vehicleData);
    if (updatedVehicle) {
      res.status(200).json(updatedVehicle);
    } else {
      next(new HttpError('Vehicle not found to update', 404, 'VEHICLE_NOT_FOUND'));
    }
  } catch (error: any) {
    if (error.code === 'P2002') {
      next(
        new HttpError('A vehicle with this information already exists', 409, 'DUPLICATE_VEHICLE'),
      );
    } else {
      next(
        new HttpError('Error updating vehicle', 500, 'VEHICLE_UPDATE_ERROR', {
          details: error.message,
        }),
      );
    }
  }
};

export const deleteVehicle = async (
  req: Request,
  res: Response,
  next: NextFunction,
): Promise<void> => {
  const { id } = req.params;
  const vehicleId = parseInt(id, 10);
  if (isNaN(vehicleId)) {
    next(new HttpError('Invalid vehicle ID format', 400, 'INVALID_VEHICLE_ID'));
    return;
  }
  try {
    const deletedVehicle = await vehicleModel.deleteVehicle(vehicleId);
    if (deletedVehicle) {
      res.status(200).json({ message: 'Vehicle deleted successfully', vehicle: deletedVehicle });
    } else {
      next(new HttpError('Vehicle not found to delete', 404, 'VEHICLE_NOT_FOUND'));
    }
  } catch (error: any) {
    next(
      new HttpError('Error deleting vehicle', 500, 'VEHICLE_DELETE_ERROR', {
        details: error.message,
      }),
    );
  }
};
