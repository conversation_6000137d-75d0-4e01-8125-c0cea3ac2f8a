import {
  afterAll,
  afterEach,
  beforeAll,
  beforeEach,
  describe,
  expect,
  jest,
  test,
} from '@jest/globals';
// This setup file helps configure Jest for ESM modules
// Using createRequire to enable CommonJS imports in ESM context
import { createRequire } from 'node:module';

// Create require function for ESM context
const require = createRequire(import.meta.url);

// Make Jest available globally for ESM modules
// @ts-ignore - extending global object
global.jest = jest;
// @ts-ignore - extending global object
global.expect = expect;
// @ts-ignore - extending global object
global.test = test;
// @ts-ignore - extending global object
global.describe = describe;
// @ts-ignore - extending global object
global.beforeEach = beforeEach;
// @ts-ignore - extending global object
global.afterEach = afterEach;
// @ts-ignore - extending global object
global.beforeAll = beforeAll;
// @ts-ignore - extending global object
global.afterAll = afterAll;
