# Generic Dashboard Framework

A reusable, configurable dashboard framework that provides consistent UI patterns and state management across different entities in the WorkHub application.

## Features

- **Multiple View Modes**: Cards, Table, List, Calendar, Grid
- **Persistent User Preferences**: Layout, filters, and settings saved per entity
- **Advanced Filtering**: Configurable filters with multiple types
- **Responsive Design**: Mobile-first approach with flexible layouts
- **State Management**: Zustand-based stores with persistence
- **TypeScript Support**: Full type safety with generic interfaces
- **SRP & DRY Compliance**: Single responsibility and reusable components

## Quick Start

### 1. Create Entity-Specific Configuration

```typescript
import type { DashboardConfig } from '@/components/dashboard/types';
import type { YourEntity } from '@/lib/types/domain';

const yourEntityDashboardConfig: DashboardConfig<YourEntity> = {
  entityType: 'yourEntity',
  title: 'Your Entity Dashboard',
  description: 'Manage your entities effectively.',
  viewModes: ['cards', 'table', 'list'],
  defaultViewMode: 'cards',
  enableBulkActions: true,
  enableExport: true,
  refreshInterval: 30000,
};
```

### 2. Create Entity-Specific Settings Component

```typescript
import React from 'react';
import { DashboardSettings } from '@/components/dashboard/DashboardSettings';
import { useDashboardStore } from '@/lib/hooks/useDashboardStore';

export const YourEntityDashboardSettings: React.FC = () => {
  const dashboardStore = useDashboardStore('yourEntity');
  const {
    layout,
    monitoring,
    setViewMode,
    setGridColumns,
    toggleCompactMode,
    setMonitoringEnabled,
    setRefreshInterval,
    toggleAutoRefresh,
    resetSettings,
  } = dashboardStore();

  return (
    <DashboardSettings
      config={yourEntityDashboardConfig}
      entityType="yourEntity"
      layout={layout}
      monitoring={monitoring}
      setViewMode={setViewMode}
      setGridColumns={setGridColumns}
      toggleCompactMode={toggleCompactMode}
      setMonitoringEnabled={setMonitoringEnabled}
      setRefreshInterval={setRefreshInterval}
      toggleAutoRefresh={toggleAutoRefresh}
      resetSettings={resetSettings}
    />
  );
};
```

### 3. Use in Your Page Component

```typescript
import { useDashboardStore } from '@/lib/hooks/useDashboardStore';
import { YourEntityDashboardSettings } from './YourEntityDashboardSettings';

export default function YourEntityPage() {
  const dashboardStore = useDashboardStore('yourEntity');
  const { layout } = dashboardStore();

  return (
    <div>
      {/* Settings Modal */}
      <Dialog>
        <DialogTrigger asChild>
          <Button>Settings</Button>
        </DialogTrigger>
        <DialogContent>
          <YourEntityDashboardSettings />
        </DialogContent>
      </Dialog>

      {/* Dynamic Layout */}
      <div className={getLayoutClasses(layout)}>
        {data.map(item => (
          <YourEntityCard key={item.id} item={item} />
        ))}
      </div>
    </div>
  );
}
```

## Configuration Options

### DashboardConfig

```typescript
interface DashboardConfig<T = any> {
  entityType: string;           // Unique identifier for the entity
  title: string;               // Display title
  description?: string;        // Optional description
  viewModes: ViewMode[];       // Available view modes
  defaultViewMode: ViewMode;   // Default view mode
  tabs?: TabConfig[];          // Optional tab configuration
  filters?: FilterConfig[];    // Optional filter configuration
  sortOptions?: SortConfig[];  // Optional sort configuration
  enableBulkActions?: boolean; // Enable bulk operations
  enableExport?: boolean;      // Enable export functionality
  refreshInterval?: number;    // Default refresh interval
}
```

### View Modes

- `'cards'` - Card-based grid layout
- `'table'` - Table layout with rows and columns
- `'list'` - Vertical list layout
- `'calendar'` - Calendar view (for date-based entities)
- `'grid'` - Custom grid layout

### Filter Types

- `'select'` - Single selection dropdown
- `'multiselect'` - Multiple selection dropdown
- `'date'` - Date picker
- `'daterange'` - Date range picker
- `'search'` - Text search input
- `'toggle'` - Boolean toggle switch

## State Management

The framework uses Zustand stores with persistence. Each entity gets its own store instance:

```typescript
const delegationStore = useDashboardStore('delegation');
const employeeStore = useDashboardStore('employee');
const vehicleStore = useDashboardStore('vehicle');
```

### Persisted State

The following state is automatically persisted:
- Layout preferences (view mode, grid columns, compact mode)
- Monitoring settings (refresh interval, auto-refresh)
- Filter values
- Sort preferences

## Examples

### Delegation Dashboard
- **View Modes**: Cards, Table, List, Calendar
- **Filters**: Status, Location, Date Range
- **Features**: Bulk actions, Export, Real-time updates

### Employee Dashboard
- **View Modes**: Cards, Table, List
- **Filters**: Role, Department, Status
- **Features**: Bulk actions, Export

### Vehicle Dashboard
- **View Modes**: Cards, Table, List
- **Filters**: Type, Status, Owner
- **Features**: Maintenance tracking, Location monitoring

## Best Practices

1. **Entity-Specific Configuration**: Always create entity-specific config objects
2. **Consistent Naming**: Use consistent entity names across the application
3. **Type Safety**: Leverage TypeScript generics for type safety
4. **Performance**: Use appropriate refresh intervals for different data types
5. **User Experience**: Choose sensible defaults for view modes and settings

## Architecture Benefits

- **SRP Compliance**: Each component has a single, well-defined responsibility
- **DRY Principle**: Common patterns are abstracted and reused
- **Maintainability**: Consistent patterns make the codebase easier to understand
- **Scalability**: Easy to add new entities with minimal code duplication
- **User Experience**: Consistent interface across different sections
