/**
 * @file Security Module Exports - DRY Principle
 * @module lib/security
 *
 * Centralized exports for all security-related modules following DRY principles.
 * This provides a single import point for security functionality across the application.
 */

import { CSRFProtection } from './CSRFProtection';
import { InputValidator } from './InputValidator';
import { PermissionManager } from './PermissionManager';
import { SecureStorage } from './SecureStorage';
import { SessionManager } from './SessionManager';
// Import classes for SecurityUtils object
import { TokenManager } from './TokenManager';

// CSRF Protection (SRP)
export { CSRFProtection } from './CSRFProtection';
export type { CSRFToken, CSRFValidationResult } from './CSRFProtection';

// Input Validation (SRP)
export { InputValidator } from './InputValidator';
export type {
  ValidationResult,
  ValidationRule,
  ValidationSchema,
} from './InputValidator';

// Permission Management (SRP)
export { PermissionManager } from './PermissionManager';
export type {
  Permission,
  PermissionCheck,
  UserRole,
} from './PermissionManager';

// Secure Storage (SRP)
export { SecureStorage } from './SecureStorage';
export type { CookieOptions, StorageResult } from './SecureStorage';

// Session Management (SRP)
export { SessionManager } from './SessionManager';
export type {
  ConcurrentSession,
  SessionEvent,
  SessionState,
} from './SessionManager';

// Token Management (SRP)
export { TokenManager } from './TokenManager';
export type { TokenPayload, TokenValidationResult } from './TokenManager';

// Circuit breaker state management (internal)
interface CircuitBreakerState {
  attempts: number;
  lastAttemptTime: number;
  isOpen: boolean;
  activeOperations: Set<string>;
  lastOperationTime: number;
}

// Internal circuit breaker state
let circuitBreakerState: CircuitBreakerState = {
  attempts: 0,
  lastAttemptTime: 0,
  isOpen: false,
  activeOperations: new Set<string>(),
  lastOperationTime: 0,
};

// Re-export commonly used security utilities for convenience (DRY)
export const SecurityUtils = {
  attachCSRFToRequest: CSRFProtection.attachToRequest,
  clearAllCookies: SecureStorage.clearAllCookies,
  // Session utilities
  detectTimeout: SessionManager.detectTimeout,
  escapeForDisplay: InputValidator.escapeForDisplay, // RENAMED: Was sanitizeForSQL
  extractEmployeeId: TokenManager.extractEmployeeId,

  extractUserRole: TokenManager.extractUserRole,
  // CSRF utilities
  generateCSRFToken: CSRFProtection.generateToken,
  getCurrentSessionId: SessionManager.getCurrentSessionId,
  getPermissionsForRole: PermissionManager.getPermissionsForRole,
  // Storage utilities (SECURITY FIX: Updated method names for clarity)
  getSecureItem: SecureStorage.getSecureItem,

  hasMinimumRole: PermissionManager.hasMinimumRole,
  // Permission utilities
  hasPermission: PermissionManager.hasPermission,
  isCSRFRequired: CSRFProtection.isProtectionRequired,

  isStorageAvailable: SecureStorage.isAvailable,
  isUserActive: TokenManager.isUserActive,
  removeSecureItem: SecureStorage.removeSecureItem,

  sanitizeForXSS: InputValidator.sanitizeForXSS,
  setClientSideCookie: SecureStorage.setClientSideCookie, // RENAMED: Was setSecureItem
  updateActivity: SessionManager.updateActivity,
  validateCSRFToken: CSRFProtection.validateToken,

  validateEmail: InputValidator.validateEmail,
  validateObject: InputValidator.validateObject,
  validatePhone: InputValidator.validatePhone,
  // Token utilities
  validateToken: TokenManager.validateToken,
  validateURL: InputValidator.validateURL,
  validateUUID: InputValidator.validateUUID,
  // Input validation utilities
  validateValue: InputValidator.validateValue,
  willExpireSoon: TokenManager.willExpireSoon,

  // Circuit Breaker Pattern - Verification Loop Prevention
  /**
   * Check if security operations can be performed (circuit breaker check)
   * Single responsibility: Circuit breaker state validation only
   */
  canPerformSecurityCheck(): boolean {
    if (typeof window === 'undefined') return true;

    const now = Date.now();

    // Reset circuit breaker if timeout has passed
    if (
      circuitBreakerState.isOpen &&
      now - circuitBreakerState.lastAttemptTime >
        SECURITY_CONSTANTS.CIRCUIT_BREAKER_RESET_TIMEOUT
    ) {
      circuitBreakerState.isOpen = false;
      circuitBreakerState.attempts = 0;
      console.log('🔄 Circuit breaker reset - security operations enabled');
    }

    // Check if circuit is open
    if (circuitBreakerState.isOpen) {
      console.warn(
        '🔒 Circuit breaker OPEN - preventing security verification loop'
      );
      return false;
    }

    // Check cooldown period between operations
    if (
      now - circuitBreakerState.lastOperationTime <
      SECURITY_CONSTANTS.SECURITY_OPERATION_COOLDOWN
    ) {
      console.debug('⏳ Security operation cooldown active');
      return false;
    }

    return true;
  },

  /**
   * Record a security verification attempt (circuit breaker tracking)
   * Single responsibility: Attempt tracking only
   */
  recordSecurityAttempt(): void {
    if (typeof window === 'undefined') return;

    const now = Date.now();
    circuitBreakerState.attempts++;
    circuitBreakerState.lastAttemptTime = now;

    // Store in localStorage for persistence across page reloads
    try {
      localStorage.setItem(
        SECURITY_CONSTANTS.VERIFICATION_LOOP_STORAGE_KEY,
        JSON.stringify({
          attempts: circuitBreakerState.attempts,
          lastAttemptTime: now,
        })
      );
    } catch (error) {
      console.warn('Failed to store verification attempts:', error);
    }

    // Check if we should open the circuit breaker
    if (
      circuitBreakerState.attempts >=
      SECURITY_CONSTANTS.CIRCUIT_BREAKER_MAX_ATTEMPTS
    ) {
      circuitBreakerState.isOpen = true;
      console.error(
        '🚨 Security verification loop detected - circuit breaker activated'
      );

      // Force clean state reset to break the loop
      this.forceSecurityReset();
    }
  },

  /**
   * Record successful security operation (reset circuit breaker)
   * Single responsibility: Success tracking only
   */
  recordSecuritySuccess(): void {
    if (typeof window === 'undefined') return;

    circuitBreakerState.attempts = 0;
    circuitBreakerState.isOpen = false;
    circuitBreakerState.lastOperationTime = Date.now();

    // Clear stored attempts
    try {
      localStorage.removeItem(SECURITY_CONSTANTS.VERIFICATION_LOOP_STORAGE_KEY);
    } catch (error) {
      console.warn('Failed to clear verification attempts:', error);
    }
  },

  /**
   * Start a security operation (coordination)
   * Single responsibility: Operation coordination only
   */
  startSecurityOperation(operationId: string): boolean {
    if (typeof window === 'undefined') return true;

    // Check if circuit breaker allows operations
    if (!this.canPerformSecurityCheck()) {
      return false;
    }

    // Check if operation is already running
    if (circuitBreakerState.activeOperations.has(operationId)) {
      console.debug(`🔄 Security operation ${operationId} already in progress`);
      return false;
    }

    // Add to active operations
    circuitBreakerState.activeOperations.add(operationId);
    circuitBreakerState.lastOperationTime = Date.now();

    // Store active operations for coordination
    try {
      localStorage.setItem(
        SECURITY_CONSTANTS.SECURITY_OPERATIONS_STORAGE_KEY,
        JSON.stringify(Array.from(circuitBreakerState.activeOperations))
      );
    } catch (error) {
      console.warn('Failed to store active operations:', error);
    }

    console.debug(`🔐 Started security operation: ${operationId}`);
    return true;
  },

  /**
   * End a security operation (coordination)
   * Single responsibility: Operation cleanup only
   */
  endSecurityOperation(operationId: string): void {
    if (typeof window === 'undefined') return;

    circuitBreakerState.activeOperations.delete(operationId);

    // Update stored operations
    try {
      if (circuitBreakerState.activeOperations.size === 0) {
        localStorage.removeItem(
          SECURITY_CONSTANTS.SECURITY_OPERATIONS_STORAGE_KEY
        );
      } else {
        localStorage.setItem(
          SECURITY_CONSTANTS.SECURITY_OPERATIONS_STORAGE_KEY,
          JSON.stringify(Array.from(circuitBreakerState.activeOperations))
        );
      }
    } catch (error) {
      console.warn('Failed to update active operations:', error);
    }

    console.debug(`✅ Ended security operation: ${operationId}`);
  },

  /**
   * Check if circuit breaker is currently open
   * Single responsibility: Circuit state check only
   */
  isCircuitOpen(): boolean {
    return circuitBreakerState.isOpen;
  },

  /**
   * Get current circuit breaker state for monitoring
   * Single responsibility: State inspection only
   */
  getCircuitBreakerState(): {
    attemptCount: number;
    lastAttempt: number;
    isOpen: boolean;
    activeOperations: string[];
    lastOperationTime: number;
  } {
    return {
      attemptCount: circuitBreakerState.attempts,
      lastAttempt: circuitBreakerState.lastAttemptTime,
      isOpen: circuitBreakerState.isOpen,
      activeOperations: Array.from(circuitBreakerState.activeOperations),
      lastOperationTime: circuitBreakerState.lastOperationTime,
    };
  },

  /**
   * Force security state reset (emergency recovery)
   * Single responsibility: Emergency state reset only
   */
  forceSecurityReset(): void {
    if (typeof window === 'undefined') return;

    console.warn('🔧 Forcing security state reset to break verification loop');

    // Clear all security-related localStorage
    try {
      // Clear session state
      SessionManager.clearSessionState();

      // Clear circuit breaker state
      localStorage.removeItem(SECURITY_CONSTANTS.VERIFICATION_LOOP_STORAGE_KEY);
      localStorage.removeItem(
        SECURITY_CONSTANTS.SECURITY_OPERATIONS_STORAGE_KEY
      );

      // Clear logout event key
      localStorage.removeItem(SECURITY_CONSTANTS.LOGOUT_EVENT_KEY);

      // Reset internal state
      circuitBreakerState = {
        attempts: 0,
        lastAttemptTime: 0,
        isOpen: false,
        activeOperations: new Set<string>(),
        lastOperationTime: 0,
      };

      console.log('✅ Security state reset complete');

      // Redirect to login after a short delay to allow cleanup
      setTimeout(() => {
        window.location.href = '/auth-test';
      }, 1000);
    } catch (error) {
      console.error('❌ Failed to reset security state:', error);
      // Fallback: force page reload
      window.location.reload();
    }
  },

  /**
   * Reset circuit breaker state for testing purposes
   * Single responsibility: Test state reset only
   */
  resetCircuitBreakerForTesting(): void {
    if (typeof window === 'undefined') return;

    // Reset internal state without side effects
    circuitBreakerState = {
      attempts: 0,
      lastAttemptTime: 0,
      isOpen: false,
      activeOperations: new Set<string>(),
      lastOperationTime: 0,
    };

    // Clear localStorage without redirects
    try {
      localStorage.removeItem(SECURITY_CONSTANTS.VERIFICATION_LOOP_STORAGE_KEY);
      localStorage.removeItem(
        SECURITY_CONSTANTS.SECURITY_OPERATIONS_STORAGE_KEY
      );
    } catch (error) {
      // Ignore localStorage errors in tests
    }
  },

  /**
   * Initialize circuit breaker from stored state (on app startup)
   * Single responsibility: State initialization only
   */
  initializeCircuitBreaker(): void {
    if (typeof window === 'undefined') return;

    try {
      // Restore verification attempts
      const storedAttempts = localStorage.getItem(
        SECURITY_CONSTANTS.VERIFICATION_LOOP_STORAGE_KEY
      );
      if (storedAttempts) {
        const { attempts, lastAttemptTime } = JSON.parse(storedAttempts);
        const now = Date.now();

        // Check if attempts are still within the reset timeout
        if (
          now - lastAttemptTime <
          SECURITY_CONSTANTS.CIRCUIT_BREAKER_RESET_TIMEOUT
        ) {
          circuitBreakerState.attempts = attempts;
          circuitBreakerState.lastAttemptTime = lastAttemptTime;

          // Check if circuit should be open
          if (attempts >= SECURITY_CONSTANTS.CIRCUIT_BREAKER_MAX_ATTEMPTS) {
            circuitBreakerState.isOpen = true;
            console.warn('🔒 Circuit breaker restored in OPEN state');
          }
        } else {
          // Attempts are too old, clear them
          localStorage.removeItem(
            SECURITY_CONSTANTS.VERIFICATION_LOOP_STORAGE_KEY
          );
        }
      }

      // Restore active operations (clear stale ones)
      const storedOperations = localStorage.getItem(
        SECURITY_CONSTANTS.SECURITY_OPERATIONS_STORAGE_KEY
      );
      if (storedOperations) {
        // Clear stale operations on startup
        localStorage.removeItem(
          SECURITY_CONSTANTS.SECURITY_OPERATIONS_STORAGE_KEY
        );
        circuitBreakerState.activeOperations.clear();
      }

      console.log('🔐 Circuit breaker initialized');
    } catch (error) {
      console.warn('Failed to initialize circuit breaker:', error);
      // Reset to safe state on error
      circuitBreakerState = {
        attempts: 0,
        lastAttemptTime: 0,
        isOpen: false,
        activeOperations: new Set<string>(),
        lastOperationTime: 0,
      };
    }
  },
} as const;

// Security constants (DRY)
export const SECURITY_CONSTANTS = {
  DEFAULT_COOKIE_NAME: 'sb-access-token',
  LOGOUT_EVENT_KEY: 'workhub-logout-event',
  MAX_CONCURRENT_SESSIONS: 5,
  REFRESH_COOKIE_NAME: 'sb-refresh-token',
  SESSION_TIMEOUT_MINUTES: 30,
  TOKEN_EXPIRY_THRESHOLD_MINUTES: 5,
  // Circuit breaker constants
  CIRCUIT_BREAKER_MAX_ATTEMPTS: 3,
  CIRCUIT_BREAKER_RESET_TIMEOUT: 30000, // 30 seconds
  SECURITY_OPERATION_COOLDOWN: 5000, // 5 seconds between operations
  VERIFICATION_LOOP_STORAGE_KEY: 'workhub_verification_attempts',
  SECURITY_OPERATIONS_STORAGE_KEY: 'workhub_active_operations',
} as const;

// Security event types for consistent usage (DRY)
export const SECURITY_EVENTS = {
  CROSS_TAB_LOGOUT: 'cross_tab_logout',
  SECURITY_VIOLATION: 'security_violation',
  SESSION_TIMEOUT: 'session_timeout',
  TOKEN_EXPIRED: 'token_expired',
  TOKEN_REFRESH_FAILED: 'token_refresh_failed',
  UNAUTHORIZED_ACCESS: 'unauthorized_access',
} as const;

export type SecurityEventType =
  (typeof SECURITY_EVENTS)[keyof typeof SECURITY_EVENTS];
