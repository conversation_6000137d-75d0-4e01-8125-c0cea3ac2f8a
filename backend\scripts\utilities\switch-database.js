/**
 * Database Switcher Script
 *
 * This script helps switch between local PostgreSQL and Supabase
 * by updating the .env file configuration.
 *
 * Usage:
 *   node scripts/switch-database.js local   # Switch to local PostgreSQL
 *   node scripts/switch-database.js supabase # Switch to Supabase
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import readline from 'readline';

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.resolve(__dirname, '..');
const envPath = path.join(rootDir, '.env');

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

// Check if .env file exists
if (!fs.existsSync(envPath)) {
  console.error('Error: .env file not found. Please create one based on .env.example');
  process.exit(1);
}

// Read the .env file
const envContent = fs.readFileSync(envPath, 'utf8');

// Parse command line arguments
const args = process.argv.slice(2);
const targetDb = args[0]?.toLowerCase();

if (!targetDb || (targetDb !== 'local' && targetDb !== 'supabase')) {
  console.error('Error: Please specify either "local" or "supabase"');
  console.log('Usage: node scripts/switch-database.js [local|supabase]');
  process.exit(1);
}

// Function to update environment variables in the .env file
function updateEnvVar(content, varName, varValue) {
  // Check if the variable exists (uncommented)
  const regex = new RegExp(`^${varName}=.*$`, 'm');
  if (regex.test(content)) {
    // Update existing variable
    return content.replace(regex, `${varName}=${varValue}`);
  }

  // Check if the variable exists but is commented out
  const commentedRegex = new RegExp(`^# ${varName}=.*$`, 'm');
  if (commentedRegex.test(content)) {
    // Uncomment and update the variable
    return content.replace(commentedRegex, `${varName}=${varValue}`);
  }

  // Variable doesn't exist, add it at the end
  return `${content}\n${varName}=${varValue}`;
}

// Function to comment out an environment variable
function commentEnvVar(content, varName) {
  const regex = new RegExp(`^${varName}=.*$`, 'm');
  if (regex.test(content)) {
    return content.replace(regex, `# ${varName}=$1`);
  }
  return content;
}

// Switch to local PostgreSQL
function switchToLocal(content) {
  console.log('Switching to local PostgreSQL database...');

  // Update USE_SUPABASE flag
  let updatedContent = updateEnvVar(content, 'USE_SUPABASE', 'false');

  // Set DATABASE_URL to local PostgreSQL
  updatedContent = updateEnvVar(
    updatedContent,
    'DATABASE_URL',
    '**************************************/workhub_db',
  );

  // Comment out Supabase-specific variables if they exist
  const supabaseVars = ['SUPABASE_URL', 'SUPABASE_KEY'];
  for (const varName of supabaseVars) {
    const regex = new RegExp(`^${varName}=.*$`, 'm');
    if (regex.test(updatedContent)) {
      updatedContent = updatedContent.replace(regex, `# ${varName}=$1`);
    }
  }

  fs.writeFileSync(envPath, updatedContent);
  console.log('Successfully switched to local PostgreSQL database.');
  rl.close();
}

// Switch to Supabase
function switchToSupabase(content) {
  console.log('Switching to Supabase...');

  // Check if Supabase connection details are already in the .env file
  const supabaseUrlMatch = content.match(/SUPABASE_URL=https:\/\/([^.]+)\.supabase\.co/);
  const supabaseKeyMatch = content.match(/SUPABASE_KEY=([^\s]+)/);
  const databaseUrlMatch = content.match(/DATABASE_URL=postgresql:\/\/postgres[^@]*@([^:]+)/);

  if (
    supabaseUrlMatch &&
    supabaseKeyMatch &&
    databaseUrlMatch &&
    databaseUrlMatch[1].includes('supabase') &&
    !databaseUrlMatch[1].includes('[YOUR-PROJECT-REF]')
  ) {
    // Supabase is already configured, just enable it
    const updatedContent = updateEnvVar(content, 'USE_SUPABASE', 'true');
    fs.writeFileSync(envPath, updatedContent);
    console.log('Supabase is already configured. Enabled Supabase mode.');
    rl.close();
    return;
  }

  // Need to configure Supabase
  rl.question('Enter your Supabase project reference (e.g., "abcdefghijklm"): ', projectRef => {
    if (!projectRef) {
      console.error('Error: Project reference cannot be empty');
      rl.close();
      process.exit(1);
    }

    rl.question('Enter your Supabase database password: ', password => {
      if (!password) {
        console.error('Error: Password cannot be empty');
        rl.close();
        process.exit(1);
      }

      rl.question('Enter your Supabase anon key: ', anonKey => {
        if (!anonKey) {
          console.error('Error: Anon key cannot be empty');
          rl.close();
          process.exit(1);
        }

        // Update environment variables
        let updatedContent = updateEnvVar(content, 'USE_SUPABASE', 'true');
        updatedContent = updateEnvVar(
          updatedContent,
          'DATABASE_URL',
          `postgresql://postgres:${password}@db.${projectRef}.supabase.co:5432/postgres`,
        );
        updatedContent = updateEnvVar(
          updatedContent,
          'SUPABASE_URL',
          `https://${projectRef}.supabase.co`,
        );
        updatedContent = updateEnvVar(updatedContent, 'SUPABASE_KEY', anonKey);

        fs.writeFileSync(envPath, updatedContent);
        console.log('Successfully switched to Supabase.');
        rl.close();
      });
    });
  });
}

// Switch database based on target
if (targetDb === 'local') {
  switchToLocal(envContent);
} else if (targetDb === 'supabase') {
  switchToSupabase(envContent);
}
