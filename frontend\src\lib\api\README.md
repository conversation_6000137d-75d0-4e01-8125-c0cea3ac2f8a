# API Layer Documentation

This directory contains the enhanced API layer implementation for the WorkHub application with comprehensive security architecture.

## 🏗️ Architecture Overview

The API layer follows a modern, secure, and maintainable architecture with:
- **Secure API Client**: Enhanced with comprehensive security features
- **Service Layer**: Standardized services extending BaseApiService
- **Security Integration**: Centralized security with SecurityComposer
- **React Integration**: Type-safe hooks with SecurityConfigProvider
- **Migration Support**: Backward compatibility and migration utilities

## 📁 Structure

```
lib/api/
├── core/                    # Core infrastructure
│   ├── apiClient.ts        # Base HTTP client
│   ├── baseApiService.ts   # Service foundation
│   ├── interfaces.ts       # Core interfaces
│   ├── types.ts           # Type definitions
│   └── errors.ts          # Error handling
├── security/               # Security architecture
│   ├── providers/         # React security providers
│   ├── hooks/            # Security hooks (moved from hooks/security)
│   ├── migration/        # Migration utilities
│   ├── examples/         # Usage examples
│   ├── __tests__/        # Comprehensive test suite
│   ├── composer.ts       # Security orchestration
│   ├── secureApiClient.ts # Enhanced secure client
│   └── index.ts          # Security exports
├── services/              # API service implementations
│   ├── domain/           # Domain services (employees, vehicles, etc.)
│   ├── external/         # External services (flights, etc.)
│   └── admin/            # Admin services
└── utils/                # Utility functions
```

## 🚀 Quick Start

### 1. Basic API Service Usage

```typescript
import { ApiClient } from '@/lib/api/core/apiClient';
import { EmployeeApiService } from '@/lib/api/services/domain/employeeApi';

const apiClient = new ApiClient({ baseURL: '/api' });
const employeeService = new EmployeeApiService(apiClient);

// Use the service with built-in caching, error handling, and metrics
const employees = await employeeService.getAll();
```

### 2. Enhanced Secure API Usage (Recommended)

```typescript
import { useSecureApiClient } from '@/lib/api/security';

function MyComponent() {
  const { 
    secureRequest, 
    isAuthenticated, 
    securityStatus,
    refreshSecurityFeatures 
  } = useSecureApiClient();
  
  const fetchData = async () => {
    if (isAuthenticated) {
      const response = await secureRequest({
        url: '/api/data',
        method: 'GET'
      });
      return response.data;
    }
  };
  
  // Enhanced security monitoring
  console.log('Security Status:', securityStatus);
}
```

### 3. Backward Compatible Usage

```typescript
import { useSecureApiReplacement } from '@/lib/api/security';

function LegacyComponent() {
  // Drop-in replacement for useSecureApi
  const { secureRequest, isAuthenticated } = useSecureApiReplacement();
  
  // Same interface as before, enhanced architecture underneath
  const fetchData = async () => {
    const response = await secureRequest({
      url: '/api/data',
      method: 'GET'
    });
    return response.data;
  };
}
```

## 🔐 Security Architecture

### SecurityConfigProvider Setup

```typescript
import { SecurityConfigProvider } from '@/lib/api/security';

function App() {
  return (
    <SecurityConfigProvider
      initialConfig={{
        csrf: { enabled: true },
        tokenValidation: { enabled: true, autoRefresh: true },
        inputSanitization: { enabled: true },
        authentication: { enabled: true, autoLogout: true },
      }}
      validateConfig={true}
      onConfigChange={(config) => console.log('Security config updated:', config)}
    >
      <YourApp />
    </SecurityConfigProvider>
  );
}
```

### Security Features

- **CSRF Protection**: Automatic CSRF token management
- **Token Validation**: JWT validation and automatic refresh
- **Input Sanitization**: XSS and injection prevention
- **Session Security**: Session management and timeout handling
- **Threat Assessment**: Real-time security status monitoring

## 🏭 Service Layer

### Creating a New Service

```typescript
import { BaseApiService, type DataTransformer } from '@/lib/api/core/baseApiService';
import type { ApiClient } from '@/lib/api/core/apiClient';

interface MyEntity {
  id: string;
  name: string;
  // ... other properties
}

const MyEntityTransformer: DataTransformer<MyEntity> = {
  fromApi: (data: any) => ({
    id: data.id,
    name: data.name,
    // Transform API data to domain model
  }),
  toApi: (data: MyEntity) => ({
    id: data.id,
    name: data.name,
    // Transform domain model to API format
  }),
};

export class MyEntityApiService extends BaseApiService<
  MyEntity,
  Partial<MyEntity>,
  Partial<MyEntity>
> {
  protected endpoint = '/my-entities';
  protected transformer = MyEntityTransformer;

  constructor(apiClient: ApiClient, config?: ServiceConfig) {
    super(apiClient, {
      cacheDuration: 5 * 60 * 1000, // 5 minutes
      retryAttempts: 3,
      circuitBreakerThreshold: 5,
      enableMetrics: true,
      ...config,
    });
  }

  // Custom methods
  async getByStatus(status: string): Promise<MyEntity[]> {
    const result = await this.getAll({ status });
    return result.data;
  }
}
```

### Service Features

- **Circuit Breaker**: Automatic failure protection
- **Caching**: Request caching and deduplication
- **Retry Logic**: Configurable retry mechanisms
- **Metrics**: Performance monitoring
- **Error Handling**: Standardized error processing
- **Data Transformation**: Consistent API/domain mapping

## 🔄 Migration Guide

### From useSecureApi to New Architecture

**Step 1: Update Imports**
```typescript
// Before
import { useSecureApi } from '@/hooks/security/useSecureApi';

// After (Drop-in replacement)
import { useSecureApiReplacement } from '@/lib/api/security';

// After (Enhanced features)
import { useSecureApiClient } from '@/lib/api/security';
```

**Step 2: Update Component**
```typescript
// Before
function MyComponent() {
  const { secureRequest, isAuthenticated } = useSecureApi();
  // ... component logic
}

// After (Enhanced)
function MyComponent() {
  const { 
    secureRequest, 
    isAuthenticated,
    securityStatus,
    refreshSecurityFeatures 
  } = useSecureApiClient();
  // ... enhanced component logic with new features
}
```

**Step 3: Add SecurityConfigProvider (Optional)**
```typescript
// Wrap your app for centralized security configuration
<SecurityConfigProvider>
  <App />
</SecurityConfigProvider>
```

### Migration Utilities

```typescript
import { MigrationUtils } from '@/lib/api/security/migration/migrationUtils';

// Track migration progress
MigrationUtils.MigrationTracker.markComponentMigrated('MyComponent');

// Get migration status
const status = MigrationUtils.MigrationTracker.getMigrationStatus();
console.log(`Migration progress: ${status.progress}%`);

// Validate migration readiness
const validation = MigrationUtils.validateMigrationReadiness('MyComponent');
console.log('Migration recommendations:', validation.recommendations);
```

## 🧪 Testing

### Running Tests

```bash
# Run all API tests
npm test -- --testPathPattern=api

# Run security tests
npm test -- --testPathPattern=security

# Run specific test suites
npm test SecurityConfigProvider.test.ts
npm test useSecureApiClient.test.ts
npm test integration.test.tsx
```

### Test Coverage

- **Unit Tests**: All components and services
- **Integration Tests**: Complete architecture validation
- **Migration Tests**: Backward compatibility verification
- **Performance Tests**: No regression validation

## 📊 Performance

### Benchmarks

- **Hook Initialization**: <50ms
- **Request Processing**: <100ms
- **Security Processing**: <150ms
- **Memory Usage**: Efficient, no leaks detected

### Optimization Features

- **Request Deduplication**: Prevents duplicate requests
- **Intelligent Caching**: Configurable cache strategies
- **Circuit Breaker**: Prevents cascade failures
- **Lazy Loading**: Components loaded on demand

## 🔧 Configuration

### Security Configuration

```typescript
const securityConfig = {
  csrf: {
    enabled: true,
    tokenHeader: 'X-CSRF-Token',
    excludePaths: ['/api/public'],
  },
  tokenValidation: {
    enabled: true,
    refreshThreshold: 300, // seconds
    autoRefresh: true,
  },
  inputSanitization: {
    enabled: true,
    sanitizers: ['xss', 'sql'],
  },
  authentication: {
    enabled: true,
    autoLogout: true,
    redirectOnFailure: true,
  },
  http: {
    baseURL: '/api',
    timeout: 10000,
    retryAttempts: 3,
  },
};
```

### Service Configuration

```typescript
const serviceConfig = {
  cacheDuration: 5 * 60 * 1000, // 5 minutes
  retryAttempts: 3,
  circuitBreakerThreshold: 5,
  enableMetrics: true,
  timeout: 10000,
};
```

## 🎯 Best Practices

1. **Use SecurityConfigProvider** for centralized security configuration
2. **Prefer useSecureApiClient** for new implementations
3. **Extend BaseApiService** for all new API services
4. **Implement proper error handling** in all components
5. **Use migration utilities** for tracking progress
6. **Write comprehensive tests** for all new features
7. **Monitor security status** in production applications

## 📚 Additional Resources

- [Security Architecture Details](./security/README.md)
- [Service Layer Patterns](./core/baseApiService.ts)
- [Migration Utilities](./security/migration/migrationUtils.ts)
- [Usage Examples](./security/examples/SecureApiExample.tsx)
- [Test Suite](./security/__tests__/index.ts)
