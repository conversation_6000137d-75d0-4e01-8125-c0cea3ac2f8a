/**
 * @file DelegatesCard component for displaying delegation delegates
 * @module components/delegations/detail/assignments/DelegatesCard
 */

import { Users } from 'lucide-react';
import React from 'react';

import type { Delegation } from '@/lib/types/domain';

import { AssignmentSection } from './AssignmentSection';

interface DelegatesCardProps {
  className?: string;
  delegation: Delegation;
}

/**
 * DelegatesCard component for displaying delegation delegates
 * Uses the generic AssignmentSection for consistent styling
 */
export function DelegatesCard({ className, delegation }: DelegatesCardProps) {
  return (
    <AssignmentSection
      className={className ?? ''}
      emptyMessage="No delegates assigned to this delegation."
      icon={Users}
      items={delegation.delegates ?? []}
      renderItem={(delegate, index) => (
        <div
          className="space-y-2 rounded-lg border border-gray-200 bg-white p-4 transition-shadow hover:shadow-sm dark:border-gray-700 dark:bg-gray-800"
          key={delegate.id ?? index}
        >
          <h4 className="font-semibold text-gray-900 dark:text-white">
            {delegate.name}
          </h4>
          {delegate.title && (
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {delegate.title}
            </p>
          )}
          {delegate.notes && (
            <p className="rounded bg-gray-50 p-2 text-xs italic text-gray-500 dark:bg-gray-700 dark:text-gray-500">
              {delegate.notes}
            </p>
          )}
        </div>
      )}
      title="Delegates"
    />
  );
}

export default DelegatesCard;
