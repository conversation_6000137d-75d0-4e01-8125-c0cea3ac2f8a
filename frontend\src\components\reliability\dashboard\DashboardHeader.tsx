/**
 * @file Dashboard header component with navigation, connection status, and global controls.
 * This component provides the top-level navigation and status information for the reliability dashboard.
 * @module components/reliability/dashboard/DashboardHeader
 */

'use client';

import {
  Activity,
  AlertTriangle,
  BarChart3,
  History,
  Home,
  Pause,
  Play,
  Settings,
  Shield,
} from 'lucide-react';
import React, { useState } from 'react';

import { ActionButton } from '@/components/ui/action-button';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { useReliabilityStore } from '@/lib/hooks';
import {
  useAlerts,
  useAlertStatistics,
} from '@/lib/stores/queries/useReliability';

import { ConnectionStatusIndicator } from './ConnectionStatusIndicator';
import { DashboardSettings } from './DashboardSettings';

/**
 * Props for the DashboardHeader component
 */
export interface DashboardHeaderProps {
  /** Optional CSS class name for styling */
  className?: string;
}

/**
 * Dashboard header component with navigation, connection status, and global controls.
 *
 * This component provides:
 * - Tab navigation between dashboard sections
 * - Real-time connection status indicator
 * - Global monitoring controls (pause/resume)
 * - Settings access
 * - Alert count badges
 *
 * Features:
 * - Responsive design with mobile-friendly navigation
 * - Real-time status updates
 * - Keyboard navigation support
 * - Accessibility compliance with ARIA labels
 *
 * @param props - Component props
 * @returns JSX element representing the dashboard header
 */
export const DashboardHeader: React.FC<DashboardHeaderProps> = ({
  className = '',
}) => {
  // Local state for settings dialog
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);

  // Dashboard state and actions
  const activeTab = useReliabilityStore(state => state.ui.activeTab);
  const setActiveTab = useReliabilityStore(state => state.setActiveTab);
  const isMonitoringEnabled = useReliabilityStore(
    state => state.monitoring.isEnabled
  );
  const setMonitoringEnabled = useReliabilityStore(
    state => state.setMonitoringEnabled
  );
  const toggleFilterPanel = useReliabilityStore(
    state => state.toggleFilterPanel
  );
  const isFilterPanelOpen = useReliabilityStore(
    state => state.ui.isFilterPanelOpen
  );

  // Get alert data for badge counts
  const { data: alertsData } = useAlerts();
  const { data: alertsStatistics } = useAlertStatistics();
  const activeAlertCount =
    alertsData?.filter((alert: any) => alert.status === 'active').length || 0;
  const criticalAlertCount =
    alertsData?.filter(
      (alert: any) => alert.status === 'active' && alert.severity === 'critical'
    ).length || 0;

  /**
   * Handle monitoring toggle
   */
  const handleMonitoringToggle = () => {
    setMonitoringEnabled(!isMonitoringEnabled);
  };

  /**
   * Handle settings access
   */
  const handleSettingsClick = () => {
    setIsSettingsOpen(true);
  };

  return (
    <TooltipProvider>
      <header
        aria-label="Reliability dashboard header"
        className={`space-y-2 ${className}`}
        role="banner"
      >
        {/* Compact Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div>
              <h1 className="text-lg font-semibold text-primary">
                Reliability Dashboard
              </h1>
              <p className="text-xs text-muted-foreground">System monitoring</p>
            </div>

            {/* Connection Status */}
            <ConnectionStatusIndicator />
          </div>

          {/* Global Controls */}
          <div className="flex items-center gap-2">
            {/* Monitoring Toggle */}
            <Tooltip>
              <TooltipTrigger asChild>
                <ActionButton
                  actionType={isMonitoringEnabled ? 'secondary' : 'primary'}
                  aria-label={
                    isMonitoringEnabled
                      ? 'Pause monitoring'
                      : 'Resume monitoring'
                  }
                  icon={
                    isMonitoringEnabled ? (
                      <Pause className="size-4" />
                    ) : (
                      <Play className="size-4" />
                    )
                  }
                  onClick={handleMonitoringToggle}
                  size="sm"
                >
                  {isMonitoringEnabled ? 'Pause' : 'Resume'}
                </ActionButton>
              </TooltipTrigger>
              <TooltipContent>
                {isMonitoringEnabled
                  ? 'Pause real-time monitoring'
                  : 'Resume real-time monitoring'}
              </TooltipContent>
            </Tooltip>

            {/* Settings Menu */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button size="sm" variant="outline">
                  <Settings className="size-4" />
                  <span className="sr-only">Dashboard settings</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuItem onClick={handleSettingsClick}>
                  <Settings className="mr-2 size-4" />
                  Dashboard Settings
                </DropdownMenuItem>
                <DropdownMenuItem onClick={toggleFilterPanel}>
                  <Shield className="mr-2 size-4" />
                  {isFilterPanelOpen ? 'Hide Filters' : 'Show Filters'}
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem>
                  <BarChart3 className="mr-2 size-4" />
                  Export Data
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="overflow-x-auto border-b">
          <Tabs
            className="w-full"
            onValueChange={value => setActiveTab(value as any)}
            value={activeTab}
          >
            <TabsList className="inline-flex w-full min-w-fit lg:grid lg:w-full lg:grid-cols-5">
              <TabsTrigger
                aria-label="Overview dashboard"
                className="flex shrink-0 items-center gap-2 px-3 py-2 lg:justify-center"
                value="overview"
              >
                <Home className="size-4" />
                <span className="hidden sm:inline">Overview</span>
              </TabsTrigger>

              <TabsTrigger
                aria-label="System health monitoring"
                className="flex shrink-0 items-center gap-2 px-3 py-2 lg:justify-center"
                value="health"
              >
                <Activity className="size-4" />
                <span className="hidden sm:inline">Health</span>
              </TabsTrigger>

              <TabsTrigger
                aria-label="Performance metrics"
                className="flex shrink-0 items-center gap-2 px-3 py-2 lg:justify-center"
                value="metrics"
              >
                <BarChart3 className="size-4" />
                <span className="hidden sm:inline">Metrics</span>
              </TabsTrigger>

              <TabsTrigger
                aria-label="Active alerts and notifications"
                className="flex shrink-0 items-center gap-2 px-3 py-2 lg:justify-center"
                value="alerts"
              >
                <AlertTriangle className="size-4" />
                <span className="hidden sm:inline">Alerts</span>
                {activeAlertCount > 0 && (
                  <Badge
                    className="ml-1 h-5 min-w-[20px] shrink-0 text-xs"
                    variant={
                      criticalAlertCount > 0 ? 'destructive' : 'secondary'
                    }
                  >
                    {activeAlertCount}
                  </Badge>
                )}
              </TabsTrigger>

              <TabsTrigger
                aria-label="Historical data and trends"
                className="flex shrink-0 items-center gap-2 px-3 py-2 lg:justify-center"
                value="history"
              >
                <History className="size-4" />
                <span className="hidden sm:inline">History</span>
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>

        {/* Alert Summary Bar (when there are active alerts) */}
        {activeAlertCount > 0 && (
          <div className="rounded-lg border border-orange-200 bg-orange-50 p-3 dark:border-orange-800 dark:bg-orange-950">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <AlertTriangle className="size-4 text-orange-600 dark:text-orange-400" />
                <span className="text-sm font-medium text-orange-800 dark:text-orange-200">
                  {activeAlertCount} active alert
                  {activeAlertCount === 1 ? '' : 's'}
                  {criticalAlertCount > 0 && (
                    <span className="ml-1 text-red-600 dark:text-red-400">
                      ({criticalAlertCount} critical)
                    </span>
                  )}
                </span>
              </div>
              <Button
                className="text-orange-700 hover:text-orange-800 dark:text-orange-300 dark:hover:text-orange-200"
                onClick={() => setActiveTab('alerts')}
                size="sm"
                variant="outline"
              >
                View Alerts
              </Button>
            </div>
          </div>
        )}

        {/* Settings Dialog */}
        <Dialog onOpenChange={setIsSettingsOpen} open={isSettingsOpen}>
          <DialogContent className="max-h-[90vh] max-w-4xl overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Dashboard Settings</DialogTitle>
            </DialogHeader>
            <DashboardSettings />
          </DialogContent>
        </Dialog>
      </header>
    </TooltipProvider>
  );
};

/**
 * Default export for the DashboardHeader component
 */
export default DashboardHeader;
