// frontend/src/components/features/reporting/dashboard/filters/ReportingFilters.tsx

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { DateRangeFilter } from './DateRangeFilter';
import { StatusFilter } from './StatusFilter';
import { LocationFilter } from './LocationFilter';
import { EmployeeFilter } from './EmployeeFilter';
import { VehicleFilter } from './VehicleFilter';
import { FilterPresets } from './FilterPresets';
import {
  useReportingFilters,
  useReportingFiltersActions,
  useReportingFiltersUI,
  useReportingFiltersValidation,
} from '../../data/stores/useReportingFiltersStore';
import { Filter, X, RotateCcw, Check } from 'lucide-react';

interface ReportingFiltersProps {
  className?: string;
  showPresets?: boolean;
  compact?: boolean;
  // ENHANCED: Optional service history support
  includeServiceFilters?: boolean;
  includeTaskFilters?: boolean;
}

/**
 * @component ReportingFilters
 * @description Comprehensive filter panel for reporting dashboard following SOLID principles
 *
 * Responsibilities:
 * - Provides unified interface for all reporting filters
 * - Manages filter state and validation
 * - Handles filter presets and bulk operations
 * - Displays filter status and applied filters
 *
 * SOLID Principles Applied:
 * - SRP: Single responsibility of managing reporting filters UI
 * - OCP: Open for extension via new filter components
 * - DIP: Depends on filter store abstractions
 */
export const ReportingFilters: React.FC<ReportingFiltersProps> = ({
  className = '',
  showPresets = true,
  compact = false,
  // ENHANCED: Service and task filter support
  includeServiceFilters = false,
  includeTaskFilters = false,
}) => {
  // Filter state and actions
  const filters = useReportingFilters();
  const { resetFilters, applyFilters, revertChanges } =
    useReportingFiltersActions();
  const { isFilterPanelOpen, hasUnsavedChanges, setFilterPanelOpen } =
    useReportingFiltersUI();
  const { validationErrors, isValid } = useReportingFiltersValidation();

  // Handle filter actions
  const handleReset = () => {
    resetFilters();
  };

  const handleApply = () => {
    if (isValid) {
      applyFilters();
    }
  };

  const handleRevert = () => {
    revertChanges();
  };

  const handleClear = () => {
    setFilterPanelOpen(false);
  };

  // FIXED: Memoize active filter count calculation to prevent unnecessary re-renders
  const activeFilterCount = React.useMemo(() => {
    let count = 0;

    // Date range is always active, so don't count it
    if (filters.status.length > 0) count++;
    if (filters.locations.length > 0) count++;
    if (filters.employees.length > 0) count++;
    if (filters.vehicles.length > 0) count++;

    // ENHANCED: Count service filters when enabled
    if (includeServiceFilters) {
      if (filters.serviceTypes?.length) count++;
      if (filters.serviceStatus?.length) count++;
      if (filters.costRange) count++;
    }

    // ENHANCED: Count task filters when enabled
    if (includeTaskFilters && filters.includeTaskData) {
      count++;
    }

    return count;
  }, [filters, includeServiceFilters, includeTaskFilters]);

  // FIXED: Memoize filter summary badges to prevent unnecessary re-renders
  const filterSummaryBadges = React.useMemo(() => {
    const badges = [];

    if (filters.status.length > 0) {
      badges.push(
        <Badge key="status" variant="secondary" className="text-xs">
          Status: {filters.status.length}
        </Badge>
      );
    }

    if (filters.locations.length > 0) {
      badges.push(
        <Badge key="locations" variant="secondary" className="text-xs">
          Locations: {filters.locations.length}
        </Badge>
      );
    }

    if (filters.employees.length > 0) {
      badges.push(
        <Badge key="employees" variant="secondary" className="text-xs">
          Employees: {filters.employees.length}
        </Badge>
      );
    }

    if (filters.vehicles.length > 0) {
      badges.push(
        <Badge key="vehicles" variant="secondary" className="text-xs">
          Vehicles: {filters.vehicles.length}
        </Badge>
      );
    }

    // ENHANCED: Service filter badges
    if (includeServiceFilters) {
      if (filters.serviceTypes?.length) {
        badges.push(
          <Badge key="serviceTypes" variant="secondary" className="text-xs">
            Service Types: {filters.serviceTypes.length}
          </Badge>
        );
      }

      if (filters.serviceStatus?.length) {
        badges.push(
          <Badge key="serviceStatus" variant="secondary" className="text-xs">
            Service Status: {filters.serviceStatus.length}
          </Badge>
        );
      }

      if (filters.costRange) {
        badges.push(
          <Badge key="costRange" variant="secondary" className="text-xs">
            Cost: ${filters.costRange.min} - ${filters.costRange.max}
          </Badge>
        );
      }
    }

    // ENHANCED: Task filter badges
    if (includeTaskFilters && filters.includeTaskData) {
      badges.push(
        <Badge key="taskData" variant="secondary" className="text-xs">
          Task Data Included
        </Badge>
      );
    }

    return badges;
  }, [filters, includeServiceFilters, includeTaskFilters]);

  // Render validation errors
  const renderValidationErrors = () => {
    const errors = Object.values(validationErrors);
    if (errors.length === 0) return null;

    return (
      <div className="space-y-1">
        {errors.map((error, index) => (
          <div
            key={index}
            className="text-sm text-red-600 bg-red-50 p-2 rounded"
          >
            {error}
          </div>
        ))}
      </div>
    );
  };

  if (compact) {
    return (
      <div className={`space-y-4 ${className}`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Filter className="h-4 w-4" />
            <span className="text-sm font-medium">Filters</span>
            {activeFilterCount > 0 && (
              <Badge variant="default" className="text-xs">
                {activeFilterCount}
              </Badge>
            )}
          </div>
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="sm" onClick={handleReset}>
              <RotateCcw className="h-4 w-4" />
            </Button>
            <Button
              variant="default"
              size="sm"
              onClick={handleApply}
              disabled={!isValid || !hasUnsavedChanges}
            >
              <Check className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-2 lg:grid-cols-5 gap-4">
          <DateRangeFilter compact />
          <StatusFilter compact />
          <LocationFilter compact />
          <EmployeeFilter compact />
          <VehicleFilter compact />
        </div>

        {renderValidationErrors()}
      </div>
    );
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Reporting Filters
            {activeFilterCount > 0 && (
              <Badge variant="default" className="text-xs">
                {activeFilterCount} active
              </Badge>
            )}
          </CardTitle>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleClear}
            className="h-8 w-8 p-0"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        {filterSummaryBadges.length > 0 && (
          <div className="flex flex-wrap gap-2 mt-2">{filterSummaryBadges}</div>
        )}
      </CardHeader>

      <CardContent className="space-y-6">
        {showPresets && (
          <>
            <FilterPresets />
            <Separator />
          </>
        )}

        <div className="space-y-4">
          <DateRangeFilter />
          <StatusFilter />
          <LocationFilter />
          <EmployeeFilter />
          <VehicleFilter />
        </div>

        {renderValidationErrors()}

        <Separator />

        <div className="flex items-center justify-between">
          <Button
            variant="outline"
            onClick={handleReset}
            className="flex items-center gap-2"
          >
            <RotateCcw className="h-4 w-4" />
            Reset All
          </Button>

          <div className="flex items-center gap-2">
            {hasUnsavedChanges && (
              <Button variant="ghost" onClick={handleRevert}>
                Revert
              </Button>
            )}
            <Button
              onClick={handleApply}
              disabled={!isValid || !hasUnsavedChanges}
              className="flex items-center gap-2"
            >
              <Check className="h-4 w-4" />
              Apply Filters
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default ReportingFilters;
