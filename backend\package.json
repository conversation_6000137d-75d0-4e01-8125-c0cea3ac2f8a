{"name": "backend", "version": "1.0.0", "main": "dist/server.js", "directories": {"test": "tests"}, "scripts": {"build": "tsc", "start": "node dist/server.js", "start:prod": "node dist/server.js", "dev": "tsx src/server.ts", "test": "node --experimental-vm-modules node_modules/jest/bin/jest.js --runInBand", "test:reliability": "node --experimental-vm-modules node_modules/jest/bin/jest.js --runInBand src/tests/reliability", "test:reliability:unit": "node --experimental-vm-modules node_modules/jest/bin/jest.js --runInBand src/tests/reliability/unit", "test:reliability:integration": "node --experimental-vm-modules node_modules/jest/bin/jest.js --runInBand src/tests/reliability/integration", "test:reliability:performance": "node --experimental-vm-modules node_modules/jest/bin/jest.js --runInBand src/tests/reliability/performance", "test:coverage": "node --experimental-vm-modules node_modules/jest/bin/jest.js --runInBand --coverage", "test:watch": "node --experimental-vm-modules node_modules/jest/bin/jest.js --runInBand --watch", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit", "migrate:dev": "prisma migrate dev", "migrate:deploy": "prisma migrate deploy", "migrate:status": "prisma migrate status", "db:push": "prisma db push", "db:seed": "node --loader ts-node/esm prisma/seed.ts", "generate": "prisma generate", "studio": "prisma studio", "docker:build": "docker build -t workhub-backend .", "docker:run": "docker run -p 3001:3001 workhub-backend", "db:local": "node scripts/switch-database.js local", "db:supabase": "node scripts/switch-database.js supabase", "db:status": "node -e \"const fs=require('fs');const env=fs.readFileSync('.env','utf8');const useSupabase=/USE_SUPABASE=true/.test(env);console.log('Current database:',useSupabase?'Supabase':'Local PostgreSQL');\"", "db:migrate": "prisma migrate deploy", "db:seed:force": "node --loader ts-node/esm prisma/seed.ts", "supabase:login": "node scripts/supabase-cli.js login", "supabase:link": "node scripts/supabase-cli.js link", "supabase:pull": "node scripts/supabase-cli.js db:pull", "supabase:push": "node scripts/supabase-cli.js db:push", "supabase:migrate": "node scripts/supabase-cli.js db:migrate", "supabase:seed": "node scripts/supabase-cli.js db:seed", "supabase:config": "node scripts/supabase-cli.js config", "generate-secrets": "node scripts/generate-secrets.js", "validate-secrets": "node -e \"require('./dist/config/secrets.js').validateSecretsOrThrow()\"", "security:check": "npm run validate-secrets"}, "prisma": {"seed": "node --loader ts-node/esm prisma/seed.ts"}, "keywords": [], "author": "", "license": "ISC", "type": "module", "description": "", "dependencies": {"@prisma/client": "^6.8.2", "@supabase/supabase-js": "^2.49.4", "@types/cors": "^2.8.18", "@types/dompurify": "^3.0.5", "@types/express-rate-limit": "^5.1.3", "@types/ioredis": "^4.28.10", "@types/jsdom": "^21.1.7", "@types/opossum": "^8.1.8", "axios": "^1.6.7", "cors": "^2.8.5", "dompurify": "^3.2.6", "dotenv": "^16.5.0", "ejs": "^3.1.9", "esm": "^3.2.25", "express": "^5.1.0", "express-prom-bundle": "^8.0.0", "express-rate-limit": "^7.5.0", "helmet": "^8.1.0", "ioredis": "^5.6.1", "jsdom": "^26.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "node-cache": "^5.1.2", "node-fetch": "^3.3.2", "opossum": "^8.5.0", "pg": "^8.15.6", "prom-client": "^15.1.3", "puppeteer": "^22.4.0", "rate-limiter-flexible": "^7.1.1", "socket.io": "^4.8.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "uuid": "^11.1.0", "winston": "^3.17.0", "zod": "^3.24.4"}, "devDependencies": {"@eslint/js": "^9.28.0", "@types/ejs": "^3.1.5", "@types/express": "^5.0.1", "@types/jest": "^29.5.14", "@types/morgan": "^1.9.9", "@types/node": "^22.15.17", "@types/pg": "^8.15.1", "@types/supertest": "^6.0.3", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.33.1", "@typescript-eslint/parser": "^8.33.1", "eslint": "^9.28.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-perfectionist": "^4.13.0", "eslint-plugin-prettier": "^5.4.1", "jest": "^29.7.0", "prettier": "^3.5.3", "prisma": "^6.8.2", "supabase": "^2.22.12", "supertest": "^7.1.0", "ts-jest": "^29.3.2", "ts-node": "^10.9.2", "tsx": "^4.19.4", "typescript": "^5.8.3", "typescript-eslint": "^8.33.1"}}