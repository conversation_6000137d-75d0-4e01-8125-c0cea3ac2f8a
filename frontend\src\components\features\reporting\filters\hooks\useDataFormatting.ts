// frontend/src/components/features/reporting/filters/hooks/useDataFormatting.ts
import { useCallback } from 'react';
import { format } from 'date-fns';

/**
 * @hook useDataFormatting
 * @description Provides utility functions for consistent data formatting across the reporting system.
 * Adheres to SRP by focusing solely on data presentation logic.
 * Memoization (useCallback) ensures performance and stability.
 */
export const useDataFormatting = () => {
  /**
   * Formats a Date object or string into a 'MMM dd, yyyy' string.
   * @param {string | Date} date - The date to format.
   * @returns {string} The formatted date string.
   */
  const formatDate = useCallback((date: string | Date): string => {
    if (!date) return '';
    return format(new Date(date), 'MMM dd, yyyy');
  }, []);

  /**
   * Formats a date range into a 'MMM dd - MMM dd, yyyy' string.
   * @param {string | Date} from - The start date of the range.
   * @param {string | Date} to - The end date of the range.
   * @returns {string} The formatted date range string.
   */
  const formatDateRange = useCallback(
    (from: string | Date, to: string | Date): string => {
      if (!from || !to) return '';
      const fromDate = format(new Date(from), 'MMM dd');
      const toDate = format(new Date(to), 'MMM dd, yyyy');
      return `${fromDate} - ${toDate}`;
    },
    []
  );

  /**
   * Formats a number as a percentage string (e.g., 0.75 -> "75%").
   * @param {number} value - The numeric value to format (expected between 0 and 1).
   * @returns {string} The formatted percentage string.
   */
  const formatPercentage = useCallback((value: number): string => {
    if (typeof value !== 'number' || isNaN(value)) return 'N/A';
    return `${Math.round(value * 100)}%`;
  }, []);

  /**
   * Formats a large number for readability (e.g., 12345 -> "12.3K").
   * @param {number} value - The number to format.
   * @returns {string} The formatted number string.
   */
  const formatLargeNumber = useCallback((value: number): string => {
    if (value >= 1_000_000_000) return (value / 1_000_000_000).toFixed(1) + 'B';
    if (value >= 1_000_000) return (value / 1_000_000).toFixed(1) + 'M';
    if (value >= 1_000) return (value / 1_000).toFixed(1) + 'K';
    return value.toString();
  }, []);

  return {
    formatDate,
    formatDateRange,
    formatPercentage,
    formatLargeNumber,
  };
};
