{"common": {"loading": "Loading...", "error": "Error", "retry": "Retry", "cancel": "Cancel", "save": "Save", "edit": "Edit", "delete": "Delete", "add": "Add", "search": "Search", "filter": "Filter", "clearAll": "Clear All", "noResults": "No results found", "required": "Required", "optional": "Optional", "actions": "Actions", "back": "Back", "next": "Next", "previous": "Previous", "close": "Close", "confirm": "Confirm", "yes": "Yes", "no": "No", "all": "All", "none": "None", "some": "Some", "many": "Many", "created": "Created", "updated": "Updated"}, "navigation": {"gifts": "Gifts", "recipients": "Recipients", "dashboard": "Dashboard", "giftTracking": "Gift Tracking"}, "gifts": {"title": "Gift Management", "addGift": "Add New Gift", "editGift": "Edit Gift", "deleteGift": "Delete Gift", "giftDetails": "Gift Details", "noGifts": "No gifts found", "noGiftsDescription": "Start by adding your first gift", "giftCount": "{count, plural, =0 {No gifts} =1 {1 gift} other {# gifts}}", "recentGifts": "Recent Gifts", "filters": {"title": "Filters", "description": "Filter gifts by search terms, recipients, occasions, and dates", "search": "Search gifts, recipients, or senders...", "recipient": "Recipient", "allRecipients": "All recipients", "occasion": "Occasion", "allOccasions": "All occasions", "sender": "Sender", "allSenders": "All senders", "dateRange": "Date Range", "from": "From", "to": "To"}, "form": {"itemDescription": "Item Description", "itemDescriptionPlaceholder": "Describe the gift item...", "itemDescriptionDescription": "A detailed description of the gift", "recipient": "Recipient", "recipientPlaceholder": "Select a recipient", "recipientDescription": "Choose who will receive this gift", "occasion": "Occasion", "occasionPlaceholder": "Select or enter an occasion", "occasionDescription": "The occasion for this gift", "senderName": "Sender Name", "senderNamePlaceholder": "Enter sender's name", "senderNameDescription": "The name of the person giving the gift", "dateSent": "Date Sent", "dateSentDescription": "When the gift was or will be sent", "estimatedValue": "Estimated Value", "estimatedValuePlaceholder": "0.00", "estimatedValueDescription": "Optional estimated value of the gift", "notes": "Notes", "notesPlaceholder": "Any additional notes about this gift...", "notesDescription": "Optional notes or special instructions", "saveGift": "Save Gift", "updateGift": "Update Gift", "saving": "Saving..."}, "occasions": {"birthday": "Birthday", "anniversary": "Anniversary", "wedding": "Wedding", "graduation": "Graduation", "holiday": "Holiday", "christmas": "Christmas", "valentines": "Valentine's Day", "mothersDay": "Mother's Day", "fathersDay": "Father's Day", "thanksgiving": "Thanksgiving", "newYear": "New Year", "other": "Other"}, "results": {"showing": "Showing {filtered} of {total} gifts", "filteredOut": "({filtered} filtered out)", "total": "{count} gift{count, plural, =1 {} other {s}} total", "noMatches": "No gifts match your filters", "noMatchesDescription": "Try adjusting your search criteria or clearing filters"}}, "recipients": {"title": "Recipient Management", "addRecipient": "Add New Recipient", "editRecipient": "Edit Recipient", "deleteRecipient": "Delete Recipient", "recipientDetails": "Recipient Details", "noRecipients": "No recipients found", "noRecipientsDescription": "Start by adding your first recipient", "recipientCount": "{count, plural, =0 {No recipients} =1 {1 recipient} other {# recipients}}", "filters": {"title": "Filters", "description": "Filter recipients by contact information and gift history", "search": "Search recipients by name, email, phone, or notes...", "emailAddress": "Email Address", "hasEmail": "Has email ({count})", "noEmail": "No email ({count})", "phoneNumber": "Phone Number", "hasPhone": "Has phone ({count})", "noPhone": "No phone ({count})", "address": "Address", "hasAddress": "Has address ({count})", "noAddress": "No address ({count})", "giftHistory": "Gift History", "noGifts": "No gifts ({count})", "someGifts": "1-4 gifts ({count})", "manyGifts": "5+ gifts ({count})"}, "form": {"name": "Name", "namePlaceholder": "Enter recipient's full name", "nameDescription": "The full name of the gift recipient", "email": "Email", "emailPlaceholder": "<EMAIL>", "emailDescription": "Optional email address for the recipient", "phone": "Phone Number", "phonePlaceholder": "+****************", "phoneDescription": "Optional phone number for the recipient", "address": "Address", "addressPlaceholder": "Enter the recipient's address...", "addressDescription": "Optional mailing address for the recipient", "notes": "Notes", "notesPlaceholder": "Any additional notes about this recipient...", "notesDescription": "Optional notes, preferences, or special information about the recipient", "saveRecipient": "Save Recipient", "updateRecipient": "Update Recipient", "saving": "Saving..."}, "statistics": {"totalRecipients": "Total Recipients", "withEmail": "With Email", "withPhone": "With Phone", "withAddress": "With Address"}, "results": {"showing": "Showing {filtered} of {total} recipients", "filteredOut": "({filtered} filtered out)", "total": "{count} recipient{count, plural, =1 {} other {s}} total", "noMatches": "No recipients match your filters", "noMatchesDescription": "Try adjusting your search criteria or clearing filters"}}, "dashboard": {"title": "Gift Tracking Dashboard", "overview": "Overview", "statistics": "Statistics", "recentActivity": "Recent Activity", "quickActions": "Quick Actions"}, "validation": {"required": "This field is required", "email": "Please enter a valid email address", "phone": "Please enter a valid phone number", "maxLength": "Must be less than {max} characters", "minLength": "Must be at least {min} characters", "invalidDate": "Please enter a valid date", "invalidNumber": "Please enter a valid number"}, "dates": {"today": "Today", "yesterday": "Yesterday", "tomorrow": "Tomorrow", "thisWeek": "This week", "lastWeek": "Last week", "thisMonth": "This month", "lastMonth": "Last month", "thisYear": "This year", "lastYear": "Last year"}}