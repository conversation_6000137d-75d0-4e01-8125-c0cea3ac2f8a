/**
 * @file Standardized API Query Hook
 * @description Provides consistent API query patterns with error handling, caching, and loading states
 */

import {
  type QueryKey,
  useQuery,
  type UseQueryOptions,
  type UseQueryResult,
} from '@tanstack/react-query';
import { useEffect } from 'react';

import type { PaginatedResponse } from '@/types';

import { useToast } from '@/hooks/utils/use-toast';

/**
 * Configuration options for API queries
 */
export interface ApiQueryOptions<T>
  extends Omit<UseQueryOptions<T>, 'queryFn' | 'queryKey'> {
  /** Cache duration in milliseconds */
  cacheDuration?: number;
  /** Whether to retry on error */
  enableRetry?: boolean;
  /** Custom error message */
  errorMessage?: string;
  /** Number of retry attempts */
  retryAttempts?: number;
  /** Whether to show error toasts automatically */
  showErrorToast?: boolean;
  /** Whether to show success toasts automatically */
  showSuccessToast?: boolean;
  /** Custom success message */
  successMessage?: string;
}

/**
 * Enhanced API query result with additional utilities
 */
export type ApiQueryResult<T> = UseQueryResult<T> & {
  /** Force refresh the query */
  forceRefresh: () => Promise<UseQueryResult<T>>;
  /** Check if data is stale */
  isStale: boolean;
  /** Last updated timestamp */
  lastUpdated: null | number;
};

/**
 * Standardized API query hook with consistent error handling and caching
 *
 * @example
 * ```typescript
 * const { data, isLoading, error, forceRefresh } = useApiQuery(
 *   ['users', filters],
 *   () => userService.getAll(filters),
 *   {
 *     showErrorToast: true,
 *     cacheDuration: 5 * 60 * 1000, // 5 minutes
 *     enableRetry: true,
 *   }
 * );
 * ```
 */
export const useApiQuery = <T>(
  queryKey: QueryKey,
  queryFn: () => Promise<T>,
  options: ApiQueryOptions<T> = {}
): ApiQueryResult<T> => {
  const { toast } = useToast();

  const {
    cacheDuration = 5 * 60 * 1000, // 5 minutes default
    enableRetry = true,
    errorMessage,
    retryAttempts = 3,
    showErrorToast = true,
    showSuccessToast = false,
    successMessage,
    ...queryOptions
  } = options;

  const queryResult = useQuery({
    gcTime: cacheDuration * 2, // Keep in cache for twice the stale time
    queryFn,
    queryKey,
    retry: enableRetry ? retryAttempts : false,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30_000),
    staleTime: cacheDuration,
    ...queryOptions,
  });

  // FIXED: Handle success notifications in useEffect to prevent setState during render
  useEffect(() => {
    if (
      showSuccessToast &&
      queryResult.isSuccess &&
      queryResult.data &&
      successMessage
    ) {
      toast({
        description: successMessage,
        title: 'Success',
      });
    }
  }, [
    showSuccessToast,
    queryResult.isSuccess,
    queryResult.data,
    successMessage,
    toast,
  ]);

  // FIXED: Handle error notifications in useEffect to prevent setState during render
  useEffect(() => {
    if (showErrorToast && queryResult.isError) {
      const message =
        errorMessage ||
        (queryResult.error instanceof Error
          ? queryResult.error.message
          : 'An error occurred');

      toast({
        description: message,
        title: 'Error',
        variant: 'destructive',
      });
    }
  }, [
    showErrorToast,
    queryResult.isError,
    queryResult.error,
    errorMessage,
    toast,
  ]);

  // Enhanced result with additional utilities
  const enhancedResult: ApiQueryResult<T> = {
    ...queryResult,
    forceRefresh: async () => await queryResult.refetch(),
    isStale: queryResult.isStale || false,
    lastUpdated: queryResult.dataUpdatedAt || null,
  };

  return enhancedResult;
};

/**
 * Hook for queries that depend on other queries
 * Automatically handles dependency loading states
 */
export const useDependentApiQuery = <T, TDep>(
  queryKey: QueryKey,
  queryFn: (dependency: TDep) => Promise<T>,
  dependency: TDep | undefined,
  options: ApiQueryOptions<T> = {}
): ApiQueryResult<T> => {
  return useApiQuery(
    queryKey,
    () => {
      if (!dependency) {
        throw new Error('Dependency not available');
      }
      return queryFn(dependency);
    },
    {
      ...options,
      enabled: !!dependency && options.enabled !== false,
    }
  );
};

/**
 * Type for the raw API response of a paginated query
 */
export interface PaginatedApiResponse<T> {
  data: T[];
  pagination: PaginatedResponse<any>['pagination'];
}

/**
 * Hook for paginated API queries with consistent pagination handling
 */
export interface PaginatedQueryOptions<T>
  extends ApiQueryOptions<PaginatedApiResponse<T>> {
  /** Whether to keep previous data while loading new page */
  keepPreviousData?: boolean;
  /** Current page number */
  page?: number;
  /** Items per page */
  pageSize?: number;
}

export type PaginatedQueryResult<T> = Omit<
  ApiQueryResult<PaginatedApiResponse<T>>,
  'data'
> & {
  /** Current page number */
  currentPage: number;
  /** The actual data array */
  data: T[];
  /** Go to specific page */
  goToPage: (page: number) => void;
  /** Whether there's a next page */
  hasNextPage: boolean;
  /** Whether there's a previous page */
  hasPrevPage: boolean;
  /** Go to next page */
  nextPage: () => void;
  /** The pagination object */
  pagination: PaginatedResponse<any>['pagination'];
  /** Go to previous page */
  prevPage: () => void;
  /** Total number of pages */
  totalPages: number;
};

/**
 * Standardized paginated API query hook
 */
export const usePaginatedApiQuery = <T>(
  baseQueryKey: QueryKey,
  queryFn: (page: number, pageSize: number) => Promise<PaginatedApiResponse<T>>,
  options: PaginatedQueryOptions<T> = {}
): PaginatedQueryResult<T> => {
  const {
    keepPreviousData = true,
    page = 1,
    pageSize = 10,
    ...apiOptions
  } = options;

  const queryKey = [...baseQueryKey, 'paginated', page, pageSize];

  const queryResult = useApiQuery<PaginatedApiResponse<T>>(
    queryKey,
    () => queryFn(page, pageSize),
    {
      ...apiOptions,
      ...(keepPreviousData
        ? {
            placeholderData: (prev: PaginatedApiResponse<T> | undefined) =>
              prev,
          }
        : {}),
    }
  );

  const pagination = queryResult.data?.pagination;

  const enhancedResult: PaginatedQueryResult<T> = {
    ...queryResult,
    currentPage: page,
    data: queryResult.data?.data ?? [],
    goToPage: (newPage: number) => {
      // This would typically be handled by the parent component
    },
    hasNextPage: pagination ? pagination.hasNext : false,
    hasPrevPage: pagination ? pagination.hasPrevious : false,
    nextPage: () => {
      if (pagination && pagination.hasNext) {
        // This would typically be handled by the parent component
        // by updating the page state that's passed to this hook
      }
    },
    pagination: pagination ?? {
      hasNext: false,
      hasPrevious: false,
      limit: pageSize,
      page: 1,
      total: 0,
      totalPages: 1,
    },
    prevPage: () => {
      if (pagination && pagination.hasPrevious) {
        // This would typically be handled by the parent component
      }
    },
    totalPages: pagination ? pagination.totalPages : 1,
  };

  return enhancedResult;
};
