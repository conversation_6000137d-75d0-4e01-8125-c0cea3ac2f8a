'use client';

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>lipboard<PERSON>ist,
  Refresh<PERSON>w,
  Search,
  X,
} from 'lucide-react';
import { useSearchParams } from 'next/navigation';
import { Suspense, useCallback, useEffect, useMemo, useState } from 'react';

import type { Task } from '@/lib/types/domain'; // Updated to domain types

import { EnhancedTasksContainer } from '@/components/features/tasks/EnhancedTasksContainer';
import { ReportActions } from '@/components/reports/ReportActions';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { SkeletonLoader } from '@/components/ui/loading';
import { PageHeader } from '@/components/ui/PageHeader';
// Removed unused date-fns imports
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useEmployees } from '@/lib/stores/queries/useEmployees'; // Added React Query hook
import { useTasks } from '@/lib/stores/queries/useTasks'; // Added React Query hook

// Removed unused getStatusColor function

// Removed unused getPriorityColor function

// Removed unused formatDate function

// Removed unused mapDomainStatusToOldUITaskStatus function

export default function TaskReportPage() {
  return (
    <Suspense
      fallback={<div className="py-10 text-center">Loading report...</div>}
    >
      <TaskReportContent />
    </Suspense>
  );
}

function TaskReportContent() {
  const searchParams = useSearchParams();
  const {
    data: allTasksData,
    error: tasksError,
    isLoading: isLoadingTasks,
    refetch: refetchTasks,
  } = useTasks();
  const {
    data: employeesData,
    error: employeesError,
    isLoading: isLoadingEmployees,
    refetch: refetchEmployees,
  } = useEmployees();

  const allTasks = useMemo(() => allTasksData || [], [allTasksData]);
  const employeesList = useMemo(() => employeesData || [], [employeesData]);

  // Filter state
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [priorityFilter, setPriorityFilter] = useState<string>('all');
  const [employeeFilter, setEmployeeFilter] = useState<string>('all');

  // Initialize filters from URL params & set document title
  useEffect(() => {
    document.title = 'Task Report';
    const urlSearchTerm = searchParams?.get('searchTerm') || '';
    const urlStatusFilter = searchParams?.get('status') || 'all';
    const urlPriorityFilter = searchParams?.get('priority') || 'all';
    const urlEmployeeFilter = searchParams?.get('employee') || 'all';

    setSearchTerm(urlSearchTerm);
    setDebouncedSearchTerm(urlSearchTerm);
    setStatusFilter(urlStatusFilter);
    setPriorityFilter(urlPriorityFilter);
    setEmployeeFilter(urlEmployeeFilter);
  }, [searchParams]);

  // Debounce search term
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchTerm]);

  // 🔧 FIX: Replace useEffect + useState with useMemo to prevent infinite loop
  // This prevents the "Maximum update depth exceeded" error by avoiding unstable array references
  const filteredTasks = useMemo(() => {
    let tempTasks = [...allTasks];
    const lowercasedSearch = debouncedSearchTerm.toLowerCase();

    // Filter by status
    if (statusFilter !== 'all') {
      tempTasks = tempTasks.filter(task => task.status === statusFilter);
    }

    // Filter by priority
    if (priorityFilter !== 'all') {
      tempTasks = tempTasks.filter(task => task.priority === priorityFilter);
    }

    // Filter by employee
    if (employeeFilter !== 'all') {
      if (employeeFilter === 'unassigned') {
        // This logic needs re-evaluation as staffEmployeeId is not optional in DomainTask.
        // For now, assuming unassigned might mean a specific ID or this filter is less relevant.
        // tempTasks = tempTasks.filter(task => !task.staffEmployeeId); // Or a specific value
      } else {
        tempTasks = tempTasks.filter(
          task => task.staffEmployeeId?.toString() === employeeFilter
        );
      }
    }

    // Filter by search term
    if (lowercasedSearch) {
      tempTasks = tempTasks.filter(task => {
        // Prefer staffEmployeeId for assignee name lookup
        const employee = task.staffEmployeeId
          ? employeesList.find(e => e.id === task.staffEmployeeId)
          : task.driverEmployeeId
            ? employeesList.find(e => e.id === task.driverEmployeeId)
            : null;
        const employeeName = employee
          ? employee.fullName ||
            employee.name ||
            'Unknown Employee'.trim().toLowerCase()
          : ''; // Updated to use firstName/lastName

        return (
          (task.description && // Use description as title was removed
            task.description.toLowerCase().includes(lowercasedSearch)) ||
          // task.location and task.notes removed as they are not in domain.Task
          employeeName.includes(lowercasedSearch) ||
          task.status.toLowerCase().includes(lowercasedSearch) ||
          task.priority.toLowerCase().includes(lowercasedSearch)
        );
      });
    }

    return tempTasks;
  }, [
    allTasks,
    debouncedSearchTerm,
    statusFilter,
    priorityFilter,
    employeeFilter,
    employeesList,
  ]);

  // Handle retry for data fetching errors
  const handleDataRetry = useCallback(() => {
    refetchTasks();
    refetchEmployees();
  }, [refetchTasks, refetchEmployees]);

  // If we're loading the initial data, show a loading skeleton
  if (isLoadingTasks || isLoadingEmployees) {
    return (
      <div className="space-y-6">
        <PageHeader
          description="Loading task data..."
          icon={ClipboardList}
          title="Tasks Report"
        />
        <div className="space-y-6">
          <SkeletonLoader count={1} variant="card" />
          <SkeletonLoader className="mt-6" count={5} variant="table" />
        </div>
      </div>
    );
  }

  // If there was an error fetching data, show an error message with retry button
  const combinedError = tasksError || employeesError;
  if (combinedError) {
    return (
      <div className="space-y-6">
        <PageHeader
          description="Error loading task data."
          icon={ClipboardList}
          title="Tasks Report"
        />
        <Alert className="mb-6" variant="destructive">
          <AlertTriangle className="size-4" />
          <AlertTitle>Error Loading Data</AlertTitle>
          <AlertDescription>
            {tasksError?.message ||
              employeesError?.message ||
              'An unknown error occurred.'}
          </AlertDescription>
          <Button
            className="mt-2"
            onClick={handleDataRetry}
            size="sm"
            variant="outline"
          >
            <RefreshCw className="mr-2 size-4" />
            Try Again
          </Button>
        </Alert>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <PageHeader
        description="View and manage all tasks and assignments."
        icon={ClipboardList}
        title="Tasks Report"
      >
        <div className="no-print flex items-center gap-2">
          <ReportActions
            enableCsv={filteredTasks.length > 0}
            fileName={`tasks-report-${new Date().toISOString().split('T')[0]}`}
            reportContentId="#task-report-content"
            reportType="task" // Added missing reportType prop
            tableId="#tasks-table"
          />
        </div>
      </PageHeader>

      {/* Filters */}
      <Card className="no-print shadow-md">
        <CardContent className="pt-6">
          <div className="filter-grid mb-6 grid grid-cols-1 items-end gap-4 md:grid-cols-2 lg:grid-cols-4">
            <div>
              <Label
                className="mb-1 block text-sm font-medium text-muted-foreground"
                htmlFor="status-filter"
              >
                Filter by Status
              </Label>
              <Select
                aria-label="Filter by status"
                onValueChange={setStatusFilter}
                value={statusFilter}
              >
                <SelectTrigger className="w-full" id="status-filter">
                  <SelectValue placeholder="All Statuses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="Pending">Pending</SelectItem>
                  <SelectItem value="Assigned">Assigned</SelectItem>
                  <SelectItem value="In_Progress">In Progress</SelectItem>
                  <SelectItem value="Completed">Completed</SelectItem>
                  <SelectItem value="Cancelled">Cancelled</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label
                className="mb-1 block text-sm font-medium text-muted-foreground"
                htmlFor="priority-filter"
              >
                Filter by Priority
              </Label>
              <Select
                aria-label="Filter by priority"
                onValueChange={setPriorityFilter}
                value={priorityFilter}
              >
                <SelectTrigger className="w-full" id="priority-filter">
                  <SelectValue placeholder="All Priorities" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Priorities</SelectItem>
                  <SelectItem value="High">High</SelectItem>
                  <SelectItem value="Medium">Medium</SelectItem>
                  <SelectItem value="Low">Low</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label
                className="mb-1 block text-sm font-medium text-muted-foreground"
                htmlFor="employee-filter"
              >
                Filter by Assignee
              </Label>
              <Select
                aria-label="Filter by assignee"
                onValueChange={setEmployeeFilter}
                value={employeeFilter}
              >
                <SelectTrigger className="w-full" id="employee-filter">
                  <SelectValue placeholder="All Assignees" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Assignees</SelectItem>
                  <SelectItem value="unassigned">Unassigned</SelectItem>
                  {Array.isArray(employeesList) &&
                    employeesList.map(employee => (
                      <SelectItem
                        key={employee.id}
                        value={employee.id.toString()}
                      >
                        {' '}
                        {/* Convert number ID to string for value prop */}
                        {employee.fullName ||
                          employee.name ||
                          `Employee ${employee.id}`}
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
            </div>

            <div className="relative">
              <Label
                className="mb-1 block text-sm font-medium text-muted-foreground"
                htmlFor="search-tasks"
              >
                Search Tasks
              </Label>
              <div className="relative">
                <Input
                  aria-label="Search tasks"
                  className="px-10"
                  id="search-tasks"
                  onChange={e => setSearchTerm(e.target.value)}
                  placeholder="Search by description, location, notes..."
                  type="text"
                  value={searchTerm}
                />
                <Search
                  aria-hidden="true"
                  className="absolute left-3 top-1/2 size-5 -translate-y-1/2 text-muted-foreground"
                />
                {searchTerm && (
                  <Button
                    aria-label="Clear search"
                    className="absolute right-1 top-1/2 size-7 -translate-y-1/2"
                    onClick={() => setSearchTerm('')}
                    size="icon"
                    variant="ghost"
                  >
                    <X className="size-4" />
                    <span className="sr-only">Clear search</span>
                  </Button>
                )}
              </div>
            </div>
          </div>

          {/* Filter Summary & Reset */}
          {(statusFilter !== 'all' ||
            priorityFilter !== 'all' ||
            employeeFilter !== 'all' ||
            searchTerm) && (
            <div className="mt-4 flex items-center justify-between rounded bg-gray-50 p-2">
              <div className="text-sm">
                <span className="font-medium">Active Filters:</span>
                {statusFilter !== 'all' && (
                  <span className="ml-2">
                    Status: {statusFilter.replace('_', ' ')}
                  </span>
                )}
                {priorityFilter !== 'all' && (
                  <span className="ml-2">Priority: {priorityFilter}</span>
                )}
                {employeeFilter !== 'all' && (
                  <span className="ml-2">
                    Assignee:{' '}
                    {employeeFilter === 'unassigned'
                      ? 'Unassigned'
                      : (employeesList.find(
                          e => e.id.toString() === employeeFilter
                        ) // Compare string ID from filter with stringified number ID
                          ? employeesList.find(
                              e => e.id.toString() === employeeFilter
                            )?.fullName ||
                            employeesList.find(
                              e => e.id.toString() === employeeFilter
                            )?.name ||
                            'Unknown Employee'
                          : '') || 'Unknown'}
                  </span>
                )}
                {searchTerm && (
                  <span className="ml-2">Search: "{searchTerm}"</span>
                )}
              </div>
              <Button
                aria-label="Reset all filters"
                onClick={() => {
                  setStatusFilter('all');
                  setPriorityFilter('all');
                  setEmployeeFilter('all');
                  setSearchTerm('');
                }}
                size="sm"
                variant="ghost"
              >
                Reset Filters
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Tasks Report Content */}
      <div className="report-content" id="task-report-content">
        <header className="print-only mb-8 border-b-2 border-gray-300 pb-4 text-center">
          <h1 className="text-3xl font-bold text-gray-800">Tasks Report</h1>
          <p className="text-md text-gray-600">
            {statusFilter !== 'all' &&
              `Status: ${statusFilter.replace('_', ' ')}`}
            {priorityFilter !== 'all' &&
              (statusFilter === 'all' ? '' : ' | ') +
                `Priority: ${priorityFilter}`}
            {employeeFilter !== 'all' &&
              (statusFilter !== 'all' || priorityFilter !== 'all'
                ? ' | '
                : '') +
                `Assignee: ${
                  employeeFilter === 'unassigned'
                    ? 'Unassigned'
                    : (Array.isArray(employeesList) &&
                      employeesList.find(
                        e => e.id.toString() === employeeFilter
                      ) // Compare string ID
                        ? employeesList.find(
                            e => e.id.toString() === employeeFilter
                          )?.fullName ||
                          employeesList.find(
                            e => e.id.toString() === employeeFilter
                          )?.name ||
                          'Unknown Employee'
                        : '') || 'Employee ' + employeeFilter
                }`}
          </p>
        </header>

        <EnhancedTasksContainer
          error={tasksError}
          isLoading={isLoadingTasks}
          onRetry={refetchTasks}
          tasks={filteredTasks}
        />

        <footer className="mt-10 border-t-2 border-gray-300 pt-4 text-center text-xs text-gray-500">
          <p>Report generated on: {new Date().toLocaleDateString()}</p>
          <p>WorkHub - Task Management</p>
        </footer>
      </div>

      {/* Print Styles */}
      <style global jsx>{`
        .print-only {
          display: none;
        }
        @media print {
          .no-print {
            display: none !important;
          }
          .print-only {
            display: block;
          }
          .print-container {
            padding: 1rem;
          }
          .card-print {
            box-shadow: none !important;
            border: none !important;
          }
          .print-description,
          .print-location,
          .print-service-col,
          .print-notes-col {
            max-width: 200px;
            white-space: normal !important;
            word-break: break-word;
          }
          .print-text-wrap {
            word-break: break-word;
            white-space: normal !important;
          }
        }

        @media (max-width: 640px) {
          .overflow-x-auto {
            overflow-x: auto;
          }

          .filter-grid {
            grid-template-columns: 1fr !important;
          }

          .summary-grid {
            grid-template-columns: 1fr 1fr !important;
          }
        }
      `}</style>
    </div>
  );
}
