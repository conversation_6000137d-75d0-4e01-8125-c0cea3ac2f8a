'use client';

import { format, parseISO } from 'date-fns';
import {
  <PERSON><PERSON><PERSON>riangle,
  ArrowRight,
  CalendarDays,
  Car,
  ClipboardList,
  Clock,
  MapPin,
  User,
  Users,
} from 'lucide-react';
import Link from 'next/link';
import { useEffect, useState } from 'react';

import type { Employee, Task } from '@/lib/types/domain';

import { ActionButton } from '@/components/ui/action-button';
import { Badge } from '@/components/ui/badge';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';
import { formatEmployeeName } from '@/lib/utils/formattingUtils';

interface TaskCardProps {
  task: Task;
}

const getStatusColor = (status: Task['status']) => {
  switch (status) {
    case 'Assigned': {
      return 'bg-blue-500/20 text-blue-700 border-blue-500/30 dark:text-blue-400 dark:bg-blue-500/10 dark:border-blue-500/20';
    }
    case 'Cancelled': {
      return 'bg-red-500/20 text-red-700 border-red-500/30 dark:text-red-400 dark:bg-red-500/10 dark:border-red-500/20';
    }
    case 'Completed': {
      return 'bg-green-500/20 text-green-700 border-green-500/30 dark:text-green-400 dark:bg-green-500/10 dark:border-green-500/20';
    }
    case 'In_Progress': {
      return 'bg-indigo-500/20 text-indigo-700 border-indigo-500/30 dark:text-indigo-400 dark:bg-indigo-500/10 dark:border-indigo-500/20';
    }
    case 'Pending': {
      return 'bg-yellow-500/20 text-yellow-700 border-yellow-500/30 dark:text-yellow-400 dark:bg-yellow-500/10 dark:border-yellow-500/20';
    }
    default: {
      return 'bg-gray-500/20 text-gray-700 border-gray-500/30 dark:text-gray-400 dark:bg-gray-500/10 dark:border-gray-500/20';
    }
  }
};

const getPriorityColor = (priority: Task['priority']) => {
  switch (priority) {
    case 'High': {
      return 'bg-red-500/20 text-red-700 border-red-500/30 dark:text-red-400 dark:bg-red-500/10 dark:border-red-500/20';
    }
    case 'Low': {
      return 'bg-green-500/20 text-green-700 border-green-500/30 dark:text-green-400 dark:bg-green-500/10 dark:border-green-500/20';
    }
    case 'Medium': {
      return 'bg-yellow-500/20 text-yellow-700 border-yellow-500/30 dark:text-yellow-400 dark:bg-yellow-500/10 dark:border-yellow-500/20';
    }
    default: {
      return 'bg-gray-500/20 text-gray-700 border-gray-500/30 dark:text-gray-400 dark:bg-gray-500/10 dark:border-gray-500/20';
    }
  }
};

const formatDate = (dateString: string | undefined) => {
  if (!dateString) return 'N/A';
  try {
    return format(parseISO(dateString), 'MMM d, yyyy HH:mm');
  } catch {
    return 'Invalid Date';
  }
};

export default function TaskCard({ task }: TaskCardProps) {
  // Note: Task domain type has IDs, not full objects
  // For now, we'll just show the IDs. In a full implementation,
  // you'd need to fetch the related data or include it in the Task type
  const hasStaffAssignment = !!task.staffEmployeeId;
  const hasDriverAssignment = !!task.driverEmployeeId;
  const hasVehicleAssignment = !!task.vehicleId;

  return (
    <Card className="flex h-full flex-col overflow-hidden border-border/60 bg-card shadow-md">
      <CardHeader className="p-5">
        <div className="flex items-start justify-between gap-2">
          <CardTitle
            className="line-clamp-2 text-lg font-semibold text-primary"
            title={task.description}
          >
            {task.description}
          </CardTitle>
          <div className="flex shrink-0 flex-col items-end gap-1">
            <Badge
              className={cn(
                'text-xs py-1 px-2 font-semibold',
                getStatusColor(task.status)
              )}
            >
              {task.status}
            </Badge>
            <Badge
              className={cn(
                'text-xs py-1 px-2 font-semibold',
                getPriorityColor(task.priority)
              )}
            >
              {task.priority} Priority
            </Badge>
          </div>
        </div>
        <CardDescription className="flex items-center pt-1 text-sm text-muted-foreground">
          <MapPin className="mr-1.5 size-4 shrink-0 text-accent" />
          {task.location}
        </CardDescription>
      </CardHeader>
      <CardContent className="flex grow flex-col p-5">
        <Separator className="my-3 bg-border/50" />
        <div className="grow space-y-2.5 text-sm text-foreground">
          <div className="flex items-center">
            <CalendarDays className="mr-2.5 size-4 shrink-0 text-accent" />
            <div>
              <span className="text-muted-foreground">Start: </span>
              <strong className="font-semibold">
                {formatDate(task.dateTime)}
              </strong>
            </div>
          </div>
          {task.deadline && (
            <div className="flex items-center">
              <Clock className="mr-2.5 size-4 shrink-0 text-accent" />
              <div>
                <span className="text-muted-foreground">Deadline: </span>
                <strong className="font-semibold">
                  {formatDate(task.deadline)}
                </strong>
              </div>
            </div>
          )}
          <div className="flex items-center">
            <Clock className="mr-2.5 size-4 shrink-0 text-accent" />
            <div>
              <span className="text-muted-foreground">Duration: </span>
              <strong className="font-semibold">
                {task.estimatedDuration} mins
              </strong>
            </div>
          </div>
          {/* Display staff assignment */}
          {hasStaffAssignment && (
            <div className="flex items-center">
              <User className="mr-2.5 size-4 shrink-0 text-accent" />
              <div>
                <span className="text-muted-foreground">Staff: </span>
                <strong className="font-semibold">
                  {task.staffEmployee
                    ? formatEmployeeName(task.staffEmployee)
                    : `ID: ${task.staffEmployeeId}`}
                </strong>
              </div>
            </div>
          )}

          {/* Display driver assignment */}
          {hasDriverAssignment && (
            <div className="flex items-center">
              <User className="mr-2.5 size-4 shrink-0 text-accent" />
              <div>
                <span className="text-muted-foreground">Driver: </span>
                <strong className="font-semibold">
                  {task.driverEmployee
                    ? formatEmployeeName(task.driverEmployee)
                    : `ID: ${task.driverEmployeeId}`}
                </strong>
              </div>
            </div>
          )}

          {/* Display vehicle assignment */}
          {hasVehicleAssignment && (
            <div className="flex items-center">
              <Car className="mr-2.5 size-4 shrink-0 text-accent" />
              <div>
                <span className="text-muted-foreground">Vehicle: </span>
                <strong className="font-semibold">
                  {task.vehicle
                    ? `${task.vehicle.make} ${task.vehicle.model} (${
                        task.vehicle.licensePlate || `ID: ${task.vehicle.id}`
                      })`
                    : `ID: ${task.vehicleId}`}
                </strong>
              </div>
            </div>
          )}

          {/* Show unassigned warning if no staff */}
          {!hasStaffAssignment &&
            task.status !== 'Completed' &&
            task.status !== 'Cancelled' && (
              <div className="flex items-center">
                <AlertTriangle className="mr-2.5 size-4 shrink-0 text-destructive" />
                <strong className="font-semibold text-destructive">
                  No Staff Assigned
                </strong>
              </div>
            )}
        </div>
        {task.notes && (
          <p
            className="mt-3 line-clamp-2 border-t border-dashed border-border/50 pt-2 text-xs text-muted-foreground"
            title={task.notes}
          >
            {task.notes}
          </p>
        )}
      </CardContent>
      <CardFooter className="border-t border-border/60 bg-muted/20 p-4">
        <ActionButton
          actionType="tertiary"
          asChild
          className="w-full"
          icon={<ArrowRight className="size-4" />}
        >
          <Link href={`/tasks/${task.id}`}>View Details</Link>
        </ActionButton>
      </CardFooter>
    </Card>
  );
}
