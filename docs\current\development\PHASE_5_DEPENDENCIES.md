# Phase 5 Advanced Features - Required Dependencies

## Overview

Phase 5 introduces advanced export capabilities with chart capture functionality. The following dependencies need to be installed to support the enhanced features.

## Required Dependencies

### 1. Chart Capture Dependencies

```bash
# Install html2canvas for chart capture
npm install html2canvas

# Install jsPDF for advanced PDF generation
npm install jspdf

# Install type definitions
npm install --save-dev @types/html2canvas
```

### 2. Date Formatting (Already Available)

```bash
# date-fns is already installed for date formatting in collaborative features
# No additional installation needed
```

## Dependency Details

### html2canvas
- **Purpose**: Captures DOM elements (charts, dashboards) as images
- **Usage**: Used in `exportDashboardToPDF` and `exportChartToPDF` methods
- **Features**: 
  - High DPI support (scale: 2)
  - CORS support for external resources
  - Background color control
  - Foreign object rendering

### jsPDF
- **Purpose**: Advanced PDF generation with programmatic control
- **Usage**: Used alongside html2canvas for dashboard PDF exports
- **Features**:
  - Multiple page support
  - Custom headers and footers
  - Image embedding
  - Text formatting
  - Landscape/portrait orientation

## Installation Commands

Run the following commands in the frontend directory:

```bash
cd frontend

# Install production dependencies
npm install html2canvas jspdf

# Install development dependencies
npm install --save-dev @types/html2canvas
```

## Bundle Impact

### Lazy Loading Implementation
The enhanced export functionality uses dynamic imports to minimize bundle impact:

```typescript
// Lazy load html2canvas
const html2canvas = (await import('html2canvas')).default;

// Lazy load jsPDF
const jsPDF = (await import('jspdf')).default;
```

### Bundle Size Impact
- **html2canvas**: ~500KB (loaded only when exporting)
- **jsPDF**: ~200KB (loaded only when exporting)
- **Total**: ~700KB additional bundle size, but only loaded on-demand

## Existing Dependencies Leveraged

### Already Available
- **@react-pdf/renderer**: Used for existing PDF export functionality
- **xlsx**: Used for Excel export functionality
- **zustand**: Used for dashboard customization store
- **@tanstack/react-query**: Used for cache management in collaborative features
- **date-fns**: Used for date formatting in comments
- **socket.io-client**: Used via existing WebSocket infrastructure

## Architecture Integration

### No Duplication
- Enhanced export functionality extends existing `useExport` hook
- Dashboard customization extends existing Zustand store patterns
- Collaborative features leverage existing WebSocket infrastructure
- All new features integrate with existing data layer

### SOLID Principles Maintained
- **Single Responsibility**: Each service handles one specific concern
- **Open/Closed**: Services are open for extension, closed for modification
- **Dependency Inversion**: All services depend on abstractions

## Verification

After installing dependencies, verify the installation:

```bash
# Check if dependencies are installed
npm list html2canvas jspdf

# Run TypeScript check
npm run type-check

# Run build to ensure no issues
npm run build
```

## Troubleshooting

### Common Issues

1. **TypeScript Errors**: Ensure type definitions are installed
2. **Bundle Size**: Verify lazy loading is working correctly
3. **CORS Issues**: Ensure html2canvas CORS settings are correct

### Solutions

```bash
# If TypeScript errors persist
npm install --save-dev @types/html2canvas @types/jspdf

# Clear node_modules and reinstall if needed
rm -rf node_modules package-lock.json
npm install
```

## Next Steps

1. Install the required dependencies
2. Run the application to test enhanced export functionality
3. Test dashboard customization features
4. Test collaborative features with WebSocket connection
5. Verify all Phase 5 features are working correctly

The enhanced reporting system is now ready for production use with all Phase 5 advanced features!
