'use client';

import { <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON> } from 'lucide-react';
import { useTheme as useNextTheme } from 'next-themes';
import React from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { useTheme } from '@/hooks/ui/useTheme';

/**
 * Theme options with display information
 */
const THEME_OPTIONS = [
  {
    description: 'Clean and bright interface',
    icon: Sun,
    label: 'Light',
    preview: 'bg-white border-gray-200 text-gray-900',
    value: 'light' as const,
  },
  {
    description: 'Easy on the eyes in low light',
    icon: Moon,
    label: 'Dark',
    preview: 'bg-gray-900 border-gray-700 text-white',
    value: 'dark' as const,
  },
  {
    description: 'Follows your device settings',
    icon: Monitor,
    label: 'System',
    preview:
      'bg-gradient-to-r from-white to-gray-900 border-gray-400 text-gray-600',
    value: 'system' as const,
  },
];

/**
 * Theme Settings Component
 * Provides comprehensive theme management interface
 */
export const ThemeSettings: React.FC = () => {
  const { currentTheme } = useTheme();
  const {
    setTheme: setNextTheme,
    systemTheme,
    theme: nextTheme,
  } = useNextTheme();
  const { setTheme: setZustandTheme } = useTheme();

  // Enhanced theme switching that updates both systems
  const handleThemeChange = (newTheme: 'dark' | 'light' | 'system') => {
    setNextTheme(newTheme);
    if (newTheme === 'system') {
      // If system theme is selected, update Zustand with the actual system preference
      const systemPreference = systemTheme || 'light';
      setZustandTheme(systemPreference as 'dark' | 'light');
    } else {
      setZustandTheme(newTheme);
    }
  };

  const effectiveTheme = nextTheme || 'system';

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Palette className="size-5" />
          Theme Preferences
        </CardTitle>
        <CardDescription>
          Choose your preferred color scheme and appearance settings
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Current Theme Display */}
        <div className="flex items-center justify-between rounded-lg bg-muted/50 p-4">
          <div>
            <p className="font-medium">Current Theme</p>
            <p className="text-sm text-muted-foreground">
              Applied across the entire application
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Badge className="capitalize" variant="secondary">
              {effectiveTheme}
            </Badge>
            {effectiveTheme === 'system' && systemTheme && (
              <Badge className="text-xs" variant="outline">
                System: {systemTheme}
              </Badge>
            )}
          </div>
        </div>

        {/* Theme Options */}
        <div className="space-y-3">
          <h4 className="text-sm font-medium uppercase tracking-wide text-muted-foreground">
            Available Themes
          </h4>
          {THEME_OPTIONS.map(option => {
            const IconComponent = option.icon;
            const isSelected = effectiveTheme === option.value;

            return (
              <div
                className={`
                  relative cursor-pointer rounded-lg border p-4 transition-all
                  ${
                    isSelected
                      ? 'border-primary bg-primary/5 ring-1 ring-primary/20'
                      : 'border-border hover:border-primary/50 hover:bg-muted/30'
                  }
                `}
                key={option.value}
                onClick={() => handleThemeChange(option.value)}
              >
                <div className="flex items-start justify-between">
                  <div className="flex flex-1 items-start gap-3">
                    <div className="flex size-10 items-center justify-center rounded-lg border bg-background">
                      <IconComponent className="size-5" />
                    </div>
                    <div className="flex-1">
                      <div className="mb-1 flex items-center gap-2">
                        <h5 className="font-medium">{option.label}</h5>
                        {isSelected && (
                          <Check className="size-4 text-primary" />
                        )}
                      </div>
                      <p className="mb-3 text-sm text-muted-foreground">
                        {option.description}
                      </p>
                      {/* Theme Preview */}
                      <div
                        className={`
                        h-8 w-full rounded border-2 ${option.preview}
                        flex items-center justify-center text-xs font-medium
                      `}
                      >
                        Preview
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Quick Action Buttons */}
        <div className="flex items-center justify-between border-t pt-4">
          <div className="flex gap-2">
            {THEME_OPTIONS.map(option => {
              const IconComponent = option.icon;
              return (
                <Button
                  className="flex items-center gap-2"
                  key={option.value}
                  onClick={() => handleThemeChange(option.value)}
                  size="sm"
                  variant={
                    effectiveTheme === option.value ? 'default' : 'outline'
                  }
                >
                  <IconComponent className="size-4" />
                  {option.label}
                </Button>
              );
            })}
          </div>
          <Button
            className="text-muted-foreground"
            onClick={() => handleThemeChange('system')}
            size="sm"
            variant="ghost"
          >
            Reset to System
          </Button>
        </div>

        {/* Theme Information */}
        <div className="rounded-lg border bg-background p-4">
          <h5 className="mb-2 font-medium">Theme Information</h5>
          <div className="space-y-2 text-sm text-muted-foreground">
            <div className="flex justify-between">
              <span>Selected Theme:</span>
              <span className="font-medium capitalize">{effectiveTheme}</span>
            </div>
            <div className="flex justify-between">
              <span>Zustand Store:</span>
              <span className="font-medium capitalize">{currentTheme}</span>
            </div>
            {systemTheme && (
              <div className="flex justify-between">
                <span>System Preference:</span>
                <span className="font-medium capitalize">{systemTheme}</span>
              </div>
            )}
            <div className="flex justify-between">
              <span>Auto-sync:</span>
              <span className="font-medium">
                {effectiveTheme === 'system' ? 'Enabled' : 'Disabled'}
              </span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

/**
 * Compact Theme Selector for toolbars or settings panels
 */
export const ThemeSelector: React.FC = () => {
  const { setTheme: setNextTheme, theme: nextTheme } = useNextTheme();
  const { setTheme: setZustandTheme } = useTheme();

  const handleThemeChange = (newTheme: 'dark' | 'light' | 'system') => {
    setNextTheme(newTheme);
    if (newTheme !== 'system') {
      setZustandTheme(newTheme);
    }
  };

  return (
    <div className="flex items-center gap-2">
      <Palette className="size-4 text-muted-foreground" />
      <select
        className="
          rounded border border-input bg-background px-2 py-1 text-sm
          focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2
        "
        onChange={e =>
          handleThemeChange(e.target.value as 'dark' | 'light' | 'system')
        }
        value={nextTheme || 'system'}
      >
        <option value="light">Light</option>
        <option value="dark">Dark</option>
        <option value="system">System</option>
      </select>
    </div>
  );
};

/**
 * Icon-based Theme Toggle for minimal UI
 */
export const ThemeToggleCompact: React.FC = () => {
  const { setTheme: setNextTheme, theme: nextTheme } = useNextTheme();
  const { setTheme: setZustandTheme } = useTheme();

  const cycleTheme = () => {
    const themes: ('dark' | 'light' | 'system')[] = ['light', 'dark', 'system'];
    const currentIndex = themes.indexOf((nextTheme as any) || 'system');
    const nextIndex = (currentIndex + 1) % themes.length;
    const newTheme = themes[nextIndex];
    if (newTheme) {
      setNextTheme(newTheme);
      if (setZustandTheme && (newTheme === 'light' || newTheme === 'dark')) {
        setZustandTheme(newTheme);
      }
    }
  };

  const getIcon = () => {
    switch (nextTheme) {
      case 'dark': {
        return Moon;
      }
      case 'light': {
        return Sun;
      }
      default: {
        return Monitor;
      }
    }
  };

  const IconComponent = getIcon();

  return (
    <Button
      className="flex items-center gap-2"
      onClick={cycleTheme}
      size="sm"
      title={`Current: ${nextTheme || 'system'}. Click to cycle.`}
      variant="ghost"
    >
      <IconComponent className="size-4" />
      <span className="text-xs capitalize">{nextTheme || 'system'}</span>
    </Button>
  );
};
