import { ReportingDataService } from '../../../data/services/ReportingDataService';
import { reportingDataService } from '../../../data/services/ReportingDataService';
import type { ReportingFilters } from '../../../data/types/reporting';
import type {
  DelegationStatusPrisma,
  TaskStatusPrisma,
  TaskPriorityPrisma,
} from '@/lib/types/domain';

// Mock fetch globally
global.fetch = jest.fn();

describe('ReportingDataService', () => {
  let service: ReportingDataService;

  beforeEach(() => {
    service = new ReportingDataService();
    // Reset all mocks before each test
    jest.resetAllMocks();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getDelegationAnalytics', () => {
    it('should fetch delegation analytics with correct parameters', async () => {
      // Arrange
      const mockFilters: ReportingFilters = {
        dateRange: { from: new Date('2024-01-01'), to: new Date('2024-12-31') },
        status: [
          'PENDING' as DelegationStatusPrisma,
          'APPROVED' as DelegationStatusPrisma,
        ],
        locations: ['New York', 'London'],
        employees: [1, 2, 3],
        vehicles: [1, 2],
      };

      const mockResponse = {
        status: 'success',
        data: {
          totalCount: 100,
          statusDistribution: [
            { status: 'PENDING', count: 30, percentage: 30, color: '#orange' },
            { status: 'APPROVED', count: 70, percentage: 70, color: '#green' },
          ],
          trendData: [],
          locationMetrics: [],
          summary: {
            totalDelegations: 100,
            activeDelegations: 30,
            completedDelegations: 70,
            totalDelegates: 10,
            averageDuration: 5.5,
            completionRate: 0.7,
          },
        },
      };

      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      // Act
      const result = await service.getDelegationAnalytics(mockFilters);

      // Assert
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/reporting/delegations/analytics'),
        expect.objectContaining({
          method: 'GET',
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
            Authorization: expect.stringContaining('Bearer'),
          }),
        })
      );
      expect(result).toBeDefined();
      expect(result.totalCount).toBe(100);
    });

    it('should handle API errors gracefully', async () => {
      // Arrange
      const mockFilters: ReportingFilters = {
        dateRange: { from: new Date(), to: new Date() },
        status: [],
        locations: [],
        employees: [],
        vehicles: [],
      };

      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error',
      });

      // Act & Assert
      await expect(service.getDelegationAnalytics(mockFilters)).rejects.toThrow(
        'Failed to fetch delegation analytics: Internal Server Error'
      );
    });

    it('should handle network errors', async () => {
      // Arrange
      const mockFilters: ReportingFilters = {
        dateRange: { from: new Date(), to: new Date() },
        status: [],
        locations: [],
        employees: [],
        vehicles: [],
      };

      (global.fetch as jest.Mock).mockRejectedValueOnce(
        new Error('Network error')
      );

      // Act & Assert
      await expect(service.getDelegationAnalytics(mockFilters)).rejects.toThrow(
        'Failed to load delegation analytics: Network error'
      );
    });
  });

  describe('getTaskAnalytics', () => {
    it('should fetch task analytics successfully', async () => {
      // Arrange
      const mockFilters: ReportingFilters = {
        dateRange: { from: new Date('2024-01-01'), to: new Date('2024-12-31') },
        status: [],
        locations: [],
        employees: [],
        vehicles: [],
        taskStatus: [
          'IN_PROGRESS' as TaskStatusPrisma,
          'COMPLETED' as TaskStatusPrisma,
        ],
        taskPriority: [
          'HIGH' as TaskPriorityPrisma,
          'MEDIUM' as TaskPriorityPrisma,
        ],
      };

      const mockResponse = {
        status: 'success',
        data: {
          totalCount: 50,
          statusDistribution: [
            {
              status: 'IN_PROGRESS',
              count: 20,
              percentage: 40,
              color: '#blue',
            },
            { status: 'COMPLETED', count: 30, percentage: 60, color: '#green' },
          ],
          priorityDistribution: [],
          completionRate: 0.6,
          overdueCount: 5,
          averageCompletionTime: 3.2,
          assignmentMetrics: [],
        },
      };

      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      // Act
      const result = await service.getTaskAnalytics(mockFilters);

      // Assert
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/reporting/tasks/analytics'),
        expect.objectContaining({
          method: 'GET',
        })
      );
      expect(result).toBeDefined();
      expect(result.totalCount).toBe(50);
    });
  });

  describe('getVehicleAnalytics', () => {
    it('should fetch vehicle analytics successfully', async () => {
      // Arrange
      const mockFilters: ReportingFilters = {
        dateRange: { from: new Date('2024-01-01'), to: new Date('2024-12-31') },
        status: [],
        locations: ['Depot A', 'Depot B'],
        employees: [],
        vehicles: [1, 2, 3],
      };

      const mockResponse = {
        status: 'success',
        data: {
          totalCount: 25,
          serviceHistory: [],
          costAnalysis: {
            totalCost: 15000,
            averageCostPerService: 300,
            costByType: [],
            monthlyTrend: [],
          },
          utilizationMetrics: [],
          maintenanceSchedule: [],
        },
      };

      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      // Act
      const result = await service.getVehicleAnalytics(mockFilters);

      // Assert
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/reporting/vehicles/analytics'),
        expect.objectContaining({
          method: 'GET',
        })
      );
      expect(result).toBeDefined();
      expect(result.totalCount).toBe(25);
    });
  });

  describe('getEmployeeAnalytics', () => {
    it('should fetch employee analytics successfully', async () => {
      // Arrange
      const mockFilters: ReportingFilters = {
        dateRange: { from: new Date('2024-01-01'), to: new Date('2024-12-31') },
        status: [],
        locations: ['Site A', 'Site B'],
        employees: [1, 2, 3],
        vehicles: [],
      };

      const mockResponse = {
        status: 'success',
        data: {
          totalCount: 75,
          performanceMetrics: [],
          delegationHistory: [],
          taskAssignments: [],
          availabilityMetrics: [],
        },
      };

      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      // Act
      const result = await service.getEmployeeAnalytics(mockFilters);

      // Assert
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/reporting/employees/analytics'),
        expect.objectContaining({
          method: 'GET',
        })
      );
      expect(result).toBeDefined();
      expect(result.totalCount).toBe(75);
    });
  });

  describe('getCrossEntityAnalytics', () => {
    it('should fetch cross-entity analytics successfully', async () => {
      // Arrange
      const mockFilters: ReportingFilters = {
        dateRange: { from: new Date('2024-01-01'), to: new Date('2024-12-31') },
        status: [],
        locations: [],
        employees: [],
        vehicles: [],
        includeCrossEntityCorrelations: true,
      };

      const mockResponse = {
        status: 'success',
        data: {
          correlations: {
            delegationTaskCorrelation: 0.75,
            vehicleUtilizationCorrelation: 0.68,
            employeePerformanceCorrelation: 0.82,
            costEfficiencyCorrelation: 0.71,
          },
          summary: {
            totalEntities: 4,
            activeEntities: 4,
            performanceScore: 0.85,
            efficiencyRating: 0.78,
            recommendations: [
              'Optimize vehicle utilization',
              'Balance employee workload',
            ],
          },
        },
      };

      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      // Act
      const result = await service.getCrossEntityAnalytics(mockFilters);

      // Assert
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/reporting/cross-entity/analytics'),
        expect.objectContaining({
          method: 'GET',
        })
      );
      expect(result).toBeDefined();
      expect(result.correlations).toBeDefined();
    });
  });

  describe('getTaskMetrics', () => {
    it('should fetch task metrics for specific delegations', async () => {
      // Arrange
      const delegationIds = ['del-1', 'del-2', 'del-3'];

      const mockResponse = {
        status: 'success',
        data: {
          totalTasks: 15,
          completedTasks: 10,
          pendingTasks: 3,
          inProgressTasks: 2,
          averageCompletionTime: 2.5,
          tasksByPriority: [],
          tasksByStatus: [],
        },
      };

      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      // Act
      const result = await service.getTaskMetrics(delegationIds);

      // Assert
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining(
          '/api/reporting/tasks/metrics?delegationIds=del-1,del-2,del-3'
        ),
        expect.objectContaining({
          method: 'GET',
        })
      );
      expect(result).toBeDefined();
      expect(result.totalTasks).toBe(15);
    });

    it('should fetch all task metrics when no delegation IDs provided', async () => {
      // Arrange
      const mockResponse = {
        status: 'success',
        data: {
          totalTasks: 100,
          completedTasks: 75,
          pendingTasks: 15,
          inProgressTasks: 10,
          averageCompletionTime: 3.1,
          tasksByPriority: [],
          tasksByStatus: [],
        },
      };

      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      // Act
      const result = await service.getTaskMetrics();

      // Assert
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/reporting/tasks/metrics?'),
        expect.objectContaining({
          method: 'GET',
        })
      );
      expect(result).toBeDefined();
      expect(result.totalTasks).toBe(100);
    });
  });

  describe('Singleton Pattern', () => {
    it('should return the same instance when using the singleton', () => {
      // Act
      const instance1 = reportingDataService;
      const instance2 = reportingDataService;

      // Assert
      expect(instance1).toBe(instance2);
      expect(instance1).toBeInstanceOf(ReportingDataService);
    });
  });

  describe('Error Handling', () => {
    it('should handle malformed JSON responses', async () => {
      // Arrange
      const mockFilters: ReportingFilters = {
        dateRange: { from: new Date(), to: new Date() },
        status: [],
        locations: [],
        employees: [],
        vehicles: [],
      };

      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => {
          throw new Error('Invalid JSON');
        },
      });

      // Act & Assert
      await expect(service.getDelegationAnalytics(mockFilters)).rejects.toThrow(
        'Failed to load delegation analytics: Invalid JSON'
      );
    });

    it('should handle timeout scenarios', async () => {
      // Arrange
      const mockFilters: ReportingFilters = {
        dateRange: { from: new Date(), to: new Date() },
        status: [],
        locations: [],
        employees: [],
        vehicles: [],
      };

      (global.fetch as jest.Mock).mockImplementationOnce(
        () =>
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error('Request timeout')), 100)
          )
      );

      // Act & Assert
      await expect(service.getDelegationAnalytics(mockFilters)).rejects.toThrow(
        'Failed to load delegation analytics: Request timeout'
      );
    });
  });
});
