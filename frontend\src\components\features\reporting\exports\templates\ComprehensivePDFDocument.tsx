/**
 * @component ComprehensivePDFDocument
 * @description React PDF component for generating comprehensive reports
 *
 * Uses react-pdf to create professional PDF documents with all entity data
 * Follows SOLID principles with single responsibility for PDF document structure
 */

import React from 'react';
import {
  Document,
  Page,
  Text,
  View,
  StyleSheet,
  Image,
  Font,
} from '@react-pdf/renderer';
import type { ComprehensiveReportData, ReportTemplate } from '../../data/types/export';

// Define styles with proper typing
const styles = StyleSheet.create({
  page: {
    flexDirection: 'column',
    backgroundColor: '#FFFFFF',
    padding: 30,
    fontFamily: 'Helvetica',
  },
  header: {
    fontSize: 24,
    marginBottom: 20,
    textAlign: 'center',
    color: '#2563eb',
    fontWeight: 'bold',
  },
  subHeader: {
    fontSize: 16,
    marginBottom: 15,
    textAlign: 'center',
    color: '#64748b',
  },
  section: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 16,
    marginBottom: 10,
    color: '#1e293b',
    fontWeight: 'bold',
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0',
    paddingBottom: 5,
  },
  table: {
    width: 'auto',
    borderStyle: 'solid',
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  tableRow: {
    margin: 'auto',
    flexDirection: 'row',
  },
  tableCol: {
    width: '25%',
    borderStyle: 'solid',
    borderWidth: 1,
    borderColor: '#e2e8f0',
    padding: 5,
  },
  tableColHeader: {
    width: '25%',
    borderStyle: 'solid',
    borderWidth: 1,
    borderColor: '#e2e8f0',
    backgroundColor: '#f8fafc',
    padding: 5,
  },
  tableCell: {
    fontSize: 10,
    textAlign: 'center',
  },
  tableCellHeader: {
    fontSize: 10,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  footer: {
    position: 'absolute',
    fontSize: 10,
    bottom: 30,
    left: 30,
    right: 30,
    textAlign: 'center',
    color: '#64748b',
  },
  smallTitle: {
    fontSize: 14,
    marginBottom: 10,
    color: '#1e293b',
    fontWeight: 'bold',
  },
});

interface ComprehensivePDFDocumentProps {
  data: ComprehensiveReportData;
  template?: string | undefined;
}

export const ComprehensivePDFDocument: React.FC<
  ComprehensivePDFDocumentProps
> = ({ data, template }) => {
  const currentDate = new Date().toLocaleDateString();
  const headerText = 'Comprehensive Report';
  const footerText = 'Generated by WorkHub Reporting System';

  return (
    <Document>
      <Page size="A4" style={styles.page}>
        <Text style={styles.header}>{headerText}</Text>
        <Text style={styles.subHeader}>Generated on {currentDate}</Text>

        {/* Delegation Summary */}
        {data.delegations && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Delegation Summary</Text>
            <View style={styles.table}>
              <View style={styles.tableRow}>
                <View style={styles.tableColHeader}>
                  <Text style={styles.tableCellHeader}>Total Delegations</Text>
                </View>
                <View style={styles.tableColHeader}>
                  <Text style={styles.tableCellHeader}>Active Delegations</Text>
                </View>
                <View style={styles.tableColHeader}>
                  <Text style={styles.tableCellHeader}>
                    Completed Delegations
                  </Text>
                </View>
                <View style={styles.tableColHeader}>
                  <Text style={styles.tableCellHeader}>Completion Rate</Text>
                </View>
              </View>
              <View style={styles.tableRow}>
                <View style={styles.tableCol}>
                  <Text style={styles.tableCell}>
                    {data.delegations.summary?.totalDelegations || 0}
                  </Text>
                </View>
                <View style={styles.tableCol}>
                  <Text style={styles.tableCell}>
                    {data.delegations.summary?.activeDelegations || 0}
                  </Text>
                </View>
                <View style={styles.tableCol}>
                  <Text style={styles.tableCell}>
                    {data.delegations.summary?.completedDelegations || 0}
                  </Text>
                </View>
                <View style={styles.tableCol}>
                  <Text style={styles.tableCell}>
                    {data.delegations.summary?.completionRate?.toFixed(1) || 0}%
                  </Text>
                </View>
              </View>
            </View>

            {/* Status Distribution */}
            {data.delegations.statusDistribution && (
              <View style={styles.section}>
                <Text style={styles.smallTitle}>Status Distribution</Text>
                <View style={styles.table}>
                  <View style={styles.tableRow}>
                    <View style={styles.tableColHeader}>
                      <Text style={styles.tableCellHeader}>Status</Text>
                    </View>
                    <View style={styles.tableColHeader}>
                      <Text style={styles.tableCellHeader}>Count</Text>
                    </View>
                    <View style={styles.tableColHeader}>
                      <Text style={styles.tableCellHeader}>Percentage</Text>
                    </View>
                    <View style={styles.tableColHeader}>
                      <Text style={styles.tableCellHeader}>-</Text>
                    </View>
                  </View>
                  {data.delegations.statusDistribution.map(
                    (item: any, index: number) => (
                      <View key={index} style={styles.tableRow}>
                        <View style={styles.tableCol}>
                          <Text style={styles.tableCell}>
                            {item.status || ''}
                          </Text>
                        </View>
                        <View style={styles.tableCol}>
                          <Text style={styles.tableCell}>
                            {item.count || 0}
                          </Text>
                        </View>
                        <View style={styles.tableCol}>
                          <Text style={styles.tableCell}>
                            {item.percentage?.toFixed(1) || 0}%
                          </Text>
                        </View>
                        <View style={styles.tableCol}>
                          <Text style={styles.tableCell}>-</Text>
                        </View>
                      </View>
                    )
                  )}
                </View>
              </View>
            )}
          </View>
        )}

        {/* Vehicle Utilization */}
        {data.vehicles?.utilization && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Vehicle Utilization</Text>
            <View style={styles.table}>
              <View style={styles.tableRow}>
                <View style={styles.tableColHeader}>
                  <Text style={styles.tableCellHeader}>Vehicle ID</Text>
                </View>
                <View style={styles.tableColHeader}>
                  <Text style={styles.tableCellHeader}>Make</Text>
                </View>
                <View style={styles.tableColHeader}>
                  <Text style={styles.tableCellHeader}>Model</Text>
                </View>
                <View style={styles.tableColHeader}>
                  <Text style={styles.tableCellHeader}>Utilization %</Text>
                </View>
              </View>
              {data.vehicles.utilization
                .slice(0, 10)
                .map((vehicle: any, index: number) => (
                  <View key={index} style={styles.tableRow}>
                    <View style={styles.tableCol}>
                      <Text style={styles.tableCell}>
                        {vehicle.vehicleId || ''}
                      </Text>
                    </View>
                    <View style={styles.tableCol}>
                      <Text style={styles.tableCell}>{vehicle.make || ''}</Text>
                    </View>
                    <View style={styles.tableCol}>
                      <Text style={styles.tableCell}>
                        {vehicle.model || ''}
                      </Text>
                    </View>
                    <View style={styles.tableCol}>
                      <Text style={styles.tableCell}>
                        {vehicle.utilizationRate?.toFixed(1) || 0}%
                      </Text>
                    </View>
                  </View>
                ))}
            </View>
          </View>
        )}

        {/* Employee Performance */}
        {data.employees?.performance && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Employee Performance</Text>
            <View style={styles.table}>
              <View style={styles.tableRow}>
                <View style={styles.tableColHeader}>
                  <Text style={styles.tableCellHeader}>Employee ID</Text>
                </View>
                <View style={styles.tableColHeader}>
                  <Text style={styles.tableCellHeader}>Name</Text>
                </View>
                <View style={styles.tableColHeader}>
                  <Text style={styles.tableCellHeader}>Tasks Completed</Text>
                </View>
                <View style={styles.tableColHeader}>
                  <Text style={styles.tableCellHeader}>Performance Score</Text>
                </View>
              </View>
              {data.employees.performance
                .slice(0, 10)
                .map((employee: any, index: number) => (
                  <View key={index} style={styles.tableRow}>
                    <View style={styles.tableCol}>
                      <Text style={styles.tableCell}>
                        {employee.employeeId || ''}
                      </Text>
                    </View>
                    <View style={styles.tableCol}>
                      <Text style={styles.tableCell}>
                        {employee.name || ''}
                      </Text>
                    </View>
                    <View style={styles.tableCol}>
                      <Text style={styles.tableCell}>
                        {employee.tasksCompleted || 0}
                      </Text>
                    </View>
                    <View style={styles.tableCol}>
                      <Text style={styles.tableCell}>
                        {employee.performanceScore?.toFixed(1) || 0}
                      </Text>
                    </View>
                  </View>
                ))}
            </View>
          </View>
        )}

        <Text style={styles.footer}>{footerText}</Text>
      </Page>
    </Document>
  );
};
