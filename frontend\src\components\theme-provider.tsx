'use client';

import type { ThemeProviderProps } from 'next-themes/dist/types';

import {
  ThemeProvider as NextThemesProvider,
  useTheme as useNextTheme,
} from 'next-themes';
import React, { useEffect } from 'react';

import { useAppStore } from '@/lib/stores/zustand/appStore';

/**
 * Enhanced ThemeProvider that integrates Next-themes with Zustand store
 */
export function ThemeProvider({ children, ...props }: ThemeProviderProps) {
  return (
    <NextThemesProvider {...props}>
      <ThemeSync />
      {children}
    </NextThemesProvider>
  );
}

/**
 * Theme synchronization component that bridges Next-themes and Zustand store
 */
function ThemeSync() {
  const {
    setTheme: setNextTheme,
    systemTheme,
    theme: nextTheme,
  } = useNextTheme();
  const { currentTheme, setTheme: setZustandTheme } = useAppStore();
  const [isInitialized, setIsInitialized] = React.useState(false);

  // Initialize theme from Next-themes after hydration
  useEffect(() => {
    if (!isInitialized && nextTheme) {
      setIsInitialized(true);

      // Determine the effective theme
      const effectiveTheme = nextTheme === 'system' ? systemTheme : nextTheme;

      // Only update Zustand if it's different and not system
      if (
        effectiveTheme &&
        effectiveTheme !== 'system' &&
        effectiveTheme !== currentTheme
      ) {
        setZustandTheme(effectiveTheme as 'dark' | 'light');
      }
    }
  }, [nextTheme, systemTheme, currentTheme, setZustandTheme, isInitialized]);

  // Only sync from Zustand to Next-themes when explicitly changed via Zustand
  // This prevents infinite loops
  useEffect(() => {
    if (
      isInitialized &&
      currentTheme &&
      currentTheme !== nextTheme &&
      nextTheme !== 'system'
    ) {
      // Only update if the themes are actually different
      const effectiveNextTheme =
        nextTheme === 'system' ? systemTheme : nextTheme;
      if (currentTheme !== effectiveNextTheme) {
        setNextTheme(currentTheme);
      }
    }
  }, [currentTheme, nextTheme, systemTheme, setNextTheme, isInitialized]);

  return null;
}
