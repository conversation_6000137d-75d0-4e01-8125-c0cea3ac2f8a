/**
 * @file Response Wrapper Middleware
 * @description Middleware to standardize API responses across all endpoints
 * @module middleware/responseWrapper
 */

import type { NextFunction, Request, Response } from 'express';

import type {
  PaginationMetadata,
  ResponseWrapperConfig,
  StandardApiResponse,
} from '../types/response.types.js';

import { isStandardApiResponse } from '../types/response.types.js';
import logger from '../utils/logger.js';

/**
 * Default configuration for response wrapper
 */
const DEFAULT_CONFIG: ResponseWrapperConfig = {
  excludePaths: ['/health', '/metrics', '/api/health', '/api/metrics', '/swagger', '/api-docs'],
  includeRequestId: true,
  includeTimestamp: true,
  statusCodeMappings: {},
};

/**
 * Response wrapper middleware factory
 * Creates middleware that wraps API responses in standard format
 */
export function createResponseWrapper(config: Partial<ResponseWrapperConfig> = {}) {
  const finalConfig = { ...DEFAULT_CONFIG, ...config };

  return (req: Request, res: Response, next: NextFunction): void => {
    // Skip wrapping for excluded paths
    if (shouldExcludePath(req.path, finalConfig.excludePaths ?? [])) {
      next();
      return;
    }

    // Store original json method
    const originalJson = res.json.bind(res);

    // Override res.json to wrap responses
    res.json = function <T>(data: T): Response {
      try {
        // Skip wrapping if response is already wrapped
        if (isStandardApiResponse(data)) {
          logger.debug('Response already wrapped, skipping wrapper', {
            method: req.method,
            path: req.path,
          });
          return originalJson(data);
        }

        // Skip wrapping for error responses (handled by error middleware)
        if (res.statusCode >= 400) {
          return originalJson(data);
        }

        // Create wrapped response
        const wrappedResponse: StandardApiResponse<T> = {
          data,
          status: 'success',
          timestamp: new Date().toISOString(),
        };

        // Add request ID if enabled and available
        if (finalConfig.includeRequestId === true && req.headers['x-request-id']) {
          wrappedResponse.requestId = req.headers['x-request-id'] as string;
        }

        // Add pagination metadata if present in data
        if (data !== null && typeof data === 'object') {
          if (hasPaginationMetadata(data)) {
            wrappedResponse.pagination = extractPaginationMetadata(
              data as { pagination: PaginationMetadata },
            );
            // Remove pagination from the main data object
            const { pagination: _pagination, ...actualData } = data as {
              pagination: PaginationMetadata;
            };
            wrappedResponse.data = actualData as T; // Cast actualData back to T
          }
        }

        logger.debug('Response wrapped successfully', {
          hasPagination: !!wrappedResponse.pagination,
          hasRequestId: !!wrappedResponse.requestId,
          method: req.method,
          path: req.path,
          statusCode: res.statusCode,
        });

        return originalJson(wrappedResponse);
      } catch (error) {
        logger.error('Error in response wrapper', {
          error: error instanceof Error ? error.message : String(error),
          method: req.method,
          path: req.path,
        });
        // Fallback to original response if wrapping fails
        return originalJson(data);
      }
    };

    next();
  };
}

/**
 * Extract pagination metadata from data object with compatibility layer
 */
function extractPaginationMetadata(data: { pagination: PaginationMetadata }): PaginationMetadata {
  const { pagination } = data;
  const totalPages = Math.ceil(pagination.total / pagination.limit);

  // Compatibility layer: handle field name variations
  let hasPrevious = pagination.hasPrevious;
  if (hasPrevious === undefined && 'hasPrev' in pagination) {
    // Handle legacy field name
    hasPrevious = (pagination as any).hasPrev;
    logger.debug('Pagination field compatibility: converted hasPrev to hasPrevious', {
      originalPagination: pagination,
    });
  }

  return {
    hasNext: pagination.page < totalPages,
    hasPrevious: hasPrevious ?? pagination.page > 1, // Fallback calculation
    limit: pagination.limit,
    page: pagination.page,
    total: pagination.total,
    totalPages,
  };
}

/**
 * Check if data contains pagination metadata
 */
function hasPaginationMetadata(data: {
  pagination?: PaginationMetadata;
}): data is { pagination: PaginationMetadata } {
  return (
    typeof data === 'object' &&
    data !== null &&
    'pagination' in data &&
    data.pagination !== undefined &&
    data.pagination !== null &&
    typeof data.pagination === 'object' &&
    'page' in data.pagination &&
    'limit' in data.pagination &&
    'total' in data.pagination
  );
}

/**
 * Check if path should be excluded from wrapping
 */
function shouldExcludePath(path: string, excludePaths: string[]): boolean {
  return excludePaths.some(excludePath => {
    // Support both exact matches and prefix matches
    return path === excludePath || path.startsWith(excludePath + '/');
  });
}

/**
 * Default response wrapper middleware with standard configuration
 */
export const responseWrapper = createResponseWrapper();

/**
 * Response wrapper middleware for development with extended logging
 */
export const devResponseWrapper = createResponseWrapper({
  includeRequestId: true,
  includeTimestamp: true,
});

/**
 * Response wrapper middleware for production with minimal overhead
 */
export const prodResponseWrapper = createResponseWrapper({
  includeRequestId: false,
  includeTimestamp: false,
});

/**
 * Utility function to manually wrap a response
 * Useful for special cases where middleware can't be used
 */
export function wrapResponse<T>(
  data: T,
  requestId?: string,
  pagination?: PaginationMetadata,
): StandardApiResponse<T> {
  const response: StandardApiResponse<T> = {
    data,
    status: 'success',
    timestamp: new Date().toISOString(),
  };

  if (requestId) {
    response.requestId = requestId;
  }

  if (pagination) {
    response.pagination = pagination;
  }

  return response;
}
