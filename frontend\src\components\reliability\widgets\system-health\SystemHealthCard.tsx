/**
 * @file System health card widget component.
 * This component provides a comprehensive overview of system health status including
 * overall health indicators, dependency status, and key system metrics.
 * @module components/reliability/widgets/system-health/SystemHealthCard
 */

'use client';

import {
  Activity,
  AlertTriangle,
  CheckCircle,
  Clock,
  Database,
  Globe,
  Heart,
  Server,
  Shield,
  Wifi,
  XCircle,
} from 'lucide-react';
import React from 'react';

import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import {
  useSystemHealth,
  useDetailedSystemHealth,
} from '@/lib/stores/queries/useReliability';
import type { HealthStatus } from '@/lib/types/domain';
import { cn } from '@/lib/utils';

/**
 * Props for the SystemHealthCard component
 */
export interface SystemHealthCardProps {
  /** Optional CSS class name for styling */
  className?: string;
  /** Whether to show detailed health information */
  showDetails?: boolean;
  /** Whether to show dependency status */
  showDependencies?: boolean;
}

/**
 * System health card widget component.
 *
 * This component provides:
 * - Overall system health status indicator
 * - Key system metrics and performance indicators
 * - Dependency health status overview
 * - Real-time health monitoring with WebSocket updates
 *
 * Features:
 * - Real-time health status updates
 * - Color-coded health indicators
 * - Dependency status monitoring
 * - Performance metrics display
 * - Responsive design
 * - Accessibility support
 *
 * @param props - Component props
 * @returns JSX element representing the system health card
 */
export const SystemHealthCard: React.FC<SystemHealthCardProps> = ({
  className = '',
  showDetails = true,
  showDependencies = true,
}) => {
  const { data: systemHealth, isLoading, error } = useSystemHealth();
  const { data: detailedHealth } = useDetailedSystemHealth();

  /**
   * Get health status configuration
   */
  const getHealthConfig = (status: HealthStatus) => {
    switch (status) {
      case 'healthy':
        return {
          icon: CheckCircle,
          color: 'text-green-600 dark:text-green-400',
          bgColor: 'bg-green-100 dark:bg-green-900/20',
          label: 'Healthy',
          variant: 'default' as const,
        };
      case 'degraded':
        return {
          icon: AlertTriangle,
          color: 'text-yellow-600 dark:text-yellow-400',
          bgColor: 'bg-yellow-100 dark:bg-yellow-900/20',
          label: 'Degraded',
          variant: 'secondary' as const,
        };
      case 'unhealthy':
        return {
          icon: XCircle,
          color: 'text-red-600 dark:text-red-400',
          bgColor: 'bg-red-100 dark:bg-red-900/20',
          label: 'Unhealthy',
          variant: 'destructive' as const,
        };
      default:
        return {
          icon: Clock,
          color: 'text-gray-600 dark:text-gray-400',
          bgColor: 'bg-gray-100 dark:bg-gray-900/20',
          label: 'Unknown',
          variant: 'outline' as const,
        };
    }
  };

  /**
   * Calculate overall health score
   */
  const getHealthScore = () => {
    if (!detailedHealth?.checks) return 0;

    const checks = detailedHealth.checks;
    const healthyCount = Object.values(checks).filter(
      (check: any) => check.status === 'healthy'
    ).length;

    return Object.keys(checks).length > 0
      ? Math.round((healthyCount / Object.keys(checks).length) * 100)
      : 100;
  };

  /**
   * Format uptime duration
   */
  const formatUptime = (uptime?: number) => {
    if (!uptime) return 'Unknown';

    const days = Math.floor(uptime / (24 * 60 * 60 * 1000));
    const hours = Math.floor(
      (uptime % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000)
    );
    const minutes = Math.floor((uptime % (60 * 60 * 1000)) / (60 * 1000));

    if (days > 0) return `${days}d ${hours}h`;
    if (hours > 0) return `${hours}h ${minutes}m`;
    return `${minutes}m`;
  };

  // Handle loading state
  if (isLoading) {
    return (
      <div className={cn('space-y-4', className)}>
        <Card className="animate-pulse">
          <CardHeader>
            <div className="h-6 w-32 bg-muted rounded"></div>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="h-20 bg-muted rounded"></div>
              <div className="grid grid-cols-2 gap-4">
                <div className="h-16 bg-muted rounded"></div>
                <div className="h-16 bg-muted rounded"></div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Handle error state
  if (error || !systemHealth) {
    return (
      <div className={cn('flex items-center justify-center py-8', className)}>
        <Card className="w-full">
          <CardContent className="pt-6">
            <div className="text-center">
              <AlertTriangle className="mx-auto h-8 w-8 text-muted-foreground mb-2" />
              <p className="text-sm text-muted-foreground">
                Failed to load system health data
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  const healthConfig = getHealthConfig(systemHealth.status);
  const HealthIcon = healthConfig.icon;
  const healthScore = getHealthScore();

  return (
    <div className={cn('space-y-4', className)}>
      {/* Main Health Status Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Heart className="h-5 w-5 text-red-500" />
            System Health
            <Badge variant={healthConfig.variant}>{healthConfig.label}</Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Overall Status */}
            <div
              className={cn(
                'flex items-center gap-4 p-4 rounded-lg',
                healthConfig.bgColor
              )}
            >
              <div className="flex-shrink-0">
                <HealthIcon className={cn('h-8 w-8', healthConfig.color)} />
              </div>
              <div className="flex-1">
                <h3 className="font-semibold text-lg">
                  System is {healthConfig.label}
                </h3>
                <p className="text-sm text-muted-foreground">
                  All systems operational
                </p>
              </div>
              <div className="text-right">
                <div className={cn('text-2xl font-bold', healthConfig.color)}>
                  {healthScore}%
                </div>
                <p className="text-xs text-muted-foreground">Health Score</p>
              </div>
            </div>

            {/* Health Score Progress */}
            <div>
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium">Overall Health</span>
                <span className="text-sm text-muted-foreground">
                  {healthScore}%
                </span>
              </div>
              <Progress
                value={healthScore}
                className="h-2"
                aria-label={`System health score: ${healthScore}%`}
              />
            </div>

            {/* System Metrics */}
            {showDetails && (
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                {/* Uptime */}
                <div className="flex items-center gap-3 p-3 rounded-lg bg-muted/50">
                  <Clock className="h-5 w-5 text-blue-600" />
                  <div>
                    <p className="font-medium">Uptime</p>
                    <p className="text-sm text-muted-foreground">
                      {formatUptime(systemHealth.uptime)}
                    </p>
                  </div>
                </div>

                {/* Version */}
                <div className="flex items-center gap-3 p-3 rounded-lg bg-muted/50">
                  <Server className="h-5 w-5 text-purple-600" />
                  <div>
                    <p className="font-medium">Version</p>
                    <p className="text-sm text-muted-foreground">
                      {systemHealth.version || 'Unknown'}
                    </p>
                  </div>
                </div>

                {/* Environment */}
                <div className="flex items-center gap-3 p-3 rounded-lg bg-muted/50">
                  <Globe className="h-5 w-5 text-green-600" />
                  <div>
                    <p className="font-medium">Environment</p>
                    <p className="text-sm text-muted-foreground">
                      {systemHealth.environment || 'Unknown'}
                    </p>
                  </div>
                </div>

                {/* Last Check */}
                <div className="flex items-center gap-3 p-3 rounded-lg bg-muted/50">
                  <Activity className="h-5 w-5 text-orange-600" />
                  <div>
                    <p className="font-medium">Last Check</p>
                    <p className="text-sm text-muted-foreground">
                      {systemHealth.timestamp
                        ? new Date(systemHealth.timestamp).toLocaleTimeString()
                        : 'Unknown'}
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Component Health Status (if enabled and available) */}
      {showDependencies && detailedHealth?.checks && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              System Components
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {Object.entries(detailedHealth.checks).map(([name, check]) => {
                const checkConfig = getHealthConfig((check as any).status);
                const CheckIcon = checkConfig.icon;

                return (
                  <div
                    key={name}
                    className="flex items-center justify-between p-3 rounded-lg border"
                  >
                    <div className="flex items-center gap-3">
                      <div
                        className={cn('p-2 rounded-full', checkConfig.bgColor)}
                      >
                        <CheckIcon
                          className={cn('h-4 w-4', checkConfig.color)}
                        />
                      </div>
                      <div>
                        <p className="font-medium capitalize">
                          {name.replace(/([A-Z])/g, ' $1').trim()}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          {checkConfig.label}
                        </p>
                      </div>
                    </div>
                    <Badge variant={checkConfig.variant}>
                      {checkConfig.label}
                    </Badge>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

/**
 * Default export for the SystemHealthCard component
 */
export default SystemHealthCard;
