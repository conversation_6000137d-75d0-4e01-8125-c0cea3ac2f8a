/**
 * @file Employee-specific dashboard settings component
 * @module components/employees/dashboard/EmployeeDashboardSettings
 */

'use client';

import React from 'react';

import { DashboardSettings } from '@/components/dashboard/DashboardSettings';
import type { DashboardConfig } from '@/components/dashboard/types';
import type { Employee } from '@/lib/types/domain';
import { useDashboardStore } from '@/hooks/domain/useDashboardStore';

/**
 * Employee dashboard configuration
 */
const employeeDashboardConfig: DashboardConfig<Employee> = {
  entityType: 'employee',
  title: 'Employee Dashboard',
  description: 'Manage and track all employee information and assignments.',
  viewModes: ['cards', 'table', 'list'],
  defaultViewMode: 'table',
  enableBulkActions: true,
  enableExport: true,
  refreshInterval: 60000, // 1 minute for employee data
};

/**
 * Props for EmployeeDashboardSettings component
 */
interface EmployeeDashboardSettingsProps {
  className?: string;
}

/**
 * Employee-specific dashboard settings component that uses the generic
 * DashboardSettings component with employee-specific configuration.
 * 
 * This demonstrates how the generic dashboard framework can be reused
 * for different entities with their own specific configurations.
 * 
 * @param props - Component props
 * @returns JSX element representing the employee dashboard settings
 */
export const EmployeeDashboardSettings: React.FC<EmployeeDashboardSettingsProps> = ({
  className = '',
}) => {
  // Get employee dashboard store
  const dashboardStore = useDashboardStore('employee');
  const {
    layout,
    monitoring,
    setViewMode,
    setGridColumns,
    toggleCompactMode,
    setMonitoringEnabled,
    setRefreshInterval,
    toggleAutoRefresh,
    resetSettings,
  } = dashboardStore();

  return (
    <DashboardSettings
      config={employeeDashboardConfig}
      entityType="employee"
      layout={layout}
      monitoring={monitoring}
      setViewMode={setViewMode}
      setGridColumns={setGridColumns}
      toggleCompactMode={toggleCompactMode}
      setMonitoringEnabled={setMonitoringEnabled}
      setRefreshInterval={setRefreshInterval}
      toggleAutoRefresh={toggleAutoRefresh}
      resetSettings={resetSettings}
      className={className}
    />
  );
};

export default EmployeeDashboardSettings;
