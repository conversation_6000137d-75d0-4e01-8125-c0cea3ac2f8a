'use client';

import { format } from 'date-fns';
import { Loader2, Search } from 'lucide-react';
import { Calendar as CalendarIcon } from 'lucide-react';
import React, { useCallback, useEffect, useState } from 'react';

import type { AuditLog, AuditLogFilters } from '@/types';

import { Button } from '@/components/ui/button';
import { Calendar as ShadcnCalendar } from '@/components/ui/calendar';
import { Input } from '@/components/ui/input';
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { useToast } from '@/hooks/utils/use-toast';
import { adminService } from '@/lib/api/services/admin';
import { cn } from '@/lib/utils';

const ITEMS_PER_PAGE = 10; // This should ideally match the backend's default limit

export function AuditLogViewer() {
  const [logs, setLogs] = useState<AuditLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterAction, setFilterAction] = useState('');
  const [filterUserId, setFilterUserId] = useState('');
  const [startDate, setStartDate] = useState<Date | undefined>();
  const [endDate, setEndDate] = useState<Date | undefined>();
  const [currentPage, setCurrentPage] = useState(1);
  const [totalLogs, setTotalLogs] = useState(0);
  const { toast } = useToast();

  const totalPages = Math.ceil(totalLogs / ITEMS_PER_PAGE); // Calculate totalPages dynamically

  const fetchAuditLogs = useCallback(async () => {
    setLoading(true);
    try {
      const filters: AuditLogFilters = {
        action: filterAction,
        endDate: endDate,
        limit: ITEMS_PER_PAGE,
        page: currentPage,
        search: searchTerm,
        startDate: startDate,
        userId: filterUserId,
      };
      const response = await adminService.getAuditLogs(filters);
      setLogs(response.data);
      setTotalLogs(response.pagination.total); // Set totalLogs from response
    } catch (error: any) {
      toast({
        description: error.message ?? 'Failed to load audit log data.',
        title: 'Error fetching audit logs',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  }, [
    currentPage,
    searchTerm,
    filterAction,
    filterUserId,
    startDate,
    endDate,
    toast,
    totalLogs, // Add totalLogs to dependencies
  ]);

  useEffect(() => {
    fetchAuditLogs();
  }, [fetchAuditLogs]);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Audit Log Viewer</h2>
        <div className="flex items-center space-x-2">
          <div className="relative">
            <Search className="absolute left-2 top-1/2 size-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              className="w-[250px] pl-8"
              onChange={e => {
                setSearchTerm(e.target.value);
                setCurrentPage(1); // Reset to first page on search
              }}
              placeholder="Search logs (user, action, details)..."
              value={searchTerm}
            />
          </div>
          <Input
            className="w-[200px]"
            onChange={e => {
              setFilterAction(e.target.value);
              setCurrentPage(1); // Reset to first page on filter change
            }}
            placeholder="Filter by action (e.g., LOGIN)"
            value={filterAction}
          />
          <Input
            className="w-[200px]"
            onChange={e => {
              setFilterUserId(e.target.value);
              setCurrentPage(1); // Reset to first page on filter change
            }}
            placeholder="Filter by User ID"
            value={filterUserId}
          />
          <Popover>
            <PopoverTrigger asChild>
              <Button
                className={cn(
                  'w-[280px] justify-start text-left font-normal',
                  !startDate && !endDate && 'text-muted-foreground'
                )}
                variant={'outline'}
              >
                <CalendarIcon className="mr-2 size-4" />
                {startDate ? (
                  endDate ? (
                    <>
                      {format(startDate, 'LLL dd, y')} -{' '}
                      {format(endDate, 'LLL dd, y')}
                    </>
                  ) : (
                    format(startDate, 'LLL dd, y')
                  )
                ) : (
                  <span>Pick a date range</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="flex w-auto p-0">
              <ShadcnCalendar
                mode="range"
                numberOfMonths={2}
                onSelect={range => {
                  setStartDate(range?.from);
                  setEndDate(range?.to);
                  setCurrentPage(1); // Reset to first page on date filter change
                }}
                selected={{ from: startDate, to: endDate }}
              />
            </PopoverContent>
          </Popover>
          {(startDate ??
            endDate ??
            filterAction ??
            filterUserId ??
            searchTerm) && (
            <Button
              onClick={() => {
                setSearchTerm('');
                setFilterAction('');
                setFilterUserId('');
                setStartDate(undefined);
                setEndDate(undefined);
                setCurrentPage(1);
              }}
              variant="outline"
            >
              Clear Filters
            </Button>
          )}
        </div>
      </div>

      {loading ? (
        <div className="flex h-64 items-center justify-center">
          <Loader2 className="size-8 animate-spin text-primary" />
          <span className="ml-2 text-lg">Loading audit logs...</span>
        </div>
      ) : (
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[100px]">ID</TableHead>
                <TableHead>Timestamp</TableHead>
                <TableHead>User Email</TableHead>
                <TableHead>Action</TableHead>
                <TableHead>Details</TableHead>
                <TableHead>IP Address</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {logs.length === 0 ? (
                <TableRow>
                  <TableCell className="h-24 text-center" colSpan={6}>
                    No audit logs found.
                  </TableCell>
                </TableRow>
              ) : (
                logs.map(log => (
                  <TableRow key={log.id}>
                    <TableCell className="font-medium">{log.id}</TableCell>
                    <TableCell>
                      {new Date(log.timestamp).toLocaleString()}
                    </TableCell>
                    <TableCell>{log.userId || 'N/A'}</TableCell>
                    <TableCell>{log.action}</TableCell>
                    <TableCell className="max-w-[300px] truncate">
                      {log.details}
                    </TableCell>
                    <TableCell>{'N/A'}</TableCell> {/* ip_address removed */}
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      )}

      {totalLogs > 0 && (
        <Pagination>
          <PaginationContent>
            <PaginationItem>
              <PaginationPrevious
                className={
                  currentPage === 1 ? 'pointer-events-none opacity-50' : ''
                }
                onClick={e => {
                  e.preventDefault();
                  handlePageChange(currentPage - 1);
                }}
              />
            </PaginationItem>
            {Array.from({ length: totalPages }, (_, i) => (
              <PaginationItem key={i}>
                <PaginationLink
                  isActive={currentPage === i + 1}
                  onClick={e => {
                    e.preventDefault();
                    handlePageChange(i + 1);
                  }}
                >
                  {i + 1}
                </PaginationLink>
              </PaginationItem>
            ))}
            <PaginationItem>
              <PaginationNext
                className={
                  currentPage === totalPages
                    ? 'pointer-events-none opacity-50'
                    : ''
                }
                onClick={e => {
                  e.preventDefault();
                  handlePageChange(currentPage + 1);
                }}
              />
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      )}
    </div>
  );
}
