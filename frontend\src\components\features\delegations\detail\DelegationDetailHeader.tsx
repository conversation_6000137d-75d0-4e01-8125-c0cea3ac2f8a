/**
 * @file DelegationDetailHeader component for delegation detail page header
 * @module components/delegations/detail/DelegationDetailHeader
 */

import React from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Edit, Trash2, MoreHorizontal, FileText, Printer } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Sheet, Sheet<PERSON>ontent, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';
import { ActionButton } from '@/components/ui/action-button';
import { ViewReportButton } from '@/components/reports/ViewReportButton';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { formatDelegationStatusForDisplay } from '@/lib/utils/formattingUtils';
import { cn } from '@/lib/utils';
import type { Delegation, DelegationStatusPrisma } from '@/lib/types/domain';

interface DelegationDetailHeaderProps {
  delegation: Delegation;
  onDelete?: () => void;
  className?: string;
}

const getStatusColor = (status: DelegationStatusPrisma | undefined) => {
  switch (status) {
    case 'Cancelled': {
      return 'bg-red-500/20 text-red-700 border-red-500/30 dark:text-red-400 dark:bg-red-500/10 dark:border-red-500/20';
    }
    case 'Completed': {
      return 'bg-purple-500/20 text-purple-700 border-purple-500/30 dark:text-purple-400 dark:bg-purple-500/10 dark:border-purple-500/20';
    }
    case 'Confirmed': {
      return 'bg-green-500/20 text-green-700 border-green-500/30 dark:text-green-400 dark:bg-green-500/10 dark:border-green-500/20';
    }
    case 'In_Progress': {
      return 'bg-yellow-500/20 text-yellow-700 border-yellow-500/30 dark:text-yellow-400 dark:bg-yellow-500/10 dark:border-yellow-500/20';
    }
    case 'Planned': {
      return 'bg-blue-500/20 text-blue-700 border-blue-500/30 dark:text-blue-400 dark:bg-blue-500/10 dark:border-blue-500/20';
    }
    case 'No_details':
    default: {
      return 'bg-gray-500/20 text-gray-700 border-gray-500/30 dark:text-gray-400 dark:bg-gray-500/10 dark:border-gray-500/20';
    }
  }
};

/**
 * DelegationDetailHeader component for delegation detail page header
 * Provides responsive header with actions and navigation
 */
export function DelegationDetailHeader({ delegation, onDelete, className }: DelegationDetailHeaderProps) {
  const router = useRouter();

  const ActionButtons = () => (
    <>
      <ActionButton
        actionType="tertiary"
        icon={<ArrowLeft className="size-4" />}
        onClick={() => router.push('/delegations')}
        size="sm"
      >
        <span className="hidden sm:inline">Back to List</span>
        <span className="sm:hidden">Back</span>
      </ActionButton>
      <ActionButton
        actionType="secondary"
        asChild
        icon={<Edit className="size-4" />}
        size="sm"
      >
        <Link href={`/delegations/${delegation.id}/edit`}>
          Edit
        </Link>
      </ActionButton>
      <ViewReportButton
        href={`/delegations/${delegation.id}/report`}
      />
      {onDelete && (
        <AlertDialog>
          <AlertDialogTrigger asChild>
            <ActionButton
              actionType="danger"
              icon={<Trash2 className="size-4" />}
              size="sm"
            >
              <span className="hidden sm:inline">Delete Delegation</span>
              <span className="sm:hidden">Delete</span>
            </ActionButton>
          </AlertDialogTrigger>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Are you sure?</AlertDialogTitle>
              <AlertDialogDescription>
                This action cannot be undone. This will permanently delete the delegation and all its related information.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                className="bg-destructive hover:bg-destructive/90"
                onClick={onDelete}
              >
                Delete
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      )}
    </>
  );

  return (
    <div className={cn('border-b bg-white dark:bg-gray-800', className)}>
      <div className="container mx-auto px-4 py-6">
        {/* Mobile Layout */}
        <div className="flex items-center justify-between lg:hidden">
          <div className="min-w-0 flex-1">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white truncate">
              {delegation.eventName}
            </h1>
            <div className="mt-2 flex items-center space-x-2">
              <Badge
                className={cn(
                  'text-sm py-1 px-3 font-semibold',
                  getStatusColor(delegation.status)
                )}
              >
                {formatDelegationStatusForDisplay(delegation.status as DelegationStatusPrisma)}
              </Badge>
              {delegation.location && (
                <>
                  <Separator orientation="vertical" className="h-4" />
                  <span className="text-sm text-gray-600 dark:text-gray-400 truncate">
                    {delegation.location}
                  </span>
                </>
              )}
            </div>
          </div>
          <Sheet>
            <SheetTrigger asChild>
              <Button variant="outline" size="sm">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </SheetTrigger>
            <SheetContent side="bottom" className="h-[80vh]">
              <SheetHeader>
                <SheetTitle>Delegation Actions</SheetTitle>
              </SheetHeader>
              <div className="grid gap-4 py-4">
                <ActionButtons />
              </div>
            </SheetContent>
          </Sheet>
        </div>

        {/* Desktop Layout */}
        <div className="hidden lg:flex lg:items-center lg:justify-between">
          <div className="min-w-0 flex-1">
            <h1 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-white">
              {delegation.eventName}
            </h1>
            <div className="mt-2 flex items-center space-x-4">
              <Badge
                className={cn(
                  'text-sm py-1 px-3 font-semibold',
                  getStatusColor(delegation.status)
                )}
              >
                {formatDelegationStatusForDisplay(delegation.status as DelegationStatusPrisma)}
              </Badge>
              {delegation.location && (
                <>
                  <Separator orientation="vertical" className="h-4" />
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {delegation.location}
                  </span>
                </>
              )}
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <ActionButtons />
          </div>
        </div>
      </div>
    </div>
  );
}

export default DelegationDetailHeader;
