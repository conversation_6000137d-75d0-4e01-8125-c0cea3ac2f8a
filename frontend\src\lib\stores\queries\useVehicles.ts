/**
 * @file TanStack Query hooks for Vehicle-related data.
 * @module stores/queries/useVehicles
 */

import type { UseQueryOptions } from '@tanstack/react-query';

import { useMutation, useQueryClient } from '@tanstack/react-query';

import type { ApiError } from '@/lib/types/api';
import type { CreateVehicleData, Vehicle } from '@/lib/types/domain';

import { useCrudQuery } from '@/hooks/api/useSmartQuery';
import { useNotifications } from '@/hooks/ui/useNotifications';
import { VehicleTransformer } from '@/lib/transformers/vehicleTransformer';

import { vehicleApiService } from '../../api/services/apiServiceFactory'; // Use centralized service from factory

export const vehicleQueryKeys = {
  all: ['vehicles'] as const,
  detail: (id: number) => ['vehicles', id] as const,
};

/**
 * Hook to fetch all vehicles.
 */
export const useVehicles = (
  options?: Omit<
    UseQueryOptions<Vehicle[], ApiError>,
    'queryFn' | 'queryKey'
  > & { enabled?: boolean }
) => {
  return useCrudQuery<Vehicle[], ApiError>(
    [...vehicleQueryKeys.all], // Spread to create a mutable array
    async () => {
      const response = await vehicleApiService.getAll();
      return response.data;
    },
    'vehicle', // entityType for WebSocket events
    {
      staleTime: 5 * 60 * 1000, // 5 minutes
      ...options,
    }
  );
};

/**
 * Hook to fetch a single vehicle by its ID.
 */
export const useVehicle = (
  id: null | number,
  options?: Omit<
    UseQueryOptions<Vehicle, ApiError>,
    'enabled' | 'queryFn' | 'queryKey'
  > & { enabled?: boolean }
) => {
  return useCrudQuery<Vehicle, ApiError>(
    [...vehicleQueryKeys.detail(id!)],
    () => vehicleApiService.getById(id!),
    'vehicle', // entityType for WebSocket events
    {
      enabled: !!id && (options?.enabled ?? true), // Only run query if id is not null AND options.enabled is true (or undefined)
      staleTime: 5 * 60 * 1000,
      ...options, // Spread additional options
    }
  );
};

/**
 * Hook to create a new vehicle.
 */
export const useCreateVehicle = () => {
  const queryClient = useQueryClient();
  const { showError, showSuccess } = useNotifications();

  return useMutation<Vehicle, ApiError, CreateVehicleData>({
    mutationFn: (newVehicleData: CreateVehicleData) => {
      const transformedData =
        VehicleTransformer.toCreateRequest(newVehicleData);
      return vehicleApiService.create(transformedData);
    },
    onError: error => {
      showError(
        `Failed to create vehicle: ${error.message || 'Unknown error occurred'}`
      );
    },
    onSuccess: data => {
      // Invalidate and refetch all vehicles query after a successful creation
      queryClient.invalidateQueries({ queryKey: vehicleQueryKeys.all });
      showSuccess(
        `Vehicle "${data.licensePlate}" has been created successfully!`
      );
    },
  });
};

/**
 * Hook to update an existing vehicle.
 */
export const useUpdateVehicle = () => {
  const queryClient = useQueryClient();
  const { showError, showSuccess } = useNotifications();

  return useMutation<
    Vehicle,
    ApiError,
    { data: Partial<CreateVehicleData>; id: number }
  >({
    mutationFn: ({ data, id }) => {
      const transformedData = VehicleTransformer.toUpdateRequest(data);
      return vehicleApiService.update(id, transformedData);
    },
    onError: error => {
      showError(
        `Failed to update vehicle: ${error.message || 'Unknown error occurred'}`
      );
    },
    onSuccess: updatedVehicle => {
      queryClient.invalidateQueries({ queryKey: vehicleQueryKeys.all });
      queryClient.invalidateQueries({
        queryKey: vehicleQueryKeys.detail(updatedVehicle.id),
      });
      showSuccess(
        `Vehicle "${updatedVehicle.licensePlate}" has been updated successfully!`
      );
    },
  });
};

/**
 * Hook to delete a vehicle.
 */
export const useDeleteVehicle = () => {
  const queryClient = useQueryClient();
  const { showError, showSuccess } = useNotifications();

  return useMutation<void, ApiError, number>({
    mutationFn: (id: number) => vehicleApiService.delete(id),
    onError: error => {
      showError(
        `Failed to delete vehicle: ${error.message || 'Unknown error occurred'}`
      );
    },
    onSuccess: (_data, id) => {
      queryClient.invalidateQueries({ queryKey: vehicleQueryKeys.all });
      queryClient.removeQueries({ queryKey: vehicleQueryKeys.detail(id) });
      showSuccess('Vehicle has been deleted successfully!');
    },
  });
};
