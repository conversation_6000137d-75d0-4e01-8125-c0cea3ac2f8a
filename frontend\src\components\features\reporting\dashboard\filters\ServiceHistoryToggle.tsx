// frontend/src/components/features/reporting/dashboard/filters/ServiceHistoryToggle.tsx

import React from 'react';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { History, Info } from 'lucide-react';
import {
  useReportingFilters,
  useReportingFiltersActions,
} from '../../data/stores/useReportingFiltersStore';

interface ServiceHistoryToggleProps {
  compact?: boolean;
  className?: string;
  showDescription?: boolean;
}

/**
 * @component ServiceHistoryToggle
 * @description Toggle component for enabling service history in reports
 * 
 * Responsibilities:
 * - Provides simple toggle for service history inclusion
 * - Integrates with existing reporting filters store
 * - Shows helpful description when enabled
 * - Follows existing component patterns
 * 
 * SOLID Principles Applied:
 * - SRP: Single responsibility of toggling service history
 * - OCP: Open for extension via props
 * - DIP: Depends on existing filter store abstractions
 */
export const ServiceHistoryToggle: React.FC<ServiceHistoryToggleProps> = ({
  compact = false,
  className = '',
  showDescription = true,
}) => {
  const filters = useReportingFilters();
  const { setIncludeServiceHistory } = useReportingFiltersActions();

  const handleToggle = (checked: boolean) => {
    setIncludeServiceHistory(checked);
  };

  if (compact) {
    return (
      <div className={cn('space-y-1', className)}>
        <Label className="text-xs font-medium">Service History</Label>
        <div className="flex items-center space-x-2">
          <Switch
            id="service-history-compact"
            checked={filters.includeServiceHistory || false}
            onCheckedChange={handleToggle}
          />
          <Label 
            htmlFor="service-history-compact" 
            className="text-sm cursor-pointer flex items-center gap-1"
          >
            <History className="h-3 w-3" />
            Include
          </Label>
        </div>
      </div>
    );
  }

  return (
    <div className={cn('space-y-2', className)}>
      <Label className="text-sm font-medium flex items-center gap-2">
        <History className="h-4 w-4" />
        Service History Integration
      </Label>
      
      <Card className={cn(
        'transition-colors',
        filters.includeServiceHistory 
          ? 'border-blue-200 bg-blue-50' 
          : 'border-gray-200'
      )}>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <div className="flex items-center space-x-2">
                <Switch
                  id="service-history"
                  checked={filters.includeServiceHistory || false}
                  onCheckedChange={handleToggle}
                />
                <Label 
                  htmlFor="service-history" 
                  className="text-sm font-medium cursor-pointer"
                >
                  Include Service History Data
                </Label>
              </div>
              
              {showDescription && (
                <p className="text-xs text-muted-foreground">
                  {filters.includeServiceHistory 
                    ? 'Service history data will be included in delegation reports'
                    : 'Enable to show vehicle service history alongside delegation data'
                  }
                </p>
              )}
            </div>
            
            {filters.includeServiceHistory && (
              <div className="flex items-center gap-1 text-blue-600">
                <Info className="h-4 w-4" />
                <span className="text-xs font-medium">Active</span>
              </div>
            )}
          </div>
          
          {filters.includeServiceHistory && showDescription && (
            <div className="mt-3 p-2 bg-blue-100 rounded text-xs text-blue-800">
              <strong>What's included:</strong>
              <ul className="mt-1 space-y-1 list-disc list-inside">
                <li>Vehicle maintenance records</li>
                <li>Service costs and trends</li>
                <li>Service-delegation correlations</li>
                <li>Vehicle utilization metrics</li>
              </ul>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default ServiceHistoryToggle;
