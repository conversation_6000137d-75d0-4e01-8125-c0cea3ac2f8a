/**
 * useDelegationFormData Hook - Data Management Layer
 *
 * Manages data fetching, filtering, and state for delegation forms.
 * Provides centralized data management following SOLID principles.
 *
 * @module useDelegationFormData
 */

import { useMemo } from 'react';
import type { Employee, Vehicle } from '@/lib/types/domain';
import { useEmployeesByRole } from '@/lib/stores/queries/useEmployees';
import { useVehicles } from '@/lib/stores/queries/useVehicles';

// ============================================================================
// INTERFACES
// ============================================================================

export interface UseDelegationFormDataReturn {
  // Raw data
  employees: Employee[];
  drivers: Employee[];
  escorts: Employee[];
  vehicles: Vehicle[];

  // Loading states
  isLoadingEmployees: boolean;
  isLoadingVehicles: boolean;
  isDataReady: boolean;

  // Error handling
  dataError: Error | null;
  refetchData: () => void;

  // Filtered data
  availableDrivers: Employee[];
  availableEscorts: Employee[];
  availableVehicles: Vehicle[];

  // Data utilities
  getEmployeeById: (id: number) => Employee | undefined;
  getVehicleById: (id: number) => Vehicle | undefined;
  filterEmployeesByRole: (role: string) => Employee[];
}

export interface DelegationFormDataOptions {
  // Filtering options
  includeInactiveEmployees?: boolean;
  includeUnavailableVehicles?: boolean;

  // Data refresh options
  refetchInterval?: number;
  staleTime?: number;
}

// ============================================================================
// HOOK IMPLEMENTATION
// ============================================================================

/**
 * Custom hook for managing delegation form data
 *
 * Provides centralized data fetching and management for delegation forms.
 * This is a placeholder implementation that will be fully developed in Phase 2.
 *
 * @param options - Configuration options for data fetching
 * @returns Data management interface
 */
export const useDelegationFormData = (
  options: DelegationFormDataOptions = {}
): UseDelegationFormDataReturn => {
  // ============================================================================
  // DATA FETCHING
  // ============================================================================

  // Fetch all active employees
  const {
    data: allActiveEmployees = [],
    isLoading: allEmployeesLoading,
    error: allEmployeesError,
    refetch: refetchAllEmployees,
  } = useEmployeesByRole();

  // Fetch driver employees specifically (lowercase as expected by backend)
  const {
    data: driverEmployees = [],
    isLoading: driversLoading,
    error: driversError,
    refetch: refetchDrivers,
  } = useEmployeesByRole('driver');

  // Fetch vehicles
  const {
    data: vehicles = [],
    isLoading: vehiclesLoading,
    error: vehiclesError,
    refetch: refetchVehicles,
  } = useVehicles();

  // ============================================================================
  // COMPUTED VALUES
  // ============================================================================

  // Filter non-driver employees for escorts
  const escortEmployees = useMemo(() => {
    return allActiveEmployees.filter(emp => emp.role !== 'driver');
  }, [allActiveEmployees]);

  // Combined loading state
  const isLoadingEmployees = allEmployeesLoading || driversLoading;
  const isLoadingVehicles = vehiclesLoading;
  const isDataReady = !isLoadingEmployees && !isLoadingVehicles;

  // Combined error state
  const dataError = allEmployeesError || driversError || vehiclesError || null;

  // Available data (filtered based on options)
  const availableDrivers = useMemo(() => {
    // TODO: Implement filtering logic based on options
    return driverEmployees;
  }, [driverEmployees, options]);

  const availableEscorts = useMemo(() => {
    // TODO: Implement filtering logic based on options
    return escortEmployees;
  }, [escortEmployees, options]);

  const availableVehicles = useMemo(() => {
    // TODO: Implement filtering logic based on options
    return vehicles;
  }, [vehicles, options]);

  // ============================================================================
  // UTILITY FUNCTIONS
  // ============================================================================

  const getEmployeeById = (id: number): Employee | undefined => {
    return allActiveEmployees.find(emp => emp.id === id);
  };

  const getVehicleById = (id: number): Vehicle | undefined => {
    return vehicles.find(vehicle => vehicle.id === id);
  };

  const filterEmployeesByRole = (role: string): Employee[] => {
    return allActiveEmployees.filter(emp => emp.role === role);
  };

  const refetchData = () => {
    refetchAllEmployees();
    refetchDrivers();
    refetchVehicles();
  };

  // ============================================================================
  // RETURN INTERFACE
  // ============================================================================

  return {
    // Raw data
    employees: allActiveEmployees,
    drivers: driverEmployees,
    escorts: escortEmployees,
    vehicles,

    // Loading states
    isLoadingEmployees,
    isLoadingVehicles,
    isDataReady,

    // Error handling
    dataError,
    refetchData,

    // Filtered data
    availableDrivers,
    availableEscorts,
    availableVehicles,

    // Data utilities
    getEmployeeById,
    getVehicleById,
    filterEmployeesByRole,
  };
};

// ============================================================================
// EXPORTS
// ============================================================================

export default useDelegationFormData;
