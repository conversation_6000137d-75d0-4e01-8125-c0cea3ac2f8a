/**
 * @file Generic dashboard framework types
 * @module components/dashboard/types
 */

import { ReactNode } from 'react';

/**
 * Available view modes for dashboard content
 */
export type ViewMode = 'cards' | 'table' | 'calendar' | 'list' | 'grid';

/**
 * Dashboard tab configuration
 */
export interface TabConfig {
  id: string;
  label: string;
  icon?: ReactNode;
  count?: number;
  disabled?: boolean;
}

/**
 * Filter configuration for dashboard
 */
export interface FilterConfig {
  id: string;
  label: string;
  type: 'select' | 'multiselect' | 'date' | 'daterange' | 'search' | 'toggle';
  options?: Array<{ label: string; value: string }>;
  defaultValue?: any;
  placeholder?: string;
}

/**
 * Sort configuration for dashboard
 */
export interface SortConfig {
  id: string;
  label: string;
  field: string;
  direction?: 'asc' | 'desc';
}

/**
 * Dashboard layout preferences
 */
export interface DashboardLayout {
  viewMode: ViewMode;
  gridColumns: number;
  compactMode: boolean;
  showFilters: boolean;
  showSettings: boolean;
}

/**
 * Dashboard monitoring preferences
 */
export interface DashboardMonitoring {
  enabled: boolean;
  refreshInterval: number;
  autoRefresh: boolean;
  pausedDataTypes: Set<string>;
}

/**
 * Generic dashboard configuration
 */
export interface DashboardConfig<T = any> {
  entityType: string;
  title: string;
  description?: string;
  viewModes: ViewMode[];
  defaultViewMode: ViewMode;
  tabs?: TabConfig[];
  filters?: FilterConfig[];
  sortOptions?: SortConfig[];
  enableBulkActions?: boolean;
  enableExport?: boolean;
  refreshInterval?: number;
}

/**
 * Dashboard state interface
 */
export interface DashboardState {
  activeTab: string;
  layout: DashboardLayout;
  monitoring: DashboardMonitoring;
  filters: Record<string, any>;
  sortBy: string;
  sortDirection: 'asc' | 'desc';
  selectedItems: Set<string>;
  searchTerm: string;
}

/**
 * Dashboard actions interface
 */
export interface DashboardActions {
  setActiveTab: (tab: string) => void;
  setViewMode: (mode: ViewMode) => void;
  setGridColumns: (columns: number) => void;
  toggleCompactMode: () => void;
  toggleFilters: () => void;
  toggleSettings: () => void;
  updateFilter: (filterId: string, value: any) => void;
  clearFilters: () => void;
  setSorting: (field: string, direction: 'asc' | 'desc') => void;
  setSearchTerm: (term: string) => void;
  toggleItemSelection: (id: string) => void;
  clearSelection: () => void;
  selectAll: (ids: string[]) => void;
  // Monitoring actions
  setMonitoringEnabled: (enabled: boolean) => void;
  setRefreshInterval: (interval: number) => void;
  toggleAutoRefresh: () => void;
  pauseDataType: (dataType: string) => void;
  resumeDataType: (dataType: string) => void;
  resetSettings: () => void;
}

/**
 * Combined dashboard store interface
 */
export interface DashboardStore extends DashboardState, DashboardActions {
  // Computed selectors
  getFilteredData: <T extends { id: string }>(
    data: T[],
    config: DashboardConfig<T>
  ) => T[];
  getSelectedCount: () => number;
  hasActiveFilters: () => boolean;
}

/**
 * Dashboard component props
 */
export interface DashboardProps<T = any> {
  config: DashboardConfig<T>;
  data?: T[];
  isLoading?: boolean;
  error?: string | null;
  onRefresh?: () => void;
  className?: string;
  children?: ReactNode;
}

/**
 * View renderer function type
 */
export type ViewRenderer<T = any> = (props: {
  data: T[];
  viewMode: ViewMode;
  layout: DashboardLayout;
  selectedItems: Set<string>;
  onItemSelect: (id: string) => void;
}) => ReactNode;
