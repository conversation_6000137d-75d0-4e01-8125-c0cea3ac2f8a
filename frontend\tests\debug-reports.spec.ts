import { test, expect } from '@playwright/test';

const BASE_URL = 'http://localhost:9002';
const REPORTS_URL = `${BASE_URL}/reports`;

test.describe('Debug Reports Page', () => {
  test('debug - what is actually on the page', async ({ page }) => {
    // Navigate to reports page
    await page.goto(REPORTS_URL);
    
    // Wait for page to load
    await page.waitForLoadState('networkidle');
    
    // Take a screenshot to see what's there
    await page.screenshot({ 
      path: 'debug-reports-page.png', 
      fullPage: true 
    });
    
    // Get page title
    const title = await page.title();
    console.log('Page title:', title);
    
    // Get page URL
    const url = page.url();
    console.log('Page URL:', url);
    
    // Get all text content
    const textContent = await page.textContent('body');
    console.log('Page text content (first 500 chars):', textContent?.substring(0, 500));
    
    // Get all headings
    const headings = await page.locator('h1, h2, h3, h4, h5, h6').allTextContents();
    console.log('All headings:', headings);
    
    // Get all links
    const links = await page.locator('a').allTextContents();
    console.log('All links:', links.slice(0, 10)); // First 10 links
    
    // Get all buttons
    const buttons = await page.locator('button').allTextContents();
    console.log('All buttons:', buttons);
    
    // Check if there are any error messages
    const errors = await page.locator('[role="alert"], .error, .alert-error').allTextContents();
    console.log('Error messages:', errors);
    
    // Check console logs
    const logs: string[] = [];
    page.on('console', msg => {
      logs.push(`${msg.type()}: ${msg.text()}`);
    });
    
    // Wait a bit more for any async content
    await page.waitForTimeout(3000);
    
    console.log('Console logs:', logs);
    
    // Get HTML structure
    const htmlStructure = await page.evaluate(() => {
      const getStructure = (element: Element, depth = 0): any => {
        if (depth > 3) return '...'; // Limit depth
        
        const result: any = {
          tag: element.tagName.toLowerCase(),
          classes: element.className || '',
          id: element.id || '',
          children: []
        };
        
        for (let i = 0; i < Math.min(element.children.length, 5); i++) {
          result.children.push(getStructure(element.children[i], depth + 1));
        }
        
        return result;
      };
      
      return getStructure(document.body);
    });
    
    console.log('HTML structure:', JSON.stringify(htmlStructure, null, 2));
    
    // This test always passes - it's just for debugging
    expect(true).toBe(true);
  });

  test('debug - check for authentication or loading issues', async ({ page }) => {
    // Navigate to reports page
    await page.goto(REPORTS_URL);
    
    // Check if we're redirected to login or another page
    await page.waitForLoadState('networkidle');
    
    const finalUrl = page.url();
    console.log('Final URL after navigation:', finalUrl);
    
    // Check if there's a login form
    const loginForm = await page.locator('form[action*="login"], form[action*="auth"], input[type="password"]').count();
    console.log('Login form elements found:', loginForm);
    
    // Check if there's a loading spinner
    const loadingElements = await page.locator('.loading, .spinner, [aria-label*="loading"]').count();
    console.log('Loading elements found:', loadingElements);
    
    // Check if there's an error page
    const errorElements = await page.locator('.error-page, .not-found, .404').count();
    console.log('Error page elements found:', errorElements);
    
    // Check network requests
    const responses: string[] = [];
    page.on('response', response => {
      responses.push(`${response.status()} ${response.url()}`);
    });
    
    // Reload to catch network requests
    await page.reload();
    await page.waitForLoadState('networkidle');
    
    console.log('Network responses:', responses.slice(0, 10)); // First 10 responses
    
    expect(true).toBe(true);
  });

  test('debug - check specific selectors', async ({ page }) => {
    await page.goto(REPORTS_URL);
    await page.waitForLoadState('networkidle');
    
    // Check for various possible selectors
    const selectors = [
      'h1',
      'h2', 
      '[data-testid]',
      '.card',
      '.dashboard',
      '.reports',
      '.navigation',
      'nav',
      'main',
      '[role="main"]',
      '.container',
      '.layout'
    ];
    
    for (const selector of selectors) {
      const count = await page.locator(selector).count();
      const text = count > 0 ? await page.locator(selector).first().textContent() : '';
      console.log(`Selector "${selector}": ${count} elements found, first text: "${text?.substring(0, 100)}"`);
    }
    
    expect(true).toBe(true);
  });
});
