/**
 * @file StatusBadge component for delegation status display
 * @module components/ui/status-badge
 */

import React from 'react';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { getStatusColor } from '@/lib/utils/delegationUtils';
import { formatDelegationStatusForDisplay } from '@/lib/utils/formattingUtils';
import type { Delegation } from '@/lib/types/domain';

interface StatusBadgeProps {
  /** Delegation status */
  status: Delegation['status'];
  /** Additional CSS classes */
  className?: string;
  /** Size variant */
  size?: 'sm' | 'md' | 'lg';
  /** Whether to show as floating badge */
  floating?: boolean;
}

/**
 * Reusable component for displaying delegation status
 * Follows SRP by handling only status display logic
 */
export const StatusBadge: React.FC<StatusBadgeProps> = ({
  status,
  className,
  size = 'md',
  floating = false,
}) => {
  const sizeClasses = {
    sm: 'text-xs py-1 px-2',
    md: 'text-xs py-1.5 px-3',
    lg: 'text-sm py-2 px-4',
  };

  const baseClasses = cn(
    'font-medium border shadow-sm transition-all duration-200',
    sizeClasses[size],
    floating && 'absolute top-4 right-4',
    getStatusColor(status)
  );

  return (
    <Badge className={cn(baseClasses, className)}>
      {formatDelegationStatusForDisplay(status)}
    </Badge>
  );
};

export default StatusBadge;
