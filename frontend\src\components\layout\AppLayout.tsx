/**
 * @file Modern app layout component with sidebar navigation
 * @module components/layout/AppLayout
 */

'use client';

import { Menu, X } from 'lucide-react';
import React, { useState } from 'react';

import { AppSidebar } from './AppSidebar';
import { Button } from '@/components/ui/button';
import { UserProfile } from '@/components/user/UserProfile';
import { ThemeToggle } from '@/components/theme-toggle';
import { useUiPreferences } from '@/hooks/ui/useUiPreferences';
import { cn } from '@/lib/utils';

interface AppLayoutProps {
  children: React.ReactNode;
  className?: string;
}

export const AppLayout: React.FC<AppLayoutProps> = ({
  children,
  className,
}) => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const { fontSize } = useUiPreferences();

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  return (
    <div
      className={cn(
        'flex h-screen bg-background',
        fontSize === 'small' && 'text-sm',
        fontSize === 'large' && 'text-lg',
        className
      )}
    >
      {/* Mobile Sidebar Overlay */}
      {mobileMenuOpen && (
        <div
          className="fixed inset-0 z-50 bg-black/50 lg:hidden"
          onClick={() => setMobileMenuOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div
        className={cn(
          'fixed inset-y-0 left-0 z-50 lg:static lg:z-auto',
          mobileMenuOpen
            ? 'translate-x-0'
            : '-translate-x-full lg:translate-x-0',
          'transition-transform duration-200 ease-in-out'
        )}
      >
        <AppSidebar collapsed={sidebarCollapsed} className="h-full" />
      </div>

      {/* Main Content Area */}
      <div className="flex flex-1 flex-col overflow-hidden">
        {/* Top Header */}
        <header className="flex h-16 items-center justify-between border-b bg-background px-4 lg:px-6">
          <div className="flex items-center gap-3">
            {/* Mobile Menu Button */}
            <Button
              variant="ghost"
              size="icon"
              className="lg:hidden"
              onClick={toggleMobileMenu}
            >
              <Menu className="size-5" />
              <span className="sr-only">Open navigation menu</span>
            </Button>

            {/* Desktop Sidebar Toggle */}
            <Button
              variant="ghost"
              size="icon"
              className="hidden lg:flex"
              onClick={toggleSidebar}
            >
              {sidebarCollapsed ? (
                <Menu className="size-5" />
              ) : (
                <X className="size-5" />
              )}
              <span className="sr-only">Toggle sidebar</span>
            </Button>
          </div>

          {/* Right Side Controls */}
          <div className="flex items-center gap-3">
            <ThemeToggle />
            <UserProfile variant="dropdown" />
          </div>
        </header>

        {/* Main Content with consistent padding */}
        <main className="flex-1 overflow-auto">
          <div className="h-full p-6">{children}</div>
        </main>
      </div>
    </div>
  );
};
