/**
 * DelegationAssignmentSection Component - SOLID Principles Implementation
 *
 * Single Responsibility: Handles all assignment-related selections
 * - Combines drivers, escorts, and vehicles in a logical grouping
 * - Enforces business rules (vehicles require drivers)
 * - Follows SRP by focusing only on assignment management
 *
 * @module DelegationAssignmentSection
 */

import React from 'react';
import { Users } from 'lucide-react';

import type { DelegationFormData } from '@/lib/schemas/delegationSchemas';

import DelegationDriversSection from './DelegationDriversSection';
import DelegationEscortsSection from './DelegationEscortsSection';
import DelegationVehiclesSection from './DelegationVehiclesSection';

// ============================================================================
// INTERFACES
// ============================================================================

export interface DelegationAssignmentSectionProps {
  /** Whether the form is currently submitting */
  isSubmitting?: boolean;
  /** Additional CSS classes */
  className?: string;
  /** Available employees for assignment */
  employees?: Array<{ id: number; name: string; role: string }>;
  /** Available vehicles for assignment */
  vehicles?: Array<{ id: number; name: string; type: string }>;
  /** User's role for permission-based UI */
  userRole?: string;
}

// ============================================================================
// COMPONENT IMPLEMENTATION
// ============================================================================

/**
 * Assignment Section for Delegation Form
 *
 * Orchestrates driver, escort, and vehicle selection with proper dependencies.
 * This component follows SRP by focusing solely on assignment coordination.
 */
export const DelegationAssignmentSection: React.FC<
  DelegationAssignmentSectionProps
> = ({
  isSubmitting = false,
  className = '',
  employees = [],
  vehicles = [],
  userRole = 'user',
}) => {
  return (
    <section className={`space-y-6 ${className}`}>
      <div className="flex items-center space-x-2">
        <Users className="size-6 text-accent" />
        <h2 className="text-xl font-semibold text-foreground">
          Assignment Details
        </h2>
      </div>

      <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
        {/* Drivers Section */}
        <DelegationDriversSection isSubmitting={isSubmitting} />

        {/* Escorts Section */}
        <DelegationEscortsSection isSubmitting={isSubmitting} />

        {/* Vehicles Section - Depends on drivers */}
        <DelegationVehiclesSection isSubmitting={isSubmitting} />
      </div>

      <div className="rounded-lg bg-muted/50 p-4">
        <h4 className="text-sm font-medium text-foreground mb-2">
          Assignment Guidelines
        </h4>
        <ul className="text-sm text-muted-foreground space-y-1">
          <li>• At least one escort is typically required for security</li>
          <li>• Drivers are required before vehicles can be assigned</li>
          <li>• Vehicle assignments should match the number of drivers</li>
          <li>• Consider delegation size when selecting personnel</li>
        </ul>
      </div>
    </section>
  );
};

export default DelegationAssignmentSection;
