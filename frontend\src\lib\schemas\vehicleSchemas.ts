import * as z from 'zod';

// Vehicle status enum schema
export const VehicleStatusSchema = z.enum([
  'Active',
  'Inactive',
  'Maintenance',
  'Out_of_Service',
]);

// Vehicle fuel type enum schema
export const VehicleFuelTypeSchema = z.enum([
  'Gasoline',
  'Diesel',
  'Electric',
  'Hybrid',
  'CNG',
  'LPG',
]);

export const VehicleFormSchema = z.object({
  // Basic Information
  make: z.string().min(1, 'Make is required'),
  model: z.string().min(1, 'Model is required'),
  year: z.coerce
    .number()
    .min(1900, 'Year must be 1900 or later')
    .max(
      new Date().getFullYear() + 1,
      `Year cannot be more than ${new Date().getFullYear() + 1}`
    ),
  color: z.string().optional(),

  // Identification
  licensePlate: z.string().min(1, 'License plate is required'),
  vin: z
    .string()
    .optional()
    .refine(
      val => !val || /^[A-HJ-NPR-Z0-9]{17}$/.test(val),
      'VIN must be a valid 17-character format (only capital letters A-H, J-N, P-R, Z and numbers 0-9)'
    ),

  // Additional Information
  mileage: z.coerce.number().min(0, 'Mileage cannot be negative').optional(),
  status: z.enum(['active', 'maintenance', 'inactive']).default('active'),
  notes: z.string().optional(),

  // Legacy fields for backward compatibility
  ownerContact: z.string().optional(),
  ownerName: z.string().optional(),
  imageUrl: z.string().url('Invalid image URL').optional().or(z.literal('')),
  initialOdometer: z.coerce
    .number()
    .min(0, 'Odometer reading cannot be negative')
    .optional(),

  // serviceHistory is not part of the form for new/edit, handled separately
  // id, createdAt, updatedAt are not part of the form
});

// Export types
export type VehicleFormData = z.infer<typeof VehicleFormSchema>;
export type VehicleStatus = z.infer<typeof VehicleStatusSchema>;
export type VehicleFuelType = z.infer<typeof VehicleFuelTypeSchema>;
