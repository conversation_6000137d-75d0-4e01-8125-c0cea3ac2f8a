/**
 * @file Query optimization hook for enhanced performance
 * @module hooks/useQueryOptimization
 */

import type {
  UseMutationOptions,
  UseQueryOptions} from '@tanstack/react-query';

import {
  useQueryClient
} from '@tanstack/react-query';
import { useCallback } from 'react';

/**
 * Configuration for query optimization
 */
interface QueryOptimizationConfig {
  /**
   * Custom cache time override
   */
  customCacheTime?: number;

  /**
   * Custom stale time override
   */
  customStaleTime?: number;

  /**
   * Data type for optimized cache settings
   */
  dataType?: 'critical' | 'dynamic' | 'realtime' | 'static';

  /**
   * Enable background refetching
   */
  enableBackgroundRefetch?: boolean;

  /**
   * Enable optimistic updates for mutations
   */
  enableOptimisticUpdates?: boolean;
}

/**
 * Optimized query options based on data type
 */
const OPTIMIZATION_PRESETS = {
  critical: {
    gcTime: 2 * 60 * 1000, // 2 minutes
    refetchInterval: false,
    refetchOnReconnect: true,
    refetchOnWindowFocus: true,
    staleTime: 0, // Always fresh
  },
  dynamic: {
    gcTime: 15 * 60 * 1000, // 15 minutes
    refetchInterval: false,
    refetchOnReconnect: true,
    refetchOnWindowFocus: true,
    staleTime: 5 * 60 * 1000, // 5 minutes
  },
  realtime: {
    gcTime: 5 * 60 * 1000, // 5 minutes
    refetchInterval: false,
    refetchOnReconnect: true,
    refetchOnWindowFocus: true,
    staleTime: 30 * 1000, // 30 seconds
  },
  static: {
    gcTime: 60 * 60 * 1000, // 1 hour
    refetchInterval: false,
    refetchOnReconnect: false,
    refetchOnWindowFocus: false,
    staleTime: 30 * 60 * 1000, // 30 minutes
  },
} as const;

/**
 * Hook for query optimization with intelligent caching strategies
 */
export const useQueryOptimization = (config: QueryOptimizationConfig = {}) => {
  const queryClient = useQueryClient();

  const {
    customCacheTime,
    customStaleTime,
    dataType = 'dynamic',
    enableBackgroundRefetch = true,
    enableOptimisticUpdates = true,
  } = config;

  /**
   * Get optimized query options based on data type
   */
  const getOptimizedQueryOptions = useCallback(
    <T = unknown>(
      baseOptions: Partial<UseQueryOptions<T>> = {}
    ): Partial<UseQueryOptions<T>> => {
      const preset = OPTIMIZATION_PRESETS[dataType];

      return {
        ...baseOptions,
        gcTime: customCacheTime ?? preset.gcTime,
        refetchInterval: enableBackgroundRefetch
          ? preset.refetchInterval
          : false,
        refetchOnReconnect: enableBackgroundRefetch
          ? preset.refetchOnReconnect
          : false,
        refetchOnWindowFocus: enableBackgroundRefetch
          ? preset.refetchOnWindowFocus
          : false,
        // Enhanced error handling
        retry: (failureCount, error) => {
          // Don't retry on 4xx errors
          if (error && typeof error === 'object' && 'status' in error) {
            const status = (error as any).status;
            if (status >= 400 && status < 500) {
              return false;
            }
          }
          return failureCount < 3;
        },

        // Exponential backoff
        retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30_000),

        staleTime: customStaleTime ?? preset.staleTime,
      };
    },
    [dataType, enableBackgroundRefetch, customStaleTime, customCacheTime]
  );

  /**
   * Get optimized mutation options with optimistic updates
   */
  const getOptimizedMutationOptions = useCallback(
    <T = unknown, E = unknown, V = unknown>(
      baseMutationOptions: UseMutationOptions<T, E, V> = {},
      optimisticUpdateConfig?: {
        queryKey: unknown[];
        updateFn: (oldData: any, variables: V) => any;
      }
    ): UseMutationOptions<T, E, V> => {
      const options: UseMutationOptions<T, E, V> = {
        ...baseMutationOptions,

        // Enhanced retry logic for mutations
        retry: (failureCount, error) => {
          // Generally don't retry mutations to avoid side effects
          // Only retry on network errors
          if (error && typeof error === 'object' && 'message' in error) {
            const message = (error as any).message?.toLowerCase() || '';
            if (message.includes('network') || message.includes('timeout')) {
              return failureCount < 1; // Only retry once for network errors
            }
          }
          return false;
        },
      };

      // Add optimistic updates if enabled and config provided
      if (enableOptimisticUpdates && optimisticUpdateConfig) {
        const { queryKey, updateFn } = optimisticUpdateConfig;

        options.onMutate = async variables => {
          // Cancel any outgoing refetches
          await queryClient.cancelQueries({ queryKey });

          // Snapshot the previous value
          const previousData = queryClient.getQueryData(queryKey);

          // Optimistically update to the new value
          queryClient.setQueryData(queryKey, (old: any) =>
            updateFn(old, variables)
          );

          // Return a context object with the snapshotted value
          return { previousData };
        };

        options.onError = (err, variables, context) => {
          // If the mutation fails, use the context returned from onMutate to roll back
          if (
            context &&
            typeof context === 'object' &&
            'previousData' in context
          ) {
            queryClient.setQueryData(queryKey, (context as any).previousData);
          }

          // Call original onError if provided
          baseMutationOptions.onError?.(err, variables, context);
        };

        options.onSettled = (data, error, variables, context) => {
          // Always refetch after error or success to ensure we have the latest data
          queryClient.invalidateQueries({ queryKey });

          // Call original onSettled if provided
          baseMutationOptions.onSettled?.(data, error, variables, context);
        };
      }

      return options;
    },
    [queryClient, enableOptimisticUpdates]
  );

  /**
   * Prefetch related data based on current query
   */
  const prefetchRelatedData = useCallback(
    async (
      _currentQueryKey: unknown[],
      relatedQueries: {
        queryFn: () => Promise<any>;
        queryKey: unknown[];
        staleTime?: number;
      }[]
    ) => {
      const prefetchPromises = relatedQueries.map(
        ({ queryFn, queryKey, staleTime }) =>
          queryClient.prefetchQuery({
            queryFn,
            queryKey,
            staleTime: staleTime ?? OPTIMIZATION_PRESETS[dataType].staleTime,
          })
      );

      await Promise.allSettled(prefetchPromises);
    },
    [queryClient, dataType]
  );

  /**
   * Intelligent cache invalidation
   */
  const invalidateRelatedQueries = useCallback(
    (baseQueryKey: unknown[], relatedPatterns: string[] = []) => {
      // Invalidate the base query
      queryClient.invalidateQueries({ queryKey: baseQueryKey });

      // Invalidate related queries based on patterns
      for (const pattern of relatedPatterns) {
        queryClient.invalidateQueries({
          predicate: query => {
            const keyString = JSON.stringify(query.queryKey);
            return keyString.includes(pattern);
          },
        });
      }
    },
    [queryClient]
  );

  /**
   * Batch multiple cache updates
   */
  const batchCacheUpdates = useCallback(
    (
      updates: {
        queryKey: unknown[];
        updateFn: (oldData: any) => any;
      }[]
    ) => {
      // Batch updates manually since batch method may not be available
      for (const { queryKey, updateFn } of updates) {
        queryClient.setQueryData(queryKey, updateFn);
      }
    },
    [queryClient]
  );

  /**
   * Get cache statistics for monitoring
   */
  const getCacheStats = useCallback(() => {
    const cache = queryClient.getQueryCache();
    const queries = cache.getAll();

    const stats = {
      activeQueries: queries.filter(q => q.getObserversCount() > 0).length,
      errorQueries: queries.filter(q => q.state.status === 'error').length,
      loadingQueries: queries.filter(q => q.state.status === 'pending').length,
      staleQueries: queries.filter(q => q.isStale()).length,
      totalQueries: queries.length,
    };

    return stats;
  }, [queryClient]);

  return {
    batchCacheUpdates,
    getCacheStats,
    getOptimizedMutationOptions,
    getOptimizedQueryOptions,
    invalidateRelatedQueries,
    prefetchRelatedData,
  };
};

/**
 * Specialized hooks for different data types
 */

/**
 * Hook for static data (rarely changes)
 */
export const useStaticDataOptimization = () => {
  return useQueryOptimization({
    dataType: 'static',
    enableBackgroundRefetch: false,
  });
};

/**
 * Hook for dynamic data (changes moderately)
 */
export const useDynamicDataOptimization = () => {
  return useQueryOptimization({
    dataType: 'dynamic',
    enableBackgroundRefetch: true,
  });
};

/**
 * Hook for real-time data (changes frequently)
 */
export const useRealTimeDataOptimization = () => {
  return useQueryOptimization({
    dataType: 'realtime',
    enableBackgroundRefetch: true,
    enableOptimisticUpdates: true,
  });
};

/**
 * Hook for critical data (always fresh)
 */
export const useCriticalDataOptimization = () => {
  return useQueryOptimization({
    dataType: 'critical',
    enableBackgroundRefetch: true,
    enableOptimisticUpdates: true,
  });
};

/**
 * Performance monitoring hook for queries
 */
export const useQueryPerformanceMonitor = () => {
  const queryClient = useQueryClient();

  const getSlowQueries = useCallback(
    (thresholdMs = 2000) => {
      const cache = queryClient.getQueryCache();
      const queries = cache.getAll();

      return queries
        .filter(query => {
          // Use dataUpdatedAt as a proxy for query duration
          const now = Date.now();
          const lastUpdate = query.state.dataUpdatedAt || 0;
          const duration = now - lastUpdate;
          return duration > thresholdMs && query.state.status === 'success';
        })
        .map(query => ({
          duration: Date.now() - (query.state.dataUpdatedAt || 0),
          queryKey: query.queryKey,
          status: query.state.status,
        }));
    },
    [queryClient]
  );

  const getCacheEfficiency = useCallback(() => {
    const cache = queryClient.getQueryCache();
    const queries = cache.getAll();

    const totalQueries = queries.length;
    const cachedQueries = queries.filter(
      q => q.state.data !== undefined
    ).length;

    return totalQueries > 0 ? (cachedQueries / totalQueries) * 100 : 0;
  }, [queryClient]);

  return {
    getCacheEfficiency,
    getSlowQueries,
  };
};
