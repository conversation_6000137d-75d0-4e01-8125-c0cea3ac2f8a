/**
 * @file EmployeeWorkloadWidget.tsx
 * @description Employee workload distribution widget following existing patterns and SOLID principles
 */

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Progress } from '@/components/ui/progress';
import {
  Users,
  AlertTriangle,
  CheckCircle,
  Clock,
  TrendingUp,
  MoreHorizontal,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useEmployeeAnalytics } from '../../hooks/useEmployeeAnalytics';
import { DataLoader } from '@/components/ui/data-loader';
import { ErrorDisplay } from '@/components/ui/error-display';
import type { ReportingFilters } from '../../data/types/reporting';
import type { WorkloadDistributionData } from '../../data/types/reporting';

/**
 * Props interface for EmployeeWorkloadWidget
 */
interface EmployeeWorkloadWidgetProps {
  filters?: ReportingFilters;
  className?: string;
  compact?: boolean;
  maxItems?: number;
}

/**
 * Workload item component
 */
interface WorkloadItemProps {
  item: WorkloadDistributionData;
  compact?: boolean;
}

const WorkloadItem: React.FC<WorkloadItemProps> = ({
  item,
  compact = false,
}) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Underutilized':
        return 'text-blue-600 bg-blue-100';
      case 'Optimal':
        return 'text-green-600 bg-green-100';
      case 'Overloaded':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Underutilized':
        return <Clock className="h-4 w-4 text-blue-600" />;
      case 'Optimal':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'Overloaded':
        return <AlertTriangle className="h-4 w-4 text-red-600" />;
      default:
        return <Users className="h-4 w-4 text-gray-600" />;
    }
  };

  const progressColor =
    item.status === 'Overloaded'
      ? 'bg-red-500'
      : item.status === 'Optimal'
        ? 'bg-green-500'
        : 'bg-blue-500';

  return (
    <div
      className={cn(
        'flex items-center justify-between p-3 border rounded-lg',
        item.status === 'Overloaded' && 'border-red-200 bg-red-50',
        item.status === 'Optimal' && 'border-green-200 bg-green-50',
        item.status === 'Underutilized' && 'border-blue-200 bg-blue-50'
      )}
    >
      <div className="flex items-center space-x-3 flex-1">
        <Avatar className="h-8 w-8">
          <AvatarFallback className="text-xs">
            {item.employeeName
              .split(' ')
              .map(n => n[0])
              .join('')}
          </AvatarFallback>
        </Avatar>
        <div className="flex-1">
          <div className="flex items-center gap-2">
            <p className="font-medium text-sm">{item.employeeName}</p>
            {!compact && (
              <Badge className={cn('text-xs', getStatusColor(item.status))}>
                {item.status}
              </Badge>
            )}
          </div>
          <div className="mt-1">
            <div className="flex items-center gap-2 text-xs text-gray-500">
              <span>
                Workload: {item.currentWorkload}/{item.capacity}
              </span>
              <span>•</span>
              <span>{item.workloadPercentage}%</span>
            </div>
            <Progress
              value={item.workloadPercentage}
              className="h-2 mt-1"
              // Custom color based on status
              style={
                {
                  '--progress-background':
                    item.status === 'Overloaded'
                      ? '#ef4444'
                      : item.status === 'Optimal'
                        ? '#10b981'
                        : '#3b82f6',
                } as React.CSSProperties
              }
            />
          </div>
        </div>
      </div>

      <div className="flex items-center gap-2">
        {getStatusIcon(item.status)}
        {compact && (
          <Badge className={cn('text-xs', getStatusColor(item.status))}>
            {item.workloadPercentage}%
          </Badge>
        )}
      </div>
    </div>
  );
};

/**
 * EmployeeWorkloadWidget Component
 *
 * Displays employee workload distribution following existing widget patterns.
 *
 * Responsibilities:
 * - Display workload distribution in widget format
 * - Show overloaded and underutilized employees
 * - Follow established widget composition patterns
 * - Maintain consistent styling and behavior
 *
 * SOLID Principles Applied:
 * - SRP: Single responsibility of displaying workload data
 * - OCP: Open for extension via props configuration
 * - DIP: Depends on existing widget framework abstractions
 */
export const EmployeeWorkloadWidget: React.FC<EmployeeWorkloadWidgetProps> = ({
  filters,
  className = '',
  compact = false,
  maxItems = 8,
}) => {
  // Use existing hook patterns
  const {
    data: employeeAnalytics,
    isLoading,
    error,
  } = useEmployeeAnalytics(filters);
  const workloadData = employeeAnalytics?.workloadDistribution || [];

  // Handle loading state
  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Employee Workload
          </CardTitle>
        </CardHeader>
        <CardContent>
          <DataLoader isLoading={true} data={null} error={null}>
            {() => null}
          </DataLoader>
        </CardContent>
      </Card>
    );
  }

  // Handle error state
  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Employee Workload
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ErrorDisplay error={error} />
        </CardContent>
      </Card>
    );
  }

  // Process workload data
  const overloadedEmployees = workloadData.filter(
    emp => emp.status === 'Overloaded'
  );
  const underutilizedEmployees = workloadData.filter(
    emp => emp.status === 'Underutilized'
  );
  const optimalEmployees = workloadData.filter(emp => emp.status === 'Optimal');

  // Sort and limit items for display - prioritize overloaded employees
  const displayItems = [
    ...overloadedEmployees,
    ...optimalEmployees,
    ...underutilizedEmployees,
  ].slice(0, maxItems);

  const avgWorkloadPercentage =
    workloadData.length > 0
      ? Math.round(
          workloadData.reduce((sum, emp) => sum + emp.workloadPercentage, 0) /
            workloadData.length
        )
      : 0;

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Employee Workload
          </CardTitle>
          <div className="flex items-center gap-2">
            <Badge variant="secondary" className="text-xs">
              Avg: {avgWorkloadPercentage}%
            </Badge>
            {overloadedEmployees.length > 0 && (
              <Badge variant="destructive" className="text-xs">
                {overloadedEmployees.length} overloaded
              </Badge>
            )}
            <Button variant="ghost" size="sm">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Summary Stats */}
        <div className="grid grid-cols-3 gap-4">
          <div className="text-center p-3 bg-blue-50 rounded-lg">
            <div className="text-2xl font-bold text-blue-700">
              {underutilizedEmployees.length}
            </div>
            <div className="text-xs text-blue-600">Underutilized</div>
          </div>
          <div className="text-center p-3 bg-green-50 rounded-lg">
            <div className="text-2xl font-bold text-green-700">
              {optimalEmployees.length}
            </div>
            <div className="text-xs text-green-600">Optimal</div>
          </div>
          <div className="text-center p-3 bg-red-50 rounded-lg">
            <div className="text-2xl font-bold text-red-700">
              {overloadedEmployees.length}
            </div>
            <div className="text-xs text-red-600">Overloaded</div>
          </div>
        </div>

        {/* Workload Items List */}
        <div className="space-y-3">
          {displayItems.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Users className="h-12 w-12 mx-auto mb-2 opacity-50" />
              <p>No workload data available</p>
            </div>
          ) : (
            displayItems.map((item, index) => (
              <WorkloadItem
                key={`${item.employeeId}-${index}`}
                item={item}
                compact={compact}
              />
            ))
          )}
        </div>

        {/* Workload Alerts */}
        {overloadedEmployees.length > 0 && (
          <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-red-600" />
              <span className="font-medium text-red-800">Workload Alert</span>
            </div>
            <p className="text-sm text-red-700 mt-1">
              {overloadedEmployees.length} employee
              {overloadedEmployees.length > 1 ? 's are' : ' is'} overloaded.
              Consider redistributing tasks or adjusting schedules.
            </p>
          </div>
        )}

        {!compact && workloadData.length > maxItems && (
          <div className="pt-4 border-t">
            <Button variant="outline" size="sm" className="w-full">
              View All Employees ({workloadData.length} total)
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default EmployeeWorkloadWidget;
