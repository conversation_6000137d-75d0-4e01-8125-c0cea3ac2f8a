import { Request, Response, NextFunction } from 'express';
import { logGDPRAuditEvent, generateCorrelationId } from '../utils/auditLogger.js';
import { securityMonitoringService } from '../services/securityMonitoring.service.js';

/**
 * Enhanced audit middleware for comprehensive API request logging
 * Implements GDPR compliance and correlation tracking
 */
export const auditMiddleware = (req: Request, res: Response, next: NextFunction): void => {
  // Generate correlation ID if not present
  if (!req.headers['x-correlation-id']) {
    req.headers['x-correlation-id'] = generateCorrelationId();
  }

  // Generate request ID if not present
  if (!req.headers['x-request-id']) {
    req.headers['x-request-id'] = generateCorrelationId();
  }

  // Capture request start time
  const startTime = Date.now();

  // Override res.send to capture response details
  const originalSend = res.send;
  res.send = function (data: any) {
    const endTime = Date.now();
    const duration = endTime - startTime;

    // Determine if personal data is involved based on endpoint
    const personalDataEndpoints = [
      '/api/employees',
      '/api/user-profiles',
      '/api/session',
      '/api/auth',
    ];
    const personalDataInvolved = personalDataEndpoints.some(endpoint =>
      req.path.startsWith(endpoint),
    );

    // Determine data classification based on endpoint
    let dataClassification: 'PUBLIC' | 'INTERNAL' | 'CONFIDENTIAL' | 'RESTRICTED' = 'INTERNAL';
    if (req.path.startsWith('/api/admin')) {
      dataClassification = 'RESTRICTED';
    } else if (personalDataInvolved) {
      dataClassification = 'CONFIDENTIAL';
    } else if (req.path.startsWith('/api/public')) {
      dataClassification = 'PUBLIC';
    }

    // Determine risk level based on operation and status
    let riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' = 'LOW';
    if (res.statusCode >= 500) {
      riskLevel = 'HIGH';
    } else if (res.statusCode >= 400) {
      riskLevel = 'MEDIUM';
    } else if (req.method !== 'GET' && personalDataInvolved) {
      riskLevel = 'MEDIUM';
    }

    // Log the API request with enhanced audit information
    logGDPRAuditEvent(
      {
        eventType: 'DATA_ACCESS',
        action: `API_${req.method}_REQUEST`,
        userId: (req as any).userId,
        userRole: (req as any).userRole,
        employeeId: (req as any).employeeId,
        targetResource: `${req.method} ${req.path}`,
        outcome: res.statusCode < 400 ? 'SUCCESS' : 'FAILURE',
        statusCode: res.statusCode,
        message: `API ${req.method} request to ${req.path} completed with status ${res.statusCode}`,
        details: {
          method: req.method,
          path: req.path,
          query: req.query,
          body: req.method !== 'GET' ? sanitizeRequestBody(req.body) : undefined,
          duration,
          responseSize: data ? JSON.stringify(data).length : 0,
          userAgent: req.get('User-Agent'),
        },
        dataClassification,
        personalDataInvolved,
        gdprLawfulBasis: personalDataInvolved ? 'legitimate_interests' : undefined,
        riskLevel,
        alertTriggered: riskLevel === 'HIGH',
      },
      req,
    );

    return originalSend.call(this, data);
  };

  next();
};

/**
 * PHASE 2 SECURITY HARDENING: Data Protection Audit Middleware
 * Monitors sensitive data access with enhanced GDPR compliance
 */
export const dataProtectionAuditMiddleware = (dataType: string) => {
  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const startTime = Date.now();
    const userId = (req as any).userId || 'anonymous';
    const operation = req.method === 'GET' ? 'READ' : 'WRITE';

    // Monitor data access before processing
    await securityMonitoringService.monitorDataAccess(userId, dataType, operation, {
      endpoint: req.path,
      method: req.method,
      userAgent: req.get('User-Agent'),
      requestId: req.headers['x-request-id'],
    });

    // Override res.send to capture response details
    const originalSend = res.send;
    res.send = function (data: any) {
      const responseTime = Date.now() - startTime;
      const statusCode = res.statusCode;

      // Log data access event with GDPR compliance
      logGDPRAuditEvent(
        {
          eventType: 'DATA_PROTECTION',
          action: `DATA_ACCESS_${dataType.toUpperCase()}`,
          userId,
          outcome: statusCode < 400 ? 'SUCCESS' : 'FAILURE',
          statusCode,
          message: `Data access to ${dataType}: ${req.method} ${req.path}`,
          details: {
            dataType,
            operation,
            method: req.method,
            path: req.path,
            responseTime,
            statusCode,
            dataSize: typeof data === 'string' ? data.length : JSON.stringify(data || {}).length,
          },
          dataClassification: classifyDataType(dataType),
          personalDataInvolved: isPersonalData(dataType),
          gdprLawfulBasis: 'legitimate_interests',
          riskLevel: assessDataAccessRisk(dataType, operation, statusCode),
          retentionPeriod: isPersonalData(dataType) ? 2555 : 365, // 7 years for personal data, 1 year for others
        },
        req,
      );

      return originalSend.call(this, data);
    };

    next();
  };
};

/**
 * Sanitize request body to remove sensitive information from logs
 */
function sanitizeRequestBody(body: any): any {
  if (!body || typeof body !== 'object') {
    return body;
  }

  const sensitiveFields = [
    'password',
    'token',
    'secret',
    'key',
    'authorization',
    'ssn',
    'creditCard',
  ];
  const sanitized = { ...body };

  for (const field of sensitiveFields) {
    if (sanitized[field]) {
      sanitized[field] = '[REDACTED]';
    }
  }

  return sanitized;
}

/**
 * Helper functions for data classification and risk assessment
 */
function classifyDataType(dataType: string): 'PUBLIC' | 'INTERNAL' | 'CONFIDENTIAL' | 'RESTRICTED' {
  const restrictedTypes = ['employee', 'financial', 'personal', 'medical'];
  const confidentialTypes = ['vehicle', 'service', 'task', 'customer'];

  if (restrictedTypes.includes(dataType.toLowerCase())) {
    return 'RESTRICTED';
  }
  if (confidentialTypes.includes(dataType.toLowerCase())) {
    return 'CONFIDENTIAL';
  }
  return 'INTERNAL';
}

function isPersonalData(dataType: string): boolean {
  const personalDataTypes = ['employee', 'personal', 'contact', 'customer', 'medical'];
  return personalDataTypes.includes(dataType.toLowerCase());
}

function assessDataAccessRisk(
  dataType: string,
  operation: string,
  statusCode: number,
): 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' {
  const sensitiveDataTypes = ['employee', 'financial', 'personal', 'medical'];
  const highRiskOperations = ['WRITE', 'DELETE'];

  if (statusCode >= 400) {
    return 'HIGH'; // Failed access attempts are high risk
  }

  if (
    sensitiveDataTypes.includes(dataType.toLowerCase()) &&
    highRiskOperations.includes(operation)
  ) {
    return 'HIGH';
  }
  if (
    sensitiveDataTypes.includes(dataType.toLowerCase()) ||
    highRiskOperations.includes(operation)
  ) {
    return 'MEDIUM';
  }
  return 'LOW';
}

/**
 * Audit middleware specifically for admin operations
 */
export const adminAuditMiddleware = (req: Request, res: Response, next: NextFunction): void => {
  const originalSend = res.send;
  res.send = function (data: any) {
    logGDPRAuditEvent(
      {
        eventType: 'SECURITY',
        action: `ADMIN_${req.method}_REQUEST`,
        userId: (req as any).userId,
        userRole: (req as any).userRole,
        employeeId: (req as any).employeeId,
        targetResource: `ADMIN ${req.method} ${req.path}`,
        targetUserId: req.params.userId || req.body?.userId,
        outcome: res.statusCode < 400 ? 'SUCCESS' : 'FAILURE',
        statusCode: res.statusCode,
        message: `Admin ${req.method} request to ${req.path} completed with status ${res.statusCode}`,
        details: {
          method: req.method,
          path: req.path,
          query: req.query,
          body: sanitizeRequestBody(req.body),
          adminAction: true,
        },
        dataClassification: 'RESTRICTED',
        personalDataInvolved: true,
        gdprLawfulBasis: 'legitimate_interests',
        riskLevel: 'HIGH',
        alertTriggered: true,
      },
      req,
    );

    return originalSend.call(this, data);
  };

  next();
};

/**
 * Audit middleware for security-sensitive operations
 */
export const securityAuditMiddleware = (action: string) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const originalSend = res.send;
    res.send = function (data: any) {
      logGDPRAuditEvent(
        {
          eventType: 'SECURITY',
          action: action,
          userId: (req as any).userId,
          userRole: (req as any).userRole,
          employeeId: (req as any).employeeId,
          targetResource: req.path,
          targetUserId: req.params.userId || req.body?.userId,
          outcome: res.statusCode < 400 ? 'SUCCESS' : 'FAILURE',
          statusCode: res.statusCode,
          message: `Security operation ${action} completed with status ${res.statusCode}`,
          details: {
            method: req.method,
            path: req.path,
            securityAction: action,
          },
          dataClassification: 'RESTRICTED',
          personalDataInvolved: true,
          gdprLawfulBasis: 'legitimate_interests',
          riskLevel: 'CRITICAL',
          alertTriggered: true,
        },
        req,
      );

      return originalSend.call(this, data);
    };

    next();
  };
};
