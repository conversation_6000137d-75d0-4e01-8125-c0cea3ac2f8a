import { jest } from '@jest/globals';
import request from 'supertest';
import { initializeAndGetApp } from '../../app';
import { afterAll, beforeAll, beforeEach, describe, expect, it } from '@jest/globals';

let app;

const mockSupabase = {
  auth: {
    signInWithPassword: jest.fn<() => Promise<{ data: any; error: any }>>(),
    refreshSession: jest.fn<() => Promise<{ data: any; error: any }>>(),
  },
};

jest.mock('../../lib/supabase.js', () => ({
  supabaseAdmin: mockSupabase,
}));

beforeAll(async () => {
  app = await initializeAndGetApp();
});

afterAll(() => {
  jest.restoreAllMocks();
});

describe('Auth API Routes', () => {
  beforeEach(() => {
    mockSupabase.auth.signInWithPassword.mockClear();
    mockSupabase.auth.refreshSession.mockClear();
  });

  describe('POST /api/auth/login', () => {
    it('should login a user and return a wrapped success response', async () => {
      const mockSession = {
        access_token: 'mock-access-token',
        refresh_token: 'mock-refresh-token',
        expires_in: 3600,
      };
      const mockUser = { id: 'user-123', email: '<EMAIL>' };
      mockSupabase.auth.signInWithPassword.mockResolvedValue({
        data: { session: mockSession, user: mockUser },
        error: null,
      });

      const response = await request(app)
        .post('/api/auth/login')
        .send({ email: '<EMAIL>', password: 'password123' });

      expect(response.statusCode).toBe(200);
      expect(response.body.status).toBe('success');
      expect(response.body.data.message).toBe('Login successful');
      expect(response.body.data.user).toEqual({ id: 'user-123', email: '<EMAIL>' });
      expect(response.headers['set-cookie']).toEqual(
        expect.arrayContaining([
          expect.stringContaining('sb-access-token=mock-access-token'),
          expect.stringContaining('sb-refresh-token=mock-refresh-token'),
        ]),
      );
    });

    it('should return 401 for failed login', async () => {
      mockSupabase.auth.signInWithPassword.mockResolvedValue({
        data: { session: null, user: null },
        error: { message: 'Invalid credentials', name: 'AuthApiError', status: 401 },
      });

      const response = await request(app)
        .post('/api/auth/login')
        .send({ email: '<EMAIL>', password: 'wrongpassword' });

      expect(response.statusCode).toBe(401);
      expect(response.body.code).toBe('INVALID_CREDENTIALS');
    });
  });

  describe('POST /api/auth/logout', () => {
    it('should clear cookies and return a wrapped success response', async () => {
      const response = await request(app).post('/api/auth/logout');

      expect(response.statusCode).toBe(200);
      expect(response.body.status).toBe('success');
      expect(response.body.data.message).toBe('Logout successful');
      expect(response.headers['set-cookie']).toEqual(
        expect.arrayContaining([
          expect.stringContaining('sb-access-token=;'),
          expect.stringContaining('sb-refresh-token=;'),
        ]),
      );
    });
  });

  describe('POST /api/auth/refresh', () => {
    it('should refresh the token and return a wrapped success response', async () => {
      const mockSession = {
        access_token: 'new-mock-access-token',
        refresh_token: 'new-mock-refresh-token',
        expires_in: 3600,
      };
      const mockUser = { id: 'user-123', email: '<EMAIL>' };
      mockSupabase.auth.refreshSession.mockResolvedValue({
        data: { session: mockSession, user: mockUser },
        error: null,
      });

      const response = await request(app)
        .post('/api/auth/refresh')
        .set('Cookie', 'sb-refresh-token=mock-refresh-token');

      expect(response.statusCode).toBe(200);
      expect(response.body.status).toBe('success');
      expect(response.body.data.message).toBe('Token refreshed successfully');
      expect(response.headers['set-cookie']).toEqual(
        expect.arrayContaining([
          expect.stringContaining('sb-access-token=new-mock-access-token'),
          expect.stringContaining('sb-refresh-token=new-mock-refresh-token'),
        ]),
      );
    });
  });
});
