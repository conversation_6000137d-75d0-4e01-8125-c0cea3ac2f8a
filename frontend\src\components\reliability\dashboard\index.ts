/**
 * @file Centralized exports for reliability dashboard components.
 * This module provides easy access to all dashboard-related components.
 * @module components/reliability/dashboard
 */

// Main dashboard components
export { ReliabilityDashboard } from './ReliabilityDashboard';
export { DashboardHeader } from './DashboardHeader';
export { DashboardGrid } from './DashboardGrid';
export { WidgetContainer } from './WidgetContainer';
export { ConnectionStatusIndicator } from './ConnectionStatusIndicator';
export { DashboardSettings } from './DashboardSettings';

// Re-export types for convenience
export type { ReliabilityDashboardProps } from './ReliabilityDashboard';
export type { DashboardHeaderProps } from './DashboardHeader';
export type { DashboardGridProps } from './DashboardGrid';
export type { WidgetContainerProps } from './WidgetContainer';
export type { ConnectionStatusIndicatorProps } from './ConnectionStatusIndicator';
export type { DashboardSettingsProps } from './DashboardSettings';
