/**
 * Next.js Params Utilities
 *
 * Provides type-safe utilities for handling Next.js params and searchParams
 * that can be null in strict TypeScript mode.
 */

import { ReadonlyURLSearchParams } from 'next/navigation';

/**
 * Safely extracts a string parameter from Next.js params
 * @param params - The params object from useParams()
 * @param key - The parameter key to extract
 * @param fallback - Fallback value if param is missing or null
 * @returns The parameter value or fallback
 */
export function getParam(
  params: Record<string, string | string[]> | null,
  key: string,
  fallback: string = ''
): string {
  if (!params || !params[key]) {
    return fallback;
  }

  const value = params[key];
  return Array.isArray(value) ? value[0] || fallback : value;
}

/**
 * Safely extracts a required string parameter from Next.js params
 * @param params - The params object from useParams()
 * @param key - The parameter key to extract
 * @throws Error if the parameter is missing or null
 * @returns The parameter value
 */
export function getRequiredParam(
  params: Record<string, string | string[]> | null,
  key: string
): string {
  if (!params || !params[key]) {
    throw new Error(`Required parameter '${key}' is missing`);
  }

  const value = params[key];
  const result = Array.isArray(value) ? value[0] : value;

  if (!result) {
    throw new Error(`Required parameter '${key}' is empty`);
  }

  return result;
}

/**
 * Safely extracts a numeric parameter from Next.js params
 * @param params - The params object from useParams()
 * @param key - The parameter key to extract
 * @param fallback - Fallback value if param is missing, null, or invalid
 * @returns The parameter value as number or fallback
 */
export function getNumericParam(
  params: Record<string, string | string[]> | null,
  key: string,
  fallback: number = 0
): number {
  const stringValue = getParam(params, key);
  if (!stringValue) {
    return fallback;
  }

  const numericValue = Number(stringValue);
  return isNaN(numericValue) ? fallback : numericValue;
}

/**
 * Safely extracts a required numeric parameter from Next.js params
 * @param params - The params object from useParams()
 * @param key - The parameter key to extract
 * @throws Error if the parameter is missing, null, or not a valid number
 * @returns The parameter value as number
 */
export function getRequiredNumericParam(
  params: Record<string, string | string[]> | null,
  key: string
): number {
  const stringValue = getRequiredParam(params, key);
  const numericValue = Number(stringValue);

  if (isNaN(numericValue)) {
    throw new Error(
      `Parameter '${key}' must be a valid number, got: ${stringValue}`
    );
  }

  return numericValue;
}

/**
 * Safely extracts a search parameter from URLSearchParams
 * @param searchParams - The searchParams object from useSearchParams()
 * @param key - The parameter key to extract
 * @param fallback - Fallback value if param is missing or null
 * @returns The parameter value or fallback
 */
export function getSearchParam(
  searchParams: ReadonlyURLSearchParams | null,
  key: string,
  fallback: string = ''
): string {
  if (!searchParams) {
    return fallback;
  }

  return searchParams.get(key) || fallback;
}

/**
 * Safely extracts multiple search parameters from URLSearchParams
 * @param searchParams - The searchParams object from useSearchParams()
 * @param keys - Array of parameter keys to extract
 * @returns Object with parameter values (empty string for missing params)
 */
export function getSearchParams(
  searchParams: ReadonlyURLSearchParams | null,
  keys: string[]
): Record<string, string> {
  const result: Record<string, string> = {};

  for (const key of keys) {
    result[key] = getSearchParam(searchParams, key);
  }

  return result;
}

/**
 * Safely checks if a pathname starts with a given route
 * @param pathname - The pathname from usePathname()
 * @param route - The route to check against
 * @returns Boolean indicating if pathname starts with route
 */
export function pathnameStartsWith(
  pathname: string | null,
  route: string
): boolean {
  if (!pathname) {
    return false;
  }

  return pathname.startsWith(route);
}

/**
 * Safely checks if a pathname matches any of the given routes
 * @param pathname - The pathname from usePathname()
 * @param routes - Array of routes to check against
 * @returns Boolean indicating if pathname matches any route
 */
export function isPathnameInRoutes(
  pathname: string | null,
  routes: string[]
): boolean {
  if (!pathname) {
    return false;
  }

  return routes.some(route => pathname.startsWith(route));
}

/**
 * Type guard to check if params object is valid
 * @param params - The params object to check
 * @returns Boolean indicating if params is valid
 */
export function isValidParams(
  params: Record<string, string | string[]> | null
): params is Record<string, string | string[]> {
  return params !== null && typeof params === 'object';
}

/**
 * Type guard to check if searchParams object is valid
 * @param searchParams - The searchParams object to check
 * @returns Boolean indicating if searchParams is valid
 */
export function isValidSearchParams(
  searchParams: ReadonlyURLSearchParams | null
): searchParams is ReadonlyURLSearchParams {
  return searchParams !== null && typeof searchParams === 'object';
}
