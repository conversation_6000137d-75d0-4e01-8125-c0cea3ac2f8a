/**
 * @file Schema Index
 * @description Centralized exports for all validation schemas following DRY principles
 */

// Delegation schemas
export {
  DelegationStatusSchema,
  StatusHistoryEntrySchema,
  DelegateSchema,
  FlightDetailsSchema,
  DelegationFormSchema,
  type DelegationFormData,
  type DelegationStatus,
  type StatusHistoryEntry,
  type Delegate,
  type FlightDetails,
} from './delegationSchemas';

// Employee schemas
export {
  EmployeeStatusSchema,
  EmployeeRoleSchema,
  EmployeeFormSchema,
  type EmployeeFormData,
  type EmployeeStatus,
  type EmployeeRole,
} from './employeeSchemas';

// Driver schemas
export {
  DriverAvailabilitySchema,
  type DriverAvailability,
} from './driverSchemas';

// Task schemas
export {
  TaskStatusSchema,
  TaskPrioritySchema,
  SubTaskSchema,
  TaskFormSchema,
  type TaskFormData,
  type TaskStatus,
  type TaskPriority,
  type SubTask,
} from './taskSchemas';

// Vehicle schemas
export {
  VehicleStatusSchema,
  VehicleFuelTypeSchema,
  VehicleFormSchema,
  type VehicleFormData,
  type VehicleStatus,
  type VehicleFuelType,
} from './vehicleSchemas';

// Service Record schemas
export {
  ServiceRecordFormSchema,
  ServiceRecordCreateSchema,
  ServiceRecordUpdateSchema,
  ServiceRecordSchemaUtils,
  type ServiceRecordFormData,
  type ServiceRecordCreateData,
  type ServiceRecordUpdateData,
} from './serviceRecordSchemas';

// Schema utilities for consistent validation patterns
export const SchemaUtils = {
  // Common validation patterns
  isValidUUID: (value: string): boolean => {
    const uuidRegex =
      /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(value);
  },

  isValidEmail: (value: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(value);
  },

  isValidPhoneNumber: (value: string): boolean => {
    const phoneRegex = /^\+?[\d\s\-\(\)]{10,}$/;
    return phoneRegex.test(value);
  },

  // Date validation helpers
  isValidDateRange: (startDate: string, endDate: string): boolean => {
    return new Date(startDate) <= new Date(endDate);
  },

  isFutureDate: (date: string): boolean => {
    return new Date(date) > new Date();
  },

  isPastDate: (date: string): boolean => {
    return new Date(date) < new Date();
  },
} as const;

// Common schema patterns for reuse
export const CommonSchemas = {
  uuid: () => import('zod').then(z => z.string().uuid()),
  email: () => import('zod').then(z => z.string().email()),
  phone: () => import('zod').then(z => z.string().min(10).max(20)),
  url: () => import('zod').then(z => z.string().url()),
  positiveNumber: () => import('zod').then(z => z.number().positive()),
  nonEmptyString: (minLength = 1) =>
    import('zod').then(z => z.string().min(minLength)),
} as const;
