/**
 * @file useSecureHttpClient Hook - Phase 3 React Integration Layer
 * @module api/security/hooks/useSecureHttpClient
 *
 * This hook replaces useSecureApi with proper separation of concerns and dependency injection.
 * It uses the enhanced SecureApiClient with SecurityComposer integration.
 *
 * Phase 3: React Integration Layer
 * - Uses moved security hooks directly
 * - Integrates with SecurityConfigProvider
 * - Provides better separation of concerns
 * - Eliminates HTTP duplication from useSecureApi
 */

'use client';

import { useCallback, useEffect, useMemo, useRef } from 'react';
import type { IHttpClient } from '../../core/interfaces';
import type { RequestConfig } from '../../core/types';
import {
  createSecureApiClient,
  type SecureApiClientConfig,
} from '../secureApiClient';
import type { SecurityFeatures } from '../composer';
import { useAuthContext } from '../../../../contexts/AuthContext';
import { useSecurityConfig } from '../providers/SecurityConfigProvider';

// Import the moved security hooks
import { useCSRFProtection } from './useCSRFProtection';
import { useTokenManagement } from './useTokenManagement';
import { useInputValidation } from './useInputValidation';
import { useSessionSecurity } from './useSessionSecurity';

/**
 * useSecureHttpClient Hook Configuration
 */
export interface UseSecureHttpClientConfig
  extends Partial<SecureApiClientConfig> {
  autoInitialize?: boolean;
  enableLogging?: boolean;
  onSecurityError?: (error: Error) => void;
  onSecurityStatusChange?: (status: any) => void;
}

/**
 * useSecureHttpClient Hook Return Type
 */
export interface UseSecureHttpClientReturn {
  // HTTP Client Interface
  client: IHttpClient;

  // Security Status
  isAuthenticated: boolean;
  hasValidToken: boolean;
  securityStatus: any;

  // Security Actions
  refreshToken: () => Promise<boolean>;
  refreshSecurityFeatures: () => void;
  updateSecurityConfig: (config: any) => void;

  // Utility Functions
  sanitizeInput: (input: any) => any;

  // Status Flags
  isInitialized: boolean;
  isLoading: boolean;
  error: Error | null;
}

/**
 * useSecureHttpClient Hook
 *
 * Replaces useSecureApi with enhanced architecture:
 * - Uses SecureApiClient with SecurityComposer
 * - Integrates with SecurityConfigProvider
 * - Leverages moved security hooks directly
 * - Provides proper separation of concerns
 * - Eliminates HTTP duplication
 */
export function useSecureHttpClient(
  config: UseSecureHttpClientConfig = {}
): UseSecureHttpClientReturn {
  const {
    autoInitialize = true,
    enableLogging = true,
    onSecurityError,
    onSecurityStatusChange,
    ...clientConfig
  } = config;

  // Get auth context and security configuration
  const { session, user, loading: authLoading, signOut } = useAuthContext();
  const { config: securityConfig, isConfigValid } = useSecurityConfig();

  // Initialize security hooks (moved from hooks/security/)
  // SECURITY NOTE: HttpOnly Cookie Compliance
  // Token passed to useTokenManagement is for client-side validation only
  // Actual API authentication relies on HttpOnly cookies via credentials: 'include'
  const csrfProtection = useCSRFProtection();
  const tokenManagement = useTokenManagement(session?.access_token);
  const inputValidation = useInputValidation();
  const sessionSecurity = useSessionSecurity();

  // Refs for stable references
  const clientRef = useRef<any>(null);
  const errorRef = useRef<Error | null>(null);

  // Compute authentication status
  const isAuthenticated = useMemo(() => {
    return !!user && !!session?.access_token && !authLoading;
  }, [user, session?.access_token, authLoading]);

  const hasValidToken = useMemo(() => {
    return (
      isAuthenticated &&
      tokenManagement.isTokenValid &&
      !tokenManagement.isTokenExpired
    );
  }, [
    isAuthenticated,
    tokenManagement.isTokenValid,
    tokenManagement.isTokenExpired,
  ]);

  // Create security features object
  const securityFeatures = useMemo<SecurityFeatures>(
    () => ({
      csrfProtection,
      tokenManagement,
      inputValidation,
      sessionSecurity,
    }),
    [csrfProtection, tokenManagement, inputValidation, sessionSecurity]
  );

  // Create SecureApiClient configuration
  const apiClientConfig = useMemo<SecureApiClientConfig>(
    () => ({
      baseURL: securityConfig.http.baseURL,
      timeout: securityConfig.http.timeout,
      retryAttempts: securityConfig.http.retryAttempts,
      getAuthToken: () => session?.access_token || null,
      enableCSRF: securityConfig.csrf.enabled,
      enableInputSanitization: securityConfig.inputSanitization.enabled,
      enableTokenValidation: securityConfig.tokenValidation.enabled,
      enableAutoLogout: securityConfig.authentication.autoLogout,
      securityConfig,
      validateSecurityFeatures: true,
      ...clientConfig,
    }),
    [securityConfig, session?.access_token, clientConfig]
  );

  // Initialize SecureApiClient
  const client = useMemo(() => {
    try {
      if (!isConfigValid) {
        throw new Error('Invalid security configuration');
      }

      const newClient = createSecureApiClient(
        apiClientConfig,
        securityFeatures
      );

      // Initialize security features if auto-initialize is enabled
      if (autoInitialize && securityFeatures) {
        newClient.initializeSecurity(securityFeatures);
      }

      clientRef.current = newClient;
      errorRef.current = null;

      if (enableLogging) {
        console.log('🔐 useSecureHttpClient: Client initialized successfully', {
          isAuthenticated,
          hasValidToken,
          securityFeatures: Object.keys(securityFeatures).filter(
            key => securityFeatures[key as keyof SecurityFeatures]
          ),
        });
      }

      return newClient;
    } catch (error) {
      const clientError =
        error instanceof Error
          ? error
          : new Error('Failed to initialize secure client');
      errorRef.current = clientError;
      onSecurityError?.(clientError);

      console.error(
        'useSecureHttpClient: Failed to initialize client:',
        clientError
      );
      throw clientError;
    }
  }, [
    apiClientConfig,
    securityFeatures,
    autoInitialize,
    isConfigValid,
    isAuthenticated,
    hasValidToken,
    enableLogging,
    onSecurityError,
  ]);

  // Get security status
  const securityStatus = useMemo(() => {
    try {
      return client?.getSecurityStatus() || null;
    } catch (error) {
      console.warn(
        'useSecureHttpClient: Failed to get security status:',
        error
      );
      return null;
    }
  }, [client]);

  // Security status change effect
  useEffect(() => {
    if (securityStatus && onSecurityStatusChange) {
      onSecurityStatusChange(securityStatus);
    }
  }, [securityStatus, onSecurityStatusChange]);

  // Refresh security features
  const refreshSecurityFeatures = useCallback(() => {
    try {
      if (client && securityFeatures) {
        client.refreshSecurityFeatures();

        if (enableLogging) {
          console.log('🔄 useSecureHttpClient: Security features refreshed');
        }
      }
    } catch (error) {
      console.error(
        'useSecureHttpClient: Failed to refresh security features:',
        error
      );
      onSecurityError?.(
        error instanceof Error ? error : new Error('Refresh failed')
      );
    }
  }, [client, securityFeatures, enableLogging, onSecurityError]);

  // Update security configuration
  const updateSecurityConfig = useCallback(
    (newConfig: any) => {
      try {
        if (client) {
          client.updateSecurityConfig(newConfig);

          if (enableLogging) {
            console.log(
              '🔧 useSecureHttpClient: Security configuration updated',
              newConfig
            );
          }
        }
      } catch (error) {
        console.error(
          'useSecureHttpClient: Failed to update security config:',
          error
        );
        onSecurityError?.(
          error instanceof Error ? error : new Error('Config update failed')
        );
      }
    },
    [client, enableLogging, onSecurityError]
  );

  // Sanitize input function
  const sanitizeInput = useCallback(
    (input: any) => {
      return inputValidation.sanitizeInput(input);
    },
    [inputValidation]
  );

  // Refresh token function
  const refreshToken = useCallback(async (): Promise<boolean> => {
    try {
      return await tokenManagement.refreshToken();
    } catch (error) {
      console.error('useSecureHttpClient: Token refresh failed:', error);
      onSecurityError?.(
        error instanceof Error ? error : new Error('Token refresh failed')
      );
      return false;
    }
  }, [tokenManagement, onSecurityError]);

  return {
    // HTTP Client Interface
    client,

    // Security Status
    isAuthenticated,
    hasValidToken,
    securityStatus,

    // Security Actions
    refreshToken,
    refreshSecurityFeatures,
    updateSecurityConfig,

    // Utility Functions
    sanitizeInput,

    // Status Flags
    isInitialized: !!client && !errorRef.current,
    isLoading: authLoading,
    error: errorRef.current,
  };
}
