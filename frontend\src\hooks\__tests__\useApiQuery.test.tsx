/**
 * @file Tests for useApiQuery hook
 */

import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { act, renderHook, waitFor } from '@testing-library/react';
import React from 'react';

import { useApiQuery } from '../api/useApiQuery';

// Mock the toast hook
jest.mock('@/hooks/utils/use-toast', () => ({
  useToast: () => ({
    toast: jest.fn(),
  }),
}));

describe('useApiQuery', () => {
  let queryClient: QueryClient;

  const createWrapper = ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: {
          retry: false,
        },
      },
    });
  });

  afterEach(() => {
    queryClient.clear();
  });

  it('should return loading state initially', () => {
    const mockFn = jest.fn().mockResolvedValue({ data: 'test' });

    const { result } = renderHook(() => useApiQuery(['test'], mockFn), {
      wrapper: createWrapper,
    });

    expect(result.current.isLoading).toBe(true);
    expect(result.current.data).toBeUndefined();
    expect(result.current.error).toBeNull();
  });

  it('should return data on successful query', async () => {
    const mockData = { data: 'test' };
    const mockFn = jest.fn().mockResolvedValue(mockData);

    const { result } = renderHook(() => useApiQuery(['test'], mockFn), {
      wrapper: createWrapper,
    });

    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true);
    });

    expect(result.current.data).toEqual(mockData);
    expect(result.current.isLoading).toBe(false);
    expect(result.current.error).toBeNull();
  });

  it('should handle errors correctly', async () => {
    const mockError = new Error('Test error');
    const mockFn = jest.fn().mockRejectedValue(mockError);

    const { result } = renderHook(() => useApiQuery(['test'], mockFn), {
      wrapper: createWrapper,
    });

    await waitFor(() => {
      expect(result.current.isError).toBe(true);
    });

    expect(result.current.error).toEqual(mockError);
    expect(result.current.data).toBeUndefined();
    expect(result.current.isLoading).toBe(false);
  });

  it('should provide forceRefresh functionality', async () => {
    const mockData = { data: 'test' };
    const mockFn = jest.fn().mockResolvedValue(mockData);

    const { result } = renderHook(() => useApiQuery(['test'], mockFn), {
      wrapper: createWrapper,
    });

    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true);
    });

    // Clear the mock to test refetch
    mockFn.mockClear();
    mockFn.mockResolvedValue({ data: 'refreshed' });

    await act(async () => {
      await result.current.forceRefresh();
    });

    expect(mockFn).toHaveBeenCalledTimes(1);
  });

  it('should respect cache duration', () => {
    const mockFn = jest.fn().mockResolvedValue({ data: 'test' });
    const cacheDuration = 10000; // 10 seconds

    renderHook(() => useApiQuery(['test'], mockFn, { cacheDuration }), {
      wrapper: createWrapper,
    });

    // The query should be configured with the correct stale time
    const query = queryClient.getQueryCache().find({ queryKey: ['test'] });
    expect(query?.state.dataUpdatedAt).toBeDefined();
  });

  it('should handle dependent queries correctly', async () => {
    const mockFn = jest.fn().mockResolvedValue({ data: 'dependent' });
    const dependency = 'test-dependency';

    const { result } = renderHook(
      () =>
        useApiQuery(['dependent'], () => mockFn(dependency), {
          enabled: !!dependency,
        }),
      { wrapper: createWrapper }
    );

    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true);
    });

    expect(mockFn).toHaveBeenCalledWith(dependency);
    expect(result.current.data).toEqual({ data: 'dependent' });
  });

  it('should provide stale and lastUpdated information', async () => {
    const mockData = { data: 'test' };
    const mockFn = jest.fn().mockResolvedValue(mockData);

    const { result } = renderHook(() => useApiQuery(['test'], mockFn), {
      wrapper: createWrapper,
    });

    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true);
    });

    expect(typeof result.current.isStale).toBe('boolean');
    expect(typeof result.current.lastUpdated).toBe('number');
  });
});
