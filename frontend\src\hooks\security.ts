/**
 * @file Security Hooks Exports - DRY Principle
 * @module hooks/security
 *
 * Centralized exports for all security-related hooks following DRY principles.
 * This provides a single import point for security hooks across the application.
 */

import { usePermissions } from '../lib/api/security';
import { useCSRFProtection } from '../lib/api/security/hooks/useCSRFProtection';
import { useSecureApi } from '../lib/api/security/hooks/useSecureApi';
import { useSessionSecurity } from '../lib/api/security/hooks/useSessionSecurity';
// Import hooks for re-export object
import { useTokenManagement } from '../lib/api/security/hooks/useTokenManagement';
// CSRF Protection Hook (DRY)
export { useCSRFProtection } from '../lib/api/security/hooks/useCSRFProtection';
export type {
  CSRFProtectionActions,
  CSRFProtectionState,
  UseCSRFProtectionReturn,
} from '../lib/api/security/hooks/useCSRFProtection';

// Secure API Hook (DRY + Separation of Concerns)
export { useSecureApi } from '../lib/api/security/hooks/useSecureApi';
export type {
  ApiError,
  ApiResponse,
  RequestConfig,
  UseSecureApiReturn,
} from '../lib/api/security/hooks/useSecureApi';

// Session Security Hook (SRP)
export { useSessionSecurity } from '../lib/api/security/hooks/useSessionSecurity';
export type {
  SessionSecurityActions,
  SessionSecurityState,
  UseSessionSecurityReturn,
} from '../lib/api/security/hooks/useSessionSecurity';

// Token Management Hook (SRP)
export { useTokenManagement } from '../lib/api/security/hooks/useTokenManagement';
export type {
  TokenManagementActions,
  TokenManagementState,
  UseTokenManagementReturn,
} from '../lib/api/security/hooks/useTokenManagement';

// Re-export for convenience (DRY)
export const SecurityHooks = {
  useCSRFProtection,
  usePermissions,
  useSecureApi,
  useSessionSecurity,
  useTokenManagement,
} as const;
