/**
 * Admin Services Index
 *
 * Centralized exports for all admin-related services, providing a clean
 * interface for importing admin functionality throughout the application.
 *
 * This consolidates:
 * - Unified AdminService (main interface)
 * - Specialized services (UserService, AuditService)
 * - Cache utilities
 * - Type definitions
 */

// Export the main AdminService class and singleton instance
export { adminCache, AdminService, adminService } from './adminService';

// Export legacy function names for backward compatibility
export {
  adminService as createAuditLog,
  adminService as createUser,
  adminService as default,
  adminService as deleteUser,
  adminService as getAllUsers,
  adminService as getAuditLogs,
  adminService as getHealthStatus,
  adminService as getPerformanceMetrics,
  adminService as getRecentErrors,
  adminService as toggleUserActivation,
  adminService as updateUser,
} from './adminService';
// Export specialized services for direct access if needed
export { AuditService } from './auditService';

export { UserService } from './UserService';

// Re-export types for convenience
export type {
  AuditLog,
  AuditLogFilters,
  ErrorLogEntry,
  HealthResponse,
  PaginatedResponse,
  PerformanceMetrics,
  User,
  UserFilters,
} from '@/types';
