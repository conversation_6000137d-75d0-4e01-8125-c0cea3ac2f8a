/**
 * @file Tests for CircuitBreakerOverview component.
 * @module components/reliability/widgets/circuit-breakers/__tests__/CircuitBreakerOverview.test
 */

import { render, screen, waitFor } from '@testing-library/react';
import React from 'react';

import { CircuitBreakerOverview } from '../CircuitBreakerOverview';
import { useCircuitBreakerStatus } from '@/lib/stores/queries/useReliability';
import type { CircuitBreakerStatus } from '@/lib/types/domain';

// Mock the useCircuitBreakerStatus hook
jest.mock('@/lib/stores/queries/useReliability', () => ({
  useCircuitBreakerStatus: jest.fn(),
}));

const mockUseCircuitBreakerStatus = useCircuitBreakerStatus as jest.MockedFunction<
  typeof useCircuitBreakerStatus
>;

describe('CircuitBreakerOverview', () => {
  const mockCircuitBreakerData: CircuitBreakerStatus = {
    circuitBreakers: [
      {
        name: 'database-service',
        state: 'CLOSED',
        failureCount: 0,
        successCount: 150,
        timeout: 5000,
      },
      {
        name: 'payment-service',
        state: 'OPEN',
        failureCount: 8,
        lastFailureTime: '2024-01-15T10:30:00Z',
        nextAttempt: '2024-01-15T10:35:00Z',
        timeout: 10000,
      },
      {
        name: 'notification-service',
        state: 'HALF_OPEN',
        failureCount: 3,
        successCount: 2,
        lastFailureTime: '2024-01-15T10:25:00Z',
        timeout: 7500,
      },
    ],
    summary: {
      total: 3,
      closed: 1,
      open: 1,
      halfOpen: 1,
    },
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders loading state correctly', () => {
    mockUseCircuitBreakerStatus.mockReturnValue({
      data: undefined,
      isLoading: true,
      error: null,
    } as any);

    render(<CircuitBreakerOverview />);

    // Check for loading skeletons
    expect(screen.getAllByRole('generic')).toHaveLength(4); // 4 skeleton cards
  });

  it('renders error state correctly', () => {
    mockUseCircuitBreakerStatus.mockReturnValue({
      data: undefined,
      isLoading: false,
      error: new Error('Failed to fetch'),
    } as any);

    render(<CircuitBreakerOverview />);

    expect(screen.getByText('Failed to load circuit breaker data')).toBeInTheDocument();
  });

  it('renders circuit breaker data correctly', async () => {
    mockUseCircuitBreakerStatus.mockReturnValue({
      data: mockCircuitBreakerData,
      isLoading: false,
      error: null,
    } as any);

    render(<CircuitBreakerOverview />);

    // Check summary cards
    expect(screen.getByText('3')).toBeInTheDocument(); // Total
    expect(screen.getByText('1')).toBeInTheDocument(); // Healthy (closed)
    expect(screen.getByText('1')).toBeInTheDocument(); // Testing (half-open)
    expect(screen.getByText('1')).toBeInTheDocument(); // Failed (open)

    // Check labels
    expect(screen.getByText('Total')).toBeInTheDocument();
    expect(screen.getByText('Healthy')).toBeInTheDocument();
    expect(screen.getByText('Testing')).toBeInTheDocument();
    expect(screen.getByText('Failed')).toBeInTheDocument();

    // Check health percentage (1 out of 3 = 33%)
    expect(screen.getByText('33%')).toBeInTheDocument();
  });

  it('calculates health percentage correctly', async () => {
    const allHealthyData: CircuitBreakerStatus = {
      circuitBreakers: [
        { name: 'service-1', state: 'CLOSED', failureCount: 0 },
        { name: 'service-2', state: 'CLOSED', failureCount: 0 },
      ],
      summary: { total: 2, closed: 2, open: 0, halfOpen: 0 },
    };

    mockUseCircuitBreakerStatus.mockReturnValue({
      data: allHealthyData,
      isLoading: false,
      error: null,
    } as any);

    render(<CircuitBreakerOverview />);

    expect(screen.getByText('100%')).toBeInTheDocument();
  });

  it('shows circuit breaker details when showDetails is true', async () => {
    mockUseCircuitBreakerStatus.mockReturnValue({
      data: mockCircuitBreakerData,
      isLoading: false,
      error: null,
    } as any);

    render(<CircuitBreakerOverview showDetails={true} />);

    // Check for circuit breaker names
    expect(screen.getByText('database-service')).toBeInTheDocument();
    expect(screen.getByText('payment-service')).toBeInTheDocument();
    expect(screen.getByText('notification-service')).toBeInTheDocument();

    // Check for status descriptions
    expect(screen.getByText('Operating normally')).toBeInTheDocument();
    expect(screen.getByText('Blocking requests')).toBeInTheDocument();
    expect(screen.getByText('Testing recovery')).toBeInTheDocument();
  });

  it('hides circuit breaker details when showDetails is false', async () => {
    mockUseCircuitBreakerStatus.mockReturnValue({
      data: mockCircuitBreakerData,
      isLoading: false,
      error: null,
    } as any);

    render(<CircuitBreakerOverview showDetails={false} />);

    // Circuit breaker names should not be visible
    expect(screen.queryByText('database-service')).not.toBeInTheDocument();
    expect(screen.queryByText('payment-service')).not.toBeInTheDocument();
    expect(screen.queryByText('notification-service')).not.toBeInTheDocument();
  });

  it('shows failure counts for failed circuit breakers', async () => {
    mockUseCircuitBreakerStatus.mockReturnValue({
      data: mockCircuitBreakerData,
      isLoading: false,
      error: null,
    } as any);

    render(<CircuitBreakerOverview showDetails={true} />);

    // Check for failure count display
    expect(screen.getByText('8 failures')).toBeInTheDocument();
    expect(screen.getByText('3 failures')).toBeInTheDocument();
  });

  it('limits displayed circuit breakers to 5', async () => {
    const manyBreakersData: CircuitBreakerStatus = {
      circuitBreakers: Array.from({ length: 8 }, (_, i) => ({
        name: `service-${i + 1}`,
        state: 'CLOSED' as const,
        failureCount: 0,
      })),
      summary: { total: 8, closed: 8, open: 0, halfOpen: 0 },
    };

    mockUseCircuitBreakerStatus.mockReturnValue({
      data: manyBreakersData,
      isLoading: false,
      error: null,
    } as any);

    render(<CircuitBreakerOverview showDetails={true} />);

    // Should show first 5 services
    expect(screen.getByText('service-1')).toBeInTheDocument();
    expect(screen.getByText('service-5')).toBeInTheDocument();
    
    // Should show "+3 more" message
    expect(screen.getByText('+3 more circuit breakers')).toBeInTheDocument();
  });

  it('applies custom className', () => {
    mockUseCircuitBreakerStatus.mockReturnValue({
      data: mockCircuitBreakerData,
      isLoading: false,
      error: null,
    } as any);

    const { container } = render(
      <CircuitBreakerOverview className="custom-class" />
    );

    expect(container.firstChild).toHaveClass('custom-class');
  });

  it('handles empty circuit breaker data', async () => {
    const emptyData: CircuitBreakerStatus = {
      circuitBreakers: [],
      summary: { total: 0, closed: 0, open: 0, halfOpen: 0 },
    };

    mockUseCircuitBreakerStatus.mockReturnValue({
      data: emptyData,
      isLoading: false,
      error: null,
    } as any);

    render(<CircuitBreakerOverview />);

    // Should show 0 for all counts
    expect(screen.getAllByText('0')).toHaveLength(4); // Total, Healthy, Testing, Failed
    
    // Health percentage should be 100% when no circuit breakers exist
    expect(screen.getByText('100%')).toBeInTheDocument();
  });

  it('has proper accessibility attributes', async () => {
    mockUseCircuitBreakerStatus.mockReturnValue({
      data: mockCircuitBreakerData,
      isLoading: false,
      error: null,
    } as any);

    render(<CircuitBreakerOverview />);

    // Check for progress bar accessibility
    const progressBar = screen.getByRole('progressbar');
    expect(progressBar).toHaveAttribute('aria-label', 'Circuit breaker health: 33%');
  });
});
