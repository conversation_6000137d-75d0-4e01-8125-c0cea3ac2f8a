# 🔐 WorkHub Authentication System - Component Diagrams

**Document Version:** 1.0  
**Date:** January 2025  
**Status:** ✅ **ACTIVE** - Visual Architecture Reference  
**Last Updated:** January 2025

---

## 📋 **Overview**

This document provides visual diagrams and component relationships for the WorkHub authentication system. These diagrams complement the main architecture documentation and provide clear visual references for developers.

---

## 🏗️ **System Component Architecture**

### **High-Level Component Diagram**

```mermaid
graph TB
    subgraph "Frontend Application"
        subgraph "Authentication Layer"
            AC[AuthContext Provider]
            LF[LoginForm Component]
            PR[ProtectedRoute Component]
            AT[Auth Types & Interfaces]
        end
        
        subgraph "API Layer"
            API[ApiClient Core]
            SAC[SecureApiClient]
            SF[Service Factory]
            DS[Domain Services]
        end
        
        subgraph "Security Layer"
            TM[Token Management]
            RM[Role Management]
            SM[Session Management]
            CS[CSRF Protection]
        end
        
        subgraph "UI Components"
            D[Dashboard]
            RD[Reliability Dashboard]
            RP[Reporting Pages]
            UP[User Profile]
        end
    end
    
    subgraph "Backend Services"
        subgraph "Authentication Middleware"
            JM[JWT Middleware]
            RA[Role Authorization]
            RLS[RLS Policies]
        end
        
        subgraph "API Endpoints"
            AE[Auth Endpoints]
            BE[Business Endpoints]
            HE[Health Endpoints]
        end
    end
    
    subgraph "External Services"
        SB[Supabase Auth]
        DB[(PostgreSQL Database)]
        CACHE[(Redis Cache)]
    end
    
    %% Connections
    AC --> LF
    AC --> PR
    AC --> TM
    PR --> RM
    API --> SAC
    SAC --> SF
    SF --> DS
    TM --> SM
    
    PR --> D
    PR --> RD
    PR --> RP
    PR --> UP
    
    DS --> JM
    JM --> RA
    RA --> RLS
    JM --> AE
    RA --> BE
    
    AC --> SB
    RLS --> DB
    TM --> CACHE
    
    %% Styling
    classDef frontend fill:#e1f5fe
    classDef backend fill:#f3e5f5
    classDef external fill:#e8f5e8
    classDef security fill:#fff3e0
    
    class AC,LF,PR,AT,API,SAC,SF,DS,D,RD,RP,UP frontend
    class JM,RA,RLS,AE,BE,HE backend
    class SB,DB,CACHE external
    class TM,RM,SM,CS security
```

---

## 🔄 **Authentication Flow Diagram**

### **Complete Authentication Process**

```mermaid
sequenceDiagram
    participant U as User
    participant UI as Login UI
    participant AC as AuthContext
    participant API as ApiClient
    participant SB as Supabase
    participant BE as Backend
    participant DB as Database
    
    Note over U,DB: Initial Page Load
    U->>UI: Navigate to protected route
    UI->>AC: Check authentication status
    AC->>SB: getSession()
    SB-->>AC: session | null
    
    alt User not authenticated
        AC-->>UI: Redirect to login
        UI->>U: Show login form
        
        Note over U,DB: Login Process
        U->>UI: Enter credentials
        UI->>AC: signIn(email, password)
        AC->>SB: signInWithPassword()
        SB-->>AC: { user, session, error }
        
        alt Login successful
            AC->>AC: setUser(user)
            AC->>AC: setSession(session)
            AC->>API: setAuthTokenProvider()
            AC-->>UI: Success
            UI->>U: Redirect to dashboard
        else Login failed
            AC-->>UI: Error message
            UI->>U: Show error
        end
    else User authenticated
        AC-->>UI: User data
        UI->>U: Show protected content
    end
    
    Note over U,DB: API Request Flow
    U->>UI: Trigger API call
    UI->>API: makeRequest()
    API->>API: getAuthToken()
    API->>BE: Request with JWT
    BE->>BE: Validate JWT
    BE->>DB: Query with RLS
    DB-->>BE: Filtered data
    BE-->>API: Response
    API-->>UI: Data
    UI->>U: Display data
```

---

## 🔐 **Security Component Interaction**

### **JWT Token Lifecycle**

```mermaid
stateDiagram-v2
    [*] --> Unauthenticated
    
    Unauthenticated --> Authenticating: signIn()
    Authenticating --> Authenticated: Success
    Authenticating --> Unauthenticated: Failure
    
    Authenticated --> TokenValid: Check token
    TokenValid --> TokenExpiring: Near expiry
    TokenExpiring --> Refreshing: Auto refresh
    Refreshing --> TokenValid: Success
    Refreshing --> Unauthenticated: Refresh failed
    
    TokenValid --> APICall: Make request
    APICall --> TokenValid: Success
    APICall --> Unauthenticated: 401 Error
    
    Authenticated --> Unauthenticated: signOut()
    TokenValid --> Unauthenticated: Manual logout
    
    note right of TokenExpiring
        Automatic refresh when
        token expires in < 5 minutes
    end note
    
    note right of APICall
        All API calls include
        JWT token automatically
    end note
```

---

## 🏛️ **Role-Based Access Control (RBAC)**

### **Permission Hierarchy**

```mermaid
graph TD
    SA[SUPER_ADMIN] --> A[ADMIN]
    A --> M[MANAGER]
    M --> E[EMPLOYEE]
    E --> V[VIEWER]
    
    subgraph "Permissions"
        SA --> P1[Full System Access]
        A --> P2[Administrative Access]
        M --> P3[Management Operations]
        E --> P4[Basic User Access]
        V --> P5[Read-Only Access]
    end
    
    subgraph "Resource Access"
        P1 --> R1[Users: CRUD]
        P1 --> R2[Vehicles: CRUD]
        P1 --> R3[Tasks: CRUD]
        P1 --> R4[Reports: CRUD]
        P1 --> R5[System: CRUD]
        
        P2 --> R6[Users: CRUD]
        P2 --> R7[Vehicles: CRUD]
        P2 --> R8[Tasks: CRUD]
        P2 --> R9[Reports: CRUD]
        
        P3 --> R10[Users: R]
        P3 --> R11[Vehicles: CRUD]
        P3 --> R12[Tasks: CRUD]
        P3 --> R13[Reports: CR]
        
        P4 --> R14[Users: R]
        P4 --> R15[Vehicles: RU]
        P4 --> R16[Tasks: CRUD]
        P4 --> R17[Reports: R]
        
        P5 --> R18[Users: R]
        P5 --> R19[Vehicles: R]
        P5 --> R20[Tasks: R]
        P5 --> R21[Reports: R]
    end
    
    %% Styling
    classDef role fill:#e3f2fd
    classDef permission fill:#f1f8e9
    classDef resource fill:#fff3e0
    
    class SA,A,M,E,V role
    class P1,P2,P3,P4,P5 permission
    class R1,R2,R3,R4,R5,R6,R7,R8,R9,R10,R11,R12,R13,R14,R15,R16,R17,R18,R19,R20,R21 resource
```

---

## 🔧 **API Client Architecture**

### **Service Layer Structure**

```mermaid
graph TB
    subgraph "Application Layer"
        C[React Components]
        H[Custom Hooks]
    end
    
    subgraph "Service Layer"
        SF[Service Factory]
        
        subgraph "Domain Services"
            DS1[DelegationApiService]
            DS2[VehicleApiService]
            DS3[TaskApiService]
            DS4[EmployeeApiService]
            DS5[ReliabilityApiService]
        end
        
        subgraph "Base Services"
            BAS[BaseApiService]
            DT[Data Transformers]
        end
    end
    
    subgraph "Core Layer"
        AC[ApiClient Core]
        SAC[SecureApiClient]
        
        subgraph "Security Features"
            TM[Token Management]
            IS[Input Sanitization]
            CS[CSRF Protection]
            AL[Auto Logout]
        end
    end
    
    subgraph "Infrastructure"
        HTTP[HTTP Client]
        CB[Circuit Breaker]
        CACHE[Response Cache]
        RETRY[Retry Logic]
    end
    
    %% Connections
    C --> H
    H --> SF
    SF --> DS1
    SF --> DS2
    SF --> DS3
    SF --> DS4
    SF --> DS5
    
    DS1 --> BAS
    DS2 --> BAS
    DS3 --> BAS
    DS4 --> BAS
    DS5 --> BAS
    
    BAS --> DT
    BAS --> SAC
    SAC --> AC
    
    SAC --> TM
    SAC --> IS
    SAC --> CS
    SAC --> AL
    
    AC --> HTTP
    AC --> CB
    AC --> CACHE
    AC --> RETRY
    
    %% Styling
    classDef app fill:#e8f5e8
    classDef service fill:#e1f5fe
    classDef core fill:#f3e5f5
    classDef infra fill:#fff3e0
    
    class C,H app
    class SF,DS1,DS2,DS3,DS4,DS5,BAS,DT service
    class AC,SAC,TM,IS,CS,AL core
    class HTTP,CB,CACHE,RETRY infra
```

---

## 🔄 **State Management Flow**

### **Authentication State Transitions**

```mermaid
stateDiagram-v2
    [*] --> INITIALIZING
    
    INITIALIZING --> LOADING: Start auth check
    LOADING --> AUTHENTICATED: Valid session found
    LOADING --> UNAUTHENTICATED: No session
    LOADING --> ERROR: Auth check failed
    
    UNAUTHENTICATED --> AUTHENTICATING: User login attempt
    AUTHENTICATING --> AUTHENTICATED: Login success
    AUTHENTICATING --> ERROR: Login failed
    AUTHENTICATING --> UNAUTHENTICATED: Login cancelled
    
    AUTHENTICATED --> REFRESHING: Token near expiry
    REFRESHING --> AUTHENTICATED: Refresh success
    REFRESHING --> UNAUTHENTICATED: Refresh failed
    
    AUTHENTICATED --> UNAUTHENTICATED: Logout
    ERROR --> LOADING: Retry
    ERROR --> UNAUTHENTICATED: Clear error
    
    note right of AUTHENTICATED
        User has valid session
        API calls are authorized
    end note
    
    note right of REFRESHING
        Automatic token refresh
        User experience uninterrupted
    end note
```

---

## 🛡️ **Security Layer Integration**

### **Multi-Layer Security Architecture**

```mermaid
graph TB
    subgraph "Frontend Security"
        subgraph "Input Layer"
            IV[Input Validation]
            IS[Input Sanitization]
            XSS[XSS Protection]
        end
        
        subgraph "Authentication Layer"
            TM[Token Management]
            SM[Session Management]
            AL[Auto Logout]
        end
        
        subgraph "Authorization Layer"
            RM[Role Management]
            PM[Permission Checks]
            RC[Route Control]
        end
    end
    
    subgraph "Transport Security"
        HTTPS[HTTPS/TLS]
        CORS[CORS Policy]
        CSP[Content Security Policy]
    end
    
    subgraph "Backend Security"
        subgraph "API Security"
            JV[JWT Validation]
            RA[Role Authorization]
            RL[Rate Limiting]
        end
        
        subgraph "Data Security"
            RLS[Row Level Security]
            ENC[Data Encryption]
            AUDIT[Audit Logging]
        end
    end
    
    subgraph "Database Security"
        AUTH[Supabase Auth]
        POLICIES[RLS Policies]
        RBAC[Role-Based Access]
    end
    
    %% Flow connections
    IV --> TM
    IS --> TM
    XSS --> TM
    
    TM --> RM
    SM --> RM
    AL --> RM
    
    RM --> PM
    PM --> RC
    
    RC --> HTTPS
    HTTPS --> CORS
    CORS --> CSP
    
    CSP --> JV
    JV --> RA
    RA --> RL
    
    RL --> RLS
    RLS --> ENC
    ENC --> AUDIT
    
    AUDIT --> AUTH
    AUTH --> POLICIES
    POLICIES --> RBAC
    
    %% Styling
    classDef frontend fill:#e1f5fe
    classDef transport fill:#f3e5f5
    classDef backend fill:#e8f5e8
    classDef database fill:#fff3e0
    
    class IV,IS,XSS,TM,SM,AL,RM,PM,RC frontend
    class HTTPS,CORS,CSP transport
    class JV,RA,RL,RLS,ENC,AUDIT backend
    class AUTH,POLICIES,RBAC database
```

---

## 📱 **Component Integration Map**

### **React Component Hierarchy**

```mermaid
graph TD
    APP[App.tsx]
    
    APP --> ACP[AuthContext Provider]
    ACP --> ROUTER[Router]
    
    ROUTER --> PUBLIC[Public Routes]
    ROUTER --> PROTECTED[Protected Routes]
    
    PUBLIC --> LOGIN[Login Page]
    PUBLIC --> LANDING[Landing Page]
    
    PROTECTED --> PR[ProtectedRoute Wrapper]
    PR --> DASHBOARD[Dashboard]
    PR --> RELIABILITY[Reliability Dashboard]
    PR --> REPORTING[Reporting Pages]
    PR --> PROFILE[User Profile]
    
    LOGIN --> LF[LoginForm]
    LF --> UI_COMPONENTS[UI Components]
    
    DASHBOARD --> WIDGETS[Dashboard Widgets]
    RELIABILITY --> RW[Reliability Widgets]
    REPORTING --> RT[Report Tables]
    
    %% Auth Context connections
    ACP -.-> LF
    ACP -.-> PR
    ACP -.-> WIDGETS
    ACP -.-> RW
    ACP -.-> RT
    
    %% Styling
    classDef app fill:#e3f2fd
    classDef auth fill:#f1f8e9
    classDef public fill:#fff3e0
    classDef protected fill:#e8f5e8
    classDef components fill:#fce4ec
    
    class APP app
    class ACP,PR,LF auth
    class PUBLIC,LOGIN,LANDING public
    class PROTECTED,DASHBOARD,RELIABILITY,REPORTING,PROFILE protected
    class UI_COMPONENTS,WIDGETS,RW,RT components
```

---

## 🔗 **Related Documentation**

- [`AUTHENTICATION_SYSTEM_ARCHITECTURE.md`](./AUTHENTICATION_SYSTEM_ARCHITECTURE.md) - Main architecture documentation
- [`../security/SECURITY_ENHANCEMENT_PLAN_V3.md`](../security/SECURITY_ENHANCEMENT_PLAN_V3.md) - Security implementation details
- [`../../frontend/HANDOFF_SECURE_API_ARCHITECTURE.md`](../../frontend/HANDOFF_SECURE_API_ARCHITECTURE.md) - API architecture details

---

**Document Maintainer:** Development Team  
**Review Schedule:** Quarterly  
**Next Review:** April 2025
