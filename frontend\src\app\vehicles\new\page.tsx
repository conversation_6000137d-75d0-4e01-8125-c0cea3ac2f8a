'use client';

import { Car } from 'lucide-react';
import { useRouter } from 'next/navigation';
import React from 'react';

import type { CreateVehicleData } from '@/lib/types/domain';

import { PageHeader } from '@/components/ui/PageHeader';
import { VehicleForm } from '@/components/features/vehicles/forms/vehicleForm';
import { useFormSubmission } from '@/hooks/forms/useFormSubmission';
import { useCreateVehicle } from '@/lib/stores/queries/useVehicles';

const AddVehiclePage = () => {
  const router = useRouter();
  const { mutateAsync: createVehicle } = useCreateVehicle();

  // ✅ Using modern useFormSubmission hook with SOLID principles
  const { handleSubmit, isLoading, error, clearError, state, retry } =
    useFormSubmission(
      async (data: CreateVehicleData) => {
        const newVehicle = await createVehicle(data);
        // Navigate to vehicles list after successful creation
        router.push('/vehicles');
        return newVehicle;
      },
      {
        toast: {
          entityType: 'vehicle',
          entity: { make: '', model: '' }, // Will be populated with actual data
          successMessage: 'Vehicle added to fleet! 🚗',
        },
        preSubmitValidation: data => {
          // Ensure required fields are present and not empty
          if (!data.make?.trim()) {
            throw new Error('Make is required');
          }
          if (!data.model?.trim()) {
            throw new Error('Model is required');
          }
          if (!data.licensePlate?.trim()) {
            throw new Error('License Plate is required');
          }
          return true;
        },
        transformData: data => ({
          ...data,
          year:
            typeof data.year === 'string' ? parseInt(data.year, 10) : data.year,
          make: data.make?.trim() || '',
          model: data.model?.trim() || '',
          licensePlate: data.licensePlate?.trim()?.toUpperCase() || '',
        }),
        retry: {
          maxAttempts: 3,
          exponentialBackoff: true,
          retryCondition: error =>
            error.message.includes('network') ||
            error.message.includes('timeout'),
        },
      }
    );

  return (
    <div className="container mx-auto space-y-8 py-8">
      <PageHeader
        description="Enter the details for your new vehicle."
        icon={Car}
        title="Add New Vehicle"
      />

      {/* Enhanced error display with retry functionality */}
      {error && (
        <div className="rounded-md bg-red-50 border border-red-200 p-4">
          <div className="flex items-start justify-between">
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <svg
                  className="h-5 w-5 text-red-400"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Error</h3>
                <p className="text-sm text-red-700 mt-1">{error}</p>
              </div>
            </div>
            <div className="flex space-x-2">
              {state === 'error' && (
                <button
                  onClick={retry}
                  className="text-red-700 hover:text-red-900 text-sm font-medium"
                >
                  Retry
                </button>
              )}
              <button
                onClick={clearError}
                className="text-red-700 hover:text-red-900 text-sm"
              >
                ✕
              </button>
            </div>
          </div>
        </div>
      )}

      <VehicleForm
        isEditing={false}
        isLoading={isLoading}
        onSubmit={handleSubmit}
      />
    </div>
  );
};

export default AddVehiclePage;
