/**
 * @file useSecureApiClient Hook Tests
 * @module api/security/__tests__/useSecureApiClient.test
 * 
 * Phase 5: Testing & Validation
 * Comprehensive tests for useSecureApiClient hook
 */

import { renderHook, act } from '@testing-library/react';
import { useSecureApiClient, useSecureApiReplacement } from '../hooks/useSecureApiClient';

// Mock dependencies
jest.mock('../hooks/useSecureHttpClient', () => ({
  useSecureHttpClient: jest.fn(() => ({
    client: {
      get: jest.fn(),
      post: jest.fn(),
      put: jest.fn(),
      patch: jest.fn(),
      delete: jest.fn(),
    },
    isAuthenticated: true,
    hasValidToken: true,
    securityStatus: {
      threatLevel: 'low',
      isAuthenticated: true,
      hasValidToken: true,
    },
    refreshToken: jest.fn().mockResolvedValue(true),
    refreshSecurityFeatures: jest.fn(),
    updateSecurityConfig: jest.fn(),
    sanitizeInput: jest.fn((input) => input),
    isInitialized: true,
    isLoading: false,
    error: null,
  })),
}));

jest.mock('../providers/SecurityConfigProvider', () => ({
  useSecurityConfig: jest.fn(() => ({
    config: {
      csrf: { enabled: true },
      tokenValidation: { enabled: true },
      inputSanitization: { enabled: true },
      authentication: { enabled: true },
    },
    isConfigValid: true,
  })),
}));

describe('useSecureApiClient', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Hook Initialization', () => {
    it('should initialize with default configuration', () => {
      const { result } = renderHook(() => useSecureApiClient());

      expect(result.current.isAuthenticated).toBe(true);
      expect(result.current.hasValidToken).toBe(true);
      expect(result.current.isInitialized).toBe(true);
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBeNull();
    });

    it('should accept custom configuration', () => {
      const config = {
        enableLogging: false,
        enableCSRF: false,
      };

      const { result } = renderHook(() => useSecureApiClient(config));

      expect(result.current.client).toBeDefined();
      expect(result.current.securityStatus).toBeDefined();
    });
  });

  describe('Security Status', () => {
    it('should provide comprehensive security status', () => {
      const { result } = renderHook(() => useSecureApiClient());

      expect(result.current.securityStatus).toEqual({
        threatLevel: 'low',
        isAuthenticated: true,
        hasValidToken: true,
      });
    });

    it('should handle security status updates', () => {
      const { result } = renderHook(() => useSecureApiClient());

      act(() => {
        result.current.refreshSecurityFeatures();
      });

      // Should not throw and should call the underlying method
      expect(result.current.refreshSecurityFeatures).toBeDefined();
    });
  });

  describe('API Request Methods', () => {
    it('should provide secureRequest method', async () => {
      const { result } = renderHook(() => useSecureApiClient());

      const requestConfig = {
        url: '/api/test',
        method: 'GET' as const,
      };

      // Mock the client response
      const mockResponse = { data: { success: true } };
      (result.current.client.get as jest.Mock).mockResolvedValue(mockResponse.data);

      const response = await result.current.secureRequest(requestConfig);

      expect(response).toEqual({
        data: mockResponse.data,
        headers: {},
        status: 200,
        statusText: 'OK',
      });
    });

    it('should handle different HTTP methods', async () => {
      const { result } = renderHook(() => useSecureApiClient());

      const methods = ['GET', 'POST', 'PUT', 'PATCH', 'DELETE'] as const;

      for (const method of methods) {
        const requestConfig = {
          url: '/api/test',
          method,
          data: method !== 'GET' ? { test: 'data' } : undefined,
        };

        const mockResponse = { success: true };
        const clientMethod = method.toLowerCase() as keyof typeof result.current.client;
        (result.current.client[clientMethod] as jest.Mock).mockResolvedValue(mockResponse);

        const response = await result.current.secureRequest(requestConfig);

        expect(response.data).toEqual(mockResponse);
      }
    });

    it('should handle request errors', async () => {
      const { result } = renderHook(() => useSecureApiClient());

      const requestConfig = {
        url: '/api/test',
        method: 'GET' as const,
      };

      const mockError = new Error('Request failed');
      (result.current.client.get as jest.Mock).mockRejectedValue(mockError);

      await expect(result.current.secureRequest(requestConfig)).rejects.toThrow('Request failed');
    });
  });

  describe('Security Actions', () => {
    it('should provide token refresh functionality', async () => {
      const { result } = renderHook(() => useSecureApiClient());

      const refreshResult = await result.current.refreshToken();

      expect(refreshResult).toBe(true);
      expect(result.current.refreshToken).toBeDefined();
    });

    it('should provide input sanitization', () => {
      const { result } = renderHook(() => useSecureApiClient());

      const input = '<script>alert("xss")</script>';
      const sanitized = result.current.sanitizeInput(input);

      expect(result.current.sanitizeInput).toBeDefined();
      expect(typeof sanitized).toBe('string');
    });

    it('should allow security configuration updates', () => {
      const { result } = renderHook(() => useSecureApiClient());

      const newConfig = { csrf: { enabled: false } };

      act(() => {
        result.current.updateSecurityConfig(newConfig);
      });

      expect(result.current.updateSecurityConfig).toBeDefined();
    });
  });

  describe('Error Handling', () => {
    it('should handle initialization errors gracefully', () => {
      // Mock an error in the underlying hook
      const mockUseSecureHttpClient = require('../hooks/useSecureHttpClient').useSecureHttpClient;
      mockUseSecureHttpClient.mockImplementationOnce(() => {
        throw new Error('Initialization failed');
      });

      expect(() => {
        renderHook(() => useSecureApiClient());
      }).toThrow('Initialization failed');
    });

    it('should provide error information when available', () => {
      const mockUseSecureHttpClient = require('../hooks/useSecureHttpClient').useSecureHttpClient;
      const mockError = new Error('Test error');
      
      mockUseSecureHttpClient.mockReturnValueOnce({
        client: null,
        isAuthenticated: false,
        hasValidToken: false,
        securityStatus: null,
        refreshToken: jest.fn(),
        refreshSecurityFeatures: jest.fn(),
        updateSecurityConfig: jest.fn(),
        sanitizeInput: jest.fn(),
        isInitialized: false,
        isLoading: false,
        error: mockError,
      });

      const { result } = renderHook(() => useSecureApiClient());

      expect(result.current.error).toBe(mockError);
      expect(result.current.isInitialized).toBe(false);
    });
  });
});

describe('useSecureApiReplacement', () => {
  it('should provide backward-compatible interface', () => {
    const { result } = renderHook(() => useSecureApiReplacement());

    // Should only include the original useSecureApi interface
    expect(result.current).toHaveProperty('hasValidToken');
    expect(result.current).toHaveProperty('isAuthenticated');
    expect(result.current).toHaveProperty('refreshToken');
    expect(result.current).toHaveProperty('sanitizeInput');
    expect(result.current).toHaveProperty('secureRequest');

    // Should not include enhanced features
    expect(result.current).not.toHaveProperty('client');
    expect(result.current).not.toHaveProperty('securityStatus');
    expect(result.current).not.toHaveProperty('refreshSecurityFeatures');
  });

  it('should maintain the same API as original useSecureApi', async () => {
    const { result } = renderHook(() => useSecureApiReplacement());

    // Test the interface matches exactly
    expect(typeof result.current.hasValidToken).toBe('boolean');
    expect(typeof result.current.isAuthenticated).toBe('boolean');
    expect(typeof result.current.refreshToken).toBe('function');
    expect(typeof result.current.sanitizeInput).toBe('function');
    expect(typeof result.current.secureRequest).toBe('function');

    // Test that secureRequest works
    const requestConfig = {
      url: '/api/test',
      method: 'GET' as const,
    };

    const mockResponse = { data: { success: true } };
    const mockUseSecureHttpClient = require('../hooks/useSecureHttpClient').useSecureHttpClient;
    const mockClient = mockUseSecureHttpClient().client;
    mockClient.get.mockResolvedValue(mockResponse.data);

    const response = await result.current.secureRequest(requestConfig);
    expect(response.data).toEqual(mockResponse.data);
  });
});
