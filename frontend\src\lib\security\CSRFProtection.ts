/**
 * @file CSRF Protection Service - Single Responsibility Principle (SRP)
 * @module lib/security/CSRFProtection
 * 
 * This class handles ONLY CSRF protection operations following SRP principles.
 * It provides CSRF token generation, validation, and request modification.
 * 
 * SECURITY NOTE: This manages CSRF protection without handling authentication logic.
 */

export interface CSRFToken {
  token: string;
  expiresAt: Date;
  isValid: boolean;
}

export interface RequestConfig {
  url: string;
  method?: string;
  headers?: Record<string, string>;
  body?: any;
}

export interface CSRFValidationResult {
  isValid: boolean;
  error?: string;
  token?: string;
}

/**
 * CSRFProtection - Single Responsibility: CSRF Protection Operations Only
 * 
 * Handles CSRF token generation, validation, and request modification.
 * Does NOT handle authentication or session management.
 */
export class CSRFProtection {
  private static readonly CSRF_HEADER_NAME = 'X-CSRF-Token';
  private static readonly CSRF_COOKIE_NAME = 'csrf-token';
  private static readonly CSRF_STORAGE_KEY = 'workhub_csrf_token';
  private static readonly TOKEN_LIFETIME_MINUTES = 60; // 1 hour

  /**
   * Generate CSRF token
   * Single responsibility: CSRF token generation only
   */
  static generateToken(): string {
    // Generate cryptographically secure random token
    const array = new Uint8Array(32);
    if (typeof window !== 'undefined' && window.crypto) {
      window.crypto.getRandomValues(array);
    } else {
      // Fallback for environments without crypto API
      for (let i = 0; i < array.length; i++) {
        array[i] = Math.floor(Math.random() * 256);
      }
    }
    
    // Convert to base64 string
    return btoa(String.fromCharCode(...array))
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=/g, '');
  }

  /**
   * Validate CSRF token
   * Single responsibility: CSRF token validation only
   */
  static validateToken(token: string): CSRFValidationResult {
    if (!token || typeof token !== 'string') {
      return {
        isValid: false,
        error: 'Invalid token format',
      };
    }

    // Check token format (base64url)
    const base64UrlPattern = /^[A-Za-z0-9_-]+$/;
    if (!base64UrlPattern.test(token)) {
      return {
        isValid: false,
        error: 'Invalid token format',
      };
    }

    // Check token length (should be 43 characters for 32-byte token)
    if (token.length < 32) {
      return {
        isValid: false,
        error: 'Token too short',
      };
    }

    // Get stored token for comparison
    const storedToken = this.getStoredToken();
    if (!storedToken) {
      return {
        isValid: false,
        error: 'No stored token found',
      };
    }

    // Check if stored token is expired
    if (!storedToken.isValid || storedToken.expiresAt < new Date()) {
      return {
        isValid: false,
        error: 'Stored token expired',
      };
    }

    // Compare tokens
    if (token !== storedToken.token) {
      return {
        isValid: false,
        error: 'Token mismatch',
      };
    }

    return {
      isValid: true,
      token,
    };
  }

  /**
   * Attach CSRF token to request
   * Single responsibility: Request modification only
   */
  static attachToRequest(config: RequestConfig): RequestConfig {
    // Only attach to state-changing requests
    const stateChangingMethods = ['POST', 'PUT', 'PATCH', 'DELETE'];
    const method = (config.method || 'GET').toUpperCase();
    
    if (!stateChangingMethods.includes(method)) {
      return config;
    }

    // Get current token
    const csrfToken = this.getCurrentToken();
    if (!csrfToken) {
      console.warn('No CSRF token available for request');
      return config;
    }

    // Add CSRF header
    const headers = {
      ...config.headers,
      [this.CSRF_HEADER_NAME]: csrfToken.token,
    };

    return {
      ...config,
      headers,
    };
  }

  /**
   * Get current valid CSRF token
   * Single responsibility: Current token retrieval only
   */
  static getCurrentToken(): CSRFToken | null {
    const storedToken = this.getStoredToken();
    
    if (!storedToken || !storedToken.isValid || storedToken.expiresAt < new Date()) {
      // Generate new token if none exists or expired
      return this.refreshToken();
    }

    return storedToken;
  }

  /**
   * Refresh CSRF token
   * Single responsibility: Token refresh only
   */
  static refreshToken(): CSRFToken {
    const token = this.generateToken();
    const expiresAt = new Date(Date.now() + (this.TOKEN_LIFETIME_MINUTES * 60 * 1000));
    
    const csrfToken: CSRFToken = {
      token,
      expiresAt,
      isValid: true,
    };

    this.storeToken(csrfToken);
    return csrfToken;
  }

  /**
   * Clear CSRF token
   * Single responsibility: Token clearing only
   */
  static clearToken(): void {
    if (typeof window === 'undefined') return;

    try {
      localStorage.removeItem(this.CSRF_STORAGE_KEY);
      
      // Also clear cookie if it exists
      document.cookie = `${this.CSRF_COOKIE_NAME}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/`;
    } catch (error) {
      console.error('Failed to clear CSRF token:', error);
    }
  }

  /**
   * Initialize CSRF protection
   * Single responsibility: CSRF initialization only
   */
  static initialize(): CSRFToken {
    // Generate initial token
    return this.refreshToken();
  }

  /**
   * Check if CSRF protection is required for request
   * Single responsibility: CSRF requirement check only
   */
  static isProtectionRequired(config: RequestConfig): boolean {
    const stateChangingMethods = ['POST', 'PUT', 'PATCH', 'DELETE'];
    const method = (config.method || 'GET').toUpperCase();
    
    return stateChangingMethods.includes(method);
  }

  /**
   * Extract CSRF token from response headers
   * Single responsibility: Token extraction only
   */
  static extractTokenFromResponse(headers: Record<string, string>): string | null {
    return headers[this.CSRF_HEADER_NAME.toLowerCase()] || 
           headers[this.CSRF_HEADER_NAME] || 
           null;
  }

  // Private helper methods

  /**
   * Get stored CSRF token
   * Single responsibility: Token storage retrieval only
   */
  private static getStoredToken(): CSRFToken | null {
    if (typeof window === 'undefined') return null;

    try {
      const tokenJson = localStorage.getItem(this.CSRF_STORAGE_KEY);
      if (!tokenJson) return null;

      const tokenData = JSON.parse(tokenJson);
      return {
        ...tokenData,
        expiresAt: new Date(tokenData.expiresAt),
      };
    } catch (error) {
      console.error('Failed to get stored CSRF token:', error);
      return null;
    }
  }

  /**
   * Store CSRF token
   * Single responsibility: Token storage only
   */
  private static storeToken(csrfToken: CSRFToken): void {
    if (typeof window === 'undefined') return;

    try {
      localStorage.setItem(this.CSRF_STORAGE_KEY, JSON.stringify(csrfToken));
    } catch (error) {
      console.error('Failed to store CSRF token:', error);
    }
  }
}
