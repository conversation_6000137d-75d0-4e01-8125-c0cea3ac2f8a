/**
 * @file Tests for the enhanced AdminService using BaseApiService infrastructure
 */

import type { ApiClient } from '@/lib/api/core/apiClient';

import { AdminService } from '../adminService';

// Mock the API client
jest.mock('@/lib/api/core/apiClient');
jest.mock('@/lib/api/services/apiServiceFactory');

describe('AdminService', () => {
  let adminService: AdminService;
  let mockApiClient: jest.Mocked<ApiClient>;

  beforeEach(() => {
    // Create a mock API client
    mockApiClient = {
      delete: jest.fn(),
      get: jest.fn(),
      patch: jest.fn(),
      post: jest.fn(),
      put: jest.fn(),
    } as any;

    // Create AdminService instance with mock client
    adminService = new AdminService(mockApiClient);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('User Management', () => {
    describe('createUser', () => {
      it('should create a user successfully', async () => {
        const userData = {
          email: '<EMAIL>',
          isActive: true,
          role: 'USER',
        };

        const expectedUser = {
          email: '<EMAIL>',
          id: '123',
          isActive: true,
          role: 'USER',
        };

        mockApiClient.post.mockResolvedValue(expectedUser);

        const result = await adminService.createUser(userData);

        expect(mockApiClient.post).toHaveBeenCalledWith(
          '/admin/users',
          userData
        );
        expect(result).toEqual(expectedUser);
      });

      it('should handle creation errors gracefully', async () => {
        const userData = {
          email: '<EMAIL>',
          role: 'USER',
        };

        mockApiClient.post.mockRejectedValue(new Error('Creation failed'));

        await expect(adminService.createUser(userData)).rejects.toThrow();
      });
    });

    describe('updateUser', () => {
      it('should update a user successfully', async () => {
        const userId = '123';
        const updateData = {
          email: '<EMAIL>',
          isActive: false,
        };

        const expectedUser = {
          email: '<EMAIL>',
          id: userId,
          isActive: false,
        };

        mockApiClient.put.mockResolvedValue(expectedUser);

        const result = await adminService.updateUser(userId, updateData);

        expect(mockApiClient.put).toHaveBeenCalledWith(
          `/admin/users/${userId}`,
          updateData
        );
        expect(result).toEqual(expectedUser);
      });
    });

    describe('deleteUser', () => {
      it('should delete a user successfully', async () => {
        const userId = '123';

        // mockApiClient.delete.mockResolvedValue();

        await adminService.deleteUser(userId);

        expect(mockApiClient.delete).toHaveBeenCalledWith(
          `/admin/users/${userId}`
        );
      });
    });

    describe('toggleUserActivation', () => {
      it('should toggle user activation successfully', async () => {
        const userId = '123';
        const isActive = false;

        const expectedUser = {
          id: userId,
          isActive,
        };

        mockApiClient.patch.mockResolvedValue(expectedUser);

        const result = await adminService.toggleUserActivation(
          userId,
          isActive
        );

        expect(mockApiClient.patch).toHaveBeenCalledWith(
          `/admin/users/${userId}/activate`,
          { isActive }
        );
        expect(result).toEqual(expectedUser);
      });
    });
  });

  describe('Health Monitoring', () => {
    describe('getSystemHealthStatus', () => {
      it('should get health status successfully', async () => {
        const expectedHealth = {
          services: {
            api: { responseTime: 23, status: 'healthy' },
            database: { responseTime: 45, status: 'healthy' },
          },
          status: 'healthy',
          timestamp: new Date().toISOString(),
          uptime: 3600,
        };

        mockApiClient.get.mockResolvedValue(expectedHealth);

        const result = await adminService.getSystemHealthStatus();

        expect(mockApiClient.get).toHaveBeenCalledWith('/admin/health');
        expect(result).toEqual(expectedHealth);
      });
    });

    describe('getPerformanceMetrics', () => {
      it('should get performance metrics successfully', async () => {
        const expectedMetrics = {
          cpu: { cores: 4, usage: 45.2 },
          errors: { rate: 0.5, total: 5 },
          memory: { percentage: 25, total: 8192, used: 2048 },
          requests: { averageResponseTime: 150, perSecond: 10, total: 1000 },
          timestamp: new Date().toISOString(),
        };

        mockApiClient.get.mockResolvedValue(expectedMetrics);

        const result = await adminService.getPerformanceMetrics();

        expect(mockApiClient.get).toHaveBeenCalledWith('/admin/performance');
        expect(result).toEqual(expectedMetrics);
      });
    });

    describe('getRecentErrors', () => {
      it('should get recent errors successfully', async () => {
        const expectedErrors = {
          data: [
            {
              id: '1',
              level: 'ERROR',
              message: 'Database connection failed',
              timestamp: new Date().toISOString(),
            },
          ],
          pagination: { limit: 10, page: 1, total: 1 },
        };

        mockApiClient.get.mockResolvedValue(expectedErrors);

        const result = await adminService.getRecentErrors(1, 10, 'ERROR');

        expect(mockApiClient.get).toHaveBeenCalledWith(
          '/admin/logs/errors?page=1&limit=10&level=ERROR'
        );
        expect(result).toEqual(expectedErrors);
      });
    });
  });

  describe('Audit Logging', () => {
    describe('createAuditLog', () => {
      it('should create audit log successfully', async () => {
        const logData = {
          action: 'LOGIN',
          details: 'User logged in',
          ipAddress: '***********',
          userId: '123',
        };

        const expectedLog = {
          id: 'log-123',
          ...logData,
          timestamp: new Date(),
        };

        mockApiClient.post.mockResolvedValue(expectedLog);

        const result = await adminService.createAuditLog(logData);

        expect(mockApiClient.post).toHaveBeenCalledWith(
          '/admin/audit-logs',
          logData
        );
        expect(result).toEqual(expectedLog);
      });
    });
  });

  describe('Cache Management', () => {
    describe('cacheUtils', () => {
      it('should provide cache utilities', () => {
        const cacheUtils = adminService.cacheUtils;

        expect(cacheUtils).toBeDefined();
        expect(typeof cacheUtils.forceRefreshHealth).toBe('function');
        expect(typeof cacheUtils.forceRefreshPerformance).toBe('function');
        expect(typeof cacheUtils.getStats).toBe('function');
        expect(typeof cacheUtils.invalidateAll).toBe('function');
        expect(typeof cacheUtils.clearAll).toBe('function');
      });
    });
  });

  describe('Service Health', () => {
    it('should provide health status for the service', () => {
      const healthStatus = adminService.getHealthStatus();

      expect(healthStatus).toBeDefined();
      expect(healthStatus.service).toBe('AdminService');
      expect(healthStatus.endpoint).toBe('/admin');
      expect(healthStatus.circuitBreakerState).toBeDefined();
      expect(healthStatus.metrics).toBeDefined();
      expect(healthStatus.cacheStats).toBeDefined();
    });
  });

  describe('Mock Data Methods', () => {
    describe('getMockHealthStatus', () => {
      it('should return mock health status', () => {
        const mockHealth = adminService.getMockHealthStatus();

        expect(mockHealth).toBeDefined();
        expect(mockHealth.status).toBe('healthy');
        expect(mockHealth.services).toBeDefined();
        expect(mockHealth.uptime).toBeDefined();
      });
    });

    describe('getMockPerformanceMetrics', () => {
      it('should return mock performance metrics', () => {
        const mockMetrics = adminService.getMockPerformanceMetrics();

        expect(mockMetrics).toBeDefined();
        expect(mockMetrics.cpu).toBeDefined();
        expect(mockMetrics.memory).toBeDefined();
        expect(mockMetrics.requests).toBeDefined();
        expect(mockMetrics.errors).toBeDefined();
      });
    });

    describe('getMockRecentErrors', () => {
      it('should return mock recent errors', () => {
        const mockErrors = adminService.getMockRecentErrors();

        expect(mockErrors).toBeDefined();
        expect(mockErrors.data).toBeInstanceOf(Array);
        expect(mockErrors.pagination).toBeDefined();
      });
    });
  });
});
