'use client';

import { User } from 'lucide-react';
import React from 'react';

import type { Employee } from '@/lib/types/domain';

import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useEmployees } from '@/lib/stores/queries/useEmployees';
import { cn } from '@/lib/utils';

interface EmployeeSelectorProps {
  allowClear?: boolean;
  className?: string;
  disabled?: boolean;
  error?: string;
  label?: string;
  onValueChange: (value: null | number) => void;
  placeholder?: string;
  required?: boolean;
  value?: null | number | string;
}

// Format employee display name
const formatEmployeeName = (employee: Employee): string => {
  const name = employee.fullName ?? employee.name ?? 'Unknown';
  const id = employee.employeeId ? ` (ID: ${employee.employeeId})` : '';
  const position = employee.position ? ` - ${employee.position}` : '';
  return `${name}${id}${position}`;
};

/**
 * Enhanced Employee Selector Component
 *
 * Professional employee selection dropdown with enhanced UX.
 * Integrates with the existing employee API and follows the
 * established design patterns.
 *
 * Features:
 * - Professional visual design with employee information
 * - Loading and error states
 * - Clear selection option
 * - Responsive design
 */
export function EmployeeSelector({
  allowClear = true,
  className,
  disabled = false,
  error,
  label,
  onValueChange,
  placeholder = 'Select employee...',
  required = false,
  value,
}: EmployeeSelectorProps) {
  const { data: employees = [], error: fetchError, isLoading } = useEmployees();

  // Find selected employee
  const selectedEmployee = employees.find(emp => emp.id === value);

  const handleValueChange = (employeeId: string) => {
    if (employeeId === 'clear') {
      onValueChange(null);
      return;
    }
    const numericId = Number.parseInt(employeeId, 10);
    onValueChange(Number.isNaN(numericId) ? null : numericId);
  };

  if (isLoading) {
    return (
      <div className={cn('space-y-2', className)}>
        {label && (
          <Label className="text-sm font-medium">
            {label}
            {required && <span className="ml-1 text-destructive">*</span>}
          </Label>
        )}
        <Select disabled>
          <SelectTrigger>
            <SelectValue placeholder="Loading employees..." />
          </SelectTrigger>
        </Select>
      </div>
    );
  }

  if (fetchError) {
    return (
      <div className={cn('space-y-2', className)}>
        {label && (
          <Label className="text-sm font-medium">
            {label}
            {required && <span className="ml-1 text-destructive">*</span>}
          </Label>
        )}
        <Select disabled>
          <SelectTrigger>
            <SelectValue placeholder="Error loading employees" />
          </SelectTrigger>
        </Select>
        <p className="text-sm text-destructive">Failed to load employees</p>
      </div>
    );
  }

  return (
    <div className={cn('space-y-2', className)}>
      {label && (
        <Label className="text-sm font-medium">
          {label}
          {required && <span className="ml-1 text-destructive">*</span>}
        </Label>
      )}

      <Select
        disabled={disabled}
        onValueChange={handleValueChange}
        value={value?.toString() ?? ''}
      >
        <SelectTrigger
          className={cn(error && 'border-destructive focus:border-destructive')}
        >
          <SelectValue placeholder={placeholder}>
            {selectedEmployee && (
              <div className="flex items-center space-x-2">
                <User className="size-4 text-muted-foreground" />
                <span className="truncate">
                  {formatEmployeeName(selectedEmployee)}
                </span>
              </div>
            )}
          </SelectValue>
        </SelectTrigger>

        <SelectContent>
          {allowClear && value && (
            <SelectItem className="text-muted-foreground" value="clear">
              <div className="flex items-center space-x-2">
                <span className="text-xs">×</span>
                <span>Clear selection</span>
              </div>
            </SelectItem>
          )}

          {employees.map(employee => (
            <SelectItem key={employee.id} value={employee.id.toString()}>
              <div className="flex w-full items-center space-x-2">
                <User className="size-4 shrink-0 text-muted-foreground" />
                <div className="min-w-0 flex-1">
                  <div className="truncate font-medium">
                    {employee.fullName ?? employee.name ?? 'Unknown'}
                  </div>
                  <div className="truncate text-xs text-muted-foreground">
                    {employee.employeeId && `ID: ${employee.employeeId}`}
                    {employee.position && ` • ${employee.position}`}
                    {employee.department && ` • ${employee.department}`}
                  </div>
                </div>
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      {error && <p className="text-sm text-destructive">{error}</p>}
    </div>
  );
}
