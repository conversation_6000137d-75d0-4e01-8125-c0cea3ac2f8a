/**
 * @file Active alerts widget component.
 * This component displays current active alerts with real-time updates.
 * @module components/reliability/widgets/alerts/ActiveAlerts
 */

'use client';

import { AlertTriangle, CheckCircle, Clock, XCircle } from 'lucide-react';
import React from 'react';

import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useAlerts } from '@/lib/stores/queries/useReliability';
import type { AlertSeverity } from '@/lib/types/domain';
import { cn } from '@/lib/utils';

/**
 * Props for the ActiveAlerts component
 */
export interface ActiveAlertsProps {
  /** Optional CSS class name for styling */
  className?: string;
  /** Maximum number of alerts to display */
  maxAlerts?: number;
}

/**
 * Active alerts widget component.
 *
 * This component provides:
 * - Real-time active alert monitoring
 * - Severity-based color coding
 * - Alert count and status indicators
 * - Quick alert acknowledgment actions
 *
 * Features:
 * - Real-time WebSocket updates
 * - Severity-based styling
 * - Responsive design
 * - Accessibility support
 *
 * @param props - Component props
 * @returns JSX element representing the active alerts widget
 */
export const ActiveAlerts: React.FC<ActiveAlertsProps> = ({
  className = '',
  maxAlerts = 10,
}) => {
  const { data: alerts, isLoading, error } = useAlerts();

  /**
   * Get severity configuration
   */
  const getSeverityConfig = (severity: AlertSeverity) => {
    switch (severity) {
      case 'critical':
        return {
          icon: XCircle,
          color: 'text-red-600 dark:text-red-400',
          bgColor: 'bg-red-100 dark:bg-red-900/20',
          variant: 'destructive' as const,
        };
      case 'high':
        return {
          icon: AlertTriangle,
          color: 'text-orange-600 dark:text-orange-400',
          bgColor: 'bg-orange-100 dark:bg-orange-900/20',
          variant: 'destructive' as const,
        };
      case 'medium':
        return {
          icon: AlertTriangle,
          color: 'text-yellow-600 dark:text-yellow-400',
          bgColor: 'bg-yellow-100 dark:bg-yellow-900/20',
          variant: 'secondary' as const,
        };
      case 'low':
        return {
          icon: Clock,
          color: 'text-blue-600 dark:text-blue-400',
          bgColor: 'bg-blue-100 dark:bg-blue-900/20',
          variant: 'outline' as const,
        };
    }
  };

  /**
   * Format alert timestamp
   */
  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffMins < 1440) return `${Math.floor(diffMins / 60)}h ago`;
    return date.toLocaleDateString();
  };

  // Handle loading state
  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5" />
            Active Alerts
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="h-16 bg-muted rounded"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  // Handle error state
  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5" />
            Active Alerts
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-4">
            <AlertTriangle className="mx-auto h-8 w-8 text-muted-foreground mb-2" />
            <p className="text-sm text-muted-foreground">
              Failed to load alerts
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const displayAlerts = alerts?.slice(0, maxAlerts) || [];
  const criticalCount =
    alerts?.filter(alert => alert.severity === 'critical').length || 0;

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5" />
            Active Alerts
          </div>
          <div className="flex items-center gap-2">
            <Badge variant={criticalCount > 0 ? 'destructive' : 'default'}>
              {alerts?.length || 0} total
            </Badge>
            {criticalCount > 0 && (
              <Badge variant="destructive">{criticalCount} critical</Badge>
            )}
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {displayAlerts.length === 0 ? (
          <div className="text-center py-8">
            <CheckCircle className="mx-auto h-8 w-8 text-green-600 mb-2" />
            <p className="text-sm text-muted-foreground">No active alerts</p>
            <p className="text-xs text-muted-foreground mt-1">
              All systems operating normally
            </p>
          </div>
        ) : (
          <div className="space-y-3">
            {displayAlerts.map(alert => {
              const config = getSeverityConfig(alert.severity);
              const Icon = config.icon;

              return (
                <div
                  key={alert.id}
                  className={cn(
                    'flex items-start gap-3 p-3 rounded-lg border',
                    config.bgColor
                  )}
                >
                  <div className="flex-shrink-0 mt-0.5">
                    <Icon className={cn('h-4 w-4', config.color)} />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between gap-2">
                      <div>
                        <p className="font-medium text-sm">{alert.message}</p>
                        <p className="text-xs text-muted-foreground mt-1">
                          {alert.type} • {alert.source}
                        </p>
                      </div>
                      <Badge variant={config.variant} className="flex-shrink-0">
                        {alert.severity}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between mt-2">
                      <span className="text-xs text-muted-foreground">
                        {formatTimestamp(alert.timestamp)}
                      </span>
                      <span className="text-xs text-muted-foreground">
                        Status: {alert.status}
                      </span>
                    </div>
                  </div>
                </div>
              );
            })}

            {alerts && alerts.length > maxAlerts && (
              <div className="text-center pt-2">
                <p className="text-sm text-muted-foreground">
                  +{alerts.length - maxAlerts} more alerts
                </p>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

/**
 * Default export for the ActiveAlerts component
 */
export default ActiveAlerts;
