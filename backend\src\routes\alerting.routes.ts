import { Router, Request, Response } from 'express';
import {
  getActiveAlerts,
  getAlertHistory,
  getAlertStatistics,
  resolveAlert,
  acknowledgeAlert,
  testAlertSystem,
} from '../services/alerting.service.js';
import { enhancedAuthenticateUser } from '../middleware/jwtAuth.middleware.js';
import { requireRole } from '../middleware/supabaseAuth.js';
import logger from '../utils/logger.js';

const router = Router();

// GET /api/alerts
router.get('/', (req: Request, res: Response): void => {
  try {
    const activeAlerts = getActiveAlerts();
    res.json({
      data: {
        alerts: activeAlerts,
        count: activeAlerts.length,
      },
      status: 'success',
      timestamp: new Date().toISOString(),
    });
  } catch (error: unknown) {
    logger.error('Failed to retrieve active alerts', {
      error: (error as Error).message,
      endpoint: '/alerts',
    });
    res.status(500).json({
      error: (error as Error).message,
      message: 'Failed to retrieve active alerts',
      status: 'error',
      timestamp: new Date().toISOString(),
    });
  }
});

// GET /api/alerts/history
router.get('/history', (req: Request, res: Response): void => {
  try {
    const page = parseInt(req.query.page as string);
    const limit = parseInt(req.query.limit as string);
    const history = getAlertHistory(page || 1, limit || 50);
    res.json({
      data: history,
      status: 'success',
      timestamp: new Date().toISOString(),
    });
  } catch (error: unknown) {
    logger.error('Failed to retrieve alert history', {
      error: (error as Error).message,
      endpoint: '/alerts/history',
    });
    res.status(500).json({
      error: (error as Error).message,
      message: 'Failed to retrieve alert history',
      status: 'error',
      timestamp: new Date().toISOString(),
    });
  }
});

// GET /api/alerts/statistics
router.get('/statistics', (req: Request, res: Response): void => {
  try {
    const statistics = getAlertStatistics();
    res.json({
      data: statistics,
      status: 'success',
      timestamp: new Date().toISOString(),
    });
  } catch (error: unknown) {
    logger.error('Failed to retrieve alert statistics', {
      error: (error as Error).message,
      endpoint: '/alerts/statistics',
    });
    res.status(500).json({
      error: (error as Error).message,
      message: 'Failed to retrieve alert statistics',
      status: 'error',
      timestamp: new Date().toISOString(),
    });
  }
});

// POST /api/alerts/:alertId/resolve
router.post(
  '/:alertId/resolve',
  enhancedAuthenticateUser,
  requireRole(['ADMIN', 'MANAGER']),
  async (req: Request, res: Response): Promise<void> => {
    try {
      const { alertId } = req.params;
      const resolvedBy = req.user?.email ?? 'unknown_user_resolve';
      const resolvedAlert = await resolveAlert(alertId, resolvedBy);
      if (!resolvedAlert) {
        res.status(404).json({
          message: 'Alert not found',
          status: 'error',
          timestamp: new Date().toISOString(),
        });
        return;
      }
      res.json({ data: resolvedAlert, status: 'success', timestamp: new Date().toISOString() });
    } catch (error: unknown) {
      logger.error('Failed to resolve alert', {
        error: (error as Error).message,
        alertId: req.params.alertId,
        endpoint: '/alerts/:alertId/resolve',
      });
      res.status(500).json({
        error: (error as Error).message,
        message: 'Failed to resolve alert',
        status: 'error',
        timestamp: new Date().toISOString(),
      });
    }
  },
);

// POST /api/alerts/:alertId/acknowledge
router.post(
  '/:alertId/acknowledge',
  enhancedAuthenticateUser,
  requireRole(['ADMIN', 'MANAGER']),
  async (req: Request, res: Response): Promise<void> => {
    try {
      const { alertId } = req.params;
      const acknowledgedBy = req.user?.email ?? 'unknown_user_acknowledge';
      const acknowledgedAlert = await acknowledgeAlert(alertId, acknowledgedBy);
      if (!acknowledgedAlert) {
        res.status(404).json({
          message: 'Alert not found or already acknowledged',
          status: 'error',
          timestamp: new Date().toISOString(),
        });
        return;
      }
      res.json({ data: acknowledgedAlert, status: 'success', timestamp: new Date().toISOString() });
    } catch (error: unknown) {
      logger.error('Failed to acknowledge alert', {
        error: (error as Error).message,
        alertId: req.params.alertId,
        endpoint: '/alerts/:alertId/acknowledge',
      });
      res.status(500).json({
        error: (error as Error).message,
        message: 'Failed to acknowledge alert',
        status: 'error',
        timestamp: new Date().toISOString(),
      });
    }
  },
);

// POST /api/alerts/test
router.post(
  '/test',
  enhancedAuthenticateUser,
  requireRole(['ADMIN', 'MANAGER']),
  async (req: Request, res: Response): Promise<void> => {
    try {
      await testAlertSystem();
      res.status(200).json({
        message: 'Test alert sequence initiated.',
        status: 'success',
        timestamp: new Date().toISOString(),
      });
    } catch (error: unknown) {
      logger.error('Failed to trigger test alert system', {
        error: (error as Error).message,
        endpoint: '/alerts/test',
      });
      res.status(500).json({
        error: (error as Error).message,
        message: 'Failed to trigger test alert system',
        status: 'error',
        timestamp: new Date().toISOString(),
      });
    }
  },
);

export default router;
