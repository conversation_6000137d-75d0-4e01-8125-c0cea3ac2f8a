/**
 * @file Generic dashboard store hook
 * @module hooks/useDashboardStore
 */

import { useMemo } from 'react';
import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';

import type {
  DashboardConfig,
  DashboardStore,
  ViewMode,
} from '@/components/dashboard/types';

/**
 * Create a dashboard store for a specific entity type
 */
export function createDashboardStore(entityType: string) {
  return create<DashboardStore>()(
    devtools(
      persist(
        (set, get) => ({
          // Initial state
          activeTab: 'all',
          layout: {
            viewMode: 'cards' as ViewMode,
            gridColumns: 3,
            compactMode: false,
            showFilters: true,
            showSettings: false,
          },
          monitoring: {
            enabled: false,
            refreshInterval: 30000,
            autoRefresh: true,
            pausedDataTypes: new Set<string>(),
          },
          filters: {},
          sortBy: 'createdAt',
          sortDirection: 'desc',
          selectedItems: new Set<string>(),
          searchTerm: '',

          // Actions
          setActiveTab: (tab: string) =>
            set({ activeTab: tab }, false, 'setActiveTab'),

          setViewMode: (mode: ViewMode) =>
            set(
              state => ({
                layout: { ...state.layout, viewMode: mode },
              }),
              false,
              'setViewMode'
            ),

          setGridColumns: (columns: number) =>
            set(
              state => ({
                layout: { ...state.layout, gridColumns: columns },
              }),
              false,
              'setGridColumns'
            ),

          toggleCompactMode: () =>
            set(
              state => ({
                layout: {
                  ...state.layout,
                  compactMode: !state.layout.compactMode,
                },
              }),
              false,
              'toggleCompactMode'
            ),

          toggleFilters: () =>
            set(
              state => ({
                layout: {
                  ...state.layout,
                  showFilters: !state.layout.showFilters,
                },
              }),
              false,
              'toggleFilters'
            ),

          toggleSettings: () =>
            set(
              state => ({
                layout: {
                  ...state.layout,
                  showSettings: !state.layout.showSettings,
                },
              }),
              false,
              'toggleSettings'
            ),

          updateFilter: (filterId: string, value: any) =>
            set(
              state => ({
                filters: { ...state.filters, [filterId]: value },
              }),
              false,
              'updateFilter'
            ),

          clearFilters: () => set({ filters: {} }, false, 'clearFilters'),

          setSorting: (field: string, direction: 'asc' | 'desc') =>
            set(
              { sortBy: field, sortDirection: direction },
              false,
              'setSorting'
            ),

          setSearchTerm: (term: string) =>
            set({ searchTerm: term }, false, 'setSearchTerm'),

          toggleItemSelection: (id: string) =>
            set(
              state => {
                const newSelection = new Set(state.selectedItems);
                if (newSelection.has(id)) {
                  newSelection.delete(id);
                } else {
                  newSelection.add(id);
                }
                return { selectedItems: newSelection };
              },
              false,
              'toggleItemSelection'
            ),

          clearSelection: () =>
            set({ selectedItems: new Set() }, false, 'clearSelection'),

          selectAll: (ids: string[]) =>
            set({ selectedItems: new Set(ids) }, false, 'selectAll'),

          // Monitoring actions
          setMonitoringEnabled: (enabled: boolean) =>
            set(
              state => ({
                monitoring: { ...state.monitoring, enabled },
              }),
              false,
              'setMonitoringEnabled'
            ),

          setRefreshInterval: (interval: number) =>
            set(
              state => ({
                monitoring: { ...state.monitoring, refreshInterval: interval },
              }),
              false,
              'setRefreshInterval'
            ),

          toggleAutoRefresh: () =>
            set(
              state => ({
                monitoring: {
                  ...state.monitoring,
                  autoRefresh: !state.monitoring.autoRefresh,
                },
              }),
              false,
              'toggleAutoRefresh'
            ),

          pauseDataType: (dataType: string) =>
            set(
              state => ({
                monitoring: {
                  ...state.monitoring,
                  pausedDataTypes: new Set([
                    ...state.monitoring.pausedDataTypes,
                    dataType,
                  ]),
                },
              }),
              false,
              'pauseDataType'
            ),

          resumeDataType: (dataType: string) =>
            set(
              state => {
                const newPausedTypes = new Set(
                  state.monitoring.pausedDataTypes
                );
                newPausedTypes.delete(dataType);
                return {
                  monitoring: {
                    ...state.monitoring,
                    pausedDataTypes: newPausedTypes,
                  },
                };
              },
              false,
              'resumeDataType'
            ),

          resetSettings: () =>
            set(
              {
                layout: {
                  viewMode: 'cards' as ViewMode,
                  gridColumns: 3,
                  compactMode: false,
                  showFilters: true,
                  showSettings: false,
                },
                monitoring: {
                  enabled: false,
                  refreshInterval: 30000,
                  autoRefresh: true,
                  pausedDataTypes: new Set<string>(),
                },
                filters: {},
                sortBy: 'createdAt',
                sortDirection: 'desc',
                selectedItems: new Set<string>(),
                searchTerm: '',
              },
              false,
              'resetSettings'
            ),

          // Computed selectors
          getFilteredData: <T extends { id: string }>(
            data: T[],
            config: DashboardConfig<T>
          ): T[] => {
            const state = get();
            let filtered = [...data];

            // Apply search filter
            if (state.searchTerm) {
              const searchLower = state.searchTerm.toLowerCase();
              filtered = filtered.filter((item: any) =>
                Object.values(item).some(value =>
                  String(value).toLowerCase().includes(searchLower)
                )
              );
            }

            // Apply other filters
            Object.entries(state.filters).forEach(([filterId, value]) => {
              if (value !== undefined && value !== null && value !== '') {
                filtered = filtered.filter((item: any) => {
                  const filterConfig = config.filters?.find(
                    f => f.id === filterId
                  );
                  if (!filterConfig) return true;

                  switch (filterConfig.type) {
                    case 'select':
                      return item[filterId] === value;
                    case 'multiselect':
                      return Array.isArray(value)
                        ? value.includes(item[filterId])
                        : true;
                    case 'toggle':
                      return value ? item[filterId] : true;
                    default:
                      return true;
                  }
                });
              }
            });

            // Apply sorting
            filtered.sort((a: any, b: any) => {
              const aValue = a[state.sortBy];
              const bValue = b[state.sortBy];
              const direction = state.sortDirection === 'asc' ? 1 : -1;

              if (aValue < bValue) return -1 * direction;
              if (aValue > bValue) return 1 * direction;
              return 0;
            });

            return filtered;
          },

          getSelectedCount: () => get().selectedItems.size,

          hasActiveFilters: () => {
            const state = get();
            return (
              state.searchTerm.length > 0 ||
              Object.values(state.filters).some(
                value => value !== undefined && value !== null && value !== ''
              )
            );
          },
        }),
        {
          name: `workhub-dashboard-${entityType}`,
          partialize: state => ({
            layout: state.layout,
            monitoring: state.monitoring,
            filters: state.filters,
            sortBy: state.sortBy,
            sortDirection: state.sortDirection,
          }),
        }
      ),
      {
        name: `dashboard-${entityType}`,
      }
    )
  );
}

/**
 * Store registry to ensure single instance per entity type
 */
const storeRegistry = new Map<
  string,
  ReturnType<typeof createDashboardStore>
>();

/**
 * Hook to use dashboard store for a specific entity type
 */
export function useDashboardStore(entityType: string) {
  return useMemo(() => {
    if (!storeRegistry.has(entityType)) {
      storeRegistry.set(entityType, createDashboardStore(entityType));
    }
    return storeRegistry.get(entityType)!;
  }, [entityType]);
}

export default useDashboardStore;
