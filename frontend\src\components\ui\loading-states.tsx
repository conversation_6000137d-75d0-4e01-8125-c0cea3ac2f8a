'use client';

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Loader2 } from 'lucide-react';
import React from 'react';

import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { cn } from '@/lib/utils';

/**
 * Props for LoadingCard component
 */
interface LoadingCardProps {
  /** Custom CSS class names */
  className?: string;
  /** Number of skeleton items to display */
  count?: number;
  /** Height of each skeleton item */
  height?: number;
}

/**
 * Props for LoadingContainer component
 */
interface LoadingContainerProps {
  /** Content to display when loaded successfully */
  children: React.ReactNode;
  /** Custom CSS class names */
  className?: string;
  /** Error message, if any */
  error?: null | string;
  /** Custom error component */
  errorComponent?: React.ReactNode;
  /** Whether the data is currently loading */
  isLoading: boolean;
  /** Custom loading component */
  loadingComponent?: React.ReactNode;
  /** Function to retry the operation */
  onRetry?: () => void;
}

/**
 * Props for LoadingError component
 */
interface LoadingErrorProps {
  /** Custom CSS class names */
  className?: string;
  /** Error message to display */
  message: string;
  /** Function to retry the operation */
  onRetry?: () => void;
}

/**
 * Props for LoadingSpinner component
 */
interface LoadingSpinnerProps {
  /** Custom CSS class names */
  className?: string;
  /** Size of the spinner in pixels */
  size?: number;
  /** Text to display next to the spinner */
  text?: string;
}

/**
 * A card loading state with skeleton UI
 */
export function LoadingCard({
  className,
  count = 3,
  height = 20,
}: LoadingCardProps) {
  return (
    <div className={cn('space-y-2', className)}>
      <Skeleton className="mb-4 h-8 w-[250px]" />
      {Array.from({ length: count }).map((_, i) => (
        <Skeleton
          className="w-full"
          key={i}
          style={{ height: `${height}px` }}
        />
      ))}
    </div>
  );
}

/**
 * A container component that handles loading, error, and success states
 */
export function LoadingContainer({
  children,
  className,
  error,
  errorComponent,
  isLoading,
  loadingComponent,
  onRetry,
}: LoadingContainerProps) {
  if (isLoading) {
    return (
      loadingComponent || (
        <LoadingSpinner {...(className && { className })} text="Loading..." />
      )
    );
  }

  if (error) {
    return (
      errorComponent || (
        <LoadingError
          {...(className && { className })}
          message={error}
          {...(onRetry && { onRetry })}
        />
      )
    );
  }

  return <div className={className}>{children}</div>;
}

/**
 * An error state component for failed loading operations
 */
export function LoadingError({
  className,
  message,
  onRetry,
}: LoadingErrorProps) {
  return (
    <Alert className={cn('my-4', className)} variant="destructive">
      <AlertCircle className="size-4" />
      <AlertTitle>Error</AlertTitle>
      <AlertDescription>
        <div className="mt-2">
          <p className="mb-4 text-sm text-muted-foreground">{message}</p>
          {onRetry && (
            <Button
              className="flex items-center"
              onClick={onRetry}
              size="sm"
              variant="outline"
            >
              <Loader2 className="mr-2 size-4" />
              Retry
            </Button>
          )}
        </div>
      </AlertDescription>
    </Alert>
  );
}

/**
 * A simple loading spinner component
 */
export function LoadingSpinner({
  className,
  size = 24,
  text,
}: LoadingSpinnerProps) {
  return (
    <div className={cn('flex items-center justify-center', className)}>
      <Loader2
        className="animate-spin text-muted-foreground"
        style={{ height: size, width: size }}
      />
      {text && (
        <span className="ml-2 text-sm text-muted-foreground">{text}</span>
      )}
    </div>
  );
}
