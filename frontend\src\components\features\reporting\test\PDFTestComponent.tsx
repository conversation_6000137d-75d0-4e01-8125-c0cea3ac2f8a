/**
 * Test component for PDF generation debugging
 */

import React from 'react';
import { Button } from '@/components/ui/button';
import { useExport } from '../exports/hooks/useExport';
import { toastService } from '@/lib/services/toastService';

export const PDFTestComponent: React.FC = () => {
  const { exportReportToPDF } = useExport('test-report');

  const testPDFGeneration = async () => {
    try {
      // Create minimal test data
      const testData = {
        data: {
          totalCount: 5,
          summary: {
            message: 'Test delegation report',
            totalDelegations: 5,
            completedDelegations: 3,
            pendingDelegations: 2,
          },
          records: [
            { id: 1, name: 'Test Delegation 1', status: 'Completed' },
            { id: 2, name: 'Test Delegation 2', status: 'Pending' },
          ],
          statusDistribution: [
            { status: 'Completed', count: 3 },
            { status: 'Pending', count: 2 },
          ],
          priorityDistribution: [
            { priority: 'High', count: 2 },
            { priority: 'Medium', count: 3 },
          ],
        },
        metadata: {
          id: 'test_delegations_' + Date.now(),
          type: 'test',
          entityType: 'delegations',
          format: 'pdf',
          generatedAt: new Date().toISOString(),
          generatedBy: 'test-user',
        },
      };

      console.log('Testing PDF generation with data:', testData);

      toastService.show({
        title: 'Testing PDF Generation',
        description: 'Generating test PDF...',
        duration: 2000,
      });

      await exportReportToPDF(
        testData,
        'delegations',
        'Test Delegations Report',
        'test-delegations-report'
      );

      toastService.success(
        'PDF Test Successful',
        'Test PDF has been generated and downloaded successfully!'
      );
    } catch (error: any) {
      console.error('PDF test failed:', error);
      toastService.error(
        'PDF Test Failed',
        `PDF generation test failed: ${error.message || 'Unknown error'}`
      );
    }
  };

  return (
    <div className="p-4 border rounded-lg bg-yellow-50">
      <h3 className="text-lg font-semibold mb-2">PDF Generation Test</h3>
      <p className="text-sm text-gray-600 mb-4">
        This component tests PDF generation with minimal data to debug issues.
      </p>
      <Button onClick={testPDFGeneration} variant="outline">
        Test PDF Generation
      </Button>
    </div>
  );
};
