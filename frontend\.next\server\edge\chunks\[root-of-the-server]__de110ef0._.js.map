{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/middleware.ts"], "sourcesContent": ["import createMiddleware from 'next-intl/middleware';\n\nimport { routing } from './src/i18n/routing';\n\nexport default createMiddleware(routing);\n\nexport const config = {\n  // Match all pathnames except for\n  // - API routes\n  // - _next (Next.js internals)\n  // - _vercel (Vercel internals)\n  // - Files with extensions (e.g. favicon.ico)\n  matcher: [String.raw`/((?!api|_next|_vercel|.*\\..*).*)`],\n};\n"], "names": [], "mappings": ";;;;AAAA;;;;;;;;uCAIe,CAAA,GAAA,8LAAA,CAAA,UAAgB,AAAD,EAAE;AAEzB,MAAM,SAAS;IACpB,iCAAiC;IACjC,eAAe;IACf,8BAA8B;IAC9B,+BAA+B;IAC/B,6CAA6C;IAC7C,SAAS;QAAC,OAAO,GAAG,CAAC,iCAAiC,CAAC;KAAC;AAC1D"}}]}