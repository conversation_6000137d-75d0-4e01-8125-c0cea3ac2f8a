/**
 * @file Debug Configuration
 * @description Centralized configuration for debug components and performance tools
 * @module lib/config/debugConfig
 */

/**
 * Debug configuration interface
 */
export interface DebugConfig {
  /** Show token refresh debug component */
  showTokenRefreshDebug: boolean;
  /** Show CSP debug component */
  showCSPDebug: boolean;
  /** Show performance monitoring tools */
  showPerformanceTools: boolean;
  /** Show development tools */
  showDevTools: boolean;
  /** Enable debug logging */
  enableDebugLogging: boolean;
}

/**
 * Get debug configuration based on environment variables
 */
export function getDebugConfig(): DebugConfig {
  // Check if we're in development mode
  const isDevelopment = process.env.NODE_ENV === 'development';

  // Check for specific debug flags
  const showTokenRefreshDebug =
    process.env.NEXT_PUBLIC_SHOW_TOKEN_DEBUG === 'true';
  const showCSPDebug = process.env.NEXT_PUBLIC_SHOW_CSP_DEBUG === 'true';
  const showPerformanceTools =
    process.env.NEXT_PUBLIC_SHOW_PERFORMANCE_TOOLS === 'true';
  const showDevTools = process.env.NEXT_PUBLIC_SHOW_DEV_TOOLS === 'true';
  const enableDebugLogging =
    process.env.NEXT_PUBLIC_ENABLE_DEBUG_LOGGING === 'true';

  return {
    // Default to false for cleaner UI, can be enabled via env vars
    showTokenRefreshDebug: isDevelopment && showTokenRefreshDebug,
    showCSPDebug: isDevelopment && showCSPDebug,
    showPerformanceTools: isDevelopment && showPerformanceTools,
    showDevTools: isDevelopment && showDevTools,
    enableDebugLogging: isDevelopment && enableDebugLogging,
  };
}

/**
 * Debug configuration singleton
 */
export const debugConfig = getDebugConfig();

/**
 * Utility function to check if any debug components should be shown
 */
export function shouldShowDebugComponents(): boolean {
  const config = getDebugConfig();
  return (
    config.showTokenRefreshDebug ||
    config.showCSPDebug ||
    config.showPerformanceTools
  );
}

/**
 * Utility function to check if performance tools should be shown
 */
export function shouldShowPerformanceTools(): boolean {
  return getDebugConfig().showPerformanceTools;
}

/**
 * Utility function to check if performance widgets should be shown in reliability dashboard
 */
export function shouldShowPerformanceWidgets(): boolean {
  const isDevelopment = process.env.NODE_ENV === 'development';
  const showPerformanceWidgets =
    process.env.NEXT_PUBLIC_SHOW_PERFORMANCE_WIDGETS === 'true';

  // Default to false for cleaner UI, can be enabled via env var
  return isDevelopment && showPerformanceWidgets;
}

/**
 * Utility function to check if development tools should be shown
 */
export function shouldShowDevTools(): boolean {
  return getDebugConfig().showDevTools;
}
