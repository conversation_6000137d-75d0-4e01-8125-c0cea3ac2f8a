import type { ApiClient } from '@/lib/api/core/apiClient';
import type { PaginatedResponse, User, UserFilters } from '@/types';

import {
  BaseApiService,
  type DataTransformer,
  type ServiceConfig,
} from '@/lib/api/core/baseApiService';

// Define UserTransformer for consistent data transformation
const UserTransformer: DataTransformer<User> = {
  fromApi: (data: any): User => {
    // Handle the API response structure:
    // Top level: { id, role, is_active, created_at, updated_at, employee_id, users: [...] }
    // Nested users array: [{ email, email_confirmed_at }]
    const nestedUserData = data.users?.[0] || {};

    return {
      created_at: data.created_at || '',
      email: nestedUserData.email || data.email || '',
      email_confirmed_at:
        nestedUserData.email_confirmed_at || data.email_confirmed_at || null,
      employee_id: data.employee_id || null,
      full_name: data.full_name || data.name || '',
      id: data.id,
      isActive: data.is_active ?? true,
      last_sign_in_at: data.last_sign_in_at || null,
      phone: data.phone || null,
      phone_confirmed_at: data.phone_confirmed_at || null,
      role: data.role || 'USER',
      updated_at: data.updated_at || '',
      users: data.users,
    };
  },
  toApi: (data: any) => data,
};

/**
 * Enhanced User Service using BaseApiService infrastructure
 * Provides user management operations with production-grade reliability
 */
export class UserService extends BaseApiService<
  User,
  Partial<User>,
  Partial<User>
> {
  protected endpoint = '/admin/users';
  protected transformer = UserTransformer;

  constructor(apiClient: ApiClient, config?: ServiceConfig) {
    super(apiClient, {
      cacheDuration: 5 * 60 * 1000, // 5 minutes for user data
      circuitBreakerThreshold: 5,
      enableMetrics: true,
      retryAttempts: 3,
      ...config,
    });
  }

  /**
   * Override getAll to correctly handle the nested 'data' property from the API response.
   */
  async getAll(filters: UserFilters = {}): Promise<PaginatedResponse<User>> {
    const params = new URLSearchParams(
      filters as Record<string, string>
    ).toString();
    const url = `${this.endpoint}?${params}`;
    const cacheKey = `${this.endpoint}:getAll:${JSON.stringify(filters)}`;

    return this.executeWithInfrastructure(cacheKey, async () => {
      // The API returns { data: [...], pagination: {...} }
      const response = await this.apiClient.get<any>(url);

      // Extract the actual data array and pagination object
      const rawUsers = Array.isArray(response.data)
        ? response.data
        : response.data?.data || [];
      const pagination = response.pagination ||
        response.data?.pagination || {
          hasNext: false,
          hasPrevious: false,
          limit: filters.limit || 10,
          page: 1,
          total: 0,
          totalPages: 0,
        };

      // Apply the transformer to each user object
      const transformedUsers = rawUsers.map((user: any) =>
        this.transformer.fromApi ? this.transformer.fromApi(user) : user
      );

      return {
        data: transformedUsers,
        pagination: pagination,
      };
    });
  }

  /**
   * Get users by role with filtering
   */
  async getUsersByRole(
    role: string,
    filters: UserFilters = {}
  ): Promise<User[]> {
    const result = await this.getAll({ ...filters, role });
    return result.data;
  }

  /**
   * Toggle user activation status
   */
  async toggleActivation(id: string, isActive: boolean): Promise<User> {
    return this.executeWithInfrastructure(null, async () => {
      const response = await this.apiClient.patch<User>(
        `${this.endpoint}/${id}/toggle-activation`,
        { isActive }
      );

      // Invalidate related caches
      this.cache.invalidatePattern(new RegExp(`^${this.endpoint}:`));
      this.cache.invalidate(`${this.endpoint}:getById:${id}`);

      return response;
    });
  }
}
