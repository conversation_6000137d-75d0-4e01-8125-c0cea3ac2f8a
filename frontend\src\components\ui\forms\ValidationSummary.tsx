'use client';

import { <PERSON>ert<PERSON>ircle, AlertTriangle, CheckCircle, X } from 'lucide-react';
import React from 'react';
import { useFormContext } from 'react-hook-form';

import type { ValidationState } from '@/hooks/forms/useDelegationValidation';
import type { DelegationFormData } from '@/lib/schemas/delegationSchemas';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useDelegationValidation } from '@/hooks/forms/useDelegationValidation';
import { cn } from '@/lib/utils';

// ============================================================================
// TYPES
// ============================================================================

export interface ValidationSummaryProps {
  /** Additional CSS classes */
  className?: string;
  /** Callback when summary is dismissed */
  onDismiss?: () => void;
  /** Whether to show the validation summary */
  show: boolean;
  /** Whether to show field navigation buttons */
  showFieldNavigation?: boolean;
  /** Whether to show success state when form is valid */
  showSuccess?: boolean;
  /** Whether to show warnings in addition to errors */
  showWarnings?: boolean;
}

// ============================================================================
// UTILS
// ============================================================================

/**
 * Type guard to check if an object is a ValidationState
 */
function isValidationState(obj: unknown): obj is ValidationState {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    'isValid' in (obj as object) &&
    typeof (obj as ValidationState).isValid === 'boolean' &&
    'hasErrors' in (obj as object) &&
    typeof (obj as ValidationState).hasErrors === 'boolean' &&
    'hasWarnings' in (obj as object) &&
    typeof (obj as ValidationState).hasWarnings === 'boolean'
  );
}

/**
 * Get user-friendly field names
 */
const getFieldDisplayName = (fieldName: string): string => {
  const fieldNameMap: Record<string, string> = {
    delegates: 'Delegates',
    driverEmployeeIds: 'Drivers',
    durationFrom: 'Start Date',
    durationTo: 'End Date',
    escortEmployeeIds: 'Escorts',
    eventName: 'Event Name',
    'flightArrivalDetails.flightNumber': 'Arrival Flight Number',
    'flightDepartureDetails.flightNumber': 'Departure Flight Number',
    imageUrl: 'Image URL',
    invitationFrom: 'Invitation From',
    invitationTo: 'Invitation To',
    location: 'Location',
    notes: 'Notes',
    vehicleIds: 'Vehicles',
  };

  return (
    fieldNameMap[fieldName] ??
    fieldName
      .replaceAll(/([A-Z])/g, ' $1')
      .replace(/^./, str => str.toUpperCase())
  );
};

// ============================================================================
// COMPONENT
// ============================================================================

/**
 * Comprehensive validation summary component for delegation forms
 *
 * Features:
 * - Displays all form errors and warnings
 * - Cross-field validation feedback
 * - Field navigation for quick error fixing
 * - Success state indication
 * - Accessibility support with proper ARIA attributes
 * - Dismissible interface
 */
export const ValidationSummary: React.FC<ValidationSummaryProps> = ({
  className = '',
  onDismiss,
  show,
  showFieldNavigation = true,
  showSuccess = false,
  showWarnings = true,
}) => {
  const {
    formState: { errors },
    setFocus,
  } = useFormContext<DelegationFormData>();

  const { getCrossFieldErrors, getCrossFieldWarnings, validationState } =
    useDelegationValidation();

  // Don't render if not shown
  if (!show) return null;

  const crossFieldErrors = getCrossFieldErrors();
  const crossFieldWarnings = showWarnings ? getCrossFieldWarnings() : [];
  const formErrors = Object.entries(errors);

  const hasErrors =
    (isValidationState(validationState) && validationState.hasErrors) ||
    formErrors.length > 0;
  const hasWarnings =
    isValidationState(validationState) &&
    validationState.hasWarnings &&
    showWarnings;
  const isFormValid =
    isValidationState(validationState) &&
    validationState.isValid &&
    formErrors.length === 0;

  // Handle field navigation
  const handleFieldFocus = (fieldName: string) => {
    setFocus(fieldName as keyof DelegationFormData);
    if (onDismiss) {
      onDismiss();
    }
  };

  // Success state
  if (isFormValid && showSuccess) {
    return (
      <Card
        className={cn(
          'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20',
          className
        )}
      >
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-green-700 dark:text-green-400">
            <CheckCircle className="size-5" />
            Form Validation Successful
            {onDismiss && (
              <Button
                className="ml-auto size-6 p-0 text-green-700 hover:text-green-800 dark:text-green-400"
                onClick={onDismiss}
                size="sm"
                variant="ghost"
              >
                <X className="size-4" />
                <span className="sr-only">Dismiss</span>
              </Button>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-green-600 dark:text-green-300">
            All required fields are completed and valid. You can now submit the
            delegation form.
          </p>
        </CardContent>
      </Card>
    );
  }

  // Error/Warning state
  if (hasErrors || hasWarnings) {
    return (
      <Alert
        className={cn(
          hasErrors
            ? 'border-destructive/50 text-destructive dark:border-destructive'
            : 'border-amber-200 bg-amber-50 text-amber-800 dark:border-amber-800 dark:bg-amber-900/20 dark:text-amber-200',
          className
        )}
        variant={hasErrors ? 'destructive' : 'default'}
      >
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-2">
            {hasErrors ? (
              <AlertCircle className="size-4" />
            ) : (
              <AlertTriangle className="size-4" />
            )}
            <div className="font-medium">
              {hasErrors
                ? 'Please correct the following issues:'
                : 'Please review the following:'}
            </div>
          </div>
          {onDismiss && (
            <Button
              className="size-6 p-0 opacity-70 hover:opacity-100"
              onClick={onDismiss}
              size="sm"
              variant="ghost"
            >
              <X className="size-4" />
              <span className="sr-only">Dismiss</span>
            </Button>
          )}
        </div>

        <AlertDescription className="mt-3">
          {/* Form Field Errors */}
          {formErrors.length > 0 && (
            <div className="space-y-2">
              <div className="text-sm font-medium">Field Errors:</div>
              <ul className="list-inside list-disc space-y-1 text-sm">
                {formErrors.map(([fieldName, error]) => (
                  <li
                    className="flex items-center justify-between"
                    key={fieldName}
                  >
                    <span>
                      <strong>{getFieldDisplayName(fieldName)}:</strong>{' '}
                      {error.message ?? 'Invalid value'}
                    </span>
                    {showFieldNavigation && (
                      <Button
                        className="ml-2 h-6 px-2 text-xs"
                        onClick={() => handleFieldFocus(fieldName)}
                        size="sm"
                        variant="ghost"
                      >
                        Go to field
                      </Button>
                    )}
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Cross-Field Errors */}
          {crossFieldErrors.length > 0 && (
            <div className="mt-4 space-y-2">
              <div className="text-sm font-medium">Validation Errors:</div>
              <ul className="list-inside list-disc space-y-1 text-sm">
                {crossFieldErrors.map((error, index) => (
                  <li className="flex items-center justify-between" key={index}>
                    <span>
                      <strong>{getFieldDisplayName(error.field)}:</strong>{' '}
                      {error.message}
                    </span>
                    {showFieldNavigation && (
                      <Button
                        className="ml-2 h-6 px-2 text-xs"
                        onClick={() => handleFieldFocus(error.field)}
                        size="sm"
                        variant="ghost"
                      >
                        Go to field
                      </Button>
                    )}
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Cross-Field Warnings */}
          {crossFieldWarnings.length > 0 && (
            <div className="mt-4 space-y-2">
              <div className="text-sm font-medium">Warnings:</div>
              <ul className="list-inside list-disc space-y-1 text-sm">
                {crossFieldWarnings.map((warning, index) => (
                  <li className="flex items-center justify-between" key={index}>
                    <span>
                      <strong>{getFieldDisplayName(warning.field)}:</strong>{' '}
                      {warning.message}
                    </span>
                    {showFieldNavigation && (
                      <Button
                        className="ml-2 h-6 px-2 text-xs"
                        onClick={() => handleFieldFocus(warning.field)}
                        size="sm"
                        variant="ghost"
                      >
                        Go to field
                      </Button>
                    )}
                  </li>
                ))}
              </ul>
            </div>
          )}
        </AlertDescription>
      </Alert>
    );
  }

  return null;
};

export default ValidationSummary;
