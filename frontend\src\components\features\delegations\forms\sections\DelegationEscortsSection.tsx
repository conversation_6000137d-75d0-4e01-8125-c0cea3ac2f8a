/**
 * DelegationEscortsSection Component - SOLID Principles Implementation
 *
 * Single Responsibility: Handles escort selection and management
 * - Multi-select escort functionality with filtering
 * - Follows SRP by focusing only on escort selection
 *
 * @module DelegationEscortsSection
 */

import React from 'react';
import { useFormContext } from 'react-hook-form';
import { Shield, Trash2 } from 'lucide-react';

import type { DelegationFormData } from '@/lib/schemas/delegationSchemas';

import { Badge } from '@/components/ui/badge';
import { FormMessage } from '@/components/ui/form';
import { useEmployeesByRole } from '@/lib/stores/queries/useEmployees';

// ============================================================================
// INTERFACES
// ============================================================================

export interface DelegationEscortsSectionProps {
  /** Whether the form is currently submitting */
  isSubmitting?: boolean;
  /** Additional CSS classes */
  className?: string;
}

// ============================================================================
// COMPONENT IMPLEMENTATION
// ============================================================================

/**
 * Escorts Section for Delegation Form
 *
 * Manages escort selection with multi-select functionality and filtering.
 * This component follows SRP by focusing solely on escort selection.
 */
export const DelegationEscortsSection: React.FC<
  DelegationEscortsSectionProps
> = ({ isSubmitting = false, className = '' }) => {
  const { watch, setValue } = useFormContext<DelegationFormData>();

  // Watch selected escorts
  const selectedEscortIds = watch('escortEmployeeIds') || [];

  // Fetch all employees and filter out drivers (escorts can be any non-driver role)
  const {
    data: allEmployees = [],
    isLoading: escortsLoading,
    error: escortsError,
  } = useEmployeesByRole();

  // Filter out drivers to get potential escorts
  const escorts = allEmployees.filter(emp => emp.role !== 'driver');

  // Filter out already selected escorts for the dropdown
  const availableEscorts = escorts.filter(
    escort => !selectedEscortIds.includes(escort.id)
  );

  // Get selected escort details
  const selectedEscorts = escorts.filter(escort =>
    selectedEscortIds.includes(escort.id)
  );

  const handleAddEscort = (escortId: number) => {
    if (!selectedEscortIds.includes(escortId)) {
      const newEscortIds = [...selectedEscortIds, escortId];
      setValue('escortEmployeeIds', newEscortIds);
    }
  };

  const handleRemoveEscort = (escortId: number) => {
    const newEscortIds = selectedEscortIds.filter(id => id !== escortId);
    setValue('escortEmployeeIds', newEscortIds);
  };

  return (
    <section className={`space-y-4 rounded-lg border bg-card p-6 ${className}`}>
      <h3 className="flex items-center text-lg font-semibold text-foreground">
        <Shield className="mr-2 size-5 text-accent" />
        Escorts
      </h3>

      {/* Escort Selection Dropdown */}
      <div className="space-y-2">
        <label className="text-sm font-medium text-foreground">
          Select Escorts
        </label>
        <select
          className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
          onChange={e => {
            const escortId = parseInt(e.target.value, 10);
            if (escortId) {
              handleAddEscort(escortId);
              e.target.value = ''; // Reset selection
            }
          }}
          disabled={isSubmitting || escortsLoading}
        >
          <option value="">
            {escortsLoading ? 'Loading escorts...' : 'Select an escort to add'}
          </option>
          {availableEscorts.map(escort => (
            <option key={escort.id} value={escort.id}>
              {escort.fullName || escort.name}
              {escort.employeeId && ` (${escort.employeeId})`}
              {escort.status && ` - ${escort.status}`}
              {escort.role && ` (${escort.role})`}
            </option>
          ))}
        </select>
      </div>

      {/* Selected Escorts Display */}
      {selectedEscorts.length > 0 && (
        <div className="space-y-2">
          <label className="text-sm font-medium text-foreground">
            Selected Escorts ({selectedEscorts.length})
          </label>
          <div className="flex flex-wrap gap-2">
            {selectedEscorts.map(escort => (
              <Badge
                key={escort.id}
                variant="secondary"
                className="inline-flex items-center gap-1 px-3 py-1"
              >
                <div className="flex flex-col">
                  <span className="font-medium">
                    {escort.fullName || escort.name}
                  </span>
                  {(escort.status || escort.role) && (
                    <span className="text-xs text-muted-foreground">
                      {escort.role}
                      {escort.role && escort.status && ' • '}
                      {escort.status}
                    </span>
                  )}
                </div>
                {!isSubmitting && (
                  <button
                    type="button"
                    onClick={() => handleRemoveEscort(escort.id)}
                    className="ml-1 text-muted-foreground hover:text-destructive"
                    aria-label={`Remove ${escort.fullName || escort.name}`}
                  >
                    <Trash2 className="size-3" />
                  </button>
                )}
              </Badge>
            ))}
          </div>
        </div>
      )}

      {/* No escorts selected message */}
      {selectedEscorts.length === 0 && (
        <p className="text-sm text-muted-foreground">
          No escorts selected. Select escorts from the dropdown above.
        </p>
      )}

      {/* No available escorts message */}
      {!escortsLoading && escorts.length === 0 && (
        <p className="text-sm text-muted-foreground">
          No escorts available. Please add escorts to the system first.
        </p>
      )}

      <FormMessage />
    </section>
  );
};

export default DelegationEscortsSection;
