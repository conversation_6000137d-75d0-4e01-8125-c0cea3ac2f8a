/**
 * @file Standardized API Response Types
 * @description TypeScript interfaces for consistent API response structures
 * @module types/response
 */

/**
 * Standard error response structure
 */
export interface ApiErrorResponse {
  /** Error code for programmatic handling */
  code: string;
  /** Detailed error information */
  error: {
    details?: Record<string, any>;
    message: string;
    stack?: string;
  };
  /** Validation errors for field-specific issues */
  errors?: ValidationError[];
  /** Human-readable error message */
  message: string;
  /** Request tracking identifier */
  requestId?: string;
  /** Response status indicating error */
  status: 'error';
  /** HTTP status code */
  statusCode: number;
  /** Error timestamp in ISO format */
  timestamp: string;
}

/**
 * Union type for all possible API responses
 */
export type ApiResponse<T = any> = ApiErrorResponse | StandardApiResponse<T>;

/**
 * Pagination metadata for list responses
 */
export interface PaginationMetadata {
  /** Whether there is a next page */
  hasNext: boolean;
  /** Whether there is a previous page */
  hasPrevious: boolean;
  /** Number of items per page */
  limit: number;
  /** Current page number (1-indexed) */
  page: number;
  /** Total number of items */
  total: number;
  /** Total number of pages */
  totalPages: number;
}

/**
 * Configuration options for response wrapper
 */
export interface ResponseWrapperConfig {
  /** Paths that should be excluded from wrapping */
  excludePaths?: string[];
  /** Whether to include request ID in responses */
  includeRequestId?: boolean;
  /** Whether to include timestamp in responses */
  includeTimestamp?: boolean;
  /** Custom status code mappings */
  statusCodeMappings?: Record<number, string>;
}

/**
 * Generic success response wrapper
 * @template T - The type of the data payload
 */
export interface StandardApiResponse<T = any> {
  /** The actual response data */
  data: T;
  /** Optional pagination metadata for list responses */
  pagination?: PaginationMetadata;
  /** Request tracking identifier */
  requestId?: string;
  /** Response status indicating success */
  status: 'success';
  /** Response timestamp in ISO format */
  timestamp: string;
}

/**
 * Validation error for specific fields
 */
export interface ValidationError {
  /** Error code for this validation failure */
  code?: string;
  /** Field path that failed validation */
  field: string;
  /** Error message for this field */
  message: string;
  /** Invalid value that was provided */
  value?: any;
}

/**
 * Type guard to check if response is an error response
 */
export function isApiErrorResponse(obj: any): obj is ApiErrorResponse {
  return (
    obj && typeof obj === 'object' && 'status' in obj && 'message' in obj && obj.status === 'error'
  );
}

/**
 * Type guard to check if response is already wrapped
 */
export function isStandardApiResponse(obj: any): obj is StandardApiResponse {
  return (
    obj && typeof obj === 'object' && 'status' in obj && 'data' in obj && obj.status === 'success'
  );
}
