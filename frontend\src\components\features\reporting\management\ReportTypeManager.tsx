/**
 * @file ReportTypeManager.tsx
 * @description Report type management component following SOLID principles and established patterns
 */

import React, { useState, useMemo } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Plus,
  Edit,
  Trash2,
  Copy,
  Settings,
  FileText,
  MoreHorizontal,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useReportTypes } from '../hooks/useReportTypes';
import { DataLoader } from '@/components/ui/data-loader';
import { ErrorDisplay } from '@/components/ui/error-display';
import { ReportTypeForm } from './ReportTypeForm';
import { ReportTypeCard } from './ReportTypeCard';
import type { ReportType } from '../data/types/reporting';

/**
 * Props interface for ReportTypeManager
 */
interface ReportTypeManagerProps {
  className?: string;
  onReportTypeSelect?: (reportType: ReportType) => void;
  allowEdit?: boolean;
  allowDelete?: boolean;
}

/**
 * ReportTypeManager Component
 *
 * Manages CRUD operations for report types following established patterns.
 *
 * Responsibilities:
 * - Display list of available report types
 * - Handle creation, editing, and deletion of report types
 * - Follow established CRUD component patterns
 * - Maintain consistent styling and behavior
 *
 * SOLID Principles Applied:
 * - SRP: Single responsibility of managing report types
 * - OCP: Open for extension via props configuration
 * - DIP: Depends on established hook and component abstractions
 */
export const ReportTypeManager: React.FC<ReportTypeManagerProps> = ({
  className = '',
  onReportTypeSelect,
  allowEdit = true,
  allowDelete = true,
}) => {
  // State management
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingReportType, setEditingReportType] = useState<ReportType | null>(
    null
  );
  const [searchTerm, setSearchTerm] = useState('');

  // Use established hook patterns
  const {
    data: reportTypes,
    isLoading,
    error,
    createReportType,
    updateReportType,
    deleteReportType,
    duplicateReportType,
  } = useReportTypes();

  // Handle form submission
  const handleFormSubmit = async (formData: {
    name: string;
    category: string;
    dataSource: string;
    widgets: string[];
    isActive: boolean;
    isPublic: boolean;
    description?: string | undefined;
    filters?: string[] | undefined;
    refreshInterval?: number | undefined;
    tags?: string[] | undefined;
  }) => {
    try {
      // Convert form data to ReportType format
      const reportTypeData: Partial<ReportType> = {
        name: formData.name,
        category: formData.category,
        dataSource: formData.dataSource,
        widgets: formData.widgets,
        isActive: formData.isActive,
        isPublic: formData.isPublic,
        ...(formData.description && { description: formData.description }),
        ...(formData.filters && { filters: formData.filters }),
        ...(formData.refreshInterval && {
          refreshInterval: formData.refreshInterval,
        }),
        ...(formData.tags && { tags: formData.tags }),
      };

      if (editingReportType) {
        await updateReportType.mutateAsync({
          id: editingReportType.id,
          ...reportTypeData,
        });
      } else {
        await createReportType.mutateAsync(reportTypeData);
      }

      // Reset form state
      setIsFormOpen(false);
      setEditingReportType(null);
    } catch (error) {
      console.error('Failed to save report type:', error);
    }
  };

  // Handle edit action
  const handleEdit = (reportType: ReportType) => {
    setEditingReportType(reportType);
    setIsFormOpen(true);
  };

  // Handle delete action
  const handleDelete = async (reportType: ReportType) => {
    if (
      window.confirm(`Are you sure you want to delete "${reportType.name}"?`)
    ) {
      try {
        await deleteReportType.mutateAsync(reportType.id);
      } catch (error) {
        console.error('Failed to delete report type:', error);
      }
    }
  };

  // Handle duplicate action
  const handleDuplicate = async (reportType: ReportType) => {
    try {
      await duplicateReportType.mutateAsync(reportType.id);
    } catch (error) {
      console.error('Failed to duplicate report type:', error);
    }
  };

  // Filter report types based on search
  const filteredReportTypes = useMemo(
    () =>
      reportTypes?.filter(
        reportType =>
          reportType.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          reportType.description
            ?.toLowerCase()
            .includes(searchTerm.toLowerCase())
      ) || [],
    [reportTypes, searchTerm]
  );

  // Handle loading state
  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Report Type Manager
          </CardTitle>
        </CardHeader>
        <CardContent>
          <DataLoader isLoading={true} data={null} error={null}>
            {() => null}
          </DataLoader>
        </CardContent>
      </Card>
    );
  }

  // Handle error state
  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Report Type Manager
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ErrorDisplay error={error} />
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={cn('space-y-6', className)}>
      {/* Header Section */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Report Type Manager
            </CardTitle>
            <div className="flex items-center gap-2">
              <Badge variant="secondary" className="text-xs">
                {filteredReportTypes.length} types
              </Badge>
              <Button
                onClick={() => setIsFormOpen(true)}
                size="sm"
                className="flex items-center gap-2"
              >
                <Plus className="h-4 w-4" />
                New Report Type
              </Button>
            </div>
          </div>
        </CardHeader>

        <CardContent>
          {/* Search and Filters */}
          <div className="flex items-center gap-4 mb-6">
            <div className="flex-1">
              <input
                type="text"
                placeholder="Search report types..."
                value={searchTerm}
                onChange={e => setSearchTerm(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <Button variant="outline" size="sm">
              <Settings className="h-4 w-4" />
            </Button>
          </div>

          {/* Report Types Grid */}
          {filteredReportTypes.length === 0 ? (
            <div className="text-center py-12 text-gray-500">
              <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <h3 className="text-lg font-medium mb-2">
                No Report Types Found
              </h3>
              <p className="text-sm mb-4">
                {searchTerm
                  ? 'No report types match your search.'
                  : 'Get started by creating your first report type.'}
              </p>
              <Button onClick={() => setIsFormOpen(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Create Report Type
              </Button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredReportTypes.map(reportType => (
                <ReportTypeCard
                  key={reportType.id}
                  reportType={reportType}
                  {...(onReportTypeSelect && { onSelect: onReportTypeSelect })}
                  {...(allowEdit && { onEdit: handleEdit })}
                  {...(allowDelete && { onDelete: handleDelete })}
                  onDuplicate={handleDuplicate}
                />
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Report Type Form Modal/Drawer */}
      {isFormOpen && (
        <ReportTypeForm
          reportType={editingReportType}
          onSubmit={handleFormSubmit}
          onCancel={() => {
            setIsFormOpen(false);
            setEditingReportType(null);
          }}
          isLoading={createReportType.isPending || updateReportType.isPending}
        />
      )}
    </div>
  );
};

export default ReportTypeManager;
