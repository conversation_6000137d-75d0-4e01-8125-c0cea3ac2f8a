/**
 * Quick script to get JW<PERSON> token for testing API endpoints
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: './.env' });

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseAnonKey = process.env.SUPABASE_ANON_KEY;

console.log('Debug - SUPABASE_URL:', supabaseUrl);
console.log('Debug - SUPABASE_ANON_KEY:', supabaseAnonKey ? 'Found' : 'Missing');

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing Supabase environment variables');
  console.error('Required: SUPABASE_URL, SUPABASE_ANON_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function getTestToken() {
  try {
    console.log('🔐 Attempting to sign in with test credentials...');

    const { data, error } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'TestPassword123!',
    });

    if (error) {
      console.error('❌ Sign in failed:', error.message);
      return;
    }

    if (data.session) {
      console.log('✅ Sign in successful!');
      console.log('🎫 Access Token:', data.session.access_token);
      console.log('\n📋 Test API call:');
      console.log(`curl -X GET http://localhost:3001/api/reporting/reports/templates \\`);
      console.log(`  -H "Authorization: Bearer ${data.session.access_token}" \\`);
      console.log(`  -H "Content-Type: application/json"`);
    } else {
      console.error('❌ No session returned');
    }
  } catch (err) {
    console.error('❌ Error:', err.message);
  }
}

getTestToken();
