'use client';

import React from 'react';

import type { Task } from '@/lib/types/domain';

import { cn } from '@/lib/utils';
import TaskCard from './TaskCard';
import { TasksTable } from './TasksTable';

/**
 * Props for the TaskViewRenderer component
 */
interface TaskViewRendererProps {
  /** Array of tasks to display */
  tasks: Task[];
  /** View mode for displaying tasks */
  viewMode: 'cards' | 'table' | 'list' | 'grid' | 'calendar';
  /** Number of grid columns for card view */
  gridColumns?: number;
  /** Whether to use compact mode */
  compactMode?: boolean;
  /** Additional CSS class names */
  className?: string;
}

/**
 * Task view renderer component that switches between different view modes.
 *
 * Supported view modes:
 * - cards: Grid of task cards
 * - table: Tabular view with sortable columns
 * - list: Vertical list of task cards
 * - grid: Same as cards (alias)
 * - calendar: Falls back to cards view
 *
 * @param props - Component props
 * @returns JSX element representing the tasks in the selected view mode
 */
export const TaskViewRenderer: React.FC<TaskViewRendererProps> = ({
  className = '',
  compactMode,
  tasks,
  gridColumns = 3,
  viewMode,
}) => {
  // Render based on view mode
  switch (viewMode) {
    case 'list': {
      return (
        <div
          className={cn(
            'flex flex-col',
            compactMode ? 'gap-2' : 'gap-4',
            className
          )}
        >
          {tasks.map(task => (
            <TaskCard task={task} key={task.id} />
          ))}
        </div>
      );
    }

    case 'table': {
      return <TasksTable className={className} tasks={tasks} />;
    }

    case 'cards':
    case 'grid':
    case 'calendar':
    default: {
      return (
        <div
          className={cn(
            'grid grid-cols-1 gap-6',
            `md:grid-cols-2 lg:grid-cols-${gridColumns}`,
            compactMode && 'gap-3',
            className
          )}
        >
          {tasks.map(task => (
            <TaskCard task={task} key={task.id} />
          ))}
        </div>
      );
    }
  }
};

export default TaskViewRenderer;
