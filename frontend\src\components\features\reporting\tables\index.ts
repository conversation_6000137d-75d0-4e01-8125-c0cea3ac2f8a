// frontend/src/components/features/reporting/tables/index.ts

/**
 * Reporting Tables Export Index
 *
 * Centralized exports for all reporting table components following DRY principles.
 */

// Base Table Component
export { ReportingDataTable } from './ReportingDataTable';
export type { ReportingTableData } from './ReportingDataTable';

// Specialized Table Components
export { DelegationReportTable } from './DelegationReportTable';
// FIXED: DelegationReportData is now imported from centralized types, not exported from table

// Phase 2: Task Reporting Table
export { TaskReportingTable } from './TaskReportingTable';
