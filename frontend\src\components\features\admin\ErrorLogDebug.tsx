'use client';

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ug, <PERSON>fresh<PERSON><PERSON>, <PERSON>, Trash2 } from 'lucide-react';
import React, { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { adminCache } from '@/lib/api/services/admin';
import { CircuitState } from '@/lib/utils/circuitBreaker';
import type { CircuitBreakerStatus } from '@/lib/types/domain';

/**
 * Debug component for error logs troubleshooting
 */
export function ErrorLogDebug() {
  const [debugInfo, setDebugInfo] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const handleGetDebugInfo = async () => {
    setLoading(true);
    try {
      // Get circuit breaker status
      const circuitBreakers: Record<string, CircuitBreakerStatus> = {}; // Mock implementation

      // Get cache stats
      const cacheStats = adminCache.getStats();

      // Get error-specific cache entries
      const errorCacheEntries: any[] = []; // Mock implementation since entries property doesn't exist

      setDebugInfo({
        cacheStats: {
          errorCacheKeys: errorCacheEntries.map((e: any) => e.key || 'unknown'),
          errorEntries: errorCacheEntries.length,
          totalEntries: cacheStats.size,
        },
        circuitBreakers,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      console.error('Failed to get debug info:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleResetCircuitBreakers = () => {
    // Mock implementation - just clear all cache
    adminCache.clearAll();
    console.log('🔄 Circuit breakers reset');
    handleGetDebugInfo(); // Refresh debug info
  };

  const handleClearErrorCache = () => {
    // Mock implementation - just clear all cache
    adminCache.clearAll();
    console.log('🔄 Error cache cleared');
    handleGetDebugInfo(); // Refresh debug info
  };

  const handleClearAllCache = () => {
    adminCache.clearAll();
    console.log('🔄 All cache cleared');
    handleGetDebugInfo(); // Refresh debug info
  };

  const getCircuitBreakerColor = (state: CircuitState) => {
    switch (state) {
      case CircuitState.CLOSED: {
        return 'bg-green-100 text-green-800';
      }
      case CircuitState.HALF_OPEN: {
        return 'bg-yellow-100 text-yellow-800';
      }
      case CircuitState.OPEN: {
        return 'bg-red-100 text-red-800';
      }
      default: {
        return 'bg-gray-100 text-gray-800';
      }
    }
  };

  return (
    <Card className="shadow-md">
      <CardHeader className="p-5">
        <CardTitle className="flex items-center gap-2 text-xl font-semibold text-primary">
          <Bug className="size-5" />
          Error Logs Debug
        </CardTitle>
      </CardHeader>
      <CardContent className="p-5">
        <div className="space-y-4">
          {/* Action Buttons */}
          <div className="flex flex-wrap gap-2">
            <Button
              className="flex items-center gap-2"
              disabled={loading}
              onClick={handleGetDebugInfo}
            >
              <RefreshCw
                className={`size-4 ${loading ? 'animate-spin' : ''}`}
              />
              Get Debug Info
            </Button>
            <Button
              className="flex items-center gap-2 text-orange-600 hover:text-orange-700"
              onClick={handleResetCircuitBreakers}
              variant="outline"
            >
              <Shield className="size-4" />
              Reset Circuit Breakers
            </Button>
            <Button
              className="flex items-center gap-2 text-blue-600 hover:text-blue-700"
              onClick={handleClearErrorCache}
              variant="outline"
            >
              <Trash2 className="size-4" />
              Clear Error Cache
            </Button>
            <Button
              className="flex items-center gap-2 text-red-600 hover:text-red-700"
              onClick={handleClearAllCache}
              variant="outline"
            >
              <Trash2 className="size-4" />
              Clear All Cache
            </Button>
          </div>

          {/* Debug Information */}
          {debugInfo && (
            <div className="space-y-4">
              {/* Circuit Breaker Status */}
              <div className="rounded-lg bg-gray-50 p-4">
                <h4 className="mb-3 flex items-center gap-2 font-semibold">
                  <Shield className="size-4" />
                  Circuit Breaker Status
                </h4>
                <div className="grid grid-cols-1 gap-3 md:grid-cols-2 lg:grid-cols-4">
                  {Object.entries(debugInfo.circuitBreakers).map(
                    ([name, status]: [string, any]) => (
                      <div className="rounded border bg-white p-3" key={name}>
                        <div className="mb-2 flex items-center justify-between">
                          <span className="text-sm font-medium capitalize">
                            {name}
                          </span>
                          <Badge
                            className={getCircuitBreakerColor(status.state)}
                          >
                            {status.state}
                          </Badge>
                        </div>
                        <div className="text-xs text-muted-foreground">
                          <div>Failures: {status.failureCount}</div>
                          {status.timeUntilRetry && (
                            <div>
                              Retry in:{' '}
                              {Math.round(status.timeUntilRetry / 1000)}s
                            </div>
                          )}
                        </div>
                      </div>
                    )
                  )}
                </div>
              </div>

              {/* Cache Status */}
              <div className="rounded-lg bg-blue-50 p-4">
                <h4 className="mb-3 font-semibold text-blue-900">
                  Cache Status
                </h4>
                <div className="grid grid-cols-2 gap-4 text-sm md:grid-cols-3">
                  <div>
                    <span className="font-medium text-blue-800">
                      Total Entries:
                    </span>
                    <span className="ml-2 text-blue-600">
                      {debugInfo.cacheStats.totalEntries}
                    </span>
                  </div>
                  <div>
                    <span className="font-medium text-blue-800">
                      Error Entries:
                    </span>
                    <span className="ml-2 text-blue-600">
                      {debugInfo.cacheStats.errorEntries}
                    </span>
                  </div>
                  <div>
                    <span className="font-medium text-blue-800">
                      Last Updated:
                    </span>
                    <span className="ml-2 text-blue-600">
                      {new Date(debugInfo.timestamp).toLocaleTimeString()}
                    </span>
                  </div>
                </div>

                {debugInfo.cacheStats.errorCacheKeys.length > 0 && (
                  <div className="mt-3">
                    <div className="mb-2 font-medium text-blue-800">
                      Error Cache Keys:
                    </div>
                    <div className="space-y-1">
                      {debugInfo.cacheStats.errorCacheKeys.map(
                        (key: string, index: number) => (
                          <div
                            className="rounded border bg-white p-2 font-mono text-xs"
                            key={index}
                          >
                            {key}
                          </div>
                        )
                      )}
                    </div>
                  </div>
                )}
              </div>

              {/* Troubleshooting Tips */}
              <div className="rounded-lg bg-yellow-50 p-4">
                <h4 className="mb-3 flex items-center gap-2 font-semibold text-yellow-900">
                  <AlertTriangle className="size-4" />
                  Troubleshooting Tips
                </h4>
                <ul className="space-y-1 text-sm text-yellow-800">
                  <li>
                    • If circuit breakers are OPEN (red), reset them and try
                    again
                  </li>
                  <li>
                    • If error cache is stale, clear it to force fresh data
                  </li>
                  <li>• Check browser console for network errors</li>
                  <li>• Verify backend server is running and accessible</li>
                  <li>• Check if rate limiting is affecting requests</li>
                </ul>
              </div>
            </div>
          )}

          {!debugInfo && (
            <div className="py-8 text-center text-muted-foreground">
              <Bug className="mx-auto mb-3 size-12 opacity-50" />
              <p>
                Click "Get Debug Info" to see error logs debugging information
              </p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
