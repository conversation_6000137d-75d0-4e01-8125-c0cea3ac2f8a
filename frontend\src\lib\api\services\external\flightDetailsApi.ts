/**
 * @file API service for FlightDetails-related operations.
 * @module api/services/flightDetailsApi
 * Refactored to use BaseApiService pattern for consistency.
 */

import type { FlightDetails } from '../../../types/domain';
import type { ApiClient } from '../../core/apiClient';
import {
  BaseApiService,
  type DataTransformer,
  type ServiceConfig,
} from '../../core/baseApiService';

const FlightDetailsTransformer: DataTransformer<FlightDetails> = {
  fromApi: (data: any) => data,
  toApi: (data: any) => data,
};

/**
 * Service class for interacting with the Flight Details API endpoints.
 * Now extends BaseApiService for consistency and enhanced features.
 */
export class FlightDetailsApiService extends BaseApiService<
  FlightDetails,
  Partial<FlightDetails>,
  Partial<FlightDetails>
> {
  protected endpoint = '/flights';
  protected transformer: DataTransformer<FlightDetails> =
    FlightDetailsTransformer;

  constructor(apiClient: ApiClient, config?: ServiceConfig) {
    super(apiClient, {
      cacheDuration: 5 * 60 * 1000, // 5 minutes for flight details
      retryAttempts: 3,
      circuitBreakerThreshold: 5,
      enableMetrics: true,
      ...config,
    });
  }

  /**
   * Creates or updates flight details.
   * If flightDetails.id is provided, it attempts to update. Otherwise, it creates a new one.
   */
  public async createOrUpdateFlight(
    flightDetails: Partial<FlightDetails>
  ): Promise<FlightDetails> {
    return this.executeWithInfrastructure(null, async () => {
      if (flightDetails.id) {
        // Use the inherited update method
        return this.update(flightDetails.id, flightDetails);
      } else {
        // Use the inherited create method
        return this.create(flightDetails);
      }
    });
  }
}
