import * as z from 'zod';

import { isValidDateString } from '@/lib/utils/dateUtils';

export const DelegationStatusSchema = z.enum([
  'Planned',
  'Confirmed',
  'In_Progress', // Changed from 'In Progress' to match backend
  'Completed',
  'Cancelled',
  'No_details', // Changed from 'No details' to match backend
]);

export const StatusHistoryEntrySchema = z.object({
  changedAt: z
    .string()
    .datetime({ message: 'Invalid date/time format for status change' }),
  id: z.string().uuid(),
  reason: z.string().optional(),
  status: DelegationStatusSchema,
});

export const DelegateSchema = z.object({
  id: z.string().optional(), // Optional for new delegates, will be generated - removed UUID validation for flexibility
  name: z.string().min(1, 'Delegate name is required'),
  notes: z.string().optional(),
  title: z.string().min(1, 'Delegate title is required'),
});

export const FlightDetailsSchema = z.preprocess(
  // First preprocess to handle the entire object being null/undefined
  val => {
    // If the value is null or undefined, return null
    if (val === null || val === undefined) {
      return null;
    }

    // If it's an object, check if all required fields are empty
    if (typeof val === 'object') {
      const obj = val as any;
      const isEmpty =
        (!obj.flightNumber || obj.flightNumber.trim() === '') &&
        (!obj.dateTime || obj.dateTime.trim() === '') &&
        (!obj.airport || obj.airport.trim() === '');

      if (isEmpty) {
        return null;
      }
    }

    // Otherwise return the value as is
    return val;
  },
  z
    .object({
      airport: z.preprocess(
        val => val ?? '', // Convert null/undefined to empty string
        z.string().min(1, 'Airport is required')
      ),
      // Ensure dateTime is a non-empty string and a valid date
      dateTime: z.preprocess(
        val => val ?? '', // Convert null/undefined to empty string
        z
          .string()
          .min(1, 'Date & Time is required') // Explicitly require non-empty
          .refine(val => !isNaN(Date.parse(val)), {
            // Then check if it's a valid date
            message: 'Invalid date & time format',
          })
      ),
      flightNumber: z.preprocess(
        val => val ?? '', // Convert null/undefined to empty string
        z.string().min(1, 'Flight number is required')
      ),
      notes: z
        .string()
        .trim()
        .nullable()
        .optional()
        .transform(val => (val === '' ? null : val)), // Store empty string as null
      terminal: z
        .string()
        .trim()
        .nullable()
        .optional()
        .transform(val => (val === '' ? null : val)), // Store empty string as null
    })
    .nullable()
    .optional()
);

export const DelegationFormSchema = z
  .object({
    delegates: z
      .array(DelegateSchema)
      .min(1, 'At least one delegate is required'),
    driverEmployeeIds: z
      .array(
        z
          .number()
          .int()
          .positive('Driver Employee ID must be a positive integer.')
      )
      .optional()
      .default([]),
    durationFrom: z.string().refine(val => isValidDateString(val), {
      message: 'Invalid start date format',
    }),
    durationTo: z.string().refine(val => isValidDateString(val), {
      message: 'Invalid end date format',
    }),
    // NEW PHASE 3 ASSIGNMENT FIELDS
    escortEmployeeIds: z
      .array(
        z
          .number()
          .int()
          .positive('Escort Employee ID must be a positive integer.')
      )
      .optional()
      .default([])
      .refine(arr => arr.length <= 10, {
        message: 'Maximum 10 escort employees allowed',
      }),
    eventName: z.string().min(1, 'Event name is required'),
    flightArrivalDetails: FlightDetailsSchema.nullable().optional(),
    flightDepartureDetails: FlightDetailsSchema.nullable().optional(),
    imageUrl: z
      .string()
      .url('Invalid URL for image')
      .optional()
      .or(z.literal('')),
    invitationFrom: z.string().optional(),
    invitationTo: z.string().optional(),
    location: z.string().min(1, 'Location is required'),
    notes: z.string().optional(),
    status: DelegationStatusSchema.default('Planned'),
    statusChangeReason: z.string().optional(),
    vehicleIds: z
      .array(
        z.number().int().positive('Vehicle ID must be a positive integer.')
      )
      .optional()
      .default([]),
  })
  .superRefine((data, ctx) => {
    if (new Date(data.durationFrom) > new Date(data.durationTo)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'End date cannot be earlier than start date',
        path: ['durationTo'],
      });
    }

    // NEW PHASE 3: Vehicle assignment validation for multiple vehicles
    if (
      data.vehicleIds &&
      data.vehicleIds.length > 0 &&
      (!data.driverEmployeeIds || data.driverEmployeeIds.length === 0)
    ) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Vehicles cannot be assigned without at least one driver',
        path: ['vehicleIds'],
      });
    }

    // The detailed checks for partial filling of flight details are now largely handled
    // by the stricter FlightDetailsSchema fields using preprocess and min(1)/refine.
    // If flightArrivalDetails or flightDepartureDetails is an object, Zod will enforce
    // that its fields (flightNumber, dateTime, airport) are validly filled.
    // The .nullable().optional() on flightArrivalDetails/flightDepartureDetails in this schema
    // allows these entire objects to be null or undefined if no flight info is provided.

    // Example: if user provides arrival details but not departure.
    // data.flightArrivalDetails would be an object, validated by FlightDetailsSchema.
    // data.flightDepartureDetails would be null, which is allowed.
  });

// Export types
export type DelegationFormData = z.infer<typeof DelegationFormSchema>;
export type DelegationStatus = z.infer<typeof DelegationStatusSchema>;
export type StatusHistoryEntry = z.infer<typeof StatusHistoryEntrySchema>;
export type Delegate = z.infer<typeof DelegateSchema>;
export type FlightDetails = z.infer<typeof FlightDetailsSchema>;
