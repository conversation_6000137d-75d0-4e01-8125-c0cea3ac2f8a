import type { SupabaseClient } from '@supabase/supabase-js';

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

import { PrismaClient } from '../generated/prisma/index.js';
import logger from '../utils/logger.js';
import { executeWithCircuitBreaker } from './circuitBreaker.service.js';
import { businessMetrics } from './metrics.service.js';

// Load environment variables
dotenv.config();

interface ConnectionDetails {
  prisma: {
    connectionString: string;
    error: null | PrismaErrorDetails;
  };
  supabase: {
    clientInitialized: boolean;
    data: any;
    error: null | SupabaseErrorDetails;
    keyProvided: boolean;
    url: string;
  };
}

interface ConnectionResults {
  details: ConnectionDetails;
  prisma: boolean;
  supabase: boolean;
  timestamp: string;
}

// Database configuration
interface DatabaseConfig {
  databaseUrl: string;
  supabaseKey?: string;
  supabaseUrl?: string;
  useSupabase: boolean;
}

// Error interfaces
interface PrismaErrorDetails {
  code?: string;
  message?: string;
  meta?: any;
}

interface SupabaseErrorDetails {
  clientInitialized?: boolean;
  code?: string;
  details?: string;
  hint?: string;
  message?: string;
  name?: string;
  stack?: string;
}

// Get database configuration from environment variables
const getDatabaseConfig = (): DatabaseConfig => {
  // Check if USE_SUPABASE is set to true
  const useSupabase = process.env.USE_SUPABASE === 'true';

  // Get database URL
  const databaseUrl = process.env.DATABASE_URL || '';

  // Get Supabase credentials if using Supabase
  const supabaseUrl = useSupabase ? process.env.SUPABASE_URL : undefined;
  const supabaseKey = useSupabase ? process.env.SUPABASE_SERVICE_ROLE_KEY : undefined;

  // Log configuration details (hiding sensitive info)
  logger.info('Database Configuration:', {
    connectionMode: databaseUrl.includes(':6543/') ? 'transaction' : 'session',
    databaseUrl: databaseUrl ? databaseUrl.replace(/\/\/.*?@/, '//****@') : 'not set',
    supabaseKeyProvided: !!supabaseKey,
    supabaseUrl: supabaseUrl ? supabaseUrl : 'not set',
    useSupabase,
  });

  return {
    databaseUrl,
    supabaseKey,
    supabaseUrl,
    useSupabase,
  };
};

// Initialize Prisma client
const prisma = new PrismaClient({
  log: [
    {
      emit: 'stdout',
      level: 'query',
    },
    {
      emit: 'stdout',
      level: 'info',
    },
    {
      emit: 'stdout',
      level: 'warn',
    },
    {
      emit: 'stdout',
      level: 'error',
    },
  ],
});

// Initialize Supabase client if configured
let supabase: null | SupabaseClient = null;

const initializeSupabase = () => {
  const config = getDatabaseConfig();

  if (config.useSupabase) {
    if (!config.supabaseUrl) {
      logger.error('Failed to initialize Supabase: SUPABASE_URL is not set');
      return null;
    }

    if (!config.supabaseKey) {
      logger.error('Failed to initialize Supabase: SUPABASE_SERVICE_ROLE_KEY is not set');
      return null;
    }

    try {
      logger.info(`Initializing Supabase client with URL: ${config.supabaseUrl}`);
      const options = {
        auth: {
          autoRefreshToken: true,
          persistSession: true,
        },
        global: {
          headers: {
            'X-Client-Info': 'car-service-tracking-system',
          },
        },
      };

      supabase = createClient(config.supabaseUrl, config.supabaseKey, options);
      logger.info('Supabase client initialized successfully');
      return supabase;
    } catch (error: any) {
      logger.error('Failed to initialize Supabase client:', error);
      logger.error('Error details:', JSON.stringify(error, null, 2));
      logger.error('Stack trace:', error.stack);
      return null;
    }
  } else {
    logger.info('Supabase initialization skipped - USE_SUPABASE is not set to true');
  }

  return null;
};

// Test database connections
const testDatabaseConnections = async (): Promise<ConnectionResults> => {
  const config = getDatabaseConfig();
  const results: ConnectionResults = {
    details: {
      prisma: {
        connectionString: config.databaseUrl
          ? config.databaseUrl.replace(/\/\/.*?@/, '//****@')
          : 'not set',
        error: null,
      },
      supabase: {
        clientInitialized: !!supabase,
        data: null,
        error: null,
        keyProvided: !!config.supabaseKey,
        url: config.supabaseUrl || 'not set',
      },
    },
    prisma: false,
    supabase: false,
    timestamp: new Date().toISOString(),
  };

  // Test Prisma connection with circuit breaker protection
  try {
    logger.info('Testing Prisma connection...');
    const startTime = Date.now();

    await executeWithCircuitBreaker('database', async () => {
      await prisma.$connect();
      await prisma.$queryRaw`SELECT 1`;
    });

    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;

    // Record successful database operation
    businessMetrics.recordDatabaseQuery('connection_test', 'system', duration, 'success');

    logger.info(
      `✅ Successfully connected to the PostgreSQL database via Prisma (${endTime - startTime}ms)`,
    );
    results.prisma = true;
  } catch (error: any) {
    // Record failed database operation
    businessMetrics.recordDatabaseQuery('connection_test', 'system', 0, 'error');

    logger.error('❌ Failed to connect to the PostgreSQL database via Prisma:', error);
    logger.error('Prisma error details:', JSON.stringify(error, null, 2));
    logger.error('Stack trace:', error.stack);
    results.details.prisma.error = {
      code: error.code || 'UNKNOWN',
      message: error.message || 'Unknown error',
      meta: error.meta || {},
    };
  }

  // Test Supabase connection if configured
  if (config.useSupabase) {
    if (supabase) {
      try {
        logger.info('Testing Supabase connection...');
        const startTime = Date.now();

        // Test Supabase connection with circuit breaker protection
        const { data, error } = await executeWithCircuitBreaker('supabase', async () => {
          return await supabase!.from('_prisma_migrations').select('*').limit(1);
        });

        const endTime = Date.now();
        const duration = (endTime - startTime) / 1000;

        if (error) {
          // Record failed Supabase operation
          businessMetrics.recordDatabaseQuery('connection_test', 'supabase', duration, 'error');

          logger.error('❌ Failed to query Supabase table:', error.message);
          logger.error('Supabase error details:', JSON.stringify(error, null, 2));
          results.details.supabase.error = {
            code: error.code || 'QUERY_FAILED',
            details: error.details || '',
            hint: error.hint || '',
            message: error.message || 'Query failed',
          };
        } else {
          // Record successful Supabase operation
          businessMetrics.recordDatabaseQuery('connection_test', 'supabase', duration, 'success');

          logger.info(
            `✅ Successfully connected to Supabase and queried data (${endTime - startTime}ms)`,
          );
          logger.debug('Supabase data sample:', JSON.stringify(data));
          results.supabase = true;
          results.details.supabase.data = data || [];
        }
      } catch (error: any) {
        // Record failed Supabase operation
        businessMetrics.recordDatabaseQuery('connection_test', 'supabase', 0, 'error');

        logger.error('❌ Error testing Supabase connection:', error);
        logger.error('Error details:', JSON.stringify(error, null, 2));
        logger.error('Stack trace:', error.stack);
        results.details.supabase.error = {
          message: error.message || 'Unknown error',
          name: error.name || 'Error',
          stack: error.stack || '',
        };
      }
    } else {
      logger.warn('⚠️ Supabase is enabled but client initialization failed');
      results.details.supabase.error = {
        clientInitialized: false,
        message: 'Supabase client initialization failed',
      };
    }
  } else {
    logger.info('ℹ️ Supabase connection test skipped - not configured');
    results.details.supabase.error = {
      message: 'Supabase not configured (USE_SUPABASE is not set to true)',
    };
  }

  // Log the final results
  logger.info('Database connection test results:', JSON.stringify(results, null, 2));

  return results;
};

// Initialize Supabase if configured
initializeSupabase();

export { getDatabaseConfig, initializeSupabase, prisma, supabase, testDatabaseConnections };

export default prisma;
