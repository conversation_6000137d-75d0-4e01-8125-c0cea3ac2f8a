/**
 * @file DelegationFormContainer Tests
 * @description Tests for the SOLID-compliant DelegationFormContainer component
 */

import React from 'react';
import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { DelegationFormContainer } from '../DelegationFormContainer';

// Mock the router
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    back: jest.fn(),
    push: jest.fn(),
  }),
}));

// Mock the form submission hook
jest.mock('@/hooks/forms/useFormSubmission', () => ({
  useFormSubmission: () => ({
    handleSubmit: jest.fn(),
    isLoading: false,
    error: null,
    state: 'idle',
    retry: jest.fn(),
    ariaAttributes: {},
  }),
}));

// Mock the employee and vehicle queries
jest.mock('@/lib/stores/queries/useEmployees', () => ({
  useEmployeesByRole: () => ({
    data: [],
    isLoading: false,
  }),
}));

jest.mock('@/lib/stores/queries/useVehicles', () => ({
  useVehicles: () => ({
    data: [],
    isLoading: false,
  }),
}));

// Test wrapper with QueryClient
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );
};

describe('DelegationFormContainer', () => {
  const mockOnSubmit = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders all form sections', () => {
    render(
      <TestWrapper>
        <DelegationFormContainer onSubmit={mockOnSubmit} />
      </TestWrapper>
    );

    // Check that all sections are rendered
    expect(screen.getByText('Basic Information')).toBeInTheDocument();
    expect(screen.getByText('Assignment Details')).toBeInTheDocument();
    expect(screen.getByText('Delegates')).toBeInTheDocument();
    expect(screen.getByText('Drivers')).toBeInTheDocument();
    expect(screen.getByText('Escorts')).toBeInTheDocument();
    expect(screen.getByText('Vehicles')).toBeInTheDocument();
    expect(screen.getByText('Arrival Flight (Optional)')).toBeInTheDocument();
    expect(screen.getByText('Departure Flight (Optional)')).toBeInTheDocument();
    expect(screen.getByText('General Notes (Optional)')).toBeInTheDocument();
  });

  it('renders form controls', () => {
    render(
      <TestWrapper>
        <DelegationFormContainer onSubmit={mockOnSubmit} />
      </TestWrapper>
    );

    // Check for form controls
    expect(screen.getByRole('button', { name: /cancel/i })).toBeInTheDocument();
    expect(
      screen.getByRole('button', { name: /create delegation/i })
    ).toBeInTheDocument();
  });

  it('shows edit mode correctly', () => {
    render(
      <TestWrapper>
        <DelegationFormContainer onSubmit={mockOnSubmit} isEditing={true} />
      </TestWrapper>
    );

    expect(
      screen.getByRole('button', { name: /save changes/i })
    ).toBeInTheDocument();
  });

  it('renders with initial data', () => {
    const initialData = {
      eventName: 'Test Event',
      location: 'Test Location',
      delegates: [{ name: 'John Doe', title: 'Manager', notes: 'Test notes' }],
      driverEmployeeIds: [],
      escortEmployeeIds: [],
      vehicleIds: [],
      durationFrom: '2024-01-01',
      durationTo: '2024-01-02',
      status: 'Planned' as const,
      notes: 'Test delegation notes',
      flightArrivalDetails: {
        flightNumber: '',
        dateTime: '',
        airport: '',
        terminal: '',
        notes: '',
      },
      flightDepartureDetails: {
        flightNumber: '',
        dateTime: '',
        airport: '',
        terminal: '',
        notes: '',
      },
      imageUrl: '',
      invitationFrom: '',
      invitationTo: '',
    };

    render(
      <TestWrapper>
        <DelegationFormContainer
          onSubmit={mockOnSubmit}
          initialData={initialData}
          isEditing={true}
        />
      </TestWrapper>
    );

    // The form should render with the initial data
    expect(screen.getByDisplayValue('Test Event')).toBeInTheDocument();
    expect(screen.getByDisplayValue('Test Location')).toBeInTheDocument();
  });
});
