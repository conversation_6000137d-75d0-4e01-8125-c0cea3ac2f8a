import { circuitBreakerRegistry } from '../services/circuitBreaker.service.js';
import { initializeRedisClient } from '../services/redis.service.js';
import { testDatabaseConnections } from '../services/database.service.js';
import { validateSecretsOrThrow } from '../config/secrets.js';
import logger from '../utils/logger.js';

// Utility function to wrap startup tasks with consistent logging

// Utility function to wrap startup tasks with consistent logging
const executeStartupTask = async (
  task: () => Promise<void>,
  description: string,
): Promise<void> => {
  logger.info(`Attempting to ${description}...`);
  try {
    await task();
    logger.info(`✅ ${description} completed successfully.`);
  } catch (error) {
    logger.error(
      `🚨 ${description} failed:`,
      error instanceof Error ? error.message : String(error),
    );
    // Re-throw the error to be caught by the main initializeApplication catch block
    // This ensures that a failure in any task stops the initialization process.
    throw error;
  }
};

// Individual startup tasks (their internal logging can be removed or simplified)
const validateSecrets = async (): Promise<void> => {
  // Load environment variables first
  const dotenv = await import('dotenv');
  dotenv.config();

  // Validate all application secrets including COOKIE_SECRET
  validateSecretsOrThrow();
};

const testDatabaseConnection = async (): Promise<void> => {
  const results = await testDatabaseConnections();
  if (!results.prisma) {
    throw new Error('Database connection test failed');
  }
  logger.info('Database connection test passed');
};

const initializeRedis = async (): Promise<void> => {
  try {
    await initializeRedisClient();
    logger.info('Redis client initialized successfully');
  } catch (error) {
    logger.warn('Redis initialization failed, will use memory fallback:', error);
    // Don't throw error as Redis is optional
  }
};

const initializeCircuitBreakers = async (): Promise<void> => {
  try {
    // Initialize circuit breakers for critical services
    const supabaseBreaker = circuitBreakerRegistry.getBreaker('supabase');
    const redisBreaker = circuitBreakerRegistry.getBreaker('redis');
    const databaseBreaker = circuitBreakerRegistry.getBreaker('database');

    logger.info('Circuit breakers initialized successfully:', {
      supabase: supabaseBreaker.name,
      redis: redisBreaker.name,
      database: databaseBreaker.name,
      totalBreakers: 3,
    });
  } catch (error) {
    logger.error('Failed to initialize circuit breakers:', error);
    throw error;
  }
};

const testSupabaseAuth = async (): Promise<void> => {
  // Placeholder for actual Supabase auth test
  await new Promise(resolve => setTimeout(resolve, 100));
};

export const initializeApplication = async (): Promise<void> => {
  logger.info('🚀 Application initialization process started...');
  try {
    await executeStartupTask(validateSecrets, 'validate application secrets');
    await executeStartupTask(testDatabaseConnection, 'test database connection');
    await executeStartupTask(initializeRedis, 'initialize Redis client');
    await executeStartupTask(initializeCircuitBreakers, 'initialize circuit breakers');
    await executeStartupTask(testSupabaseAuth, 'test Supabase authentication');

    logger.info('👍 All startup checks passed. Application initialization process completed.');
  } catch (error) {
    // The error is already logged by executeStartupTask with details
    // This catch block is for the overall failure message and to stop further execution.
    logger.error('❌ Critical application startup task failed. Application will not start.');
    // In a real app, logger.fatal might be used here.
    // Re-throw if app.ts needs to handle process.exit, or handle it here.
    throw error; // Propagate to app.ts which handles process.exit
  }
};
