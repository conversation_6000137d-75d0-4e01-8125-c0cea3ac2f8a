// ============================================================================
// WorkHub Phase 1.5: Session Management & Invalidation
// Implements comprehensive session tracking with Redis integration
// File: backend/src/services/sessionManager.ts
// ============================================================================

import { v4 as uuidv4 } from 'uuid';
import logger from '../utils/logger.js';
import { logAuditEvent } from '../utils/auditLogger.js';
import { getRedisClient, isRedisAvailable } from './redis.service.js';
import type { Redis } from 'ioredis';

// SRP: Session data interface
interface SessionData {
  sessionId: string;
  userId: string;
  userRole: string;
  employeeId?: string;
  ipAddress: string;
  userAgent: string;
  createdAt: Date;
  lastActivity: Date;
  expiresAt: Date;
  isActive: boolean;
}

// SRP: Session configuration
interface SessionConfig {
  defaultTimeout: number; // in seconds
  maxConcurrentSessions: number;
  cleanupInterval: number; // in seconds
  redisKeyPrefix: string;
}

// SRP: Redis session storage
class SessionStorage {
  private config: SessionConfig;

  constructor(config: SessionConfig) {
    this.config = config;
  }

  private getSessionKey(sessionId: string): string {
    return `${this.config.redisKeyPrefix}:session:${sessionId}`;
  }

  private getUserSessionsKey(userId: string): string {
    return `${this.config.redisKeyPrefix}:user:${userId}:sessions`;
  }

  async storeSession(sessionData: SessionData): Promise<void> {
    const redis = getRedisClient();
    if (!redis) {
      throw new Error('Redis not available for session storage');
    }

    const sessionKey = this.getSessionKey(sessionData.sessionId);
    const userSessionsKey = this.getUserSessionsKey(sessionData.userId);
    
    const ttl = Math.floor((sessionData.expiresAt.getTime() - Date.now()) / 1000);
    
    await Promise.all([
      redis.setex(sessionKey, ttl, JSON.stringify(sessionData)),
      redis.sadd(userSessionsKey, sessionData.sessionId),
      redis.expire(userSessionsKey, ttl)
    ]);
  }

  async getSession(sessionId: string): Promise<SessionData | null> {
    const redis = getRedisClient();
    if (!redis) {
      return null;
    }

    const sessionKey = this.getSessionKey(sessionId);
    const sessionJson = await redis.get(sessionKey);
    
    if (!sessionJson) {
      return null;
    }
    
    try {
      const sessionData = JSON.parse(sessionJson);
      return {
        ...sessionData,
        createdAt: new Date(sessionData.createdAt),
        lastActivity: new Date(sessionData.lastActivity),
        expiresAt: new Date(sessionData.expiresAt)
      };
    } catch (error) {
      logger.error('Failed to parse session data', { sessionId, error });
      return null;
    }
  }

  async updateLastActivity(sessionId: string): Promise<void> {
    const session = await this.getSession(sessionId);
    if (!session) return;

    session.lastActivity = new Date();
    await this.storeSession(session);
  }

  async removeSession(sessionId: string): Promise<void> {
    const redis = getRedisClient();
    if (!redis) return;

    const session = await this.getSession(sessionId);
    if (!session) return;

    const sessionKey = this.getSessionKey(sessionId);
    const userSessionsKey = this.getUserSessionsKey(session.userId);
    
    await Promise.all([
      redis.del(sessionKey),
      redis.srem(userSessionsKey, sessionId)
    ]);
  }

  async getUserSessions(userId: string): Promise<SessionData[]> {
    const redis = getRedisClient();
    if (!redis) return [];

    const userSessionsKey = this.getUserSessionsKey(userId);
    const sessionIds = await redis.smembers(userSessionsKey);
    
    const sessions: SessionData[] = [];
    for (const sessionId of sessionIds) {
      const session = await this.getSession(sessionId);
      if (session && session.isActive) {
        sessions.push(session);
      }
    }
    
    return sessions;
  }

  async removeAllUserSessions(userId: string): Promise<void> {
    const sessions = await this.getUserSessions(userId);
    
    for (const session of sessions) {
      await this.removeSession(session.sessionId);
    }
  }
}

// SRP: Session validation and management
class SessionValidator {
  private storage: SessionStorage;
  private config: SessionConfig;

  constructor(storage: SessionStorage, config: SessionConfig) {
    this.storage = storage;
    this.config = config;
  }

  async validateSession(sessionId: string): Promise<SessionData | null> {
    const session = await this.storage.getSession(sessionId);
    
    if (!session) {
      return null;
    }

    // Check if session is expired
    if (session.expiresAt < new Date()) {
      await this.storage.removeSession(sessionId);
      return null;
    }

    // Check if session is active
    if (!session.isActive) {
      return null;
    }

    // Update last activity
    await this.storage.updateLastActivity(sessionId);
    
    return session;
  }

  async enforceConcurrentSessionLimit(userId: string): Promise<void> {
    const sessions = await this.storage.getUserSessions(userId);
    
    if (sessions.length >= this.config.maxConcurrentSessions) {
      // Remove oldest sessions
      const sortedSessions = sessions.sort((a, b) => 
        a.lastActivity.getTime() - b.lastActivity.getTime()
      );
      
      const sessionsToRemove = sortedSessions.slice(
        0, 
        sessions.length - this.config.maxConcurrentSessions + 1
      );
      
      for (const session of sessionsToRemove) {
        await this.storage.removeSession(session.sessionId);
        
        logAuditEvent({
          eventType: 'SESSION',
          action: 'SESSION_LIMIT_EXCEEDED',
          userId: session.userId,
          userRole: session.userRole,
          outcome: 'SUCCESS',
          message: 'Session removed due to concurrent session limit',
          details: { 
            removedSessionId: session.sessionId,
            maxSessions: this.config.maxConcurrentSessions
          }
        }, {} as any);
      }
    }
  }
}

// SRP: Main session manager
export class SessionManager {
  private storage: SessionStorage;
  private validator: SessionValidator;
  private config: SessionConfig;
  private cleanupInterval: NodeJS.Timeout | null = null;

  constructor(config?: Partial<SessionConfig>) {
    this.config = {
      defaultTimeout: 1800, // 30 minutes
      maxConcurrentSessions: 5,
      cleanupInterval: 300, // 5 minutes
      redisKeyPrefix: 'workhub',
      ...config
    };

    this.storage = new SessionStorage(this.config);
    this.validator = new SessionValidator(this.storage, this.config);
    
    this.startCleanupProcess();
  }

  async createSession(
    userId: string, 
    userRole: string, 
    ipAddress: string, 
    userAgent: string,
    employeeId?: string
  ): Promise<string> {
    if (!(await isRedisAvailable())) {
      throw new Error('Session management requires Redis connection');
    }

    const sessionId = uuidv4();
    const now = new Date();
    const expiresAt = new Date(now.getTime() + this.config.defaultTimeout * 1000);

    const sessionData: SessionData = {
      sessionId,
      userId,
      userRole,
      employeeId,
      ipAddress,
      userAgent,
      createdAt: now,
      lastActivity: now,
      expiresAt,
      isActive: true
    };

    // Enforce concurrent session limit
    await this.validator.enforceConcurrentSessionLimit(userId);

    // Store new session
    await this.storage.storeSession(sessionData);

    logAuditEvent({
      eventType: 'SESSION',
      action: 'SESSION_CREATED',
      userId,
      userRole,
      outcome: 'SUCCESS',
      message: 'New session created successfully',
      details: { 
        sessionId,
        ipAddress,
        userAgent: userAgent.substring(0, 100) // Truncate for logging
      }
    }, {} as any);

    return sessionId;
  }

  async validateSession(sessionId: string): Promise<SessionData | null> {
    return this.validator.validateSession(sessionId);
  }

  async invalidateSession(sessionId: string, reason?: string): Promise<void> {
    const session = await this.storage.getSession(sessionId);
    if (!session) return;

    await this.storage.removeSession(sessionId);

    logAuditEvent({
      eventType: 'SESSION',
      action: 'SESSION_INVALIDATED',
      userId: session.userId,
      userRole: session.userRole,
      outcome: 'SUCCESS',
      message: 'Session invalidated',
      details: { 
        sessionId,
        reason: reason || 'Manual invalidation'
      }
    }, {} as any);
  }

  async invalidateAllUserSessions(userId: string, reason?: string): Promise<void> {
    const sessions = await this.storage.getUserSessions(userId);
    
    await this.storage.removeAllUserSessions(userId);

    logAuditEvent({
      eventType: 'SESSION',
      action: 'ALL_SESSIONS_INVALIDATED',
      userId,
      outcome: 'SUCCESS',
      message: 'All user sessions invalidated',
      details: { 
        sessionCount: sessions.length,
        reason: reason || 'Manual invalidation'
      }
    }, {} as any);
  }

  async getUserSessions(userId: string): Promise<SessionData[]> {
    return this.storage.getUserSessions(userId);
  }

  private startCleanupProcess(): void {
    this.cleanupInterval = setInterval(async () => {
      try {
        await this.cleanupExpiredSessions();
      } catch (error) {
        logger.error('Session cleanup error', { error });
      }
    }, this.config.cleanupInterval * 1000);
  }

  private async cleanupExpiredSessions(): Promise<void> {
    // Redis TTL handles most cleanup automatically
    logger.debug('Session cleanup process executed');
  }

  async shutdown(): Promise<void> {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
  }
}

// Export singleton instance
let sessionManager: SessionManager | null = null;

export const initializeSessionManager = (config?: Partial<SessionConfig>): SessionManager => {
  if (!sessionManager) {
    sessionManager = new SessionManager(config);
  }
  return sessionManager;
};

export const getSessionManager = (): SessionManager => {
  if (!sessionManager) {
    throw new Error('SessionManager not initialized. Call initializeSessionManager first.');
  }
  return sessionManager;
};
