/**
 * Enhanced Entity Form Component with Generic Toast Service Integration
 *
 * This component demonstrates how to create forms that work with any entity type
 * and automatically provide appropriate toast notifications using the generic
 * toast service system.
 */

'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import React from 'react';
import {
  type DefaultValues,
  type FieldValues,
  type SubmitHandler,
  useForm,
} from 'react-hook-form';
import { type ZodSchema } from 'zod';

import { Form } from '@/components/ui/form';
import {
  type EntityToastConfig,
  type GenericEntityToastService,
  createSimpleEntityToastService,
} from '@/lib/services/toastService';
import { useEntityFormToast } from '@/hooks/forms/useFormToast';

// Generic form props that work with any entity type
export interface EntityFormProps<T extends FieldValues, E = any> {
  children: React.ReactNode;
  defaultValues?: DefaultValues<T>;
  onSubmit: (data: T) => Promise<E>; // Returns the created/updated entity
  schema: ZodSchema<T>;

  // Entity configuration for toast messages
  entityConfig?: EntityToastConfig<E>;
  entityService?: GenericEntityToastService<E>;

  // Optional simple entity configuration
  entityName?: string;
  getEntityDisplayName?: (entity: E) => string;

  // Form behavior options
  showSuccessToast?: boolean;
  showErrorToast?: boolean;
  onSuccess?: (entity: E) => void;
  onError?: (error: Error) => void;

  // Custom messages
  successTitle?: string;
  successDescription?: string | ((entity: E) => string);
  errorTitle?: string;
  errorDescription?: string | ((error: Error) => string);
}

/**
 * Generic Entity Form Component
 *
 * Supports three levels of toast integration:
 * 1. Predefined entity services (employee, vehicle, task, delegation)
 * 2. Custom entity configuration
 * 3. Simple entity configuration with just name and display function
 * 4. Fallback to generic success/error messages
 */
export const EntityForm = <T extends FieldValues, E = any>({
  children,
  defaultValues,
  onSubmit,
  schema,
  entityConfig,
  entityService,
  entityName,
  getEntityDisplayName,
  showSuccessToast = true,
  showErrorToast = true,
  onSuccess,
  onError,
  successTitle,
  successDescription,
  errorTitle,
  errorDescription,
}: EntityFormProps<T, E>) => {
  // Create entity service if needed
  const effectiveEntityService = React.useMemo(() => {
    if (entityService) return entityService;
    if (entityConfig) return undefined; // Will be handled by useEntityFormToast
    if (entityName && getEntityDisplayName) {
      return createSimpleEntityToastService(entityName, getEntityDisplayName);
    }
    return undefined;
  }, [entityService, entityConfig, entityName, getEntityDisplayName]);

  // Get toast functions
  const {
    showEntityCreated,
    showEntityCreationError,
    showFormSuccess,
    showFormError,
  } = useEntityFormToast(entityConfig, effectiveEntityService);

  const form = useForm<T>({
    ...(defaultValues && { defaultValues }),
    resolver: zodResolver(schema),
  });

  const handleSubmit: SubmitHandler<T> = async data => {
    try {
      const entity = await onSubmit(data);

      if (showSuccessToast) {
        if (effectiveEntityService || entityConfig) {
          // Use entity-specific toast
          showEntityCreated(entity);
        } else if (successTitle || successDescription) {
          // Use custom success message
          const title = successTitle || 'Success';
          const description =
            typeof successDescription === 'function'
              ? successDescription(entity)
              : successDescription || 'Operation completed successfully';
          showFormSuccess({
            successTitle: title,
            successDescription: description,
          });
        } else {
          // Use default success message
          showFormSuccess();
        }
      }

      // Call success callback if provided
      onSuccess?.(entity);
    } catch (error: unknown) {
      console.error('Form submission error:', error);

      const errorObj =
        error instanceof Error
          ? error
          : new Error('An unexpected error occurred');

      if (showErrorToast) {
        if (effectiveEntityService || entityConfig) {
          // Use entity-specific error toast
          showEntityCreationError(errorObj);
        } else if (errorTitle || errorDescription) {
          // Use custom error message
          const title = errorTitle || 'Error';
          const description =
            typeof errorDescription === 'function'
              ? errorDescription(errorObj)
              : errorDescription || errorObj.message;
          showFormError(errorObj, {
            errorTitle: title,
            errorDescription: description,
          });
        } else {
          // Use default error message
          showFormError(errorObj);
        }
      }

      // Call error callback if provided
      onError?.(errorObj);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)}>{children}</form>
    </Form>
  );
};

// =============================================================================
// PREDEFINED ENTITY FORM COMPONENTS
// =============================================================================

/**
 * Employee Form with predefined toast messages
 */
export const EmployeeForm = <T extends FieldValues>(
  props: Omit<
    EntityFormProps<T, { name: string }>,
    'entityName' | 'getEntityDisplayName'
  >
) => (
  <EntityForm
    {...props}
    entityName="Employee"
    getEntityDisplayName={employee => employee.name}
  />
);

/**
 * Vehicle Form with predefined toast messages
 */
export const VehicleForm = <T extends FieldValues>(
  props: Omit<
    EntityFormProps<T, { make: string; model: string }>,
    'entityName' | 'getEntityDisplayName'
  >
) => (
  <EntityForm
    {...props}
    entityName="Vehicle"
    getEntityDisplayName={vehicle => `${vehicle.make} ${vehicle.model}`}
  />
);

/**
 * Task Form with predefined toast messages
 */
export const TaskForm = <T extends FieldValues>(
  props: Omit<
    EntityFormProps<T, { title?: string; name?: string }>,
    'entityName' | 'getEntityDisplayName'
  >
) => (
  <EntityForm
    {...props}
    entityName="Task"
    getEntityDisplayName={task => task.title || task.name || 'Task'}
  />
);

/**
 * Delegation Form with predefined toast messages
 */
export const DelegationForm = <T extends FieldValues>(
  props: Omit<
    EntityFormProps<T, { event?: string; location?: string }>,
    'entityName' | 'getEntityDisplayName'
  >
) => (
  <EntityForm
    {...props}
    entityName="Delegation"
    getEntityDisplayName={delegation =>
      delegation.event || delegation.location || 'Delegation'
    }
  />
);
