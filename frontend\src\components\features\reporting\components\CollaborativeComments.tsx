// frontend/src/components/features/reporting/components/CollaborativeComments.tsx

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import {
  MessageSquare,
  Plus,
  Edit2,
  Trash2,
  Users,
  Wifi,
  WifiOff,
} from 'lucide-react';
import {
  useCollaborativeFeatures,
  DashboardComment,
} from '../hooks/useCollaborativeFeatures';
import { formatDistanceToNow } from 'date-fns';
import { useAuthContext } from '@/contexts/AuthContext';

interface CollaborativeCommentsProps {
  dashboardId: string;
  className?: string;
}

/**
 * Collaborative Comments Component
 *
 * ENHANCED: Single Responsibility - Handles comment display and interaction
 * Integrates with existing collaborative features hook
 * Follows established component patterns from existing codebase
 */
export const CollaborativeComments: React.FC<CollaborativeCommentsProps> = ({
  dashboardId,
  className = '',
}) => {
  const { user } = useAuthContext();
  const {
    comments,
    activeUsers,
    isConnected,
    addComment,
    updateComment,
    deleteComment,
  } = useCollaborativeFeatures(dashboardId);

  const [newComment, setNewComment] = useState('');
  const [editingComment, setEditingComment] = useState<string | null>(null);
  const [editContent, setEditContent] = useState('');

  // Check if current user can edit/delete comments
  const canEditComment = (comment: DashboardComment) => {
    return user?.id === comment.userId || user?.user_metadata?.role === 'admin';
  };

  const handleAddComment = () => {
    if (newComment.trim()) {
      addComment(newComment.trim());
      setNewComment('');
    }
  };

  const handleEditComment = (comment: DashboardComment) => {
    setEditingComment(comment.id);
    setEditContent(comment.content);
  };

  const handleSaveEdit = () => {
    if (editingComment && editContent.trim()) {
      updateComment(editingComment, editContent.trim());
      setEditingComment(null);
      setEditContent('');
    }
  };

  const handleCancelEdit = () => {
    setEditingComment(null);
    setEditContent('');
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MessageSquare className="h-5 w-5" />
          Comments
          <Badge
            variant={isConnected ? 'default' : 'secondary'}
            className="flex items-center gap-1"
          >
            {isConnected ? (
              <Wifi className="h-3 w-3" />
            ) : (
              <WifiOff className="h-3 w-3" />
            )}
            {isConnected ? 'Live' : 'Offline'}
          </Badge>
        </CardTitle>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Active Users */}
        {activeUsers.length > 0 && (
          <div className="flex items-center gap-2">
            <Users className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm text-muted-foreground">Active users:</span>
            <div className="flex -space-x-2">
              {activeUsers.map(user => (
                <Avatar
                  key={user.userId}
                  className="h-6 w-6 border-2 border-background"
                >
                  <AvatarImage src={user.avatar} />
                  <AvatarFallback className="text-xs">
                    {user.userName.charAt(0).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
              ))}
            </div>
          </div>
        )}

        {/* Add Comment */}
        <div className="space-y-2">
          <Textarea
            placeholder="Add a comment..."
            value={newComment}
            onChange={e => setNewComment(e.target.value)}
            className="min-h-[80px]"
            disabled={!isConnected}
          />
          <Button
            onClick={handleAddComment}
            disabled={!newComment.trim() || !isConnected}
            size="sm"
            className="w-full"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Comment
          </Button>
        </div>

        {/* Comments List */}
        <div className="space-y-3 max-h-96 overflow-y-auto">
          {comments.map(comment => (
            <div key={comment.id} className="border rounded-lg p-3 space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Avatar className="h-6 w-6">
                    <AvatarFallback className="text-xs">
                      {comment.userName.charAt(0).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  <span className="text-sm font-medium">
                    {comment.userName}
                  </span>
                  <span className="text-xs text-muted-foreground">
                    {formatDistanceToNow(new Date(comment.createdAt), {
                      addSuffix: true,
                    })}
                  </span>
                </div>

                {canEditComment(comment) && (
                  <div className="flex items-center gap-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleEditComment(comment)}
                      className="h-6 w-6 p-0"
                      disabled={!isConnected}
                      title="Edit comment"
                    >
                      <Edit2 className="h-3 w-3" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => deleteComment(comment.id)}
                      className="h-6 w-6 p-0 text-destructive"
                      disabled={!isConnected}
                      title="Delete comment"
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                )}
              </div>

              {editingComment === comment.id ? (
                <div className="space-y-2">
                  <Textarea
                    value={editContent}
                    onChange={e => setEditContent(e.target.value)}
                    className="min-h-[60px]"
                  />
                  <div className="flex gap-2">
                    <Button size="sm" onClick={handleSaveEdit}>
                      Save
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={handleCancelEdit}
                    >
                      Cancel
                    </Button>
                  </div>
                </div>
              ) : (
                <p className="text-sm">{comment.content}</p>
              )}
            </div>
          ))}

          {comments.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              <MessageSquare className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p>No comments yet. Be the first to add one!</p>
            </div>
          )}
        </div>

        {/* Connection Status */}
        {!isConnected && (
          <div className="text-center p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <p className="text-sm text-yellow-800">
              Real-time features are currently offline. Comments will sync when
              connection is restored.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default CollaborativeComments;
