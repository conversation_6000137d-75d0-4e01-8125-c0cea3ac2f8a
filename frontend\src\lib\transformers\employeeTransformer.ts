/**
 * @file Data transformer for Employee domain models.
 * @module transformers/employeeTransformer
 */

import type {
  CreateEmployeeRequest,
  EmployeeApiResponse,
  UpdateEmployeeRequest,
} from '../types/apiContracts'; // Changed import path
import type {
  CreateEmployeeData,
  DriverAvailabilityPrisma,
  Employee,
  EmployeeRolePrisma,
  EmployeeStatusPrisma,
} from '../types/domain';

import { formatDateForApi } from '../utils/dateUtils';

/**
 * Transforms employee data between API response formats and frontend domain models.
 */
export const EmployeeTransformer = {
  /**
   * Converts an API Employee response into a frontend Employee domain model.
   * @param apiData - The data received from the API.
   * @returns The Employee domain model.
   */
  fromApi(apiData: EmployeeApiResponse): Employee {
    return {
      availability: apiData.availability as DriverAvailabilityPrisma,
      contactEmail: apiData.contactEmail ?? null,
      contactInfo: apiData.contactInfo,
      contactMobile: apiData.contactMobile ?? null,
      contactPhone: apiData.contactPhone ?? null,
      createdAt: apiData.createdAt,
      currentLocation: apiData.currentLocation ?? null,
      department: apiData.department ?? null,
      employeeId: apiData.employeeId,
      fullName: apiData.fullName ?? null, // Provide fallback for fullName
      generalAssignments: apiData.generalAssignments,
      hireDate: apiData.hireDate ?? null,
      id: apiData.id,
      name: apiData.name || '', // Provide fallback for name
      notes: apiData.notes ?? null,
      position: apiData.position ?? null,
      profileImageUrl: apiData.profileImageUrl ?? null,
      role: apiData.role as EmployeeRolePrisma,
      shiftSchedule: apiData.shiftSchedule ?? null,
      skills: apiData.skills,
      status: apiData.status as EmployeeStatusPrisma,
      updatedAt: apiData.updatedAt,
      workingHours: apiData.workingHours ?? null,
    };
  },

  /**
   * Converts frontend data for creating an employee into an API request payload.
   * @param employeeData - The data from the frontend for creating a new employee.
   * @returns The transformed CreateEmployeeRequest payload.
   */
  toCreateRequest(employeeData: CreateEmployeeData): CreateEmployeeRequest {
    // Convert form data to API request format

    const request: CreateEmployeeRequest = {
      availability: employeeData.availability as string, // Type assertion
      contactEmail: employeeData.contactEmail?.trim() ?? null,
      contactInfo: employeeData.contactInfo.trim(),
      contactMobile: employeeData.contactMobile?.trim() ?? null,
      contactPhone: employeeData.contactPhone?.trim() ?? null,
      currentLocation: employeeData.currentLocation ?? null,
      department: employeeData.department?.trim() ?? null,
      employeeId: employeeData.employeeId,
      fullName: employeeData.fullName?.trim() ?? null,
      generalAssignments: employeeData.generalAssignments,
      hireDate: employeeData.hireDate
        ? formatDateForApi(employeeData.hireDate)
        : null, // Convert to ISO format
      name: employeeData.name.trim(),

      notes: employeeData.notes?.trim() ?? null,
      position: employeeData.position?.trim() ?? null,
      profileImageUrl: employeeData.profileImageUrl?.trim() ?? null,
      role: employeeData.role as string, // Type assertion
      shiftSchedule: employeeData.shiftSchedule ?? null,
      skills: employeeData.skills,
      status: employeeData.status as string, // Type assertion
      workingHours: employeeData.workingHours ?? null,
    };

    return request;
  },

  /**
   * Converts partial frontend employee data into an API request payload for updating.
   * @param employeeData - The partial data from the frontend for updating an employee.
   * @returns The transformed UpdateEmployeeRequest payload.
   */
  toUpdateRequest(
    employeeData: Partial<CreateEmployeeData>
  ): UpdateEmployeeRequest {
    // Convert partial form data to API request format

    const request: UpdateEmployeeRequest = {};

    if (employeeData.name !== undefined)
      request.name = employeeData.name?.trim() ?? null;
    if (employeeData.employeeId !== undefined)
      request.employeeId = employeeData.employeeId;
    if (employeeData.contactInfo !== undefined)
      request.contactInfo = employeeData.contactInfo?.trim() ?? null;
    if (employeeData.contactEmail !== undefined)
      request.contactEmail = employeeData.contactEmail?.trim() ?? null;
    if (employeeData.contactMobile !== undefined)
      request.contactMobile = employeeData.contactMobile?.trim() ?? null;
    if (employeeData.contactPhone !== undefined)
      request.contactPhone = employeeData.contactPhone?.trim() ?? null;
    if (employeeData.position !== undefined)
      request.position = employeeData.position?.trim() ?? null;
    if (employeeData.department !== undefined)
      request.department = employeeData.department?.trim() ?? null;
    if (employeeData.hireDate !== undefined)
      request.hireDate = employeeData.hireDate
        ? formatDateForApi(employeeData.hireDate)
        : null; // Convert to ISO format
    if (employeeData.fullName !== undefined)
      request.fullName = employeeData.fullName?.trim() ?? null;
    if (employeeData.role !== undefined)
      request.role = employeeData.role as string; // Type assertion
    if (employeeData.status !== undefined)
      request.status = employeeData.status as string; // Type assertion

    if (employeeData.availability !== undefined)
      request.availability = employeeData.availability as string; // Type assertion
    if (employeeData.currentLocation !== undefined)
      request.currentLocation = employeeData.currentLocation;
    if (employeeData.workingHours !== undefined)
      request.workingHours = employeeData.workingHours;
    if (employeeData.generalAssignments !== undefined)
      request.generalAssignments = employeeData.generalAssignments;
    if (employeeData.notes !== undefined)
      request.notes = employeeData.notes?.trim() ?? null;
    if (employeeData.profileImageUrl !== undefined)
      request.profileImageUrl = employeeData.profileImageUrl?.trim() ?? null;
    if (employeeData.shiftSchedule !== undefined)
      request.shiftSchedule = employeeData.shiftSchedule;
    if (employeeData.skills !== undefined) request.skills = employeeData.skills;

    return request;
  },
};
