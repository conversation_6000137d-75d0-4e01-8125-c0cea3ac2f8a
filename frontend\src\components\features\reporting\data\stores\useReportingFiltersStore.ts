// frontend/src/components/features/reporting/data/stores/useReportingFiltersStore.ts

import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import { devtools } from 'zustand/middleware';
import { subscribeWithSelector } from 'zustand/middleware';
import { useShallow } from 'zustand/react/shallow';
import { shallow } from 'zustand/shallow';

import type {
  DelegationStatusPrisma,
  ReportingFilters,
  TaskPriorityPrisma,
  TaskStatusPrisma,
} from '../types/reporting';
import type {
  ServiceStatusPrisma,
  ServiceTypePrisma,
} from '../types/vehicleService';

import { ServicePriorityPrisma } from '../types/vehicleService';

/**
 * Reporting Filters Store Actions Interface
 *
 * Follows SRP by grouping related actions together
 */
interface ReportingFiltersActions {
  applyFilters: () => void;
  // Presets
  applyPreset: (presetName: string) => void;
  clearValidationErrors: () => void;
  deletePreset: (name: string) => void;
  getPresets: () => Record<string, ReportingFilters>;

  resetFilters: () => void;
  revertChanges: () => void;
  saveAsPreset: (name: string) => void;
  setCostRange: (min: number, max: number) => void;

  // Filter value setters
  setDateRange: (from: Date, to: Date) => void;
  setEmployees: (employees: number[]) => void;
  setFilterPanelOpen: (open: boolean) => void;

  // Bulk operations
  setFilters: (filters: Partial<ReportingFilters>) => void;
  setIncludeServiceHistory: (include: boolean) => void;
  setIncludeTaskData: (include: boolean) => void;
  setLocations: (locations: string[]) => void;

  setServiceStatus: (status: ServiceStatusPrisma[]) => void;
  // ENHANCED: Service history filter setters
  setServiceTypes: (types: ServiceTypePrisma[]) => void;

  setStatus: (status: DelegationStatusPrisma[]) => void;
  setTaskPriorities: (priorities: TaskPriorityPrisma[]) => void;

  // ENHANCED: Task filter setters
  setTaskStatus: (status: TaskStatusPrisma[]) => void;
  setVehicles: (vehicles: number[]) => void;
  // UI state management
  toggleFilterPanel: () => void;
  // Validation
  validateFilters: () => boolean;
}

/**
 * Reporting Filters Store State Interface
 *
 * Follows Interface Segregation Principle by defining focused state structure
 */
interface ReportingFiltersState {
  // Current filter values
  filters: ReportingFilters;

  hasUnsavedChanges: boolean;
  // UI state for filter management
  isFilterPanelOpen: boolean;
  isValid: boolean;

  lastAppliedFilters: ReportingFilters;
  // Validation state
  validationErrors: Record<string, string>;
}

/**
 * Complete store type combining state and actions
 */
type ReportingFiltersStore = ReportingFiltersActions & ReportingFiltersState;

/**
 * Default filter values following business requirements
 * FIXED: Create stable default filters to prevent infinite re-renders
 * Use a function that creates fresh dates only when needed
 */
const getDefaultFilters = (): ReportingFilters => {
  const now = new Date();
  const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

  return {
    costRange: { max: 10_000, min: 0 },
    dateRange: {
      from: thirtyDaysAgo,
      to: now,
    },
    employees: [],
    includeServiceHistory: false,
    includeTaskData: false,

    locations: [],
    serviceStatus: [],
    // ENHANCED: Default service history values
    serviceTypes: [],
    status: [], // All statuses by default
    vehicles: [],
  };
};

/**
 * FIXED: Create stable default filters object to prevent infinite re-renders
 * Only create new dates when absolutely necessary
 */
const createStableDefaultFilters = (): ReportingFilters => {
  const now = new Date();
  const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

  return {
    costRange: { max: 10_000, min: 0 },
    dateRange: {
      from: thirtyDaysAgo,
      to: now,
    },
    employees: [],
    includeServiceHistory: false,
    includeTaskData: false,
    locations: [],
    serviceStatus: [],
    serviceTypes: [],
    status: [],
    vehicles: [],
  };
};

// FIXED: Create stable default filters instance
const STABLE_DEFAULT_FILTERS = createStableDefaultFilters();

/**
 * Filter validation logic
 *
 * @param filters - Filters to validate
 * @returns Validation errors object
 */
const validateFiltersLogic = (
  filters: ReportingFilters
): Record<string, string> => {
  const errors: Record<string, string> = {};

  // Date range validation
  if (filters.dateRange) {
    if (filters.dateRange.from > filters.dateRange.to) {
      errors.dateRange = 'Start date must be before end date';
    }

    const daysDiff =
      Math.abs(
        filters.dateRange.to.getTime() - filters.dateRange.from.getTime()
      ) /
      (1000 * 60 * 60 * 24);
    if (daysDiff > 365) {
      errors.dateRange = 'Date range cannot exceed 365 days';
    }
  }

  // Status validation
  if (filters.status.length > 10) {
    errors.status = 'Too many statuses selected (maximum 10)';
  }

  // Location validation
  if (filters.locations.length > 50) {
    errors.locations = 'Too many locations selected (maximum 50)';
  }

  // Employee validation
  if (filters.employees.length > 100) {
    errors.employees = 'Too many employees selected (maximum 100)';
  }

  // Vehicle validation
  if (filters.vehicles.length > 100) {
    errors.vehicles = 'Too many vehicles selected (maximum 100)';
  }

  // ENHANCED: Service history validation
  if (filters.serviceTypes && filters.serviceTypes.length > 20) {
    errors.serviceTypes = 'Too many service types selected (maximum 20)';
  }

  if (filters.serviceStatus && filters.serviceStatus.length > 10) {
    errors.serviceStatus = 'Too many service statuses selected (maximum 10)';
  }

  if (filters.costRange) {
    if (filters.costRange.min < 0) {
      errors.costRange = 'Minimum cost cannot be negative';
    }
    if (filters.costRange.min >= filters.costRange.max) {
      errors.costRange = 'Minimum cost must be less than maximum cost';
    }
    if (filters.costRange.max > 1_000_000) {
      errors.costRange = 'Maximum cost cannot exceed $1,000,000';
    }
  }

  return errors;
};

/**
 * Zustand store for managing reporting filters state
 *
 * Follows SRP: Only responsible for filter state management
 * Uses middleware for persistence, devtools, and subscriptions
 *
 * Features:
 * - Persistent storage with localStorage
 * - URL synchronization (handled by separate hook)
 * - Validation with error tracking
 * - Preset management
 * - Optimistic updates with revert capability
 */
export const useReportingFiltersStore = create<ReportingFiltersStore>()(
  devtools(
    subscribeWithSelector(
      persist(
        (set, get) => ({
          applyFilters: () => {
            const { filters, isValid } = get();
            if (isValid) {
              set({
                hasUnsavedChanges: false,
                lastAppliedFilters: { ...filters },
              });
            }
          },
          // Preset management
          applyPreset: (presetName: string) => {
            try {
              const stored = localStorage.getItem('reporting-filter-presets');
              const presets = stored ? JSON.parse(stored) : {};
              const preset = presets[presetName];

              if (preset) {
                set(state => ({
                  filters: { ...preset },
                  hasUnsavedChanges: true,
                  lastAppliedFilters: state.lastAppliedFilters,
                }));
              }
            } catch (error) {
              console.error('Failed to apply preset:', error);
            }
          },
          clearValidationErrors: () => {
            set({
              isValid: true,
              validationErrors: {},
            });
          },
          deletePreset: (name: string) => {
            try {
              const stored = localStorage.getItem('reporting-filter-presets');
              const presets = stored ? JSON.parse(stored) : {};
              delete presets[name];
              localStorage.setItem(
                'reporting-filter-presets',
                JSON.stringify(presets)
              );
            } catch (error) {
              console.error('Failed to delete preset:', error);
            }
          },
          // Initial state
          filters: getDefaultFilters(),
          getPresets: () => {
            try {
              const stored = localStorage.getItem('reporting-filter-presets');
              return stored ? JSON.parse(stored) : {};
            } catch {
              return {};
            }
          },

          hasUnsavedChanges: false,

          isFilterPanelOpen: false,

          isValid: true,

          lastAppliedFilters: getDefaultFilters(),

          resetFilters: () => {
            set({
              filters: getDefaultFilters(),
              hasUnsavedChanges: true,
              isValid: true,
              validationErrors: {},
            });
          },

          revertChanges: () => {
            const { lastAppliedFilters } = get();
            set({
              filters: { ...lastAppliedFilters },
              hasUnsavedChanges: false,
              isValid: true,
              validationErrors: {},
            });
          },

          saveAsPreset: (name: string) => {
            try {
              const { filters } = get();
              const stored = localStorage.getItem('reporting-filter-presets');
              const presets = stored ? JSON.parse(stored) : {};
              presets[name] = { ...filters };
              localStorage.setItem(
                'reporting-filter-presets',
                JSON.stringify(presets)
              );
            } catch (error) {
              console.error('Failed to save preset:', error);
            }
          },

          setCostRange: (min: number, max: number) => {
            set(state => {
              const newFilters = { ...state.filters, costRange: { max, min } };
              const errors = validateFiltersLogic(newFilters);

              return {
                filters: newFilters,
                hasUnsavedChanges: true,
                isValid: Object.keys(errors).length === 0,
                validationErrors: errors,
              };
            });
          },

          // Filter value setters
          setDateRange: (from: Date, to: Date) => {
            set(state => {
              const newFilters = {
                ...state.filters,
                dateRange: { from, to },
              };
              const errors = validateFiltersLogic(newFilters);

              return {
                filters: newFilters,
                hasUnsavedChanges: true,
                isValid: Object.keys(errors).length === 0,
                validationErrors: errors,
              };
            });
          },

          setEmployees: (employees: number[]) => {
            set(state => {
              const newFilters = { ...state.filters, employees };
              const errors = validateFiltersLogic(newFilters);

              return {
                filters: newFilters,
                hasUnsavedChanges: true,
                isValid: Object.keys(errors).length === 0,
                validationErrors: errors,
              };
            });
          },

          setFilterPanelOpen: (open: boolean) => {
            set({ isFilterPanelOpen: open });
          },

          // Bulk operations
          setFilters: (partialFilters: Partial<ReportingFilters>) => {
            set(state => {
              const newFilters = { ...state.filters, ...partialFilters };
              const errors = validateFiltersLogic(newFilters);

              return {
                filters: newFilters,
                hasUnsavedChanges: true,
                isValid: Object.keys(errors).length === 0,
                validationErrors: errors,
              };
            });
          },

          setIncludeServiceHistory: (includeServiceHistory: boolean) => {
            set(state => {
              const newFilters = { ...state.filters, includeServiceHistory };
              const errors = validateFiltersLogic(newFilters);

              return {
                filters: newFilters,
                hasUnsavedChanges: true,
                isValid: Object.keys(errors).length === 0,
                validationErrors: errors,
              };
            });
          },

          setIncludeTaskData: (includeTaskData: boolean) => {
            set(state => {
              const newFilters = { ...state.filters, includeTaskData };
              const errors = validateFiltersLogic(newFilters);

              return {
                filters: newFilters,
                hasUnsavedChanges: true,
                isValid: Object.keys(errors).length === 0,
                validationErrors: errors,
              };
            });
          },

          setLocations: (locations: string[]) => {
            set(state => {
              const newFilters = { ...state.filters, locations };
              const errors = validateFiltersLogic(newFilters);

              return {
                filters: newFilters,
                hasUnsavedChanges: true,
                isValid: Object.keys(errors).length === 0,
                validationErrors: errors,
              };
            });
          },

          setServiceStatus: (serviceStatus: ServiceStatusPrisma[]) => {
            set(state => {
              const newFilters = { ...state.filters, serviceStatus };
              const errors = validateFiltersLogic(newFilters);

              return {
                filters: newFilters,
                hasUnsavedChanges: true,
                isValid: Object.keys(errors).length === 0,
                validationErrors: errors,
              };
            });
          },

          // ENHANCED: Service history filter setters
          setServiceTypes: (serviceTypes: ServiceTypePrisma[]) => {
            set(state => {
              const newFilters = { ...state.filters, serviceTypes };
              const errors = validateFiltersLogic(newFilters);

              return {
                filters: newFilters,
                hasUnsavedChanges: true,
                isValid: Object.keys(errors).length === 0,
                validationErrors: errors,
              };
            });
          },

          setStatus: (status: DelegationStatusPrisma[]) => {
            set(state => {
              const newFilters = { ...state.filters, status };
              const errors = validateFiltersLogic(newFilters);

              return {
                filters: newFilters,
                hasUnsavedChanges: true,
                isValid: Object.keys(errors).length === 0,
                validationErrors: errors,
              };
            });
          },

          setTaskPriorities: (taskPriorities: TaskPriorityPrisma[]) => {
            set(state => {
              const newFilters = { ...state.filters, taskPriorities };
              const errors = validateFiltersLogic(newFilters);

              return {
                filters: newFilters,
                hasUnsavedChanges: true,
                isValid: Object.keys(errors).length === 0,
                validationErrors: errors,
              };
            });
          },

          // ENHANCED: Task filter setters
          setTaskStatus: (taskStatus: TaskStatusPrisma[]) => {
            set(state => {
              const newFilters = { ...state.filters, taskStatus };
              const errors = validateFiltersLogic(newFilters);

              return {
                filters: newFilters,
                hasUnsavedChanges: true,
                isValid: Object.keys(errors).length === 0,
                validationErrors: errors,
              };
            });
          },

          setVehicles: (vehicles: number[]) => {
            set(state => {
              const newFilters = { ...state.filters, vehicles };
              const errors = validateFiltersLogic(newFilters);

              return {
                filters: newFilters,
                hasUnsavedChanges: true,
                isValid: Object.keys(errors).length === 0,
                validationErrors: errors,
              };
            });
          },

          // UI state management
          toggleFilterPanel: () => {
            set(state => ({
              isFilterPanelOpen: !state.isFilterPanelOpen,
            }));
          },

          // Validation
          validateFilters: () => {
            const { filters } = get();
            const errors = validateFiltersLogic(filters);
            const isValid = Object.keys(errors).length === 0;

            set({
              isValid,
              validationErrors: errors,
            });

            return isValid;
          },

          validationErrors: {},
        }),
        {
          name: 'reporting-filters-storage',
          partialize: state => ({
            // Only persist essential filter state, not UI state
            filters: state.filters,
            lastAppliedFilters: state.lastAppliedFilters,
          }),
          storage: createJSONStorage(() => localStorage),
        }
      )
    ),
    {
      name: 'reporting-filters-store',
    }
  )
);

/**
 * Selector hooks for optimized component subscriptions
 *
 * Follows DRY principle by providing reusable selectors
 * FIXED: Create stable selectors to prevent infinite re-renders
 */

// FIXED: Create stable selector functions to prevent infinite loops
const filtersSelector = (state: ReportingFiltersStore) => state.filters;
const actionsSelector = (state: ReportingFiltersStore) => ({
  applyFilters: state.applyFilters,
  resetFilters: state.resetFilters,
  revertChanges: state.revertChanges,
  setCostRange: state.setCostRange,
  setDateRange: state.setDateRange,
  setEmployees: state.setEmployees,
  setFilters: state.setFilters,
  setIncludeServiceHistory: state.setIncludeServiceHistory,
  setIncludeTaskData: state.setIncludeTaskData,
  setLocations: state.setLocations,
  setServiceStatus: state.setServiceStatus,
  setServiceTypes: state.setServiceTypes,
  setStatus: state.setStatus,
  setTaskPriorities: state.setTaskPriorities,
  setTaskStatus: state.setTaskStatus,
  setVehicles: state.setVehicles,
});

const uiSelector = (state: ReportingFiltersStore) => ({
  hasUnsavedChanges: state.hasUnsavedChanges,
  isFilterPanelOpen: state.isFilterPanelOpen,
  setFilterPanelOpen: state.setFilterPanelOpen,
  toggleFilterPanel: state.toggleFilterPanel,
});

const validationSelector = (state: ReportingFiltersStore) => ({
  clearValidationErrors: state.clearValidationErrors,
  isValid: state.isValid,
  validateFilters: state.validateFilters,
  validationErrors: state.validationErrors,
});

const presetsSelector = (state: ReportingFiltersStore) => ({
  applyPreset: state.applyPreset,
  deletePreset: state.deletePreset,
  getPresets: state.getPresets,
  saveAsPreset: state.saveAsPreset,
});

export const useReportingFilters = () =>
  useReportingFiltersStore(useShallow(filtersSelector));

export const useReportingFiltersActions = () =>
  useReportingFiltersStore(useShallow(actionsSelector));

export const useReportingFiltersUI = () =>
  useReportingFiltersStore(useShallow(uiSelector));

export const useReportingFiltersValidation = () =>
  useReportingFiltersStore(useShallow(validationSelector));

export const useReportingFiltersPresets = () =>
  useReportingFiltersStore(useShallow(presetsSelector));
