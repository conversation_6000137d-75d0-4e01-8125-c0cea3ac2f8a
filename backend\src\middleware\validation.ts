import type { NextFunction, Request, Response } from 'express';
import type { z } from 'zod';

import { createContextLogger } from '../utils/logger.js';
import HttpError from '../utils/HttpError.js';

/**
 * Middleware factory for validating request data against a Zod schema
 *
 * @param schema The Zod schema to validate against
 * @param source Where to find the data to validate (body, params, query)
 * @returns Express middleware function
 */
export const validate = (schema: z.ZodType, source: 'body' | 'params' | 'query' = 'body') => {
  return (req: Request, res: Response, next: NextFunction): void => {
    // Create context logger for this validation
    const contextLogger = createContextLogger({
      method: req.method,
      requestId: req.headers['x-request-id'] as string,
      service: 'validation-middleware',
      url: req.url,
      userId: (req as any).userId,
    });

    try {
      // Log the incoming data for debugging (debug level)
      contextLogger.debug(`Validating ${source} data`, {
        dataKeys: Object.keys(req[source] || {}),
        dataSize: JSON.stringify(req[source] || {}).length,
        source,
      });

      const result = schema.safeParse(req[source]);

      // Log the parsed/validated data for query params (debug level)
      if (source === 'query') {
        contextLogger.debug('Zod query validation result', {
          dataKeys: result.success ? Object.keys(result.data || {}) : undefined,
          errorCount: result.success ? 0 : result.error.issues.length,
          success: result.success,
        });
      }

      if (!result.success) {
        // Format the validation errors for a cleaner response
        const formattedErrors = formatZodErrors(result.error, req[source]);

        // Log detailed validation errors (warn level)
        contextLogger.warn('Validation failed', {
          errorCount: result.error.issues.length,
          errors: formattedErrors,
          receivedDataKeys: Object.keys(req[source] || {}),
          source,
        });

        // Create an HttpError and pass it to the central error handler
        const error = new HttpError('Validation failed', 400, 'VALIDATION_ERROR', {
          errors: formattedErrors,
          receivedData: sanitizeData(req[source]),
          ...(process.env.NODE_ENV !== 'production' && {
            debugInfo: {
              flattenedErrors: result.error.flatten(),
              zodErrors: result.error.issues,
            },
          }),
        });
        return next(error);
      }

      // Store the validated data into a new property on the request object
      // to avoid issues with modifying Express's built-in req.query, req.body, etc.
      (req as any).validatedData = result.data;

      // Log successful validation (debug level)
      contextLogger.debug(`Validation successful for ${source}`, {
        source,
        validatedDataKeys: Object.keys(result.data || {}),
      });

      next();
    } catch (error) {
      contextLogger.error('Unexpected error in validation middleware', {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
      });
      next(error);
    }
  };
};

/**
 * Helper function to format Zod errors into a more user-friendly structure
 */
const formatZodErrors = (error: z.ZodError, data?: any) => {
  return error.issues.map(issue => ({
    code: issue.code,
    message: issue.message,
    path: issue.path.join('.'),
    received: issue.path.length > 0 && data ? getValueAtPath(issue.path, data) : undefined,
  }));
};

/**
 * Helper to get the value at a specific path in an object
 */
const getValueAtPath = (path: (number | string)[], data: any) => {
  try {
    const value = path.reduce((obj, key) => obj[key], data);
    return typeof value === 'object' ? JSON.stringify(value) : value;
  } catch (e) {
    return undefined;
  }
};

/**
 * Sanitize data for logging/error responses by removing sensitive fields
 */
const sanitizeData = (data: any) => {
  if (!data || typeof data !== 'object') return data;

  // Create a deep copy to avoid modifying the original
  const sanitized = JSON.parse(JSON.stringify(data));

  // List of sensitive fields to redact
  const sensitiveFields = ['password', 'token', 'secret', 'apiKey', 'api_key'];

  // Recursive function to sanitize nested objects
  const sanitizeObject = (obj: any) => {
    if (!obj || typeof obj !== 'object') return;

    Object.keys(obj).forEach(key => {
      // Check if this is a sensitive field
      if (sensitiveFields.some(field => key.toLowerCase().includes(field.toLowerCase()))) {
        obj[key] = '[REDACTED]';
      } else if (typeof obj[key] === 'object') {
        // Recursively sanitize nested objects and arrays
        sanitizeObject(obj[key]);
      }
    });
  };

  sanitizeObject(sanitized);
  return sanitized;
};
