/**
 * Flight Search Service - Flight Operations Layer
 *
 * Handles flight search, validation, and formatting operations for delegation forms.
 * Abstracts flight-related business logic following SOLID principles.
 *
 * @module FlightSearchService
 */

// ============================================================================
// INTERFACES
// ============================================================================

export interface FlightData {
  callsign: string;
  arrivalAirport?: string;
  arrivalTime?: number;
  departureAirport?: string;
  departureTime?: number;
  // Additional flight data fields
  airline?: string;
  aircraft?: string;
  status?: string;
}

export interface FlightFormData {
  flightNumber: string;
  dateTime: string;
  airport: string;
  terminal?: string;
  notes?: string;
}

export interface FormattedFlight {
  displayName: string;
  formattedTime: string;
  airportName: string;
  status: string;
  details: string;
}

export interface FlightDetails {
  flightNumber: string;
  dateTime: string;
  airport: string;
  terminal?: string;
  notes?: string;
}

export interface FlightSearchOptions {
  date: string;
  type: 'arrival' | 'departure';
  limit?: number;
  includeDelayed?: boolean;
}

export interface FlightValidationResult {
  isValid: boolean;
  errors: string[];
  warnings?: string[];
}

// ============================================================================
// SERVICE INTERFACE
// ============================================================================

export interface IFlightSearchService {
  // Flight search operations
  searchFlights(callsign: string, date: string): Promise<FlightData[]>;
  searchFlightsWithOptions(
    callsign: string,
    options: FlightSearchOptions
  ): Promise<FlightData[]>;
  validateFlightData(flight: FlightData): FlightValidationResult;

  // Data formatting
  formatFlightForForm(
    flight: FlightData,
    type: 'arrival' | 'departure'
  ): FlightFormData;
  formatFlightForDisplay(flight: FlightData): FormattedFlight;
  formatFlightTime(timestamp: number): string;

  // Flight selection logic
  handleFlightSelection(
    flight: FlightData,
    type: 'arrival' | 'departure'
  ): FlightDetails;
  validateFlightSelection(flight: FlightData): FlightValidationResult;

  // Utility methods
  parseFlightCallsign(callsign: string): {
    airline: string;
    flightNumber: string;
  };
  isValidCallsign(callsign: string): boolean;
  getAirportName(code: string): string;
}

// ============================================================================
// IMPLEMENTATION
// ============================================================================

/**
 * Flight Search Service Implementation
 *
 * Provides flight search and formatting operations for delegation forms.
 * This is a placeholder implementation that will be fully developed in Phase 2.
 */
export class FlightSearchService implements IFlightSearchService {
  // Flight search operations
  async searchFlights(callsign: string, date: string): Promise<FlightData[]> {
    try {
      // Validate inputs
      if (!this.isValidCallsign(callsign) || !date) {
        return [];
      }

      // For now, return mock data based on callsign pattern
      // In production, this would call an actual flight API
      return this.generateMockFlightData(callsign, date);
    } catch (error) {
      console.error('Flight search error:', error);
      return [];
    }
  }

  private generateMockFlightData(callsign: string, date: string): FlightData[] {
    const baseDate = new Date(date);
    const timestamp = Math.floor(baseDate.getTime() / 1000);

    // Generate mock flights based on callsign
    const flights: FlightData[] = [];

    // Add arrival flight
    flights.push({
      callsign: callsign.toUpperCase(),
      arrivalAirport: this.getMockAirport(callsign),
      arrivalTime: timestamp + Math.random() * 3600, // Random time within hour
      departureAirport: this.getMockOriginAirport(callsign),
      departureTime: timestamp - Math.random() * 7200, // 2 hours before
      airline: this.extractAirlineFromCallsign(callsign),
      aircraft: this.getMockAircraft(),
      status: 'Scheduled',
    });

    return flights;
  }

  private getMockAirport(callsign: string): string {
    const airports = ['LHR', 'JFK', 'CDG', 'FRA', 'AMS', 'DXB', 'SIN'];
    const index = callsign.charCodeAt(0) % airports.length;
    return airports[index] || 'LHR';
  }

  private getMockOriginAirport(callsign: string): string {
    const airports = ['LAX', 'ORD', 'ATL', 'DEN', 'DFW', 'SEA', 'BOS'];
    const index = callsign.charCodeAt(1) % airports.length;
    return airports[index] || 'LAX';
  }

  private getMockAircraft(): string {
    const aircraft = [
      'Boeing 777',
      'Airbus A320',
      'Boeing 737',
      'Airbus A380',
      'Boeing 787',
    ];
    return (
      aircraft[Math.floor(Math.random() * aircraft.length)] || 'Boeing 737'
    );
  }

  private extractAirlineFromCallsign(callsign: string): string {
    const airlineMap: Record<string, string> = {
      BA: 'British Airways',
      AA: 'American Airlines',
      UA: 'United Airlines',
      LH: 'Lufthansa',
      AF: 'Air France',
      KL: 'KLM',
      EK: 'Emirates',
      SQ: 'Singapore Airlines',
    };

    const prefix = callsign.substring(0, 2).toUpperCase();
    return airlineMap[prefix] || 'Unknown Airline';
  }

  async searchFlightsWithOptions(
    callsign: string,
    options: FlightSearchOptions
  ): Promise<FlightData[]> {
    // TODO: Implement flight search with advanced options
    return [];
  }

  validateFlightData(flight: FlightData): FlightValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (!flight) {
      errors.push('Flight data is required');
      return { isValid: false, errors, warnings };
    }

    if (!flight.callsign || flight.callsign.trim() === '') {
      errors.push('Flight callsign is required');
    }

    if (!this.isValidCallsign(flight.callsign)) {
      errors.push('Invalid flight callsign format');
    }

    // Check for required fields based on flight type
    if (flight.arrivalTime && !flight.arrivalAirport) {
      errors.push('Arrival airport is required when arrival time is provided');
    }

    if (flight.departureTime && !flight.departureAirport) {
      errors.push(
        'Departure airport is required when departure time is provided'
      );
    }

    // Add warnings for potential issues
    if (flight.arrivalTime && flight.departureTime) {
      if (flight.arrivalTime <= flight.departureTime) {
        warnings.push('Arrival time should be after departure time');
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  // Data formatting methods
  formatFlightForForm(
    flight: FlightData,
    type: 'arrival' | 'departure'
  ): FlightFormData {
    const isArrival = type === 'arrival';
    const timestamp = isArrival ? flight.arrivalTime : flight.departureTime;
    const airport = isArrival ? flight.arrivalAirport : flight.departureAirport;

    return {
      flightNumber: flight.callsign || '',
      dateTime: timestamp
        ? new Date(timestamp * 1000).toISOString().slice(0, 16)
        : '', // Format for datetime-local input
      airport: airport || '',
      terminal: '', // Not available in mock data
      notes: `${flight.airline || ''} ${flight.aircraft || ''}`.trim(),
    };
  }

  formatFlightForDisplay(flight: FlightData): FormattedFlight {
    const arrivalTime = flight.arrivalTime
      ? this.formatFlightTime(flight.arrivalTime)
      : 'Unknown';
    const departureTime = flight.departureTime
      ? this.formatFlightTime(flight.departureTime)
      : 'Unknown';

    return {
      displayName: `${flight.callsign} - ${flight.airline || 'Unknown Airline'}`,
      formattedTime: `Dep: ${departureTime} → Arr: ${arrivalTime}`,
      airportName: `${flight.departureAirport || 'Unknown'} → ${flight.arrivalAirport || 'Unknown'}`,
      status: flight.status || 'Unknown',
      details: `Aircraft: ${flight.aircraft || 'Unknown'}`,
    };
  }

  formatFlightTime(timestamp: number): string {
    // TODO: Implement time formatting
    if (!timestamp) return 'Unknown';
    return new Date(timestamp * 1000).toLocaleString();
  }

  // Flight selection logic
  handleFlightSelection(
    flight: FlightData,
    type: 'arrival' | 'departure'
  ): FlightDetails {
    // TODO: Implement flight selection logic
    return {
      flightNumber: flight.callsign,
      dateTime: '',
      airport:
        type === 'arrival'
          ? flight.arrivalAirport || ''
          : flight.departureAirport || '',
      terminal: '',
      notes: '',
    };
  }

  validateFlightSelection(flight: FlightData): FlightValidationResult {
    // TODO: Implement flight selection validation
    return {
      isValid: true,
      errors: [],
    };
  }

  // Utility methods
  parseFlightCallsign(callsign: string): {
    airline: string;
    flightNumber: string;
  } {
    if (!callsign || callsign.trim() === '') {
      return { airline: '', flightNumber: '' };
    }

    const trimmed = callsign.trim().toUpperCase();

    // Extract airline code (2-3 letters) and flight number
    const match = trimmed.match(/^([A-Z]{2,3})([0-9]{1,4}[A-Z]?)$/);

    if (match) {
      return {
        airline: match[1] || '',
        flightNumber: match[2] || '',
      };
    }

    // Fallback: assume first 2-3 characters are airline
    const airlineMatch = trimmed.match(/^[A-Z]{2,3}/);
    const airline = airlineMatch ? airlineMatch[0] : '';
    const flightNumber = airline ? trimmed.substring(airline.length) : trimmed;

    return {
      airline,
      flightNumber,
    };
  }

  isValidCallsign(callsign: string): boolean {
    if (!callsign || callsign.trim() === '') {
      return false;
    }

    // Basic validation: 2-3 letter airline code + 1-4 digit flight number
    const pattern = /^[A-Z]{2,3}[0-9]{1,4}[A-Z]?$/i;
    return pattern.test(callsign.trim());
  }

  getAirportName(code: string): string {
    // TODO: Implement airport name lookup
    return code;
  }
}

// ============================================================================
// SINGLETON INSTANCE
// ============================================================================

/**
 * Singleton instance of the flight search service
 * Use this instance throughout the application for consistency
 */
export const flightSearchService = new FlightSearchService();

// ============================================================================
// EXPORTS
// ============================================================================

export default flightSearchService;
