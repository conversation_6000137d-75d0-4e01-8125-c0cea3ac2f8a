import { jest } from '@jest/globals';
import request from 'supertest';
import { initializeAndGetApp } from '../../app';
import { afterAll, beforeAll, describe, expect, it } from '@jest/globals';

let app;

const mockFlightData = [
  {
    callsign: 'SWR123',
    begin: Math.floor(Date.now() / 1000),
    end: Math.floor(Date.now() / 1000) + 3600,
    airport: 'EDDF',
    id: 'flight_mock_123',
  },
];

jest.mock('../../controllers/flight.controller.js', () => ({
  searchFlights: (req, res) => res.status(200).json(mockFlightData),
  getFlightsByAirport: (req, res) => res.status(200).json(mockFlightData),
  getFlightsByTimeInterval: (req, res) => res.status(200).json(mockFlightData),
  getAllFlights: (req, res) => res.status(200).json(mockFlightData),
  getFlightById: (req, res) => res.status(200).json(mockFlightData[0]),
  createFlight: (req, res) => res.status(201).json({ ...req.body, id: 'flight_mock_new' }),
  updateFlight: (req, res) => res.status(200).json({ ...mockFlightData[0], ...req.body }),
  deleteFlight: (req, res) => res.status(200).json({ message: 'Flight deleted successfully' }),
}));

jest.mock('../../middleware/jwtAuth.middleware.js', () => ({
  enhancedAuthenticateUser: (req, res, next) => {
    req.user = { id: 'mock-user-id', role: 'ADMIN' };
    next();
  },
}));

beforeAll(async () => {
  app = await initializeAndGetApp();
});

afterAll(() => {
  // No prisma disconnect needed
});

describe('Flight API Routes', () => {
  const newFlightData = {
    callsign: 'SWR123',
    begin: Math.floor(Date.now() / 1000),
    end: Math.floor(Date.now() / 1000) + 3600,
    airport: 'EDDF',
  };

  it('should create a new flight', async () => {
    const response = await request(app)
      .post('/api/flights')
      .send({ ...newFlightData, callsign: 'NEWFLIGHT' });
    expect(response.statusCode).toBe(201);
    expect(response.body.status).toBe('success');
    expect(response.body.data).toHaveProperty('id');
    expect(response.body.data.callsign).toBe('NEWFLIGHT');
  });

  it('should get all flights', async () => {
    const response = await request(app).get('/api/flights');
    expect(response.statusCode).toBe(200);
    expect(response.body.status).toBe('success');
    expect(Array.isArray(response.body.data)).toBe(true);
    expect(response.body.data[0].callsign).toBe('SWR123');
  });

  it('should get a flight by ID', async () => {
    const response = await request(app).get(`/api/flights/flight_mock_123`);
    expect(response.statusCode).toBe(200);
    expect(response.body.status).toBe('success');
    expect(response.body.data.id).toBe('flight_mock_123');
  });

  it('should update a flight', async () => {
    const updates = { callsign: 'UPDATEDCS' };
    const response = await request(app).put(`/api/flights/flight_mock_123`).send(updates);
    expect(response.statusCode).toBe(200);
    expect(response.body.status).toBe('success');
    expect(response.body.data.callsign).toBe('UPDATEDCS');
  });

  it('should delete a flight', async () => {
    const response = await request(app).delete(`/api/flights/flight_mock_123`);
    expect(response.statusCode).toBe(200);
    expect(response.body.status).toBe('success');
    expect(response.body.data.message).toContain('deleted successfully');
  });

  it('should search flights by callsign', async () => {
    const response = await request(app).get('/api/flights/search').query({ callsign: 'SWR123' });
    expect(response.statusCode).toBe(200);
    expect(response.body.status).toBe('success');
    expect(response.body.data[0].callsign).toBe('SWR123');
  });

  it('should get flights by airport', async () => {
    const response = await request(app)
      .get('/api/flights/airport')
      .query({
        airport: 'EDDF',
        begin: newFlightData.begin - 100,
        end: newFlightData.end + 100,
      });
    expect(response.statusCode).toBe(200);
    expect(response.body.status).toBe('success');
    expect(response.body.data[0].airport).toBe('EDDF');
  });

  it('should get flights by time interval', async () => {
    const response = await request(app)
      .get('/api/flights/interval')
      .query({
        begin: newFlightData.begin - 100,
        end: newFlightData.end + 100,
      });
    expect(response.statusCode).toBe(200);
    expect(response.body.status).toBe('success');
    expect(response.body.data.length).toBe(1);
  });
});
