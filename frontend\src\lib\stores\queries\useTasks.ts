/**
 * @file TanStack Query hooks for Task-related data.
 * These hooks manage fetching, caching, and mutating task data,
 * integrating with the TaskApiService and TaskTransformer.
 * @module stores/queries/useTasks
 */

import type { UseQueryOptions } from '@tanstack/react-query';

import { useMutation, useQueries, useQueryClient } from '@tanstack/react-query';
import { useCallback, useMemo } from 'react';
import { useCrudQuery } from '@/hooks/api/useSmartQuery'; // Adjusted import path

import type { CreateTaskData, Task } from '@/lib/types/domain';

import { taskApiService } from '../../api/services/apiServiceFactory'; // Use centralized service
import { enrichTask } from '../../transformers/taskEnrichment';
import { TaskTransformer } from '@/lib/transformers/taskTransformer';
import { createTaskWithAssignmentsQueries, taskQueryKeys } from './taskQueries';
import { undefinedToNull } from '@/lib/utils/typeHelpers';

// Re-export query keys from taskQueries for backward compatibility
export { taskQueryKeys } from './taskQueries';

/**
 * Custom hook to fetch all tasks.
 * @param options - Optional React Query options
 * @returns Query result containing an array of Task domain models.
 */
export const useTasks = (
  options?: Omit<UseQueryOptions<Task[], Error>, 'queryFn' | 'queryKey'>
) => {
  return useCrudQuery<Task[], Error>(
    [...taskQueryKeys.all], // queryKey - spread for mutability
    async () => {
      const response = await taskApiService.getAll();
      return response.data;
    },
    'task', // entityType
    {
      staleTime: 0, // Existing staleTime
      ...options,
    }
  );
};

/**
 * Custom hook to fetch a single task by its ID.
 * @param id - The ID of the task to fetch.
 * @returns Query result containing a single Task domain model or undefined.
 */
export const useTask = (id: string) => {
  return useCrudQuery<Task, Error>(
    [...taskQueryKeys.detail(id)],
    async () => {
      return await taskApiService.getById(id);
    },
    'task', // entityType for WebSocket events
    {
      enabled: !!id, // Only run query if id is truthy
      staleTime: 5 * 60 * 1000, // 5 minutes
    }
  );
};

// ✅ OPTIMIZED: Fast parallel data fetching for task with assignments
export const useTaskWithAssignments = (id: string) => {
  // Execute all queries in parallel using useQueries for maximum performance
  const results = useQueries({
    queries: createTaskWithAssignmentsQueries(id),
  });

  const [taskQuery, employeesQuery, vehiclesQuery] = results;

  // Compute enriched task when all data is available
  const enrichedTask = useMemo(() => {
    if (!taskQuery?.data || !employeesQuery?.data || !vehiclesQuery?.data) {
      return;
    }

    try {
      const task = TaskTransformer.fromApi(taskQuery.data as any);

      // Defensive programming: Ensure employees and vehicles are arrays
      const employees = Array.isArray(employeesQuery.data)
        ? employeesQuery.data
        : [];
      const vehicles = Array.isArray(vehiclesQuery.data)
        ? vehiclesQuery.data
        : [];

      return enrichTask(task, employees, vehicles);
    } catch (error) {
      console.error('Error enriching task data:', error);
      throw error;
    }
  }, [taskQuery?.data, employeesQuery?.data, vehiclesQuery?.data]);

  // ✅ PRODUCTION FIX: Memoize refetch function to prevent infinite re-renders
  const refetch = useCallback(() => {
    taskQuery?.refetch();
    employeesQuery?.refetch();
    vehiclesQuery?.refetch();
  }, [taskQuery?.refetch, employeesQuery?.refetch, vehiclesQuery?.refetch]);

  // Return combined state with optimized loading states
  return {
    data: enrichedTask,
    error: taskQuery?.error || employeesQuery?.error || vehiclesQuery?.error,
    isError:
      taskQuery?.isError || employeesQuery?.isError || vehiclesQuery?.isError,
    isLoading:
      taskQuery?.isLoading ||
      employeesQuery?.isLoading ||
      vehiclesQuery?.isLoading,
    isPending:
      taskQuery?.isPending ||
      employeesQuery?.isPending ||
      vehiclesQuery?.isPending,
    refetch,
  };
};

// ✅ BACKWARD COMPATIBILITY: Alias for the optimized hook
export const useTaskEnriched = useTaskWithAssignments;

/**
 * Custom hook for creating a new task.
 * Includes optimistic updates and cache invalidation.
 * @returns Mutation result for creating a task.
 */
export const useCreateTask = () => {
  const queryClient = useQueryClient();

  interface CreateContext {
    previousTasks: Task[] | undefined;
  }

  return useMutation<Task, Error, CreateTaskData, CreateContext>({
    mutationFn: async (taskData: CreateTaskData) => {
      const request = TaskTransformer.toCreateRequest(taskData);
      return await taskApiService.create(request); // Removed redundant TaskTransformer.fromApi
    },
    onError: (err, newTaskData, context) => {
      if (context?.previousTasks) {
        queryClient.setQueryData(taskQueryKeys.all, context.previousTasks);
      }
      console.error('Failed to create task:', err);
    },
    onMutate: async newTaskData => {
      await queryClient.cancelQueries({ queryKey: taskQueryKeys.all });
      const previousTasks = queryClient.getQueryData<Task[]>(taskQueryKeys.all);

      queryClient.setQueryData<Task[]>(taskQueryKeys.all, (old = []) => {
        const tempId = 'optimistic-' + Date.now().toString();
        const now = new Date().toISOString();
        const optimisticTask: Task = {
          createdAt: now,
          dateTime: newTaskData.dateTime ?? null,
          deadline: newTaskData.deadline ?? null,
          description: newTaskData.description, // Direct mapping
          driverEmployee: null,
          driverEmployeeId: newTaskData.driverEmployeeId ?? null,
          estimatedDuration: newTaskData.estimatedDuration ?? null,
          id: tempId,
          location: newTaskData.location ?? null,
          notes: newTaskData.notes ?? null,
          priority: newTaskData.priority,
          requiredSkills: newTaskData.requiredSkills ?? null,
          staffEmployee: null,
          staffEmployeeId: newTaskData.staffEmployeeId ?? null,
          status: newTaskData.status || 'Pending',
          subtasks:
            newTaskData.subtasks?.map(s => ({
              completed: s.completed || false,
              id: `optimistic-subtask-${Date.now()}-${Math.random().toString(36).slice(2, 7)}`,
              taskId: tempId, // Assign the optimistic task's ID as subtask's taskId
              title: s.title,
            })) || [],
          updatedAt: now,
          vehicle: null,
          vehicleId: newTaskData.vehicleId ?? null,
        };
        return [...old, optimisticTask];
      });

      return { previousTasks };
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: taskQueryKeys.all });
    },
  });
};

/**
 * Custom hook for updating an existing task.
 * Includes optimistic updates and rollback on error.
 * @returns Mutation result for updating a task.
 */
export const useUpdateTask = () => {
  const queryClient = useQueryClient();

  interface UpdateContext {
    previousTask: Task | undefined;
    previousTasksList: Task[] | undefined;
  }

  return useMutation<
    Task,
    Error,
    { data: Partial<CreateTaskData>; id: string }, // Corrected data type
    UpdateContext
  >({
    mutationFn: async ({ data, id }) => {
      const request = TaskTransformer.toUpdateRequest(data); // Removed cast
      return await taskApiService.update(id, request); // Removed redundant TaskTransformer.fromApi
    },
    onError: (err, variables, context) => {
      if (context?.previousTask) {
        queryClient.setQueryData(
          taskQueryKeys.detail(variables.id),
          context.previousTask
        );
      }
      if (context?.previousTasksList) {
        queryClient.setQueryData(taskQueryKeys.all, context.previousTasksList);
      }
      console.error('Failed to update task:', err);
    },
    onMutate: async ({ data, id }) => {
      await queryClient.cancelQueries({ queryKey: taskQueryKeys.all });
      await queryClient.cancelQueries({ queryKey: taskQueryKeys.detail(id) });

      const previousTask = queryClient.getQueryData<Task>(
        taskQueryKeys.detail(id)
      );
      const previousTasksList = queryClient.getQueryData<Task[]>(
        taskQueryKeys.all
      );

      queryClient.setQueryData<Task>(taskQueryKeys.detail(id), old => {
        if (!old) return old;
        const now = new Date().toISOString();

        // Explicitly map updated fields to avoid issues with spread operator on different types
        const updatedOptimistic: Task = {
          ...old,
          dateTime: data.dateTime !== undefined ? data.dateTime : old.dateTime,
          deadline: undefinedToNull(
            data.deadline !== undefined ? data.deadline : old.deadline
          ),
          description: data.description ?? old.description,
          driverEmployeeId: undefinedToNull(
            data.driverEmployeeId !== undefined
              ? data.driverEmployeeId
              : old.driverEmployeeId
          ),
          estimatedDuration:
            data.estimatedDuration !== undefined
              ? data.estimatedDuration
              : old.estimatedDuration,
          location: data.location !== undefined ? data.location : old.location,
          notes: undefinedToNull(
            data.notes !== undefined ? data.notes : old.notes
          ),
          priority: data.priority ?? old.priority,
          requiredSkills:
            data.requiredSkills !== undefined
              ? data.requiredSkills
              : old.requiredSkills,
          staffEmployeeId:
            data.staffEmployeeId !== undefined
              ? data.staffEmployeeId
              : old.staffEmployeeId,
          status: data.status ?? old.status,
          subtasks:
            data.subtasks?.map(s => ({
              completed: s.completed ?? false,
              id: `optimistic-subtask-${Date.now()}-${Math.random().toString(36).slice(2, 7)}`,
              taskId: id, // Assign the current task's ID as subtask's taskId
              title: s.title,
            })) ||
            old.subtasks ||
            [],
          updatedAt: now,
          vehicleId: undefinedToNull(
            data.vehicleId !== undefined ? data.vehicleId : old.vehicleId
          ),
        };
        return updatedOptimistic;
      });

      queryClient.setQueryData<Task[]>(taskQueryKeys.all, (old = []) => {
        return old.map(task => {
          if (task.id === id) {
            const now = new Date().toISOString();
            const optimisticSubtasks =
              data.subtasks?.map(s => ({
                completed: s.completed ?? false,
                id: `optimistic-subtask-${Date.now()}-${Math.random().toString(36).slice(2, 7)}`,
                taskId: id, // Assign the current task's ID as subtask's taskId
                title: s.title,
              })) ||
              task.subtasks ||
              [];
            return {
              ...task,
              dateTime:
                data.dateTime !== undefined ? data.dateTime : task.dateTime,
              deadline: undefinedToNull(
                data.deadline !== undefined ? data.deadline : task.deadline
              ),
              description: data.description ?? task.description,
              driverEmployeeId: undefinedToNull(
                data.driverEmployeeId !== undefined
                  ? data.driverEmployeeId
                  : task.driverEmployeeId
              ),
              estimatedDuration:
                data.estimatedDuration !== undefined
                  ? data.estimatedDuration
                  : task.estimatedDuration,
              location:
                data.location !== undefined ? data.location : task.location,
              notes: undefinedToNull(
                data.notes !== undefined ? data.notes : task.notes
              ),
              priority: data.priority ?? task.priority,
              requiredSkills:
                data.requiredSkills !== undefined
                  ? data.requiredSkills
                  : task.requiredSkills,
              staffEmployeeId:
                data.staffEmployeeId !== undefined
                  ? data.staffEmployeeId
                  : task.staffEmployeeId,
              status: data.status ?? task.status,
              subtasks: optimisticSubtasks,
              updatedAt: now,
              vehicleId: undefinedToNull(
                data.vehicleId !== undefined ? data.vehicleId : task.vehicleId
              ),
            };
          }
          return task;
        });
      });

      return { previousTask, previousTasksList };
    },
    onSettled: (data, error, variables) => {
      queryClient.invalidateQueries({
        queryKey: taskQueryKeys.detail(variables.id),
      });
      queryClient.invalidateQueries({ queryKey: taskQueryKeys.all });
    },
  });
};

/**
 * Custom hook for deleting an existing task.
 * Includes cache updates.
 * @returns Mutation result for deleting a task.
 */
export const useDeleteTask = () => {
  const queryClient = useQueryClient();

  interface DeleteContext {
    previousTasksList: Task[] | undefined;
  }

  return useMutation<string, Error, string, DeleteContext>({
    mutationFn: async (id: string) => {
      await taskApiService.delete(id);
      return id;
    },
    onError: (err, id, context) => {
      if (context?.previousTasksList) {
        queryClient.setQueryData(taskQueryKeys.all, context.previousTasksList);
      }
      console.error('Failed to delete task:', err);
    },
    onMutate: async id => {
      await queryClient.cancelQueries({ queryKey: taskQueryKeys.all });
      await queryClient.cancelQueries({ queryKey: taskQueryKeys.detail(id) });

      const previousTasksList = queryClient.getQueryData<Task[]>(
        taskQueryKeys.all
      );

      queryClient.setQueryData<Task[]>(taskQueryKeys.all, (old = []) =>
        old.filter(task => task.id !== id)
      );

      queryClient.removeQueries({ queryKey: taskQueryKeys.detail(id) });

      return { previousTasksList };
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: taskQueryKeys.all });
    },
  });
};

// Removed useAssignTask and useManageSubtasks hooks as their functionality is now
// handled by the main create/update task mutations.
