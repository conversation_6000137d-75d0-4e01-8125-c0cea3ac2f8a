import type {
  CreateDelegationRequest,
  CreateFlightDetailsRequest,
  DelegationApiResponse,
  UpdateDelegationRequest,
} from '../../../types/apiContracts';
import type {
  Delegation,
  DelegationStatusPrisma,
  FlightDetails,
} from '../../../types/domain';
import type { ApiClient } from '../../core/apiClient';
import { DelegationTransformer } from '../../../transformers/delegationTransformer';

import {
  BaseApiService,
  type DataTransformer,
  type ServiceConfig,
} from '../../core/baseApiService';

const DelegationApiTransformer: DataTransformer<Delegation> = {
  fromApi: (data: DelegationApiResponse) => DelegationTransformer.fromApi(data),
  toApi: (data: CreateDelegationRequest | UpdateDelegationRequest) => data,
};

const FlightDetailsTransformer = {
  toCreateRequest: (data: CreateFlightDetailsRequest) => data,
};

export class DelegationApiService extends BaseApiService<
  Delegation,
  CreateDelegationRequest,
  UpdateDelegationRequest
> {
  protected endpoint = '/delegations';
  protected transformer: DataTransformer<Delegation> = DelegationApiTransformer;

  constructor(apiClient: ApiClient, config?: ServiceConfig) {
    super(apiClient, {
      cacheDuration: 2 * 60 * 1000, // 2 minutes for delegations
      circuitBreakerThreshold: 5,
      enableMetrics: true,
      retryAttempts: 3,
      ...config,
    });
  }

  async getByStatus(status: DelegationStatusPrisma): Promise<Delegation[]> {
    const result = await this.getAll({ status });
    return result.data;
  }

  async manageFlightDetails(
    id: string,
    flightDetails: FlightDetails
  ): Promise<Delegation> {
    return this.executeWithInfrastructure(null, async () => {
      const requestPayload =
        FlightDetailsTransformer.toCreateRequest(flightDetails);
      const response = await this.apiClient.patch<DelegationApiResponse>(
        `${this.endpoint}/${id}/flight-details`,
        requestPayload
      );

      this.cache.invalidatePattern(new RegExp(`^${this.endpoint}:`));
      this.cache.invalidate(`${this.endpoint}:getById:${id}`);

      return DelegationTransformer.fromApi(response);
    });
  }

  async setDelegates(
    id: string,
    delegates: { employeeId: number; notes?: string; role: string }[]
  ): Promise<Delegation> {
    return this.executeWithInfrastructure(null, async () => {
      const response = await this.apiClient.patch<DelegationApiResponse>(
        `${this.endpoint}/${id}/delegates`,
        { delegates }
      );

      this.cache.invalidatePattern(new RegExp(`^${this.endpoint}:`));
      this.cache.invalidate(`${this.endpoint}:getById:${id}`);

      return DelegationTransformer.fromApi(response);
    });
  }

  async setDrivers(
    id: string,
    driverEmployeeIds: number[]
  ): Promise<Delegation> {
    return this.executeWithInfrastructure(null, async () => {
      const requestPayload = driverEmployeeIds.map(employeeId => ({
        employeeId,
      }));
      const response = await this.apiClient.patch<DelegationApiResponse>(
        `${this.endpoint}/${id}/drivers`,
        { drivers: requestPayload }
      );

      this.cache.invalidatePattern(new RegExp(`^${this.endpoint}:`));
      this.cache.invalidate(`${this.endpoint}:getById:${id}`);

      return DelegationTransformer.fromApi(response);
    });
  }

  async setEscorts(
    id: string,
    escortEmployeeIds: number[]
  ): Promise<Delegation> {
    return this.executeWithInfrastructure(null, async () => {
      const requestPayload = escortEmployeeIds.map(employeeId => ({
        employeeId,
      }));
      const response = await this.apiClient.patch<DelegationApiResponse>(
        `${this.endpoint}/${id}/escorts`,
        { escorts: requestPayload }
      );

      this.cache.invalidatePattern(new RegExp(`^${this.endpoint}:`));
      this.cache.invalidate(`${this.endpoint}:getById:${id}`);

      return DelegationTransformer.fromApi(response);
    });
  }

  async setVehicles(id: string, vehicleIds: number[]): Promise<Delegation> {
    return this.executeWithInfrastructure(null, async () => {
      const requestPayload = vehicleIds.map(vehicleId => ({
        assignedDate: new Date().toISOString(),
        vehicleId,
      }));
      const response = await this.apiClient.patch<DelegationApiResponse>(
        `${this.endpoint}/${id}/vehicles`,
        { vehicleAssignments: requestPayload }
      );

      this.cache.invalidatePattern(new RegExp(`^${this.endpoint}:`));
      this.cache.invalidate(`${this.endpoint}:getById:${id}`);

      return DelegationTransformer.fromApi(response);
    });
  }

  async updateStatus(
    id: string,
    newStatus: DelegationStatusPrisma,
    statusChangeReason?: string
  ): Promise<Delegation> {
    return this.executeWithInfrastructure(null, async () => {
      const response = await this.apiClient.put<DelegationApiResponse>(
        `${this.endpoint}/${id}`,
        { status: newStatus, statusChangeReason }
      );

      this.cache.invalidatePattern(new RegExp(`^${this.endpoint}:`));
      this.cache.invalidate(`${this.endpoint}:getById:${id}`);

      return DelegationTransformer.fromApi(response);
    });
  }
}
