import type {
  CreateTaskRequest,
  TaskApiResponse,
  UpdateTaskRequest,
} from '../../../types/apiContracts';
import type { Task, TaskStatusPrisma } from '../../../types/domain';
import type { ApiClient } from '../../core/apiClient';
import { TaskTransformer } from '../../../transformers/taskTransformer';
import {
  BaseApiService,
  type DataTransformer,
  type ServiceConfig,
} from '../../core/baseApiService';

const TaskApiTransformer: DataTransformer<Task> = {
  fromApi: (data: TaskApiResponse) => TaskTransformer.fromApi(data),
  toApi: (data: any) => data,
};

export class TaskApiService extends BaseApiService<
  Task,
  CreateTaskRequest,
  UpdateTaskRequest
> {
  protected endpoint = '/tasks';
  protected transformer: DataTransformer<Task> = TaskApiTransformer;

  constructor(apiClient: ApiClient, config?: ServiceConfig) {
    super(apiClient, {
      cacheDuration: 3 * 60 * 1000, // 3 minutes for tasks
      retryAttempts: 3,
      circuitBreakerThreshold: 5,
      enableMetrics: true,
      ...config,
    });
  }

  async getByStatus(status: TaskStatusPrisma): Promise<Task[]> {
    const result = await this.getAll({ status });
    return result.data;
  }

  async updateTaskStatus(
    taskId: string,
    newStatus: TaskStatusPrisma
  ): Promise<Task> {
    return this.executeWithInfrastructure(null, async () => {
      const response = await this.apiClient.patch<TaskApiResponse>(
        `${this.endpoint}/${taskId}/status`,
        { status: newStatus }
      );

      this.cache.invalidatePattern(new RegExp(`^${this.endpoint}:`));
      this.cache.invalidate(`${this.endpoint}:getById:${taskId}`);

      return TaskTransformer.fromApi(response);
    });
  }
}
