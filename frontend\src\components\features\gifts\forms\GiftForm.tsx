'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { <PERSON>Left, Save } from 'lucide-react';
import { useRouter } from 'next/navigation';
import React from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import type { CreateGiftData, Gift, UpdateGiftData } from '@/lib/types/domain';

import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';

// Gift form validation schema
const giftFormSchema = z.object({
  itemDescription: z
    .string()
    .min(1, 'Item description is required')
    .max(500, 'Item description must be less than 500 characters'),
  recipientId: z.string().min(1, 'Recipient is required'),
  dateSent: z.string().min(1, 'Date sent is required'),
  senderName: z
    .string()
    .min(1, 'Sender name is required')
    .max(255, 'Sender name must be less than 255 characters'),
  occasion: z.string().max(100, 'Occasion must be less than 100 characters').optional(),
  notes: z.string().max(1000, 'Notes must be less than 1000 characters').optional(),
});

type GiftFormData = z.infer<typeof giftFormSchema>;

interface GiftFormProps {
  initialData?: Partial<Gift>;
  isEditing?: boolean;
  isLoading?: boolean;
  onSubmit: (data: CreateGiftData | UpdateGiftData) => Promise<void>;
  recipients?: Array<{ id: string; name: string }>;
}

// Common occasions for quick selection
const commonOccasions = [
  'Birthday',
  'Anniversary',
  'Christmas',
  'Wedding',
  'Graduation',
  'Baby Shower',
  'Retirement',
  'Promotion',
  'Thank You',
  'Get Well Soon',
  'Sympathy',
  'Housewarming',
  'Valentine\'s Day',
  'Mother\'s Day',
  'Father\'s Day',
];

export const GiftForm: React.FC<GiftFormProps> = ({
  initialData,
  isEditing = false,
  isLoading = false,
  onSubmit,
  recipients = [],
}) => {
  const router = useRouter();

  const form = useForm<GiftFormData>({
    resolver: zodResolver(giftFormSchema),
    defaultValues: {
      itemDescription: initialData?.itemDescription || '',
      recipientId: initialData?.recipientId || '',
      dateSent: initialData?.dateSent ? initialData.dateSent.split('T')[0] : '',
      senderName: initialData?.senderName || '',
      occasion: initialData?.occasion || '',
      notes: initialData?.notes || '',
    },
  });

  const handleSubmit = async (data: GiftFormData) => {
    try {
      // Convert date to ISO string
      const formattedData = {
        ...data,
        dateSent: new Date(data.dateSent).toISOString(),
        occasion: data.occasion || null,
        notes: data.notes || null,
      };

      await onSubmit(formattedData);
    } catch (error) {
      console.error('Error submitting gift form:', error);
    }
  };

  const handleCancel = () => {
    router.back();
  };

  return (
    <Card className="max-w-2xl mx-auto">
      <CardHeader>
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleCancel}
            className="p-2"
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <CardTitle>
            {isEditing ? 'Edit Gift' : 'Add New Gift'}
          </CardTitle>
        </div>
      </CardHeader>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)}>
          <CardContent className="space-y-6">
            {/* Item Description */}
            <FormField
              control={form.control}
              name="itemDescription"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Item Description *</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="e.g., Bouquet of roses, Gift card, Watch..."
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    Describe the gift item that was sent
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Recipient */}
            <FormField
              control={form.control}
              name="recipientId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Recipient *</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a recipient" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {recipients.map((recipient) => (
                        <SelectItem key={recipient.id} value={recipient.id}>
                          {recipient.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    Choose who received this gift
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Date Sent */}
            <FormField
              control={form.control}
              name="dateSent"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Date Sent *</FormLabel>
                  <FormControl>
                    <Input
                      type="date"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    When was this gift sent?
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Sender Name */}
            <FormField
              control={form.control}
              name="senderName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Sender Name *</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Who sent this gift?"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    Name of the person who sent the gift
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Occasion */}
            <FormField
              control={form.control}
              name="occasion"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Occasion</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select an occasion (optional)" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {commonOccasions.map((occasion) => (
                        <SelectItem key={occasion} value={occasion}>
                          {occasion}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    What was the occasion for this gift?
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Notes */}
            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Notes</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Any additional notes about this gift..."
                      className="min-h-[100px]"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    Optional notes or additional details
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>

          <CardFooter className="flex justify-end gap-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isLoading}
              className="min-w-[100px]"
            >
              {isLoading ? (
                'Saving...'
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  {isEditing ? 'Update Gift' : 'Save Gift'}
                </>
              )}
            </Button>
          </CardFooter>
        </form>
      </Form>
    </Card>
  );
};
