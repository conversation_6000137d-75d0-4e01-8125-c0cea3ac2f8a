/**
 * @file Reusable widget container with expand/collapse functionality.
 * This component provides a consistent wrapper for dashboard widgets with common functionality.
 * @module components/reliability/dashboard/WidgetContainer
 */

'use client';

import {
  ChevronDown,
  ChevronUp,
  EyeOff,
  MoreVertical,
  RefreshCw,
} from 'lucide-react';
import React, { useState } from 'react';

import { ActionButton } from '@/components/ui/action-button';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { useReliabilityStore } from '@/lib/hooks';
import { cn } from '@/lib/utils';

/**
 * Props for the WidgetContainer component
 */
export interface WidgetContainerProps {
  /** Unique identifier for the widget */
  widgetId: string;
  /** Widget title displayed in the header */
  title: string;
  /** Optional subtitle or description */
  subtitle?: string;
  /** Widget content */
  children: React.ReactNode;
  /** Optional CSS class name for styling */
  className?: string;
  /** Whether the widget supports refresh functionality */
  refreshable?: boolean;
  /** Callback function for refresh action */
  onRefresh?: () => void | Promise<void>;
  /** Whether the widget is currently loading */
  isLoading?: boolean;
  /** Error state for the widget */
  error?: string | null;
  /** Custom actions to display in the widget header */
  actions?: React.ReactNode;
}

/**
 * Reusable widget container with expand/collapse functionality.
 *
 * This component provides:
 * - Consistent styling and layout for dashboard widgets
 * - Expand/collapse functionality with state persistence
 * - Widget visibility controls
 * - Refresh functionality for data updates
 * - Error state handling
 * - Loading state indicators
 * - Accessibility support
 *
 * Features:
 * - Responsive design with mobile-friendly controls
 * - Keyboard navigation support
 * - ARIA labels for screen readers
 * - Smooth animations for expand/collapse
 * - Customizable actions in the header
 *
 * @param props - Component props
 * @returns JSX element representing the widget container
 *
 * @example
 * ```tsx
 * <WidgetContainer
 *   widgetId="system-health"
 *   title="System Health"
 *   subtitle="Overall system status"
 *   refreshable
 *   onRefresh={handleRefresh}
 * >
 *   <SystemHealthWidget />
 * </WidgetContainer>
 * ```
 */
export const WidgetContainer: React.FC<WidgetContainerProps> = ({
  widgetId,
  title,
  subtitle,
  children,
  className = '',
  refreshable = false,
  onRefresh,
  isLoading = false,
  error = null,
  actions,
}) => {
  // Widget state from store
  const expandedWidgets = useReliabilityStore(
    state => state.preferences.dashboardLayout.expandedWidgets
  );
  const setWidgetExpanded = useReliabilityStore(
    state => state.setWidgetExpanded
  );
  const toggleWidget = useReliabilityStore(state => state.toggleWidget);

  // Local state for refresh loading
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Check if widget is expanded
  const isExpanded = expandedWidgets.has(widgetId as any);

  /**
   * Handle expand/collapse toggle
   */
  const handleToggleExpanded = () => {
    setWidgetExpanded(widgetId as any, !isExpanded);
  };

  /**
   * Handle widget hide
   */
  const handleHideWidget = () => {
    toggleWidget(widgetId as any);
  };

  /**
   * Handle refresh action
   */
  const handleRefresh = async () => {
    if (!onRefresh || isRefreshing) return;

    setIsRefreshing(true);
    try {
      await onRefresh();
    } catch (error) {
      console.error(`Failed to refresh widget ${widgetId}:`, error);
    } finally {
      setIsRefreshing(false);
    }
  };

  return (
    <TooltipProvider>
      <Card
        className={cn(
          'shadow-md transition-all duration-200',
          isExpanded ? 'row-span-2' : '',
          error ? 'border-destructive/50' : '',
          className
        )}
        role="region"
        aria-labelledby={`widget-title-${widgetId}`}
        aria-expanded={isExpanded}
      >
        {/* Widget Header */}
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
          <div className="space-y-1">
            <CardTitle
              id={`widget-title-${widgetId}`}
              className="text-base font-semibold"
            >
              {title}
            </CardTitle>
            {subtitle && (
              <p className="text-sm text-muted-foreground">{subtitle}</p>
            )}
          </div>

          <div className="flex items-center gap-1">
            {/* Custom Actions */}
            {actions}

            {/* Refresh Button */}
            {refreshable && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleRefresh}
                    disabled={isRefreshing || isLoading}
                    aria-label={`Refresh ${title}`}
                  >
                    <RefreshCw
                      className={cn(
                        'h-4 w-4',
                        (isRefreshing || isLoading) && 'animate-spin'
                      )}
                    />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Refresh widget data</TooltipContent>
              </Tooltip>
            )}

            {/* Widget Menu */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  aria-label={`${title} widget options`}
                >
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuItem onClick={handleToggleExpanded}>
                  {isExpanded ? (
                    <>
                      <ChevronUp className="mr-2 h-4 w-4" />
                      Collapse Widget
                    </>
                  ) : (
                    <>
                      <ChevronDown className="mr-2 h-4 w-4" />
                      Expand Widget
                    </>
                  )}
                </DropdownMenuItem>

                {refreshable && (
                  <DropdownMenuItem
                    onClick={handleRefresh}
                    disabled={isRefreshing || isLoading}
                  >
                    <RefreshCw className="mr-2 h-4 w-4" />
                    Refresh Data
                  </DropdownMenuItem>
                )}

                <DropdownMenuSeparator />

                <DropdownMenuItem
                  onClick={handleHideWidget}
                  className="text-destructive focus:text-destructive"
                >
                  <EyeOff className="mr-2 h-4 w-4" />
                  Hide Widget
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </CardHeader>

        {/* Widget Content */}
        <CardContent className="pt-0">
          {error ? (
            <div className="flex items-center justify-center py-8">
              <div className="text-center">
                <p className="text-sm text-destructive font-medium">
                  Failed to load widget data
                </p>
                <p className="mt-1 text-xs text-muted-foreground">{error}</p>
                {refreshable && (
                  <ActionButton
                    actionType="secondary"
                    size="sm"
                    className="mt-3"
                    onClick={handleRefresh}
                    isLoading={isRefreshing}
                  >
                    Retry
                  </ActionButton>
                )}
              </div>
            </div>
          ) : (
            <div
              className={cn(
                'transition-all duration-200',
                isExpanded ? 'min-h-[400px]' : 'min-h-[200px]'
              )}
            >
              {children}
            </div>
          )}
        </CardContent>
      </Card>
    </TooltipProvider>
  );
};

/**
 * Default export for the WidgetContainer component
 */
export default WidgetContainer;
