/**
 * @file Permissions Hook - Single Responsibility Principle (SRP)
 * @module lib/api/security/hooks/usePermissions
 *
 * This hook handles ONLY permission state management following SRP principles.
 * It provides permission checking functionality for components.
 *
 * SECURITY NOTE: This is the primary interface for permission checking in components.
 */

'use client';

import { useCallback, useMemo } from 'react';
import { useAuthContext } from '../../../../contexts/AuthContext';
import { PermissionManager } from '../../../security/PermissionManager';
import type {
  Permission,
  UserRole,
  PermissionCheck,
} from '../../../security/PermissionManager';

export interface UsePermissionsReturn {
  // Current user info
  userRole: UserRole | null;
  isAuthenticated: boolean;

  // Permission checking functions
  hasPermission: (permission: Permission) => boolean;
  hasAllPermissions: (permissions: Permission[]) => boolean;
  hasAnyPermission: (permissions: Permission[]) => boolean;
  hasMinimumRole: (minimumRole: UserRole) => boolean;

  // Permission details
  getPermissionCheck: (permission: Permission) => PermissionCheck;
  getAllPermissions: () => Permission[];

  // Convenience role checks
  isUser: boolean;
  isAdmin: boolean;
  isSuperAdmin: boolean;
}

/**
 * Permissions Hook - Single Responsibility: Permission State Management Only
 *
 * Provides permission checking functionality for components.
 * Does NOT handle authentication or token management.
 */
export function usePermissions(): UsePermissionsReturn {
  const { user, userRole: contextUserRole, loading } = useAuthContext();

  const isAuthenticated = useMemo(() => {
    return !!user && !loading;
  }, [user, loading]);

  const userRole = useMemo((): UserRole | null => {
    if (!isAuthenticated || !contextUserRole) {
      return null;
    }

    // Normalize role from context
    const normalizedRole = contextUserRole.toUpperCase();
    if (['USER', 'ADMIN', 'SUPER_ADMIN'].includes(normalizedRole)) {
      return normalizedRole as UserRole;
    }

    return 'USER'; // Default fallback
  }, [isAuthenticated, contextUserRole]);

  /**
   * Check if user has specific permission
   * Single responsibility: Permission checking only
   */
  const hasPermission = useCallback(
    (permission: Permission): boolean => {
      if (!userRole) return false;

      const check = PermissionManager.hasPermission(userRole, permission);
      return check.hasPermission;
    },
    [userRole]
  );

  /**
   * Check if user has all specified permissions
   * Single responsibility: Multiple permission checking only
   */
  const hasAllPermissions = useCallback(
    (permissions: Permission[]): boolean => {
      if (!userRole) return false;

      const check = PermissionManager.hasAllPermissions(userRole, permissions);
      return check.hasPermission;
    },
    [userRole]
  );

  /**
   * Check if user has any of the specified permissions
   * Single responsibility: Alternative permission checking only
   */
  const hasAnyPermission = useCallback(
    (permissions: Permission[]): boolean => {
      if (!userRole) return false;

      const check = PermissionManager.hasAnyPermission(userRole, permissions);
      return check.hasPermission;
    },
    [userRole]
  );

  /**
   * Check if user meets minimum role requirement
   * Single responsibility: Role hierarchy checking only
   */
  const hasMinimumRole = useCallback(
    (minimumRole: UserRole): boolean => {
      if (!userRole) return false;

      return PermissionManager.hasMinimumRole(userRole, minimumRole);
    },
    [userRole]
  );

  /**
   * Get detailed permission check result
   * Single responsibility: Detailed permission information only
   */
  const getPermissionCheck = useCallback(
    (permission: Permission): PermissionCheck => {
      if (!userRole) {
        return {
          hasPermission: false,
          reason: 'User not authenticated',
        };
      }

      return PermissionManager.hasPermission(userRole, permission);
    },
    [userRole]
  );

  /**
   * Get all permissions for current user role
   * Single responsibility: Permission enumeration only
   */
  const getAllPermissions = useCallback((): Permission[] => {
    if (!userRole) return [];

    return PermissionManager.getPermissionsForRole(userRole);
  }, [userRole]);

  // Convenience role checks
  const isUser = useMemo(() => userRole === 'USER', [userRole]);
  const isAdmin = useMemo(() => userRole === 'ADMIN', [userRole]);
  const isSuperAdmin = useMemo(() => userRole === 'SUPER_ADMIN', [userRole]);

  return {
    // Current user info
    userRole,
    isAuthenticated,

    // Permission checking functions
    hasPermission,
    hasAllPermissions,
    hasAnyPermission,
    hasMinimumRole,

    // Permission details
    getPermissionCheck,
    getAllPermissions,

    // Convenience role checks
    isUser,
    isAdmin,
    isSuperAdmin,
  };
}
