/**
 * @file Form Submission Configuration Service
 * @description Default configurations for form submission following SRP
 * @version 2.0.0
 * <AUTHOR> Development Team
 */

import type {
  RetryConfig,
  AccessibilityConfig,
  PerformanceConfig,
  ToastConfig,
} from '../types/FormSubmissionTypes';

/**
 * Default retry configuration
 */
export const DEFAULT_RETRY_CONFIG: RetryConfig = {
  maxAttempts: 3,
  delay: 1000,
  exponentialBackoff: true,
  retryCondition: error =>
    error.message.includes('network') ||
    error.message.includes('timeout') ||
    error.message.includes('502') ||
    error.message.includes('503') ||
    error.message.includes('504'),
};

/**
 * Default accessibility configuration
 */
export const DEFAULT_ACCESSIBILITY_CONFIG: AccessibilityConfig = {
  announceStatus: true,
  focusManagement: 'first-error',
  screenReaderAnnouncements: true,
};

/**
 * Default performance configuration
 */
export const DEFAULT_PERFORMANCE_CONFIG: PerformanceConfig = {
  debounceMs: 300,
  enableDeduplication: true,
  cacheResults: false,
  timeoutMs: 30000,
};

/**
 * Default toast configuration
 */
export const DEFAULT_TOAST_CONFIG: ToastConfig = {
  showSuccessToast: true,
  showErrorToast: true,
  successMessage: 'Operation completed successfully',
  errorMessage: 'An unexpected error occurred',
  entityType: 'generic',
};

/**
 * Configuration merger utility following DRY principle
 */
export class FormSubmissionConfigService {
  /**
   * Merge user config with defaults for retry settings
   */
  static mergeRetryConfig(userConfig?: Partial<RetryConfig>): RetryConfig {
    return { ...DEFAULT_RETRY_CONFIG, ...userConfig };
  }

  /**
   * Merge user config with defaults for accessibility settings
   */
  static mergeAccessibilityConfig(
    userConfig?: Partial<AccessibilityConfig>
  ): AccessibilityConfig {
    return { ...DEFAULT_ACCESSIBILITY_CONFIG, ...userConfig };
  }

  /**
   * Merge user config with defaults for performance settings
   */
  static mergePerformanceConfig(
    userConfig?: Partial<PerformanceConfig>
  ): PerformanceConfig {
    return { ...DEFAULT_PERFORMANCE_CONFIG, ...userConfig };
  }

  /**
   * Merge user config with defaults for toast settings
   */
  static mergeToastConfig(userConfig?: Partial<ToastConfig>): ToastConfig {
    return { ...DEFAULT_TOAST_CONFIG, ...userConfig };
  }
}
