'use client';

import { useLocale } from 'next-intl';
import { useEffect } from 'react';

import { localeDirections } from '@/i18n';

interface RTLProviderProps {
  children: React.ReactNode;
}

/**
 * RTL Provider Component
 * Handles RTL layout direction for Arabic language
 */
export const RTLProvider: React.FC<RTLProviderProps> = ({ children }) => {
  const locale = useLocale();
  const direction =
    localeDirections[locale as keyof typeof localeDirections] || 'ltr';

  useEffect(() => {
    // Set document direction
    document.documentElement.dir = direction;
    document.documentElement.lang = locale;

    // Add/remove RTL class for Tailwind CSS
    if (direction === 'rtl') {
      document.documentElement.classList.add('rtl');
    } else {
      document.documentElement.classList.remove('rtl');
    }

    // Set CSS custom property for direction-aware styling
    document.documentElement.style.setProperty('--text-direction', direction);
  }, [direction, locale]);

  return <>{children}</>;
};
