import type { Prisma, TaskStatus as PrismaTaskStatus, Task } from '../generated/prisma/index.js';

import { TaskPriority as PrismaTaskPriority, TaskStatusEntry } from '../generated/prisma/index.js';
import { PrismaClientKnownRequestError } from '../generated/prisma/runtime/library.js';
import prisma from './index.js';

// The 'data' parameter (Prisma.TaskCreateInput) is prepared by the controller.
// It should correctly structure relational links and enum types.
export const createTask = async (data: Prisma.TaskCreateInput): Promise<null | Task> => {
  try {
    // The controller (processTaskData) is responsible for formatting `data` correctly,
    // including `statusHistory`, `status`, and `priority` with correct enum types.
    return await prisma.task.create({
      data, // Directly use the data prepared by the controller
      include: {
        Employee_Task_driverEmployeeIdToEmployee: true,
        // NEW PHASE 1 RELATIONS
        Employee_Task_staffEmployeeIdToEmployee: true,
        SubTask: true,
        TaskStatusEntry: { orderBy: { changedAt: 'desc' } },
        Vehicle: true,
      },
    });
  } catch (error) {
    console.error('Error creating task:', error);
    if (error instanceof PrismaClientKnownRequestError && error.code === 'P2003') {
      const fieldName = error.meta?.field_name as string | undefined;
      if (fieldName?.toLowerCase().includes('vehicleid')) {
        throw new Error(`Vehicle not found for the provided vehicleId.`);
      }
      // Updated to check for the actual foreign key constraint names if possible, or a more generic check
      if (
        fieldName?.toLowerCase().includes('employeeid') ||
        fieldName?.toLowerCase().includes('task')
      ) {
        // General check
        throw new Error(`One or more assigned employees not found.`);
      }
    }
    return null;
  }
};

export const getAllTasks = async (): Promise<Task[]> => {
  try {
    return await prisma.task.findMany({
      include: {
        Employee_Task_driverEmployeeIdToEmployee: true,
        // NEW PHASE 1 RELATIONS
        Employee_Task_staffEmployeeIdToEmployee: true,
        SubTask: true,
        TaskStatusEntry: { orderBy: { changedAt: 'desc' } },
        Vehicle: true,
      },
      orderBy: { dateTime: 'desc' },
    });
  } catch (error) {
    console.error('Error fetching all tasks:', error);
    return [];
  }
};

export const getTaskById = async (id: string): Promise<null | Task> => {
  try {
    return await prisma.task.findUnique({
      include: {
        Employee_Task_driverEmployeeIdToEmployee: true,
        // NEW PHASE 1 RELATIONS
        Employee_Task_staffEmployeeIdToEmployee: true,
        SubTask: true,
        TaskStatusEntry: { orderBy: { changedAt: 'desc' } },
        Vehicle: true,
      },
      where: { id },
    });
  } catch (error) {
    console.error(`Error fetching task with ID ${id}:`, error);
    return null;
  }
};

export const updateTask = async (
  id: string,
  data: Prisma.TaskUpdateInput,
  statusChangeReason?: null | string,
): Promise<null | Task> => {
  try {
    let updatedTask: null | Task = null;
    const taskDataForUpdate: Prisma.TaskUpdateInput = { ...data };

    // Controller should ensure correct enum types. These casts are defensive.
    if (typeof data.status === 'string') {
      taskDataForUpdate.status = data.status;
    }
    if (typeof data.priority === 'string') {
      taskDataForUpdate.priority = data.priority;
    }

    await prisma.$transaction(async tx => {
      if (taskDataForUpdate.status) {
        // Check if status is part of the update
        const currentTask = await tx.task.findUnique({
          select: { status: true },
          where: { id },
        });
        const newStatusEnumValue = taskDataForUpdate.status as PrismaTaskStatus; // Already cast or should be from controller

        if (currentTask && currentTask.status !== newStatusEnumValue) {
          await tx.taskStatusEntry.create({
            data: {
              id: `task_status_${id}_${Date.now()}`, // Generate unique ID
              reason: statusChangeReason || 'Status updated',
              status: newStatusEnumValue,
              taskId: id,
            },
          });
        }
      }

      updatedTask = await tx.task.update({
        data: taskDataForUpdate,
        include: {
          Employee_Task_driverEmployeeIdToEmployee: true,
          // NEW PHASE 1 RELATIONS
          Employee_Task_staffEmployeeIdToEmployee: true,
          SubTask: true,
          TaskStatusEntry: { orderBy: { changedAt: 'desc' } },
          Vehicle: true,
        },
        where: { id },
      });
    });
    return updatedTask;
  } catch (error) {
    console.error(`Error updating task with ID ${id}:`, error);
    if (error instanceof PrismaClientKnownRequestError && error.code === 'P2025') {
      return null;
    }
    if (error instanceof PrismaClientKnownRequestError && error.code === 'P2003') {
      const fieldName = error.meta?.field_name as string | undefined;
      if (fieldName?.toLowerCase().includes('vehicleid')) {
        throw new Error(`Vehicle not found for update.`);
      }
      if (
        fieldName?.toLowerCase().includes('employeeid') ||
        fieldName?.toLowerCase().includes('task')
      ) {
        throw new Error(`One or more assigned employees not found for update.`);
      }
    }
    return null;
  }
};

export const deleteTask = async (id: string): Promise<null | Task> => {
  try {
    return await prisma.$transaction(async tx => {
      const taskToDelete = await tx.task.findUnique({ where: { id } });
      if (!taskToDelete) return null;

      await tx.subTask.deleteMany({ where: { taskId: id } });
      await tx.taskStatusEntry.deleteMany({ where: { taskId: id } });

      // Note: Dissociating employees is handled by Prisma schema if relations are set up correctly (e.g. onDelete: SetNull or Cascade)
      // or might need explicit handling if it's a many-to-many relation table.
      // Current schema implies Task.assignedEmployees is a list of relations, Prisma handles the join table.

      await tx.task.delete({ where: { id } });
      return taskToDelete;
    });
  } catch (error) {
    console.error(`Error deleting task with ID ${id}:`, error);
    if (error instanceof PrismaClientKnownRequestError && error.code === 'P2025') {
      return null;
    }
    return null;
  }
};
