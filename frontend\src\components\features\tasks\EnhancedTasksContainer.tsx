'use client';

import { Info, RefreshCw } from 'lucide-react';
import React, { useCallback, useEffect, useMemo, useState } from 'react';

import type { Task } from '@/lib/types/domain';

import { TaskSummary } from '@/components/reports/TaskSummary';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { ErrorHandler } from '@/components/ui/error-handler';
import { SkeletonLoader } from '@/components/ui/loading';
import { PaginationControls } from '@/components/ui/pagination';

import { TasksTable } from './TasksTable';

/**
 * Props for the EnhancedTasksContainer component
 */
interface EnhancedTasksContainerProps {
  /** Additional CSS class names */
  className?: string;
  /** Error message, if any */
  error: null | string;
  /** Whether data is currently loading */
  isLoading: boolean;
  /** Function to retry loading data */
  onRetry: () => void;
  /** Array of tasks to display */
  tasks: Task[];
}

/**
 * A container component that handles sorting, pagination, and display of tasks
 *
 * @example
 * ```tsx
 * <EnhancedTasksContainer
 *   tasks={filteredTasks}
 *   isLoading={isLoading}
 *   error={error}
 *   onRetry={handleRetry}
 * />
 * ```
 */
export function EnhancedTasksContainer({
  className,
  error,
  isLoading,
  onRetry,
  tasks,
}: EnhancedTasksContainerProps) {
  // Sorting state
  const [sortField, setSortField] = useState<string>('dateTime');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);

  // Custom sort order for status
  const statusOrder = {
    Assigned: 2,
    Cancelled: 5,
    Completed: 4,
    In_Progress: 1,
    Pending: 3,
  };

  // Custom sort order for priority
  const priorityOrder = {
    High: 1,
    Low: 3,
    Medium: 2,
  };

  // Sort tasks
  const sortedTasks = useMemo(() => {
    return [...tasks].sort((a, b) => {
      let valueA, valueB;

      // Handle different field types
      switch (sortField) {
        case 'dateTime':
        case 'deadline': {
          valueA = a[sortField] ? new Date(a[sortField]).getTime() : 0;
          valueB = b[sortField] ? new Date(b[sortField]).getTime() : 0;
          break;
        }
        case 'description':
        case 'location': {
          valueA = a[sortField]?.toLowerCase() || '';
          valueB = b[sortField]?.toLowerCase() || '';
          break;
        }
        case 'priority': {
          valueA =
            priorityOrder[a.priority as keyof typeof priorityOrder] || 999;
          valueB =
            priorityOrder[b.priority as keyof typeof priorityOrder] || 999;
          break;
        }
        case 'staffEmployeeId': {
          valueA = a.staffEmployeeId || 999; // Sort unassigned last
          valueB = b.staffEmployeeId || 999;
          break;
        }
        case 'status': {
          valueA = statusOrder[a.status as keyof typeof statusOrder] || 999;
          valueB = statusOrder[b.status as keyof typeof statusOrder] || 999;
          break;
        }
        default: {
          valueA = a[sortField as keyof Task];
          valueB = b[sortField as keyof Task];
        }
      }

      // Compare values based on direction (handle null/undefined)
      const safeValueA = valueA ?? '';
      const safeValueB = valueB ?? '';
      if (safeValueA < safeValueB) return sortDirection === 'asc' ? -1 : 1;
      if (safeValueA > safeValueB) return sortDirection === 'asc' ? 1 : -1;
      return 0;
    });
  }, [tasks, sortField, sortDirection, statusOrder, priorityOrder]);

  // Handle sort
  const handleSort = useCallback(
    (field: string) => {
      if (sortField === field) {
        setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
      } else {
        setSortField(field);
        setSortDirection('asc');
      }
    },
    [sortField, sortDirection]
  );

  // Pagination logic
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const paginatedTasks = useMemo(
    () => sortedTasks.slice(indexOfFirstItem, indexOfLastItem),
    [sortedTasks, indexOfFirstItem, indexOfLastItem]
  );

  const totalPages = useMemo(
    () => Math.ceil(sortedTasks.length / itemsPerPage),
    [sortedTasks.length, itemsPerPage]
  );

  // Handle page change
  const handlePageChange = useCallback((page: number) => {
    setCurrentPage(page);
  }, []);

  // Reset pagination when tasks change
  useEffect(() => {
    setCurrentPage(1);
  }, [tasks]);

  // Render loading state
  if (isLoading) {
    return (
      <div className="space-y-4" data-testid="loading-skeleton">
        <SkeletonLoader count={5} variant="table" />
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div className="space-y-4">
        <ErrorHandler context="Loading Tasks" error={error} />
        <div className="flex justify-center">
          <Button
            aria-label="Try loading tasks again"
            onClick={onRetry}
            size="sm"
            variant="outline"
          >
            <RefreshCw className="mr-2 size-4" />
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  // Render empty state
  if (tasks.length === 0) {
    return (
      <Alert className="mb-6" variant="default">
        <Info className="size-4" />
        <AlertTitle>No Tasks</AlertTitle>
        <AlertDescription>
          No tasks match your current filters. Try adjusting your search or
          filter criteria.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* Summary Statistics */}
      <TaskSummary tasks={sortedTasks} />

      {/* Tasks Table */}
      <Card className="card-print shadow-md">
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <TasksTable tasks={paginatedTasks} />
          </div>
        </CardContent>
      </Card>

      {/* Pagination */}
      {sortedTasks.length > itemsPerPage && (
        <div className="no-print mt-4 flex justify-center">
          <PaginationControls
            currentPage={currentPage}
            onPageChange={handlePageChange}
            totalPages={totalPages}
          />
        </div>
      )}

      {/* Task Count Summary */}
      <div className="no-print text-center text-sm text-muted-foreground">
        Showing {indexOfFirstItem + 1} to{' '}
        {Math.min(indexOfLastItem, sortedTasks.length)} of {sortedTasks.length}{' '}
        tasks
      </div>
    </div>
  );
}
