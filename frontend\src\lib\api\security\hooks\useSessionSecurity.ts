/**
 * @file Session Security Hook - Single Responsibility Principle (SRP)
 * @module hooks/useSessionSecurity
 *
 * This hook handles ONLY session security state and actions following SRP principles.
 * It provides session timeout detection, cross-tab logout, and concurrent session management.
 *
 * SECURITY NOTE: This manages session security state without handling authentication logic.
 */

'use client';

import { useCallback, useEffect, useState } from 'react';

import type {
  ConcurrentSession,
  SessionEvent,
  SessionState,
} from '../../../security/SessionManager';

import { useAuthContext } from '../../../../contexts/AuthContext';
import {
  SECURITY_EVENTS,
  SessionManager,
  SecurityUtils,
} from '../../../security';

export interface SessionSecurityActions {
  clearSession: () => void;
  handleCrossTabLogout: () => void;
  refreshSession: () => void;
  updateActivity: () => void;
}

export interface SessionSecurityState {
  concurrentSessions: ConcurrentSession[];
  isSessionActive: boolean;
  isSessionExpired: boolean;
  lastActivity: Date | null;
  sessionId: string;
  sessionWarning: boolean; // Warning before timeout
}

export interface UseSessionSecurityReturn
  extends SessionSecurityActions,
    SessionSecurityState {}

/**
 * Session Security Hook - Single Responsibility: Session Security State Management Only
 *
 * Manages session security state and provides session-related actions.
 * Does NOT handle authentication or token management.
 */
export function useSessionSecurity(): UseSessionSecurityReturn {
  const { signOut } = useAuthContext();

  const [state, setState] = useState<SessionSecurityState>({
    concurrentSessions: [],
    isSessionActive: true,
    isSessionExpired: false,
    lastActivity: null,
    sessionId: '',
    sessionWarning: false,
  });

  /**
   * Update activity timestamp
   * Single responsibility: Activity update only
   */
  const updateActivity = useCallback(() => {
    SessionManager.updateActivity();
    setState(prev => ({
      ...prev,
      isSessionActive: true,
      lastActivity: new Date(),
      sessionWarning: false,
    }));
  }, []);

  /**
   * Handle cross-tab logout
   * Single responsibility: Cross-tab logout handling only
   */
  const handleCrossTabLogout = useCallback(() => {
    SessionManager.handleCrossTabLogout();
    setState(prev => ({
      ...prev,
      isSessionActive: false,
      isSessionExpired: true,
    }));
  }, []);

  /**
   * Clear session state
   * Single responsibility: Session clearing only
   */
  const clearSession = useCallback(() => {
    SessionManager.clearSessionState();
    setState({
      concurrentSessions: [],
      isSessionActive: false,
      isSessionExpired: true,
      lastActivity: null,
      sessionId: '',
      sessionWarning: false,
    });
  }, []);

  /**
   * Refresh session state
   * Single responsibility: Session refresh only
   */
  const refreshSession = useCallback(() => {
    const sessionState = SessionManager.getSessionState();
    const sessionId = SessionManager.getCurrentSessionId();

    if (sessionState) {
      setState(prev => ({
        ...prev,
        isSessionActive: sessionState.isActive,
        isSessionExpired: false,
        lastActivity: sessionState.lastActivity,
        sessionId,
        sessionWarning: false,
      }));
    }

    // Update concurrent sessions
    SessionManager.manageConcurrentSessions();
  }, []);

  // Initialize session management and circuit breaker
  useEffect(() => {
    // Initialize circuit breaker first
    SecurityUtils.initializeCircuitBreaker();

    // Initialize session management
    SessionManager.initialize();

    // Perform initial integrity check
    const performInitialCheck = async () => {
      try {
        const integrityCheck = await SessionManager.performIntegrityCheck();
        if (!integrityCheck) {
          console.warn(
            '📊 Initial session integrity check failed, attempting recovery...'
          );
          const recovered = SessionManager.recoverFromCorruptedState();

          if (!recovered) {
            console.error('❌ Initial session recovery failed');
            setState(prev => ({
              ...prev,
              isSessionActive: false,
              isSessionExpired: true,
            }));
            return;
          }
        }

        // Set initial state after validation
        const sessionState = SessionManager.getSessionState();
        const sessionId = SessionManager.getCurrentSessionId();

        setState(prev => ({
          ...prev,
          isSessionActive: sessionState?.isActive ?? true,
          lastActivity: sessionState?.lastActivity ?? new Date(),
          sessionId,
        }));

        console.log('✅ Session security initialized successfully');
      } catch (error) {
        console.error('❌ Session security initialization failed:', error);
        setState(prev => ({
          ...prev,
          isSessionActive: false,
          isSessionExpired: true,
        }));
      }
    };

    performInitialCheck();

    // Cleanup on unmount
    return () => {
      SessionManager.cleanup();
    };
  }, []);

  // Listen for session events with circuit breaker protection
  useEffect(() => {
    const handleSessionEvent = (event: SessionEvent) => {
      // Circuit breaker check for event handling
      if (!SecurityUtils.canPerformSecurityCheck()) {
        console.debug('🔒 Session event handling blocked by circuit breaker');
        return;
      }

      const operationId = `session-event-${event.type}`;
      if (!SecurityUtils.startSecurityOperation(operationId)) {
        console.debug(`🔄 Session event ${event.type} already being handled`);
        return;
      }

      try {
        switch (event.type) {
          case SECURITY_EVENTS.CROSS_TAB_LOGOUT: {
            console.log('🔄 Cross-tab logout event received');
            SecurityUtils.recordSecurityAttempt();
            setState(prev => ({
              ...prev,
              isSessionActive: false,
              isSessionExpired: true,
            }));

            // Debounced signOut to prevent multiple calls
            setTimeout(() => signOut(), 100);
            break;
          }

          case SECURITY_EVENTS.SESSION_TIMEOUT: {
            console.log('⏰ Session timeout event received');
            SecurityUtils.recordSecurityAttempt();
            setState(prev => ({
              ...prev,
              isSessionActive: false,
              isSessionExpired: true,
            }));

            // Debounced signOut to prevent multiple calls
            setTimeout(() => signOut(), 100);
            break;
          }

          case 'session_validated': {
            console.log('✅ Session validated event received');
            SecurityUtils.recordSecuritySuccess();
            setState(prev => ({
              ...prev,
              isSessionActive: true,
              isSessionExpired: false,
              sessionWarning: false,
            }));
            break;
          }

          case 'token_refresh_success': {
            console.log('🔄 Token refresh success event received');
            SecurityUtils.recordSecuritySuccess();
            // Update session state to reflect successful refresh
            refreshSession();
            break;
          }

          case SECURITY_EVENTS.TOKEN_REFRESH_FAILED: {
            console.warn('❌ Token refresh failed event received');
            SecurityUtils.recordSecurityAttempt();
            setState(prev => ({
              ...prev,
              sessionWarning: true,
            }));
            break;
          }

          default: {
            console.debug(`🔍 Unknown session event: ${event.type}`);
            break;
          }
        }
      } catch (error) {
        console.error(`❌ Error handling session event ${event.type}:`, error);
        SecurityUtils.recordSecurityAttempt();
      } finally {
        SecurityUtils.endSecurityOperation(operationId);
      }
    };

    const cleanup = SessionManager.addSessionEventListener(handleSessionEvent);
    return cleanup;
  }, [signOut, refreshSession]);

  // Check for session timeout periodically with circuit breaker protection
  useEffect(() => {
    const checkSessionTimeout = () => {
      // Circuit breaker check - prevent verification loops
      if (!SecurityUtils.canPerformSecurityCheck()) {
        console.debug('🔒 Session timeout check blocked by circuit breaker');
        return;
      }

      const operationId = 'session-timeout-check';
      if (!SecurityUtils.startSecurityOperation(operationId)) {
        console.debug('🔄 Session timeout check already in progress');
        return;
      }

      try {
        // Perform session integrity check first
        const isConsistent = SessionManager.validateSessionConsistency();
        if (!isConsistent) {
          console.warn('📊 Session state inconsistent, attempting recovery...');
          const recovered = SessionManager.recoverFromCorruptedState();

          if (!recovered) {
            console.error('❌ Session recovery failed, forcing logout');
            SecurityUtils.recordSecurityAttempt();
            setState(prev => ({
              ...prev,
              isSessionActive: false,
              isSessionExpired: true,
            }));
            signOut();
            return;
          }
        }

        // Check for timeout
        const isTimeout = SessionManager.detectTimeout();
        if (isTimeout) {
          console.log('⏰ Session timeout detected');
          SecurityUtils.recordSecurityAttempt();
          setState(prev => ({
            ...prev,
            isSessionActive: false,
            isSessionExpired: true,
          }));

          // Debounced signOut to prevent multiple calls
          setTimeout(() => signOut(), 100);
          return;
        }

        // Check for session warning (5 minutes before timeout)
        const sessionState = SessionManager.getSessionState();
        if (sessionState?.lastActivity) {
          const timeSinceActivity =
            Date.now() - sessionState.lastActivity.getTime();
          const warningThreshold = (30 - 5) * 60 * 1000; // 25 minutes (5 min warning)

          if (timeSinceActivity > warningThreshold) {
            setState(prev => ({
              ...prev,
              sessionWarning: true,
            }));
          }
        }

        // Record successful check
        SecurityUtils.recordSecuritySuccess();
      } catch (error) {
        console.error('❌ Session timeout check failed:', error);
        SecurityUtils.recordSecurityAttempt();
      } finally {
        SecurityUtils.endSecurityOperation(operationId);
      }
    };

    // Reduced frequency to prevent excessive checking (every 2 minutes instead of 1)
    const interval = setInterval(checkSessionTimeout, 120_000);

    // Initial check with delay to allow other security operations to complete
    setTimeout(checkSessionTimeout, 1000);

    return () => clearInterval(interval);
  }, [signOut]);

  // Update concurrent sessions periodically
  useEffect(() => {
    const updateConcurrentSessions = () => {
      SessionManager.manageConcurrentSessions();
      // Note: We could fetch and update concurrent sessions state here if needed
    };

    const interval = setInterval(updateConcurrentSessions, 5 * 60 * 1000); // Every 5 minutes

    return () => clearInterval(interval);
  }, []);

  return {
    clearSession,
    concurrentSessions: state.concurrentSessions,
    handleCrossTabLogout,
    // State
    isSessionActive: state.isSessionActive,
    isSessionExpired: state.isSessionExpired,
    lastActivity: state.lastActivity,

    refreshSession,
    sessionId: state.sessionId,
    sessionWarning: state.sessionWarning,
    // Actions
    updateActivity,
  };
}
