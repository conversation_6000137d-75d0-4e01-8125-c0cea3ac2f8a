import type { NextFunction, Request, Response } from 'express';

import HttpError from '../utils/HttpError.js';
import logger, { createContextLogger } from '../utils/logger.js';
import { logAuditEvent } from '../utils/auditLogger.js';
import type { ApiErrorResponse } from '../types/response.types.js';

const errorHandler = (
  err: Error | HttpError,
  req: Request,
  res: Response,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  next: NextFunction, // Must be defined for Express to recognize it as an error handler
): void => {
  // Create context logger with request information
  const contextLogger = createContextLogger({
    ip: req.ip,
    method: req.method,
    requestId: req.requestId,
    service: 'error-handler',
    url: req.url,
    userAgent: req.headers['user-agent'],
    userId: req.user?.id,
  });

  const isProduction = process.env.NODE_ENV === 'production';
  let statusCode = 500;
  let message = isProduction ? 'An unexpected internal server error occurred.' : err.message;
  let code = 'INTERNAL_SERVER_ERROR';

  // Log error with full context
  if (err instanceof HttpError) {
    statusCode = err.status;
    message = err.message;
    code = err.code;
    // Log HTTP errors as warnings (client errors)
    contextLogger.warn('HTTP Error encountered', {
      message: err.message,
      stack: err.stack,
      statusCode,
      code,
    });
  } else {
    // Log unexpected errors as errors (server errors)
    contextLogger.error('Unexpected error encountered', {
      message: err.message,
      name: err.name,
      stack: err.stack,
    });

    // Add audit log for unexpected server errors
    logAuditEvent(
      {
        eventType: 'SYSTEM_EVENT',
        action: 'UNEXPECTED_SERVER_ERROR',
        outcome: 'FAILURE',
        userId: req.user?.id,
        userRole: req.user?.role,
        message: `Unexpected server error: ${err.message}`,
        statusCode: 500,
        errorCode: code,
        details: {
          errorName: err.name,
          errorMessage: err.message,
          stack: err.stack?.substring(0, 500),
          path: req.path,
          method: req.method,
          requestId: req.requestId,
        },
      },
      req,
    );
  }

  // Create standardized error response
  const errorResponse: ApiErrorResponse = {
    status: 'error' as const,
    code,
    message,
    statusCode,
    error: {
      message: err.message,
      ...(isProduction ? {} : { stack: err.stack }),
      ...(err instanceof HttpError && err.details ? { details: err.details } : {}),
    },
    requestId: req.requestId,
    timestamp: new Date().toISOString(),
  };

  res.status(statusCode).json(errorResponse);
};

export default errorHandler;
