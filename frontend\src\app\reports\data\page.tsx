// frontend/src/app/reports/data/page.tsx

import { Metadata } from 'next';
import { ReportingDashboard } from '@/components/features/reporting/dashboard/ReportingDashboard';

export const metadata: Metadata = {
  title: 'Data View - Reports - WorkHub',
  description: 'Raw delegation data in tabular format for detailed analysis',
};

/**
 * Data View Page
 * 
 * Table-focused view of the reporting dashboard.
 * Uses the existing reporting dashboard with data tab pre-selected.
 */
export default function DataViewPage() {
  return <ReportingDashboard />;
}
