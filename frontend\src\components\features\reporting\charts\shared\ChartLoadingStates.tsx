// frontend/src/components/features/reporting/charts/shared/ChartLoadingStates.tsx

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';

/**
 * Props for loading state components
 */
interface LoadingStateProps {
  height?: number;
  title?: string;
  description?: string;
  className?: string;
}

/**
 * Generic chart loading skeleton
 * 
 * Follows SRP: Only responsible for displaying loading state
 */
export const ChartLoadingSkeleton: React.FC<LoadingStateProps> = ({
  height = 400,
  title = "Loading Chart",
  description = "Please wait while we load your data...",
  className = '',
}) => (
  <Card className={className}>
    <CardHeader>
      <CardTitle>
        <Skeleton className="h-6 w-48" />
      </CardTitle>
      <CardDescription>
        <Skeleton className="h-4 w-64" />
      </CardDescription>
    </CardHeader>
    <CardContent>
      <div className="animate-pulse">
        <div 
          className="flex items-center justify-center bg-gray-100 rounded-lg"
          style={{ height: height - 100 }}
        >
          <div className="text-gray-400 text-4xl">📊</div>
        </div>
        
        {/* Legend Skeleton */}
        <div className="flex justify-center gap-4 mt-4">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="flex items-center gap-2">
              <Skeleton className="w-3 h-3 rounded-full" />
              <Skeleton className="w-16 h-4" />
            </div>
          ))}
        </div>
      </div>
    </CardContent>
  </Card>
);

/**
 * Pie chart specific loading skeleton
 */
export const PieChartLoadingSkeleton: React.FC<LoadingStateProps> = ({
  height = 400,
  className = '',
}) => (
  <Card className={className}>
    <CardHeader>
      <Skeleton className="h-6 w-48 mb-2" />
      <Skeleton className="h-4 w-64" />
    </CardHeader>
    <CardContent>
      <div className="animate-pulse">
        <div className="flex justify-center items-center" style={{ height: height - 150 }}>
          <div className="w-48 h-48 bg-gray-200 rounded-full relative">
            {/* Pie slice skeletons */}
            <div className="absolute inset-0 rounded-full border-8 border-blue-200 border-t-transparent"></div>
            <div className="absolute inset-2 rounded-full border-8 border-green-200 border-r-transparent"></div>
            <div className="absolute inset-4 rounded-full border-8 border-yellow-200 border-b-transparent"></div>
          </div>
        </div>
        
        {/* Legend */}
        <div className="flex justify-center gap-4 mt-4">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="flex items-center gap-2">
              <Skeleton className="w-3 h-3 rounded-full" />
              <Skeleton className="w-16 h-4" />
            </div>
          ))}
        </div>
      </div>
    </CardContent>
  </Card>
);

/**
 * Bar chart specific loading skeleton
 */
export const BarChartLoadingSkeleton: React.FC<LoadingStateProps> = ({
  height = 400,
  className = '',
}) => (
  <Card className={className}>
    <CardHeader>
      <Skeleton className="h-6 w-48 mb-2" />
      <Skeleton className="h-4 w-64" />
    </CardHeader>
    <CardContent>
      <div className="animate-pulse">
        <div className="flex items-end justify-between gap-2 mb-4" style={{ height: height - 150 }}>
          {[1, 2, 3, 4, 5, 6, 7].map((i) => (
            <Skeleton
              key={i}
              className="rounded-t"
              style={{
                width: '12%',
                height: `${Math.random() * 80 + 20}%`,
              }}
            />
          ))}
        </div>
        
        {/* X-axis labels */}
        <div className="flex justify-between">
          {[1, 2, 3, 4, 5, 6, 7].map((i) => (
            <Skeleton key={i} className="w-8 h-3" />
          ))}
        </div>
      </div>
    </CardContent>
  </Card>
);

/**
 * Line chart specific loading skeleton
 */
export const LineChartLoadingSkeleton: React.FC<LoadingStateProps> = ({
  height = 400,
  className = '',
}) => (
  <Card className={className}>
    <CardHeader>
      <Skeleton className="h-6 w-48 mb-2" />
      <Skeleton className="h-4 w-64" />
    </CardHeader>
    <CardContent>
      <div className="animate-pulse">
        <div className="relative" style={{ height: height - 150 }}>
          {/* Grid lines */}
          <div className="absolute inset-0">
            {[1, 2, 3, 4, 5].map((i) => (
              <div
                key={i}
                className="absolute w-full border-t border-gray-200"
                style={{ top: `${i * 20}%` }}
              />
            ))}
          </div>
          
          {/* Line path skeleton */}
          <svg className="absolute inset-0 w-full h-full">
            <path
              d="M 0,80 Q 50,60 100,70 T 200,50 T 300,60 T 400,40"
              stroke="#e5e7eb"
              strokeWidth="3"
              fill="none"
              className="animate-pulse"
            />
          </svg>
          
          {/* Data points */}
          {[1, 2, 3, 4, 5, 6, 7].map((i) => (
            <div
              key={i}
              className="absolute w-2 h-2 bg-gray-300 rounded-full"
              style={{
                left: `${i * 14}%`,
                top: `${Math.random() * 60 + 20}%`,
              }}
            />
          ))}
        </div>
        
        {/* Legend */}
        <div className="flex justify-center gap-6 mt-4">
          {['Created', 'Completed', 'In Progress'].map((label) => (
            <div key={label} className="flex items-center gap-2">
              <Skeleton className="w-3 h-3 rounded-full" />
              <Skeleton className="w-16 h-4" />
            </div>
          ))}
        </div>
      </div>
    </CardContent>
  </Card>
);

/**
 * Metrics dashboard loading skeleton
 */
export const MetricsLoadingSkeleton: React.FC<LoadingStateProps> = ({
  height = 400,
  className = '',
}) => (
  <Card className={className}>
    <CardHeader>
      <Skeleton className="h-6 w-48 mb-2" />
      <Skeleton className="h-4 w-64" />
    </CardHeader>
    <CardContent className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {[1, 2, 3, 4].map((i) => (
          <div key={i} className="p-4 border rounded-lg animate-pulse">
            <Skeleton className="w-8 h-8 rounded mb-2" />
            <Skeleton className="w-16 h-6 mb-1" />
            <Skeleton className="w-20 h-4" />
          </div>
        ))}
      </div>
      
      {/* Progress Bar */}
      <div className="space-y-2">
        <div className="flex justify-between">
          <Skeleton className="w-32 h-4" />
          <Skeleton className="w-12 h-4" />
        </div>
        <Skeleton className="w-full h-2 rounded-full" />
      </div>
      
      {/* Chart Area */}
      <div className="flex justify-center items-center" style={{ height: height - 250 }}>
        <div className="w-48 h-48 bg-gray-200 rounded-full animate-pulse"></div>
      </div>
    </CardContent>
  </Card>
);

/**
 * Error state component
 */
interface ErrorStateProps extends LoadingStateProps {
  error: string;
  onRetry?: () => void;
}

export const ChartErrorState: React.FC<ErrorStateProps> = ({
  height = 400,
  title = "Chart Error",
  description = "Failed to load chart data",
  error,
  onRetry,
  className = '',
}) => (
  <Card className={className}>
    <CardHeader>
      <CardTitle>{title}</CardTitle>
      <CardDescription>{description}</CardDescription>
    </CardHeader>
    <CardContent>
      <div 
        className="flex flex-col items-center justify-center text-gray-500"
        style={{ height: height - 100 }}
      >
        <div className="text-4xl mb-4">⚠️</div>
        <p className="text-sm text-center mb-2">Unable to load chart data</p>
        <p className="text-xs text-gray-400 text-center mb-4">{error}</p>
        {onRetry && (
          <button
            onClick={onRetry}
            className="px-4 py-2 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
          >
            Try Again
          </button>
        )}
      </div>
    </CardContent>
  </Card>
);

/**
 * Empty state component
 */
export const ChartEmptyState: React.FC<LoadingStateProps & { icon?: string }> = ({
  height = 400,
  title = "No Data",
  description = "No data available to display",
  icon = "📊",
  className = '',
}) => (
  <Card className={className}>
    <CardHeader>
      <CardTitle>{title}</CardTitle>
      <CardDescription>{description}</CardDescription>
    </CardHeader>
    <CardContent>
      <div 
        className="flex flex-col items-center justify-center text-gray-500"
        style={{ height: height - 100 }}
      >
        <div className="text-4xl mb-4">{icon}</div>
        <p className="text-sm text-center">No data to display</p>
      </div>
    </CardContent>
  </Card>
);
