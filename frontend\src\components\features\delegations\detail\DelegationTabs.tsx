/**
 * @file DelegationTabs component for organizing delegation detail content
 * @module components/delegations/detail/DelegationTabs
 */

import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { DelegationOverviewCard } from './DelegationOverviewCard';
import { FlightDetailsCard } from './FlightDetailsCard';
import {
  DelegatesCard,
  EscortsCard,
  DriversCard,
  VehiclesCard,
} from './assignments';
import StatusHistoryCard from './StatusHistoryCard';
import type { Delegation } from '@/lib/types/domain';

interface DelegationTabsProps {
  delegation: Delegation;
  onStatusUpdate?: (status: any, reason: string) => Promise<void>;
  className?: string;
}

/**
 * DelegationTabs component for organizing delegation detail content
 * Provides tab-based navigation for different sections of delegation information
 */
export function DelegationTabs({
  delegation,
  onStatusUpdate,
  className,
}: DelegationTabsProps) {
  return (
    <Tabs defaultValue="overview" className={className}>
      <TabsList className="grid w-full grid-cols-4 mb-6">
        <TabsTrigger value="overview" className="text-sm">
          Overview
        </TabsTrigger>
        <TabsTrigger value="assignments" className="text-sm">
          Assignments
        </TabsTrigger>
        <TabsTrigger value="flights" className="text-sm">
          Flights
        </TabsTrigger>
        <TabsTrigger value="history" className="text-sm">
          History
        </TabsTrigger>
      </TabsList>

      <TabsContent value="overview" className="space-y-6 mt-0">
        <DelegationOverviewCard delegation={delegation} />
      </TabsContent>

      <TabsContent value="assignments" className="space-y-6 mt-0">
        <div className="grid gap-6 lg:grid-cols-2">
          <DelegatesCard delegation={delegation} />
          <EscortsCard delegation={delegation} />
          <DriversCard delegation={delegation} />
          <VehiclesCard delegation={delegation} />
        </div>
      </TabsContent>

      <TabsContent value="flights" className="space-y-6 mt-0">
        <FlightDetailsCard delegation={delegation} />
      </TabsContent>

      <TabsContent value="history" className="space-y-6 mt-0">
        {onStatusUpdate ? (
          <StatusHistoryCard
            currentStatus={delegation.status as any}
            delegationId={delegation.id}
            statusHistory={delegation.statusHistory || []}
            onStatusUpdate={onStatusUpdate}
          />
        ) : (
          <div className="rounded-lg border border-gray-200 bg-white p-8 text-center dark:border-gray-700 dark:bg-gray-800">
            <div className="mx-auto mb-4 h-12 w-12 rounded-full bg-blue-50 dark:bg-blue-900/30 flex items-center justify-center">
              <span className="text-2xl">📊</span>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              Status History
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              Status history will be displayed here when available.
            </p>
          </div>
        )}
      </TabsContent>
    </Tabs>
  );
}

export default DelegationTabs;
