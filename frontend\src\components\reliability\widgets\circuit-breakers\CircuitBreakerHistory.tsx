/**
 * @file Circuit breaker history widget component.
 * This component provides historical data and trends for circuit breaker state changes,
 * recovery patterns, and failure analysis over time.
 * @module components/reliability/widgets/circuit-breakers/CircuitBreakerHistory
 */

'use client';

import {
  AlertTriangle,
  Calendar,
  Clock,
  History,
  Shield,
  ShieldAlert,
  ShieldCheck,
  TrendingDown,
  TrendingUp,
} from 'lucide-react';
import React, { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useCircuitBreakerStatus } from '@/lib/stores/queries/useReliability';
import type { CircuitBreakerState } from '@/lib/types/domain';
import { cn } from '@/lib/utils';

/**
 * Props for the CircuitBreakerHistory component
 */
export interface CircuitBreakerHistoryProps {
  /** Optional CSS class name for styling */
  className?: string;
  /** Time range for historical data */
  timeRange?: '1h' | '6h' | '24h' | '7d' | '30d';
}

/**
 * Mock historical data for demonstration
 * In a real implementation, this would come from the API
 */
const generateMockHistoryData = (timeRange: string) => {
  const now = new Date();
  const events = [];

  // Generate mock events based on time range
  const hoursBack =
    timeRange === '1h'
      ? 1
      : timeRange === '6h'
        ? 6
        : timeRange === '24h'
          ? 24
          : timeRange === '7d'
            ? 168
            : 720;
  const eventCount = Math.min(hoursBack / 2, 20); // Limit to 20 events max

  for (let i = 0; i < eventCount; i++) {
    const timestamp = new Date(
      now.getTime() - Math.random() * hoursBack * 60 * 60 * 1000
    );
    const states: CircuitBreakerState[] = ['CLOSED', 'OPEN', 'HALF_OPEN'];
    const fromState = states[Math.floor(Math.random() * states.length)];
    const toState = states[Math.floor(Math.random() * states.length)];

    if (fromState !== toState) {
      events.push({
        id: `event-${i}`,
        circuitBreakerName: `service-${Math.floor(Math.random() * 5) + 1}`,
        fromState,
        toState,
        timestamp: timestamp.toISOString(),
        reason:
          fromState === 'CLOSED' && toState === 'OPEN'
            ? 'Failure threshold exceeded'
            : fromState === 'OPEN' && toState === 'HALF_OPEN'
              ? 'Timeout period elapsed'
              : fromState === 'HALF_OPEN' && toState === 'CLOSED'
                ? 'Test request succeeded'
                : fromState === 'HALF_OPEN' && toState === 'OPEN'
                  ? 'Test request failed'
                  : 'State transition',
        failureCount: Math.floor(Math.random() * 10),
      });
    }
  }

  return events.sort(
    (a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
  );
};

/**
 * Circuit breaker history widget component.
 *
 * This component provides:
 * - Historical circuit breaker state changes
 * - Recovery pattern analysis
 * - Failure trend visualization
 * - Time-based filtering
 *
 * Features:
 * - Time range selection
 * - State transition timeline
 * - Failure analysis
 * - Recovery metrics
 * - Responsive design
 *
 * @param props - Component props
 * @returns JSX element representing the circuit breaker history
 */
export const CircuitBreakerHistory: React.FC<CircuitBreakerHistoryProps> = ({
  className = '',
  timeRange: initialTimeRange = '24h',
}) => {
  const {
    data: circuitBreakerData,
    isLoading,
    error,
  } = useCircuitBreakerStatus();
  const [selectedTimeRange, setSelectedTimeRange] = useState(initialTimeRange);

  /**
   * Get state configuration for circuit breaker states
   */
  const getStateConfig = (state: CircuitBreakerState) => {
    switch (state) {
      case 'CLOSED':
        return {
          icon: ShieldCheck,
          color: 'text-green-600 dark:text-green-400',
          bgColor: 'bg-green-100 dark:bg-green-900/20',
          label: 'Healthy',
          variant: 'default' as const,
        };
      case 'HALF_OPEN':
        return {
          icon: Shield,
          color: 'text-yellow-600 dark:text-yellow-400',
          bgColor: 'bg-yellow-100 dark:bg-yellow-900/20',
          label: 'Testing',
          variant: 'secondary' as const,
        };
      case 'OPEN':
        return {
          icon: ShieldAlert,
          color: 'text-red-600 dark:text-red-400',
          bgColor: 'bg-red-100 dark:bg-red-900/20',
          label: 'Failed',
          variant: 'destructive' as const,
        };
    }
  };

  /**
   * Format relative time
   */
  const formatRelativeTime = (timestamp: string) => {
    const now = new Date();
    const time = new Date(timestamp);
    const diffMs = now.getTime() - time.getTime();
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMinutes / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffDays > 0) return `${diffDays}d ago`;
    if (diffHours > 0) return `${diffHours}h ago`;
    if (diffMinutes > 0) return `${diffMinutes}m ago`;
    return 'Just now';
  };

  /**
   * Get transition type for styling
   */
  const getTransitionType = (
    fromState: CircuitBreakerState,
    toState: CircuitBreakerState
  ) => {
    if (fromState === 'CLOSED' && toState === 'OPEN') return 'failure';
    if (fromState === 'OPEN' && toState === 'HALF_OPEN')
      return 'recovery-attempt';
    if (fromState === 'HALF_OPEN' && toState === 'CLOSED')
      return 'recovery-success';
    if (fromState === 'HALF_OPEN' && toState === 'OPEN')
      return 'recovery-failure';
    return 'neutral';
  };

  // Generate mock history data
  const historyData = generateMockHistoryData(selectedTimeRange);

  // Handle loading state
  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>Circuit Breaker History</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Array.from({ length: 5 }).map((_, i) => (
              <div
                key={i}
                className="flex items-center space-x-4 animate-pulse"
              >
                <div className="h-10 w-10 bg-muted rounded-full"></div>
                <div className="space-y-2 flex-1">
                  <div className="h-4 bg-muted rounded w-2/3"></div>
                  <div className="h-3 bg-muted rounded w-1/2"></div>
                </div>
                <div className="h-6 w-16 bg-muted rounded"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  // Handle error state
  if (error || !circuitBreakerData) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>Circuit Breaker History</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="text-center">
              <AlertTriangle className="mx-auto h-8 w-8 text-muted-foreground mb-2" />
              <p className="text-sm text-muted-foreground">
                Failed to load circuit breaker history
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <History className="h-5 w-5" />
            Circuit Breaker History
          </CardTitle>

          <Select
            value={selectedTimeRange}
            onValueChange={value => setSelectedTimeRange(value as any)}
          >
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1h">Last Hour</SelectItem>
              <SelectItem value="6h">Last 6 Hours</SelectItem>
              <SelectItem value="24h">Last 24 Hours</SelectItem>
              <SelectItem value="7d">Last 7 Days</SelectItem>
              <SelectItem value="30d">Last 30 Days</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardHeader>

      <CardContent>
        {historyData.length === 0 ? (
          <div className="text-center py-8">
            <Calendar className="mx-auto h-8 w-8 text-muted-foreground mb-2" />
            <p className="text-sm text-muted-foreground">
              No state changes in the selected time range
            </p>
            <p className="text-xs text-muted-foreground mt-1">
              This indicates stable circuit breaker operation
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {/* Summary Stats */}
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-3 mb-6">
              <div className="text-center p-3 rounded-lg bg-muted/50">
                <div className="text-lg font-bold">{historyData.length}</div>
                <div className="text-xs text-muted-foreground">
                  State Changes
                </div>
              </div>
              <div className="text-center p-3 rounded-lg bg-muted/50">
                <div className="text-lg font-bold text-red-600">
                  {
                    historyData.filter(
                      event =>
                        event.fromState &&
                        event.toState &&
                        getTransitionType(event.fromState, event.toState) ===
                          'failure'
                    ).length
                  }
                </div>
                <div className="text-xs text-muted-foreground">Failures</div>
              </div>
              <div className="text-center p-3 rounded-lg bg-muted/50">
                <div className="text-lg font-bold text-green-600">
                  {
                    historyData.filter(
                      event =>
                        event.fromState &&
                        event.toState &&
                        getTransitionType(event.fromState, event.toState) ===
                          'recovery-success'
                    ).length
                  }
                </div>
                <div className="text-xs text-muted-foreground">Recoveries</div>
              </div>
            </div>

            {/* Timeline */}
            <div className="space-y-3">
              {historyData.map((event, index) => {
                // Add null checks for states
                if (!event.fromState || !event.toState) {
                  return null;
                }

                const fromConfig = getStateConfig(event.fromState);
                const toConfig = getStateConfig(event.toState);
                const transitionType = getTransitionType(
                  event.fromState,
                  event.toState
                );
                const FromIcon = fromConfig.icon;
                const ToIcon = toConfig.icon;

                return (
                  <div
                    key={event.id}
                    className="flex items-center gap-4 p-3 rounded-lg border bg-card"
                  >
                    {/* Timeline indicator */}
                    <div className="flex flex-col items-center">
                      <div
                        className={cn(
                          'w-3 h-3 rounded-full',
                          transitionType === 'failure'
                            ? 'bg-red-500'
                            : transitionType === 'recovery-success'
                              ? 'bg-green-500'
                              : transitionType === 'recovery-attempt'
                                ? 'bg-yellow-500'
                                : 'bg-muted-foreground'
                        )}
                      />
                      {index < historyData.length - 1 && (
                        <div className="w-px h-8 bg-border mt-2" />
                      )}
                    </div>

                    {/* Event details */}
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <span className="font-medium">
                          {event.circuitBreakerName}
                        </span>
                        <div className="flex items-center gap-2">
                          <div
                            className={cn('p-1 rounded', fromConfig.bgColor)}
                          >
                            <FromIcon
                              className={cn('h-3 w-3', fromConfig.color)}
                            />
                          </div>
                          <span className="text-muted-foreground">→</span>
                          <div className={cn('p-1 rounded', toConfig.bgColor)}>
                            <ToIcon className={cn('h-3 w-3', toConfig.color)} />
                          </div>
                        </div>
                        <Badge variant={toConfig.variant} className="text-xs">
                          {toConfig.label}
                        </Badge>
                      </div>

                      <div className="flex items-center justify-between">
                        <p className="text-sm text-muted-foreground">
                          {event.reason}
                          {event.failureCount > 0 && (
                            <span className="ml-2 text-red-600">
                              ({event.failureCount} failures)
                            </span>
                          )}
                        </p>
                        <div className="flex items-center gap-1 text-xs text-muted-foreground">
                          <Clock className="h-3 w-3" />
                          {formatRelativeTime(event.timestamp)}
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

/**
 * Default export for the CircuitBreakerHistory component
 */
export default CircuitBreakerHistory;
