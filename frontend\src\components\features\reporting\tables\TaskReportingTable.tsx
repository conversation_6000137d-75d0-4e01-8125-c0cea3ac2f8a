/**
 * @file Task Reporting Table Component - Phase 2 Implementation
 * @description Task data table component following latest shadcn/ui patterns
 *
 * SOLID Principles Applied:
 * - SRP: Single responsibility of displaying task data in table format
 * - OCP: Open for extension via column configuration and filtering options
 * - DIP: Depends on existing table abstractions and data service interfaces
 *
 * Architecture Compliance:
 * - Follows latest shadcn/ui DataTable patterns
 * - Uses TanStack React Table for advanced functionality
 * - Integrates with existing data structures and services
 * - Maintains consistent styling and behavior
 */

'use client';

import React, { useMemo } from 'react';
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { useTasks } from '@/lib/stores/queries/useTasks';
import { TaskStatusPrisma, TaskPriorityPrisma, Task } from '@/lib/types/domain';
import { format } from 'date-fns';
import {
  CheckSquare,
  Download,
  Filter,
  ArrowUpDown,
  MoreHorizontal,
  Eye,
  Edit,
  Trash2,
} from 'lucide-react';

// Task data interface for table display
interface TaskTableData {
  id: string;
  title: string;
  status: TaskStatusPrisma;
  priority: TaskPriorityPrisma;
  assignedTo: string;
  dueDate: string;
  createdAt: string;
  completedAt?: string | undefined;
  estimatedHours: number;
  actualHours: number;
}

interface TaskReportingTableProps {
  className?: string;
  showExportOptions?: boolean;
  maxRows?: number;
}

/**
 * @component TaskReportingTable
 * @description Task data table following latest shadcn/ui patterns
 *
 * Responsibilities:
 * - Display task data in sortable, filterable table format
 * - Provide export functionality for task data
 * - Follow latest DataTable patterns from shadcn/ui
 * - Integrate with existing data services and filters
 * - Support pagination, sorting, and column visibility
 *
 * SOLID Principles Applied:
 * - SRP: Single responsibility of displaying task table data
 * - OCP: Open for extension via column configuration
 * - DIP: Depends on table and data service abstractions
 */
export const TaskReportingTable: React.FC<TaskReportingTableProps> = ({
  className = '',
  showExportOptions = true,
  maxRows = 100,
}) => {
  // State management for table features
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    []
  );
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = React.useState({});

  // Data fetching using real task data
  const {
    data: tasks,
    isLoading,
    error,
  } = useTasks({
    staleTime: 2 * 60 * 1000,
  });

  // Transform real task data to table format
  const tableData = useMemo<TaskTableData[]>(() => {
    if (!tasks) return [];

    // Transform real task data to table format
    return tasks.slice(0, maxRows).map((task: Task) => ({
      id: task.id,
      title: task.description, // Use description as title
      status: task.status,
      priority: task.priority,
      assignedTo:
        task.staffEmployee?.name || task.driverEmployee?.name || 'Unassigned',
      dueDate: task.deadline
        ? format(new Date(task.deadline), 'yyyy-MM-dd')
        : 'No deadline',
      createdAt: format(new Date(task.createdAt), 'yyyy-MM-dd'),
      completedAt:
        task.status === 'Completed'
          ? format(new Date(task.updatedAt), 'yyyy-MM-dd')
          : undefined,
      estimatedHours: task.estimatedDuration || 0,
      actualHours: task.estimatedDuration || 0, // Use estimated as actual for now
    }));
  }, [tasks, maxRows]);

  // Column definitions following latest shadcn/ui patterns
  const columns = useMemo<ColumnDef<TaskTableData>[]>(
    () => [
      {
        accessorKey: 'title',
        header: ({ column }) => (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="h-8 px-2"
          >
            Task Title
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        ),
        cell: ({ row }) => (
          <div className="font-medium">{row.getValue('title')}</div>
        ),
      },
      {
        accessorKey: 'status',
        header: ({ column }) => (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="h-8 px-2"
          >
            Status
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        ),
        cell: ({ row }) => {
          const status = row.getValue('status') as TaskStatusPrisma;
          return (
            <Badge variant={getStatusVariant(status)}>
              {formatStatus(status)}
            </Badge>
          );
        },
      },
      {
        accessorKey: 'priority',
        header: ({ column }) => (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="h-8 px-2"
          >
            Priority
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        ),
        cell: ({ row }) => {
          const priority = row.getValue('priority') as TaskPriorityPrisma;
          return (
            <Badge variant={getPriorityVariant(priority)}>{priority}</Badge>
          );
        },
      },
      {
        accessorKey: 'assignedTo',
        header: ({ column }) => (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="h-8 px-2"
          >
            Assigned To
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        ),
        cell: ({ row }) => (
          <div className="text-sm">{row.getValue('assignedTo')}</div>
        ),
      },
      {
        accessorKey: 'dueDate',
        header: ({ column }) => (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="h-8 px-2"
          >
            Due Date
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        ),
        cell: ({ row }) => (
          <div className="text-sm">{row.getValue('dueDate')}</div>
        ),
      },
      {
        accessorKey: 'estimatedHours',
        header: 'Est. Hours',
        cell: ({ row }) => (
          <div className="text-sm text-center">
            {row.getValue('estimatedHours')}h
          </div>
        ),
      },
      {
        accessorKey: 'actualHours',
        header: 'Actual Hours',
        cell: ({ row }) => (
          <div className="text-sm text-center">
            {row.getValue('actualHours')}h
          </div>
        ),
      },
      {
        id: 'actions',
        header: 'Actions',
        cell: ({ row }) => {
          const task = row.original;

          return (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="h-8 w-8 p-0">
                  <span className="sr-only">Open menu</span>
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuCheckboxItem
                  onClick={() => console.log('View task:', task.id)}
                >
                  <Eye className="mr-2 h-4 w-4" />
                  View Details
                </DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem
                  onClick={() => console.log('Edit task:', task.id)}
                >
                  <Edit className="mr-2 h-4 w-4" />
                  Edit Task
                </DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem
                  onClick={() => navigator.clipboard.writeText(task.id)}
                >
                  Copy Task ID
                </DropdownMenuCheckboxItem>
              </DropdownMenuContent>
            </DropdownMenu>
          );
        },
      },
    ],
    []
  );

  // Table configuration using latest patterns
  const table = useReactTable({
    data: tableData,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
  });

  // Export functionality
  const handleExportCSV = async () => {
    try {
      if (!tableData || tableData.length === 0) {
        console.warn('No data to export');
        return;
      }

      const csvData = tableData.map(task => ({
        'Task ID': task.id,
        Title: task.title,
        Status: task.status,
        Priority: task.priority,
        'Assigned To': task.assignedTo,
        'Due Date': task.dueDate,
        'Created Date': task.createdAt,
        'Completed Date': task.completedAt || '',
        'Estimated Hours': task.estimatedHours || '',
        'Actual Hours': task.actualHours || '',
      }));

      if (csvData.length === 0) {
        console.warn('No data to export');
        return;
      }

      const csvContent = [
        Object.keys(csvData[0]!).join(','),
        ...csvData.map(row => Object.values(row).join(',')),
      ].join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `task-report-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Export failed:', error);
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckSquare className="h-5 w-5" />
            Task Report
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="h-8 bg-muted rounded animate-pulse" />
            <div className="space-y-2">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="h-12 bg-muted rounded animate-pulse" />
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Error state
  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckSquare className="h-5 w-5" />
            Task Report
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground">
            <CheckSquare className="h-12 w-12 mx-auto mb-2 opacity-50" />
            <p>Failed to load task data</p>
            <p className="text-sm">{error.message}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
        <div>
          <CardTitle className="flex items-center gap-2">
            <CheckSquare className="h-5 w-5" />
            Task Report
          </CardTitle>
          <p className="text-sm text-muted-foreground mt-1">
            {tableData.length} tasks found
          </p>
        </div>
        {showExportOptions && (
          <Button
            variant="outline"
            size="sm"
            onClick={handleExportCSV}
            className="h-8"
          >
            <Download className="h-3 w-3 mr-1" />
            Export CSV
          </Button>
        )}
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Filters and Controls */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Input
              placeholder="Filter tasks..."
              value={
                (table.getColumn('title')?.getFilterValue() as string) ?? ''
              }
              onChange={event =>
                table.getColumn('title')?.setFilterValue(event.target.value)
              }
              className="max-w-sm"
            />
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="ml-auto">
                <Filter className="mr-2 h-4 w-4" />
                Columns
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {table
                .getAllColumns()
                .filter(column => column.getCanHide())
                .map(column => (
                  <DropdownMenuCheckboxItem
                    key={column.id}
                    className="capitalize"
                    checked={column.getIsVisible()}
                    onCheckedChange={value => column.toggleVisibility(!!value)}
                  >
                    {column.id}
                  </DropdownMenuCheckboxItem>
                ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {/* Data Table */}
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              {table.getHeaderGroups().map(headerGroup => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map(header => (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  ))}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map(row => (
                  <TableRow
                    key={row.id}
                    data-state={row.getIsSelected() && 'selected'}
                  >
                    {row.getVisibleCells().map(cell => (
                      <TableCell key={cell.id}>
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext()
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell
                    colSpan={columns.length}
                    className="h-24 text-center"
                  >
                    No tasks found.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>

        {/* Pagination */}
        <div className="flex items-center justify-end space-x-2 py-4">
          <div className="flex-1 text-sm text-muted-foreground">
            {table.getFilteredSelectedRowModel().rows.length} of{' '}
            {table.getFilteredRowModel().rows.length} row(s) selected.
          </div>
          <div className="space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => table.previousPage()}
              disabled={!table.getCanPreviousPage()}
            >
              Previous
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => table.nextPage()}
              disabled={!table.getCanNextPage()}
            >
              Next
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

// Utility functions for badge variants
const getStatusVariant = (
  status: TaskStatusPrisma
): 'default' | 'secondary' | 'destructive' | 'outline' => {
  switch (status) {
    case 'Completed':
      return 'default';
    case 'In_Progress':
      return 'secondary';
    case 'Cancelled':
      return 'destructive';
    default:
      return 'outline';
  }
};

const getPriorityVariant = (
  priority: TaskPriorityPrisma
): 'default' | 'secondary' | 'destructive' | 'outline' => {
  switch (priority) {
    case 'High':
      return 'destructive';
    case 'Medium':
      return 'secondary';
    case 'Low':
      return 'outline';
    default:
      return 'default';
  }
};

const formatStatus = (status: TaskStatusPrisma): string => {
  switch (status) {
    case 'In_Progress':
      return 'In Progress';
    default:
      return status;
  }
};
