/**
 * Table Components Index
 *
 * Centralized exports for all table-related components, providing a clean
 * interface for importing table functionality throughout the application.
 */

// Export main table components
export { DataTable } from './DataTable';
export type { DataTableProps } from './DataTable';

// Export column helpers
export {
  createSortableHeader,
  createDateColumn,
  createStatusColumn,
  createActionsColumn,
  createTextColumn,
  createNumericColumn,
  createBooleanColumn,
  createSelectionColumn,
  createTitleSubtitleColumn,
  createIconTextColumn,
  createEnhancedActionsColumn,
  statusConfigs,
} from './columnHelpers';

// Re-export TanStack Table types for convenience
export type {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  Row,
  Table as TanStackTable,
} from '@tanstack/react-table';

// Re-export shadcn/ui table components for direct use
export {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableFooter,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
