# ⚡ WorkHub Authentication - Quick Reference Guide

## 🚀 **Quick Start**

### **Environment Setup**
```bash
# Required environment variables
COOKIE_SECRET=your-secret-key-here
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
```

### **Start Backend**
```bash
npm run dev  # Development
npm start    # Production
```

---

## 🔧 **API Endpoints**

### **Authentication**
```bash
# Login (sets HttpOnly cookies)
POST /api/auth/login
Content-Type: application/json
{
  "email": "<EMAIL>",
  "password": "password123"
}

# Logout (clears cookies)
POST /api/auth/logout

# Refresh tokens
POST /api/auth/refresh

# Test authentication
GET /api/auth/test
```

### **Protected Endpoints**
```bash
# All API endpoints support dual authentication:
# 1. Authorization: Bearer <token>
# 2. HttpOnly cookies (automatic)

GET /api/vehicles
GET /api/tasks
GET /api/employees
GET /api/delegations
```

---

## 🍪 **Cookie Behavior**

### **Automatic Cookie Setting**
```javascript
// After successful login, cookies are automatically set:
// sb-access-token=token.timestamp.signature; HttpOnly; Secure; SameSite=Lax
// sb-refresh-token=token.timestamp.signature; HttpOnly; Secure; SameSite=Strict
```

### **Cookie Verification**
```typescript
// JWT middleware automatically:
1. Extracts signed cookie value
2. Verifies HMAC signature
3. Checks timestamp expiration
4. Falls back to Authorization header if needed
```

---

## 🔍 **Debugging**

### **Enable Debug Mode**
```typescript
// Add to any route for detailed auth debugging:
import { debugAuthMiddleware } from '../middleware/debugAuth.middleware.js';

router.use(debugAuthMiddleware);        // Before auth
router.use(enhancedAuthenticateUser);   // Auth middleware
```

### **Debug Output**
```
🔍 BACKEND AUTH DEBUG
📋 Request Details: {method, path, ip}
📨 Headers Analysis: {authorization, cookie}
🔑 Token Analysis: {cookieAccessToken, bearerToken}
🧪 TOKEN VALIDATION: ✅ SUCCESS
📋 EXTRACTED CLAIMS: {employeeId, userRole, isActive}
```

### **Common Debug Commands**
```bash
# Check backend logs
tail -f backend.log | grep "AUTH DEBUG"

# Test login with curl
curl -X POST http://localhost:3001/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password"}' \
  -c cookies.txt -v

# Test API with cookies
curl -X GET http://localhost:3001/api/vehicles \
  -b cookies.txt -v
```

---

## ⚠️ **Troubleshooting**

### **Common Issues**

| Issue | Symptom | Solution |
|-------|---------|----------|
| **401 Unauthorized** | API calls fail | Check token validity, login again |
| **Cookie not set** | No sb-access-token | Verify login endpoint response |
| **Signature failed** | Cookie verification error | Check COOKIE_SECRET matches |
| **Token expired** | Intermittent 401s | Implement token refresh |

### **Quick Fixes**
```bash
# Clear browser cookies
# In browser console:
document.cookie.split(";").forEach(c => {
  document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/");
});

# Restart backend with clean state
npm run dev

# Check environment variables
echo $COOKIE_SECRET
echo $SUPABASE_URL
```

---

## 🛡️ **Security Checklist**

### **Production Deployment**
- [ ] COOKIE_SECRET set to secure random value
- [ ] HTTPS enabled for all endpoints
- [ ] Secure cookie flags enabled
- [ ] SameSite policies configured
- [ ] Domain validation active
- [ ] Debug middleware removed

### **Development Best Practices**
- [ ] Never log actual tokens (use token hashes)
- [ ] Rotate COOKIE_SECRET periodically
- [ ] Monitor authentication failure rates
- [ ] Implement proper error handling
- [ ] Use HTTPS even in development

---

## 📊 **Monitoring**

### **Key Metrics**
```bash
# Authentication success rate
grep "AUTHENTICATION_SUCCESS" backend.log | wc -l

# Cookie verification success
grep "Successfully extracted token from signed cookie" backend.log | wc -l

# Security events
grep "SUSPICIOUS_TOKEN_REFRESH" backend.log
```

### **Health Checks**
```bash
# Test authentication flow
curl -s http://localhost:3001/api/auth/test \
  -H "Authorization: Bearer $TOKEN" | jq .

# Check server health
curl -s http://localhost:3001/api/health | jq .
```

---

## 🔄 **Token Lifecycle**

### **Access Token**
- **Lifetime**: 15 minutes (configurable)
- **Storage**: HttpOnly cookie + localStorage
- **Refresh**: Automatic via refresh token

### **Refresh Token**
- **Lifetime**: 7 days (configurable)
- **Storage**: HttpOnly cookie only
- **Rotation**: New token on each refresh

### **Cookie Expiration**
- **Access Cookie**: 24 hours
- **Refresh Cookie**: 7 days
- **Signature Check**: Prevents tampering

---

## 🎯 **Frontend Integration**

### **No Changes Required**
```typescript
// Frontend continues to work automatically:
// 1. Existing Authorization headers work
// 2. HttpOnly cookies work automatically
// 3. No code changes needed
```

### **Optional Enhancements**
```typescript
// Add authentication-aware prefetching:
const { isAuthenticated } = useAuth();
if (isAuthenticated) {
  // Prefetch data
}
```

---

## 📞 **Quick Support**

### **Log Analysis**
```bash
# Find authentication issues
grep -E "(401|AUTHENTICATION_FAILURE)" backend.log

# Check cookie processing
grep "cookie" backend.log | tail -20

# Monitor token validation
grep "TOKEN_VALIDATION" backend.log | tail -10
```

### **Emergency Reset**
```bash
# Clear all authentication state
1. Clear browser cookies
2. Clear localStorage
3. Restart backend server
4. Re-login user
```

---

**Status**: ✅ **READY FOR PRODUCTION**  
**Last Updated**: June 22, 2025  
**Version**: 1.0.0
