/**
 * Prettier 2025 Configuration for WorkHub Backend
 * Modern formatting rules optimized for TypeScript and Node.js development
 */
export default {
  // Line formatting
  printWidth: 100, // Increased from 80 for modern wide screens
  tabWidth: 2,
  useTabs: false, // Spaces are more consistent across editors

  // Quotes and semicolons
  semi: true,
  singleQuote: true,
  quoteProps: 'as-needed',

  // Trailing commas (ES2017+ support)
  trailingComma: 'all', // Better for git diffs and easier array/object manipulation

  // Brackets and spacing
  bracketSpacing: true,
  bracketSameLine: false,

  // Arrow functions
  arrowParens: 'avoid', // Cleaner for single parameter functions

  // Line endings (consistent across platforms)
  endOfLine: 'lf',

  // Embedded language formatting
  embeddedLanguageFormatting: 'auto',

  // HTML whitespace sensitivity (for any template strings)
  htmlWhitespaceSensitivity: 'css',

  // Prose wrapping (for markdown/comments)
  proseWrap: 'preserve',

  // Vue files (if we add Vue later)
  vueIndentScriptAndStyle: false,

  // Override for specific file types
  overrides: [
    {
      files: '*.json',
      options: {
        printWidth: 120,
        tabWidth: 2,
      },
    },
    {
      files: '*.md',
      options: {
        printWidth: 80,
        proseWrap: 'always',
      },
    },
    {
      files: '*.yml',
      options: {
        tabWidth: 2,
        singleQuote: false,
      },
    },
  ],
};
