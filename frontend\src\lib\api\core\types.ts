/**
 * @file Common API types and interfaces.
 * @module api/base/types
 */

/**
 * Configuration interface for the ApiClient.
 * @property baseURL - The base URL for API requests.
 * @property timeout - Optional. The request timeout in milliseconds. Defaults to 10000 (10 seconds).
 * @property retryAttempts - Optional. The number of retry attempts for failed requests. Defaults to 3.
 * @property headers - Optional. Default headers to be sent with every request.
 * @property authToken - Optional. Authentication token to be included in requests.
 * @property getAuthToken - Optional. Function to get the current authentication token dynamically.
 */
export interface ApiClientConfig {
  authToken?: string;
  baseURL: string;
  getAuthToken?: () => string | null;
  headers?: Record<string, string>;
  retryAttempts?: number;
  timeout?: number;
}

/**
 * Represents a generic API error response structure.
 * This can be extended or specialized for specific API error formats.
 */
export interface ApiErrorResponse {
  code?: string;
  details?: any;
  message: string;
}

/**
 * Represents a generic request payload for creation operations.
 * @template T - The type of the data to be created.
 */
export interface CreateRequest<T> {
  data: T;
}

/**
 * Defines the supported HTTP methods.
 */
export type HttpMethod = 'DELETE' | 'GET' | 'PATCH' | 'POST' | 'PUT';

// Legacy PaginatedApiResponse interface removed - use PaginatedResponse from types/index.ts instead

/**
 * Represents a raw HTTP response structure from fetch operations.
 * This represents the low-level HTTP response details.
 * @template T - The type of the data payload in the response.
 */
export interface RawHttpResponse<T = any> {
  data: T;
  headers: Headers;
  status: number;
  statusText: string;
  url?: string;
}

/**
 * Configuration interface for individual API requests.
 * @property headers - Optional. Headers specific to this request, overriding default client headers.
 * @property timeout - Optional. Timeout specific to this request, overriding client default.
 * @property signal - Optional. An AbortSignal to cancel the request.
 */
export interface RequestConfig {
  headers?: Record<string, string>;
  retryDelay?: number; // Added for custom retry delay per request
  signal?: AbortSignal;
  timeout?: number;
}

/**
 * Represents a generic request payload for update operations.
 * @template T - The type of the data to be updated.
 */
export interface UpdateRequest<T> {
  data: Partial<T>;
}
