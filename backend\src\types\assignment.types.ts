/**
 * TypeScript interfaces for Phase 1 Assignment Structures
 * WorkHub Backend API Enhancement
 */

// Assignment Validation Types
export interface AssignmentValidationRequest {
  driverEmployeeId?: number;
  escortEmployeeId?: number;
  staffEmployeeId?: number;
  vehicleId?: number;
}

export interface AssignmentValidationResponse {
  code?: string;
  error?: string;
  isValid: boolean;
}

export interface DelegateCreateRequest {
  name: string;
  notes?: string;
  title: string;
}

export interface DelegateResponse {
  createdAt: string;
  delegationId: string;
  id: string;
  name: string;
  notes?: string;
  title: string;
  updatedAt: string;
}

// Delegation Assignment Interfaces
export interface DelegationCreateRequest {
  delegates?: DelegateCreateRequest[];
  driverEmployeeId?: number;
  durationFrom: string;
  durationTo: string;
  // NEW PHASE 1 ASSIGNMENT FIELDS
  escortEmployeeId: number;
  eventName: string;
  flightArrivalDetails?: FlightDetailsCreateRequest;
  flightDepartureDetails?: FlightDetailsCreateRequest;
  imageUrl?: string;
  invitationFrom?: string;
  invitationTo?: string;
  location: string;
  notes?: string;
  status?: 'Cancelled' | 'Completed' | 'In_Progress' | 'Planned';
  vehicleId?: number;
}

export interface DelegationResponse {
  createdAt: string;
  delegates: DelegateResponse[];
  driverEmployee?: EmployeeResponse;
  driverEmployeeId?: number;
  durationFrom: string;
  durationTo: string;
  // Relations
  escortEmployee: EmployeeResponse;
  // NEW PHASE 1 ASSIGNMENT FIELDS
  escortEmployeeId: number;
  eventName: string;
  flightArrivalDetails?: FlightDetailsResponse;
  flightDepartureDetails?: FlightDetailsResponse;
  id: string;
  imageUrl?: string;
  invitationFrom?: string;
  invitationTo?: string;

  location: string;
  notes?: string;
  status: string;
  statusHistory: DelegationStatusEntryResponse[];
  updatedAt: string;
  vehicle?: VehicleResponse;
  vehicleId?: number;
}

export interface DelegationStatusEntryResponse {
  changedAt: string;
  delegationId: string;
  id: string;
  reason: string;
  status: string;
}

export interface DelegationUpdateRequest {
  delegates?: DelegateCreateRequest[];
  driverEmployeeId?: number;
  durationFrom?: string;
  durationTo?: string;
  // NEW PHASE 1 ASSIGNMENT FIELDS
  escortEmployeeId?: number;
  eventName?: string;
  flightArrivalDetails?: FlightDetailsCreateRequest | null;
  flightDepartureDetails?: FlightDetailsCreateRequest | null;
  imageUrl?: string;
  invitationFrom?: string;
  invitationTo?: string;
  location?: string;
  notes?: string;
  status?: 'Cancelled' | 'Completed' | 'In_Progress' | 'Planned';
  vehicleId?: number;
}

// Supporting Response Interfaces
export interface EmployeeResponse {
  availability?: string;
  contactEmail?: string;
  contactInfo: string;
  contactMobile?: string;
  contactPhone?: string;
  department?: string;
  employeeId: string;
  fullName?: string;
  id: number;
  name: string;
  position?: string;
  role: string;
  status?: string;
}

export interface FlightDetailsCreateRequest {
  airline?: string;
  airport: string;
  dateTime: string;
  flightNumber: string;
  gate?: string;
  notes?: string;
  terminal?: string;
}

export interface FlightDetailsResponse {
  airline?: string;
  airport: string;
  createdAt: string;
  dateTime: string;
  flightNumber: string;
  gate?: string;
  id: string;
  notes?: string;
  terminal?: string;
  updatedAt: string;
}

export interface SubTaskCreateRequest {
  description: string;
  isCompleted?: boolean;
  notes?: string;
}

export interface SubTaskResponse {
  createdAt: string;
  description: string;
  id: string;
  isCompleted: boolean;
  notes?: string;
  taskId: string;
  updatedAt: string;
}

// Task Assignment Interfaces
export interface TaskCreateRequest {
  dateTime: string;
  deadline?: string;
  description: string;
  driverEmployeeId?: number;
  estimatedDuration: number;
  location: string;
  notes?: string;
  priority: 'Critical' | 'High' | 'Low' | 'Medium';
  requiredSkills?: string[];
  // NEW PHASE 1 ASSIGNMENT FIELDS
  staffEmployeeId: number;
  status?: 'Cancelled' | 'Completed' | 'In_Progress' | 'Pending';
  subTasks?: SubTaskCreateRequest[];
  vehicleId?: number;
}

// Response Interfaces with Relations
export interface TaskResponse {
  createdAt: string;
  dateTime: string;
  deadline?: string;
  description: string;
  driverEmployee?: EmployeeResponse;
  driverEmployeeId?: number;
  estimatedDuration: number;
  id: string;
  location: string;
  notes?: string;
  priority: string;
  requiredSkills: string[];
  staffEmployee: EmployeeResponse;
  // NEW PHASE 1 ASSIGNMENT FIELDS
  staffEmployeeId: number;
  status: string;

  statusHistory: TaskStatusEntryResponse[];
  // REMOVED: assignedEmployees - replaced by specific staff/driver assignments
  subTasks: SubTaskResponse[];
  updatedAt: string;
  // Relations - Updated for Phase 1 assignment structure
  vehicle?: VehicleResponse;
  vehicleId?: number;
}

export interface TaskStatusEntryResponse {
  changedAt: string;
  id: string;
  reason: string;
  status: string;
  taskId: string;
}

export interface TaskUpdateRequest {
  dateTime?: string;
  deadline?: string;
  description?: string;
  driverEmployeeId?: number;
  estimatedDuration?: number;
  location?: string;
  notes?: string;
  priority?: 'Critical' | 'High' | 'Low' | 'Medium';
  requiredSkills?: string[];
  // NEW PHASE 1 ASSIGNMENT FIELDS
  staffEmployeeId?: number;
  status?: 'Cancelled' | 'Completed' | 'In_Progress' | 'Pending';
  vehicleId?: number;
}

// Validation Error Response
export interface ValidationErrorResponse {
  code: string;
  error: string;
  message: string;
}

export interface VehicleResponse {
  color?: string;
  id: number;
  licensePlate: string;
  make: string;
  model: string;
  ownerContact: string;
  ownerName: string;
  vin: string;
  year: number;
}
