import { z } from 'zod';

/**
 * PHASE 1 SECURITY HARDENING: Input Validation Schemas
 *
 * Comprehensive Zod schemas for validating and sanitizing all API inputs.
 * These schemas work with the inputValidation middleware to provide
 * robust protection against malicious input.
 */

// Common validation patterns
const emailSchema = z.string().email().max(254);
const phoneSchema = z
  .string()
  .regex(/^[\+]?[1-9][\d]{0,15}$/)
  .max(20);
const uuidSchema = z.string().uuid();
const dateSchema = z.string().datetime().or(z.date());
const positiveIntSchema = z.number().int().positive();
const nonEmptyStringSchema = z.string().min(1).max(1000);

// Employee validation schemas
export const employeeSchemas = {
  create: z.object({
    body: z.object({
      department: z.string().min(1).max(100).optional(),
      email: emailSchema,
      firstName: z.string().min(1).max(50),
      hireDate: dateSchema.optional(),
      isActive: z.boolean().default(true),
      lastName: z.string().min(1).max(50),
      phone: phoneSchema.optional(),
      position: z.string().min(1).max(100),
    }),
    params: z.object({}),
    query: z.object({}),
  }),

  getById: z.object({
    body: z.object({}),
    params: z.object({
      id: z.string().regex(/^\d+$/, 'ID must be a valid number').transform(Number),
    }),
    query: z.object({}),
  }),

  list: z.object({
    body: z.object({}),
    params: z.object({}),
    query: z
      .object({
        department: z.string().max(100).optional(),
        isActive: z
          .string()
          .regex(/^(true|false)$/)
          .transform(val => val === 'true')
          .optional(),
        limit: z.string().regex(/^\d+$/).transform(Number).optional(),
        page: z.string().regex(/^\d+$/).transform(Number).optional(),
        role: z
          .enum([
            'driver',
            'mechanic',
            'administrator',
            'office_staff',
            'manager',
            'service_advisor',
            'technician',
            'other',
          ])
          .optional(),
        search: z.string().max(100).optional(),
      })
      .passthrough(), // PHASE 2.1 FIX: Allow additional query parameters
  }),

  update: z.object({
    body: z.object({
      department: z.string().min(1).max(100).optional(),
      email: emailSchema.optional(),
      firstName: z.string().min(1).max(50).optional(),
      hireDate: dateSchema.optional(),
      isActive: z.boolean().optional(),
      lastName: z.string().min(1).max(50).optional(),
      phone: phoneSchema.optional(),
      position: z.string().min(1).max(100).optional(),
    }),
    params: z.object({
      id: z.string().regex(/^\d+$/, 'ID must be a valid number').transform(Number),
    }),
    query: z.object({}),
  }),
};

// Vehicle validation schemas
export const vehicleSchemas = {
  create: z.object({
    body: z.object({
      color: z.string().min(1).max(30).optional(),
      fuelType: z.enum(['GASOLINE', 'DIESEL', 'ELECTRIC', 'HYBRID']).optional(),
      licensePlate: z.string().min(1).max(20),
      make: z.string().min(1).max(50),
      mileage: z.number().int().min(0).optional(),
      model: z.string().min(1).max(50),
      status: z.enum(['ACTIVE', 'MAINTENANCE', 'RETIRED']).default('ACTIVE'),
      vin: z
        .string()
        .length(17)
        .regex(/^[A-HJ-NPR-Z0-9]{17}$/),
      year: z
        .number()
        .int()
        .min(1900)
        .max(new Date().getFullYear() + 1),
    }),
    params: z.object({}),
    query: z.object({}),
  }),

  getById: z.object({
    body: z.object({}),
    params: z.object({
      id: uuidSchema,
    }),
    query: z.object({}),
  }),

  list: z.object({
    body: z.object({}),
    params: z.object({}),
    query: z.object({
      fuelType: z.enum(['GASOLINE', 'DIESEL', 'ELECTRIC', 'HYBRID']).optional(),
      limit: z.string().regex(/^\d+$/).transform(Number).optional(),
      page: z.string().regex(/^\d+$/).transform(Number).optional(),
      search: z.string().max(100).optional(),
      status: z.enum(['ACTIVE', 'MAINTENANCE', 'RETIRED']).optional(),
    }),
  }),

  update: z.object({
    body: z.object({
      color: z.string().min(1).max(30).optional(),
      fuelType: z.enum(['GASOLINE', 'DIESEL', 'ELECTRIC', 'HYBRID']).optional(),
      licensePlate: z.string().min(1).max(20).optional(),
      make: z.string().min(1).max(50).optional(),
      mileage: z.number().int().min(0).optional(),
      model: z.string().min(1).max(50).optional(),
      status: z.enum(['ACTIVE', 'MAINTENANCE', 'RETIRED']).optional(),
      vin: z
        .string()
        .length(17)
        .regex(/^[A-HJ-NPR-Z0-9]{17}$/)
        .optional(),
      year: z
        .number()
        .int()
        .min(1900)
        .max(new Date().getFullYear() + 1)
        .optional(),
    }),
    params: z.object({
      id: uuidSchema,
    }),
    query: z.object({}),
  }),
};

// Service Record validation schemas
export const serviceRecordSchemas = {
  create: z.object({
    body: z.object({
      cost: z.number().min(0).max(999999.99),
      description: z.string().min(1).max(5000),
      mileage: z.number().int().min(0).optional(),
      notes: z.string().max(10000).optional(),
      performedBy: z.string().min(1).max(100).optional(),
      serviceDate: dateSchema,
      serviceType: z.string().min(1).max(100),
      vehicleId: uuidSchema,
    }),
    params: z.object({}),
    query: z.object({}),
  }),

  getById: z.object({
    body: z.object({}),
    params: z.object({
      id: uuidSchema,
    }),
    query: z.object({}),
  }),

  list: z.object({
    body: z.object({}),
    params: z.object({}),
    query: z.object({
      endDate: dateSchema.optional(),
      limit: z.string().regex(/^\d+$/).transform(Number).optional(),
      page: z.string().regex(/^\d+$/).transform(Number).optional(),
      serviceType: z.string().max(100).optional(),
      startDate: dateSchema.optional(),
      vehicleId: uuidSchema.optional(),
    }),
  }),

  update: z.object({
    body: z.object({
      cost: z.number().min(0).max(999999.99).optional(),
      description: z.string().min(1).max(5000).optional(),
      mileage: z.number().int().min(0).optional(),
      notes: z.string().max(10000).optional(),
      performedBy: z.string().min(1).max(100).optional(),
      serviceDate: dateSchema.optional(),
      serviceType: z.string().min(1).max(100).optional(),
      vehicleId: uuidSchema.optional(),
    }),
    params: z.object({
      id: uuidSchema,
    }),
    query: z.object({}),
  }),
};

// Delegation validation schemas
export const delegationSchemas = {
  create: z.object({
    body: z.object({
      destination: z.string().min(1).max(200),
      employeeId: uuidSchema,
      endDate: dateSchema,
      notes: z.string().max(2000).optional(),
      purpose: z.string().min(1).max(200),
      startDate: dateSchema,
      status: z
        .enum(['PENDING', 'APPROVED', 'ACTIVE', 'COMPLETED', 'CANCELLED'])
        .default('PENDING'),
      vehicleId: uuidSchema,
    }),
    params: z.object({}),
    query: z.object({}),
  }),

  getById: z.object({
    body: z.object({}),
    params: z.object({
      id: uuidSchema,
    }),
    query: z.object({}),
  }),

  list: z.object({
    body: z.object({}),
    params: z.object({}),
    query: z.object({
      employeeId: uuidSchema.optional(),
      endDate: dateSchema.optional(),
      limit: z.string().regex(/^\d+$/).transform(Number).optional(),
      page: z.string().regex(/^\d+$/).transform(Number).optional(),
      startDate: dateSchema.optional(),
      status: z.enum(['PENDING', 'APPROVED', 'ACTIVE', 'COMPLETED', 'CANCELLED']).optional(),
      vehicleId: uuidSchema.optional(),
    }),
  }),

  update: z.object({
    body: z.object({
      destination: z.string().min(1).max(200).optional(),
      employeeId: uuidSchema.optional(),
      endDate: dateSchema.optional(),
      notes: z.string().max(2000).optional(),
      purpose: z.string().min(1).max(200).optional(),
      startDate: dateSchema.optional(),
      status: z.enum(['PENDING', 'APPROVED', 'ACTIVE', 'COMPLETED', 'CANCELLED']).optional(),
      vehicleId: uuidSchema.optional(),
    }),
    params: z.object({
      id: uuidSchema,
    }),
    query: z.object({}),
  }),
};

// Task validation schemas
export const taskSchemas = {
  create: z.object({
    body: z.object({
      assignedTo: uuidSchema.optional(),
      delegationId: uuidSchema.optional(),
      description: z.string().min(1).max(5000),
      dueDate: dateSchema.optional(),
      priority: z.enum(['LOW', 'MEDIUM', 'HIGH', 'URGENT']).default('MEDIUM'),
      status: z.enum(['PENDING', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED']).default('PENDING'),
      title: z.string().min(1).max(200),
    }),
    params: z.object({}),
    query: z.object({}),
  }),

  getById: z.object({
    body: z.object({}),
    params: z.object({
      id: uuidSchema,
    }),
    query: z.object({}),
  }),

  list: z.object({
    body: z.object({}),
    params: z.object({}),
    query: z.object({
      assignedTo: uuidSchema.optional(),
      delegationId: uuidSchema.optional(),
      limit: z.string().regex(/^\d+$/).transform(Number).optional(),
      page: z.string().regex(/^\d+$/).transform(Number).optional(),
      priority: z.enum(['LOW', 'MEDIUM', 'HIGH', 'URGENT']).optional(),
      status: z.enum(['PENDING', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED']).optional(),
    }),
  }),

  update: z.object({
    body: z.object({
      assignedTo: uuidSchema.optional(),
      delegationId: uuidSchema.optional(),
      description: z.string().min(1).max(5000).optional(),
      dueDate: dateSchema.optional(),
      priority: z.enum(['LOW', 'MEDIUM', 'HIGH', 'URGENT']).optional(),
      status: z.enum(['PENDING', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED']).optional(),
      title: z.string().min(1).max(200).optional(),
    }),
    params: z.object({
      id: uuidSchema,
    }),
    query: z.object({}),
  }),
};

// Admin validation schemas
export const adminSchemas = {
  diagnostics: z.object({
    body: z.object({}),
    params: z.object({}),
    query: z.object({
      includeDetails: z
        .string()
        .regex(/^(true|false)$/)
        .transform(val => val === 'true')
        .optional(),
    }),
  }),
};

export default {
  adminSchemas,
  delegationSchemas,
  employeeSchemas,
  serviceRecordSchemas,
  taskSchemas,
  vehicleSchemas,
};
