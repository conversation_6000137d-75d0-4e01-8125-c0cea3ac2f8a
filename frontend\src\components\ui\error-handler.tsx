'use client';

import { Al<PERSON><PERSON>riangle, Info, XCircle } from 'lucide-react';
import React from 'react';

import type { ApiValidationError } from '@/lib/types/api';

import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { useFormToast } from '@/hooks/forms/useFormToast';
import {
  ERROR_CODE_MESSAGES,
  formatErrorForUser,
  getErrorMessage,
  isAssignmentError,
  isValidationError,
  type UserFriendlyError,
} from '@/lib/utils/errorHandling';

/**
 * PHASE 3: Consolidated Error Handler Component
 * Integrates with errorHandling.ts utilities for consistent error handling
 * Handles the {message, error, code} format from Phase 2 backend validation
 */
export interface StructuredError {
  code: string;
  error: string;
  message: string;
}

interface ErrorHandlerProps {
  className?: string;
  context?: string;
  error: Error | null | string | StructuredError;
  showToast?: boolean;
}

/**
 * Component to display structured errors from the backend
 * Handles the Phase 2 backend error format: {message, error, code}
 */
export function ErrorHandler({
  className = '',
  context,
  error,
  showToast = false,
}: ErrorHandlerProps) {
  const { showFormError } = useFormToast();

  React.useEffect(() => {
    if (error && showToast) {
      // Use centralized error message extraction
      const errorMessage = getErrorMessage(error);
      const userFriendlyError = formatErrorForUser(error, context);

      showFormError(userFriendlyError.message, {
        errorTitle: userFriendlyError.title,
      });
    }
  }, [error, showToast, showFormError, context]);

  if (!error) return null;

  // Use centralized error formatting
  const userFriendlyError = formatErrorForUser(error, context);
  const severity = getErrorSeverity(userFriendlyError.code || 'UNKNOWN_ERROR');

  return (
    <Alert className={`${className} ${getSeverityStyles(severity)}`}>
      {getErrorIcon(severity)}
      <AlertTitle>{userFriendlyError.title}</AlertTitle>
      <AlertDescription>
        {userFriendlyError.message}
        {userFriendlyError.code && (
          <span className="mt-1 block text-xs text-muted-foreground">
            Error Code: {userFriendlyError.code}
          </span>
        )}
        {userFriendlyError.field && (
          <span className="mt-1 block text-xs text-muted-foreground">
            Field: {userFriendlyError.field}
          </span>
        )}
      </AlertDescription>
    </Alert>
  );
}

/**
 * Utility function to check if an error is a structured backend error
 */
export function isStructuredError(error: any): error is StructuredError {
  return (
    error &&
    typeof error === 'object' &&
    'message' in error &&
    'error' in error &&
    'code' in error
  );
}

// Note: parseError and getErrorMessage functions removed - using centralized utilities from errorHandling.ts

/**
 * Hook for handling API errors with structured error format
 */
export function useErrorHandler() {
  const { showFormError } = useFormToast();

  const handleError = React.useCallback(
    (error: Error | string | StructuredError, context?: string) => {
      // Use centralized error formatting
      const userFriendlyError = formatErrorForUser(error, context);

      showFormError(userFriendlyError.message, {
        errorTitle: userFriendlyError.title,
      });

      // Log for debugging with full error details
      console.error('Error handled:', {
        code: userFriendlyError.code,
        context,
        field: userFriendlyError.field,
        message: userFriendlyError.message,
        originalError: error,
        title: userFriendlyError.title,
      });
    },
    [showFormError]
  );

  return { handleError };
}

/**
 * Get appropriate icon for error severity
 */
function getErrorIcon(severity: 'error' | 'info' | 'warning') {
  switch (severity) {
    case 'error': {
      return <XCircle className="size-4" />;
    }
    case 'warning': {
      return <AlertTriangle className="size-4" />;
    }
    case 'info':
    default: {
      return <Info className="size-4" />;
    }
  }
}

/**
 * Determine error severity based on error code
 */
function getErrorSeverity(code: string): 'error' | 'info' | 'warning' {
  const errorCodes = ['VALIDATION_ERROR', 'ASSIGNMENT_ERROR', 'ROLE_ERROR'];
  const warningCodes = ['BUSINESS_RULE_WARNING', 'DEPRECATION_WARNING'];

  if (errorCodes.some(ec => code.includes(ec))) {
    return 'error';
  }

  if (warningCodes.some(wc => code.includes(wc))) {
    return 'warning';
  }

  return 'info';
}

/**
 * Get CSS classes for error severity
 */
function getSeverityStyles(severity: 'error' | 'info' | 'warning'): string {
  switch (severity) {
    case 'error': {
      return 'border-destructive/50 text-destructive dark:border-destructive';
    }
    case 'warning': {
      return 'border-yellow-500/50 text-yellow-600 dark:border-yellow-500 dark:text-yellow-400';
    }
    case 'info':
    default: {
      return 'border-blue-500/50 text-blue-600 dark:border-blue-500 dark:text-blue-400';
    }
  }
}
