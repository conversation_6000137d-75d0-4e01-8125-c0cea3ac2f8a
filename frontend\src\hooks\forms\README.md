# Form Submission Hook - SOLID Principles Implementation

## Overview

The `useFormSubmission` hook has been completely refactored to follow **SOLID principles** and integrate seamlessly with the existing toast service. This implementation provides a scalable, maintainable, and extensible solution for form submissions across the WorkHub application.

## SOLID Principles Applied

### 1. Single Responsibility Principle (SRP)

Each service has one clear, focused purpose:

- **FormSubmissionConfigService**: Manages configuration merging
- **FormSubmissionToastService**: Handles toast notifications
- **FormSubmissionAccessibilityService**: Manages accessibility features
- **FormSubmissionRetryService**: Handles retry logic
- **FormSubmissionPerformanceService**: Tracks performance metrics

### 2. Open/Closed Principle

The system is open for extension through:

- Configuration options
- Service injection
- Custom entity types for toast service

### 3. Liskov Substitution Principle

Services can be substituted with compatible implementations without breaking functionality.

### 4. Interface Segregation Principle

Each service exposes only the methods relevant to its responsibility.

### 5. Dependency Inversion Principle

The hook depends on abstractions (services) rather than concrete implementations.

## Key Features

- ✅ **Integrated Toast Service**: Uses your existing `toastService.ts`
- ✅ **Entity-Specific Toasts**: Automatic entity recognition (employee, vehicle, task, etc.)
- ✅ **Comprehensive Error Handling**: Centralized error management
- ✅ **Accessibility Support**: ARIA attributes, screen reader announcements
- ✅ **Performance Tracking**: Built-in metrics and optimization
- ✅ **Retry Logic**: Configurable retry with exponential backoff
- ✅ **Type Safety**: Full TypeScript support with clear interfaces

## Basic Usage Examples

### 1. Simple Form with Entity-Specific Toast

```typescript
import { useFormSubmission } from '@/hooks/forms';

const CreateEmployeeForm = () => {
  const { handleSubmit, isLoading, error } = useFormSubmission(
    async (data) => await api.employees.create(data),
    {
      toast: {
        entityType: 'employee',
        entity: { name: data.name }, // For personalized messages
      }
    }
  );

  return (
    <form onSubmit={handleSubmit}>
      {/* Your form fields */}
      <button type="submit" disabled={isLoading}>
        {isLoading ? 'Creating...' : 'Create Employee'}
      </button>
    </form>
  );
};
```

### 2. Vehicle Form with Custom Configuration

```typescript
const CreateVehicleForm = () => {
  const { handleSubmit, state, retry, ariaAttributes } = useFormSubmission(
    async (data) => await api.vehicles.create(data),
    {
      toast: {
        entityType: 'vehicle',
        entity: data, // Full entity for toast service
        successMessage: 'Vehicle added to fleet! 🚗',
      },
      preSubmitValidation: (data) => {
        return data.make && data.model && data.year;
      },
      transformData: (data) => ({
        ...data,
        year: parseInt(data.year),
        mileage: parseFloat(data.mileage),
      }),
      retry: {
        maxAttempts: 5,
        exponentialBackoff: true,
      },
      accessibility: {
        focusManagement: 'first-error',
      },
      onSuccess: (data, result) => {
        router.push(`/vehicles/${result.id}`);
      },
    }
  );

  return (
    <form onSubmit={handleSubmit} {...ariaAttributes}>
      {/* Form implementation */}
    </form>
  );
};
```

### 3. Service Record Form

```typescript
const CreateServiceRecordForm = () => {
  const { handleSubmit, isLoading } = useFormSubmission(
    async data => await api.serviceRecords.create(data),
    {
      toast: {
        entityType: 'serviceRecord',
        entity: {
          vehicleName: selectedVehicle.make + ' ' + selectedVehicle.model,
          serviceType: data.serviceType,
        },
      },
      performance: {
        debounceMs: 500, // Longer debounce for service records
        timeoutMs: 45000, // Longer timeout for complex operations
      },
    }
  );

  // Component implementation...
};
```

### 4. Generic Form with Custom Entity

```typescript
const CustomEntityForm = () => {
  const { handleSubmit } = useFormSubmission(
    async data => await api.customEntity.create(data),
    {
      toast: {
        entityType: 'generic',
        successMessage: 'Custom entity created successfully!',
        errorMessage: 'Failed to create custom entity',
      },
    }
  );

  // Component implementation...
};
```

## Advanced Features

### Custom Entity Toast Service

```typescript
// Create a custom toast service for new entity types
import { FormSubmissionToastService } from '@/hooks/forms';

const customToastService =
  FormSubmissionToastService.createCustomEntityToastService(
    'Project',
    project => project.name
  );

// Use in your hook
const { handleSubmit } = useFormSubmission(
  async data => await api.projects.create(data),
  {
    // ... other options
  }
);
```

### Performance Monitoring

```typescript
const { metrics, submissionDuration } = useFormSubmission(
  async data => await api.submit(data),
  {
    performance: {
      debounceMs: 300,
      timeoutMs: 30000,
    },
  }
);

// Access performance metrics
console.log(
  'Success rate:',
  (metrics.successfulSubmissions / metrics.totalSubmissions) * 100
);
console.log('Average duration:', metrics.averageDuration);
console.log('Last submission took:', submissionDuration, 'ms');
```

### Accessibility Features

```typescript
const { ariaAttributes, state } = useFormSubmission(
  async (data) => await api.submit(data),
  {
    accessibility: {
      announceStatus: true,
      focusManagement: 'first-error',
      screenReaderAnnouncements: true,
      errorDescribedBy: 'custom-error-id',
    },
  }
);

// Apply to your form
<form {...ariaAttributes}>
  <input aria-describedby="custom-error-id" />
  {state === 'error' && (
    <div id="custom-error-id" role="alert">
      Error occurred
    </div>
  )}
</form>
```

## Integration with React Hook Form

```typescript
import { useForm } from 'react-hook-form';
import { useFormSubmission } from '@/hooks/forms';

const MyForm = () => {
  const form = useForm();
  const { handleSubmit: handleFormSubmission } = useFormSubmission(
    async (data) => await api.submit(data),
    {
      toast: {
        entityType: 'employee',
        entity: { name: data.name },
      },
      formReset: form.reset,
      formFocus: (fieldName) => form.setFocus(fieldName),
      resetOnSuccess: true,
    }
  );

  return (
    <form onSubmit={form.handleSubmit(handleFormSubmission)}>
      {/* Form fields using react-hook-form */}
    </form>
  );
};
```

## Migration Guide

### From Old useFormSubmission

```typescript
// OLD WAY ❌
const { handleSubmit } = useFormSubmission(
  async data => await api.submit(data),
  {
    successMessage: 'Success!',
    errorMessage: 'Error!',
    showSuccessToast: true,
    showErrorToast: true,
  }
);

// NEW WAY ✅
const { handleSubmit } = useFormSubmission(
  async data => await api.submit(data),
  {
    toast: {
      entityType: 'generic', // or specific entity type
      successMessage: 'Success!',
      errorMessage: 'Error!',
      showSuccessToast: true,
      showErrorToast: true,
    },
  }
);
```

## Available Entity Types

The toast service supports these entity types out of the box:

- `employee` - Uses `employeeToast` service
- `vehicle` - Uses `vehicleToast` service
- `task` - Uses `taskToast` service
- `delegation` - Uses `delegationToast` service
- `serviceRecord` - Uses `serviceRecordToast` service
- `generic` - Uses base `toastService`

## Error Handling

The hook provides comprehensive error handling:

```typescript
const { error, errorObject, retry, cancel } = useFormSubmission(
  async (data) => await api.submit(data),
  {
    retry: {
      maxAttempts: 3,
      exponentialBackoff: true,
      retryCondition: (error) => {
        // Custom retry logic
        return error.message.includes('network') || error.status >= 500;
      },
    },
    onError: (error, data) => {
      // Custom error handling
      console.error('Submission failed:', error);
    },
  }
);

// Manual retry
<button onClick={retry}>Retry Submission</button>

// Cancel ongoing submission
<button onClick={cancel}>Cancel</button>
```

## Best Practices

1. **Use Entity-Specific Types**: Always specify the correct `entityType` for better user experience
2. **Provide Entity Data**: Pass entity data for personalized toast messages
3. **Configure Accessibility**: Enable accessibility features for better UX
4. **Monitor Performance**: Use performance metrics to optimize form submissions
5. **Handle Errors Gracefully**: Implement proper error handling and retry logic
6. **Follow DRY Principles**: Reuse configurations across similar forms

## TypeScript Support

All services and hooks are fully typed:

```typescript
import type {
  FormSubmissionOptions,
  FormSubmissionResult,
  ToastConfig,
} from '@/hooks/forms';

const options: FormSubmissionOptions<MyFormData> = {
  toast: {
    entityType: 'vehicle',
    entity: myVehicleData,
  } as ToastConfig,
  // ... other options
};
```

This refactored implementation provides a solid foundation for form submissions while maintaining backward compatibility and following SOLID principles for better maintainability and extensibility.
