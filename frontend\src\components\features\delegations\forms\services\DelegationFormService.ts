/**
 * Delegation Form Service - Enhanced Business Logic Layer
 *
 * Handles core business logic for delegation form operations following SOLID principles.
 * This service abstracts business rules, validation, and data transformation logic.
 *
 * Enhanced features:
 * - Comprehensive validation with user-friendly messages
 * - Smart data transformation and cleanup
 * - Progress tracking and completion status
 * - Role-based validation logic
 *
 * @module DelegationFormService
 */

import type { DelegationFormData } from '@/lib/schemas/delegationSchemas';
import type { Delegation } from '@/lib/types/domain';

// ============================================================================
// INTERFACES
// ============================================================================

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings?: ValidationWarning[];
}

export interface ValidationError {
  field: string;
  message: string;
  code: string;
}

export interface ValidationWarning {
  field: string;
  message: string;
  code: string;
}

export interface ProcessedDelegationData extends DelegationFormData {
  // Additional processed fields
  calculatedDuration?: number;
  processedFlightDetails?: {
    arrival?: ProcessedFlightDetails | undefined;
    departure?: ProcessedFlightDetails | undefined;
  };
}

export interface ProcessedFlightDetails {
  flightNumber: string;
  dateTime: string;
  airport: string;
  terminal?: string | undefined;
  notes?: string | undefined;
  // Additional processed fields
  formattedDateTime?: string | undefined;
  airportCode?: string | undefined;
}

export interface DelegateData {
  name: string;
  title: string;
  notes?: string | undefined;
  id?: string | undefined; // Optional for new delegates
}

// ============================================================================
// SERVICE INTERFACE
// ============================================================================

export interface IDelegationFormService {
  // Data validation
  validateFormData(data: DelegationFormData): ValidationResult;
  validateSection(
    section: string,
    data: Partial<DelegationFormData>
  ): ValidationResult;
  validateDelegateData(delegate: DelegateData): ValidationResult;
  validateFlightDetails(details: any): ValidationResult;

  // Data transformation
  transformForSubmission(data: DelegationFormData): ProcessedDelegationData;
  transformInitialData(delegation?: Delegation): DelegationFormData;

  // Business logic
  calculateDuration(from: string, to: string): number;
  validateDateRange(from: string, to: string): boolean;

  // Field array operations
  createDefaultDelegate(): DelegateData;
  validateDelegates(delegates: DelegateData[]): ValidationResult;

  // Status operations
  getAvailableStatuses(): string[];
  validateStatusTransition(from: string, to: string): boolean;
}

// ============================================================================
// IMPLEMENTATION
// ============================================================================

/**
 * Delegation Form Service Implementation
 *
 * Provides business logic for delegation form operations.
 * This is a placeholder implementation that will be fully developed in Phase 2.
 */
export class DelegationFormService implements IDelegationFormService {
  validateFlightDetails(details: any): ValidationResult {
    const errors: ValidationError[] = [];

    if (!details) {
      return { isValid: true, errors: [] };
    }

    // Check if flight details are complete or empty
    const hasFlightNumber =
      details.flightNumber && details.flightNumber.trim() !== '';
    const hasDateTime = details.dateTime && details.dateTime.trim() !== '';
    const hasAirport = details.airport && details.airport.trim() !== '';

    // If any field is provided, all required fields must be provided
    if (hasFlightNumber || hasDateTime || hasAirport) {
      if (!hasFlightNumber) {
        errors.push({
          field: 'flightNumber',
          message: 'Flight number is required when flight details are provided',
          code: 'REQUIRED_FIELD',
        });
      }

      if (!hasDateTime) {
        errors.push({
          field: 'dateTime',
          message:
            'Date and time are required when flight details are provided',
          code: 'REQUIRED_FIELD',
        });
      }

      if (!hasAirport) {
        errors.push({
          field: 'airport',
          message: 'Airport is required when flight details are provided',
          code: 'REQUIRED_FIELD',
        });
      }

      // Validate date format if provided
      if (hasDateTime) {
        try {
          const date = new Date(details.dateTime);
          if (isNaN(date.getTime())) {
            errors.push({
              field: 'dateTime',
              message: 'Invalid date/time format',
              code: 'INVALID_FORMAT',
            });
          }
        } catch (e) {
          errors.push({
            field: 'dateTime',
            message: 'Invalid date/time format',
            code: 'INVALID_FORMAT',
          });
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }
  // Data validation methods
  validateFormData(data: DelegationFormData): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];

    // Validate basic required fields
    if (!data.eventName?.trim()) {
      errors.push({
        field: 'eventName',
        message: 'Event name is required',
        code: 'REQUIRED_FIELD',
      });
    }

    if (!data.location?.trim()) {
      errors.push({
        field: 'location',
        message: 'Location is required',
        code: 'REQUIRED_FIELD',
      });
    }

    // Validate date range
    if (!this.validateDateRange(data.durationFrom, data.durationTo)) {
      errors.push({
        field: 'durationTo',
        message: 'End date cannot be earlier than start date',
        code: 'INVALID_DATE_RANGE',
      });
    }

    // Validate delegates
    const delegateValidation = this.validateDelegates(data.delegates || []);
    if (!delegateValidation.isValid) {
      errors.push(...delegateValidation.errors);
    }

    // Business rule: Vehicles require drivers
    if (
      data.vehicleIds &&
      data.vehicleIds.length > 0 &&
      (!data.driverEmployeeIds || data.driverEmployeeIds.length === 0)
    ) {
      errors.push({
        field: 'vehicleIds',
        message:
          'Vehicles can only be assigned when at least one driver is assigned',
        code: 'VEHICLES_REQUIRE_DRIVERS',
      });
    }

    // Validate flight details if provided
    if (data.flightArrivalDetails) {
      const flightValidation = this.validateFlightDetails(
        data.flightArrivalDetails
      );
      if (!flightValidation.isValid) {
        errors.push(
          ...flightValidation.errors.map((err: ValidationError) => ({
            ...err,
            field: `flightArrivalDetails.${err.field}`,
          }))
        );
      }
    }

    if (data.flightDepartureDetails) {
      const flightValidation = this.validateFlightDetails(
        data.flightDepartureDetails
      );
      if (!flightValidation.isValid) {
        errors.push(
          ...flightValidation.errors.map((err: ValidationError) => ({
            ...err,
            field: `flightDepartureDetails.${err.field}`,
          }))
        );
      }
    }

    // Add warnings for potential issues
    if (data.delegates && data.delegates.length > 5) {
      warnings.push({
        field: 'delegates',
        message:
          'Large number of delegates may require additional coordination',
        code: 'MANY_DELEGATES',
      });
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  validateSection(
    section: string,
    data: Partial<DelegationFormData>
  ): ValidationResult {
    // TODO: Implement section-specific validation
    return {
      isValid: true,
      errors: [],
    };
  }

  validateDelegateData(delegate: DelegateData): ValidationResult {
    // TODO: Implement delegate validation
    return {
      isValid: true,
      errors: [],
    };
  }

  // Data transformation methods
  transformForSubmission(data: DelegationFormData): ProcessedDelegationData {
    const processedData: ProcessedDelegationData = {
      ...data,
      calculatedDuration: this.calculateDuration(
        data.durationFrom,
        data.durationTo
      ),
      processedFlightDetails: {
        arrival: data.flightArrivalDetails
          ? this.processFlightDetailsForSubmission(data.flightArrivalDetails)
          : undefined,
        departure: data.flightDepartureDetails
          ? this.processFlightDetailsForSubmission(data.flightDepartureDetails)
          : undefined,
      },
    };

    return processedData;
  }

  private processFlightDetailsForSubmission(
    details: any
  ): ProcessedFlightDetails {
    return {
      flightNumber: details.flightNumber?.trim() || '',
      dateTime: details.dateTime || '',
      airport: details.airport?.trim() || '',
      terminal: details.terminal?.trim() || undefined,
      notes: details.notes?.trim() || undefined,
      formattedDateTime: details.dateTime
        ? new Date(details.dateTime).toISOString()
        : undefined,
      airportCode: this.extractAirportCode(details.airport),
    };
  }

  private extractAirportCode(airport: string): string | undefined {
    if (!airport) return undefined;
    // Simple extraction - look for 3-letter codes in parentheses or at the end
    const match = airport.match(/\(([A-Z]{3})\)|([A-Z]{3})$/);
    return match ? match[1] || match[2] : undefined;
  }

  transformInitialData(delegation?: Delegation): DelegationFormData {
    // TODO: Implement transformation from API data to form data
    return {} as DelegationFormData;
  }

  // Business logic methods
  calculateDuration(from: string, to: string): number {
    try {
      const fromDate = new Date(from);
      const toDate = new Date(to);

      // Check if dates are valid
      if (isNaN(fromDate.getTime()) || isNaN(toDate.getTime())) {
        return 0;
      }

      // Calculate duration in days
      const diffTime = Math.abs(toDate.getTime() - fromDate.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      return diffDays;
    } catch (e) {
      return 0;
    }
  }

  validateDateRange(from: string, to: string): boolean {
    try {
      const fromDate = new Date(from);
      const toDate = new Date(to);

      // Check if dates are valid
      if (isNaN(fromDate.getTime()) || isNaN(toDate.getTime())) {
        return false;
      }

      // End date must be after or equal to start date
      return fromDate <= toDate;
    } catch (e) {
      return false;
    }
  }

  // Field array operations
  createDefaultDelegate(): DelegateData {
    return {
      name: '',
      title: '',
      notes: '',
    };
  }

  validateDelegates(delegates: DelegateData[]): ValidationResult {
    const errors: ValidationError[] = [];

    // At least one delegate is required
    if (!delegates || delegates.length === 0) {
      errors.push({
        field: 'delegates',
        message: 'At least one delegate is required',
        code: 'REQUIRED_FIELD',
      });
      return { isValid: false, errors };
    }

    // Validate each delegate
    delegates.forEach((delegate, index) => {
      if (!delegate.name || delegate.name.trim() === '') {
        errors.push({
          field: `delegates.${index}.name`,
          message: 'Delegate name is required',
          code: 'REQUIRED_FIELD',
        });
      }

      if (!delegate.title || delegate.title.trim() === '') {
        errors.push({
          field: `delegates.${index}.title`,
          message: 'Delegate title is required',
          code: 'REQUIRED_FIELD',
        });
      }

      // Check for duplicate names
      const duplicateIndex = delegates.findIndex(
        (d, i) =>
          i !== index &&
          d.name.trim().toLowerCase() === delegate.name.trim().toLowerCase()
      );
      if (duplicateIndex !== -1) {
        errors.push({
          field: `delegates.${index}.name`,
          message: 'Delegate names must be unique',
          code: 'DUPLICATE_VALUE',
        });
      }
    });

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  // Status operations
  getAvailableStatuses(): string[] {
    // TODO: Implement status retrieval
    return [];
  }

  validateStatusTransition(from: string, to: string): boolean {
    // TODO: Implement status transition validation
    return true;
  }
}

// ============================================================================
// SINGLETON INSTANCE
// ============================================================================

/**
 * Singleton instance of the delegation form service
 * Use this instance throughout the application for consistency
 */
export const delegationFormService = new DelegationFormService();

// ============================================================================
// EXPORTS
// ============================================================================

export default delegationFormService;
