/**
 * @file Test Suite Index
 * @module api/security/__tests__/index
 *
 * Phase 5: Testing & Validation
 * Centralized test suite for secure API architecture
 */

// Test setup
import './setup';

// Unit tests
export * from './SecurityConfigProvider.test';
export * from './useSecureApiClient.test';
export * from './SecurityComposer.test';

// Integration tests
export * from './integration.test';

// Migration tests
// Migration tests removed - migration completed

// Performance tests
export * from './performance.test';

/**
 * Test Suite Summary
 *
 * This comprehensive test suite covers:
 *
 * 1. **Unit Tests**:
 *    - SecurityConfigProvider: React context for configuration management
 *    - useSecureApiClient: Main hook for secure API interactions
 *    - SecurityComposer: Security orchestration and middleware
 *
 * 2. **Integration Tests**:
 *    - Complete architecture integration
 *    - Error handling across the stack
 *    - Performance and memory management
 *    - Backward compatibility validation
 *
 * 3. **Migration Tests**:
 *    - Migration utilities and adapters
 *    - Backward compatibility interfaces
 *    - Migration tracking and progress
 *    - Legacy API adapter functionality
 *
 * 4. **Performance Tests**:
 *    - Hook initialization performance
 *    - Request processing efficiency
 *    - Memory usage and leak detection
 *    - Security processing performance
 *    - Configuration change performance
 *
 * 5. **Test Coverage**:
 *    - All public APIs and interfaces
 *    - Error conditions and edge cases
 *    - Security feature validation
 *    - Configuration validation
 *    - Migration scenarios
 *    - Performance benchmarks
 *
 * Run tests with:
 * ```bash
 * npm test -- --testPathPattern=security
 * ```
 *
 * Run specific test suites:
 * ```bash
 * npm test SecurityConfigProvider.test.ts
 * npm test useSecureApiClient.test.ts
 * npm test integration.test.tsx
 * npm test migration.test.ts
 * npm test performance.test.ts
 * ```
 */

export const TEST_SUITE_INFO = {
  name: 'Secure API Architecture Test Suite',
  version: '1.0.0',
  coverage: {
    unit: 'SecurityConfigProvider, useSecureApiClient, SecurityComposer',
    integration: 'Complete architecture, error handling, performance',
    migration: 'Migration utilities, backward compatibility',
    performance: 'Initialization, requests, memory, security processing',
  },
  requirements: {
    node: '>=16.0.0',
    jest: '>=27.0.0',
    '@testing-library/react': '>=12.0.0',
    '@testing-library/react-hooks': '>=8.0.0',
  },
} as const;
