/**
 * @file EmployeeAnalyticsWidget.tsx
 * @description Employee analytics widget following existing patterns and SOLID principles
 */

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Users,
  TrendingUp,
  AlertTriangle,
  Clock,
  CheckCircle,
  UserCheck,
  Download,
  MoreHorizontal,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useEmployeeAnalytics } from '../../hooks/useEmployeeAnalytics';
import { DataLoader } from '@/components/ui/data-loader';
import { ErrorDisplay } from '@/components/ui/error-display';
import type { ReportingFilters } from '../../data/types/reporting';

/**
 * Props interface for EmployeeAnalyticsWidget
 */
interface EmployeeAnalyticsWidgetProps {
  filters?: ReportingFilters;
  className?: string;
  showExportOptions?: boolean;
  compact?: boolean;
}

/**
 * Metric card component for displaying individual metrics
 */
interface MetricCardProps {
  label: string;
  value: string | number;
  icon: React.ReactNode;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  variant?: 'default' | 'warning' | 'success' | 'destructive';
}

const MetricCard: React.FC<MetricCardProps> = ({
  label,
  value,
  icon,
  trend,
  variant = 'default',
}) => {
  const variantStyles = {
    default: 'border-gray-200',
    warning: 'border-orange-200 bg-orange-50',
    success: 'border-green-200 bg-green-50',
    destructive: 'border-red-200 bg-red-50',
  };

  return (
    <div className={cn('p-4 border rounded-lg', variantStyles[variant])}>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          {icon}
          <span className="text-sm font-medium text-gray-600">{label}</span>
        </div>
        {trend && (
          <Badge
            variant={trend.isPositive ? 'default' : 'destructive'}
            className="text-xs"
          >
            {trend.isPositive ? '+' : ''}
            {trend.value}%
          </Badge>
        )}
      </div>
      <div className="mt-2">
        <span className="text-2xl font-bold">{value}</span>
      </div>
    </div>
  );
};

/**
 * EmployeeAnalyticsWidget Component
 *
 * Displays comprehensive employee analytics following existing widget patterns.
 *
 * Responsibilities:
 * - Display employee analytics in widget format
 * - Integrate with existing dashboard layout
 * - Follow established widget composition patterns
 * - Maintain consistent styling and behavior
 *
 * SOLID Principles Applied:
 * - SRP: Single responsibility of displaying employee metrics
 * - OCP: Open for extension via props configuration
 * - DIP: Depends on existing widget framework abstractions
 */
export const EmployeeAnalyticsWidget: React.FC<
  EmployeeAnalyticsWidgetProps
> = ({
  filters,
  className = '',
  showExportOptions = true,
  compact = false,
}) => {
  // Use existing hook patterns
  const {
    data: employeeAnalytics,
    isLoading,
    error,
  } = useEmployeeAnalytics(filters);

  // Handle loading state
  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Employee Analytics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <DataLoader isLoading={true} data={null} error={null}>
            {() => null}
          </DataLoader>
        </CardContent>
      </Card>
    );
  }

  // Handle error state
  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Employee Analytics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ErrorDisplay error={error} />
        </CardContent>
      </Card>
    );
  }

  // Calculate derived metrics
  const totalEmployees = employeeAnalytics?.totalCount || 0;
  const activeEmployees =
    employeeAnalytics?.performanceMetrics?.filter(
      emp => emp.completedDelegations > 0 || emp.completedTasks > 0
    ).length || 0;

  const avgPerformanceScore =
    employeeAnalytics?.performanceMetrics?.length &&
    employeeAnalytics.performanceMetrics.length > 0
      ? Math.round(
          employeeAnalytics.performanceMetrics.reduce(
            (sum, emp) => sum + emp.averageRating,
            0
          ) / employeeAnalytics.performanceMetrics.length
        )
      : 0;

  const totalCompletedTasks =
    employeeAnalytics?.taskAssignments?.reduce(
      (sum, emp) => sum + emp.completedTasks,
      0
    ) || 0;

  const totalOverdueTasks =
    employeeAnalytics?.taskAssignments?.reduce(
      (sum, emp) => sum + emp.overdueTasksCount,
      0
    ) || 0;

  const avgUtilization =
    employeeAnalytics?.availabilityMetrics?.length &&
    employeeAnalytics.availabilityMetrics.length > 0
      ? Math.round(
          employeeAnalytics.availabilityMetrics.reduce(
            (sum, emp) => sum + emp.utilizationRate,
            0
          ) / employeeAnalytics.availabilityMetrics.length
        )
      : 0;

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Employee Analytics
          </CardTitle>
          <div className="flex items-center gap-2">
            <Badge variant="secondary" className="text-xs">
              {totalEmployees} employees
            </Badge>
            {totalOverdueTasks > 0 && (
              <Badge variant="destructive" className="text-xs">
                {totalOverdueTasks} overdue
              </Badge>
            )}
            {showExportOptions && (
              <Button variant="ghost" size="sm">
                <Download className="h-4 w-4" />
              </Button>
            )}
            <Button variant="ghost" size="sm">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Key Metrics Grid - Following existing patterns */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <MetricCard
            label="Total Employees"
            value={totalEmployees}
            icon={<Users className="h-4 w-4" />}
          />
          <MetricCard
            label="Active Employees"
            value={activeEmployees}
            icon={<UserCheck className="h-4 w-4" />}
            variant={
              activeEmployees < totalEmployees * 0.8 ? 'warning' : 'success'
            }
          />
          <MetricCard
            label="Avg Performance"
            value={`${avgPerformanceScore}/10`}
            icon={<TrendingUp className="h-4 w-4" />}
            variant={avgPerformanceScore < 7 ? 'warning' : 'success'}
          />
          <MetricCard
            label="Utilization Rate"
            value={`${avgUtilization}%`}
            icon={<Clock className="h-4 w-4" />}
            variant={
              avgUtilization < 70
                ? 'warning'
                : avgUtilization > 90
                  ? 'destructive'
                  : 'success'
            }
          />
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="p-4 bg-blue-50 rounded-lg">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-blue-700">
                Completed Tasks
              </span>
              <CheckCircle className="h-4 w-4 text-blue-600" />
            </div>
            <span className="text-xl font-bold text-blue-900">
              {totalCompletedTasks}
            </span>
          </div>

          <div className="p-4 bg-red-50 rounded-lg">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-red-700">
                Overdue Tasks
              </span>
              <AlertTriangle className="h-4 w-4 text-red-600" />
            </div>
            <span className="text-xl font-bold text-red-900">
              {totalOverdueTasks}
            </span>
          </div>

          <div className="p-4 bg-green-50 rounded-lg">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-green-700">
                On-Time Rate
              </span>
              <Clock className="h-4 w-4 text-green-600" />
            </div>
            <span className="text-xl font-bold text-green-900">
              {employeeAnalytics?.performanceMetrics?.length &&
              employeeAnalytics.performanceMetrics.length > 0
                ? Math.round(
                    employeeAnalytics.performanceMetrics.reduce(
                      (sum, emp) => sum + emp.onTimePerformance,
                      0
                    ) / employeeAnalytics.performanceMetrics.length
                  )
                : 0}
              %
            </span>
          </div>
        </div>

        {/* Workload Distribution Summary */}
        {!compact && employeeAnalytics?.workloadDistribution && (
          <div className="space-y-3">
            <h4 className="text-sm font-medium">Workload Distribution</h4>
            <div className="grid grid-cols-3 gap-4 text-center">
              <div className="p-3 bg-green-50 rounded-lg">
                <div className="text-lg font-bold text-green-700">
                  {
                    employeeAnalytics.workloadDistribution.filter(
                      emp => emp.status === 'Underutilized'
                    ).length
                  }
                </div>
                <div className="text-xs text-green-600">Underutilized</div>
              </div>
              <div className="p-3 bg-blue-50 rounded-lg">
                <div className="text-lg font-bold text-blue-700">
                  {
                    employeeAnalytics.workloadDistribution.filter(
                      emp => emp.status === 'Optimal'
                    ).length
                  }
                </div>
                <div className="text-xs text-blue-600">Optimal</div>
              </div>
              <div className="p-3 bg-red-50 rounded-lg">
                <div className="text-lg font-bold text-red-700">
                  {
                    employeeAnalytics.workloadDistribution.filter(
                      emp => emp.status === 'Overloaded'
                    ).length
                  }
                </div>
                <div className="text-xs text-red-600">Overloaded</div>
              </div>
            </div>
          </div>
        )}

        {!compact && (
          <div className="pt-4 border-t">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">
                Last updated: {new Date().toLocaleTimeString()}
              </span>
              <Button variant="outline" size="sm">
                View Details
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default EmployeeAnalyticsWidget;
