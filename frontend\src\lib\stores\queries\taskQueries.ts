/**
 * @file Task query configurations following Single Responsibility Principle
 * @description Centralized query configurations for task-related data fetching
 */

import type { UseQueryOptions } from '@tanstack/react-query';

import type { Task } from '../../types/domain';

import {
  taskApiService,
  employeeApiService,
  vehicleApiService,
} from '../../api/services/apiServiceFactory';
import { TaskTransformer } from '../../transformers/taskTransformer';

/**
 * Query keys for task-related queries
 */
export const taskQueryKeys = {
  all: ['tasks'] as const,
  detail: (id: string) => ['tasks', id] as const,
  withAssignments: (id: string) => ['tasks', id, 'with-assignments'] as const,
};

/**
 * Creates query configuration for fetching a single task
 */
export const createTaskQuery = (id: string) => ({
  enabled: !!id,
  queryFn: () => taskApiService.getById(id),
  queryKey: taskQueryKeys.detail(id),
  staleTime: 5 * 60 * 1000, // 5 minutes
});

/**
 * Creates query configuration for fetching all employees
 */
export const createEmployeesQuery = () => ({
  queryFn: () => employeeApiService.getAll(),
  queryKey: ['employees'] as const,
  staleTime: 10 * 60 * 1000, // 10 minutes - employees change less frequently
});

/**
 * Creates query configuration for fetching all vehicles
 */
export const createVehiclesQuery = () => ({
  queryFn: () => vehicleApiService.getAll(),
  queryKey: ['vehicles'] as const,
  staleTime: 10 * 60 * 1000, // 10 minutes - vehicles change less frequently
});

/**
 * Creates parallel query configurations for task with assignments
 */
export const createTaskWithAssignmentsQueries = (id: string) => [
  createTaskQuery(id),
  createEmployeesQuery(),
  createVehiclesQuery(),
];

/**
 * Standard query options for task queries
 */
export const taskQueryOptions: Partial<UseQueryOptions<Task, Error>> = {
  gcTime: 10 * 60 * 1000, // 10 minutes garbage collection time
  retry: 3,
  retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30_000),
  staleTime: 5 * 60 * 1000,
};
