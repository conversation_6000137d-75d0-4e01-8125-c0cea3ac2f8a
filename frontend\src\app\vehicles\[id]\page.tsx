'use client';

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, Edit, Trash2 } from 'lucide-react';
import Link from 'next/link';
import { useParams, useRouter } from 'next/navigation';
import React, { useCallback } from 'react';

//import type { EnrichedServiceRecord, Vehicle } from '@/lib/types/domain';
import ErrorBoundary from '@/components/error-boundaries/ErrorBoundary';
import { ViewReportButton } from '@/components/reports/ViewReportButton';
import { EnhancedServiceHistoryContainer } from '@/components/service-history/EnhancedServiceHistoryContainer';
import { ActionButton } from '@/components/ui/action-button';
import { AppBreadcrumb } from '@/components/ui/app-breadcrumb';
import { DataLoader, SkeletonLoader } from '@/components/ui/loading';
import { PageHeader } from '@/components/ui/PageHeader';
import { usePredefinedEntityToast } from '@/hooks/forms/useFormToast';
import { usePermissions } from '@/lib/api/security'; // Phase 3 permissions
import { useVehicleServiceRecords } from '@/lib/stores/queries/useServiceRecords'; // Modern React Query hooks
import { useDeleteVehicle, useVehicle } from '@/lib/stores/queries/useVehicles'; // Modern React Query hooks

import MaintenanceSuggestor from './components/MaintenanceSuggestor'; // Import the new component
import ServiceLogForm from './components/ServiceLogForm';
import VehicleInfoCard from './components/VehicleInfoCard';

const VehicleDetailPage = () => {
  const router = useRouter();
  const params = useParams();
  const { showEntityDeleted, showEntityDeletionError } =
    usePredefinedEntityToast('vehicle');

  // Phase 3: Use permissions
  const { hasPermission } = usePermissions();

  const vehicleId = params?.id ? Number(params.id) : null;

  // Phase 3: Use React Query hooks instead of manual state management
  const {
    data: vehicle,
    error,
    isLoading,
    refetch: refetchVehicle,
  } = useVehicle(vehicleId);

  const deleteVehicleMutation = useDeleteVehicle();

  // Phase 4: Migrated to React Query hook for service records
  const {
    data: serviceRecords = [],
    error: recordsError,
    isLoading: isLoadingRecords,
    refetch: refetchServiceRecords,
  } = useVehicleServiceRecords(vehicleId ?? 0, {
    enabled: !!vehicleId,
  });

  // Phase 3: Removed legacy fetchVehicleDetails - now handled by useVehicle hook
  // Phase 4: Removed manual fetchServiceRecords - now handled by useVehicleServiceRecords hook

  // Handle retry for service records
  const handleRetryRecords = useCallback(() => {
    refetchServiceRecords();
  }, [refetchServiceRecords]);

  const handleDelete = async () => {
    if (!vehicleId || !vehicle) return;

    // Phase 3: Check permissions before allowing delete
    if (!hasPermission('vehicles:delete')) {
      showEntityDeletionError('You do not have permission to delete vehicles.');
      return;
    }

    if (
      globalThis.confirm(
        'Are you sure you want to delete this vehicle permanently?'
      )
    ) {
      try {
        // Phase 3: Use React Query mutation with CSRF protection
        await deleteVehicleMutation.mutateAsync(vehicleId);
        const vehicleForToast = {
          make: vehicle.make,
          model: vehicle.model,
        };
        showEntityDeleted(vehicleForToast);
        router.push('/vehicles');
      } catch (error_: any) {
        console.error('Failed to delete vehicle:', error_);
        showEntityDeletionError(
          error_.message || 'Failed to delete vehicle. Please try again.'
        );
      }
    }
  };

  const handleServiceRecordAdded = () => {
    refetchVehicle(); // Phase 3: Use React Query refetch
    refetchServiceRecords(); // Phase 4: Use React Query refetch for service records
    // Toast notification is now handled by ServiceLogForm using standardized service
  };

  return (
    <ErrorBoundary>
      <div className="container mx-auto space-y-6 py-8">
        <AppBreadcrumb />
        <DataLoader
          data={vehicle}
          emptyComponent={
            <div className="py-10 text-center">
              <PageHeader icon={AlertTriangle} title="Vehicle Not Found" />
              <p className="mb-4">The requested vehicle could not be found.</p>
            </div>
          }
          error={error?.message ?? null}
          isLoading={isLoading}
          loadingComponent={
            <div className="space-y-6">
              <PageHeader icon={Car} title="Loading Vehicle..." />
              <SkeletonLoader count={1} variant="card" />
              <div className="grid items-start gap-6 lg:grid-cols-3">
                <SkeletonLoader
                  className="lg:col-span-2"
                  count={1}
                  variant="card"
                />
                <SkeletonLoader count={1} variant="card" />
              </div>
            </div>
          }
          onRetry={refetchVehicle}
        >
          {loadedVehicle => (
            <>
              <PageHeader
                description={`VIN: ${loadedVehicle.vin} | Plate: ${loadedVehicle.licensePlate}`}
                icon={Car}
                title={`${loadedVehicle.make} ${loadedVehicle.model}`}
              >
                <div className="flex space-x-2">
                  <ActionButton
                    actionType="secondary"
                    asChild
                    icon={<Edit className="size-4" />}
                  >
                    <Link href={`/vehicles/edit/${loadedVehicle.id}`}>
                      Edit
                    </Link>
                  </ActionButton>
                  <ViewReportButton
                    href={`/vehicles/${loadedVehicle.id}/report`}
                  />
                  <ActionButton
                    actionType="danger"
                    icon={<Trash2 className="size-4" />}
                    isLoading={deleteVehicleMutation.isPending}
                    loadingText="Deleting..."
                    onClick={handleDelete}
                  >
                    Delete
                  </ActionButton>
                </div>
              </PageHeader>

              <div className="grid items-start gap-6 lg:grid-cols-3">
                <div className="space-y-6 lg:col-span-2">
                  <VehicleInfoCard vehicle={loadedVehicle} />
                  <EnhancedServiceHistoryContainer
                    error={recordsError?.message ?? null}
                    isLoading={isLoadingRecords}
                    onRetry={handleRetryRecords}
                    records={serviceRecords}
                    showVehicleInfo={false}
                    vehicleSpecific={true}
                  />
                </div>
                <div className="space-y-6 lg:col-span-1">
                  <ServiceLogForm
                    currentOdometerReading={Math.max(
                      ...(loadedVehicle.serviceHistory ?? []).map(
                        s => s.odometer
                      ),
                      loadedVehicle.initialOdometer ?? 0
                    )}
                    onServiceRecordAdded={handleServiceRecordAdded}
                    vehicle={loadedVehicle}
                  />
                  <MaintenanceSuggestor vehicle={loadedVehicle} />
                </div>
              </div>
            </>
          )}
        </DataLoader>
      </div>
    </ErrorBoundary>
  );
};

export default VehicleDetailPage;
