/**
 * @file ExportService.ts
 * @description Comprehensive export service for individual and aggregate data across all entities
 */

import { pdf } from '@react-pdf/renderer';
import { format } from 'date-fns';
import * as XLSX from 'xlsx';

// React PDF components will be imported as needed
import type {
  Delegation,
  DelegationAnalytics,
  EmployeeAnalytics,
  ExportOptions,
  ReportingFilters,
  TaskAnalytics,
  VehicleAnalytics,
} from '../data/types/reporting';

/**
 * Export data types
 */
export type ExportDataType =
  | 'aggregate'
  | 'delegations'
  | 'employees'
  | 'tasks'
  | 'vehicles';

export interface ExportRequest {
  aggregateLevel?: 'both' | 'individual' | 'summary';
  customFields?: string[];
  dataType: ExportDataType;
  filters?: ReportingFilters;
  format: 'csv' | 'excel' | 'pdf';
  includeCharts?: boolean;
}

/**
 * ExportService Class
 *
 * Handles comprehensive data export functionality for all entity types.
 *
 * Responsibilities:
 * - Export individual entity records
 * - Export aggregate analytics data
 * - Support multiple formats (CSV, Excel, PDF)
 * - Handle custom field selection
 * - Generate printable reports
 *
 * SOLID Principles Applied:
 * - SRP: Single responsibility of handling data exports
 * - OCP: Open for extension via new export formats
 * - DIP: Depends on data service abstractions
 */
export class ExportService {
  /**
   * Export comprehensive report with all entities
   */
  static async exportComprehensiveReport(
    data: {
      delegations?: DelegationAnalytics;
      employees?: EmployeeAnalytics;
      tasks?: TaskAnalytics;
      vehicles?: VehicleAnalytics;
    },
    options: ExportOptions = {}
  ): Promise<void> {
    const filename =
      options.filename || `comprehensive_report_${this.getTimestamp()}`;

    switch (options.format) {
      case 'excel': {
        await this.exportComprehensiveExcel(data, filename, options);
        break;
      }
      case 'pdf': {
        await this.exportComprehensiveExcel(data, filename, options);
        break;
      }
      default: {
        // Default to Excel for comprehensive reports
        await this.exportComprehensiveExcel(data, filename, options);
      }
    }
  }

  /**
   * Export delegations data
   */
  static async exportDelegations(
    data: Delegation[] | DelegationAnalytics,
    options: ExportOptions = {}
  ): Promise<void> {
    const filename = options.filename || `delegations_${this.getTimestamp()}`;

    if (Array.isArray(data)) {
      // Individual delegation records
      await this.exportIndividualData(data, 'delegations', filename, options);
    } else {
      // Aggregate delegation analytics
      await this.exportAggregateData(data, 'delegations', filename, options);
    }
  }

  /**
   * Export employees data
   */
  static async exportEmployees(
    data: any[] | EmployeeAnalytics,
    options: ExportOptions = {}
  ): Promise<void> {
    const filename = options.filename || `employees_${this.getTimestamp()}`;

    await (Array.isArray(data)
      ? this.exportIndividualData(data, 'employees', filename, options)
      : this.exportAggregateData(data, 'employees', filename, options));
  }

  /**
   * Export tasks data
   */
  static async exportTasks(
    data: any[] | TaskAnalytics,
    options: ExportOptions = {}
  ): Promise<void> {
    const filename = options.filename || `tasks_${this.getTimestamp()}`;

    await (Array.isArray(data)
      ? this.exportIndividualData(data, 'tasks', filename, options)
      : this.exportAggregateData(data, 'tasks', filename, options));
  }

  /**
   * Export vehicles data
   */
  static async exportVehicles(
    data: any[] | VehicleAnalytics,
    options: ExportOptions = {}
  ): Promise<void> {
    const filename = options.filename || `vehicles_${this.getTimestamp()}`;

    await (Array.isArray(data)
      ? this.exportIndividualData(data, 'vehicles', filename, options)
      : this.exportAggregateData(data, 'vehicles', filename, options));
  }

  /**
   * Create comprehensive summary
   */
  private static createComprehensiveSummary(data: any): any[] {
    const summary = [
      { Metric: 'Report Generated', Value: format(new Date(), 'PPpp') },
      { Metric: 'Report Type', Value: 'Comprehensive System Report' },
    ];

    if (data.delegations) {
      summary.push({
        Metric: 'Total Delegations',
        Value: data.delegations.totalCount,
      });
    }
    if (data.tasks) {
      summary.push({ Metric: 'Total Tasks', Value: data.tasks.totalCount });
    }
    if (data.vehicles) {
      summary.push({
        Metric: 'Total Vehicles',
        Value: data.vehicles.totalCount,
      });
    }
    if (data.employees) {
      summary.push({
        Metric: 'Total Employees',
        Value: data.employees.totalCount,
      });
    }

    return summary;
  }

  private static downloadBlob(blob: Blob, filename: string): void {
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.append(link);
    link.click();
    link.remove();
    URL.revokeObjectURL(url);
  }

  private static downloadFile(
    content: string,
    filename: string,
    mimeType: string
  ): void {
    const blob = new Blob([content], { type: mimeType });
    this.downloadBlob(blob, filename);
  }

  /**
   * Export aggregate analytics data
   */
  private static async exportAggregateData(
    data: any,
    entityType: string,
    filename: string,
    options: ExportOptions
  ): Promise<void> {
    // Transform analytics data to exportable format
    const exportData = this.transformAnalyticsData(data, entityType);

    switch (options.format) {
      case 'excel': {
        this.exportAnalyticsToExcel(exportData, filename, options, entityType);
        break;
      }
      case 'pdf': {
        this.exportAnalyticsToExcel(exportData, filename, options, entityType);
        break;
      }
      default: {
        this.exportToCSV(exportData.summary, filename, options);
      }
    }
  }

  /**
   * Export analytics to Excel with multiple sheets
   */
  private static exportAnalyticsToExcel(
    data: any,
    filename: string,
    options: ExportOptions,
    entityType: string
  ): void {
    const workbook = XLSX.utils.book_new();

    // Summary sheet
    if (data.summary) {
      const summarySheet = XLSX.utils.json_to_sheet([data.summary]);
      XLSX.utils.book_append_sheet(workbook, summarySheet, 'Summary');
    }

    // Distribution data
    if (data.distribution) {
      const distSheet = XLSX.utils.json_to_sheet(data.distribution);
      XLSX.utils.book_append_sheet(workbook, distSheet, 'Distribution');
    }

    // Trend data
    if (data.trends) {
      const trendSheet = XLSX.utils.json_to_sheet(data.trends);
      XLSX.utils.book_append_sheet(workbook, trendSheet, 'Trends');
    }

    const excelBuffer = XLSX.write(workbook, {
      bookType: 'xlsx',
      type: 'array',
    });
    const blob = new Blob([excelBuffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    });
    this.downloadBlob(blob, `${filename}_analytics.xlsx`);
  }

  /**
   * Export comprehensive report to Excel
   */
  private static async exportComprehensiveExcel(
    data: any,
    filename: string,
    options: ExportOptions
  ): Promise<void> {
    const workbook = XLSX.utils.book_new();

    // Add summary sheet
    const summaryData = this.createComprehensiveSummary(data);
    const summarySheet = XLSX.utils.json_to_sheet(summaryData);
    XLSX.utils.book_append_sheet(workbook, summarySheet, 'Summary');

    // Add individual entity sheets
    for (const [entityType, entityData] of Object.entries(data)) {
      if (entityData) {
        const transformedData = this.transformAnalyticsData(
          entityData,
          entityType
        );
        if (transformedData.summary) {
          const sheet = XLSX.utils.json_to_sheet([transformedData.summary]);
          XLSX.utils.book_append_sheet(
            workbook,
            sheet,
            entityType.charAt(0).toUpperCase() + entityType.slice(1)
          );
        }
      }
    }

    const excelBuffer = XLSX.write(workbook, {
      bookType: 'xlsx',
      type: 'array',
    });
    const blob = new Blob([excelBuffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    });
    this.downloadBlob(blob, `${filename}.xlsx`);
  }

  /**
   * Export individual entity records
   */
  private static async exportIndividualData(
    data: any[],
    entityType: string,
    filename: string,
    options: ExportOptions
  ): Promise<void> {
    switch (options.format) {
      case 'csv': {
        this.exportToCSV(data, filename, options);
        break;
      }
      case 'excel': {
        this.exportToExcel(data, filename, options, entityType);
        break;
      }
      case 'pdf': {
        this.exportToPDF(data, filename, options, entityType);
        break;
      }
      default: {
        this.exportToCSV(data, filename, options);
      }
    }
  }

  /**
   * Export to CSV format
   */
  private static exportToCSV(
    data: any[],
    filename: string,
    options: ExportOptions
  ): void {
    if (!data || data.length === 0) return;

    const headers = Object.keys(data[0]);
    const csvContent = [
      headers.join(','),
      ...data.map(row =>
        headers
          .map(header => {
            const value = row[header];
            // Handle values that might contain commas or quotes
            if (
              typeof value === 'string' &&
              (value.includes(',') || value.includes('"'))
            ) {
              return `"${value.replaceAll('"', '""')}"`;
            }
            return value || '';
          })
          .join(',')
      ),
    ].join('\n');

    this.downloadFile(csvContent, `${filename}.csv`, 'text/csv');
  }

  /**
   * Export to Excel format
   */
  private static exportToExcel(
    data: any[],
    filename: string,
    options: ExportOptions,
    entityType: string
  ): void {
    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.json_to_sheet(data);

    // Add worksheet to workbook
    XLSX.utils.book_append_sheet(workbook, worksheet, entityType);

    // Add metadata sheet if requested
    if (options.includeTimestamp) {
      const metadata = [
        { Property: 'Export Date', Value: new Date().toISOString() },
        { Property: 'Entity Type', Value: entityType },
        { Property: 'Record Count', Value: data.length },
        { Property: 'Format', Value: 'Excel' },
      ];
      const metaSheet = XLSX.utils.json_to_sheet(metadata);
      XLSX.utils.book_append_sheet(workbook, metaSheet, 'Metadata');
    }

    // Generate and download file
    const excelBuffer = XLSX.write(workbook, {
      bookType: 'xlsx',
      type: 'array',
    });
    const blob = new Blob([excelBuffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    });
    this.downloadBlob(blob, `${filename}.xlsx`);
  }

  /**
   * Export to PDF format using react-pdf
   * Uses the working useExport hook for consistent PDF generation
   */
  private static async exportToPDF(
    data: any[],
    filename: string,
    options: ExportOptions,
    entityType: string
  ): Promise<void> {
    try {
      // Import the working PDF export functionality
      const { useExport } = await import('../exports/hooks/useExport');
      const { exportReportToPDF } = useExport(filename);

      // Transform data to the format expected by PDF export
      const analyticsData = {
        data: {
          totalCount: data.length,
          summary: {
            totalRecords: data.length,
            exportedAt: new Date().toISOString(),
            entityType,
          },
          records: data,
        },
        metadata: {
          id: `export_${entityType}_${Date.now()}`,
          type: 'data_export',
          entityType,
          format: 'pdf',
          generatedAt: new Date().toISOString(),
          generatedBy: 'system',
        },
      };

      // Use the working PDF export
      await exportReportToPDF(
        analyticsData,
        entityType as 'delegations' | 'tasks' | 'vehicles' | 'employees',
        `${entityType.charAt(0).toUpperCase() + entityType.slice(1)} Export Report`,
        filename
      );

      console.info(`PDF export completed successfully for ${entityType}`);
    } catch (error) {
      console.error('PDF export failed, falling back to Excel:', error);
      // Fallback to Excel only if PDF export fails
      this.exportToExcel(data, filename, options, entityType);
    }
  }

  /**
   * Utility methods
   */
  private static getTimestamp(): string {
    return format(new Date(), 'yyyy-MM-dd_HH-mm-ss');
  }

  /**
   * Transform analytics data for export
   */
  private static transformAnalyticsData(data: any, entityType: string): any {
    switch (entityType) {
      case 'delegations': {
        return {
          distribution: data.statusDistribution,
          summary: {
            'Active Delegations': data.summary?.activeDelegations,
            'Average Duration': `${data.summary?.averageDuration} hours`,
            'Completed Delegations': data.summary?.completedDelegations,
            'Completion Rate': `${data.summary?.completionRate}%`,
            'Total Delegations': data.totalCount,
          },
          trends: data.trendData,
        };
      }
      case 'employees': {
        return {
          performance: data.performanceMetrics,
          summary: {
            'Active Employees': data.performanceMetrics?.filter(
              (e: any) => e.completedDelegations > 0
            ).length,
            'Average Performance':
              data.performanceMetrics?.reduce(
                (sum: number, e: any) => sum + e.averageRating,
                0
              ) / data.performanceMetrics?.length,
            'Total Completed Tasks': data.taskAssignments?.reduce(
              (sum: number, e: any) => sum + e.completedTasks,
              0
            ),
            'Total Employees': data.totalCount,
          },
          workload: data.workloadDistribution,
        };
      }
      case 'tasks': {
        return {
          distribution: data.statusDistribution,
          summary: {
            'Completed Tasks':
              data.statusDistribution?.find(
                (s: any) => s.status === 'Completed'
              )?.count || 0,
            'Completion Rate': `${data.completionRate}%`,
            'Overdue Tasks': data.overdueCount,
            'Pending Tasks':
              data.statusDistribution?.find((s: any) => s.status === 'Pending')
                ?.count || 0,
            'Total Tasks': data.totalCount,
          },
          trends: data.trendData,
        };
      }
      case 'vehicles': {
        return {
          maintenance: data.maintenanceSchedule,
          summary: {
            'Active Vehicles': data.utilizationMetrics?.filter(
              (v: any) => v.utilizationRate > 0
            ).length,
            'Average Cost Per Service': `$${data.costAnalysis?.averageCostPerService}`,
            'Total Service Cost': `$${data.costAnalysis?.totalCost}`,
            'Total Vehicles': data.totalCount,
          },
          utilization: data.utilizationMetrics,
        };
      }
      default: {
        return { summary: data };
      }
    }
  }
}
