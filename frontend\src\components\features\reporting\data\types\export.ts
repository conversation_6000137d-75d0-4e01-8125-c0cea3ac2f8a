/**
 * @file Export Types
 * @description Comprehensive type definitions for the enhanced export system
 *
 * SOLID Principles Applied:
 * - ISP: Interface segregation with specific export option types
 * - SRP: Each interface has a single, well-defined purpose
 */

import type { ReportingFilters } from '../../data/types/reporting';

// Base export types
export type ExportFormat = 'pdf' | 'excel' | 'csv' | 'json' | 'png';

// PDF Document Props for react-pdf components
export interface ReportDocumentProps {
  data: any;
  title?: string;
  template?: ReportTemplate;
  options?: Partial<PDFExportOptions>;
  filters?: ReportingFilters;
  reportTitle?: string;
  reportDate?: string;
  includeCharts?: boolean;
  includeServiceHistory?: boolean;
}
export type EntityType = 'delegation' | 'task' | 'vehicle' | 'employee';
export type ExportStatus =
  | 'pending'
  | 'processing'
  | 'completed'
  | 'failed'
  | 'cancelled';

// Template types
export interface ReportTemplate {
  id: string;
  name: string;
  description: string;
  format: ExportFormat;
  entityTypes: EntityType[];
  isDefault: boolean;
  customFields?: string[];
  styling?: TemplateStyle;
}

export interface TemplateStyle {
  primaryColor?: string;
  secondaryColor?: string;
  fontFamily?: string;
  fontSize?: number;
  logoUrl?: string;
  headerText?: string;
  footerText?: string;
}

// Export options interfaces
export interface BaseExportOptions {
  format: ExportFormat;
  filename?: string;
  template?: string;
  includeCharts?: boolean;
  includeMetadata?: boolean;
  customFields?: string[];
}

export interface PDFExportOptions extends BaseExportOptions {
  format: 'pdf';
  orientation?: 'portrait' | 'landscape';
  pageSize?: 'A4' | 'A3' | 'Letter' | 'Legal';
  margins?: {
    top: number;
    right: number;
    bottom: number;
    left: number;
  };
  includeTableOfContents?: boolean;
  includePageNumbers?: boolean;
  watermark?: string;
}

export interface ExcelExportOptions extends BaseExportOptions {
  format: 'excel';
  includeFormulas?: boolean;
  includeFormatting?: boolean;
  separateSheets?: boolean;
  sheetNames?: Record<EntityType, string>;
  includeChartSheets?: boolean;
}

export interface CSVExportOptions extends BaseExportOptions {
  format: 'csv';
  delimiter?: ',' | ';' | '\t';
  encoding?: 'utf-8' | 'utf-16' | 'ascii';
  includeHeaders?: boolean;
  flattenNested?: boolean;
}

export interface JSONExportOptions extends BaseExportOptions {
  format: 'json';
  prettyPrint?: boolean;
  includeSchema?: boolean;
  compression?: 'none' | 'gzip';
}

// Comprehensive export options
export interface ComprehensiveExportOptions {
  format: ExportFormat;
  filters: ReportingFilters;
  template?: string;
  filename?: string;
  pdfOptions?: Partial<PDFExportOptions>;
  excelOptions?: Partial<ExcelExportOptions>;
  csvOptions?: Partial<CSVExportOptions>;
  jsonOptions?: Partial<JSONExportOptions>;
  includeAllEntities?: boolean;
  entityTypes?: EntityType[];
}

// Entity-specific export options
export interface EntityExportOptions {
  format: ExportFormat;
  filters: ReportingFilters;
  template?: string;
  filename?: string;
  pdfOptions?: Partial<PDFExportOptions>;
  excelOptions?: Partial<ExcelExportOptions>;
  csvOptions?: Partial<CSVExportOptions>;
  jsonOptions?: Partial<JSONExportOptions>;
  includeRelatedData?: boolean;
  relatedEntityTypes?: EntityType[];
}

// Export result types
export interface ExportResult {
  id: string;
  filename: string;
  format: ExportFormat;
  fileSize: number;
  downloadUrl: string;
  expiresAt: Date;
  metadata: ExportMetadata;
}

export interface ExportMetadata {
  generatedAt: Date;
  generatedBy: string;
  recordCount: number;
  entityTypes: EntityType[];
  filters: ReportingFilters;
  template?: string | undefined;
  processingTime: number;
  fileHash?: string;
}

// Progress tracking types
export interface ExportProgress {
  id: string;
  status: ExportStatus;
  progress: number; // 0-100
  message: string;
  startedAt: Date;
  estimatedCompletion?: Date | undefined;
  error?: string;
}

// Export history types
export interface ExportHistory {
  id: string;
  timestamp: Date;
  format: ExportFormat;
  filename: string;
  fileSize: number;
  status: ExportStatus;
  options: ComprehensiveExportOptions | EntityExportOptions;
  downloadCount?: number;
  lastDownloaded?: Date;
}

// Service interfaces following DIP
export interface IExportService {
  exportComprehensiveReport(
    options: ComprehensiveExportOptions
  ): Promise<ExportResult>;
  exportEntityReport(
    entityType: EntityType,
    entityId: string,
    options: EntityExportOptions
  ): Promise<ExportResult>;
  getExportProgress(exportId: string): ExportProgress | null;
  getExportHistory(): ExportHistory[];
  cancelExport(exportId: string): boolean;
}

export interface IPDFExportService {
  generateComprehensiveReport(
    data: ComprehensiveReportData
  ): Promise<ExportResult>;
  generateEntityReport(
    entityType: EntityType,
    data: any,
    options?: Partial<PDFExportOptions>
  ): Promise<ExportResult>;
}

export interface IExcelExportService {
  generateComprehensiveReport(
    data: ComprehensiveReportData
  ): Promise<ExportResult>;
  generateEntityReport(
    entityType: EntityType,
    data: any,
    options?: Partial<ExcelExportOptions>
  ): Promise<ExportResult>;
}

export interface ICSVExportService {
  generateComprehensiveReport(
    data: ComprehensiveReportData
  ): Promise<ExportResult>;
  generateEntityReport(
    entityType: EntityType,
    data: any,
    options?: Partial<CSVExportOptions>
  ): Promise<ExportResult>;
}

export interface IReportTemplateService {
  getTemplates(format?: ExportFormat): Promise<ReportTemplate[]>;
  getTemplate(id: string): Promise<ReportTemplate | null>;
  createTemplate(template: Omit<ReportTemplate, 'id'>): Promise<ReportTemplate>;
  updateTemplate(
    id: string,
    updates: Partial<ReportTemplate>
  ): Promise<ReportTemplate>;
  deleteTemplate(id: string): Promise<boolean>;
}

// Comprehensive report data structure
export interface ComprehensiveReportData {
  delegations: any;
  tasks: any;
  vehicles: any;
  employees: any;
  template?: string | undefined;
  options?: any;
}

export interface EntityReportData {
  entityType: EntityType;
  entityId: string;
  entityName?: string;
  summary?: {
    totalTasks?: number;
    completedTasks?: number;
    completionRate?: number;
    performanceScore?: number;
  };
  details?: {
    vehicles?: any[];
    employees?: any[];
    delegations?: any[];
  };
  tasks?: any[];
  template?: string;
  options?: any;
}

// Export queue types
export interface ExportQueueItem {
  id: string;
  type: 'comprehensive' | 'entity';
  options: ComprehensiveExportOptions | EntityExportOptions;
  priority: 'low' | 'normal' | 'high';
  createdAt: Date;
  scheduledFor?: Date;
  attempts: number;
  maxAttempts: number;
}

// Export notification types
export interface ExportNotification {
  id: string;
  exportId: string;
  type: 'started' | 'progress' | 'completed' | 'failed';
  message: string;
  timestamp: Date;
  data?: any;
}

// Export validation types
export interface ExportValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  estimatedSize?: number;
  estimatedTime?: number;
}

// Export configuration types
export interface ExportConfiguration {
  maxFileSize: number; // in bytes
  maxRecords: number;
  allowedFormats: ExportFormat[];
  defaultTemplate: Record<ExportFormat, string>;
  retentionDays: number;
  compressionThreshold: number; // file size threshold for compression
}

// Error types
export class ExportError extends Error {
  constructor(
    message: string,
    public code: string,
    public exportId?: string,
    public details?: any
  ) {
    super(message);
    this.name = 'ExportError';
  }
}

export class ExportValidationError extends ExportError {
  constructor(
    message: string,
    public validationErrors: string[],
    exportId?: string
  ) {
    super(message, 'VALIDATION_ERROR', exportId, { validationErrors });
    this.name = 'ExportValidationError';
  }
}

export class ExportTimeoutError extends ExportError {
  constructor(
    message: string,
    exportId?: string,
    public timeoutMs?: number
  ) {
    super(message, 'TIMEOUT_ERROR', exportId, { timeoutMs });
    this.name = 'ExportTimeoutError';
  }
}
