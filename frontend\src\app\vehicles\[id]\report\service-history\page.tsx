'use client';

import { Car, History } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { useCallback, useEffect, useState } from 'react';

import type { EnrichedServiceRecord, Vehicle } from '@/lib/types/domain';

import ErrorBoundary from '@/components/error-boundaries/ErrorBoundary';
import { ReportActions } from '@/components/reports/ReportActions';
import { EnhancedServiceHistoryContainer } from '@/components/service-history/EnhancedServiceHistoryContainer';
import { ActionButton } from '@/components/ui/action-button';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { DataLoader, SkeletonLoader } from '@/components/ui/loading';
import { PageHeader } from '@/components/ui/PageHeader';
import { useAuthContext } from '@/contexts/AuthContext';
import { useVehicle } from '@/lib/stores/queries/useVehicles';
import { useVehicleServiceRecords } from '@/lib/stores/queries/useServiceRecords';
import { getErrorMessage } from '@/lib/utils/errorHandling'; // Import getErrorMessage

export default function VehicleServiceHistoryPage() {
  const params = useParams();
  const { loading: authLoading, session, user } = useAuthContext();
  const isAuthenticated = !!user && !!session?.access_token;

  const vehicleId = params?.id as string;
  const {
    data: vehicle,
    error: vehicleError,
    isLoading: isVehicleLoading,
    refetch: refetchVehicle,
  } = useVehicle(Number(vehicleId), { enabled: isAuthenticated });

  // Use React Query hook for service records
  const {
    data: serviceRecords = [],
    error: recordsError,
    isLoading: isRecordsLoading,
    refetch: refetchServiceRecords,
  } = useVehicleServiceRecords(Number(vehicleId), {
    enabled: isAuthenticated && !!vehicle?.id,
  });

  useEffect(() => {
    if (vehicle) {
      document.title = `${vehicle.make} ${vehicle.model} - Service History Report`;
    }
  }, [vehicle]);

  const handleRetry = useCallback(() => {
    refetchVehicle(); // Retry fetching vehicle data
    refetchServiceRecords(); // Retry fetching service records
  }, [refetchVehicle, refetchServiceRecords]);

  const isLoading = authLoading || isVehicleLoading || isRecordsLoading;
  const error = vehicleError || recordsError;

  if (isLoading) {
    return (
      <ErrorBoundary>
        <div className="mx-auto max-w-5xl p-4">
          <div className="space-y-6">
            <SkeletonLoader count={1} variant="card" />
            <SkeletonLoader count={5} variant="table" />
          </div>
        </div>
      </ErrorBoundary>
    );
  }

  if (error || !vehicle) {
    return (
      <ErrorBoundary>
        <div className="mx-auto max-w-5xl p-4">
          <Card className="shadow-md">
            <CardContent className="p-6">
              <h2 className="mb-2 text-xl font-semibold text-red-600">Error</h2>
              <p className="mb-4 text-gray-700">
                {getErrorMessage(error) || 'Vehicle not found'}
              </p>
              <div className="flex gap-2">
                <Button onClick={handleRetry}>Try Again</Button>
                <Button asChild variant="outline">
                  <Link href="/vehicles">Back to Vehicles</Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </ErrorBoundary>
    );
  }

  return (
    <ErrorBoundary>
      <div className="print-container mx-auto max-w-5xl space-y-6 p-4">
        <PageHeader
          description={`${vehicle.make} ${vehicle.model} (${vehicle.year})`}
          icon={History}
          title="Vehicle Service History"
        >
          <div className="no-print flex gap-2">
            <ActionButton
              actionType="tertiary"
              asChild
              icon={<Car className="size-4" />}
            >
              <Link href={`/vehicles/${vehicleId}`}>View Vehicle</Link>
            </ActionButton>
            <ReportActions
              enableCsv={serviceRecords.length > 0}
              entityId={vehicleId}
              fileName={`vehicle-service-history-${vehicle.make}-${vehicle.model}`}
              reportContentId="#vehicle-service-history-content"
              reportType="vehicle-service-history"
              tableId="#service-history-table"
            />
          </div>
        </PageHeader>

        <div className="report-content" id="vehicle-service-history-content">
          {/* Vehicle Summary Card */}
          <Card className="card-print mb-6 shadow-md">
            <CardContent className="p-4">
              <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                <div className="col-span-1 md:col-span-2">
                  <h2 className="mb-4 text-xl font-semibold text-gray-800">
                    Vehicle Details
                  </h2>
                  <div className="grid grid-cols-2 gap-x-4 gap-y-2 text-sm">
                    <div>
                      <span className="font-medium">Make:</span> {vehicle.make}
                    </div>
                    <div>
                      <span className="font-medium">Model:</span>{' '}
                      {vehicle.model}
                    </div>
                    <div>
                      <span className="font-medium">Year:</span> {vehicle.year}
                    </div>
                    {vehicle.licensePlate && (
                      <div>
                        <span className="font-medium">Plate Number:</span>{' '}
                        {vehicle.licensePlate}
                      </div>
                    )}
                    {vehicle.color && (
                      <div>
                        <span className="font-medium">Color:</span>{' '}
                        {vehicle.color}
                      </div>
                    )}
                    <div>
                      <strong>Initial Odometer:</strong>{' '}
                      {vehicle.initialOdometer !== null &&
                      vehicle.initialOdometer !== undefined
                        ? `${vehicle.initialOdometer.toLocaleString()} miles`
                        : 'Not recorded'}
                    </div>
                    <div>
                      <span className="font-medium">Current Odometer:</span>{' '}
                      {serviceRecords.length > 0
                        ? `${Math.max(
                            ...serviceRecords.map(r => r.odometer)
                          ).toLocaleString()} miles`
                        : vehicle.initialOdometer !== null &&
                            vehicle.initialOdometer !== undefined
                          ? `${vehicle.initialOdometer.toLocaleString()} miles`
                          : 'No odometer data available'}
                    </div>
                    <div className="col-span-2">
                      <span className="font-medium">Last Updated:</span>{' '}
                      {serviceRecords.length > 0
                        ? new Date(
                            Math.max(
                              ...serviceRecords.map(r =>
                                new Date(r.date).getTime()
                              )
                            )
                          ).toLocaleDateString()
                        : 'No service records'}
                    </div>
                  </div>
                </div>

                {vehicle.imageUrl && (
                  <div className="no-print col-span-1">
                    <div className="relative aspect-[4/3] w-full overflow-hidden rounded">
                      <Image
                        alt={`${vehicle.make} ${vehicle.model}`}
                        fill
                        sizes="(max-width: 768px) 100vw, 300px"
                        src={vehicle.imageUrl}
                        style={{ objectFit: 'cover' }}
                      />
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Service History */}
          <header className="print-only mb-8 border-b-2 border-gray-300 pb-4 text-center">
            <h1 className="text-3xl font-bold text-gray-800">
              Vehicle Service History Report
            </h1>
            <p className="text-md text-gray-600">
              {vehicle.make} {vehicle.model} ({vehicle.year})
              {vehicle.licensePlate && ` - ${vehicle.licensePlate}`}
            </p>
          </header>

          <EnhancedServiceHistoryContainer
            error={null}
            isLoading={false}
            onRetry={handleRetry}
            records={serviceRecords}
            showVehicleInfo={false}
            vehicleSpecific={true}
          />

          <footer className="mt-10 border-t-2 border-gray-300 pt-4 text-center text-xs text-gray-500">
            <p>Report generated on: {new Date().toLocaleDateString()}</p>
            <p>WorkHub - Vehicle Service Management</p>
          </footer>
        </div>

        {/* Print Styles */}
        <style global jsx>{`
          .print-only {
            display: none;
          }
          @media print {
            .no-print {
              display: none !important;
            }
            .print-only {
              display: block;
            }
            .print-container {
              padding: 1rem;
            }
            .card-print {
              box-shadow: none !important;
              border: none !important;
            }
            .print-service-col {
              max-width: 200px;
              white-space: normal !important;
            }
            .print-notes-col {
              max-width: 200px;
              white-space: normal !important;
            }
            .print-text-wrap {
              word-break: break-word;
              white-space: normal !important;
            }
          }

          @media (max-width: 640px) {
            .overflow-x-auto {
              overflow-x: auto;
            }

            .summary-grid {
              grid-template-columns: 1fr 1fr !important;
            }
          }
        `}</style>
      </div>
    </ErrorBoundary>
  );
}
