import type { Request } from 'express';
import type { User } from '@supabase/supabase-js';

// Define a custom interface for the AuthenticatedUser by extending Supabase's User type
export interface AuthenticatedUser extends User {
  // Custom claims that might not be directly on the Supabase User object
  employee_id?: string;
  is_active?: boolean;
  user_role?: string;
  // Add role property if it's a custom claim not covered by Supabase's User type
  role?: string;
}

// Extend Express Request interface to include custom properties
declare global {
  namespace Express {
    interface Request {
      user?: AuthenticatedUser;
      userId?: string;
      employeeId?: string;
      userRole?: string;
      isActive?: boolean; // Add isActive to the Request interface
      requestId?: string; // Add requestId for tracking requests
      validatedData?: {
        body: any;
        params: any;
        query: any;
      }; // Add validatedData for input validation middleware
    }
  }
}
