'use client';

import { ListPlus } from 'lucide-react';
import { useRouter } from 'next/navigation';

import type { TaskFormData } from '@/lib/schemas/taskSchemas';
import type {
  CreateTaskData,
  TaskPriorityPrisma,
  TaskStatusPrisma,
} from '@/lib/types/domain';

import TaskForm from '@/components/features/tasks/TaskForm';
import { PageHeader } from '@/components/ui/PageHeader';
import { usePredefinedEntityToast } from '@/hooks/forms/useFormToast';
// import {addTask as storeAddTask} from '@/lib/store'; // Removed
import { useCreateTask } from '@/lib/stores/queries/useTasks'; // Added

export default function AddTaskPage() {
  const router = useRouter();
  const { showEntityCreated, showEntityCreationError } =
    usePredefinedEntityToast('task');
  const {
    error: submissionError,
    isPending,
    mutateAsync: createTask,
  } = useCreateTask();

  const handleSubmit = async (data: TaskFormData) => {
    try {
      // Transform TaskFormData to CreateTaskData with proper type alignment
      const createTaskData: CreateTaskData = {
        dateTime: data.dateTime, // Already formatted by TaskForm
        deadline: data.deadline || undefined, // Convert empty string to undefined
        description: data.description,
        driverEmployeeId: data.driverEmployeeId || undefined, // Convert null to undefined
        estimatedDuration: data.estimatedDuration,
        location: data.location,
        notes: data.notes || undefined, // Convert empty string to undefined
        priority: data.priority as TaskPriorityPrisma, // Direct mapping - already correct format
        requiredSkills: data.requiredSkills,
        staffEmployeeId: data.staffEmployeeId, // Required field, already validated by form
        status: data.status.replace(' ', '_') as TaskStatusPrisma, // Convert "In Progress" to "In_Progress"
        subtasks: data.subtasks, // Direct mapping - already correct format
        vehicleId: data.vehicleId || undefined, // Convert null to undefined
      };

      const newTask = await createTask(createTaskData);

      // Create a task object with the data needed for the toast display
      const taskForToast = {
        title:
          data.description.slice(0, 30) +
          (data.description.length > 30 ? '...' : ''),
        name:
          data.description.slice(0, 30) +
          (data.description.length > 30 ? '...' : ''),
      };
      showEntityCreated(taskForToast);
      router.push('/tasks');
    } catch (error: any) {
      // Renamed error to err to avoid conflict with submissionError
      console.error('Error adding task:', error);
      const errorMessage =
        error.message ||
        submissionError?.message ||
        'Failed to add task. Please try again.';
      showEntityCreationError(errorMessage);
    }
  };

  return (
    <div className="space-y-6">
      <PageHeader
        description="Enter the details for the new task or job."
        icon={ListPlus}
        title="Add New Task"
      />
      {submissionError && (
        <p className="rounded-md bg-red-100 p-3 text-red-500">
          Error: {submissionError.message}
        </p>
      )}
      <TaskForm
        isEditing={false}
        onSubmit={handleSubmit}
        isLoading={isPending}
      />
    </div>
  );
}
