'use client';

import type { ColumnDef } from '@tanstack/react-table';

import {
  Activity,
  CheckCircle,
  Edit,
  Eye,
  Loader2,
  MoreHorizontal,
  Plus,
  RefreshCw,
  Shield,
  Trash2,
  UserPlus,
  Users,
  XCircle,
} from 'lucide-react';
import React, { useCallback, useEffect, useState } from 'react';

import type { User } from '@/types';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { EmployeeSelector } from '@/components/ui/forms/EmployeeSelector';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { DataTable } from '@/components/ui/tables/DataTable';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import { useFormToast } from '@/hooks/forms/useFormToast';
import { adminService } from '@/lib/api/services/admin';
import { getTokenRefreshService } from '@/lib/services/TokenRefreshService';

export function UserManagement() {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingUser, setEditingUser] = useState<null | User>(null);
  const [newUser, setNewUser] = useState({
    email: '',
    emailVerified: false,
    employee_id: '',
    full_name: '',
    isActive: true,
    phone: '',
    role: 'USER',
  });
  const [totalUsers, setTotalUsers] = useState(0);
  const [limit] = useState(10); // Items per page for DataTable
  const { showFormError, showFormSuccess } = useFormToast();

  // State for Alert Dialogs
  const [isDeleteAlertOpen, setIsDeleteAlertOpen] = useState(false);
  const [userToDeleteId, setUserToDeleteId] = useState<null | string>(null);
  const [isToggleAlertOpen, setIsToggleAlertOpen] = useState(false);
  const [userToToggleId, setUserToToggleId] = useState<null | string>(null);
  const [userToToggleStatus, setUserToToggleStatus] = useState<boolean | null>(
    null
  );

  // Authentication error state
  const [authError, setAuthError] = useState<null | string>(null);
  const [isRefreshingAuth, setIsRefreshingAuth] = useState(false);

  // Client-side email validation
  const isValidEmail = (email: string) => {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  };

  // Helper functions for formatting
  const formatDate = (dateString: null | string | undefined) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleDateString('en-US', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
    });
  };

  const getTimeAgo = (dateString: null | string | undefined) => {
    if (!dateString) return 'Never';
    const now = new Date();
    const date = new Date(dateString);
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) return 'Just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
    if (diffInSeconds < 86_400)
      return `${Math.floor(diffInSeconds / 3600)}h ago`;
    if (diffInSeconds < 2_592_000)
      return `${Math.floor(diffInSeconds / 86_400)}d ago`;
    return formatDate(dateString);
  };

  const getUserInitials = (email: string, fullName?: string) => {
    if (fullName?.trim()) {
      const names = fullName.trim().split(' ');
      return names.length > 1
        ? `${names[0]?.[0] || ''}${names.at(-1)?.[0] || ''}`.toUpperCase()
        : fullName.slice(0, 2).toUpperCase();
    }
    return email.slice(0, 2).toUpperCase();
  };

  const fetchUsers = useCallback(async () => {
    setLoading(true);
    setAuthError(null); // Clear previous auth errors
    try {
      const response = await adminService.getAllUsers({
        limit: 100, // Get more users for DataTable to handle pagination
        page: 1,
        search: '',
      });
      // UserService transformer handles the data transformation automatically
      setUsers(response.data || []);

      // Handle pagination safely
      const pagination = response.pagination || { limit: 100, total: 0 };
      setTotalUsers(pagination.total);
    } catch (error: any) {
      // Check if this is an authentication error
      const isAuthError =
        error?.status === 401 ||
        error?.status === 500 ||
        error?.code === 'NO_TOKEN' ||
        error?.code === 'INVALID_TOKEN' ||
        error?.message?.includes('Authentication failed') ||
        error?.message?.includes('Failed to fetch users'); // Backend returns 500 but frontend sees generic error

      if (isAuthError) {
        // Try to validate and refresh the session
        try {
          const tokenService = getTokenRefreshService();
          const sessionInfo = await tokenService.getSessionInfo();

          if (sessionInfo.isValid) {
            // Session seems valid but API call failed - might be a server issue
            setAuthError(
              'Server error occurred. This might be a temporary issue. Try refreshing.'
            );
          } else {
            if (sessionInfo.isExpired) {
              setAuthError(
                'Your session has expired. Click "Refresh Authentication" to renew your session.'
              );
            } else {
              setAuthError(
                'Authentication failed. Please refresh the page to sign in again.'
              );
            }
          }
        } catch {
          setAuthError(
            'Authentication system error. Please refresh the page to sign in again.'
          );
        }
      } else {
        // Non-authentication error
        showFormError(error as Error, {
          errorDescription: error.message || 'Failed to load user data.',
          errorTitle: 'Error fetching users',
        });
      }
    } finally {
      setLoading(false);
    }
  }, [showFormError]);

  useEffect(() => {
    fetchUsers();
  }, [fetchUsers]);

  const handleAuthRefresh = async () => {
    setIsRefreshingAuth(true);
    setAuthError(null);

    try {
      const tokenService = getTokenRefreshService();
      const refreshSuccess = await tokenService.refreshNow();

      if (refreshSuccess) {
        // Token refreshed successfully, retry the API call
        await fetchUsers();
      } else {
        setAuthError('Failed to refresh authentication. Please sign in again.');
      }
    } catch {
      setAuthError('Authentication refresh failed. Please sign in again.');
    } finally {
      setIsRefreshingAuth(false);
    }
  };

  const handlePageRefresh = () => {
    globalThis.location.reload();
  };

  const handleAddUser = async () => {
    if (!isValidEmail(newUser.email)) {
      showFormError(new Error('Please enter a valid email address.'), {
        errorTitle: 'Validation Error',
      });
      return;
    }

    setLoading(true);
    try {
      const createdUser = await adminService.createUser(newUser);
      showFormSuccess({
        successDescription: `User ${createdUser.email} has been added.`,
        successTitle: 'User created',
      });
      setIsDialogOpen(false);
      setNewUser({
        email: '',
        emailVerified: false,
        employee_id: '',
        full_name: '',
        isActive: true,
        phone: '',
        role: 'USER',
      });
      fetchUsers(); // Refresh list
    } catch (error: any) {
      showFormError(error.message || 'Failed to create user.', {
        errorTitle: 'Error creating user',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleEditUser = async () => {
    if (!editingUser) return;

    if (!isValidEmail(editingUser.email || '')) {
      showFormError('Please enter a valid email address.', {
        errorTitle: 'Validation Error',
      });
      return;
    }

    setLoading(true);
    try {
      const updatedUser = await adminService.updateUser(editingUser.id, {
        email: editingUser.email,
        emailVerified: editingUser.email_confirmed_at ? true : false,
        isActive: editingUser.isActive,
        role: editingUser.role,
      });
      showFormSuccess({
        successDescription: `User ${(updatedUser as any).email} has been updated.`,
        successTitle: 'User updated',
      });
      setIsDialogOpen(false);
      setEditingUser(null);
      fetchUsers(); // Refresh list
    } catch (error: any) {
      showFormError(error.message || 'Failed to update user.', {
        errorTitle: 'Error updating user',
      });
    } finally {
      setLoading(false);
    }
  };

  const confirmDeleteUser = async () => {
    if (!userToDeleteId) return;
    setLoading(true);
    try {
      await adminService.deleteUser(userToDeleteId);
      showFormSuccess({
        successDescription: 'User has been successfully deleted.',
        successTitle: 'User deleted',
      });
      fetchUsers(); // Refresh list
    } catch (error: any) {
      showFormError(error.message || 'Failed to delete user.', {
        errorTitle: 'Error deleting user',
      });
    } finally {
      setLoading(false);
      setIsDeleteAlertOpen(false);
      setUserToDeleteId(null);
    }
  };

  const confirmToggleActivation = async () => {
    if (!userToToggleId || userToToggleStatus === null) return;
    setLoading(true);
    try {
      const updatedUser = await adminService.toggleUserActivation(
        userToToggleId,
        !userToToggleStatus
      );
      showFormSuccess({
        successDescription: `User ${(updatedUser as any).email} is now ${
          (updatedUser as any).isActive ? 'active' : 'inactive'
        }.`,
        successTitle: 'User status updated',
      });
      fetchUsers(); // Refresh list
    } catch (error: any) {
      showFormError(error.message || 'Failed to toggle user activation.', {
        errorTitle: 'Error updating status',
      });
    } finally {
      setLoading(false);
      setIsToggleAlertOpen(false);
      setUserToToggleId(null);
      setUserToToggleStatus(null);
    }
  };

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case 'ADMIN': {
        return 'bg-purple-500 hover:bg-purple-600 text-white';
      }
      case 'MANAGER': {
        return 'bg-blue-500 hover:bg-blue-600 text-white';
      }
      case 'READONLY': {
        return 'bg-yellow-500 hover:bg-yellow-600 text-white';
      }
      case 'SUPER_ADMIN': {
        return 'bg-red-500 hover:bg-red-600 text-white';
      }
      case 'USER': {
        return 'bg-green-500 hover:bg-green-600 text-white';
      }
      default: {
        return 'bg-gray-500 hover:bg-gray-600 text-white';
      }
    }
  };

  // Define columns for DataTable
  const columns: ColumnDef<User>[] = [
    {
      accessorKey: 'email',
      cell: ({ row }) => {
        const user = row.original;
        return (
          <div className="flex items-center space-x-3">
            <Avatar className="size-10">
              <AvatarImage alt={user.email} src="" />
              <AvatarFallback className="text-sm">
                {getUserInitials(user.email, user.full_name)}
              </AvatarFallback>
            </Avatar>
            <div className="min-w-0 flex-1">
              <div className="flex items-center space-x-2">
                <p className="truncate text-sm font-medium">
                  {user.full_name || user.email.split('@')[0]}
                </p>
                {user.email_confirmed_at && (
                  <Shield className="size-3 text-green-500" />
                )}
              </div>
              <p className="truncate text-xs text-muted-foreground">
                {user.email}
              </p>
              {user.employee_id && (
                <p className="text-xs text-muted-foreground">
                  ID: {user.employee_id}
                </p>
              )}
            </div>
          </div>
        );
      },
      header: 'User',
    },
    {
      accessorKey: 'role',
      cell: ({ row }) => {
        const user = row.original;
        return (
          <Badge className={getRoleBadgeColor(user.role)}>
            {(user.role || 'USER').replace('_', ' ')}
          </Badge>
        );
      },
      header: 'Role',
    },
    {
      accessorKey: 'isActive',
      cell: ({ row }) => {
        const user = row.original;
        return (
          <div className="flex items-center space-x-2">
            <Badge variant={user.isActive ? 'default' : 'destructive'}>
              {user.isActive ? 'Active' : 'Inactive'}
            </Badge>
            {user.email_confirmed_at ? (
              <CheckCircle className="size-4 text-green-500" />
            ) : (
              <XCircle className="size-4 text-red-500" />
            )}
          </div>
        );
      },
      header: 'Status',
    },
    {
      accessorKey: 'last_sign_in_at',
      cell: ({ row }) => {
        const user = row.original;
        return (
          <div className="text-sm">{getTimeAgo(user.last_sign_in_at)}</div>
        );
      },
      header: 'Last Activity',
    },
    {
      accessorKey: 'created_at',
      cell: ({ row }) => {
        const user = row.original;
        return <div className="text-sm">{formatDate(user.created_at)}</div>;
      },
      header: 'Joined',
    },
    {
      cell: ({ row }) => {
        const user = row.original;
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button className="size-8 p-0" variant="ghost">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="size-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem
                onClick={() => {
                  setEditingUser(user);
                  setIsDialogOpen(true);
                }}
              >
                <Edit className="mr-2 size-4" />
                Edit user
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() => {
                  setUserToToggleId(user.id);
                  setUserToToggleStatus(user.isActive);
                  setIsToggleAlertOpen(true);
                }}
              >
                <Activity className="mr-2 size-4" />
                {user.isActive ? 'Deactivate' : 'Activate'}
              </DropdownMenuItem>
              <DropdownMenuItem
                className="text-red-600 focus:text-red-600"
                onClick={() => {
                  setUserToDeleteId(user.id);
                  setIsDeleteAlertOpen(true);
                }}
              >
                <Trash2 className="mr-2 size-4" />
                Delete user
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
      header: 'Actions',
      id: 'actions',
    },
  ];

  return (
    <div className="space-y-6">
      {/* Header Section with Statistics */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-3xl font-bold tracking-tight">
              User Management
            </h2>
            <p className="text-muted-foreground">
              Manage user accounts, roles, and permissions
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Button onClick={fetchUsers} size="sm" variant="outline">
              <RefreshCw className="mr-2 size-4" />
              Refresh
            </Button>
            <Dialog onOpenChange={setIsDialogOpen} open={isDialogOpen}>
              <DialogTrigger asChild>
                <Button
                  onClick={() => {
                    setEditingUser(null);
                    setNewUser({
                      email: '',
                      emailVerified: false,
                      employee_id: '',
                      full_name: '',
                      isActive: true,
                      phone: '',
                      role: 'USER',
                    });
                  }}
                >
                  <Plus className="mr-2 size-4" /> Add User
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[600px]">
                <DialogHeader>
                  <DialogTitle className="flex items-center gap-2">
                    {editingUser ? (
                      <>
                        <Edit className="size-5" />
                        Edit User Profile
                      </>
                    ) : (
                      <>
                        <UserPlus className="size-5" />
                        Add New User
                      </>
                    )}
                  </DialogTitle>
                  <DialogDescription>
                    {editingUser
                      ? 'Update user information and permissions.'
                      : 'Create a new user account with role and permissions.'}
                  </DialogDescription>
                </DialogHeader>

                <Tabs className="w-full" defaultValue="basic">
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="basic">Basic Info</TabsTrigger>
                    <TabsTrigger value="advanced">Advanced</TabsTrigger>
                  </TabsList>

                  <TabsContent className="mt-4 space-y-4" value="basic">
                    <div className="grid gap-4">
                      <div className="grid grid-cols-4 items-center gap-4">
                        <Label className="text-right" htmlFor="full_name">
                          Full Name
                        </Label>
                        <Input
                          className="col-span-3"
                          id="full_name"
                          onChange={e =>
                            editingUser
                              ? setEditingUser({
                                  ...editingUser,
                                  full_name: e.target.value,
                                })
                              : setNewUser({
                                  ...newUser,
                                  full_name: e.target.value,
                                })
                          }
                          placeholder="Enter full name"
                          value={
                            editingUser
                              ? editingUser.full_name || ''
                              : newUser.full_name
                          }
                        />
                      </div>
                      <div className="grid grid-cols-4 items-center gap-4">
                        <Label className="text-right" htmlFor="email">
                          Email *
                        </Label>
                        <Input
                          className="col-span-3"
                          id="email"
                          onChange={e =>
                            editingUser
                              ? setEditingUser({
                                  ...editingUser,
                                  email: e.target.value,
                                })
                              : setNewUser({
                                  ...newUser,
                                  email: e.target.value,
                                })
                          }
                          placeholder="<EMAIL>"
                          type="email"
                          value={
                            editingUser ? editingUser.email : newUser.email
                          }
                        />
                      </div>
                      <div className="grid grid-cols-4 items-center gap-4">
                        <Label className="text-right" htmlFor="phone">
                          Phone
                        </Label>
                        <Input
                          className="col-span-3"
                          id="phone"
                          onChange={e =>
                            editingUser
                              ? setEditingUser({
                                  ...editingUser,
                                  phone: e.target.value,
                                })
                              : setNewUser({
                                  ...newUser,
                                  phone: e.target.value,
                                })
                          }
                          placeholder="+****************"
                          type="tel"
                          value={
                            editingUser
                              ? editingUser.phone || ''
                              : newUser.phone
                          }
                        />
                      </div>
                      <div className="grid grid-cols-4 items-center gap-4">
                        <Label className="text-right" htmlFor="role">
                          Role *
                        </Label>
                        <Select
                          onValueChange={value =>
                            editingUser
                              ? setEditingUser({ ...editingUser, role: value })
                              : setNewUser({ ...newUser, role: value })
                          }
                          value={editingUser ? editingUser.role : newUser.role}
                        >
                          <SelectTrigger className="col-span-3">
                            <SelectValue placeholder="Select a role" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="SUPER_ADMIN">
                              <div className="flex items-center gap-2">
                                <Shield className="size-4 text-red-500" />
                                Super Admin
                              </div>
                            </SelectItem>
                            <SelectItem value="ADMIN">
                              <div className="flex items-center gap-2">
                                <Shield className="size-4 text-purple-500" />
                                Admin
                              </div>
                            </SelectItem>
                            <SelectItem value="MANAGER">
                              <div className="flex items-center gap-2">
                                <Users className="size-4 text-blue-500" />
                                Manager
                              </div>
                            </SelectItem>
                            <SelectItem value="USER">
                              <div className="flex items-center gap-2">
                                <Users className="size-4 text-green-500" />
                                User
                              </div>
                            </SelectItem>
                            <SelectItem value="READONLY">
                              <div className="flex items-center gap-2">
                                <Eye className="size-4 text-yellow-500" />
                                Read Only
                              </div>
                            </SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent className="mt-4 space-y-4" value="advanced">
                    <div className="grid gap-4">
                      <div className="grid grid-cols-4 items-center gap-4">
                        <div className="col-span-4">
                          <EmployeeSelector
                            allowClear={true}
                            className="w-full"
                            label="Link to Employee (Optional)"
                            onValueChange={employeeId =>
                              editingUser
                                ? setEditingUser({
                                    ...editingUser,
                                    employee_id: employeeId,
                                  })
                                : setNewUser({
                                    ...newUser,
                                    employee_id: employeeId?.toString() || '',
                                  })
                            }
                            placeholder="Select an employee to link this user account..."
                            value={
                              editingUser
                                ? (editingUser.employee_id ?? null)
                                : newUser.employee_id
                                  ? Number.parseInt(newUser.employee_id)
                                  : null
                            }
                          />
                        </div>
                      </div>
                      <div className="grid grid-cols-4 items-center gap-4">
                        <Label className="text-right" htmlFor="isActive">
                          Account Status
                        </Label>
                        <div className="col-span-3 flex items-center space-x-2">
                          <Checkbox
                            checked={
                              editingUser
                                ? editingUser.isActive
                                : newUser.isActive
                            }
                            id="isActive"
                            onCheckedChange={checked =>
                              editingUser
                                ? setEditingUser({
                                    ...editingUser,
                                    isActive: Boolean(checked),
                                  })
                                : setNewUser({
                                    ...newUser,
                                    isActive: Boolean(checked),
                                  })
                            }
                          />
                          <Label className="text-sm" htmlFor="isActive">
                            Active account
                          </Label>
                        </div>
                      </div>
                      <div className="grid grid-cols-4 items-center gap-4">
                        <Label className="text-right" htmlFor="emailVerified">
                          Email Verification
                        </Label>
                        <div className="col-span-3 flex items-center space-x-2">
                          <Checkbox
                            checked={
                              editingUser
                                ? editingUser.email_confirmed_at
                                  ? true
                                  : false
                                : newUser.emailVerified
                            }
                            id="emailVerified"
                            onCheckedChange={checked =>
                              editingUser
                                ? setEditingUser({
                                    ...editingUser,
                                    email_confirmed_at: checked
                                      ? new Date().toISOString()
                                      : null,
                                  })
                                : setNewUser({
                                    ...newUser,
                                    emailVerified: Boolean(checked),
                                  })
                            }
                          />
                          <Label className="text-sm" htmlFor="emailVerified">
                            Email verified
                          </Label>
                        </div>
                      </div>
                    </div>
                  </TabsContent>
                </Tabs>
                <DialogFooter>
                  <Button
                    disabled={loading}
                    onClick={editingUser ? handleEditUser : handleAddUser}
                    type="submit"
                  >
                    {loading && (
                      <Loader2 className="mr-2 size-4 animate-spin" />
                    )}
                    {editingUser ? 'Save changes' : 'Add User'}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Statistics Cards */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Users</CardTitle>
              <Users className="size-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalUsers}</div>
              <p className="text-xs text-muted-foreground">
                {users.filter(u => u.isActive).length} active
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Active Users
              </CardTitle>
              <Activity className="size-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {users.filter(u => u.isActive).length}
              </div>
              <p className="text-xs text-muted-foreground">
                {Math.round(
                  (users.filter(u => u.isActive).length /
                    Math.max(users.length, 1)) *
                    100
                )}
                % of total
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Verified Emails
              </CardTitle>
              <Shield className="size-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {users.filter(u => u.email_confirmed_at).length}
              </div>
              <p className="text-xs text-muted-foreground">
                {Math.round(
                  (users.filter(u => u.email_confirmed_at).length /
                    Math.max(users.length, 1)) *
                    100
                )}
                % verified
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Admins</CardTitle>
              <Shield className="size-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {
                  users.filter(
                    u => u.role === 'ADMIN' || u.role === 'SUPER_ADMIN'
                  ).length
                }
              </div>
              <p className="text-xs text-muted-foreground">
                System administrators
              </p>
            </CardContent>
          </Card>
        </div>
      </div>

      {authError ? (
        <div className="rounded-md border border-red-200 bg-red-50 p-4">
          <div className="flex items-center space-x-2">
            <XCircle className="size-5 text-red-500" />
            <div>
              <h3 className="font-medium text-red-800">Authentication Error</h3>
              <p className="text-sm text-red-700">{authError}</p>
              <div className="mt-3 flex space-x-2">
                <Button
                  disabled={isRefreshingAuth}
                  onClick={handleAuthRefresh}
                  size="sm"
                >
                  {isRefreshingAuth ? (
                    <>
                      <Loader2 className="mr-2 size-4 animate-spin" />
                      Refreshing...
                    </>
                  ) : (
                    'Refresh Authentication'
                  )}
                </Button>
                <Button onClick={handlePageRefresh} size="sm" variant="outline">
                  Refresh Page
                </Button>
              </div>
            </div>
          </div>
        </div>
      ) : loading ? (
        <div className="flex h-64 items-center justify-center">
          <Loader2 className="size-8 animate-spin text-primary" />
          <span className="ml-2 text-lg">Loading users...</span>
        </div>
      ) : (
        <DataTable
          className="w-full"
          columns={columns}
          data={users}
          emptyMessage="No users found."
          enableColumnVisibility={true}
          enableGlobalFilter={true}
          enableRowSelection={false}
          pageSize={limit}
          searchColumn="email"
          searchPlaceholder="Search users by email or role..."
        />
      )}

      {/* Alert Dialogs (moved outside of TableRow) */}
      <AlertDialog onOpenChange={setIsToggleAlertOpen} open={isToggleAlertOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {userToToggleStatus ? 'Deactivate User' : 'Activate User'}
            </AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to{' '}
              {userToToggleStatus ? 'deactivate' : 'activate'} user{' '}
              <span className="font-bold">
                {users.find(u => u.id === userToToggleId)?.email}
              </span>
              ?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmToggleActivation}>
              {userToToggleStatus ? 'Deactivate' : 'Activate'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <AlertDialog onOpenChange={setIsDeleteAlertOpen} open={isDeleteAlertOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete User</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to permanently delete user{' '}
              <span className="font-bold">
                {users.find(u => u.id === userToDeleteId)?.email}
              </span>
              ? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDeleteUser}>
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
