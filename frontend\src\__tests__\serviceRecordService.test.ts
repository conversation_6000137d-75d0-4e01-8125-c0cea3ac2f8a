import type { EnrichedServiceRecord, ServiceRecord } from '../lib/types/domain';

import { adminService } from '@/lib/api/services/admin';

// Mock globals for tests
declare global {
  var serviceRecordService: any;
}

// Mock the API service
jest.mock('../lib/services/apiService', () => ({
  api: {
    delete: jest.fn(),
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
  },
  extractApiData: jest.fn(response => response.data),
}));

describe('Service Record Service', () => {
  const mockServiceRecords: ServiceRecord[] = [
    {
      cost: 50,
      createdAt: '2023-01-01T10:00:00Z', // Added
      date: '2023-01-01',
      id: '1',
      notes: 'Regular maintenance',
      odometer: 10_000,
      servicePerformed: ['Oil Change'],
      updatedAt: '2023-01-01T10:00:00Z', // Added
      vehicleId: 1,
    },
    {
      cost: 30,
      createdAt: '2023-02-01T10:00:00Z', // Added
      date: '2023-02-01',
      id: '2',
      notes: 'Seasonal maintenance',
      odometer: 20_000,
      servicePerformed: ['Tire Rotation'],
      updatedAt: '2023-02-01T10:00:00Z', // Added
      vehicleId: 2,
    },
  ];

  const mockEnrichedServiceRecords: EnrichedServiceRecord[] = [
    {
      cost: 50,
      createdAt: '2023-01-01T10:00:00Z', // Added
      date: '2023-01-01',
      id: '1',
      licensePlate: 'XYZ-123', // Changed from vehiclePlateNumber to licensePlate
      notes: 'Regular maintenance',
      odometer: 10_000,
      servicePerformed: ['Oil Change'],
      updatedAt: '2023-01-01T10:00:00Z', // Added
      vehicleId: 1, // Changed to number
      vehicleMake: 'Toyota',
      vehicleModel: 'Camry',
      vehicleYear: 2020,
    },
    {
      cost: 30,
      createdAt: '2023-02-01T10:00:00Z', // Added
      date: '2023-02-01',
      id: '2',
      licensePlate: 'ABC-789', // Changed from vehiclePlateNumber to licensePlate
      notes: 'Seasonal maintenance',
      odometer: 20_000,
      servicePerformed: ['Tire Rotation'],
      updatedAt: '2023-02-01T10:00:00Z', // Added
      vehicleId: 2, // Changed to number
      vehicleMake: 'Honda',
      vehicleModel: 'Civic',
      vehicleYear: 2019,
    },
  ];

  beforeEach(() => {
    jest.resetAllMocks();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should fetch all service records', async () => {
    // Setup
    const apiModule = require('../lib/services/apiService');
    apiModule.api.get.mockResolvedValue({ data: mockServiceRecords });

    // Execute
    const result = await serviceRecordService.getAllServiceRecords();

    // Verify
    expect(apiModule.api.get).toHaveBeenCalledTimes(1);
    expect(apiModule.api.get).toHaveBeenCalledWith(
      '/servicerecords',
      undefined
    );
    expect(result).toEqual(mockServiceRecords);
  });

  it('should handle errors when fetching all service records', async () => {
    // Setup
    const mockError = new Error('API error');
    const apiModule = require('../lib/services/apiService');
    apiModule.api.get.mockRejectedValue(mockError);

    // Execute & Verify
    await expect(serviceRecordService.getAllServiceRecords()).rejects.toThrow(
      mockError
    );
    expect(apiModule.api.get).toHaveBeenCalledTimes(1);
  });

  it('should fetch service records for a specific vehicle', async () => {
    // Setup
    const vehicleId = 1;
    const vehicleServiceRecords = mockServiceRecords.filter(
      record => record.vehicleId === vehicleId
    );
    const apiModule = require('../lib/services/apiService');
    apiModule.api.get.mockResolvedValue({ data: vehicleServiceRecords });

    // Execute
    const result = await serviceRecordService.getServiceRecords(vehicleId);

    // Verify
    expect(apiModule.api.get).toHaveBeenCalledTimes(1);
    expect(apiModule.api.get).toHaveBeenCalledWith(
      `/vehicles/${vehicleId}/servicerecords`,
      undefined
    );
    expect(result).toEqual(vehicleServiceRecords);
  });

  it('should fetch enriched service records', async () => {
    // Setup
    const apiModule = require('../lib/services/apiService');
    apiModule.api.get.mockResolvedValue({ data: mockEnrichedServiceRecords });

    // Execute
    const result = await serviceRecordService.getAllEnrichedServiceRecords();

    // Verify
    expect(apiModule.api.get).toHaveBeenCalledTimes(1);
    expect(apiModule.api.get).toHaveBeenCalledWith(
      '/servicerecords/enriched',
      undefined
    );
    expect(result).toEqual(mockEnrichedServiceRecords);
  });
});
