import type { NextFunction, Request, Response } from 'express';

import type { AuthenticatedUser } from '../types/express.d.js'; // Import AuthenticatedUser

import { supabaseAdmin } from '../lib/supabase.js';
import { logAuditEvent } from '../utils/auditLogger.js';
import logger from '../utils/logger.js';

// Define interface for user profile data retrieved from Supabase
interface UserProfileData {
  is_active: boolean;
  role: string;
}

/**
 * Enhanced middleware for role-based access control
 * Uses custom claims injected by the Auth Hook from user_profiles table
 *
 * OPTIMIZED: Auth Hook verified working - custom claims (user_role, is_active, employee_id)
 * are consistently injected into all JWTs. Database fallback was removed.
 */
export const requireRole = (allowedRoles: string[]) => {
  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        logAuditEvent(
          {
            action: 'AUTHORIZATION_FAILURE',
            details: { method: req.method, path: req.path, requiredRoles: allowedRoles },
            errorCode: 'NO_USER_FOR_AUTHZ',
            eventType: 'AUTHZ',
            message: 'Authorization check failed: User not authenticated.',
            outcome: 'FAILURE',
            statusCode: 401,
          },
          req,
        );
        res.status(401).json({
          code: 'NO_USER',
          error: 'Authentication required',
          message: 'User must be authenticated to access this resource',
        });
        return;
      }

      const authenticatedUser = req.user; // Ensure type
      const userRole = req.userRole; // Use req.userRole
      const isActive = req.isActive; // Use req.isActive

      // Debug logging for development only (contains sensitive JWT data)
      if (process.env.NODE_ENV !== 'production') {
        logger.debug('🔍 JWT Claims Analysis for Role Check:', {
          email: req.user?.email, // Use req.user?.email
          isActive,
          userId: req.userId, // Use req.userId
          userRole,
        });
      }

      if (userRole === undefined || isActive === undefined) {
        logAuditEvent(
          {
            action: 'AUTHORIZATION_FAILURE',
            details: {
              email: req.user?.email, // Use req.user?.email
              isActiveProvided: isActive !== undefined,
              method: req.method,
              path: req.path,
              userRoleProvided: userRole !== undefined,
            },
            errorCode: 'MISSING_CRITICAL_CLAIMS',
            eventType: 'AUTHZ',
            message:
              'Authorization check failed: Critical user profile information missing from token.',
            outcome: 'FAILURE',
            statusCode: 500,
            userId: req.userId, // Use req.userId
          },
          req,
        );
        logger.error(
          '❌ Critical: user_role or is_active missing from JWT claims despite expectation.',
          {
            email: req.user?.email, // Use req.user?.email
            isActiveProvided: isActive !== undefined,
            userId: req.userId, // Use req.userId
            userRoleProvided: userRole !== undefined,
          },
        );
        res.status(500).json({
          code: 'MISSING_CRITICAL_CLAIMS',
          error: 'Authentication configuration error',
          message:
            'Critical user profile information is missing from token. Please contact support.',
        });
        return;
      }

      // Check if user is active
      if (!isActive) {
        logAuditEvent(
          {
            action: 'AUTHORIZATION_FAILURE',
            details: { email: req.user?.email, method: req.method, path: req.path }, // Use req.user?.email
            errorCode: 'ACCOUNT_INACTIVE',
            eventType: 'AUTHZ',
            message: 'Authorization check failed: User account is inactive.',
            outcome: 'FAILURE',
            statusCode: 403,
            userId: req.userId, // Use req.userId
            userRole: userRole, // userRole is confirmed to exist here
          },
          req,
        );
        res.status(403).json({
          code: 'ACCOUNT_INACTIVE',
          error: 'Account inactive',
          message: 'Your account has been deactivated. Please contact an administrator.',
        });
        return;
      }

      if (!allowedRoles.includes(userRole)) {
        logAuditEvent(
          {
            action: 'AUTHORIZATION_FAILURE',
            details: {
              email: req.user?.email, // Use req.user?.email
              method: req.method,
              path: req.path,
              requiredRoles: allowedRoles,
            },
            errorCode: 'INSUFFICIENT_PERMISSIONS',
            eventType: 'AUTHZ',
            message: `Authorization check failed: User role '${userRole}' not in allowed roles [${allowedRoles.join(', ')}].`,
            outcome: 'FAILURE',
            statusCode: 403,
            userId: req.userId, // Use req.userId
            userRole: userRole,
          },
          req,
        );
        res.status(403).json({
          code: 'INSUFFICIENT_PERMISSIONS',
          error: 'Insufficient permissions',
          message: `Access denied. Required roles: ${allowedRoles.join(
            ', ',
          )}. Your role: ${userRole}`,
        });
        return;
      }

      logAuditEvent(
        {
          action: 'AUTHORIZATION_SUCCESS',
          details: {
            email: req.user?.email, // Use req.user?.email
            method: req.method,
            path: req.path,
          },
          eventType: 'AUTHZ',
          message: `User authorized successfully for roles [${allowedRoles.join(', ')}]`,
          outcome: 'SUCCESS',
          userId: req.userId, // Use req.userId
          userRole: userRole,
        },
        req,
      );

      // Log successful authorization (development only)
      if (process.env.NODE_ENV !== 'production') {
        logger.info(
          `✅ User authorized: ${String(req.user?.email)} (Role: ${userRole} from JWT_CLAIMS)`, // Use req.user?.email
        );
      }

      next();
    } catch (error: any) {
      logAuditEvent(
        {
          action: 'AUTHORIZATION_SERVICE_ERROR',
          details: {
            error: error.message,
            method: req.method,
            path: req.path,
            stack: error.stack?.substring(0, 200),
          },
          errorCode: 'AUTHZ_SERVICE_ERROR',
          eventType: 'AUTHZ',
          message: 'Unexpected error during role authorization process.',
          outcome: 'FAILURE',
          statusCode: 500,
          userId: req.user?.id, // User might not be fully populated if error is early
          userRole: req.user?.user_role,
        },
        req,
      );
      // Always log authorization errors as they're security-critical
      logger.error('Role authorization error:', error);
      res.status(500).json({
        code: 'AUTHZ_SERVICE_ERROR',
        error: 'Authorization service unavailable',
      });
    }
  };
};
