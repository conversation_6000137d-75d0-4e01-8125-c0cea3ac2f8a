// frontend/src/components/features/reporting/tables/ReportingDataTable.tsx

'use client';

import React, { useMemo, useState } from 'react';
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
  SortingState,
  ColumnFiltersState,
  VisibilityState,
} from '@tanstack/react-table';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  ChevronDown,
  Download,
  Filter,
  Search,
  Settings,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
} from 'lucide-react';

/**
 * Generic data interface for reporting table
 */
export interface ReportingTableData {
  id: string;
  [key: string]: any;
}

/**
 * Props for ReportingDataTable component
 */
interface ReportingDataTableProps<TData extends ReportingTableData> {
  data: TData[];
  columns: ColumnDef<TData>[];
  title?: string;
  description?: string;
  loading?: boolean;
  error?: string | undefined;
  searchable?: boolean;
  filterable?: boolean;
  exportable?: boolean;
  pagination?: boolean;
  pageSize?: number;
  className?: string;
  onRowClick?: ((row: TData) => void) | undefined;
  onExport?: ((data: TData[]) => void) | undefined;
}

/**
 * Loading skeleton for data table
 */
const TableLoadingSkeleton = ({ columns }: { columns: number }) => (
  <div className="animate-pulse">
    <div className="border rounded-lg">
      {/* Header */}
      <div className="border-b p-4">
        <div className="flex gap-4">
          {Array.from({ length: columns }).map((_, i) => (
            <div key={i} className="h-4 bg-gray-200 rounded flex-1"></div>
          ))}
        </div>
      </div>

      {/* Rows */}
      {Array.from({ length: 5 }).map((_, rowIndex) => (
        <div key={rowIndex} className="border-b p-4">
          <div className="flex gap-4">
            {Array.from({ length: columns }).map((_, colIndex) => (
              <div
                key={colIndex}
                className="h-4 bg-gray-100 rounded flex-1"
              ></div>
            ))}
          </div>
        </div>
      ))}
    </div>
  </div>
);

/**
 * Error display for data table
 */
const TableErrorDisplay = ({ error }: { error: string }) => (
  <div className="flex flex-col items-center justify-center h-64 text-gray-500 border rounded-lg">
    <div className="text-4xl mb-2">📋</div>
    <p className="text-sm">Failed to load table data</p>
    <p className="text-xs text-gray-400 mt-1">{error}</p>
  </div>
);

/**
 * Empty state for data table
 */
const TableEmptyState = () => (
  <div className="flex flex-col items-center justify-center h-64 text-gray-500 border rounded-lg">
    <div className="text-4xl mb-2">📋</div>
    <p className="text-sm">No data available</p>
  </div>
);

/**
 * ReportingDataTable Component
 *
 * A comprehensive, reusable data table component for reporting data.
 * Built with TanStack Table for advanced features and performance.
 *
 * Features:
 * - Sorting, filtering, and pagination
 * - Column visibility controls
 * - Global search
 * - Export functionality
 * - Loading and error states
 * - Responsive design
 * - Row click handling
 *
 * @param props - Component props
 * @returns JSX element
 */
export function ReportingDataTable<TData extends ReportingTableData>({
  data,
  columns,
  title = 'Data Table',
  description,
  loading = false,
  error,
  searchable = true,
  filterable = true,
  exportable = true,
  pagination = true,
  pageSize = 10,
  className = '',
  onRowClick,
  onExport,
}: ReportingDataTableProps<TData>) {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [globalFilter, setGlobalFilter] = useState('');

  // Enhanced columns with sorting icons
  const enhancedColumns = useMemo(() => {
    return columns.map(column => ({
      ...column,
      header: (headerContext: any) => {
        const { column: col } = headerContext;
        const originalHeader =
          typeof column.header === 'function'
            ? column.header(headerContext)
            : column.header;

        if (!col.getCanSort()) {
          return originalHeader;
        }

        return (
          <Button
            variant="ghost"
            onClick={() => col.toggleSorting(col.getIsSorted() === 'asc')}
            className="h-auto p-0 font-semibold hover:bg-transparent"
          >
            {originalHeader}
            {col.getIsSorted() === 'asc' ? (
              <ArrowUp className="ml-2 h-4 w-4" />
            ) : col.getIsSorted() === 'desc' ? (
              <ArrowDown className="ml-2 h-4 w-4" />
            ) : (
              <ArrowUpDown className="ml-2 h-4 w-4" />
            )}
          </Button>
        );
      },
    }));
  }, [columns]);

  const table = useReactTable({
    data,
    columns: enhancedColumns as ColumnDef<TData>[],
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    onGlobalFilterChange: setGlobalFilter,
    getCoreRowModel: getCoreRowModel(),
    ...(pagination && { getPaginationRowModel: getPaginationRowModel() }),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      globalFilter,
    },
    initialState: {
      pagination: {
        pageSize,
      },
    },
  });

  // Handle export
  const handleExport = () => {
    if (onExport) {
      const filteredData = table
        .getFilteredRowModel()
        .rows.map(row => row.original);
      onExport(filteredData);
    }
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>{title}</CardTitle>
          {description && <CardDescription>{description}</CardDescription>}
        </CardHeader>
        <CardContent>
          <TableLoadingSkeleton columns={columns.length} />
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>{title}</CardTitle>
          {description && <CardDescription>{description}</CardDescription>}
        </CardHeader>
        <CardContent>
          <TableErrorDisplay error={error} />
        </CardContent>
      </Card>
    );
  }

  if (!data || data.length === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>{title}</CardTitle>
          {description && <CardDescription>{description}</CardDescription>}
        </CardHeader>
        <CardContent>
          <TableEmptyState />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Table Controls */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {/* Global Search */}
            {searchable && (
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search all columns..."
                  value={globalFilter}
                  onChange={e => setGlobalFilter(e.target.value)}
                  className="pl-8 w-[250px]"
                />
              </div>
            )}

            {/* Filter Indicator */}
            {filterable && columnFilters.length > 0 && (
              <Badge variant="secondary" className="flex items-center gap-1">
                <Filter className="h-3 w-3" />
                {columnFilters.length} filter(s)
              </Badge>
            )}
          </div>

          <div className="flex items-center space-x-2">
            {/* Export Button - Placeholder for integration with existing export system */}
            {exportable && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleExport}
                className="flex items-center gap-2"
              >
                <Download className="h-4 w-4" />
                Export
              </Button>
            )}

            {/* Column Visibility */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-2"
                >
                  <Settings className="h-4 w-4" />
                  Columns
                  <ChevronDown className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-[200px]">
                {table
                  .getAllColumns()
                  .filter(column => column.getCanHide())
                  .map(column => {
                    return (
                      <DropdownMenuCheckboxItem
                        key={column.id}
                        className="capitalize"
                        checked={column.getIsVisible()}
                        onCheckedChange={value =>
                          column.toggleVisibility(!!value)
                        }
                      >
                        {column.id}
                      </DropdownMenuCheckboxItem>
                    );
                  })}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Table */}
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              {table.getHeaderGroups().map(headerGroup => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map(header => (
                    <TableHead key={header.id} className="font-semibold">
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  ))}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map(row => (
                  <TableRow
                    key={row.id}
                    data-state={row.getIsSelected() && 'selected'}
                    className={
                      onRowClick ? 'cursor-pointer hover:bg-muted/50' : ''
                    }
                    onClick={() => onRowClick?.(row.original)}
                  >
                    {row.getVisibleCells().map(cell => (
                      <TableCell key={cell.id}>
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext()
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell
                    colSpan={columns.length}
                    className="h-24 text-center"
                  >
                    No results found.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>

        {/* Pagination */}
        {pagination && (
          <div className="flex items-center justify-between space-x-2 py-4">
            <div className="text-sm text-muted-foreground">
              Showing {table.getState().pagination.pageIndex * pageSize + 1} to{' '}
              {Math.min(
                (table.getState().pagination.pageIndex + 1) * pageSize,
                table.getFilteredRowModel().rows.length
              )}{' '}
              of {table.getFilteredRowModel().rows.length} entries
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => table.previousPage()}
                disabled={!table.getCanPreviousPage()}
              >
                Previous
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => table.nextPage()}
                disabled={!table.getCanNextPage()}
              >
                Next
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
