// frontend/src/components/features/reporting/dashboard/ReportingOverview.tsx

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { ReportingDashboard } from './ReportingDashboard';
import { SingleDelegationWidget, SingleTaskWidget } from './widgets';
import {
  BarChart3,
  FileText,
  CheckSquare,
  Search,
  TrendingUp,
  Users,
  Calendar,
  History,
} from 'lucide-react';

interface ReportingOverviewProps {
  className?: string;
}

/**
 * @component ReportingOverview
 * @description Comprehensive reporting overview with all report types
 *
 * Provides access to:
 * - All Delegations Report (dashboard with analytics)
 * - Single Delegation Report (detailed view)
 * - All Tasks Report (aggregated metrics)
 * - Single Task Report (detailed view)
 *
 * SOLID Principles Applied:
 * - SRP: Single responsibility of coordinating all reporting views
 * - OCP: Open for extension via new report types
 * - DIP: Depends on reporting component abstractions
 */
export const ReportingOverview: React.FC<ReportingOverviewProps> = ({
  className = '',
}) => {
  const [selectedDelegationId, setSelectedDelegationId] = useState('');
  const [selectedTaskId, setSelectedTaskId] = useState('');

  // Report type configurations
  const reportTypes = [
    {
      id: 'all-delegations',
      title: 'All Delegations',
      description: 'Comprehensive analytics and insights for all delegations',
      icon: BarChart3,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
    },
    {
      id: 'single-delegation',
      title: 'Single Delegation',
      description: 'Detailed report for a specific delegation',
      icon: FileText,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
    },
    {
      id: 'all-tasks',
      title: 'All Tasks',
      description: 'Task metrics and completion analytics',
      icon: TrendingUp,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
    },
    {
      id: 'single-task',
      title: 'Single Task',
      description: 'Detailed report for a specific task',
      icon: CheckSquare,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
    },
    // ENHANCED: Service history report type
    {
      id: 'service-history',
      title: 'Service History',
      description: 'Vehicle service history and maintenance analytics',
      icon: History,
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-50',
    },
  ];

  // Render report type cards
  const renderReportTypeCards = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4 mb-6">
      {reportTypes.map(type => {
        const Icon = type.icon;
        return (
          <Card
            key={type.id}
            className={`${type.bgColor} border-2 hover:shadow-md transition-shadow`}
          >
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-sm">
                <Icon className={`h-5 w-5 ${type.color}`} />
                {type.title}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-xs text-muted-foreground">
                {type.description}
              </p>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );

  // Render single delegation input
  const renderDelegationInput = () => (
    <div className="space-y-4">
      <div className="flex items-center gap-4">
        <div className="flex-1">
          <Label htmlFor="delegation-id">Delegation ID</Label>
          <div className="flex gap-2 mt-1">
            <Input
              id="delegation-id"
              placeholder="Enter delegation ID..."
              value={selectedDelegationId}
              onChange={e => setSelectedDelegationId(e.target.value)}
            />
            <Button
              variant="outline"
              onClick={() => setSelectedDelegationId('')}
              disabled={!selectedDelegationId}
            >
              Clear
            </Button>
          </div>
        </div>
      </div>

      {selectedDelegationId && (
        <div className="mt-4">
          <SingleDelegationWidget
            delegationId={selectedDelegationId}
            title={`Delegation Report - ${selectedDelegationId}`}
          />
        </div>
      )}

      {!selectedDelegationId && (
        <Card className="border-dashed">
          <CardContent className="flex items-center justify-center py-8">
            <div className="text-center">
              <Search className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
              <p className="text-sm text-muted-foreground">
                Enter a delegation ID to view detailed report
              </p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );

  // Render single task input
  const renderTaskInput = () => (
    <div className="space-y-4">
      <div className="flex items-center gap-4">
        <div className="flex-1">
          <Label htmlFor="task-id">Task ID</Label>
          <div className="flex gap-2 mt-1">
            <Input
              id="task-id"
              placeholder="Enter task ID..."
              value={selectedTaskId}
              onChange={e => setSelectedTaskId(e.target.value)}
            />
            <Button
              variant="outline"
              onClick={() => setSelectedTaskId('')}
              disabled={!selectedTaskId}
            >
              Clear
            </Button>
          </div>
        </div>
      </div>

      {selectedTaskId && (
        <div className="mt-4">
          <SingleTaskWidget
            taskId={selectedTaskId}
            title={`Task Report - ${selectedTaskId}`}
          />
        </div>
      )}

      {!selectedTaskId && (
        <Card className="border-dashed">
          <CardContent className="flex items-center justify-center py-8">
            <div className="text-center">
              <Search className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
              <p className="text-sm text-muted-foreground">
                Enter a task ID to view detailed report
              </p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">Reporting Center</h1>
        <p className="text-muted-foreground">
          Comprehensive reporting and analytics for delegations and tasks
        </p>
      </div>

      {/* Report Type Overview */}
      {renderReportTypeCards()}

      {/* Report Tabs */}
      <Tabs defaultValue="all-delegations" className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger
            value="all-delegations"
            className="flex items-center gap-2"
          >
            <BarChart3 className="h-4 w-4" />
            All Delegations
          </TabsTrigger>
          <TabsTrigger
            value="single-delegation"
            className="flex items-center gap-2"
          >
            <FileText className="h-4 w-4" />
            Single Delegation
          </TabsTrigger>
          <TabsTrigger value="all-tasks" className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            All Tasks
          </TabsTrigger>
          <TabsTrigger value="single-task" className="flex items-center gap-2">
            <CheckSquare className="h-4 w-4" />
            Single Task
          </TabsTrigger>
          <TabsTrigger
            value="service-history"
            className="flex items-center gap-2"
          >
            <History className="h-4 w-4" />
            Service History
          </TabsTrigger>
        </TabsList>

        <TabsContent value="all-delegations" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                All Delegations Analytics
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ReportingDashboard />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="single-delegation" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Single Delegation Report
              </CardTitle>
            </CardHeader>
            <CardContent>{renderDelegationInput()}</CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="all-tasks" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                All Tasks Analytics
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Calendar className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">
                  Task Analytics Dashboard
                </h3>
                <p className="text-muted-foreground mb-4">
                  Task analytics dashboard will be available in Phase 5
                </p>
                <p className="text-sm text-muted-foreground">
                  Currently showing task metrics within delegation analytics
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="single-task" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CheckSquare className="h-5 w-5" />
                Single Task Report
              </CardTitle>
            </CardHeader>
            <CardContent>{renderTaskInput()}</CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="service-history" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <History className="h-5 w-5" />
                Service History Analytics
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <History className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">
                  Service History Dashboard
                </h3>
                <p className="text-muted-foreground mb-4">
                  Enhanced delegation reports now include service history data
                </p>
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-left">
                  <h4 className="font-medium text-blue-900 mb-2">
                    Service History Integration
                  </h4>
                  <p className="text-sm text-blue-800 mb-3">
                    Service history data is now integrated into the main
                    delegation reports. Enable "Include Service History" in the
                    filters to see:
                  </p>
                  <ul className="text-sm text-blue-800 space-y-1 list-disc list-inside">
                    <li>Vehicle maintenance records alongside delegations</li>
                    <li>Service costs and delegation cost correlations</li>
                    <li>Vehicle utilization and service efficiency metrics</li>
                    <li>Service-delegation timeline integration</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ReportingOverview;
