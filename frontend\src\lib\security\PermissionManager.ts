/**
 * @file Permission Management Service - Single Responsibility Principle (SRP)
 * @module lib/security/PermissionManager
 *
 * This class handles ONLY permission checking logic following SRP principles.
 * It provides centralized permission validation and role-based access control.
 *
 * SECURITY NOTE: This is the single source of truth for permission logic.
 */

import { undefinedToNull } from '../utils/typeHelpers';

export type UserRole =
  | 'READONLY'
  | 'USER'
  | 'MANAGER'
  | 'ADMIN'
  | 'SUPER_ADMIN';

export type Permission =
  // General permissions
  | 'read'
  | 'write'
  | 'delete'
  | 'admin'

  // Entity-specific permissions
  | 'employees:read'
  | 'employees:write'
  | 'employees:delete'
  | 'employees:admin'
  | 'vehicles:read'
  | 'vehicles:write'
  | 'vehicles:delete'
  | 'vehicles:admin'
  | 'delegations:read'
  | 'delegations:write'
  | 'delegations:delete'
  | 'delegations:admin'
  | 'tasks:read'
  | 'tasks:write'
  | 'tasks:delete'
  | 'tasks:admin'
  | 'reports:read'
  | 'reports:write'
  | 'reports:admin'
  | 'settings:read'
  | 'settings:write'
  | 'settings:admin'

  // System permissions
  | 'system:admin'
  | 'system:audit'
  | 'system:backup';

export interface PermissionCheck {
  hasPermission: boolean;
  reason?: string;
  requiredRole?: UserRole;
}

/**
 * PermissionManager - Single Responsibility: Permission Checking Logic Only
 *
 * Handles role-based permission validation and access control logic.
 * Does NOT handle authentication or user session management.
 */
export class PermissionManager {
  /**
   * Role hierarchy definition (higher roles inherit lower role permissions)
   */
  private static readonly ROLE_HIERARCHY: Record<UserRole, number> = {
    READONLY: 0,
    USER: 1,
    MANAGER: 2,
    ADMIN: 3,
    SUPER_ADMIN: 4,
  };

  /**
   * Permission mappings for each role
   */
  private static readonly ROLE_PERMISSIONS: Record<UserRole, Permission[]> = {
    READONLY: [
      'read',
      'employees:read',
      'vehicles:read',
      'delegations:read',
      'tasks:read',
      'reports:read',
    ],
    USER: [
      'read',
      'employees:read',
      'vehicles:read',
      'delegations:read',
      'tasks:read',
      'reports:read',
      'settings:read',
    ],
    MANAGER: [
      'read',
      'write',
      'employees:read',
      'employees:write',
      'vehicles:read',
      'vehicles:write',
      'delegations:read',
      'delegations:write',
      'tasks:read',
      'tasks:write',
      'reports:read',
      'reports:write',
      'settings:read',
    ],
    ADMIN: [
      'read',
      'write',
      'delete',
      'employees:read',
      'employees:write',
      'employees:delete',
      'vehicles:read',
      'vehicles:write',
      'vehicles:delete',
      'delegations:read',
      'delegations:write',
      'delegations:delete',
      'tasks:read',
      'tasks:write',
      'tasks:delete',
      'reports:read',
      'reports:write',
      'settings:read',
      'settings:write',
    ],
    SUPER_ADMIN: [
      'read',
      'write',
      'delete',
      'admin',
      'employees:read',
      'employees:write',
      'employees:delete',
      'employees:admin',
      'vehicles:read',
      'vehicles:write',
      'vehicles:delete',
      'vehicles:admin',
      'delegations:read',
      'delegations:write',
      'delegations:delete',
      'delegations:admin',
      'tasks:read',
      'tasks:write',
      'tasks:delete',
      'tasks:admin',
      'reports:read',
      'reports:write',
      'reports:admin',
      'settings:read',
      'settings:write',
      'settings:admin',
      'system:admin',
      'system:audit',
      'system:backup',
    ],
  };

  /**
   * Check if user role has specific permission
   * Single responsibility: Permission checking only
   */
  static hasPermission(
    userRole: string,
    requiredPermission: Permission
  ): PermissionCheck {
    // Normalize role
    const normalizedRole = this.normalizeRole(userRole);

    if (!normalizedRole) {
      return {
        hasPermission: false,
        reason: 'Invalid user role',
      };
    }

    // Get permissions for role
    const rolePermissions = this.ROLE_PERMISSIONS[normalizedRole];

    // Check direct permission
    if (rolePermissions.includes(requiredPermission)) {
      return {
        hasPermission: true,
      };
    }

    // Check if higher role has permission through hierarchy
    const hasHierarchicalPermission = this.checkHierarchicalPermission(
      normalizedRole,
      requiredPermission
    );

    if (hasHierarchicalPermission) {
      return {
        hasPermission: true,
      };
    }

    return {
      hasPermission: false,
      reason: `Access denied. Required role: ${normalizedRole || 'Unknown'}`,
      requiredRole: normalizedRole || 'USER',
    };
  }

  /**
   * Check if user role meets minimum role requirement
   * Single responsibility: Role hierarchy checking only
   */
  static hasMinimumRole(userRole: string, minimumRole: UserRole): boolean {
    const normalizedUserRole = this.normalizeRole(userRole);

    if (!normalizedUserRole) {
      return false;
    }

    const userRoleLevel = this.ROLE_HIERARCHY[normalizedUserRole];
    const minimumRoleLevel = this.ROLE_HIERARCHY[minimumRole];

    return userRoleLevel >= minimumRoleLevel;
  }

  /**
   * Get all permissions for a user role
   * Single responsibility: Permission enumeration only
   */
  static getPermissionsForRole(userRole: string): Permission[] {
    const normalizedRole = this.normalizeRole(userRole);

    if (!normalizedRole) {
      return [];
    }

    return [...this.ROLE_PERMISSIONS[normalizedRole]];
  }

  /**
   * Check multiple permissions at once
   * Single responsibility: Batch permission checking only
   */
  static hasAllPermissions(
    userRole: string,
    requiredPermissions: Permission[]
  ): PermissionCheck {
    for (const permission of requiredPermissions) {
      const check = this.hasPermission(userRole, permission);
      if (!check.hasPermission) {
        return {
          hasPermission: false,
          reason: `Missing permission: ${permission}`,
          requiredRole: check.requiredRole || 'USER',
        };
      }
    }

    return {
      hasPermission: true,
    };
  }

  /**
   * Check if user has any of the specified permissions
   * Single responsibility: Alternative permission checking only
   */
  static hasAnyPermission(
    userRole: string,
    requiredPermissions: Permission[]
  ): PermissionCheck {
    for (const permission of requiredPermissions) {
      const check = this.hasPermission(userRole, permission);
      if (check.hasPermission) {
        return {
          hasPermission: true,
        };
      }
    }

    return {
      hasPermission: false,
      reason: `None of the required permissions found: ${requiredPermissions.join(', ')}`,
    };
  }

  /**
   * Normalize role string to valid UserRole
   * Single responsibility: Role normalization only
   */
  private static normalizeRole(role: string): UserRole | null {
    if (!role || typeof role !== 'string') {
      return null;
    }

    const upperRole = role.toUpperCase() as UserRole;

    if (Object.keys(this.ROLE_HIERARCHY).includes(upperRole)) {
      return upperRole;
    }

    return null;
  }

  /**
   * Check hierarchical permissions
   * Single responsibility: Hierarchy-based permission checking only
   */
  private static checkHierarchicalPermission(
    userRole: UserRole,
    permission: Permission
  ): boolean {
    // Check if any higher role has this permission
    const userRoleLevel = this.ROLE_HIERARCHY[userRole];

    for (const [role, level] of Object.entries(this.ROLE_HIERARCHY)) {
      if (level > userRoleLevel) {
        const higherRolePermissions = this.ROLE_PERMISSIONS[role as UserRole];
        if (higherRolePermissions.includes(permission)) {
          return false; // Permission exists in higher role, user doesn't have it
        }
      }
    }

    return false;
  }

  /**
   * Get minimum role required for permission
   * Single responsibility: Minimum role determination only
   */
  private static getMinimumRoleForPermission(
    permission: Permission
  ): UserRole | undefined {
    for (const [role, permissions] of Object.entries(this.ROLE_PERMISSIONS)) {
      if (permissions.includes(permission)) {
        return role as UserRole;
      }
    }
    return undefined;
  }
}
