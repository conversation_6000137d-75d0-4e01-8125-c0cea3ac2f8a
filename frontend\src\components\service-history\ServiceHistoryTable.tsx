'use client';

import type { ColumnDef } from '@tanstack/react-table';
import { Trash } from 'lucide-react';
import React from 'react';

import type { EnrichedServiceRecord } from '@/lib/types/domain';

import {
  DataTable,
  createSelectionColumn,
  createTextColumn,
  createDateColumn,
  createNumericColumn,
  createEnhancedActionsColumn,
  createSortableHeader,
} from '@/components/ui/tables';

/**
 * Props for the ServiceHistoryTable component
 */
interface ServiceHistoryTableProps {
  /** Additional CSS class names */
  className?: string;
  /** Array of service records to display */
  records: EnrichedServiceRecord[];
  /** Whether to show vehicle information column */
  showVehicleInfo?: boolean;
  /** Callback for record deletion */
  onDelete?: (record: EnrichedServiceRecord) => Promise<void>;
  /** Callback for bulk record deletion */
  onBulkDelete?: (records: EnrichedServiceRecord[]) => Promise<void>;
}

/**
 * A component that displays service history records using the standardized DataTable component
 *
 * @example
 * ```tsx
 * <ServiceHistoryTable
 *   records={records}
 *   showVehicleInfo={true}
 *   onDelete={handleDelete}
 * />
 * ```
 */
export function ServiceHistoryTable({
  className = '',
  records,
  showVehicleInfo = true,
  onDelete,
  onBulkDelete,
}: ServiceHistoryTableProps) {
  // Define columns using standardized helpers
  const columns: ColumnDef<EnrichedServiceRecord>[] = [
    // Row selection
    createSelectionColumn<EnrichedServiceRecord>(),

    // Date column
    createDateColumn('date', 'Date', 'MMM dd, yyyy'),

    // Vehicle info column (conditional)
    ...(showVehicleInfo
      ? [
          {
            accessorKey: 'vehicleMake',
            header: createSortableHeader('Vehicle'),
            cell: ({ row }: { row: any }) => {
              const record = row.original;
              return (
                <div className="space-y-1">
                  <div className="font-medium text-sm">
                    {record.vehicleMake} {record.vehicleModel} (
                    {record.vehicleYear})
                  </div>
                  {record.licensePlate && (
                    <div className="text-xs text-muted-foreground font-mono">
                      {record.licensePlate}
                    </div>
                  )}
                </div>
              );
            },
          },
        ]
      : []),

    // Service performed
    {
      accessorKey: 'servicePerformed',
      header: createSortableHeader('Service(s)'),
      cell: ({ row }) => {
        const services = row.getValue('servicePerformed') as string[];
        const servicesText = services.join(', ');
        return (
          <div className="max-w-xs truncate" title={servicesText}>
            {servicesText}
          </div>
        );
      },
    },

    // Odometer reading
    {
      accessorKey: 'odometer',
      header: createSortableHeader('Odometer'),
      cell: ({ row }) => {
        const odometer = row.getValue('odometer') as number;
        return odometer ? odometer.toLocaleString() : '-';
      },
    },

    // Cost
    createNumericColumn('cost', 'Cost', {
      decimals: 2,
      prefix: '$',
    }),

    // Notes
    createTextColumn('notes', 'Notes', {
      maxLength: 50,
      className: 'max-w-xs',
    }),

    // Actions
    createEnhancedActionsColumn({
      viewHref: record => `/service-records/${record.id}`,
      editHref: record => `/service-records/${record.id}/edit`,
      ...(onDelete && {
        onDelete: (record: EnrichedServiceRecord) => {
          onDelete(record);
        },
      }),
      showCopyId: true,
      customActions: [
        {
          label: 'View Vehicle',
          onClick: record => {
            window.location.href = `/vehicles/${record.vehicleId}`;
          },
        },
      ],
    }),
  ];

  // Bulk actions for selected rows
  const bulkActions = [
    ...(onBulkDelete
      ? [
          {
            label: 'Delete Selected',
            icon: ({ className }: { className?: string }) => (
              <Trash className={className} />
            ),
            onClick: async (selectedRecords: EnrichedServiceRecord[]) => {
              await onBulkDelete(selectedRecords);
            },
            variant: 'destructive' as const,
          },
        ]
      : []),
  ];

  return (
    <DataTable
      data={records}
      columns={columns}
      className={className}
      searchPlaceholder="Search service records by service type or notes..."
      searchColumn="servicePerformed"
      emptyMessage="No service records found. Add your first service record to get started."
      pageSize={15}
      // Advanced features
      enableRowSelection={true}
      enableBulkActions={bulkActions.length > 0}
      bulkActions={bulkActions}
      enableColumnVisibility={true}
      // Professional styling with service theme
      tableClassName="shadow-lg"
      headerClassName="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20"
      rowClassName="hover:bg-green-50/50 dark:hover:bg-green-900/10"
    />
  );
}
