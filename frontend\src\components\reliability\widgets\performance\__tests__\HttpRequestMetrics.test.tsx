/**
 * @file Tests for HttpRequestMetrics widget - verifying real API integration
 * @description Tests to ensure HttpRequestMetrics uses real API data instead of mock data
 */

import React from 'react';
import { render } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { HttpRequestMetrics } from '../HttpRequestMetrics';

// Mock the reliability hooks
jest.mock('@/lib/stores/queries/useReliability', () => ({
  useHttpRequestMetrics: jest.fn(),
}));

const { useHttpRequestMetrics } = require('@/lib/stores/queries/useReliability');

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('HttpRequestMetrics Widget - Real API Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should use real API data for HTTP request metrics', () => {
    const mockHttpMetrics = {
      name: 'http_request_duration_seconds',
      help: 'Duration of HTTP requests in seconds',
      type: 'histogram',
      values: [
        {
          labels: {
            method: 'GET',
            route: '/api/health',
            statusCode: '200',
            userRole: 'system',
          },
          value: 0.050, // 50ms
        },
        {
          labels: {
            method: 'POST',
            route: '/api/auth/login',
            statusCode: '200',
            userRole: 'user',
          },
          value: 0.150, // 150ms
        },
        {
          labels: {
            method: 'GET',
            route: '/api/dashboard',
            statusCode: '500',
            userRole: 'admin',
          },
          value: 0.300, // 300ms
        },
        {
          labels: {
            method: 'PUT',
            route: '/api/users/profile',
            statusCode: '200',
            userRole: 'user',
          },
          value: 0.200, // 200ms
        },
      ],
    };

    useHttpRequestMetrics.mockReturnValue({
      data: mockHttpMetrics,
      isLoading: false,
      error: null,
    });

    const { container } = render(
      <TestWrapper>
        <HttpRequestMetrics />
      </TestWrapper>
    );

    // Verify the component renders without errors
    expect(container).toBeTruthy();
    
    // Verify useHttpRequestMetrics was called
    expect(useHttpRequestMetrics).toHaveBeenCalled();
  });

  it('should calculate real metrics from API data', () => {
    const mockHttpMetrics = {
      name: 'http_request_duration_seconds',
      help: 'Duration of HTTP requests in seconds',
      type: 'histogram',
      values: [
        {
          labels: {
            method: 'GET',
            route: '/api/test',
            statusCode: '200',
            userRole: 'user',
          },
          value: 0.100, // 100ms
        },
        {
          labels: {
            method: 'POST',
            route: '/api/test',
            statusCode: '400',
            userRole: 'user',
          },
          value: 0.200, // 200ms
        },
      ],
    };

    useHttpRequestMetrics.mockReturnValue({
      data: mockHttpMetrics,
      isLoading: false,
      error: null,
    });

    render(
      <TestWrapper>
        <HttpRequestMetrics />
      </TestWrapper>
    );

    // The widget should calculate:
    // - Total requests: 2
    // - Average response time: (100 + 200) / 2 = 150ms
    // - Error rate: 1/2 = 50%
    expect(useHttpRequestMetrics).toHaveBeenCalled();
  });

  it('should handle loading state', () => {
    useHttpRequestMetrics.mockReturnValue({
      data: null,
      isLoading: true,
      error: null,
    });

    const { container } = render(
      <TestWrapper>
        <HttpRequestMetrics />
      </TestWrapper>
    );

    expect(container).toBeTruthy();
    expect(useHttpRequestMetrics).toHaveBeenCalled();
  });

  it('should handle error state', () => {
    useHttpRequestMetrics.mockReturnValue({
      data: null,
      isLoading: false,
      error: { message: 'API Error' },
    });

    const { container } = render(
      <TestWrapper>
        <HttpRequestMetrics />
      </TestWrapper>
    );

    expect(container).toBeTruthy();
    expect(useHttpRequestMetrics).toHaveBeenCalled();
  });

  it('should handle empty data gracefully', () => {
    const mockHttpMetrics = {
      name: 'http_request_duration_seconds',
      help: 'Duration of HTTP requests in seconds',
      type: 'histogram',
      values: [],
    };

    useHttpRequestMetrics.mockReturnValue({
      data: mockHttpMetrics,
      isLoading: false,
      error: null,
    });

    const { container } = render(
      <TestWrapper>
        <HttpRequestMetrics />
      </TestWrapper>
    );

    expect(container).toBeTruthy();
    expect(useHttpRequestMetrics).toHaveBeenCalled();
  });

  it('should process endpoint performance correctly', () => {
    const mockHttpMetrics = {
      name: 'http_request_duration_seconds',
      help: 'Duration of HTTP requests in seconds',
      type: 'histogram',
      values: [
        {
          labels: {
            method: 'GET',
            route: '/api/fast-endpoint',
            statusCode: '200',
            userRole: 'user',
          },
          value: 0.050, // 50ms - fast endpoint
        },
        {
          labels: {
            method: 'POST',
            route: '/api/slow-endpoint',
            statusCode: '200',
            userRole: 'admin',
          },
          value: 0.600, // 600ms - slow endpoint
        },
        {
          labels: {
            method: 'GET',
            route: '/api/error-endpoint',
            statusCode: '500',
            userRole: 'user',
          },
          value: 0.100, // 100ms but with error
        },
      ],
    };

    useHttpRequestMetrics.mockReturnValue({
      data: mockHttpMetrics,
      isLoading: false,
      error: null,
    });

    render(
      <TestWrapper>
        <HttpRequestMetrics />
      </TestWrapper>
    );

    // The widget should identify:
    // - /api/fast-endpoint as excellent (50ms, no errors)
    // - /api/slow-endpoint as critical (600ms > 500ms threshold)
    // - /api/error-endpoint as critical (500 status code)
    expect(useHttpRequestMetrics).toHaveBeenCalled();
  });
});
