/**
 * @file WidgetPalette.tsx
 * @description Widget palette component for drag-and-drop report builder
 */

import React from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useDraggable } from '@dnd-kit/core';
import {
  BarChart3,
  Pie<PERSON>hart,
  LineChart,
  Table,
  Users,
  Car,
  CheckSquare,
  FileText,
  TrendingUp,
  Network,
  Calendar,
  DollarSign,
} from 'lucide-react';
import { cn } from '@/lib/utils';

/**
 * Props interface for WidgetPalette
 */
interface WidgetPaletteProps {
  dataSource: string;
  className?: string;
}

/**
 * Widget type definition
 */
interface WidgetType {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  category: string;
  supportedDataSources: string[];
}

/**
 * Draggable widget item component
 */
interface DraggableWidgetProps {
  widget: WidgetType;
  isDisabled?: boolean;
}

const DraggableWidget: React.FC<DraggableWidgetProps> = ({
  widget,
  isDisabled = false,
}) => {
  const { attributes, listeners, setNodeRef, transform, isDragging } =
    useDraggable({
      id: widget.id,
      data: {
        type: 'widget-type',
        widgetType: widget.id,
      },
      disabled: isDisabled,
    });

  const style = transform
    ? {
        transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,
      }
    : undefined;

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...listeners}
      {...attributes}
      className={cn(
        'p-3 border rounded-lg cursor-grab active:cursor-grabbing transition-all',
        isDragging && 'opacity-50',
        isDisabled
          ? 'opacity-50 cursor-not-allowed'
          : 'hover:shadow-md hover:border-blue-300',
        !isDisabled && 'bg-white'
      )}
    >
      <div className="flex items-center gap-2 mb-2">
        {widget.icon}
        <span className="text-sm font-medium">{widget.name}</span>
      </div>
      <p className="text-xs text-gray-600 mb-2">{widget.description}</p>
      <Badge variant="outline" className="text-xs">
        {widget.category}
      </Badge>
    </div>
  );
};

/**
 * Available widget types
 */
const WIDGET_TYPES: WidgetType[] = [
  // Analytics Widgets
  {
    id: 'analytics',
    name: 'Analytics Widget',
    description: 'Key metrics and performance indicators',
    icon: <TrendingUp className="h-4 w-4" />,
    category: 'Analytics',
    supportedDataSources: [
      'delegations',
      'tasks',
      'vehicles',
      'employees',
      'cross-entity',
    ],
  },
  {
    id: 'metrics',
    name: 'Metrics Widget',
    description: 'Display key performance metrics',
    icon: <BarChart3 className="h-4 w-4" />,
    category: 'Analytics',
    supportedDataSources: ['delegations', 'tasks', 'vehicles', 'employees'],
  },

  // Chart Widgets
  {
    id: 'bar-chart',
    name: 'Bar Chart',
    description: 'Compare values across categories',
    icon: <BarChart3 className="h-4 w-4" />,
    category: 'Charts',
    supportedDataSources: ['delegations', 'tasks', 'vehicles', 'employees'],
  },
  {
    id: 'pie-chart',
    name: 'Pie Chart',
    description: 'Show proportional data distribution',
    icon: <PieChart className="h-4 w-4" />,
    category: 'Charts',
    supportedDataSources: ['delegations', 'tasks', 'vehicles', 'employees'],
  },
  {
    id: 'line-chart',
    name: 'Line Chart',
    description: 'Display trends over time',
    icon: <LineChart className="h-4 w-4" />,
    category: 'Charts',
    supportedDataSources: ['delegations', 'tasks', 'vehicles', 'employees'],
  },

  // Data Widgets
  {
    id: 'data-table',
    name: 'Data Table',
    description: 'Tabular data with sorting and filtering',
    icon: <Table className="h-4 w-4" />,
    category: 'Data',
    supportedDataSources: ['delegations', 'tasks', 'vehicles', 'employees'],
  },

  // Entity-Specific Widgets
  {
    id: 'employee-performance',
    name: 'Employee Performance',
    description: 'Employee performance metrics and charts',
    icon: <Users className="h-4 w-4" />,
    category: 'Employee',
    supportedDataSources: ['employees', 'cross-entity'],
  },
  {
    id: 'vehicle-utilization',
    name: 'Vehicle Utilization',
    description: 'Vehicle usage and utilization metrics',
    icon: <Car className="h-4 w-4" />,
    category: 'Vehicle',
    supportedDataSources: ['vehicles', 'cross-entity'],
  },
  {
    id: 'task-status',
    name: 'Task Status',
    description: 'Task completion and status tracking',
    icon: <CheckSquare className="h-4 w-4" />,
    category: 'Task',
    supportedDataSources: ['tasks', 'cross-entity'],
  },
  {
    id: 'delegation-overview',
    name: 'Delegation Overview',
    description: 'Delegation status and distribution',
    icon: <FileText className="h-4 w-4" />,
    category: 'Delegation',
    supportedDataSources: ['delegations', 'cross-entity'],
  },

  // Specialized Widgets
  {
    id: 'correlation',
    name: 'Correlation Analysis',
    description: 'Cross-entity relationships and correlations',
    icon: <Network className="h-4 w-4" />,
    category: 'Analysis',
    supportedDataSources: ['cross-entity'],
  },
  {
    id: 'timeline',
    name: 'Timeline Widget',
    description: 'Events and activities over time',
    icon: <Calendar className="h-4 w-4" />,
    category: 'Timeline',
    supportedDataSources: ['delegations', 'tasks', 'vehicles', 'employees'],
  },
  {
    id: 'cost-analysis',
    name: 'Cost Analysis',
    description: 'Financial metrics and cost tracking',
    icon: <DollarSign className="h-4 w-4" />,
    category: 'Financial',
    supportedDataSources: ['vehicles', 'employees', 'cross-entity'],
  },
];

/**
 * WidgetPalette Component
 *
 * Displays available widgets for drag-and-drop into report builder.
 *
 * Responsibilities:
 * - Display available widget types
 * - Filter widgets by data source compatibility
 * - Provide drag-and-drop functionality
 * - Group widgets by category
 *
 * SOLID Principles Applied:
 * - SRP: Single responsibility of displaying widget palette
 * - OCP: Open for extension via new widget types
 * - DIP: Depends on drag-and-drop framework abstractions
 */
export const WidgetPalette: React.FC<WidgetPaletteProps> = ({
  dataSource,
  className = '',
}) => {
  // Filter widgets by data source compatibility
  const availableWidgets = WIDGET_TYPES.filter(
    widget =>
      widget.supportedDataSources.includes(dataSource) ||
      widget.supportedDataSources.includes('cross-entity')
  );

  // Group widgets by category
  const widgetsByCategory = availableWidgets.reduce(
    (acc, widget) => {
      if (!acc[widget.category]) {
        acc[widget.category] = [];
      }
      acc[widget.category]!.push(widget);
      return acc;
    },
    {} as Record<string, WidgetType[]>
  );

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <CardTitle className="text-lg">Widget Palette</CardTitle>
        <p className="text-sm text-gray-600">
          Drag widgets to the report canvas to build your custom report
        </p>
        <Badge variant="secondary" className="text-xs w-fit">
          {availableWidgets.length} widgets available
        </Badge>
      </CardHeader>

      <CardContent className="space-y-4">
        {Object.entries(widgetsByCategory).map(([category, widgets]) => (
          <div key={category}>
            <h4 className="text-sm font-medium mb-2 text-gray-700">
              {category}
            </h4>
            <div className="space-y-2">
              {widgets.map(widget => (
                <DraggableWidget
                  key={widget.id}
                  widget={widget}
                  isDisabled={
                    !widget.supportedDataSources.includes(dataSource) &&
                    !widget.supportedDataSources.includes('cross-entity')
                  }
                />
              ))}
            </div>
          </div>
        ))}

        {availableWidgets.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            <div className="text-sm">
              No widgets available for this data source
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default WidgetPalette;
