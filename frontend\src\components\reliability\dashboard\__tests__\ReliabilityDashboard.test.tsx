/**
 * @file Unit tests for ReliabilityDashboard component
 * @module components/reliability/dashboard/__tests__/ReliabilityDashboard
 */

import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import React from 'react';

import { ReliabilityDashboard } from '../ReliabilityDashboard';

// Mock the reliability store
const mockReliabilityStore = {
  monitoring: {
    isEnabled: true,
    connectionStatus: 'connected' as const,
  },
  ui: {
    activeTab: 'overview' as const,
  },
  setActiveTab: jest.fn(),
};

jest.mock('@/lib/hooks', () => ({
  useReliabilityStore: (selector: any) => selector(mockReliabilityStore),
  useReliabilityWebSocketManager: jest.fn(),
}));

// Mock the reliability queries
const mockReliabilityQueries = {
  systemHealth: { data: { status: 'healthy' }, isLoading: false, error: null },
  circuitBreakers: {
    data: { summary: { total: 5, closed: 5 } },
    isLoading: false,
    error: null,
  },
  metrics: { data: { cpu: 45, memory: 60 }, isLoading: false, error: null },
  alerts: { data: [], isLoading: false, error: null },
  alertsStatistics: { data: { total: 0 }, isLoading: false, error: null },
  isLoading: false,
  error: null,
};

jest.mock('@/lib/stores/queries/useReliability', () => ({
  useReliability: () => mockReliabilityQueries,
}));

// Mock child components
jest.mock('../DashboardHeader', () => ({
  DashboardHeader: () => (
    <div data-testid="dashboard-header">Dashboard Header</div>
  ),
}));

jest.mock('../DashboardGrid', () => ({
  DashboardGrid: () => <div data-testid="dashboard-grid">Dashboard Grid</div>,
}));

jest.mock('../DashboardSettings', () => ({
  DashboardSettings: () => (
    <div data-testid="dashboard-settings">Dashboard Settings</div>
  ),
}));

// Mock error boundary
jest.mock('@/components/error-boundaries/ErrorBoundary', () => ({
  __esModule: true,
  default: ({ children }: { children: React.ReactNode }) => (
    <div>{children}</div>
  ),
}));

describe('ReliabilityDashboard', () => {
  let queryClient: QueryClient;

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    });
    jest.clearAllMocks();
  });

  const renderWithProviders = (component: React.ReactElement) => {
    return render(
      <QueryClientProvider client={queryClient}>
        {component}
      </QueryClientProvider>
    );
  };

  describe('Rendering', () => {
    it('should render the dashboard when monitoring is enabled', () => {
      renderWithProviders(<ReliabilityDashboard />);

      expect(screen.getByTestId('dashboard-header')).toBeInTheDocument();
      expect(screen.getByTestId('dashboard-grid')).toBeInTheDocument();
      expect(screen.getByRole('main')).toHaveAttribute(
        'aria-label',
        'Reliability monitoring dashboard'
      );
    });

    it('should render with custom className', () => {
      renderWithProviders(<ReliabilityDashboard className="custom-class" />);

      const mainElement = screen.getByRole('main');
      expect(mainElement).toHaveClass('custom-class');
    });

    it('should show settings when showSettings prop is true', () => {
      renderWithProviders(<ReliabilityDashboard showSettings />);

      expect(screen.getByTestId('dashboard-settings')).toBeInTheDocument();
    });
  });

  describe('Monitoring Disabled State', () => {
    beforeEach(() => {
      mockReliabilityStore.monitoring.isEnabled = false;
    });

    afterEach(() => {
      mockReliabilityStore.monitoring.isEnabled = true;
    });

    it('should show monitoring disabled message when monitoring is disabled', () => {
      renderWithProviders(<ReliabilityDashboard />);

      expect(screen.getByText('Monitoring Disabled')).toBeInTheDocument();
      expect(
        screen.getByText(/Reliability monitoring is currently disabled/)
      ).toBeInTheDocument();
      expect(screen.getByTestId('dashboard-settings')).toBeInTheDocument();
    });
  });

  describe('Loading State', () => {
    beforeEach(() => {
      mockReliabilityQueries.isLoading = true;
      mockReliabilityQueries.systemHealth.data = null as any;
      mockReliabilityQueries.circuitBreakers.data = null as any;
    });

    afterEach(() => {
      mockReliabilityQueries.isLoading = false;
      mockReliabilityQueries.systemHealth.data = { status: 'healthy' };
      mockReliabilityQueries.circuitBreakers.data = {
        summary: { total: 5, closed: 5 },
      };
    });

    it('should show loading state when data is loading', () => {
      renderWithProviders(<ReliabilityDashboard />);

      expect(
        screen.getByText('Loading reliability dashboard...')
      ).toBeInTheDocument();
      expect(screen.getByTestId('dashboard-header')).toBeInTheDocument();
    });
  });

  describe('Error State', () => {
    beforeEach(() => {
      mockReliabilityQueries.error = new Error('Failed to load data') as any;
      mockReliabilityQueries.systemHealth.data = null as any;
      mockReliabilityQueries.circuitBreakers.data = null as any;
    });

    afterEach(() => {
      mockReliabilityQueries.error = null;
      mockReliabilityQueries.systemHealth.data = { status: 'healthy' };
      mockReliabilityQueries.circuitBreakers.data = {
        summary: { total: 5, closed: 5 },
      };
    });

    it('should show error state when data loading fails', () => {
      renderWithProviders(<ReliabilityDashboard />);

      expect(screen.getByText('Dashboard Error')).toBeInTheDocument();
      expect(
        screen.getByText(/Failed to load reliability dashboard data/)
      ).toBeInTheDocument();
    });

    it('should show error details when available', () => {
      renderWithProviders(<ReliabilityDashboard />);

      const detailsElement = screen.getByText('Error details');
      expect(detailsElement).toBeInTheDocument();
    });
  });

  describe('Connection Status', () => {
    it('should show connection warning when disconnected', () => {
      mockReliabilityStore.monitoring.connectionStatus = 'disconnected' as any;

      renderWithProviders(<ReliabilityDashboard />);

      expect(screen.getByText('Real-time Connection Lost')).toBeInTheDocument();
      expect(
        screen.getByText(
          /The real-time connection to the monitoring system has been lost/
        )
      ).toBeInTheDocument();
    });

    it('should not show connection warning when connected', () => {
      mockReliabilityStore.monitoring.connectionStatus = 'connected';

      renderWithProviders(<ReliabilityDashboard />);

      expect(
        screen.queryByText('Real-time Connection Lost')
      ).not.toBeInTheDocument();
    });
  });

  describe('Tab Management', () => {
    it('should set initial tab if not set', async () => {
      mockReliabilityStore.ui.activeTab = null as any;

      renderWithProviders(<ReliabilityDashboard />);

      await waitFor(() => {
        expect(mockReliabilityStore.setActiveTab).toHaveBeenCalledWith(
          'overview'
        );
      });
    });

    it('should not set initial tab if already set', () => {
      mockReliabilityStore.ui.activeTab = 'health' as any;

      renderWithProviders(<ReliabilityDashboard />);

      expect(mockReliabilityStore.setActiveTab).not.toHaveBeenCalled();
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA labels', () => {
      renderWithProviders(<ReliabilityDashboard />);

      const mainElement = screen.getByRole('main');
      expect(mainElement).toHaveAttribute(
        'aria-label',
        'Reliability monitoring dashboard'
      );
    });

    it('should be keyboard navigable', async () => {
      const user = userEvent.setup();
      renderWithProviders(<ReliabilityDashboard />);

      // Tab through the component
      await user.tab();

      // Should be able to navigate through the dashboard
      expect(document.activeElement).toBeInTheDocument();
    });
  });

  describe('Integration', () => {
    it('should initialize WebSocket manager', () => {
      const useReliabilityWebSocketManager =
        require('@/lib/hooks').useReliabilityWebSocketManager;

      renderWithProviders(<ReliabilityDashboard />);

      expect(useReliabilityWebSocketManager).toHaveBeenCalled();
    });

    it('should render all child components', () => {
      renderWithProviders(<ReliabilityDashboard />);

      expect(screen.getByTestId('dashboard-header')).toBeInTheDocument();
      expect(screen.getByTestId('dashboard-grid')).toBeInTheDocument();
    });
  });
});
