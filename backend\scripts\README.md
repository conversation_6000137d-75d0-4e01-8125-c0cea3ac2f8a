# Backend Scripts Directory

This directory contains various utility scripts for managing the WorkHub backend
application.

## Directory Structure

### 📁 `/auth/` - Authentication & Authorization Scripts

Scripts related to Supabase authentication, RBAC (Role-Based Access Control),
and JWT token management. **Status:** Active - Contains current verification and
testing scripts

### 📁 `/migration/` - Database Migration Scripts

Scripts for migrating data, user roles, and database schema changes. **Status:**
Mostly archived - Major migrations completed

### 📁 `/testing/` - Testing & Debugging Scripts

Scripts for testing various components, debugging issues, and validating system
functionality. **Status:** Active - Used for ongoing testing and validation

### 📁 `/utilities/` - General Utility Scripts

General-purpose scripts for secrets management, database switching, and CLI
operations. **Status:** Active - Used for environment management

### 📁 `/archive/` - Completed & Historical Scripts

- `/completed-migrations/` - Completed migration scripts (archived to prevent
  re-execution)
- `/completed-fixes/` - Completed emergency fixes and patches **Status:**
  Archived - Historical reference only

## Usage Guidelines

### Prerequisites

- Ensure all environment variables are properly configured
- Backend should be built (`npm run build`) before running most scripts
- Some scripts require Supabase service role key for admin operations

### Running Scripts

```bash
# From the backend directory
cd backend

# For Node.js scripts
node scripts/[category]/[script-name].js

# For SQL scripts (run via Supabase CLI or database client)
psql -f scripts/[category]/[script-name].sql
```

### Safety Notes

⚠️ **IMPORTANT**: Many scripts in this directory perform database operations
that can modify or delete data. Always:

1. **Backup your database** before running migration or cleanup scripts
2. **Test in development environment** first
3. **Review script contents** before execution
4. **Check environment variables** to ensure you're targeting the correct
   database

## Script Categories

### Authentication Scripts (`/auth/`)

- User role management and verification
- JWT token validation and debugging
- RLS (Row Level Security) policy testing
- Auth hook setup and verification

### Migration Scripts (`/migration/`)

- User role data migration from metadata to user_profiles table
- Database schema migrations
- System cleanup and optimization

### Testing Scripts (`/testing/`)

- WebSocket and real-time functionality testing
- User authentication flow testing
- System integration testing

### Utility Scripts (`/utilities/`)

- Environment and database configuration
- Secrets generation and management
- CLI helper scripts

## Maintenance

### Regular Cleanup

- Review and remove outdated scripts quarterly
- Update documentation when adding new scripts
- Archive completed migration scripts to prevent accidental re-execution

### Adding New Scripts

1. Place in appropriate category directory
2. Add clear documentation header in script
3. Update this README if adding new categories
4. Test thoroughly in development environment

## Support

For questions about specific scripts, refer to the individual script
documentation or contact the development team.
