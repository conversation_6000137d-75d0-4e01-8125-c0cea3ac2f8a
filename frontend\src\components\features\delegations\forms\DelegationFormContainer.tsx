/**
 * DelegationFormContainer Component - Enhanced SOLID Principles Implementation
 *
 * Single Responsibility: Orchestrates delegation form sections and submission
 * - Uses useFormSubmission for modern form handling
 * - Composes section components following SRP
 * - Handles data transformation and validation
 * - Enhanced UX with better clarity and streamlined process
 *
 * @module DelegationFormContainer
 */

import { zodResolver } from '@hookform/resolvers/zod';
import {
  AlertCircle,
  ArrowLeft,
  Calendar,
  CheckCircle,
  Info,
  MapPin,
  Save,
  Users,
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import React, { useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';

import type { DelegationFormData } from '@/lib/schemas/delegationSchemas';
import type { Delegation } from '@/lib/types/domain';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  <PERSON>,
  Card<PERSON>ontent,
  <PERSON><PERSON>ooter,
  <PERSON><PERSON>eader,
  <PERSON>Title,
} from '@/components/ui/card';
// Import validation components
import { DelegationValidationSummary } from '@/components/ui/forms/DelegationValidationSummary';
import { Progress } from '@/components/ui/progress';

import { useFormSubmission } from '@/hooks/forms/useFormSubmission';
import { DelegationFormSchema } from '@/lib/schemas/delegationSchemas';

// Import section components
import {
  DelegationAssignmentSection,
  DelegationBasicInfoSection,
  DelegationDelegatesSection,
  DelegationFlightSection,
  DelegationNotesSection,
} from './sections';
// Import services
import { delegationFormService } from './services/DelegationFormService';

// ============================================================================
// INTERFACES
// ============================================================================

export interface DelegationFormContainerProps {
  /** Available employees for assignment */
  employees?: { id: number; name: string; role: string }[];
  /** Initial data for editing existing delegation */
  initialData?: Delegation | DelegationFormData;
  /** Whether this is an edit operation */
  isEditing?: boolean;
  /** Form submission handler */
  onSubmit: (data: DelegationFormData) => Promise<void>;
  /** User's role for permission-based UI */
  userRole?: string;
  /** Available vehicles for assignment */
  vehicles?: { id: number; name: string; type: string }[];
}

// ============================================================================
// FORM PROGRESS TRACKING
// ============================================================================

interface FormSection {
  icon: React.ReactNode;
  id: string;
  isComplete: (data: Partial<DelegationFormData>) => boolean;
  name: string;
  required: boolean;
}

const formSections: FormSection[] = [
  {
    icon: <Calendar className="size-4" />,
    id: 'basic',
    isComplete: data =>
      !!(
        data.eventName &&
        data.location &&
        data.durationFrom &&
        data.durationTo
      ),
    name: 'Event Details',
    required: true,
  },
  {
    icon: <Users className="size-4" />,
    id: 'delegates',
    isComplete: data =>
      !!(
        data.delegates &&
        data.delegates.length > 0 &&
        data.delegates.every(d => d.name && d.title)
      ),
    name: 'Delegates',
    required: true,
  },
  {
    icon: <Users className="size-4" />,
    id: 'assignment',
    isComplete: data =>
      !!(
        (data.driverEmployeeIds && data.driverEmployeeIds.length > 0) ||
        (data.escortEmployeeIds && data.escortEmployeeIds.length > 0)
      ),
    name: 'Team Assignment',
    required: false,
  },
  {
    icon: <MapPin className="size-4" />,
    id: 'logistics',
    isComplete: data =>
      !!(
        (data.vehicleIds && data.vehicleIds.length > 0) ||
        data.flightArrivalDetails?.flightNumber ||
        data.flightDepartureDetails?.flightNumber
      ),
    name: 'Travel & Logistics',
    required: false,
  },
  {
    icon: <Info className="size-4" />,
    id: 'notes',
    isComplete: data =>
      !!(
        data.notes?.trim() ||
        data.invitationFrom?.trim() ||
        data.invitationTo?.trim() ||
        data.imageUrl?.trim()
      ),
    name: 'Additional Information',
    required: false,
  },
];

// ============================================================================
// HELPER FUNCTIONS
// ============================================================================

/**
 * Transforms initial data to form format with better defaults
 */
const transformInitialData = (
  initialData?: Delegation | DelegationFormData
): DelegationFormData => {
  if (!initialData) {
    return {
      delegates: [{ name: '', notes: '', title: '' }],
      driverEmployeeIds: [],
      durationFrom: '',
      durationTo: '',
      escortEmployeeIds: [],
      eventName: '',
      flightArrivalDetails: null,
      flightDepartureDetails: null,
      imageUrl: '',
      invitationFrom: '',
      invitationTo: '',
      location: '',
      notes: '',
      status: 'Planned',
      vehicleIds: [],
    };
  }

  // Return form data as-is - the parent component should handle transformation
  return initialData as DelegationFormData;
};

/**
 * Calculate form completion percentage
 */
const calculateFormProgress = (data: Partial<DelegationFormData>): number => {
  const completedSections = formSections.filter(section =>
    section.isComplete(data)
  );
  const requiredSections = formSections.filter(section => section.required);
  const requiredCompleted = requiredSections.filter(section =>
    section.isComplete(data)
  );

  // Base progress on required sections, with bonus for optional sections
  const requiredProgress =
    (requiredCompleted.length / requiredSections.length) * 70;
  const optionalProgress =
    ((completedSections.length - requiredCompleted.length) /
      (formSections.length - requiredSections.length)) *
    30;

  const totalProgress = Math.round(requiredProgress + optionalProgress);

  console.log('Progress Calculation:', {
    optionalProgress,
    requiredProgress,
    totalProgress,
  });

  return totalProgress;
};

/**
 * Get validation status for better user feedback
 */
const getValidationStatus = (
  errors: any,
  isDirty: boolean
): 'idle' | 'invalid' | 'valid' | 'validating' => {
  if (!isDirty) return 'idle';
  const hasErrors = Object.keys(errors).length > 0;
  return hasErrors ? 'invalid' : 'valid';
};

// ============================================================================
// COMPONENT IMPLEMENTATION
// ============================================================================

/**
 * Enhanced Delegation Form Container - Modern SOLID Architecture
 *
 * Provides improved user experience with:
 * - Clear visual progress indicators
 * - Better error handling and validation feedback
 * - Streamlined workflow with logical section organization
 * - Enhanced accessibility and form state management
 */
export const DelegationFormContainer: React.FC<
  DelegationFormContainerProps
> = ({
  employees = [],
  initialData,
  isEditing = false,
  onSubmit,
  userRole = 'user',
  vehicles = [],
}) => {
  const router = useRouter();
  const [showValidationSummary, setShowValidationSummary] = useState(false);

  // Transform initial data
  const defaultValues = transformInitialData(initialData);

  // Initialize form with React Hook Form
  const form = useForm<DelegationFormData>({
    defaultValues,
    mode: 'onChange',
    resolver: zodResolver(DelegationFormSchema),
  });

  const {
    formState: { errors, isDirty, isValid },
    watch,
  } = form;
  const watchedValues = watch();

  // Note: Enhanced validation is handled within individual form sections

  // Calculate form progress
  const formProgress = calculateFormProgress(watchedValues);
  const validationStatus = getValidationStatus(errors, isDirty);

  // Enhanced form submission with useFormSubmission
  const {
    ariaAttributes,
    clearError,
    error,
    handleSubmit,
    isLoading,
    retry,
    state,
  } = useFormSubmission(
    async (data: DelegationFormData) => {
      // Validate form data using React Hook Form
      const isFormValid = await form.trigger();

      if (!isFormValid) {
        setShowValidationSummary(true);
        const errors = form.formState.errors;
        const firstErrorField = Object.keys(errors)[0];
        if (firstErrorField) {
          form.setFocus(firstErrorField as any);
        }
        throw new Error('Please complete all required fields correctly');
      }

      // Use service for additional business logic validation
      const validationResult = delegationFormService.validateFormData(data);

      if (!validationResult.isValid) {
        throw new Error(validationResult.errors.map(e => e.message).join(', '));
      }

      // Submit the cleaned data
      await onSubmit(data);
    },
    {
      accessibility: {
        announceStatus: true,
        focusManagement: 'first-error',
      },
      formFocus: field => form.setFocus(field as any),
      formReset: form.reset,
      formValidate: () => form.trigger(),
      performance: {
        debounceMs: 500,
        timeoutMs: 45_000,
      },
      preSubmitValidation: async (_data: DelegationFormData) => {
        const isValid = await form.trigger();
        if (!isValid) {
          setShowValidationSummary(true);
          const errors = form.formState.errors;
          const firstErrorField = Object.keys(errors)[0];
          if (firstErrorField) {
            form.setFocus(firstErrorField as any);
          }
        }
        return isValid;
      },
      retry: {
        exponentialBackoff: true,
        maxAttempts: 3,
        retryCondition: error => {
          return (
            error.message.includes('network') ||
            error.message.includes('timeout') ||
            error.message.includes('fetch')
          );
        },
      },
      toast: {
        entity: (data: DelegationFormData) => ({
          eventName: data.eventName,
          location: data.location,
        }),
        entityType: 'delegation',
      },
      transformData: (data: DelegationFormData) => {
        // Clean up form data
        return {
          ...data,
          delegates:
            data.delegates?.filter(d => d.name?.trim() && d.title?.trim()) ||
            [],
          driverEmployeeIds:
            data.driverEmployeeIds?.filter(id => !isNaN(id) && id > 0) || [],
          escortEmployeeIds:
            data.escortEmployeeIds?.filter(id => !isNaN(id) && id > 0) || [],
          vehicleIds: data.vehicleIds?.filter(id => !isNaN(id) && id > 0) || [],
        };
      },
    }
  );

  // Enhanced form submission handler
  const onFormSubmit = (data: DelegationFormData) => {
    if (state === 'error') {
      clearError();
    }

    // Clean data before submission
    const cleanedData = {
      ...data,
      delegates:
        data.delegates?.filter(d => d.name?.trim() && d.title?.trim()) || [],
      driverEmployeeIds:
        data.driverEmployeeIds?.filter(id => !isNaN(id) && id > 0) || [],
      escortEmployeeIds:
        data.escortEmployeeIds?.filter(id => !isNaN(id) && id > 0) || [],
      vehicleIds: data.vehicleIds?.filter(id => !isNaN(id) && id > 0) || [],
    };

    handleSubmit(cleanedData);
  };

  return (
    <FormProvider {...form}>
      <form onSubmit={form.handleSubmit(onFormSubmit)} {...ariaAttributes}>
        <style jsx>{`
          .field-valid input,
          .field-valid select,
          .field-valid textarea {
            border-color: rgb(34, 197, 94);
            box-shadow: 0 0 0 1px rgba(34, 197, 94, 0.1);
          }

          .field-invalid input,
          .field-invalid select,
          .field-invalid textarea {
            border-color: rgb(239, 68, 68);
            box-shadow: 0 0 0 1px rgba(239, 68, 68, 0.1);
          }

          .field-pending input,
          .field-pending select,
          .field-pending textarea {
            border-color: rgb(59, 130, 246);
            box-shadow: 0 0 0 1px rgba(59, 130, 246, 0.1);
          }
        `}</style>
        <div className="space-y-6">
          {/* Form Header with Progress */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Calendar className="size-5 text-accent" />
                    {isEditing ? 'Edit Delegation' : 'Create New Delegation'}
                  </CardTitle>
                  <p className="mt-1 text-sm text-muted-foreground">
                    {isEditing
                      ? 'Update delegation details and assignments'
                      : 'Create a new delegation with responsibilities and team assignments'}
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  <Badge
                    variant={
                      validationStatus === 'valid'
                        ? 'default'
                        : validationStatus === 'invalid'
                          ? 'destructive'
                          : validationStatus === 'validating'
                            ? 'secondary'
                            : 'outline'
                    }
                  >
                    {validationStatus === 'valid' && (
                      <CheckCircle className="mr-1 size-3" />
                    )}
                    {validationStatus === 'invalid' && (
                      <AlertCircle className="mr-1 size-3" />
                    )}
                    {validationStatus === 'validating' && (
                      <Info className="mr-1 size-3 animate-pulse" />
                    )}
                    {validationStatus === 'idle' && 'Not Started'}
                    {validationStatus === 'valid' && 'All Valid'}
                    {validationStatus === 'invalid' && 'Has Errors'}
                    {validationStatus === 'validating' && 'Validating...'}
                  </Badge>

                  {/* Real-time error count */}
                  {Object.keys(errors).length > 0 && (
                    <Badge className="text-xs" variant="destructive">
                      {Object.keys(errors).length} error
                      {Object.keys(errors).length > 1 ? 's' : ''}
                    </Badge>
                  )}
                </div>
              </div>

              {/* Progress Bar */}
              <div className="mt-4">
                <div className="mb-2 flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Form Progress</span>
                  <span className="font-medium">{formProgress}% Complete</span>
                </div>
                <Progress className="h-2" value={formProgress} />
              </div>

              {/* Section Status Indicators */}
              <div className="mt-4 grid grid-cols-2 gap-2 md:grid-cols-5">
                {formSections.map(section => {
                  const isComplete = section.isComplete(watchedValues);
                  return (
                    <div
                      className={`flex items-center gap-2 rounded-lg border p-2 transition-colors ${
                        isComplete
                          ? 'border-green-200 bg-green-50 text-green-700 dark:border-green-800 dark:bg-green-900/20 dark:text-green-400'
                          : section.required
                            ? 'border-amber-200 bg-amber-50 text-amber-700 dark:border-amber-800 dark:bg-amber-900/20 dark:text-amber-400'
                            : 'border-border bg-muted text-muted-foreground'
                      }`}
                      key={section.id}
                    >
                      {section.icon}
                      <span className="text-xs font-medium">
                        {section.name}
                      </span>
                      {isComplete && <CheckCircle className="ml-auto size-3" />}
                      {!isComplete && section.required && (
                        <AlertCircle className="ml-auto size-3" />
                      )}
                    </div>
                  );
                })}
              </div>
            </CardHeader>
          </Card>

          {/* Enhanced Validation Summary */}
          <DelegationValidationSummary
            onDismiss={() => setShowValidationSummary(false)}
            show={showValidationSummary}
            showFieldNavigation={true}
            showSuccess={false}
            showWarnings={true}
          />

          {/* Form Sections */}
          <Card className="shadow-lg">
            <CardContent className="space-y-8 pt-6">
              {/* Basic Information Section */}
              <DelegationBasicInfoSection
                isSubmitting={isLoading}
                userRole={userRole}
              />

              {/* Delegates Section */}
              <DelegationDelegatesSection
                isSubmitting={isLoading}
                userRole={userRole}
              />

              {/* Assignment Section - Drivers, Escorts, Vehicles */}
              <DelegationAssignmentSection
                employees={employees}
                isSubmitting={isLoading}
                userRole={userRole}
                vehicles={vehicles}
              />

              {/* Flight Details Section */}
              <DelegationFlightSection
                isSubmitting={isLoading}
                userRole={userRole}
              />

              {/* Notes Section */}
              <DelegationNotesSection
                isSubmitting={isLoading}
                userRole={userRole}
              />

              {/* Error Display */}
              {error && (
                <Alert variant="destructive">
                  <AlertCircle className="size-4" />
                  <AlertDescription>
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="font-medium">Submission Error</div>
                        <div className="mt-1 text-sm">{error}</div>
                      </div>
                      {state === 'error' && (
                        <Button
                          onClick={retry}
                          size="sm"
                          type="button"
                          variant="outline"
                        >
                          Retry
                        </Button>
                      )}
                    </div>
                  </AlertDescription>
                </Alert>
              )}

              {/* State Indicators */}
              {state === 'validating' && (
                <Alert>
                  <Info className="size-4" />
                  <AlertDescription>Validating form data...</AlertDescription>
                </Alert>
              )}

              {state === 'retrying' && (
                <Alert>
                  <Info className="size-4" />
                  <AlertDescription>Retrying submission...</AlertDescription>
                </Alert>
              )}
            </CardContent>

            <CardFooter className="flex justify-between gap-4 border-t pt-6">
              <div className="flex gap-2">
                <Button
                  className="min-w-[100px]"
                  disabled={isLoading}
                  onClick={() => router.back()}
                  type="button"
                  variant="outline"
                >
                  <ArrowLeft className="mr-2 size-4" />
                  Cancel
                </Button>

                {process.env.NODE_ENV === 'development' && (
                  <Button
                    disabled={isLoading}
                    onClick={() =>
                      setShowValidationSummary(!showValidationSummary)
                    }
                    size="sm"
                    type="button"
                    variant="secondary"
                  >
                    🔍 Debug
                  </Button>
                )}
              </div>

              <div className="flex items-center gap-4">
                {formProgress < 100 && (
                  <div className="text-sm text-muted-foreground">
                    Complete required sections to enable submission
                  </div>
                )}

                <Button
                  className="min-w-[140px] bg-accent text-accent-foreground hover:bg-accent/90"
                  disabled={isLoading || formProgress < 35 || !isValid} // Lower threshold - just need basic info mostly complete
                  type="submit"
                >
                  <Save className="mr-2 size-4" />
                  {isLoading
                    ? 'Saving...'
                    : isEditing
                      ? 'Save Changes'
                      : 'Create Delegation'}
                </Button>
              </div>
            </CardFooter>
          </Card>
        </div>
      </form>
    </FormProvider>
  );
};

export default DelegationFormContainer;
