/**
 * @file Delegation dashboard component using generic dashboard framework
 * @module components/delegations/dashboard/DelegationDashboard
 */

'use client';

import React from 'react';
import { Briefcase, Calendar, CheckCircle, Clock, Users } from 'lucide-react';

import { DashboardPage } from '@/components/dashboard/DashboardLayout';
import type { DashboardConfig, TabConfig } from '@/components/dashboard/types';
import type { Delegation } from '@/lib/types/domain';
import { useDashboardStore } from '@/hooks/domain/useDashboardStore';
import { useDelegations } from '@/lib/stores/queries/useDelegations';

/**
 * Delegation dashboard configuration
 */
const delegationDashboardConfig: DashboardConfig<Delegation> = {
  entityType: 'delegation',
  title: 'Manage Delegations',
  description:
    'Track and manage all your events, trips, and delegate information.',
  viewModes: ['cards', 'table', 'calendar'],
  defaultViewMode: 'cards',
  tabs: [
    {
      id: 'all',
      label: 'All Delegations',
      icon: <Briefcase className="size-4" />,
    },
    {
      id: 'upcoming',
      label: 'Upcoming',
      icon: <Clock className="size-4" />,
    },
    {
      id: 'in-progress',
      label: 'In Progress',
      icon: <Users className="size-4" />,
    },
    {
      id: 'completed',
      label: 'Completed',
      icon: <CheckCircle className="size-4" />,
    },
  ] as TabConfig[],
  filters: [
    {
      id: 'status',
      label: 'Status',
      type: 'select',
      options: [
        { label: 'All Statuses', value: '' },
        { label: 'Planned', value: 'Planned' },
        { label: 'Confirmed', value: 'Confirmed' },
        { label: 'In Progress', value: 'In_Progress' },
        { label: 'Completed', value: 'Completed' },
        { label: 'Cancelled', value: 'Cancelled' },
      ],
    },
    {
      id: 'location',
      label: 'Location',
      type: 'search',
      placeholder: 'Filter by location...',
    },
    {
      id: 'dateRange',
      label: 'Date Range',
      type: 'daterange',
    },
  ],
  sortOptions: [
    { id: 'eventName', label: 'Event Name', field: 'eventName' },
    { id: 'durationFrom', label: 'Start Date', field: 'durationFrom' },
    { id: 'status', label: 'Status', field: 'status' },
    { id: 'location', label: 'Location', field: 'location' },
    { id: 'createdAt', label: 'Created', field: 'createdAt' },
  ],
  enableBulkActions: true,
  enableExport: true,
  refreshInterval: 30000, // 30 seconds
};

/**
 * Props for DelegationDashboard component
 */
interface DelegationDashboardProps {
  className?: string;
}

/**
 * Main delegation dashboard component using the generic dashboard framework.
 *
 * This component provides:
 * - Multiple view modes (cards, table, calendar)
 * - Advanced filtering and sorting
 * - Tab-based organization
 * - Bulk operations
 * - Export functionality
 * - Persistent user preferences
 *
 * @param props - Component props
 * @returns JSX element representing the delegation dashboard
 */
export const DelegationDashboard: React.FC<DelegationDashboardProps> = ({
  className = '',
}) => {
  // Get delegation data
  const {
    data: delegations = [],
    isLoading,
    error,
    refetch,
  } = useDelegations();

  // Get dashboard store for delegations
  const dashboardStore = useDashboardStore('delegation');
  const {
    activeTab,
    layout,
    filters,
    searchTerm,
    selectedItems,
    getFilteredData,
    getSelectedCount,
    hasActiveFilters,
  } = dashboardStore();

  // Filter delegations based on active tab
  const getTabFilteredDelegations = (delegations: Delegation[]) => {
    const now = new Date();

    switch (activeTab) {
      case 'upcoming':
        return delegations.filter(
          d =>
            new Date(d.durationFrom) > now &&
            ['Planned', 'Confirmed'].includes(d.status)
        );
      case 'in-progress':
        return delegations.filter(d => d.status === 'In_Progress');
      case 'completed':
        return delegations.filter(d => d.status === 'Completed');
      default:
        return delegations;
    }
  };

  // Get filtered and sorted data
  const tabFilteredDelegations = getTabFilteredDelegations(delegations);
  const filteredDelegations = getFilteredData(
    tabFilteredDelegations,
    delegationDashboardConfig
  );

  // Update tab counts
  const updatedConfig = {
    ...delegationDashboardConfig,
    tabs:
      delegationDashboardConfig.tabs?.map(tab => ({
        ...tab,
        count: getTabFilteredDelegations(delegations).length,
      })) || [],
  };

  return (
    <DashboardPage config={updatedConfig} className={className}>
      {/* Dashboard implementation will be completed in the next phase */}
      <div className="space-y-6">
        <div className="text-center py-12">
          <Briefcase className="mx-auto mb-4 size-16 text-muted-foreground" />
          <h2 className="text-2xl font-semibold mb-2">
            Enhanced Delegation Dashboard
          </h2>
          <p className="text-muted-foreground mb-4">
            Using reusable patterns from the reliability dashboard
          </p>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-md mx-auto">
            <div className="text-center">
              <div className="text-2xl font-bold text-primary">
                {delegations.length}
              </div>
              <div className="text-sm text-muted-foreground">Total</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {getTabFilteredDelegations(delegations).length}
              </div>
              <div className="text-sm text-muted-foreground">Filtered</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {getSelectedCount()}
              </div>
              <div className="text-sm text-muted-foreground">Selected</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">
                {hasActiveFilters() ? '✓' : '○'}
              </div>
              <div className="text-sm text-muted-foreground">Filters</div>
            </div>
          </div>
        </div>
      </div>
    </DashboardPage>
  );
};

export default DelegationDashboard;
