/**
 * @file Hooks Index
 * @description Centralized exports for all custom hooks following Phase 4 standardization
 */

// ===== PHASE 4 STANDARDIZED HOOKS =====

// API Hooks - Standardized patterns for API interactions
export * from './api';

// Form Hooks - Standardized patterns for form handling
export * from './forms';

// UI Hooks - Standardized patterns for UI state management
export * from './ui';

// ===== CATEGORIZED HOOKS =====

// Authentication Hooks - Standardized patterns for authentication
// Auth hooks removed - use AuthContext directly
// export * from './auth';

// Security Hooks - Standardized patterns for security
export * from './security';

// Domain Hooks - Domain-specific business logic
export * from './domain';

// Utility Hooks - General utility functions
export * from './utils';

// ===== LEGACY HOOKS (Maintained for backward compatibility) =====

// Store hooks (re-exported for convenience)
export { useAppStore } from '../lib/stores/zustand/appStore';
export { useReliabilityStore } from '../lib/stores/zustand/reliabilityStore';
export { useUiStore } from '../lib/stores/zustand/uiStore';

// Legacy API hooks (will be deprecated in favor of standardized API hooks)
// Note: useApi.ts has been removed as it duplicated functionality with new standardized hooks

// ===== CONVENIENCE HOOKS =====

/**
 * Convenience hook that combines commonly used UI hooks
 * @example
 * ```typescript
 * const { theme, notifications, sidebar, modal } = useWorkHubCore();
 * ```
 */
export const useWorkHubCore = () => {
  // Import from new standardized locations
  const { useTheme } = require('./ui/useTheme');
  const { useNotifications } = require('./ui/useNotifications');
  const { useUiPreferences } = require('./ui/useUiPreferences');
  const { useSidebar } = require('./ui/useSidebar');
  const { useModal } = require('./ui/useModal');

  const theme = useTheme();
  const notifications = useNotifications();
  const uiPreferences = useUiPreferences();
  const sidebar = useSidebar();
  const modal = useModal();

  return {
    modal,
    notifications,
    sidebar,
    theme,
    uiPreferences,
  };
};
