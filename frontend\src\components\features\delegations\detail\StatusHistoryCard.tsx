'use client';

import { format, parseISO } from 'date-fns';
import {
  AlertCircle,
  Calendar,
  CheckCircle2,
  ClipboardList,
  Clock,
  History,
  PlayCircle,
  Plus,
  RefreshCw,
  TrendingUp,
  Trophy,
  User,
  XCircle,
} from 'lucide-react';
import React, { useState } from 'react';

import type {
  DelegationStatusEntry,
  DelegationStatusPrisma,
} from '@/lib/types/domain';

import StatusUpdateModal from '@/components/StatusUpdateModal';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';
import { formatDelegationStatusForDisplay } from '@/lib/utils/formattingUtils';

interface StatusHistoryCardProps {
  currentStatus: DelegationStatusPrisma;
  delegationId: string;
  isUpdating?: boolean;
  onStatusUpdate: (
    status: DelegationStatusPrisma,
    reason: string
  ) => Promise<void>;
  statusHistory: DelegationStatusEntry[];
}

// Enhanced status color function with modern gradients
const getStatusColor = (status: DelegationStatusPrisma | undefined) => {
  switch (status) {
    case 'Cancelled': {
      return 'bg-gradient-to-r from-red-500/20 to-rose-500/20 text-red-700 border-red-500/30 dark:text-red-400 dark:from-red-500/10 dark:to-rose-500/10 dark:border-red-500/20';
    }
    case 'Completed': {
      return 'bg-gradient-to-r from-purple-500/20 to-violet-500/20 text-purple-700 border-purple-500/30 dark:text-purple-400 dark:from-purple-500/10 dark:to-violet-500/10 dark:border-purple-500/20';
    }
    case 'Confirmed': {
      return 'bg-gradient-to-r from-green-500/20 to-emerald-500/20 text-green-700 border-green-500/30 dark:text-green-400 dark:from-green-500/10 dark:to-emerald-500/10 dark:border-green-500/20';
    }
    case 'In_Progress': {
      return 'bg-gradient-to-r from-yellow-500/20 to-amber-500/20 text-yellow-700 border-yellow-500/30 dark:text-yellow-400 dark:from-yellow-500/10 dark:to-amber-500/10 dark:border-yellow-500/20';
    }
    case 'Planned': {
      return 'bg-gradient-to-r from-blue-500/20 to-blue-600/20 text-blue-700 border-blue-500/30 dark:text-blue-400 dark:from-blue-500/10 dark:to-blue-600/10 dark:border-blue-500/20';
    }
    case 'No_details':
    default: {
      return 'bg-gradient-to-r from-gray-500/20 to-slate-500/20 text-gray-700 border-gray-500/30 dark:text-gray-400 dark:from-gray-500/10 dark:to-slate-500/10 dark:border-gray-500/20';
    }
  }
};

// Function to get the appropriate icon for each status
const getStatusIcon = (status: DelegationStatusPrisma | undefined) => {
  switch (status) {
    case 'Cancelled': {
      return XCircle;
    }
    case 'Completed': {
      return Trophy;
    }
    case 'Confirmed': {
      return CheckCircle2;
    }
    case 'In_Progress': {
      return PlayCircle;
    }
    case 'Planned': {
      return ClipboardList;
    }
    case 'No_details':
    default: {
      return AlertCircle;
    }
  }
};

// Function to get the background color for status icons
const getStatusIconBg = (status: DelegationStatusPrisma | undefined) => {
  switch (status) {
    case 'Cancelled': {
      return 'from-red-500/20 to-rose-500/20 border-red-500/30';
    }
    case 'Completed': {
      return 'from-purple-500/20 to-violet-500/20 border-purple-500/30';
    }
    case 'Confirmed': {
      return 'from-green-500/20 to-emerald-500/20 border-green-500/30';
    }
    case 'In_Progress': {
      return 'from-yellow-500/20 to-amber-500/20 border-yellow-500/30';
    }
    case 'Planned': {
      return 'from-blue-500/20 to-blue-600/20 border-blue-500/30';
    }
    case 'No_details':
    default: {
      return 'from-gray-500/20 to-slate-500/20 border-gray-500/30';
    }
  }
};

const formatDate = (dateString: string | undefined, includeTime = false) => {
  if (!dateString) return 'N/A';
  try {
    return format(
      parseISO(dateString),
      includeTime ? 'MMM d, yyyy, HH:mm' : 'MMM d, yyyy'
    );
  } catch {
    return 'Invalid Date';
  }
};

export default function StatusHistoryCard({
  currentStatus,
  delegationId,
  isUpdating = false,
  onStatusUpdate,
  statusHistory,
}: StatusHistoryCardProps) {
  const [isStatusModalOpen, setIsStatusModalOpen] = useState(false);

  const handleStatusUpdateClick = () => {
    setIsStatusModalOpen(true);
  };

  const handleStatusUpdateConfirm = async (
    status: DelegationStatusPrisma,
    reason: string
  ) => {
    try {
      await onStatusUpdate(status, reason);
      setIsStatusModalOpen(false);
    } catch (error) {
      // Error handling is done in the parent component
      console.error('Status update failed:', error);
    }
  };

  const sortedHistory =
    statusHistory
      ?.slice()
      .sort(
        (a, b) =>
          new Date(b.changedAt).getTime() - new Date(a.changedAt).getTime()
      ) || [];

  return (
    <>
      <Card className="border-border/60 bg-gradient-to-br from-card to-card/95 shadow-lg backdrop-blur-sm">
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between gap-4">
            <div className="flex min-w-0 flex-1 items-center gap-3">
              <div className="shrink-0 rounded-lg bg-primary/10 p-2 text-primary">
                <History className="size-5" />
              </div>
              <div className="flex min-w-0 items-center gap-2">
                <CardTitle className="whitespace-nowrap text-xl font-semibold text-primary">
                  Status History
                </CardTitle>
                <Badge className="shrink-0 text-xs" variant="secondary">
                  {sortedHistory.length}{' '}
                  {sortedHistory.length === 1 ? 'entry' : 'entries'}
                </Badge>
              </div>
            </div>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    className="shrink-0 gap-2 whitespace-nowrap bg-gradient-to-r from-primary to-primary/90 text-primary-foreground shadow-md transition-all duration-200 hover:from-primary/90 hover:to-primary hover:shadow-lg"
                    disabled={isUpdating}
                    onClick={handleStatusUpdateClick}
                    size="sm"
                  >
                    {isUpdating ? (
                      <RefreshCw className="size-4 animate-spin" />
                    ) : (
                      <Plus className="size-4" />
                    )}
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Change delegation status</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </CardHeader>

        <CardContent className="pt-0">
          {sortedHistory.length > 0 ? (
            <div className="scrollbar-thin scrollbar-thumb-border scrollbar-track-transparent max-h-96 space-y-4 overflow-y-auto pr-2">
              {sortedHistory.map((entry, index) => (
                <div key={entry.id || index}>
                  <div className="group relative rounded-xl border border-border/50 bg-gradient-to-r from-background/50 to-background/30 p-4 transition-all duration-200 hover:border-border">
                    {/* Timeline connector */}
                    {index < sortedHistory.length - 1 && (
                      <div className="absolute left-7 top-16 h-8 w-0.5 bg-gradient-to-b from-border via-border/50 to-transparent" />
                    )}

                    <div className="flex items-start gap-4">
                      {/* Status indicator with specific icon */}
                      <div className="relative">
                        {(() => {
                          const StatusIcon = getStatusIcon(
                            entry.status as DelegationStatusPrisma
                          );
                          const iconBgClasses = getStatusIconBg(
                            entry.status as DelegationStatusPrisma
                          );
                          return (
                            <div
                              className={cn(
                                'p-2.5 rounded-full bg-gradient-to-br border-2 transition-all duration-200 group-hover:scale-105',
                                iconBgClasses
                              )}
                            >
                              <StatusIcon className="size-4" />
                            </div>
                          );
                        })()}
                        {index === 0 && (
                          <div className="absolute -right-1 -top-1 size-3 animate-pulse rounded-full border-2 border-background bg-green-500 shadow-lg" />
                        )}
                      </div>

                      <div className="min-w-0 flex-1">
                        <div className="mb-2 flex items-center justify-between gap-3">
                          <Badge
                            className={cn(
                              'text-sm py-1.5 px-3 font-medium border shadow-sm flex items-center gap-1.5 flex-shrink-0 whitespace-nowrap',
                              getStatusColor(entry.status)
                            )}
                          >
                            {(() => {
                              const StatusIcon = getStatusIcon(
                                entry.status as DelegationStatusPrisma
                              );
                              return (
                                <StatusIcon className="size-3.5 shrink-0" />
                              );
                            })()}
                            <span className="whitespace-nowrap">
                              {formatDelegationStatusForDisplay(
                                entry.status as DelegationStatusPrisma
                              )}
                            </span>
                          </Badge>

                          <div className="flex shrink-0 items-center gap-2 text-xs text-muted-foreground">
                            <Calendar className="size-3" />
                            <span className="whitespace-nowrap">
                              {formatDate(entry.changedAt, true)}
                            </span>
                          </div>
                        </div>

                        {entry.reason && (
                          <div className="mt-2 rounded-lg border border-border/30 bg-muted/30 p-3">
                            <p className="flex items-start gap-2 text-sm italic text-muted-foreground">
                              <User className="mt-0.5 size-3 shrink-0" />
                              <span className="shrink-0 font-medium">
                                Reason:
                              </span>
                              <span className="break-words">
                                {entry.reason}
                              </span>
                            </p>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  {index < sortedHistory.length - 1 && (
                    <Separator className="my-2 bg-gradient-to-r from-transparent via-border to-transparent" />
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="py-12 text-center">
              <div className="mx-auto mb-4 flex size-16 items-center justify-center rounded-full bg-muted/30 p-4">
                <Clock className="size-8 text-muted-foreground" />
              </div>
              <p className="text-sm text-muted-foreground">
                No status history available.
              </p>
              <p className="mt-1 text-xs text-muted-foreground">
                Status changes will appear here.
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Status Update Modal */}
      <StatusUpdateModal
        currentStatus={currentStatus}
        delegationId={delegationId}
        isOpen={isStatusModalOpen}
        onClose={() => setIsStatusModalOpen(false)}
        onConfirm={handleStatusUpdateConfirm}
      />
    </>
  );
}
