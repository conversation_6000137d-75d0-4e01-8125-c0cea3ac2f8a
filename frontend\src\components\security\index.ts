/**
 * @file Security Components Exports - SOLID Principles
 * @module components/security
 *
 * Centralized exports for all security-related components following SOLID principles.
 * This provides a single import point for security components across the application.
 */

// ===== COMPONENT EXPORTS (SRP: Single Responsibility) =====

// Individual component exports for better tree-shaking
export {
  ProtectedComponent,
  AdminOnly,
  SuperAdminOnly,
  WriteProtected,
  DeleteProtected,
} from './ProtectedComponent';

// ===== TYPE EXPORTS (ISP: Interface Segregation) =====

export type { ProtectedComponentProps } from './ProtectedComponent';

// ===== CONVENIENCE UTILITIES (DRY: Don't Repeat Yourself) =====

/**
 * Security component utilities for common use cases
 * @deprecated Use individual component imports for better tree-shaking
 */
export const SecurityUtils = {
  // Re-export components for legacy support only
  get ProtectedComponent() {
    return require('./ProtectedComponent').ProtectedComponent;
  },
  get AdminOnly() {
    return require('./ProtectedComponent').AdminOnly;
  },
  get SuperAdminOnly() {
    return require('./ProtectedComponent').SuperAdminOnly;
  },
  get WriteProtected() {
    return require('./ProtectedComponent').WriteProtected;
  },
  get DeleteProtected() {
    return require('./ProtectedComponent').DeleteProtected;
  },
} as const;
