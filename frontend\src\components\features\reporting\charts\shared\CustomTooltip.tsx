// frontend/src/components/features/reporting/charts/shared/CustomTooltip.tsx

import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { format } from 'date-fns';

/**
 * Base tooltip data interface
 */
export interface TooltipData {
  label?: string;
  value?: number | string;
  color?: string;
  name?: string;
  payload?: any;
}

/**
 * Props for CustomTooltip component
 */
interface CustomTooltipProps {
  active?: boolean;
  payload?: TooltipData[];
  label?: string;
  title?: string;
  showColor?: boolean;
  showPercentage?: boolean;
  formatValue?: (value: any) => string;
  formatLabel?: (label: string) => string;
  className?: string;
}

/**
 * Default value formatter
 */
const defaultFormatValue = (value: any): string => {
  if (typeof value === 'number') {
    return value.toLocaleString();
  }
  return String(value);
};

/**
 * Default label formatter
 */
const defaultFormatLabel = (label: string): string => {
  // Try to parse as date first
  try {
    const date = new Date(label);
    if (!isNaN(date.getTime())) {
      return format(date, 'MMM dd, yyyy');
    }
  } catch {
    // Not a date, return as is
  }
  return label;
};

/**
 * CustomTooltip Component
 * 
 * A reusable, customizable tooltip component for charts.
 * Follows SRP by only handling tooltip rendering and formatting.
 * 
 * Features:
 * - Flexible data formatting
 * - Color indicators
 * - Percentage display
 * - Date formatting
 * - Custom styling
 * - Responsive design
 * 
 * @param props - Component props
 * @returns JSX element or null
 */
export const CustomTooltip: React.FC<CustomTooltipProps> = ({
  active = false,
  payload = [],
  label,
  title,
  showColor = true,
  showPercentage = false,
  formatValue = defaultFormatValue,
  formatLabel = defaultFormatLabel,
  className = '',
}) => {
  if (!active || !payload || payload.length === 0) {
    return null;
  }

  const formattedLabel = label ? formatLabel(label) : '';

  return (
    <Card className={`shadow-lg border-0 ${className}`}>
      <CardContent className="p-4 min-w-[200px]">
        {/* Title */}
        {title && (
          <h4 className="font-semibold text-gray-900 mb-2 text-sm">
            {title}
          </h4>
        )}
        
        {/* Label */}
        {formattedLabel && (
          <p className="font-medium text-gray-800 mb-3 text-sm">
            {formattedLabel}
          </p>
        )}
        
        {/* Data Items */}
        <div className="space-y-2">
          {payload.map((entry, index) => {
            const value = entry.value;
            const name = entry.name || entry.label || `Item ${index + 1}`;
            const percentage = entry.payload?.percentage;
            
            return (
              <div key={index} className="flex items-center justify-between gap-3">
                <div className="flex items-center gap-2 min-w-0 flex-1">
                  {/* Color Indicator */}
                  {showColor && entry.color && (
                    <div
                      className="w-3 h-3 rounded-full flex-shrink-0"
                      style={{ backgroundColor: entry.color }}
                    />
                  )}
                  
                  {/* Name */}
                  <span className="text-sm text-gray-600 truncate">
                    {name}:
                  </span>
                </div>
                
                {/* Value and Percentage */}
                <div className="flex items-center gap-2 flex-shrink-0">
                  <Badge variant="secondary" className="text-xs">
                    {formatValue(value)}
                  </Badge>
                  
                  {showPercentage && percentage !== undefined && (
                    <Badge variant="outline" className="text-xs">
                      {typeof percentage === 'number' 
                        ? `${percentage.toFixed(1)}%` 
                        : percentage
                      }
                    </Badge>
                  )}
                </div>
              </div>
            );
          })}
        </div>
        
        {/* Additional Data from Payload */}
        {payload[0]?.payload && (
          <div className="mt-3 pt-3 border-t border-gray-100">
            {Object.entries(payload[0].payload)
              .filter(([key, value]) => 
                !['name', 'value', 'color', 'percentage'].includes(key) &&
                value !== undefined &&
                value !== null
              )
              .map(([key, value]) => (
                <div key={key} className="flex justify-between items-center text-xs text-gray-500">
                  <span className="capitalize">
                    {key.replace(/([A-Z])/g, ' $1').trim()}:
                  </span>
                  <span className="font-medium">
                    {formatValue(value)}
                  </span>
                </div>
              ))
            }
          </div>
        )}
      </CardContent>
    </Card>
  );
};

/**
 * Specialized tooltip for status charts
 */
export const StatusTooltip: React.FC<Omit<CustomTooltipProps, 'showPercentage'>> = (props) => (
  <CustomTooltip {...props} showPercentage={true} />
);

/**
 * Specialized tooltip for trend charts
 */
export const TrendTooltip: React.FC<CustomTooltipProps> = (props) => (
  <CustomTooltip 
    {...props} 
    formatLabel={(label) => {
      try {
        const date = new Date(label);
        return format(date, 'MMM dd, yyyy');
      } catch {
        return label;
      }
    }}
  />
);

/**
 * Specialized tooltip for metric charts
 */
export const MetricTooltip: React.FC<CustomTooltipProps> = (props) => (
  <CustomTooltip 
    {...props} 
    formatValue={(value) => {
      if (typeof value === 'number') {
        return value.toLocaleString();
      }
      return String(value);
    }}
  />
);
