import { test, expect } from '@playwright/test';

test.describe('Reporting System E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the reporting dashboard
    await page.goto('/reporting');

    // Wait for the page to load
    await page.waitForLoadState('networkidle');
  });

  test.describe('Dashboard Functionality', () => {
    test('should load reporting dashboard with all widgets', async ({
      page,
    }) => {
      // Check that main dashboard elements are visible
      await expect(
        page.locator('[data-testid="reporting-dashboard"]')
      ).toBeVisible();

      // Check that all main widgets are present
      await expect(
        page.locator('[data-testid="delegation-analytics-widget"]')
      ).toBeVisible();
      await expect(
        page.locator('[data-testid="task-analytics-widget"]')
      ).toBeVisible();
      await expect(
        page.locator('[data-testid="vehicle-analytics-widget"]')
      ).toBeVisible();
      await expect(
        page.locator('[data-testid="employee-analytics-widget"]')
      ).toBeVisible();

      // Check that summary metrics are displayed
      await expect(
        page.locator('[data-testid="total-delegations"]')
      ).toBeVisible();
      await expect(page.locator('[data-testid="total-tasks"]')).toBeVisible();
      await expect(
        page.locator('[data-testid="total-vehicles"]')
      ).toBeVisible();
      await expect(
        page.locator('[data-testid="total-employees"]')
      ).toBeVisible();
    });

    test('should display loading states and then data', async ({ page }) => {
      // Reload to see loading states
      await page.reload();

      // Check for loading indicators
      await expect(
        page.locator('[data-testid="loading-spinner"]').first()
      ).toBeVisible();

      // Wait for data to load
      await page.waitForSelector('[data-testid="delegation-analytics-chart"]', {
        timeout: 10000,
      });

      // Verify data is displayed
      await expect(
        page.locator('[data-testid="delegation-analytics-chart"]')
      ).toBeVisible();
      await expect(
        page.locator('[data-testid="task-analytics-chart"]')
      ).toBeVisible();
    });
  });

  test.describe('Filtering Functionality', () => {
    test('should apply date range filters and update data', async ({
      page,
    }) => {
      // Open date range filter
      await page.click('[data-testid="date-range-filter"]');

      // Set date range
      await page.fill('[data-testid="date-from-input"]', '2024-01-01');
      await page.fill('[data-testid="date-to-input"]', '2024-06-30');

      // Apply filters
      await page.click('[data-testid="apply-filters-button"]');

      // Wait for data to update
      await page.waitForResponse(
        response =>
          response.url().includes('/api/reporting/delegations/analytics') &&
          response.status() === 200
      );

      // Verify that the filter was applied (check URL params or data changes)
      await expect(
        page.locator('[data-testid="active-filters"]')
      ).toContainText('2024-01-01');
    });

    test('should apply status filters and update charts', async ({ page }) => {
      // Open status filter dropdown
      await page.click('[data-testid="status-filter-dropdown"]');

      // Select specific statuses
      await page.check('[data-testid="status-pending-checkbox"]');
      await page.check('[data-testid="status-approved-checkbox"]');
      await page.uncheck('[data-testid="status-completed-checkbox"]');

      // Apply filters
      await page.click('[data-testid="apply-filters-button"]');

      // Wait for API response
      await page.waitForResponse(
        response =>
          response.url().includes('/api/reporting') && response.status() === 200
      );

      // Verify chart updates
      await expect(
        page.locator('[data-testid="status-distribution-chart"]')
      ).toBeVisible();

      // Check that only selected statuses are shown in the chart
      await expect(page.locator('[data-testid="chart-legend"]')).toContainText(
        'Pending'
      );
      await expect(page.locator('[data-testid="chart-legend"]')).toContainText(
        'Approved'
      );
      await expect(
        page.locator('[data-testid="chart-legend"]')
      ).not.toContainText('Completed');
    });

    test('should apply location filters', async ({ page }) => {
      // Open location filter
      await page.click('[data-testid="location-filter-dropdown"]');

      // Select specific locations
      await page.check('[data-testid="location-new-york-checkbox"]');
      await page.check('[data-testid="location-london-checkbox"]');

      // Apply filters
      await page.click('[data-testid="apply-filters-button"]');

      // Wait for data update
      await page.waitForTimeout(2000);

      // Verify location metrics update
      await expect(
        page.locator('[data-testid="location-metrics-widget"]')
      ).toBeVisible();
      await expect(
        page.locator('[data-testid="location-metrics-list"]')
      ).toContainText('New York');
      await expect(
        page.locator('[data-testid="location-metrics-list"]')
      ).toContainText('London');
    });

    test('should clear all filters', async ({ page }) => {
      // Apply some filters first
      await page.click('[data-testid="status-filter-dropdown"]');
      await page.check('[data-testid="status-pending-checkbox"]');
      await page.click('[data-testid="apply-filters-button"]');

      // Wait for filters to be applied
      await page.waitForTimeout(1000);

      // Clear all filters
      await page.click('[data-testid="clear-filters-button"]');

      // Wait for data to reload
      await page.waitForResponse(
        response =>
          response.url().includes('/api/reporting') && response.status() === 200
      );

      // Verify filters are cleared
      await expect(page.locator('[data-testid="active-filters"]')).toBeEmpty();
    });
  });

  test.describe('Export Functionality', () => {
    test('should export delegation report as PDF', async ({ page }) => {
      // Navigate to delegation analytics
      await page.click('[data-testid="delegation-analytics-widget"]');

      // Open export menu
      await page.click('[data-testid="export-menu-button"]');

      // Select PDF export
      await page.click('[data-testid="export-pdf-option"]');

      // Configure export options
      await page.check('[data-testid="include-charts-checkbox"]');
      await page.check('[data-testid="include-summary-checkbox"]');

      // Start download
      const downloadPromise = page.waitForEvent('download');
      await page.click('[data-testid="start-export-button"]');

      // Wait for download to complete
      const download = await downloadPromise;

      // Verify download
      expect(download.suggestedFilename()).toMatch(/delegation-report.*\.pdf$/);
    });

    test('should export task analytics as Excel', async ({ page }) => {
      // Navigate to task analytics
      await page.click('[data-testid="task-analytics-widget"]');

      // Open export menu
      await page.click('[data-testid="export-menu-button"]');

      // Select Excel export
      await page.click('[data-testid="export-excel-option"]');

      // Configure export options
      await page.check('[data-testid="include-raw-data-checkbox"]');

      // Start download
      const downloadPromise = page.waitForEvent('download');
      await page.click('[data-testid="start-export-button"]');

      // Wait for download
      const download = await downloadPromise;

      // Verify download
      expect(download.suggestedFilename()).toMatch(/task-analytics.*\.xlsx$/);
    });

    test('should export vehicle data as CSV', async ({ page }) => {
      // Navigate to vehicle analytics
      await page.click('[data-testid="vehicle-analytics-widget"]');

      // Open export menu
      await page.click('[data-testid="export-menu-button"]');

      // Select CSV export
      await page.click('[data-testid="export-csv-option"]');

      // Start download
      const downloadPromise = page.waitForEvent('download');
      await page.click('[data-testid="start-export-button"]');

      // Wait for download
      const download = await downloadPromise;

      // Verify download
      expect(download.suggestedFilename()).toMatch(/vehicle-analytics.*\.csv$/);
    });
  });

  test.describe('Real-time Updates', () => {
    test('should update data when refresh button is clicked', async ({
      page,
    }) => {
      // Get initial data
      const initialCount = await page
        .locator('[data-testid="total-delegations"]')
        .textContent();

      // Click refresh button
      await page.click('[data-testid="refresh-data-button"]');

      // Wait for API calls to complete
      await page.waitForResponse(
        response =>
          response.url().includes('/api/reporting') && response.status() === 200
      );

      // Verify data refreshed (loading indicator should appear and disappear)
      await expect(
        page.locator('[data-testid="loading-spinner"]')
      ).toBeVisible();
      await expect(
        page.locator('[data-testid="loading-spinner"]')
      ).not.toBeVisible();

      // Data should be updated (even if same values)
      await expect(
        page.locator('[data-testid="total-delegations"]')
      ).toBeVisible();
    });

    test('should handle WebSocket updates', async ({ page }) => {
      // Mock WebSocket connection (this would depend on your WebSocket implementation)
      await page.evaluate(() => {
        // Simulate WebSocket message
        window.dispatchEvent(
          new CustomEvent('websocket-update', {
            detail: {
              type: 'delegation-update',
              data: { totalCount: 999 },
            },
          })
        );
      });

      // Wait for UI to update
      await page.waitForTimeout(1000);

      // Verify data updated
      await expect(
        page.locator('[data-testid="total-delegations"]')
      ).toContainText('999');
    });
  });

  test.describe('Error Handling', () => {
    test('should display error message when API fails', async ({ page }) => {
      // Intercept API calls and return error
      await page.route('**/api/reporting/delegations/analytics', route => {
        route.fulfill({
          status: 500,
          contentType: 'application/json',
          body: JSON.stringify({ error: 'Internal Server Error' }),
        });
      });

      // Reload page to trigger API call
      await page.reload();

      // Wait for error state
      await expect(page.locator('[data-testid="error-message"]')).toBeVisible();
      await expect(page.locator('[data-testid="error-message"]')).toContainText(
        'Failed to load'
      );
    });

    test('should allow retry when error occurs', async ({ page }) => {
      // Intercept first API call with error
      let callCount = 0;
      await page.route('**/api/reporting/delegations/analytics', route => {
        callCount++;
        if (callCount === 1) {
          route.fulfill({
            status: 500,
            contentType: 'application/json',
            body: JSON.stringify({ error: 'Server Error' }),
          });
        } else {
          route.continue();
        }
      });

      // Reload to trigger error
      await page.reload();

      // Wait for error state
      await expect(page.locator('[data-testid="error-message"]')).toBeVisible();

      // Click retry button
      await page.click('[data-testid="retry-button"]');

      // Wait for successful load
      await expect(
        page.locator('[data-testid="delegation-analytics-chart"]')
      ).toBeVisible();
      await expect(
        page.locator('[data-testid="error-message"]')
      ).not.toBeVisible();
    });
  });

  test.describe('Performance', () => {
    test('should load dashboard within acceptable time', async ({ page }) => {
      const startTime = Date.now();

      // Navigate to dashboard
      await page.goto('/reporting');

      // Wait for all widgets to load
      await page.waitForSelector('[data-testid="delegation-analytics-chart"]');
      await page.waitForSelector('[data-testid="task-analytics-chart"]');
      await page.waitForSelector('[data-testid="vehicle-analytics-chart"]');
      await page.waitForSelector('[data-testid="employee-analytics-chart"]');

      const loadTime = Date.now() - startTime;

      // Assert load time is under 5 seconds
      expect(loadTime).toBeLessThan(5000);
    });

    test('should handle large datasets without performance issues', async ({
      page,
    }) => {
      // Mock API to return large dataset
      await page.route('**/api/reporting/delegations/analytics', route => {
        const largeDataset = {
          totalCount: 10000,
          statusDistribution: Array.from({ length: 10 }, (_, i) => ({
            status: `STATUS_${i}`,
            count: 1000,
            percentage: 10,
            color: `#color${i}`,
          })),
          trendData: Array.from({ length: 365 }, (_, i) => ({
            date: `2024-${String(Math.floor(i / 30) + 1).padStart(2, '0')}-${String((i % 30) + 1).padStart(2, '0')}`,
            created: Math.floor(Math.random() * 50),
            completed: Math.floor(Math.random() * 40),
            inProgress: Math.floor(Math.random() * 20),
          })),
          locationMetrics: Array.from({ length: 100 }, (_, i) => ({
            location: `Location ${i}`,
            delegationCount: Math.floor(Math.random() * 100),
            averageDuration: Math.random() * 10,
            completionRate: Math.random(),
          })),
          summary: {
            totalDelegations: 10000,
            activeDelegations: 3000,
            completedDelegations: 7000,
            totalDelegates: 500,
            averageDuration: 4.2,
            completionRate: 0.7,
          },
        };

        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify(largeDataset),
        });
      });

      const startTime = Date.now();

      // Load dashboard with large dataset
      await page.reload();
      await page.waitForSelector('[data-testid="delegation-analytics-chart"]');

      const renderTime = Date.now() - startTime;

      // Should still render within reasonable time
      expect(renderTime).toBeLessThan(10000); // 10 seconds for large dataset

      // Verify data is displayed correctly
      await expect(
        page.locator('[data-testid="total-delegations"]')
      ).toContainText('10000');
    });
  });

  test.describe('Accessibility', () => {
    test('should be accessible with keyboard navigation', async ({ page }) => {
      // Test tab navigation
      await page.keyboard.press('Tab');
      await expect(page.locator(':focus')).toBeVisible();

      // Navigate through filters
      await page.keyboard.press('Tab');
      await page.keyboard.press('Tab');
      await page.keyboard.press('Enter'); // Open filter

      // Use arrow keys in dropdown
      await page.keyboard.press('ArrowDown');
      await page.keyboard.press('Space'); // Select option
      await page.keyboard.press('Escape'); // Close dropdown

      // Navigate to apply button and activate
      await page.keyboard.press('Tab');
      await page.keyboard.press('Enter');

      // Verify filter was applied
      await page.waitForTimeout(1000);
      await expect(
        page.locator('[data-testid="active-filters"]')
      ).toBeVisible();
    });

    test('should have proper ARIA labels and roles', async ({ page }) => {
      // Check main dashboard has proper role
      await expect(
        page.locator('[data-testid="reporting-dashboard"]')
      ).toHaveAttribute('role', 'main');

      // Check charts have proper labels
      await expect(
        page.locator('[data-testid="delegation-analytics-chart"]')
      ).toHaveAttribute('aria-label');
      await expect(
        page.locator('[data-testid="task-analytics-chart"]')
      ).toHaveAttribute('aria-label');

      // Check buttons have proper labels
      await expect(
        page.locator('[data-testid="apply-filters-button"]')
      ).toHaveAttribute('aria-label');
      await expect(
        page.locator('[data-testid="export-menu-button"]')
      ).toHaveAttribute('aria-label');
    });
  });

  test.describe('Mobile Responsiveness', () => {
    test('should display correctly on mobile devices', async ({ page }) => {
      // Set mobile viewport
      await page.setViewportSize({ width: 375, height: 667 });

      // Reload page
      await page.reload();

      // Check that dashboard adapts to mobile
      await expect(
        page.locator('[data-testid="reporting-dashboard"]')
      ).toBeVisible();

      // Check that widgets stack vertically
      const widgets = page.locator('[data-testid*="analytics-widget"]');
      const widgetCount = await widgets.count();

      for (let i = 0; i < widgetCount; i++) {
        await expect(widgets.nth(i)).toBeVisible();
      }

      // Check that filters are accessible (might be in a mobile menu)
      await expect(
        page.locator('[data-testid="mobile-filter-menu"]')
      ).toBeVisible();
    });

    test('should handle touch interactions', async ({ page }) => {
      // Set mobile viewport
      await page.setViewportSize({ width: 375, height: 667 });

      // Test touch interactions on charts
      await page.tap('[data-testid="delegation-analytics-chart"]');

      // Test swipe gestures (if implemented)
      await page.touchscreen.tap(200, 300);
      await page.touchscreen.tap(100, 300);

      // Verify interactions work
      await expect(page.locator('[data-testid="chart-tooltip"]')).toBeVisible();
    });
  });
});
