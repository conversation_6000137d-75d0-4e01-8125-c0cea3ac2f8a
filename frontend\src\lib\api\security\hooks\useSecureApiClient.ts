/**
 * @file useSecureApiClient Hook - Enhanced Secure API Client for React
 * @module api/security/hooks/useSecureApiClient
 *
 * Phase 3: React Integration Layer
 * This hook provides a complete replacement for useSecure<PERSON>pi with enhanced architecture.
 * It combines the enhanced SecureApiClient with React-specific functionality.
 */

'use client';

import { useCallback, useMemo } from 'react';
import type { RequestConfig } from '../../core/types';
import {
  useSecureHttpClient,
  type UseSecureHttpClientConfig,
} from './useSecureHttpClient';

/**
 * API Response interface (compatible with existing useSecureApi)
 */
export interface ApiResponse<T = any> {
  data: T;
  headers: Record<string, string>;
  status: number;
  statusText: string;
}

/**
 * Request Configuration (compatible with existing useSecureApi)
 */
export interface SecureApiRequestConfig {
  data?: any;
  headers?: Record<string, string>;
  method?: 'DELETE' | 'GET' | 'PATCH' | 'POST' | 'PUT';
  timeout?: number;
  url: string;
}

/**
 * useSecureApiClient Return Type (compatible with useSecureApi)
 */
export interface UseSecureApiClientReturn {
  // Core API functionality (compatible with useSecureApi)
  hasValidToken: boolean;
  isAuthenticated: boolean;
  refreshToken: () => Promise<boolean>;
  sanitizeInput: (input: any) => any;
  secureRequest: <T = any>(
    config: SecureApiRequestConfig
  ) => Promise<ApiResponse<T>>;

  // Enhanced functionality from new architecture
  client: any; // IHttpClient
  securityStatus: any;
  refreshSecurityFeatures: () => void;
  updateSecurityConfig: (config: any) => void;

  // Status and error handling
  isInitialized: boolean;
  isLoading: boolean;
  error: Error | null;
}

/**
 * useSecureApiClient Hook
 *
 * Complete replacement for useSecureApi with enhanced architecture:
 * - Maintains backward compatibility with existing useSecureApi interface
 * - Uses enhanced SecureApiClient with SecurityComposer
 * - Provides additional functionality from new architecture
 * - Eliminates HTTP duplication by using proper HTTP client
 */
export function useSecureApiClient(
  config: UseSecureHttpClientConfig = {}
): UseSecureApiClientReturn {
  // Use the enhanced secure HTTP client
  const {
    client,
    isAuthenticated,
    hasValidToken,
    securityStatus,
    refreshToken,
    refreshSecurityFeatures,
    updateSecurityConfig,
    sanitizeInput,
    isInitialized,
    isLoading,
    error,
  } = useSecureHttpClient(config);

  /**
   * Secure request function (compatible with useSecureApi interface)
   * This replaces the HTTP logic that was duplicated in useSecureApi
   */
  const secureRequest = useCallback(
    async <T = any>(
      requestConfig: SecureApiRequestConfig
    ): Promise<ApiResponse<T>> => {
      const {
        data,
        headers = {},
        method = 'GET',
        timeout,
        url,
      } = requestConfig;

      try {
        // Convert to RequestConfig format for the HTTP client
        const clientConfig: RequestConfig = {
          headers,
          timeout: timeout || 10000,
          // Additional config can be added here
        };

        // Use the appropriate HTTP client method based on the request method
        let response: T;

        switch (method.toUpperCase()) {
          case 'GET':
            response = await client.get<T>(url, clientConfig);
            break;
          case 'POST':
            response = await client.post<T>(url, data, clientConfig);
            break;
          case 'PUT':
            response = await client.put<T>(url, data, clientConfig);
            break;
          case 'PATCH':
            response = await client.patch<T>(url, data, clientConfig);
            break;
          case 'DELETE':
            response = await client.delete<T>(url, clientConfig);
            break;
          default:
            throw new Error(`Unsupported HTTP method: ${method}`);
        }

        // Return in the format expected by useSecureApi consumers
        return {
          data: response,
          headers: {}, // Headers would be populated by the HTTP client if needed
          status: 200, // Status would be populated by the HTTP client
          statusText: 'OK', // Status text would be populated by the HTTP client
        };
      } catch (error) {
        // Re-throw errors in a format compatible with useSecureApi
        if (error instanceof Error) {
          throw error;
        }
        throw new Error('Request failed');
      }
    },
    [client]
  );

  // Return interface compatible with useSecureApi plus enhanced functionality
  return {
    // Core API functionality (compatible with useSecureApi)
    hasValidToken,
    isAuthenticated,
    refreshToken,
    sanitizeInput,
    secureRequest,

    // Enhanced functionality from new architecture
    client,
    securityStatus,
    refreshSecurityFeatures,
    updateSecurityConfig,

    // Status and error handling
    isInitialized,
    isLoading,
    error,
  };
}

/**
 * Convenience hook that provides only the useSecureApi-compatible interface
 * This can be used as a drop-in replacement for useSecureApi
 */
export function useSecureApiReplacement(
  config: UseSecureHttpClientConfig = {}
): Pick<
  UseSecureApiClientReturn,
  | 'hasValidToken'
  | 'isAuthenticated'
  | 'refreshToken'
  | 'sanitizeInput'
  | 'secureRequest'
> {
  const apiClient = useSecureApiClient(config);

  return {
    hasValidToken: apiClient.hasValidToken,
    isAuthenticated: apiClient.isAuthenticated,
    refreshToken: apiClient.refreshToken,
    sanitizeInput: apiClient.sanitizeInput,
    secureRequest: apiClient.secureRequest,
  };
}

/**
 * Hook for accessing enhanced security features
 * This provides access to the new architecture features
 */
export function useSecureApiClientFeatures(
  config: UseSecureHttpClientConfig = {}
): Pick<
  UseSecureApiClientReturn,
  | 'client'
  | 'securityStatus'
  | 'refreshSecurityFeatures'
  | 'updateSecurityConfig'
  | 'isInitialized'
  | 'isLoading'
  | 'error'
> {
  const apiClient = useSecureApiClient(config);

  return {
    client: apiClient.client,
    securityStatus: apiClient.securityStatus,
    refreshSecurityFeatures: apiClient.refreshSecurityFeatures,
    updateSecurityConfig: apiClient.updateSecurityConfig,
    isInitialized: apiClient.isInitialized,
    isLoading: apiClient.isLoading,
    error: apiClient.error,
  };
}
