'use client';

import React from 'react';

import type { EnrichedServiceRecord } from '@/lib/types/domain';

import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { cn } from '@/lib/utils';

/**
 * Props for the ServiceHistorySummary component
 */
interface ServiceHistorySummaryProps {
  /** Additional CSS class names */
  className?: string;
  /** Array of service records to display summary statistics for */
  records: EnrichedServiceRecord[];
  /** Whether this summary is for a vehicle-specific view */
  vehicleSpecific?: boolean;
}

/**
 * Props for the SummaryStatCard component
 */
interface SummaryStatCardProps {
  /** Additional CSS class for the card background */
  className?: string;
  /** Optional column span for the card */
  colSpan?: string;
  /** Label to display below the value */
  label: string;
  /** CSS class for the label text color */
  textColor?: string;
  /** Value to display (typically a number) */
  value: number | string;
}

/**
 * A component that displays summary statistics for service history records
 *
 * @example
 * ```tsx
 * <ServiceHistorySummary records={filteredRecords} vehicleSpecific={false} />
 * ```
 */
export function ServiceHistorySummary({
  className,
  records,
  vehicleSpecific = false,
}: ServiceHistorySummaryProps) {
  // Calculate statistics
  const totalRecords = records.length;
  const totalCost = records.reduce(
    (sum, record) => sum + (Number(record.cost) || 0),
    0
  );

  // Get date range
  const dates = records.map(record => new Date(record.date).getTime());
  const oldestDate = dates.length > 0 ? new Date(Math.min(...dates)) : null;
  const newestDate = dates.length > 0 ? new Date(Math.max(...dates)) : null;

  // Get service type counts
  const serviceTypeCounts = records.reduce(
    (acc, record) => {
      for (const service of record.servicePerformed) {
        acc[service] = (acc[service] || 0) + 1;
      }
      return acc;
    },
    {} as Record<string, number>
  );

  // Get top services
  const topServices = Object.entries(serviceTypeCounts)
    .sort((a, b) => b[1] - a[1])
    .slice(0, 3);

  // Calculate odometer range for vehicle-specific view
  const odometerRange =
    vehicleSpecific && records.length > 0
      ? Math.max(...records.map(r => r.odometer)) -
        Math.min(...records.map(r => r.odometer))
      : 0;

  // Count unique vehicles for general view
  const uniqueVehicleCount =
    !vehicleSpecific && records.length > 0
      ? new Set(records.map(r => r.vehicleId)).size
      : 0;

  return (
    <div
      className={cn(
        'mt-6 grid grid-cols-2 sm:grid-cols-3 gap-4 summary-grid',
        className
      )}
    >
      {/* Total Services Card */}
      <SummaryStatCard
        className="border-primary/10"
        label="Total Services"
        value={totalRecords}
      />

      {/* Total Cost Card */}
      <SummaryStatCard
        className="border-primary/10"
        label="Total Cost"
        value={`$${totalCost.toFixed(2)}`}
      />

      {/* Vehicles Serviced Card (only for general view) */}
      {!vehicleSpecific && records.length > 0 && (
        <SummaryStatCard
          className="border-primary/10"
          label="Vehicles Serviced"
          value={uniqueVehicleCount}
        />
      )}

      {/* Odometer Range Card (only for vehicle-specific view) */}
      {vehicleSpecific && records.length > 0 && (
        <SummaryStatCard
          className="border-primary/10"
          label="Odometer Range Covered"
          value={odometerRange.toLocaleString()}
        />
      )}

      {/* Date Range Card */}
      {records.length > 0 && (
        <SummaryStatCard
          className="border-primary/10"
          colSpan="col-span-2 sm:col-span-3"
          label="Date Range"
          value={`${oldestDate?.toLocaleDateString()} - ${newestDate?.toLocaleDateString()}`}
        />
      )}

      {/* Top Services Card */}
      {topServices.length > 0 && (
        <Card className="col-span-2 overflow-hidden border-primary/10 sm:col-span-3">
          <CardContent className="p-4">
            <h3 className="mb-2 text-sm font-semibold text-card-foreground">
              Top Services
            </h3>
            <div className="flex flex-wrap gap-2">
              {topServices.map(([service, count]) => (
                <Badge
                  aria-label={`${service}: ${count} services`}
                  className="px-2 py-1 text-xs"
                  key={service}
                  variant="secondary"
                >
                  {service} ({count})
                </Badge>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

/**
 * A card component for displaying a summary statistic
 */
function SummaryStatCard({
  className,
  colSpan,
  label,
  textColor = 'text-muted-foreground',
  value,
}: SummaryStatCardProps) {
  return (
    <Card className={cn('overflow-hidden', className, colSpan)}>
      <CardContent className="p-4 text-center">
        <p className="text-2xl font-semibold text-card-foreground">{value}</p>
        <p className={cn('text-sm', textColor)}>{label}</p>
      </CardContent>
    </Card>
  );
}
