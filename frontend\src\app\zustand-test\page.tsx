/**
 * @file Test page for Zustand store functionality
 * @module app/zustand-test/page
 */

'use client';

import { AlertCircle, CheckCircle, Info, TestTube } from 'lucide-react';
import { useTheme as useNextTheme } from 'next-themes';
import React from 'react';

import { ThemeDebug } from '@/components/debug/ThemeDebug';
import { ZustandShowcase } from '@/components/examples/ZustandExamples';
import { AppBreadcrumb } from '@/components/ui/app-breadcrumb';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useNotifications } from '@/hooks/ui/useNotifications';
import { useTheme } from '@/hooks/ui/useTheme';
import { useAppStore } from '@/lib/stores/zustand/appStore';
import { useUiStore } from '@/lib/stores/zustand/uiStore';

/**
 * Theme integration test component
 */
const ThemeIntegrationTest: React.FC = () => {
  const { currentTheme, setTheme: setZustandTheme } = useTheme();
  const { setTheme: setNextTheme, theme: nextTheme } = useNextTheme();
  const { showInfo, showSuccess } = useNotifications();

  const testThemeSync = () => {
    showInfo(
      'Testing theme synchronization between Next-themes and Zustand...'
    );

    // Test Zustand -> Next-themes sync
    setTimeout(() => {
      setZustandTheme('dark');
      showInfo('Set Zustand theme to dark');
    }, 1000);

    setTimeout(() => {
      setZustandTheme('light');
      showInfo('Set Zustand theme to light');
    }, 2000);

    setTimeout(() => {
      setNextTheme('dark');
      showInfo('Set Next-themes to dark');
    }, 3000);

    setTimeout(() => {
      showSuccess('Theme synchronization test completed!');
    }, 4000);
  };

  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          🎨 Theme Integration Test
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="rounded border p-3">
              <h4 className="font-medium">Zustand Theme</h4>
              <p className="text-sm text-muted-foreground">
                Current: {currentTheme}
              </p>
            </div>
            <div className="rounded border p-3">
              <h4 className="font-medium">Next-themes</h4>
              <p className="text-sm text-muted-foreground">
                Current: {nextTheme}
              </p>
            </div>
          </div>

          <div className="flex gap-2">
            <Button onClick={testThemeSync} variant="outline">
              Test Theme Sync
            </Button>
            <Button
              onClick={() => setZustandTheme('dark')}
              size="sm"
              variant="outline"
            >
              Zustand Dark
            </Button>
            <Button
              onClick={() => setZustandTheme('light')}
              size="sm"
              variant="outline"
            >
              Zustand Light
            </Button>
            <Button
              onClick={() => setNextTheme('system')}
              size="sm"
              variant="outline"
            >
              System Theme
            </Button>
          </div>

          <div className="text-xs text-muted-foreground">
            Both theme values should stay synchronized. Test by clicking buttons
            and checking values above.
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

/**
 * Test status component
 */
const TestStatus: React.FC = () => {
  const { currentTheme } = useAppStore();
  const { fontSize, notificationsEnabled } = useUiStore();
  const { unreadCount } = useNotifications();

  const tests = [
    {
      description: `Current theme: ${currentTheme}`,
      name: 'Theme Persistence',
      status: currentTheme ? 'pass' : 'fail',
    },
    {
      description: `Font size: ${fontSize}`,
      name: 'Font Size Setting',
      status: fontSize ? 'pass' : 'fail',
    },
    {
      description: `Unread notifications: ${unreadCount()}`,
      name: 'Notification System',
      status: typeof unreadCount === 'function' ? 'pass' : 'fail',
    },
    {
      description: `Notifications enabled: ${notificationsEnabled}`,
      name: 'UI Preferences',
      status: typeof notificationsEnabled === 'boolean' ? 'pass' : 'fail',
    },
  ];

  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <TestTube className="size-5" />
          Zustand Store Integration Tests
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          {tests.map((test, index) => (
            <div
              className="flex items-center justify-between rounded border p-3"
              key={index}
            >
              <div>
                <div className="flex items-center gap-2">
                  <span className="font-medium">{test.name}</span>
                  <Badge
                    variant={test.status === 'pass' ? 'default' : 'destructive'}
                  >
                    {test.status === 'pass' ? (
                      <CheckCircle className="mr-1 size-3" />
                    ) : (
                      <AlertCircle className="mr-1 size-3" />
                    )}
                    {test.status}
                  </Badge>
                </div>
                <p className="text-sm text-muted-foreground">
                  {test.description}
                </p>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

/**
 * Persistence test component
 */
const PersistenceTest: React.FC = () => {
  const { showInfo, showSuccess } = useNotifications();

  const testPersistence = () => {
    showInfo(
      'Refresh the page to test persistence. Theme and font size should be maintained.'
    );
  };

  const confirmPersistence = () => {
    showSuccess(
      'Persistence test passed! Your preferences were maintained after refresh.'
    );
  };

  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Info className="size-5" />
          Persistence Testing
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <p className="text-sm text-muted-foreground">
            Test that your theme and UI preferences persist across browser
            sessions.
          </p>
          <div className="flex gap-2">
            <Button onClick={testPersistence} variant="outline">
              Test Persistence
            </Button>
            <Button onClick={confirmPersistence} variant="default">
              Confirm Persistence Works
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

/**
 * Main test page component
 */
export default function ZustandTestPage() {
  return (
    <div className="container mx-auto py-8">
      <AppBreadcrumb />
      <div className="mb-8">
        <h1 className="mb-2 text-3xl font-bold">Zustand Store Testing</h1>
        <p className="text-muted-foreground">
          This page demonstrates and tests all Zustand store functionality in
          WorkHub. Use this page to verify that stores are working correctly and
          persistence is functioning.
        </p>
      </div>

      <ThemeDebug />
      <ThemeIntegrationTest />
      <TestStatus />
      <PersistenceTest />
      <ZustandShowcase />
    </div>
  );
}
