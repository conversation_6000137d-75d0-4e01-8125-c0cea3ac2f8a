import { supabaseAdmin } from '../lib/supabase.js';
import logger from '../utils/logger.js';
import { logGDPRAuditEvent } from '../utils/auditLogger.js';

export interface ComplianceReport {
  reportId: string;
  reportType: 'GDPR' | 'SOC2' | 'AUDIT_TRAIL' | 'DATA_ACCESS';
  generatedAt: string;
  generatedBy: string;
  period: {
    startDate: string;
    endDate: string;
  };
  summary: {
    totalEvents: number;
    dataAccessEvents: number;
    personalDataEvents: number;
    securityEvents: number;
    complianceViolations: number;
  };
  details: any;
  status: 'GENERATED' | 'REVIEWED' | 'APPROVED' | 'ARCHIVED';
}

export interface DataRetentionPolicy {
  id: string;
  name: string;
  description: string;
  dataType: string;
  retentionPeriodDays: number;
  autoDelete: boolean;
  gdprBasis: string;
  enabled: boolean;
}

export interface GDPRDataSubjectRequest {
  id: string;
  requestType: 'ACCESS' | 'RECTIFICATION' | 'ERASURE' | 'PORTABILITY' | 'RESTRICTION';
  dataSubjectId: string;
  requestedAt: string;
  requestedBy: string;
  status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'REJECTED';
  completedAt?: string;
  details: any;
}

/**
 * Compliance Service
 * Handles GDPR compliance, SOC 2 reporting, and data retention policies
 */
export class ComplianceService {
  private retentionPolicies: DataRetentionPolicy[] = [];

  constructor() {
    this.initializeDefaultRetentionPolicies();
  }

  /**
   * Generate GDPR compliance report
   */
  async generateGDPRReport(
    startDate: string,
    endDate: string,
    generatedBy: string
  ): Promise<ComplianceReport> {
    try {
      const reportId = `gdpr-${Date.now()}`;

      // Get audit logs for the period
      const { data: auditLogs, error } = await supabaseAdmin
        .from('audit_logs')
        .select('*')
        .gte('created_at', startDate)
        .lte('created_at', endDate);

      if (error) {
        throw new Error(`Failed to fetch audit logs: ${error.message}`);
      }

      // Analyze logs for GDPR compliance
      const personalDataEvents = auditLogs.filter(log => 
        log.details?.personalDataInvolved === true
      );

      const dataAccessEvents = auditLogs.filter(log => 
        log.action.includes('DATA_ACCESS') || log.action.includes('API_')
      );

      const securityEvents = auditLogs.filter(log => 
        log.action.includes('SECURITY') || log.action.includes('AUTH')
      );

      // Check for compliance violations
      const complianceViolations = this.detectComplianceViolations(auditLogs);

      const report: ComplianceReport = {
        reportId,
        reportType: 'GDPR',
        generatedAt: new Date().toISOString(),
        generatedBy,
        period: { startDate, endDate },
        summary: {
          totalEvents: auditLogs.length,
          dataAccessEvents: dataAccessEvents.length,
          personalDataEvents: personalDataEvents.length,
          securityEvents: securityEvents.length,
          complianceViolations: complianceViolations.length,
        },
        details: {
          personalDataEvents,
          complianceViolations,
          dataProcessingActivities: this.analyzeDataProcessingActivities(auditLogs),
          lawfulBasisAnalysis: this.analyzeLawfulBasis(personalDataEvents),
          retentionCompliance: await this.checkRetentionCompliance(),
        },
        status: 'GENERATED',
      };

      // Log report generation
      logGDPRAuditEvent({
        eventType: 'COMPLIANCE',
        action: 'GDPR_REPORT_GENERATED',
        userId: generatedBy,
        outcome: 'SUCCESS',
        message: `GDPR compliance report generated for period ${startDate} to ${endDate}`,
        details: {
          reportId,
          period: { startDate, endDate },
          summary: report.summary,
        },
        dataClassification: 'RESTRICTED',
        riskLevel: 'MEDIUM',
      });

      return report;
    } catch (error: any) {
      logger.error('Failed to generate GDPR report:', error);
      throw error;
    }
  }

  /**
   * Generate SOC 2 compliance report
   */
  async generateSOC2Report(
    startDate: string,
    endDate: string,
    generatedBy: string
  ): Promise<ComplianceReport> {
    try {
      const reportId = `soc2-${Date.now()}`;

      // Get audit logs for the period
      const { data: auditLogs, error } = await supabaseAdmin
        .from('audit_logs')
        .select('*')
        .gte('created_at', startDate)
        .lte('created_at', endDate);

      if (error) {
        throw new Error(`Failed to fetch audit logs: ${error.message}`);
      }

      // SOC 2 Trust Service Criteria analysis
      const securityEvents = auditLogs.filter(log => 
        log.action.includes('SECURITY') || log.action.includes('AUTH')
      );

      const availabilityEvents = auditLogs.filter(log => 
        log.action.includes('SYSTEM') || log.action.includes('HEALTH')
      );

      const processingIntegrityEvents = auditLogs.filter(log => 
        log.action.includes('DATA_') || log.action.includes('VALIDATION')
      );

      const confidentialityEvents = auditLogs.filter(log => 
        log.details?.dataClassification === 'CONFIDENTIAL' || 
        log.details?.dataClassification === 'RESTRICTED'
      );

      const report: ComplianceReport = {
        reportId,
        reportType: 'SOC2',
        generatedAt: new Date().toISOString(),
        generatedBy,
        period: { startDate, endDate },
        summary: {
          totalEvents: auditLogs.length,
          dataAccessEvents: processingIntegrityEvents.length,
          personalDataEvents: confidentialityEvents.length,
          securityEvents: securityEvents.length,
          complianceViolations: 0, // SOC 2 violations would be calculated differently
        },
        details: {
          trustServiceCriteria: {
            security: this.analyzeSecurityCriteria(securityEvents),
            availability: this.analyzeAvailabilityCriteria(availabilityEvents),
            processingIntegrity: this.analyzeProcessingIntegrityCriteria(processingIntegrityEvents),
            confidentiality: this.analyzeConfidentialityCriteria(confidentialityEvents),
          },
          controlsEffectiveness: this.assessControlsEffectiveness(auditLogs),
          incidentResponse: this.analyzeIncidentResponse(auditLogs),
        },
        status: 'GENERATED',
      };

      // Log report generation
      logGDPRAuditEvent({
        eventType: 'COMPLIANCE',
        action: 'SOC2_REPORT_GENERATED',
        userId: generatedBy,
        outcome: 'SUCCESS',
        message: `SOC 2 compliance report generated for period ${startDate} to ${endDate}`,
        details: {
          reportId,
          period: { startDate, endDate },
          summary: report.summary,
        },
        dataClassification: 'RESTRICTED',
        riskLevel: 'MEDIUM',
      });

      return report;
    } catch (error: any) {
      logger.error('Failed to generate SOC 2 report:', error);
      throw error;
    }
  }

  /**
   * Process GDPR data subject request
   */
  async processDataSubjectRequest(
    requestType: GDPRDataSubjectRequest['requestType'],
    dataSubjectId: string,
    requestedBy: string,
    details: any
  ): Promise<GDPRDataSubjectRequest> {
    try {
      const request: GDPRDataSubjectRequest = {
        id: `dsr-${Date.now()}`,
        requestType,
        dataSubjectId,
        requestedAt: new Date().toISOString(),
        requestedBy,
        status: 'PENDING',
        details,
      };

      // Log the data subject request
      logGDPRAuditEvent({
        eventType: 'COMPLIANCE',
        action: `GDPR_${requestType}_REQUEST`,
        userId: requestedBy,
        dataSubjectId,
        outcome: 'SUCCESS',
        message: `GDPR ${requestType} request initiated for data subject ${dataSubjectId}`,
        details: request,
        dataClassification: 'RESTRICTED',
        personalDataInvolved: true,
        gdprLawfulBasis: 'legal_obligation',
        riskLevel: 'HIGH',
      });

      return request;
    } catch (error: any) {
      logger.error('Failed to process data subject request:', error);
      throw error;
    }
  }

  /**
   * Check data retention compliance
   */
  async checkRetentionCompliance(): Promise<any> {
    try {
      const retentionIssues = [];

      for (const policy of this.retentionPolicies) {
        if (!policy.enabled) continue;

        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - policy.retentionPeriodDays);

        // Check audit logs that exceed retention period
        const { data: expiredLogs, error } = await supabaseAdmin
          .from('audit_logs')
          .select('id, created_at')
          .lt('created_at', cutoffDate.toISOString())
          .limit(100);

        if (error) {
          logger.error(`Failed to check retention for policy ${policy.id}:`, error);
          continue;
        }

        if (expiredLogs && expiredLogs.length > 0) {
          retentionIssues.push({
            policyId: policy.id,
            policyName: policy.name,
            expiredRecords: expiredLogs.length,
            oldestRecord: expiredLogs[0]?.created_at,
            autoDelete: policy.autoDelete,
          });

          // Auto-delete if enabled
          if (policy.autoDelete) {
            await this.deleteExpiredAuditLogs(policy, cutoffDate);
          }
        }
      }

      return {
        retentionPolicies: this.retentionPolicies,
        retentionIssues,
        lastChecked: new Date().toISOString(),
      };
    } catch (error: any) {
      logger.error('Failed to check retention compliance:', error);
      return { error: error.message };
    }
  }

  /**
   * Initialize default data retention policies
   */
  private initializeDefaultRetentionPolicies(): void {
    this.retentionPolicies = [
      {
        id: 'audit-logs-general',
        name: 'General Audit Logs',
        description: 'Standard audit logs retention',
        dataType: 'audit_logs',
        retentionPeriodDays: 2555, // 7 years
        autoDelete: false,
        gdprBasis: 'legitimate_interests',
        enabled: true,
      },
      {
        id: 'security-events',
        name: 'Security Events',
        description: 'Security-related audit logs',
        dataType: 'security_audit_logs',
        retentionPeriodDays: 3650, // 10 years
        autoDelete: false,
        gdprBasis: 'legal_obligation',
        enabled: true,
      },
      {
        id: 'personal-data-access',
        name: 'Personal Data Access Logs',
        description: 'Logs involving personal data access',
        dataType: 'personal_data_logs',
        retentionPeriodDays: 2190, // 6 years
        autoDelete: false,
        gdprBasis: 'legal_obligation',
        enabled: true,
      },
    ];
  }

  private detectComplianceViolations(auditLogs: any[]): any[] {
    // Implementation would detect various compliance violations
    return [];
  }

  private analyzeDataProcessingActivities(auditLogs: any[]): any {
    // Implementation would analyze data processing activities
    return {};
  }

  private analyzeLawfulBasis(personalDataEvents: any[]): any {
    // Implementation would analyze lawful basis for processing
    return {};
  }

  private analyzeSecurityCriteria(securityEvents: any[]): any {
    // Implementation would analyze security criteria for SOC 2
    return {};
  }

  private analyzeAvailabilityCriteria(availabilityEvents: any[]): any {
    // Implementation would analyze availability criteria
    return {};
  }

  private analyzeProcessingIntegrityCriteria(events: any[]): any {
    // Implementation would analyze processing integrity
    return {};
  }

  private analyzeConfidentialityCriteria(events: any[]): any {
    // Implementation would analyze confidentiality criteria
    return {};
  }

  private assessControlsEffectiveness(auditLogs: any[]): any {
    // Implementation would assess controls effectiveness
    return {};
  }

  private analyzeIncidentResponse(auditLogs: any[]): any {
    // Implementation would analyze incident response
    return {};
  }

  private async deleteExpiredAuditLogs(policy: DataRetentionPolicy, cutoffDate: Date): Promise<void> {
    // Implementation would delete expired audit logs
    logger.info(`Auto-deleting expired audit logs for policy: ${policy.name}`);
  }
}

export const complianceService = new ComplianceService();
