/**
 * @file Flight Service - Backward Compatibility Export
 * @module api/services/flight.service
 *
 * This file provides backward compatibility for imports that expect
 * flight.service.ts instead of the new external/flightApi.ts structure
 */

// Re-export everything from the flight API modules
export * from './external/flightApi';
export * from './external/flightDetailsApi';

// Ensure all the commonly used exports are available
export {
  FlightApiService,
  type FlightData,
  type FlightSearchResponse,
} from './external/flightApi';

export { FlightDetailsApiService } from './external/flightDetailsApi';

// Import FlightData type for the function signature
import type { FlightData } from './external/flightApi';

// Re-export the searchFlightsByCallsignAndDate function specifically
export const searchFlightsByCallsignAndDate = async (
  callsign: string,
  date: string
): Promise<FlightData[]> => {
  // This is a temporary implementation for backward compatibility
  // In a real implementation, you would create a service instance
  throw new Error(
    'Please use FlightApiService instance instead of direct function call'
  );
};
