// Import required types from domain layer
import type {
  DelegationStatusPrisma,
  TaskPriorityPrisma,
  TaskStatusPrisma,
} from '@/lib/types/domain';

import type { ServiceStatusPrisma, ServiceTypePrisma } from './vehicleService';

export interface AvailabilityMetrics {
  availableHours: number;
  employeeId: number;
  employeeName: string;
  overtimeHours: number;
  scheduledHours: number;
  utilizationRate: number;
}

// Base interfaces for extensibility (Open/Closed Principle)
export interface BaseChartProps {
  error?: string;
  height?: number;
  interactive?: boolean;
  loading?: boolean;
}

export interface BaseWidgetProps {
  actions?: React.ReactNode;
  description?: string;
  error?: string | undefined;
  loading?: boolean;
  title: string;
}

// Cross-Entity Analytics Extensions
export interface CrossEntityAnalytics {
  correlations: {
    employeeVehicle?: {
      correlation: number;
      employeeName: string;
      employeePerformance: number;
      vehicleName: string;
      vehicleUtilization: number;
    }[];
    overall?: {
      correlation: number;
      entityName: string;
      xValue: number;
      yValue: number;
    }[];
    performanceWorkload?: {
      correlation: number;
      employeeName: string;
      performanceScore: number;
      workloadPercentage: number;
    }[];
    taskDelegation?: {
      correlation: number;
      delegationSuccess: number;
      taskComplexity: number;
      taskType: string;
    }[];
  };
  insights?: {
    description: string;
    title: string;
    type: 'negative' | 'neutral' | 'positive';
  }[];
  metrics: {
    employeeVehicle: number;
    overallEfficiency: number;
    performanceWorkload: number;
    taskDelegation: number;
  };
  network?: {
    edges: {
      from: string;
      to: string;
      type: string;
      weight: number;
    }[];
    nodes: {
      connectionCount: number;
      id: string;
      name: string;
      strength: number;
      type: string;
    }[];
  };
}

export interface CrossEntityCorrelations {
  costEfficiencyCorrelation: number;
  delegationTaskCorrelation: number;
  employeePerformanceCorrelation: number;
  vehicleUtilizationCorrelation: number;
}

export interface CrossEntitySummary {
  activeEntities: number;
  efficiencyRating: number;
  performanceScore: number;
  recommendations: string[];
  totalEntities: number;
}

// Service interfaces for Dependency Inversion
/**
 * Represents a single delegation record.
 */
export interface Delegation {
  assignedEmployee: string;
  completedAt: null | string; // ISO date string
  createdAt: string; // ISO date string
  customerName: string;
  delegationId: string;
  id: number;
  licensePlate: string;
  location: string;
  status: DelegationStatusPrisma;
  vehicleModel: string;
}

// Single Responsibility - Analytics data structure
export interface DelegationAnalytics {
  // Optional extended data
  delegations?: Delegation[];
  locationMetrics: LocationMetrics[];
  serviceCosts?: ServiceCostSummary;
  serviceHistory?: ServiceHistoryData;
  statusDistribution: StatusDistributionData[];
  summary: SummaryMetrics;
  taskData?: TaskAnalyticsData;
  totalCount: number;
  trendData: TrendData[];
}

export interface DelegationHistoryData {
  averageDuration: number;
  completedDelegations: number;
  employeeId: number;
  employeeName: string;
  successRate: number;
  totalDelegations: number;
}

// Delegation report data for tables
export interface DelegationReportData {
  actualDuration?: number;
  assignedEmployee: string;
  completedAt: null | string;
  createdAt: string;
  customerName: string;
  delegationId: string;
  estimatedDuration?: number;
  id: string; // Changed to string to match ReportingTableData
  licensePlate: string;
  location: string;
  priority?: TaskPriorityPrisma;
  status: DelegationStatusPrisma;
  vehicleModel: string;
}

export interface EmployeeAnalytics {
  availabilityMetrics: AvailabilityMetrics[];
  delegationHistory: DelegationHistoryData[];
  performanceMetrics: EmployeePerformanceData[];
  taskAssignments: TaskAssignmentData[];
  totalCount: number;
  workloadDistribution?: WorkloadDistributionData[];
}

export interface EmployeePerformanceData {
  averageRating: number;
  completedDelegations: number;
  completedTasks: number;
  employeeId: number;
  employeeName: string;
  onTimePerformance: number;
  workloadScore: number;
}

// Export options interface
export interface ExportOptions {
  customHeaders?: Record<string, string>;
  dateFormat?: string;
  filename?: string;
  format?: 'csv' | 'excel' | 'pdf' | 'png';
  includeCharts?: boolean;
  includeTimestamp?: boolean;
  orientation?: 'landscape' | 'portrait';
}

// PHASE 1 ENHANCEMENT: Extended service interface following ISP
export interface IReportingDataService {
  getCrossEntityAnalytics(
    filters: ReportingFilters
  ): Promise<CrossEntityAnalytics>;
  // Existing methods (maintain backward compatibility)
  getDelegationAnalytics(
    filters: ReportingFilters
  ): Promise<DelegationAnalytics>;
  getDelegations(
    filters: ReportingFilters,
    pagination: { page: number; pageSize: number }
  ): Promise<PaginatedDelegationsResponse>;
  getEmployeeAnalytics(filters: ReportingFilters): Promise<EmployeeAnalytics>;
  getLocationMetrics(filters: ReportingFilters): Promise<LocationMetrics[]>;

  // PHASE 1: New entity analytics methods
  getTaskAnalytics(filters: ReportingFilters): Promise<TaskAnalytics>;
  getTaskMetrics(delegationIds?: string[]): Promise<TaskMetrics>;
  getTrendData(filters: ReportingFilters): Promise<TrendData[]>;
  getVehicleAnalytics(filters: ReportingFilters): Promise<VehicleAnalytics>;
}

// PHASE 1 ENHANCEMENT: Cross-entity analytics interface (extended below)

// Location metrics
export interface LocationMetrics {
  averageDuration: number;
  completionRate?: number; // Optional completion rate percentage
  delegationCount: number;
  location: string;
}

export interface MaintenanceScheduleData {
  estimatedCost: number;
  maintenanceType: ServiceTypePrisma;
  nextMaintenanceDate: string;
  priority: 'Critical' | 'High' | 'Low' | 'Medium';
  vehicleId: number;
  vehicleName: string;
}

/**
 * Represents the structure of a paginated API response for delegations.
 */
export interface PaginatedDelegationsResponse {
  data: Delegation[];
  meta: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
}

// Raw data types for API responses (used in transformers)
export interface RawDelegationAnalytics {
  delegations?: any[];
  locationMetrics: any[];
  serviceCosts?: any[];
  serviceHistory?: any[];
  statusDistribution: any[];
  summary: any;
  taskData?: any[];
  totalCount: number;
  trendData: any[];
}

export interface RawLocationMetrics {
  averageDuration: number;
  completionRate?: number;
  count?: number;
  delegationCount: number;
  delegationsCount?: number;
  location: string;
  name?: string;
}

export interface RawServiceCostSummary {
  averageCost: number;
  // Additional properties that might be present in raw data
  averageCostPerService?: number;
  costByType: any[];
  costTrend: any[];
  monthlyTrend?: any[];
  totalCost: number;
}

export interface RawServiceHistoryData {
  completedDate?: string;
  cost: number;
  description: string;
  id: string;
  relatedDelegationId?: string;
  relatedTaskId?: string;
  scheduledDate: string;
  serviceType: any;
  status: any;
  vehicleId: number;
  vehicleName: string;
}

export interface RawTaskAnalyticsData {
  averageCompletionTime?: number;
  completedTasks?: number;
  metrics: RawTaskMetrics;
  overdueTasks?: number;
  pendingTasks?: number;
  priorityDistribution: any[];
  tasksByPriority?: any[];
  // Additional properties that might be present in raw data
  totalTasks?: number;
  trendData: any[];
}

export interface RawTaskMetrics {
  averageCompletionTime: number;
  completedTasks: number;
  inProgressTasks: number;
  overdueTasks?: number;
  pendingTasks: number;
  tasksByPriority: any[];
  tasksByStatus: any[];
  totalTasks: number;
}

export interface RawTrendData {
  cancelled?: number;
  completed: number;
  created: number;
  date: string;
  inProgress: number;
}

// Interface Segregation - Separate interfaces for different concerns
// PHASE 1 ENHANCEMENT: Extended ReportingFilters for comprehensive entity support
export interface ReportingFilters {
  costRange?: {
    max: number;
    min: number;
  };
  dateRange: {
    from: Date;
    to: Date;
  };
  employees: number[];
  includeCrossEntityCorrelations?: boolean;
  includeEmployeeMetrics?: boolean;

  // PHASE 1: Cross-entity options
  includeServiceHistory?: boolean;
  includeTaskData?: boolean;
  includeVehicleAnalytics?: boolean;
  locations: string[];
  maxCost?: number;

  minCost?: number;
  serviceStatus?: ServiceStatusPrisma[];
  serviceTypes?: ServiceTypePrisma[];
  status: DelegationStatusPrisma[];
  taskPriority?: TaskPriorityPrisma[];
  // PHASE 1: Additional entity filters (optional for backward compatibility)
  taskStatus?: TaskStatusPrisma[];
  vehicles: number[];
}

// Report Type Management Types
export interface ReportType {
  category: string;
  createdAt?: string;
  createdBy?: string;
  dataSource: string;
  description?: string;
  filters?: string[];
  id: string;
  isActive: boolean;
  isPublic: boolean;
  name: string;
  refreshInterval?: number;
  tags?: string[];
  updatedAt?: string;
  widgetConfigs?: WidgetConfig[];
  widgets: string[];
}

// Service cost summary
export interface ServiceCostSummary {
  averageCostPerService: number;
  costByType: {
    cost: number;
    count: number;
    type: ServiceTypePrisma;
  }[];
  monthlyTrend: {
    cost: number;
    month: string;
  }[];
  totalCost: number;
}

// Service history interfaces
export interface ServiceHistoryData {
  completedDate?: string;
  cost: number;
  description: string;
  id: string;
  relatedDelegationId?: string;
  relatedTaskId?: string;
  scheduledDate: string;
  serviceType: ServiceTypePrisma;
  status: ServiceStatusPrisma;
  vehicleId: number;
  vehicleName: string;
}

// Focused interface for status data
export interface StatusDistributionData {
  color: string;
  count: number;
  percentage: number;
  status: DelegationStatusPrisma;
}

// Summary metrics interface
export interface SummaryMetrics {
  activeDelegations: number;
  averageDuration: number;
  completedDelegations: number;
  completionRate: number;
  totalDelegates: number;
  totalDelegations: number;
}

// PHASE 1 ENHANCEMENT: New entity analytics interfaces following SRP
export interface TaskAnalytics {
  assignmentMetrics: TaskAssignmentMetrics[];
  averageCompletionTime: number;
  completionRate: number;
  overdueCount: number;
  priorityDistribution: TaskPriorityDistributionData[];
  statusDistribution: TaskStatusDistributionData[];
  totalCount: number;
  trendData?: TaskTrendData[];
}

// Task analytics data
export interface TaskAnalyticsData {
  averageCompletionTime: number;
  completedTasks: number;
  overdueTasks: number;
  pendingTasks: number;
  tasksByPriority: {
    count: number;
    priority: TaskPriorityPrisma;
  }[];
  totalTasks: number;
}

export interface TaskAssignmentData {
  assignedTasks: number;
  completedTasks: number;
  employeeId: number;
  employeeName: string;
  overdueTasksCount: number;
  pendingTasks: number;
}

export interface TaskAssignmentMetrics {
  assignedTasks: number;
  averageCompletionTime: number;
  completedTasks: number;
  completionRate: number;
  employeeId: number;
  employeeName: string;
}

// Task-related interfaces - ENHANCED for Phase 1
export interface TaskMetrics {
  averageCompletionTime: number;
  completedTasks: number;
  inProgressTasks: number;
  overdueTasks?: number; // Optional overdue tasks count
  pendingTasks: number;
  tasksByPriority: {
    count: number;
    priority: TaskPriorityPrisma;
  }[];
  tasksByStatus: {
    count: number;
    status: TaskStatusPrisma;
  }[];
  totalTasks: number;
}

export interface TaskPriorityDistributionData {
  color: string;
  count: number;
  percentage: number;
  priority: TaskPriorityPrisma;
}

// Supporting interfaces for new analytics
export interface TaskStatusDistributionData {
  color: string;
  count: number;
  percentage: number;
  status: TaskStatusPrisma;
}

export interface TaskTrendData {
  completed: number;
  created: number;
  date: string;
  inProgress: number;
  overdue: number;
}

// Time-series data interface
export interface TrendData {
  completed: number;
  created: number;
  date: string;
  inProgress: number;
}

export interface VehicleAnalytics {
  costAnalysis: ServiceCostSummary;
  maintenanceSchedule: MaintenanceScheduleData[];
  performanceMetrics?: VehiclePerformanceData[];
  serviceHistory: ServiceHistoryData[];
  totalCount: number;
  utilizationMetrics: VehicleUtilizationData[];
}

export interface VehiclePerformanceData {
  downtime: number;
  fuelEfficiency: number;
  maintenanceCost: number;
  reliabilityScore: number;
  vehicleId: number;
  vehicleName: string;
}

export type { ServiceStatusPrisma, ServiceTypePrisma } from './vehicleService';

// Re-export enum types for convenience
export type {
  DelegationStatusPrisma,
  TaskPriorityPrisma,
  TaskStatusPrisma,
} from '@/lib/types/domain';

export interface VehicleUtilizationData {
  activeDelegations: number;
  maintenanceHours: number;
  totalDelegations: number;
  utilizationRate: number;
  vehicleId: number;
  vehicleName: string;
}

export interface WidgetConfig {
  config?: Record<string, any>;
  id: string;
  position: number;
  span: string;
  title: string;
  type: string;
}

export interface WorkloadDistributionData {
  capacity: number;
  currentWorkload: number;
  employeeId: number;
  employeeName: string;
  status: 'Optimal' | 'Overloaded' | 'Underutilized';
  workloadPercentage: number;
}
