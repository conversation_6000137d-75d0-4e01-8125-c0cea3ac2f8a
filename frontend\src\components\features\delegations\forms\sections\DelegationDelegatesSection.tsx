/**
 * DelegationDelegatesSection Component - Enhanced SOLID Principles Implementation
 *
 * Single Responsibility: Handles delegates field array management
 * - Dynamic delegate fields with add/remove functionality
 * - Enhanced UX with better visual organization and validation
 * - Follows SRP by focusing only on delegates management
 *
 * @module DelegationDelegatesSection
 */

import React from 'react';
import { useFieldArray, useFormContext } from 'react-hook-form';
import {
  Users,
  Trash2,
  PlusCircle,
  CheckCircle,
  AlertCircle,
  Info,
  User,
} from 'lucide-react';

import type { DelegationFormData } from '@/lib/schemas/delegationSchemas';
import { useDelegationValidation } from '@/hooks/forms/useDelegationValidation';

import { ActionButton } from '@/components/ui/action-button';
import { ValidatedField } from '@/components/ui/forms/ValidatedField';
import { FormField } from '@/components/ui/forms/formField';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

// ============================================================================
// INTERFACES
// ============================================================================

export interface DelegationDelegatesSectionProps {
  /** Whether the form is currently submitting */
  isSubmitting?: boolean;
  /** Additional CSS classes */
  className?: string;
  /** Minimum number of delegates required */
  minDelegates?: number;
  /** Maximum number of delegates allowed */
  maxDelegates?: number;
  /** User's role for permission-based UI */
  userRole?: string;
}

// ============================================================================
// HELPER FUNCTIONS
// ============================================================================

/**
 * Check if delegate is complete
 */
const isDelegateComplete = (delegate: any): boolean => {
  return !!(delegate?.name?.trim() && delegate?.title?.trim());
};

/**
 * Get delegate completion status
 */
const getDelegateStatus = (
  delegate: any
): 'complete' | 'incomplete' | 'empty' => {
  if (!delegate?.name?.trim() && !delegate?.title?.trim()) return 'empty';
  if (delegate?.name?.trim() && delegate?.title?.trim()) return 'complete';
  return 'incomplete';
};

// ============================================================================
// COMPONENT IMPLEMENTATION
// ============================================================================

/**
 * Enhanced Delegates Section for Delegation Form
 *
 * Provides improved user experience with:
 * - Clear visual organization for each delegate
 * - Real-time validation feedback
 * - Smart add/remove controls with limits
 * - Progress indicators for delegate completion
 * - Helpful guidance for users
 */
export const DelegationDelegatesSection: React.FC<
  DelegationDelegatesSectionProps
> = ({
  isSubmitting = false,
  className = '',
  minDelegates = 1,
  maxDelegates = 10,
  userRole = 'user',
}) => {
  const {
    control,
    watch,
    formState: { errors },
  } = useFormContext<DelegationFormData>();

  const { getSectionValidationState } = useDelegationValidation();

  const {
    fields: delegateFields,
    append: appendDelegate,
    remove: removeDelegate,
  } = useFieldArray({
    control,
    name: 'delegates',
  });

  const watchedDelegates = watch('delegates');
  const completedDelegates =
    watchedDelegates?.filter(delegate => isDelegateComplete(delegate)) || [];
  const hasMinimumDelegates = completedDelegates.length >= minDelegates;

  const handleAddDelegate = () => {
    if (delegateFields.length < maxDelegates) {
      appendDelegate({ name: '', notes: '', title: '' });
    }
  };

  const handleRemoveDelegate = (index: number) => {
    if (delegateFields.length > minDelegates) {
      removeDelegate(index);
    }
  };

  const canAddDelegate = delegateFields.length < maxDelegates;
  const canRemoveDelegate = delegateFields.length > minDelegates;

  // Calculate section completion
  const sectionProgress = Math.round(
    (completedDelegates.length / Math.max(delegateFields.length, 1)) * 100
  );

  return (
    <section className={`space-y-6 rounded-lg border bg-card p-6 ${className}`}>
      {/* Section Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="flex items-center justify-center w-10 h-10 rounded-lg bg-accent/10">
            <Users className="h-5 w-5 text-accent" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-foreground">Delegates</h3>
            <p className="text-sm text-muted-foreground">
              People receiving delegated responsibilities and authority
            </p>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Badge variant={hasMinimumDelegates ? 'default' : 'secondary'}>
            {completedDelegates.length} of {delegateFields.length} complete
          </Badge>
          {hasMinimumDelegates && (
            <Badge variant="default" className="flex items-center gap-1">
              <CheckCircle className="h-3 w-3" />
              Valid
            </Badge>
          )}
        </div>
      </div>

      {/* Progress and Guidelines */}
      <div className="space-y-4">
        {/* Progress Indicator */}
        <div className="flex items-center justify-between text-sm">
          <span className="text-muted-foreground">Section Progress</span>
          <span className="font-medium">{sectionProgress}%</span>
        </div>

        {/* Guidelines */}
        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription>
            <div className="space-y-2">
              <div className="font-medium">Delegate Guidelines</div>
              <ul className="text-sm space-y-1">
                <li>
                  • At least {minDelegates} delegate
                  {minDelegates > 1 ? 's are' : ' is'} required
                </li>
                <li>
                  • Include full name and official title for each delegate
                </li>
                <li>
                  • Add notes for special responsibilities or requirements
                </li>
                <li>
                  • Maximum {maxDelegates} delegates allowed per delegation
                </li>
              </ul>
            </div>
          </AlertDescription>
        </Alert>
      </div>

      {/* Delegates List */}
      <div className="space-y-4">
        {delegateFields.map((field, index) => {
          const delegateStatus = getDelegateStatus(watchedDelegates?.[index]);
          const hasErrors = errors.delegates?.[index];

          return (
            <Card
              key={field.id}
              className={`transition-colors ${
                delegateStatus === 'complete'
                  ? 'border-green-200 bg-background'
                  : delegateStatus === 'incomplete'
                    ? 'border-amber-200 bg-background'
                    : 'border-border bg-background'
              }`}
            >
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div
                      className={`flex items-center justify-center w-8 h-8 rounded-lg ${
                        delegateStatus === 'complete'
                          ? 'bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400'
                          : delegateStatus === 'incomplete'
                            ? 'bg-amber-100 text-amber-700 dark:bg-amber-900/20 dark:text-amber-400'
                            : 'bg-muted text-muted-foreground'
                      }`}
                    >
                      <User className="h-4 w-4" />
                    </div>
                    <div>
                      <h4 className="font-medium text-sm">
                        Delegate {index + 1}
                        {delegateStatus === 'complete' &&
                          watchedDelegates?.[index]?.name &&
                          ` - ${watchedDelegates[index].name}`}
                      </h4>
                      <p className="text-xs text-muted-foreground">
                        {delegateStatus === 'complete' && 'Complete'}
                        {delegateStatus === 'incomplete' && 'Needs attention'}
                        {delegateStatus === 'empty' && 'Not started'}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    {delegateStatus === 'complete' && (
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    )}
                    {delegateStatus === 'incomplete' && (
                      <AlertCircle className="h-4 w-4 text-amber-600" />
                    )}
                    {canRemoveDelegate && (
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => handleRemoveDelegate(index)}
                        disabled={isSubmitting}
                        className="h-8 w-8 p-0 text-destructive hover:text-destructive hover:bg-destructive/10"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>
              </CardHeader>

              <CardContent className="space-y-4 pt-0">
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <div className="relative">
                    <FormField
                      label="Full Name"
                      name={`delegates.${index}.name`}
                      disabled={isSubmitting}
                      placeholder="e.g., Dr. Sarah Johnson"
                      description="Complete legal name"
                    />
                    {watchedDelegates?.[index]?.name &&
                      watchedDelegates[index].name.length > 0 && (
                        <CheckCircle className="absolute top-8 right-3 h-4 w-4 text-green-500" />
                      )}
                    {errors.delegates?.[index]?.name && (
                      <div className="absolute top-8 right-3">
                        <AlertCircle className="h-4 w-4 text-red-500" />
                      </div>
                    )}
                  </div>

                  <div className="relative">
                    <FormField
                      label="Official Title"
                      name={`delegates.${index}.title`}
                      disabled={isSubmitting}
                      placeholder="e.g., Director of Operations"
                      description="Current position or role"
                    />
                    {watchedDelegates?.[index]?.title &&
                      watchedDelegates[index].title.length > 0 && (
                        <CheckCircle className="absolute top-8 right-3 h-4 w-4 text-green-500" />
                      )}
                    {errors.delegates?.[index]?.title && (
                      <div className="absolute top-8 right-3">
                        <AlertCircle className="h-4 w-4 text-red-500" />
                      </div>
                    )}
                  </div>
                </div>

                <FormField
                  label="Special Notes or Requirements"
                  name={`delegates.${index}.notes`}
                  disabled={isSubmitting}
                  placeholder="Optional: Special responsibilities, dietary requirements, accessibility needs..."
                  description="Any additional information relevant to this delegate"
                  type="textarea"
                  rows={2}
                />

                {hasErrors && (
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      Please complete the required fields for this delegate.
                    </AlertDescription>
                  </Alert>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Add Delegate Button */}
      <div className="flex items-center justify-between pt-4 border-t">
        <div className="text-sm text-muted-foreground">
          {delegateFields.length === 0 && 'No delegates added yet'}
          {delegateFields.length === 1 && '1 delegate'}
          {delegateFields.length > 1 && `${delegateFields.length} delegates`}
          {delegateFields.length > 0 &&
            ` • ${completedDelegates.length} completed`}
        </div>

        <Button
          type="button"
          variant="outline"
          onClick={handleAddDelegate}
          disabled={!canAddDelegate || isSubmitting}
          className="flex items-center gap-2"
        >
          <PlusCircle className="h-4 w-4" />
          Add Delegate
          {!canAddDelegate && ` (Max ${maxDelegates})`}
        </Button>
      </div>

      {/* Validation Feedback */}
      {!hasMinimumDelegates && delegateFields.length > 0 && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            At least {minDelegates} completed delegate
            {minDelegates > 1 ? 's are' : ' is'} required. Please complete the
            name and title for all delegates.
          </AlertDescription>
        </Alert>
      )}

      {/* Success Message */}
      {hasMinimumDelegates &&
        completedDelegates.length === delegateFields.length && (
          <Alert>
            <CheckCircle className="h-4 w-4 text-green-600" />
            <AlertDescription className="text-green-700">
              All delegates are properly configured. You can proceed to the next
              section or add more delegates if needed.
            </AlertDescription>
          </Alert>
        )}
    </section>
  );
};

export default DelegationDelegatesSection;
