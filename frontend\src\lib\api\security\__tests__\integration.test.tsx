/**
 * @file Integration Tests
 * @module api/security/__tests__/integration.test
 *
 * Phase 5: Testing & Validation
 * Integration tests for the complete secure API architecture
 */

import React from 'react';
import { renderHook, act } from '@testing-library/react';
import { SecurityConfigProvider } from '../providers/SecurityConfigProvider';
import { useSecureApiClient } from '../hooks/useSecureApiClient';
import { createSecureApiClient } from '../secureApiClient';
import { SECURITY_CONSTANTS } from '../../../security';

// Mock fetch for integration tests
global.fetch = jest.fn();

// Mock AuthContext
jest.mock('../../../../contexts/AuthContext', () => ({
  useAuthContext: () => ({
    session: {
      access_token: 'mock-token',
    },
    user: {
      id: '123',
      email: '<EMAIL>',
    },
    loading: false,
    signOut: jest.fn(),
  }),
}));

// Mock security hooks
jest.mock('../hooks/useCSRFProtection', () => ({
  useCSRFProtection: () => ({
    attachCSRF: jest.fn(config => ({
      ...config,
      headers: { ...config.headers, 'X-CSRF-Token': 'mock-csrf-token' },
    })),
  }),
}));

jest.mock('../hooks/useTokenManagement', () => ({
  useTokenManagement: () => ({
    isTokenValid: true,
    isTokenExpired: false,
    refreshToken: jest.fn().mockResolvedValue(true),
    updateActivity: jest.fn(),
  }),
}));

jest.mock('../hooks/useInputValidation', () => ({
  useInputValidation: () => ({
    sanitizeInput: jest.fn(input => {
      if (typeof input === 'string') {
        return input.replace(/<script.*?<\/script>/gi, '');
      }
      return input;
    }),
  }),
}));

jest.mock('../hooks/useSessionSecurity', () => ({
  useSessionSecurity: () => ({
    isSessionActive: true,
    clearSession: jest.fn(),
    updateActivity: jest.fn(),
  }),
}));

describe('Secure API Architecture Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (global.fetch as jest.Mock).mockResolvedValue({
      ok: true,
      status: 200,
      statusText: 'OK',
      headers: new Map([['content-type', 'application/json']]),
      json: jest.fn().mockResolvedValue({ success: true, data: 'test' }),
    });
  });

  describe('Complete Architecture Integration', () => {
    it('should integrate SecurityConfigProvider with useSecureApiClient', async () => {
      const wrapper = ({ children }: { children: React.ReactNode }) => (
        <SecurityConfigProvider
          initialConfig={{
            csrf: {
              enabled: true,
              tokenHeader: 'X-CSRF-Token',
              excludePaths: [],
            },
            tokenValidation: {
              enabled: true,
              refreshThreshold: 300,
              autoRefresh: true,
            },
            inputSanitization: { enabled: true, sanitizers: ['xss', 'sql'] },
          }}
        >
          {children}
        </SecurityConfigProvider>
      );

      const { result } = renderHook(() => useSecureApiClient(), { wrapper });

      expect(result.current.isAuthenticated).toBe(true);
      expect(result.current.hasValidToken).toBe(true);
      expect(result.current.isInitialized).toBe(true);

      // Test secure request
      const response = await result.current.secureRequest({
        url: '/api/test',
        method: 'POST',
        data: { message: 'Hello World' },
      });

      expect(response.data).toEqual({ success: true, data: 'test' });
    });

    it('should apply all security features in correct order', async () => {
      const wrapper = ({ children }: { children: React.ReactNode }) => (
        <SecurityConfigProvider>{children}</SecurityConfigProvider>
      );

      const { result } = renderHook(() => useSecureApiClient(), { wrapper });

      const maliciousData = {
        message: '<script>alert("xss")</script>',
        safe: 'normal data',
      };

      await result.current.secureRequest({
        url: '/api/test',
        method: 'POST',
        data: maliciousData,
      });

      // Verify fetch was called with sanitized data and CSRF protection
      expect(global.fetch).toHaveBeenCalledWith(
        '/api/test',
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'X-CSRF-Token': 'mock-csrf-token',
            Authorization: 'Bearer mock-token',
            'Content-Type': 'application/json',
          }),
          credentials: 'include',
          body: expect.any(String),
        })
      );
    });

    it('should handle security configuration changes', async () => {
      const wrapper = ({ children }: { children: React.ReactNode }) => (
        <SecurityConfigProvider
          initialConfig={{
            csrf: {
              enabled: false,
              tokenHeader: 'X-CSRF-Token',
              excludePaths: [],
            }, // Start with CSRF disabled
          }}
        >
          {children}
        </SecurityConfigProvider>
      );

      const { result } = renderHook(() => useSecureApiClient(), { wrapper });

      await result.current.secureRequest({
        url: '/api/test',
        method: 'POST',
        data: { test: 'data' },
      });

      // Should not include CSRF token when disabled
      expect(global.fetch).toHaveBeenCalledWith(
        '/api/test',
        expect.objectContaining({
          headers: expect.not.objectContaining({
            'X-CSRF-Token': expect.any(String),
          }),
        })
      );
    });
  });

  describe('Error Handling Integration', () => {
    it('should handle authentication errors across the stack', async () => {
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 401,
        statusText: 'Unauthorized',
        headers: new Map(),
        json: jest.fn().mockResolvedValue({ error: 'Unauthorized' }),
      });

      const wrapper = ({ children }: { children: React.ReactNode }) => (
        <SecurityConfigProvider>{children}</SecurityConfigProvider>
      );

      const { result } = renderHook(() => useSecureApiClient(), { wrapper });

      await expect(
        result.current.secureRequest({
          url: '/api/protected',
          method: 'GET',
        })
      ).rejects.toThrow();
    });

    it('should handle network errors gracefully', async () => {
      (global.fetch as jest.Mock).mockRejectedValueOnce(
        new Error('Network error')
      );

      const wrapper = ({ children }: { children: React.ReactNode }) => (
        <SecurityConfigProvider>{children}</SecurityConfigProvider>
      );

      const { result } = renderHook(() => useSecureApiClient(), { wrapper });

      await expect(
        result.current.secureRequest({
          url: '/api/test',
          method: 'GET',
        })
      ).rejects.toThrow('Network error');
    });
  });

  describe('Performance and Memory', () => {
    it('should not create unnecessary re-renders', () => {
      const wrapper = ({ children }: { children: React.ReactNode }) => (
        <SecurityConfigProvider>{children}</SecurityConfigProvider>
      );

      const { result, rerender } = renderHook(() => useSecureApiClient(), {
        wrapper,
      });

      const initialClient = result.current.client;
      const initialSecurityStatus = result.current.securityStatus;

      // Re-render should not create new instances
      rerender();

      expect(result.current.client).toBe(initialClient);
      expect(result.current.securityStatus).toBe(initialSecurityStatus);
    });

    it('should handle multiple concurrent requests', async () => {
      const wrapper = ({ children }: { children: React.ReactNode }) => (
        <SecurityConfigProvider>{children}</SecurityConfigProvider>
      );

      const { result } = renderHook(() => useSecureApiClient(), { wrapper });

      // Make multiple concurrent requests
      const requests = Array.from({ length: 5 }, (_, i) =>
        result.current.secureRequest({
          url: `/api/test-${i}`,
          method: 'GET',
        })
      );

      const responses = await Promise.all(requests);

      expect(responses).toHaveLength(5);
      responses.forEach(response => {
        expect(response.data).toEqual({ success: true, data: 'test' });
      });

      // Should have made 5 fetch calls
      expect(global.fetch).toHaveBeenCalledTimes(5);
    });
  });

  describe('Backward Compatibility', () => {
    it('should maintain compatibility with legacy useSecureApi interface', async () => {
      const wrapper = ({ children }: { children: React.ReactNode }) => (
        <SecurityConfigProvider>{children}</SecurityConfigProvider>
      );

      const { result } = renderHook(() => useSecureApiClient(), { wrapper });

      // Test that all legacy methods are available
      expect(typeof result.current.hasValidToken).toBe('boolean');
      expect(typeof result.current.isAuthenticated).toBe('boolean');
      expect(typeof result.current.refreshToken).toBe('function');
      expect(typeof result.current.sanitizeInput).toBe('function');
      expect(typeof result.current.secureRequest).toBe('function');

      // Test legacy secureRequest format
      const legacyResponse = await result.current.secureRequest({
        url: '/api/legacy',
        method: 'POST',
        data: { legacy: 'data' },
        headers: { 'Custom-Header': 'value' },
        timeout: 5000,
      });

      expect(legacyResponse).toHaveProperty('data');
      expect(legacyResponse).toHaveProperty('headers');
      expect(legacyResponse).toHaveProperty('status');
      expect(legacyResponse).toHaveProperty('statusText');
    });
  });

  describe('Security Constants Integration', () => {
    it('should use SECURITY_CONSTANTS throughout the architecture', () => {
      const wrapper = ({ children }: { children: React.ReactNode }) => (
        <SecurityConfigProvider>{children}</SecurityConfigProvider>
      );

      const { result } = renderHook(() => useSecureApiClient(), { wrapper });

      // Security status should include constants
      expect(result.current.securityStatus).toBeDefined();

      // Should use constants for configuration
      expect(SECURITY_CONSTANTS.TOKEN_EXPIRY_THRESHOLD_MINUTES).toBeDefined();
      expect(SECURITY_CONSTANTS.SESSION_TIMEOUT_MINUTES).toBeDefined();
    });
  });
});
