import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import React from 'react';

import { usePaginatedApiQuery } from '@/hooks/api/useApiQuery';

import { ErrorLog } from '../ErrorLog';

// Mock the hooks
jest.mock('@/hooks/api/useApiQuery', () => ({
  usePaginatedApiQuery: jest.fn(),
}));

// Mock the components
jest.mock('@/components/ui/loading-states', () => ({
  LoadingError: ({
    message,
    onRetry,
  }: {
    message: string;
    onRetry: () => void;
  }) => (
    <div data-testid="loading-error">
      <p>{message}</p>
      <button onClick={onRetry}>Retry</button>
    </div>
  ),
}));

jest.mock('@/components/ui/error-boundary', () => ({
  ErrorBoundary: ({ children }: { children: React.ReactNode }) => (
    <div>{children}</div>
  ),
}));

jest.mock('@/components/ui/scroll-area', () => ({
  ScrollArea: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="scroll-area">{children}</div>
  ),
}));

describe('ErrorLog Component', () => {
  const mockRefetch = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render loading state correctly', () => {
    (usePaginatedApiQuery as jest.Mock).mockReturnValue({
      data: [],
      error: null,
      hasNextPage: false,
      hasPrevPage: false,
      isLoading: true,
      nextPage: jest.fn(),
      page: 1,
      prevPage: jest.fn(),
      refetch: mockRefetch,
      totalPages: 0,
    });

    render(<ErrorLog />);

    expect(screen.getAllByTestId('skeleton')).toHaveLength(5);
  });

  it('should render error state correctly', () => {
    (usePaginatedApiQuery as jest.Mock).mockReturnValue({
      data: [],
      error: 'Failed to fetch error logs',
      hasNextPage: false,
      hasPrevPage: false,
      isLoading: false,
      nextPage: jest.fn(),
      page: 1,
      prevPage: jest.fn(),
      refetch: mockRefetch,
      totalPages: 0,
    });

    render(<ErrorLog />);

    expect(screen.getByTestId('loading-error')).toBeInTheDocument();
    expect(screen.getByText('Failed to fetch error logs')).toBeInTheDocument();

    // Test retry functionality
    fireEvent.click(screen.getByText('Retry'));
    expect(mockRefetch).toHaveBeenCalledTimes(1);
  });

  it('should render error logs correctly', () => {
    const mockErrors = [
      {
        details: {},
        id: '1',
        level: 'ERROR',
        message: 'Test error 1',
        source: 'frontend',
        timestamp: '2025-01-01T10:00:00Z',
      },
      {
        details: {},
        id: '2',
        level: 'WARNING',
        message: 'Test warning 1',
        source: 'backend',
        timestamp: '2025-01-01T11:00:00Z',
      },
    ];

    (usePaginatedApiQuery as jest.Mock).mockReturnValue({
      currentPage: 1,
      data: mockErrors,
      error: null,
      hasNextPage: false,
      hasPrevPage: false,
      isLoading: false,
      nextPage: jest.fn(),
      pagination: {
        currentPage: 1,
        limit: 10,
        offset: 0,
        pages: 1,
        total: mockErrors.length,
      },
      prevPage: jest.fn(),
      refetch: mockRefetch,
      totalPages: 1,
    });

    render(<ErrorLog />);

    expect(screen.getByTestId('scroll-area')).toBeInTheDocument();

    // Check that all error messages are displayed
    for (const error of mockErrors) {
      expect(screen.getByText(error.message)).toBeInTheDocument();
    }
  });

  it('should render empty state correctly', () => {
    (usePaginatedApiQuery as jest.Mock).mockReturnValue({
      data: [],
      error: null,
      hasNextPage: false,
      hasPrevPage: false,
      isLoading: false,
      nextPage: jest.fn(),
      page: 1,
      prevPage: jest.fn(),
      refetch: mockRefetch,
      totalPages: 0,
    });

    render(<ErrorLog />);

    expect(screen.getByText('No errors or warnings found')).toBeInTheDocument();
  });

  it('should handle pagination correctly', () => {
    const mockNextPage = jest.fn();
    const mockPrevPage = jest.fn();
    const mockErrors = [
      {
        details: {},
        id: '1',
        level: 'ERROR',
        message: 'Test error 1',
        source: 'frontend',
        timestamp: '2025-01-01T10:00:00Z',
      },
      {
        details: {},
        id: '2',
        level: 'WARNING',
        message: 'Test warning 1',
        source: 'backend',
        timestamp: '2025-01-01T11:00:00Z',
      },
    ];

    (usePaginatedApiQuery as jest.Mock).mockReturnValue({
      currentPage: 2,
      data: mockErrors,
      error: null,
      hasNextPage: true,
      hasPrevPage: true,
      isLoading: false,
      nextPage: mockNextPage,
      pagination: {
        currentPage: 2,
        limit: 10,
        offset: 10,
        pages: 3,
        total: 30,
      },
      prevPage: mockPrevPage,
      refetch: mockRefetch,
      totalPages: 3,
    });

    render(<ErrorLog />);

    // Check pagination controls
    expect(screen.getByText('Page 2 of 3')).toBeInTheDocument();

    // Test next page button
    fireEvent.click(screen.getByText('Next'));
    expect(mockNextPage).toHaveBeenCalledTimes(1);

    // Test previous page button
    fireEvent.click(screen.getByText('Previous'));
    expect(mockPrevPage).toHaveBeenCalledTimes(1);
  });

  it('should handle refresh button correctly', async () => {
    const mockErrors = [
      {
        details: {},
        id: '1',
        level: 'ERROR',
        message: 'Test error 1',
        source: 'frontend',
        timestamp: '2025-01-01T10:00:00Z',
      },
    ];
    (usePaginatedApiQuery as jest.Mock).mockReturnValue({
      currentPage: 1,
      data: mockErrors,
      error: null,
      hasNextPage: false,
      hasPrevPage: false,
      isLoading: false,
      nextPage: jest.fn(),
      pagination: {
        currentPage: 1,
        limit: 10,
        offset: 0,
        pages: 1,
        total: mockErrors.length,
      },
      prevPage: jest.fn(),
      refetch: mockRefetch,
      totalPages: 1,
    });

    render(<ErrorLog />);

    // Test refresh button
    fireEvent.click(screen.getByText('Refresh Logs'));
    expect(mockRefetch).toHaveBeenCalledTimes(1);
  });
});
