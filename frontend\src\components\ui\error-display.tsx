/**
 * @file Error Display Component
 * @description Standardized error display component following SOLID principles
 */

import React from 'react';
import { AlertTriangle, RefreshCw } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from './alert';
import { Button } from './button';

export interface ErrorDisplayProps {
  /**
   * Error message to display
   */
  error?: string | Error | null;
  
  /**
   * Custom title for the error
   */
  title?: string;
  
  /**
   * Retry function to call when retry button is clicked
   */
  onRetry?: () => void;
  
  /**
   * Custom CSS class names
   */
  className?: string;
  
  /**
   * Whether to show the retry button
   */
  showRetry?: boolean;
  
  /**
   * Custom retry button text
   */
  retryText?: string;
}

/**
 * ErrorDisplay component for consistent error handling across the application
 * 
 * @example
 * <ErrorDisplay
 *   error="Failed to load data"
 *   onRetry={refetch}
 *   title="Loading Error"
 * />
 */
export function ErrorDisplay({
  error,
  title = 'Error',
  onRetry,
  className,
  showRetry = true,
  retryText = 'Try Again',
}: ErrorDisplayProps) {
  if (!error) {
    return null;
  }

  const errorMessage = error instanceof Error ? error.message : String(error);

  return (
    <Alert variant="destructive" className={className}>
      <AlertTriangle className="h-4 w-4" />
      <AlertTitle>{title}</AlertTitle>
      <AlertDescription className="mt-2">
        <p className="mb-3">{errorMessage}</p>
        {showRetry && onRetry && (
          <Button
            variant="outline"
            size="sm"
            onClick={onRetry}
            className="h-8"
          >
            <RefreshCw className="h-3 w-3 mr-2" />
            {retryText}
          </Button>
        )}
      </AlertDescription>
    </Alert>
  );
}
