import { z } from 'zod';

// Basic schema for flight ID validation (e.g., in path parameters)
export const flightIdSchema = z.object({
  id: z.string().min(1, { message: 'Flight ID is required' }), // Assuming flight ID is a string
});

// Basic schema for creating a flight
// Based on typical flight data, actual fields would depend on requirements
export const flightCreateSchema = z.object({
  callsign: z
    .string()
    .min(3, { message: 'Callsign must be at least 3 characters' })
    .max(10)
    .optional(),
  originAirport: z
    .string()
    .length(4, { message: 'Origin airport ICAO must be 4 characters' })
    .optional(),
  destinationAirport: z
    .string()
    .length(4, { message: 'Destination airport ICAO must be 4 characters' })
    .optional(),
  departureTime: z
    .preprocess(
      arg => {
        if (typeof arg === 'string' || arg instanceof Date) return new Date(arg);
        return arg;
      },
      z.date({ message: 'Invalid departure time' }),
    )
    .optional(),
  arrivalTime: z
    .preprocess(
      arg => {
        if (typeof arg === 'string' || arg instanceof Date) return new Date(arg);
        return arg;
      },
      z.date({ message: 'Invalid arrival time' }),
    )
    .optional(),
  // Add other fields as necessary, e.g., aircraftType, status, etc.
  // For now, keeping it simple to resolve import errors.
  // Example additional fields:
  // aircraftType: z.string().optional(),
  // status: z.enum(['SCHEDULED', 'DELAYED', 'DEPARTED', 'ARRIVED', 'CANCELLED']).optional(),
});

// Basic schema for updating a flight
// All fields are typically optional during an update
export const flightUpdateSchema = z.object({
  callsign: z.string().min(3).max(10).optional(),
  originAirport: z.string().length(4).optional(),
  destinationAirport: z.string().length(4).optional(),
  departureTime: z.preprocess(
    arg => {
      if (typeof arg === 'string' || arg instanceof Date) return new Date(arg);
      if (arg === null || arg === undefined) return arg; // Allow null/undefined to clear date
      return arg;
    },
    z.date({ message: 'Invalid departure time' }).optional().nullable(),
  ),
  arrivalTime: z.preprocess(
    arg => {
      if (typeof arg === 'string' || arg instanceof Date) return new Date(arg);
      if (arg === null || arg === undefined) return arg; // Allow null/undefined to clear date
      return arg;
    },
    z.date({ message: 'Invalid arrival time' }).optional().nullable(),
  ),
  // aircraftType: z.string().optional(),
  // status: z.enum(['SCHEDULED', 'DELAYED', 'DEPARTED', 'ARRIVED', 'CANCELLED']).optional(),
});

// Ensure you have zod installed: npm install zod
