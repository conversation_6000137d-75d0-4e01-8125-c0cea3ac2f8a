'use client';

import type * as z from 'zod';

import { format, parseISO } from 'date-fns';
import { UserCog } from 'lucide-react';
import { useParams, useRouter } from 'next/navigation';
import { useEffect, useMemo } from 'react';

import type {
  EmployeeRoleSchema,
  EmployeeStatusSchema,
} from '@/lib/schemas/employeeSchemas';
import type {
  CreateEmployeeData, // Added import
  Employee as DomainEmployee, // Corrected alias import
  EmployeeRolePrisma,
  EmployeeStatusPrisma,
} from '@/lib/types/domain';

import { EmployeeForm } from '@/components/features/employees/forms/employeeForm';
import { PageHeader } from '@/components/ui/PageHeader';
import { Skeleton } from '@/components/ui/skeleton';
import { employeeToast } from '@/lib/services/toastService';
import { type EmployeeFormData } from '@/lib/schemas/employeeSchemas';
import {
  useEmployee,
  useUpdateEmployee,
} from '@/lib/stores/queries/useEmployees';

type EmployeeFormRole = z.infer<typeof EmployeeRoleSchema>;
type EmployeeFormStatus = z.infer<typeof EmployeeStatusSchema>;

const mapDomainToFormStatus = (
  domainStatus: EmployeeStatusPrisma | null | undefined
): EmployeeFormStatus => {
  switch (domainStatus) {
    case 'Active': {
      return 'Active';
    }
    case 'Inactive': {
      return 'Inactive';
    }
    case 'On_Leave': {
      return 'On_Leave';
    }
    case 'Terminated': {
      return 'Terminated';
    }
    default: {
      return 'Inactive';
    }
  }
};

const mapFormToDomainStatus = (
  formStatus: EmployeeFormStatus
): { status: EmployeeStatusPrisma } => {
  switch (formStatus) {
    case 'Active': {
      return { status: 'Active' };
    }
    case 'Inactive': {
      return { status: 'Inactive' };
    }
    case 'On_Leave': {
      return { status: 'On_Leave' };
    }
    case 'Terminated': {
      return { status: 'Terminated' };
    }
    default: {
      return { status: 'Inactive' };
    }
  }
};

export default function EditEmployeePage() {
  const router = useRouter();
  const params = useParams();
  const employeeIdParam = params?.id as string;

  const {
    data: employee,
    error: employeeError,
    isLoading: isLoadingEmployee,
  } = useEmployee(employeeIdParam);

  const updateEmployeeMutation = useUpdateEmployee();

  useEffect(() => {
    if (employeeError) {
      console.error('Failed to fetch employee:', employeeError);
      employeeToast.error(
        'Error Loading Employee',
        (employeeError as any)?.message || 'Failed to load employee data.'
      );
      router.push('/employees');
    }
  }, [employeeError, router]);

  const handleSubmit = async (formData: EmployeeFormData) => {
    if (!employee) {
      employeeToast.error('Error', 'Employee data not available for update.');
      return;
    }

    const domainStatusMapping = mapFormToDomainStatus(formData.status);

    const updatePayload: Partial<CreateEmployeeData> = {
      // Note: Vehicle assignments are now context-specific (per task/delegation)
      availability: formData.availability ?? null,
      contactInfo: formData.contactInfo,
      contactMobile: formData.contactMobile ?? null,
      contactPhone: formData.contactPhone ?? null,
      currentLocation: formData.currentLocation ?? null,
      department: formData.department ?? null,
      employeeId: formData.employeeId,
      fullName: formData.fullName ?? null,
      generalAssignments: formData.generalAssignments ?? [],
      hireDate: formData.hireDate ?? null,
      name: formData.name,
      notes: formData.notes ?? null,
      position: formData.position ?? null,
      profileImageUrl: formData.profileImageUrl ?? null,
      role: formData.role as EmployeeRolePrisma,
      shiftSchedule: formData.shiftSchedule ?? null,
      skills: formData.skills ?? [],
      status: domainStatusMapping.status,
      //workingHours: formData.workingHours ?? null,
    };

    // Clean undefined values from the payload for the PATCH request
    const cleanedUpdateData = Object.fromEntries(
      Object.entries(updatePayload).filter(([, value]) => value !== undefined)
    ) as Partial<CreateEmployeeData>;

    try {
      const updatedEmployeeResult = await updateEmployeeMutation.mutateAsync({
        data: cleanedUpdateData,
        id: employee.id.toString(),
      });
      const updatedEmployee = { name: updatedEmployeeResult.name };
      employeeToast.entityUpdated(updatedEmployee);
      router.push(`/employees/${employee.employeeId}`);
    } catch (error: any) {
      console.error('Failed to update employee:', error);
      const errorMessage =
        error.response?.data?.error ||
        error.message ||
        'An unexpected error occurred.';
      employeeToast.entityUpdateError(errorMessage);
    }
  };

  const initialFormData = useMemo((): EmployeeFormData | undefined => {
    if (employee) {
      const formStatus = mapDomainToFormStatus(employee.status);

      // Ensure contactPhone and contactMobile are explicitly null if they are null from the backend
      // This aligns with the Zod schema's .nullable() and avoids potential type inference issues
      // when passing to the form component.
      const formContactPhone: null | string | undefined = employee.contactPhone;
      const formContactMobile: null | string | undefined =
        employee.contactMobile;

      return {
        availability: employee.availability || null,
        contactEmail: employee.contactEmail || '',
        contactInfo: employee.contactInfo || '',
        contactMobile: employee.contactMobile || '',
        contactPhone: employee.contactPhone || '',
        currentLocation: employee.currentLocation || '',
        department: employee.department || '',
        employeeId: employee.employeeId,
        fullName: employee.fullName || employee.name || '',
        generalAssignments: employee.generalAssignments || [],
        hireDate: employee.hireDate
          ? format(parseISO(employee.hireDate), 'yyyy-MM-dd')
          : '',
        name: employee.name || '',
        notes: employee.notes || '',
        position: employee.position || '',
        profileImageUrl: employee.profileImageUrl || '',
        role: employee.role || 'other',
        shiftSchedule: employee.shiftSchedule || '',
        //workingHours: employee.workingHours || '',
        // Note: Vehicle assignments are now context-specific (per task/delegation)
        skills: employee.skills || [],
        status: formStatus,
      };
    }
    return undefined;
  }, [employee]);

  if (isLoadingEmployee || (employeeIdParam && !employee && !employeeError)) {
    return (
      <div className="space-y-6">
        <PageHeader icon={UserCog} title="Loading Employee Data..." />
        <Skeleton className="h-[700px] w-full rounded-lg bg-card" />
      </div>
    );
  }

  if (!employee || !initialFormData) {
    return <p>Employee not found or data could not be prepared.</p>;
  }

  return (
    <div className="space-y-6">
      <PageHeader
        description="Modify the details for this employee."
        icon={UserCog}
        title={`Edit Employee: ${employee.name}`}
      />
      <EmployeeForm
        initialData={initialFormData as any}
        isEditing={true}
        isLoading={updateEmployeeMutation.isPending}
        onSubmit={handleSubmit}
      />
    </div>
  );
}
