/**
 * @file DetailItem component for displaying labeled information with icons
 * @module components/delegations/common/DetailItem
 */

import React from 'react';
import { cn } from '@/lib/utils';

interface DetailItemProps {
  children?: React.ReactNode;
  icon: React.ElementType;
  label: string;
  value?: null | number | string;
  valueClassName?: string;
  className?: string;
}

/**
 * DetailItem component for displaying labeled information with icons
 * Used throughout delegation detail views for consistent information display
 */
export function DetailItem({
  children,
  icon: Icon,
  label,
  value,
  valueClassName,
  className,
}: DetailItemProps) {
  return (
    <div className={cn('flex items-start space-x-3', className)}>
      <div className="mt-0.5 rounded-full bg-blue-50 dark:bg-blue-900/30 p-2">
        <Icon className="size-4 text-blue-600 dark:text-blue-400" />
      </div>
      <div className="flex-1 min-w-0">
        <div className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide mb-1">
          {label}
        </div>
        <div className={cn('font-medium text-gray-900 dark:text-white', valueClassName)}>
          {children || value || 'N/A'}
        </div>
      </div>
    </div>
  );
}

export default DetailItem;
