/**
 * Enhanced Admin Service - Production-Ready Service Foundation
 *
 * This service provides a unified interface for all admin operations,
 * consolidating functionality from multiple admin services while maintaining
 * Single Responsibility Principle through delegation to specialized services.
 *
 * Features:
 * - BaseApiService integration for consistent patterns
 * - Circuit breaker protection
 * - Request caching and deduplication
 * - Comprehensive error handling with retry logic
 * - Performance monitoring and metrics
 * - Audit logging integration
 *
 * Built upon the BaseApiService foundation for production-grade reliability.
 */

import type { PaginatedApiResponse } from '@/hooks/api/useApiQuery'; // Import PaginatedApiResponse
import type { ApiClient } from '@/lib/api/core/apiClient';
import type { ErrorLogEntry, LogLevel } from '@/lib/types/domain'; // Direct import for problematic types
import type {
  AuditLog,
  AuditLogFilters,
  HealthResponse,
  PaginatedResponse, // Keep PaginatedResponse for other uses if needed
  PerformanceMetrics,
  User,
  UserFilters,
} from '@/types'; // Keep other types from '@/types'

import {
  BaseApiService,
  type DataTransformer,
  type ServiceConfig,
} from '@/lib/api/core/baseApiService';
import { apiServiceFactory } from '@/lib/api/services/apiServiceFactory';

import { AuditService } from './auditService';
import { UserService } from './UserService';

/**
 * Admin data transformer for consistent data transformation
 */
const AdminTransformer: DataTransformer<any> = {
  fromApi: (data: any) => data,
  toApi: (data: any) => data,
};

/**
 * Enhanced Admin Service Class
 * Extends BaseApiService for consistent patterns and production-grade reliability
 */
export class AdminService extends BaseApiService<any> {
  /**
   * Enhanced cache management utilities using BaseApiService infrastructure
   */
  get cacheUtils() {
    return {
      /**
       * Clear all cache
       */
      clearAll: () => this.clearCache(),

      /**
       * Force refresh health status (bypasses cache)
       */
      forceRefreshHealth: async (): Promise<HealthResponse> => {
        this.cache.invalidate('admin:health');
        return this.getSystemHealthStatus();
      },

      /**
       * Force refresh performance metrics (bypasses cache)
       */
      forceRefreshPerformance: async (): Promise<PerformanceMetrics> => {
        this.cache.invalidate('admin:performance');
        return this.getPerformanceMetrics();
      },

      /**
       * Get cache statistics
       */
      getStats: () => this.cache.getStats(),

      /**
       * Invalidate all admin cache entries
       */
      invalidateAll: () => this.cache.invalidatePattern(/^admin:/),
    };
  }
  protected endpoint = '/admin';

  protected transformer: DataTransformer<any> = AdminTransformer;
  // Specialized service instances
  private readonly auditService: AuditService;

  private readonly userService: UserService;

  constructor(apiClient?: ApiClient, config?: ServiceConfig) {
    const client = apiClient || apiServiceFactory.getApiClient();
    super(client, {
      cacheDuration: 5 * 60 * 1000, // 5 minutes for admin operations
      circuitBreakerThreshold: 5,
      enableMetrics: true,
      retryAttempts: 3,
      ...config,
    });

    // Initialize specialized services with the same API client
    this.auditService = new AuditService(client);
    this.userService = new UserService(client);
  }

  // ===== USER MANAGEMENT =====

  /**
   * Create audit log entry using BaseApiService infrastructure
   */
  async createAuditLog(logData: {
    action: string;
    details: string;
    ipAddress?: string;
    userAgent?: string;
    userId: string;
  }): Promise<AuditLog> {
    return this.executeWithInfrastructure(null, async () => {
      const response = await this.apiClient.post<AuditLog>(
        '/admin/audit-logs',
        logData
      );

      // Invalidate audit logs cache
      this.cache.invalidatePattern(/^admin:audit:/);

      return response;
    });
  }

  /**
   * Create a new user using BaseApiService infrastructure
   */
  async createUser(userData: {
    email: string;
    emailVerified?: boolean;
    isActive?: boolean;
    role: string;
  }): Promise<User> {
    return this.executeWithInfrastructure(null, async () => {
      const response = await this.apiClient.post<User>(
        '/admin/users',
        userData
      );

      // Invalidate users cache after creation
      this.cache.invalidatePattern(/^admin:users:/);

      return response;
    });
  }

  // ===== USER MANAGEMENT OPERATIONS =====

  /**
   * Delete a user using BaseApiService infrastructure
   */
  async deleteUser(userId: string): Promise<void> {
    return this.executeWithInfrastructure(null, async () => {
      await this.apiClient.delete(`/admin/users/${userId}`);

      // Invalidate relevant caches
      this.cache.invalidatePattern(/^admin:users:/);
      this.cache.invalidate(`admin:user:${userId}`);
    });
  }

  /**
   * Get all users with pagination and filtering (delegates to UserService)
   */
  async getAllUsers(
    filters: UserFilters = {}
  ): Promise<PaginatedResponse<User>> {
    return this.userService.getAll(filters);
  }

  /**
   * Get audit logs with pagination and filtering (delegates to AuditService)
   */
  async getAuditLogs(
    filters: AuditLogFilters = {}
  ): Promise<PaginatedResponse<AuditLog>> {
    // Use the AuditService's getAll method directly with proper filters
    return this.auditService.getAll(filters);
  }

  /**
   * Get mock health status data
   */
  getMockHealthStatus(): HealthResponse {
    return {
      services: {
        api: { responseTime: 23, status: 'healthy' },
        cache: { responseTime: 12, status: 'healthy' },
        database: { responseTime: 45, status: 'healthy' },
      },
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: 3600,
    };
  }

  // ===== AUDIT LOG MANAGEMENT =====

  /**
   * Get mock performance metrics
   */
  getMockPerformanceMetrics(): PerformanceMetrics {
    return {
      cpu: {
        cores: 4,
        usage: Math.random() * 100,
      },

      errors: {
        rate: Math.random() * 5,
        total: Math.floor(Math.random() * 500),
      },
      memory: {
        percentage: Math.random() * 100,
        total: 8000,
        used: Math.random() * 8000,
      },
      requests: {
        averageResponseTime: Math.random() * 1000,
        perSecond: Math.random() * 100,
        total: Math.floor(Math.random() * 10_000),
      },
      timestamp: new Date().toISOString(),
    };
  }

  // ===== HEALTH MONITORING =====

  /**
   * Get mock recent errors
   */
  getMockRecentErrors(): PaginatedResponse<ErrorLogEntry> {
    const mockErrors: ErrorLogEntry[] = [
      {
        details: { component: 'database', errorType: 'timeout' }, // Example direct details
        id: '1',
        level: 'ERROR',
        message: 'Database connection timeout',
        requestId: 'req-456',
        source: 'database.service.ts', // Now a direct property
        stack: 'Error: Connection timeout\n    at Database.connect...',
        timestamp: new Date().toISOString(),
        userId: 'user123',
      },
      {
        details: { component: 'system', metric: 'memory' }, // Example direct details
        id: '2',
        level: 'WARNING', // Changed from 'WARN' to 'WARNING'
        message: 'High memory usage detected',
        requestId: 'req-789',
        source: 'monitoring.service.ts', // Now a direct property
        timestamp: new Date(Date.now() - 300_000).toISOString(),
      },
    ];

    return {
      data: mockErrors,
      pagination: {
        hasNext: false,
        hasPrevious: false,
        limit: 10,
        page: 1,
        total: mockErrors.length,
        totalPages: Math.ceil(mockErrors.length / 10),
      },
    };
  }

  /**
   * Get performance metrics using BaseApiService infrastructure
   */
  async getPerformanceMetrics(): Promise<PerformanceMetrics> {
    const cacheKey = 'admin:performance';

    return this.executeWithInfrastructure(cacheKey, async () => {
      return this.apiClient.get<PerformanceMetrics>('/admin/performance');
    });
  }

  /**
   * Get recent error logs using BaseApiService infrastructure
   */
  async getRecentErrors(
    page = 1,
    limit = 10,
    level?: string
  ): Promise<PaginatedApiResponse<ErrorLogEntry>> {
    // Changed return type
    const params = new URLSearchParams();
    params.append('page', page.toString());
    params.append('limit', limit.toString());
    if (level) params.append('level', level);

    const url = `/admin/logs/errors?${params.toString()}`;
    const cacheKey = `admin:errors:${page}:${limit}:${level || 'all'}`;

    return this.executeWithInfrastructure(cacheKey, async () => {
      return this.apiClient.get<PaginatedApiResponse<ErrorLogEntry>>(url); // Changed return type
    });
  }

  // ===== MOCK DATA METHODS (for development) =====

  /**
   * Get system health status using BaseApiService infrastructure
   */
  async getSystemHealthStatus(): Promise<HealthResponse> {
    const cacheKey = 'admin:health';

    return this.executeWithInfrastructure(cacheKey, async () => {
      return this.apiClient.get<HealthResponse>('/admin/health');
    });
  }

  // ===== MOCK DATA METHODS (for development) =====

  /**
   * Toggle user activation status using BaseApiService infrastructure
   */
  async toggleUserActivation(userId: string, isActive: boolean): Promise<User> {
    return this.executeWithInfrastructure(null, async () => {
      const response = await this.apiClient.patch<User>(
        `/admin/users/${userId}/toggle-activation`,
        { isActive }
      );

      // Invalidate relevant caches
      this.cache.invalidatePattern(/^admin:users:/);
      this.cache.invalidate(`admin:user:${userId}`);

      return response;
    });
  }

  /**
   * Update user details using BaseApiService infrastructure
   */
  async updateUser(
    userId: string,
    userData: Partial<{
      email: string;
      emailVerified: boolean;
      isActive: boolean;
      role: string;
    }>
  ): Promise<User> {
    return this.executeWithInfrastructure(null, async () => {
      const response = await this.apiClient.put<User>(
        `/admin/users/${userId}`,
        userData
      );

      // Invalidate relevant caches
      this.cache.invalidatePattern(/^admin:users:/);
      this.cache.invalidate(`admin:user:${userId}`);

      return response;
    });
  }
}

// Export singleton instance
export const adminService = new AdminService();

// Export cache utilities for backward compatibility
export const adminCache = adminService.cacheUtils;
