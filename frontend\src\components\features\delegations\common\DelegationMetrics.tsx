/**
 * @file DelegationMetrics component for displaying delegation statistics
 * @module components/delegations/common/DelegationMetrics
 */

import React from 'react';
import { Users, Shield, Car, User, Clock, Calendar, MapPin, Plane } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import type { Delegation } from '@/lib/types/domain';
import { differenceInDays, parseISO } from 'date-fns';

interface DelegationMetricsProps {
  delegation: Delegation;
  className?: string;
}

interface MetricItemProps {
  icon: React.ElementType;
  label: string;
  value: string | number;
  color: string;
  bgColor: string;
}

function MetricItem({ icon: Icon, label, value, color, bgColor }: MetricItemProps) {
  return (
    <div className="flex items-center justify-between p-3 rounded-lg border border-gray-200 dark:border-gray-700">
      <div className="flex items-center space-x-3">
        <div className={`p-2 rounded-full ${bgColor}`}>
          <Icon className={`h-4 w-4 ${color}`} />
        </div>
        <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
          {label}
        </span>
      </div>
      <Badge variant="secondary" className="font-semibold">
        {value}
      </Badge>
    </div>
  );
}

const calculateDuration = (startDate: string | undefined, endDate: string | undefined): string => {
  if (!startDate || !endDate) return 'N/A';
  try {
    const start = parseISO(startDate);
    const end = parseISO(endDate);
    const days = differenceInDays(end, start) + 1;
    return `${days} day${days !== 1 ? 's' : ''}`;
  } catch {
    return 'Invalid';
  }
};

/**
 * DelegationMetrics component for displaying delegation statistics
 * Shows key metrics and counts in a compact, visual format
 */
export function DelegationMetrics({ delegation, className }: DelegationMetricsProps) {
  const metrics = [
    {
      icon: Users,
      label: 'Delegates',
      value: delegation.delegates?.length || 0,
      color: 'text-blue-600 dark:text-blue-400',
      bgColor: 'bg-blue-50 dark:bg-blue-900/30',
    },
    {
      icon: Shield,
      label: 'Escorts',
      value: delegation.escorts?.length || 0,
      color: 'text-green-600 dark:text-green-400',
      bgColor: 'bg-green-50 dark:bg-green-900/30',
    },
    {
      icon: User,
      label: 'Drivers',
      value: delegation.drivers?.length || 0,
      color: 'text-purple-600 dark:text-purple-400',
      bgColor: 'bg-purple-50 dark:bg-purple-900/30',
    },
    {
      icon: Car,
      label: 'Vehicles',
      value: delegation.vehicles?.length || 0,
      color: 'text-orange-600 dark:text-orange-400',
      bgColor: 'bg-orange-50 dark:bg-orange-900/30',
    },
    {
      icon: Plane,
      label: 'Flights',
      value: [delegation.arrivalFlight, delegation.departureFlight].filter(Boolean).length,
      color: 'text-indigo-600 dark:text-indigo-400',
      bgColor: 'bg-indigo-50 dark:bg-indigo-900/30',
    },
    {
      icon: Clock,
      label: 'Duration',
      value: calculateDuration(delegation.durationFrom, delegation.durationTo),
      color: 'text-gray-600 dark:text-gray-400',
      bgColor: 'bg-gray-50 dark:bg-gray-900/30',
    },
  ];

  const totalPeople = (delegation.delegates?.length || 0) + 
                     (delegation.escorts?.length || 0) + 
                     (delegation.drivers?.length || 0);

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Calendar className="h-5 w-5 text-blue-600 dark:text-blue-400" />
            <span>Delegation Metrics</span>
          </div>
          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900/30 dark:text-blue-300 dark:border-blue-800">
            {totalPeople} people total
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        {metrics.map((metric, index) => (
          <MetricItem
            key={index}
            icon={metric.icon}
            label={metric.label}
            value={metric.value}
            color={metric.color}
            bgColor={metric.bgColor}
          />
        ))}
        
        {delegation.location && (
          <div className="mt-4 p-3 rounded-lg bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
            <div className="flex items-center space-x-2">
              <MapPin className="h-4 w-4 text-gray-600 dark:text-gray-400" />
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Location
              </span>
            </div>
            <p className="mt-1 text-sm text-gray-600 dark:text-gray-400 ml-6">
              {delegation.location}
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

export default DelegationMetrics;
