/**
 * @file Excel Export Service
 * @description Production-ready Excel export service for reporting data
 *
 * SOLID Principles Applied:
 * - SRP: Single responsibility for Excel export operations
 * - OCP: Open for extension via options and templates
 * - DIP: Depends on abstractions (interfaces) not concretions
 */

import type {
  ComprehensiveReportData,
  EntityType,
  ExcelExportOptions,
  ExportMetadata,
  ExportResult,
  IExcelExportService,
} from '../../types/export';
import type { ReportingFilters } from '../../types/reporting';

export class ExcelExportService implements IExcelExportService {
  private readonly defaultOptions: Partial<ExcelExportOptions> = {
    includeChartSheets: false,
    includeFormatting: true,
    includeFormulas: false,
    separateSheets: true,
  };

  async generateComprehensiveReport(
    data: ComprehensiveReportData
  ): Promise<ExportResult> {
    try {
      // Import XLSX library for actual Excel generation
      const XLSX = await import('xlsx');

      // Create workbook
      const workbook = XLSX.utils.book_new();

      // Add summary sheet
      const summaryData = [
        ['Report Type', 'Comprehensive Report'],
        ['Generated At', new Date().toISOString()],
        ['Total Records', this.calculateRecordCount(data)],
        [''],
        ['Entity Type', 'Count'],
        ['Delegations', data.delegations?.length || 0],
        ['Tasks', data.tasks?.length || 0],
        ['Vehicles', data.vehicles?.length || 0],
        ['Employees', data.employees?.length || 0],
      ];
      const summarySheet = XLSX.utils.aoa_to_sheet(summaryData);
      XLSX.utils.book_append_sheet(workbook, summarySheet, 'Summary');

      // Add entity-specific sheets
      if (data.delegations?.length) {
        const delegationsSheet = XLSX.utils.json_to_sheet(data.delegations);
        XLSX.utils.book_append_sheet(workbook, delegationsSheet, 'Delegations');
      }

      if (data.tasks?.length) {
        const tasksSheet = XLSX.utils.json_to_sheet(data.tasks);
        XLSX.utils.book_append_sheet(workbook, tasksSheet, 'Tasks');
      }

      if (data.vehicles?.length) {
        const vehiclesSheet = XLSX.utils.json_to_sheet(data.vehicles);
        XLSX.utils.book_append_sheet(workbook, vehiclesSheet, 'Vehicles');
      }

      if (data.employees?.length) {
        const employeesSheet = XLSX.utils.json_to_sheet(data.employees);
        XLSX.utils.book_append_sheet(workbook, employeesSheet, 'Employees');
      }

      // Generate Excel file
      const buffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'buffer' });
      const blob = new Blob([buffer], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      });

      const filename = `comprehensive-report-${Date.now()}.xlsx`;
      const downloadUrl = URL.createObjectURL(blob);

      const metadata: ExportMetadata = {
        entityTypes: ['delegation', 'task', 'vehicle', 'employee'],
        filters: {
          dateRange: { from: new Date(), to: new Date() },
          employees: [],
          locations: [],
          status: [],
          vehicles: [],
        },
        generatedAt: new Date(),
        generatedBy: 'system',
        processingTime: 0,
        recordCount: this.calculateRecordCount(data),
      };

      return {
        downloadUrl,
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
        filename,
        fileSize: blob.size,
        format: 'excel',
        id: `excel-${Date.now()}`,
        metadata,
      };
    } catch (error) {
      throw new Error(
        `Failed to generate comprehensive Excel report: ${error}`
      );
    }
  }

  async generateEntityReport(
    entityType: EntityType,
    data: any,
    options?: Partial<ExcelExportOptions>
  ): Promise<ExportResult> {
    try {
      const mergedOptions = { ...this.defaultOptions, ...options };

      // Import XLSX library for actual Excel generation
      const XLSX = await import('xlsx');

      // Create workbook
      const workbook = XLSX.utils.book_new();

      // Prepare data for Excel
      const entityData = Array.isArray(data) ? data : [data];

      // Add main data sheet
      const worksheet = XLSX.utils.json_to_sheet(entityData);
      XLSX.utils.book_append_sheet(
        workbook,
        worksheet,
        entityType.charAt(0).toUpperCase() + entityType.slice(1)
      );

      // Add summary sheet if multiple records
      if (entityData.length > 1) {
        const summaryData = [
          ['Entity Type', entityType],
          ['Total Records', entityData.length],
          ['Generated At', new Date().toISOString()],
          [''],
          ['Summary Statistics'],
          // Add basic statistics based on entity type
          ...this.generateEntityStatistics(entityType, entityData),
        ];
        const summarySheet = XLSX.utils.aoa_to_sheet(summaryData);
        XLSX.utils.book_append_sheet(workbook, summarySheet, 'Summary');
      }

      // Generate Excel file
      const buffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'buffer' });
      const blob = new Blob([buffer], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      });

      const filename = `${entityType}-report-${Date.now()}.xlsx`;
      const downloadUrl = URL.createObjectURL(blob);

      const metadata: ExportMetadata = {
        entityTypes: [entityType],
        filters: {
          dateRange: { from: new Date(), to: new Date() },
          employees: [],
          locations: [],
          status: [],
          vehicles: [],
        },
        generatedAt: new Date(),
        generatedBy: 'system',
        processingTime: 0,
        recordCount: entityData.length,
      };

      return {
        downloadUrl,
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
        filename,
        fileSize: blob.size,
        format: 'excel',
        id: `excel-${entityType}-${Date.now()}`,
        metadata,
      };
    } catch (error) {
      throw new Error(
        `Failed to generate ${entityType} Excel report: ${error}`
      );
    }
  }

  /**
   * Calculate total record count from comprehensive data
   */
  private calculateRecordCount(data: ComprehensiveReportData): number {
    return (
      (data.delegations?.length || 0) +
      (data.tasks?.length || 0) +
      (data.vehicles?.length || 0) +
      (data.employees?.length || 0)
    );
  }

  /**
   * Count occurrences of a property in data array
   */
  private countByProperty(
    data: Record<string, unknown>[],
    property: string
  ): Record<string, number> {
    const counts: Record<string, number> = {};

    for (const item of data) {
      const value = String(item[property] ?? 'Unknown');
      counts[value] = (counts[value] ?? 0) + 1;
    }

    return counts;
  }
  /**
   * Generate entity-specific statistics for summary sheet
   */
  private generateEntityStatistics(
    entityType: EntityType,
    data: any[]
  ): string[][] {
    const stats: string[][] = [];

    if (data.length === 0) return stats;

    // Common statistics
    stats.push(['Total Records', data.length.toString()]);

    // Entity-specific statistics
    switch (entityType) {
      case 'delegation': {
        const statusCounts = this.countByProperty(data, 'status');
        stats.push(['Status Distribution', '']);
        for (const [status, count] of Object.entries(statusCounts)) {
          stats.push([`  ${status}`, count.toString()]);
        }
        break;
      }

      case 'employee': {
        const roleCounts = this.countByProperty(data, 'role');
        stats.push(['Role Distribution', '']);
        for (const [role, count] of Object.entries(roleCounts)) {
          stats.push([`  ${role}`, count.toString()]);
        }
        break;
      }

      case 'task': {
        const priorityCounts = this.countByProperty(data, 'priority');
        stats.push(['Priority Distribution', '']);
        for (const [priority, count] of Object.entries(priorityCounts)) {
          stats.push([`  ${priority}`, count.toString()]);
        }
        break;
      }

      case 'vehicle': {
        const makeCounts = this.countByProperty(data, 'make');
        stats.push(['Make Distribution', '']);
        for (const [make, count] of Object.entries(makeCounts)) {
          stats.push([`  ${make}`, count.toString()]);
        }
        break;
      }
    }

    return stats;
  }
}
