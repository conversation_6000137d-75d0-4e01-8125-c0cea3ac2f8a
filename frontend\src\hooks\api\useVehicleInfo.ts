/**
 * @file Vehicle Information Hook
 * @description Reusable hook for fetching vehicle information by vehicleId
 * Follows SOLID principles with single responsibility and separation of concerns
 */

import { useVehicle } from '@/lib/stores/queries/useVehicles';

export interface UseVehicleInfoResult {
  error: Error | null;
  isLoading: boolean;
  refetch: () => void;
  vehicleInfo: null | VehicleInfo;
}

export interface VehicleInfo {
  color?: null | string;
  id: number;
  licensePlate: string;
  make: string;
  model: string;
  vin?: null | string;
  year: number;
}

/**
 * Hook to fetch vehicle information by vehicleId
 * @param vehicleId - The ID of the vehicle to fetch
 * @param options - Query options
 * @returns Vehicle information, loading state, error, and refetch function
 */
export function useVehicleInfo(
  vehicleId: null | number | undefined,
  options?: { enabled?: boolean }
): UseVehicleInfoResult {
  const {
    data: vehicle,
    error,
    isLoading,
    refetch,
  } = useVehicle(vehicleId ?? 0, {
    enabled: (options?.enabled ?? true) && !!vehicleId,
  });

  // Transform vehicle data to VehicleInfo format
  const vehicleInfo: null | VehicleInfo = vehicle
    ? {
        color: vehicle.color ?? null,
        id: vehicle.id,
        licensePlate: vehicle.licensePlate,
        make: vehicle.make,
        model: vehicle.model,
        vin: vehicle.vin,
        year: vehicle.year,
      }
    : null;

  return {
    error,
    isLoading,
    refetch,
    vehicleInfo,
  };
}
