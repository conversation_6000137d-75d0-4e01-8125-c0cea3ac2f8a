import type {
  CreateRecipientRequest,
  RecipientApiResponse,
  RecipientStatisticsApiResponse,
  UpdateRecipientRequest,
} from '../../../types/apiContracts';
import type { Recipient, RecipientStatistics } from '../../../types/domain';
import type { ApiClient } from '../../core/apiClient';
import { RecipientTransformer } from '../../../transformers/recipientTransformer';
import {
  BaseApiService,
  type DataTransformer,
  type ServiceConfig,
} from '../../core/baseApiService';

const RecipientApiTransformer: DataTransformer<Recipient> = {
  fromApi: (data: RecipientApiResponse) => RecipientTransformer.fromApi(data),
  toApi: (data: any) => data,
};

export class RecipientApiService extends BaseApiService<
  Recipient,
  CreateRecipientRequest,
  UpdateRecipientRequest
> {
  protected endpoint = '/recipients';
  protected transformer: DataTransformer<Recipient> = RecipientApiTransformer;

  constructor(apiClient: ApiClient, config?: ServiceConfig) {
    super(apiClient, {
      cacheDuration: 10 * 60 * 1000, // 10 minutes for recipients (longer cache as they change less frequently)
      retryAttempts: 3,
      circuitBreakerThreshold: 5,
      enableMetrics: true,
      ...config,
    });
  }

  /**
   * Search recipients by name
   * @param searchTerm - The search term to filter by
   * @returns Promise resolving to array of recipients
   */
  async searchByName(searchTerm: string): Promise<Recipient[]> {
    const result = await this.getAll({ search: searchTerm });
    return result.data;
  }

  /**
   * Get recipients with gift counts
   * @returns Promise resolving to array of recipients with gift counts
   */
  async getWithGiftCounts(): Promise<Recipient[]> {
    return this.executeWithInfrastructure('getWithGiftCounts', async () => {
      const response = await this.apiClient.get<RecipientApiResponse[]>(
        `${this.endpoint}/with-gift-counts`
      );

      return RecipientTransformer.fromApiArray(response);
    });
  }

  /**
   * Get recipient statistics
   * @returns Promise resolving to recipient statistics
   */
  async getStatistics(): Promise<RecipientStatistics> {
    return this.executeWithInfrastructure('getStatistics', async () => {
      const response = await this.apiClient.get<RecipientStatisticsApiResponse>(
        `${this.endpoint}/statistics`
      );

      return {
        totalRecipients: response.totalRecipients,
        recipientsThisMonth: response.recipientsThisMonth,
        recipientsThisYear: response.recipientsThisYear,
        mostGifted: response.mostGifted,
      };
    });
  }

  /**
   * Get recipients by email domain
   * @param domain - The email domain to filter by (e.g., 'gmail.com')
   * @returns Promise resolving to array of recipients
   */
  async getByEmailDomain(domain: string): Promise<Recipient[]> {
    const result = await this.getAll({ emailDomain: domain });
    return result.data;
  }

  /**
   * Get recipients who have received gifts
   * @returns Promise resolving to array of recipients with gifts
   */
  async getWithGifts(): Promise<Recipient[]> {
    const result = await this.getAll({ hasGifts: true });
    return result.data;
  }

  /**
   * Get recipients who haven't received gifts
   * @returns Promise resolving to array of recipients without gifts
   */
  async getWithoutGifts(): Promise<Recipient[]> {
    const result = await this.getAll({ hasGifts: false });
    return result.data;
  }

  /**
   * Get recipients for autocomplete/dropdown (limited results)
   * @param searchTerm - The search term to filter by
   * @returns Promise resolving to array of recipients (limited to 10)
   */
  async getForAutocomplete(searchTerm: string): Promise<Recipient[]> {
    return this.executeWithInfrastructure(`autocomplete:${searchTerm}`, async () => {
      const response = await this.apiClient.get<RecipientApiResponse[]>(
        `${this.endpoint}/search`,
        { params: { q: searchTerm, limit: 10 } }
      );

      return RecipientTransformer.fromApiArray(response);
    });
  }
}
