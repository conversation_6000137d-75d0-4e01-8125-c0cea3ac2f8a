// frontend/src/components/features/reporting/charts/shared/ChartStateComponents.tsx

/**
 * Shared Chart State Components
 *
 * Following Single Responsibility Principle (SRP) - Each component handles one specific state
 * Following DRY Principle - Reusable components eliminate duplication across charts
 * Following Separation of Concerns - Clear boundaries between loading, error, and no-data states
 */

import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { AlertCircle, BarChart3 } from 'lucide-react';

/**
 * Loading skeleton component for charts
 * Single Responsibility: Handle loading state display
 */
export const LoadingSkeleton: React.FC<{ height?: number }> = ({
  height = 300,
}) => {
  return (
    <Card className="w-full">
      <CardContent className="p-6">
        <div className="space-y-4">
          <Skeleton className="h-6 w-1/3" />
          <Skeleton className={`w-full`} style={{ height: `${height}px` }} />
          <div className="flex space-x-4">
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-4 w-20" />
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

/**
 * Error display component for charts
 * Single Responsibility: Handle error state display
 */
export const ErrorDisplay: React.FC<{
  error: string;
  onRetry?: (() => void) | undefined;
}> = ({ error, onRetry }) => {
  return (
    <Card className="w-full">
      <CardContent className="p-6">
        <div className="flex flex-col items-center justify-center space-y-4 text-center">
          <AlertCircle className="h-12 w-12 text-red-500" />
          <div className="space-y-2">
            <h3 className="text-lg font-semibold text-gray-900">
              Unable to load chart
            </h3>
            <p className="text-sm text-gray-600 max-w-md">{error}</p>
          </div>
          {onRetry && (
            <button
              onClick={onRetry}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              Try Again
            </button>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

/**
 * No data display component for charts
 * Single Responsibility: Handle empty state display
 */
export const NoDataDisplay: React.FC<{
  message?: string | undefined;
  suggestion?: string | undefined;
}> = ({
  message = 'No data available',
  suggestion = 'Try adjusting your filters or date range to see more data.',
}) => {
  return (
    <Card className="w-full">
      <CardContent className="p-6">
        <div className="flex flex-col items-center justify-center space-y-4 text-center">
          <BarChart3 className="h-12 w-12 text-gray-400" />
          <div className="space-y-2">
            <h3 className="text-lg font-semibold text-gray-900">{message}</h3>
            <p className="text-sm text-gray-600 max-w-md">{suggestion}</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

/**
 * Generic chart wrapper with state handling
 * Single Responsibility: Coordinate chart state display logic
 */
export const ChartWrapper: React.FC<{
  loading?: boolean;
  error?: string;
  hasData?: boolean;
  children: React.ReactNode;
  height?: number;
  onRetry?: (() => void) | undefined;
  noDataMessage?: string | undefined;
  noDataSuggestion?: string | undefined;
}> = ({
  loading,
  error,
  hasData = true,
  children,
  height = 300,
  onRetry,
  noDataMessage,
  noDataSuggestion,
}) => {
  if (loading) {
    return <LoadingSkeleton height={height} />;
  }

  if (error) {
    return <ErrorDisplay error={error} onRetry={onRetry} />;
  }

  if (!hasData) {
    return (
      <NoDataDisplay message={noDataMessage} suggestion={noDataSuggestion} />
    );
  }

  return <>{children}</>;
};
