export * from './DelegationStatusWidget';
export * from './DelegationTrendWidget';
export * from './LocationDistributionWidget';
export * from './TaskMetricsWidget';
export * from './ReportingTableWidget';
export * from './SingleDelegationWidget';
export * from './SingleTaskWidget';
export * from './BaseWidget';

// Phase 2: Task Reporting Components
export * from './TaskStatusChart';
export * from './TaskPriorityDistribution';
export * from './TaskAssignmentMetrics';

// Phase 2: Vehicle Reporting Components
export * from './VehicleAnalyticsWidget';
export * from './VehicleUtilizationChart';
export * from './VehicleMaintenanceWidget';
export * from './VehicleCostAnalyticsWidget';

// Phase 2: Employee Reporting Components
export * from './EmployeeAnalyticsWidget';
export * from './EmployeePerformanceChart';
export * from './EmployeeWorkloadWidget';

// Phase 2: Cross-Entity Correlation Components
export * from './CrossEntityCorrelationWidget';
export * from './EntityRelationshipNetworkWidget';
