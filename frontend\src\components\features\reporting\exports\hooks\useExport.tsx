import { Document, Page, pdf, Text, View } from '@react-pdf/renderer'; // Import React-PDF components
// frontend/src/components/features/reporting/exports/hooks/useExport.tsx
import { useCallback, useState } from 'react';
import * as XLSX from 'xlsx'; // Assuming xlsx library for Excel

import type {
  DelegationAnalytics,
  ExportOptions,
  ReportingFilters,
} from '../../data/types'; // Import ExportOptions, DelegationAnalytics, ReportingFilters

import { DelegationReportDocument } from '../pdf/DelegationReportDocument'; // Import the PDF document component
import { EmployeeReportDocument } from '../pdf/EmployeeReportDocument';
import { TaskReportDocument } from '../pdf/TaskReportDocument';
import { VehicleReportDocument } from '../pdf/VehicleReportDocument';
// Note: UniversalReportDocument removed - using entity-specific components instead

/**
 * @hook useExport
 * @description Provides utility functions for exporting data to various formats (PDF, Excel, CSV).
 * Adheres to SRP by encapsulating export logic.
 * @param {string} defaultFilename - The default filename for exported files.
 */
export const useExport = (defaultFilename = 'report') => {
  const [isExporting, setIsExporting] = useState(false);
  const [exportError, setExportError] = useState<null | string>(null);

  /**
   * Exports data to a PDF using @react-pdf/renderer.
   * @param {DelegationAnalytics} data - The analytics data to include in the report.
   * @param {ReportingFilters} filters - The filters applied to the data.
   * @param {ExportOptions} options - Export options including filename.
   */
  const exportToPDF = useCallback(
    async (
      data: DelegationAnalytics,
      filters: ReportingFilters,
      options: ExportOptions
    ) => {
      setIsExporting(true);
      setExportError(null);
      try {
        const blob = await pdf(
          <DelegationReportDocument
            data={data}
            filters={filters}
            reportDate={new Date().toLocaleString()} // Using current date/time
            reportTitle={options.filename || defaultFilename}
          />
        ).toBlob();

        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute(
          'download',
          `${options.filename || defaultFilename}.pdf`
        );
        document.body.append(link);
        link.click();
        link.remove();
        URL.revokeObjectURL(url); // Clean up the URL object
      } catch (error: any) {
        console.error('PDF export failed:', error);
        setExportError(error.message || 'Failed to export to PDF.');
      } finally {
        setIsExporting(false);
      }
    },
    [defaultFilename]
  );

  /**
   * Exports tabular data to an Excel (XLSX) file.
   * @param {Array<Record<string, any>>} data - The array of objects to export.
   * @param {ExportOptions} options - Export options including filename.
   */
  const exportToExcel = useCallback(
    (data: Record<string, any>[], options: ExportOptions) => {
      setIsExporting(true);
      setExportError(null);
      try {
        const worksheet = XLSX.utils.json_to_sheet(data);
        const workbook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(workbook, worksheet, 'Report Data');
        XLSX.writeFile(workbook, `${options.filename || defaultFilename}.xlsx`);
      } catch (error: any) {
        console.error('Excel export failed:', error);
        setExportError(error.message || 'Failed to export to Excel.');
      } finally {
        setIsExporting(false);
      }
    },
    [defaultFilename]
  );

  /**
   * Exports tabular data to a CSV file.
   * @param {Array<Record<string, any>>} data - The array of objects to export.
   * @param {ExportOptions} options - Export options including filename.
   */
  const exportToCSV = useCallback(
    (data: Record<string, any>[], options: ExportOptions) => {
      setIsExporting(true);
      setExportError(null);
      try {
        const header = Object.keys(data[0] || {}).join(',');
        const rows = data.map(row =>
          Object.values(row)
            .map(value => `"${String(value).replaceAll('"', '""')}"`)
            .join(',')
        );
        const csvContent = [header, ...rows].join('\n');
        const blob = new Blob([csvContent], {
          type: 'text/csv;charset=utf-8;',
        });
        const link = document.createElement('a');
        if (link.download !== undefined) {
          const url = URL.createObjectURL(blob);
          link.setAttribute('href', url);
          link.setAttribute(
            'download',
            `${options.filename || defaultFilename}.csv`
          );
          link.style.visibility = 'hidden';
          document.body.append(link);
          link.click();
          link.remove();
        }
      } catch (error: any) {
        console.error('CSV export failed:', error);
        setExportError(error.message || 'Failed to export to CSV.');
      } finally {
        setIsExporting(false);
      }
    },
    [defaultFilename]
  );

  // ENHANCED: Modern React-PDF dashboard export
  const exportDashboardToPDF = useCallback(
    async (
      data: DelegationAnalytics,
      filters: ReportingFilters,
      filename?: string
    ) => {
      setIsExporting(true);
      setExportError(null);

      try {
        // Use existing React-PDF infrastructure - modern approach
        const blob = await pdf(
          <DelegationReportDocument
            data={data}
            filters={filters}
            includeCharts={true}
            includeServiceHistory={Boolean(filters.includeServiceHistory)}
            reportDate={new Date().toLocaleString()}
            reportTitle={filename || 'Dashboard Report'}
          />
        ).toBlob();

        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', `${filename || 'dashboard-report'}.pdf`);
        document.body.append(link);
        link.click();
        link.remove();
        URL.revokeObjectURL(url);
      } catch (error: any) {
        console.error('Dashboard PDF export failed:', error);
        setExportError(error.message || 'Failed to export dashboard to PDF.');
      } finally {
        setIsExporting(false);
      }
    },
    []
  );

  // ENHANCED: Chart-specific PDF export using React-PDF
  const exportChartToPDF = useCallback(
    async (chartData: any, title: string, filename?: string) => {
      setIsExporting(true);
      setExportError(null);

      try {
        // Create a minimal chart-focused PDF document
        const ChartDocument = () => (
          <Document>
            <Page orientation="landscape" size="A4" style={{ padding: 30 }}>
              <View style={{ marginBottom: 20 }}>
                <Text style={{ fontSize: 20, fontWeight: 'bold' }}>
                  {title}
                </Text>
                <Text style={{ fontSize: 10, marginTop: 5 }}>
                  Generated on: {new Date().toLocaleDateString()}
                </Text>
              </View>
              <View
                style={{
                  alignItems: 'center',
                  flex: 1,
                  justifyContent: 'center',
                }}
              >
                <Text style={{ fontSize: 14 }}>
                  Chart data: {JSON.stringify(chartData, null, 2)}
                </Text>
              </View>
            </Page>
          </Document>
        );

        const blob = await pdf(<ChartDocument />).toBlob();

        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute(
          'download',
          filename || `${title.toLowerCase().replaceAll(/\s+/g, '-')}.pdf`
        );
        document.body.append(link);
        link.click();
        link.remove();
        URL.revokeObjectURL(url);
      } catch (error: any) {
        console.error('Chart PDF export failed:', error);
        setExportError(error.message || 'Failed to export chart to PDF.');
      } finally {
        setIsExporting(false);
      }
    },
    []
  );

  /**
   * Export report data to PDF based on entity type
   */
  const exportReportToPDF = useCallback(
    async (
      reportData: any,
      entityType: 'delegations' | 'employees' | 'tasks' | 'vehicles',
      reportTitle: string,
      filename?: string
    ) => {
      setIsExporting(true);
      setExportError(null);

      // --- START AGGRESSIVE SANITIZATION ---
      const sanitizeData = (data: any): any => {
        if (data === null || data === undefined) {
          return null; // Keep null/undefined as null for React-PDF compatibility
        }
        if (Array.isArray(data)) {
          return data.map(sanitizeData); // Recursively sanitize array elements
        }
        if (typeof data === 'object' && data.constructor === Object) {
          const sanitizedObject: Record<string, any> = {};
          for (const key in data) {
            // Ensure we only process own properties and avoid problematic keys
            if (
              Object.prototype.hasOwnProperty.call(data, key) &&
              key !== '__proto__' &&
              key !== 'constructor'
            ) {
              const sanitizedValue = sanitizeData(data[key]);
              // Only include non-undefined values
              if (sanitizedValue !== undefined) {
                sanitizedObject[key] = sanitizedValue;
              }
            }
          }
          return sanitizedObject;
        }
        // Handle functions, symbols, and other non-serializable types
        if (typeof data === 'function' || typeof data === 'symbol') {
          return null;
        }
        // Return primitives (string, number, boolean) as is
        return data;
      };
      // --- END AGGRESSIVE SANITIZATION ---

      try {
        if (!reportData) {
          throw new Error('No report data provided for PDF export');
        }

        // Ensure we have a valid data structure to work with
        const rawData = reportData?.data || reportData || {};

        // Create a fully sanitized and safe data structure
        const sanitizedReportData = sanitizeData(rawData);

        // Ensure we have at least basic structure for PDF generation
        const safeData = {
          totalCount: sanitizedReportData?.totalCount || 0,
          summary: sanitizedReportData?.summary || {
            message: 'No data available',
          },
          records: sanitizedReportData?.records || [],
          statusDistribution: sanitizedReportData?.statusDistribution || [],
          priorityDistribution: sanitizedReportData?.priorityDistribution || [],
          ...sanitizedReportData,
        };

        const normalizedData = {
          data: safeData,
          metadata: reportData?.metadata || {
            entityType,
            format: 'pdf',
            generatedAt: new Date().toISOString(),
            generatedBy: 'system',
            id: `${entityType}_${Date.now()}`,
            type: 'aggregate',
          },
        };

        // Final check to ensure we have something to render
        if (Object.keys(normalizedData.data).length === 0) {
          throw new Error(
            'After sanitization, no valid data is available for PDF export.'
          );
        }

        let PDFComponent;

        const safeReportTitle = reportTitle || `${entityType} Report`;
        const safeMetadata = normalizedData.metadata || {};

        switch (entityType) {
          case 'delegations': {
            // Create safe filters object with proper structure
            const safeFilters = {
              dateRange: {
                from: new Date(),
                to: new Date(),
              },
              employees: [],
              locations: [],
              status: [],
              vehicles: [],
            };

            PDFComponent = (
              <DelegationReportDocument
                data={normalizedData}
                filters={safeFilters}
                reportDate={new Date().toLocaleString()}
                reportTitle={safeReportTitle}
              />
            );
            break;
          }
          case 'employees': {
            PDFComponent = (
              <EmployeeReportDocument
                data={normalizedData}
                metadata={safeMetadata}
                reportTitle={safeReportTitle}
              />
            );
            break;
          }
          case 'tasks': {
            PDFComponent = (
              <TaskReportDocument
                data={normalizedData}
                metadata={safeMetadata}
                reportTitle={safeReportTitle}
              />
            );
            break;
          }
          case 'vehicles': {
            PDFComponent = (
              <VehicleReportDocument
                data={normalizedData}
                metadata={safeMetadata}
                reportTitle={safeReportTitle}
              />
            );
            break;
          }
          default: {
            throw new Error(
              `Unsupported entity type: ${entityType}. Supported types are: delegations, tasks, vehicles, employees`
            );
          }
        }

        console.log('Generating PDF with SANITIZED data:', normalizedData);
        console.log('Entity Type:', entityType);
        console.log('Raw data structure:', reportData);
        console.log('Normalized data structure:', normalizedData);
        console.log('PDF Component:', PDFComponent);
        console.log('Entity type:', entityType);
        console.log('Report title:', safeReportTitle);

        // Generate PDF with additional error handling
        let blob;
        try {
          console.log('Starting PDF blob generation...');
          blob = await pdf(PDFComponent).toBlob();
          console.log('PDF blob generated successfully:', {
            size: blob.size,
            type: blob.type,
          });
        } catch (pdfError: any) {
          console.error('PDF generation failed:', pdfError);
          console.error('PDF error stack:', pdfError.stack);
          throw new Error(
            `PDF generation failed: ${pdfError.message ?? 'Unknown PDF error'}`
          );
        }

        // Verify blob is valid before attempting download
        if (!blob || blob.size === 0) {
          throw new Error('Generated PDF blob is empty or invalid');
        }

        console.log('Creating download link...');
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        const downloadFilename = `${filename || `${entityType}-report`}.pdf`;
        link.setAttribute('download', downloadFilename);

        console.log('Triggering download for:', downloadFilename);
        document.body.append(link);
        link.click();
        link.remove();
        URL.revokeObjectURL(url);
        console.log('Download triggered successfully');
      } catch (error: any) {
        console.error('Report PDF export failed:', error);
        setExportError(error.message || 'Failed to export report to PDF.');
      } finally {
        setIsExporting(false);
      }
    },
    []
  );

  /**
   * Export report data to Excel with proper formatting
   */
  const exportReportToExcel = useCallback(
    (reportData: any, entityType: string, filename?: string) => {
      setIsExporting(true);
      setExportError(null);

      try {
        const workbook = XLSX.utils.book_new();
        const data = reportData.data || reportData;

        // Create summary sheet
        if (data.summary || data.totalCount) {
          const summaryData = [];
          if (data.totalCount)
            summaryData.push({ Metric: 'Total Count', Value: data.totalCount });
          if (data.summary) {
            for (const [key, value] of Object.entries(data.summary)) {
              summaryData.push({
                Metric: key
                  .replaceAll(/([A-Z])/g, ' $1')
                  .replace(/^./, str => str.toUpperCase()),
                Value: value,
              });
            }
          }
          const summarySheet = XLSX.utils.json_to_sheet(summaryData);
          XLSX.utils.book_append_sheet(workbook, summarySheet, 'Summary');
        }

        // Create distribution sheets
        if (data.statusDistribution) {
          const statusSheet = XLSX.utils.json_to_sheet(data.statusDistribution);
          XLSX.utils.book_append_sheet(
            workbook,
            statusSheet,
            'Status Distribution'
          );
        }

        if (data.priorityDistribution) {
          const prioritySheet = XLSX.utils.json_to_sheet(
            data.priorityDistribution
          );
          XLSX.utils.book_append_sheet(
            workbook,
            prioritySheet,
            'Priority Distribution'
          );
        }

        // Add entity-specific sheets
        if (data.locationMetrics) {
          const locationSheet = XLSX.utils.json_to_sheet(data.locationMetrics);
          XLSX.utils.book_append_sheet(
            workbook,
            locationSheet,
            'Location Metrics'
          );
        }

        if (data.maintenanceMetrics) {
          const maintenanceSheet = XLSX.utils.json_to_sheet([
            data.maintenanceMetrics,
          ]);
          XLSX.utils.book_append_sheet(
            workbook,
            maintenanceSheet,
            'Maintenance Metrics'
          );
        }

        if (data.performanceMetrics) {
          const performanceSheet = XLSX.utils.json_to_sheet([
            data.performanceMetrics,
          ]);
          XLSX.utils.book_append_sheet(
            workbook,
            performanceSheet,
            'Performance Metrics'
          );
        }

        XLSX.writeFile(workbook, `${filename || `${entityType}-report`}.xlsx`);
      } catch (error: any) {
        console.error('Report Excel export failed:', error);
        setExportError(error.message || 'Failed to export report to Excel.');
      } finally {
        setIsExporting(false);
      }
    },
    []
  );

  return {
    exportChartToPDF,
    // ENHANCED: Advanced export methods
    exportDashboardToPDF,
    exportError,
    exportReportToExcel,
    // NEW: Report-specific export methods
    exportReportToPDF,
    exportToCSV,
    exportToExcel,
    exportToPDF,
    isExporting,
  };
};
