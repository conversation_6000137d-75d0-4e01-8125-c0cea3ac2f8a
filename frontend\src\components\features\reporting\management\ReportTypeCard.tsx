/**
 * @file ReportTypeCard.tsx
 * @description Report type card component following established card patterns and SOLID principles
 */

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  FileText, 
  Edit, 
  Trash2, 
  Copy,
  Play,
  Pause,
  MoreHorizontal,
  Clock,
  Users,
  Eye
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';
import type { ReportType } from '../data/types/reporting';

/**
 * Props interface for ReportTypeCard
 */
interface ReportTypeCardProps {
  reportType: ReportType;
  onSelect?: (reportType: ReportType) => void;
  onEdit?: (reportType: ReportType) => void;
  onDelete?: (reportType: ReportType) => void;
  onDuplicate?: (reportType: ReportType) => void;
  onToggleActive?: (reportType: ReportType) => void;
  className?: string;
  showActions?: boolean;
}

/**
 * Get category color based on category type
 */
const getCategoryColor = (category: string): string => {
  switch (category.toLowerCase()) {
    case 'operational':
      return 'bg-blue-100 text-blue-800';
    case 'performance':
      return 'bg-green-100 text-green-800';
    case 'financial':
      return 'bg-yellow-100 text-yellow-800';
    case 'compliance':
      return 'bg-red-100 text-red-800';
    case 'analytics':
      return 'bg-purple-100 text-purple-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

/**
 * Get data source icon
 */
const getDataSourceIcon = (dataSource: string) => {
  switch (dataSource.toLowerCase()) {
    case 'delegations':
      return <FileText className="h-4 w-4" />;
    case 'tasks':
      return <FileText className="h-4 w-4" />;
    case 'vehicles':
      return <FileText className="h-4 w-4" />;
    case 'employees':
      return <Users className="h-4 w-4" />;
    default:
      return <FileText className="h-4 w-4" />;
  }
};

/**
 * ReportTypeCard Component
 * 
 * Displays report type information in a card format following established patterns.
 * 
 * Responsibilities:
 * - Display report type details in card format
 * - Handle user interactions (select, edit, delete, etc.)
 * - Follow established card component patterns
 * - Maintain consistent styling and behavior
 * 
 * SOLID Principles Applied:
 * - SRP: Single responsibility of displaying report type information
 * - OCP: Open for extension via props configuration
 * - DIP: Depends on established UI component abstractions
 */
export const ReportTypeCard: React.FC<ReportTypeCardProps> = ({
  reportType,
  onSelect,
  onEdit,
  onDelete,
  onDuplicate,
  onToggleActive,
  className = '',
  showActions = true
}) => {
  // Handle card click
  const handleCardClick = () => {
    if (onSelect) {
      onSelect(reportType);
    }
  };

  // Handle action clicks (prevent event bubbling)
  const handleActionClick = (e: React.MouseEvent, action: () => void) => {
    e.stopPropagation();
    action();
  };

  return (
    <Card 
      className={cn(
        'cursor-pointer transition-all duration-200 hover:shadow-md',
        !reportType.isActive && 'opacity-60',
        className
      )}
      onClick={handleCardClick}
    >
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-lg flex items-center gap-2">
              {getDataSourceIcon(reportType.dataSource)}
              {reportType.name}
            </CardTitle>
            <div className="flex items-center gap-2 mt-2">
              <Badge className={cn('text-xs', getCategoryColor(reportType.category))}>
                {reportType.category}
              </Badge>
              {!reportType.isActive && (
                <Badge variant="secondary" className="text-xs">
                  Inactive
                </Badge>
              )}
              {reportType.isPublic && (
                <Badge variant="outline" className="text-xs">
                  <Eye className="h-3 w-3 mr-1" />
                  Public
                </Badge>
              )}
            </div>
          </div>
          
          {showActions && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button 
                  variant="ghost" 
                  size="sm"
                  onClick={(e) => e.stopPropagation()}
                >
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                {onSelect && (
                  <DropdownMenuItem onClick={(e) => handleActionClick(e, () => onSelect(reportType))}>
                    <Play className="h-4 w-4 mr-2" />
                    Use Report Type
                  </DropdownMenuItem>
                )}
                {onEdit && (
                  <DropdownMenuItem onClick={(e) => handleActionClick(e, () => onEdit(reportType))}>
                    <Edit className="h-4 w-4 mr-2" />
                    Edit
                  </DropdownMenuItem>
                )}
                {onDuplicate && (
                  <DropdownMenuItem onClick={(e) => handleActionClick(e, () => onDuplicate(reportType))}>
                    <Copy className="h-4 w-4 mr-2" />
                    Duplicate
                  </DropdownMenuItem>
                )}
                {onToggleActive && (
                  <DropdownMenuItem onClick={(e) => handleActionClick(e, () => onToggleActive(reportType))}>
                    {reportType.isActive ? (
                      <>
                        <Pause className="h-4 w-4 mr-2" />
                        Deactivate
                      </>
                    ) : (
                      <>
                        <Play className="h-4 w-4 mr-2" />
                        Activate
                      </>
                    )}
                  </DropdownMenuItem>
                )}
                {onDelete && (
                  <>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem 
                      onClick={(e) => handleActionClick(e, () => onDelete(reportType))}
                      className="text-red-600"
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Delete
                    </DropdownMenuItem>
                  </>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Description */}
        {reportType.description && (
          <p className="text-sm text-gray-600 line-clamp-2">
            {reportType.description}
          </p>
        )}

        {/* Widgets */}
        <div>
          <h4 className="text-sm font-medium mb-2">Widgets ({reportType.widgets?.length || 0})</h4>
          <div className="flex flex-wrap gap-1">
            {reportType.widgets?.slice(0, 3).map((widget, index) => (
              <Badge key={index} variant="outline" className="text-xs">
                {widget}
              </Badge>
            ))}
            {(reportType.widgets?.length || 0) > 3 && (
              <Badge variant="outline" className="text-xs">
                +{(reportType.widgets?.length || 0) - 3} more
              </Badge>
            )}
          </div>
        </div>

        {/* Data Source and Refresh Info */}
        <div className="flex items-center justify-between text-xs text-gray-500">
          <div className="flex items-center gap-1">
            {getDataSourceIcon(reportType.dataSource)}
            <span>{reportType.dataSource}</span>
          </div>
          {reportType.refreshInterval && (
            <div className="flex items-center gap-1">
              <Clock className="h-3 w-3" />
              <span>{reportType.refreshInterval}m</span>
            </div>
          )}
        </div>

        {/* Tags */}
        {reportType.tags && reportType.tags.length > 0 && (
          <div className="flex flex-wrap gap-1">
            {reportType.tags.slice(0, 3).map((tag, index) => (
              <Badge key={index} variant="secondary" className="text-xs">
                {tag}
              </Badge>
            ))}
            {reportType.tags.length > 3 && (
              <Badge variant="secondary" className="text-xs">
                +{reportType.tags.length - 3}
              </Badge>
            )}
          </div>
        )}

        {/* Metadata */}
        <div className="pt-2 border-t text-xs text-gray-500">
          <div className="flex items-center justify-between">
            <span>
              Created: {reportType.createdAt ? format(new Date(reportType.createdAt), 'MMM dd, yyyy') : 'Unknown'}
            </span>
            {reportType.updatedAt && (
              <span>
                Updated: {format(new Date(reportType.updatedAt), 'MMM dd')}
              </span>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default ReportTypeCard;
