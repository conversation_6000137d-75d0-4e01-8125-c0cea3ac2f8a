-- ============================================================================
-- WorkHub Critical RLS Security Fixes - IMMEDIATE IMPLEMENTATION REQUIRED
-- ============================================================================
--
-- 🔴 CRITICAL SECURITY VULNERABILITIES IDENTIFIED:
-- 1. Employee/Vehicle tables: RLS enabled but NO policies = users can't access ANY data
-- 2. Task/ServiceRecord tables: NO RLS = ANY authenticated user can access ALL data
-- 3. Multiple tables completely unprotected
--
-- EXECUTE THESE FIXES IMMEDIATELY TO RESTORE SECURITY
-- ============================================================================

-- ============================================================================
-- STEP 1: ENABLE RLS ON COMPLETELY OPEN TABLES (CRITICAL)
-- ============================================================================

-- These tables are completely open - ANY authenticated user can access ALL data
ALTER TABLE public."Task" ENABLE ROW LEVEL SECURITY;
ALTER TABLE public."ServiceRecord" ENABLE ROW LEVEL SECURITY;
ALTER TABLE public."SubTask" ENABLE ROW LEVEL SECURITY;
ALTER TABLE public."FlightDetails" ENABLE ROW LEVEL SECURITY;
ALTER TABLE public."TaskStatusEntry" ENABLE ROW LEVEL SECURITY;
ALTER TABLE public."EmployeeStatusEntry" ENABLE ROW LEVEL SECURITY;
ALTER TABLE public."Delegate" ENABLE ROW LEVEL SECURITY;
ALTER TABLE public."DelegationStatusEntry" ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- STEP 2: CREATE MISSING POLICIES FOR EMPLOYEE TABLE (CRITICAL)
-- ============================================================================

-- Employee table has RLS enabled but NO policies = users can't access ANY employee data
-- This breaks application functionality for non-admin users

CREATE POLICY "employee_admin_full_access" ON public."Employee"
  FOR ALL TO authenticated 
  USING (private.is_admin_or_above());

CREATE POLICY "employee_user_view_own" ON public."Employee"
  FOR SELECT TO authenticated 
  USING (
    id = private.get_user_employee_id(auth.uid())
    OR private.is_admin_or_above()
  );

CREATE POLICY "employee_user_view_colleagues" ON public."Employee"
  FOR SELECT TO authenticated 
  USING (
    -- Users can view employees they work with on delegations
    id IN (
      SELECT DISTINCT de."employeeId" FROM "DelegationEscort" de
      JOIN "DelegationEscort" de2 ON de."delegationId" = de2."delegationId"
      WHERE de2."employeeId" = private.get_user_employee_id(auth.uid())
      UNION
      SELECT DISTINCT dd."employeeId" FROM "DelegationDriver" dd
      JOIN "DelegationEscort" de ON dd."delegationId" = de."delegationId"
      WHERE de."employeeId" = private.get_user_employee_id(auth.uid())
      UNION
      SELECT DISTINCT de."employeeId" FROM "DelegationEscort" de
      JOIN "DelegationDriver" dd ON de."delegationId" = dd."delegationId"
      WHERE dd."employeeId" = private.get_user_employee_id(auth.uid())
    )
    OR private.is_admin_or_above()
  );

-- ============================================================================
-- STEP 3: CREATE MISSING POLICIES FOR VEHICLE TABLE (CRITICAL)
-- ============================================================================

-- Vehicle table has RLS enabled but NO policies = users can't access ANY vehicle data
-- This breaks application functionality for non-admin users

CREATE POLICY "vehicle_admin_full_access" ON public."Vehicle"
  FOR ALL TO authenticated 
  USING (private.is_admin_or_above());

CREATE POLICY "vehicle_user_view_assigned" ON public."Vehicle"
  FOR SELECT TO authenticated 
  USING (
    -- Users can view vehicles assigned to their delegations
    id IN (
      SELECT dv."vehicleId" FROM "DelegationVehicle" dv
      JOIN "DelegationEscort" de ON dv."delegationId" = de."delegationId"
      WHERE de."employeeId" = private.get_user_employee_id(auth.uid())
      UNION
      SELECT dv."vehicleId" FROM "DelegationVehicle" dv
      JOIN "DelegationDriver" dd ON dv."delegationId" = dd."delegationId"
      WHERE dd."employeeId" = private.get_user_employee_id(auth.uid())
    )
    OR private.is_admin_or_above()
  );

-- ============================================================================
-- STEP 4: CREATE RLS POLICIES FOR TASK TABLE (CRITICAL)
-- ============================================================================

-- Task table is completely open - ANY authenticated user can access ALL tasks
-- This is a massive privacy violation

CREATE POLICY "task_admin_full_access" ON public."Task"
  FOR ALL TO authenticated 
  USING (private.is_admin_or_above());

CREATE POLICY "task_user_view_assigned" ON public."Task"
  FOR SELECT TO authenticated 
  USING (
    "staffEmployeeId" = private.get_user_employee_id(auth.uid())
    OR "driverEmployeeId" = private.get_user_employee_id(auth.uid())
    OR private.is_admin_or_above()
  );

CREATE POLICY "task_user_update_assigned" ON public."Task"
  FOR UPDATE TO authenticated 
  USING (
    "staffEmployeeId" = private.get_user_employee_id(auth.uid())
    OR "driverEmployeeId" = private.get_user_employee_id(auth.uid())
    OR private.is_admin_or_above()
  );

-- ============================================================================
-- STEP 5: CREATE RLS POLICIES FOR SERVICE RECORD TABLE (CRITICAL)
-- ============================================================================

-- ServiceRecord table is completely open - ANY authenticated user can access ALL service records

CREATE POLICY "service_record_admin_full_access" ON public."ServiceRecord"
  FOR ALL TO authenticated 
  USING (private.is_admin_or_above());

CREATE POLICY "service_record_user_view_own_vehicle" ON public."ServiceRecord"
  FOR SELECT TO authenticated 
  USING (
    -- Users can view service records for vehicles they're assigned to
    "vehicleId" IN (
      SELECT dv."vehicleId" FROM "DelegationVehicle" dv
      JOIN "DelegationEscort" de ON dv."delegationId" = de."delegationId"
      WHERE de."employeeId" = private.get_user_employee_id(auth.uid())
      UNION
      SELECT dv."vehicleId" FROM "DelegationVehicle" dv
      JOIN "DelegationDriver" dd ON dv."delegationId" = dd."delegationId"
      WHERE dd."employeeId" = private.get_user_employee_id(auth.uid())
    )
    OR private.is_admin_or_above()
  );

-- ============================================================================
-- STEP 6: CREATE RLS POLICIES FOR SUBTASK TABLE (CRITICAL)
-- ============================================================================

-- SubTask table is completely open - ANY authenticated user can access ALL subtasks

CREATE POLICY "subtask_admin_full_access" ON public."SubTask"
  FOR ALL TO authenticated 
  USING (private.is_admin_or_above());

CREATE POLICY "subtask_user_view_assigned" ON public."SubTask"
  FOR SELECT TO authenticated 
  USING (
    -- Users can view subtasks for tasks they're assigned to
    "taskId" IN (
      SELECT id FROM "Task" 
      WHERE "staffEmployeeId" = private.get_user_employee_id(auth.uid())
         OR "driverEmployeeId" = private.get_user_employee_id(auth.uid())
    )
    OR private.is_admin_or_above()
  );

CREATE POLICY "subtask_user_update_assigned" ON public."SubTask"
  FOR UPDATE TO authenticated 
  USING (
    "taskId" IN (
      SELECT id FROM "Task" 
      WHERE "staffEmployeeId" = private.get_user_employee_id(auth.uid())
         OR "driverEmployeeId" = private.get_user_employee_id(auth.uid())
    )
    OR private.is_admin_or_above()
  );

-- ============================================================================
-- STEP 7: CREATE RLS POLICIES FOR FLIGHT DETAILS TABLE (CRITICAL)
-- ============================================================================

-- FlightDetails table is completely open - ANY authenticated user can access ALL flight details

CREATE POLICY "flight_details_admin_full_access" ON public."FlightDetails"
  FOR ALL TO authenticated 
  USING (private.is_admin_or_above());

CREATE POLICY "flight_details_user_view_assigned" ON public."FlightDetails"
  FOR SELECT TO authenticated 
  USING (
    -- Users can view flight details for delegations they're assigned to
    "delegationId" IN (
      SELECT de."delegationId" FROM "DelegationEscort" de
      WHERE de."employeeId" = private.get_user_employee_id(auth.uid())
      UNION
      SELECT dd."delegationId" FROM "DelegationDriver" dd
      WHERE dd."employeeId" = private.get_user_employee_id(auth.uid())
    )
    OR private.is_admin_or_above()
  );

-- ============================================================================
-- STEP 8: CREATE RLS POLICIES FOR STATUS ENTRY TABLES (CRITICAL)
-- ============================================================================

-- TaskStatusEntry table is completely open
CREATE POLICY "task_status_admin_full_access" ON public."TaskStatusEntry"
  FOR ALL TO authenticated 
  USING (private.is_admin_or_above());

CREATE POLICY "task_status_user_view_assigned" ON public."TaskStatusEntry"
  FOR SELECT TO authenticated 
  USING (
    "taskId" IN (
      SELECT id FROM "Task" 
      WHERE "staffEmployeeId" = private.get_user_employee_id(auth.uid())
         OR "driverEmployeeId" = private.get_user_employee_id(auth.uid())
    )
    OR private.is_admin_or_above()
  );

-- EmployeeStatusEntry table is completely open
CREATE POLICY "employee_status_admin_full_access" ON public."EmployeeStatusEntry"
  FOR ALL TO authenticated 
  USING (private.is_admin_or_above());

CREATE POLICY "employee_status_user_view_own" ON public."EmployeeStatusEntry"
  FOR SELECT TO authenticated 
  USING (
    "employeeId" = private.get_user_employee_id(auth.uid())
    OR private.is_admin_or_above()
  );

-- DelegationStatusEntry table is completely open
CREATE POLICY "delegation_status_admin_full_access" ON public."DelegationStatusEntry"
  FOR ALL TO authenticated 
  USING (private.is_admin_or_above());

CREATE POLICY "delegation_status_user_view_assigned" ON public."DelegationStatusEntry"
  FOR SELECT TO authenticated 
  USING (
    "delegationId" IN (
      SELECT de."delegationId" FROM "DelegationEscort" de
      WHERE de."employeeId" = private.get_user_employee_id(auth.uid())
      UNION
      SELECT dd."delegationId" FROM "DelegationDriver" dd
      WHERE dd."employeeId" = private.get_user_employee_id(auth.uid())
    )
    OR private.is_admin_or_above()
  );

-- ============================================================================
-- STEP 9: CREATE RLS POLICIES FOR DELEGATE TABLE (CRITICAL)
-- ============================================================================

-- Delegate table is completely open
CREATE POLICY "delegate_admin_full_access" ON public."Delegate"
  FOR ALL TO authenticated 
  USING (private.is_admin_or_above());

CREATE POLICY "delegate_user_view_assigned" ON public."Delegate"
  FOR SELECT TO authenticated 
  USING (
    -- Users can view delegates for delegations they're assigned to
    "delegationId" IN (
      SELECT de."delegationId" FROM "DelegationEscort" de
      WHERE de."employeeId" = private.get_user_employee_id(auth.uid())
      UNION
      SELECT dd."delegationId" FROM "DelegationDriver" dd
      WHERE dd."employeeId" = private.get_user_employee_id(auth.uid())
    )
    OR private.is_admin_or_above()
  );

-- ============================================================================
-- STEP 10: REMOVE DUPLICATE PUBLIC SCHEMA FUNCTIONS (SECURITY CLEANUP)
-- ============================================================================

-- Remove security functions from public schema to prevent API exposure
DROP FUNCTION IF EXISTS public.get_user_employee_id(UUID);
DROP FUNCTION IF EXISTS public.get_user_role(UUID);
DROP FUNCTION IF EXISTS public.is_admin(UUID);
DROP FUNCTION IF EXISTS public.is_admin_user();

-- ============================================================================
-- VERIFICATION QUERIES - RUN THESE TO CONFIRM FIXES
-- ============================================================================

-- Check RLS status for all tables
SELECT 
  tablename, 
  rowsecurity,
  CASE 
    WHEN rowsecurity = true THEN '✅ RLS ENABLED'
    ELSE '❌ RLS DISABLED'
  END as status
FROM pg_tables 
WHERE schemaname = 'public' 
  AND tablename NOT LIKE '\_%' 
  AND tablename != 'audit_logs'
ORDER BY rowsecurity DESC, tablename;

-- Check policy count for each table
SELECT 
  t.tablename,
  t.rowsecurity,
  COUNT(p.policyname) as policy_count,
  CASE 
    WHEN t.rowsecurity = false THEN '🔴 NO RLS'
    WHEN COUNT(p.policyname) = 0 THEN '🔴 NO POLICIES'
    ELSE '✅ PROTECTED'
  END as security_status
FROM pg_tables t 
LEFT JOIN pg_policies p ON t.tablename = p.tablename AND t.schemaname = p.schemaname
WHERE t.schemaname = 'public' 
  AND t.tablename NOT LIKE '\_%' 
  AND t.tablename != 'audit_logs'
GROUP BY t.tablename, t.rowsecurity
ORDER BY security_status, t.tablename;

-- ============================================================================
-- EXECUTION NOTES
-- ============================================================================
--
-- 1. Execute these fixes IMMEDIATELY - critical security vulnerabilities exist
-- 2. Test with different user roles after implementation
-- 3. Monitor application functionality to ensure no legitimate access is blocked
-- 4. Update WorkHub Central Planning System documentation with completion status
-- 5. Run verification queries to confirm all tables are properly protected
--
-- EXPECTED RESULT: All tables should show "✅ PROTECTED" status
-- ============================================================================
