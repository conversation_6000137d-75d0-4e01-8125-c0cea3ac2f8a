/**
 * Advanced Employee Table Component
 *
 * Demonstrates the full power of our DataTable component with advanced features
 * for employee management. Shows consistent patterns with VehicleTable while
 * handling employee-specific data and actions.
 */

'use client';

import type { ColumnDef } from '@tanstack/react-table';
import {
  Archive,
  MessageSquare,
  Trash,
  User,
  Calendar,
  Download,
} from 'lucide-react';
import { useRouter } from 'next/navigation';

import type { Employee } from '@/lib/types/domain';

import {
  DataTable,
  createSelectionColumn,
  createTextColumn,
  createStatusColumn,
  createDateColumn,
  createEnhancedActionsColumn,
  statusConfigs,
} from '@/components/ui/tables';
import { useToast } from '@/hooks/utils/use-toast';

interface EmployeeTableProps {
  employees: Employee[];
  onDelete?: (employee: Employee) => Promise<void>;
  onBulkDelete?: (employees: Employee[]) => Promise<void>;
  onBulkArchive?: (employees: Employee[]) => Promise<void>;
  onSendMessage?: (employee: Employee) => void;
  className?: string;
}

export const EmployeeTable = ({
  employees,
  onDelete,
  onBulkDelete,
  onBulkArchive,
  onSendMessage,
  className = '',
}: EmployeeTableProps) => {
  const router = useRouter();
  const { toast } = useToast();

  // Define advanced columns using helper functions
  const columns: ColumnDef<Employee>[] = [
    // Row selection checkbox
    createSelectionColumn<Employee>(),

    // Employee name and ID
    createTextColumn('name', 'Name', {
      className: 'font-semibold',
    }),

    // Employee ID with monospace styling
    createTextColumn('employeeId', 'Employee ID', {
      className: 'font-mono text-sm bg-gray-100 px-2 py-1 rounded',
    }),

    // Position/Role
    createTextColumn('position', 'Position'),

    // Department
    createTextColumn('department', 'Department'),

    // Status with proper badge styling
    createStatusColumn('status', 'Status', statusConfigs.employee),

    // Contact information
    createTextColumn('contactInfo', 'Contact', {
      maxLength: 20,
    }),

    // Hire date
    createDateColumn('hireDate', 'Hire Date', 'MMM dd, yyyy'),

    // Enhanced actions with employee-specific options
    createEnhancedActionsColumn({
      viewHref: employee => `/employees/${employee.id}`,
      editHref: employee => `/employees/${employee.id}/edit`,
      ...(onDelete && {
        onDelete: (employee: Employee) => {
          onDelete(employee);
        },
      }),
      showCopyId: true,
      customActions: [
        {
          label: 'Send Message',
          icon: ({ className }: { className?: string }) => (
            <MessageSquare className={className} />
          ),
          onClick: employee => {
            if (onSendMessage) {
              onSendMessage(employee);
            } else {
              toast({
                title: 'Feature Coming Soon',
                description: `Messaging functionality for ${employee.name}`,
              });
            }
          },
        },
        {
          label: 'View Schedule',
          icon: ({ className }: { className?: string }) => (
            <Calendar className={className} />
          ),
          onClick: employee => {
            router.push(`/employees/${employee.id}/schedule`);
          },
        },
        {
          label: 'Archive',
          icon: ({ className }: { className?: string }) => (
            <Archive className={className} />
          ),
          onClick: employee => {
            toast({
              title: 'Employee Archived',
              description: `${employee.name} has been archived`,
            });
          },
          variant: 'destructive',
        },
      ],
    }),
  ];

  // Bulk actions for selected rows
  const bulkActions = [
    {
      label: 'Send Bulk Message',
      icon: ({ className }: { className?: string }) => (
        <MessageSquare className={className} />
      ),
      onClick: async (selectedEmployees: Employee[]) => {
        toast({
          title: 'Bulk Message',
          description: `Sending message to ${selectedEmployees.length} employees`,
        });
      },
    },
    {
      label: 'Archive Selected',
      icon: ({ className }: { className?: string }) => (
        <Archive className={className} />
      ),
      onClick: async (selectedEmployees: Employee[]) => {
        if (onBulkArchive) {
          await onBulkArchive(selectedEmployees);
          toast({
            title: 'Employees Archived',
            description: `${selectedEmployees.length} employees have been archived`,
          });
        }
      },
    },
    {
      label: 'Delete Selected',
      icon: ({ className }: { className?: string }) => (
        <Trash className={className} />
      ),
      onClick: async (selectedEmployees: Employee[]) => {
        if (onBulkDelete) {
          await onBulkDelete(selectedEmployees);
          toast({
            title: 'Employees Deleted',
            description: `${selectedEmployees.length} employees have been deleted`,
          });
        }
      },
      variant: 'destructive' as const,
    },
  ];

  return (
    <DataTable
      data={employees}
      columns={columns}
      className={className}
      searchPlaceholder="Search employees by name, ID, or department..."
      searchColumn="name"
      onRowClick={employee => router.push(`/employees/${employee.id}`)}
      emptyMessage="No employees found. Add your first employee to get started."
      pageSize={25}
      // Advanced features
      enableRowSelection={true}
      enableBulkActions={true}
      bulkActions={bulkActions}
      enableColumnVisibility={true}
      // Professional styling with employee theme
      tableClassName="shadow-lg"
      headerClassName="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20"
      rowClassName="hover:bg-green-50/50 dark:hover:bg-green-900/10"
    />
  );
};

export default EmployeeTable;
