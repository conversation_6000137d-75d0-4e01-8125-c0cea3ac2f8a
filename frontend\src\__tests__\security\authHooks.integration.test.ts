/**
 * @file Authentication Hooks Integration Tests
 * @description Tests for verification loop prevention in authentication hooks
 */

import { renderHook, act } from '@testing-library/react';
import { useSessionSecurity } from '../../lib/api/security/hooks/useSessionSecurity';
import { useTokenManagement } from '../../lib/api/security/hooks/useTokenManagement';
import { SecurityUtils, SessionManager } from '../../lib/security';

// Mock the AuthContext
const mockAuthContext = {
  user: { id: 'test-user', email: '<EMAIL>' },
  session: { access_token: 'mock-token' },
  signOut: jest.fn(),
  refreshSession: jest.fn(),
};

jest.mock('../../contexts/AuthContext', () => ({
  useAuthContext: () => mockAuthContext,
}));

// Mock the token refresh service
const mockTokenRefreshService = {
  refreshNow: jest.fn(),
};

jest.mock('../../lib/services/TokenRefreshService', () => ({
  getTokenRefreshService: () => mockTokenRefreshService,
}));

// Mock console
const mockConsole = {
  log: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
  debug: jest.fn(),
};

describe('Authentication Hooks Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    Object.defineProperty(global, 'console', {
      value: mockConsole,
      writable: true,
    });

    // Reset security state
    SecurityUtils.resetCircuitBreakerForTesting();
    SecurityUtils.initializeCircuitBreaker();
    SessionManager.initialize();
  });

  afterEach(() => {
    SessionManager.cleanup();
  });

  describe('useSessionSecurity Hook', () => {
    test('should initialize without triggering verification loops', () => {
      const { result } = renderHook(() => useSessionSecurity());

      expect(result.current.isSessionActive).toBe(true);
      expect(result.current.sessionWarning).toBe(false);
      expect(result.current.isSessionExpired).toBe(false);
    });

    test('should handle session timeout with circuit breaker protection', async () => {
      const { result } = renderHook(() => useSessionSecurity());

      // Mock session timeout
      jest.spyOn(SessionManager, 'detectTimeout').mockReturnValue(true);

      await act(async () => {
        // Trigger timeout check
        await new Promise(resolve => setTimeout(resolve, 100));
      });

      // Should handle timeout gracefully without loops
      const state = SecurityUtils.getCircuitBreakerState();
      expect(state.attemptCount).toBeLessThan(5); // Should not hit circuit breaker
    });

    test('should prevent concurrent session checks', async () => {
      const { result } = renderHook(() => useSessionSecurity());

      // Simulate multiple rapid session checks
      const promises = Array.from({ length: 10 }, () =>
        act(async () => {
          await new Promise(resolve => setTimeout(resolve, 10));
        })
      );

      await Promise.all(promises);

      // Circuit breaker should prevent excessive operations
      const state = SecurityUtils.getCircuitBreakerState();
      expect(state.activeOperations.length).toBeLessThanOrEqual(3);
    });
  });

  describe('useTokenManagement Hook', () => {
    test('should initialize token management without loops', () => {
      const { result } = renderHook(() =>
        useTokenManagement('mock-access-token')
      );

      expect(result.current.isTokenValid).toBe(true);
      expect(result.current.isTokenExpired).toBe(false);
      expect(result.current.tokenError).toBeNull();
    });

    test('should handle token refresh with circuit breaker coordination', async () => {
      mockTokenRefreshService.refreshNow.mockResolvedValue(true);

      const { result } = renderHook(() =>
        useTokenManagement('mock-access-token')
      );

      await act(async () => {
        const success = await result.current.refreshToken();
        expect(success).toBe(true);
      });

      // Should record success and not trigger circuit breaker
      const state = SecurityUtils.getCircuitBreakerState();
      expect(state.attemptCount).toBe(0);
    });

    test('should prevent concurrent token refresh operations', async () => {
      mockTokenRefreshService.refreshNow.mockImplementation(
        () => new Promise(resolve => setTimeout(() => resolve(true), 100))
      );

      const { result } = renderHook(() =>
        useTokenManagement('mock-access-token')
      );

      // Start multiple refresh operations
      const refreshPromises = Array.from({ length: 5 }, () =>
        act(async () => {
          await result.current.refreshToken();
        })
      );

      await Promise.all(refreshPromises);

      // Should coordinate operations and prevent duplicates
      expect(mockTokenRefreshService.refreshNow).toHaveBeenCalledTimes(1);
    });

    test('should handle token validation failures gracefully', async () => {
      const { result } = renderHook(() => useTokenManagement('invalid-token'));

      await act(async () => {
        // Simulate validation failure
        const validation = result.current.validateCurrentToken();
        expect(validation).toBeDefined();
      });

      // Should handle failure without triggering circuit breaker
      const state = SecurityUtils.getCircuitBreakerState();
      expect(state.isOpen).toBe(false);
    });
  });

  describe('Cross-Hook Coordination', () => {
    test('should coordinate between session security and token management', async () => {
      const sessionHook = renderHook(() => useSessionSecurity());
      const tokenHook = renderHook(() => useTokenManagement('mock-token'));

      // Both hooks should work together without conflicts
      expect(sessionHook.result.current.isSessionActive).toBe(true);
      expect(tokenHook.result.current.isTokenValid).toBe(true);

      // Simulate concurrent operations
      await act(async () => {
        await Promise.all([
          new Promise(resolve => setTimeout(resolve, 50)),
          tokenHook.result.current.refreshToken(),
        ]);
      });

      // Should maintain coordination
      const state = SecurityUtils.getCircuitBreakerState();
      expect(state.activeOperations.length).toBeLessThanOrEqual(2);
    });

    test('should handle authentication failures across hooks', async () => {
      mockTokenRefreshService.refreshNow.mockRejectedValue(
        new Error('Authentication failed')
      );

      const sessionHook = renderHook(() => useSessionSecurity());
      const tokenHook = renderHook(() => useTokenManagement('mock-token'));

      await act(async () => {
        try {
          await tokenHook.result.current.refreshToken();
        } catch (error) {
          // Expected to fail
        }
      });

      // Should handle failure gracefully without affecting session
      expect(sessionHook.result.current.isSessionActive).toBe(true);
      expect(mockConsole.error).toHaveBeenCalled();
    });
  });

  describe('Circuit Breaker Integration', () => {
    test('should respect circuit breaker state across hooks', async () => {
      // Open the circuit breaker
      for (let i = 0; i < 5; i++) {
        SecurityUtils.recordSecurityAttempt();
      }

      const sessionHook = renderHook(() => useSessionSecurity());
      const tokenHook = renderHook(() => useTokenManagement('mock-token'));

      await act(async () => {
        // Operations should be blocked
        const tokenRefresh = await tokenHook.result.current.refreshToken();
        expect(tokenRefresh).toBe(false);
      });

      // Should log circuit breaker blocks
      expect(mockConsole.debug).toHaveBeenCalledWith(
        expect.stringContaining('blocked by circuit breaker')
      );
    });

    test('should recover from circuit breaker state', async () => {
      // Open the circuit breaker
      for (let i = 0; i < 5; i++) {
        SecurityUtils.recordSecurityAttempt();
      }

      // Mock time passage for reset
      const originalNow = Date.now;
      Date.now = jest.fn(() => originalNow() + 360000); // 6 minutes

      try {
        const tokenHook = renderHook(() => useTokenManagement('mock-token'));

        await act(async () => {
          mockTokenRefreshService.refreshNow.mockResolvedValue(true);
          const success = await tokenHook.result.current.refreshToken();
          expect(success).toBe(true);
        });

        // Circuit should be reset
        const state = SecurityUtils.getCircuitBreakerState();
        expect(state.isOpen).toBe(false);
      } finally {
        Date.now = originalNow;
      }
    });
  });

  describe('Error Recovery Scenarios', () => {
    test('should handle session corruption during token operations', async () => {
      const tokenHook = renderHook(() => useTokenManagement('mock-token'));

      // Corrupt session state
      jest
        .spyOn(SessionManager, 'validateSessionConsistency')
        .mockReturnValue(false);
      jest
        .spyOn(SessionManager, 'recoverFromCorruptedState')
        .mockReturnValue(true);

      await act(async () => {
        mockTokenRefreshService.refreshNow.mockResolvedValue(true);
        const success = await tokenHook.result.current.refreshToken();
        expect(success).toBe(true);
      });

      // Should attempt recovery
      expect(SessionManager.recoverFromCorruptedState).toHaveBeenCalled();
    });

    test('should handle network failures gracefully', async () => {
      mockTokenRefreshService.refreshNow.mockRejectedValue(
        new Error('Network error')
      );

      const tokenHook = renderHook(() => useTokenManagement('mock-token'));

      await act(async () => {
        const success = await tokenHook.result.current.refreshToken();
        expect(success).toBe(false);
      });

      // Should handle network errors without triggering circuit breaker excessively
      const state = SecurityUtils.getCircuitBreakerState();
      expect(state.attemptCount).toBe(1);
    });
  });

  describe('Performance and Memory', () => {
    test('should not create memory leaks with multiple hook instances', () => {
      const hooks = Array.from({ length: 10 }, () =>
        renderHook(() => useSessionSecurity())
      );

      // All hooks should initialize successfully
      hooks.forEach(({ result }) => {
        expect(result.current.isSessionActive).toBe(true);
      });

      // Cleanup should work without errors
      hooks.forEach(({ unmount }) => {
        expect(() => unmount()).not.toThrow();
      });
    });

    test('should handle rapid hook mounting and unmounting', () => {
      for (let i = 0; i < 20; i++) {
        const { unmount } = renderHook(() => useTokenManagement('mock-token'));
        unmount();
      }

      // Should not accumulate active operations
      const state = SecurityUtils.getCircuitBreakerState();
      expect(state.activeOperations.length).toBe(0);
    });
  });
});
