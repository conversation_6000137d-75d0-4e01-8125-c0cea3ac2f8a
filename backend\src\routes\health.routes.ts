import type { Request, Response } from 'express';

import { Router } from 'express';

import type { SystemHealthReport } from '../services/health.service.js';

import {
  checkEnhancedDatabaseHealth,
  getComprehensiveHealthReport,
  getHealthTrendData, // Type import
} from '../services/health.service.js';
import logger from '../utils/logger.js'; // For /detailed endpoint error logging

const router = Router();

// Original path: /api/health
router.get('/', async (req: Request, res: Response): Promise<void> => {
  try {
    const databaseHealth = await checkEnhancedDatabaseHealth();
    const isHealthy = databaseHealth.status === 'healthy';

    res.status(isHealthy ? 200 : 503).json({
      data: {
        environment: process.env.NODE_ENV ?? 'development',
        message: isHealthy ? 'Backend service is healthy' : 'Backend service is degraded',
        status: isHealthy ? 'UP' : 'DOWN',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        version: process.env.npm_package_version ?? '1.0.0',
      },
      status: isHealthy ? 'success' : 'error',
    });
  } catch (error: unknown) {
    res.status(503).json({
      error: (error as Error).message,
      message: 'Backend service is unhealthy',
      status: 'error',
      timestamp: new Date().toISOString(),
    });
  }
});

// Original path: /api/health/detailed
router.get('/detailed', async (req: Request, res: Response): Promise<void> => {
  try {
    const healthReportPromise = getComprehensiveHealthReport();
    const timeoutPromise = new Promise(
      (_, reject) =>
        setTimeout(() => {
          reject(new Error('Health check timeout'));
        }, 30000), // 30 seconds timeout
    );

    const healthReport = await Promise.race([healthReportPromise, timeoutPromise]);

    if (healthReport instanceof Error) {
      throw healthReport; // Re-throw the timeout error
    }

    res.status(200).json({
      data: healthReport as SystemHealthReport,
      status: 'success',
    });
  } catch (error: unknown) {
    logger.error('Detailed health check failed', {
      endpoint: '/api/health/detailed',
      error: (error as Error).message,
      service: 'health',
    });
    // Fallback report as in app.ts
    const fallbackReport: SystemHealthReport = {
      checks: {
        businessLogic: {
          message: 'Unknown',
          status: 'degraded',
          timestamp: new Date().toISOString(),
        },
        cache: { message: 'Unknown', status: 'degraded', timestamp: new Date().toISOString() },
        circuitBreakers: {
          message: 'Unknown',
          status: 'degraded',
          timestamp: new Date().toISOString(),
        },
        database: { message: 'Unknown', status: 'degraded', timestamp: new Date().toISOString() },
        supabase: { message: 'Unknown', status: 'degraded', timestamp: new Date().toISOString() },
        systemResources: {
          message: 'Unknown',
          status: 'healthy',
          timestamp: new Date().toISOString(),
        },
      },
      environment: process.env.NODE_ENV ?? 'development',
      status: 'degraded',
      summary: {
        degradedChecks: 5,
        healthyChecks: 1,
        totalChecks: 6,
        unhealthyChecks: 0,
      },
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: process.env.npm_package_version ?? '1.0.0',
    };
    res.status(200).json({ data: fallbackReport, status: 'degraded' });
  }
});

// Dependencies health check endpoint
router.get('/dependencies', async (req: Request, res: Response): Promise<void> => {
  try {
    // Check external dependencies - create array structure as expected by frontend
    const dependencyChecks: {
      error: string | undefined;
      lastChecked: string;
      name: string;
      responseTime: number;
      status: string;
    }[] = [
      { error: undefined, lastChecked: '', name: 'Database', responseTime: 0, status: 'healthy' },
      { error: undefined, lastChecked: '', name: 'Supabase', responseTime: 0, status: 'healthy' },
      { error: undefined, lastChecked: '', name: 'Cache', responseTime: 0, status: 'healthy' },
    ];

    // Check database
    const dbStart = Date.now();
    try {
      const dbHealth = await checkEnhancedDatabaseHealth();
      const dbIndex = dependencyChecks.findIndex(dep => dep.name === 'Database');
      dependencyChecks[dbIndex] = {
        error: undefined,
        lastChecked: new Date().toISOString(),
        name: 'Database',
        responseTime: Date.now() - dbStart,
        status: dbHealth.status,
      };
    } catch (error) {
      const dbIndex = dependencyChecks.findIndex(dep => dep.name === 'Database');
      dependencyChecks[dbIndex] = {
        error: (error as Error).message,
        lastChecked: new Date().toISOString(),
        name: 'Database',
        responseTime: Date.now() - dbStart,
        status: 'unhealthy',
      };
    }

    // Add lastChecked to other dependencies
    const timestamp = new Date().toISOString();
    dependencyChecks.forEach(dep => {
      if (!dep.lastChecked) {
        dep.lastChecked = timestamp;
      }
    });

    // Calculate summary
    const healthy = dependencyChecks.filter(dep => dep.status === 'healthy').length;
    const unhealthy = dependencyChecks.filter(dep => dep.status === 'unhealthy').length;
    const degraded = dependencyChecks.filter(dep => dep.status === 'degraded').length;

    res.status(200).json({
      data: {
        dependencies: dependencyChecks,
        summary: {
          degraded,
          healthy,
          total: dependencyChecks.length,
          unhealthy,
        },
      },
      status: 'success',
    });
  } catch (error: unknown) {
    logger.error('Dependencies health check failed', {
      endpoint: '/api/health/dependencies',
      error: (error as Error).message,
      service: 'health',
    });

    res.status(503).json({
      error: (error as Error).message,
      message: 'Dependencies health check failed',
      status: 'error',
      timestamp: new Date().toISOString(),
    });
  }
});

// GET /api/health/trends - Get health trend data for charts
router.get('/trends', async (req: Request, res: Response): Promise<void> => {
  try {
    const timeframe = req.query.timeframe as '1h' | '6h' | '24h' | '7d' || '24h';

    // Validate timeframe parameter
    const validTimeframes = ['1h', '6h', '24h', '7d'];
    if (!validTimeframes.includes(timeframe)) {
      res.status(400).json({
        status: 'error',
        message: 'Invalid timeframe. Must be one of: 1h, 6h, 24h, 7d',
        timestamp: new Date().toISOString(),
      });
      return;
    }

    const trendData = await getHealthTrendData(timeframe);

    res.status(200).json({
      data: trendData,
      status: 'success',
      timestamp: new Date().toISOString(),
    });
  } catch (error: unknown) {
    logger.error('Health trends endpoint failed', {
      endpoint: '/api/health/trends',
      error: (error as Error).message,
      service: 'health',
    });

    res.status(500).json({
      status: 'error',
      message: 'Failed to retrieve health trend data',
      error: (error as Error).message,
      timestamp: new Date().toISOString(),
    });
  }
});

export default router;
