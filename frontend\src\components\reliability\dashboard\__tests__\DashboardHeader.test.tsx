/**
 * @file Unit tests for DashboardHeader component
 * @module components/reliability/dashboard/__tests__/DashboardHeader
 */

import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import React from 'react';

import { DashboardHeader } from '../DashboardHeader';

// Mock the reliability store
const mockReliabilityStore = {
  ui: {
    activeTab: 'overview' as const,
    isFilterPanelOpen: false,
  },
  monitoring: {
    isEnabled: true,
  },
  setActiveTab: jest.fn(),
  setMonitoringEnabled: jest.fn(),
  toggleFilterPanel: jest.fn(),
};

jest.mock('@/lib/hooks', () => ({
  useReliabilityStore: (selector: any) => selector(mockReliabilityStore),
}));

// Mock the reliability queries
const mockReliabilityQueries = {
  alerts: {
    data: [
      { id: '1', status: 'active', severity: 'high', message: 'High alert' },
      {
        id: '2',
        status: 'active',
        severity: 'critical',
        message: 'Critical alert',
      },
      {
        id: '3',
        status: 'resolved',
        severity: 'medium',
        message: 'Resolved alert',
      },
    ],
  },
  alertsStatistics: { data: { total: 3, active: 2 } },
};

jest.mock('@/lib/stores/queries/useReliability', () => ({
  useReliability: () => mockReliabilityQueries,
}));

// Mock the ConnectionStatusIndicator
jest.mock('../ConnectionStatusIndicator', () => ({
  ConnectionStatusIndicator: () => (
    <div data-testid="connection-status">Connection Status</div>
  ),
}));

describe('DashboardHeader', () => {
  let queryClient: QueryClient;

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    });
    jest.clearAllMocks();
  });

  const renderWithProviders = (component: React.ReactElement) => {
    return render(
      <QueryClientProvider client={queryClient}>
        {component}
      </QueryClientProvider>
    );
  };

  describe('Rendering', () => {
    it('should render the header with title and description', () => {
      renderWithProviders(<DashboardHeader />);

      expect(screen.getByText('Reliability Dashboard')).toBeInTheDocument();
      expect(
        screen.getByText('Real-time system monitoring and health insights')
      ).toBeInTheDocument();
    });

    it('should render connection status indicator', () => {
      renderWithProviders(<DashboardHeader />);

      expect(screen.getByTestId('connection-status')).toBeInTheDocument();
    });

    it('should render navigation tabs', () => {
      renderWithProviders(<DashboardHeader />);

      expect(
        screen.getByRole('tab', { name: /overview dashboard/i })
      ).toBeInTheDocument();
      expect(
        screen.getByRole('tab', { name: /system health monitoring/i })
      ).toBeInTheDocument();
      expect(
        screen.getByRole('tab', { name: /performance metrics/i })
      ).toBeInTheDocument();
      expect(
        screen.getByRole('tab', { name: /active alerts and notifications/i })
      ).toBeInTheDocument();
      expect(
        screen.getByRole('tab', { name: /historical data and trends/i })
      ).toBeInTheDocument();
    });

    it('should render with custom className', () => {
      renderWithProviders(<DashboardHeader className="custom-class" />);

      const headerElement = screen.getByRole('banner');
      expect(headerElement).toHaveClass('custom-class');
    });
  });

  describe('Monitoring Controls', () => {
    it('should show pause button when monitoring is enabled', () => {
      mockReliabilityStore.monitoring.isEnabled = true;

      renderWithProviders(<DashboardHeader />);

      expect(
        screen.getByRole('button', { name: /pause monitoring/i })
      ).toBeInTheDocument();
      expect(screen.getByText('Pause')).toBeInTheDocument();
    });

    it('should show resume button when monitoring is disabled', () => {
      mockReliabilityStore.monitoring.isEnabled = false;

      renderWithProviders(<DashboardHeader />);

      expect(
        screen.getByRole('button', { name: /resume monitoring/i })
      ).toBeInTheDocument();
      expect(screen.getByText('Resume')).toBeInTheDocument();
    });

    it('should toggle monitoring when button is clicked', async () => {
      const user = userEvent.setup();
      renderWithProviders(<DashboardHeader />);

      const toggleButton = screen.getByRole('button', {
        name: /pause monitoring/i,
      });
      await user.click(toggleButton);

      expect(mockReliabilityStore.setMonitoringEnabled).toHaveBeenCalledWith(
        false
      );
    });
  });

  describe('Settings Menu', () => {
    it('should render settings dropdown', () => {
      renderWithProviders(<DashboardHeader />);

      expect(
        screen.getByRole('button', { name: /dashboard settings/i })
      ).toBeInTheDocument();
    });

    it('should open settings menu when clicked', async () => {
      const user = userEvent.setup();
      renderWithProviders(<DashboardHeader />);

      const settingsButton = screen.getByRole('button', {
        name: /dashboard settings/i,
      });
      await user.click(settingsButton);

      expect(screen.getByText('Dashboard Settings')).toBeInTheDocument();
      expect(screen.getByText('Export Data')).toBeInTheDocument();
    });

    it('should toggle filter panel when menu item is clicked', async () => {
      const user = userEvent.setup();
      renderWithProviders(<DashboardHeader />);

      const settingsButton = screen.getByRole('button', {
        name: /dashboard settings/i,
      });
      await user.click(settingsButton);

      const filterMenuItem = screen.getByText('Show Filters');
      await user.click(filterMenuItem);

      expect(mockReliabilityStore.toggleFilterPanel).toHaveBeenCalled();
    });

    it('should show "Hide Filters" when filter panel is open', async () => {
      mockReliabilityStore.ui.isFilterPanelOpen = true;
      const user = userEvent.setup();
      renderWithProviders(<DashboardHeader />);

      const settingsButton = screen.getByRole('button', {
        name: /dashboard settings/i,
      });
      await user.click(settingsButton);

      expect(screen.getByText('Hide Filters')).toBeInTheDocument();
    });
  });

  describe('Tab Navigation', () => {
    it('should highlight the active tab', () => {
      mockReliabilityStore.ui.activeTab = 'health' as any;

      renderWithProviders(<DashboardHeader />);

      const healthTab = screen.getByRole('tab', {
        name: /system health monitoring/i,
      });
      expect(healthTab).toHaveAttribute('data-state', 'active');
    });

    it('should call setActiveTab when tab is clicked', async () => {
      const user = userEvent.setup();
      renderWithProviders(<DashboardHeader />);

      const metricsTab = screen.getByRole('tab', {
        name: /performance metrics/i,
      });
      await user.click(metricsTab);

      expect(mockReliabilityStore.setActiveTab).toHaveBeenCalledWith('metrics');
    });
  });

  describe('Alert Badges', () => {
    it('should show alert count badge when there are active alerts', () => {
      renderWithProviders(<DashboardHeader />);

      const alertsTab = screen.getByRole('tab', {
        name: /active alerts and notifications/i,
      });
      expect(alertsTab).toBeInTheDocument();

      // Should show badge with count of 2 (active alerts)
      expect(screen.getByText('2')).toBeInTheDocument();
    });

    it('should show critical alert styling when there are critical alerts', () => {
      renderWithProviders(<DashboardHeader />);

      const badge = screen.getByText('2');
      expect(badge).toHaveClass('bg-destructive'); // Critical alert styling
    });

    it('should not show alert badge when there are no active alerts', () => {
      mockReliabilityQueries.alerts.data = [];

      renderWithProviders(<DashboardHeader />);

      expect(screen.queryByText('0')).not.toBeInTheDocument();
    });
  });

  describe('Alert Summary Bar', () => {
    it('should show alert summary when there are active alerts', () => {
      renderWithProviders(<DashboardHeader />);

      expect(screen.getByText('2 active alerts')).toBeInTheDocument();
      expect(screen.getByText('(1 critical)')).toBeInTheDocument();
    });

    it('should show view alerts button', async () => {
      const user = userEvent.setup();
      renderWithProviders(<DashboardHeader />);

      const viewAlertsButton = screen.getByRole('button', {
        name: /view alerts/i,
      });
      expect(viewAlertsButton).toBeInTheDocument();

      await user.click(viewAlertsButton);
      expect(mockReliabilityStore.setActiveTab).toHaveBeenCalledWith('alerts');
    });

    it('should not show alert summary when there are no active alerts', () => {
      mockReliabilityQueries.alerts.data = [];

      renderWithProviders(<DashboardHeader />);

      expect(screen.queryByText(/active alerts/)).not.toBeInTheDocument();
    });

    it('should handle singular alert text correctly', () => {
      mockReliabilityQueries.alerts.data = [
        {
          id: '1',
          status: 'active',
          severity: 'medium',
          message: 'Single alert',
        },
      ];

      renderWithProviders(<DashboardHeader />);

      expect(screen.getByText('1 active alert')).toBeInTheDocument(); // Singular form
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA labels', () => {
      renderWithProviders(<DashboardHeader />);

      const headerElement = screen.getByRole('banner');
      expect(headerElement).toHaveAttribute(
        'aria-label',
        'Reliability dashboard header'
      );
    });

    it('should have accessible tab navigation', () => {
      renderWithProviders(<DashboardHeader />);

      const tabs = screen.getAllByRole('tab');
      tabs.forEach(tab => {
        expect(tab).toHaveAttribute('aria-label');
      });
    });

    it('should have accessible button labels', () => {
      renderWithProviders(<DashboardHeader />);

      const pauseButton = screen.getByRole('button', {
        name: /pause monitoring/i,
      });
      expect(pauseButton).toHaveAttribute('aria-label', 'Pause monitoring');

      const settingsButton = screen.getByRole('button', {
        name: /dashboard settings/i,
      });
      expect(settingsButton).toBeInTheDocument();
    });
  });

  describe('Responsive Design', () => {
    it('should hide text on small screens for tabs', () => {
      renderWithProviders(<DashboardHeader />);

      // Check that tab text has responsive classes
      const overviewTab = screen.getByRole('tab', {
        name: /overview dashboard/i,
      });
      const tabText = overviewTab.querySelector('span');
      expect(tabText).toHaveClass('hidden', 'sm:inline');
    });
  });
});
