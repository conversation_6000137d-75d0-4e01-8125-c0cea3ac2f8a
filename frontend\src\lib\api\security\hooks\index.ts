/**
 * @file Security Hooks Index
 * @description Centralized exports for all security-related hooks
 *
 * Includes:
 * - Original security hooks (Phase 1)
 * - New Phase 3 React Integration Layer hooks
 */

// Security hooks (Phase 1)
export { useCSRFProtection } from './useCSRFProtection';

export type { UseCSRFProtectionReturn } from './useCSRFProtection';

// Input validation and sanitization
export {
  useEmployeeValidation,
  useFieldValidation,
  useInputValidation,
  type UseInputValidationReturn,
  useVehicleValidation,
  type ValidationActions,
  type ValidationState,
} from './useInputValidation';

// Permissions Hook (SRP)
export { usePermissions } from './usePermissions';
export type { UsePermissionsReturn } from './usePermissions';

// Secure API communication
export {
  type ApiResponse,
  type RequestConfig,
  useSecureApi,
  type UseSecureApiReturn,
} from './useSecureApi';
export {
  useSecureApiClient,
  useSecureApiClientFeatures,
  useSecureApiReplacement,
} from './useSecureApiClient';
export type {
  SecureApiRequestConfig,
  ApiResponse as SecureApiResponse,
  UseSecureApiClientReturn,
} from './useSecureApiClient';
// Phase 3: React Integration Layer hooks
export { useSecureHttpClient } from './useSecureHttpClient';

export type {
  UseSecureHttpClientConfig,
  UseSecureHttpClientReturn,
} from './useSecureHttpClient';
export { useSecurityMonitoring } from './useSecurityMonitoring';

export type { UseSecurityMonitoringReturn } from './useSecurityMonitoring';
export { useSessionSecurity } from './useSessionSecurity';

export type { UseSessionSecurityReturn } from './useSessionSecurity';
export { useTokenManagement } from './useTokenManagement';

export type { UseTokenManagementReturn } from './useTokenManagement';
// Token refresh monitoring
export {
  type TokenRefreshStatus,
  type TokenRefreshStatusInfo,
  useAuthenticationRequired,
  useTokenRefreshMessages,
  useTokenRefreshStatus,
} from './useTokenRefreshStatus';

// Security utilities
export const SecurityHookUtils = {
  sanitizeInput: (input: string): string => {
    return input.replaceAll(/[<>]/g, '');
  },

  // Common validation patterns
  validateEmail: (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },

  validatePassword: (password: string): boolean => {
    // At least 8 characters, 1 uppercase, 1 lowercase, 1 number
    const passwordRegex =
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/;
    return passwordRegex.test(password);
  },
} as const;
