'use client';

import type { ReactNode } from 'react';

import React from 'react';

import ErrorBoundary from './ErrorBoundary';

interface DelegationErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
}

/**
 * Specialized error boundary for delegation components
 * Uses the generic ErrorBoundary with delegation specific messaging
 */
const DelegationErrorBoundary: React.FC<DelegationErrorBoundaryProps> = ({
  children,
  fallback,
}) => {
  return (
    <ErrorBoundary
      description="An unexpected error occurred while loading or updating delegation data."
      fallback={fallback}
      onError={(error, errorInfo) => {
        // Log the error with delegation context
        console.error('Delegation component error:', error);
        console.error('Component stack:', errorInfo.componentStack);

        // In a production app, you would send this to a monitoring service
        // Example: errorReportingService.captureError(error, {
        //   context: 'Delegation',
        //   componentStack: errorInfo.componentStack
        // });
      }}
      resetLabel="Try Again"
      title="Error Loading Delegation"
    >
      {children}
    </ErrorBoundary>
  );
};

export default DelegationErrorBoundary;
