/**
 * @file Generic AssignmentSection component for displaying assignment lists
 * @module components/delegations/detail/assignments/AssignmentSection
 */

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface AssignmentSectionProps<T> {
  title: string;
  icon: React.ElementType;
  items: T[];
  renderItem: (item: T, index: number) => React.ReactNode;
  emptyMessage: string;
  className?: string | undefined;
}

/**
 * Generic AssignmentSection component for displaying assignment lists
 * Provides consistent styling and structure for delegates, escorts, drivers, vehicles
 */
export function AssignmentSection<T>({
  title,
  icon: Icon,
  items,
  renderItem,
  emptyMessage,
  className,
}: AssignmentSectionProps<T>) {
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Icon className="h-5 w-5 text-blue-600 dark:text-blue-400" />
            <span>{title}</span>
          </div>
          <Badge
            variant="secondary"
            className="bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900/30 dark:text-blue-300 dark:border-blue-800"
          >
            {items.length}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {items.length > 0 ? (
          <div className="space-y-3">{items.map(renderItem)}</div>
        ) : (
          <div className="flex flex-col items-center justify-center py-8 text-center">
            <div className="rounded-full bg-gray-100 dark:bg-gray-800 p-3 mb-3">
              <Icon className="h-6 w-6 text-gray-400" />
            </div>
            <p className="text-gray-600 dark:text-gray-400 font-medium">
              {emptyMessage}
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

export default AssignmentSection;
