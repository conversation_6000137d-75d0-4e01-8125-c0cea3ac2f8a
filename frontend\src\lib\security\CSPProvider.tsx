/**
 * @file CSP Provider for 2025 Security Standards
 * @module lib/security/CSPProvider
 *
 * Implements nonce-based Content Security Policy following OWASP 2025 guidelines.
 * Provides secure nonce propagation from middleware to client components.
 */

'use client';

import {
  createContext,
  useContext,
  useEffect,
  useState,
  type ReactNode,
} from 'react';
import { validateNonce } from './cspConfig';

interface CSPContextValue {
  nonce: string | null;
  isStrictCSP: boolean;
  violationCount: number;
  isNonceValid: boolean;
  reportViolation: (violation: CSPViolationData) => void;
  getSecureNonce: () => string | null;
  resetViolationCount: () => void;
}

interface CSPViolationData {
  violatedDirective: string;
  blockedURI: string;
  documentURI: string;
  effectiveDirective: string;
  originalPolicy: string;
  sourceFile?: string;
  lineNumber?: number;
  columnNumber?: number;
}

const CSPContext = createContext<CSPContextValue>({
  nonce: null,
  isStrictCSP: false,
  violationCount: 0,
  isNonceValid: false,
  reportViolation: () => {},
  getSecureNonce: () => null,
  resetViolationCount: () => {},
});

interface CSPProviderProps {
  children: ReactNode;
  nonce: string | null;
}

/**
 * CSP Provider Component
 *
 * Provides CSP nonce and violation reporting to child components.
 * Single responsibility: CSP context management only.
 */
export function CSPProvider({ children, nonce }: CSPProviderProps) {
  const [violationCount, setViolationCount] = useState(0);

  // Validate nonce strength and format
  const isNonceValid = nonce ? validateNonce(nonce) : false;

  // Determine if we're using strict CSP (no unsafe directives)
  const isStrictCSP =
    isNonceValid && !process.env.NODE_ENV?.includes('development');

  const reportViolation = (violation: CSPViolationData) => {
    // Increment violation counter
    setViolationCount(prev => prev + 1);

    // Enhanced violation reporting with additional context
    if (typeof window !== 'undefined') {
      const enhancedReport = {
        'csp-report': {
          'document-uri': violation.documentURI,
          'violated-directive': violation.violatedDirective,
          'blocked-uri': violation.blockedURI,
          'effective-directive': violation.effectiveDirective,
          'original-policy': violation.originalPolicy,
          'source-file': violation.sourceFile || '',
          'line-number': violation.lineNumber || 0,
          'column-number': violation.columnNumber || 0,
          referrer: document.referrer,
          disposition: 'enforce',
          'status-code': 200,
          'script-sample': '',
          // Enhanced 2025 fields
          timestamp: new Date().toISOString(),
          'user-agent': navigator.userAgent,
          'violation-count': violationCount + 1,
          'nonce-valid': isNonceValid,
          'strict-csp': isStrictCSP,
        },
      };

      fetch('/api/csp-report', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(enhancedReport),
      }).catch(error => {
        console.error('Failed to report CSP violation:', error);
      });
    }
  };

  const getSecureNonce = (): string | null => {
    return isNonceValid ? nonce : null;
  };

  const resetViolationCount = () => {
    setViolationCount(0);
  };

  const contextValue: CSPContextValue = {
    nonce,
    isStrictCSP,
    violationCount,
    isNonceValid,
    reportViolation,
    getSecureNonce,
    resetViolationCount,
  };

  return (
    <CSPContext.Provider value={contextValue}>{children}</CSPContext.Provider>
  );
}

/**
 * Hook to access CSP context
 *
 * Provides nonce and violation reporting functionality.
 * Single responsibility: CSP context access only.
 */
export function useCSP(): CSPContextValue {
  const context = useContext(CSPContext);

  if (!context) {
    throw new Error('useCSP must be used within a CSPProvider');
  }

  return context;
}

/**
 * Hook for secure nonce access
 *
 * Returns the current nonce for use in script tags and inline styles.
 * Single responsibility: Nonce access only.
 */
export function useNonce(): string | null {
  const { nonce } = useCSP();
  return nonce;
}

/**
 * Hook for CSP violation reporting
 *
 * Returns a function to report CSP violations.
 * Single responsibility: Violation reporting only.
 */
export function useCSPReporting(): (violation: CSPViolationData) => void {
  const { reportViolation } = useCSP();
  return reportViolation;
}

/**
 * Utility function to create nonce-aware script props
 *
 * Helper for Next.js Script components to include nonce automatically.
 */
export function createSecureScriptProps(nonce: string | null) {
  return nonce ? { nonce } : {};
}

/**
 * CSP Violation Event Listener
 *
 * Automatically reports CSP violations when they occur.
 * Should be initialized once in the app root.
 */
export function initializeCSPViolationReporting(
  reportViolation: (violation: CSPViolationData) => void
) {
  if (typeof window === 'undefined') return;

  // Listen for CSP violations
  document.addEventListener('securitypolicyviolation', event => {
    reportViolation({
      violatedDirective: event.violatedDirective,
      blockedURI: event.blockedURI,
      documentURI: event.documentURI,
      effectiveDirective: event.effectiveDirective,
      originalPolicy: event.originalPolicy,
      sourceFile: event.sourceFile,
      lineNumber: event.lineNumber,
      columnNumber: event.columnNumber,
    });
  });

  console.log('🔒 CSP violation reporting initialized');
}
