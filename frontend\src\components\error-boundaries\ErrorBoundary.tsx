'use client';

import type { ErrorInfo, ReactNode } from 'react';

import { <PERSON><PERSON><PERSON>riangle, RefreshCw } from 'lucide-react';
import React, { Component } from 'react';

import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';

interface Props {
  children: ReactNode;
  description?: string;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  resetLabel?: string;
  title?: string;
}

interface State {
  error: Error | null;
  errorInfo: ErrorInfo | null;
  hasError: boolean;
}

/**
 * Generic Error Boundary component
 * Catches errors in its child component tree and displays a fallback UI
 */
class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      error: null,
      errorInfo: null,
      hasError: false,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    // Update state so the next render will show the fallback UI
    return {
      error,
      hasError: true,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    // Update state with error info for detailed reporting
    this.setState({
      errorInfo,
    });

    // Log the error
    console.error('Error caught by ErrorBoundary:', error);
    console.error('Component stack:', errorInfo.componentStack);

    // Call onError prop if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // In a production app, you would send this to a monitoring service
    // Example: errorReportingService.captureError(error, errorInfo);
  }

  handleRetry = (): void => {
    // Reset the error boundary state to trigger a re-render
    this.setState({
      error: null,
      errorInfo: null,
      hasError: false,
    });
  };

  render(): ReactNode {
    const {
      description = 'An unexpected error occurred.',
      resetLabel = 'Try Again',
      title = 'Something went wrong',
    } = this.props;

    if (this.state.hasError) {
      // If a custom fallback is provided, use it
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Otherwise, use the default error UI
      return (
        <Alert className="my-4" variant="destructive">
          <AlertTriangle className="mr-2 size-4" />
          <AlertTitle className="text-lg font-semibold">{title}</AlertTitle>
          <AlertDescription className="mt-2">
            <p className="mb-2">{this.state.error?.message || description}</p>
            {process.env.NODE_ENV !== 'production' && this.state.errorInfo && (
              <details className="mt-2 text-xs">
                <summary>Error details</summary>
                <pre className="mt-2 max-h-[200px] overflow-auto whitespace-pre-wrap rounded bg-slate-100 p-2 dark:bg-slate-900">
                  {this.state.error?.stack}
                  {'\n\nComponent Stack:\n'}
                  {this.state.errorInfo.componentStack}
                </pre>
              </details>
            )}
            <Button
              className="mt-4"
              onClick={this.handleRetry}
              size="sm"
              variant="outline"
            >
              <RefreshCw className="mr-2 size-4" />
              {resetLabel}
            </Button>
          </AlertDescription>
        </Alert>
      );
    }

    // If there's no error, render the children
    return this.props.children;
  }
}

export default ErrorBoundary;
