import type { IncomingMessage, ServerResponse } from 'http';

import http from 'http';
import { Server } from 'socket.io';

import { initializeAndGetApp } from './app.js';
import { startHealthMonitoring } from './services/alerting.service.js';
import { getUnifiedWebSocketService } from './services/UnifiedWebSocketService.js';
import logger from './utils/logger.js';

const PORT = process.env.PORT ?? 3001;

void (async () => {
  const app = await initializeAndGetApp();
  // eslint-disable-next-line @typescript-eslint/no-unsafe-return
  const server = http.createServer((req: IncomingMessage, res: ServerResponse) => app(req, res));

  // Define allowed origins for Socket.IO, similar to app.ts
  const defaultSocketOrigins = ['http://localhost:3000', 'http://localhost:9002'];
  const socketOriginSettingArray = process.env.FRONTEND_URL
    ? process.env.FRONTEND_URL.split(',').map(url => url.trim())
    : defaultSocketOrigins;

  logger.info('[Socket.IO CORS] Allowed Origins:', socketOriginSettingArray);
  logger.info('[Socket.IO CORS] FRONTEND_URL env var:', process.env.FRONTEND_URL);

  const io = new Server(server, {
    cors: {
      credentials: true,
      methods: ['GET', 'POST'],
      origin: (requestOrigin, callback) => {
        logger.debug(`[Socket.IO CORS] Request Origin: ${String(requestOrigin)}`);
        // Allow requests with no origin (like mobile apps or curl requests for testing)
        if (!requestOrigin) {
          logger.debug('[Socket.IO CORS] No origin, allowing.');
          callback(null, true);
          return;
        }
        if (socketOriginSettingArray.includes(requestOrigin)) {
          logger.debug(`[Socket.IO CORS] Origin ${String(requestOrigin)} allowed, reflecting.`);
          callback(null, requestOrigin); // Reflect the specific (single) origin
        } else {
          logger.warn(`[Socket.IO CORS] Origin ${String(requestOrigin)} NOT allowed.`);
          callback(new Error('Socket.IO: Not allowed by CORS'));
        }
      },
    },
  });

  // Initialize UnifiedWebSocketService
  const _unifiedSocketService = getUnifiedWebSocketService(io);

  // Use http server instead of app.listen
  server.listen(PORT, () => {
    logger.info(`Server is running on http://localhost:${String(PORT)}`);

    // Start health monitoring after server is successfully listening
    try {
      logger.info('🚨 PHASE 3 RELIABILITY: Starting health monitoring and alerting...');
      startHealthMonitoring();
      logger.info('✅ Health monitoring started successfully');
    } catch (error) {
      logger.error('❌ Failed to start health monitoring:', error);
      // Don't crash the server if health monitoring fails to start
    }
  });
})();
