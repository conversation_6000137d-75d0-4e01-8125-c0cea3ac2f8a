/**
 * @file Reporting Layout Component
 * @description Provides a consistent layout structure for all reporting pages.
 */

import React from 'react';

interface ReportingLayoutProps {
  title: string;
  description?: string;
  children: React.ReactNode;
  actions?: React.ReactNode;
  filters?: React.ReactNode;
}

export const ReportingLayout: React.FC<ReportingLayoutProps> = ({
  title,
  description,
  children,
  actions,
  filters,
}) => {
  return (
    <div className="flex flex-col h-full p-4 md:p-6 lg:p-8 gap-6">
      <header className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div className="flex-grow">
          <h1 className="text-2xl md:text-3xl font-bold tracking-tight text-gray-900 dark:text-gray-100">
            {title}
          </h1>
          {description && (
            <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
              {description}
            </p>
          )}
        </div>
        {actions && <div className="flex-shrink-0">{actions}</div>}
      </header>

      {filters && <aside>{filters}</aside>}

      <main className="flex-1">{children}</main>
    </div>
  );
};
