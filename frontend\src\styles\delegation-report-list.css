/**
 * Delegation Report List Page Styles
 * Comprehensive styling for both screen and print layouts
 */

/* Screen-only styles */
@media screen {
  .delegation-report-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 1rem;
    background: white;
    min-height: 100vh;
  }

  .delegation-report-header {
    text-align: center;
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 2px solid #e5e7eb;
  }

  .delegation-report-title {
    font-size: 2.5rem;
    font-weight: bold;
    color: #1f2937;
    margin-bottom: 1rem;
  }

  .delegation-report-subtitle {
    font-size: 1.125rem;
    color: #6b7280;
    margin-bottom: 0.5rem;
  }

  .delegation-report-date {
    font-size: 0.875rem;
    color: #9ca3af;
  }

  /* Summary cards grid */
  .delegation-summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
  }

  /* Filters section */
  .delegation-filters {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: #f9fafb;
    border-radius: 0.5rem;
    border: 1px solid #e5e7eb;
  }

  /* Table container */
  .delegation-table-container {
    background: white;
    border-radius: 0.5rem;
    border: 1px solid #e5e7eb;
    overflow: hidden;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  }

  .delegation-table {
    width: 100%;
    border-collapse: collapse;
  }

  .delegation-table th {
    background: linear-gradient(to right, #f8fafc, #f1f5f9);
    padding: 1rem;
    text-align: left;
    font-weight: 600;
    color: #374151;
    border-bottom: 2px solid #e5e7eb;
    position: sticky;
    top: 0;
    z-index: 10;
  }

  .delegation-table td {
    padding: 1rem;
    border-bottom: 1px solid #f3f4f6;
    vertical-align: top;
  }

  .delegation-table tr:hover {
    background-color: #f9fafb;
  }

  /* Responsive design */
  @media (max-width: 768px) {
    .delegation-report-container {
      padding: 0.5rem;
    }

    .delegation-report-title {
      font-size: 2rem;
    }

    .delegation-summary-grid {
      grid-template-columns: 1fr;
      gap: 1rem;
    }

    .delegation-filters {
      grid-template-columns: 1fr;
      padding: 1rem;
    }

    .delegation-table-container {
      overflow-x: auto;
      -webkit-overflow-scrolling: touch;
    }

    .delegation-table th,
    .delegation-table td {
      padding: 0.75rem 0.5rem;
      font-size: 0.875rem;
    }

    .delegation-table th {
      white-space: nowrap;
    }
  }

  @media (max-width: 640px) {
    .delegation-summary-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }
}
