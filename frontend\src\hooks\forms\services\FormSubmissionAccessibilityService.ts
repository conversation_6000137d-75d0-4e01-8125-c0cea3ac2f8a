/**
 * @file Form Submission Accessibility Service
 * @description Handles accessibility features for form submissions following SRP
 * @version 2.0.0
 * <AUTHOR> Development Team
 */

import type {
  AccessibilityConfig,
  AriaAttributes,
} from '../types/FormSubmissionTypes';

/**
 * Form submission accessibility service following SRP
 * Responsible only for handling accessibility features during form submission
 */
export class FormSubmissionAccessibilityService {
  private config: AccessibilityConfig;

  constructor(config: AccessibilityConfig) {
    this.config = config;
  }

  /**
   * Announce status for screen readers
   */
  announceStatus(
    message: string,
    priority: 'polite' | 'assertive' = 'polite'
  ): void {
    if (!this.config.announceStatus || !this.config.screenReaderAnnouncements) {
      return;
    }

    // Create or update live region for announcements
    let liveRegion = document.getElementById('form-submission-announcements');
    if (!liveRegion) {
      liveRegion = document.createElement('div');
      liveRegion.id = 'form-submission-announcements';
      liveRegion.setAttribute('aria-live', priority);
      liveRegion.setAttribute('aria-atomic', 'true');
      liveRegion.className =
        'sr-only absolute left-[-10000px] top-[-10000px] w-[1px] h-[1px] overflow-hidden';
      document.body.appendChild(liveRegion);
    }

    // Update the announcement
    liveRegion.textContent = message;

    // Clear the announcement after a delay to allow for multiple announcements
    setTimeout(() => {
      if (liveRegion && liveRegion.textContent === message) {
        liveRegion.textContent = '';
      }
    }, 1000);
  }

  /**
   * Generate ARIA attributes for form elements
   */
  generateAriaAttributes(
    isLoading: boolean,
    hasError: boolean,
    state: string
  ): AriaAttributes {
    return {
      'aria-busy': isLoading,
      'aria-invalid': hasError,
      'aria-describedby':
        this.config.errorDescribedBy || (hasError ? 'form-error' : undefined),
      'aria-live':
        state === 'submitting' || state === 'validating' ? 'polite' : 'off',
    };
  }

  /**
   * Manage focus based on configuration
   */
  manageFocus(
    focusType: 'success' | 'error' | 'retry',
    formFocus?: (fieldName: string) => void
  ): void {
    if (this.config.focusManagement === 'none') return;

    switch (focusType) {
      case 'error':
        if (this.config.focusManagement === 'first-error' && formFocus) {
          // Focus first field with error
          formFocus('first-error');
        }
        break;

      case 'success':
        if (this.config.focusManagement === 'success-message') {
          // Focus success message if it exists
          const successElement = document.getElementById(
            'form-success-message'
          );
          if (successElement) {
            successElement.focus();
          }
        } else if (this.config.focusManagement === 'next-field' && formFocus) {
          // Focus next logical field or submit button
          formFocus('next-field');
        }
        break;

      case 'retry':
        if (formFocus) {
          // Focus retry button or first field
          formFocus('retry-button');
        }
        break;
    }
  }

  /**
   * Create accessible error message element
   */
  createErrorMessage(error: string): HTMLElement {
    const errorElement = document.createElement('div');
    errorElement.id = this.config.errorDescribedBy || 'form-error';
    errorElement.setAttribute('role', 'alert');
    errorElement.setAttribute('aria-live', 'assertive');
    errorElement.className = 'sr-only';
    errorElement.textContent = error;

    return errorElement;
  }

  /**
   * Update or create error message in DOM
   */
  updateErrorMessage(error: string | null): void {
    const errorId = this.config.errorDescribedBy || 'form-error';
    let errorElement = document.getElementById(errorId);

    if (error) {
      if (!errorElement) {
        errorElement = this.createErrorMessage(error);
        document.body.appendChild(errorElement);
      } else {
        errorElement.textContent = error;
      }
    } else if (errorElement) {
      errorElement.remove();
    }
  }

  /**
   * Get status message for current state
   */
  getStatusMessage(
    state: string,
    retryAttempt?: number,
    maxAttempts?: number
  ): string {
    switch (state) {
      case 'validating':
        return 'Validating form data...';
      case 'submitting':
        return 'Submitting form...';
      case 'retrying':
        return `Retrying submission... (Attempt ${retryAttempt || 1}/${maxAttempts || 3})`;
      case 'success':
        return 'Form submitted successfully';
      case 'error':
        return 'Form submission failed';
      default:
        return '';
    }
  }

  /**
   * Set up keyboard navigation for submission states
   */
  setupKeyboardNavigation(): () => void {
    // Add keyboard shortcuts for common actions
    const handleKeyDown = (event: KeyboardEvent) => {
      // Escape key to cancel submission
      if (event.key === 'Escape') {
        const cancelButton = document.querySelector(
          '[data-form-cancel]'
        ) as HTMLElement;
        if (cancelButton) {
          cancelButton.click();
        }
      }

      // Ctrl+Enter or Cmd+Enter to submit
      if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
        const submitButton = document.querySelector(
          '[type="submit"]'
        ) as HTMLButtonElement;
        if (submitButton && !submitButton.disabled) {
          submitButton.click();
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);

    // Return cleanup function
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }

  /**
   * Create progress announcements for long-running operations
   */
  announceProgress(
    step: number,
    totalSteps: number,
    stepDescription: string
  ): void {
    if (!this.config.screenReaderAnnouncements) return;

    const message = `Step ${step} of ${totalSteps}: ${stepDescription}`;
    this.announceStatus(message, 'polite');
  }

  /**
   * Cleanup accessibility resources
   */
  cleanup(): void {
    // Remove live region
    const liveRegion = document.getElementById('form-submission-announcements');
    if (liveRegion) {
      liveRegion.remove();
    }

    // Remove error message
    this.updateErrorMessage(null);
  }
}
