'use client';

import { <PERSON>, <PERSON>, Sun } from 'lucide-react';
import { useTheme as useNextTheme } from 'next-themes';
import * as React from 'react';

import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useTheme } from '@/hooks/ui/useTheme';

export function ThemeToggle() {
  const {
    setTheme: setNextTheme,
    systemTheme,
    theme: nextTheme,
  } = useNextTheme();
  const { currentTheme, setTheme: setZustandTheme } = useTheme();
  const [mounted, setMounted] = React.useState(false);

  // Ensure component is mounted to avoid hydration issues
  React.useEffect(() => {
    setMounted(true);
  }, []);

  // Enhanced theme switching that updates both systems
  const handleThemeChange = (newTheme: 'dark' | 'light' | 'system') => {
    setNextTheme(newTheme);
    if (newTheme === 'system') {
      // If system theme is selected, update Zustand with the actual system preference
      const systemPreference = systemTheme || 'light';
      setZustandTheme(systemPreference as 'dark' | 'light');
    } else {
      setZustandTheme(newTheme);
    }
  };

  // Get the current effective theme for display
  const effectiveTheme = nextTheme || currentTheme;
  const resolvedTheme =
    effectiveTheme === 'system' ? systemTheme : effectiveTheme;
  const isDark = resolvedTheme === 'dark';

  // Don't render until mounted to avoid hydration mismatch
  if (!mounted) {
    return (
      <Button className="text-foreground" size="icon" variant="ghost">
        <div className="size-[1.2rem]" />
      </Button>
    );
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          aria-label={`Current theme: ${effectiveTheme}. Click to change theme`}
          className="text-foreground hover:bg-accent hover:text-accent-foreground"
          size="icon"
          variant="ghost"
        >
          <Sun
            className={`size-[1.2rem] transition-all duration-300 ${
              isDark ? 'rotate-90 scale-0' : 'rotate-0 scale-100'
            }`}
          />
          <Moon
            className={`absolute size-[1.2rem] transition-all duration-300 ${
              isDark ? 'rotate-0 scale-100' : 'rotate-90 scale-0'
            }`}
          />
          <span className="sr-only">Toggle theme</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="min-w-32">
        <DropdownMenuItem
          className={
            effectiveTheme === 'light' ? 'bg-accent text-accent-foreground' : ''
          }
          onClick={() => handleThemeChange('light')}
        >
          <Sun className="mr-2 size-4" />
          Light
        </DropdownMenuItem>
        <DropdownMenuItem
          className={
            effectiveTheme === 'dark' ? 'bg-accent text-accent-foreground' : ''
          }
          onClick={() => handleThemeChange('dark')}
        >
          <Moon className="mr-2 size-4" />
          Dark
        </DropdownMenuItem>
        <DropdownMenuItem
          className={
            effectiveTheme === 'system'
              ? 'bg-accent text-accent-foreground'
              : ''
          }
          onClick={() => handleThemeChange('system')}
        >
          <Monitor className="mr-2 size-4" />
          System
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
