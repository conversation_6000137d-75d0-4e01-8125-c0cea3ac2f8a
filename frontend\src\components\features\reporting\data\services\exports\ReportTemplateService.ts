/**
 * @service ReportTemplateService
 * @description Manages PDF report templates for the export system
 *
 * Responsibilities:
 * - Manage report templates (CRUD operations)
 * - Provide default templates for different formats
 * - Handle template validation and styling
 *
 * SOLID Principles Applied:
 * - SRP: Single responsibility of template management
 * - OCP: Open for extension via new template types
 */

import type {
  IReportTemplateService,
  ReportTemplate,
  ExportFormat,
  EntityType,
  TemplateStyle,
} from '../../types/export';

export class ReportTemplateService implements IReportTemplateService {
  private templates: Map<string, ReportTemplate> = new Map();

  constructor() {
    this.initializeDefaultTemplates();
  }

  /**
   * Get all templates, optionally filtered by format
   */
  async getTemplates(format?: ExportFormat): Promise<ReportTemplate[]> {
    const allTemplates = Array.from(this.templates.values());

    if (format) {
      return allTemplates.filter(template => template.format === format);
    }

    return allTemplates;
  }

  /**
   * Get a specific template by ID
   */
  async getTemplate(id: string): Promise<ReportTemplate | null> {
    return this.templates.get(id) || null;
  }

  /**
   * Create a new template
   */
  async createTemplate(
    template: Omit<ReportTemplate, 'id'>
  ): Promise<ReportTemplate> {
    const id = this.generateTemplateId();
    const newTemplate: ReportTemplate = {
      ...template,
      id,
    };

    this.templates.set(id, newTemplate);
    return newTemplate;
  }

  /**
   * Update an existing template
   */
  async updateTemplate(
    id: string,
    updates: Partial<ReportTemplate>
  ): Promise<ReportTemplate> {
    const existingTemplate = this.templates.get(id);
    if (!existingTemplate) {
      throw new Error(`Template with ID ${id} not found`);
    }

    const updatedTemplate: ReportTemplate = {
      ...existingTemplate,
      ...updates,
      id, // Ensure ID cannot be changed
    };

    this.templates.set(id, updatedTemplate);
    return updatedTemplate;
  }

  /**
   * Delete a template
   */
  async deleteTemplate(id: string): Promise<boolean> {
    const template = this.templates.get(id);
    if (!template) {
      return false;
    }

    // Prevent deletion of default templates
    if (template.isDefault) {
      throw new Error('Cannot delete default templates');
    }

    return this.templates.delete(id);
  }

  /**
   * Get default template for a format
   */
  getDefaultTemplate(format: ExportFormat): ReportTemplate | null {
    const templates = Array.from(this.templates.values());
    return templates.find(t => t.format === format && t.isDefault) || null;
  }

  /**
   * Private helper methods
   */
  private initializeDefaultTemplates(): void {
    // Default PDF template
    const defaultPdfTemplate: ReportTemplate = {
      id: 'default-pdf',
      name: 'Default PDF Template',
      description: 'Standard PDF report template with professional styling',
      format: 'pdf',
      entityTypes: ['delegation', 'task', 'vehicle', 'employee'],
      isDefault: true,
      styling: {
        primaryColor: '#428bca',
        secondaryColor: '#5bc0de',
        fontFamily: 'Helvetica',
        fontSize: 12,
        headerText: 'Comprehensive Report',
        footerText: 'Generated by WorkHub Reporting System',
      },
    };

    // Default Excel template
    const defaultExcelTemplate: ReportTemplate = {
      id: 'default-excel',
      name: 'Default Excel Template',
      description: 'Standard Excel report template with data formatting',
      format: 'excel',
      entityTypes: ['delegation', 'task', 'vehicle', 'employee'],
      isDefault: true,
      styling: {
        primaryColor: '#217346',
        secondaryColor: '#70ad47',
        headerText: 'Comprehensive Report',
      },
    };

    // Default CSV template
    const defaultCsvTemplate: ReportTemplate = {
      id: 'default-csv',
      name: 'Default CSV Template',
      description: 'Standard CSV export template',
      format: 'csv',
      entityTypes: ['delegation', 'task', 'vehicle', 'employee'],
      isDefault: true,
    };

    // Executive PDF template
    const executivePdfTemplate: ReportTemplate = {
      id: 'executive-pdf',
      name: 'Executive PDF Template',
      description: 'Executive summary PDF template with charts and key metrics',
      format: 'pdf',
      entityTypes: ['delegation', 'task', 'vehicle', 'employee'],
      isDefault: false,
      styling: {
        primaryColor: '#2c3e50',
        secondaryColor: '#34495e',
        fontFamily: 'Helvetica',
        fontSize: 11,
        headerText: 'Executive Summary Report',
        footerText: 'Confidential - Executive Use Only',
      },
      customFields: [
        'executiveSummary',
        'keyMetrics',
        'recommendations',
        'actionItems',
      ],
    };

    // Detailed analysis template
    const detailedPdfTemplate: ReportTemplate = {
      id: 'detailed-pdf',
      name: 'Detailed Analysis Template',
      description:
        'Comprehensive detailed analysis template with full data tables',
      format: 'pdf',
      entityTypes: ['delegation', 'task', 'vehicle', 'employee'],
      isDefault: false,
      styling: {
        primaryColor: '#8e44ad',
        secondaryColor: '#9b59b6',
        fontFamily: 'Helvetica',
        fontSize: 10,
        headerText: 'Detailed Analysis Report',
        footerText: 'Detailed Analysis - All Data Included',
      },
      customFields: [
        'fullDataTables',
        'detailedCharts',
        'statisticalAnalysis',
        'correlationAnalysis',
      ],
    };

    // Store default templates
    this.templates.set(defaultPdfTemplate.id, defaultPdfTemplate);
    this.templates.set(defaultExcelTemplate.id, defaultExcelTemplate);
    this.templates.set(defaultCsvTemplate.id, defaultCsvTemplate);
    this.templates.set(executivePdfTemplate.id, executivePdfTemplate);
    this.templates.set(detailedPdfTemplate.id, detailedPdfTemplate);
  }

  private generateTemplateId(): string {
    return `template_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Validate template configuration
   */
  validateTemplate(template: Partial<ReportTemplate>): {
    isValid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    if (!template.name || template.name.trim().length === 0) {
      errors.push('Template name is required');
    }

    if (!template.format) {
      errors.push('Template format is required');
    }

    if (!template.entityTypes || template.entityTypes.length === 0) {
      errors.push('At least one entity type must be specified');
    }

    if (
      template.styling?.fontSize &&
      (template.styling.fontSize < 8 || template.styling.fontSize > 24)
    ) {
      errors.push('Font size must be between 8 and 24');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Get template styling with fallbacks
   */
  getTemplateStyleWithFallbacks(templateId?: string): TemplateStyle {
    const defaultStyle: TemplateStyle = {
      primaryColor: '#428bca',
      secondaryColor: '#5bc0de',
      fontFamily: 'Helvetica',
      fontSize: 12,
      headerText: 'Report',
      footerText: 'Generated by WorkHub',
    };

    if (!templateId) {
      return defaultStyle;
    }

    const template = this.templates.get(templateId);
    if (!template || !template.styling) {
      return defaultStyle;
    }

    return {
      ...defaultStyle,
      ...template.styling,
    };
  }
}
