import { jest } from '@jest/globals';
import request from 'supertest';
import { initializeAndGetApp } from '../../app.js';
import { afterAll, beforeAll, beforeEach, describe, expect, it } from '@jest/globals';
import prisma from '../../utils/prisma.js';

let app;

jest.mock('../../middleware/jwtAuth.middleware.js', () => ({
  enhancedAuthenticateUser: (req, res, next) => {
    req.user = { id: 'mock-user-id', role: 'ADMIN' };
    next();
  },
}));

jest.mock('../../middleware/supabaseAuth.js', () => ({
  requireRole: roles => (req, res, next) => {
    if (roles.includes(req.user.role)) {
      return next();
    }
    return res.status(403).json({ message: 'Forbidden' });
  },
}));

beforeAll(async () => {
  app = await initializeAndGetApp();
});

afterAll(async () => {
  await prisma.$disconnect();
});

beforeEach(async () => {
  await prisma.task.deleteMany({});
  await prisma.serviceRecord.deleteMany({});
  await prisma.vehicle.deleteMany({});
  await prisma.employee.deleteMany({});
});

describe('Task API Routes', () => {
  let serviceRecord;
  let vehicle;
  let employee;

  beforeEach(async () => {
    vehicle = await prisma.vehicle.create({
      data: {
        make: 'TestMake',
        model: 'TestModel',
        year: 2023,
        vin: `TASK-VIN-${Date.now()}`,
        licensePlate: 'TASK-LP',
        ownerName: 'Test Owner',
        ownerContact: '<EMAIL>',
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    });

    employee = await prisma.employee.create({
      data: {
        name: 'Test Employee for Task',
        role: 'mechanic',
        employeeId: `EMP-TASK-${Date.now()}`,
        contactInfo: '<EMAIL>',
        status: 'Active',
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    });

    serviceRecord = await prisma.serviceRecord.create({
      data: {
        Vehicle: { connect: { id: vehicle.id } },
        servicePerformed: ['Initial service for task tests'],
        cost: 100.0,
        date: new Date(),
        odometer: 10000,
        updatedAt: new Date(),
      },
    });
  });

  const newTaskData = () => ({
    title: 'Test Task',
    description: 'Perform standard checks',
    serviceRecordId: serviceRecord.id,
    assignedToId: employee.id,
    status: 'PENDING',
    priority: 'MEDIUM',
    dueDate: new Date(new Date().setDate(new Date().getDate() + 7)).toISOString(),
  });

  it('should create a new task', async () => {
    const response = await request(app).post('/api/tasks').send(newTaskData());
    expect(response.statusCode).toBe(201);
    expect(response.body.status).toBe('success');
    expect(response.body.data).toHaveProperty('id');
    expect(response.body.data.title).toBe('Test Task');
  });

  it('should get all tasks', async () => {
    await request(app).post('/api/tasks').send(newTaskData());
    const response = await request(app).get('/api/tasks');
    expect(response.statusCode).toBe(200);
    expect(response.body.status).toBe('success');
    expect(Array.isArray(response.body.data)).toBe(true);
    expect(response.body.data.length).toBe(1);
  });

  it('should get a task by ID', async () => {
    const creationResponse = await request(app).post('/api/tasks').send(newTaskData());
    const taskId = creationResponse.body.data.id;
    const response = await request(app).get(`/api/tasks/${taskId}`);
    expect(response.statusCode).toBe(200);
    expect(response.body.status).toBe('success');
    expect(response.body.data.id).toBe(taskId);
  });

  it('should update a task', async () => {
    const creationResponse = await request(app).post('/api/tasks').send(newTaskData());
    const taskId = creationResponse.body.data.id;
    const updates = { status: 'IN_PROGRESS', priority: 'HIGH' };
    const response = await request(app).put(`/api/tasks/${taskId}`).send(updates);
    expect(response.statusCode).toBe(200);
    expect(response.body.status).toBe('success');
    expect(response.body.data.status).toBe('IN_PROGRESS');
    expect(response.body.data.priority).toBe('HIGH');
  });

  it('should delete a task', async () => {
    const creationResponse = await request(app).post('/api/tasks').send(newTaskData());
    const taskId = creationResponse.body.data.id;
    const response = await request(app).delete(`/api/tasks/${taskId}`);
    expect(response.statusCode).toBe(200);
    expect(response.body.status).toBe('success');
    expect(response.body.data.message).toBe('Task deleted successfully');
  });

  it('should return 404 for a non-existent task ID', async () => {
    const response = await request(app).get('/api/tasks/non-existent-id');
    expect(response.statusCode).toBe(404);
    expect(response.body.status).toBe('error');
  });
});
