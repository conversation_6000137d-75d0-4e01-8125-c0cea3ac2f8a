/**
 * @file Centralized exports for all custom hooks
 * @module hooks/index
 */

// Store hooks (re-exported for convenience)
export { useAppStore } from '../stores/zustand/appStore';
export { useReliabilityStore } from '../stores/zustand/reliabilityStore';
export { useUiStore } from '../stores/zustand/uiStore';

// WebSocket hooks
/* // Removing these exports
export {
  useReliabilityWebSocket,
  useReliabilityWebSocketSubscription,
  useReliabilityWebSocketStatus,
  useReliabilityWebSocketManager,
} from './useReliabilityWebSocket';
*/

// API hooks
export {
  useApiQuery,
  useDependentApiQuery,
  usePaginatedApiQuery,
  type ApiQueryOptions,
  type ApiQueryResult,
} from '../../hooks/api';

// Other existing hooks
// export { useAuthenticatedApi } from './useAuthenticatedApi'; // File not found
// export { useGlobalBackgroundSync } from './useBackgroundSync'; // Removed export

// UI hooks - Updated to use new standardized locations
export { type ModalContent, useModal } from '../../hooks/ui/useModal';
export {
  useNotifications,
  useWorkHubNotifications,
} from '../../hooks/ui/useNotifications';
export { useSidebar } from '../../hooks/ui/useSidebar';
export { useTheme } from '../../hooks/ui/useTheme';
export { useUiPreferences } from '../../hooks/ui/useUiPreferences';

// Re-export useWorkHubCore from main hooks index to avoid duplication
export { useWorkHubCore } from '../../hooks';
