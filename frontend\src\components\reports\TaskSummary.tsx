'use client';

import { isPast } from 'date-fns';
import React from 'react';

import type { Task } from '@/lib/types/domain';

import { Card, CardContent } from '@/components/ui/card';
import { cn } from '@/lib/utils';

/**
 * Props for the SummaryStatCard component
 */
interface SummaryStatCardProps {
  /** Additional CSS class for the card background */
  className?: string;
  /** Optional column span for the card */
  colSpan?: 'col-span-1' | 'col-span-2' | 'col-span-3';
  /** Label to display below the value */
  label: string;
  /** CSS class for the label text color */
  textColor?: string;
  /** Value to display (typically a number) */
  value: number | string;
}

/**
 * Props for the TaskSummary component
 */
interface TaskSummaryProps {
  /** Additional CSS class names */
  className?: string;
  /** Array of tasks to display summary statistics for */
  tasks: Task[];
}

/**
 * A component that displays summary statistics for tasks
 *
 * @example
 * ```tsx
 * <TaskSummary tasks={filteredTasks} />
 * ```
 */
export function TaskSummary({ className, tasks }: TaskSummaryProps) {
  // Calculate statistics
  const totalTasks = tasks.length;

  // Count tasks by status
  const tasksByStatus = tasks.reduce(
    (acc, task) => {
      acc[task.status] = (acc[task.status] || 0) + 1;
      return acc;
    },
    {} as Record<string, number>
  );

  // Count tasks by priority
  const tasksByPriority = tasks.reduce(
    (acc, task) => {
      acc[task.priority] = (acc[task.priority] || 0) + 1;
      return acc;
    },
    {} as Record<string, number>
  );

  // Count overdue tasks (tasks with a deadline in the past that are not Completed or Cancelled)
  const overdueTasks = tasks.filter(
    task =>
      task.deadline &&
      isPast(new Date(task.deadline)) &&
      task.status !== 'Completed' &&
      task.status !== 'Cancelled'
  ).length;

  // Count unassigned tasks (tasks without a staffEmployeeId that are not Completed or Cancelled)
  const unassignedTasks = tasks.filter(
    task =>
      !task.staffEmployeeId &&
      task.status !== 'Completed' &&
      task.status !== 'Cancelled'
  ).length;

  // Get status distribution for visualization
  const statusOrder = [
    'Pending',
    'Assigned',
    'In_Progress',
    'Completed',
    'Cancelled',
  ];
  const statusColors = {
    Assigned: 'bg-blue-100 text-blue-800',
    Cancelled: 'bg-gray-100 text-gray-800',
    Completed: 'bg-green-100 text-green-800',
    In_Progress: 'bg-purple-100 text-purple-800',
    Pending: 'bg-yellow-100 text-yellow-800',
  };

  // Get priority distribution for visualization
  const priorityOrder = ['High', 'Medium', 'Low'];
  const priorityColors = {
    High: 'bg-red-100 text-red-800',
    Low: 'bg-blue-100 text-blue-800',
    Medium: 'bg-orange-100 text-orange-800',
  };

  return (
    <div className={cn('mt-4 space-y-4', className)}>
      <div className="summary-grid grid grid-cols-2 gap-2 sm:grid-cols-3 md:grid-cols-5">
        {/* Total Tasks Card */}
        <SummaryStatCard
          className="bg-gray-50"
          label="Total Tasks"
          textColor="text-gray-500"
          value={totalTasks}
        />

        {/* Overdue Tasks Card */}
        <SummaryStatCard
          className={cn('bg-red-50', overdueTasks > 0 ? 'border-red-200' : '')}
          label="Overdue Tasks"
          textColor="text-red-500"
          value={overdueTasks}
        />

        {/* Unassigned Tasks Card */}
        <SummaryStatCard
          className={cn(
            'bg-amber-50',
            unassignedTasks > 0 ? 'border-amber-200' : ''
          )}
          label="Unassigned Tasks"
          textColor="text-amber-500"
          value={unassignedTasks}
        />

        {/* In Progress Tasks Card */}
        <SummaryStatCard
          className="bg-purple-50"
          label="In Progress"
          textColor="text-purple-500"
          value={tasksByStatus['In_Progress'] || 0}
        />

        {/* Completed Tasks Card */}
        <SummaryStatCard
          className="bg-green-50"
          label="Completed"
          textColor="text-green-500"
          value={tasksByStatus['Completed'] || 0}
        />
      </div>

      {/* Status Distribution */}
      <Card className="overflow-hidden">
        <CardContent className="p-4">
          <h3 className="mb-2 text-sm font-semibold">Status Distribution</h3>
          <div className="space-y-2">
            {statusOrder.map(status => (
              <div className="space-y-1" key={status}>
                <div className="flex justify-between text-xs">
                  <span>{status}</span>
                  <span>
                    {tasksByStatus[status] || 0} tasks (
                    {totalTasks
                      ? Math.round(
                          ((tasksByStatus[status] || 0) / totalTasks) * 100
                        )
                      : 0}
                    %)
                  </span>
                </div>
                <div className="h-2 w-full rounded-full bg-gray-200">
                  <div
                    aria-label={`${status}: ${tasksByStatus[status] || 0} tasks`}
                    className={cn(
                      'h-2 rounded-full',
                      statusColors[status as keyof typeof statusColors]
                    )}
                    style={{
                      width: `${totalTasks ? ((tasksByStatus[status] || 0) / totalTasks) * 100 : 0}%`,
                    }}
                  />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Priority Distribution */}
      <Card className="overflow-hidden">
        <CardContent className="p-4">
          <h3 className="mb-2 text-sm font-semibold">Priority Distribution</h3>
          <div className="mt-1 flex flex-wrap gap-2">
            {priorityOrder.map(priority => (
              <div
                className={cn(
                  'text-xs px-3 py-1 rounded-full',
                  priorityColors[priority as keyof typeof priorityColors]
                )}
                key={priority}
              >
                {priority}: {tasksByPriority[priority] || 0}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

/**
 * A card component for displaying a summary statistic
 */
function SummaryStatCard({
  className,
  colSpan,
  label,
  textColor = 'text-gray-500',
  value,
}: SummaryStatCardProps) {
  return (
    <Card className={cn('overflow-hidden', className, colSpan)}>
      <CardContent className="p-2 text-center">
        <p className="text-2xl font-semibold">{value}</p>
        <p className={cn('text-xs', textColor)}>{label}</p>
      </CardContent>
    </Card>
  );
}
