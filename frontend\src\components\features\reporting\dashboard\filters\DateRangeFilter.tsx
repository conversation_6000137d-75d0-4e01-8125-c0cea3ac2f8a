// frontend/src/components/features/reporting/dashboard/filters/DateRangeFilter.tsx

import React from 'react';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';
import { Calendar as CalendarIcon, X } from 'lucide-react';
import {
  useReportingFilters,
  useReportingFiltersActions,
  useReportingFiltersValidation,
} from '../../data/stores/useReportingFiltersStore';

interface DateRangeFilterProps {
  compact?: boolean;
  className?: string;
}

/**
 * @component DateRangeFilter
 * @description Date range filter component for reporting dashboard
 *
 * Responsibilities:
 * - Provides date range selection interface
 * - Integrates with reporting filters store
 * - Handles validation and error display
 * - Supports compact and full layouts
 *
 * SOLID Principles Applied:
 * - SRP: Single responsibility of date range filtering
 * - OCP: Open for extension via props
 * - DIP: Depends on filter store abstractions
 */
export const DateRangeFilter: React.FC<DateRangeFilterProps> = ({
  compact = false,
  className = '',
}) => {
  const filters = useReportingFilters();
  const { setDateRange } = useReportingFiltersActions();
  const { validationErrors } = useReportingFiltersValidation();

  const [isOpen, setIsOpen] = React.useState(false);

  // Handle date range selection - FIXED: Updated type for Calendar component compatibility
  const handleDateRangeSelect = (range: any) => {
    if (range?.from && range?.to) {
      setDateRange(range.from, range.to);
      setIsOpen(false);
    }
  };

  // Quick date range presets
  const quickRanges = [
    {
      label: 'Last 7 days',
      getValue: () => ({
        from: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
        to: new Date(),
      }),
    },
    {
      label: 'Last 30 days',
      getValue: () => ({
        from: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
        to: new Date(),
      }),
    },
    {
      label: 'Last 90 days',
      getValue: () => ({
        from: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000),
        to: new Date(),
      }),
    },
    {
      label: 'This year',
      getValue: () => ({
        from: new Date(new Date().getFullYear(), 0, 1),
        to: new Date(),
      }),
    },
  ];

  const handleQuickRange = (range: { from: Date; to: Date }) => {
    setDateRange(range.from, range.to);
    setIsOpen(false);
  };

  // Format date range for display
  const formatDateRange = () => {
    const { from, to } = filters.dateRange;
    if (!from || !to) return 'Select date range';

    if (compact) {
      return `${format(from, 'MMM d')} - ${format(to, 'MMM d')}`;
    }

    return `${format(from, 'MMM d, yyyy')} - ${format(to, 'MMM d, yyyy')}`;
  };

  const hasError = validationErrors.dateRange;

  if (compact) {
    return (
      <div className={cn('space-y-1', className)}>
        <Label className="text-xs font-medium">Date Range</Label>
        <Popover open={isOpen} onOpenChange={setIsOpen}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className={cn(
                'w-full justify-start text-left font-normal',
                !filters.dateRange.from && 'text-muted-foreground',
                hasError && 'border-red-500'
              )}
            >
              <CalendarIcon className="mr-2 h-4 w-4" />
              {formatDateRange()}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="start">
            <div className="p-3">
              <div className="grid grid-cols-2 gap-2 mb-3">
                {quickRanges.map(range => (
                  <Button
                    key={range.label}
                    variant="ghost"
                    size="sm"
                    onClick={() => handleQuickRange(range.getValue())}
                    className="text-xs"
                  >
                    {range.label}
                  </Button>
                ))}
              </div>
              <Calendar
                mode="range"
                selected={{
                  from: filters.dateRange.from,
                  to: filters.dateRange.to,
                }}
                onSelect={handleDateRangeSelect}
                numberOfMonths={1}
                className="rounded-md border"
              />
            </div>
          </PopoverContent>
        </Popover>
        {hasError && <p className="text-xs text-red-600">{hasError}</p>}
      </div>
    );
  }

  return (
    <div className={cn('space-y-2', className)}>
      <Label className="text-sm font-medium">Date Range</Label>
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className={cn(
              'w-full justify-start text-left font-normal',
              !filters.dateRange.from && 'text-muted-foreground',
              hasError && 'border-red-500'
            )}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {formatDateRange()}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <div className="p-4">
            <div className="space-y-2 mb-4">
              <h4 className="font-medium text-sm">Quick Ranges</h4>
              <div className="grid grid-cols-2 gap-2">
                {quickRanges.map(range => (
                  <Button
                    key={range.label}
                    variant="ghost"
                    size="sm"
                    onClick={() => handleQuickRange(range.getValue())}
                    className="text-sm"
                  >
                    {range.label}
                  </Button>
                ))}
              </div>
            </div>
            <Calendar
              mode="range"
              selected={{
                from: filters.dateRange.from,
                to: filters.dateRange.to,
              }}
              onSelect={handleDateRangeSelect}
              numberOfMonths={2}
              className="rounded-md border"
            />
          </div>
        </PopoverContent>
      </Popover>
      {hasError && <p className="text-sm text-red-600">{hasError}</p>}
    </div>
  );
};

export default DateRangeFilter;
