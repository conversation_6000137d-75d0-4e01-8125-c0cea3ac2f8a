import type { NextFunction, Request, Response } from 'express';
import { createHash } from 'crypto';

import helmet from 'helmet';
import { logAuditEvent } from '../utils/auditLogger.js';
import logger from '../utils/logger.js';

/**
 * PHASE 1 SECURITY HARDENING: Comprehensive Security Headers
 *
 * This middleware implements Helmet.js security headers to protect against
 * common web vulnerabilities including XSS, clickjacking, and MIME sniffing.
 */

export const securityHeaders = helmet({
  // Content Security Policy
  contentSecurityPolicy: {
    directives: {
      baseUri: ["'self'"],
      connectSrc: ["'self'", process.env.SUPABASE_URL || 'https://*.supabase.co'],
      defaultSrc: ["'self'"],
      fontSrc: ["'self'"],
      formAction: ["'self'"],
      frameSrc: ["'none'"],
      imgSrc: ["'self'", 'data:', 'https:'],
      mediaSrc: ["'self'"],
      objectSrc: ["'none'"],
      scriptSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"], // For CSS frameworks like Tailwind
    },
  },

  // Cross-Origin Embedder Policy: Disable if causing issues
  crossOriginEmbedderPolicy: false,

  // Cross-Origin Opener Policy: Prevent window.opener access
  crossOriginOpenerPolicy: { policy: 'same-origin-allow-popups' },

  // Cross-Origin Resource Policy: Allow cross-origin requests
  crossOriginResourcePolicy: { policy: 'cross-origin' },

  // X-Frame-Options: Prevent clickjacking
  frameguard: { action: 'deny' },

  // Hide X-Powered-By header (redundant with app.disable but ensures coverage)
  hidePoweredBy: true,

  // HTTP Strict Transport Security (HSTS)
  hsts: {
    includeSubDomains: true,
    maxAge: 31536000, // 1 year in seconds
    preload: true,
  },

  // X-Content-Type-Options: Prevent MIME sniffing
  noSniff: true,

  // Referrer Policy: Control referrer information
  referrerPolicy: { policy: 'same-origin' },

  // X-XSS-Protection: Enable XSS filtering
  xssFilter: true,
});

/**
 * Generate a cryptographically secure nonce for CSP
 */
function generateCSPNonce(): string {
  return createHash('sha256')
    .update(Math.random().toString() + Date.now().toString())
    .digest('base64')
    .substring(0, 16);
}

/**
 * Additional security middleware for custom headers and enhanced protection
 * SECURITY HARDENING: Enhanced with request validation and monitoring
 */
export const additionalSecurity = (req: Request, res: Response, next: NextFunction): void => {
  // Ensure X-Powered-By is removed (backup to app.disable)
  res.removeHeader('X-Powered-By');

  // Add custom security headers
  res.setHeader('X-API-Version', '1.0');
  res.setHeader('X-Security-Level', 'HIGH');
  res.setHeader('X-Security-Phase', 'PHASE-1-HARDENED-V2');

  // Add security timestamp for monitoring
  res.setHeader('X-Security-Timestamp', new Date().toISOString());

  // SECURITY ENHANCEMENT: Add enhanced security headers
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  res.setHeader('Permissions-Policy', 'geolocation=(), microphone=(), camera=()');

  // Generate and set CSP nonce for enhanced security
  const nonce = generateCSPNonce();
  res.locals.cspNonce = nonce;
  res.setHeader('X-CSP-Nonce', nonce);

  // SECURITY ENHANCEMENT: Request size validation
  try {
    const requestSize = JSON.stringify({
      body: req.body || {},
      params: req.params || {},
      query: req.query || {},
    }).length;

    if (requestSize > 1024 * 1024) {
      // 1MB limit
      logAuditEvent(
        {
          eventType: 'SECURITY',
          action: 'OVERSIZED_REQUEST_BLOCKED',
          outcome: 'FAILURE',
          message: 'Request blocked due to excessive size',
          details: { requestSize, limit: '1MB' },
          riskLevel: 'MEDIUM',
        },
        req,
      );

      res.status(413).json({
        error: 'Request too large',
        code: 'REQUEST_TOO_LARGE',
      });
      return;
    }

    // SECURITY ENHANCEMENT: Validate User-Agent
    const userAgent = req.get('User-Agent');
    if (!userAgent || userAgent.length < 10 || userAgent.length > 500) {
      logAuditEvent(
        {
          eventType: 'SECURITY',
          action: 'SUSPICIOUS_USER_AGENT',
          outcome: 'WARNING',
          message: 'Suspicious or missing User-Agent header',
          details: { userAgentLength: userAgent?.length || 0 },
          riskLevel: 'LOW',
        },
        req,
      );
    }
  } catch (error) {
    logger.error('Security validation error in additionalSecurity middleware', {
      error: error instanceof Error ? error.message : String(error),
      service: 'security-middleware',
    });
  }

  next();
};

/**
 * Development-specific security middleware
 * Relaxes some restrictions for development environment
 */
export const developmentSecurity = (req: Request, res: Response, next: NextFunction): void => {
  if (process.env.NODE_ENV === 'development') {
    // Add development security headers
    res.setHeader('X-Development-Mode', 'true');
    res.setHeader('X-Security-Warning', 'Development environment - not for production');
  }

  next();
};

/**
 * Production-specific security enhancements
 * SECURITY HARDENING: Enhanced with additional production controls
 */
export const productionSecurity = (req: Request, res: Response, next: NextFunction): void => {
  if (process.env.NODE_ENV === 'production') {
    // SECURITY ENHANCEMENT: Validate PRODUCTION_DOMAIN is set
    if (!process.env.PRODUCTION_DOMAIN) {
      logger.error('PRODUCTION_DOMAIN not configured in production', {
        security: 'CRITICAL_CONFIG_MISSING',
        service: 'production-security',
      });
    }

    // Force HTTPS in production
    if (req.header('x-forwarded-proto') !== 'https') {
      res.redirect(`https://${req.header('host')}${req.url}`);
      return;
    }

    // Add production security headers
    res.setHeader('X-Production-Mode', 'true');
    res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');

    // SECURITY ENHANCEMENT: Additional production headers
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.setHeader('X-Frame-Options', 'DENY');
    res.setHeader('X-XSS-Protection', '1; mode=block');
  }

  next();
};

/**
 * Authentication-specific security middleware
 * SECURITY HARDENING: Enhanced security for authentication endpoints
 */
export const authSecurityMiddleware = (req: Request, res: Response, next: NextFunction): void => {
  // Add authentication-specific headers
  res.setHeader('X-Auth-Security-Level', 'HIGH');
  res.setHeader('X-Token-Rotation', 'ENABLED');
  res.setHeader('X-Session-Security', 'HARDENED');
  res.setHeader('X-Auth-Method', 'JWT-ENHANCED');
  res.setHeader('X-Auth-Provider', 'SUPABASE');
  res.setHeader('X-Claims-Validation', 'STANDARDIZED');

  // Prevent caching of authentication responses
  res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, private');
  res.setHeader('Pragma', 'no-cache');
  res.setHeader('Expires', '0');

  next();
};
