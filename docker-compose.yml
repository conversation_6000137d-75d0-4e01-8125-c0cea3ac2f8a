services:
  # PostgreSQL Database
  db:
    image: postgres:14
    # SECURITY FIX: Remove exposed ports in production, use environment variables for credentials
    ports:
      - '5432:5432' # Only expose in development
    environment:
      - POSTGRES_USER=${DB_USER:-postgres}
      - POSTGRES_PASSWORD=${DB_PASSWORD:-postgres}
      - POSTGRES_DB=${DB_NAME:-workhub_db}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -U postgres']
      interval: 5s
      timeout: 5s
      retries: 5

  # Backend API service
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    image: workhub-backend
    ports:
      - '3001:3001'
    env_file:
      - ./backend/.env
    environment:
      - NODE_ENV=production
      - PORT=3001
      # Use the .env file for database configuration
      # - DATABASE_URL=${DATABASE_URL:-**************************************/workhub_db}
      - USE_SUPABASE=true
      # FRONTEND_URL is loaded from the root .env file

      # Redis Configuration (for distributed rate limiting and request deduplication)
      # When running via Docker Compose, connect to the 'redis' service within the Docker network.
      - REDIS_URL=redis://redis:6379

      # Request Deduplication Configuration - Reliability Enhancement
      - REQUEST_DEDUP_ENABLED=${REQUEST_DEDUP_ENABLED:-true}
      - REQUEST_DEDUP_DEFAULT_TTL=${REQUEST_DEDUP_DEFAULT_TTL:-300}

      # Service-Specific Deduplication TTL Settings
      - ADMIN_DEDUP_TTL=${ADMIN_DEDUP_TTL:-300}
      - API_DEDUP_TTL=${API_DEDUP_TTL:-60}
      - PERFORMANCE_DEDUP_TTL=${PERFORMANCE_DEDUP_TTL:-30}
      - IDEMPOTENT_DEDUP_TTL=${IDEMPOTENT_DEDUP_TTL:-600}

      # Nginx IP Allowlist Configuration (for reverse proxy support)
      - NGINX_PROXY_IPS=${NGINX_PROXY_IPS:-}

      # OpenSky Network API doesn't require an API key for basic access
    depends_on:
      db:
        condition: service_healthy
        required: false # Makes the dependency optional
    restart: unless-stopped

  # Frontend web application
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    image: workhub-frontend
    ports:
      - '3000:3000'
    env_file:
      - ./frontend/.env.local
      - ./frontend/.env
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_BASE_URL=http://backend:3001/api
    depends_on:
      - backend
    restart: unless-stopped
  # Redis service (optional - for development/testing with distributed caching)
  redis:
    image: redis:7-alpine
    ports:
      - '6379:6379'
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    healthcheck:
      test: ['CMD', 'redis-cli', 'ping']
      interval: 5s
      timeout: 3s
      retries: 5
    restart: unless-stopped
    profiles:
      - redis # Use 'docker-compose --profile redis up' to include Redis

  pgladmin:
    image: dpage/pgadmin4
    # SECURITY FIX: Use environment variables for admin credentials
    environment:
      - PGADMIN_DEFAULT_EMAIL=${PGADMIN_EMAIL:-<EMAIL>}
      - PGADMIN_DEFAULT_PASSWORD=${PGADMIN_PASSWORD:-admin}
    ports:
      - '5050:80'
    depends_on:
      - db
    volumes:
      - pgadmin_data:/var/lib/pgadmin

volumes:
  postgres_data:
  pgladmin_data:
  redis_data:
