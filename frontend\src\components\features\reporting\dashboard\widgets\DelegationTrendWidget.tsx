/**
 * @file Delegation Trend Widget
 * @description A widget that displays a line chart of delegation trends over time.
 */

'use client';

import React from 'react';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useTrendDataQuery } from '../../data/hooks';
import { useReportingFilters } from '../../data/stores';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { TrendingUp } from 'lucide-react';

export const DelegationTrendWidget = () => {
  const filters = useReportingFilters();
  const { data, isLoading, error } = useTrendDataQuery(filters);

  if (isLoading) {
    return <Skeleton className="h-[350px] w-full" />;
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>{error.message}</AlertDescription>
      </Alert>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <TrendingUp className="h-5 w-5" />
          Delegation Trends
        </CardTitle>
      </CardHeader>
      <CardContent>
        <ResponsiveContainer width="100%" height={300}>
          <LineChart data={data || []}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="date" />
            <YAxis />
            <Tooltip />
            <Legend />
            <Line
              type="monotone"
              dataKey="created"
              stroke="#8884d8"
              name="Created"
            />
            <Line
              type="monotone"
              dataKey="completed"
              stroke="#82ca9d"
              name="Completed"
            />
            <Line
              type="monotone"
              dataKey="inProgress"
              stroke="#ffc658"
              name="In Progress"
            />
          </LineChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
};
