/**
 * Debug Script: Check User JWT Claims
 *
 * This script helps debug JWT token claims for a specific user
 * to understand why admin access might be denied.
 */

import { createClient } from '@supabase/supabase-js';
import jwt from 'jsonwebtoken';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseAnonKey = process.env.SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing Supabase environment variables');
  console.error('Required: SUPABASE_URL, SUPABASE_ANON_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function checkUserJWTClaims() {
  console.log('🔍 Checking JWT Claims for Admin Access Issue\n');

  try {
    // Get current session
    const { data: sessionData } = await supabase.auth.getSession();

    if (!sessionData.session) {
      console.log('❌ No active session found');
      console.log('   Please sign in through your frontend application first');
      console.log('   Then run this script again\n');
      return;
    }

    const { access_token, user } = sessionData.session;

    console.log('📋 Session Information:');
    console.log('======================');
    console.log(`👤 User ID: ${user.id}`);
    console.log(`📧 Email: ${user.email}`);
    console.log(
      `⏰ Token Expires: ${new Date(sessionData.session.expires_at * 1000).toISOString()}\n`,
    );

    // Decode JWT token
    const decoded = jwt.decode(access_token);

    if (!decoded) {
      console.log('❌ Failed to decode JWT token');
      return;
    }

    console.log('🔑 JWT Token Claims:');
    console.log('====================');
    console.log(`👤 Subject (sub): ${decoded.sub}`);
    console.log(`📧 Email: ${decoded.email}`);
    console.log(`🎭 Auth Role: ${decoded.role}`);
    console.log(`⏰ Issued At: ${new Date(decoded.iat * 1000).toISOString()}`);
    console.log(`⏰ Expires At: ${new Date(decoded.exp * 1000).toISOString()}\n`);

    // Check for custom claims
    console.log('🎯 Custom Claims Analysis:');
    console.log('===========================');

    const userRole = decoded.user_role;
    const isActive = decoded.is_active;
    const employeeId = decoded.employee_id;

    if (userRole) {
      console.log(`✅ user_role: ${userRole}`);
    } else {
      console.log('❌ user_role: MISSING');
    }

    if (isActive !== undefined) {
      console.log(`✅ is_active: ${isActive}`);
    } else {
      console.log('❌ is_active: MISSING');
    }

    if (employeeId !== undefined) {
      console.log(`✅ employee_id: ${employeeId}`);
    } else {
      console.log('❌ employee_id: MISSING');
    }

    // Check admin access eligibility
    console.log('\n🔐 Admin Access Analysis:');
    console.log('==========================');

    const hasAdminRole = userRole === 'ADMIN' || userRole === 'SUPER_ADMIN';
    const isUserActive = isActive === true;

    if (hasAdminRole && isUserActive) {
      console.log('✅ SHOULD HAVE ADMIN ACCESS');
      console.log(`   Role: ${userRole} (Valid for admin)`);
      console.log(`   Active: ${isActive} (User is active)`);
      console.log('\n🔍 If access is still denied, check:');
      console.log('   1. Frontend ProtectedRoute component');
      console.log('   2. Backend middleware role checking');
      console.log('   3. API endpoint authentication');
    } else {
      console.log('❌ ADMIN ACCESS DENIED');
      if (!hasAdminRole) {
        console.log(`   ❌ Role: ${userRole || 'MISSING'} (Not admin role)`);
      }
      if (!isUserActive) {
        console.log(`   ❌ Active: ${isActive} (User not active)`);
      }

      console.log('\n🔧 To fix this issue:');
      console.log('   1. Sign out completely');
      console.log('   2. Sign back in to get fresh JWT token');
      console.log('   3. Verify database has correct role');
    }

    // Show all claims for debugging
    console.log('\n📝 All JWT Claims (for debugging):');
    console.log('===================================');
    Object.keys(decoded).forEach(key => {
      if (!['iat', 'exp', 'iss', 'aud'].includes(key)) {
        console.log(`   ${key}: ${JSON.stringify(decoded[key])}`);
      }
    });
  } catch (error) {
    console.error('❌ Error checking JWT claims:', error.message);
  }
}

// Run the check
checkUserJWTClaims().catch(console.error);
