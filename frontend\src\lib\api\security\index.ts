/**
 * @file Security Module Index
 * @module api/security
 * 
 * Centralized exports for all security-related functionality
 * 
 * Phase 3: React Integration Layer
 * - Security hooks (original + new React integration)
 * - Security providers and contexts
 * - Enhanced SecureApiClient with SecurityComposer
 */

// Export security hooks (Phase 1 + Phase 3)
export * from './hooks';

// Export security providers (Phase 3)
export * from './providers';

// Export security composer (Phase 2)
export { SecurityComposer, createSecurityComposer } from './composer';
export type { SecurityFeatures } from './composer';

// Export secure API client (Phase 2 Enhanced)
export { SecureApiClient, createSecureApiClient } from './secureApiClient';
export type { SecureApiClientConfig } from './secureApiClient';
