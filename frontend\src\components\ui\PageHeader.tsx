import type { LucideIcon } from 'lucide-react';

import React from 'react';

interface PageHeaderProps {
  children?: React.ReactNode; // For action buttons like "Add New"
  description?: string;
  icon?: LucideIcon;
  title: string;
}

export function PageHeader({
  children,
  description,
  icon: Icon,
  title,
}: PageHeaderProps) {
  return (
    <div className="mb-6 flex items-center justify-between border-b border-border/50 pb-4">
      <div>
        <div className="flex items-center gap-3">
          {Icon && <Icon className="size-8 text-primary" />}
          <h1 className="text-3xl font-bold tracking-tight text-foreground">
            {title}
          </h1>
        </div>
        {description && (
          <p className="mt-1 text-muted-foreground">{description}</p>
        )}
      </div>
      {children && <div className="flex items-center gap-2">{children}</div>}
    </div>
  );
}
