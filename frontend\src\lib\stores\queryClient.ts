/**
 * @file Configuration for TanStack Query (React Query) QueryClient.
 * This file sets up the global QueryClient instance with default options
 * for caching, retries, and error handling.
 * @module stores/queryClient
 */

import { MutationCache, QueryCache, QueryClient } from '@tanstack/react-query';

import {
  ApiError,
  AuthenticationError,
  NetworkError,
} from '../api/core/errors';

/**
 * Performance metrics tracking for queries
 */
interface QueryMetrics {
  cacheHit: boolean;
  duration: number;
  queryKey: string;
  timestamp: number;
}

/**
 * Global performance metrics store
 */
const performanceMetrics: QueryMetrics[] = [];

/**
 * Configures and exports a new QueryClient instance for TanStack Query.
 * This client manages caching, background refetching, and mutations.
 * Enhanced with performance optimization and monitoring capabilities.
 */
export const queryClient = new QueryClient({
  defaultOptions: {
    mutations: {
      // Default retry attempts for mutations (often 0 or 1 for mutations)
      retry: 0, // Mutations are typically not retried automatically to avoid duplicate side effects

      // Retry delay for mutations (if retry > 0)
      retryDelay: 1000,
    },
    queries: {
      // Optimized cache time (garbage collection time)
      gcTime: 10 * 60 * 1000, // 10 minutes (was cacheTime in v4)

      // Background refetch interval for critical data
      refetchInterval: false, // Disabled by default, enabled per-query as needed

      refetchOnMount: true, // Refetch when component mounts
      refetchOnReconnect: true, // Refetch when network reconnects
      // Background refetching configuration
      refetchOnWindowFocus: true, // Refetch when window regains focus

      // Retry configuration with exponential backoff
      retry: (failureCount, error: unknown) => {
        // Do not retry on specific API errors like 400, 401, 404
        if (
          error instanceof AuthenticationError ||
          error instanceof NetworkError
        ) {
          return false;
        }
        if (
          error instanceof ApiError &&
          ((error as ApiError).status === 400 ||
            (error as ApiError).status === 401 || // ✅ CRITICAL: Don't retry 401 auth errors
            (error as ApiError).status === 404)
        ) {
          return false;
        }
        // Retry up to 3 times by default for other errors
        return failureCount < 3;
      },

      // Exponential backoff for retries
      retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30_000),

      // Optimized staleTime for different data types
      staleTime: 5 * 60 * 1000, // 5 minutes (default)
    },
  },
  mutationCache: new MutationCache({
    onError: (error: unknown) => {
      // Explicitly type error
      if (error instanceof AuthenticationError) {
        console.error(
          'Mutation Authentication Error:',
          (error as AuthenticationError).message
        );
      } else if (error instanceof NetworkError) {
        console.error(
          'Mutation Network Error:',
          (error as NetworkError).message
        );
      } else if (error instanceof ApiError) {
        const apiError = error as ApiError;
        console.error(
          `Mutation API Error (${apiError.status}):`,
          apiError.message,
          apiError.details
        );
      } else {
        console.error('An unexpected mutation error occurred:', error);
      }
      // You might use a global toast/notification system here
    },
  }),
  queryCache: new QueryCache({
    onError: (error: unknown) => {
      // Explicitly type error
      if (error instanceof AuthenticationError) {
        console.error(
          'Authentication Error:',
          (error as AuthenticationError).message
        );
        // Potentially redirect to login or show a global auth error notification
      } else if (error instanceof NetworkError) {
        console.error('Network Error:', (error as NetworkError).message);
        // Show a global network error message
      } else if (error instanceof ApiError) {
        const apiError = error as ApiError;
        console.error(
          `API Error (${apiError.status}):`,
          apiError.message,
          apiError.details
        );
        // Show a generic API error message
      } else {
        console.error('An unexpected error occurred:', error);
        // Fallback for unknown errors
      }
      // You might use a global toast/notification system here
    },

    // Performance monitoring for queries
    onSuccess: (_data, query) => {
      const queryKey = JSON.stringify(query.queryKey);
      const duration = Date.now() - (query.state.dataUpdatedAt || Date.now());
      const cacheHit =
        query.state.fetchStatus !== 'fetching' &&
        query.state.data !== undefined;

      // Track performance metrics
      performanceMetrics.push({
        cacheHit,
        duration,
        queryKey,
        timestamp: Date.now(),
      });

      // Keep only last 1000 metrics to prevent memory leaks
      if (performanceMetrics.length > 1000) {
        performanceMetrics.splice(0, performanceMetrics.length - 1000);
      }

      // Log slow queries in development
      if (process.env.NODE_ENV === 'development' && duration > 2000) {
        console.warn(`Slow query detected: ${queryKey} took ${duration}ms`);
      }
    },
  }),
});

/**
 * Performance monitoring utilities
 */
export const performanceUtils = {
  /**
   * Clear performance metrics
   */
  clearMetrics: () => {
    performanceMetrics.length = 0;
  },

  /**
   * Get average query duration
   */
  getAverageQueryDuration: () => {
    if (performanceMetrics.length === 0) return 0;
    const totalDuration = performanceMetrics.reduce(
      (sum, m) => sum + m.duration,
      0
    );
    return totalDuration / performanceMetrics.length;
  },

  /**
   * Get cache hit rate percentage
   */
  getCacheHitRate: () => {
    if (performanceMetrics.length === 0) return 0;
    const cacheHits = performanceMetrics.filter(m => m.cacheHit).length;
    return (cacheHits / performanceMetrics.length) * 100;
  },

  /**
   * Get performance metrics for analysis
   */
  getMetrics: () => [...performanceMetrics],

  /**
   * Get performance summary
   */
  getPerformanceSummary: () => {
    const metrics = performanceMetrics;
    const cacheHitRate = performanceUtils.getCacheHitRate();
    const avgDuration = performanceUtils.getAverageQueryDuration();
    const slowQueries = performanceUtils.getSlowQueries();

    return {
      averageDuration: Math.round(avgDuration * 100) / 100,
      cacheHitRate: Math.round(cacheHitRate * 100) / 100,
      slowQueriesCount: slowQueries.length,
      slowQueriesPercentage:
        metrics.length > 0
          ? Math.round((slowQueries.length / metrics.length) * 10_000) / 100
          : 0,
      totalQueries: metrics.length,
    };
  },

  /**
   * Get slow queries (> 2 seconds)
   */
  getSlowQueries: () => {
    return performanceMetrics.filter(m => m.duration > 2000);
  },
};

/**
 * Prefetching utilities for critical user journeys
 */
export const prefetchUtils = {
  /**
   * Prefetch all critical data for dashboard
   * @param isAuthReady - Whether authentication system is ready for API calls
   */
  prefetchDashboardData: async (isAuthReady: boolean = false) => {
    // Guard against premature API calls during auth initialization
    if (!isAuthReady) {
      console.warn(
        'Authentication not ready, deferring dashboard data prefetch.'
      );
      return;
    }

    // Additional safety check: Verify auth token provider exists
    const { getGlobalAuthTokenProvider } = await import('../api');
    const authTokenProvider = getGlobalAuthTokenProvider();
    if (!authTokenProvider || !authTokenProvider()) {
      console.warn(
        'No auth token available, skipping dashboard data prefetch.'
      );
      return;
    }

    // Import API services
    const {
      delegationApiService,
      employeeApiService,
      taskApiService,
      vehicleApiService,
    } = await import('../api/services/apiServiceFactory');

    const promises = [
      queryClient.prefetchQuery({
        queryFn: () => vehicleApiService.getAll(),
        queryKey: ['vehicles'],
        staleTime: 10 * 60 * 1000, // 10 minutes
      }),
      queryClient.prefetchQuery({
        queryFn: () => taskApiService.getAll(),
        queryKey: ['tasks'],
        staleTime: 5 * 60 * 1000, // 5 minutes
      }),
      queryClient.prefetchQuery({
        queryFn: () => employeeApiService.getAll(),
        queryKey: ['employees'],
        staleTime: 10 * 60 * 1000, // 10 minutes
      }),
      queryClient.prefetchQuery({
        queryFn: () => delegationApiService.getAll(),
        queryKey: ['delegations'],
        staleTime: 5 * 60 * 1000, // 5 minutes
      }),
    ];

    await Promise.allSettled(promises);
  },

  /**
   * Prefetch data for task management
   * @param isAuthReady - Whether authentication system is ready for API calls
   */
  prefetchTaskManagementData: async (isAuthReady: boolean = true) => {
    // Guard against premature API calls during auth initialization
    if (!isAuthReady) {
      console.warn(
        'Authentication not ready, deferring task management data prefetch.'
      );
      return;
    }

    const { employeeApiService, taskApiService, vehicleApiService } =
      await import('../api/services/apiServiceFactory');

    const promises = [
      queryClient.prefetchQuery({
        queryFn: () => taskApiService.getAll(),
        queryKey: ['tasks'],
        staleTime: 5 * 60 * 1000,
      }),
      queryClient.prefetchQuery({
        queryFn: () => employeeApiService.getAll(),
        queryKey: ['employees'],
        staleTime: 10 * 60 * 1000,
      }),
      queryClient.prefetchQuery({
        queryFn: () => vehicleApiService.getAll(),
        queryKey: ['vehicles'],
        staleTime: 10 * 60 * 1000,
      }),
    ];

    await Promise.allSettled(promises);
  },

  /**
   * Prefetch data for a specific vehicle detail page
   * @param vehicleId - The vehicle ID to prefetch details for
   * @param isAuthReady - Whether authentication system is ready for API calls
   */
  prefetchVehicleDetails: async (
    vehicleId: number,
    isAuthReady: boolean = true
  ) => {
    // Guard against premature API calls during auth initialization
    if (!isAuthReady) {
      console.warn(
        'Authentication not ready, deferring vehicle details prefetch.'
      );
      return;
    }

    const { vehicleApiService } = await import(
      '../api/services/apiServiceFactory'
    );

    await queryClient.prefetchQuery({
      queryFn: () => vehicleApiService.getById(vehicleId),
      queryKey: ['vehicles', vehicleId],
      staleTime: 10 * 60 * 1000,
    });
  },
};

// Query Key Factory (Conceptual):
// A query key factory is a pattern to centralize and standardize query keys.
// This helps prevent typos and ensures consistency across your application.
// Example:
/*
export const queryKeys = {
    vehicles: {
        all: ['vehicles'] as const,
        detail: (id: string) => ['vehicles', id] as const,
        lists: (filters: Record<string, any>) => ['vehicles', 'list', filters] as const,
    },
    delegations: {
        all: ['delegations'] as const,
        detail: (id: string) => ['delegations', id] as const,
    },
    // ... other domains
};
*/

// Devtools Integration:
// To integrate React Query Devtools, you would typically add it to your root component (e.g., _app.tsx in Next.js):
/*
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';

function MyApp({ Component, pageProps }: AppProps) {
    return (
        <QueryClientProvider client={queryClient}>
            <Component {...pageProps} />
            <ReactQueryDevtools initialIsOpen={false} />
        </QueryClientProvider>
    );
}
*/
