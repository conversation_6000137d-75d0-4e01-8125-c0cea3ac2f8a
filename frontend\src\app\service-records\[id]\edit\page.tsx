'use client';

import { zod<PERSON><PERSON><PERSON><PERSON> } from '@hookform/resolvers/zod';
import {
  AlertTriangle,
  ArrowLeft,
  CheckCircle2,
  Save,
  Trash2,
} from 'lucide-react';
import Link from 'next/link';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';

import { VehicleInfoHeader } from '@/components/service-records/VehicleInfoHeader';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import { Skeleton } from '@/components/ui/skeleton';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/utils/use-toast';
// Validation schemas
import {
  type ServiceRecordFormData,
  ServiceRecordFormSchema,
  ServiceRecordSchemaUtils,
} from '@/lib/schemas/serviceRecordSchemas';
// Types and API hooks
import {
  useDeleteServiceRecord,
  useServiceRecord,
  useUpdateServiceRecord,
} from '@/lib/stores/queries/useServiceRecords';

// Using imported validation schema for consistency

export default function ServiceRecordEditPage() {
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();

  const recordId = (params?.id as string) || '';

  // Mutation hooks - must be called before any early returns
  const updateMutation = useUpdateServiceRecord();
  const deleteMutation = useDeleteServiceRecord();

  // Fetch initial record data using the real API hook
  const {
    data: record,
    error: fetchError,
    isLoading: loading,
  } = useServiceRecord(recordId, {
    enabled: !!recordId && typeof params?.id === 'string', // Only fetch if recordId is valid
  });

  // React Hook Form with Zod validation
  const form = useForm<ServiceRecordFormData>({
    defaultValues: {
      cost: 0,
      date: '',
      employeeId: null,
      notes: '',
      odometer: 0,
      servicePerformed: [],
    },
    mode: 'onChange', // Enable real-time validation
    resolver: zodResolver(ServiceRecordFormSchema),
  });

  // Initialize form when record is loaded
  useEffect(() => {
    if (record) {
      form.reset({
        cost: record.cost || 0,
        date: record.date.split('T')[0] ?? '',
        employeeId: record.employeeId,
        notes: record.notes ?? '',
        odometer: record.odometer || 0,
        servicePerformed: record.servicePerformed || [],
      });
    }
  }, [record, form]);

  if (!params || typeof params.id !== 'string') {
    return (
      <div className="container mx-auto py-8 text-center text-red-500">
        <p>Error: Invalid or missing Service Record ID.</p>
        <Button asChild className="mt-4">
          <Link href="/service-history">
            <ArrowLeft className="size-4" /> Back to Service History
          </Link>
        </Button>
      </div>
    );
  }

  // Optimistic update handler
  const handleOptimisticUpdate = async (data: ServiceRecordFormData) => {
    if (!record) {
      toast({
        description:
          'Service record data is not available. Please refresh the page.',
        title: 'Error',
        variant: 'destructive',
      });
      return;
    }

    // Validate that vehicleId is available
    if (!record.vehicleId) {
      toast({
        description: 'Vehicle ID is missing. Cannot update service record.',
        title: 'Error',
        variant: 'destructive',
      });
      console.error('Service record missing vehicleId:', record);
      return;
    }

    // Use the transformer to properly handle optional fields and type compatibility
    const submissionData =
      ServiceRecordSchemaUtils.transformToUpdatePayload(data);

    try {
      await updateMutation.mutateAsync({
        data: submissionData,
        id: recordId,
        vehicleId: record.vehicleId,
      });

      toast({
        description: 'Service record updated successfully! 🎉',
        title: 'Success!',
        variant: 'default',
      });

      router.push(`/service-records/${recordId}`);
    } catch (error) {
      console.error('Failed to update service record:', error);
      toast({
        description: 'Failed to update service record. Please try again.',
        title: 'Error',
        variant: 'destructive',
      });
    }
  };

  // Delete handler with confirmation
  const handleDelete = async () => {
    if (!record) {
      toast({
        description: 'Service record data is not available.',
        title: 'Error',
        variant: 'destructive',
      });
      return;
    }

    // Validate that vehicleId is available
    if (!record.vehicleId) {
      toast({
        description: 'Vehicle ID is missing. Cannot delete service record.',
        title: 'Error',
        variant: 'destructive',
      });
      console.error('Service record missing vehicleId:', record);
      return;
    }

    try {
      await deleteMutation.mutateAsync({
        id: recordId,
        vehicleId: record.vehicleId,
      });

      toast({
        description: 'Service record deleted successfully.',
        title: 'Deleted!',
        variant: 'default',
      });

      router.push('/service-history');
    } catch (error) {
      console.error('Failed to delete service record:', error);
      toast({
        description: 'Failed to delete service record. Please try again.',
        title: 'Error',
        variant: 'destructive',
      });
    }
  };

  // Service performed input handler
  const handleServicePerformedChange = (value: string) => {
    const services = ServiceRecordSchemaUtils.parseServicePerformed(value);
    form.setValue('servicePerformed', services, { shouldValidate: true });
  };

  if (loading) {
    return (
      <div className="container mx-auto py-8">
        <Card>
          <CardHeader>
            <Skeleton className="h-8 w-3/4" />
          </CardHeader>
          <CardContent className="space-y-4">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-2/3" />
          </CardContent>
        </Card>
      </div>
    );
  }

  if (fetchError) {
    // Display fetch error
    return (
      <div className="container mx-auto py-8 text-center text-red-500">
        <p>Error loading service record: {fetchError.message}</p>
        <Button asChild className="mt-4">
          <Link href="/service-history">
            <ArrowLeft className="mr-2 size-4" /> Back to Service History
          </Link>
        </Button>
      </div>
    );
  }

  // Ensure record is defined before proceeding to render the form
  if (!record) {
    return (
      <div className="container mx-auto py-8 text-center text-gray-500">
        <p>No service record data available for editing.</p>
        <Button asChild className="mt-4">
          <Link href="/service-history">
            <ArrowLeft className="mr-2 size-4" /> Back to Service History
          </Link>
        </Button>
      </div>
    );
  }

  return (
    <div className="container mx-auto space-y-6 py-8">
      {/* Header with navigation and actions */}
      <div className="flex items-center justify-between">
        <Button asChild size="sm" variant="outline">
          <Link href={`/service-records/${record.id}`}>
            <ArrowLeft className="mr-2 size-4" /> Back to Details
          </Link>
        </Button>

        <div className="flex items-center gap-2">
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button
                disabled={deleteMutation.isPending}
                size="sm"
                variant="destructive"
              >
                <Trash2 className="mr-2 size-4" />
                Delete
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle className="flex items-center gap-2">
                  <AlertTriangle className="size-5 text-destructive" />
                  Delete Service Record
                </AlertDialogTitle>
                <AlertDialogDescription>
                  Are you sure you want to delete this service record? This
                  action cannot be undone.
                  <div className="mt-2 rounded-md bg-muted p-3">
                    <p className="text-sm font-medium">Record Details:</p>
                    <p className="text-sm text-muted-foreground">
                      {record.servicePerformed.join(', ')} -{' '}
                      {new Date(record.date).toLocaleDateString()}
                    </p>
                  </div>
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction
                  className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                  onClick={handleDelete}
                >
                  Delete Record
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      </div>

      {/* Form validation status */}
      {form.formState.isValid && form.formState.isDirty && (
        <Alert className="border-green-200 bg-green-50">
          <CheckCircle2 className="size-4 text-green-600" />
          <AlertDescription className="text-green-800">
            All fields are valid. Ready to save changes.
          </AlertDescription>
        </Alert>
      )}

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Save className="size-5" />
            Edit Service Record
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form
              className="space-y-6"
              onSubmit={form.handleSubmit(handleOptimisticUpdate)}
            >
              {/* Vehicle Information - Read Only */}
              <div className="rounded-lg bg-muted/50 p-4">
                <VehicleInfoHeader vehicleId={record.vehicleId} />
              </div>

              <Separator />

              {/* Date Field */}
              <FormField
                control={form.control}
                name="date"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Service Date</FormLabel>
                    <FormControl>
                      <Input type="date" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Service Performed Field */}
              <FormField
                control={form.control}
                name="servicePerformed"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Services Performed</FormLabel>
                    <FormControl>
                      <Input
                        onChange={e =>
                          handleServicePerformedChange(e.target.value)
                        }
                        placeholder="Oil change, brake inspection, tire rotation..."
                        value={ServiceRecordSchemaUtils.formatServicePerformed(
                          field.value || []
                        )}
                      />
                    </FormControl>
                    <FormDescription>
                      Enter services separated by commas.{' '}
                      {field.value?.length || 0}/10 services
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Odometer Field */}
              <FormField
                control={form.control}
                name="odometer"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Odometer Reading</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="0"
                        type="number"
                        {...field}
                        onChange={e =>
                          field.onChange(Number(e.target.value) || 0)
                        }
                      />
                    </FormControl>
                    <FormDescription>
                      Current mileage on the vehicle
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Cost Field */}
              <FormField
                control={form.control}
                name="cost"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Total Cost</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="0.00"
                        step="0.01"
                        type="number"
                        {...field}
                        onChange={e =>
                          field.onChange(Number(e.target.value) || 0)
                        }
                      />
                    </FormControl>
                    <FormDescription>
                      Total cost of the service in dollars
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Notes Field */}
              <FormField
                control={form.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Notes (Optional)</FormLabel>
                    <FormControl>
                      <Textarea
                        className="min-h-[100px]"
                        placeholder="Additional notes about the service..."
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      {field.value?.length ?? 0}/1000 characters
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <Separator />

              {/* Submit Button */}
              <div className="flex justify-end gap-3">
                <Button asChild type="button" variant="outline">
                  <Link href={`/service-records/${recordId}`}>Cancel</Link>
                </Button>
                <Button
                  className="min-w-[120px]"
                  disabled={updateMutation.isPending || !form.formState.isValid}
                  type="submit"
                >
                  {updateMutation.isPending ? (
                    <>
                      <div className="mr-2 size-4 animate-spin rounded-full border-2 border-background border-t-transparent" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 size-4" />
                      Save Changes
                    </>
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}
