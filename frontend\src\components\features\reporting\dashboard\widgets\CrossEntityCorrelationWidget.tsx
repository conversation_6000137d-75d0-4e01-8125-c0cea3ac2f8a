/**
 * @file CrossEntityCorrelationWidget.tsx
 * @description Cross-entity correlation widget showing relationships between delegations, tasks, vehicles, and employees
 */

import {
  Car,
  CheckSquare,
  FileText,
  MoreHorizontal,
  Network,
  TrendingUp,
  Users,
} from 'lucide-react';
import React, { useMemo } from 'react';
import {
  CartesianGrid,
  Cell,
  ResponsiveContainer,
  Scatter,
  ScatterChart,
  Tooltip,
  XAxis,
  YAxis,
} from 'recharts';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { DataLoader } from '@/components/ui/data-loader';
import { ErrorDisplay } from '@/components/ui/error-display';
import { cn } from '@/lib/utils';

import type { ReportingFilters } from '../../data/types/reporting';

import { useCrossEntityAnalytics } from '../../hooks/useCrossEntityAnalytics';

/**
 * Props interface for CrossEntityCorrelationWidget
 */
interface CrossEntityCorrelationWidgetProps {
  className?: string;
  correlationType?:
    | 'all'
    | 'employee-vehicle'
    | 'performance-workload'
    | 'task-delegation';
  filters?: ReportingFilters;
  interactive?: boolean;
}

/**
 * Custom tooltip for correlation chart
 */
const CustomTooltip = ({ active, payload }: any) => {
  if (active && payload?.length) {
    const data = payload[0].payload;
    return (
      <div className="rounded-lg border bg-white p-3 shadow-lg">
        <p className="font-medium">{data.name}</p>
        <p className="text-sm text-gray-600">
          X-Axis: <span className="font-medium">{data.x}</span>
        </p>
        <p className="text-sm text-gray-600">
          Y-Axis: <span className="font-medium">{data.y}</span>
        </p>
        {data.correlation && (
          <p className="text-sm text-gray-600">
            Correlation: <span className="font-medium">{data.correlation}</span>
          </p>
        )}
      </div>
    );
  }
  return null;
};

/**
 * Correlation metric card component
 */
interface CorrelationMetricProps {
  description: string;
  icon: React.ReactNode;
  title: string;
  value: number;
}

const CorrelationMetric: React.FC<CorrelationMetricProps> = ({
  description,
  icon,
  title,
  value,
}) => {
  const getCorrelationColor = (correlation: number) => {
    const abs = Math.abs(correlation);
    if (abs >= 0.8) return 'text-green-600 bg-green-100';
    if (abs >= 0.6) return 'text-blue-600 bg-blue-100';
    if (abs >= 0.4) return 'text-orange-600 bg-orange-100';
    return 'text-gray-600 bg-gray-100';
  };

  const getCorrelationStrength = (correlation: number) => {
    const abs = Math.abs(correlation);
    if (abs >= 0.8) return 'Strong';
    if (abs >= 0.6) return 'Moderate';
    if (abs >= 0.4) return 'Weak';
    return 'Very Weak';
  };

  return (
    <div className="rounded-lg border p-4">
      <div className="mb-2 flex items-center justify-between">
        <div className="flex items-center gap-2">
          {icon}
          <span className="text-sm font-medium">{title}</span>
        </div>
        <Badge className={cn('text-xs', getCorrelationColor(value))}>
          {getCorrelationStrength(value)}
        </Badge>
      </div>
      <div className="mb-1 text-2xl font-bold">{value.toFixed(3)}</div>
      <div className="text-xs text-gray-600">{description}</div>
    </div>
  );
};

/**
 * CrossEntityCorrelationWidget Component
 *
 * Displays correlations and relationships between different entities in the system.
 *
 * Responsibilities:
 * - Show correlations between employees, vehicles, tasks, and delegations
 * - Visualize relationships using scatter plots and metrics
 * - Follow established widget composition patterns
 * - Maintain consistent styling and behavior
 *
 * SOLID Principles Applied:
 * - SRP: Single responsibility of displaying cross-entity correlations
 * - OCP: Open for extension via props configuration
 * - DIP: Depends on existing widget framework abstractions
 */
export const CrossEntityCorrelationWidget: React.FC<
  CrossEntityCorrelationWidgetProps
> = ({
  className = '',
  correlationType = 'all',
  filters,
  interactive = true,
}) => {
  // Use hook for cross-entity analytics
  const {
    data: correlationData,
    error,
    isLoading,
  } = useCrossEntityAnalytics(filters);

  // Transform data for visualization
  const chartData = useMemo(() => {
    if (!correlationData?.correlations) return [];

    switch (correlationType) {
      case 'employee-vehicle': {
        return (
          correlationData.correlations.employeeVehicle?.map((item, index) => ({
            color:
              item.correlation > 0.5
                ? '#10b981'
                : item.correlation > 0
                  ? '#3b82f6'
                  : '#ef4444',
            correlation: item.correlation,
            name: `${item.employeeName} - ${item.vehicleName}`,
            x: item.employeePerformance,
            y: item.vehicleUtilization,
          })) || []
        );
      }

      case 'performance-workload': {
        return (
          correlationData.correlations.performanceWorkload?.map(
            (item, index) => ({
              color:
                item.correlation > 0.5
                  ? '#10b981'
                  : item.correlation > 0
                    ? '#3b82f6'
                    : '#ef4444',
              correlation: item.correlation,
              name: item.employeeName,
              x: item.workloadPercentage,
              y: item.performanceScore,
            })
          ) || []
        );
      }

      case 'task-delegation': {
        return (
          correlationData.correlations.taskDelegation?.map((item, index) => ({
            color:
              item.correlation > 0.5
                ? '#10b981'
                : item.correlation > 0
                  ? '#3b82f6'
                  : '#ef4444',
            correlation: item.correlation,
            name: item.taskType,
            x: item.taskComplexity,
            y: item.delegationSuccess,
          })) || []
        );
      }

      default: {
        return (
          correlationData.correlations.overall?.map((item, index) => ({
            color:
              item.correlation > 0.5
                ? '#10b981'
                : item.correlation > 0
                  ? '#3b82f6'
                  : '#ef4444',
            correlation: item.correlation,
            name: item.entityName,
            x: item.xValue,
            y: item.yValue,
          })) || []
        );
      }
    }
  }, [correlationData, correlationType]);

  // Handle loading state
  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Network className="size-5" />
            Cross-Entity Correlations
          </CardTitle>
        </CardHeader>
        <CardContent>
          <DataLoader data={null} error={null} isLoading={true}>
            {() => null}
          </DataLoader>
        </CardContent>
      </Card>
    );
  }

  // Handle error state
  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Network className="size-5" />
            Cross-Entity Correlations
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ErrorDisplay error={error} />
        </CardContent>
      </Card>
    );
  }

  const correlationMetrics = correlationData?.metrics ?? {
    employeeVehicle: 0,
    overallEfficiency: 0,
    performanceWorkload: 0,
    taskDelegation: 0,
  };

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Network className="size-5" />
            Cross-Entity Correlations
          </CardTitle>
          <div className="flex items-center gap-2">
            <Badge className="text-xs" variant="secondary">
              {chartData.length} relationships
            </Badge>
            <Button size="sm" variant="ghost">
              <MoreHorizontal className="size-4" />
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Correlation Metrics Grid */}
        <div className="grid grid-cols-2 gap-4 md:grid-cols-4">
          <CorrelationMetric
            description="Performance vs Vehicle Usage"
            icon={<Users className="size-4" />}
            title="Employee-Vehicle"
            value={correlationMetrics.employeeVehicle || 0}
          />
          <CorrelationMetric
            description="Task Complexity vs Success"
            icon={<CheckSquare className="size-4" />}
            title="Task-Delegation"
            value={correlationMetrics.taskDelegation || 0}
          />
          <CorrelationMetric
            description="Workload vs Performance"
            icon={<TrendingUp className="size-4" />}
            title="Performance-Workload"
            value={correlationMetrics.performanceWorkload || 0}
          />
          <CorrelationMetric
            description="System-wide Correlation"
            icon={<Network className="size-4" />}
            title="Overall Efficiency"
            value={correlationMetrics.overallEfficiency || 0}
          />
        </div>

        {/* Correlation Scatter Plot */}
        <div>
          <h4 className="mb-3 text-sm font-medium">
            Relationship Visualization
          </h4>
          {chartData.length > 0 ? (
            <ResponsiveContainer height={300} width="100%">
              <ScatterChart
                data={chartData}
                margin={{
                  bottom: 20,
                  left: 20,
                  right: 30,
                  top: 20,
                }}
              >
                <CartesianGrid stroke="#f0f0f0" strokeDasharray="3 3" />
                <XAxis dataKey="x" fontSize={12} name="X-Axis" type="number" />
                <YAxis dataKey="y" fontSize={12} name="Y-Axis" type="number" />
                {interactive && <Tooltip content={<CustomTooltip />} />}
                <Scatter dataKey="y" fill="#3b82f6">
                  {chartData.map((entry, index) => (
                    <Cell fill={entry.color} key={`cell-${index}`} />
                  ))}
                </Scatter>
              </ScatterChart>
            </ResponsiveContainer>
          ) : (
            <div className="flex h-64 items-center justify-center text-gray-500">
              <div className="text-center">
                <Network className="mx-auto mb-2 size-12 opacity-50" />
                <p>No correlation data available</p>
              </div>
            </div>
          )}
        </div>

        {/* Key Insights */}
        {correlationData?.insights && correlationData.insights.length > 0 && (
          <div className="space-y-3">
            <h4 className="text-sm font-medium">Key Insights</h4>
            <div className="space-y-2">
              {correlationData.insights.slice(0, 3).map((insight, index) => (
                <div
                  className="rounded-lg border border-blue-200 bg-blue-50 p-3"
                  key={index}
                >
                  <div className="flex items-start gap-2">
                    <TrendingUp className="mt-0.5 size-4 text-blue-600" />
                    <div>
                      <p className="text-sm font-medium text-blue-800">
                        {insight.title}
                      </p>
                      <p className="mt-1 text-xs text-blue-700">
                        {insight.description}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default CrossEntityCorrelationWidget;
