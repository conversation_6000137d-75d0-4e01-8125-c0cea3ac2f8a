/**
 * DelegationValidationSummary Component
 * 
 * A wrapper around ValidationSummary that integrates with the delegation validation hook
 * and provides delegation-specific validation feedback.
 */

import React from 'react';
import { ValidationSummary } from './ValidationSummary';
import { useDelegationValidation } from '@/hooks/forms/useDelegationValidation';

interface DelegationValidationSummaryProps {
  show: boolean;
  onDismiss: () => void;
  showWarnings?: boolean;
  showSuccess?: boolean;
  showFieldNavigation?: boolean;
}

export const DelegationValidationSummary: React.FC<DelegationValidationSummaryProps> = ({
  show,
  onDismiss,
  showWarnings = true,
  showSuccess = false,
  showFieldNavigation = true,
}) => {
  // This hook will now be called within the FormProvider context
  const { getCrossFieldErrors, getCrossFieldWarnings } = useDelegationValidation();

  return (
    <ValidationSummary
      show={show}
      onDismiss={onDismiss}
      showWarnings={showWarnings}
      showSuccess={showSuccess}
      showFieldNavigation={showFieldNavigation}
    />
  );
};

export default DelegationValidationSummary;
