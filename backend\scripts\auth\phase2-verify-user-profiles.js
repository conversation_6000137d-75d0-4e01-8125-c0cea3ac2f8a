#!/usr/bin/env node

/**
 * PHASE 2.1: User Profiles Verification Script
 *
 * This script verifies and creates user_profiles entries for existing auth.users
 * to ensure the Hybrid RBAC system works correctly.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables:');
  console.error('   SUPABASE_URL:', supabaseUrl ? '✅ Set' : '❌ Missing');
  console.error('   SUPABASE_SERVICE_ROLE_KEY:', supabaseServiceKey ? '✅ Set' : '❌ Missing');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function verifyAndCreateUserProfiles() {
  console.log('🔍 PHASE 2.1: Verifying User Profiles for Hybrid RBAC...\n');

  try {
    // Step 1: Get all users from auth.users
    console.log('📋 Step 1: Fetching all users from auth.users...');
    const { data: authUsers, error: authError } = await supabase.auth.admin.listUsers();

    if (authError) {
      console.error('❌ Error fetching auth users:', authError);
      return;
    }

    console.log(`✅ Found ${authUsers.users.length} users in auth.users`);

    // Step 2: Get existing user_profiles
    console.log('\n📋 Step 2: Fetching existing user_profiles...');
    const { data: userProfiles, error: profilesError } = await supabase
      .from('user_profiles')
      .select('*');

    if (profilesError) {
      console.error('❌ Error fetching user_profiles:', profilesError);
      return;
    }

    console.log(`✅ Found ${userProfiles.length} existing user_profiles`);

    // Step 3: Identify users without profiles
    const existingProfileIds = new Set(userProfiles.map(p => p.id));
    const usersWithoutProfiles = authUsers.users.filter(user => !existingProfileIds.has(user.id));

    console.log(`\n📋 Step 3: Analysis Results:`);
    console.log(`   Users with profiles: ${authUsers.users.length - usersWithoutProfiles.length}`);
    console.log(`   Users without profiles: ${usersWithoutProfiles.length}`);

    // Step 4: Create missing profiles
    if (usersWithoutProfiles.length > 0) {
      console.log('\n📋 Step 4: Creating missing user_profiles...');

      for (const user of usersWithoutProfiles) {
        const profileData = {
          id: user.id,
          role: determineUserRole(user),
          is_active: true,
          employee_id: null, // Will be linked manually if needed
        };

        console.log(`   Creating profile for: ${user.email} (Role: ${profileData.role})`);

        const { error: insertError } = await supabase.from('user_profiles').insert(profileData);

        if (insertError) {
          console.error(`   ❌ Error creating profile for ${user.email}:`, insertError);
        } else {
          console.log(`   ✅ Profile created for ${user.email}`);
        }
      }
    } else {
      console.log('\n✅ All users already have profiles!');
    }

    // Step 5: Verify specific test user
    console.log('\n📋 Step 5: Verifying test user (<EMAIL>)...');
    const testUser = authUsers.users.find(u => u.email === '<EMAIL>');

    if (testUser) {
      const testProfile = userProfiles.find(p => p.id === testUser.id);
      console.log(`   Test user found: ${testUser.email} (ID: ${testUser.id})`);
      console.log(`   Profile exists: ${testProfile ? '✅ Yes' : '❌ No'}`);

      if (testProfile) {
        console.log(`   Role: ${testProfile.role}`);
        console.log(`   Active: ${testProfile.is_active}`);
        console.log(`   Employee ID: ${testProfile.employee_id || 'Not linked'}`);
      }
    } else {
      console.log('   ❌ Test user (<EMAIL>) not found in auth.users');
    }

    // Step 6: Display final summary
    console.log('\n📊 FINAL SUMMARY:');
    const { data: finalProfiles, error: finalError } = await supabase
      .from('user_profiles')
      .select('*');

    if (!finalError) {
      console.log(`   Total user_profiles: ${finalProfiles.length}`);
      console.log(`   Role distribution:`);

      const roleCount = finalProfiles.reduce((acc, profile) => {
        acc[profile.role] = (acc[profile.role] || 0) + 1;
        return acc;
      }, {});

      Object.entries(roleCount).forEach(([role, count]) => {
        console.log(`     ${role}: ${count}`);
      });

      console.log(`   Active users: ${finalProfiles.filter(p => p.is_active).length}`);
      console.log(`   Inactive users: ${finalProfiles.filter(p => !p.is_active).length}`);
    }

    console.log('\n🎉 PHASE 2.1: User Profiles verification completed!');
    console.log('\n📝 Next Steps:');
    console.log('   1. Log out and log back in to get fresh JWT tokens with custom claims');
    console.log('   2. Test API endpoints to verify role-based access control');
    console.log('   3. Run the enhanced RLS policies migration');
  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

function determineUserRole(user) {
  // Determine role based on email or other criteria
  const email = user.email.toLowerCase();

  if (email.includes('admin') || email === '<EMAIL>') {
    return 'ADMIN';
  } else if (email.includes('manager')) {
    return 'MANAGER';
  } else if (email.includes('super') || email === '<EMAIL>') {
    return 'SUPER_ADMIN';
  } else {
    return 'USER'; // Default role
  }
}

// Run the verification
verifyAndCreateUserProfiles().catch(console.error);
