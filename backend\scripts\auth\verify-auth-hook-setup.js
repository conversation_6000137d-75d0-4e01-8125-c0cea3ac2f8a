/**
 * Verification Script: Check Auth Hook Setup
 *
 * This script verifies that the auth hook function exists and is properly configured
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  console.error('Required: SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function verifyAuthHookSetup() {
  console.log('🔍 Verifying Auth Hook Setup\n');

  try {
    // Check if the custom_access_token_hook function exists
    console.log('📋 Test 1: Check if auth hook function exists');
    console.log('===============================================');

    const { data: functions, error: funcError } = await supabase.rpc('custom_access_token_hook', {
      event: {
        user_id: '00000000-0000-0000-0000-000000000000',
        claims: {},
      },
    });

    if (funcError) {
      if (funcError.message.includes('function "custom_access_token_hook" does not exist')) {
        console.log('❌ Auth hook function does not exist');
        console.log('💡 You need to create the function in Supabase SQL Editor:');
        console.log('\n-- Copy and paste this SQL in Supabase SQL Editor:');
        console.log(getAuthHookSQL());
        return;
      } else {
        console.log('✅ Auth hook function exists (got expected error for test UUID)');
      }
    } else {
      console.log('✅ Auth hook function exists and responded');
    }

    // Check if user_profiles table exists and has correct structure
    console.log('\n📋 Test 2: Check user_profiles table structure');
    console.log('==============================================');

    const { data: tableInfo, error: tableError } = await supabase
      .from('user_profiles')
      .select('*')
      .limit(1);

    if (tableError) {
      console.log('❌ Error accessing user_profiles table:', tableError.message);
      return;
    }

    console.log('✅ user_profiles table is accessible');

    // Check table columns by trying to select specific fields
    const { data: columnTest, error: columnError } = await supabase
      .from('user_profiles')
      .select('id, role, is_active, employee_id, created_at, updated_at')
      .limit(1);

    if (columnError) {
      console.log('❌ user_profiles table missing required columns:', columnError.message);
      return;
    }

    console.log('✅ user_profiles table has all required columns');

    // Check if UserRole enum values are correct
    console.log('\n📋 Test 3: Check UserRole enum values');
    console.log('=====================================');

    // Try to insert a test record with each role (will rollback)
    const testRoles = ['USER', 'MANAGER', 'ADMIN', 'SUPER_ADMIN', 'READONLY'];
    let validRoles = [];

    for (const role of testRoles) {
      try {
        // This will fail if the enum value doesn't exist
        const { error } = await supabase.from('user_profiles').insert({
          id: '00000000-0000-0000-0000-000000000001',
          role: role,
          is_active: true,
        });

        if (!error || !error.message.includes('invalid input value for enum')) {
          validRoles.push(role);

          // Clean up the test record if it was inserted
          await supabase
            .from('user_profiles')
            .delete()
            .eq('id', '00000000-0000-0000-0000-000000000001');
        }
      } catch (e) {
        // Expected for invalid enum values
      }
    }

    if (validRoles.length === testRoles.length) {
      console.log('✅ All UserRole enum values are valid:', validRoles.join(', '));
    } else {
      console.log('⚠️  Some UserRole enum values may be missing:', validRoles.join(', '));
    }

    // Summary
    console.log('\n📊 Setup Verification Summary');
    console.log('=============================');
    console.log('✅ Auth hook function: Ready');
    console.log('✅ user_profiles table: Ready');
    console.log('✅ UserRole enum: Ready');
    console.log('\n🎯 Next Steps:');
    console.log('1. Sign in through your frontend application');
    console.log('2. Run: node scripts/test-auth-hook.js');
    console.log('3. Verify JWT tokens contain custom claims');
  } catch (error) {
    console.error('❌ Verification failed:', error.message);
  }
}

function getAuthHookSQL() {
  return `
-- Create the custom access token hook function
CREATE OR REPLACE FUNCTION public.custom_access_token_hook(event jsonb)
RETURNS jsonb
LANGUAGE plpgsql
STABLE
AS $$
DECLARE
  claims jsonb;
  user_role text;
  is_active boolean;
  employee_id integer;
BEGIN
  -- Get the claims from the event
  claims := event->'claims';

  -- Fetch user role, status, and employee_id from user_profiles
  SELECT role, "is_active", "employee_id" 
  INTO user_role, is_active, employee_id
  FROM public.user_profiles 
  WHERE id = (event->>'user_id')::uuid;

  -- Set custom claims in the JWT
  IF user_role IS NOT NULL THEN
    claims := jsonb_set(claims, '{custom_claims}', jsonb_build_object(
      'user_role', user_role,
      'is_active', COALESCE(is_active, true),
      'employee_id', employee_id
    ));
  ELSE
    -- Default claims for users without a profile
    claims := jsonb_set(claims, '{custom_claims}', jsonb_build_object(
      'user_role', 'USER',
      'is_active', true,
      'employee_id', null
    ));
  END IF;

  -- Update the event with the new claims
  event := jsonb_set(event, '{claims}', claims);
  
  RETURN event;
END;
$$;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION public.custom_access_token_hook(jsonb) TO supabase_auth_admin;
GRANT EXECUTE ON FUNCTION public.custom_access_token_hook(jsonb) TO service_role;
`;
}

// Run the verification
verifyAuthHookSetup().catch(console.error);
