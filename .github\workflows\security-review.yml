name: 🔒 Security Review Pipeline

on:
  pull_request:
    branches: [ main ]
    paths:
      - 'backend/src/routes/auth.routes.ts'
      - 'backend/src/middleware/jwtAuth.middleware.ts'
      - 'backend/src/services/userManagement.service.ts'
      - 'frontend/src/lib/security/**'
      - 'backend/src/services/auditLog.service.ts'
      - '**/*auth*'
      - '**/*security*'
  workflow_dispatch:

env:
  NODE_VERSION: '20'

jobs:
  # 🔍 Enhanced Security Scanning
  security-deep-scan:
    name: 🔍 Deep Security Analysis
    runs-on: ubuntu-latest
    permissions:
      security-events: write
      contents: read
      pull-requests: write
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 🔍 Install security tools
        run: |
          npm install -g @microsoft/eslint-plugin-sdl
          npm install -g semgrep
          npm install -g retire

      - name: 🛡️ Run Semgrep security scan
        run: |
          semgrep --config=auto --json --output=semgrep-results.json .
          
      - name: 🔍 Check for vulnerable dependencies
        run: |
          cd frontend && npm audit --audit-level=moderate
          cd ../backend && npm audit --audit-level=moderate

      - name: 🔍 Retire.js scan for vulnerable JS libraries
        run: retire --path . --outputformat json --outputpath retire-results.json

      - name: 🔒 JWT Security Analysis
        run: |
          echo "🔍 Analyzing JWT implementation..."
          grep -r "jwt\|JWT\|token" --include="*.ts" --include="*.js" . > jwt-analysis.txt
          
      - name: 🔍 Authentication Flow Analysis
        run: |
          echo "🔍 Analyzing authentication flows..."
          grep -r "auth\|Auth\|login\|password" --include="*.ts" --include="*.js" . > auth-analysis.txt

      - name: 📊 Upload security scan results
        uses: actions/upload-artifact@v4
        with:
          name: security-scan-results
          path: |
            semgrep-results.json
            retire-results.json
            jwt-analysis.txt
            auth-analysis.txt

  # 🧪 Authentication Tests
  auth-security-tests:
    name: 🧪 Authentication Security Tests
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: test_password
          POSTGRES_USER: test_user
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 🔧 Setup test environment
        working-directory: ./backend
        run: |
          npm ci
          npx prisma migrate deploy
        env:
          DATABASE_URL: postgresql://test_user:test_password@localhost:5432/test_db

      - name: 🧪 Run authentication tests
        working-directory: ./backend
        run: |
          npm run test -- --testPathPattern=auth
          npm run test -- --testPathPattern=jwt
          npm run test -- --testPathPattern=security
        env:
          DATABASE_URL: postgresql://test_user:test_password@localhost:5432/test_db

      - name: 🔒 Test JWT token validation
        working-directory: ./backend
        run: |
          echo "🔒 Testing JWT token validation..."
          npm run test -- --testPathPattern=token

      - name: 🛡️ Test RBAC implementation
        working-directory: ./backend
        run: |
          echo "🛡️ Testing RBAC implementation..."
          npm run test -- --testPathPattern=rbac

  # 🔍 Code Quality for Security
  security-code-quality:
    name: 🔍 Security Code Quality
    runs-on: ubuntu-latest
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 🔍 Security-focused linting
        run: |
          cd frontend && npm ci && npm run lint:security
          cd ../backend && npm ci && npm run lint:security

      - name: 🔒 Check for hardcoded secrets
        run: |
          echo "🔍 Scanning for hardcoded secrets..."
          grep -r -i "password\|secret\|key\|token" --include="*.ts" --include="*.js" --exclude-dir=node_modules . || true

      - name: 🛡️ Validate security headers
        run: |
          echo "🛡️ Checking security headers implementation..."
          grep -r "helmet\|cors\|csp" --include="*.ts" --include="*.js" . || true

  # 📋 Security Review Checklist
  security-checklist:
    name: 📋 Security Review Checklist
    runs-on: ubuntu-latest
    needs: [security-deep-scan, auth-security-tests, security-code-quality]
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📋 Generate security review checklist
        run: |
          echo "# 🔒 Security Review Checklist" > security-checklist.md
          echo "" >> security-checklist.md
          echo "## Authentication & Authorization" >> security-checklist.md
          echo "- [ ] JWT tokens properly validated" >> security-checklist.md
          echo "- [ ] Refresh token mechanism secure" >> security-checklist.md
          echo "- [ ] RBAC properly implemented" >> security-checklist.md
          echo "- [ ] No hardcoded credentials" >> security-checklist.md
          echo "" >> security-checklist.md
          echo "## Input Validation" >> security-checklist.md
          echo "- [ ] All user inputs validated" >> security-checklist.md
          echo "- [ ] SQL injection prevention" >> security-checklist.md
          echo "- [ ] XSS prevention measures" >> security-checklist.md
          echo "" >> security-checklist.md
          echo "## Data Protection" >> security-checklist.md
          echo "- [ ] Sensitive data encrypted" >> security-checklist.md
          echo "- [ ] Proper session management" >> security-checklist.md
          echo "- [ ] Audit logging implemented" >> security-checklist.md

      - name: 📤 Upload security checklist
        uses: actions/upload-artifact@v4
        with:
          name: security-checklist
          path: security-checklist.md

      - name: 💬 Comment on PR
        uses: actions/github-script@v7
        with:
          script: |
            const fs = require('fs');
            const checklist = fs.readFileSync('security-checklist.md', 'utf8');
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: `## 🔒 Security Review Required\n\n${checklist}\n\n**⚠️ This PR contains security-sensitive changes and requires security team review.**`
            });
