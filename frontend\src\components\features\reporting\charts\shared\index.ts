// frontend/src/components/features/reporting/charts/shared/index.ts

/**
 * Shared Chart Components Export Index
 *
 * Centralized exports for reusable chart components following DRY principles.
 */

// Tooltip Components
export {
  CustomTooltip,
  StatusTooltip,
  TrendTooltip,
  MetricTooltip,
} from './CustomTooltip';

export type { TooltipData } from './CustomTooltip';

// Legend Components
export {
  CustomLegend,
  StatusLegend,
  TrendLegend,
  CompactLegend,
} from './CustomLegend';

export type { LegendItem } from './CustomLegend';

// Loading and Error States
export {
  ChartLoadingSkeleton,
  PieChartLoadingSkeleton,
  BarChartLoadingSkeleton,
  LineChartLoadingSkeleton,
  MetricsLoadingSkeleton,
  ChartErrorState,
  ChartEmptyState,
} from './ChartLoadingStates';

// Chart State Components
export {
  LoadingSkeleton,
  ErrorDisplay,
  NoDataDisplay,
  ChartWrapper,
} from './ChartStateComponents';
