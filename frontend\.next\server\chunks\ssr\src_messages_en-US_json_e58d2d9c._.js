module.exports = {

"[project]/src/messages/en-US.json (json)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v(JSON.parse("{\"metadata\":{\"title\":\"WorkHub - Delegation Management System\",\"description\":\"Professional delegation and task management platform\",\"keywords\":\"delegation,task management,workflow,productivity\"},\"common\":{\"loading\":\"Loading...\",\"error\":\"An error occurred\",\"retry\":\"Retry\",\"cancel\":\"Cancel\",\"save\":\"Save\",\"edit\":\"Edit\",\"delete\":\"Delete\",\"add\":\"Add\",\"search\":\"Search\",\"filter\":\"Filter\",\"clear\":\"Clear\",\"submit\":\"Submit\",\"back\":\"Back\",\"next\":\"Next\",\"previous\":\"Previous\",\"close\":\"Close\",\"confirm\":\"Confirm\",\"yes\":\"Yes\",\"no\":\"No\"},\"navigation\":{\"dashboard\":\"Dashboard\",\"delegations\":\"Delegations\",\"employees\":\"Employees\",\"reports\":\"Reports\",\"settings\":\"Settings\",\"profile\":\"Profile\",\"logout\":\"Logout\"},\"auth\":{\"login\":\"Login\",\"logout\":\"Logout\",\"email\":\"Email\",\"password\":\"Password\",\"forgotPassword\":\"Forgot Password?\",\"rememberMe\":\"Remember Me\",\"loginError\":\"Invalid email or password\",\"loginSuccess\":\"Login successful\"}}"));}}),

};