/**
 * @file A simple presentational component for displaying vehicle information.
 * This component is used in the VehicleList example for migration.
 * @module components/VehicleCard
 */

import React from 'react';

import type { Vehicle } from '../lib/types/domain';

interface VehicleCardProps {
  vehicle: Vehicle;
}

/**
 * Renders a card displaying details of a single vehicle.
 * @param props - The component props.
 * @param props.vehicle - The vehicle data to display.
 * @returns A React functional component.
 */
const VehicleCard: React.FC<VehicleCardProps> = ({ vehicle }) => {
  return (
    <div
      style={{
        border: '1px solid #ccc',
        borderRadius: '5px',
        margin: '10px',
        padding: '10px',
      }}
    >
      <h3>
        {vehicle.make} {vehicle.model} ({vehicle.year})
      </h3>
      <p>
        <strong>License Plate:</strong> {vehicle.licensePlate}
      </p>
      <p>
        <strong>Owner:</strong> {vehicle.ownerName}
      </p>
      {vehicle.imageUrl && (
        <img
          alt={`${vehicle.make} ${vehicle.model}`}
          src={vehicle.imageUrl}
          style={{ height: 'auto', maxWidth: '100px' }}
        />
      )}
      {/* Add more details as needed */}
    </div>
  );
};

export default VehicleCard;
