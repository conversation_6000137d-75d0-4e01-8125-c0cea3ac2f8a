import type { Application } from 'express';

import express from 'express';

import { configureGlobalMiddleware } from './config/middleware.js';
import errorHandler from './middleware/errorHandler.js';
import { adminRateLimit } from './middleware/rateLimiting.js';
import { adminDeduplication, apiDeduplication } from './middleware/requestDeduplication.js';
import { createResponseWrapper } from './middleware/responseWrapper.js';
import { adminRoutes } from './modules/admin/index.js';
import alertingRoutes from './routes/alerting.routes.js';
import auditRoutes from './routes/audit.routes.js';
import authRoutes from './routes/auth.routes.js';
import delegationRoutes from './routes/delegation.routes.js';
import diagnosticsRoutes from './routes/diagnostics.routes.js';
import directServiceRecordRoutes from './routes/directServiceRecord.routes.js';
import employeeRoutes from './routes/employee.routes.js';
import flightRoutes from './routes/flight.routes.js';
import giftRoutes from './routes/gift.routes.js';
import healthRoutes from './routes/health.routes.js';
import monitoringRoutes from './routes/monitoring.routes.js';
import recipientRoutes from './routes/recipient.routes.js';
import reportingRoutes from './routes/reporting.routes.js';
import serviceRecordRoutes from './routes/serviceRecord.routes.js';
import sessionRoutes from './routes/session.routes.js';
import taskRoutes from './routes/task.routes.js';
import vehicleRoutes from './routes/vehicle.routes.js';
import { initializeApplication } from './startup/initializer.js';
import { logSecurityEvent, logStartup } from './utils/logger.js';

export async function initializeAndGetApp(): Promise<Application> {
  try {
    logStartup('🚀 Starting application initialization...');
    await initializeApplication();
    logStartup('✅ Application initialized successfully. Setting up Express server...');

    const app: Application = express();

    // Apply global middleware (Step 2)
    configureGlobalMiddleware(app);

    // Apply the response wrapper middleware to standardize responses
    // CRITICAL: Must be applied BEFORE route handlers to wrap responses
    app.use(createResponseWrapper());

    // Mount new route modules (Step 3)
    app.use('/api', monitoringRoutes); // Mount monitoring routes
    app.use('/api/health', healthRoutes); // Mount health routes
    app.use('/api/alerts', alertingRoutes); // Mount alerting routes
    app.use('/api/audit', auditRoutes); // Mount audit routes (Phase 3)
    app.use('/api/diagnostics', diagnosticsRoutes); // <<< ADD THIS LINE TO MOUNT THE ROUTE
    app.use('/api/auth', authRoutes); // <<< ADD THIS LINE TO MOUNT THE ROUTE

    app.use('/api/session', apiDeduplication, sessionRoutes);

    // Mount existing and new routes with request deduplication
    app.use('/api/vehicles', apiDeduplication, vehicleRoutes);
    app.use('/api/employees', apiDeduplication, employeeRoutes);
    app.use('/api/delegations', apiDeduplication, delegationRoutes);
    app.use('/api/tasks', apiDeduplication, taskRoutes);
    // Use the direct service record routes for the /api/servicerecords endpoint
    app.use('/api/servicerecords', apiDeduplication, directServiceRecordRoutes);
    // Keep the nested service record routes for /api/vehicles/:vehicleId/servicerecords
    app.use('/api/vehicles/:vehicleId/servicerecords', apiDeduplication, serviceRecordRoutes);
    app.use('/api/flights', apiDeduplication, flightRoutes);
    // Mount consolidated admin routes (Phase 2) with strict rate limiting and admin-specific deduplication
    app.use('/api/admin', adminRateLimit, adminDeduplication, adminRoutes);
    // Mount the new reporting routes
    app.use('/api/reporting', apiDeduplication, reportingRoutes);
    // Mount gift tracking routes
    app.use('/api/gifts', apiDeduplication, giftRoutes);
    app.use('/api/recipients', apiDeduplication, recipientRoutes);

    app.use(errorHandler);

    return app;
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logStartup('🚨🚨 CRITICAL: Application failed to initialize and start 🚨🚨', {
      error: errorMessage,
    });
    logSecurityEvent(
      'APP_STARTUP_FAILURE',
      { error: errorMessage, phase: 'initializeApplication-call' },
      'error',
    ); // Changed 'fatal' to 'error'
    throw new Error('Critical initialization failure'); // Throw error instead of exiting process
  }
}
