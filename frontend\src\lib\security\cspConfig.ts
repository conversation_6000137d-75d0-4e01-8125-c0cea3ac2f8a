/**
 * PHASE 3 SECURITY HARDENING: Enhanced CSP Configuration - 2025 Best Practices
 *
 * Implements strict Content Security Policy with:
 * - Nonce-based script execution
 * - strict-dynamic for trusted scripts
 * - Comprehensive security directives
 * - CSP violation reporting
 * - Supply chain security for third-party scripts
 * - Enhanced monitoring and alerting
 */

export interface CSPConfig {
  nonce: string;
  reportUri?: string;
  isDevelopment: boolean;
}

/**
 * Generate strict CSP policy following 2025 best practices
 */
export function generateStrictCSP(config: CSPConfig): string {
  const { nonce, reportUri, isDevelopment } = config;

  // Base strict CSP directives
  const directives: Record<string, string[]> = {
    // Script sources - strict nonce-based approach
    'script-src': [
      "'self'",
      `'nonce-${nonce}'`,
      "'strict-dynamic'",
      // Allow specific trusted domains for production
      ...(isDevelopment
        ? []
        : ['https://cdn.jsdelivr.net', 'https://unpkg.com']),
    ],

    // Style sources - nonce-based with fallbacks
    'style-src': [
      "'self'",
      `'nonce-${nonce}'`,
      // Allow inline styles for CSS-in-JS libraries in development
      ...(isDevelopment ? ["'unsafe-inline'"] : []),
      // Trusted style CDNs
      'https://fonts.googleapis.com',
      'https://cdn.jsdelivr.net',
    ],

    // Font sources
    'font-src': [
      "'self'",
      'https://fonts.gstatic.com',
      'https://cdn.jsdelivr.net',
      'data:',
    ],

    // Image sources
    'img-src': [
      "'self'",
      'data:',
      'blob:',
      'https:',
      // Supabase storage
      'https://*.supabase.co',
      // Common image CDNs
      'https://images.unsplash.com',
      'https://via.placeholder.com',
    ],

    // Connect sources for API calls
    'connect-src': [
      "'self'",
      // Supabase endpoints
      'https://*.supabase.co',
      'wss://*.supabase.co',
      // Development WebSocket
      ...(isDevelopment ? ['ws://localhost:*', 'wss://localhost:*'] : []),
      // Backend API endpoints (for development and Docker environments)
      ...(isDevelopment || process.env.NEXT_PUBLIC_DOCKER_ENV === 'true'
        ? ['http://localhost:3001', 'http://backend:3001']
        : []),
      // Additional connect sources from environment variable
      ...(process.env.NEXT_PUBLIC_ALLOWED_CSP_CONNECT_SRC
        ? process.env.NEXT_PUBLIC_ALLOWED_CSP_CONNECT_SRC.split(',')
            .map(s => s.trim())
            .filter(Boolean)
        : []),
      // API endpoints
      'https://api.github.com',
    ],

    // Frame sources
    'frame-src': [
      "'self'",
      // Allow specific trusted frames
      'https://www.youtube.com',
      'https://player.vimeo.com',
    ],

    // Object and embed restrictions
    'object-src': ["'none'"],
    'embed-src': ["'none'"],

    // Base URI restriction
    'base-uri': ["'self'"],

    // Form action restriction
    'form-action': ["'self'"],

    // Frame ancestors (clickjacking protection)
    'frame-ancestors': ["'none'"],

    // Block mixed content
    'block-all-mixed-content': [],

    // Default fallback
    'default-src': ["'self'"],
  };

  // Add upgrade insecure requests in production
  if (!isDevelopment) {
    directives['upgrade-insecure-requests'] = [];
  }

  // Add reporting if configured
  if (reportUri) {
    directives['report-uri'] = [reportUri];
    directives['report-to'] = ['csp-endpoint'];
  }

  // Convert directives to CSP string
  return Object.entries(directives)
    .map(([directive, sources]) => {
      if (sources.length === 0) {
        return directive;
      }
      return `${directive} ${sources.join(' ')}`;
    })
    .join('; ');
}

/**
 * Generate additional security headers following 2025 best practices
 */
export function generateSecurityHeaders(): Record<string, string> {
  return {
    // Strict Transport Security
    'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload',

    // X-Frame-Options (backup for frame-ancestors)
    'X-Frame-Options': 'DENY',

    // X-Content-Type-Options
    'X-Content-Type-Options': 'nosniff',

    // Referrer Policy
    'Referrer-Policy': 'strict-origin-when-cross-origin',

    // X-XSS-Protection (legacy browsers)
    'X-XSS-Protection': '1; mode=block',

    // Permissions Policy (formerly Feature Policy)
    'Permissions-Policy': [
      'camera=()',
      'microphone=()',
      'geolocation=()',
      'payment=()',
      'usb=()',
      'magnetometer=()',
      'accelerometer=()',
      'gyroscope=()',
    ].join(', '),

    // Cross-Origin Policies
    'Cross-Origin-Embedder-Policy': 'require-corp',
    'Cross-Origin-Opener-Policy': 'same-origin',
    'Cross-Origin-Resource-Policy': 'same-origin',
  };
}

/**
 * CSP Reporting configuration
 */
export function generateCSPReportingConfig() {
  return {
    group: 'csp-endpoint',
    max_age: 10886400, // 126 days
    endpoints: [
      {
        url: '/api/csp-report',
        priority: 1,
        weight: 1,
      },
    ],
  };
}

/**
 * PHASE 3 SECURITY HARDENING: Supply Chain Security for Third-Party Scripts
 * Validates and manages trusted third-party script sources
 */
export interface TrustedScript {
  url: string;
  integrity: string;
  crossorigin: 'anonymous' | 'use-credentials';
  purpose: string;
  lastVerified: string;
}

/**
 * Trusted third-party scripts with SRI hashes
 * SECURITY: All third-party scripts must have integrity hashes
 */
export const TRUSTED_SCRIPTS: TrustedScript[] = [
  {
    url: 'https://cdn.jsdelivr.net/npm/react@18.2.0/umd/react.production.min.js',
    integrity:
      'sha384-/bQdsTh/da6pkI1MST/rWKFNjaCP5gBSY4sEBT38Q/9RBh9AH40zEOg7Hlq2THRZ',
    crossorigin: 'anonymous',
    purpose: 'React library for production builds',
    lastVerified: '2025-01-24',
  },
  // Add more trusted scripts as needed
];

/**
 * Validate third-party script integrity
 */
export function validateScriptIntegrity(
  url: string,
  expectedIntegrity: string
): boolean {
  const trustedScript = TRUSTED_SCRIPTS.find(script => script.url === url);
  return trustedScript?.integrity === expectedIntegrity;
}

/**
 * Generate Subresource Integrity (SRI) policy
 */
export function generateSRIPolicy(): string {
  return TRUSTED_SCRIPTS.map(script => `'${script.integrity}'`).join(' ');
}

/**
 * Validate nonce format and strength
 * PHASE 3 ENHANCEMENT: Enhanced nonce validation with entropy check
 */
export function validateNonce(nonce: string): boolean {
  // Nonce should be at least 16 characters, base64 encoded
  const base64Regex = /^[A-Za-z0-9+/]+=*$/;
  const hasMinLength = nonce.length >= 16;
  const isValidBase64 = base64Regex.test(nonce);

  // PHASE 3: Check for sufficient entropy (no repeated patterns)
  const hasEntropy = !/(.)\1{3,}/.test(nonce); // No character repeated 4+ times

  return hasMinLength && isValidBase64 && hasEntropy;
}

/**
 * PHASE 3: CSP Violation Analysis and Reporting
 */
export interface CSPViolation {
  documentUri: string;
  violatedDirective: string;
  blockedUri: string;
  effectiveDirective: string;
  originalPolicy: string;
  sourceFile?: string;
  lineNumber?: number;
  columnNumber?: number;
  timestamp: string;
  userAgent: string;
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
}

/**
 * Analyze CSP violation risk level
 */
export function analyzeCSPViolationRisk(
  violation: CSPViolation
): 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' {
  const { violatedDirective, blockedUri } = violation;

  // Critical: Script injection attempts
  if (
    violatedDirective.includes('script-src') &&
    (blockedUri.includes('javascript:') ||
      blockedUri.includes('data:') ||
      blockedUri.includes('blob:'))
  ) {
    return 'CRITICAL';
  }

  // High: External script loading from untrusted domains
  if (
    violatedDirective.includes('script-src') &&
    !TRUSTED_SCRIPTS.some(script =>
      blockedUri.startsWith(script.url.split('/').slice(0, 3).join('/'))
    )
  ) {
    return 'HIGH';
  }

  // Medium: Style or image violations
  if (
    violatedDirective.includes('style-src') ||
    violatedDirective.includes('img-src')
  ) {
    return 'MEDIUM';
  }

  return 'LOW';
}

/**
 * Generate cryptographically secure nonce
 */
export function generateSecureNonce(): string {
  if (typeof crypto !== 'undefined' && crypto.getRandomValues) {
    // Browser environment
    const array = new Uint8Array(24); // 192 bits
    crypto.getRandomValues(array);
    return btoa(String.fromCharCode(...array));
  } else {
    // Node.js environment
    const crypto = require('crypto');
    return crypto.randomBytes(24).toString('base64');
  }
}
