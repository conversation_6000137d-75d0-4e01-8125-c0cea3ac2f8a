// backend/src/schemas/reporting.schema.ts

import { z } from 'zod';

/**
 * Reporting Validation Schemas
 * 
 * Follows DRY principle by centralizing validation logic.
 * Uses actual Prisma enum values for validation.
 */

// Enum schemas matching actual Prisma schema
export const DelegationStatusSchema = z.enum([
  'Planned',
  'Confirmed', 
  'In_Progress',
  'Completed',
  'Cancelled',
  'No_details'
]);

export const TaskStatusSchema = z.enum([
  'Pending',
  'Assigned',
  'In_Progress', 
  'Completed',
  'Cancelled'
]);

export const TaskPrioritySchema = z.enum([
  'Low',
  'Medium',
  'High'
]);

// Date range validation
export const DateRangeSchema = z.object({
  from: z.string().datetime().or(z.string().regex(/^\d{4}-\d{2}-\d{2}$/)),
  to: z.string().datetime().or(z.string().regex(/^\d{4}-\d{2}-\d{2}$/))
}).refine(
  (data) => new Date(data.from) <= new Date(data.to),
  {
    message: "Start date must be before or equal to end date",
    path: ["from"]
  }
);

// Reporting filters validation schema
export const ReportingFiltersSchema = z.object({
  // Date range (required)
  dateRange: DateRangeSchema.optional().default({
    from: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days ago
    to: new Date().toISOString().split('T')[0] // today
  }),
  
  // Status filters
  status: z.array(DelegationStatusSchema).optional().default([]),
  
  // Location filters
  locations: z.array(z.string()).optional().default([]),
  
  // Employee filters (numeric IDs)
  employees: z.array(z.number().int().positive()).optional().default([]),
  
  // Vehicle filters (numeric IDs)
  vehicles: z.array(z.number().int().positive()).optional().default([]),
  
  // Optional service history fields
  serviceTypes: z.array(z.string()).optional(),
  serviceStatus: z.array(z.string()).optional(),
  costRange: z.object({
    min: z.number().min(0),
    max: z.number().min(0)
  }).optional(),
  includeServiceHistory: z.boolean().optional().default(false),
  includeTaskData: z.boolean().optional().default(false)
});

// Enhanced filters for cross-entity reporting
export const EnhancedReportingFiltersSchema = ReportingFiltersSchema.extend({
  // Report scope
  reportScope: z.enum(['delegations', 'tasks', 'services', 'all']).default('delegations'),
  
  // Task-specific filters
  taskStatus: z.array(TaskStatusSchema).optional().default([]),
  taskPriorities: z.array(TaskPrioritySchema).optional().default([]),
  assignees: z.array(z.number().int().positive()).optional().default([]),
  
  // Advanced filtering options
  timeframe: z.enum(['day', 'week', 'month', 'quarter', 'year', 'custom']).optional().default('month'),
  groupBy: z.enum(['date', 'status', 'location', 'employee', 'vehicle', 'type']).optional().default('date'),
  sortBy: z.enum(['date', 'cost', 'priority', 'status', 'name']).optional().default('date'),
  sortOrder: z.enum(['asc', 'desc']).optional().default('desc')
});

// Query parameter validation (for URL query strings)
export const QueryFiltersSchema = z.object({
  // Date range as strings
  'dateRange.from': z.string().optional(),
  'dateRange.to': z.string().optional(),
  
  // Status as comma-separated string
  status: z.string().optional().transform((val) => 
    val ? val.split(',').filter(Boolean) : []
  ),
  
  // Locations as comma-separated string
  locations: z.string().optional().transform((val) => 
    val ? val.split(',').filter(Boolean) : []
  ),
  
  // Employees as comma-separated string of numbers
  employees: z.string().optional().transform((val) => 
    val ? val.split(',').map(Number).filter(n => !isNaN(n)) : []
  ),
  
  // Vehicles as comma-separated string of numbers
  vehicles: z.string().optional().transform((val) => 
    val ? val.split(',').map(Number).filter(n => !isNaN(n)) : []
  ),
  
  // Boolean flags
  includeServiceHistory: z.string().optional().transform((val) => val === 'true'),
  includeTaskData: z.string().optional().transform((val) => val === 'true'),
  
  // Pagination
  page: z.string().optional().transform((val) => val ? parseInt(val, 10) : 1),
  limit: z.string().optional().transform((val) => val ? parseInt(val, 10) : 50),
  
  // Sorting
  sortBy: z.enum(['date', 'cost', 'priority', 'status', 'name']).optional(),
  sortOrder: z.enum(['asc', 'desc']).optional()
}).transform((data) => ({
  dateRange: {
    from: data['dateRange.from'] || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    to: data['dateRange.to'] || new Date().toISOString().split('T')[0]
  },
  status: data.status,
  locations: data.locations,
  employees: data.employees,
  vehicles: data.vehicles,
  includeServiceHistory: data.includeServiceHistory,
  includeTaskData: data.includeTaskData,
  pagination: {
    page: data.page,
    limit: Math.min(data.limit, 1000) // Cap at 1000 for performance
  },
  sortBy: data.sortBy,
  sortOrder: data.sortOrder
}));

// Export options validation
export const ExportOptionsSchema = z.object({
  filename: z.string().min(1).max(255),
  format: z.enum(['pdf', 'csv', 'excel']),
  includeCharts: z.boolean().optional().default(false),
  orientation: z.enum(['portrait', 'landscape']).optional().default('portrait'),
  dateRange: DateRangeSchema.optional(),
  filters: ReportingFiltersSchema.optional()
});

// Delegation IDs validation for task metrics
export const DelegationIdsSchema = z.object({
  delegationIds: z.string().optional().transform((val) => 
    val ? val.split(',').filter(Boolean) : undefined
  )
});

// Consolidated validation schemas export
export const reportingValidationSchema = {
  filtersSchema: QueryFiltersSchema,
  enhancedFiltersSchema: EnhancedReportingFiltersSchema,
  exportOptionsSchema: ExportOptionsSchema,
  delegationIdsSchema: DelegationIdsSchema,
  dateRangeSchema: DateRangeSchema
} as const;

// Type exports for TypeScript
export type ReportingFilters = z.infer<typeof ReportingFiltersSchema>;
export type EnhancedReportingFilters = z.infer<typeof EnhancedReportingFiltersSchema>;
export type QueryFilters = z.infer<typeof QueryFiltersSchema>;
export type ExportOptions = z.infer<typeof ExportOptionsSchema>;
export type DelegationIds = z.infer<typeof DelegationIdsSchema>;
