(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["chunks/[root-of-the-server]__5c5f3bce._.js", {

"[externals]/node:buffer [external] (node:buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:buffer", () => require("node:buffer"));

module.exports = mod;
}}),
"[externals]/node:async_hooks [external] (node:async_hooks, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:async_hooks", () => require("node:async_hooks"));

module.exports = mod;
}}),
"[project]/src/i18n/config.ts [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file i18n Configuration
 * @module i18n/config
 *
 * Central configuration for internationalization settings.
 * Defines supported locales, default locale, and locale-specific settings.
 */ /**
 * Supported locales in the application
 */ __turbopack_context__.s({
    "defaultLocale": (()=>defaultLocale),
    "isRTLLocale": (()=>isRTLLocale),
    "localeConfig": (()=>localeConfig),
    "localeNames": (()=>localeNames),
    "locales": (()=>locales),
    "rtlLocales": (()=>rtlLocales)
});
const locales = [
    'en-US',
    'ar-IQ'
];
const defaultLocale = 'en-US';
const rtlLocales = [
    'ar-IQ'
];
function isRTLLocale(locale) {
    return rtlLocales.includes(locale);
}
const localeNames = {
    'en-US': 'English',
    'ar-IQ': 'العربية'
};
const localeConfig = {
    locales,
    defaultLocale,
    localePrefix: 'always'
};
}}),
"[project]/src/i18n/routing.ts [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file i18n Routing Configuration
 * @module i18n/routing
 *
 * Centralized routing configuration for next-intl.
 * Defines routing behavior, locale prefixes, and navigation settings.
 */ __turbopack_context__.s({
    "routing": (()=>routing)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$routing$2f$defineRouting$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__default__as__defineRouting$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/routing/defineRouting.js [middleware-edge] (ecmascript) <export default as defineRouting>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$i18n$2f$config$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/i18n/config.ts [middleware-edge] (ecmascript)");
;
;
const routing = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$routing$2f$defineRouting$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__default__as__defineRouting$3e$__["defineRouting"])({
    // List of all supported locales
    locales: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$i18n$2f$config$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["locales"],
    // Default locale used when no locale matches
    defaultLocale: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$i18n$2f$config$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["defaultLocale"],
    // Always show locale prefix in URLs for consistency
    localePrefix: 'always'
});
}}),
"[project]/middleware.ts [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "config": (()=>config),
    "default": (()=>middleware)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$buffer__$5b$external$5d$__$28$node$3a$buffer$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/node:buffer [external] (node:buffer, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$middleware$2f$middleware$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/middleware/middleware.js [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$api$2f$server$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/api/server.js [middleware-edge] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/server/web/spec-extension/response.js [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$i18n$2f$routing$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/i18n/routing.ts [middleware-edge] (ecmascript)");
;
;
;
// Create the intl middleware using the routing configuration
const handleI18nRouting = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$middleware$2f$middleware$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$i18n$2f$routing$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["routing"]);
// Public routes that don't require authentication
const publicRoutes = new Set([
    '/',
    '/forgot-password',
    '/login',
    '/reset-password',
    '/signup'
]);
// Protected routes that require authentication
const protectedRoutes = [
    '/admin',
    '/delegations',
    '/tasks',
    '/vehicles',
    '/employees',
    '/reports',
    '/reporting',
    '/settings',
    '/reliability',
    '/profile',
    '/gifts',
    '/recipients'
];
function middleware(request) {
    const pathname = request.nextUrl.pathname;
    // Skip middleware for static files and API routes
    if (pathname.startsWith('/api/') || pathname.startsWith('/_next/') || pathname.startsWith('/favicon.ico') || pathname.includes('.')) {
        return handleI18nRouting(request);
    }
    // Extract locale and path without locale prefix
    const localeMatch = pathname.match(/^\/([a-z]{2}-[A-Z]{2})(\/.*)?$/);
    const locale = localeMatch?.[1];
    const pathWithoutLocale = localeMatch?.[2] || '/';
    // Check if it's a public route
    const isPublicRoute = publicRoutes.has(pathWithoutLocale);
    // Check if it's a protected route
    const isProtectedRoute = protectedRoutes.some((route)=>pathWithoutLocale.startsWith(route));
    // Handle authentication for protected routes
    if (isProtectedRoute && !isPublicRoute) {
        const accessToken = request.cookies.get('sb-access-token')?.value;
        if (!accessToken) {
            // Redirect to login with current locale
            const loginUrl = new URL(`/${locale || __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$i18n$2f$routing$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["routing"].defaultLocale}/login`, request.url);
            loginUrl.searchParams.set('redirect', pathname);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(loginUrl);
        }
        // Basic token validation (signature validation should be done server-side)
        try {
            const tokenParts = accessToken.split('.');
            if (tokenParts.length === 3) {
                const payload = JSON.parse(atob(tokenParts[1]));
                const now = Math.floor(Date.now() / 1000);
                if (payload.exp && payload.exp < now) {
                    // Token expired, redirect to login
                    const loginUrl = new URL(`/${locale || __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$i18n$2f$routing$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["routing"].defaultLocale}/login`, request.url);
                    loginUrl.searchParams.set('redirect', pathname);
                    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(loginUrl);
                }
            }
        } catch  {
            // Invalid token, redirect to login
            const loginUrl = new URL(`/${locale || __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$i18n$2f$routing$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["routing"].defaultLocale}/login`, request.url);
            loginUrl.searchParams.set('redirect', pathname);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(loginUrl);
        }
    }
    // Handle i18n routing
    return handleI18nRouting(request);
}
/**
 * Enhanced CSP implementation now handled by cspConfig.ts
 * This provides strict 2025 security standards with comprehensive directives
 */ /**
 * Generate cryptographically secure nonce
 */ function generateNonce() {
    // Generate 32 random bytes for a secure nonce
    const bytes = new Uint8Array(32);
    crypto.getRandomValues(bytes);
    return __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$buffer__$5b$external$5d$__$28$node$3a$buffer$2c$__cjs$29$__["Buffer"].from(bytes).toString('base64');
}
/**
 * Authentication middleware
 */ async function handleAuthentication(request) {
    const path = request.nextUrl.pathname;
    // Extract the path without locale prefix for route matching
    const pathWithoutLocale = path.replace(/^\/[a-z]{2}-[A-Z]{2}/, '') || '/';
    const isProtectedRoute = protectedRoutes.some((route)=>pathWithoutLocale.startsWith(route));
    const isPublicRoute = publicRoutes.has(pathWithoutLocale);
    const isAdminRoute = adminRoutes.some((route)=>pathWithoutLocale.startsWith(route));
    // Skip auth for public routes and API routes
    if (isPublicRoute || path.startsWith('/api/') || path.startsWith('/_next/')) {
        return null;
    }
    // SECURITY FIX: Use proper JWT token validation instead of insecure session decryption
    // Get JWT token from secure httpOnly cookie (set by backend)
    const accessToken = request.cookies.get('sb-access-token')?.value;
    if (!accessToken && isProtectedRoute) {
        // Extract locale from path for proper redirect
        const localeMatch = path.match(/^\/([a-z]{2}-[A-Z]{2})/);
        const locale = localeMatch ? localeMatch[1] : defaultLocale;
        const loginUrl = new URL(`/${locale}/login`, request.url);
        loginUrl.searchParams.set('redirect', path);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(loginUrl);
    }
    if (accessToken) {
        try {
            // Basic JWT format validation (3 parts separated by dots)
            const tokenParts = accessToken.split('.');
            if (tokenParts.length !== 3) {
                throw new Error('Invalid JWT format');
            }
            // Decode JWT payload (this is safe as it's just base64 decoding for reading claims)
            // Note: This does NOT validate the signature - that must be done server-side
            const payloadPart = tokenParts[1];
            if (!payloadPart) {
                throw new Error('Invalid JWT payload');
            }
            const payload = JSON.parse(atob(payloadPart));
            // Check if token is expired
            const now = Math.floor(Date.now() / 1000);
            if (payload.exp && payload.exp < now) {
                throw new Error('Token expired');
            }
            // Extract user role for admin route protection
            const userRole = payload.user_role ?? payload.role ?? 'USER';
            // Admin route protection
            if (isAdminRoute && ![
                'ADMIN',
                'SUPER_ADMIN'
            ].includes(userRole)) {
                // Extract locale for proper redirect
                const localeMatch = path.match(/^\/([a-z]{2}-[A-Z]{2})/);
                const locale = localeMatch ? localeMatch[1] : defaultLocale;
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(new URL(`/${locale}/unauthorized`, request.url));
            }
            // Redirect authenticated users from auth pages
            if (payload.sub && (pathWithoutLocale === '/login' || pathWithoutLocale === '/signup')) {
                // Extract locale for proper redirect
                const localeMatch = path.match(/^\/([a-z]{2}-[A-Z]{2})/);
                const locale = localeMatch ? localeMatch[1] : defaultLocale;
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(new URL(`/${locale}`, request.url));
            }
        } catch  {
            // Invalid or expired token, redirect to login for protected routes
            if (isProtectedRoute) {
                // Extract locale for proper redirect
                const localeMatch = path.match(/^\/([a-z]{2}-[A-Z]{2})/);
                const locale = localeMatch ? localeMatch[1] : defaultLocale;
                const loginUrl = new URL(`/${locale}/login`, request.url);
                loginUrl.searchParams.set('redirect', path);
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(loginUrl);
            }
        }
    }
    return null;
}
/**
 * CORS middleware for API routes
 */ function handleCORS(request) {
    // Only apply CORS to API routes
    if (!request.nextUrl.pathname.startsWith('/api/')) {
        return null;
    }
    const allowedOrigins = [
        'https://workhub.company.com',
        'https://staging.workhub.company.com',
        ...("TURBOPACK compile-time truthy", 1) ? [
            'http://localhost:9002',
            'http://localhost:3000'
        ] : ("TURBOPACK unreachable", undefined)
    ];
    const origin = request.headers.get('origin') ?? '';
    const isAllowedOrigin = allowedOrigins.includes(origin);
    // Handle preflight requests
    if (request.method === 'OPTIONS') {
        const preflightHeaders = {
            'Access-Control-Allow-Credentials': 'true',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-CSRF-Token, X-Requested-With',
            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
            'Access-Control-Max-Age': '86400',
            ...isAllowedOrigin && {
                'Access-Control-Allow-Origin': origin
            }
        };
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"](null, {
            headers: preflightHeaders,
            status: 200
        });
    }
    return null;
}
const config = {
    matcher: [
        /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */ '/((?!_next/static|_next/image|favicon.ico).*)'
    ]
};
}}),
}]);

//# sourceMappingURL=%5Broot-of-the-server%5D__5c5f3bce._.js.map