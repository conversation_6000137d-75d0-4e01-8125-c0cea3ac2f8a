/**
 * @file TanStack Query hooks for Employee-related data.
 * These hooks manage fetching, caching, and mutating employee data,
 * integrating with the EmployeeApiService and EmployeeTransformer.
 * @module stores/queries/useEmployees
 */

import type { UseQueryOptions } from '@tanstack/react-query';

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

import type {
  CreateEmployeeData,
  DriverAvailabilityPrisma,
  Employee,
} from '@/lib/types/domain';

import { useCrudQuery } from '@/hooks/api/useSmartQuery'; // Adjusted import path
import { useNotifications } from '@/hooks/ui/useNotifications';
import { EmployeeTransformer } from '@/lib/transformers/employeeTransformer';
import { undefinedToNull } from '@/lib/utils/typeHelpers';

import { employeeApiService } from '../../api/services/apiServiceFactory'; // Use centralized service

/**
 * Centralized query keys for employees to ensure consistency.
 */
export const employeeQueryKeys = {
  all: ['employees'] as const,
  detail: (id: string) => ['employees', id] as const,
  // Add other specific query keys as needed
};

/**
 * Custom hook to fetch all employees.
 * @param options - Optional React Query options
 * @returns Query result containing an array of Employee domain models.
 */
export const useEmployees = (
  options?: Omit<UseQueryOptions<Employee[], Error>, 'queryFn' | 'queryKey'>
) => {
  return useCrudQuery<Employee[], Error>(
    [...employeeQueryKeys.all], // queryKey - spread for mutability
    async () => {
      const response = await employeeApiService.getAll();
      return response.data;
    },
    'employee', // entityType
    {
      staleTime: 5 * 60 * 1000, // 5 minutes
      ...options,
    }
  );
};

/**
 * Custom hook to fetch employees filtered by role.
 * @param role - Optional role filter (e.g., 'driver', 'manager')
 * @param options - Optional React Query options
 * @returns Query result containing an array of Employee domain models filtered by role.
 */
export const useEmployeesByRole = (
  role?: string,
  options?: Omit<UseQueryOptions<Employee[], Error>, 'queryFn' | 'queryKey'>
) => {
  return useCrudQuery<Employee[], Error>(
    role ? ['employees', 'role', role] : [...employeeQueryKeys.all],
    async () => {
      if (role) {
        const response = await employeeApiService.getByRole(role);
        return response;
      } else {
        const response = await employeeApiService.getAll();
        return response.data;
      }
    },
    'employee', // entityType for WebSocket events
    {
      staleTime: 5 * 60 * 1000, // 5 minutes
      ...options,
    }
  );
};

/**
 * Custom hook to fetch a single employee by their ID.
 * @param id - The ID of the employee to fetch.
 * @returns Query result containing a single Employee domain model or undefined.
 */
export const useEmployee = (id: string) => {
  return useCrudQuery<Employee, Error>(
    [...employeeQueryKeys.detail(id)],
    async () => {
      return await employeeApiService.getById(id);
    },
    'employee', // entityType for WebSocket events
    {
      enabled: !!id, // Only run query if id is truthy
      staleTime: 5 * 60 * 1000, // 5 minutes
    }
  );
};

/**
 * Custom hook for creating a new employee.
 * Includes optimistic updates and cache invalidation.
 * @returns Mutation result for creating an employee.
 */
export const useCreateEmployee = () => {
  const queryClient = useQueryClient();
  const { showError, showSuccess } = useNotifications();

  interface CreateContext {
    previousEmployees: Employee[] | undefined;
  }

  return useMutation<Employee, Error, CreateEmployeeData, CreateContext>({
    mutationFn: async (employeeData: CreateEmployeeData) => {
      const request = EmployeeTransformer.toCreateRequest(employeeData);
      return await employeeApiService.create(request); // Removed redundant EmployeeTransformer.fromApi
    },
    onError: (err, _newEmployeeData, context) => {
      if (context?.previousEmployees) {
        queryClient.setQueryData(
          employeeQueryKeys.all,
          context.previousEmployees
        );
      }
      showError(
        `Failed to create employee: ${err.message || 'Unknown error occurred'}`
      );
    },
    onMutate: async newEmployeeData => {
      await queryClient.cancelQueries({ queryKey: employeeQueryKeys.all });
      const previousEmployees = queryClient.getQueryData<Employee[]>(
        employeeQueryKeys.all
      );

      queryClient.setQueryData<Employee[]>(
        employeeQueryKeys.all,
        (old = []) => {
          const tempId = 'optimistic-' + Date.now().toString();
          const now = new Date().toISOString();
          const optimisticEmployee: Employee = {
            availability: undefinedToNull(newEmployeeData.availability),
            contactEmail: undefinedToNull(newEmployeeData.contactEmail),
            contactInfo: newEmployeeData.contactInfo,
            contactMobile: undefinedToNull(newEmployeeData.contactMobile),
            contactPhone: undefinedToNull(newEmployeeData.contactPhone),
            createdAt: now,
            currentLocation: undefinedToNull(newEmployeeData.currentLocation),
            department: undefinedToNull(newEmployeeData.department),
            employeeId: newEmployeeData.employeeId,
            fullName: newEmployeeData.fullName || newEmployeeData.name,
            generalAssignments: newEmployeeData.generalAssignments || [],
            hireDate: undefinedToNull(newEmployeeData.hireDate),
            id: Number(tempId.replace('optimistic-', '')), // Attempt number ID
            name: newEmployeeData.name,
            notes: undefinedToNull(newEmployeeData.notes),
            position: undefinedToNull(newEmployeeData.position),
            profileImageUrl: undefinedToNull(newEmployeeData.profileImageUrl),
            role: newEmployeeData.role,
            shiftSchedule: undefinedToNull(newEmployeeData.shiftSchedule),
            skills: newEmployeeData.skills || [],
            status: undefinedToNull(newEmployeeData.status),
            updatedAt: now,
            ...(newEmployeeData.workingHours !== undefined && {
              workingHours: undefinedToNull(newEmployeeData.workingHours),
            }),
          };
          return [...old, optimisticEmployee];
        }
      );

      return { previousEmployees };
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: employeeQueryKeys.all });
    },
    onSuccess: data => {
      showSuccess(`Employee "${data.name}" has been created successfully!`);
    },
  });
};

/**
 * Custom hook for updating an existing employee.
 * Includes optimistic updates and rollback on error.
 * @returns Mutation result for updating an employee.
 */
export const useUpdateEmployee = () => {
  const queryClient = useQueryClient();

  interface UpdateContext {
    previousEmployee: Employee | undefined;
    previousEmployeesList: Employee[] | undefined;
  }

  return useMutation<
    Employee,
    Error,
    { data: Partial<CreateEmployeeData>; id: string },
    UpdateContext
  >({
    mutationFn: async ({ data, id }) => {
      const request = EmployeeTransformer.toUpdateRequest(data); // Removed cast
      return await employeeApiService.update(id, request); // Removed redundant EmployeeTransformer.fromApi
    },
    onError: (err, variables, context) => {
      if (context?.previousEmployee) {
        queryClient.setQueryData(
          employeeQueryKeys.detail(variables.id),
          context.previousEmployee
        );
      }
      if (context?.previousEmployeesList) {
        queryClient.setQueryData(
          employeeQueryKeys.all,
          context.previousEmployeesList
        );
      }
      console.error('Failed to update employee:', err);
    },
    onMutate: async ({ data, id }) => {
      await queryClient.cancelQueries({ queryKey: employeeQueryKeys.all });
      await queryClient.cancelQueries({
        queryKey: employeeQueryKeys.detail(id),
      });

      const previousEmployee = queryClient.getQueryData<Employee>(
        employeeQueryKeys.detail(id)
      );
      const previousEmployeesList = queryClient.getQueryData<Employee[]>(
        employeeQueryKeys.all
      );

      queryClient.setQueryData<Employee>(employeeQueryKeys.detail(id), old => {
        if (!old) return old;
        const now = new Date().toISOString();

        // Explicitly map updated fields to avoid issues with spread operator on different types
        const updatedOptimistic: Employee = {
          ...old,
          availability: undefinedToNull(
            data.availability === undefined
              ? old.availability
              : data.availability
          ),
          contactEmail: undefinedToNull(
            data.contactEmail === undefined
              ? old.contactEmail
              : data.contactEmail
          ),
          contactInfo: data.contactInfo ?? old.contactInfo,
          contactMobile: undefinedToNull(
            data.contactMobile === undefined
              ? old.contactMobile
              : data.contactMobile
          ),
          contactPhone: undefinedToNull(
            data.contactPhone === undefined
              ? old.contactPhone
              : data.contactPhone
          ),
          currentLocation: undefinedToNull(
            data.currentLocation === undefined
              ? old.currentLocation
              : data.currentLocation
          ),
          department: undefinedToNull(data.department ?? old.department),
          employeeId: data.employeeId ?? old.employeeId,
          fullName: undefinedToNull(data.fullName ?? old.fullName),
          generalAssignments: data.generalAssignments ?? old.generalAssignments,
          hireDate: undefinedToNull(data.hireDate ?? old.hireDate),
          name: data.name ?? old.name,
          notes: undefinedToNull(
            data.notes === undefined ? old.notes : data.notes
          ),
          position: undefinedToNull(data.position ?? old.position),
          profileImageUrl: undefinedToNull(
            data.profileImageUrl === undefined
              ? old.profileImageUrl
              : data.profileImageUrl
          ),
          role: data.role ?? old.role,
          shiftSchedule: undefinedToNull(
            data.shiftSchedule === undefined
              ? old.shiftSchedule
              : data.shiftSchedule
          ),
          skills: data.skills ?? old.skills,
          status: undefinedToNull(data.status ?? old.status),
          updatedAt: now,
          ...(data.workingHours !== undefined && {
            workingHours: undefinedToNull(data.workingHours),
          }),
        };
        return updatedOptimistic;
      });

      queryClient.setQueryData<Employee[]>(
        employeeQueryKeys.all,
        (old = []) => {
          return old.map(employee => {
            if (employee.id === Number(id)) {
              const now = new Date().toISOString();
              return {
                ...employee,
                availability: undefinedToNull(
                  data.availability ?? employee.availability
                ),
                contactEmail: undefinedToNull(
                  data.contactEmail ?? employee.contactEmail
                ),
                contactInfo: data.contactInfo ?? employee.contactInfo,
                contactMobile: undefinedToNull(
                  data.contactMobile ?? employee.contactMobile
                ),
                contactPhone: undefinedToNull(
                  data.contactPhone ?? employee.contactPhone
                ),
                currentLocation: undefinedToNull(
                  data.currentLocation ?? employee.currentLocation
                ),
                department: undefinedToNull(
                  data.department ?? employee.department
                ),
                employeeId: data.employeeId ?? employee.employeeId,
                fullName: undefinedToNull(data.fullName ?? employee.fullName),
                generalAssignments:
                  data.generalAssignments ?? employee.generalAssignments,
                hireDate: undefinedToNull(data.hireDate ?? employee.hireDate),
                name: data.name ?? employee.name,
                notes: undefinedToNull(data.notes ?? employee.notes),
                position: undefinedToNull(data.position ?? employee.position),
                profileImageUrl: undefinedToNull(
                  data.profileImageUrl ?? employee.profileImageUrl
                ),
                role: data.role ?? employee.role,
                shiftSchedule: undefinedToNull(
                  data.shiftSchedule ?? employee.shiftSchedule
                ),
                skills: data.skills ?? employee.skills,
                status: undefinedToNull(data.status ?? employee.status),
                updatedAt: now,
                ...(data.workingHours !== undefined && {
                  workingHours: undefinedToNull(data.workingHours),
                }),
              };
            }
            return employee;
          });
        }
      );

      return { previousEmployee, previousEmployeesList };
    },
    onSettled: (_data, _error, variables) => {
      queryClient.invalidateQueries({
        queryKey: employeeQueryKeys.detail(variables.id),
      });
      queryClient.invalidateQueries({ queryKey: employeeQueryKeys.all });
    },
  });
};

/**
 * Custom hook for deleting an existing employee.
 * Includes cache updates.
 * @returns Mutation result for deleting an employee.
 */
export const useDeleteEmployee = () => {
  const queryClient = useQueryClient();

  interface DeleteContext {
    previousEmployeesList: Employee[] | undefined;
  }

  return useMutation<string, Error, string, DeleteContext>({
    mutationFn: async (id: string) => {
      await employeeApiService.delete(id);
      return id;
    },
    onError: (err, _id, context) => {
      if (context?.previousEmployeesList) {
        queryClient.setQueryData(
          employeeQueryKeys.all,
          context.previousEmployeesList
        );
      }
      console.error('Failed to delete employee:', err);
    },
    onMutate: async id => {
      await queryClient.cancelQueries({ queryKey: employeeQueryKeys.all });
      await queryClient.cancelQueries({
        queryKey: employeeQueryKeys.detail(id),
      });

      const previousEmployeesList = queryClient.getQueryData<Employee[]>(
        employeeQueryKeys.all
      );

      queryClient.setQueryData<Employee[]>(
        employeeQueryKeys.all,
        (old = []) => old.filter(employee => employee.id !== Number(id)) // Compare number ID
      );

      queryClient.removeQueries({ queryKey: employeeQueryKeys.detail(id) });

      return { previousEmployeesList };
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: employeeQueryKeys.all });
    },
  });
};

// Removed useAssignVehicleToEmployee hook as its functionality is now
// handled by the main update employee mutation.

/**
 * Custom hook for updating an employee's availability status.
 * @returns Mutation result for updating availability status.
 */
export const useUpdateEmployeeAvailabilityStatus = () => {
  const queryClient = useQueryClient();

  interface UpdateAvailabilityContext {
    previousEmployee: Employee | undefined;
  }

  // The input 'status' should align with DriverAvailabilityPrisma
  return useMutation<
    Employee,
    Error,
    { employeeId: string; status: DriverAvailabilityPrisma },
    UpdateAvailabilityContext
  >({
    mutationFn: async ({ employeeId, status }) => {
      // employeeApiService.updateAvailabilityStatus now expects DriverAvailabilityPrisma directly
      const response = await employeeApiService.updateAvailabilityStatus(
        employeeId,
        status
      );
      return response; // Removed redundant EmployeeTransformer.fromApi
    },
    onError: (err, variables, context) => {
      if (context?.previousEmployee) {
        queryClient.setQueryData(
          employeeQueryKeys.detail(variables.employeeId),
          context.previousEmployee
        );
      }
      console.error('Failed to update employee availability status:', err);
    },
    onMutate: async ({ employeeId, status }) => {
      await queryClient.cancelQueries({
        queryKey: employeeQueryKeys.detail(employeeId),
      });
      const previousEmployee = queryClient.getQueryData<Employee>(
        employeeQueryKeys.detail(employeeId)
      );

      queryClient.setQueryData<Employee>(
        employeeQueryKeys.detail(employeeId),
        old => {
          // Update the 'availability' field in the domain model
          return old ? { ...old, availability: status } : old;
        }
      );

      return { previousEmployee };
    },
    onSettled: (_data, _error, variables) => {
      queryClient.invalidateQueries({
        queryKey: employeeQueryKeys.detail(variables.employeeId),
      });
      queryClient.invalidateQueries({ queryKey: employeeQueryKeys.all });
    },
  });
};
