import crypto from 'crypto';
import { promisify } from 'util';

import { logSecurityEvent, logStartup } from '../utils/logger.js';

// Promisify crypto functions for async usage
const scryptAsync = promisify(crypto.scrypt);

/**
 * PHASE 1 SECURITY HARDENING: Comprehensive Secrets Management
 *
 * This module validates and manages all application secrets to ensure
 * strong security practices and prevent credential exposure.
 */

// Required secrets for application security
const REQUIRED_SECRETS = [
  'SUPABASE_URL',
  'SUPABASE_ANON_KEY',
  'SUPABASE_SERVICE_ROLE_KEY',
  'JWT_SECRET',
] as const;

// Optional but recommended secrets
const RECOMMENDED_SECRETS = [
  'API_SECRET',
  'SESSION_SECRET',
  'ENCRYPTION_KEY',
  'COOKIE_SECRET',
] as const;

// Secrets that must meet minimum length requirements
const SECRET_LENGTH_REQUIREMENTS = {
  API_SECRET: 32,
  COOKIE_SECRET: 32,
  ENCRYPTION_KEY: 32,
  JWT_SECRET: 32,
  SESSION_SECRET: 32,
} as const;

// Secrets that should not contain certain patterns (weak secrets)
const WEAK_SECRET_PATTERNS = [
  /^(test|demo|example|sample|default)/i,
  /^(123|password|secret|key)/i,
  /^(GENERATE|REPLACE|CHANGE|UPDATE)/i,
  /^[a-zA-Z0-9]{1,8}$/, // Too short and simple
];

interface SecretValidationResult {
  insecure: string[];
  isValid: boolean;
  missing: string[];
  recommendations: string[];
  warnings: string[];
  weak: string[];
}

/**
 * Validates all application secrets for security compliance
 */
export const validateSecrets = (): SecretValidationResult => {
  const result: SecretValidationResult = {
    insecure: [],
    isValid: true,
    missing: [],
    recommendations: [],
    warnings: [],
    weak: [],
  };

  // Check required secrets
  for (const secret of REQUIRED_SECRETS) {
    const value = process.env[secret];

    if (!value) {
      result.missing.push(secret);
      result.isValid = false;
    } else {
      // Check for weak patterns
      if (isWeakSecret(value)) {
        result.weak.push(secret);
        result.isValid = false;
      }

      // Check length requirements
      const minLength =
        SECRET_LENGTH_REQUIREMENTS[secret as keyof typeof SECRET_LENGTH_REQUIREMENTS];
      if (minLength && value.length < minLength) {
        result.weak.push(`${secret} (minimum ${minLength} characters required)`);
        result.isValid = false;
      }

      // Check for insecure patterns
      if (isInsecureSecret(value)) {
        result.insecure.push(secret);
        result.isValid = false;
      }
    }
  }

  // Check recommended secrets
  for (const secret of RECOMMENDED_SECRETS) {
    const value = process.env[secret];

    if (!value) {
      result.warnings.push(`Recommended secret ${secret} is not set`);
      result.recommendations.push(`Generate ${secret} using: openssl rand -base64 32`);
    }
  }

  // Additional security checks
  performAdditionalSecurityChecks(result);

  return result;
};

/**
 * Checks if a secret value matches weak patterns
 */
const isWeakSecret = (value: string): boolean => {
  return WEAK_SECRET_PATTERNS.some(pattern => pattern.test(value));
};

/**
 * Checks if a secret value is insecure (e.g., contains obvious patterns)
 */
const isInsecureSecret = (value: string): boolean => {
  // Check for repeated characters
  if (/(.)\1{4,}/.test(value)) return true;

  // Check for sequential patterns
  if (/(?:abc|123|xyz|789|qwe|asd|zxc)/i.test(value)) return true;

  // Check for common words
  if (/(?:admin|user|pass|login|auth|token|secret|key|test)/i.test(value)) return true;

  return false;
};

/**
 * Performs additional security checks and recommendations
 */
const performAdditionalSecurityChecks = (result: SecretValidationResult): void => {
  // Check NODE_ENV
  const nodeEnv = process.env.NODE_ENV;
  if (nodeEnv === 'production') {
    // Production-specific checks
    if (process.env.JWT_SECRET === process.env.API_SECRET) {
      result.insecure.push('JWT_SECRET and API_SECRET should be different');
      result.isValid = false;
    }
  }

  // Check for development secrets in production
  if (nodeEnv === 'production') {
    const devPatterns = [/localhost/, /127\.0\.0\.1/, /dev/, /test/];
    for (const secret of REQUIRED_SECRETS) {
      const value = process.env[secret];
      if (value && devPatterns.some(pattern => pattern.test(value))) {
        result.insecure.push(`${secret} appears to contain development values in production`);
        result.isValid = false;
      }
    }
  }
};

/**
 * Generates a cryptographically secure secret
 */
export const generateSecureSecret = (length = 32): string => {
  return crypto.randomBytes(length).toString('base64');
};

/**
 * PHASE 2 SECURITY HARDENING: Data Protection Utilities
 * Implements secure data encryption/decryption for sensitive data at rest
 */

/**
 * Encrypts sensitive data using AES-256-GCM
 */
export const encryptSensitiveData = async (
  plaintext: string,
  encryptionKey?: string,
): Promise<{ encrypted: string; iv: string; tag: string }> => {
  try {
    const key = encryptionKey || process.env.ENCRYPTION_KEY;
    if (!key) {
      throw new Error('ENCRYPTION_KEY not configured');
    }

    // Derive key using scrypt for additional security
    const salt = crypto.randomBytes(16);
    const derivedKey = (await scryptAsync(key, salt, 32)) as Buffer;

    // Generate random IV
    const iv = crypto.randomBytes(16);

    // Create cipher
    const cipher = crypto.createCipheriv('aes-256-gcm', derivedKey, iv);
    cipher.setAAD(Buffer.from('WorkHub-Data-Protection', 'utf8'));

    // Encrypt data
    let encrypted = cipher.update(plaintext, 'utf8', 'hex');
    encrypted += cipher.final('hex');

    // Get authentication tag
    const tag = cipher.getAuthTag();

    return {
      encrypted: `${salt.toString('hex')}:${encrypted}`,
      iv: iv.toString('hex'),
      tag: tag.toString('hex'),
    };
  } catch (error) {
    throw new Error(
      `Data encryption failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
    );
  }
};

/**
 * Decrypts sensitive data using AES-256-GCM
 */
export const decryptSensitiveData = async (
  encryptedData: string,
  iv: string,
  tag: string,
  encryptionKey?: string,
): Promise<string> => {
  try {
    const key = encryptionKey || process.env.ENCRYPTION_KEY;
    if (!key) {
      throw new Error('ENCRYPTION_KEY not configured');
    }

    // Extract salt and encrypted data
    const [saltHex, encrypted] = encryptedData.split(':');
    const salt = Buffer.from(saltHex, 'hex');

    // Derive key using scrypt
    const derivedKey = (await scryptAsync(key, salt, 32)) as Buffer;

    // Create decipher
    const decipher = crypto.createDecipheriv('aes-256-gcm', derivedKey, Buffer.from(iv, 'hex'));
    decipher.setAAD(Buffer.from('WorkHub-Data-Protection', 'utf8'));
    decipher.setAuthTag(Buffer.from(tag, 'hex'));

    // Decrypt data
    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');

    return decrypted;
  } catch (error) {
    throw new Error(
      `Data decryption failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
    );
  }
};

/**
 * Validates secrets and throws error if validation fails
 */
export const validateSecretsOrThrow = (): void => {
  const validation = validateSecrets();

  if (!validation.isValid) {
    const errorMessages = [];

    if (validation.missing.length > 0) {
      errorMessages.push(`Missing required secrets: ${validation.missing.join(', ')}`);
    }

    if (validation.weak.length > 0) {
      errorMessages.push(`Weak secrets detected: ${validation.weak.join(', ')}`);
    }

    if (validation.insecure.length > 0) {
      errorMessages.push(`Insecure secrets detected: ${validation.insecure.join(', ')}`);
    }

    throw new Error(`🚨 SECURITY VALIDATION FAILED:\n${errorMessages.join('\n')}`);
  }

  // Log warnings
  if (validation.warnings.length > 0) {
    logSecurityEvent(
      'SECURITY_WARNINGS',
      {
        warnings: validation.warnings,
      },
      'warn',
    );
  }

  // Log recommendations
  if (validation.recommendations.length > 0) {
    logStartup('💡 Security Recommendations', {
      recommendations: validation.recommendations,
    });
  }

  logStartup('✅ All required secrets validated successfully');
};

/**
 * Generates all missing secrets and displays them
 */
export const generateMissingSecrets = (): void => {
  const validation = validateSecrets();

  logStartup('🔐 PHASE 1 SECURITY: Generated Secrets');
  logStartup('=====================================');

  const generatedSecrets = [];
  for (const secret of [...REQUIRED_SECRETS, ...RECOMMENDED_SECRETS]) {
    const value = process.env[secret];
    if (!value || isWeakSecret(value)) {
      const generated = generateSecureSecret();
      generatedSecrets.push(`${secret}=${generated}`);
      logStartup(`Generated secret: ${secret}`, {
        length: generated.length,
        secretName: secret,
      });
    }
  }

  logStartup('=====================================');
  logStartup('⚠️  Copy these to your .env file and restart the application', {
    generatedCount: generatedSecrets.length,
    secrets: generatedSecrets,
  });
};

export default {
  generateMissingSecrets,
  generateSecureSecret,
  validateSecrets,
  validateSecretsOrThrow,
};
