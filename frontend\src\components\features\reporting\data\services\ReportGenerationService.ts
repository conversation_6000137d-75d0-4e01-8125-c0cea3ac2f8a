// frontend/src/components/features/reporting/data/services/ReportGenerationService.ts

/**
 * Report Generation Service
 *
 * Follows SOLID Principles:
 * - SRP: Single responsibility for coordinating report generation
 * - OCP: Open for extension with new report types
 * - LSP: Implements consistent interface for all report types
 * - ISP: Focused interface for report generation
 * - DIP: Depends on abstractions (data services, export services)
 */

// Import types from the hook file where they are defined
export interface ReportGenerationResult {
  data?: any; // Main report data
  report?: any; // Legacy support
  metadata: {
    id: string;
    type: string;
    entityTypes?: string[];
    entityType?: string;
    entityId?: string;
    format: string;
    template: string;
    generatedAt: string;
    generatedBy: string;
    filters?: Record<string, any>;
    options?: Record<string, any>;
  };
}

export interface AggregateReportConfig {
  entityType: string;
  filters?: Record<string, any>;
  template?: string;
  format?: string;
  options?: Record<string, any>;
}

export interface IndividualReportConfig {
  entityType: string;
  entityId: string;
  template?: string;
  format?: string;
  options?: Record<string, any>;
}

export interface ReportGenerationConfig {
  entityTypes: string[];
  template: string;
  format: string;
  filters?: Record<string, any>;
  options?: {
    name?: string;
    description?: string;
    includeCharts?: boolean;
    includeSummary?: boolean;
  };
}

/**
 * API Client interface for dependency injection
 */
export interface IApiClient {
  request(config: {
    url: string;
    method: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';
    data?: any;
  }): Promise<{ data: any }>;
}

/**
 * Interface for report generation service (DIP - Dependency Inversion)
 */
export interface IReportGenerationService {
  generateAggregateReport(
    config: AggregateReportConfig
  ): Promise<ReportGenerationResult>;
  generateIndividualReport(
    config: IndividualReportConfig
  ): Promise<ReportGenerationResult>;
  generateComprehensiveReport(
    config: ReportGenerationConfig
  ): Promise<ReportGenerationResult>;
}

/**
 * Concrete implementation of report generation service
 *
 * SRP: Handles only report generation coordination
 * DRY: Reuses common request logic
 */
export class ReportGenerationService implements IReportGenerationService {
  constructor(private apiClient: IApiClient) {}
  /**
   * Generate aggregate analytics report for entity type
   *
   * @param config - Aggregate report configuration
   * @returns Promise<ReportGenerationResult>
   */
  async generateAggregateReport(
    config: AggregateReportConfig
  ): Promise<ReportGenerationResult> {
    try {
      const response = await this.apiClient.request({
        url: `/api/reporting/reports/aggregate/${config.entityType}`,
        method: 'POST',
        data: {
          filters: config.filters,
          template: config.template || 'default',
          format: 'json', // Always get JSON data for frontend processing
          options: config.options || {},
        },
      });

      // Normalize response structure - handle nested aggregate data
      const responseData = response.data.data || response.data;
      const aggregateData = responseData?.aggregate || responseData;

      return {
        data: aggregateData,
        metadata: response.data.metadata ||
          responseData?.metadata || {
            id: `aggregate_${config.entityType}_${Date.now()}`,
            type: 'aggregate',
            entityType: config.entityType,
            format: config.format || 'json',
            template: config.template || 'default',
            generatedAt: new Date().toISOString(),
            generatedBy: 'system',
            filters: config.filters,
            options: config.options,
          },
      };
    } catch (error) {
      console.error('Failed to generate aggregate report:', error);
      throw new Error(
        `Failed to generate ${config.entityType} aggregate report`
      );
    }
  }

  /**
   * Generate individual entity report
   *
   * @param config - Individual report configuration
   * @returns Promise<ReportGenerationResult>
   */
  async generateIndividualReport(
    config: IndividualReportConfig
  ): Promise<ReportGenerationResult> {
    try {
      const response = await this.apiClient.request({
        url: `/api/reporting/reports/individual/${config.entityType}/${config.entityId}`,
        method: 'POST',
        data: {
          template: config.template || 'default',
          format: 'json', // Always get JSON data for frontend processing
          options: config.options || {},
        },
      });

      return {
        data: response.data.data || response.data,
        metadata: response.data.metadata || {
          id: `individual_${config.entityType}_${config.entityId}_${Date.now()}`,
          type: 'individual',
          entityType: config.entityType,
          entityId: config.entityId,
          format: config.format || 'json',
          template: config.template || 'default',
          generatedAt: new Date().toISOString(),
          generatedBy: 'system',
          options: config.options,
        },
      };
    } catch (error) {
      console.error('Failed to generate individual report:', error);
      throw new Error(
        `Failed to generate ${config.entityType} individual report`
      );
    }
  }

  /**
   * Generate comprehensive cross-entity report
   *
   * @param config - Comprehensive report configuration
   * @returns Promise<ReportGenerationResult>
   */
  async generateComprehensiveReport(
    config: ReportGenerationConfig
  ): Promise<ReportGenerationResult> {
    try {
      const response = await this.apiClient.request({
        url: '/api/reporting/reports/generate',
        method: 'POST',
        data: {
          entityTypes: config.entityTypes,
          filters: config.filters,
          template: config.template || 'comprehensive',
          format: 'json', // Always get JSON data for frontend processing
          options: config.options || {},
        },
      });

      return {
        data: response.data.data || response.data,
        metadata: response.data.metadata || {
          id: `comprehensive_${Date.now()}`,
          type: 'comprehensive',
          entityTypes: config.entityTypes,
          format: config.format || 'json',
          template: config.template || 'comprehensive',
          generatedAt: new Date().toISOString(),
          generatedBy: 'system',
          filters: config.filters,
          options: config.options,
        },
      };
    } catch (error) {
      console.error('Failed to generate comprehensive report:', error);
      throw new Error('Failed to generate comprehensive report');
    }
  }
}

/**
 * Default API client implementation
 * Note: This will be injected from the hook that has access to secureRequest
 */
class DefaultApiClient implements IApiClient {
  async request(config: {
    url: string;
    method: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';
    data?: any;
  }): Promise<{ data: any }> {
    throw new Error(
      'API client not initialized. Use createReportGenerationService with proper API client.'
    );
  }
}

/**
 * Factory function for dependency injection (DIP)
 * Allows for easy testing and mocking
 */
export const createReportGenerationService = (
  apiClient: IApiClient
): IReportGenerationService => {
  return new ReportGenerationService(apiClient);
};

/**
 * Default instance (will be replaced by hook with proper API client)
 */
export const reportGenerationService = new ReportGenerationService(
  new DefaultApiClient()
);
