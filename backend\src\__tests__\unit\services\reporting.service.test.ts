import { ReportingService } from '../../../services/reporting.service';
import { PrismaClient } from '@prisma/client';
import { jest } from '@jest/globals';

// Mock Prisma Client
jest.mock('@prisma/client');
const mockPrismaClient = {
  delegation: {
    findMany: jest.fn(),
    count: jest.fn(),
    groupBy: jest.fn(),
  },
  task: {
    findMany: jest.fn(),
    count: jest.fn(),
    groupBy: jest.fn(),
  },
  vehicle: {
    findMany: jest.fn(),
    count: jest.fn(),
  },
  employee: {
    findMany: jest.fn(),
    count: jest.fn(),
  },
  serviceHistory: {
    findMany: jest.fn(),
    count: jest.fn(),
  },
} as unknown as PrismaClient;

describe('ReportingService', () => {
  let reportingService: ReportingService;

  beforeEach(() => {
    reportingService = new ReportingService(mockPrismaClient);
    jest.clearAllMocks();
  });

  describe('getDelegationAnalytics', () => {
    it('should return delegation analytics with correct structure', async () => {
      // Arrange
      const filters = {
        dateRange: {
          from: new Date('2024-01-01'),
          to: new Date('2024-12-31'),
        },
        status: ['PENDING', 'APPROVED'],
        locations: ['New York', 'London'],
        employees: [1, 2, 3],
        vehicles: [1, 2],
      };

      const mockDelegations = [
        {
          id: 1,
          delegationId: 'DEL-001',
          status: 'PENDING',
          location: 'New York',
          employeeId: 1,
          vehicleId: 1,
          createdAt: new Date('2024-06-01'),
          completedAt: null,
        },
        {
          id: 2,
          delegationId: 'DEL-002',
          status: 'APPROVED',
          location: 'London',
          employeeId: 2,
          vehicleId: 2,
          createdAt: new Date('2024-06-02'),
          completedAt: new Date('2024-06-03'),
        },
      ];

      const mockStatusDistribution = [
        { status: 'PENDING', _count: { status: 1 } },
        { status: 'APPROVED', _count: { status: 1 } },
      ];

      mockPrismaClient.delegation.findMany.mockResolvedValue(mockDelegations);
      mockPrismaClient.delegation.count.mockResolvedValue(2);
      mockPrismaClient.delegation.groupBy.mockResolvedValue(mockStatusDistribution);

      // Act
      const result = await reportingService.getDelegationAnalytics(filters);

      // Assert
      expect(result).toBeDefined();
      expect(result.totalCount).toBe(2);
      expect(result.statusDistribution).toHaveLength(2);
      expect(result.statusDistribution[0]).toMatchObject({
        status: 'PENDING',
        count: 1,
        percentage: 50,
      });
      expect(result.summary).toBeDefined();
      expect(result.summary.totalDelegations).toBe(2);
    });

    it('should handle empty results gracefully', async () => {
      // Arrange
      const filters = {
        dateRange: {
          from: new Date('2024-01-01'),
          to: new Date('2024-12-31'),
        },
        status: [],
        locations: [],
        employees: [],
        vehicles: [],
      };

      mockPrismaClient.delegation.findMany.mockResolvedValue([]);
      mockPrismaClient.delegation.count.mockResolvedValue(0);
      mockPrismaClient.delegation.groupBy.mockResolvedValue([]);

      // Act
      const result = await reportingService.getDelegationAnalytics(filters);

      // Assert
      expect(result).toBeDefined();
      expect(result.totalCount).toBe(0);
      expect(result.statusDistribution).toHaveLength(0);
      expect(result.summary.totalDelegations).toBe(0);
    });

    it('should handle database errors gracefully', async () => {
      // Arrange
      const filters = {
        dateRange: {
          from: new Date('2024-01-01'),
          to: new Date('2024-12-31'),
        },
        status: [],
        locations: [],
        employees: [],
        vehicles: [],
      };

      mockPrismaClient.delegation.findMany.mockRejectedValue(
        new Error('Database connection failed'),
      );

      // Act & Assert
      await expect(reportingService.getDelegationAnalytics(filters)).rejects.toThrow(
        'Database connection failed',
      );
    });
  });

  describe('getTaskAnalytics', () => {
    it('should return task analytics with correct structure', async () => {
      // Arrange
      const filters = {
        dateRange: {
          from: new Date('2024-01-01'),
          to: new Date('2024-12-31'),
        },
        status: [],
        locations: [],
        employees: [],
        vehicles: [],
        taskStatus: ['IN_PROGRESS', 'COMPLETED'],
        taskPriority: ['HIGH', 'MEDIUM'],
      };

      const mockTasks = [
        {
          id: 1,
          title: 'Task 1',
          status: 'IN_PROGRESS',
          priority: 'HIGH',
          assigneeId: 1,
          createdAt: new Date('2024-06-01'),
          completedAt: null,
        },
        {
          id: 2,
          title: 'Task 2',
          status: 'COMPLETED',
          priority: 'MEDIUM',
          assigneeId: 2,
          createdAt: new Date('2024-06-02'),
          completedAt: new Date('2024-06-03'),
        },
      ];

      const mockStatusDistribution = [
        { status: 'IN_PROGRESS', _count: { status: 1 } },
        { status: 'COMPLETED', _count: { status: 1 } },
      ];

      const mockPriorityDistribution = [
        { priority: 'HIGH', _count: { priority: 1 } },
        { priority: 'MEDIUM', _count: { priority: 1 } },
      ];

      mockPrismaClient.task.findMany.mockResolvedValue(mockTasks);
      mockPrismaClient.task.count.mockResolvedValue(2);
      mockPrismaClient.task.groupBy
        .mockResolvedValueOnce(mockStatusDistribution)
        .mockResolvedValueOnce(mockPriorityDistribution);

      // Act
      const result = await reportingService.getTaskAnalytics(filters);

      // Assert
      expect(result).toBeDefined();
      expect(result.totalCount).toBe(2);
      expect(result.statusDistribution).toHaveLength(2);
      expect(result.priorityDistribution).toHaveLength(2);
      expect(result.completionRate).toBeGreaterThanOrEqual(0);
      expect(result.completionRate).toBeLessThanOrEqual(1);
    });

    it('should calculate completion rate correctly', async () => {
      // Arrange
      const filters = {
        dateRange: {
          from: new Date('2024-01-01'),
          to: new Date('2024-12-31'),
        },
        status: [],
        locations: [],
        employees: [],
        vehicles: [],
      };

      const mockTasks = [
        { id: 1, status: 'COMPLETED', completedAt: new Date() },
        { id: 2, status: 'COMPLETED', completedAt: new Date() },
        { id: 3, status: 'IN_PROGRESS', completedAt: null },
        { id: 4, status: 'PENDING', completedAt: null },
      ];

      mockPrismaClient.task.findMany.mockResolvedValue(mockTasks);
      mockPrismaClient.task.count.mockResolvedValue(4);
      mockPrismaClient.task.groupBy.mockResolvedValue([]);

      // Act
      const result = await reportingService.getTaskAnalytics(filters);

      // Assert
      expect(result.completionRate).toBe(0.5); // 2 completed out of 4 total
    });
  });

  describe('getVehicleAnalytics', () => {
    it('should return vehicle analytics with correct structure', async () => {
      // Arrange
      const filters = {
        dateRange: {
          from: new Date('2024-01-01'),
          to: new Date('2024-12-31'),
        },
        status: [],
        locations: ['Depot A'],
        employees: [],
        vehicles: [1, 2, 3],
      };

      const mockVehicles = [
        {
          id: 1,
          licensePlate: 'ABC-123',
          model: 'Toyota Camry',
          location: 'Depot A',
          status: 'ACTIVE',
        },
        {
          id: 2,
          licensePlate: 'XYZ-789',
          model: 'Honda Civic',
          location: 'Depot A',
          status: 'MAINTENANCE',
        },
      ];

      const mockServiceHistory = [
        {
          id: 'service-1',
          vehicleId: 1,
          serviceType: 'MAINTENANCE',
          status: 'COMPLETED',
          cost: 500,
          scheduledDate: new Date('2024-06-01'),
          completedDate: new Date('2024-06-01'),
        },
      ];

      mockPrismaClient.vehicle.findMany.mockResolvedValue(mockVehicles);
      mockPrismaClient.vehicle.count.mockResolvedValue(2);
      mockPrismaClient.serviceHistory.findMany.mockResolvedValue(mockServiceHistory);

      // Act
      const result = await reportingService.getVehicleAnalytics(filters);

      // Assert
      expect(result).toBeDefined();
      expect(result.totalCount).toBe(2);
      expect(result.serviceHistory).toHaveLength(1);
      expect(result.costAnalysis).toBeDefined();
      expect(result.costAnalysis.totalCost).toBeGreaterThanOrEqual(0);
    });

    it('should calculate cost analysis correctly', async () => {
      // Arrange
      const filters = {
        dateRange: {
          from: new Date('2024-01-01'),
          to: new Date('2024-12-31'),
        },
        status: [],
        locations: [],
        employees: [],
        vehicles: [],
      };

      const mockServiceHistory = [
        { cost: 500, serviceType: 'MAINTENANCE' },
        { cost: 300, serviceType: 'REPAIR' },
        { cost: 200, serviceType: 'MAINTENANCE' },
      ];

      mockPrismaClient.vehicle.findMany.mockResolvedValue([]);
      mockPrismaClient.vehicle.count.mockResolvedValue(0);
      mockPrismaClient.serviceHistory.findMany.mockResolvedValue(mockServiceHistory);

      // Act
      const result = await reportingService.getVehicleAnalytics(filters);

      // Assert
      expect(result.costAnalysis.totalCost).toBe(1000);
      expect(result.costAnalysis.averageCostPerService).toBe(333.33);
      expect(result.costAnalysis.costByType).toHaveLength(2);
    });
  });

  describe('getEmployeeAnalytics', () => {
    it('should return employee analytics with correct structure', async () => {
      // Arrange
      const filters = {
        dateRange: {
          from: new Date('2024-01-01'),
          to: new Date('2024-12-31'),
        },
        status: [],
        locations: ['Site A'],
        employees: [1, 2, 3],
        vehicles: [],
      };

      const mockEmployees = [
        {
          id: 1,
          name: 'John Doe',
          email: '<EMAIL>',
          role: 'DRIVER',
          location: 'Site A',
        },
        {
          id: 2,
          name: 'Jane Smith',
          email: '<EMAIL>',
          role: 'TECHNICIAN',
          location: 'Site A',
        },
      ];

      const mockDelegations = [
        { id: 1, employeeId: 1, status: 'COMPLETED' },
        { id: 2, employeeId: 1, status: 'PENDING' },
        { id: 3, employeeId: 2, status: 'COMPLETED' },
      ];

      const mockTasks = [
        { id: 1, assigneeId: 1, status: 'COMPLETED' },
        { id: 2, assigneeId: 2, status: 'IN_PROGRESS' },
      ];

      mockPrismaClient.employee.findMany.mockResolvedValue(mockEmployees);
      mockPrismaClient.employee.count.mockResolvedValue(2);
      mockPrismaClient.delegation.findMany.mockResolvedValue(mockDelegations);
      mockPrismaClient.task.findMany.mockResolvedValue(mockTasks);

      // Act
      const result = await reportingService.getEmployeeAnalytics(filters);

      // Assert
      expect(result).toBeDefined();
      expect(result.totalCount).toBe(2);
      expect(result.performanceMetrics).toHaveLength(2);
      expect(result.delegationHistory).toHaveLength(2);
      expect(result.taskAssignments).toHaveLength(2);
    });

    it('should calculate performance metrics correctly', async () => {
      // Arrange
      const filters = {
        dateRange: {
          from: new Date('2024-01-01'),
          to: new Date('2024-12-31'),
        },
        status: [],
        locations: [],
        employees: [],
        vehicles: [],
      };

      const mockEmployees = [{ id: 1, name: 'John Doe', email: '<EMAIL>' }];

      const mockDelegations = [
        { id: 1, employeeId: 1, status: 'COMPLETED' },
        { id: 2, employeeId: 1, status: 'COMPLETED' },
        { id: 3, employeeId: 1, status: 'PENDING' },
      ];

      const mockTasks = [
        { id: 1, assigneeId: 1, status: 'COMPLETED' },
        { id: 2, assigneeId: 1, status: 'COMPLETED' },
      ];

      mockPrismaClient.employee.findMany.mockResolvedValue(mockEmployees);
      mockPrismaClient.employee.count.mockResolvedValue(1);
      mockPrismaClient.delegation.findMany.mockResolvedValue(mockDelegations);
      mockPrismaClient.task.findMany.mockResolvedValue(mockTasks);

      // Act
      const result = await reportingService.getEmployeeAnalytics(filters);

      // Assert
      const johnPerformance = result.performanceMetrics.find(p => p.employeeId === 1);
      expect(johnPerformance).toBeDefined();
      expect(johnPerformance?.completedDelegations).toBe(2);
      expect(johnPerformance?.completedTasks).toBe(2);
    });
  });

  describe('getCrossEntityAnalytics', () => {
    it('should return cross-entity analytics with correlations', async () => {
      // Arrange
      const filters = {
        dateRange: {
          from: new Date('2024-01-01'),
          to: new Date('2024-12-31'),
        },
        status: [],
        locations: [],
        employees: [],
        vehicles: [],
        includeCrossEntityCorrelations: true,
      };

      // Mock all entity data
      mockPrismaClient.delegation.findMany.mockResolvedValue([]);
      mockPrismaClient.delegation.count.mockResolvedValue(0);
      mockPrismaClient.delegation.groupBy.mockResolvedValue([]);
      mockPrismaClient.task.findMany.mockResolvedValue([]);
      mockPrismaClient.task.count.mockResolvedValue(0);
      mockPrismaClient.task.groupBy.mockResolvedValue([]);
      mockPrismaClient.vehicle.findMany.mockResolvedValue([]);
      mockPrismaClient.vehicle.count.mockResolvedValue(0);
      mockPrismaClient.employee.findMany.mockResolvedValue([]);
      mockPrismaClient.employee.count.mockResolvedValue(0);
      mockPrismaClient.serviceHistory.findMany.mockResolvedValue([]);

      // Act
      const result = await reportingService.getCrossEntityAnalytics(filters);

      // Assert
      expect(result).toBeDefined();
      expect(result.correlations).toBeDefined();
      expect(result.correlations.delegationTaskCorrelation).toBeGreaterThanOrEqual(-1);
      expect(result.correlations.delegationTaskCorrelation).toBeLessThanOrEqual(1);
      expect(result.summary).toBeDefined();
      expect(result.summary.totalEntities).toBeGreaterThanOrEqual(0);
    });
  });

  describe('Error Handling', () => {
    it('should handle Prisma connection errors', async () => {
      // Arrange
      const filters = {
        dateRange: {
          from: new Date('2024-01-01'),
          to: new Date('2024-12-31'),
        },
        status: [],
        locations: [],
        employees: [],
        vehicles: [],
      };

      mockPrismaClient.delegation.findMany.mockRejectedValue(new Error('Connection timeout'));

      // Act & Assert
      await expect(reportingService.getDelegationAnalytics(filters)).rejects.toThrow(
        'Connection timeout',
      );
    });

    it('should handle invalid date ranges', async () => {
      // Arrange
      const filters = {
        dateRange: {
          from: new Date('2024-12-31'), // Invalid: from date after to date
          to: new Date('2024-01-01'),
        },
        status: [],
        locations: [],
        employees: [],
        vehicles: [],
      };

      // Act & Assert
      await expect(reportingService.getDelegationAnalytics(filters)).rejects.toThrow();
    });
  });

  describe('Performance Optimization', () => {
    it('should use efficient queries for large datasets', async () => {
      // Arrange
      const filters = {
        dateRange: {
          from: new Date('2024-01-01'),
          to: new Date('2024-12-31'),
        },
        status: [],
        locations: [],
        employees: [],
        vehicles: [],
      };

      mockPrismaClient.delegation.findMany.mockResolvedValue([]);
      mockPrismaClient.delegation.count.mockResolvedValue(10000); // Large dataset
      mockPrismaClient.delegation.groupBy.mockResolvedValue([]);

      // Act
      await reportingService.getDelegationAnalytics(filters);

      // Assert
      expect(mockPrismaClient.delegation.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          take: expect.any(Number), // Should use pagination
        }),
      );
    });
  });
});
