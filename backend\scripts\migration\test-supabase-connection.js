#!/usr/bin/env node

/**
 * Test Supabase Connection
 * Simple script to test if Supabase connection works
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

console.log('🔍 Testing Supabase connection...');

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testConnection() {
  try {
    console.log('📋 Testing user_profiles table access...');
    
    // Test user_profiles table access with a limit
    const { data, error, count } = await supabase
      .from('user_profiles')
      .select('id, role', { count: 'exact' })
      .limit(1);

    if (error) {
      console.error('❌ Error accessing user_profiles:', error);
      return false;
    }

    console.log(`✅ user_profiles table accessible, total count: ${count}`);
    console.log(`✅ Sample data:`, data);

    console.log('📋 Testing auth.users access...');
    
    // Test auth.users access
    const { data: authData, error: authError } = await supabase.auth.admin.listUsers({
      page: 1,
      perPage: 1
    });

    if (authError) {
      console.error('❌ Error accessing auth.users:', authError);
      return false;
    }

    console.log(`✅ auth.users accessible, found ${authData.users.length} users in first page`);
    
    return true;

  } catch (error) {
    console.error('❌ Connection test failed:', error);
    return false;
  }
}

testConnection()
  .then(success => {
    if (success) {
      console.log('🎉 Supabase connection test passed!');
      process.exit(0);
    } else {
      console.error('💥 Supabase connection test failed!');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('💥 Test failed with error:', error);
    process.exit(1);
  });
