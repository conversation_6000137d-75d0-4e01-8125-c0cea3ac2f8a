/**
 * @file Data transformer for Vehicle domain models.
 * @module transformers/vehicleTransformer
 */

import type {
  CreateVehicleRequest,
  ServiceRecordApiResponse,
  UpdateVehicleRequest,
  VehicleApiResponse,
} from '../types/api'; // Corrected import path
import type {
  CreateVehicleData,
  ServiceRecord,
  Vehicle,
} from '../types/domain';

// ServiceRecordTransformer
const ServiceRecordTransformer = {
  fromApi(apiData: ServiceRecordApiResponse): ServiceRecord {
    return {
      cost: apiData.cost,
      createdAt: apiData.createdAt,
      date: apiData.date,
      employeeId: apiData.employeeId,
      id: apiData.id,
      notes: apiData.notes,
      odometer: apiData.odometer,
      servicePerformed: Array.isArray(apiData.servicePerformed)
        ? apiData.servicePerformed
        : [],
      updatedAt: apiData.updatedAt,
      vehicleId: apiData.vehicleId,
    };
  },
  // toApi for ServiceRecord might be needed if creating/updating service records through vehicle endpoint
  // For now, assuming service records are managed via their own endpoints.
};

/**
 * Transforms vehicle data between API response formats and frontend domain models.
 */
export const VehicleTransformer = {
  /**
   * Converts a raw API Vehicle response into a frontend Vehicle domain model.
   * @param apiData - The raw data received from the API.
   * @returns The transformed Vehicle domain model.
   */
  fromApi(apiData: VehicleApiResponse): Vehicle {
    return {
      color: apiData.color ?? null,
      createdAt: apiData.createdAt,
      id: apiData.id,
      imageUrl: apiData.imageUrl ?? null,
      initialOdometer: apiData.initialOdometer ?? null,
      licensePlate: apiData.licensePlate,
      make: apiData.make,
      model: apiData.model,
      ownerContact: apiData.ownerContact,
      ownerName: apiData.ownerName,
      serviceHistory: (() => {
        const records = apiData.serviceHistory || apiData.ServiceRecord;
        return Array.isArray(records)
          ? records.map(ServiceRecordTransformer.fromApi)
          : [];
      })(),
      updatedAt: apiData.updatedAt,
      vin: apiData.vin ?? null,
      year: apiData.year,
    };
  },

  /**
   * Converts frontend data for creating a vehicle into an API request payload.
   * @param vehicleData - The data from the frontend for creating a new vehicle.
   * @returns The transformed payload, compatible with CreateVehicleRequest.
   */
  toCreateRequest(vehicleData: CreateVehicleData): CreateVehicleRequest {
    // Generate a default VIN if not provided (for demo purposes)
    const defaultVin =
      vehicleData.vin?.trim() || this.generateDefaultVin(vehicleData);

    // Provide valid default values that pass backend validation
    const defaultOwnerContact =
      vehicleData.ownerContact?.trim() || '<EMAIL>';
    const defaultOwnerName =
      vehicleData.ownerName?.trim() || 'WorkHub Fleet Management';

    const request: CreateVehicleRequest = {
      color: vehicleData.color ? vehicleData.color.trim() : null,
      imageUrl: vehicleData.imageUrl ? vehicleData.imageUrl.trim() : '',
      initialOdometer: vehicleData.initialOdometer ?? null,
      licensePlate: vehicleData.licensePlate.trim(),
      make: vehicleData.make.trim(),
      model: vehicleData.model.trim(),
      ownerContact: defaultOwnerContact,
      ownerName: defaultOwnerName,
      vin: defaultVin,
      year: vehicleData.year,
    };

    if (
      !request.make ||
      !request.model ||
      !request.year ||
      !request.licensePlate
    ) {
      throw new Error(
        'Missing required fields for creating a vehicle (make, model, year, licensePlate)'
      );
    }

    // Validate VIN format
    if (!/^[A-HJ-NPR-Z0-9]{17}$/.test(request.vin!)) {
      throw new Error(
        'VIN must be exactly 17 characters and contain only valid characters (A-H, J-N, P-R, Z, 0-9)'
      );
    }

    return request;
  },

  /**
   * Generates a default VIN for demo purposes
   * In production, this should be handled differently
   */
  generateDefaultVin(vehicleData: CreateVehicleData): string {
    // Valid VIN characters (excluding I, O, Q)
    const validChars = 'ABCDEFGHJKLMNPRSTUVWXYZ0123456789';

    // Generate a valid 17-character VIN
    const makeCode = vehicleData.make
      .substring(0, 3)
      .toUpperCase()
      .replace(/[IOQ]/g, 'X')
      .padEnd(3, 'X');

    const modelCode = vehicleData.model
      .substring(0, 2)
      .toUpperCase()
      .replace(/[IOQ]/g, 'X')
      .padEnd(2, 'X');

    // Year code (last 2 digits)
    const yearCode = vehicleData.year.toString().substring(2);

    // Generate remaining 10 characters using valid VIN characters
    let randomCode = '';
    for (let i = 0; i < 10; i++) {
      randomCode += validChars.charAt(
        Math.floor(Math.random() * validChars.length)
      );
    }

    const vin = `${makeCode}${modelCode}${yearCode}${randomCode}`;

    // Ensure exactly 17 characters
    return vin.substring(0, 17).padEnd(17, 'X');
  },

  /**
   * Converts partial frontend vehicle data into an API request payload for updating.
   * @param vehicleData - The partial data from the frontend for updating a vehicle.
   * @returns The transformed payload, compatible with UpdateVehicleRequest.
   */
  toUpdateRequest(
    vehicleData: Partial<CreateVehicleData>
  ): UpdateVehicleRequest {
    const request: UpdateVehicleRequest = {};

    if (vehicleData.make !== undefined) request.make = vehicleData.make.trim();
    if (vehicleData.model !== undefined)
      request.model = vehicleData.model.trim();
    if (vehicleData.year !== undefined) request.year = vehicleData.year;
    if (vehicleData.vin !== undefined) request.vin = vehicleData.vin.trim();
    if (vehicleData.licensePlate !== undefined)
      request.licensePlate = vehicleData.licensePlate.trim();
    if (vehicleData.ownerName !== undefined)
      request.ownerName = vehicleData.ownerName.trim();
    if (vehicleData.ownerContact !== undefined)
      request.ownerContact = vehicleData.ownerContact.trim();
    if (vehicleData.color !== undefined)
      request.color = vehicleData.color ? vehicleData.color.trim() : null;
    if (vehicleData.initialOdometer !== undefined)
      request.initialOdometer = vehicleData.initialOdometer;
    if (vehicleData.imageUrl !== undefined) {
      request.imageUrl = vehicleData.imageUrl
        ? vehicleData.imageUrl.trim()
        : '';
    }

    return request;
  },
};
