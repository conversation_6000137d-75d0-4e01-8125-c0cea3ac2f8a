/**
 * @file ReportBuilder.tsx
 * @description Dynamic report builder component following SOLID principles and established patterns
 */

import type { DragEndEvent, DragStartEvent } from '@dnd-kit/core';

import { DndContext, DragOverlay } from '@dnd-kit/core';
import { Eye, Layout, Save } from 'lucide-react';
import React, { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { cn } from '@/lib/utils';

import type { ReportType, WidgetConfig } from '../data/types/reporting';

import { DropZone } from './DropZone';
import { WidgetConfigurator } from './WidgetConfigurator';
import { WidgetPalette } from './WidgetPalette';

/**
 * Props interface for ReportBuilder
 */
interface ReportBuilderProps {
  className?: string;
  onPreview?: (reportConfig: ReportConfig) => void;
  onSave?: (reportConfig: ReportConfig) => void;
  reportType?: ReportType;
}

/**
 * Report configuration interface
 */
interface ReportConfig {
  dataSource: string;
  description?: string;
  filters: string[];
  layout: {
    columns: number;
    spacing: number;
  };
  name: string;
  widgets: WidgetConfig[];
}

/**
 * ReportBuilder Component
 *
 * Provides drag-and-drop interface for building custom reports.
 *
 * Responsibilities:
 * - Provide drag-and-drop widget placement
 * - Handle widget configuration
 * - Manage report layout and structure
 * - Follow established component patterns
 *
 * SOLID Principles Applied:
 * - SRP: Single responsibility of building reports
 * - OCP: Open for extension via widget types
 * - DIP: Depends on drag-and-drop framework abstractions
 */
export const ReportBuilder: React.FC<ReportBuilderProps> = ({
  className = '',
  onPreview,
  onSave,
  reportType,
}) => {
  // State management
  const [widgets, setWidgets] = useState<WidgetConfig[]>(
    reportType?.widgetConfigs || []
  );
  const [selectedWidget, setSelectedWidget] = useState<null | WidgetConfig>(
    null
  );
  const [isConfiguring, setIsConfiguring] = useState(false);
  const [activeId, setActiveId] = useState<null | string>(null);
  const [reportConfig, setReportConfig] = useState<ReportConfig>({
    dataSource: reportType?.dataSource || 'delegations',
    description: reportType?.description || '',
    filters: reportType?.filters || [],
    layout: {
      columns: 2,
      spacing: 4,
    },
    name: reportType?.name || 'New Report',
    widgets: widgets,
  });

  // Handle drag start
  const handleDragStart = (event: DragStartEvent) => {
    setActiveId(event.active.id as string);
  };

  // Handle drag end
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (!over) {
      setActiveId(null);
      return;
    }

    // Handle dropping widget from palette to drop zone
    if (
      over.id === 'drop-zone' &&
      active.data.current?.type === 'widget-type'
    ) {
      const widgetType = active.data.current.widgetType;
      const newWidget: WidgetConfig = {
        config: {},
        id: `widget-${Date.now()}`,
        position: widgets.length,
        span: 'col-span-1',
        title: `${widgetType} Widget`,
        type: widgetType,
      };

      setWidgets(prev => [...prev, newWidget]);
      setReportConfig(prev => ({
        ...prev,
        widgets: [...prev.widgets, newWidget],
      }));
    }

    // Handle reordering widgets within drop zone
    if (over.id !== active.id && active.data.current?.type === 'widget') {
      const activeIndex = widgets.findIndex(w => w.id === active.id);
      const overIndex = widgets.findIndex(w => w.id === over.id);

      if (activeIndex !== -1 && overIndex !== -1) {
        const newWidgets = [...widgets];
        const [removed] = newWidgets.splice(activeIndex, 1);
        if (removed) {
          newWidgets.splice(overIndex, 0, removed);
        }

        // Update positions
        const updatedWidgets = newWidgets.map((widget, index) => ({
          ...widget,
          position: index,
        }));

        setWidgets(updatedWidgets);
        setReportConfig(prev => ({
          ...prev,
          widgets: updatedWidgets,
        }));
      }
    }

    setActiveId(null);
  };

  // Handle widget configuration
  const handleWidgetConfigure = (widget: WidgetConfig) => {
    setSelectedWidget(widget);
    setIsConfiguring(true);
  };

  // Handle widget update
  const handleWidgetUpdate = (updatedWidget: WidgetConfig) => {
    const updatedWidgets = widgets.map(w =>
      w.id === updatedWidget.id ? updatedWidget : w
    );

    setWidgets(updatedWidgets);
    setReportConfig(prev => ({
      ...prev,
      widgets: updatedWidgets,
    }));

    setIsConfiguring(false);
    setSelectedWidget(null);
  };

  // Handle widget deletion
  const handleWidgetDelete = (widgetId: string) => {
    const updatedWidgets = widgets.filter(w => w.id !== widgetId);
    setWidgets(updatedWidgets);
    setReportConfig(prev => ({
      ...prev,
      widgets: updatedWidgets,
    }));
  };

  // Handle save
  const handleSave = () => {
    if (onSave) {
      onSave(reportConfig);
    }
  };

  // Handle preview
  const handlePreview = () => {
    if (onPreview) {
      onPreview(reportConfig);
    }
  };

  return (
    <div className={cn('space-y-6', className)}>
      {/* Header */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Layout className="size-5" />
              Report Builder
            </CardTitle>
            <div className="flex items-center gap-2">
              <Badge className="text-xs" variant="secondary">
                {widgets.length} widgets
              </Badge>
              <Button onClick={handlePreview} size="sm" variant="outline">
                <Eye className="mr-2 size-4" />
                Preview
              </Button>
              <Button onClick={handleSave} size="sm">
                <Save className="mr-2 size-4" />
                Save Report
              </Button>
            </div>
          </div>
        </CardHeader>

        <CardContent>
          {/* Report Configuration */}
          <div className="mb-6 grid grid-cols-1 gap-4 md:grid-cols-3">
            <div>
              <label className="text-sm font-medium">Report Name</label>
              <input
                className="mt-1 w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                onChange={e =>
                  setReportConfig(prev => ({ ...prev, name: e.target.value }))
                }
                type="text"
                value={reportConfig.name}
              />
            </div>
            <div>
              <label className="text-sm font-medium">Data Source</label>
              <select
                className="mt-1 w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                onChange={e =>
                  setReportConfig(prev => ({
                    ...prev,
                    dataSource: e.target.value,
                  }))
                }
                value={reportConfig.dataSource}
              >
                <option value="delegations">Delegations</option>
                <option value="tasks">Tasks</option>
                <option value="vehicles">Vehicles</option>
                <option value="employees">Employees</option>
                <option value="cross-entity">Cross-Entity</option>
              </select>
            </div>
            <div>
              <label className="text-sm font-medium">Layout Columns</label>
              <select
                className="mt-1 w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                onChange={e =>
                  setReportConfig(prev => ({
                    ...prev,
                    layout: {
                      ...prev.layout,
                      columns: Number.parseInt(e.target.value),
                    },
                  }))
                }
                value={reportConfig.layout.columns}
              >
                <option value={1}>1 Column</option>
                <option value={2}>2 Columns</option>
                <option value={3}>3 Columns</option>
                <option value={4}>4 Columns</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Builder Interface */}
      <DndContext onDragEnd={handleDragEnd} onDragStart={handleDragStart}>
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-4">
          {/* Widget Palette */}
          <div className="lg:col-span-1">
            <WidgetPalette dataSource={reportConfig.dataSource} />
          </div>

          {/* Drop Zone */}
          <div className="lg:col-span-3">
            <DropZone
              columns={reportConfig.layout.columns}
              onWidgetConfigure={handleWidgetConfigure}
              onWidgetDelete={handleWidgetDelete}
              widgets={widgets}
            />
          </div>
        </div>

        {/* Drag Overlay */}
        <DragOverlay>
          {activeId ? (
            <div className="rounded-lg border bg-white p-4 shadow-lg">
              <div className="text-sm font-medium">
                {activeId
                  .replace('-', ' ')
                  .replaceAll(/\b\w/g, l => l.toUpperCase())}
              </div>
            </div>
          ) : null}
        </DragOverlay>
      </DndContext>

      {/* Widget Configurator */}
      {isConfiguring && selectedWidget && (
        <WidgetConfigurator
          onCancel={() => {
            setIsConfiguring(false);
            setSelectedWidget(null);
          }}
          onSave={handleWidgetUpdate}
          widget={selectedWidget}
        />
      )}
    </div>
  );
};

export default ReportBuilder;
