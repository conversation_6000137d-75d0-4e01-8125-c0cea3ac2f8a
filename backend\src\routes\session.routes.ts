import { Router, Request, Response } from 'express';
import { supabaseAdmin } from '../lib/supabase.js'; // Assuming supabaseAdmin is correctly exported
import logger from '../utils/logger.js'; // Assuming logger is correctly exported
import { enhancedAuthenticateUser } from '../middleware/jwtAuth.middleware.js';
import { requireRole } from '../middleware/supabaseAuth.js'; // As per plan, and verified to exist
import { logAuditEvent } from '../utils/auditLogger.js'; // Added import
import { authRateLimit } from '../middleware/rateLimiting.js'; // Import authentication rate limiting

const router = Router();

// Placeholder function for fetching active sessions - actual implementation is out of scope for this task
// but mentioned in the plan.
async function getActiveSessionsFromAuditLog(): Promise<any[]> {
  logger.info('Placeholder: getActiveSessionsFromAuditLog called. Returning mock data.');
  // In a real scenario, this would query a persistent store (e.g., audit logs or a sessions table)
  // For now, returning an empty array or mock data.
  return [
    {
      sessionId: 'mockSession123',
      userId: 'mockUserABC',
      lastActivity: new Date().toISOString(),
      ipAddress: '127.0.0.1',
    },
    {
      sessionId: 'mockSession456',
      userId: 'mockUserXYZ',
      lastActivity: new Date().toISOString(),
      ipAddress: '*************',
    },
  ];
}

// POST /api/session/logout - Invalidate current user's session(s)
router.post(
  '/logout',
  authRateLimit, // Apply authentication-specific rate limiting
  enhancedAuthenticateUser,
  async (req: Request, res: Response): Promise<void> => {
    const actionUserId = req.userId; // Available after enhancedAuthenticateUser
    logAuditEvent(
      {
        eventType: 'SESSION',
        action: 'LOGOUT_REQUEST',
        userId: actionUserId,
        outcome: 'PENDING',
        message: 'User initiated logout request.',
      },
      req,
    );

    try {
      if (!actionUserId) {
        logAuditEvent(
          {
            eventType: 'SESSION',
            action: 'LOGOUT_FAILURE',
            outcome: 'FAILURE',
            statusCode: 400,
            errorCode: 'USER_ID_MISSING_FOR_LOGOUT',
            message: 'Logout failed: User identifier not found in request after authentication.',
          },
          req,
        );
        // logger.warn already exists
        res.status(400).json({
          code: 'USER_ID_MISSING',
          error: 'User identifier not found in request',
          message: 'Unable to process logout due to missing user identifier.',
        });
        return;
      }

      const { error } = await supabaseAdmin.auth.admin.signOut(actionUserId);

      if (error) {
        logAuditEvent(
          {
            eventType: 'SESSION',
            action: 'LOGOUT_FAILURE',
            userId: actionUserId,
            outcome: 'FAILURE',
            statusCode: 500,
            errorCode: 'LOGOUT_PROVIDER_ERROR',
            message: 'Logout failed: Supabase session invalidation failed.',
            details: { providerError: error.message },
          },
          req,
        );
        // logger.error already exists
        res.status(500).json({
          code: 'LOGOUT_FAILED',
          error: 'Failed to invalidate session with authentication provider',
          message: 'Your logout request could not be completed. Please try again.',
        });
        return;
      }

      logAuditEvent(
        {
          eventType: 'SESSION',
          action: 'LOGOUT_SUCCESS',
          userId: actionUserId,
          outcome: 'SUCCESS',
          statusCode: 200,
          message: 'User logged out successfully.',
        },
        req,
      );
      // logger.info already exists
      res.status(200).json({
        status: 'success',
        message: 'Logged out successfully',
      });
    } catch (error: any) {
      logAuditEvent(
        {
          eventType: 'SESSION',
          action: 'LOGOUT_UNEXPECTED_ERROR',
          userId: actionUserId, // May be undefined if error is very early, but likely available
          outcome: 'FAILURE',
          statusCode: 500,
          errorCode: 'LOGOUT_UNEXPECTED_ERROR',
          message: 'Logout endpoint encountered an unexpected error.',
          details: { error: error.message, stack: error.stack?.substring(0, 200) },
        },
        req,
      );
      // logger.error already exists
      res.status(500).json({
        code: 'LOGOUT_UNEXPECTED_ERROR',
        error: 'Logout failed due to an unexpected server error',
        message: 'An unexpected error occurred during logout. Please try again.',
      });
    }
  },
);

// GET /api/session/active-sessions - Get all active sessions (Admin only)
router.get(
  '/active-sessions',
  enhancedAuthenticateUser,
  requireRole(['ADMIN', 'SUPER_ADMIN']),
  async (req: Request, res: Response): Promise<void> => {
    const adminUserId = req.userId;
    logAuditEvent(
      {
        eventType: 'SESSION',
        action: 'ACTIVE_SESSIONS_VIEW_REQUEST',
        userId: adminUserId, // Admin performing the action
        userRole: req.userRole,
        outcome: 'PENDING',
        message: 'Admin requested to view active sessions.',
      },
      req,
    );

    try {
      const activeSessions = await getActiveSessionsFromAuditLog(); // Placeholder

      logAuditEvent(
        {
          eventType: 'SESSION',
          action: 'ACTIVE_SESSIONS_VIEW_SUCCESS',
          userId: adminUserId,
          userRole: req.userRole,
          outcome: 'SUCCESS',
          statusCode: 200,
          message: 'Admin successfully retrieved active sessions list (mock data).',
        },
        req,
      );

      res.status(200).json({
        status: 'success',
        data: {
          sessions: activeSessions,
          total: activeSessions.length,
        },
      });
    } catch (error: any) {
      logAuditEvent(
        {
          eventType: 'SESSION',
          action: 'ACTIVE_SESSIONS_VIEW_FAILURE',
          userId: adminUserId,
          userRole: req.userRole,
          outcome: 'FAILURE',
          statusCode: 500,
          errorCode: 'SESSION_RETRIEVAL_FAILED',
          message: 'Failed to retrieve active sessions due to an unexpected server error.',
          details: { error: error.message, stack: error.stack?.substring(0, 200) },
        },
        req,
      );
      // logger.error already exists
      res.status(500).json({
        code: 'SESSION_RETRIEVAL_FAILED',
        error: 'Failed to retrieve active sessions',
        message: 'Could not retrieve active session list due to a server error.',
      });
    }
  },
);

// POST /api/session/invalidate/:userId - Invalidate a specific user's sessions (Admin only)
router.post(
  '/invalidate/:targetUserId',
  authRateLimit, // Apply authentication-specific rate limiting
  enhancedAuthenticateUser,
  requireRole(['ADMIN', 'SUPER_ADMIN']),
  async (req: Request, res: Response): Promise<void> => {
    const { targetUserId } = req.params;
    const adminUserId = req.userId;

    logAuditEvent(
      {
        eventType: 'SESSION',
        action: 'SESSION_INVALIDATE_REQUEST_ADMIN',
        userId: adminUserId, // Admin performing the action
        userRole: req.userRole,
        targetUserId: targetUserId,
        outcome: 'PENDING',
        message: `Admin initiated session invalidation for target user: ${targetUserId}.`,
        details: { reason: req.body.reason }, // Log reason if provided
      },
      req,
    );

    try {
      if (!targetUserId) {
        logAuditEvent(
          {
            eventType: 'SESSION',
            action: 'SESSION_INVALIDATE_FAILURE_ADMIN',
            userId: adminUserId,
            userRole: req.userRole,
            outcome: 'FAILURE',
            statusCode: 400,
            errorCode: 'TARGET_USER_ID_REQUIRED_FOR_INVALIDATION',
            message: 'Admin session invalidation failed: Target user ID is required.',
          },
          req,
        );
        res.status(400).json({
          code: 'TARGET_USER_ID_REQUIRED',
          error: 'Target user ID is required for session invalidation.',
        });
        return; // Terminate execution path
      }

      // Optional: Validate targetUser exists (as per plan)
      const { data: targetUserResult, error: getUserError } =
        await supabaseAdmin.auth.admin.getUserById(targetUserId);

      if (getUserError || !targetUserResult) {
        // Combined check for error or no user data
        const errorCode = getUserError ? 'TARGET_USER_FETCH_ERROR' : 'TARGET_USER_NOT_FOUND';
        const errorMessage = getUserError
          ? `Error fetching target user: ${getUserError.message}`
          : `Target user ${targetUserId} not found.`;
        logAuditEvent(
          {
            eventType: 'SESSION',
            action: 'SESSION_INVALIDATE_FAILURE_ADMIN',
            userId: adminUserId,
            userRole: req.userRole,
            targetUserId: targetUserId,
            outcome: 'FAILURE',
            statusCode: getUserError ? 500 : 404, // 500 for fetch error, 404 if not found
            errorCode: errorCode,
            message: `Admin session invalidation failed: ${errorMessage}`,
            details: { providerError: getUserError?.message },
          },
          req,
        );
        // logger.warn already exists
        res.status(getUserError ? 500 : 404).json({
          code: errorCode,
          error: 'Target user not found or error retrieving user details',
          message: `Could not find or verify user with ID: ${targetUserId}.`,
        });
        return;
      }
      // Redundant check for !targetUser is removed as it's covered by !targetUserResult above.

      // Invalidate all sessions for the target user
      const { error: signOutError } = await supabaseAdmin.auth.admin.signOut(targetUserId);

      if (signOutError) {
        logAuditEvent(
          {
            eventType: 'SESSION',
            action: 'SESSION_INVALIDATE_FAILURE_ADMIN',
            userId: adminUserId,
            userRole: req.userRole,
            targetUserId: targetUserId,
            outcome: 'FAILURE',
            statusCode: 500,
            errorCode: 'SESSION_INVALIDATION_PROVIDER_ERROR',
            message: `Admin session invalidation failed for target user ${targetUserId} at provider level.`,
            details: {
              providerError: signOutError.message,
              targetUserEmail: targetUserResult.user.email,
            },
          },
          req,
        );
        // logger.error already exists
        res.status(500).json({
          code: 'SESSION_INVALIDATION_FAILED',
          error: 'Failed to invalidate target user session(s)',
          message: 'Could not complete session invalidation for the specified user.',
        });
        return; // Terminate execution path
      }

      logAuditEvent(
        {
          eventType: 'SESSION',
          action: 'SESSION_INVALIDATE_SUCCESS_ADMIN',
          userId: adminUserId,
          userRole: req.userRole,
          targetUserId: targetUserId,
          outcome: 'SUCCESS',
          statusCode: 200,
          message: `Admin successfully invalidated all sessions for target user ${targetUserId}.`,
          details: { targetUserEmail: targetUserResult.user.email },
        },
        req,
      );
      // logger.info already exists
      res.status(200).json({
        status: 'success',
        message: `Successfully invalidated all sessions for user ${targetUserId}`,
      });
    } catch (error: any) {
      logAuditEvent(
        {
          eventType: 'SESSION',
          action: 'SESSION_INVALIDATE_UNEXPECTED_ERROR_ADMIN',
          userId: adminUserId,
          userRole: req.userRole,
          targetUserId: targetUserId,
          outcome: 'FAILURE',
          statusCode: 500,
          errorCode: 'INVALIDATE_SESSION_UNEXPECTED_ERROR',
          message: 'Admin session invalidation endpoint encountered an unexpected error.',
          details: { error: error.message, stack: error.stack?.substring(0, 200) },
        },
        req,
      );
      // logger.error already exists
      res.status(500).json({
        code: 'INVALIDATE_SESSION_UNEXPECTED_ERROR',
        error: 'Session invalidation failed due to an unexpected server error',
        message: 'An unexpected error occurred. Please try again.',
      });
    }
  },
);

export default router;
