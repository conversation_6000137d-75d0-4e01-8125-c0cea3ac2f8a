import { Router, Request, Response } from 'express';
import { enhancedAuthenticateUser } from '../middleware/jwtAuth.middleware.js';
import { requireRole } from '../middleware/supabaseAuth.js';
import { authRateLimit } from '../middleware/rateLimiting.js';
import { adminAuditMiddleware } from '../middleware/auditMiddleware.js';
import { securityMonitoringService } from '../services/securityMonitoring.service.js';
import logger from '../utils/logger.js';

const router = Router();

/**
 * GET /api/audit/metrics
 * Get security metrics for dashboard
 * Admin only
 */
router.get(
  '/metrics',
  authRateLimit,
  enhancedAuthenticateUser,
  requireRole(['ADMIN', 'SUPER_ADMIN']),
  adminAuditMiddleware,
  async (req: Request, res: Response) => {
    try {
      const timeframe = req.query.timeframe as '1h' | '24h' | '7d' | '30d' || '24h';
      
      const metrics = await securityMonitoringService.getSecurityMetrics(timeframe);
      
      await securityMonitoringService.logMonitoringAccess(
        req.userId!,
        'GET_SECURITY_METRICS',
        { timeframe }
      );

      res.json({
        status: 'success',
        data: {
          metrics,
          timeframe,
          timestamp: new Date().toISOString(),
        },
      });
    } catch (error: any) {
      logger.error('Failed to get security metrics:', error);
      res.status(500).json({
        status: 'error',
        code: 'METRICS_FETCH_ERROR',
        message: 'Failed to fetch security metrics',
        error: error.message,
      });
    }
  }
);

/**
 * GET /api/audit/logs
 * Get filtered audit logs
 * Admin only
 */
router.get(
  '/logs',
  authRateLimit,
  enhancedAuthenticateUser,
  requireRole(['ADMIN', 'SUPER_ADMIN']),
  adminAuditMiddleware,
  async (req: Request, res: Response) => {
    try {
      const filters = {
        startDate: req.query.startDate as string,
        endDate: req.query.endDate as string,
        eventType: req.query.eventType as string,
        action: req.query.action as string,
        userId: req.query.userId as string,
        outcome: req.query.outcome as string,
        riskLevel: req.query.riskLevel as string,
        limit: parseInt(req.query.limit as string) || 50,
        offset: parseInt(req.query.offset as string) || 0,
      };

      const logs = await securityMonitoringService.getAuditLogs(filters);
      
      await securityMonitoringService.logMonitoringAccess(
        req.userId!,
        'GET_AUDIT_LOGS',
        { filters: { ...filters, resultCount: logs.length } }
      );

      res.json({
        status: 'success',
        data: {
          logs,
          filters,
          count: logs.length,
          timestamp: new Date().toISOString(),
        },
      });
    } catch (error: any) {
      logger.error('Failed to get audit logs:', error);
      res.status(500).json({
        status: 'error',
        code: 'AUDIT_LOGS_FETCH_ERROR',
        message: 'Failed to fetch audit logs',
        error: error.message,
      });
    }
  }
);

/**
 * GET /api/audit/suspicious-activities
 * Get detected suspicious activities
 * Admin only
 */
router.get(
  '/suspicious-activities',
  authRateLimit,
  enhancedAuthenticateUser,
  requireRole(['ADMIN', 'SUPER_ADMIN']),
  adminAuditMiddleware,
  async (req: Request, res: Response) => {
    try {
      const activities = await securityMonitoringService.detectSuspiciousActivities();
      
      await securityMonitoringService.logMonitoringAccess(
        req.userId!,
        'GET_SUSPICIOUS_ACTIVITIES',
        { activitiesCount: activities.length }
      );

      res.json({
        status: 'success',
        data: {
          activities,
          count: activities.length,
          timestamp: new Date().toISOString(),
        },
      });
    } catch (error: any) {
      logger.error('Failed to get suspicious activities:', error);
      res.status(500).json({
        status: 'error',
        code: 'SUSPICIOUS_ACTIVITIES_FETCH_ERROR',
        message: 'Failed to fetch suspicious activities',
        error: error.message,
      });
    }
  }
);

/**
 * GET /api/audit/real-time
 * Get real-time security events (last 5 minutes)
 * Admin only
 */
router.get(
  '/real-time',
  authRateLimit,
  enhancedAuthenticateUser,
  requireRole(['ADMIN', 'SUPER_ADMIN']),
  adminAuditMiddleware,
  async (req: Request, res: Response) => {
    try {
      const events = await securityMonitoringService.getRealTimeEvents();
      
      await securityMonitoringService.logMonitoringAccess(
        req.userId!,
        'GET_REAL_TIME_EVENTS',
        { eventsCount: events.length }
      );

      res.json({
        status: 'success',
        data: {
          events,
          count: events.length,
          timestamp: new Date().toISOString(),
        },
      });
    } catch (error: any) {
      logger.error('Failed to get real-time events:', error);
      res.status(500).json({
        status: 'error',
        code: 'REAL_TIME_EVENTS_FETCH_ERROR',
        message: 'Failed to fetch real-time events',
        error: error.message,
      });
    }
  }
);

/**
 * GET /api/audit/user/:userId
 * Get audit logs for a specific user
 * Admin only
 */
router.get(
  '/user/:userId',
  authRateLimit,
  enhancedAuthenticateUser,
  requireRole(['ADMIN', 'SUPER_ADMIN']),
  adminAuditMiddleware,
  async (req: Request, res: Response) => {
    try {
      const { userId } = req.params;
      const limit = parseInt(req.query.limit as string) || 100;
      const offset = parseInt(req.query.offset as string) || 0;

      const logs = await securityMonitoringService.getAuditLogs({
        userId,
        limit,
        offset,
      });
      
      await securityMonitoringService.logMonitoringAccess(
        req.userId!,
        'GET_USER_AUDIT_LOGS',
        { targetUserId: userId, resultCount: logs.length }
      );

      res.json({
        status: 'success',
        data: {
          logs,
          userId,
          count: logs.length,
          timestamp: new Date().toISOString(),
        },
      });
    } catch (error: any) {
      logger.error('Failed to get user audit logs:', error);
      res.status(500).json({
        status: 'error',
        code: 'USER_AUDIT_LOGS_FETCH_ERROR',
        message: 'Failed to fetch user audit logs',
        error: error.message,
      });
    }
  }
);

/**
 * POST /api/audit/export
 * Export audit logs for compliance reporting
 * Super Admin only
 */
router.post(
  '/export',
  authRateLimit,
  enhancedAuthenticateUser,
  requireRole(['SUPER_ADMIN']),
  adminAuditMiddleware,
  async (req: Request, res: Response) => {
    try {
      const { startDate, endDate, format = 'json' } = req.body;

      if (!startDate || !endDate) {
        res.status(400).json({
          status: 'error',
          code: 'MISSING_DATE_RANGE',
          message: 'Start date and end date are required for export',
        });
        return;
      }

      const logs = await securityMonitoringService.getAuditLogs({
        startDate,
        endDate,
        limit: 10000, // Large limit for export
      });
      
      await securityMonitoringService.logMonitoringAccess(
        req.userId!,
        'EXPORT_AUDIT_LOGS',
        { 
          startDate, 
          endDate, 
          format, 
          exportedCount: logs.length 
        }
      );

      if (format === 'csv') {
        // Convert to CSV format
        const csvHeader = 'timestamp,eventType,action,userId,outcome,message,ipAddress\n';
        const csvData = logs.map(log => 
          `${log.created_at},${log.action},${log.action},${log.user_id},${log.details?.outcome || 'UNKNOWN'},${log.details},${log.ip_address || ''}`
        ).join('\n');
        
        res.setHeader('Content-Type', 'text/csv');
        res.setHeader('Content-Disposition', `attachment; filename="audit-logs-${startDate}-${endDate}.csv"`);
        res.send(csvHeader + csvData);
      } else {
        res.json({
          status: 'success',
          data: {
            logs,
            exportInfo: {
              startDate,
              endDate,
              format,
              count: logs.length,
              exportedAt: new Date().toISOString(),
              exportedBy: req.userId,
            },
          },
        });
      }
    } catch (error: any) {
      logger.error('Failed to export audit logs:', error);
      res.status(500).json({
        status: 'error',
        code: 'AUDIT_EXPORT_ERROR',
        message: 'Failed to export audit logs',
        error: error.message,
      });
    }
  }
);

export default router;
