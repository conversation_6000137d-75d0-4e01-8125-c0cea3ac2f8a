/**
 * @file Form Hooks Index - SOLID Principles Implementation
 * @description Centralized exports for all form-related hooks and services
 * @version 3.0.0
 * <AUTHOR> Development Team
 */

// Main form submission hook (refactored for SOLID principles)
export { useFormSubmission } from './useFormSubmission';

// Specialized validation hooks
export { useLoginValidation } from './useLoginValidation';

// Type definitions
export type {
  FormSubmissionOptions,
  FormSubmissionResult,
  SubmissionState,
  RetryConfig,
  AccessibilityConfig,
  PerformanceConfig,
  ToastConfig,
  PerformanceMetrics,
  AriaAttributes,
} from './types/FormSubmissionTypes';

// Services (following SRP)
export { FormSubmissionConfigService } from './services/FormSubmissionConfig';
export { FormSubmissionToastService } from './services/FormSubmissionToastService';
export { FormSubmissionAccessibilityService } from './services/FormSubmissionAccessibilityService';
export { FormSubmissionRetryService } from './services/FormSubmissionRetryService';
export { FormSubmissionPerformanceService } from './services/FormSubmissionPerformanceService';

// Configuration constants
export {
  DEFAULT_RETRY_CONFIG,
  DEFAULT_ACCESSIBILITY_CONFIG,
  DEFAULT_PERFORMANCE_CONFIG,
  DEFAULT_TOAST_CONFIG,
} from './services/FormSubmissionConfig';

// Other existing form hooks
export { useFormToast } from './useFormToast';
export { useFormValidation } from './useFormValidation';

// Re-export commonly used React Hook Form types for convenience
export type {
  FieldValues,
  FieldErrors,
  UseFormReturn,
  SubmitHandler,
} from 'react-hook-form';

// Re-export Zod types for convenience
export type { ZodSchema, ZodError } from 'zod';
