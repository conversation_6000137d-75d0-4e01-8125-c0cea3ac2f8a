'use client';

import { UserPlus2 } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useState } from 'react'; // Added useState for loading/error

import type { EmployeeFormData } from '@/lib/schemas/employeeSchemas';

import { EmployeeForm } from '@/components/features/employees/forms/employeeForm';
import { PageHeader } from '@/components/ui/PageHeader';
import { usePredefinedEntityToast } from '@/hooks/forms/useFormToast';
import { useCreateEmployee } from '@/lib/stores/queries/useEmployees';

export default function AddEmployeePage() {
  const router = useRouter();
  const [submissionError, setSubmissionError] = useState<null | string>(null);
  const { showEntityCreated, showEntityCreationError, showFormError } =
    usePredefinedEntityToast('employee');

  const createEmployeeMutation = useCreateEmployee();

  const handleSubmit = async (data: EmployeeFormData) => {
    setSubmissionError(null);
    try {
      await createEmployeeMutation.mutateAsync(data);
      const newEmployee = { name: data.fullName || data.name };
      showEntityCreated(newEmployee);
      router.push('/employees');
    } catch (error: any) {
      console.error('Error adding employee:', error);

      // Check for validation errors
      if (error.validationErrors && Array.isArray(error.validationErrors)) {
        // Format validation errors for display
        const validationMessages = error.validationErrors
          .map((err: any) => `${err.path}: ${err.message}`)
          .join('\n');

        console.log('Validation errors details:', validationMessages);

        setSubmissionError(`Validation failed: ${validationMessages}`);
        showFormError('Please check the form for errors', {
          errorTitle: 'Validation Error',
        });
      } else {
        // Handle other errors
        const errorMessage =
          error.message || 'Failed to add employee. Please try again.';
        setSubmissionError(errorMessage);
        showEntityCreationError(errorMessage);
      }
    }
  };

  return (
    <div className="space-y-6">
      <PageHeader
        description="Enter the details for the new employee."
        icon={UserPlus2}
        title="Add New Employee"
      />
      {submissionError && (
        <div className="rounded-md bg-destructive/20 p-3 text-sm text-destructive">
          {submissionError}
        </div>
      )}
      <EmployeeForm
        isEditing={false}
        isLoading={createEmployeeMutation.isPending} // Pass loading state to the form
        onSubmit={handleSubmit}
      />
    </div>
  );
}
