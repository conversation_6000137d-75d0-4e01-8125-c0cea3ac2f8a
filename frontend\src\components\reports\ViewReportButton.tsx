import { ExternalLink, FileText } from 'lucide-react';
import Link from 'next/link';
import React from 'react';

import { ActionButton } from '@/components/ui/action-button';

interface ViewReportButtonProps {
  /**
   * Additional CSS class names
   */
  className?: string;

  /**
   * Handler function that returns the URL to the report page (for list reports with dynamic parameters)
   */
  getReportUrl?: () => string;

  /**
   * Direct URL to the report page (for individual item reports)
   */
  href?: string;

  /**
   * Whether this button is for a list report (affects button text)
   */
  isList?: boolean;
}

/**
 * Standardized button for navigating to report pages.
 *
 * For individual item reports (e.g., Vehicle Report, Delegation Report):
 * ```tsx
 * <ViewReportButton href={`/vehicles/${vehicle.id}/report`} />
 * ```
 *
 * For list reports with dynamic parameters (e.g., Tasks Report, Delegations List Report):
 * ```tsx
 * <ViewReportButton
 *   isList={true}
 *   getReportUrl={() => {
 *     const params = new URLSearchParams({ searchTerm, status });
 *     return `/tasks/report?${params}`;
 *   }}
 * />
 * ```
 */
export function ViewReportButton({
  className,
  getReportUrl,
  href,
  isList = false,
}: ViewReportButtonProps) {
  if (!href && !getReportUrl) {
    console.error('ViewReportButton requires either href or getReportUrl prop');
    return null;
  }

  const buttonText = isList ? 'View List Report' : 'View Report';

  // For direct links (individual item reports)
  if (href) {
    return (
      <ActionButton
        actionType="secondary"
        asChild
        className={className}
        icon={<FileText className="size-4" />}
      >
        <Link href={href} rel="noopener noreferrer" target="_blank">
          {buttonText}
          <ExternalLink
            aria-hidden="true"
            className="ml-1.5 inline-block size-3"
          />
          <span className="sr-only">(opens in new tab)</span>
        </Link>
      </ActionButton>
    );
  }

  // For dynamic links (list reports)
  const handleClick = () => {
    if (getReportUrl) {
      const reportUrl = getReportUrl();
      window.open(reportUrl, '_blank', 'noopener,noreferrer');
    }
  };

  return (
    <ActionButton
      actionType="secondary"
      className={className}
      icon={<FileText className="size-4" />}
      onClick={handleClick}
    >
      {buttonText}
      <ExternalLink
        aria-hidden="true"
        className="ml-1.5 inline-block size-3"
      />
    </ActionButton>
  );
}
