import type {
  CreateEmployeeRequest,
  EmployeeApiResponse,
  UpdateEmployeeRequest,
} from '../../../types/apiContracts';
import type { Employee, DriverAvailabilityPrisma } from '../../../types/domain';
import type { ApiClient } from '../../core/apiClient';
import { EmployeeTransformer } from '../../../transformers/employeeTransformer';
import {
  BaseApiService,
  type DataTransformer,
  type ServiceConfig,
} from '../../core/baseApiService';

const EmployeeApiTransformer: DataTransformer<Employee> = {
  fromApi: (data: EmployeeApiResponse) => EmployeeTransformer.fromApi(data),
  toApi: (data: any) => data,
};

export class EmployeeApiService extends BaseApiService<
  Employee,
  CreateEmployeeRequest,
  UpdateEmployeeRequest
> {
  protected endpoint = '/employees';
  protected transformer: DataTransformer<Employee> = EmployeeApiTransformer;

  constructor(apiClient: ApiClient, config?: ServiceConfig) {
    super(apiClient, {
      cacheDuration: 5 * 60 * 1000, // 5 minutes for employees
      retryAttempts: 3,
      circuitBreakerThreshold: 5,
      enableMetrics: true,
      ...config,
    });
  }

  async getByRole(role: string): Promise<Employee[]> {
    const result = await this.getAll({ role });
    return result.data;
  }

  async updateAvailabilityStatus(
    employeeId: string,
    status: DriverAvailabilityPrisma
  ): Promise<Employee> {
    return this.executeWithInfrastructure(null, async () => {
      const response = await this.apiClient.patch<EmployeeApiResponse>(
        `${this.endpoint}/${employeeId}/availability`,
        { availability: status }
      );

      this.cache.invalidatePattern(new RegExp(`^${this.endpoint}:`));
      this.cache.invalidate(`${this.endpoint}:getById:${employeeId}`);

      return EmployeeTransformer.fromApi(response);
    });
  }
}
