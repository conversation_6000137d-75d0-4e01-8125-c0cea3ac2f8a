/**
 * @file EntityRelationshipNetworkWidget.tsx
 * @description Entity relationship network widget showing connections between system entities
 */

import React, { useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import {
  Network,
  Users,
  Car,
  CheckSquare,
  FileText,
  ArrowRight,
  MoreHorizontal,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useCrossEntityAnalytics } from '../../hooks/useCrossEntityAnalytics';
import { DataLoader } from '@/components/ui/data-loader';
import { ErrorDisplay } from '@/components/ui/error-display';
import type { ReportingFilters } from '../../data/types/reporting';

/**
 * Props interface for EntityRelationshipNetworkWidget
 */
interface EntityRelationshipNetworkWidgetProps {
  filters?: ReportingFilters;
  className?: string;
  maxConnections?: number;
  showDetails?: boolean;
}

/**
 * Entity node component
 */
interface EntityNodeProps {
  entity: {
    id: string;
    name: string;
    type: 'employee' | 'vehicle' | 'task' | 'delegation';
    connections: number;
    strength: number;
  };
  size?: 'sm' | 'md' | 'lg';
}

const EntityNode: React.FC<EntityNodeProps> = ({ entity, size = 'md' }) => {
  const getEntityIcon = (type: string) => {
    switch (type) {
      case 'employee':
        return <Users className="h-4 w-4" />;
      case 'vehicle':
        return <Car className="h-4 w-4" />;
      case 'task':
        return <CheckSquare className="h-4 w-4" />;
      case 'delegation':
        return <FileText className="h-4 w-4" />;
      default:
        return <Network className="h-4 w-4" />;
    }
  };

  const getEntityColor = (type: string) => {
    switch (type) {
      case 'employee':
        return 'bg-blue-100 text-blue-700 border-blue-200';
      case 'vehicle':
        return 'bg-green-100 text-green-700 border-green-200';
      case 'task':
        return 'bg-orange-100 text-orange-700 border-orange-200';
      case 'delegation':
        return 'bg-purple-100 text-purple-700 border-purple-200';
      default:
        return 'bg-gray-100 text-gray-700 border-gray-200';
    }
  };

  const sizeClasses = {
    sm: 'p-2 text-xs',
    md: 'p-3 text-sm',
    lg: 'p-4 text-base',
  };

  return (
    <div
      className={cn(
        'border rounded-lg flex items-center gap-2',
        getEntityColor(entity.type),
        sizeClasses[size]
      )}
    >
      {getEntityIcon(entity.type)}
      <div className="flex-1 min-w-0">
        <div className="font-medium truncate">{entity.name}</div>
        <div className="text-xs opacity-75">
          {entity.connections} connections
        </div>
      </div>
      <Badge variant="outline" className="text-xs">
        {Math.round(entity.strength * 100)}%
      </Badge>
    </div>
  );
};

/**
 * Connection component showing relationship between entities
 */
interface ConnectionProps {
  from: EntityNodeProps['entity'];
  to: EntityNodeProps['entity'];
  strength: number;
  type: string;
}

const Connection: React.FC<ConnectionProps> = ({
  from,
  to,
  strength,
  type,
}) => {
  const getConnectionColor = (strength: number) => {
    if (strength >= 0.8) return 'border-green-500 bg-green-50';
    if (strength >= 0.6) return 'border-blue-500 bg-blue-50';
    if (strength >= 0.4) return 'border-orange-500 bg-orange-50';
    return 'border-gray-500 bg-gray-50';
  };

  return (
    <div className={cn('p-3 border rounded-lg', getConnectionColor(strength))}>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2 flex-1">
          <EntityNode entity={from} size="sm" />
          <ArrowRight className="h-4 w-4 text-gray-400" />
          <EntityNode entity={to} size="sm" />
        </div>
        <div className="text-right">
          <Badge variant="outline" className="text-xs">
            {Math.round(strength * 100)}%
          </Badge>
          <div className="text-xs text-gray-500 mt-1">{type}</div>
        </div>
      </div>
    </div>
  );
};

/**
 * EntityRelationshipNetworkWidget Component
 *
 * Displays entity relationships and network connections in the system.
 *
 * Responsibilities:
 * - Show network of relationships between entities
 * - Visualize connection strengths and types
 * - Follow established widget composition patterns
 * - Maintain consistent styling and behavior
 *
 * SOLID Principles Applied:
 * - SRP: Single responsibility of displaying entity relationships
 * - OCP: Open for extension via props configuration
 * - DIP: Depends on existing widget framework abstractions
 */
export const EntityRelationshipNetworkWidget: React.FC<
  EntityRelationshipNetworkWidgetProps
> = ({ filters, className = '', maxConnections = 10, showDetails = true }) => {
  // Use hook for cross-entity analytics
  const {
    data: networkData,
    isLoading,
    error,
  } = useCrossEntityAnalytics(filters);

  // Transform data for network visualization
  const { entities, connections } = useMemo(() => {
    if (!networkData?.network) {
      return { entities: [], connections: [] };
    }

    const entities =
      networkData.network.nodes?.map(node => ({
        id: node.id,
        name: node.name,
        type: node.type as 'employee' | 'vehicle' | 'task' | 'delegation',
        connections: node.connectionCount || 0,
        strength: node.strength || 0,
      })) || [];

    const connections =
      networkData.network.edges?.slice(0, maxConnections).map(edge => ({
        from: entities.find(e => e.id === edge.from) || entities[0],
        to: entities.find(e => e.id === edge.to) || entities[0],
        strength: edge.weight || 0,
        type: edge.type || 'related',
      })) || [];

    return { entities, connections };
  }, [networkData, maxConnections]);

  // Handle loading state
  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Network className="h-5 w-5" />
            Entity Relationship Network
          </CardTitle>
        </CardHeader>
        <CardContent>
          <DataLoader isLoading={true} data={null} error={null}>
            {() => null}
          </DataLoader>
        </CardContent>
      </Card>
    );
  }

  // Handle error state
  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Network className="h-5 w-5" />
            Entity Relationship Network
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ErrorDisplay error={error} />
        </CardContent>
      </Card>
    );
  }

  // Calculate network statistics
  const totalEntities = entities.length;
  const totalConnections = connections.length;
  const avgConnectionStrength =
    connections.length > 0
      ? connections.reduce((sum, conn) => sum + conn.strength, 0) /
        connections.length
      : 0;

  const entityTypes = entities.reduce(
    (acc, entity) => {
      acc[entity.type] = (acc[entity.type] || 0) + 1;
      return acc;
    },
    {} as Record<string, number>
  );

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Network className="h-5 w-5" />
            Entity Relationship Network
          </CardTitle>
          <div className="flex items-center gap-2">
            <Badge variant="secondary" className="text-xs">
              {totalEntities} entities
            </Badge>
            <Badge variant="outline" className="text-xs">
              {totalConnections} connections
            </Badge>
            <Button variant="ghost" size="sm">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Network Statistics */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center p-3 bg-blue-50 rounded-lg">
            <div className="text-2xl font-bold text-blue-700">
              {entityTypes.employee || 0}
            </div>
            <div className="text-xs text-blue-600">Employees</div>
          </div>
          <div className="text-center p-3 bg-green-50 rounded-lg">
            <div className="text-2xl font-bold text-green-700">
              {entityTypes.vehicle || 0}
            </div>
            <div className="text-xs text-green-600">Vehicles</div>
          </div>
          <div className="text-center p-3 bg-orange-50 rounded-lg">
            <div className="text-2xl font-bold text-orange-700">
              {entityTypes.task || 0}
            </div>
            <div className="text-xs text-orange-600">Tasks</div>
          </div>
          <div className="text-center p-3 bg-purple-50 rounded-lg">
            <div className="text-2xl font-bold text-purple-700">
              {entityTypes.delegation || 0}
            </div>
            <div className="text-xs text-purple-600">Delegations</div>
          </div>
        </div>

        {/* Top Connected Entities */}
        <div>
          <h4 className="text-sm font-medium mb-3">Most Connected Entities</h4>
          <div className="space-y-2">
            {entities
              .sort((a, b) => b.connections - a.connections)
              .slice(0, 5)
              .map((entity, index) => (
                <EntityNode key={entity.id} entity={entity} />
              ))}
          </div>
        </div>

        {/* Strongest Connections */}
        {showDetails && connections.length > 0 && (
          <div>
            <h4 className="text-sm font-medium mb-3">Strongest Connections</h4>
            <div className="space-y-3">
              {connections
                .sort((a, b) => b.strength - a.strength)
                .slice(0, 5)
                .filter(connection => connection.from && connection.to)
                .map((connection, index) => (
                  <Connection
                    key={`${connection.from!.id}-${connection.to!.id}-${index}`}
                    from={connection.from!}
                    to={connection.to!}
                    strength={connection.strength}
                    type={connection.type}
                  />
                ))}
            </div>
          </div>
        )}

        {/* Network Health Indicator */}
        <div className="p-4 bg-gray-50 rounded-lg">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Network Health</span>
            <Badge
              variant={
                avgConnectionStrength > 0.7
                  ? 'default'
                  : avgConnectionStrength > 0.4
                    ? 'secondary'
                    : 'destructive'
              }
              className="text-xs"
            >
              {avgConnectionStrength > 0.7
                ? 'Strong'
                : avgConnectionStrength > 0.4
                  ? 'Moderate'
                  : 'Weak'}
            </Badge>
          </div>
          <div className="text-xs text-gray-600 mt-1">
            Average connection strength:{' '}
            {Math.round(avgConnectionStrength * 100)}%
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default EntityRelationshipNetworkWidget;
