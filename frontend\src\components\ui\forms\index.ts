/**
 * Form Components Index
 * 
 * Centralized exports for all form-related components, providing a clean
 * interface for importing form functionality throughout the application.
 * 
 * This consolidates:
 * - BaseForm (generic form foundation)
 * - FormField (reusable form field component)
 * - Form hooks and utilities
 * - Type definitions
 */

// Export main form components
export { BaseForm } from './baseForm';
export { FormField } from './formField';

// Export form hooks
export { useFormSubmission } from '@/hooks/forms/useFormSubmission';

// Re-export shadcn/ui form components for convenience
export {
  Form,
  FormControl,
  FormDescription,
  FormField as ShadcnFormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';

// Export types for external use
export type { BaseFormProps } from './baseForm';
export type { FormFieldProps } from './formField';

// Export common form utilities
export { zodResolver } from '@hookform/resolvers/zod';
export { useForm, useFormContext, Controller } from 'react-hook-form';
export type { 
  FieldValues, 
  DefaultValues, 
  SubmitHandler,
  UseFormReturn,
  ControllerRenderProps 
} from 'react-hook-form';
