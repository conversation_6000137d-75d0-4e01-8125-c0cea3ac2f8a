// health.service.test.ts - Unit tests for Health Monitoring Service

import { describe, test, expect, beforeEach, jest } from '@jest/globals';

// Create mock instances that can be modified in tests
const mockPrisma = {
  $queryRaw: jest.fn() as jest.MockedFunction<any>,
  $disconnect: jest.fn() as jest.MockedFunction<any>,
};

const mockSupabaseClient = {
  from: jest.fn().mockReturnValue({
    select: jest.fn().mockReturnValue({
      limit: jest.fn().mockResolvedValue({ error: null, data: [] } as any),
    }),
  }),
};

const mockCreateClient = jest.fn().mockReturnValue(mockSupabaseClient) as jest.MockedFunction<any>;

const mockCircuitBreakerRegistry = {
  getStatus: jest.fn().mockReturnValue([
    { name: 'database', state: 'CLOSED', stats: { failures: 0 } },
    { name: 'supabase', state: 'CLOSED', stats: { failures: 0 } },
  ]) as jest.MockedFunction<any>,
};

const mockRedis = jest.fn().mockImplementation(() => ({
  ping: jest.fn().mockResolvedValue('PONG' as any),
  disconnect: jest.fn(),
})) as jest.MockedFunction<any>;

const mockNodeCache = jest.fn().mockImplementation(() => ({
  getStats: jest.fn().mockReturnValue({
    keys: 10,
    hits: 100,
    misses: 20,
    ksize: 1024,
    vsize: 2048,
  }),
})) as jest.MockedFunction<any>;

const mockOs = {
  cpus: jest.fn().mockReturnValue([{}, {}, {}, {}]) as jest.MockedFunction<any>, // 4 CPUs
  totalmem: jest.fn().mockReturnValue(8 * 1024 * 1024 * 1024) as jest.MockedFunction<any>, // 8GB
  freemem: jest.fn().mockReturnValue(4 * 1024 * 1024 * 1024) as jest.MockedFunction<any>, // 4GB free
  loadavg: jest.fn().mockReturnValue([0.5, 0.7, 0.9]) as jest.MockedFunction<any>,
};

// Mock dependencies
jest.mock('../../../utils/logger', () => ({
  default: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
}));

jest.mock('../../../services/database.service', () => ({
  prisma: mockPrisma,
  testDatabaseConnections: jest.fn(),
  getDatabaseConfig: jest.fn().mockReturnValue({
    useSupabase: true,
    databaseUrl: 'postgresql://test@localhost:5432/test',
    supabaseUrl: 'https://test.supabase.co',
    supabaseKey: 'test-key',
  }),
  initializeSupabase: jest.fn(),
  default: mockPrisma,
}));

jest.mock('@supabase/supabase-js', () => ({
  createClient: mockCreateClient,
}));

jest.mock('../../../services/circuitBreaker.service', () => ({
  circuitBreakerRegistry: mockCircuitBreakerRegistry,
}));

jest.mock('ioredis', () => ({
  default: mockRedis,
}));

jest.mock('node-cache', () => ({
  default: mockNodeCache,
}));

jest.mock('os', () => mockOs);

// Import after mocking
import {
  checkEnhancedDatabaseHealth,
  checkEnhancedSupabaseHealth,
  checkCircuitBreakersHealth,
  checkCacheHealth,
  checkSystemResourcesHealth,
  checkBusinessLogicHealth,
  getComprehensiveHealthReport,
} from '../../../services/health.service';

describe('Health Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Reset environment variables
    process.env.SUPABASE_URL = 'https://test.supabase.co';
    process.env.SUPABASE_ANON_KEY = 'test-anon-key';

    // Reset mock implementations to defaults
    mockPrisma.$queryRaw.mockResolvedValue([{ result: 1 }]);
    mockCreateClient.mockReturnValue(mockSupabaseClient);
    mockCircuitBreakerRegistry.getStatus.mockReturnValue([
      { name: 'database', state: 'CLOSED', stats: { failures: 0 } },
      { name: 'supabase', state: 'CLOSED', stats: { failures: 0 } },
    ]);
    mockRedis.mockImplementation(() => ({
      ping: jest.fn().mockResolvedValue('PONG' as any),
      disconnect: jest.fn(),
    }));
    mockNodeCache.mockImplementation(() => ({
      getStats: jest.fn().mockReturnValue({
        keys: 10,
        hits: 100,
        misses: 20,
        ksize: 1024,
        vsize: 2048,
      }),
    }));
    mockOs.freemem.mockReturnValue(4 * 1024 * 1024 * 1024); // 4GB free
    mockOs.loadavg.mockReturnValue([0.5, 0.7, 0.9]);
  });

  describe('Database Health Check', () => {
    test('should return healthy status for successful database connection', async () => {
      mockPrisma.$queryRaw.mockResolvedValue([{ result: 1 }]);

      const health = await checkEnhancedDatabaseHealth();

      expect(health.status).toBe('healthy');
      expect(health.responseTime).toBeGreaterThan(0);
      expect(health.details.connectionPool).toBeDefined();
      expect(mockPrisma.$queryRaw).toHaveBeenCalledWith(expect.any(Object));
    });

    test('should return degraded status for slow database response', async () => {
      // Mock slow response
      mockPrisma.$queryRaw.mockImplementation(
        () => new Promise(resolve => setTimeout(() => resolve([{ result: 1 }]), 6000)),
      );

      const health = await checkEnhancedDatabaseHealth();

      expect(health.status).toBe('degraded');
      expect(health.message).toContain('slow');
    });

    test('should return unhealthy status for database connection failure', async () => {
      mockPrisma.$queryRaw.mockRejectedValue(new Error('Connection failed'));

      const health = await checkEnhancedDatabaseHealth();

      expect(health.status).toBe('unhealthy');
      expect(health.message).toContain('Connection failed');
    });

    test('should handle database timeout', async () => {
      // Mock timeout scenario
      mockPrisma.$queryRaw.mockImplementation(
        () => new Promise(() => {}), // Never resolves
      );

      const health = await checkEnhancedDatabaseHealth();

      expect(health.status).toBe('unhealthy');
      expect(health.message).toContain('timeout');
    });
  });

  describe('Supabase Health Check', () => {
    test('should return healthy status for successful Supabase connection', async () => {
      const health = await checkEnhancedSupabaseHealth();

      expect(health.status).toBe('healthy');
      expect(health.responseTime).toBeGreaterThan(0);
      expect(health.details.endpoint).toBe('https://test.supabase.co');
    });

    test('should return degraded status for missing configuration', async () => {
      delete process.env.SUPABASE_URL;

      const health = await checkEnhancedSupabaseHealth();

      expect(health.status).toBe('degraded');
      expect(health.message).toContain('configuration missing');
    });

    test('should return unhealthy status for Supabase connection failure', async () => {
      mockCreateClient.mockReturnValue({
        from: jest.fn().mockReturnValue({
          select: jest.fn().mockReturnValue({
            limit: jest.fn().mockRejectedValue(new Error('Supabase error') as any),
          }),
        }),
      });

      const health = await checkEnhancedSupabaseHealth();

      expect(health.status).toBe('unhealthy');
      expect(health.message).toContain('Supabase error');
    });

    test('should handle Supabase timeout', async () => {
      mockCreateClient.mockReturnValue({
        from: jest.fn().mockReturnValue({
          select: jest.fn().mockReturnValue({
            limit: jest.fn().mockImplementation(
              () => new Promise(() => {}), // Never resolves
            ),
          }),
        }),
      });

      const health = await checkEnhancedSupabaseHealth();

      expect(health.status).toBe('unhealthy');
      expect(health.message).toContain('timeout');
    });
  });

  describe('Circuit Breakers Health Check', () => {
    test('should return healthy status when all circuit breakers are closed', async () => {
      const health = await checkCircuitBreakersHealth();

      expect(health.status).toBe('healthy');
      expect(health.details.totalBreakers).toBe(2);
      expect(health.details.openBreakers).toBe(0);
    });

    test('should return degraded status when some circuit breakers are open', async () => {
      mockCircuitBreakerRegistry.getStatus.mockReturnValue([
        { name: 'database', state: 'CLOSED', stats: { failures: 0 } },
        { name: 'supabase', state: 'OPEN', stats: { failures: 5 } },
      ]);

      const health = await checkCircuitBreakersHealth();

      expect(health.status).toBe('degraded');
      expect(health.details.openBreakers).toBe(1);
      expect(health.details.openBreakerNames).toContain('supabase');
    });

    test('should return unhealthy status when many circuit breakers are open', async () => {
      mockCircuitBreakerRegistry.getStatus.mockReturnValue([
        { name: 'database', state: 'OPEN', stats: { failures: 10 } },
        { name: 'supabase', state: 'OPEN', stats: { failures: 8 } },
        { name: 'redis', state: 'OPEN', stats: { failures: 6 } },
      ]);

      const health = await checkCircuitBreakersHealth();

      expect(health.status).toBe('unhealthy');
      expect(health.details.openBreakers).toBe(3);
    });
  });

  describe('Cache Health Check', () => {
    test('should return healthy status for working cache', async () => {
      const health = await checkCacheHealth();

      expect(health.status).toBe('healthy');
      expect(health.details.redis.status).toBe('connected');
      expect(health.details.inMemory.status).toBe('active');
    });

    test('should return degraded status for Redis connection issues', async () => {
      mockRedis.mockImplementation(() => ({
        ping: jest.fn().mockRejectedValue(new Error('Redis connection failed') as any),
        disconnect: jest.fn(),
      }));

      const health = await checkCacheHealth();

      expect(health.status).toBe('degraded');
      expect(health.details.redis.status).toBe('disconnected');
    });
  });

  describe('System Resources Health Check', () => {
    test('should return healthy status for normal resource usage', async () => {
      const health = await checkSystemResourcesHealth();

      expect(health.status).toBe('healthy');
      expect(health.details.memory.usagePercent).toBeLessThan(80);
      expect(health.details.cpu.loadAverage1m).toBeLessThan(1);
    });

    test('should return degraded status for high resource usage', async () => {
      mockOs.freemem.mockReturnValue(0.5 * 1024 * 1024 * 1024); // Only 0.5GB free (93.75% used)
      mockOs.loadavg.mockReturnValue([3.5, 3.0, 2.8]); // High load

      const health = await checkSystemResourcesHealth();

      expect(health.status).toBe('degraded');
      expect(health.details.memory.usagePercent).toBeGreaterThan(90);
    });

    test('should return unhealthy status for critical resource usage', async () => {
      mockOs.freemem.mockReturnValue(0.1 * 1024 * 1024 * 1024); // Only 0.1GB free (98.75% used)
      mockOs.loadavg.mockReturnValue([8.0, 7.5, 7.0]); // Very high load

      const health = await checkSystemResourcesHealth();

      expect(health.status).toBe('unhealthy');
      expect(health.details.memory.usagePercent).toBeGreaterThan(95);
    });
  });

  describe('Business Logic Health Check', () => {
    test('should return healthy status for working business logic', async () => {
      const health = await checkBusinessLogicHealth();

      expect(health.status).toBe('healthy');
      expect(health.details.criticalServices).toBeDefined();
    });

    test('should handle business logic validation errors', async () => {
      // Mock a scenario where business logic validation fails
      mockPrisma.$queryRaw.mockRejectedValue(new Error('Business logic validation failed') as any);

      const health = await checkBusinessLogicHealth();

      expect(health.status).toBe('degraded');
    });
  });

  describe('Comprehensive Health Report', () => {
    test('should generate comprehensive health report', async () => {
      const report = await getComprehensiveHealthReport();

      expect(report.status).toBeDefined();
      expect(report.timestamp).toBeDefined();
      expect(report.uptime).toBeDefined();
      expect(report.checks).toBeDefined();
      expect(report.checks.database).toBeDefined();
      expect(report.checks.supabase).toBeDefined();
      expect(report.checks.circuitBreakers).toBeDefined();
      expect(report.checks.cache).toBeDefined();
      expect(report.checks.systemResources).toBeDefined();
      expect(report.checks.businessLogic).toBeDefined();
      expect(report.summary).toBeDefined();
    });

    test('should determine overall status correctly', async () => {
      // Mock all services as healthy
      const report = await getComprehensiveHealthReport();

      expect(report.status).toBe('healthy');
      expect(report.summary.healthyChecks).toBeGreaterThan(0);
    });

    test('should handle mixed health statuses', async () => {
      // Mock some services as degraded
      mockPrisma.$queryRaw.mockImplementation(
        () => new Promise(resolve => setTimeout(() => resolve([{ result: 1 }]), 6000)) as any,
      );

      const report = await getComprehensiveHealthReport();

      expect(report.status).toBe('degraded');
      expect(report.summary.degradedChecks).toBeGreaterThan(0);
    });

    test('should include version and environment information', async () => {
      process.env.npm_package_version = '1.2.3';
      process.env.NODE_ENV = 'test';

      const report = await getComprehensiveHealthReport();

      expect(report.version).toBe('1.2.3');
      expect(report.environment).toBe('test');
    });
  });

  describe('Health Check Timeouts', () => {
    test('should respect health check timeout configuration', async () => {
      // This test verifies that health checks don't hang indefinitely
      const startTime = Date.now();

      // Mock a hanging operation
      mockPrisma.$queryRaw.mockImplementation(
        () => new Promise(() => {}) as any, // Never resolves
      );

      await checkEnhancedDatabaseHealth();

      const duration = Date.now() - startTime;
      expect(duration).toBeLessThan(10000); // Should timeout within 10 seconds
    });
  });

  describe('Error Recovery', () => {
    test('should handle partial health check failures gracefully', async () => {
      // Mock one health check to fail
      mockPrisma.$queryRaw.mockRejectedValue(new Error('Database error') as any);

      const report = await getComprehensiveHealthReport();

      // Should still generate a report even with some failures
      expect(report).toBeDefined();
      expect(report.checks.database.status).toBe('unhealthy');
      // Other checks should still work
      expect(report.checks.supabase).toBeDefined();
    });

    test('should continue health checks even if one fails', async () => {
      // Mock database to fail but others to succeed
      mockPrisma.$queryRaw.mockRejectedValue(new Error('Database error') as any);

      const report = await getComprehensiveHealthReport();

      expect(report.checks.database.status).toBe('unhealthy');
      expect(report.checks.supabase.status).toBe('healthy');
      expect(report.checks.circuitBreakers.status).toBe('healthy');
    });
  });
});
