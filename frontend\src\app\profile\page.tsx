'use client';

import { Loader2 } from 'lucide-react';
import React from 'react';

import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { UserProfile } from '@/components/user/UserProfile';
import { useAuthContext } from '@/contexts/AuthContext';

export default function ProfilePage() {
  const { loading } = useAuthContext();

  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-50">
        <Card className="mx-auto w-full max-w-md">
          <CardContent className="flex flex-col items-center justify-center py-8">
            <Loader2 className="mb-4 size-8 animate-spin text-blue-600" />
            <p className="text-sm text-muted-foreground">
              Loading user profile...
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-background">
        <div className="container mx-auto py-6 px-4">
          <UserProfile variant="detailed" />
        </div>
      </div>
    </ProtectedRoute>
  );
}
