/**
 * @file Debug Toggle Component
 * @description Provides a way to toggle debug components on/off with keyboard shortcuts
 * @module components/debug/DebugToggle
 */

'use client';

import React, { useState, useEffect } from 'react';
import { debugConfig } from '@/lib/config/debugConfig';

interface DebugToggleProps {
  children: React.ReactNode;
}

/**
 * Debug Toggle Component
 * 
 * Allows toggling debug components with Ctrl+Shift+D
 * Only works in development mode
 */
export function DebugToggle({ children }: DebugToggleProps): JSX.Element {
  const [showDebug, setShowDebug] = useState(false);

  useEffect(() => {
    // Only enable in development
    if (process.env.NODE_ENV !== 'development') {
      return;
    }

    const handleKeyDown = (event: KeyboardEvent) => {
      // Toggle with Ctrl+Shift+D
      if (event.ctrlKey && event.shiftKey && event.key === 'D') {
        event.preventDefault();
        setShowDebug(prev => {
          const newState = !prev;
          console.log(`🔧 Debug components ${newState ? 'enabled' : 'disabled'}`);
          return newState;
        });
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  // Don't render debug components in production
  if (process.env.NODE_ENV === 'production') {
    return <></>;
  }

  // Don't render if debug is disabled
  if (!showDebug) {
    return <></>;
  }

  return (
    <>
      {children}
      {/* Debug indicator */}
      <div className="fixed bottom-2 left-2 z-50 bg-yellow-500 text-black px-2 py-1 rounded text-xs font-mono">
        DEBUG MODE (Ctrl+Shift+D to toggle)
      </div>
    </>
  );
}

/**
 * Hook to check if debug mode is enabled
 */
export function useDebugMode() {
  const [isDebugMode, setIsDebugMode] = useState(false);

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.ctrlKey && event.shiftKey && event.key === 'D') {
        setIsDebugMode(prev => !prev);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  return isDebugMode && process.env.NODE_ENV === 'development';
}
