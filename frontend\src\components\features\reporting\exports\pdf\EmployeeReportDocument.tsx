// frontend/src/components/features/reporting/exports/pdf/EmployeeReportDocument.tsx
import React from 'react';
import { Document, Page, Text, View, StyleSheet } from '@react-pdf/renderer';

const styles = StyleSheet.create({
  page: {
    flexDirection: 'column',
    backgroundColor: '#FFFFFF',
    padding: 30,
    fontSize: 12,
  },
  header: {
    fontSize: 20,
    marginBottom: 20,
    textAlign: 'center',
    color: '#7c3aed',
    fontWeight: 'bold',
  },
  subheader: {
    fontSize: 16,
    marginBottom: 15,
    color: '#374151',
    fontWeight: 'bold',
    borderBottom: '1px solid #e5e7eb',
    paddingBottom: 5,
  },
  section: {
    marginBottom: 20,
  },
  row: {
    flexDirection: 'row',
    marginBottom: 8,
    paddingVertical: 4,
  },
  label: {
    width: '40%',
    fontWeight: 'bold',
    color: '#4b5563',
  },
  value: {
    width: '60%',
    color: '#111827',
  },
  table: {
    marginTop: 10,
  },
  tableHeader: {
    flexDirection: 'row',
    backgroundColor: '#faf5ff',
    padding: 8,
    fontWeight: 'bold',
  },
  tableRow: {
    flexDirection: 'row',
    padding: 8,
    borderBottom: '1px solid #e5e7eb',
  },
  tableCell: {
    flex: 1,
    fontSize: 10,
  },
  metadata: {
    marginTop: 30,
    padding: 15,
    backgroundColor: '#faf5ff',
    borderRadius: 5,
  },
  metadataText: {
    fontSize: 10,
    color: '#6b7280',
    marginBottom: 3,
  },
});

interface EmployeeReportDocumentProps {
  data: any;
  reportTitle: string;
  metadata?: any;
}

export const EmployeeReportDocument: React.FC<EmployeeReportDocumentProps> = ({
  data,
  reportTitle,
  metadata,
}) => {
  // Defensive programming: ensure all data is properly defined
  const employeeData = React.useMemo(() => {
    if (!data) return {};
    const rawData = data?.data || data;
    if (!rawData || typeof rawData !== 'object') return {};

    return {
      totalCount: rawData.totalCount || 0,
      activeCount: rawData.activeCount || 0,
      onLeaveCount: rawData.onLeaveCount || 0,
      averagePerformanceScore: rawData.averagePerformanceScore || 0,
      satisfactionRate: rawData.satisfactionRate || 0,
      performanceMetrics: rawData.performanceMetrics || {},
      departmentDistribution: Array.isArray(rawData.departmentDistribution) ? rawData.departmentDistribution : [],
      taskAssignments: rawData.taskAssignments || {},
      workloadDistribution: Array.isArray(rawData.workloadDistribution) ? rawData.workloadDistribution : [],
      availabilityMetrics: rawData.availabilityMetrics || {},
      ...rawData,
    };
  }, [data]);
  
  const safeReportTitle = reportTitle || 'Employee Report';
  const safeMetadata = metadata || {};

  return (
    <Document>
      <Page size="A4" style={styles.page}>
        <Text style={styles.header}>{safeReportTitle}</Text>

        {/* Employee Summary */}
        <View style={styles.section}>
          <Text style={styles.subheader}>Employee Summary</Text>
          <View style={styles.row}>
            <Text style={styles.label}>Total Employees:</Text>
            <Text style={styles.value}>{employeeData.totalCount || 0}</Text>
          </View>
          <View style={styles.row}>
            <Text style={styles.label}>Active Employees:</Text>
            <Text style={styles.value}>{employeeData.activeCount || 0}</Text>
          </View>
          <View style={styles.row}>
            <Text style={styles.label}>On Leave:</Text>
            <Text style={styles.value}>{employeeData.onLeaveCount || 0}</Text>
          </View>
          <View style={styles.row}>
            <Text style={styles.label}>Average Performance Score:</Text>
            <Text style={styles.value}>
              {employeeData.averagePerformanceScore?.toFixed(2) || 0}
            </Text>
          </View>
          <View style={styles.row}>
            <Text style={styles.label}>Employee Satisfaction Rate:</Text>
            <Text style={styles.value}>
              {employeeData.satisfactionRate?.toFixed(2) || 0}%
            </Text>
          </View>
        </View>

        {/* Performance Metrics */}
        {employeeData.performanceMetrics && (
          <View style={styles.section}>
            <Text style={styles.subheader}>Performance Metrics</Text>
            <View style={styles.row}>
              <Text style={styles.label}>High Performers:</Text>
              <Text style={styles.value}>
                {employeeData.performanceMetrics.highPerformers || 0}
              </Text>
            </View>
            <View style={styles.row}>
              <Text style={styles.label}>Average Performers:</Text>
              <Text style={styles.value}>
                {employeeData.performanceMetrics.averagePerformers || 0}
              </Text>
            </View>
            <View style={styles.row}>
              <Text style={styles.label}>Low Performers:</Text>
              <Text style={styles.value}>
                {employeeData.performanceMetrics.lowPerformers || 0}
              </Text>
            </View>
            <View style={styles.row}>
              <Text style={styles.label}>Performance Improvement Rate:</Text>
              <Text style={styles.value}>
                {employeeData.performanceMetrics.improvementRate?.toFixed(2) ||
                  0}
                %
              </Text>
            </View>
          </View>
        )}

        {/* Department Distribution */}
        {employeeData.departmentDistribution && (
          <View style={styles.section}>
            <Text style={styles.subheader}>Department Distribution</Text>
            <View style={styles.table}>
              <View style={styles.tableHeader}>
                <Text style={styles.tableCell}>Department</Text>
                <Text style={styles.tableCell}>Count</Text>
                <Text style={styles.tableCell}>Percentage</Text>
              </View>
              {employeeData.departmentDistribution.map(
                (item: any, index: number) => (
                  <View key={`dept-${index}`} style={styles.tableRow}>
                    <Text style={styles.tableCell}>
                      {item?.department || item?._id || 'Unknown'}
                    </Text>
                    <Text style={styles.tableCell}>
                      {item?.count || item?._count?.department || 0}
                    </Text>
                    <Text style={styles.tableCell}>
                      {item?.percentage
                        ? `${Number(item.percentage).toFixed(1)}%`
                        : 'N/A'}
                    </Text>
                  </View>
                )
              )}
            </View>
          </View>
        )}

        {/* Task Assignments */}
        {employeeData.taskAssignments && (
          <View style={styles.section}>
            <Text style={styles.subheader}>Task Assignment Metrics</Text>
            <View style={styles.row}>
              <Text style={styles.label}>Total Tasks Assigned:</Text>
              <Text style={styles.value}>
                {employeeData.taskAssignments.totalAssigned || 0}
              </Text>
            </View>
            <View style={styles.row}>
              <Text style={styles.label}>Completed Tasks:</Text>
              <Text style={styles.value}>
                {employeeData.taskAssignments.completed || 0}
              </Text>
            </View>
            <View style={styles.row}>
              <Text style={styles.label}>Pending Tasks:</Text>
              <Text style={styles.value}>
                {employeeData.taskAssignments.pending || 0}
              </Text>
            </View>
            <View style={styles.row}>
              <Text style={styles.label}>Task Completion Rate:</Text>
              <Text style={styles.value}>
                {employeeData.taskAssignments.completionRate?.toFixed(2) || 0}%
              </Text>
            </View>
            <View style={styles.row}>
              <Text style={styles.label}>Average Tasks per Employee:</Text>
              <Text style={styles.value}>
                {employeeData.taskAssignments.averagePerEmployee?.toFixed(1) ||
                  0}
              </Text>
            </View>
          </View>
        )}

        {/* Workload Distribution */}
        {employeeData.workloadDistribution && (
          <View style={styles.section}>
            <Text style={styles.subheader}>Workload Distribution</Text>
            <View style={styles.table}>
              <View style={styles.tableHeader}>
                <Text style={styles.tableCell}>Workload Level</Text>
                <Text style={styles.tableCell}>Employee Count</Text>
                <Text style={styles.tableCell}>Percentage</Text>
              </View>
              {employeeData.workloadDistribution.map(
                (item: any, index: number) => (
                  <View key={`workload-${index}`} style={styles.tableRow}>
                    <Text style={styles.tableCell}>
                      {item?.level || item?._id || 'Unknown'}
                    </Text>
                    <Text style={styles.tableCell}>
                      {item?.count || item?._count?.level || 0}
                    </Text>
                    <Text style={styles.tableCell}>
                      {item?.percentage
                        ? `${Number(item.percentage).toFixed(1)}%`
                        : 'N/A'}
                    </Text>
                  </View>
                )
              )}
            </View>
          </View>
        )}

        {/* Availability Metrics */}
        {employeeData.availabilityMetrics && (
          <View style={styles.section}>
            <Text style={styles.subheader}>Availability Metrics</Text>
            <View style={styles.row}>
              <Text style={styles.label}>Available Employees:</Text>
              <Text style={styles.value}>
                {employeeData.availabilityMetrics.available || 0}
              </Text>
            </View>
            <View style={styles.row}>
              <Text style={styles.label}>On Assignment:</Text>
              <Text style={styles.value}>
                {employeeData.availabilityMetrics.onAssignment || 0}
              </Text>
            </View>
            <View style={styles.row}>
              <Text style={styles.label}>On Leave:</Text>
              <Text style={styles.value}>
                {employeeData.availabilityMetrics.onLeave || 0}
              </Text>
            </View>
            <View style={styles.row}>
              <Text style={styles.label}>Availability Rate:</Text>
              <Text style={styles.value}>
                {employeeData.availabilityMetrics.availabilityRate?.toFixed(
                  2
                ) || 0}
                %
              </Text>
            </View>
          </View>
        )}

        {/* Report Metadata */}
        {metadata && (
          <View style={styles.metadata}>
            <Text style={styles.subheader}>Report Information</Text>
            <Text style={styles.metadataText}>Report ID: {metadata.id}</Text>
            <Text style={styles.metadataText}>Type: {metadata.type}</Text>
            <Text style={styles.metadataText}>
              Entity Type: {metadata.entityType}
            </Text>
            <Text style={styles.metadataText}>
              Generated: {new Date(metadata.generatedAt).toLocaleString()}
            </Text>
            <Text style={styles.metadataText}>
              Generated By: {metadata.generatedBy}
            </Text>
          </View>
        )}
      </Page>
    </Document>
  );
};
