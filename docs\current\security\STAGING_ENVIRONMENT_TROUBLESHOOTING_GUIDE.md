# WorkHub Staging Environment Troubleshooting Guide

**Version:** 1.0  
**Created:** June 3, 2025  
**Last Updated:** June 3, 2025  
**Status:** Production-Ready Staging Environment

## 🎯 **Quick Reference**

### **Essential Commands**
```bash
# Start staging environment
docker compose -f docker-compose.staging.yml --env-file .env.staging up -d

# Check service status
docker compose -f docker-compose.staging.yml --env-file .env.staging ps

# View logs (all services)
docker compose -f docker-compose.staging.yml --env-file .env.staging logs -f

# View specific service logs
docker compose -f docker-compose.staging.yml --env-file .env.staging logs -f backend
docker compose -f docker-compose.staging.yml --env-file .env.staging logs -f frontend
docker compose -f docker-compose.staging.yml --env-file .env.staging logs -f nginx

# Stop staging environment
docker compose -f docker-compose.staging.yml --env-file .env.staging down

# Restart specific service
docker compose -f docker-compose.staging.yml --env-file .env.staging restart backend
```

### **Service Endpoints**
- **Primary Access (Nginx Proxy):** http://localhost/
- **API Access:** http://localhost/api/
- **Health Check:** http://localhost/health
- **Backend Direct:** http://localhost:3001
- **Frontend Direct:** http://localhost:3000

---

## 🚨 **Common Issues & Solutions**

### **Issue 1: Environment Variables Not Loading**

**Symptoms:**
- Warning: "The \"SUPABASE_URL\" variable is not set"
- Containers fail to start
- Authentication errors

**Solution:**
```bash
# Ensure you're using the --env-file flag
docker compose -f docker-compose.staging.yml --env-file .env.staging up -d

# Verify .env.staging file exists and has correct permissions
ls -la .env.staging
cat .env.staging | grep SUPABASE_URL
```

**Root Cause:** Docker Compose doesn't automatically load `.env.staging` files.

---

### **Issue 2: Nginx Configuration Errors**

**Symptoms:**
- Nginx container fails to start
- Error: "nginx: [emerg] cannot load configuration"
- Port 80/443 not accessible

**Solution:**
```bash
# Check if nginx/staging.conf is a file (not directory)
ls -la nginx/staging.conf

# If it's a directory, remove and recreate as file
rm -rf nginx/staging.conf/
# Recreate the configuration file (see main documentation)

# Test nginx configuration (if nginx is installed locally)
nginx -t -c $(pwd)/nginx/staging.conf
```

---

### **Issue 3: Frontend Build Failures**

**Symptoms:**
- Frontend container fails to build
- Error: "Missing Supabase environment variables"
- Build process exits with code 1

**Solution:**
```bash
# Ensure environment variables are properly set
docker compose -f docker-compose.staging.yml --env-file .env.staging config | grep SUPABASE

# Rebuild frontend with no cache
docker compose -f docker-compose.staging.yml --env-file .env.staging build --no-cache frontend

# Check frontend logs for specific errors
docker compose -f docker-compose.staging.yml --env-file .env.staging logs frontend
```

---

### **Issue 4: Database Connection Problems**

**Symptoms:**
- Backend health check fails
- Database connection errors in logs
- API endpoints return 500 errors

**Solution:**
```bash
# Verify Supabase credentials in .env.staging
grep -E "(SUPABASE_URL|DATABASE_URL)" .env.staging

# Test database connection manually
curl -X GET "https://abylqjnpaegeqwktcukn.supabase.co/rest/v1/" \
  -H "apikey: YOUR_ANON_KEY"

# Check backend logs for specific database errors
docker compose -f docker-compose.staging.yml --env-file .env.staging logs backend | grep -i error
```

---

### **Issue 5: Authentication Not Working**

**Symptoms:**
- All API requests return 200 instead of 401
- Authentication middleware not active
- Security tests failing

**Solution:**
```bash
# Test authentication protection
curl -s -w "%{http_code}" -o /dev/null http://localhost:3001/api/employees
# Should return 401

# Check if USE_SUPABASE is set correctly
docker compose -f docker-compose.staging.yml --env-file .env.staging exec backend env | grep USE_SUPABASE

# Verify JWT_SECRET is configured
docker compose -f docker-compose.staging.yml --env-file .env.staging exec backend env | grep JWT_SECRET
```

---

### **Issue 6: Rate Limiting Not Working**

**Symptoms:**
- No 429 responses under load
- Rate limiting policies not enforced
- Security verification warnings

**Solution:**
```bash
# Test rate limiting manually
for i in {1..20}; do curl -s -w "%{http_code}\n" -o /dev/null http://localhost/api/health; done

# Check nginx rate limiting configuration
docker compose -f docker-compose.staging.yml --env-file .env.staging exec nginx nginx -T | grep limit_req

# Verify nginx logs show rate limiting
docker compose -f docker-compose.staging.yml --env-file .env.staging logs nginx | grep limit
```

---

### **Issue 7: Security Headers Missing**

**Symptoms:**
- Security headers not present in responses
- CSP violations in browser console
- Security verification failures

**Solution:**
```bash
# Test security headers via nginx
curl -s -I http://localhost/ | grep -E "(X-Frame-Options|X-Content-Type-Options|Content-Security-Policy)"

# Check nginx configuration
docker compose -f docker-compose.staging.yml --env-file .env.staging exec nginx cat /etc/nginx/nginx.conf | grep add_header

# Restart nginx if configuration was updated
docker compose -f docker-compose.staging.yml --env-file .env.staging restart nginx
```

---

### **Issue 8: Container Health Check Failures**

**Symptoms:**
- Containers show as "unhealthy"
- Health check timeouts
- Service dependencies not starting

**Solution:**
```bash
# Check health check configuration
docker inspect $(docker compose -f docker-compose.staging.yml --env-file .env.staging ps -q backend) | grep -A 10 "Health"

# Test health endpoints manually
curl http://localhost:3001/api/health
curl http://localhost:3000

# Adjust health check intervals if needed
# Edit docker-compose.staging.yml healthcheck settings
```

---

## 🔍 **Diagnostic Commands**

### **System Status Check**
```bash
# Overall system status
docker compose -f docker-compose.staging.yml --env-file .env.staging ps

# Resource usage
docker stats $(docker compose -f docker-compose.staging.yml --env-file .env.staging ps -q)

# Network connectivity
docker compose -f docker-compose.staging.yml --env-file .env.staging exec backend ping frontend
docker compose -f docker-compose.staging.yml --env-file .env.staging exec frontend ping backend
```

### **Security Verification**
```bash
# Test authentication
curl -s -w "%{http_code}" -o /dev/null http://localhost:3001/api/employees

# Test security headers
curl -s -I http://localhost/ | grep -E "(X-Frame|X-Content|X-XSS|Content-Security)"

# Test RLS protection
curl -s "https://abylqjnpaegeqwktcukn.supabase.co/rest/v1/Employee" \
  -H "apikey: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.WCzj8fDu7vdxhvbOUuoQHVamy9-XYBr4vtTox52ap2o"
```

---

## 📞 **Emergency Procedures**

### **Complete Environment Reset**
```bash
# Stop all services
docker compose -f docker-compose.staging.yml --env-file .env.staging down

# Remove all containers and volumes
docker compose -f docker-compose.staging.yml --env-file .env.staging down -v --remove-orphans

# Rebuild everything from scratch
docker compose -f docker-compose.staging.yml --env-file .env.staging build --no-cache
docker compose -f docker-compose.staging.yml --env-file .env.staging up -d
```

### **Security Incident Response**
1. **Immediate Actions:**
   - Stop the staging environment: `docker compose down`
   - Check logs for security events
   - Verify .env.staging file integrity

2. **Investigation:**
   - Review authentication logs
   - Check for unauthorized access attempts
   - Verify RLS policies are active

3. **Recovery:**
   - Update security configurations if needed
   - Restart with enhanced monitoring
   - Run full security verification

---

## 📋 **Maintenance Checklist**

### **Daily Checks**
- [ ] All containers healthy
- [ ] No error logs
- [ ] Authentication working
- [ ] Security headers present

### **Weekly Checks**
- [ ] Run security verification script
- [ ] Update environment variables if needed
- [ ] Check resource usage
- [ ] Review nginx logs

### **Monthly Checks**
- [ ] Update base Docker images
- [ ] Review and update security policies
- [ ] Performance optimization
- [ ] Documentation updates

---

**For additional support, refer to:**
- `docs/current/security/SECURITY_STATUS_UPDATE_20250524.md`
- `docs/current/security/EMERGENCY_SECURITY_TESTING_GUIDE.md`
- `scripts/verify-staging-security-enhanced.sh`
