// frontend/src/components/features/reporting/dashboard/filters/LocationFilter.tsx

import React from 'react';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { cn } from '@/lib/utils';
import { ChevronDown, X, Search, MapPin } from 'lucide-react';
import {
  useReportingFilters,
  useReportingFiltersActions,
  useReportingFiltersValidation,
} from '../../data/stores/useReportingFiltersStore';

interface LocationFilterProps {
  compact?: boolean;
  className?: string;
}

/**
 * @component LocationFilter
 * @description Multi-select location filter component for reporting dashboard
 * 
 * Responsibilities:
 * - Provides location selection interface with search
 * - Integrates with reporting filters store
 * - Handles validation and error display
 * - Supports compact and full layouts
 * 
 * SOLID Principles Applied:
 * - SRP: Single responsibility of location filtering
 * - OCP: Open for extension via props
 * - DIP: Depends on filter store abstractions
 */
export const LocationFilter: React.FC<LocationFilterProps> = ({
  compact = false,
  className = '',
}) => {
  const filters = useReportingFilters();
  const { setLocations } = useReportingFiltersActions();
  const { validationErrors } = useReportingFiltersValidation();

  const [isOpen, setIsOpen] = React.useState(false);
  const [searchTerm, setSearchTerm] = React.useState('');

  // Mock location options - in real app, this would come from an API
  const locationOptions = [
    'New York, NY',
    'Los Angeles, CA',
    'Chicago, IL',
    'Houston, TX',
    'Phoenix, AZ',
    'Philadelphia, PA',
    'San Antonio, TX',
    'San Diego, CA',
    'Dallas, TX',
    'San Jose, CA',
    'Austin, TX',
    'Jacksonville, FL',
    'Fort Worth, TX',
    'Columbus, OH',
    'Charlotte, NC',
    'San Francisco, CA',
    'Indianapolis, IN',
    'Seattle, WA',
    'Denver, CO',
    'Washington, DC',
  ];

  // Filter locations based on search term
  const filteredLocations = locationOptions.filter(location =>
    location.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Handle location selection
  const handleLocationToggle = (location: string) => {
    const currentLocations = filters.locations;
    const isSelected = currentLocations.includes(location);
    
    if (isSelected) {
      setLocations(currentLocations.filter(l => l !== location));
    } else {
      setLocations([...currentLocations, location]);
    }
  };

  // Handle select all/none
  const handleSelectAll = () => {
    setLocations(filteredLocations);
  };

  const handleSelectNone = () => {
    setLocations([]);
  };

  // Get display text
  const getDisplayText = () => {
    const selectedCount = filters.locations.length;
    if (selectedCount === 0) return 'All locations';
    if (selectedCount === 1) return filters.locations[0];
    return `${selectedCount} locations`;
  };

  const hasError = validationErrors.locations;

  if (compact) {
    return (
      <div className={cn('space-y-1', className)}>
        <Label className="text-xs font-medium">Location</Label>
        <Popover open={isOpen} onOpenChange={setIsOpen}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className={cn(
                'w-full justify-between text-left font-normal',
                hasError && 'border-red-500'
              )}
            >
              <span className="truncate">{getDisplayText()}</span>
              <ChevronDown className="ml-2 h-4 w-4 shrink-0" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-64 p-0" align="start">
            <div className="p-3">
              <div className="relative mb-3">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search locations..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8"
                />
              </div>
              <div className="flex justify-between mb-3">
                <Button variant="ghost" size="sm" onClick={handleSelectAll}>
                  All
                </Button>
                <Button variant="ghost" size="sm" onClick={handleSelectNone}>
                  None
                </Button>
              </div>
              <div className="max-h-48 overflow-y-auto space-y-2">
                {filteredLocations.map((location) => (
                  <div key={location} className="flex items-center space-x-2">
                    <Checkbox
                      id={`location-${location}`}
                      checked={filters.locations.includes(location)}
                      onCheckedChange={() => handleLocationToggle(location)}
                    />
                    <Label
                      htmlFor={`location-${location}`}
                      className="text-sm font-normal cursor-pointer flex-1 flex items-center gap-2"
                    >
                      <MapPin className="h-3 w-3 text-muted-foreground" />
                      {location}
                    </Label>
                  </div>
                ))}
              </div>
            </div>
          </PopoverContent>
        </Popover>
        {hasError && (
          <p className="text-xs text-red-600">{hasError}</p>
        )}
      </div>
    );
  }

  return (
    <div className={cn('space-y-2', className)}>
      <Label className="text-sm font-medium">Locations</Label>
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className={cn(
              'w-full justify-between text-left font-normal',
              hasError && 'border-red-500'
            )}
          >
            <span>{getDisplayText()}</span>
            <ChevronDown className="ml-2 h-4 w-4 shrink-0" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-80 p-0" align="start">
          <div className="p-4">
            <div className="flex justify-between items-center mb-4">
              <h4 className="font-medium text-sm">Select Locations</h4>
              <div className="flex gap-2">
                <Button variant="ghost" size="sm" onClick={handleSelectAll}>
                  All
                </Button>
                <Button variant="ghost" size="sm" onClick={handleSelectNone}>
                  None
                </Button>
              </div>
            </div>
            
            <div className="relative mb-4">
              <Search className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search locations..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-9"
              />
            </div>
            
            <div className="max-h-64 overflow-y-auto space-y-3">
              {filteredLocations.map((location) => (
                <div key={location} className="flex items-center space-x-3">
                  <Checkbox
                    id={`location-${location}`}
                    checked={filters.locations.includes(location)}
                    onCheckedChange={() => handleLocationToggle(location)}
                  />
                  <Label
                    htmlFor={`location-${location}`}
                    className="text-sm font-normal cursor-pointer flex-1 flex items-center gap-2"
                  >
                    <MapPin className="h-4 w-4 text-muted-foreground" />
                    {location}
                  </Label>
                </div>
              ))}
            </div>
          </div>
        </PopoverContent>
      </Popover>
      
      {/* Selected location badges */}
      {filters.locations.length > 0 && (
        <div className="flex flex-wrap gap-1">
          {filters.locations.slice(0, 3).map((location) => (
            <Badge key={location} variant="secondary" className="text-xs pr-1">
              {location.length > 15 ? `${location.slice(0, 15)}...` : location}
              <Button
                variant="ghost"
                size="sm"
                className="h-auto p-0 ml-1 hover:bg-transparent"
                onClick={() => handleLocationToggle(location)}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          ))}
          {filters.locations.length > 3 && (
            <Badge variant="secondary" className="text-xs">
              +{filters.locations.length - 3} more
            </Badge>
          )}
        </div>
      )}
      
      {hasError && (
        <p className="text-sm text-red-600">{hasError}</p>
      )}
    </div>
  );
};

export default LocationFilter;
