/**
 * Unified Generic Toast Service - Following SRP and DRY principles
 *
 * This service provides a consistent interface for all toast notifications
 * across the application, eliminating code duplication and ensuring
 * consistent messaging patterns using generics.
 */

import { toast } from '@/hooks/utils/use-toast';

export type ToastVariant = 'default' | 'destructive';

export interface ToastOptions {
  title?: string | undefined;
  description?: string | undefined;
  variant?: ToastVariant | undefined;
  duration?: number | undefined;
}

/**
 * Configuration interface for entity-specific toast messages
 */
export interface EntityToastConfig<T = any> {
  entityName: string; // e.g., "Employee", "Vehicle", "Service Record"
  getDisplayName: (entity: T) => string; // Function to get display name from entity
  messages: {
    created: {
      title: string;
      description: (displayName: string) => string;
    };
    updated: {
      title: string;
      description: (displayName: string) => string;
    };
    deleted: {
      title: string;
      description: (displayName: string) => string;
    };
    creationError: {
      title: string;
      description: (error: string) => string;
    };
    updateError: {
      title: string;
      description: (error: string) => string;
    };
    deletionError: {
      title: string;
      description: (error: string) => string;
    };
  };
}

/**
 * Base toast service class following SRP
 */
class ToastService {
  /**
   * Show a generic toast notification
   */
  show(options: ToastOptions) {
    return toast({
      title: options.title,
      description: options.description,
      variant: options.variant || 'default',
      ...(options.duration && { duration: options.duration }),
    });
  }

  /**
   * Show a success toast notification
   */
  success(title: string, description?: string) {
    return this.show({
      title,
      description,
      variant: 'default',
    });
  }

  /**
   * Show an error toast notification
   */
  error(title: string, description?: string) {
    return this.show({
      title,
      description,
      variant: 'destructive',
    });
  }

  /**
   * Show an info toast notification
   */
  info(title: string, description?: string) {
    return this.show({
      title,
      description,
      variant: 'default',
    });
  }
}

/**
 * Generic Entity Toast Service - Works with any entity type T
 */
export class GenericEntityToastService<T = any> extends ToastService {
  private config: EntityToastConfig<T>;

  constructor(config: EntityToastConfig<T>) {
    super();
    this.config = config;
  }

  /**
   * Show entity created success toast
   */
  entityCreated(entity: T) {
    const displayName = this.config.getDisplayName(entity);
    return this.success(
      this.config.messages.created.title,
      this.config.messages.created.description(displayName)
    );
  }

  /**
   * Show entity updated success toast
   */
  entityUpdated(entity: T) {
    const displayName = this.config.getDisplayName(entity);
    return this.success(
      this.config.messages.updated.title,
      this.config.messages.updated.description(displayName)
    );
  }

  /**
   * Show entity deleted success toast
   */
  entityDeleted(entity: T) {
    const displayName = this.config.getDisplayName(entity);
    return this.success(
      this.config.messages.deleted.title,
      this.config.messages.deleted.description(displayName)
    );
  }

  /**
   * Show entity creation error toast
   */
  entityCreationError(error: string) {
    return this.error(
      this.config.messages.creationError.title,
      this.config.messages.creationError.description(error)
    );
  }

  /**
   * Show entity update error toast
   */
  entityUpdateError(error: string) {
    return this.error(
      this.config.messages.updateError.title,
      this.config.messages.updateError.description(error)
    );
  }

  /**
   * Show entity deletion error toast
   */
  entityDeletionError(error: string) {
    return this.error(
      this.config.messages.deletionError.title,
      this.config.messages.deletionError.description(error)
    );
  }
}

// =============================================================================
// ENTITY CONFIGURATIONS - Define toast messages for each domain
// =============================================================================

/**
 * Employee entity toast configuration
 */
const employeeToastConfig: EntityToastConfig<{ name: string }> = {
  entityName: 'Employee',
  getDisplayName: employee => employee.name,
  messages: {
    created: {
      title: 'Employee Added',
      description: name =>
        `The employee "${name}" has been successfully created.`,
    },
    updated: {
      title: 'Employee Updated Successfully',
      description: name => `${name} has been updated.`,
    },
    deleted: {
      title: 'Employee Deleted Successfully',
      description: name =>
        `${name} has been permanently removed from the system.`,
    },
    creationError: {
      title: 'Failed to Create Employee',
      description: error =>
        error || 'An unexpected error occurred while creating the employee.',
    },
    updateError: {
      title: 'Update Failed',
      description: error =>
        error || 'An unexpected error occurred while updating the employee.',
    },
    deletionError: {
      title: 'Failed to Delete Employee',
      description: error =>
        error || 'An unexpected error occurred while deleting the employee.',
    },
  },
};

/**
 * Delegation entity toast configuration
 */
const delegationToastConfig: EntityToastConfig<{
  event?: string;
  location?: string;
}> = {
  entityName: 'Delegation',
  getDisplayName: delegation =>
    delegation.event || delegation.location || 'Delegation',
  messages: {
    created: {
      title: 'Delegation Created',
      description: name =>
        `The delegation "${name}" has been successfully created.`,
    },
    updated: {
      title: 'Delegation Updated Successfully',
      description: name => `${name} has been updated.`,
    },
    deleted: {
      title: 'Delegation Deleted Successfully',
      description: name => `${name} has been permanently removed.`,
    },
    creationError: {
      title: 'Failed to Create Delegation',
      description: error =>
        error || 'An unexpected error occurred while creating the delegation.',
    },
    updateError: {
      title: 'Update Failed',
      description: error =>
        error || 'An unexpected error occurred while updating the delegation.',
    },
    deletionError: {
      title: 'Failed to Delete Delegation',
      description: error =>
        error || 'An unexpected error occurred while deleting the delegation.',
    },
  },
};

/**
 * Vehicle entity toast configuration
 */
const vehicleToastConfig: EntityToastConfig<{ make: string; model: string }> = {
  entityName: 'Vehicle',
  getDisplayName: vehicle => `${vehicle.make} ${vehicle.model}`,
  messages: {
    created: {
      title: 'Vehicle Added',
      description: name =>
        `The vehicle "${name}" has been successfully created.`,
    },
    updated: {
      title: 'Vehicle Updated Successfully',
      description: name => `${name} has been updated.`,
    },
    deleted: {
      title: 'Vehicle Deleted Successfully',
      description: name => `${name} has been permanently removed.`,
    },
    creationError: {
      title: 'Failed to Create Vehicle',
      description: error =>
        error || 'An unexpected error occurred while creating the vehicle.',
    },
    updateError: {
      title: 'Update Failed',
      description: error =>
        error || 'An unexpected error occurred while updating the vehicle.',
    },
    deletionError: {
      title: 'Failed to Delete Vehicle',
      description: error =>
        error || 'An unexpected error occurred while deleting the vehicle.',
    },
  },
};

/**
 * Task entity toast configuration
 */
const taskToastConfig: EntityToastConfig<{ title?: string; name?: string }> = {
  entityName: 'Task',
  getDisplayName: task => task.title || task.name || 'Task',
  messages: {
    created: {
      title: 'Task Created',
      description: name => `The task "${name}" has been successfully created.`,
    },
    updated: {
      title: 'Task Updated Successfully',
      description: name => `${name} has been updated.`,
    },
    deleted: {
      title: 'Task Deleted Successfully',
      description: name => `${name} has been permanently removed.`,
    },
    creationError: {
      title: 'Failed to Create Task',
      description: error =>
        error || 'An unexpected error occurred while creating the task.',
    },
    updateError: {
      title: 'Update Failed',
      description: error =>
        error || 'An unexpected error occurred while updating the task.',
    },
    deletionError: {
      title: 'Failed to Delete Task',
      description: error =>
        error || 'An unexpected error occurred while deleting the task.',
    },
  },
};

/**
 * Service Record-specific toast messages following DRY principles
 */
export class ServiceRecordToastService extends ToastService {
  /**
   * Show service record created success toast
   */
  serviceRecordCreated(vehicleName: string, serviceType: string) {
    return this.success(
      'Service Record Added',
      `${serviceType} service for "${vehicleName}" has been successfully logged.`
    );
  }

  /**
   * Show service record updated success toast
   */
  serviceRecordUpdated(vehicleName: string, serviceType: string) {
    return this.success(
      'Service Record Updated',
      `${serviceType} service for "${vehicleName}" has been updated.`
    );
  }

  /**
   * Show service record deleted success toast
   */
  serviceRecordDeleted(vehicleName: string, serviceType: string) {
    return this.success(
      'Service Record Deleted',
      `${serviceType} service record for "${vehicleName}" has been permanently removed.`
    );
  }

  /**
   * Show service record creation error toast
   */
  serviceRecordCreationError(error: string) {
    return this.error(
      'Failed to Log Service Record',
      error || 'An unexpected error occurred while logging the service record.'
    );
  }

  /**
   * Show service record update error toast
   */
  serviceRecordUpdateError(error: string) {
    return this.error(
      'Update Failed',
      error || 'An unexpected error occurred while updating the service record.'
    );
  }

  /**
   * Show service record deletion error toast
   */
  serviceRecordDeletionError(error: string) {
    return this.error(
      'Failed to Delete Service Record',
      error || 'An unexpected error occurred while deleting the service record.'
    );
  }
}

// =============================================================================
// UTILITY FACTORY FUNCTIONS
// =============================================================================

/**
 * Factory function to create a generic entity toast service for any entity type
 * Useful for creating toast services for new entities without pre-configuration
 */
export function createEntityToastService<T>(config: EntityToastConfig<T>) {
  return new GenericEntityToastService<T>(config);
}

/**
 * Factory function to create a simple entity toast service with minimal configuration
 * For entities that only need basic CRUD messages
 */
export function createSimpleEntityToastService<T>(
  entityName: string,
  getDisplayName: (entity: T) => string
): GenericEntityToastService<T> {
  const config: EntityToastConfig<T> = {
    entityName,
    getDisplayName,
    messages: {
      created: {
        title: `${entityName} Created`,
        description: (displayName: string) =>
          `The ${entityName.toLowerCase()} "${displayName}" has been successfully created.`,
      },
      updated: {
        title: `${entityName} Updated Successfully`,
        description: (displayName: string) =>
          `${displayName} has been updated.`,
      },
      deleted: {
        title: `${entityName} Deleted Successfully`,
        description: (displayName: string) =>
          `${displayName} has been permanently removed.`,
      },
      creationError: {
        title: `Failed to Create ${entityName}`,
        description: (error: string) =>
          error ||
          `An unexpected error occurred while creating the ${entityName.toLowerCase()}.`,
      },
      updateError: {
        title: 'Update Failed',
        description: (error: string) =>
          error ||
          `An unexpected error occurred while updating the ${entityName.toLowerCase()}.`,
      },
      deletionError: {
        title: `Failed to Delete ${entityName}`,
        description: (error: string) =>
          error ||
          `An unexpected error occurred while deleting the ${entityName.toLowerCase()}.`,
      },
    },
  };

  return new GenericEntityToastService<T>(config);
}

// =============================================================================
// SINGLETON INSTANCES - Pre-configured toast services for each domain
// =============================================================================

// Base toast service for generic use
export const toastService = new ToastService();

// Entity-specific toast services using the generic service
export const employeeToast = new GenericEntityToastService(employeeToastConfig);
export const delegationToast = new GenericEntityToastService(
  delegationToastConfig
);
export const vehicleToast = new GenericEntityToastService(vehicleToastConfig);
export const taskToast = new GenericEntityToastService(taskToastConfig);
export const serviceRecordToast = new ServiceRecordToastService();
