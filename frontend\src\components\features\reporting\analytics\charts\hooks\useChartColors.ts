// frontend/src/components/features/reporting/analytics/charts/hooks/useChartColors.ts
import { useCallback } from 'react';

/**
 * @hook useChartColors
 * @description Provides a consistent set of colors for charts and utility to get colors based on data.
 * Adheres to SRP by managing chart color logic.
 */
export const useChartColors = () => {
  // Define a palette of colors for charts
  const CHART_PALETTE = [
    '#8884d8', // Purple
    '#82ca9d', // Green
    '#ffc658', // Yellow
    '#ff7300', // Orange
    '#0088FE', // Blue
    '#00C49F', // Teal
    '#FFBB28', // Gold
    '#FF8042', // Coral
    '#AF19FF', // Dark Purple
    '#DE3163', // Crimson
  ];

  /**
   * Gets a color from the palette based on an index.
   * @param {number} index - The index in the color palette.
   * @returns {string} The color hex string.
   */
  const getColor = useCallback((index: number): string => {
    return CHART_PALETTE[index % CHART_PALETTE.length] || '#000000'; // Fallback to black if undefined
  }, []);

  /**
   * Assigns colors to data points based on their status or category.
   * This is a simplified example; in a real app, you might map specific statuses to specific colors.
   * @param {Array<any>} data - The data array, where each item might have a 'status' or 'name' property.
   * @param {string} key - The key in the data item to use for color assignment (e.g., 'status', 'name').
   * @returns {Array<any>} The data array with an added 'color' property for each item.
   */
  const assignColorsToData = useCallback(
    <T extends Record<string, any>>(data: T[], key: string = 'status'): T[] => {
      const uniqueValues = Array.from(new Set(data.map(item => item[key])));
      const colorMap = new Map<any, string>();
      uniqueValues.forEach((value, index) => {
        colorMap.set(value, getColor(index));
      });
      return data.map(item => ({
        ...item,
        color: colorMap.get(item[key]) || '#CCCCCC', // Default grey for unmatched
      }));
    },
    [getColor]
  );

  return {
    CHART_PALETTE,
    getColor,
    assignColorsToData,
  };
};
