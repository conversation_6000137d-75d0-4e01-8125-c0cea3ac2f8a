import { Router } from 'express';

import * as recipientController from '../controllers/recipient.controller.js';
import {
  addValidationSecurityHeaders,
  sanitizeInput,
  validateRequest,
} from '../middleware/inputValidation.js';
import { enhancedAuthenticateUser } from '../middleware/jwtAuth.middleware.js';
import { adaptiveRateLimit } from '../middleware/rateLimiting.js';
import { apiDeduplication } from '../middleware/requestDeduplication.js';
import { responseWrapper } from '../middleware/responseWrapper.js';
import { requireRole } from '../middleware/supabaseAuth.js';
import { validate } from '../middleware/validation.js';
import {
  recipientCreateSchema,
  recipientIdSchema,
  recipientQuerySchema,
  recipientUpdateSchema,
} from '../schemas/recipient.schema.js';

const router = Router();

// Apply security middleware to all recipient routes
router.use(adaptiveRateLimit);
router.use(apiDeduplication);
router.use(responseWrapper);

/**
 * POST /api/recipients - Create a new recipient
 * Access: ADMIN, MANAGER, OFFICE_STAFF
 */
router.post(
  '/',
  enhancedAuthenticateUser,
  requireRole(['ADMIN', 'MANAGER', 'OFFICE_STAFF']),
  validate(recipientCreateSchema, 'body'),
  (req, res, next) => {
    addValidationSecurityHeaders(res);
    next();
  },
  recipientController.createRecipient,
);

/**
 * GET /api/recipients - Get all recipients with optional filtering
 * Access: All authenticated users (for gift entry purposes)
 */
router.get(
  '/',
  enhancedAuthenticateUser,
  validate(recipientQuerySchema, 'query'),
  recipientController.getAllRecipients,
);

/**
 * GET /api/recipients/with-counts - Get recipients with their gift counts
 * Access: ADMIN, MANAGER, OFFICE_STAFF
 */
router.get(
  '/with-counts',
  enhancedAuthenticateUser,
  requireRole(['ADMIN', 'MANAGER', 'OFFICE_STAFF']),
  validate(recipientQuerySchema, 'query'),
  recipientController.getRecipientsWithGiftCounts,
);

/**
 * GET /api/recipients/:id - Get a single recipient by ID
 * Access: All authenticated users
 */
router.get(
  '/:id',
  enhancedAuthenticateUser,
  validate(recipientIdSchema, 'params'),
  recipientController.getRecipientById,
);

/**
 * PUT /api/recipients/:id - Update a recipient
 * Access: ADMIN, MANAGER, OFFICE_STAFF
 */
router.put(
  '/:id',
  enhancedAuthenticateUser,
  requireRole(['ADMIN', 'MANAGER', 'OFFICE_STAFF']),
  validate(recipientIdSchema, 'params'),
  validate(recipientUpdateSchema, 'body'),
  (req, res, next) => {
    addValidationSecurityHeaders(res);
    next();
  },
  recipientController.updateRecipient,
);

/**
 * DELETE /api/recipients/:id - Delete a recipient
 * Access: ADMIN only
 */
router.delete(
  '/:id',
  enhancedAuthenticateUser,
  requireRole(['ADMIN']),
  validate(recipientIdSchema, 'params'),
  recipientController.deleteRecipient,
);

export default router;
