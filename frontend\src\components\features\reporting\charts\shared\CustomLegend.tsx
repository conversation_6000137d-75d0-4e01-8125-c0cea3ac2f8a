// frontend/src/components/features/reporting/charts/shared/CustomLegend.tsx

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Eye, EyeOff } from 'lucide-react';

/**
 * Legend item data interface
 */
export interface LegendItem {
  value: string;
  color: string;
  dataKey?: string;
  payload?: any;
  type?: string;
}

/**
 * Props for CustomLegend component
 */
interface CustomLegendProps {
  payload?: LegendItem[];
  onItemClick?: (item: LegendItem, index: number) => void;
  onItemToggle?: (dataKey: string, visible: boolean) => void;
  hiddenItems?: Set<string>;
  interactive?: boolean;
  showToggleButtons?: boolean;
  showValues?: boolean;
  layout?: 'horizontal' | 'vertical';
  align?: 'left' | 'center' | 'right';
  className?: string;
}

/**
 * CustomLegend Component
 * 
 * A flexible, interactive legend component for charts.
 * Follows SRP by only handling legend display and interaction.
 * 
 * Features:
 * - Interactive item toggling
 * - Horizontal and vertical layouts
 * - Value display
 * - Custom styling
 * - Responsive design
 * - Accessibility support
 * 
 * @param props - Component props
 * @returns JSX element
 */
export const CustomLegend: React.FC<CustomLegendProps> = ({
  payload = [],
  onItemClick,
  onItemToggle,
  hiddenItems = new Set(),
  interactive = true,
  showToggleButtons = false,
  showValues = false,
  layout = 'horizontal',
  align = 'center',
  className = '',
}) => {
  const [localHiddenItems, setLocalHiddenItems] = useState<Set<string>>(new Set());
  
  // Use external hiddenItems if provided, otherwise use local state
  const effectiveHiddenItems = hiddenItems.size > 0 ? hiddenItems : localHiddenItems;

  // Handle item click
  const handleItemClick = (item: LegendItem, index: number) => {
    if (!interactive) return;

    if (onItemClick) {
      onItemClick(item, index);
    }

    // Toggle visibility if dataKey is available
    if (item.dataKey) {
      const isHidden = effectiveHiddenItems.has(item.dataKey);
      const newHiddenItems = new Set(effectiveHiddenItems);
      
      if (isHidden) {
        newHiddenItems.delete(item.dataKey);
      } else {
        newHiddenItems.add(item.dataKey);
      }
      
      if (hiddenItems.size === 0) {
        setLocalHiddenItems(newHiddenItems);
      }
      
      if (onItemToggle) {
        onItemToggle(item.dataKey, !isHidden);
      }
    }
  };

  // Handle toggle all
  const handleToggleAll = (visible: boolean) => {
    const newHiddenItems = new Set<string>();
    
    if (!visible) {
      payload.forEach(item => {
        if (item.dataKey) {
          newHiddenItems.add(item.dataKey);
        }
      });
    }
    
    if (hiddenItems.size === 0) {
      setLocalHiddenItems(newHiddenItems);
    }
    
    if (onItemToggle) {
      payload.forEach(item => {
        if (item.dataKey) {
          onItemToggle(item.dataKey, visible);
        }
      });
    }
  };

  if (!payload || payload.length === 0) {
    return null;
  }

  // Layout classes
  const layoutClasses = {
    horizontal: 'flex flex-wrap justify-center gap-4',
    vertical: 'flex flex-col gap-2',
  };

  const alignClasses = {
    left: 'justify-start',
    center: 'justify-center',
    right: 'justify-end',
  };

  return (
    <div className={`${className}`}>
      {/* Toggle All Buttons */}
      {showToggleButtons && interactive && (
        <div className="flex justify-center gap-2 mb-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleToggleAll(true)}
            className="flex items-center gap-2"
          >
            <Eye className="h-3 w-3" />
            Show All
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleToggleAll(false)}
            className="flex items-center gap-2"
          >
            <EyeOff className="h-3 w-3" />
            Hide All
          </Button>
        </div>
      )}

      {/* Legend Items */}
      <div 
        className={`
          ${layoutClasses[layout]} 
          ${layout === 'horizontal' ? alignClasses[align] : ''}
        `}
      >
        {payload.map((item, index) => {
          const isHidden = item.dataKey && effectiveHiddenItems.has(item.dataKey);
          const itemValue = item.payload?.value || item.payload?.count;
          
          return (
            <div
              key={`legend-item-${index}`}
              className={`
                flex items-center gap-2 transition-all duration-200
                ${interactive ? 'cursor-pointer hover:opacity-80' : ''}
                ${isHidden ? 'opacity-50' : ''}
                ${layout === 'vertical' ? 'justify-between' : ''}
              `}
              onClick={() => handleItemClick(item, index)}
              role={interactive ? 'button' : undefined}
              tabIndex={interactive ? 0 : undefined}
              onKeyDown={(e) => {
                if (interactive && (e.key === 'Enter' || e.key === ' ')) {
                  e.preventDefault();
                  handleItemClick(item, index);
                }
              }}
            >
              {/* Color Indicator */}
              <div
                className="w-3 h-3 rounded-full flex-shrink-0 transition-colors duration-200"
                style={{ 
                  backgroundColor: isHidden ? '#d1d5db' : item.color 
                }}
              />
              
              {/* Label */}
              <span 
                className={`
                  text-sm transition-colors duration-200
                  ${isHidden ? 'text-gray-400' : 'text-gray-700'}
                  ${layout === 'vertical' ? 'flex-1' : ''}
                `}
              >
                {item.value}
              </span>
              
              {/* Value Badge */}
              {showValues && itemValue !== undefined && (
                <Badge 
                  variant={isHidden ? "outline" : "secondary"}
                  className="text-xs ml-auto"
                >
                  {typeof itemValue === 'number' 
                    ? itemValue.toLocaleString() 
                    : itemValue
                  }
                </Badge>
              )}
              
              {/* Visibility Indicator */}
              {interactive && item.dataKey && (
                <div className="ml-1 opacity-0 group-hover:opacity-100 transition-opacity">
                  {isHidden ? (
                    <EyeOff className="h-3 w-3 text-gray-400" />
                  ) : (
                    <Eye className="h-3 w-3 text-gray-600" />
                  )}
                </div>
              )}
            </div>
          );
        })}
      </div>
      
      {/* Summary */}
      {showValues && payload.length > 1 && (
        <div className="mt-4 pt-3 border-t border-gray-100 text-center">
          <span className="text-xs text-gray-500">
            Total Items: {payload.length} | 
            Visible: {payload.length - effectiveHiddenItems.size}
          </span>
        </div>
      )}
    </div>
  );
};

/**
 * Specialized legend for status charts
 */
export const StatusLegend: React.FC<Omit<CustomLegendProps, 'showValues'>> = (props) => (
  <CustomLegend {...props} showValues={true} />
);

/**
 * Specialized legend for trend charts
 */
export const TrendLegend: React.FC<CustomLegendProps> = (props) => (
  <CustomLegend 
    {...props} 
    showToggleButtons={true}
    interactive={true}
  />
);

/**
 * Compact legend for small spaces
 */
export const CompactLegend: React.FC<CustomLegendProps> = (props) => (
  <CustomLegend 
    {...props} 
    layout="horizontal"
    showValues={false}
    className="text-xs"
  />
);
