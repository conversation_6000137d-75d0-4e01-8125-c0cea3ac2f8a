/**
 * @file Data transformer for Delegation domain models.
 * @module transformers/delegationTransformer
 */

// API types from the new central location
import type {
  CreateDelegateRequest,
  CreateDelegationRequest,
  CreateDriverRequest,
  CreateEscortRequest,
  CreateFlightDetailsRequest,
  CreateVehicleAssignmentRequest,
  DelegationApiResponse,
  EmployeeApiResponse,
  FlightDetailsApiResponse,
  UpdateDelegationRequest,
  VehicleApiResponse,
} from '../types/apiContracts';
import type {
  CreateDelegationData,
  Delegation,
  DelegationStatusPrisma,
  DriverAvailabilityPrisma,
  Employee,
  EmployeeRolePrisma,
  EmployeeStatusPrisma,
  FlightDetails,
  Vehicle,
} from '../types/domain';
import { undefinedToNull } from '../utils/typeHelpers';

// These helpers now produce structures compatible with CreateDelegationRequest sub-properties
const DelegationAssignmentTransformers = {
  // fromApi methods are less critical now as DelegationApiResponse is domain Delegation
  // but if API responses for these are ever separate and different, they'd be useful.
  // For now, their usage in DelegationTransformer.fromApi might be simplified.

  toDelegateApiStructure(domainData: {
    name: string;
    notes?: string | undefined;
    title: string;
  }): CreateDelegateRequest {
    // The structure of the domain data is identical to CreateDelegateRequest
    return {
      name: domainData.name,
      notes: domainData.notes ?? null,
      title: domainData.title,
    };
  },

  toDriverApiStructure(domainData: {
    employeeId: number;
    notes?: string;
  }): CreateDriverRequest {
    // The structure of the domain data is identical to CreateDriverRequest
    return {
      employeeId: domainData.employeeId,
      notes: domainData.notes ?? null,
    };
  },

  toEscortApiStructure(domainData: {
    employeeId: number;
    notes?: string;
  }): CreateEscortRequest {
    // The structure of the domain data is identical to CreateEscortRequest
    return {
      employeeId: domainData.employeeId,
      notes: domainData.notes ?? null,
    };
  },

  toVehicleAssignmentApiStructure(domainData: {
    assignedDate: string;
    notes?: null | string;
    returnDate?: null | string;
    vehicleId: number;
  }): CreateVehicleAssignmentRequest {
    // The structure of the domain data is identical to CreateVehicleAssignmentRequest
    return domainData;
  },
};

// Helper transformers for converting API response types to domain types
const EmployeeTransformer = {
  fromApi(apiData: EmployeeApiResponse): Employee {
    return {
      availability: apiData.availability as DriverAvailabilityPrisma | null, // Cast string to enum
      contactEmail: apiData.contactEmail ?? null,
      contactInfo: apiData.contactInfo,
      contactMobile: apiData.contactMobile ?? null,
      contactPhone: apiData.contactPhone ?? null,
      createdAt: apiData.createdAt,
      currentLocation: apiData.currentLocation ?? null,
      department: apiData.department ?? null,
      employeeId: apiData.employeeId,
      fullName: apiData.fullName ?? null,
      generalAssignments: apiData.generalAssignments,
      hireDate: apiData.hireDate ?? null,
      id: apiData.id,
      name: apiData.name,
      notes: apiData.notes ?? null,
      position: apiData.position ?? null,
      profileImageUrl: apiData.profileImageUrl ?? null,
      role: apiData.role as EmployeeRolePrisma, // Cast string to enum
      shiftSchedule: apiData.shiftSchedule ?? null,
      skills: apiData.skills,
      status: apiData.status as EmployeeStatusPrisma | null, // Cast string to enum
      updatedAt: apiData.updatedAt,
      workingHours: apiData.workingHours ?? null,
    };
  },
};

const VehicleTransformer = {
  fromApi(apiData: VehicleApiResponse): Vehicle {
    return {
      color: apiData.color,
      createdAt: apiData.createdAt,
      id: apiData.id,
      imageUrl: apiData.imageUrl,
      initialOdometer: apiData.initialOdometer,
      licensePlate: apiData.licensePlate,
      make: apiData.make,
      model: apiData.model,
      ownerContact: apiData.ownerContact,
      ownerName: apiData.ownerName,
      serviceHistory: [], // Not included in API response for delegation context
      updatedAt: apiData.updatedAt,
      vin: apiData.vin,
      year: apiData.year,
    };
  },
};

/**
 * Transforms delegation data between API response formats and frontend domain models.
 */
export const DelegationTransformer = {
  fromApi(apiData: DelegationApiResponse): Delegation {
    return {
      // Map nested relations using their respective transformers or direct mapping if structure matches
      arrivalFlight: apiData.flightArrivalDetails
        ? FlightDetailsTransformer.fromApi(apiData.flightArrivalDetails)
        : null,
      createdAt: apiData.createdAt, // Direct mapping
      delegates: apiData.delegates || [], // Direct mapping, handle undefined
      departureFlight: apiData.flightDepartureDetails
        ? FlightDetailsTransformer.fromApi(apiData.flightDepartureDetails)
        : null,
      // Transform drivers: API returns Employee objects directly, but domain expects nested structure
      drivers:
        apiData.drivers?.map(driverEmployee => ({
          createdAt: apiData.createdAt, // Use delegation timestamps as fallback
          createdBy: null,
          delegationId: apiData.id,
          employee: EmployeeTransformer.fromApi(driverEmployee), // Transform and nest the employee data
          employeeId: driverEmployee.id, // Keep as number
          id: `driver-${apiData.id}-${driverEmployee.id}`, // Generate ID for join table
          updatedAt: apiData.updatedAt,
        })) || [],
      durationFrom: apiData.durationFrom, // Direct mapping
      durationTo: apiData.durationTo, // Direct mapping
      // Transform escorts: API returns Employee objects directly, but domain expects nested structure
      escorts:
        apiData.escorts?.map(escortEmployee => ({
          createdAt: apiData.createdAt, // Use delegation timestamps as fallback
          createdBy: null,
          delegationId: apiData.id,
          employee: EmployeeTransformer.fromApi(escortEmployee), // Transform and nest the employee data
          employeeId: escortEmployee.id, // Keep as number
          id: `escort-${apiData.id}-${escortEmployee.id}`, // Generate ID for join table
          updatedAt: apiData.updatedAt,
        })) || [],
      eventName: apiData.eventName, // Direct mapping
      id: apiData.id, // ID is now string in API response
      imageUrl: apiData.imageUrl ?? null, // Direct mapping, handle null/undefined
      invitationFrom: apiData.invitationFrom ?? null, // Direct mapping, handle null/undefined

      invitationTo: apiData.invitationTo ?? null, // Direct mapping, handle null/undefined
      location: apiData.location, // Direct mapping
      notes: apiData.notes ?? null, // Direct mapping, handle null/undefined

      status: apiData.status as DelegationStatusPrisma, // Direct mapping with type assertion

      statusHistory: apiData.statusHistory || [], // Direct mapping, handle undefined

      updatedAt: apiData.updatedAt, // Direct mapping

      // Transform vehicles: API returns Vehicle objects directly, but domain expects nested structure
      vehicles:
        apiData.vehicles?.map(vehicleData => ({
          createdAt: apiData.createdAt, // Use delegation timestamps as fallback
          createdBy: null,
          delegationId: apiData.id,
          id: `vehicle-${apiData.id}-${vehicleData.id}`, // Generate ID for join table
          updatedAt: apiData.updatedAt,
          vehicle: VehicleTransformer.fromApi(vehicleData), // Transform and nest the vehicle data
          vehicleId: vehicleData.id,
        })) || [],
    };
  },

  toCreateRequest(domainData: CreateDelegationData): CreateDelegationRequest {
    return {
      // ✅ FIXED: Use correct field names that match backend schema
      delegates:
        domainData.delegates?.map((delegate: any) =>
          DelegationAssignmentTransformers.toDelegateApiStructure(delegate)
        ) ?? [],
      driverEmployeeIds:
        domainData.drivers?.map(driver => driver.employeeId) ?? [], // ✅ Extract IDs from nested objects
      durationFrom: domainData.durationFrom, // ✅ Direct mapping (not startDate)
      durationTo: domainData.durationTo, // ✅ Direct mapping (not endDate)
      escortEmployeeIds:
        domainData.escorts?.map(escort => escort.employeeId) ?? [], // ✅ Extract IDs from nested objects
      eventName: domainData.eventName, // ✅ Direct mapping (not title)
      flightArrivalDetails: domainData.flightArrivalDetails
        ? FlightDetailsTransformer.toApiStructureForCreate(
            domainData.flightArrivalDetails
          )
        : undefined,
      flightDepartureDetails: domainData.flightDepartureDetails
        ? FlightDetailsTransformer.toApiStructureForCreate(
            domainData.flightDepartureDetails
          )
        : undefined,
      imageUrl: domainData.imageUrl ?? null,
      invitationFrom: domainData.invitationFrom ?? null,
      invitationTo: domainData.invitationTo ?? null,
      location: domainData.location,
      notes: domainData.notes ?? null,
      status: domainData.status as string,
      vehicleIds: domainData.vehicles?.map(vehicle => vehicle.vehicleId) ?? [], // ✅ Extract IDs from nested objects
    };
  },

  toUpdateRequest(
    domainData: Partial<CreateDelegationData>
  ): UpdateDelegationRequest {
    const request: UpdateDelegationRequest = {};

    // ✅ PRODUCTION FIX: Use correct field names that match backend expectations
    if (domainData.eventName !== undefined)
      request.eventName = domainData.eventName; // ✅ Direct mapping
    if (domainData.location !== undefined)
      request.location = domainData.location;
    if (domainData.durationFrom !== undefined)
      request.durationFrom = domainData.durationFrom; // ✅ Direct mapping
    if (domainData.durationTo !== undefined)
      request.durationTo = domainData.durationTo; // ✅ Direct mapping
    if (domainData.status !== undefined)
      request.status = domainData.status as string; // Type assertion
    if (domainData.notes !== undefined) request.notes = domainData.notes; // ✅ Direct mapping
    if (domainData.imageUrl !== undefined)
      request.imageUrl = domainData.imageUrl;
    if (domainData.invitationFrom !== undefined)
      request.invitationFrom = domainData.invitationFrom;
    if (domainData.invitationTo !== undefined)
      request.invitationTo = domainData.invitationTo;

    if (domainData.flightArrivalDetails !== undefined) {
      request.flightArrivalDetails =
        domainData.flightArrivalDetails ?? undefined;
    }
    if (domainData.flightDepartureDetails !== undefined) {
      request.flightDepartureDetails =
        domainData.flightDepartureDetails ?? undefined;
    }

    // ✅ FIX: Include assignment fields in update request
    if (domainData.escorts !== undefined) {
      request.escortEmployeeIds = domainData.escorts.map(e => e.employeeId);
    }
    if (domainData.drivers !== undefined) {
      request.driverEmployeeIds = domainData.drivers.map(d => d.employeeId);
    }
    if (domainData.vehicles !== undefined) {
      request.vehicleIds = domainData.vehicles.map(v => v.vehicleId);
    }

    return request;
  },
};

export const FlightDetailsTransformer = {
  fromApi(apiData: FlightDetailsApiResponse): FlightDetails | null {
    if (!apiData) return null;
    return {
      airport: apiData.airport,
      dateTime: apiData.dateTime,
      flightNumber: apiData.flightNumber,
      id: apiData.id,
      notes: apiData.notes || null,
      terminal: apiData.terminal || null,
    };
  },

  // This is for creating flight details as part of a new delegation
  toApiStructureForCreate(
    domainData: Omit<FlightDetails, 'id'>
  ): CreateFlightDetailsRequest {
    // The structure of Omit<FlightDetails, 'id'> is identical to CreateFlightDetailsRequest
    return domainData;
  },

  toCreateRequest(domainData: FlightDetails): CreateFlightDetailsRequest {
    // This is for managing details on an existing delegation, not used for initial creation
    return {
      airport: domainData.airport,
      dateTime: domainData.dateTime,
      flightNumber: domainData.flightNumber,
      notes: domainData.notes ?? null,
      terminal: domainData.terminal ?? null,
    };
  },
};
