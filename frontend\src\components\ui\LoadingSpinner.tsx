'use client';

import { Loader2 } from 'lucide-react';
import React from 'react';

import { cn } from '@/lib/utils';

interface LoadingSpinnerProps {
  className?: string;
  fullPage?: boolean;
  size?: 'lg' | 'md' | 'sm' | 'xl';
  text?: string;
}

/**
 * Loading spinner component with optional text
 * Can be used inline or as a full-page overlay
 */
const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  className,
  fullPage = false,
  size = 'md',
  text,
}) => {
  // Size mappings
  const sizeClasses = {
    lg: 'h-12 w-12',
    md: 'h-8 w-8',
    sm: 'h-4 w-4',
    xl: 'h-16 w-16',
  };

  // Text size mappings
  const textSizeClasses = {
    lg: 'text-base',
    md: 'text-sm',
    sm: 'text-xs',
    xl: 'text-lg',
  };

  const spinnerContent = (
    <div
      className={cn(
        'flex flex-col items-center justify-center',
        fullPage ? 'fixed inset-0 bg-background/80 backdrop-blur-sm z-50' : '',
        className
      )}
    >
      <Loader2 className={cn('animate-spin text-primary', sizeClasses[size])} />
      {text && (
        <p className={cn('mt-2 text-muted-foreground', textSizeClasses[size])}>
          {text}
        </p>
      )}
    </div>
  );

  return spinnerContent;
};

export default LoadingSpinner;
