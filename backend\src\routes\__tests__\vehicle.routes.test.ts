import { jest } from '@jest/globals';
import request from 'supertest';
import { initializeAndGetApp } from '../../app.js'; // The Express app
import prisma from '../../utils/prisma.js'; // Prisma client
import { afterAll, beforeAll, beforeEach, describe, expect, it } from '@jest/globals';

let app;

beforeAll(async () => {
  app = await initializeAndGetApp();
});

// Ensure Prisma client disconnects after all tests are done
afterAll(async () => {
  await prisma.$disconnect();
});

// Clean up the Vehicle table before each test run in this suite
// This is a simple way to ensure test isolation for now.
// More robust solutions involve a dedicated test database and migrations.
beforeEach(async () => {
  // Delete records in an order that respects foreign key constraints or use $transaction
  await prisma.serviceRecord.deleteMany({});
  await prisma.task.deleteMany({}); // Assuming tasks might relate to vehicles directly or indirectly
  await prisma.employee.updateMany({ data: { assignedVehicleId: null } as any }); // Unassign vehicles from employees
  await prisma.vehicle.deleteMany({});
});

describe('Vehicle API Routes', () => {
  let createdVehicleId: number;
  const newVehicleData = {
    make: 'TestMake',
    model: 'TestModel',
    year: 2023,
    vin: `TESTVIN${Date.now()}`,
    licensePlate: 'TESTLP',
    ownerName: 'Test Owner',
    ownerContact: '<EMAIL>',
  };

  it('should create a new vehicle via POST /api/vehicles', async () => {
    const response = await request(app).post('/api/vehicles').send(newVehicleData);

    expect(response.statusCode).toBe(201);
    expect(response.body.status).toBe('success');
    expect(response.body.data).toHaveProperty('id');
    expect(response.body.data.make).toBe(newVehicleData.make);
    expect(response.body.data.vin).toBe(newVehicleData.vin);
    expect(response.body).toHaveProperty('timestamp');
    expect(response.body).toHaveProperty('requestId');
    createdVehicleId = response.body.data.id; // Save for later tests
  });

  it('should not create a vehicle with a duplicate VIN', async () => {
    // Ensure the first vehicle is created to test duplicate VIN
    await request(app)
      .post('/api/vehicles')
      .send({ ...newVehicleData, vin: 'DUPLICATEVINTEST' });

    const response = await request(app)
      .post('/api/vehicles')
      .send({ ...newVehicleData, vin: 'DUPLICATEVINTEST' }); // Using the exact same VIN

    expect(response.statusCode).toBe(409);
    expect(response.body.status).toBe('error');
    expect(response.body.code).toBe('CONFLICT');
    expect(response.body.message).toContain('already exists');
    expect(response.body.error).toBeDefined();
  });

  it('should get all vehicles via GET /api/vehicles', async () => {
    // Create one vehicle if none (previous test might have created one)
    await request(app)
      .post('/api/vehicles')
      .send({ ...newVehicleData, vin: `ANOTHERVINGETALL${Date.now()}` });

    const response = await request(app).get('/api/vehicles');
    expect(response.statusCode).toBe(200);
    expect(response.body.status).toBe('success');
    expect(Array.isArray(response.body.data)).toBe(true);
    expect(response.body.data.length).toBeGreaterThanOrEqual(1);
    // Check if the created vehicle (or one of them) is in the list, by VIN as ID might change due to beforeEach
    expect(
      response.body.data.some(
        (v: any) =>
          v.vin ===
            `ANOTHERVINGETALL${Date.now() - 1 < Date.now() ? Date.now() : Date.now() - 1}` ||
          v.vin === newVehicleData.vin,
      ),
    ).toBe(true);
  });

  it('should get a specific vehicle by ID via GET /api/vehicles/:id', async () => {
    // Create a vehicle to fetch
    const creationResponse = await request(app)
      .post('/api/vehicles')
      .send({ ...newVehicleData, vin: `FETCHBYIDVIN${Date.now()}` });
    const vehicleIdToFetch = creationResponse.body.data.id;

    const response = await request(app).get(`/api/vehicles/${vehicleIdToFetch}`);
    expect(response.statusCode).toBe(200);
    expect(response.body.status).toBe('success');
    expect(response.body.data).toHaveProperty('id', vehicleIdToFetch);
    expect(response.body.data.vin).toBe(
      `FETCHBYIDVIN${Date.now() - 1 < Date.now() ? Date.now() : Date.now() - 1}`,
    );
  });

  it('should return 404 for a non-existent vehicle ID', async () => {
    const response = await request(app).get('/api/vehicles/999999'); // Assuming 999999 does not exist
    expect(response.statusCode).toBe(404);
    expect(response.body.status).toBe('error');
    expect(response.body.code).toBe('NOT_FOUND');
    expect(response.body.message).toContain('not found');
  });

  it('should update a vehicle via PUT /api/vehicles/:id', async () => {
    const creationResponse = await request(app)
      .post('/api/vehicles')
      .send({ ...newVehicleData, vin: `UPDATEVIN${Date.now()}` });
    const vehicleIdToUpdate = creationResponse.body.data.id;

    const updates = {
      ownerName: 'Updated Test Owner',
      year: 2024,
    };
    const response = await request(app).put(`/api/vehicles/${vehicleIdToUpdate}`).send(updates);

    expect(response.statusCode).toBe(200);
    expect(response.body.status).toBe('success');
    expect(response.body.data.ownerName).toBe(updates.ownerName);
    expect(response.body.data.year).toBe(updates.year);
  });

  it('should delete a vehicle via DELETE /api/vehicles/:id', async () => {
    const creationResponse = await request(app)
      .post('/api/vehicles')
      .send({ ...newVehicleData, vin: `DELETEVIN${Date.now()}` });
    const vehicleIdToDelete = creationResponse.body.data.id;

    const response = await request(app).delete(`/api/vehicles/${vehicleIdToDelete}`);
    expect(response.statusCode).toBe(200);
    expect(response.body.status).toBe('success');
    expect(response.body.data.message).toBe('Vehicle deleted successfully');

    // Verify it's actually deleted
    const getResponse = await request(app).get(`/api/vehicles/${vehicleIdToDelete}`);
    expect(getResponse.statusCode).toBe(404);
  });
});
