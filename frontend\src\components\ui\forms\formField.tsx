import React from 'react';
import { Controller, useFormContext } from 'react-hook-form';

// Import shadcn/ui form components
import {
  FormControl,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

export interface SelectOption {
  value: string | number;
  label: string;
  disabled?: boolean;
}

export interface FormFieldProps {
  // Allow additional props for input elements, intentionally using any for flexibility
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [key: string]: any;
  className?: string;
  disabled?: boolean;
  label: string;
  name: string;
  placeholder?: string;
  render?: any; // Extract render prop explicitly
  type?: string;
  options?: SelectOption[]; // For select fields
  defaultValue?: string | number; // For select fields
  icon?: React.ComponentType<any>; // For icon support (Lucide icons)
}

export const FormField = ({
  className = '',
  disabled = false,
  label,
  name,
  placeholder,
  render,
  type = 'text',
  options = [],
  defaultValue,
  icon: Icon,
  ...props
}: FormFieldProps) => {
  const { control } = useFormContext();

  return (
    <FormItem className={className}>
      <FormLabel htmlFor={name}>{label}</FormLabel>
      <Controller
        control={control}
        name={name}
        render={
          render ||
          (({ field, fieldState: { error } }) => (
            <FormControl>
              {type === 'select' ? (
                <Select
                  onValueChange={field.onChange}
                  value={field.value || defaultValue || ''}
                  disabled={disabled}
                >
                  <SelectTrigger className={error ? 'border-red-500' : ''}>
                    <div className="flex items-center gap-2">
                      {Icon && <Icon className="h-4 w-4 text-gray-500" />}
                      <SelectValue
                        placeholder={
                          placeholder || `Select ${label.toLowerCase()}`
                        }
                      />
                    </div>
                  </SelectTrigger>
                  <SelectContent>
                    {options.map(option => (
                      <SelectItem
                        key={option.value}
                        value={String(option.value)}
                        disabled={option.disabled || false}
                      >
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              ) : type === 'textarea' ? (
                <div className="relative">
                  {Icon && (
                    <Icon className="absolute left-3 top-3 h-4 w-4 text-gray-500" />
                  )}
                  <Textarea
                    {...field}
                    {...props}
                    value={field.value ?? ''} // Ensure value is never null/undefined
                    className={`${error ? 'border-red-500' : ''} ${Icon ? 'pl-10' : ''}`}
                    disabled={disabled}
                    id={name}
                    placeholder={placeholder}
                  />
                </div>
              ) : (
                <div className="relative">
                  {Icon && (
                    <Icon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500" />
                  )}
                  <Input
                    {...field}
                    {...props}
                    value={field.value ?? ''} // Ensure value is never null/undefined
                    className={`${error ? 'border-red-500' : ''} ${Icon ? 'pl-10' : ''}`}
                    disabled={disabled}
                    id={name}
                    placeholder={placeholder}
                    type={type}
                  />
                </div>
              )}
            </FormControl>
          ))
        }
      />
      <FormMessage />
    </FormItem>
  );
};

export default FormField;
