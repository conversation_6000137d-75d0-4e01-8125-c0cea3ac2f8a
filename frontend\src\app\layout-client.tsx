/**
 * @file Client Layout Component with CSP Support
 * @module app/layout-client
 *
 * Client component containing the original layout logic with CSP nonce support.
 * Follows 2025 security standards for script loading and CSP compliance.
 */

'use client';

import type { ReactNode } from 'react';
import { QueryClientProvider } from '@tanstack/react-query';
import { usePathname } from 'next/navigation';
import Script from 'next/script';

import { AuthProvider } from '@/contexts/AuthContext';
import { ProtectedRoute } from '@/components/auth';
import { AppLayout } from '@/components/layout/AppLayout';
import Navbar from '@/components/global/Navbar';
import { SettingsModal } from '@/components/settings/SettingsModal';
import { ThemeProvider } from '@/components/theme-provider';
import { NotificationDisplay } from '@/components/ui/NotificationDisplay';
import { Toaster } from '@/components/ui/toaster';
import { QuickAccessFab } from '@/components/ui/QuickAccessFab';
import { useUiPreferences } from '@/hooks/ui/useUiPreferences';
import { queryClient } from '@/lib/stores/queryClient';
import { useAuthContext } from '@/contexts/AuthContext';
import { prefetchUtils } from '@/lib/stores/queryClient';
import { SecurityConfigProvider } from '@/lib/api/security';
// Debug components imports removed for cleaner UI
// import { TokenRefreshDebug } from '@/components/debug/TokenRefreshDebug';
// import { EnhancedCSPDebug } from '@/components/debug/EnhancedCSPDebug';
import {
  useNonce,
  initializeCSPViolationReporting,
  useCSPReporting,
} from '@/lib/security/CSPProvider';
import { useEffect, useMemo } from 'react';

interface ClientLayoutProps {
  children: ReactNode;
}

/**
 * Client Layout Component
 *
 * Contains all client-side layout logic with CSP nonce support.
 * Single responsibility: Client-side layout rendering and CSP integration.
 */
export default function ClientLayout({ children }: ClientLayoutProps) {
  const nonce = useNonce();
  const reportViolation = useCSPReporting();

  // Initialize CSP violation reporting
  useEffect(() => {
    initializeCSPViolationReporting(reportViolation);
  }, [reportViolation]);

  return (
    <ThemeProvider
      attribute="class"
      defaultTheme="system"
      disableTransitionOnChange
      enableSystem
    >
      <QueryClientProvider client={queryClient}>
        <AuthProvider>
          <SecurityConfigProvider>
            <ProtectedLayoutWrapper>{children}</ProtectedLayoutWrapper>
          </SecurityConfigProvider>
        </AuthProvider>

        {/* Notification display */}
        <NotificationDisplay />

        {/* Settings Modal */}
        <SettingsModal />

        {/* Quick Access FAB for Reporting Dashboard */}
        <QuickAccessFab />

        {/* Debug components hidden for cleaner UI */}
        {/* <TokenRefreshDebug /> */}
        {/* <EnhancedCSPDebug /> */}
      </QueryClientProvider>
    </ThemeProvider>
  );
}

/**
 * Protected Layout Wrapper Component
 *
 * This component determines which routes require authentication.
 * Public routes (like auth-test) are excluded from protection.
 * Also handles initial data prefetching when authentication is ready.
 */
function ProtectedLayoutWrapper({ children }: { children: ReactNode }) {
  const pathname = usePathname();
  const { isInitialized, loading, user } = useAuthContext();

  // Get UI preferences from Zustand stores (but not theme to avoid hydration issues)
  const { getFontSizeClass } = useUiPreferences();

  // Define public routes that don't require authentication
  const publicRoutes = ['/auth-test', '/supabase-diagnostics', '/login'];

  // Define auth routes that should have no layout wrapper
  const authRoutes = ['/login'];

  // Check if current route is public
  const isPublicRoute = publicRoutes.some(route => pathname?.startsWith(route));

  // Check if current route is an auth route (no layout needed)
  const isAuthRoute = authRoutes.some(route => pathname?.startsWith(route));

  // Determine if authentication system is ready for API calls
  // CRITICAL: Must include user check to prevent API calls before authentication
  // MEMOIZED to prevent infinite re-renders caused by user object reference changes
  const isAuthReady = useMemo(() => {
    return isInitialized && !loading && !!user;
  }, [isInitialized, loading, user?.id]); // Use user.id instead of user object to prevent reference issues

  // Trigger initial data prefetching when authentication is ready and on dashboard
  useEffect(() => {
    if (isAuthReady && pathname === '/') {
      console.log('Authentication ready, triggering dashboard data prefetch.');
      prefetchUtils.prefetchDashboardData(isAuthReady).catch(error => {
        console.warn('Failed to prefetch dashboard data:', error);
      });
    }
  }, [isAuthReady, pathname]);

  // If it's an auth route, render without any layout wrapper
  if (isAuthRoute) {
    return (
      <>
        {children}
        <Toaster />
      </>
    );
  }

  // If it's a public route, render without protection but with layout
  if (isPublicRoute) {
    return (
      <div className={`app-layout ${getFontSizeClass()}`}>
        <AppLayout>{children}</AppLayout>
        <Toaster />
        <footer className="no-print border-t border-border bg-card py-4 text-center text-sm text-card-foreground">
          <p>&copy; {new Date().getFullYear()} WorkHub. All rights reserved.</p>
        </footer>
      </div>
    );
  }

  // For all other routes, require authentication
  return (
    <div className={`app-layout ${getFontSizeClass()}`}>
      <ProtectedRoute requireEmailVerification={true}>
        <AppLayout>{children}</AppLayout>
        <Toaster />
        <footer className="no-print border-t border-border bg-card py-4 text-center text-sm text-card-foreground">
          <p>&copy; {new Date().getFullYear()} WorkHub. All rights reserved.</p>
        </footer>
      </ProtectedRoute>
    </div>
  );
}
