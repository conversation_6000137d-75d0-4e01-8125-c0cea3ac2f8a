{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/i18n/config.ts"], "sourcesContent": ["/**\n * @file i18n Configuration\n * @module i18n/config\n *\n * Central configuration for internationalization settings.\n * Defines supported locales, default locale, and locale-specific settings.\n */\n\n/**\n * Supported locales in the application\n */\nexport const locales = ['en-US', 'ar-IQ'] as const;\n\n/**\n * Default locale for the application\n */\nexport const defaultLocale = 'en-US' as const;\n\n/**\n * Type for supported locales\n */\nexport type Locale = (typeof locales)[number];\n\n/**\n * RTL (Right-to-Left) locales\n */\nexport const rtlLocales = ['ar-IQ'] as const;\n\n/**\n * Check if a locale is RTL\n */\nexport function isRTLLocale(locale: string): boolean {\n  return rtlLocales.includes(locale as any);\n}\n\n/**\n * Locale display names for UI\n */\nexport const localeNames: Record<Locale, string> = {\n  'en-US': 'English',\n  'ar-IQ': 'العربية',\n};\n\n/**\n * Locale configuration for next-intl\n */\nexport const localeConfig = {\n  locales,\n  defaultLocale,\n  localePrefix: 'always' as const,\n};\n"], "names": [], "mappings": "AAAA;;;;;;CAMC,GAED;;CAEC;;;;;;;;AACM,MAAM,UAAU;IAAC;IAAS;CAAQ;AAKlC,MAAM,gBAAgB;AAUtB,MAAM,aAAa;IAAC;CAAQ;AAK5B,SAAS,YAAY,MAAc;IACxC,OAAO,WAAW,QAAQ,CAAC;AAC7B;AAKO,MAAM,cAAsC;IACjD,SAAS;IACT,SAAS;AACX;AAKO,MAAM,eAAe;IAC1B;IACA;IACA,cAAc;AAChB"}}, {"offset": {"line": 65, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/lib/security"], "sourcesContent": ["__turbopack_context__.n(__import_unsupported(`crypto`));\n"], "names": [], "mappings": "AAAA,sBAAsB,CAAC,CAAC,qBAAqB,CAAC,MAAM,CAAC"}}, {"offset": {"line": 72, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/lib/security/cspConfig.ts"], "sourcesContent": ["/**\r\n * PHASE 3 SECURITY HARDENING: Enhanced CSP Configuration - 2025 Best Practices\r\n *\r\n * Implements strict Content Security Policy with:\r\n * - Nonce-based script execution\r\n * - strict-dynamic for trusted scripts\r\n * - Comprehensive security directives\r\n * - CSP violation reporting\r\n * - Supply chain security for third-party scripts\r\n * - Enhanced monitoring and alerting\r\n */\r\n\r\nexport interface CSPConfig {\r\n  nonce: string;\r\n  reportUri?: string;\r\n  isDevelopment: boolean;\r\n}\r\n\r\n/**\r\n * Generate strict CSP policy following 2025 best practices\r\n */\r\nexport function generateStrictCSP(config: CSPConfig): string {\r\n  const { nonce, reportUri, isDevelopment } = config;\r\n\r\n  // Base strict CSP directives\r\n  const directives: Record<string, string[]> = {\r\n    // Script sources - strict nonce-based approach\r\n    'script-src': [\r\n      \"'self'\",\r\n      `'nonce-${nonce}'`,\r\n      \"'strict-dynamic'\",\r\n      // Allow specific trusted domains for production\r\n      ...(isDevelopment\r\n        ? []\r\n        : ['https://cdn.jsdelivr.net', 'https://unpkg.com']),\r\n    ],\r\n\r\n    // Style sources - nonce-based with fallbacks\r\n    'style-src': [\r\n      \"'self'\",\r\n      `'nonce-${nonce}'`,\r\n      // Allow inline styles for CSS-in-JS libraries in development\r\n      ...(isDevelopment ? [\"'unsafe-inline'\"] : []),\r\n      // Trusted style CDNs\r\n      'https://fonts.googleapis.com',\r\n      'https://cdn.jsdelivr.net',\r\n    ],\r\n\r\n    // Font sources\r\n    'font-src': [\r\n      \"'self'\",\r\n      'https://fonts.gstatic.com',\r\n      'https://cdn.jsdelivr.net',\r\n      'data:',\r\n    ],\r\n\r\n    // Image sources\r\n    'img-src': [\r\n      \"'self'\",\r\n      'data:',\r\n      'blob:',\r\n      'https:',\r\n      // Supabase storage\r\n      'https://*.supabase.co',\r\n      // Common image CDNs\r\n      'https://images.unsplash.com',\r\n      'https://via.placeholder.com',\r\n    ],\r\n\r\n    // Connect sources for API calls\r\n    'connect-src': [\r\n      \"'self'\",\r\n      // Supabase endpoints\r\n      'https://*.supabase.co',\r\n      'wss://*.supabase.co',\r\n      // Development WebSocket\r\n      ...(isDevelopment ? ['ws://localhost:*', 'wss://localhost:*'] : []),\r\n      // Backend API endpoints (for development and Docker environments)\r\n      ...(isDevelopment || process.env.NEXT_PUBLIC_DOCKER_ENV === 'true'\r\n        ? ['http://localhost:3001', 'http://backend:3001']\r\n        : []),\r\n      // Additional connect sources from environment variable\r\n      ...(process.env.NEXT_PUBLIC_ALLOWED_CSP_CONNECT_SRC\r\n        ? process.env.NEXT_PUBLIC_ALLOWED_CSP_CONNECT_SRC.split(',')\r\n            .map(s => s.trim())\r\n            .filter(Boolean)\r\n        : []),\r\n      // API endpoints\r\n      'https://api.github.com',\r\n    ],\r\n\r\n    // Frame sources\r\n    'frame-src': [\r\n      \"'self'\",\r\n      // Allow specific trusted frames\r\n      'https://www.youtube.com',\r\n      'https://player.vimeo.com',\r\n    ],\r\n\r\n    // Object and embed restrictions\r\n    'object-src': [\"'none'\"],\r\n    'embed-src': [\"'none'\"],\r\n\r\n    // Base URI restriction\r\n    'base-uri': [\"'self'\"],\r\n\r\n    // Form action restriction\r\n    'form-action': [\"'self'\"],\r\n\r\n    // Frame ancestors (clickjacking protection)\r\n    'frame-ancestors': [\"'none'\"],\r\n\r\n    // Block mixed content\r\n    'block-all-mixed-content': [],\r\n\r\n    // Default fallback\r\n    'default-src': [\"'self'\"],\r\n  };\r\n\r\n  // Add upgrade insecure requests in production\r\n  if (!isDevelopment) {\r\n    directives['upgrade-insecure-requests'] = [];\r\n  }\r\n\r\n  // Add reporting if configured\r\n  if (reportUri) {\r\n    directives['report-uri'] = [reportUri];\r\n    directives['report-to'] = ['csp-endpoint'];\r\n  }\r\n\r\n  // Convert directives to CSP string\r\n  return Object.entries(directives)\r\n    .map(([directive, sources]) => {\r\n      if (sources.length === 0) {\r\n        return directive;\r\n      }\r\n      return `${directive} ${sources.join(' ')}`;\r\n    })\r\n    .join('; ');\r\n}\r\n\r\n/**\r\n * Generate additional security headers following 2025 best practices\r\n */\r\nexport function generateSecurityHeaders(): Record<string, string> {\r\n  return {\r\n    // Strict Transport Security\r\n    'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload',\r\n\r\n    // X-Frame-Options (backup for frame-ancestors)\r\n    'X-Frame-Options': 'DENY',\r\n\r\n    // X-Content-Type-Options\r\n    'X-Content-Type-Options': 'nosniff',\r\n\r\n    // Referrer Policy\r\n    'Referrer-Policy': 'strict-origin-when-cross-origin',\r\n\r\n    // X-XSS-Protection (legacy browsers)\r\n    'X-XSS-Protection': '1; mode=block',\r\n\r\n    // Permissions Policy (formerly Feature Policy)\r\n    'Permissions-Policy': [\r\n      'camera=()',\r\n      'microphone=()',\r\n      'geolocation=()',\r\n      'payment=()',\r\n      'usb=()',\r\n      'magnetometer=()',\r\n      'accelerometer=()',\r\n      'gyroscope=()',\r\n    ].join(', '),\r\n\r\n    // Cross-Origin Policies\r\n    'Cross-Origin-Embedder-Policy': 'require-corp',\r\n    'Cross-Origin-Opener-Policy': 'same-origin',\r\n    'Cross-Origin-Resource-Policy': 'same-origin',\r\n  };\r\n}\r\n\r\n/**\r\n * CSP Reporting configuration\r\n */\r\nexport function generateCSPReportingConfig() {\r\n  return {\r\n    group: 'csp-endpoint',\r\n    max_age: 10886400, // 126 days\r\n    endpoints: [\r\n      {\r\n        url: '/api/csp-report',\r\n        priority: 1,\r\n        weight: 1,\r\n      },\r\n    ],\r\n  };\r\n}\r\n\r\n/**\r\n * PHASE 3 SECURITY HARDENING: Supply Chain Security for Third-Party Scripts\r\n * Validates and manages trusted third-party script sources\r\n */\r\nexport interface TrustedScript {\r\n  url: string;\r\n  integrity: string;\r\n  crossorigin: 'anonymous' | 'use-credentials';\r\n  purpose: string;\r\n  lastVerified: string;\r\n}\r\n\r\n/**\r\n * Trusted third-party scripts with SRI hashes\r\n * SECURITY: All third-party scripts must have integrity hashes\r\n */\r\nexport const TRUSTED_SCRIPTS: TrustedScript[] = [\r\n  {\r\n    url: 'https://cdn.jsdelivr.net/npm/react@18.2.0/umd/react.production.min.js',\r\n    integrity:\r\n      'sha384-/bQdsTh/da6pkI1MST/rWKFNjaCP5gBSY4sEBT38Q/9RBh9AH40zEOg7Hlq2THRZ',\r\n    crossorigin: 'anonymous',\r\n    purpose: 'React library for production builds',\r\n    lastVerified: '2025-01-24',\r\n  },\r\n  // Add more trusted scripts as needed\r\n];\r\n\r\n/**\r\n * Validate third-party script integrity\r\n */\r\nexport function validateScriptIntegrity(\r\n  url: string,\r\n  expectedIntegrity: string\r\n): boolean {\r\n  const trustedScript = TRUSTED_SCRIPTS.find(script => script.url === url);\r\n  return trustedScript?.integrity === expectedIntegrity;\r\n}\r\n\r\n/**\r\n * Generate Subresource Integrity (SRI) policy\r\n */\r\nexport function generateSRIPolicy(): string {\r\n  return TRUSTED_SCRIPTS.map(script => `'${script.integrity}'`).join(' ');\r\n}\r\n\r\n/**\r\n * Validate nonce format and strength\r\n * PHASE 3 ENHANCEMENT: Enhanced nonce validation with entropy check\r\n */\r\nexport function validateNonce(nonce: string): boolean {\r\n  // Nonce should be at least 16 characters, base64 encoded\r\n  const base64Regex = /^[A-Za-z0-9+/]+=*$/;\r\n  const hasMinLength = nonce.length >= 16;\r\n  const isValidBase64 = base64Regex.test(nonce);\r\n\r\n  // PHASE 3: Check for sufficient entropy (no repeated patterns)\r\n  const hasEntropy = !/(.)\\1{3,}/.test(nonce); // No character repeated 4+ times\r\n\r\n  return hasMinLength && isValidBase64 && hasEntropy;\r\n}\r\n\r\n/**\r\n * PHASE 3: CSP Violation Analysis and Reporting\r\n */\r\nexport interface CSPViolation {\r\n  documentUri: string;\r\n  violatedDirective: string;\r\n  blockedUri: string;\r\n  effectiveDirective: string;\r\n  originalPolicy: string;\r\n  sourceFile?: string;\r\n  lineNumber?: number;\r\n  columnNumber?: number;\r\n  timestamp: string;\r\n  userAgent: string;\r\n  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';\r\n}\r\n\r\n/**\r\n * Analyze CSP violation risk level\r\n */\r\nexport function analyzeCSPViolationRisk(\r\n  violation: CSPViolation\r\n): 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' {\r\n  const { violatedDirective, blockedUri } = violation;\r\n\r\n  // Critical: Script injection attempts\r\n  if (\r\n    violatedDirective.includes('script-src') &&\r\n    (blockedUri.includes('javascript:') ||\r\n      blockedUri.includes('data:') ||\r\n      blockedUri.includes('blob:'))\r\n  ) {\r\n    return 'CRITICAL';\r\n  }\r\n\r\n  // High: External script loading from untrusted domains\r\n  if (\r\n    violatedDirective.includes('script-src') &&\r\n    !TRUSTED_SCRIPTS.some(script =>\r\n      blockedUri.startsWith(script.url.split('/').slice(0, 3).join('/'))\r\n    )\r\n  ) {\r\n    return 'HIGH';\r\n  }\r\n\r\n  // Medium: Style or image violations\r\n  if (\r\n    violatedDirective.includes('style-src') ||\r\n    violatedDirective.includes('img-src')\r\n  ) {\r\n    return 'MEDIUM';\r\n  }\r\n\r\n  return 'LOW';\r\n}\r\n\r\n/**\r\n * Generate cryptographically secure nonce\r\n */\r\nexport function generateSecureNonce(): string {\r\n  if (typeof crypto !== 'undefined' && crypto.getRandomValues) {\r\n    // Browser environment\r\n    const array = new Uint8Array(24); // 192 bits\r\n    crypto.getRandomValues(array);\r\n    return btoa(String.fromCharCode(...array));\r\n  } else {\r\n    // Node.js environment\r\n    const crypto = require('crypto');\r\n    return crypto.randomBytes(24).toString('base64');\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;;;;CAUC;;;;;;;;;;;AAWM,SAAS,kBAAkB,MAAiB;IACjD,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,aAAa,EAAE,GAAG;IAE5C,6BAA6B;IAC7B,MAAM,aAAuC;QAC3C,+CAA+C;QAC/C,cAAc;YACZ;YACA,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;YAClB;YACA,gDAAgD;eAC5C,gBACA,EAAE,GACF;gBAAC;gBAA4B;aAAoB;SACtD;QAED,6CAA6C;QAC7C,aAAa;YACX;YACA,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;YAClB,6DAA6D;eACzD,gBAAgB;gBAAC;aAAkB,GAAG,EAAE;YAC5C,qBAAqB;YACrB;YACA;SACD;QAED,eAAe;QACf,YAAY;YACV;YACA;YACA;YACA;SACD;QAED,gBAAgB;QAChB,WAAW;YACT;YACA;YACA;YACA;YACA,mBAAmB;YACnB;YACA,oBAAoB;YACpB;YACA;SACD;QAED,gCAAgC;QAChC,eAAe;YACb;YACA,qBAAqB;YACrB;YACA;YACA,wBAAwB;eACpB,gBAAgB;gBAAC;gBAAoB;aAAoB,GAAG,EAAE;YAClE,kEAAkE;eAC9D,iBAAiB,8CAAuC,SACxD;gBAAC;gBAAyB;aAAsB,GAChD,EAAE;YACN,uDAAuD;eACnD,uCACA,+DAAgD,KAAK,CAAC,KACnD,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI,IACf,MAAM,CAAC;YAEd,gBAAgB;YAChB;SACD;QAED,gBAAgB;QAChB,aAAa;YACX;YACA,gCAAgC;YAChC;YACA;SACD;QAED,gCAAgC;QAChC,cAAc;YAAC;SAAS;QACxB,aAAa;YAAC;SAAS;QAEvB,uBAAuB;QACvB,YAAY;YAAC;SAAS;QAEtB,0BAA0B;QAC1B,eAAe;YAAC;SAAS;QAEzB,4CAA4C;QAC5C,mBAAmB;YAAC;SAAS;QAE7B,sBAAsB;QACtB,2BAA2B,EAAE;QAE7B,mBAAmB;QACnB,eAAe;YAAC;SAAS;IAC3B;IAEA,8CAA8C;IAC9C,IAAI,CAAC,eAAe;QAClB,UAAU,CAAC,4BAA4B,GAAG,EAAE;IAC9C;IAEA,8BAA8B;IAC9B,IAAI,WAAW;QACb,UAAU,CAAC,aAAa,GAAG;YAAC;SAAU;QACtC,UAAU,CAAC,YAAY,GAAG;YAAC;SAAe;IAC5C;IAEA,mCAAmC;IACnC,OAAO,OAAO,OAAO,CAAC,YACnB,GAAG,CAAC,CAAC,CAAC,WAAW,QAAQ;QACxB,IAAI,QAAQ,MAAM,KAAK,GAAG;YACxB,OAAO;QACT;QACA,OAAO,GAAG,UAAU,CAAC,EAAE,QAAQ,IAAI,CAAC,MAAM;IAC5C,GACC,IAAI,CAAC;AACV;AAKO,SAAS;IACd,OAAO;QACL,4BAA4B;QAC5B,6BAA6B;QAE7B,+CAA+C;QAC/C,mBAAmB;QAEnB,yBAAyB;QACzB,0BAA0B;QAE1B,kBAAkB;QAClB,mBAAmB;QAEnB,qCAAqC;QACrC,oBAAoB;QAEpB,+CAA+C;QAC/C,sBAAsB;YACpB;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD,CAAC,IAAI,CAAC;QAEP,wBAAwB;QACxB,gCAAgC;QAChC,8BAA8B;QAC9B,gCAAgC;IAClC;AACF;AAKO,SAAS;IACd,OAAO;QACL,OAAO;QACP,SAAS;QACT,WAAW;YACT;gBACE,KAAK;gBACL,UAAU;gBACV,QAAQ;YACV;SACD;IACH;AACF;AAkBO,MAAM,kBAAmC;IAC9C;QACE,KAAK;QACL,WACE;QACF,aAAa;QACb,SAAS;QACT,cAAc;IAChB;CAED;AAKM,SAAS,wBACd,GAAW,EACX,iBAAyB;IAEzB,MAAM,gBAAgB,gBAAgB,IAAI,CAAC,CAAA,SAAU,OAAO,GAAG,KAAK;IACpE,OAAO,eAAe,cAAc;AACtC;AAKO,SAAS;IACd,OAAO,gBAAgB,GAAG,CAAC,CAAA,SAAU,CAAC,CAAC,EAAE,OAAO,SAAS,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC;AACrE;AAMO,SAAS,cAAc,KAAa;IACzC,yDAAyD;IACzD,MAAM,cAAc;IACpB,MAAM,eAAe,MAAM,MAAM,IAAI;IACrC,MAAM,gBAAgB,YAAY,IAAI,CAAC;IAEvC,+DAA+D;IAC/D,MAAM,aAAa,CAAC,YAAY,IAAI,CAAC,QAAQ,iCAAiC;IAE9E,OAAO,gBAAgB,iBAAiB;AAC1C;AAsBO,SAAS,wBACd,SAAuB;IAEvB,MAAM,EAAE,iBAAiB,EAAE,UAAU,EAAE,GAAG;IAE1C,sCAAsC;IACtC,IACE,kBAAkB,QAAQ,CAAC,iBAC3B,CAAC,WAAW,QAAQ,CAAC,kBACnB,WAAW,QAAQ,CAAC,YACpB,WAAW,QAAQ,CAAC,QAAQ,GAC9B;QACA,OAAO;IACT;IAEA,uDAAuD;IACvD,IACE,kBAAkB,QAAQ,CAAC,iBAC3B,CAAC,gBAAgB,IAAI,CAAC,CAAA,SACpB,WAAW,UAAU,CAAC,OAAO,GAAG,CAAC,KAAK,CAAC,KAAK,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,QAE/D;QACA,OAAO;IACT;IAEA,oCAAoC;IACpC,IACE,kBAAkB,QAAQ,CAAC,gBAC3B,kBAAkB,QAAQ,CAAC,YAC3B;QACA,OAAO;IACT;IAEA,OAAO;AACT;AAKO,SAAS;IACd,IAAI,OAAO,WAAW,eAAe,OAAO,eAAe,EAAE;QAC3D,sBAAsB;QACtB,MAAM,QAAQ,IAAI,WAAW,KAAK,WAAW;QAC7C,OAAO,eAAe,CAAC;QACvB,OAAO,KAAK,OAAO,YAAY,IAAI;IACrC,OAAO;QACL,sBAAsB;QACtB,MAAM;QACN,OAAO,QAAO,WAAW,CAAC,IAAI,QAAQ,CAAC;IACzC;AACF"}}, {"offset": {"line": 315, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/middleware.ts"], "sourcesContent": ["import type { NextRequest } from 'next/server';\n\nimport createIntlMiddleware from 'next-intl/middleware';\nimport { NextResponse } from 'next/server';\n\nimport { defaultLocale, locales } from '@/i18n/config';\n// SECURITY FIX: Removed insecure 'decrypt' import\n// Session validation should be done server-side only with proper JWT validation\nimport {\n  generateSecurityHeaders,\n  generateStrictCSP,\n} from '@/lib/security/cspConfig';\n\n// Create the intl middleware\nconst intlMiddleware = createIntlMiddleware({\n  defaultLocale,\n  localePrefix: 'always',\n  locales,\n});\n\n// Protected routes that require authentication\nconst protectedRoutes = [\n  '/admin',\n  '/delegations',\n  '/tasks',\n  '/vehicles',\n  '/employees',\n  '/reports',\n  '/reporting', // Add new reporting routes\n  '/settings',\n  '/reliability', // Add reliability dashboard\n  '/profile', // Add profile page\n  '/gifts', // Add gift tracking routes\n  '/recipients',\n];\n\n// Public routes that don't require authentication\nconst publicRoutes = new Set([\n  '/',\n  '/forgot-password',\n  '/login',\n  '/reset-password',\n  '/signup',\n]);\n\n// Admin-only routes\nconst adminRoutes = ['/admin', '/settings/system', '/reports/admin'];\n\n// Define JWT payload interface for type safety\ninterface JWTPayload {\n  [key: string]: any;\n  exp?: number;\n  role?: string;\n  sub?: string;\n  user_role?: string;\n}\n\n/**\n * Main middleware function\n */\nexport async function middleware(request: NextRequest) {\n  // SECURITY FIX: Remove production logging to prevent information disclosure\n\n  // Skip our custom middleware for static files and API routes\n  if (\n    request.nextUrl.pathname.startsWith('/api/') ||\n    request.nextUrl.pathname.startsWith('/_next/') ||\n    request.nextUrl.pathname.startsWith('/favicon.ico')\n  ) {\n    // Still apply intl middleware for proper locale handling\n    return intlMiddleware(request);\n  }\n\n  // Handle i18n routing first\n  const intlResponse = intlMiddleware(request);\n\n  // If it's a redirect, return it immediately\n  if (intlResponse.status >= 300 && intlResponse.status < 400) {\n    return intlResponse;\n  }\n\n  // Generate nonce for CSP\n  const nonce = generateNonce();\n\n  // Handle CORS for API routes\n  const corsResponse = handleCORS(request);\n  if (corsResponse) {\n    return corsResponse;\n  }\n\n  // Handle authentication\n  const authResponse = await handleAuthentication(request);\n  if (authResponse) {\n    return authResponse;\n  }\n\n  // Use the intl response and add our security headers to it\n  const response = intlResponse;\n\n  // Set nonce in both headers and cookies for use in components\n  response.headers.set('x-nonce', nonce);\n  response.cookies.set('x-nonce', nonce, {\n    httpOnly: false, // Needs to be accessible on client side\n    path: '/',\n    sameSite: 'strict',\n    secure: process.env.NODE_ENV === 'production',\n  });\n\n  // Debug logging removed for production compliance\n\n  // Enhanced 2025 Security Headers with Strict CSP\n  const cspConfig = {\n    isDevelopment: process.env.NODE_ENV === 'development',\n    nonce,\n    reportUri: '/api/csp-report',\n  };\n\n  const securityHeaders = {\n    'Content-Security-Policy': generateStrictCSP(cspConfig),\n    ...generateSecurityHeaders(),\n    // 2025 Standard: Report-To header for modern violation reporting\n    'Report-To': JSON.stringify({\n      endpoints: [{ url: '/api/csp-report' }],\n      group: 'csp-endpoint',\n      max_age: 10_886_400,\n    }),\n  };\n\n  // Apply security headers\n  for (const [key, value] of Object.entries(securityHeaders)) {\n    response.headers.set(key, value);\n  }\n\n  // CORS headers for allowed origins\n  const origin = request.headers.get('origin') ?? '';\n  const allowedOrigins = [\n    'https://workhub.company.com',\n    'https://staging.workhub.company.com',\n    ...(process.env.NODE_ENV === 'development'\n      ? ['http://localhost:9002', 'http://localhost:3000']\n      : []),\n  ];\n\n  if (allowedOrigins.includes(origin)) {\n    response.headers.set('Access-Control-Allow-Origin', origin);\n    response.headers.set('Access-Control-Allow-Credentials', 'true');\n  }\n\n  return response;\n}\n\n/**\n * Enhanced CSP implementation now handled by cspConfig.ts\n * This provides strict 2025 security standards with comprehensive directives\n */\n\n/**\n * Generate cryptographically secure nonce\n */\nfunction generateNonce(): string {\n  // Generate 32 random bytes for a secure nonce\n  const bytes = new Uint8Array(32);\n  crypto.getRandomValues(bytes);\n  return Buffer.from(bytes).toString('base64');\n}\n\n/**\n * Authentication middleware\n */\nasync function handleAuthentication(\n  request: NextRequest\n): Promise<NextResponse | null> {\n  const path = request.nextUrl.pathname;\n\n  // Extract the path without locale prefix for route matching\n  const pathWithoutLocale = path.replace(/^\\/[a-z]{2}-[A-Z]{2}/, '') || '/';\n\n  const isProtectedRoute = protectedRoutes.some(route =>\n    pathWithoutLocale.startsWith(route)\n  );\n  const isPublicRoute = publicRoutes.has(pathWithoutLocale);\n  const isAdminRoute = adminRoutes.some(route =>\n    pathWithoutLocale.startsWith(route)\n  );\n\n  // Skip auth for public routes and API routes\n  if (isPublicRoute || path.startsWith('/api/') || path.startsWith('/_next/')) {\n    return null;\n  }\n\n  // SECURITY FIX: Use proper JWT token validation instead of insecure session decryption\n  // Get JWT token from secure httpOnly cookie (set by backend)\n  const accessToken = request.cookies.get('sb-access-token')?.value;\n\n  if (!accessToken && isProtectedRoute) {\n    // Extract locale from path for proper redirect\n    const localeMatch = path.match(/^\\/([a-z]{2}-[A-Z]{2})/);\n    const locale = localeMatch ? localeMatch[1] : defaultLocale;\n    const loginUrl = new URL(`/${locale}/login`, request.url);\n    loginUrl.searchParams.set('redirect', path);\n    return NextResponse.redirect(loginUrl);\n  }\n\n  if (accessToken) {\n    try {\n      // Basic JWT format validation (3 parts separated by dots)\n      const tokenParts = accessToken.split('.');\n      if (tokenParts.length !== 3) {\n        throw new Error('Invalid JWT format');\n      }\n\n      // Decode JWT payload (this is safe as it's just base64 decoding for reading claims)\n      // Note: This does NOT validate the signature - that must be done server-side\n      const payloadPart = tokenParts[1];\n      if (!payloadPart) {\n        throw new Error('Invalid JWT payload');\n      }\n      const payload = JSON.parse(atob(payloadPart)) as JWTPayload;\n\n      // Check if token is expired\n      const now = Math.floor(Date.now() / 1000);\n      if (payload.exp && payload.exp < now) {\n        throw new Error('Token expired');\n      }\n\n      // Extract user role for admin route protection\n      const userRole = payload.user_role ?? payload.role ?? 'USER';\n\n      // Admin route protection\n      if (isAdminRoute && !['ADMIN', 'SUPER_ADMIN'].includes(userRole)) {\n        // Extract locale for proper redirect\n        const localeMatch = path.match(/^\\/([a-z]{2}-[A-Z]{2})/);\n        const locale = localeMatch ? localeMatch[1] : defaultLocale;\n        return NextResponse.redirect(\n          new URL(`/${locale}/unauthorized`, request.url)\n        );\n      }\n\n      // Redirect authenticated users from auth pages\n      if (\n        payload.sub &&\n        (pathWithoutLocale === '/login' || pathWithoutLocale === '/signup')\n      ) {\n        // Extract locale for proper redirect\n        const localeMatch = path.match(/^\\/([a-z]{2}-[A-Z]{2})/);\n        const locale = localeMatch ? localeMatch[1] : defaultLocale;\n        return NextResponse.redirect(new URL(`/${locale}`, request.url));\n      }\n    } catch {\n      // Invalid or expired token, redirect to login for protected routes\n      if (isProtectedRoute) {\n        // Extract locale for proper redirect\n        const localeMatch = path.match(/^\\/([a-z]{2}-[A-Z]{2})/);\n        const locale = localeMatch ? localeMatch[1] : defaultLocale;\n        const loginUrl = new URL(`/${locale}/login`, request.url);\n        loginUrl.searchParams.set('redirect', path);\n        return NextResponse.redirect(loginUrl);\n      }\n    }\n  }\n\n  return null;\n}\n\n/**\n * CORS middleware for API routes\n */\nfunction handleCORS(request: NextRequest): NextResponse | null {\n  // Only apply CORS to API routes\n  if (!request.nextUrl.pathname.startsWith('/api/')) {\n    return null;\n  }\n\n  const allowedOrigins = [\n    'https://workhub.company.com',\n    'https://staging.workhub.company.com',\n    ...(process.env.NODE_ENV === 'development'\n      ? ['http://localhost:9002', 'http://localhost:3000']\n      : []),\n  ];\n\n  const origin = request.headers.get('origin') ?? '';\n  const isAllowedOrigin = allowedOrigins.includes(origin);\n\n  // Handle preflight requests\n  if (request.method === 'OPTIONS') {\n    const preflightHeaders = {\n      'Access-Control-Allow-Credentials': 'true',\n      'Access-Control-Allow-Headers':\n        'Content-Type, Authorization, X-CSRF-Token, X-Requested-With',\n      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',\n      'Access-Control-Max-Age': '86400',\n      ...(isAllowedOrigin && { 'Access-Control-Allow-Origin': origin }),\n    };\n\n    return new NextResponse(null, { headers: preflightHeaders, status: 200 });\n  }\n\n  return null;\n}\n\n/**\n * Middleware configuration\n */\nexport const config = {\n  matcher: [\n    /*\n     * Match all request paths except for the ones starting with:\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     */\n    '/((?!_next/static|_next/image|favicon.ico).*)',\n  ],\n};\n"], "names": [], "mappings": ";;;;AAmKS;AAjKT;AACA;AAAA;AAEA;AACA,kDAAkD;AAClD,gFAAgF;AAChF;;;;;AAKA,6BAA6B;AAC7B,MAAM,iBAAiB,CAAA,GAAA,8LAAA,CAAA,UAAoB,AAAD,EAAE;IAC1C,eAAA,6HAAA,CAAA,gBAAa;IACb,cAAc;IACd,SAAA,6HAAA,CAAA,UAAO;AACT;AAEA,+CAA+C;AAC/C,MAAM,kBAAkB;IACtB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,kDAAkD;AAClD,MAAM,eAAe,IAAI,IAAI;IAC3B;IACA;IACA;IACA;IACA;CACD;AAED,oBAAoB;AACpB,MAAM,cAAc;IAAC;IAAU;IAAoB;CAAiB;AAc7D,eAAe,WAAW,OAAoB;IACnD,4EAA4E;IAE5E,6DAA6D;IAC7D,IACE,QAAQ,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,YACpC,QAAQ,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,cACpC,QAAQ,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,iBACpC;QACA,yDAAyD;QACzD,OAAO,eAAe;IACxB;IAEA,4BAA4B;IAC5B,MAAM,eAAe,eAAe;IAEpC,4CAA4C;IAC5C,IAAI,aAAa,MAAM,IAAI,OAAO,aAAa,MAAM,GAAG,KAAK;QAC3D,OAAO;IACT;IAEA,yBAAyB;IACzB,MAAM,QAAQ;IAEd,6BAA6B;IAC7B,MAAM,eAAe,WAAW;IAChC,IAAI,cAAc;QAChB,OAAO;IACT;IAEA,wBAAwB;IACxB,MAAM,eAAe,MAAM,qBAAqB;IAChD,IAAI,cAAc;QAChB,OAAO;IACT;IAEA,2DAA2D;IAC3D,MAAM,WAAW;IAEjB,8DAA8D;IAC9D,SAAS,OAAO,CAAC,GAAG,CAAC,WAAW;IAChC,SAAS,OAAO,CAAC,GAAG,CAAC,WAAW,OAAO;QACrC,UAAU;QACV,MAAM;QACN,UAAU;QACV,QAAQ,oDAAyB;IACnC;IAEA,kDAAkD;IAElD,iDAAiD;IACjD,MAAM,YAAY;QAChB,eAAe,oDAAyB;QACxC;QACA,WAAW;IACb;IAEA,MAAM,kBAAkB;QACtB,2BAA2B,CAAA,GAAA,2IAAA,CAAA,oBAAiB,AAAD,EAAE;QAC7C,GAAG,CAAA,GAAA,2IAAA,CAAA,0BAAuB,AAAD,GAAG;QAC5B,iEAAiE;QACjE,aAAa,KAAK,SAAS,CAAC;YAC1B,WAAW;gBAAC;oBAAE,KAAK;gBAAkB;aAAE;YACvC,OAAO;YACP,SAAS;QACX;IACF;IAEA,yBAAyB;IACzB,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,iBAAkB;QAC1D,SAAS,OAAO,CAAC,GAAG,CAAC,KAAK;IAC5B;IAEA,mCAAmC;IACnC,MAAM,SAAS,QAAQ,OAAO,CAAC,GAAG,CAAC,aAAa;IAChD,MAAM,iBAAiB;QACrB;QACA;WACI,uCACA;YAAC;YAAyB;SAAwB;KAEvD;IAED,IAAI,eAAe,QAAQ,CAAC,SAAS;QACnC,SAAS,OAAO,CAAC,GAAG,CAAC,+BAA+B;QACpD,SAAS,OAAO,CAAC,GAAG,CAAC,oCAAoC;IAC3D;IAEA,OAAO;AACT;AAEA;;;CAGC,GAED;;CAEC,GACD,SAAS;IACP,8CAA8C;IAC9C,MAAM,QAAQ,IAAI,WAAW;IAC7B,OAAO,eAAe,CAAC;IACvB,OAAO,qHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,OAAO,QAAQ,CAAC;AACrC;AAEA;;CAEC,GACD,eAAe,qBACb,OAAoB;IAEpB,MAAM,OAAO,QAAQ,OAAO,CAAC,QAAQ;IAErC,4DAA4D;IAC5D,MAAM,oBAAoB,KAAK,OAAO,CAAC,wBAAwB,OAAO;IAEtE,MAAM,mBAAmB,gBAAgB,IAAI,CAAC,CAAA,QAC5C,kBAAkB,UAAU,CAAC;IAE/B,MAAM,gBAAgB,aAAa,GAAG,CAAC;IACvC,MAAM,eAAe,YAAY,IAAI,CAAC,CAAA,QACpC,kBAAkB,UAAU,CAAC;IAG/B,6CAA6C;IAC7C,IAAI,iBAAiB,KAAK,UAAU,CAAC,YAAY,KAAK,UAAU,CAAC,YAAY;QAC3E,OAAO;IACT;IAEA,uFAAuF;IACvF,6DAA6D;IAC7D,MAAM,cAAc,QAAQ,OAAO,CAAC,GAAG,CAAC,oBAAoB;IAE5D,IAAI,CAAC,eAAe,kBAAkB;QACpC,+CAA+C;QAC/C,MAAM,cAAc,KAAK,KAAK,CAAC;QAC/B,MAAM,SAAS,cAAc,WAAW,CAAC,EAAE,GAAG,6HAAA,CAAA,gBAAa;QAC3D,MAAM,WAAW,IAAI,IAAI,CAAC,CAAC,EAAE,OAAO,MAAM,CAAC,EAAE,QAAQ,GAAG;QACxD,SAAS,YAAY,CAAC,GAAG,CAAC,YAAY;QACtC,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;IAC/B;IAEA,IAAI,aAAa;QACf,IAAI;YACF,0DAA0D;YAC1D,MAAM,aAAa,YAAY,KAAK,CAAC;YACrC,IAAI,WAAW,MAAM,KAAK,GAAG;gBAC3B,MAAM,IAAI,MAAM;YAClB;YAEA,oFAAoF;YACpF,6EAA6E;YAC7E,MAAM,cAAc,UAAU,CAAC,EAAE;YACjC,IAAI,CAAC,aAAa;gBAChB,MAAM,IAAI,MAAM;YAClB;YACA,MAAM,UAAU,KAAK,KAAK,CAAC,KAAK;YAEhC,4BAA4B;YAC5B,MAAM,MAAM,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK;YACpC,IAAI,QAAQ,GAAG,IAAI,QAAQ,GAAG,GAAG,KAAK;gBACpC,MAAM,IAAI,MAAM;YAClB;YAEA,+CAA+C;YAC/C,MAAM,WAAW,QAAQ,SAAS,IAAI,QAAQ,IAAI,IAAI;YAEtD,yBAAyB;YACzB,IAAI,gBAAgB,CAAC;gBAAC;gBAAS;aAAc,CAAC,QAAQ,CAAC,WAAW;gBAChE,qCAAqC;gBACrC,MAAM,cAAc,KAAK,KAAK,CAAC;gBAC/B,MAAM,SAAS,cAAc,WAAW,CAAC,EAAE,GAAG,6HAAA,CAAA,gBAAa;gBAC3D,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAC1B,IAAI,IAAI,CAAC,CAAC,EAAE,OAAO,aAAa,CAAC,EAAE,QAAQ,GAAG;YAElD;YAEA,+CAA+C;YAC/C,IACE,QAAQ,GAAG,IACX,CAAC,sBAAsB,YAAY,sBAAsB,SAAS,GAClE;gBACA,qCAAqC;gBACrC,MAAM,cAAc,KAAK,KAAK,CAAC;gBAC/B,MAAM,SAAS,cAAc,WAAW,CAAC,EAAE,GAAG,6HAAA,CAAA,gBAAa;gBAC3D,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,CAAC,EAAE,QAAQ,EAAE,QAAQ,GAAG;YAChE;QACF,EAAE,OAAM;YACN,mEAAmE;YACnE,IAAI,kBAAkB;gBACpB,qCAAqC;gBACrC,MAAM,cAAc,KAAK,KAAK,CAAC;gBAC/B,MAAM,SAAS,cAAc,WAAW,CAAC,EAAE,GAAG,6HAAA,CAAA,gBAAa;gBAC3D,MAAM,WAAW,IAAI,IAAI,CAAC,CAAC,EAAE,OAAO,MAAM,CAAC,EAAE,QAAQ,GAAG;gBACxD,SAAS,YAAY,CAAC,GAAG,CAAC,YAAY;gBACtC,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;YAC/B;QACF;IACF;IAEA,OAAO;AACT;AAEA;;CAEC,GACD,SAAS,WAAW,OAAoB;IACtC,gCAAgC;IAChC,IAAI,CAAC,QAAQ,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,UAAU;QACjD,OAAO;IACT;IAEA,MAAM,iBAAiB;QACrB;QACA;WACI,uCACA;YAAC;YAAyB;SAAwB;KAEvD;IAED,MAAM,SAAS,QAAQ,OAAO,CAAC,GAAG,CAAC,aAAa;IAChD,MAAM,kBAAkB,eAAe,QAAQ,CAAC;IAEhD,4BAA4B;IAC5B,IAAI,QAAQ,MAAM,KAAK,WAAW;QAChC,MAAM,mBAAmB;YACvB,oCAAoC;YACpC,gCACE;YACF,gCAAgC;YAChC,0BAA0B;YAC1B,GAAI,mBAAmB;gBAAE,+BAA+B;YAAO,CAAC;QAClE;QAEA,OAAO,IAAI,6LAAA,CAAA,eAAY,CAAC,MAAM;YAAE,SAAS;YAAkB,QAAQ;QAAI;IACzE;IAEA,OAAO;AACT;AAKO,MAAM,SAAS;IACpB,SAAS;QACP;;;;;KAKC,GACD;KACD;AACH"}}]}