# 🔐 WorkHub Authentication System - Quick Reference

**Document Version:** 1.0  
**Date:** January 2025  
**Status:** ✅ **ACTIVE** - Developer Quick Reference  
**Last Updated:** January 2025

---

## 📋 **Quick Start Guide**

### **Basic Authentication Usage**

```typescript
// 1. Check if user is authenticated
import { useAuthContext } from '@/contexts/AuthContext';

function MyComponent() {
  const { user, loading, isInitialized } = useAuthContext();
  
  if (!isInitialized || loading) return <LoadingSpinner />;
  if (!user) return <LoginRequired />;
  
  return <AuthenticatedContent />;
}

// 2. Make authenticated API calls
import { reliabilityApiService } from '@/lib/api/services';

const data = await reliabilityApiService.getSystemHealth();

// 3. Protect routes with roles
<ProtectedRoute allowedRoles={['ADMIN', 'MANAGER']}>
  <AdminDashboard />
</ProtectedRoute>
```

---

## 🔧 **Common Patterns**

### **Authentication Patterns**

| Pattern | Use Case | Example |
|---------|----------|---------|
| **Basic Auth Check** | Simple authentication | `if (!user) return <Login />` |
| **Role-Based Access** | Admin features | `if (user?.role === 'ADMIN')` |
| **Protected Routes** | Page-level protection | `<ProtectedRoute>` |
| **Conditional Rendering** | Feature flags | `{isAdmin && <AdminPanel />}` |

### **API Call Patterns**

```typescript
// Pattern 1: Simple API call (auto-authenticated)
const data = await apiService.getData();

// Pattern 2: Error handling with auth
try {
  const data = await apiService.getData();
} catch (error) {
  if (error.status === 401) {
    // User will be auto-logged out
    console.log('Authentication failed');
  }
}

// Pattern 3: Conditional API calls
const { user } = useAuthContext();
const { data } = useQuery({
  queryKey: ['data'],
  queryFn: () => apiService.getData(),
  enabled: !!user, // Only run if authenticated
});
```

---

## 🎯 **Role-Based Access Quick Reference**

### **Role Hierarchy**

```
SUPER_ADMIN > ADMIN > MANAGER > EMPLOYEE > VIEWER
```

### **Permission Matrix**

| Resource | SUPER_ADMIN | ADMIN | MANAGER | EMPLOYEE | VIEWER |
|----------|-------------|-------|---------|----------|--------|
| **Users** | ✅ CRUD | ✅ CRUD | ❌ R | ❌ R | ❌ R |
| **Vehicles** | ✅ CRUD | ✅ CRUD | ✅ CRUD | ⚠️ RU | ❌ R |
| **Tasks** | ✅ CRUD | ✅ CRUD | ✅ CRUD | ✅ CRUD | ❌ R |
| **Reports** | ✅ CRUD | ✅ CRUD | ⚠️ CR | ❌ R | ❌ R |
| **System** | ✅ CRUD | ❌ R | ❌ - | ❌ - | ❌ - |

**Legend**: ✅ Full Access, ⚠️ Limited Access, ❌ No Access  
**CRUD**: Create, Read, Update, Delete

### **Role Check Examples**

```typescript
// Check specific role
const isAdmin = user?.user_metadata?.role === 'ADMIN';

// Check multiple roles
const canManage = ['ADMIN', 'MANAGER'].includes(user?.user_metadata?.role);

// Check role hierarchy (admin or higher)
const isAdminOrHigher = ['SUPER_ADMIN', 'ADMIN'].includes(user?.user_metadata?.role);
```

---

## 🔐 **Security Checklist**

### **Frontend Security**

- [ ] **Never store sensitive data in localStorage**
- [ ] **Always use ProtectedRoute for sensitive pages**
- [ ] **Validate user roles before showing admin features**
- [ ] **Handle authentication errors gracefully**
- [ ] **Use HTTPS in production**

### **API Security**

- [ ] **All API calls automatically include JWT tokens**
- [ ] **Backend validates every request**
- [ ] **Implement proper error handling**
- [ ] **Use role-based endpoint protection**
- [ ] **Enable CORS properly**

### **Token Security**

- [ ] **Tokens auto-refresh before expiry**
- [ ] **Invalid tokens trigger auto-logout**
- [ ] **Cross-tab logout synchronization**
- [ ] **Secure token storage practices**
- [ ] **Monitor for token abuse**

---

## 🚨 **Common Issues & Solutions**

### **Authentication Issues**

| Issue | Cause | Solution |
|-------|-------|----------|
| **"Invalid token" errors** | Expired JWT | Check token refresh logic |
| **Cross-tab logout not working** | Event listener issues | Verify localStorage events |
| **Role access denied** | Incorrect role assignment | Check user role in database |
| **API 401 errors** | Missing/invalid token | Verify auth token provider |
| **Login form not working** | Supabase config issues | Check environment variables |

### **Debug Commands**

```typescript
// Check current auth state
console.log('Auth State:', useAuthContext());

// Verify JWT token
console.log('JWT Token:', getAuthToken());

// Check user role
console.log('User Role:', user?.user_metadata?.role);

// Enable auth debugging
localStorage.setItem('auth-debug', 'true');
```

---

## 📁 **File Locations**

### **Core Files**

| Component | Location | Purpose |
|-----------|----------|---------|
| **AuthContext** | `frontend/src/contexts/AuthContext.tsx` | Main auth provider |
| **LoginForm** | `frontend/src/components/auth/LoginForm.tsx` | Login UI |
| **ProtectedRoute** | `frontend/src/components/auth/ProtectedRoute.tsx` | Route protection |
| **ApiClient** | `frontend/src/lib/api/core/apiClient.ts` | HTTP client |
| **Service Factory** | `frontend/src/lib/api/services/factory.ts` | Service creation |

### **Backend Files**

| Component | Location | Purpose |
|-----------|----------|---------|
| **JWT Middleware** | `backend/src/middleware/jwtAuth.middleware.ts` | Token validation |
| **Role Middleware** | `backend/src/middleware/roleAuth.middleware.ts` | Role checking |
| **Auth Routes** | `backend/src/routes/auth.routes.ts` | Auth endpoints |
| **Auth Service** | `backend/src/services/auth.service.ts` | Auth logic |

---

## 🔧 **Environment Setup**

### **Required Environment Variables**

**Frontend (.env.local)**:
```bash
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
NEXT_PUBLIC_API_BASE_URL=http://localhost:3001/api
```

**Backend (.env)**:
```bash
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
JWT_SECRET=your-jwt-secret
```

### **Development Setup**

```bash
# 1. Install dependencies
npm install

# 2. Set up environment variables
cp .env.example .env.local

# 3. Start development servers
npm run dev  # Frontend (port 9002)
npm run dev  # Backend (port 3001)

# 4. Test authentication
# Navigate to http://localhost:9002/auth-test
# Use demo credentials: <EMAIL> / demo123
```

---

## 🧪 **Testing Authentication**

### **Manual Testing**

```bash
# 1. Test login flow
curl -X POST http://localhost:3001/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'

# 2. Test protected endpoint
curl -X GET http://localhost:3001/api/health \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# 3. Test role-based access
curl -X GET http://localhost:3001/api/admin/users \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### **Automated Testing**

```typescript
// Unit test example
describe('AuthContext', () => {
  it('should authenticate user successfully', async () => {
    const { result } = renderHook(() => useAuthContext());
    
    await act(async () => {
      await result.current.signIn('<EMAIL>', 'password');
    });
    
    expect(result.current.user).toBeTruthy();
  });
});

// E2E test example
test('should complete login flow', async ({ page }) => {
  await page.goto('/auth-test');
  await page.fill('[data-testid="email"]', '<EMAIL>');
  await page.fill('[data-testid="password"]', 'password123');
  await page.click('[data-testid="login-button"]');
  
  await expect(page).toHaveURL('/dashboard');
});
```

---

## 📞 **Support & Resources**

### **Documentation Links**

- [**Main Architecture**](./AUTHENTICATION_SYSTEM_ARCHITECTURE.md) - Complete system documentation
- [**Component Diagrams**](./AUTHENTICATION_COMPONENTS_DIAGRAM.md) - Visual architecture reference
- [**Security Plan**](../security/SECURITY_ENHANCEMENT_PLAN_V3.md) - Security implementation details
- [**API Architecture**](../../frontend/HANDOFF_SECURE_API_ARCHITECTURE.md) - API client details

### **Demo Credentials**

| Email | Password | Role | Access Level |
|-------|----------|------|--------------|
| `<EMAIL>` | `demo123` | EMPLOYEE | Basic access |
| `<EMAIL>` | `password123` | ADMIN | Administrative access |
| `<EMAIL>` | `manager123` | MANAGER | Management access |

### **Useful Commands**

```bash
# Check TypeScript
npm run type-check

# Run tests
npm run test

# Build for production
npm run build

# Start production server
npm start
```

---

**Document Maintainer:** Development Team  
**Review Schedule:** Monthly  
**Next Review:** February 2025
