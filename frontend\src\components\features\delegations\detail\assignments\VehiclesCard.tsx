/**
 * @file VehiclesCard component for displaying delegation vehicles
 * @module components/delegations/detail/assignments/VehiclesCard
 */

import React from 'react';
import { Car } from 'lucide-react';
import { AssignmentSection } from './AssignmentSection';
import type { Delegation } from '@/lib/types/domain';

interface VehiclesCardProps {
  delegation: Delegation;
  className?: string;
}

/**
 * VehiclesCard component for displaying delegation vehicles
 * Uses the generic AssignmentSection for consistent styling
 */
export function VehiclesCard({ delegation, className }: VehiclesCardProps) {
  return (
    <AssignmentSection
      title="Vehicles"
      icon={Car}
      items={delegation.vehicles ?? []}
      renderItem={(vehicle, index) => (
        <div
          key={vehicle.vehicleId || index}
          className="rounded-lg border border-gray-200 bg-white p-4 space-y-2 hover:shadow-sm transition-shadow dark:border-gray-700 dark:bg-gray-800"
        >
          <h4 className="font-semibold text-gray-900 dark:text-white">
            {vehicle.vehicle
              ? `${vehicle.vehicle.make} ${vehicle.vehicle.model} (${vehicle.vehicle.year})`
              : `Vehicle ID: ${vehicle.vehicleId}`}
          </h4>
          {vehicle.vehicle?.licensePlate && (
            <p className="text-sm text-gray-600 dark:text-gray-400">
              🚗 License Plate: {vehicle.vehicle.licensePlate}
            </p>
          )}
          {vehicle.vehicle?.ownerName && (
            <p className="text-xs text-gray-500 dark:text-gray-500 bg-gray-50 dark:bg-gray-700 p-2 rounded">
              👤 Owner: {vehicle.vehicle.ownerName}
            </p>
          )}
        </div>
      )}
      emptyMessage="No vehicles assigned to this delegation."
      className={className || undefined}
    />
  );
}

export default VehiclesCard;
