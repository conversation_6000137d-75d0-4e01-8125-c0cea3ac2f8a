generator client {
  provider        = "prisma-client-js"
  output          = "../src/generated/prisma"
  previewFeatures = ["multiSchema"]
  binaryTargets   = ["native", "linux-musl"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
  schemas  = ["auth", "public"]
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model audit_log_entries {
  instance_id String?   @db.Uuid
  id          String    @id @db.Uuid
  payload     Json?     @db.<PERSON><PERSON>
  created_at  DateTime? @db.Timestamptz(6)
  ip_address  String    @default("") @db.VarChar(64)

  @@index([instance_id], map: "audit_logs_instance_id_idx")
  @@schema("auth")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model flow_state {
  id                     String                @id @db.Uuid
  user_id                String?               @db.Uuid
  auth_code              String
  code_challenge_method  code_challenge_method
  code_challenge         String
  provider_type          String
  provider_access_token  String?
  provider_refresh_token String?
  created_at             DateTime?             @db.Timestamptz(6)
  updated_at             DateTime?             @db.Timestamptz(6)
  authentication_method  String
  auth_code_issued_at    DateTime?             @db.Timestamptz(6)
  saml_relay_states      saml_relay_states[]

  @@index([created_at(sort: Desc)])
  @@index([auth_code], map: "idx_auth_code")
  @@index([user_id, authentication_method], map: "idx_user_id_auth_method")
  @@schema("auth")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model identities {
  provider_id     String
  user_id         String    @db.Uuid
  identity_data   Json
  provider        String
  last_sign_in_at DateTime? @db.Timestamptz(6)
  created_at      DateTime? @db.Timestamptz(6)
  updated_at      DateTime? @db.Timestamptz(6)
  email           String?   @default(dbgenerated("lower((identity_data ->> 'email'::text))"))
  id              String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  users           users     @relation(fields: [user_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@unique([provider_id, provider], map: "identities_provider_id_provider_unique")
  @@index([email])
  @@index([user_id])
  @@schema("auth")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model instances {
  id              String    @id @db.Uuid
  uuid            String?   @db.Uuid
  raw_base_config String?
  created_at      DateTime? @db.Timestamptz(6)
  updated_at      DateTime? @db.Timestamptz(6)

  @@schema("auth")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model mfa_amr_claims {
  session_id            String   @db.Uuid
  created_at            DateTime @db.Timestamptz(6)
  updated_at            DateTime @db.Timestamptz(6)
  authentication_method String
  id                    String   @id(map: "amr_id_pk") @db.Uuid
  sessions              sessions @relation(fields: [session_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@unique([session_id, authentication_method], map: "mfa_amr_claims_session_id_authentication_method_pkey")
  @@schema("auth")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model mfa_challenges {
  id                     String      @id @db.Uuid
  factor_id              String      @db.Uuid
  created_at             DateTime    @db.Timestamptz(6)
  verified_at            DateTime?   @db.Timestamptz(6)
  ip_address             String      @db.Inet
  otp_code               String?
  web_authn_session_data Json?
  mfa_factors            mfa_factors @relation(fields: [factor_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "mfa_challenges_auth_factor_id_fkey")

  @@index([created_at(sort: Desc)], map: "mfa_challenge_created_at_idx")
  @@schema("auth")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model mfa_factors {
  id                   String           @id @db.Uuid
  user_id              String           @db.Uuid
  friendly_name        String?
  factor_type          factor_type
  status               factor_status
  created_at           DateTime         @db.Timestamptz(6)
  updated_at           DateTime         @db.Timestamptz(6)
  secret               String?
  phone                String?
  last_challenged_at   DateTime?        @unique @db.Timestamptz(6)
  web_authn_credential Json?
  web_authn_aaguid     String?          @db.Uuid
  mfa_challenges       mfa_challenges[]
  users                users            @relation(fields: [user_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@unique([user_id, phone], map: "unique_phone_factor_per_user")
  @@index([user_id, created_at], map: "factor_id_created_at_idx")
  @@index([user_id])
  @@schema("auth")
}

/// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model one_time_tokens {
  id         String              @id @db.Uuid
  user_id    String              @db.Uuid
  token_type one_time_token_type
  token_hash String
  relates_to String
  created_at DateTime            @default(now()) @db.Timestamp(6)
  updated_at DateTime            @default(now()) @db.Timestamp(6)
  users      users               @relation(fields: [user_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@unique([user_id, token_type])
  @@index([relates_to], map: "one_time_tokens_relates_to_hash_idx", type: Hash)
  @@index([token_hash], map: "one_time_tokens_token_hash_hash_idx", type: Hash)
  @@schema("auth")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model refresh_tokens {
  instance_id String?   @db.Uuid
  id          BigInt    @id @default(autoincrement())
  token       String?   @unique(map: "refresh_tokens_token_unique") @db.VarChar(255)
  user_id     String?   @db.VarChar(255)
  revoked     Boolean?
  created_at  DateTime? @db.Timestamptz(6)
  updated_at  DateTime? @db.Timestamptz(6)
  parent      String?   @db.VarChar(255)
  session_id  String?   @db.Uuid
  sessions    sessions? @relation(fields: [session_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@index([instance_id])
  @@index([instance_id, user_id])
  @@index([parent])
  @@index([session_id, revoked])
  @@index([updated_at(sort: Desc)])
  @@schema("auth")
}

/// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model saml_providers {
  id                String        @id @db.Uuid
  sso_provider_id   String        @db.Uuid
  entity_id         String        @unique
  metadata_xml      String
  metadata_url      String?
  attribute_mapping Json?
  created_at        DateTime?     @db.Timestamptz(6)
  updated_at        DateTime?     @db.Timestamptz(6)
  name_id_format    String?
  sso_providers     sso_providers @relation(fields: [sso_provider_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@index([sso_provider_id])
  @@schema("auth")
}

/// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model saml_relay_states {
  id              String        @id @db.Uuid
  sso_provider_id String        @db.Uuid
  request_id      String
  for_email       String?
  redirect_to     String?
  created_at      DateTime?     @db.Timestamptz(6)
  updated_at      DateTime?     @db.Timestamptz(6)
  flow_state_id   String?       @db.Uuid
  flow_state      flow_state?   @relation(fields: [flow_state_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
  sso_providers   sso_providers @relation(fields: [sso_provider_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@index([created_at(sort: Desc)])
  @@index([for_email])
  @@index([sso_provider_id])
  @@schema("auth")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model schema_migrations {
  version String @id @db.VarChar(255)

  @@schema("auth")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model sessions {
  id             String           @id @db.Uuid
  user_id        String           @db.Uuid
  created_at     DateTime?        @db.Timestamptz(6)
  updated_at     DateTime?        @db.Timestamptz(6)
  factor_id      String?          @db.Uuid
  aal            aal_level?
  not_after      DateTime?        @db.Timestamptz(6)
  refreshed_at   DateTime?        @db.Timestamp(6)
  user_agent     String?
  ip             String?          @db.Inet
  tag            String?
  mfa_amr_claims mfa_amr_claims[]
  refresh_tokens refresh_tokens[]
  users          users            @relation(fields: [user_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@index([not_after(sort: Desc)])
  @@index([user_id])
  @@index([user_id, created_at], map: "user_id_created_at_idx")
  @@schema("auth")
}

/// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
/// This model contains an expression index which requires additional setup for migrations. Visit https://pris.ly/d/expression-indexes for more info.
model sso_domains {
  id              String        @id @db.Uuid
  sso_provider_id String        @db.Uuid
  domain          String
  created_at      DateTime?     @db.Timestamptz(6)
  updated_at      DateTime?     @db.Timestamptz(6)
  sso_providers   sso_providers @relation(fields: [sso_provider_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@index([sso_provider_id])
  @@schema("auth")
}

/// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
/// This model contains an expression index which requires additional setup for migrations. Visit https://pris.ly/d/expression-indexes for more info.
model sso_providers {
  id                String              @id @db.Uuid
  resource_id       String?
  created_at        DateTime?           @db.Timestamptz(6)
  updated_at        DateTime?           @db.Timestamptz(6)
  saml_providers    saml_providers[]
  saml_relay_states saml_relay_states[]
  sso_domains       sso_domains[]

  @@schema("auth")
}

/// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
/// This model contains an expression index which requires additional setup for migrations. Visit https://pris.ly/d/expression-indexes for more info.
model users {
  instance_id                 String?           @db.Uuid
  id                          String            @id @db.Uuid
  aud                         String?           @db.VarChar(255)
  role                        String?           @db.VarChar(255)
  email                       String?           @db.VarChar(255)
  encrypted_password          String?           @db.VarChar(255)
  email_confirmed_at          DateTime?         @db.Timestamptz(6)
  invited_at                  DateTime?         @db.Timestamptz(6)
  confirmation_token          String?           @db.VarChar(255)
  confirmation_sent_at        DateTime?         @db.Timestamptz(6)
  recovery_token              String?           @db.VarChar(255)
  recovery_sent_at            DateTime?         @db.Timestamptz(6)
  email_change_token_new      String?           @db.VarChar(255)
  email_change                String?           @db.VarChar(255)
  email_change_sent_at        DateTime?         @db.Timestamptz(6)
  last_sign_in_at             DateTime?         @db.Timestamptz(6)
  raw_app_meta_data           Json?
  raw_user_meta_data          Json?
  is_super_admin              Boolean?
  created_at                  DateTime?         @db.Timestamptz(6)
  updated_at                  DateTime?         @db.Timestamptz(6)
  phone                       String?           @unique
  phone_confirmed_at          DateTime?         @db.Timestamptz(6)
  phone_change                String?           @default("")
  phone_change_token          String?           @default("") @db.VarChar(255)
  phone_change_sent_at        DateTime?         @db.Timestamptz(6)
  confirmed_at                DateTime?         @default(dbgenerated("LEAST(email_confirmed_at, phone_confirmed_at)")) @db.Timestamptz(6)
  email_change_token_current  String?           @default("") @db.VarChar(255)
  email_change_confirm_status Int?              @default(0) @db.SmallInt
  banned_until                DateTime?         @db.Timestamptz(6)
  reauthentication_token      String?           @default("") @db.VarChar(255)
  reauthentication_sent_at    DateTime?         @db.Timestamptz(6)
  is_sso_user                 Boolean           @default(false)
  deleted_at                  DateTime?         @db.Timestamptz(6)
  is_anonymous                Boolean           @default(false)
  identities                  identities[]
  mfa_factors                 mfa_factors[]
  one_time_tokens             one_time_tokens[]
  sessions                    sessions[]
  audit_logs                  audit_logs[]

  @@index([instance_id])
  @@index([is_anonymous])
  @@schema("auth")
}

model Delegate {
  id           String     @id @default(uuid())
  delegationId String
  name         String
  title        String
  notes        String?
  Delegation   Delegation @relation(fields: [delegationId], references: [id], onDelete: Cascade)

  @@schema("public")
}

/// Delegation - Core Entity for WorkHub Delegation Management System
///
/// RLS SECURITY IMPLEMENTATION:
/// ✅ RLS Enabled: ALTER TABLE public."Delegation" ENABLE ROW LEVEL SECURITY;
///
/// RLS POLICIES IMPLEMENTED:
/// • delegation_select: SELECT - Admins + Assigned Users can view
/// • delegation_insert: INSERT - Admins only can create delegations
/// • delegation_update: UPDATE - Admins only can modify delegations
/// • delegation_delete: DELETE - Admins only can remove delegations
///
/// ACCESS CONTROL:
/// • Admin users (ADMIN, MANAGER, administrator, manager): Full CRUD access
/// • Assigned employees: Can view delegations they're assigned to (as escort or driver)
/// • Regular users: No access unless specifically assigned to the delegation
///
/// MULTIPLE ASSIGNMENT SYSTEM:
/// • Supports multiple escorts per delegation (via DelegationEscort join table)
/// • Supports multiple drivers per delegation (via DelegationDriver join table)
/// • Supports multiple vehicles per delegation (via DelegationVehicle join table)
/// • Replaces old single-assignment columns (escortEmployeeId, driverEmployeeId, vehicleId)
///
/// BUSINESS RULES:
/// • Each delegation must have at least one escort (enforced at application level)
/// • Drivers must have 'driver' role (enforced at application level)
/// • Vehicle availability conflicts checked at application level
/// • Status transitions tracked via DelegationStatusEntry
///
/// PERFORMANCE OPTIMIZATION:
/// • Indexed on durationFrom for temporal queries
/// • Indexed on status for filtering by delegation state
model Delegation {
  id                                                        String                  @id
  eventName                                                 String
  location                                                  String
  durationFrom                                              DateTime
  durationTo                                                DateTime
  invitationFrom                                            String?
  invitationTo                                              String?
  flightArrivalId                                           String?                 @unique
  flightDepartureId                                         String?                 @unique
  status                                                    DelegationStatus
  notes                                                     String?
  imageUrl                                                  String?
  createdAt                                                 DateTime                @default(now())
  updatedAt                                                 DateTime
  Delegate                                                  Delegate[]
  FlightDetails_Delegation_flightArrivalIdToFlightDetails   FlightDetails?          @relation("Delegation_flightArrivalIdToFlightDetails", fields: [flightArrivalId], references: [id])
  FlightDetails_Delegation_flightDepartureIdToFlightDetails FlightDetails?          @relation("Delegation_flightDepartureIdToFlightDetails", fields: [flightDepartureId], references: [id])
  drivers                                                   DelegationDriver[]
  escorts                                                   DelegationEscort[]
  DelegationStatusEntry                                     DelegationStatusEntry[]
  vehicles                                                  DelegationVehicle[]

  @@index([durationFrom])
  @@index([status])
  @@schema("public")
}

/// DelegationEscort - Many-to-Many Join Table for Delegation-Escort Assignments
///
/// RLS SECURITY IMPLEMENTATION:
/// ✅ RLS Enabled: ALTER TABLE public."DelegationEscort" ENABLE ROW LEVEL SECURITY;
///
/// RLS POLICIES IMPLEMENTED:
/// • escort_select: SELECT - Admins + Assigned Escorts can view
/// • escort_insert: INSERT - Admins only can create assignments
/// • escort_update: UPDATE - Admins only can modify assignments
/// • escort_delete: DELETE - Admins only can remove assignments
///
/// ACCESS CONTROL:
/// • Admin users (ADMIN, MANAGER, administrator, manager): Full CRUD access
/// • Assigned escort employees: Can view their own assignments only
/// • Regular users: No access unless specifically assigned
///
/// BUSINESS RULES:
/// • One escort can be assigned to multiple delegations
/// • One delegation can have multiple escorts
/// • Prevents duplicate assignments (unique constraint)
/// • Cascade deletes when delegation or employee is removed
///
/// PERFORMANCE OPTIMIZATION:
/// • Indexed on employeeId for "find assignments by employee" queries
/// • Indexed on delegationId for "find escorts by delegation" queries
/// • Composite index for efficient join operations
/// • Temporal index on createdAt for audit queries
model DelegationEscort {
  id           String     @id @default(uuid())
  delegationId String
  employeeId   Int
  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @updatedAt
  createdBy    String?    @db.Uuid
  Delegation   Delegation @relation(fields: [delegationId], references: [id], onDelete: Cascade)
  Employee     Employee   @relation(fields: [employeeId], references: [id], onDelete: Cascade)

  @@unique([delegationId, employeeId])
  @@index([employeeId])
  @@index([delegationId])
  @@index([delegationId, employeeId])
  @@index([createdAt])
  @@schema("public")
}

/// DelegationDriver - Many-to-Many Join Table for Delegation-Driver Assignments
///
/// RLS SECURITY IMPLEMENTATION:
/// ✅ RLS Enabled: ALTER TABLE public."DelegationDriver" ENABLE ROW LEVEL SECURITY;
///
/// RLS POLICIES IMPLEMENTED:
/// • driver_select: SELECT - Admins + Assigned Drivers can view
/// • driver_insert: INSERT - Admins only can create assignments
/// • driver_update: UPDATE - Admins only can modify assignments
/// • driver_delete: DELETE - Admins only can remove assignments
///
/// ACCESS CONTROL:
/// • Admin users (ADMIN, MANAGER, administrator, manager): Full CRUD access
/// • Assigned driver employees: Can view their own assignments only
/// • Regular users: No access unless specifically assigned
///
/// BUSINESS RULES:
/// • One driver can be assigned to multiple delegations
/// • One delegation can have multiple drivers
/// • Employee must have 'driver' role (enforced at application level)
/// • Prevents duplicate assignments (unique constraint)
/// • Cascade deletes when delegation or employee is removed
///
/// PERFORMANCE OPTIMIZATION:
/// • Indexed on employeeId for "find assignments by driver" queries
/// • Indexed on delegationId for "find drivers by delegation" queries
/// • Composite index for efficient join operations
/// • Temporal index on createdAt for audit queries
model DelegationDriver {
  id           String     @id @default(uuid())
  delegationId String
  employeeId   Int
  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @updatedAt
  createdBy    String?    @db.Uuid
  Delegation   Delegation @relation(fields: [delegationId], references: [id], onDelete: Cascade)
  Employee     Employee   @relation(fields: [employeeId], references: [id], onDelete: Cascade)

  @@unique([delegationId, employeeId])
  @@index([employeeId])
  @@index([delegationId])
  @@index([delegationId, employeeId])
  @@index([createdAt])
  @@schema("public")
}

/// DelegationVehicle - Many-to-Many Join Table for Delegation-Vehicle Assignments
///
/// RLS SECURITY IMPLEMENTATION:
/// ✅ RLS Enabled: ALTER TABLE public."DelegationVehicle" ENABLE ROW LEVEL SECURITY;
///
/// RLS POLICIES IMPLEMENTED:
/// • vehicle_select: SELECT - Admins + Related Users can view
/// • vehicle_insert: INSERT - Admins only can create assignments
/// • vehicle_update: UPDATE - Admins only can modify assignments
/// • vehicle_delete: DELETE - Admins only can remove assignments
///
/// ACCESS CONTROL:
/// • Admin users (ADMIN, MANAGER, administrator, manager): Full CRUD access
/// • Related users: Can view vehicle assignments for delegations they're assigned to
/// • Regular users: No access unless involved in the delegation
///
/// BUSINESS RULES:
/// • One vehicle can be assigned to multiple delegations (non-overlapping times)
/// • One delegation can have multiple vehicles
/// • Vehicle availability conflicts should be checked at application level
/// • Prevents duplicate assignments (unique constraint)
/// • Cascade deletes when delegation or vehicle is removed
///
/// PERFORMANCE OPTIMIZATION:
/// • Indexed on vehicleId for "find assignments by vehicle" queries
/// • Indexed on delegationId for "find vehicles by delegation" queries
/// • Composite index for efficient join operations
/// • Temporal index on createdAt for audit queries
model DelegationVehicle {
  id           String     @id @default(uuid())
  delegationId String
  vehicleId    Int
  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @updatedAt
  createdBy    String?    @db.Uuid
  Delegation   Delegation @relation(fields: [delegationId], references: [id], onDelete: Cascade)
  Vehicle      Vehicle    @relation(fields: [vehicleId], references: [id], onDelete: Cascade)

  @@unique([delegationId, vehicleId])
  @@index([vehicleId])
  @@index([delegationId])
  @@index([delegationId, vehicleId])
  @@index([createdAt])
  @@schema("public")
}

model DelegationStatusEntry {
  id           String           @id @default(uuid())
  delegationId String
  status       DelegationStatus
  changedAt    DateTime         @default(now())
  reason       String?
  Delegation   Delegation       @relation(fields: [delegationId], references: [id], onDelete: Cascade)

  @@schema("public")
}

model Employee {
  name                                 String
  employeeId                           String                @unique
  contactInfo                          String
  createdAt                            DateTime              @default(now())
  updatedAt                            DateTime
  id                                   Int                   @id @default(autoincrement())
  availability                         DriverAvailability?
  contactEmail                         String?
  contactMobile                        String?
  contactPhone                         String?
  currentLocation                      String?
  department                           String?
  fullName                             String?
  generalAssignments                   String[]              @default([])
  hireDate                             DateTime?
  notes                                String?
  position                             String?
  profileImageUrl                      String?
  shiftSchedule                        String?
  skills                               String[]              @default([])
  status                               EmployeeStatus?
  workingHours                         String?
  role                                 EmployeeRole
  DelegationDriver                     DelegationDriver[]
  DelegationEscort                     DelegationEscort[]
  EmployeeStatusEntry                  EmployeeStatusEntry[]
  ServiceRecord                        ServiceRecord[]
  Task_Task_driverEmployeeIdToEmployee Task[]                @relation("Task_driverEmployeeIdToEmployee")
  Task_Task_staffEmployeeIdToEmployee  Task[]                @relation("Task_staffEmployeeIdToEmployee")

  @@schema("public")
}

model EmployeeStatusEntry {
  id         String         @id @default(uuid())
  employeeId Int
  status     EmployeeStatus
  changedAt  DateTime       @default(now())
  reason     String?
  Employee   Employee       @relation(fields: [employeeId], references: [id], onDelete: Cascade)

  @@schema("public")
}

model FlightDetails {
  id                                                     String      @id @default(uuid())
  flightNumber                                           String
  dateTime                                               DateTime
  airport                                                String
  terminal                                               String?
  notes                                                  String?
  arrivalDelegationId                                    String?     @unique
  departureDelegationId                                  String?     @unique
  Delegation_Delegation_flightArrivalIdToFlightDetails   Delegation? @relation("Delegation_flightArrivalIdToFlightDetails")
  Delegation_Delegation_flightDepartureIdToFlightDetails Delegation? @relation("Delegation_flightDepartureIdToFlightDetails")

  @@schema("public")
}

model ServiceRecord {
  id               String    @id @default(uuid())
  vehicleId        Int
  employeeId       Int?
  date             DateTime
  odometer         Int
  servicePerformed String[]
  notes            String?
  cost             Decimal?
  createdAt        DateTime  @default(now())
  updatedAt        DateTime
  Employee         Employee? @relation(fields: [employeeId], references: [id])
  Vehicle          Vehicle   @relation(fields: [vehicleId], references: [id], onDelete: Cascade)

  @@schema("public")
}

model SubTask {
  id        String  @id @default(uuid())
  taskId    String
  title     String
  completed Boolean @default(false)
  Task      Task    @relation(fields: [taskId], references: [id], onDelete: Cascade)

  @@schema("public")
}

model Task {
  id                                       String            @id @default(uuid())
  description                              String
  location                                 String
  dateTime                                 DateTime
  estimatedDuration                        Int
  requiredSkills                           String[]          @default([])
  priority                                 TaskPriority
  deadline                                 DateTime?
  status                                   TaskStatus
  notes                                    String?
  vehicleId                                Int?
  createdAt                                DateTime          @default(now())
  updatedAt                                DateTime
  staffEmployeeId                          Int
  driverEmployeeId                         Int?
  SubTask                                  SubTask[]
  Employee_Task_driverEmployeeIdToEmployee Employee?         @relation("Task_driverEmployeeIdToEmployee", fields: [driverEmployeeId], references: [id])
  Employee_Task_staffEmployeeIdToEmployee  Employee          @relation("Task_staffEmployeeIdToEmployee", fields: [staffEmployeeId], references: [id], onDelete: SetNull)
  Vehicle                                  Vehicle?          @relation(fields: [vehicleId], references: [id])
  TaskStatusEntry                          TaskStatusEntry[]

  @@index([dateTime])
  @@index([driverEmployeeId])
  @@index([staffEmployeeId])
  @@index([status])
  @@index([vehicleId])
  @@schema("public")
}

model TaskStatusEntry {
  id        String     @id @default(uuid())
  taskId    String
  status    TaskStatus
  changedAt DateTime   @default(now())
  reason    String?
  Task      Task       @relation(fields: [taskId], references: [id], onDelete: Cascade)

  @@schema("public")
}

model Vehicle {
  make              String
  model             String
  year              Int
  vin               String              @unique
  licensePlate      String
  ownerName         String
  ownerContact      String
  createdAt         DateTime            @default(now())
  updatedAt         DateTime
  id                Int                 @id @default(autoincrement())
  color             String?
  imageUrl          String?
  initialOdometer   Int?
  DelegationVehicle DelegationVehicle[]
  ServiceRecord     ServiceRecord[]
  Task              Task[]

  @@schema("public")
}

/// Audit Logs - Custom audit trail for application-specific actions
///
/// This table stores detailed logs of administrative and user actions.
/// It is separate from Supabase's internal auth.audit_log_entries.
/// Updated to reference auth.users directly instead of user_profiles.
model audit_logs {
  id           String   @id @default(uuid())
  user_id      String   @db.Uuid
  action       String
  details      String
  ip_address   String?
  user_agent   String?
  created_at   DateTime @default(now()) @db.Timestamptz(6)
  auth_user_id String   @db.Uuid
  auth_user    users    @relation(fields: [auth_user_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "audit_logs_auth_user_id_fkey")

  @@index([user_id])
  @@index([action])
  @@index([created_at(sort: Desc)])
  @@index([auth_user_id], map: "idx_audit_logs_auth_user_id")
  @@schema("public")
}

model user_profiles {
  id          String   @id @db.Uuid
  role        String   @default("USER")
  is_active   Boolean  @default(true)
  employee_id Int?
  created_at  DateTime @default(now()) @db.Timestamptz(6)
  updated_at  DateTime @default(now()) @db.Timestamptz(6)

  @@index([employee_id], map: "idx_user_profiles_employee_id")
  @@index([is_active], map: "idx_user_profiles_is_active")
  @@index([role], map: "idx_user_profiles_role")
  @@schema("public")
}

enum aal_level {
  aal1
  aal2
  aal3

  @@schema("auth")
}

enum code_challenge_method {
  s256
  plain

  @@schema("auth")
}

enum factor_status {
  unverified
  verified

  @@schema("auth")
}

enum factor_type {
  totp
  webauthn
  phone

  @@schema("auth")
}

enum one_time_token_type {
  confirmation_token
  reauthentication_token
  recovery_token
  email_change_token_new
  email_change_token_current
  phone_change_token

  @@schema("auth")
}

enum DelegationStatus {
  Planned
  Confirmed
  In_Progress
  Completed
  Cancelled
  No_details

  @@schema("public")
}

enum DriverAvailability {
  On_Shift
  Off_Shift
  On_Break
  Busy

  @@schema("public")
}

enum EmployeeRole {
  driver
  mechanic
  administrator
  office_staff
  manager
  service_advisor
  technician
  other

  @@schema("public")
}

enum EmployeeStatus {
  Active
  On_Leave
  Terminated
  Inactive

  @@schema("public")
}

enum TaskPriority {
  Low
  Medium
  High

  @@schema("public")
}

enum TaskStatus {
  Pending
  Assigned
  In_Progress
  Completed
  Cancelled

  @@schema("public")
}
