import type { Prisma, Vehicle } from '../generated/prisma/index.js';

import { PrismaClientKnownRequestError } from '../generated/prisma/runtime/library.js';
import prisma from './index.js';

export const createVehicle = async (data: Prisma.VehicleCreateInput): Promise<null | Vehicle> => {
  try {
    return await prisma.vehicle.create({
      data,
      include: {
        DelegationVehicle: {
          include: { Delegation: true },
          orderBy: { createdAt: 'desc' },
        },
        ServiceRecord: { orderBy: { date: 'desc' } },
        Task: true,
      },
    });
  } catch (error) {
    console.error('Error creating vehicle:', error);
    if (error instanceof PrismaClientKnownRequestError && error.code === 'P2002') {
      throw new Error(`Vehicle with VIN ${data.vin} already exists.`);
    }
    return null;
  }
};

export const getAllVehicles = async (): Promise<Vehicle[]> => {
  try {
    return await prisma.vehicle.findMany({
      include: {
        DelegationVehicle: {
          include: { Delegation: true },
          orderBy: { createdAt: 'desc' },
        },
        ServiceRecord: { orderBy: { date: 'desc' } },
        Task: true,
      },
      orderBy: { createdAt: 'desc' },
    });
  } catch (error) {
    console.error('Error fetching all vehicles:', error);
    return [];
  }
};

export const getVehicleById = async (id: number): Promise<null | Vehicle> => {
  try {
    return await prisma.vehicle.findUnique({
      include: {
        DelegationVehicle: {
          include: { Delegation: true },
          orderBy: { createdAt: 'desc' },
        },
        ServiceRecord: { orderBy: { date: 'desc' } },
        Task: true,
      },
      where: { id },
    });
  } catch (error) {
    console.error(`Error fetching vehicle with ID ${id}:`, error);
    return null;
  }
};

export const updateVehicle = async (
  id: number,
  data: Prisma.VehicleUpdateInput,
): Promise<null | Vehicle> => {
  try {
    return await prisma.vehicle.update({
      data,
      include: {
        DelegationVehicle: {
          include: { Delegation: true },
          orderBy: { createdAt: 'desc' },
        },
        ServiceRecord: { orderBy: { date: 'desc' } },
        Task: true,
      },
      where: { id },
    });
  } catch (error) {
    console.error(`Error updating vehicle with ID ${id}:`, error);
    if (error instanceof PrismaClientKnownRequestError && error.code === 'P2025') {
      return null;
    }
    if (error instanceof PrismaClientKnownRequestError && error.code === 'P2002' && data.vin) {
      throw new Error(`Another vehicle with VIN ${data.vin} already exists.`);
    }
    return null;
  }
};

export const deleteVehicle = async (id: number): Promise<null | Vehicle> => {
  try {
    return await prisma.$transaction(async tx => {
      const vehicleToDelete = await tx.vehicle.findUnique({
        include: {
          DelegationVehicle: {
            include: { Delegation: true },
            orderBy: { createdAt: 'desc' },
          },
          ServiceRecord: true,
          Task: true,
        },
        where: { id },
      });
      if (!vehicleToDelete) return null;

      // Delete related ServiceRecords
      await tx.serviceRecord.deleteMany({ where: { vehicleId: id } });
      // Delete related Tasks or disassociate them (current schema sets vehicleId to null on vehicle delete if task is not deleted)
      // If we want to delete tasks associated with a vehicle, it should be:
      // await tx.task.deleteMany({ where: { vehicleId: id } });
      // Current schema has onDelete: SetNull for Task.vehicle, so no explicit action needed on tasks to delete vehicle
      // unless we want to cascade delete tasks.

      // If a driver is assigned to this vehicle, their assignedVehicleId should be nullified
      // The relation "DriverVehicle" with onDelete: SetNull on Employee side handles this.
      // No explicit update needed here due to schema definition.

      await tx.vehicle.delete({ where: { id } });
      return vehicleToDelete;
    });
  } catch (error) {
    console.error(`Error deleting vehicle with ID ${id}:`, error);
    if (error instanceof PrismaClientKnownRequestError && error.code === 'P2025') {
      return null;
    }
    return null;
  }
};
