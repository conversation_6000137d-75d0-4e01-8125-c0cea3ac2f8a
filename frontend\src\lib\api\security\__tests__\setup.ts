/**
 * @file Test Setup Configuration
 * @module api/security/__tests__/setup
 *
 * Phase 5: Testing & Validation
 * Test setup and configuration for secure API architecture tests
 */

import '@testing-library/jest-dom';

// Mock console methods to reduce noise in tests
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;
const originalConsoleLog = console.log;

beforeEach(() => {
  // Reset console mocks before each test
  console.error = jest.fn();
  console.warn = jest.fn();
  console.log = jest.fn();
});

afterEach(() => {
  // Restore console methods after each test
  console.error = originalConsoleError;
  console.warn = originalConsoleWarn;
  console.log = originalConsoleLog;

  // Clear all mocks
  jest.clearAllMocks();
});

// Global test utilities
global.testUtils = {
  // Mock security features for testing
  createMockSecurityFeatures: () => ({
    csrfProtection: {
      attachCSRF: jest.fn(config => ({
        ...config,
        headers: { ...config.headers, 'X-CSRF-Token': 'mock-csrf-token' },
      })),
    },
    tokenManagement: {
      isTokenValid: true,
      isTokenExpired: false,
      refreshToken: jest.fn().mockResolvedValue(true),
      updateActivity: jest.fn(),
    },
    inputValidation: {
      sanitizeInput: jest.fn(input => {
        if (typeof input === 'string') {
          return input.replace(/<script.*?<\/script>/gi, '');
        }
        return input;
      }),
    },
    sessionSecurity: {
      isSessionActive: true,
      clearSession: jest.fn(),
      updateActivity: jest.fn(),
    },
  }),

  // Mock security configuration for testing
  createMockSecurityConfig: () => ({
    csrf: {
      enabled: true,
      tokenHeader: 'X-CSRF-Token',
      excludePaths: [],
    },
    tokenValidation: {
      enabled: true,
      refreshThreshold: 300,
      autoRefresh: true,
    },
    inputSanitization: {
      enabled: true,
      sanitizers: ['xss', 'sql'],
    },
    authentication: {
      enabled: true,
      autoLogout: true,
      redirectOnFailure: true,
    },
    http: {
      baseURL: '/api',
      timeout: 10000,
      retryAttempts: 3,
    },
  }),

  // Mock security context for testing
  createMockSecurityContext: () => ({
    isAuthenticated: true,
    hasValidToken: true,
    user: { id: '123', role: 'user' },
    session: { id: 'session-123' },
    timestamp: new Date(),
  }),

  // Mock fetch response
  createMockFetchResponse: (data: any, options: Partial<Response> = {}) => ({
    ok: true,
    status: 200,
    statusText: 'OK',
    headers: new Map([['content-type', 'application/json']]),
    json: jest.fn().mockResolvedValue(data),
    ...options,
  }),

  // Mock error response
  createMockErrorResponse: (status: number, message: string) => ({
    ok: false,
    status,
    statusText: message,
    headers: new Map(),
    json: jest.fn().mockResolvedValue({ error: message }),
  }),
};

// Mock environment variables
Object.defineProperty(process.env, 'NODE_ENV', {
  value: 'test',
  writable: true,
});

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

// Mock sessionStorage
const sessionStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
Object.defineProperty(window, 'sessionStorage', {
  value: sessionStorageMock,
});

// Mock fetch globally
global.fetch = jest.fn();

// Mock AbortController
global.AbortController = jest.fn(() => ({
  abort: jest.fn(),
  signal: {
    aborted: false,
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
  },
})) as any;

// Mock setTimeout and clearTimeout for testing
global.setTimeout = jest.fn(fn => {
  if (typeof fn === 'function') {
    fn();
  }
  return 123; // Mock timer ID
}) as any;

global.clearTimeout = jest.fn();

// Mock crypto for CSRF token generation
Object.defineProperty(global, 'crypto', {
  value: {
    getRandomValues: jest.fn(arr => {
      for (let i = 0; i < arr.length; i++) {
        arr[i] = Math.floor(Math.random() * 256);
      }
      return arr;
    }),
    randomUUID: jest.fn(() => 'mock-uuid-123'),
  },
});

// Mock IntersectionObserver
global.IntersectionObserver = jest.fn(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
})) as any;

// Mock ResizeObserver
global.ResizeObserver = jest.fn(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
})) as any;

// Custom matchers for testing
expect.extend({
  toHaveSecurityHeaders(received: any) {
    const hasCSRF = received.headers && received.headers['X-CSRF-Token'];
    const hasAuth = received.headers && received.headers['Authorization'];

    if (hasCSRF && hasAuth) {
      return {
        message: () => `Expected request not to have security headers`,
        pass: true,
      };
    } else {
      return {
        message: () =>
          `Expected request to have security headers (CSRF and Authorization)`,
        pass: false,
      };
    }
  },

  toBeSecureRequest(received: any) {
    const hasCredentials = received.credentials === 'include';
    const hasSecurityHeaders =
      received.headers &&
      (received.headers['X-CSRF-Token'] || received.headers['Authorization']);

    if (hasCredentials && hasSecurityHeaders) {
      return {
        message: () => `Expected request not to be secure`,
        pass: true,
      };
    } else {
      return {
        message: () =>
          `Expected request to be secure (include credentials and security headers)`,
        pass: false,
      };
    }
  },
});

// Declare custom matchers for TypeScript
declare global {
  namespace jest {
    interface Matchers<R> {
      toHaveSecurityHeaders(): R;
      toBeSecureRequest(): R;
    }
  }

  var testUtils: {
    createMockSecurityFeatures: () => any;
    createMockSecurityConfig: () => any;
    createMockSecurityContext: () => any;
    createMockFetchResponse: (data: any, options?: Partial<Response>) => any;
    createMockErrorResponse: (status: number, message: string) => any;
  };
}

export {};
