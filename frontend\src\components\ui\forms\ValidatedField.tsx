'use client';

import React from 'react';
import { useFormContext } from 'react-hook-form';
import { <PERSON>ert<PERSON>ircle, CheckCircle, AlertTriangle, Info } from 'lucide-react';

import type { DelegationFormData } from '@/lib/schemas/delegationSchemas';
import { useDelegationValidation } from '@/hooks/forms/useDelegationValidation';

import { FormField } from '@/components/ui/forms/formField';
import { cn } from '@/lib/utils';

// ============================================================================
// TYPES
// ============================================================================

export interface ValidatedFieldProps {
  name: keyof DelegationFormData;
  label: string;
  type?: string;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  showValidationIcon?: boolean;
  showValidationMessage?: boolean;
  showRequiredIndicator?: boolean;
  options?: Array<{
    label: string;
    value: string | number;
    disabled?: boolean;
  }>;
  icon?: React.ComponentType<any>;
  [key: string]: any; // Allow additional props
}

// ============================================================================
// COMPONENT
// ============================================================================

/**
 * Enhanced form field with comprehensive validation UI feedback
 *
 * Features:
 * - Real-time validation state indicators
 * - Visual feedback with icons and colors
 * - Error and warning message display
 * - Required field indicators
 * - Accessibility support
 * - Consistent styling across all form sections
 */
export const ValidatedField: React.FC<ValidatedFieldProps> = ({
  name,
  label,
  type = 'text',
  placeholder,
  disabled = false,
  className = '',
  showValidationIcon = true,
  showValidationMessage = true,
  showRequiredIndicator = true,
  options,
  icon,
  ...props
}) => {
  const { getFieldValidationState } = useDelegationValidation();
  const {
    formState: { errors },
  } = useFormContext<DelegationFormData>();

  const fieldState = getFieldValidationState(name);
  const fieldError = errors[name];

  // Determine validation state classes
  const getValidationClasses = () => {
    if (fieldState.hasError) {
      return 'border-destructive focus:border-destructive focus:ring-destructive/20';
    }

    if (fieldState.hasWarning) {
      return 'border-amber-500 focus:border-amber-500 focus:ring-amber-500/20';
    }

    if (fieldState.isValid && fieldState.isTouched && fieldState.isDirty) {
      return 'border-green-500 focus:border-green-500 focus:ring-green-500/20';
    }

    return '';
  };

  // Get validation icon
  const getValidationIcon = () => {
    if (!showValidationIcon) return null;

    if (fieldState.hasError) {
      return (
        <AlertCircle className="h-4 w-4 text-destructive" aria-label="Error" />
      );
    }

    if (fieldState.hasWarning) {
      return (
        <AlertTriangle
          className="h-4 w-4 text-amber-500"
          aria-label="Warning"
        />
      );
    }

    if (fieldState.isValid && fieldState.isTouched && fieldState.isDirty) {
      return (
        <CheckCircle className="h-4 w-4 text-green-500" aria-label="Valid" />
      );
    }

    return null;
  };

  // Get validation message
  const getValidationMessage = () => {
    if (!showValidationMessage) return null;

    if (fieldState.hasError && fieldState.errorMessage) {
      return (
        <div className="flex items-center gap-1 text-sm text-destructive mt-1">
          <AlertCircle className="h-3 w-3" />
          <span>{fieldState.errorMessage}</span>
        </div>
      );
    }

    if (fieldState.hasWarning && fieldState.warningMessage) {
      return (
        <div className="flex items-center gap-1 text-sm text-amber-600 mt-1">
          <AlertTriangle className="h-3 w-3" />
          <span>{fieldState.warningMessage}</span>
        </div>
      );
    }

    return null;
  };

  // Enhanced label with required indicator
  const enhancedLabel =
    showRequiredIndicator && fieldState.isRequired ? `${label} *` : label;

  return (
    <div className={cn('space-y-1', className)}>
      <div className="relative">
        <FormField
          name={name}
          label={enhancedLabel}
          type={type}
          placeholder={placeholder || ''}
          disabled={disabled}
          options={options || []}
          {...(icon && { icon })}
          className={cn('transition-all duration-200', getValidationClasses())}
          aria-invalid={fieldState.hasError}
          aria-describedby={
            fieldState.hasError || fieldState.hasWarning
              ? `${name}-validation-message`
              : undefined
          }
          {...props}
        />

        {/* Validation Icon Overlay */}
        {showValidationIcon && (
          <div className="absolute right-3 top-9 flex items-center">
            {getValidationIcon()}
          </div>
        )}
      </div>

      {/* Validation Message */}
      {showValidationMessage && (
        <div id={`${name}-validation-message`} role="alert" aria-live="polite">
          {getValidationMessage()}
        </div>
      )}
    </div>
  );
};

export default ValidatedField;
