// performance.validation.test.ts - Performance validation tests for reliability components

import { describe, test, expect, beforeEach, afterEach, jest } from '@jest/globals';
import express from 'express';
import request from 'supertest';
import {
  createMockRedis,
  createMockPrometheusClient,
  PerformanceMeasurer,
} from '../../reliabilityTestUtils.js';

// Mock dependencies for performance testing
const mockRedis = createMockRedis();
const mockPrometheusClient = createMockPrometheusClient();

jest.mock('ioredis', () => ({
  default: jest.fn().mockImplementation(() => mockRedis),
}));

jest.mock('prom-client', () => mockPrometheusClient);

jest.mock('../../../utils/logger.js', () => ({
  default: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
}));

// Import after mocking
import { metricsMiddleware } from '../../../services/metrics.service.js';
import { createDeduplicationMiddleware } from '../../../middleware/requestDeduplication.js';

describe('Performance Validation Tests', () => {
  let app: express.Application;
  let performanceMeasurer: PerformanceMeasurer;

  beforeEach(() => {
    app = express();
    app.use(express.json());
    performanceMeasurer = new PerformanceMeasurer();

    jest.clearAllMocks();
    mockRedis._getStore().clear();
    mockPrometheusClient._getMetrics().clear();
  });

  afterEach(() => {
    performanceMeasurer.reset();
  });

  describe('Middleware Performance Impact', () => {
    test('should measure baseline performance without middleware', async () => {
      // Baseline route without any middleware
      app.get('/api/baseline', (req, res) => {
        res.json({ message: 'baseline response' });
      });

      // Measure baseline performance
      for (let i = 0; i < 100; i++) {
        await performanceMeasurer.measure('baseline', async () => {
          return request(app).get('/api/baseline').expect(200);
        });
      }

      const baselineStats = performanceMeasurer.getStats('baseline');
      expect(baselineStats).toBeDefined();
      expect(baselineStats!.mean).toBeLessThan(50); // Baseline should be very fast

      console.log('Baseline Performance:', baselineStats);
    });

    test('should measure metrics middleware performance impact', async () => {
      // Route with only metrics middleware
      app.use('/api/metrics-only', metricsMiddleware);
      app.get('/api/metrics-only/test', (req, res) => {
        res.json({ message: 'metrics middleware test' });
      });

      // Measure performance with metrics middleware
      for (let i = 0; i < 100; i++) {
        await performanceMeasurer.measure('metrics-middleware', async () => {
          return request(app).get('/api/metrics-only/test').expect(200);
        });
      }

      const metricsStats = performanceMeasurer.getStats('metrics-middleware');
      expect(metricsStats).toBeDefined();
      expect(metricsStats!.mean).toBeLessThan(100); // Should add minimal overhead
      expect(metricsStats!.p95).toBeLessThan(200); // 95th percentile under 200ms

      console.log('Metrics Middleware Performance:', metricsStats);
    });

    test('should measure deduplication middleware performance impact', async () => {
      // Route with deduplication middleware
      app.use('/api/dedup-only', createDeduplicationMiddleware('performance'));
      app.get('/api/dedup-only/test', (req, res) => {
        res.json({ message: 'deduplication middleware test', timestamp: Date.now() });
      });

      // Measure performance with deduplication middleware
      for (let i = 0; i < 100; i++) {
        await performanceMeasurer.measure('dedup-middleware', async () => {
          return request(app).get('/api/dedup-only/test').expect(200);
        });
      }

      const dedupStats = performanceMeasurer.getStats('dedup-middleware');
      expect(dedupStats).toBeDefined();
      expect(dedupStats!.mean).toBeLessThan(150); // Should add minimal overhead
      expect(dedupStats!.p95).toBeLessThan(300); // 95th percentile under 300ms

      console.log('Deduplication Middleware Performance:', dedupStats);
    });

    test('should measure combined middleware performance impact', async () => {
      // Route with all middleware
      app.use('/api/combined', metricsMiddleware);
      app.use('/api/combined', createDeduplicationMiddleware('performance'));
      app.get('/api/combined/test', (req, res) => {
        res.json({ message: 'combined middleware test', timestamp: Date.now() });
      });

      // Measure performance with combined middleware
      for (let i = 0; i < 100; i++) {
        await performanceMeasurer.measure('combined-middleware', async () => {
          return request(app).get('/api/combined/test').expect(200);
        });
      }

      const combinedStats = performanceMeasurer.getStats('combined-middleware');
      expect(combinedStats).toBeDefined();
      expect(combinedStats!.mean).toBeLessThan(200); // Combined overhead should still be reasonable
      expect(combinedStats!.p95).toBeLessThan(500); // 95th percentile under 500ms

      console.log('Combined Middleware Performance:', combinedStats);
    });
  });

  describe('Cache Performance Validation', () => {
    test('should validate cache hit performance vs cache miss', async () => {
      app.use(createDeduplicationMiddleware('api'));
      app.get('/api/cache-test', (req, res) => {
        // Simulate some processing time
        setTimeout(() => {
          res.json({
            message: 'cache test response',
            processed: true,
            timestamp: Date.now(),
          });
        }, 10); // 10ms processing time
      });

      // First request - cache miss
      await performanceMeasurer.measure('cache-miss', async () => {
        return request(app).get('/api/cache-test').expect(200);
      });

      // Subsequent requests - cache hits
      for (let i = 0; i < 50; i++) {
        await performanceMeasurer.measure('cache-hit', async () => {
          return request(app).get('/api/cache-test').expect(200);
        });
      }

      const missStats = performanceMeasurer.getStats('cache-miss');
      const hitStats = performanceMeasurer.getStats('cache-hit');

      expect(missStats).toBeDefined();
      expect(hitStats).toBeDefined();

      // Cache hits should be significantly faster
      expect(hitStats!.mean).toBeLessThan(missStats!.mean);
      expect(hitStats!.mean).toBeLessThan(50); // Cache hits should be very fast
      expect(hitStats!.p95).toBeLessThan(100); // 95th percentile under 100ms

      console.log('Cache Miss Performance:', missStats);
      console.log('Cache Hit Performance:', hitStats);

      // Calculate cache efficiency
      const speedupRatio = missStats!.mean / hitStats!.mean;
      expect(speedupRatio).toBeGreaterThan(1.5); // At least 50% faster
      console.log(`Cache Speedup Ratio: ${speedupRatio.toFixed(2)}x`);
    });

    test('should validate cache performance under high load', async () => {
      app.use(createDeduplicationMiddleware('api'));
      app.get('/api/high-load-cache', (req, res) => {
        res.json({
          message: 'high load test',
          id: req.query.id || 'default',
          timestamp: Date.now(),
        });
      });

      // Generate mixed cache hits and misses
      const requests = [];
      for (let i = 0; i < 200; i++) {
        const id = Math.floor(i / 10); // 10 requests per unique ID (9 cache hits per miss)
        requests.push(
          performanceMeasurer.measure('high-load', async () => {
            return request(app).get(`/api/high-load-cache?id=${id}`).expect(200);
          }),
        );
      }

      await Promise.all(requests);

      const highLoadStats = performanceMeasurer.getStats('high-load');
      expect(highLoadStats).toBeDefined();
      expect(highLoadStats!.mean).toBeLessThan(100); // Should handle high load efficiently
      expect(highLoadStats!.p95).toBeLessThan(200); // 95th percentile under 200ms
      expect(highLoadStats!.max).toBeLessThan(500); // Max response time under 500ms

      console.log('High Load Cache Performance:', highLoadStats);
    });
  });

  describe('Memory Usage Validation', () => {
    test('should validate memory usage with deduplication cache', async () => {
      app.use(createDeduplicationMiddleware('api', { ttl: 300 })); // 5 minute TTL
      app.get('/api/memory-test/:id', (req, res) => {
        res.json({
          id: req.params.id,
          data: 'x'.repeat(1000), // 1KB of data per response
          timestamp: Date.now(),
        });
      });

      const initialMemory = process.memoryUsage();

      // Generate many unique requests to fill cache
      const requests = [];
      for (let i = 0; i < 1000; i++) {
        requests.push(request(app).get(`/api/memory-test/${i}`).expect(200));
      }

      await Promise.all(requests);

      const finalMemory = process.memoryUsage();
      const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed;

      // Memory increase should be reasonable (less than 50MB for 1000 1KB responses)
      expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024); // 50MB

      console.log(`Memory increase: ${(memoryIncrease / 1024 / 1024).toFixed(2)} MB`);
    });

    test('should validate memory cleanup with TTL expiration', async () => {
      // This test would require actual time passage or TTL manipulation
      // For now, we test that the cache doesn't grow indefinitely
      app.use(createDeduplicationMiddleware('api', { ttl: 1 })); // 1 second TTL
      app.get('/api/ttl-test/:id', (req, res) => {
        res.json({ id: req.params.id, timestamp: Date.now() });
      });

      // Generate requests
      for (let i = 0; i < 100; i++) {
        await request(app).get(`/api/ttl-test/${i}`).expect(200);
      }

      // Verify cache has entries
      const cacheSize = mockRedis._getStore().size;
      expect(cacheSize).toBeGreaterThan(0);
      expect(cacheSize).toBeLessThanOrEqual(100);

      console.log(`Cache entries: ${cacheSize}`);
    });
  });

  describe('Concurrent Request Performance', () => {
    test('should handle concurrent requests efficiently', async () => {
      app.use(metricsMiddleware);
      app.use(createDeduplicationMiddleware('api'));
      app.get('/api/concurrent/:id', (req, res) => {
        // Simulate some async work
        setTimeout(() => {
          res.json({
            id: req.params.id,
            processed: true,
            timestamp: Date.now(),
          });
        }, Math.random() * 20); // 0-20ms random delay
      });

      // Send many concurrent requests
      const concurrentRequests = Array.from({ length: 100 }, (_, i) =>
        performanceMeasurer.measure('concurrent', async () => {
          return request(app)
            .get(`/api/concurrent/${i % 10}`)
            .expect(200); // 10 unique IDs
        }),
      );

      await Promise.all(concurrentRequests);

      const concurrentStats = performanceMeasurer.getStats('concurrent');
      expect(concurrentStats).toBeDefined();
      expect(concurrentStats!.mean).toBeLessThan(200); // Should handle concurrency well
      expect(concurrentStats!.p95).toBeLessThan(500); // 95th percentile under 500ms

      console.log('Concurrent Request Performance:', concurrentStats);
    });

    test('should maintain performance under sustained load', async () => {
      app.use(metricsMiddleware);
      app.use(createDeduplicationMiddleware('performance'));
      app.get('/api/sustained-load', (req, res) => {
        res.json({ message: 'sustained load test', timestamp: Date.now() });
      });

      // Simulate sustained load over time
      const batches = 5;
      const requestsPerBatch = 50;

      for (let batch = 0; batch < batches; batch++) {
        const batchRequests = Array.from({ length: requestsPerBatch }, () =>
          performanceMeasurer.measure(`batch-${batch}`, async () => {
            return request(app).get('/api/sustained-load').expect(200);
          }),
        );

        await Promise.all(batchRequests);

        // Small delay between batches
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      // Verify performance doesn't degrade over time
      const firstBatchStats = performanceMeasurer.getStats('batch-0');
      const lastBatchStats = performanceMeasurer.getStats(`batch-${batches - 1}`);

      expect(firstBatchStats).toBeDefined();
      expect(lastBatchStats).toBeDefined();

      // Performance shouldn't degrade significantly
      const degradationRatio = lastBatchStats!.mean / firstBatchStats!.mean;
      expect(degradationRatio).toBeLessThan(2); // Less than 2x degradation

      console.log('First Batch Performance:', firstBatchStats);
      console.log('Last Batch Performance:', lastBatchStats);
      console.log(`Performance Degradation Ratio: ${degradationRatio.toFixed(2)}x`);
    });
  });

  describe('Performance Targets Validation', () => {
    test('should meet reliability enhancement plan performance targets', async () => {
      // Set up complete middleware stack as in production
      app.use(metricsMiddleware);
      app.use(createDeduplicationMiddleware('api'));

      app.get('/api/target-validation', (req, res) => {
        // Simulate typical API response
        res.json({
          data: { id: 1, name: 'Test', status: 'active' },
          meta: { timestamp: Date.now(), version: '1.0' },
        });
      });

      // Test performance targets from reliability plan
      for (let i = 0; i < 100; i++) {
        await performanceMeasurer.measure('target-validation', async () => {
          return request(app).get('/api/target-validation').expect(200);
        });
      }

      const stats = performanceMeasurer.getStats('target-validation');
      expect(stats).toBeDefined();

      // Performance targets from WORKHUB_RELIABILITY_SUMMARY.md
      expect(stats!.p95).toBeLessThan(2000); // < 2 seconds for 95% of requests
      expect(stats!.mean).toBeLessThan(1000); // Average under 1 second
      expect(stats!.max).toBeLessThan(5000); // Max under 5 seconds

      console.log('Performance Targets Validation:', stats);
      console.log(`✅ P95: ${stats!.p95.toFixed(2)}ms (target: <2000ms)`);
      console.log(`✅ Mean: ${stats!.mean.toFixed(2)}ms (target: <1000ms)`);
      console.log(`✅ Max: ${stats!.max.toFixed(2)}ms (target: <5000ms)`);
    });

    test('should validate cache hit rate targets', async () => {
      app.use(createDeduplicationMiddleware('api'));
      app.get('/api/hit-rate-test', (req, res) => {
        res.json({ message: 'hit rate test', timestamp: Date.now() });
      });

      // Generate requests with expected cache hit pattern
      const totalRequests = 100;
      const uniqueRequests = 20; // 80% cache hit rate expected

      for (let i = 0; i < totalRequests; i++) {
        const id = i % uniqueRequests;
        await request(app).get(`/api/hit-rate-test?id=${id}`).expect(200);
      }

      // Import and check deduplication metrics
      const { getDeduplicationMetrics } = await import(
        '../../../middleware/requestDeduplication.js'
      );
      const metrics = getDeduplicationMetrics();

      const hitRate = (metrics.cacheHits / metrics.totalRequests) * 100;

      // Target from reliability plan: > 80% cache hit rate
      expect(hitRate).toBeGreaterThan(80);

      console.log(`Cache Hit Rate: ${hitRate.toFixed(2)}% (target: >80%)`);
      console.log(`Total Requests: ${metrics.totalRequests}`);
      console.log(`Cache Hits: ${metrics.cacheHits}`);
      console.log(`Cache Misses: ${metrics.cacheMisses}`);
    });
  });
});
