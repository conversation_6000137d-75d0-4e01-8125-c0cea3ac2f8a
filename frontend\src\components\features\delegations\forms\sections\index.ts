/**
 * @file Delegation Form Sections - Barrel Export
 * @description Centralized exports for all delegation form section components
 * @module components/delegations/forms/sections
 */

// Section Components - Following SOLID Principles
export { default as DelegationBasicInfoSection } from './DelegationBasicInfoSection';
export { default as DelegationDelegatesSection } from './DelegationDelegatesSection';
export { default as DelegationDriversSection } from './DelegationDriversSection';
export { default as DelegationEscortsSection } from './DelegationEscortsSection';
export { default as DelegationVehiclesSection } from './DelegationVehiclesSection';
export { default as DelegationAssignmentSection } from './DelegationAssignmentSection';
export { default as DelegationFlightSection } from './DelegationFlightSection';
export { default as DelegationNotesSection } from './DelegationNotesSection';

// Export types for external use
export type { DelegationBasicInfoSectionProps } from './DelegationBasicInfoSection';
export type { DelegationDelegatesSectionProps } from './DelegationDelegatesSection';
export type { DelegationDriversSectionProps } from './DelegationDriversSection';
export type { DelegationEscortsSectionProps } from './DelegationEscortsSection';
export type { DelegationVehiclesSectionProps } from './DelegationVehiclesSection';
export type { DelegationAssignmentSectionProps } from './DelegationAssignmentSection';
export type { DelegationFlightSectionProps } from './DelegationFlightSection';
export type { DelegationNotesSectionProps } from './DelegationNotesSection';
