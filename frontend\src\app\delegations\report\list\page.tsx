'use client';

import { format, parseISO } from 'date-fns';

import '@/styles/delegation-report-list.css'; // Import specific styles for this page
import '@/styles/print.css'; // Import enhanced print styles

import {
  Briefcase,
  CalendarDays,
  ChevronDown,
  ChevronUp,
  MapPin,
  Search,
  Users as UsersIcon,
  X,
} from 'lucide-react';
import { useSearchParams } from 'next/navigation';
import { Suspense, useCallback, useEffect, useMemo, useState } from 'react';

import type {
  Delegation,
  DelegationStatusPrisma as DelegationStatus,
} from '@/lib/types/domain'; // Updated import

import { DelegationSummary } from '@/components/reports/DelegationSummary';
import { ReportActions } from '@/components/reports/ReportActions';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { SkeletonLoader } from '@/components/ui/loading';
import { PaginationControls } from '@/components/ui/pagination';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { useDelegations } from '@/lib/stores/queries/useDelegations'; // Added hook
import { cn } from '@/lib/utils';
import { formatDelegationStatusForDisplay } from '@/lib/utils/formattingUtils';

// Status options for filter - uses DelegationStatusPrisma from domain.ts now via alias
const STATUS_OPTIONS: DelegationStatus[] = [
  'Planned',
  'Confirmed',
  'In_Progress',
  'Completed',
  'Cancelled',
  'No_details',
];

// Status color function (unchanged, but status type is now DelegationStatusPrisma)
const getStatusColor = (status: Delegation['status']) => {
  switch (status) {
    case 'Cancelled': {
      return 'bg-red-100 text-red-800 border-red-300';
    }
    case 'Completed': {
      return 'bg-purple-100 text-purple-800 border-purple-300';
    }
    case 'Confirmed': {
      return 'bg-green-100 text-green-800 border-green-300';
    }
    case 'In_Progress': {
      // Matches DelegationStatusPrisma
      return 'bg-yellow-100 text-yellow-800 border-yellow-300';
    }
    case 'Planned': {
      return 'bg-blue-100 text-blue-800 border-blue-300';
    }
    case 'No_details':
    default: {
      return 'bg-gray-100 text-gray-800 border-gray-300';
    }
  }
};

// Date formatting function (unchanged)
const formatDate = (dateString: string | undefined): string => {
  if (!dateString) return 'N/A';
  try {
    return format(parseISO(dateString), 'MMM d, yyyy');
  } catch {
    return 'Invalid Date';
  }
};

export default function DelegationListReportPage() {
  return (
    <Suspense
      fallback={<div className="py-10 text-center">Loading report...</div>}
    >
      <DelegationListReportContent />
    </Suspense>
  );
}

function DelegationListReportContent() {
  const searchParams = useSearchParams();
  const {
    data: allDelegationsData,
    error: delegationsError,
    isLoading: isLoadingDelegations,
    refetch: refetchDelegations,
  } = useDelegations();

  const allDelegations = useMemo(
    () => allDelegationsData || [],
    [allDelegationsData]
  );
  // Filter state
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [dateRange, setDateRange] = useState<{ from?: Date; to?: Date }>({});

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);

  // Sorting state
  const [sortField, setSortField] = useState<string>('durationFrom');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');

  // Initialize from URL params
  useEffect(() => {
    const urlSearchTerm = searchParams?.get('searchTerm') || '';
    const urlStatus = searchParams?.get('status') || 'all';

    setSearchTerm(urlSearchTerm);
    setDebouncedSearchTerm(urlSearchTerm);
    setSelectedStatus(urlStatus);
  }, [searchParams]);

  // Debounce search term
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchTerm]);

  // Document title can be set in a separate useEffect.
  useEffect(() => {
    document.title = 'Delegation List Report';
  }, []);

  // Sort delegations
  const sortDelegations = useCallback(
    (delegations: Delegation[], field: string, direction: 'asc' | 'desc') => {
      return [...delegations].sort((a, b) => {
        let valueA, valueB;

        // Handle different field types
        switch (field) {
          case 'delegates': {
            valueA = a.delegates?.length || 0; // Use optional chaining and default to 0
            valueB = b.delegates?.length || 0; // Use optional chaining and default to 0
            break;
          }
          case 'durationFrom': {
            valueA = new Date(a.durationFrom).getTime();
            valueB = new Date(b.durationFrom).getTime();
            break;
          }
          case 'eventName': {
            valueA = a.eventName.toLowerCase();
            valueB = b.eventName.toLowerCase();
            break;
          }
          case 'location': {
            valueA = a.location.toLowerCase();
            valueB = b.location.toLowerCase();
            break;
          }
          case 'status': {
            valueA = a.status;
            valueB = b.status;
            break;
          }
          default: {
            valueA = a[field as keyof Delegation]; // This might need adjustment if field names changed
            valueB = b[field as keyof Delegation];
          } // This might need adjustment
        }

        // Compare values based on direction
        if (
          valueA === null ||
          valueA === undefined ||
          valueB === null ||
          valueB === undefined
        )
          return 0;
        if (valueA < valueB) return direction === 'asc' ? -1 : 1;
        if (valueA > valueB) return direction === 'asc' ? 1 : -1;
        return 0;
      });
    },
    []
  );

  // 🔧 FIX: Replace useCallback + useEffect + setState with useMemo to prevent infinite loop
  // This prevents the "Maximum update depth exceeded" error by avoiding unstable array references
  const filteredDelegations = useMemo(() => {
    let tempDelegations = [...allDelegations];

    // Apply search filter
    if (debouncedSearchTerm) {
      const lowercasedSearch = debouncedSearchTerm.toLowerCase();
      tempDelegations = tempDelegations.filter(delegation => {
        return (
          delegation.eventName.toLowerCase().includes(lowercasedSearch) ||
          delegation.location.toLowerCase().includes(lowercasedSearch) ||
          // Assuming delegation.delegates is still available or transformed by useDelegations hook
          delegation.delegates?.some((d: any) =>
            d.name.toLowerCase().includes(lowercasedSearch)
          ) ||
          delegation.notes?.toLowerCase().includes(lowercasedSearch) ||
          delegation.status.toLowerCase().includes(lowercasedSearch) // status is DelegationStatusPrisma
        );
      });
    }

    // Apply status filter
    if (selectedStatus !== 'all') {
      tempDelegations = tempDelegations.filter(
        delegation => delegation.status === selectedStatus
      );
    }

    // Apply date range filter
    if (dateRange.from) {
      tempDelegations = tempDelegations.filter(delegation => {
        const delegationDate = new Date(delegation.durationFrom);
        return delegationDate >= dateRange.from!;
      });
    }

    if (dateRange.to) {
      tempDelegations = tempDelegations.filter(delegation => {
        const delegationDate = new Date(delegation.durationFrom);
        return delegationDate <= dateRange.to!;
      });
    }

    // Apply sorting
    return sortDelegations(tempDelegations, sortField, sortDirection);
  }, [
    allDelegations,
    debouncedSearchTerm,
    selectedStatus,
    dateRange,
    sortField,
    sortDirection,
    sortDelegations,
  ]);

  // Handle sort
  const handleSort = useCallback(
    (field: string) => {
      if (sortField === field) {
        setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
      } else {
        setSortField(field);
        setSortDirection('asc');
      }
    },
    [sortField, sortDirection]
  );

  // Convert sort direction to aria-sort value
  const getAriaSortValue = useCallback(
    (field: string): 'ascending' | 'descending' | 'none' | undefined => {
      if (sortField !== field) return 'none';
      return sortDirection === 'asc' ? 'ascending' : 'descending';
    },
    [sortField, sortDirection]
  );

  // Reset filters
  const resetFilters = useCallback(() => {
    setSearchTerm('');
    setDebouncedSearchTerm('');
    setSelectedStatus('all');
    setDateRange({});
    setCurrentPage(1);
  }, []);

  // Initial data fetch useEffect is removed.

  // Pagination logic
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const paginatedDelegations = useMemo(
    () => filteredDelegations.slice(indexOfFirstItem, indexOfLastItem),
    [filteredDelegations, indexOfFirstItem, indexOfLastItem]
  );

  const totalPages = useMemo(
    () => Math.ceil(filteredDelegations.length / itemsPerPage),
    [filteredDelegations.length, itemsPerPage]
  );

  // Handle page change
  const handlePageChange = useCallback((page: number) => {
    setCurrentPage(page);
  }, []);

  // Render sort indicator
  const renderSortIndicator = useCallback(
    (field: string) => {
      if (sortField !== field) return null;
      return sortDirection === 'asc' ? (
        <ChevronUp className="ml-1 inline-block size-4" />
      ) : (
        <ChevronDown className="ml-1 inline-block size-4" />
      );
    },
    [sortField, sortDirection]
  );

  if (isLoadingDelegations) {
    // Use hook's loading state
    return (
      <div className="mx-auto max-w-5xl p-4">
        <SkeletonLoader count={5} variant="table" />
      </div>
    );
  }

  if (delegationsError) {
    return (
      <div className="mx-auto max-w-5xl p-4 text-red-500">
        Error loading delegations: {(delegationsError as Error).message}
        <Button className="ml-2" onClick={() => refetchDelegations()}>
          Retry
        </Button>
      </div>
    );
  }

  return (
    <div className="delegation-report-container">
      <div className="no-print mb-6 text-right">
        <ReportActions
          enableCsv={filteredDelegations.length > 0}
          fileName={`delegations-list-report-${
            new Date().toISOString().split('T')[0]
          }`}
          reportContentId="#delegations-list-report-content"
          reportType="delegations"
          tableId="#delegations-table"
        />
      </div>

      <div className="report-content" id="delegations-list-report-content">
        {/* Enhanced header for both screen and print */}
        <header className="delegation-report-header">
          <div className="mb-6">
            <h1 className="delegation-report-title">Delegation List Report</h1>
            <div className="no-print mx-auto h-1 w-24 rounded-full bg-gradient-to-r from-blue-500 to-indigo-600"></div>
          </div>
          <p className="delegation-report-subtitle">
            {searchTerm || selectedStatus !== 'all'
              ? `Filtered by: ${
                  selectedStatus === 'all'
                    ? ''
                    : `Status - ${formatDelegationStatusForDisplay(
                        selectedStatus as DelegationStatus
                      )}`
                }${
                  searchTerm
                    ? (selectedStatus === 'all' ? '' : ' | ') +
                      `Search - "${searchTerm}"`
                    : ''
                }`
              : 'All Delegations'}
          </p>
          <p className="delegation-report-date">
            Generated: {new Date().toLocaleDateString()}{' '}
            {new Date().toLocaleTimeString([], {
              hour: '2-digit',
              minute: '2-digit',
            })}
          </p>

          {/* Screen-only Summary Statistics */}
          <div className="no-print delegation-summary-grid">
            <DelegationSummary
              delegations={
                filteredDelegations.map(d => ({
                  ...d,
                  // Shim for DelegationSummary if it expects old type
                  delegates: d.delegates || [], // Ensure delegates is an array
                  escortEmployeeIds:
                    d.escorts?.map(e => e.employeeId.toString()) || [], // Example shim
                  // Add other properties expected by old Delegation type if necessary
                })) as any // Cast to any to bypass strict type checking for the shimmed prop
              }
            />
          </div>

          {/* Print-only linear summary statistics - A4 optimized */}
          <div className="print-only delegation-print-summary">
            <div className="delegation-print-summary-item">
              <span className="delegation-print-summary-label">
                Total Delegations:
              </span>{' '}
              <span className="delegation-print-summary-value">
                {filteredDelegations.length}
              </span>
            </div>

            <div className="delegation-print-summary-item">
              <span className="delegation-print-summary-label">
                Total Delegates:
              </span>{' '}
              <span className="delegation-print-summary-value">
                {filteredDelegations.reduce(
                  (acc, delegation) =>
                    acc + (delegation.delegates?.length || 0), // Use optional chaining and default to 0
                  0
                )}
              </span>
            </div>

            {/* Only show top 3 statuses with counts to save space */}
            {STATUS_OPTIONS.map(status => ({
              count: filteredDelegations.filter(d => d.status === status)
                .length,
              status,
            }))
              .filter(item => item.count > 0)
              .sort((a, b) => b.count - a.count)
              .slice(0, 3)
              .map(({ count, status }) => (
                <div className="delegation-print-summary-item" key={status}>
                  <span className="delegation-print-summary-label">
                    {formatDelegationStatusForDisplay(status)}:
                  </span>{' '}
                  <span className="delegation-print-summary-value">
                    {count}
                  </span>
                </div>
              ))}

            {/* Show filter information if applied */}
            {selectedStatus !== 'all' && (
              <div className="delegation-print-summary-item">
                <span className="delegation-print-summary-label">
                  Filtered by Status:
                </span>{' '}
                <span className="delegation-print-summary-value">
                  {formatDelegationStatusForDisplay(
                    selectedStatus as DelegationStatus
                  )}
                </span>
              </div>
            )}

            {searchTerm && (
              <div className="delegation-print-summary-item">
                <span className="delegation-print-summary-label">
                  Search Term:
                </span>{' '}
                <span className="delegation-print-summary-value">
                  "{searchTerm}"
                </span>
              </div>
            )}
          </div>
        </header>

        {/* Enhanced Filter Section */}
        <div className="no-print mb-8">
          <Card className="border-0 bg-gradient-to-r from-slate-50 to-gray-50 shadow-lg">
            <CardContent className="p-6">
              <div className="delegation-filters">
                {/* Status Filter */}
                <div>
                  <label
                    className="mb-2 block text-sm font-semibold text-gray-700"
                    htmlFor="status-filter"
                  >
                    Filter by Status
                  </label>
                  <Select
                    aria-label="Filter by status"
                    onValueChange={setSelectedStatus}
                    value={selectedStatus}
                  >
                    <SelectTrigger className="w-full border-gray-300 bg-white focus:border-blue-500 focus:ring-blue-500">
                      <SelectValue placeholder="All Statuses" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Statuses</SelectItem>
                      {STATUS_OPTIONS.map(status => (
                        <SelectItem key={status} value={status}>
                          {formatDelegationStatusForDisplay(status)}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Search Input */}
                <div className="relative">
                  <label
                    className="mb-2 block text-sm font-semibold text-gray-700"
                    htmlFor="search-input"
                  >
                    Search Delegations
                  </label>
                  <div className="relative">
                    <Input
                      aria-label="Search delegations"
                      className="border-gray-300 bg-white px-10 focus:border-blue-500 focus:ring-blue-500"
                      id="search-input"
                      onChange={e => setSearchTerm(e.target.value)}
                      placeholder="Search by event, location, or delegate..."
                      type="text"
                      value={searchTerm}
                    />
                    <Search
                      aria-hidden="true"
                      className="absolute left-3 top-1/2 size-5 -translate-y-1/2 text-gray-400"
                    />
                    {searchTerm && (
                      <Button
                        aria-label="Clear search"
                        className="absolute right-1 top-1/2 size-7 -translate-y-1/2"
                        onClick={() => setSearchTerm('')}
                        size="icon"
                        variant="ghost"
                      >
                        <X className="size-4" />
                        <span className="sr-only">Clear search</span>
                      </Button>
                    )}
                  </div>
                </div>

                {/* Date Range Filter - Placeholder for future implementation */}
                <div>
                  <label
                    className="mb-2 block text-sm font-semibold text-gray-700"
                    htmlFor="date-range"
                  >
                    Date Range
                  </label>
                  <Input
                    aria-label="Date range filter (coming soon)"
                    className="bg-gray-100 opacity-50"
                    disabled
                    id="date-range"
                    placeholder="Date range filter coming soon"
                    type="text"
                  />
                </div>
              </div>

              {/* Filter Summary & Reset */}
              {(searchTerm || selectedStatus !== 'all') && (
                <div className="mt-6 flex items-center justify-between rounded-lg border border-blue-200 bg-blue-50 p-4">
                  <div className="text-sm">
                    <span className="font-semibold text-blue-800">
                      Active Filters:
                    </span>
                    {searchTerm && (
                      <span className="ml-2 rounded bg-blue-100 px-2 py-1 text-xs text-blue-800">
                        Search: "{searchTerm}"
                      </span>
                    )}
                    {selectedStatus !== 'all' && (
                      <span className="ml-2 rounded bg-green-100 px-2 py-1 text-xs text-green-800">
                        Status:{' '}
                        {formatDelegationStatusForDisplay(
                          selectedStatus as DelegationStatus
                        )}
                      </span>
                    )}
                  </div>
                  <Button
                    aria-label="Reset all filters"
                    className="border-blue-300 text-blue-700 hover:bg-blue-100"
                    onClick={resetFilters}
                    size="sm"
                    variant="outline"
                  >
                    Reset Filters
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Empty State */}
        {filteredDelegations.length === 0 ? (
          <div className="rounded-xl border border-gray-200 bg-gradient-to-br from-gray-50 to-slate-100 py-16 text-center">
            <div className="mx-auto max-w-md">
              <div className="mb-4 text-gray-400">
                <CalendarDays className="mx-auto mb-4 size-16" />
              </div>
              <h3 className="mb-2 text-lg font-semibold text-gray-700">
                No delegations found
              </h3>
              <p className="mb-4 text-gray-500">
                No delegations match the current filter criteria.
              </p>
              <Button
                aria-label="Reset filters to show all delegations"
                className="mt-2"
                onClick={resetFilters}
                size="lg"
                variant="outline"
              >
                Reset Filters
              </Button>
            </div>
          </div>
        ) : (
          <>
            {/* Enhanced Table with Sorting */}
            <div className="delegation-table-container">
              <Table className="delegation-table" id="delegations-table">
                <TableHeader>
                  <TableRow className="border-b border-gray-200 bg-gradient-to-r from-slate-100 to-gray-100">
                    <TableHead
                      aria-label="Sort by event name"
                      aria-sort={getAriaSortValue('eventName')}
                      className="cursor-pointer font-semibold text-gray-700 transition-colors hover:bg-slate-200"
                      onClick={() => handleSort('eventName')}
                      onKeyDown={e => {
                        if (e.key === 'Enter' || e.key === ' ') {
                          e.preventDefault();
                          handleSort('eventName');
                        }
                      }}
                      role="columnheader"
                      style={{ width: '25%' }}
                      tabIndex={0}
                    >
                      Event Name {renderSortIndicator('eventName')}
                    </TableHead>
                    <TableHead
                      aria-label="Sort by location"
                      aria-sort={getAriaSortValue('location')}
                      className="cursor-pointer font-semibold text-gray-700 transition-colors hover:bg-slate-200"
                      onClick={() => handleSort('location')}
                      onKeyDown={e => {
                        if (e.key === 'Enter' || e.key === ' ') {
                          e.preventDefault();
                          handleSort('location');
                        }
                      }}
                      role="columnheader"
                      style={{ width: '20%' }}
                      tabIndex={0}
                    >
                      Location {renderSortIndicator('location')}
                    </TableHead>
                    <TableHead
                      aria-label="Sort by duration"
                      aria-sort={getAriaSortValue('durationFrom')}
                      className="cursor-pointer font-semibold text-gray-700 transition-colors hover:bg-slate-200"
                      onClick={() => handleSort('durationFrom')}
                      onKeyDown={e => {
                        if (e.key === 'Enter' || e.key === ' ') {
                          e.preventDefault();
                          handleSort('durationFrom');
                        }
                      }}
                      role="columnheader"
                      style={{ width: '20%' }}
                      tabIndex={0}
                    >
                      Duration {renderSortIndicator('durationFrom')}
                    </TableHead>
                    <TableHead
                      aria-label="Sort by status"
                      aria-sort={getAriaSortValue('status')}
                      className="cursor-pointer font-semibold text-gray-700 transition-colors hover:bg-slate-200"
                      onClick={() => handleSort('status')}
                      onKeyDown={e => {
                        if (e.key === 'Enter' || e.key === ' ') {
                          e.preventDefault();
                          handleSort('status');
                        }
                      }}
                      role="columnheader"
                      style={{ width: '10%' }}
                      tabIndex={0}
                    >
                      Status {renderSortIndicator('status')}
                    </TableHead>
                    <TableHead
                      aria-label="Sort by number of delegates"
                      aria-sort={getAriaSortValue('delegates')}
                      className="cursor-pointer font-semibold text-gray-700 transition-colors hover:bg-slate-200"
                      onClick={() => handleSort('delegates')}
                      onKeyDown={e => {
                        if (e.key === 'Enter' || e.key === ' ') {
                          e.preventDefault();
                          handleSort('delegates');
                        }
                      }}
                      role="columnheader"
                      style={{ width: '25%' }}
                      tabIndex={0}
                    >
                      Delegates {renderSortIndicator('delegates')}
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody className="no-print">
                  {paginatedDelegations.map((delegation, index) => (
                    <TableRow
                      className={cn(
                        'page-break-inside-avoid hover:bg-slate-50 transition-colors border-b border-gray-100',
                        index % 2 === 0 ? 'bg-white' : 'bg-slate-50/30'
                      )}
                      key={delegation.id}
                    >
                      <TableCell
                        className="print-text-wrap p-4 font-medium"
                        title={delegation.eventName}
                      >
                        <div className="font-semibold text-gray-800">
                          {delegation.eventName}
                        </div>
                      </TableCell>
                      <TableCell
                        className="print-text-wrap print-location-col p-4"
                        title={delegation.location}
                      >
                        <div className="flex items-center text-gray-600">
                          <MapPin className="mr-2 size-4 text-gray-400" />
                          {delegation.location}
                        </div>
                      </TableCell>
                      <TableCell className="whitespace-nowrap p-4">
                        <div className="flex items-center text-gray-600">
                          <CalendarDays className="mr-2 size-4 text-gray-400" />
                          <div className="text-sm">
                            <div>{formatDate(delegation.durationFrom)}</div>
                            <div className="text-xs text-gray-500">
                              to {formatDate(delegation.durationTo)}
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="p-4">
                        <Badge
                          className={cn(
                            'text-xs py-1 px-2 font-medium',
                            getStatusColor(delegation.status)
                          )}
                        >
                          {formatDelegationStatusForDisplay(delegation.status)}
                        </Badge>
                      </TableCell>
                      <TableCell
                        className="print-text-wrap max-w-xs p-4"
                        title={delegation.delegates
                          ?.map(d => d.name)
                          .join(', ')}
                      >
                        {(delegation.delegates?.length || 0) > 0 ? (
                          <div className="flex items-start">
                            <UsersIcon className="mr-2 mt-0.5 size-4 shrink-0 text-gray-400" />
                            <div>
                              {/* Screen view - truncate long lists */}
                              <span className="no-print">
                                {(delegation.delegates?.length || 0) <= 3 ? (
                                  <div className="space-y-1">
                                    {delegation.delegates?.map((d, i) => (
                                      <div
                                        className="text-sm text-gray-700"
                                        key={d.id || i} // Use d.id if available, else index
                                      >
                                        {d.name}
                                      </div>
                                    ))}
                                  </div>
                                ) : (
                                  <div>
                                    <div className="mb-1 space-y-1">
                                      {delegation.delegates
                                        ?.slice(0, 2)
                                        .map((d, i) => (
                                          <div
                                            className="text-sm text-gray-700"
                                            key={d.id || i}
                                          >
                                            {d.name}
                                          </div>
                                        ))}
                                    </div>
                                    <span className="rounded bg-gray-100 px-2 py-1 text-xs text-gray-500">
                                      +{(delegation.delegates?.length || 0) - 2}{' '}
                                      more
                                    </span>
                                  </div>
                                )}
                              </span>

                              {/* Print view - show all delegates */}
                              <span className="print-only">
                                <div className="space-y-1">
                                  {delegation.delegates?.map((d, i) => (
                                    <div className="text-sm" key={d.id || i}>
                                      {d.name}
                                    </div>
                                  ))}
                                </div>
                              </span>
                            </div>
                          </div>
                        ) : (
                          <span className="text-sm text-gray-400">
                            No delegates
                          </span>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
                <TableBody className="print-only">
                  {filteredDelegations.map((delegation, index) => (
                    <TableRow
                      className={cn(
                        'page-break-inside-avoid', // Removed border-b and border-gray-100
                        index % 2 === 0 ? 'bg-white' : 'bg-slate-50/30'
                      )}
                      key={delegation.id}
                    >
                      <TableCell
                        className="print-text-wrap font-medium" // Removed py-4 px-4
                        title={delegation.eventName}
                      >
                        <div className="font-semibold text-gray-800">
                          {delegation.eventName}
                        </div>
                      </TableCell>
                      <TableCell
                        className="print-text-wrap print-location-col" // Removed py-4 px-4
                        title={delegation.location}
                      >
                        <div className="flex items-center text-gray-600">
                          <MapPin className="mr-2 size-4 text-gray-400" />
                          {delegation.location}
                        </div>
                      </TableCell>
                      <TableCell
                        className="" /* Removed whitespace-nowrap py-4 px-4 */
                      >
                        <div className="flex items-center text-gray-600">
                          <CalendarDays className="mr-2 size-4 text-gray-400" />
                          <div className="text-sm">
                            <div>{formatDate(delegation.durationFrom)}</div>
                            <div className="text-xs text-gray-500">
                              to {formatDate(delegation.durationTo)}
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="" /* Removed py-4 px-4 */>
                        <Badge
                          className={cn(
                            'text-xs py-1 px-2 font-medium',
                            getStatusColor(delegation.status)
                          )}
                        >
                          {formatDelegationStatusForDisplay(delegation.status)}
                        </Badge>
                      </TableCell>
                      <TableCell
                        className="print-text-wrap" // Removed max-w-xs py-4 px-4
                        title={delegation.delegates
                          ?.map(d => d.name)
                          .join(', ')}
                      >
                        {(delegation.delegates?.length || 0) > 0 ? (
                          <div className="flex items-start">
                            <UsersIcon className="mr-2 mt-0.5 size-4 shrink-0 text-gray-400" />
                            <div>
                              <div className="space-y-1">
                                {delegation.delegates?.map((d, i) => (
                                  <div className="text-sm" key={d.id || i}>
                                    {d.name}
                                  </div>
                                ))}
                              </div>
                            </div>
                          </div>
                        ) : (
                          <span className="text-sm text-gray-400">
                            No delegates
                          </span>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>

            {/* Enhanced Pagination */}
            {filteredDelegations.length > itemsPerPage && (
              <div className="no-print mt-8 flex justify-center">
                <PaginationControls
                  currentPage={currentPage}
                  onPageChange={handlePageChange}
                  totalPages={totalPages}
                />
              </div>
            )}
          </>
        )}

        <footer className="delegation-report-footer">
          <div className="space-y-2">
            <p className="font-medium">
              Report generated on: {new Date().toLocaleDateString()}{' '}
              {new Date().toLocaleTimeString([], {
                hour: '2-digit',
                minute: '2-digit',
              })}
            </p>
            <p className="text-gray-400">WorkHub - Delegation Management</p>
            <p className="print-only text-xs">
              Confidential - For internal use only
            </p>
          </div>
        </footer>
      </div>
    </div>
  );
}
