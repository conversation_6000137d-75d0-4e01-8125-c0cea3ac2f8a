/**
 * JWT Claims Verification Script
 *
 * Run this script after configuring the Custom Access Token Hook
 * to verify that JWT tokens contain the proper custom claims.
 */

import { createClient } from '@supabase/supabase-js';
import jwt from 'jsonwebtoken';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseAnonKey = process.env.SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing Supabase environment variables');
  console.error('Required: SUPABASE_URL, SUPABASE_ANON_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function verifyJWTClaims() {
  console.log('🔍 JWT Claims Verification');
  console.log('==========================\n');

  try {
    // Test credentials
    const testEmail = '<EMAIL>';
    const testPassword = 'TestPassword123!';

    console.log('📋 Step 1: Sign in to get fresh JWT token');
    console.log('==========================================');

    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: testEmail,
      password: testPassword,
    });

    if (authError) {
      console.error('❌ Authentication failed:', authError.message);
      return;
    }

    console.log('✅ Authentication successful');
    console.log(`   User ID: ${authData.user.id}`);
    console.log(`   Email: ${authData.user.email}`);

    console.log('\n📋 Step 2: Decode JWT token');
    console.log('============================');

    const accessToken = authData.session.access_token;
    const decodedToken = jwt.decode(accessToken, { complete: true });

    if (!decodedToken) {
      console.error('❌ Failed to decode JWT token');
      return;
    }

    console.log('✅ JWT token decoded successfully');
    console.log('   Token payload:', JSON.stringify(decodedToken.payload, null, 2));

    console.log('\n📋 Step 3: Verify custom claims');
    console.log('================================');

    const customClaims = decodedToken.payload.custom_claims;

    if (!customClaims) {
      console.error('❌ No custom_claims found in JWT token');
      console.error('💡 This means the Custom Access Token Hook is not configured or not working');
      console.error('💡 Please configure the hook in Supabase Dashboard: Authentication → Hooks');
      return;
    }

    console.log('✅ Custom claims found in JWT token');
    console.log('   Custom claims:', JSON.stringify(customClaims, null, 2));

    // Verify expected claims structure
    const expectedClaims = ['user_role', 'is_active', 'employee_id'];
    const missingClaims = expectedClaims.filter(claim => !(claim in customClaims));

    if (missingClaims.length > 0) {
      console.error('❌ Missing expected claims:', missingClaims);
    } else {
      console.log('✅ All expected claims present');
    }

    console.log('\n📋 Step 4: Test API call with JWT');
    console.log('==================================');

    // Test an authenticated API call
    const { data: employeesData, error: apiError } = await supabase
      .from('Employee')
      .select('*')
      .limit(1);

    if (apiError) {
      console.error('❌ API call failed:', apiError.message);
    } else {
      console.log('✅ API call successful');
      console.log(`   Retrieved ${employeesData?.length || 0} employees`);
    }

    console.log('\n🎉 Verification Complete');
    console.log('========================');
    console.log('✅ JWT tokens now contain proper custom claims');
    console.log('✅ RBAC system should be working correctly');
    console.log('✅ Frontend authentication should work');
  } catch (error) {
    console.error('❌ Verification failed:', error.message);
  }
}

// Run verification
verifyJWTClaims();
