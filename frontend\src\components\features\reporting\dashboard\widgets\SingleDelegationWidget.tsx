// frontend/src/components/features/reporting/dashboard/widgets/SingleDelegationWidget.tsx

import React from 'react';
import { BaseWidget } from './BaseWidget';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
// Import real delegation hook from existing API integration
import { useDelegation } from '@/lib/stores/queries/useDelegations';
import {
  Calendar,
  MapPin,
  Users,
  Car,
  Clock,
  CheckCircle,
  AlertCircle,
  FileText,
  Download,
  ExternalLink,
} from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';

interface SingleDelegationWidgetProps {
  delegationId: string;
  title?: string;
  showActions?: boolean;
  onExport?: () => void;
  onViewDetails?: () => void;
}

/**
 * @component SingleDelegationWidget
 * @description Widget for displaying detailed information about a single delegation
 *
 * Responsibilities:
 * - Shows comprehensive delegation details
 * - Displays progress and status information
 * - Provides quick actions for the delegation
 * - Handles loading and error states
 *
 * SOLID Principles Applied:
 * - SRP: Single responsibility of presenting single delegation data
 * - OCP: Open for extension via props
 * - DIP: Depends on delegation hook abstractions
 */
export const SingleDelegationWidget: React.FC<SingleDelegationWidgetProps> = ({
  delegationId,
  title = 'Delegation Details',
  showActions = true,
  onExport,
  onViewDetails,
}) => {
  const {
    data: delegation,
    isLoading,
    error,
    refetch,
  } = useDelegation(delegationId);

  // Calculate delegation progress
  const getProgress = () => {
    if (!delegation) return 0;

    const statusProgress = {
      DRAFT: 10,
      PENDING: 25,
      APPROVED: 50,
      IN_PROGRESS: 75,
      COMPLETED: 100,
      CANCELLED: 0,
    };

    return (
      statusProgress[delegation.status as keyof typeof statusProgress] || 0
    );
  };

  // Get status color
  const getStatusColor = (status: string) => {
    const colors = {
      DRAFT: 'bg-gray-100 text-gray-800',
      PENDING: 'bg-yellow-100 text-yellow-800',
      APPROVED: 'bg-blue-100 text-blue-800',
      IN_PROGRESS: 'bg-purple-100 text-purple-800',
      COMPLETED: 'bg-green-100 text-green-800',
      CANCELLED: 'bg-red-100 text-red-800',
    };
    return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  // Handle actions
  const handleExport = () => {
    if (onExport) {
      onExport();
    } else {
      console.log('Exporting delegation:', delegationId);
    }
  };

  const handleViewDetails = () => {
    if (onViewDetails) {
      onViewDetails();
    } else {
      window.open(`/delegations/${delegationId}`, '_blank');
    }
  };

  // Render actions
  const renderActions = () => {
    if (!showActions) return null;

    return (
      <div className="flex items-center gap-2">
        <Button variant="outline" size="sm" onClick={handleExport}>
          <Download className="h-4 w-4 mr-2" />
          Export
        </Button>
        <Button variant="outline" size="sm" onClick={handleViewDetails}>
          <ExternalLink className="h-4 w-4 mr-2" />
          View
        </Button>
      </div>
    );
  };

  // Render delegation info cards
  const renderInfoCards = () => {
    if (!delegation) return null;

    const cards = [
      {
        icon: Calendar,
        label: 'Start Date',
        value: delegation.durationFrom
          ? format(new Date(delegation.durationFrom), 'MMM d, yyyy')
          : 'Not set',
      },
      {
        icon: Calendar,
        label: 'End Date',
        value: delegation.durationTo
          ? format(new Date(delegation.durationTo), 'MMM d, yyyy')
          : 'Not set',
      },
      {
        icon: MapPin,
        label: 'Location',
        value: delegation.location || 'Not specified',
      },
      {
        icon: Users,
        label: 'Delegates',
        value: delegation.delegates?.length || 0,
      },
    ];

    return (
      <div className="grid grid-cols-2 gap-3">
        {cards.map((card, index) => {
          const Icon = card.icon;
          return (
            <div
              key={index}
              className="flex items-center gap-2 p-2 bg-muted/50 rounded"
            >
              <Icon className="h-4 w-4 text-muted-foreground" />
              <div>
                <p className="text-xs text-muted-foreground">{card.label}</p>
                <p className="text-sm font-medium">{card.value}</p>
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  return (
    <BaseWidget
      title={title}
      loading={isLoading}
      error={error?.message}
      actions={renderActions()}
    >
      {delegation && (
        <div className="space-y-4">
          {/* Delegation Header */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <h3 className="font-semibold text-lg truncate">
                {delegation.eventName}
              </h3>
              <Badge
                className={cn('text-xs', getStatusColor(delegation.status))}
              >
                {delegation.status.replace('_', ' ')}
              </Badge>
            </div>

            {delegation.notes && (
              <p className="text-sm text-muted-foreground line-clamp-2">
                {delegation.notes}
              </p>
            )}
          </div>

          {/* Progress */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Progress</span>
              <span className="text-sm text-muted-foreground">
                {getProgress()}%
              </span>
            </div>
            <Progress value={getProgress()} className="h-2" />
          </div>

          <Separator />

          {/* Info Cards */}
          {renderInfoCards()}

          {/* Delegates Summary */}
          {delegation.delegates && delegation.delegates.length > 0 && (
            <>
              <Separator />
              <div className="space-y-2">
                <h4 className="font-medium flex items-center gap-2">
                  <Users className="h-4 w-4" />
                  Delegates ({delegation.delegates.length})
                </h4>
                <div className="space-y-1">
                  {delegation.delegates
                    .slice(0, 3)
                    .map((delegate: any, index: number) => (
                      <div
                        key={index}
                        className="text-sm text-muted-foreground"
                      >
                        {delegate.name} - {delegate.title}
                      </div>
                    ))}
                  {delegation.delegates.length > 3 && (
                    <div className="text-xs text-muted-foreground">
                      +{delegation.delegates.length - 3} more
                    </div>
                  )}
                </div>
              </div>
            </>
          )}
        </div>
      )}
    </BaseWidget>
  );
};

export default SingleDelegationWidget;
