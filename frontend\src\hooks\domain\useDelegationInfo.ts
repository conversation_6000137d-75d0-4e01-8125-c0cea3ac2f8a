/**
 * @file Custom hook for delegation info extraction
 * @module hooks/useDelegationInfo
 */

import { useMemo } from 'react';
import type {
  Delegation,
  DelegationEscort,
  DelegationVehicleAssignment,
} from '@/lib/types/domain';

// Define types for the partial employee/vehicle data
type PartialEmployeeInfo = NonNullable<DelegationEscort['employee']>;
type PartialVehicleInfo = NonNullable<DelegationVehicleAssignment['vehicle']>;

interface DelegationInfo {
  escortInfo: PartialEmployeeInfo | null;
  driverInfo: PartialEmployeeInfo | null;
  vehicleInfo: PartialVehicleInfo | null;
  hasFlightDetails: boolean;
  needsEscortAssignment: boolean;
  isActive: boolean;
}

/**
 * Custom hook to extract and process delegation information
 * Follows SRP by handling only data extraction logic
 *
 * @param delegation - Delegation object
 * @returns Processed delegation information
 */
export const useDelegationInfo = (delegation: Delegation): DelegationInfo => {
  return useMemo(() => {
    // Extract escort information
    const escortInfo =
      delegation.escorts &&
      delegation.escorts.length > 0 &&
      delegation.escorts[0]?.employee
        ? delegation.escorts[0].employee
        : null;

    // Extract driver information
    const driverInfo =
      delegation.drivers &&
      delegation.drivers.length > 0 &&
      delegation.drivers[0]?.employee
        ? delegation.drivers[0].employee
        : null;

    // Extract vehicle information
    const vehicleInfo =
      delegation.vehicles &&
      delegation.vehicles.length > 0 &&
      delegation.vehicles[0]?.vehicle
        ? delegation.vehicles[0].vehicle
        : null;

    // Check if flight details exist
    const hasFlightDetails = Boolean(
      delegation.arrivalFlight || delegation.departureFlight
    );

    // Check if escort assignment is needed
    const needsEscortAssignment =
      !escortInfo &&
      delegation.status !== 'Completed' &&
      delegation.status !== 'Cancelled';

    // Check if delegation is currently active
    const isActive = delegation.status === 'In_Progress';

    return {
      escortInfo,
      driverInfo,
      vehicleInfo,
      hasFlightDetails,
      needsEscortAssignment,
      isActive,
    };
  }, [delegation]);
};

export default useDelegationInfo;
