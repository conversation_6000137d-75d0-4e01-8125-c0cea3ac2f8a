/**
 * @file Tests for HealthTrendCharts widget - verifying real API integration
 * @description Tests to ensure HealthTrendCharts uses real API data instead of mock data
 */

import React from 'react';
import { render } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { HealthTrendCharts } from '../HealthTrendCharts';

// Mock the reliability hooks
jest.mock('@/lib/stores/queries/useReliability', () => ({
  useHealthTrends: jest.fn(),
}));

const { useHealthTrends } = require('@/lib/stores/queries/useReliability');

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('HealthTrendCharts Widget - Real API Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should use real API data for health trends', () => {
    const mockTrendData = {
      timeframe: '24h',
      dataPoints: [
        {
          timestamp: '2024-01-01T10:00:00Z',
          status: 'healthy',
          responseTime: 150,
          checks: {
            database: 'healthy',
            supabase: 'healthy',
            cache: 'healthy',
            systemResources: 'healthy',
            circuitBreakers: 'healthy',
            businessLogic: 'healthy',
          },
          summary: {
            totalChecks: 6,
            healthyChecks: 6,
            degradedChecks: 0,
            unhealthyChecks: 0,
          },
        },
        {
          timestamp: '2024-01-01T11:00:00Z',
          status: 'degraded',
          responseTime: 200,
          checks: {
            database: 'healthy',
            supabase: 'degraded',
            cache: 'healthy',
            systemResources: 'healthy',
            circuitBreakers: 'healthy',
            businessLogic: 'healthy',
          },
          summary: {
            totalChecks: 6,
            healthyChecks: 5,
            degradedChecks: 1,
            unhealthyChecks: 0,
          },
        },
      ],
      aggregates: {
        averageResponseTime: 175,
        uptimePercentage: 83.33,
        mostCommonStatus: 'healthy',
        totalDataPoints: 2,
      },
    };

    useHealthTrends.mockReturnValue({
      data: mockTrendData,
      isLoading: false,
      error: null,
    });

    const { container } = render(
      <TestWrapper>
        <HealthTrendCharts />
      </TestWrapper>
    );

    // Verify the component renders without errors
    expect(container).toBeTruthy();
    
    // Verify useHealthTrends was called with default timeframe
    expect(useHealthTrends).toHaveBeenCalledWith('24h');
  });

  it('should handle different timeframes', () => {
    const mockTrendData = {
      timeframe: '1h',
      dataPoints: [],
      aggregates: {
        averageResponseTime: 0,
        uptimePercentage: 100,
        mostCommonStatus: 'healthy',
        totalDataPoints: 0,
      },
    };

    useHealthTrends.mockReturnValue({
      data: mockTrendData,
      isLoading: false,
      error: null,
    });

    render(
      <TestWrapper>
        <HealthTrendCharts defaultTimeframe="1h" />
      </TestWrapper>
    );

    // Verify useHealthTrends was called with the specified timeframe
    expect(useHealthTrends).toHaveBeenCalledWith('1h');
  });

  it('should handle loading state', () => {
    useHealthTrends.mockReturnValue({
      data: null,
      isLoading: true,
      error: null,
    });

    const { container } = render(
      <TestWrapper>
        <HealthTrendCharts />
      </TestWrapper>
    );

    expect(container).toBeTruthy();
    expect(useHealthTrends).toHaveBeenCalled();
  });

  it('should handle error state', () => {
    useHealthTrends.mockReturnValue({
      data: null,
      isLoading: false,
      error: { message: 'API Error' },
    });

    const { container } = render(
      <TestWrapper>
        <HealthTrendCharts />
      </TestWrapper>
    );

    expect(container).toBeTruthy();
    expect(useHealthTrends).toHaveBeenCalled();
  });

  it('should handle empty data gracefully', () => {
    const mockTrendData = {
      timeframe: '24h',
      dataPoints: [],
      aggregates: {
        averageResponseTime: 0,
        uptimePercentage: 100,
        mostCommonStatus: 'healthy',
        totalDataPoints: 0,
      },
    };

    useHealthTrends.mockReturnValue({
      data: mockTrendData,
      isLoading: false,
      error: null,
    });

    const { container } = render(
      <TestWrapper>
        <HealthTrendCharts />
      </TestWrapper>
    );

    expect(container).toBeTruthy();
    expect(useHealthTrends).toHaveBeenCalled();
  });
});
