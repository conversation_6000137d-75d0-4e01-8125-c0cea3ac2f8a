/**
 * @file DateRangePicker.tsx
 * @description Date range picker component for report filtering
 */

import React, { useState, useEffect, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CalendarIcon, X, AlertTriangle, CheckCircle } from 'lucide-react';
import {
  format,
  subDays,
  subWeeks,
  subMonths,
  startOfMonth,
  endOfMonth,
  startOfYear,
  endOfYear,
  differenceInDays,
  isAfter,
  isBefore,
  isValid,
} from 'date-fns';
import { cn } from '@/lib/utils';

/**
 * Date range interface
 */
export interface DateRange {
  from: Date;
  to: Date;
}

/**
 * Predefined date range options with enhanced defaults
 */
const PRESET_RANGES = [
  {
    label: 'Today',
    getValue: () => ({
      from: new Date(),
      to: new Date(),
    }),
  },
  {
    label: 'Yesterday',
    getValue: () => {
      const yesterday = subDays(new Date(), 1);
      return {
        from: yesterday,
        to: yesterday,
      };
    },
  },
  {
    label: 'Last 3 days',
    getValue: () => ({
      from: subDays(new Date(), 2),
      to: new Date(),
    }),
  },
  {
    label: 'Last 7 days',
    getValue: () => ({
      from: subDays(new Date(), 6),
      to: new Date(),
    }),
  },
  {
    label: 'Last 2 weeks',
    getValue: () => ({
      from: subWeeks(new Date(), 2),
      to: new Date(),
    }),
  },
  {
    label: 'Last 30 days',
    getValue: () => ({
      from: subDays(new Date(), 29),
      to: new Date(),
    }),
  },
  {
    label: 'This week',
    getValue: () => ({
      from: subDays(new Date(), new Date().getDay()),
      to: new Date(),
    }),
  },
  {
    label: 'This month',
    getValue: () => ({
      from: startOfMonth(new Date()),
      to: endOfMonth(new Date()),
    }),
  },
  {
    label: 'Last month',
    getValue: () => {
      const lastMonth = subMonths(new Date(), 1);
      return {
        from: startOfMonth(lastMonth),
        to: endOfMonth(lastMonth),
      };
    },
  },
  {
    label: 'Last 3 months',
    getValue: () => ({
      from: subMonths(new Date(), 3),
      to: new Date(),
    }),
  },
  {
    label: 'This year',
    getValue: () => ({
      from: startOfYear(new Date()),
      to: endOfYear(new Date()),
    }),
  },
];

/**
 * Validation result interface
 */
interface ValidationResult {
  isValid: boolean;
  message?: string;
  type?: 'error' | 'warning' | 'info';
}

interface DateRangePickerProps {
  value?: DateRange | null;
  onChange?: (range: DateRange | null) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  maxDays?: number; // Maximum allowed days in range
  minDays?: number; // Minimum required days in range
  maxDate?: Date; // Maximum allowed end date
  minDate?: Date; // Minimum allowed start date
  showValidation?: boolean; // Show real-time validation feedback
}

/**
 * DateRangePicker Component
 *
 * Enhanced with real-time validation and performance optimization.
 * Follows SOLID principles with single responsibility for date range selection.
 */
export const DateRangePicker: React.FC<DateRangePickerProps> = ({
  value,
  onChange,
  placeholder = 'Select date range',
  className,
  disabled = false,
  maxDays = 365, // Default max 1 year
  minDays = 1, // Default min 1 day
  maxDate = new Date(), // Default to today
  minDate = new Date(2020, 0, 1), // Default to 2020
  showValidation = true,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [tempRange, setTempRange] = useState<DateRange | null>(value || null);

  // Real-time validation with memoization for performance
  const validation = useMemo((): ValidationResult => {
    if (!tempRange) {
      return { isValid: true };
    }

    const { from, to } = tempRange;

    // Validate date objects
    if (!isValid(from) || !isValid(to)) {
      return {
        isValid: false,
        message: 'Invalid date selected',
        type: 'error',
      };
    }

    // Check date order
    if (isAfter(from, to)) {
      return {
        isValid: false,
        message: 'Start date must be before end date',
        type: 'error',
      };
    }

    // Check date range limits
    if (minDate && isBefore(from, minDate)) {
      return {
        isValid: false,
        message: `Start date cannot be before ${format(minDate, 'MMM dd, yyyy')}`,
        type: 'error',
      };
    }

    if (maxDate && isAfter(to, maxDate)) {
      return {
        isValid: false,
        message: `End date cannot be after ${format(maxDate, 'MMM dd, yyyy')}`,
        type: 'error',
      };
    }

    // Check range duration
    const daysDiff = differenceInDays(to, from) + 1;

    if (daysDiff < minDays) {
      return {
        isValid: false,
        message: `Date range must be at least ${minDays} day${minDays > 1 ? 's' : ''}`,
        type: 'error',
      };
    }

    if (daysDiff > maxDays) {
      return {
        isValid: false,
        message: `Date range cannot exceed ${maxDays} days`,
        type: 'error',
      };
    }

    // Performance warning for large ranges
    if (daysDiff > 90) {
      return {
        isValid: true,
        message: `Large date range (${daysDiff} days) may affect performance`,
        type: 'warning',
      };
    }

    return {
      isValid: true,
      message: `${daysDiff} day${daysDiff > 1 ? 's' : ''} selected`,
      type: 'info',
    };
  }, [tempRange, maxDays, minDays, maxDate, minDate]);

  // Update temp range when value changes
  useEffect(() => {
    setTempRange(value || null);
  }, [value]);

  /**
   * Handle preset range selection with validation
   */
  const handlePresetSelect = (preset: (typeof PRESET_RANGES)[0]) => {
    const range = preset.getValue();
    setTempRange(range);

    // Only call onChange if validation passes
    const tempValidation = validateRange(range);
    if (tempValidation.isValid) {
      onChange?.(range);
      setIsOpen(false);
    }
  };

  /**
   * Validate a date range
   */
  const validateRange = (range: DateRange | null): ValidationResult => {
    if (!range) return { isValid: true };

    const { from, to } = range;
    const daysDiff = differenceInDays(to, from) + 1;

    if (daysDiff > maxDays) {
      return {
        isValid: false,
        message: `Date range cannot exceed ${maxDays} days`,
        type: 'error',
      };
    }

    return { isValid: true };
  };

  /**
   * Handle custom date selection with enhanced logic
   */
  const handleDateSelect = (date: Date | undefined) => {
    if (!date || disabled) return;

    // Validate date is within allowed range
    if (minDate && isBefore(date, minDate)) return;
    if (maxDate && isAfter(date, maxDate)) return;

    if (!tempRange || (tempRange.from && tempRange.to)) {
      // Start new range
      const newRange = { from: date, to: date };
      setTempRange(newRange);
    } else if (tempRange.from && !tempRange.to) {
      // Complete the range
      const newRange = {
        from: date < tempRange.from ? date : tempRange.from,
        to: date < tempRange.from ? tempRange.from : date,
      };
      setTempRange(newRange);

      // Only call onChange if validation passes
      const tempValidation = validateRange(newRange);
      if (tempValidation.isValid) {
        onChange?.(newRange);
        setIsOpen(false);
      }
    }
  };

  /**
   * Handle range clear
   */
  const handleClear = () => {
    setTempRange(null);
    onChange?.(null);
    setIsOpen(false);
  };

  /**
   * Format display text
   */
  const getDisplayText = () => {
    if (!value) return placeholder;

    if (value.from.toDateString() === value.to.toDateString()) {
      return format(value.from, 'MMM dd, yyyy');
    }

    return `${format(value.from, 'MMM dd, yyyy')} - ${format(value.to, 'MMM dd, yyyy')}`;
  };

  return (
    <div className={cn('relative', className)}>
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className={cn(
              'w-full justify-start text-left font-normal',
              !value && 'text-muted-foreground'
            )}
            disabled={disabled}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {getDisplayText()}
            {value && (
              <Badge variant="secondary" className="ml-auto">
                {Math.ceil(
                  (value.to.getTime() - value.from.getTime()) /
                    (1000 * 60 * 60 * 24)
                ) + 1}{' '}
                days
              </Badge>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto max-w-4xl p-0" align="start">
          <div className="flex">
            {/* Preset Options */}
            <div className="border-r p-4 space-y-2 min-w-[160px]">
              <h4 className="font-medium text-sm text-gray-900 mb-3">
                Quick Select
              </h4>
              {PRESET_RANGES.map(preset => (
                <Button
                  key={preset.label}
                  variant="ghost"
                  size="sm"
                  className="w-full justify-start text-xs h-8 px-2"
                  onClick={() => handlePresetSelect(preset)}
                >
                  {preset.label}
                </Button>
              ))}

              {value && (
                <>
                  <div className="border-t pt-2 mt-3">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="w-full justify-start text-sm text-red-600 hover:text-red-700"
                      onClick={handleClear}
                    >
                      <X className="mr-2 h-3 w-3" />
                      Clear
                    </Button>
                  </div>
                </>
              )}
            </div>

            {/* Enhanced Calendar with Range Mode */}
            <div className="p-4 min-w-[600px]">
              <Calendar
                mode="range"
                selected={tempRange || undefined}
                onSelect={range => {
                  if (range && range.from) {
                    const dateRange = {
                      from: range.from,
                      to: range.to || range.from,
                    };
                    setTempRange(dateRange);
                    // Auto-apply valid ranges
                    const tempValidation = validateRange(dateRange);
                    if (
                      tempValidation.isValid &&
                      dateRange.from &&
                      dateRange.to
                    ) {
                      onChange?.(dateRange);
                      setIsOpen(false);
                    }
                  } else {
                    setTempRange(null);
                  }
                }}
                numberOfMonths={2}
                className="rounded-md border-0"
                disabled={[
                  // Disable dates outside the allowed range
                  ...(minDate ? [{ before: minDate }] : []),
                  ...(maxDate ? [{ after: maxDate }] : []),
                ]}
                showOutsideDays
                fixedWeeks
              />

              {tempRange && (
                <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                  <h5 className="font-medium text-sm text-gray-900 mb-2">
                    Selected Range
                  </h5>
                  <div className="space-y-1 text-sm text-gray-600">
                    <div>From: {format(tempRange.from, 'MMM dd, yyyy')}</div>
                    <div>To: {format(tempRange.to, 'MMM dd, yyyy')}</div>
                    <div className="text-xs text-gray-500">
                      {Math.ceil(
                        (tempRange.to.getTime() - tempRange.from.getTime()) /
                          (1000 * 60 * 60 * 24)
                      ) + 1}{' '}
                      days
                    </div>
                  </div>

                  {/* Real-time Validation Feedback */}
                  {showValidation && validation.message && (
                    <Alert
                      className={cn(
                        'mt-3 py-2 px-3',
                        validation.type === 'error' &&
                          'border-red-200 bg-red-50',
                        validation.type === 'warning' &&
                          'border-yellow-200 bg-yellow-50',
                        validation.type === 'info' &&
                          'border-blue-200 bg-blue-50'
                      )}
                    >
                      <div className="flex items-center gap-2">
                        {validation.type === 'error' && (
                          <AlertTriangle className="h-3 w-3 text-red-600" />
                        )}
                        {validation.type === 'warning' && (
                          <AlertTriangle className="h-3 w-3 text-yellow-600" />
                        )}
                        {validation.type === 'info' && (
                          <CheckCircle className="h-3 w-3 text-blue-600" />
                        )}
                        <AlertDescription
                          className={cn(
                            'text-xs',
                            validation.type === 'error' && 'text-red-700',
                            validation.type === 'warning' && 'text-yellow-700',
                            validation.type === 'info' && 'text-blue-700'
                          )}
                        >
                          {validation.message}
                        </AlertDescription>
                      </div>
                    </Alert>
                  )}
                </div>
              )}
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
};
