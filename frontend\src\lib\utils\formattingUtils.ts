/**
 * Formatting Utilities for Phase 3
 * Handles display formatting for various data types
 */

import type {
  DelegationStatusPrisma,
  EmployeeStatusPrisma,
  TaskStatusPrisma,
} from '@/lib/types/domain';

/**
 * Formats currency values for display
 */
export function formatCurrency(
  amount: number,
  currency = 'USD'
): string {
  return new Intl.NumberFormat('en-US', {
    currency: currency,
    style: 'currency',
  }).format(amount);
}

/**
 * Formats delegation status for user-friendly display
 */
export function formatDelegationStatusForDisplay(
  status: DelegationStatusPrisma
): string {
  switch (status) {
    case 'In_Progress': {
      return 'In Progress';
    }
    case 'No_details': {
      return 'No Details';
    }
    default: {
      // For statuses like 'Planned', 'Confirmed', 'Completed', 'Cancelled'
      return status;
    }
  }
}

/**
 * Formats employee name for display, prioritizing fullName over name
 * and handling cases where name might contain role or other data
 */
export function formatEmployeeName(employee: {
  fullName?: null | string;
  name?: null | string;
  role?: string;
}): string {
  // Priority: fullName > name > fallback
  if (employee.fullName?.trim()) {
    return employee.fullName.trim();
  }

  if (employee.name?.trim()) {
    const name = employee.name.trim();

    // Check if name looks like a role (contains underscore or matches common role patterns)
    const rolePatterns = [
      'office_staff',
      'service_advisor',
      'administrator',
      'mechanic',
      'driver',
      'manager',
      'technician',
      'other',
    ];

    // If name matches a role pattern, format it nicely but indicate it's a role
    if (rolePatterns.includes(name.toLowerCase()) || name.includes('_')) {
      const formattedRole = name
        .replaceAll('_', ' ')
        .replaceAll(/\b\w/g, l => l.toUpperCase());
      return `${formattedRole} (Role)`;
    }

    // Otherwise, return the name as-is
    return name;
  }

  // Fallback to role if available
  if (employee.role) {
    const formattedRole = employee.role
      .replaceAll('_', ' ')
      .replaceAll(/\b\w/g, l => l.toUpperCase());
    return `${formattedRole} (Role)`;
  }

  return 'Unknown Employee';
}

/**
 * Formats employee role for display
 */
export function formatEmployeeRole(role: string): string {
  return role.replaceAll('_', ' ').replaceAll(/\b\w/g, l => l.toUpperCase());
}

/**
 * Formats employee status for user-friendly display
 */
export function formatEmployeeStatusForDisplay(
  status: EmployeeStatusPrisma
): string {
  return status.replaceAll('_', ' ');
}

/**
 * Formats file size in human-readable format
 */
export function formatFileSize(bytes: number): string {
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  if (bytes === 0) return '0 Bytes';

  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return Math.round((bytes / Math.pow(1024, i)) * 100) / 100 + ' ' + sizes[i];
}

/**
 * Formats numbers with thousand separators
 */
export function formatNumber(value: number, decimals = 0): string {
  return new Intl.NumberFormat('en-US', {
    maximumFractionDigits: decimals,
    minimumFractionDigits: decimals,
  }).format(value);
}

/**
 * Formats phone numbers for display
 */
export function formatPhoneNumber(phone: string): string {
  // Remove all non-digit characters
  const cleaned = phone.replaceAll(/\D/g, '');

  // Format as (XXX) XXX-XXXX for US numbers
  if (cleaned.length === 10) {
    return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(
      6
    )}`;
  }

  // Return original if not a standard US number
  return phone;
}

/**
 * Formats task status for user-friendly display
 */
export function formatTaskStatusForDisplay(status: TaskStatusPrisma): string {
  return status.replaceAll('_', ' ');
}

/**
 * Capitalizes the first letter of each word
 */
export function titleCase(text: string): string {
  return text.replaceAll(
    /\w\S*/g,
    txt => txt.charAt(0).toUpperCase() + txt.slice(1).toLowerCase()
  );
}

/**
 * Truncates text to specified length with ellipsis
 */
export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) {
    return text;
  }
  return text.slice(0, maxLength - 3) + '...';
}
