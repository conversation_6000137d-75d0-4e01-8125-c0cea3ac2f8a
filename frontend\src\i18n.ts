import { getRequestConfig } from 'next-intl/server';
import { notFound } from 'next/navigation';

// Can be imported from a shared config
export const locales = ['en', 'ar'] as const;
export type Locale = (typeof locales)[number];

export const defaultLocale: Locale = 'en';

export const localeNames: Record<Locale, string> = {
  ar: 'العربية',
  en: 'English',
};

export const localeDirections: Record<Locale, 'ltr' | 'rtl'> = {
  ar: 'rtl',
  en: 'ltr',
};

export default getRequestConfig(async ({ locale }) => {
  // Validate that the incoming `locale` parameter is valid
  if (!locales.includes(locale as Locale)) notFound();

  return {
    formats: {
      dateTime: {
        long: {
          day: 'numeric',
          month: 'long',
          weekday: 'long',
          year: 'numeric',
        },
        medium: {
          day: 'numeric',
          month: 'long',
          year: 'numeric',
        },
        short: {
          day: 'numeric',
          month: 'short',
          year: 'numeric',
        },
      },
      number: {
        currency: {
          currency: locale === 'ar' ? 'IQD' : 'USD',
          style: 'currency',
        },
      },
    },
    locale: locale as string,
    messages: await import(`../messages/${locale}.json`).then(
      module => module.default as Record<string, string>
    ),
    now: new Date(),
    timeZone: 'UTC',
  };
});
