import { Router } from 'express';

import * as taskController from '../controllers/task.controller.js';
import { enhancedAuthenticateUser } from '../middleware/jwtAuth.middleware.js';
import { adaptiveRateLimit } from '../middleware/rateLimiting.js'; // Add this import
import { apiDeduplication } from '../middleware/requestDeduplication.js'; // Add this import
import { requireRole } from '../middleware/supabaseAuth.js';
import { sanitizeTaskData } from '../middleware/taskDataSanitizer.js';
import { sanitizeTaskResponse } from '../middleware/taskResponseSanitizer.js';
import { validate } from '../middleware/validation.js';
import { taskCreateSchema, taskIdSchema, taskUpdateSchema } from '../schemas/task.schema.js';

const router = Router();

// Apply authentication to all task routes
router.use(enhancedAuthenticateUser);

// Apply adaptive rate limiting and API deduplication
router.use(adaptiveRateLimit);
router.use(apiDeduplication);

// Apply task data sanitization middleware to all task routes
router.use(sanitizeTaskData);
router.use(sanitizeTaskResponse);

/**
 * @openapi
 * /tasks:
 *   post:
 *     tags: [Tasks]
 *     summary: Create a new task
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/TaskCreateInput'
 *     responses:
 *       201:
 *         description: Task created successfully.
 *       400:
 *         description: Invalid input.
 *   get:
 *     tags: [Tasks]
 *     summary: Retrieve a list of all tasks
 *     responses:
 *       200:
 *         description: A list of tasks.
 */
router.post(
  '/',
  requireRole(['ADMIN', 'MANAGER']),
  validate(taskCreateSchema, 'body'),
  taskController.createTask,
);
router.get('/', taskController.getAllTasks);

/**
 * @openapi
 * /tasks/{id}:
 *   get:
 *     tags: [Tasks]
 *     summary: Retrieve a specific task by ID
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string # Task IDs are generic strings like "task_..."
 *     responses:
 *       200:
 *         description: Details of the task.
 *       404:
 *         description: Task not found.
 *   put:
 *     tags: [Tasks]
 *     summary: Update a specific task by ID
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string # Task IDs are generic strings like "task_..."
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/TaskUpdateInput'
 *     responses:
 *       200:
 *         description: Task updated successfully.
 *       400:
 *         description: Invalid input.
 *       404:
 *         description: Task not found.
 *   delete:
 *     tags: [Tasks]
 *     summary: Delete a specific task by ID
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string # Task IDs are generic strings like "task_..."
 *     responses:
 *       200:
 *         description: Task deleted successfully.
 *       404:
 *         description: Task not found.
 */
router.get('/:id', validate(taskIdSchema, 'params'), taskController.getTaskById);
router.put(
  '/:id',
  validate(taskIdSchema, 'params'),
  validate(taskUpdateSchema, 'body'),
  taskController.updateTask,
);
router.delete(
  '/:id',
  requireRole(['ADMIN', 'MANAGER']),
  validate(taskIdSchema, 'params'),
  taskController.deleteTask,
);

// Get subtasks for a task -- This route was causing a linter error and will be removed.
// router.get(
//     '/:id/subtasks',
//     validate(taskIdSchema, 'params'),
//     taskController.getSubTasksForTask
// );

export default router;
