/**
 * @file ReliabilityApiService Tests
 * @description Tests for circuit breaker type safety fixes
 */

import type { ApiClient } from '../../../core/apiClient';

import { ReliabilityApiService } from '../reliabilityApi';

// Mock ApiClient
const mockApiClient: jest.Mocked<ApiClient> = {
  delete: jest.fn(),
  get: jest.fn(),
  patch: jest.fn(),
  post: jest.fn(),
  put: jest.fn(),
  request: jest.fn(),
} as any;

describe('ReliabilityApiService', () => {
  let service: ReliabilityApiService;

  beforeEach(() => {
    service = new ReliabilityApiService(mockApiClient);
    jest.clearAllMocks();
  });

  describe('getCircuitBreakerStatus', () => {
    it('should handle valid array response correctly', async () => {
      const mockResponse = {
        circuitBreakers: [
          { name: 'service1', state: 'CLOSED' },
          { name: 'service2', state: 'OPEN' },
          { name: 'service3', state: 'HALF_OPEN' },
        ],
      };

      mockApiClient.get.mockResolvedValue(mockResponse);

      const result = await service.getCircuitBreakerStatus();

      expect(result).toEqual({
        circuitBreakers: mockResponse.circuitBreakers,
        summary: {
          closed: 1,
          halfOpen: 1,
          open: 1,
          total: 3,
        },
      });
    });

    it('should handle null circuitBreakers response safely', async () => {
      const mockResponse = {
        circuitBreakers: null,
      };

      mockApiClient.get.mockResolvedValue(mockResponse);

      const result = await service.getCircuitBreakerStatus();

      expect(result).toEqual({
        circuitBreakers: [],
        summary: {
          closed: 0,
          halfOpen: 0,
          open: 0,
          total: 0,
        },
      });
    });

    it('should handle undefined circuitBreakers response safely', async () => {
      const mockResponse = {};

      mockApiClient.get.mockResolvedValue(mockResponse);

      const result = await service.getCircuitBreakerStatus();

      expect(result).toEqual({
        circuitBreakers: [],
        summary: {
          closed: 0,
          halfOpen: 0,
          open: 0,
          total: 0,
        },
      });
    });

    it('should handle non-array circuitBreakers response safely', async () => {
      const mockResponse = {
        circuitBreakers: 'invalid string response',
      };

      mockApiClient.get.mockResolvedValue(mockResponse);

      const result = await service.getCircuitBreakerStatus();

      expect(result).toEqual({
        circuitBreakers: [],
        summary: {
          closed: 0,
          halfOpen: 0,
          open: 0,
          total: 0,
        },
      });
    });

    it('should handle object circuitBreakers response safely', async () => {
      const mockResponse = {
        circuitBreakers: { notAnArray: true },
      };

      mockApiClient.get.mockResolvedValue(mockResponse);

      const result = await service.getCircuitBreakerStatus();

      expect(result).toEqual({
        circuitBreakers: [],
        summary: {
          closed: 0,
          halfOpen: 0,
          open: 0,
          total: 0,
        },
      });
    });

    it('should handle API errors gracefully', async () => {
      mockApiClient.get.mockRejectedValue(new Error('API Error'));

      const result = await service.getCircuitBreakerStatus();

      expect(result).toEqual({
        circuitBreakers: [],
        summary: {
          closed: 0,
          halfOpen: 0,
          open: 0,
          total: 0,
        },
      });
    });
  });
});
