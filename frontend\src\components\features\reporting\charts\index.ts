// frontend/src/components/features/reporting/charts/index.ts

/**
 * Chart Components Export Index
 *
 * Centralized exports for all reporting chart components following DRY principles.
 * This file serves as the single source of truth for chart component imports.
 */

// Base Chart Components (Task 3.1)
export { DelegationStatusChart } from './DelegationStatusChart';
export { DelegationTrendChart } from './DelegationTrendChart';
export { LocationDistributionChart } from './LocationDistributionChart';
export { TaskMetricsChart } from './TaskMetricsChart';

// Shared Chart Components (Task 3.2)
export * from './shared';

// Re-export types for convenience
export type {
  StatusDistributionData,
  TrendData,
  LocationMetrics,
  TaskMetrics,
} from '../data/types/reporting';
