/**
 * @file Location Distribution Widget - UX Optimized
 * @description Professional horizontal bar chart with proper label positioning and enhanced tooltips
 *
 * UX Improvements Applied:
 * - Moved location labels to left of bars for better readability
 * - Enhanced tooltip with percentage and more descriptive information
 * - Improved responsive layout and spacing
 * - Professional styling with better visual hierarchy
 */

'use client';

import React from 'react';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from 'recharts';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useLocationMetricsQuery } from '../../data/hooks';
import { useReportingFilters } from '../../data/stores';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { MapPin, TrendingUp } from 'lucide-react';

// Enhanced custom tooltip with percentage and better formatting
const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    const data = payload[0].payload;
    const total = payload[0].payload.total || 100; // Fallback if total not provided
    const percentage =
      total > 0 ? Math.round((data.delegationCount / total) * 100) : 0;

    return (
      <div className="bg-white dark:bg-gray-800 p-4 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg">
        <p className="font-semibold text-gray-900 dark:text-gray-100 mb-3">
          {label}
        </p>
        <div className="space-y-2">
          <div className="flex justify-between items-center gap-6">
            <span className="text-sm text-gray-600 dark:text-gray-400">
              Delegations:
            </span>
            <span className="font-bold text-blue-600 dark:text-blue-400 text-lg">
              {data.delegationCount}
            </span>
          </div>
          <div className="flex justify-between items-center gap-6">
            <span className="text-sm text-gray-600 dark:text-gray-400">
              Percentage:
            </span>
            <span className="font-medium text-emerald-600 dark:text-emerald-400">
              {percentage}%
            </span>
          </div>
          {data.completionRate !== undefined && (
            <div className="flex justify-between items-center gap-6">
              <span className="text-sm text-gray-600 dark:text-gray-400">
                Completion Rate:
              </span>
              <span className="font-medium text-green-600 dark:text-green-400">
                {data.completionRate}%
              </span>
            </div>
          )}
          {data.averageResponseTime && (
            <div className="flex justify-between items-center gap-6">
              <span className="text-sm text-gray-600 dark:text-gray-400">
                Avg Response:
              </span>
              <span className="font-medium text-yellow-600 dark:text-yellow-400">
                {data.averageResponseTime}h
              </span>
            </div>
          )}
        </div>
      </div>
    );
  }
  return null;
};

export const LocationDistributionWidget = () => {
  const filters = useReportingFilters();
  const { data, isLoading, error } = useLocationMetricsQuery(filters);

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <Skeleton className="h-7 w-48" />
        </CardHeader>
        <CardContent>
          <Skeleton className="h-[350px] w-full rounded-lg" />
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-3">
            <MapPin className="h-6 w-6" />
            Location Distribution
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive">
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error.message}</AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  // Calculate total for percentage calculations
  const total = data?.reduce((sum, item) => sum + item.delegationCount, 0) || 0;
  const enrichedData =
    data?.map(item => ({
      ...item,
      total,
    })) || [];

  return (
    <Card>
      <CardHeader className="pb-6">
        <CardTitle className="flex items-center gap-3 text-xl">
          <MapPin className="h-6 w-6 text-primary" />
          Location Distribution
        </CardTitle>
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <TrendingUp className="h-4 w-4" />
          <span>Delegation distribution across locations</span>
        </div>
      </CardHeader>
      <CardContent className="pt-6">
        <ResponsiveContainer width="100%" height={350}>
          <BarChart
            data={enrichedData}
            layout="vertical"
            margin={{ top: 8, right: 40, left: 20, bottom: 8 }}
          >
            <CartesianGrid
              strokeDasharray="3 3"
              stroke="#f1f5f9"
              horizontal={true}
              vertical={false}
            />
            <XAxis
              type="number"
              fontSize={12}
              tick={{ fill: '#64748b' }}
              axisLine={{ stroke: '#e2e8f0' }}
              tickLine={{ stroke: '#e2e8f0' }}
            />
            <YAxis
              type="category"
              dataKey="location"
              width={140} // Increased width to accommodate longer location names
              fontSize={12}
              tick={{ fill: '#374151', fontSize: 12 }}
              axisLine={{ stroke: '#e2e8f0' }}
              tickLine={{ stroke: '#e2e8f0' }}
              // Position labels on the left of bars (standard practice)
              orientation="left"
            />
            <Tooltip content={<CustomTooltip />} />
            <Bar
              dataKey="delegationCount"
              fill="#3b82f6"
              name="Delegations"
              radius={[0, 6, 6, 0]} // Rounded right corners only
              stroke="#2563eb"
              strokeWidth={1}
            />
          </BarChart>
        </ResponsiveContainer>

        {/* Summary statistics */}
        {total > 0 && (
          <div className="mt-6 pt-4 border-t border-border">
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">Total Delegations:</span>
              <span className="font-semibold text-primary">{total}</span>
            </div>
            <div className="flex items-center justify-between text-sm mt-2">
              <span className="text-muted-foreground">Active Locations:</span>
              <span className="font-semibold text-primary">
                {enrichedData.length}
              </span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
