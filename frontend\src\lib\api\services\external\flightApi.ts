/**
 * Flight API Service
 *
 * This service provides functions to interact with the flight-related API endpoints.
 * Refactored to use BaseApiService pattern for consistency.
 */

import type { ApiClient } from '../../core/apiClient';
import {
  BaseApiService,
  type DataTransformer,
  type ServiceConfig,
} from '../../core/baseApiService';

// Define the flight data interface
export interface FlightData {
  altitude?: number;
  arrivalAirport?: string;
  arrivalTime?: number;
  callsign: string;
  departureAirport?: string;
  departureTime?: number;
  heading?: number;
  icao24: string;
  lastSeen?: number;
  latitude?: number;
  longitude?: number;
  onGround?: boolean;
  velocity?: number;
}

// Enhanced response type for flight search
export interface FlightSearchResponse {
  details?: {
    apiInfo?: string;
    possibleReasons?: string[];
    searchParams?: {
      callsign: string;
      date: string;
    };
  };
  error?: string;
  flights?: FlightData[];
  message?: string;
  timestamp?: string;
}

const FlightTransformer: DataTransformer<FlightData> = {
  fromApi: (data: any) => data,
  toApi: (data: any) => data,
};

/**
 * Flight API Service using BaseApiService pattern
 */
export class FlightApiService extends BaseApiService<FlightData, any, any> {
  protected endpoint = '/flights';
  protected transformer: DataTransformer<FlightData> = FlightTransformer;

  constructor(apiClient: ApiClient, config?: ServiceConfig) {
    super(apiClient, {
      cacheDuration: 5 * 60 * 1000, // 5 minutes for flight data
      retryAttempts: 3,
      circuitBreakerThreshold: 5,
      enableMetrics: true,
      ...config,
    });
  }

  /**
   * Search for flights by callsign and date
   */
  async searchFlightsByCallsignAndDate(
    callsign: string,
    date: string // Expected format: "YYYY-MM-DD"
  ): Promise<FlightData[]> {
    if (!date) {
      console.error('Search date is required for historical flight search.');
      throw new Error('Search date is required');
    }

    // Check if date is in the future
    const searchDate = new Date(`${date}T00:00:00.000Z`);
    const currentDate = new Date();
    if (searchDate > currentDate) {
      console.warn(`Search for future date rejected: ${date}`);
      throw new Error(
        `OpenSky API does not provide data for future dates. The date ${date} is in the future.`
      );
    }

    return this.executeWithInfrastructure(
      `search:${callsign}:${date}`,
      async () => {
        const response = await this.apiClient.get<FlightSearchResponse>(
          `/flights/search?callsign=${encodeURIComponent(callsign)}&date=${date}`
        );

        // Handle the enhanced response format
        if (Array.isArray(response)) {
          return response; // Original array response
        } else if (response.flights) {
          return response.flights; // New format with flights array
        } else {
          // If we have an error message but no flights, throw an error with the details
          if (response.message) {
            const error = new Error(response.message);
            // @ts-ignore - Add details to the error object
            error.details = response.details;
            throw error;
          }
          return []; // Fallback to empty array
        }
      }
    );
  }

  /**
   * Get flights by airport (arrivals or departures)
   */
  async getFlightsByAirport(
    airport: string,
    begin: number,
    end: number,
    type: 'arrival' | 'departure' = 'arrival'
  ): Promise<FlightData[]> {
    return this.executeWithInfrastructure(
      `airport:${airport}:${begin}:${end}:${type}`,
      async () => {
        return this.apiClient.get<FlightData[]>(
          `/flights/airport?airport=${encodeURIComponent(
            airport
          )}&begin=${begin}&end=${end}&type=${type}`
        );
      }
    );
  }

  /**
   * Get flights by time interval
   */
  async getFlightsByTimeInterval(
    begin: number,
    end: number
  ): Promise<FlightData[]> {
    return this.executeWithInfrastructure(
      `interval:${begin}:${end}`,
      async () => {
        return this.apiClient.get<FlightData[]>(
          `/flights/interval?begin=${begin}&end=${end}`
        );
      }
    );
  }
}
