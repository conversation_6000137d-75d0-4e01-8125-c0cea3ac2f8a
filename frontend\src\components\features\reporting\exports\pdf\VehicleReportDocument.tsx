// frontend/src/components/features/reporting/exports/pdf/VehicleReportDocument.tsx
import React from 'react';
import { Document, Page, Text, View, StyleSheet } from '@react-pdf/renderer';

const styles = StyleSheet.create({
  page: {
    flexDirection: 'column',
    backgroundColor: '#FFFFFF',
    padding: 30,
    fontSize: 12,
  },
  header: {
    fontSize: 20,
    marginBottom: 20,
    textAlign: 'center',
    color: '#dc2626',
    fontWeight: 'bold',
  },
  subheader: {
    fontSize: 16,
    marginBottom: 15,
    color: '#374151',
    fontWeight: 'bold',
    borderBottom: '1px solid #e5e7eb',
    paddingBottom: 5,
  },
  section: {
    marginBottom: 20,
  },
  row: {
    flexDirection: 'row',
    marginBottom: 8,
    paddingVertical: 4,
  },
  label: {
    width: '40%',
    fontWeight: 'bold',
    color: '#4b5563',
  },
  value: {
    width: '60%',
    color: '#111827',
  },
  table: {
    marginTop: 10,
  },
  tableHeader: {
    flexDirection: 'row',
    backgroundColor: '#fef2f2',
    padding: 8,
    fontWeight: 'bold',
  },
  tableRow: {
    flexDirection: 'row',
    padding: 8,
    borderBottom: '1px solid #e5e7eb',
  },
  tableCell: {
    flex: 1,
    fontSize: 10,
  },
  metadata: {
    marginTop: 30,
    padding: 15,
    backgroundColor: '#fef2f2',
    borderRadius: 5,
  },
  metadataText: {
    fontSize: 10,
    color: '#6b7280',
    marginBottom: 3,
  },
});

interface VehicleReportDocumentProps {
  data: any;
  reportTitle: string;
  metadata?: any;
}

export const VehicleReportDocument: React.FC<VehicleReportDocumentProps> = ({
  data,
  reportTitle,
  metadata,
}) => {
  // Defensive programming: ensure all data is properly defined
  const vehicleData = React.useMemo(() => {
    if (!data) return {};

    // Handle different data structures
    const rawData = data?.data || data;

    // Ensure we have a valid object
    if (!rawData || typeof rawData !== 'object') {
      return {};
    }

    // Return a safe copy with fallbacks
    return {
      totalCount: rawData.totalCount || 0,
      activeCount: rawData.activeCount || 0,
      maintenanceCount: rawData.maintenanceCount || 0,
      outOfServiceCount: rawData.outOfServiceCount || 0,
      utilizationRate: rawData.utilizationRate || 0,
      averageMileage: rawData.averageMileage || 0,
      statusDistribution: Array.isArray(rawData.statusDistribution)
        ? rawData.statusDistribution
        : [],
      typeDistribution: Array.isArray(rawData.typeDistribution)
        ? rawData.typeDistribution
        : [],
      maintenanceMetrics: rawData.maintenanceMetrics || {},
      ...rawData,
    };
  }, [data]);

  const safeReportTitle = reportTitle || 'Vehicle Report';
  const safeMetadata = metadata || {};

  return (
    <Document>
      <Page size="A4" style={styles.page}>
        <Text style={styles.header}>{safeReportTitle}</Text>

        {/* Vehicle Fleet Summary */}
        <View style={styles.section}>
          <Text style={styles.subheader}>Fleet Summary</Text>
          <View style={styles.row}>
            <Text style={styles.label}>Total Vehicles:</Text>
            <Text style={styles.value}>{vehicleData.totalCount || 0}</Text>
          </View>
          <View style={styles.row}>
            <Text style={styles.label}>Active Vehicles:</Text>
            <Text style={styles.value}>{vehicleData.activeCount || 0}</Text>
          </View>
          <View style={styles.row}>
            <Text style={styles.label}>In Maintenance:</Text>
            <Text style={styles.value}>
              {vehicleData.maintenanceCount || 0}
            </Text>
          </View>
          <View style={styles.row}>
            <Text style={styles.label}>Out of Service:</Text>
            <Text style={styles.value}>
              {vehicleData.outOfServiceCount || 0}
            </Text>
          </View>
          <View style={styles.row}>
            <Text style={styles.label}>Fleet Utilization Rate:</Text>
            <Text style={styles.value}>
              {vehicleData.utilizationRate?.toFixed(2) || 0}%
            </Text>
          </View>
          <View style={styles.row}>
            <Text style={styles.label}>Average Mileage:</Text>
            <Text style={styles.value}>
              {vehicleData.averageMileage?.toFixed(0) || 0} km
            </Text>
          </View>
        </View>

        {/* Status Distribution */}
        {vehicleData.statusDistribution && (
          <View style={styles.section}>
            <Text style={styles.subheader}>Vehicle Status Distribution</Text>
            <View style={styles.table}>
              <View style={styles.tableHeader}>
                <Text style={styles.tableCell}>Status</Text>
                <Text style={styles.tableCell}>Count</Text>
                <Text style={styles.tableCell}>Percentage</Text>
              </View>
              {vehicleData.statusDistribution.map(
                (item: any, index: number) => (
                  <View key={`status-${index}`} style={styles.tableRow}>
                    <Text style={styles.tableCell}>
                      {item?.status || item?._id || 'Unknown'}
                    </Text>
                    <Text style={styles.tableCell}>
                      {item?.count || item?._count?.status || 0}
                    </Text>
                    <Text style={styles.tableCell}>
                      {item?.percentage
                        ? `${Number(item.percentage).toFixed(1)}%`
                        : 'N/A'}
                    </Text>
                  </View>
                )
              )}
            </View>
          </View>
        )}

        {/* Vehicle Type Distribution */}
        {vehicleData.typeDistribution && (
          <View style={styles.section}>
            <Text style={styles.subheader}>Vehicle Type Distribution</Text>
            <View style={styles.table}>
              <View style={styles.tableHeader}>
                <Text style={styles.tableCell}>Type</Text>
                <Text style={styles.tableCell}>Count</Text>
                <Text style={styles.tableCell}>Percentage</Text>
              </View>
              {vehicleData.typeDistribution.map((item: any, index: number) => (
                <View key={`type-${index}`} style={styles.tableRow}>
                  <Text style={styles.tableCell}>
                    {item?.type || item?._id || 'Unknown'}
                  </Text>
                  <Text style={styles.tableCell}>
                    {item?.count || item?._count?.type || 0}
                  </Text>
                  <Text style={styles.tableCell}>
                    {item?.percentage
                      ? `${Number(item.percentage).toFixed(1)}%`
                      : 'N/A'}
                  </Text>
                </View>
              ))}
            </View>
          </View>
        )}

        {/* Maintenance Metrics */}
        {vehicleData.maintenanceMetrics && (
          <View style={styles.section}>
            <Text style={styles.subheader}>Maintenance Metrics</Text>
            <View style={styles.row}>
              <Text style={styles.label}>Total Maintenance Records:</Text>
              <Text style={styles.value}>
                {vehicleData.maintenanceMetrics.totalRecords || 0}
              </Text>
            </View>
            <View style={styles.row}>
              <Text style={styles.label}>Average Cost per Service:</Text>
              <Text style={styles.value}>
                ${vehicleData.maintenanceMetrics.averageCost?.toFixed(2) || 0}
              </Text>
            </View>
            <View style={styles.row}>
              <Text style={styles.label}>Total Maintenance Cost:</Text>
              <Text style={styles.value}>
                ${vehicleData.maintenanceMetrics.totalCost?.toFixed(2) || 0}
              </Text>
            </View>
            <View style={styles.row}>
              <Text style={styles.label}>Preventive Maintenance Rate:</Text>
              <Text style={styles.value}>
                {vehicleData.maintenanceMetrics.preventiveRate?.toFixed(2) || 0}
                %
              </Text>
            </View>
          </View>
        )}

        {/* Utilization Metrics */}
        {vehicleData.utilizationMetrics && (
          <View style={styles.section}>
            <Text style={styles.subheader}>Utilization Metrics</Text>
            <View style={styles.row}>
              <Text style={styles.label}>Average Daily Usage:</Text>
              <Text style={styles.value}>
                {vehicleData.utilizationMetrics.averageDailyUsage?.toFixed(2) ||
                  0}{' '}
                hours
              </Text>
            </View>
            <View style={styles.row}>
              <Text style={styles.label}>Peak Usage Hours:</Text>
              <Text style={styles.value}>
                {vehicleData.utilizationMetrics.peakUsageHours || 'N/A'}
              </Text>
            </View>
            <View style={styles.row}>
              <Text style={styles.label}>Idle Time Percentage:</Text>
              <Text style={styles.value}>
                {vehicleData.utilizationMetrics.idleTimePercentage?.toFixed(
                  2
                ) || 0}
                %
              </Text>
            </View>
          </View>
        )}

        {/* Report Metadata */}
        {metadata && (
          <View style={styles.metadata}>
            <Text style={styles.subheader}>Report Information</Text>
            <Text style={styles.metadataText}>Report ID: {metadata.id}</Text>
            <Text style={styles.metadataText}>Type: {metadata.type}</Text>
            <Text style={styles.metadataText}>
              Entity Type: {metadata.entityType}
            </Text>
            <Text style={styles.metadataText}>
              Generated: {new Date(metadata.generatedAt).toLocaleString()}
            </Text>
            <Text style={styles.metadataText}>
              Generated By: {metadata.generatedBy}
            </Text>
          </View>
        )}
      </Page>
    </Document>
  );
};
