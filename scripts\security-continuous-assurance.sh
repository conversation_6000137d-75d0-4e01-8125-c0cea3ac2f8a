#!/bin/bash

# ============================================================================
# PHASE 3 SECURITY HARDENING: Continuous Security Assurance Script
# Implements comprehensive security testing and monitoring
# File: scripts/security-continuous-assurance.sh
# ============================================================================

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
LOG_FILE="$PROJECT_ROOT/logs/security-assurance-$(date +%Y%m%d-%H%M%S).log"
REPORT_FILE="$PROJECT_ROOT/reports/security-report-$(date +%Y%m%d-%H%M%S).json"

# Ensure directories exist
mkdir -p "$PROJECT_ROOT/logs" "$PROJECT_ROOT/reports"

# Logging functions
log() {
    echo -e "$1" | tee -a "$LOG_FILE"
}

success() {
    log "${GREEN}✅ $1${NC}"
}

warning() {
    log "${YELLOW}⚠️  $1${NC}"
}

error() {
    log "${RED}❌ $1${NC}"
}

info() {
    log "${BLUE}ℹ️  $1${NC}"
}

# Test counters
TESTS_PASSED=0
TESTS_FAILED=0
TOTAL_TESTS=0

# Function to run a test and track results
run_test() {
    local test_name="$1"
    local test_command="$2"
    local expected_pattern="$3"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    if eval "$test_command" | grep -q "$expected_pattern"; then
        success "$test_name"
        TESTS_PASSED=$((TESTS_PASSED + 1))
        return 0
    else
        error "$test_name"
        TESTS_FAILED=$((TESTS_FAILED + 1))
        return 1
    fi
}

# Function to check if a service is running
check_service() {
    local service_name="$1"
    local port="$2"
    
    if curl -s -f "http://localhost:$port/health" > /dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# Main security assurance function
main() {
    log "🛡️  PHASE 3 SECURITY CONTINUOUS ASSURANCE"
    log "=========================================="
    log "Started at: $(date)"
    log "Project: WorkHub Security Hardening"
    log ""

    # Phase 1: Dependency Security Scanning
    log "📋 Phase 1: Supply Chain Security Assessment"
    log "============================================"
    
    # Frontend dependency audit
    info "Scanning frontend dependencies..."
    cd "$PROJECT_ROOT/frontend"
    if npm audit --audit-level moderate > /dev/null 2>&1; then
        success "Frontend dependencies: No moderate+ vulnerabilities"
        TESTS_PASSED=$((TESTS_PASSED + 1))
    else
        warning "Frontend dependencies: Vulnerabilities detected"
        npm audit --audit-level moderate | head -20 | tee -a "$LOG_FILE"
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    # Backend dependency audit
    info "Scanning backend dependencies..."
    cd "$PROJECT_ROOT/backend"
    if npm audit --audit-level moderate > /dev/null 2>&1; then
        success "Backend dependencies: No moderate+ vulnerabilities"
        TESTS_PASSED=$((TESTS_PASSED + 1))
    else
        warning "Backend dependencies: Vulnerabilities detected"
        npm audit --audit-level moderate | head -20 | tee -a "$LOG_FILE"
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    cd "$PROJECT_ROOT"
    
    # Phase 2: Client-Side Security Testing
    log ""
    log "📋 Phase 2: Client-Side Security Assessment"
    log "=========================================="
    
    # Check CSP configuration
    info "Validating Content Security Policy..."
    if [ -f "frontend/src/lib/security/cspConfig.ts" ]; then
        if grep -q "strict-dynamic" "frontend/src/lib/security/cspConfig.ts"; then
            success "CSP: strict-dynamic policy implemented"
            TESTS_PASSED=$((TESTS_PASSED + 1))
        else
            error "CSP: strict-dynamic policy missing"
            TESTS_FAILED=$((TESTS_FAILED + 1))
        fi
    else
        error "CSP: Configuration file missing"
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    # Check CSRF protection
    info "Validating CSRF protection..."
    if [ -f "frontend/src/lib/security/CSRFProtection.ts" ]; then
        if grep -q "generateToken" "frontend/src/lib/security/CSRFProtection.ts"; then
            success "CSRF: Protection mechanisms implemented"
            TESTS_PASSED=$((TESTS_PASSED + 1))
        else
            error "CSRF: Token generation missing"
            TESTS_FAILED=$((TESTS_FAILED + 1))
        fi
    else
        error "CSRF: Protection file missing"
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    # Phase 3: Backend Security Testing
    log ""
    log "📋 Phase 3: Backend Security Assessment"
    log "======================================"
    
    # Check if backend is running
    if check_service "backend" "3001"; then
        success "Backend service: Running and accessible"
        TESTS_PASSED=$((TESTS_PASSED + 1))
        
        # Test security headers
        info "Testing security headers..."
        headers_response=$(curl -s -I "http://localhost:3001/api/diagnostics" || echo "")
        
        if echo "$headers_response" | grep -qi "x-frame-options"; then
            success "Security headers: X-Frame-Options present"
            TESTS_PASSED=$((TESTS_PASSED + 1))
        else
            error "Security headers: X-Frame-Options missing"
            TESTS_FAILED=$((TESTS_FAILED + 1))
        fi
        TOTAL_TESTS=$((TOTAL_TESTS + 1))
        
        if echo "$headers_response" | grep -qi "x-content-type-options"; then
            success "Security headers: X-Content-Type-Options present"
            TESTS_PASSED=$((TESTS_PASSED + 1))
        else
            error "Security headers: X-Content-Type-Options missing"
            TESTS_FAILED=$((TESTS_FAILED + 1))
        fi
        TOTAL_TESTS=$((TOTAL_TESTS + 1))
        
        # Test rate limiting
        info "Testing rate limiting..."
        rate_limit_test=0
        for i in {1..6}; do
            if curl -s -f "http://localhost:3001/api/diagnostics" > /dev/null 2>&1; then
                rate_limit_test=$((rate_limit_test + 1))
            fi
        done
        
        if [ $rate_limit_test -eq 6 ]; then
            warning "Rate limiting: May not be properly configured (all requests succeeded)"
            TESTS_FAILED=$((TESTS_FAILED + 1))
        else
            success "Rate limiting: Appears to be working"
            TESTS_PASSED=$((TESTS_PASSED + 1))
        fi
        TOTAL_TESTS=$((TOTAL_TESTS + 1))
        
    else
        error "Backend service: Not running or not accessible"
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    # Phase 4: Docker Security Assessment
    log ""
    log "📋 Phase 4: Container Security Assessment"
    log "======================================="
    
    # Check if Docker images exist
    if docker images | grep -q "workhub-backend"; then
        success "Docker: Backend image exists"
        TESTS_PASSED=$((TESTS_PASSED + 1))
        
        # Check non-root user
        backend_user=$(docker run --rm workhub-backend:latest whoami 2>/dev/null || echo "unknown")
        if [[ "$backend_user" == "workhub" ]]; then
            success "Docker: Backend running as non-root user"
            TESTS_PASSED=$((TESTS_PASSED + 1))
        else
            error "Docker: Backend not running as expected non-root user"
            TESTS_FAILED=$((TESTS_FAILED + 1))
        fi
        TOTAL_TESTS=$((TOTAL_TESTS + 1))
        
    else
        warning "Docker: Backend image not found (may not be built yet)"
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    # Phase 5: Security Monitoring Assessment
    log ""
    log "📋 Phase 5: Security Monitoring Assessment"
    log "========================================"
    
    # Check audit logging
    if [ -f "backend/src/utils/auditLogger.ts" ]; then
        if grep -q "logGDPRAuditEvent" "backend/src/utils/auditLogger.ts"; then
            success "Audit logging: GDPR-compliant logging implemented"
            TESTS_PASSED=$((TESTS_PASSED + 1))
        else
            error "Audit logging: GDPR compliance missing"
            TESTS_FAILED=$((TESTS_FAILED + 1))
        fi
    else
        error "Audit logging: Logger file missing"
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    # Check security monitoring service
    if [ -f "backend/src/services/securityMonitoring.service.ts" ]; then
        if grep -q "createSecurityIncident" "backend/src/services/securityMonitoring.service.ts"; then
            success "Security monitoring: Incident response implemented"
            TESTS_PASSED=$((TESTS_PASSED + 1))
        else
            error "Security monitoring: Incident response missing"
            TESTS_FAILED=$((TESTS_FAILED + 1))
        fi
    else
        error "Security monitoring: Service file missing"
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    # Generate security report
    generate_security_report
    
    # Final results
    log ""
    log "🎯 SECURITY ASSURANCE RESULTS"
    log "============================"
    log "Total Tests: $TOTAL_TESTS"
    log "Passed: $TESTS_PASSED"
    log "Failed: $TESTS_FAILED"
    log "Success Rate: $(( (TESTS_PASSED * 100) / TOTAL_TESTS ))%"
    log ""
    log "Report saved to: $REPORT_FILE"
    log "Logs saved to: $LOG_FILE"
    log ""
    
    if [ $TESTS_FAILED -eq 0 ]; then
        success "🎉 ALL SECURITY ASSURANCE TESTS PASSED!"
        success "✅ WorkHub security posture is excellent"
        exit 0
    else
        error "❌ SECURITY ISSUES DETECTED"
        warning "⚠️  Please address failed tests to improve security posture"
        exit 1
    fi
}

# Generate comprehensive security report
generate_security_report() {
    local timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    local success_rate=$(( (TESTS_PASSED * 100) / TOTAL_TESTS ))
    
    cat > "$REPORT_FILE" << EOF
{
  "securityAssessment": {
    "timestamp": "$timestamp",
    "version": "PHASE-3-HARDENED",
    "summary": {
      "totalTests": $TOTAL_TESTS,
      "testsPassed": $TESTS_PASSED,
      "testsFailed": $TESTS_FAILED,
      "successRate": $success_rate,
      "overallStatus": "$([ $TESTS_FAILED -eq 0 ] && echo "SECURE" || echo "NEEDS_ATTENTION")"
    },
    "assessmentAreas": {
      "supplyChainSecurity": "$([ $TESTS_FAILED -le 2 ] && echo "GOOD" || echo "NEEDS_IMPROVEMENT")",
      "clientSideSecurity": "$([ $TESTS_FAILED -le 1 ] && echo "GOOD" || echo "NEEDS_IMPROVEMENT")",
      "backendSecurity": "$([ $TESTS_FAILED -eq 0 ] && echo "EXCELLENT" || echo "GOOD")",
      "containerSecurity": "$([ $TESTS_FAILED -eq 0 ] && echo "EXCELLENT" || echo "GOOD")",
      "securityMonitoring": "$([ $TESTS_FAILED -eq 0 ] && echo "EXCELLENT" || echo "GOOD")"
    },
    "recommendations": [
      "$([ $TESTS_FAILED -gt 0 ] && echo "Address failed security tests immediately" || echo "Maintain current security posture")",
      "Continue regular security assessments",
      "Monitor security metrics and incident response",
      "Keep dependencies updated and patched"
    ],
    "nextAssessment": "$(date -d '+7 days' -u +"%Y-%m-%dT%H:%M:%SZ")"
  }
}
EOF
    
    info "Security report generated: $REPORT_FILE"
}

# Run main function
main "$@"
