/**
 * Tests for image utility functions
 */

import {
  isValidImageUrl,
  getSafeImageUrl,
  generatePlaceholderImageUrl,
  getSafeDelegationImageUrl,
  getSafeVehicleImageUrl,
} from '../imageUtils';

describe('imageUtils', () => {
  describe('isValidImageUrl', () => {
    it('should return true for valid URLs', () => {
      expect(isValidImageUrl('https://example.com/image.jpg')).toBe(true);
      expect(isValidImageUrl('http://example.com/image.png')).toBe(true);
      expect(isValidImageUrl('https://picsum.photos/400/250')).toBe(true);
    });

    it('should return false for invalid URLs', () => {
      expect(isValidImageUrl('')).toBe(false);
      expect(isValidImageUrl('   ')).toBe(false);
      expect(isValidImageUrl(null)).toBe(false);
      expect(isValidImageUrl(undefined)).toBe(false);
    });
  });

  describe('getSafeImageUrl', () => {
    const fallbackUrl = 'https://example.com/fallback.jpg';

    it('should return the primary URL when valid', () => {
      const primaryUrl = 'https://example.com/primary.jpg';
      expect(getSafeImageUrl(primaryUrl, fallbackUrl)).toBe(primaryUrl);
    });

    it('should return the fallback URL when primary is invalid', () => {
      expect(getSafeImageUrl('', fallbackUrl)).toBe(fallbackUrl);
      expect(getSafeImageUrl('   ', fallbackUrl)).toBe(fallbackUrl);
      expect(getSafeImageUrl(null, fallbackUrl)).toBe(fallbackUrl);
      expect(getSafeImageUrl(undefined, fallbackUrl)).toBe(fallbackUrl);
    });
  });

  describe('generatePlaceholderImageUrl', () => {
    it('should generate correct picsum URLs with default dimensions', () => {
      const url = generatePlaceholderImageUrl('test-seed');
      expect(url).toBe('https://picsum.photos/seed/test-seed/400/250');
    });

    it('should generate correct picsum URLs with custom dimensions', () => {
      const url = generatePlaceholderImageUrl('test-seed', 600, 375);
      expect(url).toBe('https://picsum.photos/seed/test-seed/600/375');
    });

    it('should work with numeric seeds', () => {
      const url = generatePlaceholderImageUrl(123, 800, 600);
      expect(url).toBe('https://picsum.photos/seed/123/800/600');
    });
  });

  describe('getSafeDelegationImageUrl', () => {
    const delegationId = 'test-delegation-123';

    it('should return the delegation image URL when valid', () => {
      const imageUrl = 'https://example.com/delegation.jpg';
      expect(getSafeDelegationImageUrl(imageUrl, delegationId)).toBe(imageUrl);
    });

    it('should return placeholder URL when delegation image is invalid', () => {
      const result = getSafeDelegationImageUrl('', delegationId);
      expect(result).toBe('https://picsum.photos/seed/test-delegation-123/400/250');
    });

    it('should use correct dimensions for different sizes', () => {
      const cardResult = getSafeDelegationImageUrl(null, delegationId, 'card');
      expect(cardResult).toBe('https://picsum.photos/seed/test-delegation-123/400/250');

      const detailResult = getSafeDelegationImageUrl(null, delegationId, 'detail');
      expect(detailResult).toBe('https://picsum.photos/seed/test-delegation-123/600/375');

      const reportResult = getSafeDelegationImageUrl(null, delegationId, 'report');
      expect(reportResult).toBe('https://picsum.photos/seed/test-delegation-123/600/375');
    });
  });

  describe('getSafeVehicleImageUrl', () => {
    it('should return the vehicle image URL when valid', () => {
      const imageUrl = 'https://example.com/vehicle.jpg';
      expect(getSafeVehicleImageUrl(imageUrl, 'vehicle-123')).toBe(imageUrl);
    });

    it('should return placeholder URL when vehicle image is invalid', () => {
      const result = getSafeVehicleImageUrl('', 'vehicle-123');
      expect(result).toBe('https://picsum.photos/seed/vehicle-123/600/375');
    });

    it('should work with numeric vehicle IDs', () => {
      const result = getSafeVehicleImageUrl(null, 456);
      expect(result).toBe('https://picsum.photos/seed/456/600/375');
    });
  });
});
