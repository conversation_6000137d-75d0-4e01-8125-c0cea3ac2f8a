/**
 * @file DelegationOverviewCard component for displaying delegation overview
 * @module components/delegations/detail/DelegationOverviewCard
 */

import React from 'react';
import Image from 'next/image';
import { MapPin, CalendarDays, Users, Info } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { AspectRatio } from '@/components/ui/aspect-ratio';
import { Separator } from '@/components/ui/separator';
import { DetailItem } from '../common/DetailItem';
import { getSafeDelegationImageUrl } from '@/lib/utils/imageUtils';
import type { Delegation } from '@/lib/types/domain';
import { format, parseISO } from 'date-fns';

interface DelegationOverviewCardProps {
  delegation: Delegation;
  className?: string;
}

const formatDate = (dateString: string | undefined) => {
  if (!dateString) return 'N/A';
  try {
    return format(parseISO(dateString), 'MMM d, yyyy');
  } catch {
    return 'Invalid Date';
  }
};

/**
 * DelegationOverviewCard component for displaying delegation overview
 * Shows main delegation information with image and key details
 */
export function DelegationOverviewCard({ delegation, className }: DelegationOverviewCardProps) {
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <MapPin className="h-5 w-5 text-blue-600 dark:text-blue-400" />
          <span>Event Overview</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Image Section */}
        <AspectRatio ratio={16 / 9} className="overflow-hidden rounded-lg">
          <Image
            src={getSafeDelegationImageUrl(
              delegation.imageUrl,
              delegation.id,
              'detail'
            )}
            alt={delegation.eventName}
            className="object-cover transition-transform hover:scale-105"
            fill
            priority
          />
        </AspectRatio>
        
        <Separator />
        
        {/* Details Grid */}
        <div className="grid gap-4 md:grid-cols-2">
          <DetailItem
            icon={CalendarDays}
            label="Duration"
            value={`${formatDate(delegation.durationFrom)} - ${formatDate(delegation.durationTo)}`}
          />
          <DetailItem
            icon={MapPin}
            label="Location"
            value={delegation.location}
          />
          {delegation.invitationFrom && (
            <DetailItem
              icon={Users}
              label="Invitation From"
              value={delegation.invitationFrom}
            />
          )}
          {delegation.invitationTo && (
            <DetailItem
              icon={Users}
              label="Invitation To"
              value={delegation.invitationTo}
            />
          )}
        </div>
        
        {delegation.notes && (
          <>
            <Separator />
            <div className="space-y-2">
              <DetailItem
                icon={Info}
                label="General Notes"
                valueClassName="whitespace-pre-wrap"
                value={delegation.notes}
              />
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
}

export default DelegationOverviewCard;
