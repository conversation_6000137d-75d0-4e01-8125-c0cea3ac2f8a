// frontend/src/components/features/reporting/index.ts

/**
 * Reporting Feature Export Index
 *
 * Centralized exports for the complete reporting system following DRY principles.
 * This module provides all components, hooks, services, and types needed for reporting functionality.
 */

// Main Dashboard (Phase 4)
export * from './dashboard';

// Charts and Visualizations (Phase 3)
export * from './charts';

// Data Tables (Phase 3)
export * from './tables';

// Data Layer (Phase 2)
export * from './data/hooks/useReportingQueries';
export * from './data/hooks/useRealtimeReportingUpdates';
export * from './data/stores/useReportingFiltersStore';
export * from './data/services/ReportingDataService';
export * from './data/transformers/reportingTransformers';

// Export System (Phase 3)
export * from './exports';

// ENHANCED: Phase 5 Advanced Features - Collaborative Features
export { useCollaborativeFeatures } from './hooks/useCollaborativeFeatures';
export { CollaborativeComments } from './components/CollaborativeComments';

// Types (excluding conflicting exports)
export type {
  ReportingFilters,
  DelegationAnalytics,
  TaskMetrics,
  TrendData,
  LocationMetrics,
  StatusDistributionData,
  ServiceHistoryData,
  ServiceCostSummary,
  TaskAnalyticsData,
  DelegationReportData,
  ExportOptions,
  IReportingDataService,
  Delegation,
  SummaryMetrics,
  PaginatedDelegationsResponse,
  // Raw data types
  RawDelegationAnalytics,
  RawTaskMetrics,
  RawTrendData,
  RawLocationMetrics,
  RawServiceHistoryData,
  RawServiceCostSummary,
  RawTaskAnalyticsData,
  // Enum re-exports
  DelegationStatusPrisma,
  TaskStatusPrisma,
  TaskPriorityPrisma,
} from './data/types/reporting';

export type {
  ServiceTypePrisma,
  ServiceStatusPrisma,
} from './data/types/vehicleService';
