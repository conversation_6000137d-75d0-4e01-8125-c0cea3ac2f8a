/**
 * Enhanced HTTP Error class for standardized error handling
 * Follows the standardized API response format
 */
class HttpError extends Error {
  public readonly status: number;
  public readonly code: string;
  public readonly details?: any;

  constructor(message: string, status: number, code?: string, details?: any) {
    super(message);
    this.name = 'HttpError';
    this.status = status;
    this.code = code || this.getDefaultCode(status);
    this.details = details;

    // Ensure instanceof works correctly
    Object.setPrototypeOf(this, HttpError.prototype);
  }

  /**
   * Get default error code based on HTTP status
   */
  private getDefaultCode(status: number): string {
    switch (status) {
      case 400:
        return 'BAD_REQUEST';
      case 401:
        return 'UNAUTHORIZED';
      case 403:
        return 'FORBIDDEN';
      case 404:
        return 'NOT_FOUND';
      case 409:
        return 'CONFLICT';
      case 422:
        return 'VALIDATION_ERROR';
      case 429:
        return 'RATE_LIMIT_EXCEEDED';
      case 500:
        return 'INTERNAL_SERVER_ERROR';
      case 502:
        return 'BAD_GATEWAY';
      case 503:
        return 'SERVICE_UNAVAILABLE';
      default:
        return 'HTTP_ERROR';
    }
  }

  /**
   * Create a standardized error response object
   */
  toErrorResponse(requestId?: string): {
    status: 'error';
    code: string;
    message: string;
    statusCode: number;
    error: {
      message: string;
      details?: any;
    };
    requestId?: string;
    timestamp: string;
  } {
    return {
      status: 'error' as const,
      code: this.code,
      message: this.message,
      statusCode: this.status,
      error: {
        message: this.message,
        ...(this.details && { details: this.details }),
      },
      ...(requestId && { requestId }),
      timestamp: new Date().toISOString(),
    };
  }
}

export default HttpError;
