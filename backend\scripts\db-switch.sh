#!/bin/bash

# Database Switching Helper Script for WorkHub
# This script helps switch between local PostgreSQL and Supabase databases

set -e

BACKEND_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
ENV_FILE="$BACKEND_DIR/.env"

show_usage() {
    echo "Usage: $0 [local|supabase|status]"
    echo ""
    echo "Commands:"
    echo "  local     - Switch to local PostgreSQL database"
    echo "  supabase  - Switch to Supabase database"
    echo "  status    - Show current database configuration"
    echo ""
    echo "Examples:"
    echo "  $0 local     # Switch to local database"
    echo "  $0 supabase  # Switch to Supabase database"
    echo "  $0 status    # Check current configuration"
}

show_status() {
    echo "=== Current Database Configuration ==="
    if [ -f "$ENV_FILE" ]; then
        local use_supabase=$(grep "^USE_SUPABASE=" "$ENV_FILE" | cut -d'=' -f2)
        local database_url=$(grep "^DATABASE_URL=" "$ENV_FILE" | cut -d'=' -f2-)
        
        echo "USE_SUPABASE: $use_supabase"
        if [[ "$use_supabase" == "true" ]]; then
            echo "Current Database: Supabase"
            echo "Database URL: ${database_url:0:50}..."
        else
            echo "Current Database: Local PostgreSQL"
            echo "Database URL: $database_url"
        fi
    else
        echo "Error: .env file not found at $ENV_FILE"
        exit 1
    fi
    echo "======================================"
}

switch_to_local() {
    echo "Switching to local PostgreSQL database..."
    
    # Update USE_SUPABASE to false
    sed -i 's/^USE_SUPABASE=.*/USE_SUPABASE=false/' "$ENV_FILE"
    
    # Comment out Supabase DATABASE_URL and uncomment local DATABASE_URL
    sed -i 's/^DATABASE_URL=postgresql:\/\/postgres\..*/#&/' "$ENV_FILE"
    sed -i 's/^# DATABASE_URL=postgresql:\/\/postgres:postgres@localhost/DATABASE_URL=postgresql:\/\/postgres:postgres@localhost/' "$ENV_FILE"
    
    echo "✅ Switched to local PostgreSQL database"
    show_status
}

switch_to_supabase() {
    echo "Switching to Supabase database..."
    
    # Update USE_SUPABASE to true
    sed -i 's/^USE_SUPABASE=.*/USE_SUPABASE=true/' "$ENV_FILE"
    
    # Comment out local DATABASE_URL and uncomment Supabase DATABASE_URL
    sed -i 's/^DATABASE_URL=postgresql:\/\/postgres:postgres@localhost/#&/' "$ENV_FILE"
    sed -i 's/^#DATABASE_URL=postgresql:\/\/postgres\..*$/DATABASE_URL=postgresql:\/\/postgres.abylqjnpaegeqwktcukn:<EMAIL>:5432\/postgres/' "$ENV_FILE"
    
    echo "✅ Switched to Supabase database"
    show_status
}

# Main script logic
case "${1:-}" in
    "local")
        switch_to_local
        ;;
    "supabase")
        switch_to_supabase
        ;;
    "status")
        show_status
        ;;
    "")
        show_usage
        exit 1
        ;;
    *)
        echo "Error: Unknown command '$1'"
        echo ""
        show_usage
        exit 1
        ;;
esac
