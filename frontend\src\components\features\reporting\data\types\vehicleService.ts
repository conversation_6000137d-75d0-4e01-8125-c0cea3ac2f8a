// frontend/src/components/features/reporting/data/types/vehicleService.ts

/**
 * Vehicle Service Reporting Types
 * 
 * Defines all types and interfaces for vehicle service reporting functionality.
 * Follows SOLID principles and integrates with existing reporting architecture.
 */

// Service status enumeration
export type ServiceStatusPrisma = 
  | 'SCHEDULED'
  | 'IN_PROGRESS' 
  | 'COMPLETED'
  | 'CANCELLED'
  | 'OVERDUE'
  | 'PENDING_APPROVAL';

// Service type enumeration
export type ServiceTypePrisma = 
  | 'MAINTENANCE'
  | 'REPAIR'
  | 'INSPECTION'
  | 'CLEANING'
  | 'FUEL'
  | 'EMERGENCY'
  | 'UPGRADE'
  | 'INSURANCE';

// Priority levels for services
export type ServicePriorityPrisma = 
  | 'LOW'
  | 'MEDIUM'
  | 'HIGH'
  | 'URGENT'
  | 'CRITICAL';

/**
 * Main vehicle service analytics interface
 */
export interface VehicleServiceAnalytics {
  totalServices: number;
  statusDistribution: ServiceStatusDistribution[];
  typeDistribution: ServiceTypeDistribution[];
  costAnalytics: ServiceCostAnalytics;
  trendData: ServiceTrendData[];
  vehicleMetrics: VehicleServiceMetrics[];
  summary: ServiceSummaryMetrics;
  delegations?: string[]; // Related delegation IDs
  tasks?: string[]; // Related task IDs
}

/**
 * Service status distribution data
 */
export interface ServiceStatusDistribution {
  status: ServiceStatusPrisma;
  count: number;
  percentage: number;
  averageCost: number;
  averageDuration: number; // in hours
  color: string;
}

/**
 * Service type distribution data
 */
export interface ServiceTypeDistribution {
  type: ServiceTypePrisma;
  count: number;
  percentage: number;
  totalCost: number;
  averageCost: number;
  color: string;
}

/**
 * Service cost analytics
 */
export interface ServiceCostAnalytics {
  totalCost: number;
  averageCostPerService: number;
  costByType: ServiceTypeCost[];
  costTrend: ServiceCostTrend[];
  budgetUtilization: number;
  costPerVehicle: VehicleCostMetrics[];
  monthlyBudget: number;
  projectedAnnualCost: number;
}

/**
 * Service cost by type
 */
export interface ServiceTypeCost {
  type: ServiceTypePrisma;
  cost: number;
  percentage: number;
  count: number;
}

/**
 * Service cost trend data
 */
export interface ServiceCostTrend {
  month: string;
  cost: number;
  serviceCount: number;
  averageCostPerService: number;
}

/**
 * Vehicle cost metrics
 */
export interface VehicleCostMetrics {
  vehicleId: number;
  vehicleName: string;
  totalCost: number;
  serviceCount: number;
  averageCostPerService: number;
  lastServiceDate: string;
  nextScheduledService: string;
}

/**
 * Service trend data for time-series analysis
 */
export interface ServiceTrendData {
  date: string;
  scheduled: number;
  completed: number;
  inProgress: number;
  cancelled: number;
  totalCost: number;
}

/**
 * Vehicle service metrics
 */
export interface VehicleServiceMetrics {
  vehicleId: number;
  vehicleName: string;
  make: string;
  model: string;
  year: number;
  licensePlate: string;
  totalServices: number;
  completedServices: number;
  pendingServices: number;
  totalCost: number;
  averageCostPerService: number;
  lastServiceDate: string;
  nextScheduledService: string;
  utilizationRate: number; // percentage
  maintenanceScore: number; // 0-100
}

/**
 * Service summary metrics
 */
export interface ServiceSummaryMetrics {
  totalServices: number;
  completedServices: number;
  pendingServices: number;
  overdueServices: number;
  totalVehicles: number;
  activeVehicles: number;
  totalCost: number;
  averageCostPerService: number;
  averageServiceDuration: number; // in hours
  completionRate: number; // percentage
  onTimeCompletionRate: number; // percentage
  budgetUtilization: number; // percentage
  costEfficiencyScore: number; // 0-100
}

/**
 * Individual service record
 */
export interface VehicleServiceRecord {
  id: string;
  vehicleId: number;
  serviceType: ServiceTypePrisma;
  status: ServiceStatusPrisma;
  priority: ServicePriorityPrisma;
  title: string;
  description?: string;
  scheduledDate: string;
  startDate?: string;
  completedDate?: string;
  estimatedCost: number;
  actualCost?: number;
  estimatedDuration: number; // in hours
  actualDuration?: number; // in hours
  serviceProvider: string;
  location: string;
  assignedTechnician?: string;
  notes?: string;
  relatedDelegationId?: string;
  relatedTaskId?: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * Service provider information
 */
export interface ServiceProvider {
  id: string;
  name: string;
  type: 'INTERNAL' | 'EXTERNAL';
  contactInfo: {
    phone?: string;
    email?: string;
    address?: string;
  };
  specialties: ServiceTypePrisma[];
  rating: number; // 0-5
  totalServices: number;
  averageCost: number;
  averageRating: number;
  onTimeRate: number; // percentage
}

/**
 * Service filters specific to vehicle services
 */
export interface VehicleServiceFilters {
  serviceTypes: ServiceTypePrisma[];
  serviceStatus: ServiceStatusPrisma[];
  priorities: ServicePriorityPrisma[];
  costRange: {
    min: number;
    max: number;
  };
  serviceProviders: string[];
  vehicleIds: number[];
  scheduledDateRange: {
    from: Date;
    to: Date;
  };
  includeRelatedDelegations: boolean;
  includeRelatedTasks: boolean;
}

/**
 * Chart props for service-specific charts
 */
export interface ServiceChartProps {
  data: any;
  loading?: boolean;
  error?: string;
  height?: number;
  interactive?: boolean;
  showCostOverlay?: boolean;
  showTrendLine?: boolean;
}

/**
 * Widget props for service-specific widgets
 */
export interface ServiceWidgetProps {
  title?: string;
  description?: string;
  loading?: boolean;
  error?: string;
  showActions?: boolean;
  onExport?: () => void;
  onConfigure?: () => void;
  onViewDetails?: () => void;
}

export default VehicleServiceAnalytics;
