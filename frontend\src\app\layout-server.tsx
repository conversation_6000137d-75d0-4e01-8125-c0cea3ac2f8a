/**
 * @file Server Layout Wrapper for CSP Nonce Access
 * @module app/layout-server
 *
 * Server component that extracts nonce from middleware headers
 * and provides it to the client layout through CSP context.
 */

import { headers, cookies } from 'next/headers';
import type { ReactNode } from 'react';
import { CSPProvider } from '@/lib/security/CSPProvider';
import ClientLayout from './layout-client';

interface ServerLayoutProps {
  children: ReactNode;
}

/**
 * Server Layout Wrapper
 *
 * Extracts nonce from middleware cookies and provides CSP context.
 * Single responsibility: Server-side nonce extraction and CSP setup.
 */
export default async function ServerLayout({ children }: ServerLayoutProps) {
  // Extract nonce from middleware (try both headers and cookies)
  const headersList = await headers();
  const cookieStore = await cookies();
  const nonceFromHeader = headersList.get('x-nonce');
  const nonceFromCookie = cookieStore.get('x-nonce')?.value;

  // Fallback: Generate a static nonce for development if middleware isn't working
  const fallbackNonce =
    process.env.NODE_ENV === 'development'
      ? Buffer.from('dev-static-nonce-' + Date.now())
          .toString('base64')
          .substring(0, 16)
      : null;

  const nonce = nonceFromHeader || nonceFromCookie || fallbackNonce;

  // Log nonce for debugging (remove in production)
  if (process.env.NODE_ENV === 'development') {
    console.log(
      '🔒 CSP Nonce extracted:',
      nonce ? `Present (${nonce.substring(0, 16)}...)` : 'Missing'
    );
    console.log('🔍 Nonce from header:', nonceFromHeader || 'Not found');
    console.log('🔍 Nonce from cookie:', nonceFromCookie || 'Not found');
    console.log('🔍 Fallback nonce:', fallbackNonce || 'Not generated');
    console.log(
      '🔍 Final nonce source:',
      nonceFromHeader
        ? 'Header'
        : nonceFromCookie
          ? 'Cookie'
          : fallbackNonce
            ? 'Fallback'
            : 'None'
    );

    // Debug: Show all available headers and cookies
    const availableHeaders = Array.from(headersList.keys()).join(', ');
    const availableCookies = Array.from(cookieStore.getAll())
      .map(cookie => cookie.name)
      .join(', ');
    console.log('🔍 Available headers:', availableHeaders);
    console.log('🔍 Available cookies:', availableCookies);
  }

  return (
    <CSPProvider nonce={nonce}>
      <ClientLayout>{children}</ClientLayout>
    </CSPProvider>
  );
}
