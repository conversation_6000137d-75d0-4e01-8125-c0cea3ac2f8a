/**
 * @file Reporting Data Service
 * @description Handles all data fetching and API interactions for the reporting feature.
 */

import type {
  DelegationAnalytics,
  IReportingDataService,
  LocationMetrics,
  PaginatedDelegationsResponse,
  ReportingFilters,
  TaskMetrics,
  TrendData,
} from '@/components/features/reporting';
import type {
  CrossEntityAnalytics,
  EmployeeAnalytics,
  TaskAnalytics,
  VehicleAnalytics,
} from '@/components/features/reporting/data/types/reporting';

import { apiClient } from '@/lib/api'; // Assuming a pre-configured api client

class ReportingService implements IReportingDataService {
  private readonly baseUrl = '/reporting';

  /**
   * Fetches cross-entity analytics data from the API.
   */
  async getCrossEntityAnalytics(
    filters: ReportingFilters
  ): Promise<CrossEntityAnalytics> {
    const queryParams = new URLSearchParams();
    queryParams.append('from', filters.dateRange.from.toISOString());
    queryParams.append('to', filters.dateRange.to.toISOString());

    if (filters.status) {
      for (const status of filters.status) queryParams.append('status', status);
    }
    if (filters.taskStatus) {
      for (const status of filters.taskStatus)
        queryParams.append('taskStatus', status);
    }
    if (filters.taskPriority) {
      filters.taskPriority.forEach((priority: string) =>
        queryParams.append('taskPriority', priority)
      );
    }
    if (filters.employees) {
      for (const emp of filters.employees)
        queryParams.append('employees', emp.toString());
    }
    if (filters.vehicles) {
      for (const vehicle of filters.vehicles)
        queryParams.append('vehicles', vehicle.toString());
    }
    if (filters.locations) {
      for (const location of filters.locations)
        queryParams.append('locations', location);
    }
    if (filters.includeServiceHistory) {
      queryParams.append('includeServiceHistory', 'true');
    }
    if (filters.includeTaskData) {
      queryParams.append('includeTaskData', 'true');
    }

    return apiClient.get(
      `${this.baseUrl}/cross-entity-analytics?${queryParams.toString()}`
    );
  }

  /**
   * Fetches delegation analytics data from the API.
   * @param filters - The filters to apply to the query.
   * @returns A promise that resolves to the delegation analytics data.
   */
  async getDelegationAnalytics(
    filters: ReportingFilters
  ): Promise<DelegationAnalytics> {
    const queryParams = new URLSearchParams();

    // Add date range
    queryParams.append('dateRange.from', filters.dateRange.from.toISOString());
    queryParams.append('dateRange.to', filters.dateRange.to.toISOString());

    // Add arrays
    if (filters.status) {
      for (const status of filters.status) queryParams.append('status', status);
    }
    if (filters.locations) {
      for (const location of filters.locations)
        queryParams.append('locations', location);
    }
    if (filters.employees) {
      for (const emp of filters.employees)
        queryParams.append('employees', emp.toString());
    }
    if (filters.vehicles) {
      for (const vehicle of filters.vehicles)
        queryParams.append('vehicles', vehicle.toString());
    }

    // Add min/max cost
    if (filters.minCost !== undefined) {
      queryParams.append('minCost', filters.minCost.toString());
    }
    if (filters.maxCost !== undefined) {
      queryParams.append('maxCost', filters.maxCost.toString());
    }

    return apiClient.get(
      `${this.baseUrl}/delegation-analytics?${queryParams.toString()}`
    );
  }

  /**
   * Fetches a paginated list of delegations from the API.
   * @param filters - The filters to apply to the query.
   * @param pagination - The pagination parameters.
   * @returns A promise that resolves to the paginated delegation data.
   */
  async getDelegations(
    filters: ReportingFilters,
    pagination: { page: number; pageSize: number }
  ): Promise<PaginatedDelegationsResponse> {
    const queryParams = new URLSearchParams();

    // Add date range
    queryParams.append('dateRange.from', filters.dateRange.from.toISOString());
    queryParams.append('dateRange.to', filters.dateRange.to.toISOString());

    // Add filter arrays
    if (filters.status) {
      for (const status of filters.status) queryParams.append('status', status);
    }
    if (filters.locations) {
      for (const location of filters.locations)
        queryParams.append('locations', location);
    }
    if (filters.employees) {
      for (const emp of filters.employees)
        queryParams.append('employees', emp.toString());
    }
    if (filters.vehicles) {
      for (const vehicle of filters.vehicles)
        queryParams.append('vehicles', vehicle.toString());
    }

    // Add pagination
    queryParams.append('page', pagination.page.toString());
    queryParams.append('pageSize', pagination.pageSize.toString());

    // Add min/max cost
    if (filters.minCost !== undefined) {
      queryParams.append('minCost', filters.minCost.toString());
    }
    if (filters.maxCost !== undefined) {
      queryParams.append('maxCost', filters.maxCost.toString());
    }

    return apiClient.get(
      `${this.baseUrl}/delegations?${queryParams.toString()}`
    );
  }

  /**
   * Fetches employee analytics data from the API.
   */
  async getEmployeeAnalytics(
    filters: ReportingFilters
  ): Promise<EmployeeAnalytics> {
    const queryParams = new URLSearchParams();
    queryParams.append('from', filters.dateRange.from.toISOString());
    queryParams.append('to', filters.dateRange.to.toISOString());

    if (filters.employees) {
      for (const emp of filters.employees)
        queryParams.append('employees', emp.toString());
    }

    return apiClient.get(
      `${this.baseUrl}/employee-analytics?${queryParams.toString()}`
    );
  }

  /**
   * Fetches location metrics based on the provided filters.
   * @param filters - The filters to apply to the query.
   * @returns A promise that resolves to the location metrics.
   */
  async getLocationMetrics(
    filters: ReportingFilters
  ): Promise<LocationMetrics[]> {
    const queryParams = new URLSearchParams();
    queryParams.append('from', filters.dateRange.from.toISOString());
    queryParams.append('to', filters.dateRange.to.toISOString());
    for (const status of filters.status) queryParams.append('status', status);
    for (const location of filters.locations)
      queryParams.append('locations', location);
    for (const emp of filters.employees)
      queryParams.append('employees', emp.toString());
    for (const vehicle of filters.vehicles)
      queryParams.append('vehicles', vehicle.toString());

    return apiClient.get(
      `${this.baseUrl}/locations/metrics?${queryParams.toString()}`
    );
  }

  /**
   * Fetches task analytics data from the API.
   */
  async getTaskAnalytics(filters: ReportingFilters): Promise<TaskAnalytics> {
    const queryParams = new URLSearchParams();
    queryParams.append('from', filters.dateRange.from.toISOString());
    queryParams.append('to', filters.dateRange.to.toISOString());

    if (filters.taskStatus) {
      for (const status of filters.taskStatus)
        queryParams.append('taskStatus', status);
    }
    if (filters.taskPriority) {
      filters.taskPriority.forEach((priority: string) =>
        queryParams.append('taskPriority', priority)
      );
    }
    if (filters.employees) {
      for (const emp of filters.employees)
        queryParams.append('employees', emp.toString());
    }

    return apiClient.get(
      `${this.baseUrl}/task-analytics?${queryParams.toString()}`
    );
  }

  /**
   * Fetches task metrics for a given set of delegation IDs.
   * @param delegationIds - The IDs of the delegations to fetch task metrics for.
   * @returns A promise that resolves to the task metrics.
   */
  async getTaskMetrics(delegationIds?: string[]): Promise<TaskMetrics> {
    const queryParams = new URLSearchParams();
    if (delegationIds) {
      for (const id of delegationIds) queryParams.append('delegationIds', id);
    }
    const queryString = queryParams.toString();
    const url = queryString
      ? `${this.baseUrl}/task-metrics?${queryString}`
      : `${this.baseUrl}/task-metrics`;
    return apiClient.get(url);
  }

  /**
   * Fetches trend data based on the provided filters.
   * @param filters - The filters to apply to the query.
   * @returns A promise that resolves to the trend data.
   */
  async getTrendData(filters: ReportingFilters): Promise<TrendData[]> {
    const queryParams = new URLSearchParams();
    queryParams.append('from', filters.dateRange.from.toISOString());
    queryParams.append('to', filters.dateRange.to.toISOString());
    for (const status of filters.status) queryParams.append('status', status);
    for (const location of filters.locations)
      queryParams.append('locations', location);
    for (const emp of filters.employees)
      queryParams.append('employees', emp.toString());
    for (const vehicle of filters.vehicles)
      queryParams.append('vehicles', vehicle.toString());

    return apiClient.get(`${this.baseUrl}/trends?${queryParams.toString()}`);
  }

  /**
   * Fetches vehicle analytics data from the API.
   */
  async getVehicleAnalytics(
    filters: ReportingFilters
  ): Promise<VehicleAnalytics> {
    const queryParams = new URLSearchParams();
    queryParams.append('from', filters.dateRange.from.toISOString());
    queryParams.append('to', filters.dateRange.to.toISOString());

    if (filters.vehicles) {
      for (const vehicle of filters.vehicles)
        queryParams.append('vehicles', vehicle.toString());
    }
    if (filters.serviceTypes) {
      for (const type of filters.serviceTypes)
        queryParams.append('serviceTypes', type);
    }
    if (filters.serviceStatus) {
      for (const status of filters.serviceStatus)
        queryParams.append('serviceStatus', status);
    }

    return apiClient.get(
      `${this.baseUrl}/vehicle-analytics?${queryParams.toString()}`
    );
  }
}

export const reportingService = new ReportingService();
