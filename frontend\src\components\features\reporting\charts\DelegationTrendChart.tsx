// frontend/src/components/features/reporting/charts/DelegationTrendChart.tsx

'use client';

import React, { useMemo, useState } from 'react';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  AreaChart,
  Area,
} from 'recharts';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { TrendData } from '../data/types/reporting';
import { format, parseISO } from 'date-fns';

/**
 * Props for DelegationTrendChart component
 */
interface DelegationTrendChartProps {
  data: TrendData[];
  loading?: boolean;
  error?: string | undefined; // FIXED: Allow undefined for strict mode
  height?: number;
  showLegend?: boolean;
  interactive?: boolean;
  className?: string;
}

/**
 * Custom tooltip for trend chart
 *
 * Follows SRP: Only responsible for tooltip rendering
 */
const TrendTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    const date = format(parseISO(label), 'MMM dd, yyyy');

    return (
      <div className="bg-white p-4 border border-gray-200 rounded-lg shadow-lg">
        <p className="font-semibold text-gray-900 mb-2">{date}</p>
        {payload.map((entry: any, index: number) => (
          <div key={index} className="flex items-center gap-2 text-sm">
            <div
              className="w-3 h-3 rounded-full"
              style={{ backgroundColor: entry.color }}
            />
            <span className="text-gray-600">{entry.name}:</span>
            <span className="font-medium text-gray-900">{entry.value}</span>
          </div>
        ))}
      </div>
    );
  }
  return null;
};

/**
 * Loading skeleton for trend chart
 */
const TrendLoadingSkeleton = ({ height }: { height: number }) => (
  <div className="animate-pulse">
    <div
      className="flex items-end justify-between mb-4"
      style={{ height: height - 50 }}
    >
      {[1, 2, 3, 4, 5, 6, 7].map(i => (
        <div
          key={i}
          className="bg-gray-200 rounded-t"
          style={{
            width: '12%',
            height: `${Math.random() * 80 + 20}%`,
          }}
        />
      ))}
    </div>
    <div className="flex justify-center gap-6">
      {['Created', 'Completed', 'In Progress'].map(label => (
        <div key={label} className="flex items-center gap-2">
          <div className="w-3 h-3 bg-gray-200 rounded-full"></div>
          <div className="w-16 h-4 bg-gray-200 rounded"></div>
        </div>
      ))}
    </div>
  </div>
);

/**
 * Error display for trend chart
 */
const TrendErrorDisplay = ({ error }: { error: string }) => (
  <div className="flex flex-col items-center justify-center h-64 text-gray-500">
    <div className="text-4xl mb-2">📈</div>
    <p className="text-sm">Failed to load trend data</p>
    <p className="text-xs text-gray-400 mt-1">{error}</p>
  </div>
);

/**
 * DelegationTrendChart Component
 *
 * Displays delegation trends over time with multiple visualization options.
 * Supports both line and area chart views with interactive features.
 *
 * Features:
 * - Line and area chart views
 * - Interactive tooltips and legends
 * - Date formatting
 * - Responsive design
 * - Loading and error states
 * - Data point highlighting
 *
 * @param props - Component props
 * @returns JSX element
 */
export const DelegationTrendChart: React.FC<DelegationTrendChartProps> = ({
  data,
  loading = false,
  error,
  height = 400,
  showLegend = true,
  interactive = true,
  className = '',
}) => {
  const [hiddenLines, setHiddenLines] = useState<Set<string>>(new Set());

  // Process and format data for charts
  const processedData = useMemo(() => {
    return data.map(item => ({
      ...item,
      formattedDate: format(parseISO(item.date), 'MMM dd'),
      fullDate: item.date,
    }));
  }, [data]);

  // Chart configuration
  const chartConfig = {
    created: {
      color: '#3b82f6',
      name: 'Created',
    },
    completed: {
      color: '#10b981',
      name: 'Completed',
    },
    inProgress: {
      color: '#f59e0b',
      name: 'In Progress',
    },
  };

  // Handle legend click to toggle line visibility
  const handleLegendClick = (dataKey: string) => {
    if (!interactive) return;

    const newHiddenLines = new Set(hiddenLines);
    if (newHiddenLines.has(dataKey)) {
      newHiddenLines.delete(dataKey);
    } else {
      newHiddenLines.add(dataKey);
    }
    setHiddenLines(newHiddenLines);
  };

  // Custom legend component
  const CustomLegend = ({ payload }: any) => (
    <div className="flex flex-wrap justify-center gap-4 mt-4">
      {payload?.map((entry: any) => (
        <Button
          key={entry.dataKey}
          variant={hiddenLines.has(entry.dataKey) ? 'outline' : 'default'}
          size="sm"
          className="flex items-center gap-2 h-8"
          onClick={() => handleLegendClick(entry.dataKey)}
        >
          <div
            className="w-3 h-3 rounded-full"
            style={{
              backgroundColor: hiddenLines.has(entry.dataKey)
                ? '#d1d5db'
                : entry.color,
            }}
          />
          <span className="text-sm">{entry.value}</span>
        </Button>
      ))}
    </div>
  );

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>Delegation Trends</CardTitle>
          <CardDescription>Loading trend data...</CardDescription>
        </CardHeader>
        <CardContent>
          <TrendLoadingSkeleton height={height} />
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>Delegation Trends</CardTitle>
          <CardDescription>Trend data unavailable</CardDescription>
        </CardHeader>
        <CardContent>
          <TrendErrorDisplay error={error} />
        </CardContent>
      </Card>
    );
  }

  if (!data || data.length === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>Delegation Trends</CardTitle>
          <CardDescription>No trend data available</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center justify-center h-64 text-gray-500">
            <div className="text-4xl mb-2">📈</div>
            <p className="text-sm">No trend data to display</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>Delegation Trends</CardTitle>
        <CardDescription>Track delegation activity over time</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="line" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="line">Line Chart</TabsTrigger>
            <TabsTrigger value="area">Area Chart</TabsTrigger>
          </TabsList>

          <TabsContent value="line" className="mt-4">
            <ResponsiveContainer width="100%" height={height}>
              <LineChart
                data={processedData}
                margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                <XAxis
                  dataKey="formattedDate"
                  fontSize={12}
                  tickLine={false}
                  axisLine={false}
                />
                <YAxis fontSize={12} tickLine={false} axisLine={false} />
                {interactive && <Tooltip content={<TrendTooltip />} />}

                {Object.entries(chartConfig).map(([key, config]) => (
                  <Line
                    key={key}
                    type="monotone"
                    dataKey={key}
                    stroke={config.color}
                    strokeWidth={2}
                    dot={{ fill: config.color, strokeWidth: 2, r: 4 }}
                    activeDot={{ r: 6, stroke: config.color, strokeWidth: 2 }}
                    hide={hiddenLines.has(key)}
                    animationDuration={800}
                  />
                ))}

                {showLegend && (
                  <Legend
                    content={<CustomLegend />}
                    wrapperStyle={{ paddingTop: '20px' }}
                  />
                )}
              </LineChart>
            </ResponsiveContainer>
          </TabsContent>

          <TabsContent value="area" className="mt-4">
            <ResponsiveContainer width="100%" height={height}>
              <AreaChart
                data={processedData}
                margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                <XAxis
                  dataKey="formattedDate"
                  fontSize={12}
                  tickLine={false}
                  axisLine={false}
                />
                <YAxis fontSize={12} tickLine={false} axisLine={false} />
                {interactive && <Tooltip content={<TrendTooltip />} />}

                {Object.entries(chartConfig).map(([key, config]) => (
                  <Area
                    key={key}
                    type="monotone"
                    dataKey={key}
                    stackId="1"
                    stroke={config.color}
                    fill={config.color}
                    fillOpacity={0.6}
                    hide={hiddenLines.has(key)}
                    animationDuration={800}
                  />
                ))}

                {showLegend && (
                  <Legend
                    content={<CustomLegend />}
                    wrapperStyle={{ paddingTop: '20px' }}
                  />
                )}
              </AreaChart>
            </ResponsiveContainer>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};
