// Debug script to test delegation payload formatting
import { formatDateForApi } from './src/lib/utils/dateUtils.js';

// Test the exact format being sent
const testPayload = {
  delegates: [
    {
      name: '<PERSON>',
      title: 'Manager',
    },
  ],
  durationFrom: '2024-01-15', // Raw date from HTML input
  durationTo: '2024-01-16', // Raw date from HTML input
  eventName: 'Test Event',
  flightArrivalDetails: {
    airport: 'JFK',
    dateTime: '2024-01-15T10:00', // datetime-local input format
    flightNumber: 'AA123',
  },
  flightDepartureDetails: {
    airport: 'LAX',
    dateTime: '2024-01-16T14:00', // datetime-local input format
    flightNumber: 'AA456',
  },
  location: 'Test Location',
};

console.log('=== RAW PAYLOAD (what form sends) ===');
console.log(JSON.stringify(testPayload, null, 2));

console.log('\n=== AFTER formatDateForApi CONVERSION ===');
const processedPayload = {
  ...testPayload,
  durationFrom: formatDateForApi(testPayload.durationFrom),
  durationTo: formatDateForApi(testPayload.durationTo),
  flightArrivalDetails: testPayload.flightArrivalDetails
    ? {
        ...testPayload.flightArrivalDetails,
        dateTime: formatDateForApi(testPayload.flightArrivalDetails.dateTime),
      }
    : null,
  flightDepartureDetails: testPayload.flightDepartureDetails
    ? {
        ...testPayload.flightDepartureDetails,
        dateTime: formatDateForApi(testPayload.flightDepartureDetails.dateTime),
      }
    : null,
};

console.log(JSON.stringify(processedPayload, null, 2));

console.log('\n=== INDIVIDUAL DATE CONVERSIONS ===');
console.log(
  `durationFrom: "${testPayload.durationFrom}" -> "${formatDateForApi(
    testPayload.durationFrom
  )}"`
);
console.log(
  `durationTo: "${testPayload.durationTo}" -> "${formatDateForApi(
    testPayload.durationTo
  )}"`
);
console.log(
  `flight arrival: "${
    testPayload.flightArrivalDetails.dateTime
  }" -> "${formatDateForApi(testPayload.flightArrivalDetails.dateTime)}"`
);
console.log(
  `flight departure: "${
    testPayload.flightDepartureDetails.dateTime
  }" -> "${formatDateForApi(testPayload.flightDepartureDetails.dateTime)}"`
);
