#!/usr/bin/env node

/**
 * Backup Script: user_profiles Table Data
 *
 * This script creates a complete backup of the user_profiles table
 * before migrating to auth.users-only architecture.
 *
 * Usage: node backup-user-profiles.js
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables:');
  console.error('   SUPABASE_URL:', supabaseUrl ? '✅ Set' : '❌ Missing');
  console.error('   SUPABASE_SERVICE_ROLE_KEY:', supabaseServiceKey ? '✅ Set' : '❌ Missing');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function backupUserProfiles() {
  console.log('🔄 Starting user_profiles backup...');

  try {
    // Step 1: Get all user_profiles data
    console.log('📋 Step 1: Fetching all user_profiles data...');
    const { data: userProfiles, error: profilesError } = await supabase
      .from('user_profiles')
      .select('*')
      .order('created_at', { ascending: true });

    if (profilesError) {
      console.error('❌ Error fetching user_profiles:', profilesError);
      return false;
    }

    console.log(`✅ Found ${userProfiles.length} user profiles`);

    // Step 2: Get corresponding auth.users data for verification
    console.log('📋 Step 2: Fetching corresponding auth.users data...');
    const userIds = userProfiles.map(profile => profile.id);

    const { data: authUsers, error: authError } = await supabase.auth.admin.listUsers();

    if (authError) {
      console.error('❌ Error fetching auth users:', authError);
      return false;
    }

    // Filter auth users to only those with profiles
    const relevantAuthUsers = authUsers.users.filter(user => userIds.includes(user.id));
    console.log(`✅ Found ${relevantAuthUsers.length} corresponding auth users`);

    // Step 3: Create backup data structure
    const backupData = {
      timestamp: new Date().toISOString(),
      version: '1.0',
      description: 'Backup before migrating to auth.users-only architecture',
      user_profiles: userProfiles,
      auth_users_metadata: relevantAuthUsers.map(user => ({
        id: user.id,
        email: user.email,
        user_metadata: user.user_metadata,
        app_metadata: user.app_metadata,
        created_at: user.created_at,
        updated_at: user.updated_at,
      })),
      statistics: {
        total_profiles: userProfiles.length,
        total_auth_users: relevantAuthUsers.length,
        roles_distribution: getRoleDistribution(userProfiles),
        active_users: userProfiles.filter(p => p.is_active).length,
        inactive_users: userProfiles.filter(p => !p.is_active).length,
      },
    };

    // Step 4: Create backup directory if it doesn't exist
    const backupDir = path.join(process.cwd(), 'backups');
    if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir, { recursive: true });
    }

    // Step 5: Save backup to file
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupFileName = `user_profiles_backup_${timestamp}.json`;
    const backupFilePath = path.join(backupDir, backupFileName);

    fs.writeFileSync(backupFilePath, JSON.stringify(backupData, null, 2));
    console.log(`✅ Backup saved to: ${backupFilePath}`);

    // Step 6: Create SQL restore script
    const sqlRestoreScript = generateSQLRestoreScript(userProfiles);
    const sqlFileName = `restore_user_profiles_${timestamp}.sql`;
    const sqlFilePath = path.join(backupDir, sqlFileName);

    fs.writeFileSync(sqlFilePath, sqlRestoreScript);
    console.log(`✅ SQL restore script saved to: ${sqlFilePath}`);

    // Step 7: Verify backup integrity
    console.log('📋 Step 7: Verifying backup integrity...');
    const verificationResult = verifyBackupIntegrity(backupData, userProfiles);

    if (verificationResult.success) {
      console.log('✅ Backup integrity verified successfully');
      console.log(`📊 Backup Statistics:`);
      console.log(`   - Total Profiles: ${backupData.statistics.total_profiles}`);
      console.log(`   - Active Users: ${backupData.statistics.active_users}`);
      console.log(`   - Inactive Users: ${backupData.statistics.inactive_users}`);
      console.log(`   - Role Distribution:`, backupData.statistics.roles_distribution);

      return {
        success: true,
        backupFile: backupFilePath,
        sqlFile: sqlFilePath,
        statistics: backupData.statistics,
      };
    } else {
      console.error('❌ Backup integrity verification failed:', verificationResult.errors);
      return false;
    }
  } catch (error) {
    console.error('❌ Backup failed:', error);
    return false;
  }
}

function getRoleDistribution(userProfiles) {
  const distribution = {};
  userProfiles.forEach(profile => {
    distribution[profile.role] = (distribution[profile.role] || 0) + 1;
  });
  return distribution;
}

function generateSQLRestoreScript(userProfiles) {
  let sql = `-- User Profiles Restore Script
-- Generated: ${new Date().toISOString()}
-- This script restores the user_profiles table from backup

-- Recreate user_profiles table
CREATE TABLE IF NOT EXISTS public.user_profiles (
    id UUID PRIMARY KEY,
    role TEXT DEFAULT 'USER' CHECK (role IN ('SUPER_ADMIN', 'ADMIN', 'MANAGER', 'USER', 'READONLY')),
    employee_id INTEGER UNIQUE,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert backup data
`;

  userProfiles.forEach(profile => {
    sql += `INSERT INTO public.user_profiles (id, role, employee_id, is_active, created_at, updated_at) 
VALUES ('${profile.id}', '${profile.role}', ${profile.employee_id || 'NULL'}, ${profile.is_active}, '${profile.created_at}', '${profile.updated_at}');
`;
  });

  sql += `
-- Verify restore
SELECT COUNT(*) as restored_count FROM public.user_profiles;
SELECT role, COUNT(*) as count FROM public.user_profiles GROUP BY role;
`;

  return sql;
}

function verifyBackupIntegrity(backupData, originalProfiles) {
  const errors = [];

  // Check if all profiles are backed up
  if (backupData.user_profiles.length !== originalProfiles.length) {
    errors.push(
      `Profile count mismatch: backup has ${backupData.user_profiles.length}, original has ${originalProfiles.length}`,
    );
  }

  // Check if all IDs are present
  const backupIds = new Set(backupData.user_profiles.map(p => p.id));
  const originalIds = new Set(originalProfiles.map(p => p.id));

  for (const id of originalIds) {
    if (!backupIds.has(id)) {
      errors.push(`Missing profile ID in backup: ${id}`);
    }
  }

  // Check statistics
  const expectedActive = originalProfiles.filter(p => p.is_active).length;
  if (backupData.statistics.active_users !== expectedActive) {
    errors.push(
      `Active user count mismatch: backup has ${backupData.statistics.active_users}, expected ${expectedActive}`,
    );
  }

  return {
    success: errors.length === 0,
    errors,
  };
}

// Run backup if this script is executed directly
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const isMainModule = process.argv[1] === __filename;

if (isMainModule) {
  console.log('🚀 Starting backup script...');

  backupUserProfiles()
    .then(result => {
      if (result) {
        console.log('🎉 Backup completed successfully!');
        console.log('📁 Backup file:', result.backupFile);
        console.log('📄 SQL file:', result.sqlFile);
        process.exit(0);
      } else {
        console.error('💥 Backup failed!');
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('💥 Backup failed with error:', error);
      console.error('Stack trace:', error.stack);
      process.exit(1);
    });
}

export { backupUserProfiles };
