/**
 * @file Modern delegation card skeleton aligned with design system
 * @module components/delegations/common/DelegationCardSkeleton
 */

'use client';

import { Skeleton } from '@/components/ui/skeleton';
import { cn } from '@/lib/utils';

interface DelegationCardSkeletonProps {
  className?: string;
}

/**
 * Loading skeleton component that matches the modern DelegationCard structure
 */
export default function DelegationCardSkeleton({
  className,
}: DelegationCardSkeletonProps = {}) {
  return (
    <div
      className={cn(
        'flex h-full flex-col overflow-hidden border-border/60 bg-card shadow-md rounded-lg',
        className
      )}
    >
      {/* Status Indicator Line */}
      <Skeleton className="h-1 w-full bg-muted/50" />

      {/* Header - Consistent p-5 padding */}
      <div className="p-5 pb-3">
        <div className="flex items-start justify-between gap-3">
          <div className="min-w-0 flex-1 space-y-2">
            {/* Title */}
            <Skeleton className="h-6 w-3/4 bg-muted/50" />
            {/* Location */}
            <div className="flex items-center gap-2">
              <Skeleton className="size-4 bg-muted/50" />
              <Skeleton className="h-4 w-1/2 bg-muted/50" />
            </div>
          </div>
          {/* Status Badge */}
          <Skeleton className="h-6 w-16 rounded-full bg-muted/50" />
        </div>
        {/* Active Indicator */}
        <Skeleton className="h-6 w-24 rounded-full bg-muted/50 mt-3" />
      </div>

      {/* Content - Consistent p-5 padding */}
      <div className="flex-1 p-5 pt-0">
        {/* Duration Section */}
        <div className="mb-4">
          <div className="flex items-center gap-2 mb-2">
            <Skeleton className="size-4 bg-muted/50" />
            <Skeleton className="h-4 w-16 bg-muted/50" />
          </div>
          <Skeleton className="h-4 w-full bg-muted/50" />
        </div>

        {/* Separator */}
        <Skeleton className="h-px w-full bg-muted/50 my-4" />

        {/* Key Information */}
        <div className="space-y-3">
          {/* Delegates */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Skeleton className="size-4 bg-muted/50" />
              <Skeleton className="h-4 w-16 bg-muted/50" />
            </div>
            <Skeleton className="h-5 w-8 rounded-full bg-muted/50" />
          </div>

          {/* Assignments Section */}
          <Skeleton className="h-px w-full bg-muted/50 my-3" />
          <div className="space-y-2">
            <Skeleton className="h-3 w-20 bg-muted/50" />
            <div className="space-y-2">
              {Array.from({ length: 2 }).map((_, i) => (
                <div key={i} className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Skeleton className="size-4 bg-muted/50" />
                    <Skeleton className="h-4 w-16 bg-muted/50" />
                  </div>
                  <Skeleton className="h-4 w-20 bg-muted/50" />
                </div>
              ))}
            </div>
          </div>

          {/* Notes Section */}
          <div className="mt-4 p-3 rounded-lg bg-muted/20">
            <div className="flex items-start gap-2">
              <Skeleton className="size-4 mt-0.5 bg-muted/50" />
              <div className="flex-1 space-y-1">
                <Skeleton className="h-3 w-12 bg-muted/50" />
                <Skeleton className="h-4 w-full bg-muted/50" />
                <Skeleton className="h-4 w-3/4 bg-muted/50" />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="border-t bg-muted/20 p-4">
        <Skeleton className="h-10 w-full rounded-md bg-muted/50" />
      </div>
    </div>
  );
}
