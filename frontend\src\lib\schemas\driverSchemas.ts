import * as z from 'zod';

// This schema is reused in EmployeeSchema for employees with 'driver' role.
export const DriverAvailabilitySchema = z.enum([
  'On_Shift',
  'Off_Shift',
  'On_Break',
  'Busy',
]);

// Export type
export type DriverAvailability = z.infer<typeof DriverAvailabilitySchema>;

// DriverSchema itself is removed as its fields are merged into EmployeeSchema.
// export const DriverSchema = z.object({ ... });
// export type DriverFormData = z.infer<typeof DriverSchema>;
