/**
 * @file Health status indicators widget component for real-time system health monitoring.
 * This component provides individual health status indicators with real-time updates
 * and component-level health breakdown for comprehensive system monitoring.
 * @module components/reliability/widgets/system-health/HealthStatusIndicators
 */

'use client';

import {
  Activity,
  AlertTriangle,
  CheckCircle,
  Clock,
  Database,
  Globe,
  Heart,
  Server,
  Shield,
  Wifi,
  XCircle,
} from 'lucide-react';
import React from 'react';

import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Skeleton } from '@/components/ui/skeleton';
import {
  useSystemHealth,
  useDetailedSystemHealth,
} from '@/lib/stores/queries/useReliability';
import type { HealthStatus, ComponentHealth } from '@/lib/types/domain';
import { cn } from '@/lib/utils';

/**
 * Props for the HealthStatusIndicators component
 */
export interface HealthStatusIndicatorsProps {
  /** Optional CSS class name for styling */
  className?: string;
  /** Whether to show detailed component breakdown */
  showDetails?: boolean;
  /** Whether to show response times */
  showResponseTimes?: boolean;
  /** Compact mode for smaller displays */
  compact?: boolean;
}

/**
 * Health status indicator item interface
 */
interface HealthIndicatorItem {
  id: string;
  label: string;
  status: HealthStatus;
  icon: React.ComponentType<{ className?: string }>;
  responseTime?: number;
  details?: string;
}

/**
 * Get status color classes for consistent styling
 */
const getStatusColor = (status: HealthStatus): string => {
  switch (status) {
    case 'healthy':
      return 'text-green-600 bg-green-50 border-green-200';
    case 'degraded':
      return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    case 'unhealthy':
      return 'text-red-600 bg-red-50 border-red-200';
    default:
      return 'text-gray-600 bg-gray-50 border-gray-200';
  }
};

/**
 * Get status icon based on health status
 */
const getStatusIcon = (
  status: HealthStatus
): React.ComponentType<{ className?: string }> => {
  const IconComponent = ({ className }: { className?: string }) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle className={className} />;
      case 'degraded':
        return <AlertTriangle className={className} />;
      case 'unhealthy':
        return <XCircle className={className} />;
      default:
        return <Clock className={className} />;
    }
  };
  return IconComponent;
};

/**
 * Format response time for display
 */
const formatResponseTime = (responseTime?: number): string => {
  if (!responseTime) return 'N/A';
  return responseTime < 1000
    ? `${responseTime}ms`
    : `${(responseTime / 1000).toFixed(2)}s`;
};

/**
 * Health status indicators widget component.
 *
 * This component provides:
 * - Real-time health status indicators for all system components
 * - Individual component health breakdown with detailed status
 * - Color-coded status badges and icons for quick visual assessment
 * - Response time monitoring for performance tracking
 * - Compact mode for space-constrained layouts
 *
 * Features:
 * - Real-time health status updates via WebSocket integration
 * - Component-level health monitoring (database, cache, circuit breakers, etc.)
 * - Visual status indicators with consistent color coding
 * - Response time display for performance monitoring
 * - Responsive design with mobile-first approach
 * - Accessibility support with proper ARIA labels
 * - Loading states and error handling
 *
 * @param props - Component props
 * @returns JSX element representing the health status indicators
 *
 * @example
 * ```tsx
 * <HealthStatusIndicators
 *   showDetails={true}
 *   showResponseTimes={true}
 *   compact={false}
 * />
 * ```
 */
export const HealthStatusIndicators: React.FC<HealthStatusIndicatorsProps> = ({
  className = '',
  showDetails = true,
  showResponseTimes = true,
  compact = false,
}) => {
  const {
    data: systemHealth,
    isLoading: isLoadingBasic,
    error: basicError,
  } = useSystemHealth();
  const {
    data: detailedHealth,
    isLoading: isLoadingDetailed,
    error: detailedError,
  } = useDetailedSystemHealth();

  const isLoading = isLoadingBasic || isLoadingDetailed;
  const error = basicError || detailedError;

  // Build health indicators from detailed health data
  const healthIndicators: HealthIndicatorItem[] = React.useMemo(() => {
    if (!detailedHealth?.checks) return [];

    return [
      {
        id: 'database',
        label: 'Database',
        status: detailedHealth.checks.database.status,
        icon: ({ className }: { className?: string }) => (
          <Database className={className} />
        ),
        responseTime: detailedHealth.checks.database.responseTime || 0,
        details: 'PostgreSQL connection and query performance',
      },
      {
        id: 'supabase',
        label: 'Supabase',
        status: detailedHealth.checks.supabase.status,
        icon: ({ className }: { className?: string }) => (
          <Globe className={className} />
        ),
        responseTime: detailedHealth.checks.supabase.responseTime || 0,
        details: 'Supabase API and real-time connections',
      },
      {
        id: 'cache',
        label: 'Cache',
        status: detailedHealth.checks.cache.status,
        icon: ({ className }: { className?: string }) => (
          <Server className={className} />
        ),
        responseTime: detailedHealth.checks.cache.responseTime || 0,
        details:
          detailedHealth.checks.cache.details?.redis?.status ||
          'Cache system status',
      },
      {
        id: 'circuitBreakers',
        label: 'Circuit Breakers',
        status: detailedHealth.checks.circuitBreakers.status,
        icon: ({ className }: { className?: string }) => (
          <Shield className={className} />
        ),
        responseTime: detailedHealth.checks.circuitBreakers.responseTime || 0,
        details: `${detailedHealth.checks.circuitBreakers.details?.openBreakers || 0} open breakers`,
      },
      {
        id: 'systemResources',
        label: 'System Resources',
        status: detailedHealth.checks.systemResources.status,
        icon: ({ className }: { className?: string }) => (
          <Activity className={className} />
        ),
        responseTime: detailedHealth.checks.systemResources.responseTime || 0,
        details: `${detailedHealth.checks.systemResources.details?.memory?.usagePercent || 0}% memory usage`,
      },
      {
        id: 'businessLogic',
        label: 'Business Logic',
        status: detailedHealth.checks.businessLogic.status,
        icon: ({ className }: { className?: string }) => (
          <Heart className={className} />
        ),
        responseTime: detailedHealth.checks.businessLogic.responseTime || 0,
        details: 'Core application services and workflows',
      },
    ];
  }, [detailedHealth]);

  // Loading state
  if (isLoading) {
    return (
      <div className={cn('space-y-4', className)}>
        <div className="flex items-center justify-between">
          <Skeleton className="h-6 w-32" />
          <Skeleton className="h-5 w-20" />
        </div>
        <div className="grid gap-3 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
          {Array.from({ length: 6 }).map((_, index) => (
            <Skeleton key={index} className="h-16 w-full" />
          ))}
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className={cn('text-center py-8', className)}>
        <XCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
        <p className="text-sm text-red-600 font-medium">
          Failed to load health status
        </p>
        <p className="text-xs text-muted-foreground mt-1">
          {error.message || 'Unable to retrieve system health information'}
        </p>
      </div>
    );
  }

  return (
    <div className={cn('space-y-4', className)}>
      {/* Overall Status Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Heart className="h-5 w-5 text-muted-foreground" />
          <h3 className="font-semibold text-sm">System Health Status</h3>
        </div>
        {systemHealth && (
          <Badge
            variant="outline"
            className={cn('font-medium', getStatusColor(systemHealth.status))}
          >
            {systemHealth.status.toUpperCase()}
          </Badge>
        )}
      </div>

      {/* Health Indicators Grid */}
      <div
        className={cn(
          'grid gap-3',
          compact
            ? 'grid-cols-2 sm:grid-cols-3'
            : 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3'
        )}
      >
        {healthIndicators.map(indicator => {
          const StatusIcon = getStatusIcon(indicator.status);
          const IconComponent = indicator.icon;

          return (
            <Card
              key={indicator.id}
              className={cn(
                'transition-all duration-200 hover:shadow-md',
                getStatusColor(indicator.status),
                compact ? 'p-3' : ''
              )}
            >
              <CardContent className={cn('p-4', compact && 'p-3')}>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <IconComponent
                      className={cn('h-4 w-4', compact ? 'h-3 w-3' : 'h-4 w-4')}
                    />
                    <span
                      className={cn(
                        'font-medium',
                        compact ? 'text-xs' : 'text-sm'
                      )}
                    >
                      {indicator.label}
                    </span>
                  </div>
                  <StatusIcon className={cn(compact ? 'h-3 w-3' : 'h-4 w-4')} />
                </div>

                {showResponseTimes && indicator.responseTime && (
                  <div className="mt-2">
                    <p
                      className={cn(
                        'text-muted-foreground',
                        compact ? 'text-xs' : 'text-xs'
                      )}
                    >
                      Response: {formatResponseTime(indicator.responseTime)}
                    </p>
                  </div>
                )}

                {showDetails && indicator.details && !compact && (
                  <div className="mt-2">
                    <p className="text-xs text-muted-foreground">
                      {indicator.details}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Summary Statistics */}
      {detailedHealth?.summary && !compact && (
        <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 pt-2">
          <div className="text-center">
            <p className="text-lg font-semibold text-green-600">
              {detailedHealth.summary.healthyChecks}
            </p>
            <p className="text-xs text-muted-foreground">Healthy</p>
          </div>
          <div className="text-center">
            <p className="text-lg font-semibold text-yellow-600">
              {detailedHealth.summary.degradedChecks}
            </p>
            <p className="text-xs text-muted-foreground">Degraded</p>
          </div>
          <div className="text-center">
            <p className="text-lg font-semibold text-red-600">
              {detailedHealth.summary.unhealthyChecks}
            </p>
            <p className="text-xs text-muted-foreground">Unhealthy</p>
          </div>
          <div className="text-center">
            <p className="text-lg font-semibold text-blue-600">
              {detailedHealth.summary.totalChecks}
            </p>
            <p className="text-xs text-muted-foreground">Total</p>
          </div>
        </div>
      )}
    </div>
  );
};

/**
 * Default export for the HealthStatusIndicators component
 */
export default HealthStatusIndicators;
