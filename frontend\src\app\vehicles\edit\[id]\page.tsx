'use client';

import { Car, Edit } from 'lucide-react';
import { useParams, useRouter } from 'next/navigation';
import React from 'react'; // Removed useEffect, useState, useCallback

import type { CreateVehicleData, Vehicle } from '@/lib/types/domain';

import { Button } from '@/components/ui/button';
import { PageHeader } from '@/components/ui/PageHeader';
import { Skeleton } from '@/components/ui/skeleton';
import { VehicleForm } from '@/components/features/vehicles/forms/vehicleForm';
import { usePredefinedEntityToast } from '@/hooks/forms/useFormToast';
// import {getVehicleById, updateVehicle} from '@/lib/store'; // Removed
import { useUpdateVehicle, useVehicle } from '@/lib/stores/queries/useVehicles'; // Added

const EditVehiclePage = () => {
  const router = useRouter();
  const params = useParams();
  const { showEntityUpdated, showEntityUpdateError } =
    usePredefinedEntityToast('vehicle');

  const vehicleIdParam = params?.id as string;
  const vehicleIdNumber = vehicleIdParam ? Number(vehicleIdParam) : null; // Renamed and kept as number for logic

  const {
    data: vehicle,
    error: vehicleError,
    isLoading: isVehicleLoading,
  } = useVehicle(vehicleIdNumber); // Pass number ID directly

  const {
    error: updateError,
    isPending: isUpdating,
    mutateAsync: updateVehicleMutation,
  } = useUpdateVehicle();

  const handleSubmit = async (data: CreateVehicleData) => {
    if (!vehicleIdNumber) {
      // Use vehicleIdNumber for checks
      showEntityUpdateError('Vehicle ID is missing. Cannot update.');
      return;
    }

    try {
      // Ensure initialOdometer is a number.
      // The data passed to mutateAsync should match what useUpdateVehicle expects for its 'data' field.
      // Typically, this would be Partial<Vehicle> or a specific UpdateVehicleData type.
      // VehicleFormData should be largely compatible.
      const dataToUpdate = {
        ...data,
        initialOdometer:
          data.initialOdometer === undefined
            ? vehicle?.initialOdometer || 0 // Fallback to existing or 0
            : data.initialOdometer,
      };

      await updateVehicleMutation({
        data: dataToUpdate,
        id: vehicleIdNumber, // Pass number ID
      });
      const vehicleForToast = {
        make: data.make,
        model: data.model,
      };
      showEntityUpdated(vehicleForToast);
      router.push('/vehicles'); // Navigate to the list page
    } catch (error: any) {
      console.error('Failed to update vehicle:', error);
      showEntityUpdateError(
        error.message ||
          updateError?.message ||
          'Could not update the vehicle. Please check the details and try again.'
      );
    }
  };

  if (!vehicleIdNumber) {
    // Use vehicleIdNumber for checks
    // Handle case where vehicleId is not present or invalid early
    return (
      <div className="container mx-auto space-y-8 py-8 text-center">
        <PageHeader
          description="Invalid Vehicle ID."
          icon={Car}
          title="Error"
        />
        <Button onClick={() => router.push('/vehicles')}>
          Back to Vehicles
        </Button>
      </div>
    );
  }

  if (isVehicleLoading) {
    return (
      <div className="container mx-auto space-y-8 py-8">
        <PageHeader
          description="Loading vehicle details..."
          icon={Edit}
          title="Edit Vehicle"
        />
        <div className="mx-auto max-w-2xl space-y-6">
          <Skeleton className="h-10 w-1/3" />
          <Skeleton className="h-12 w-full" />
          <Skeleton className="h-12 w-full" />
          <Skeleton className="h-12 w-full" />
          <div className="flex justify-end space-x-3 pt-6">
            <Skeleton className="h-10 w-24" />
            <Skeleton className="h-10 w-24" />
          </div>
        </div>
      </div>
    );
  }

  if (vehicleError) {
    return (
      <div className="container mx-auto space-y-8 py-8 text-center">
        <PageHeader
          description={vehicleError.message || 'Could not load vehicle data.'}
          icon={Car}
          title="Error Loading Vehicle"
        />
        <Button onClick={() => router.push('/vehicles')}>
          Back to Vehicles
        </Button>
      </div>
    );
  }

  if (!vehicle) {
    // Handle case where vehicle is not found after loading (e.g., 404 from API)
    return (
      <div className="container mx-auto space-y-8 py-8 text-center">
        <PageHeader
          description="The requested vehicle could not be found."
          icon={Car}
          title="Vehicle Not Found"
        />
        <Button onClick={() => router.push('/vehicles')}>
          Back to Vehicles
        </Button>
      </div>
    );
  }

  return (
    <div className="container mx-auto space-y-8 py-8">
      <PageHeader
        description={`Update details for ${vehicle?.make || 'vehicle'} ${
          vehicle?.model || ''
        }`}
        icon={Edit}
        title="Edit Vehicle"
      />
      {updateError && (
        <p className="rounded-md bg-red-100 p-3 text-red-500">
          Error updating: {updateError.message}
        </p>
      )}
      {vehicle && ( // Ensure vehicle data is available before rendering form
        <VehicleForm
          initialData={
            vehicle
              ? { ...vehicle, id: String(vehicle.id) }
              : (undefined as any)
          } // Convert vehicle.id to string if VehicleForm expects string ID
          isEditing={true}
          isLoading={isUpdating} // Use isPending from the update mutation
          onSubmit={handleSubmit}
        />
      )}
    </div>
  );
};

export default EditVehiclePage;
