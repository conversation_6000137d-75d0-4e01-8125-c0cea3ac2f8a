import { z } from 'zod';

const DelegationStatusEnum = z.enum([
  'Planned',
  'Confirmed',
  'In_Progress',
  'Completed',
  'Cancelled',
  'No_details',
]);
export type DelegationStatusEnumType = z.infer<typeof DelegationStatusEnum>;

const DelegateSchema = z.object({
  id: z.string().uuid().optional(),
  name: z.string().min(1, 'Delegate name is required'),
  notes: z.string().optional().nullable(),
  title: z.string().min(1, 'Delegate title is required'),
});

const FlightDetailsSchema = z.preprocess(
  // First preprocess to handle the entire object being null/undefined
  val => {
    // If the value is null or undefined, return null
    if (val === null || val === undefined) {
      return null;
    }

    // If it's an object, check if all required fields are empty
    if (typeof val === 'object') {
      const obj = val as any;
      const isEmpty =
        (!obj.flightNumber || obj.flightNumber.trim() === '') &&
        (!obj.dateTime || obj.dateTime.trim() === '') &&
        (!obj.airport || obj.airport.trim() === '');

      if (isEmpty) {
        return null;
      }
    }

    // Otherwise return the value as is
    return val;
  },
  z
    .object({
      airport: z.string().min(1, 'Airport is required'),
      dateTime: z
        .string()
        .refine(
          val => {
            try {
              // Check if it's a valid date
              const date = new Date(val);
              return !isNaN(date.getTime());
            } catch (e) {
              return false;
            }
          },
          {
            message: 'Invalid date/time format for flight',
          },
        )
        .transform(val => {
          // Ensure consistent ISO format
          try {
            return new Date(val).toISOString();
          } catch (e) {
            return val; // If transformation fails, return original value for validation to fail
          }
        }),
      flightNumber: z.string().min(1, 'Flight number is required'),
      id: z.string().uuid().optional(),
      notes: z.string().optional().nullable(),
      terminal: z.string().optional().nullable(),
    })
    .nullable()
    .optional(),
);

/**
 * @openapi
 * components:
 *   schemas:
 *     DelegateInput:
 *       type: object
 *       required:
 *         - name
 *         - title
 *       properties:
 *         name:
 *           type: string
 *         title:
 *           type: string
 *         notes:
 *           type: string
 *           nullable: true
 *     FlightDetailsInput:
 *       type: object
 *       required:
 *         - flightNumber
 *         - dateTime
 *         - airport
 *       properties:
 *         flightNumber:
 *           type: string
 *         dateTime:
 *           type: string
 *           format: date-time
 *         airport:
 *           type: string
 *         terminal:
 *           type: string
 *           nullable: true
 *         notes:
 *           type: string
 *           nullable: true
 *     DelegationCreateInput:
 *       type: object
 *       required:
 *         - eventName
 *         - location
 *         - durationFrom
 *         - durationTo
 *       properties:
 *         eventName:
 *           type: string
 *         location:
 *           type: string
 *         durationFrom:
 *           type: string
 *           format: date-time
 *         durationTo:
 *           type: string
 *           format: date-time
 *         invitationFrom:
 *           type: string
 *           nullable: true
 *         invitationTo:
 *           type: string
 *           nullable: true
 *         notes:
 *           type: string
 *           nullable: true
 *         imageUrl:
 *           type: string
 *           format: url
 *           nullable: true
 *         status:
 *           $ref: '#/components/schemas/DelegationStatusEnum'
 *         delegates:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/DelegateInput'
 *         flightArrivalDetails:
 *           $ref: '#/components/schemas/FlightDetailsInput'
 *           nullable: true
 *         flightDepartureDetails:
 *           $ref: '#/components/schemas/FlightDetailsInput'
 *           nullable: true
 *         escortEmployeeIds:
 *           type: array
 *           items:
 *             type: integer
 *             format: int32
 *           description: Array of Employee IDs for escorts.
 *         driverEmployeeIds:
 *           type: array
 *           items:
 *             type: integer
 *             format: int32
 *           description: Array of Employee IDs for drivers.
 *         vehicleIds:
 *           type: array
 *           items:
 *             type: integer
 *             format: int32
 *           description: Array of Vehicle IDs.
 *     DelegationUpdateInput:
 *       type: object
 *       properties:
 *         eventName:
 *           type: string
 *         location:
 *           type: string
 *         durationFrom:
 *           type: string
 *           format: date-time
 *         durationTo:
 *           type: string
 *           format: date-time
 *         invitationFrom:
 *           type: string
 *           nullable: true
 *         invitationTo:
 *           type: string
 *           nullable: true
 *         notes:
 *           type: string
 *           nullable: true
 *         imageUrl:
 *           type: string
 *           format: url
 *           nullable: true
 *         status:
 *           $ref: '#/components/schemas/DelegationStatusEnum'
 *         statusChangeReason:
 *           type: string
 *           description: Reason for status change if status is updated.
 *           nullable: true
 *         delegates:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/DelegateInput'
 *           description: To update delegates, provide the full new list. Existing delegates not in the list will be removed.
 *         flightArrivalDetails:
 *           $ref: '#/components/schemas/FlightDetailsInput'
 *           nullable: true
 *           description: To remove, pass null. To update/create, pass the object.
 *         flightDepartureDetails:
 *           $ref: '#/components/schemas/FlightDetailsInput'
 *           nullable: true
 *           description: To remove, pass null. To update/create, pass the object.
 *         escortEmployeeIds:
 *           type: array
 *           items:
 *             type: integer
 *             format: int32
 *           description: Array of Employee IDs for escorts. To remove all, pass an empty array.
 *         driverEmployeeIds:
 *           type: array
 *           items:
 *             type: integer
 *             format: int32
 *           description: Array of Employee IDs for drivers. To remove all, pass an empty array.
 *         vehicleIds:
 *           type: array
 *           items:
 *             type: integer
 *             format: int32
 *           description: Array of Vehicle IDs. To remove all, pass an empty array.
 *     DelegationStatusEnum:
 *       type: string
 *       enum: [Planned, Confirmed, In_Progress, Completed, Cancelled, No_details]
 *     Delegation:
 *       allOf:
 *         - $ref: '#/components/schemas/DelegationCreateInput'
 *         - type: object
 *           properties:
 *             id:
 *               type: string
 *               format: uuid
 *             createdAt:
 *               type: string
 *               format: date-time
 *             updatedAt:
 *               type: string
 *               format: date-time
 *             statusHistory:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/DelegationStatusEntry'
 *     DelegationStatusEntry:
 *       type: object
 *       properties:
 *         id: { type: string, format: uuid }
 *         status: { $ref: '#/components/schemas/DelegationStatusEnum' }
 *         changedAt: { type: string, format: date-time }
 *         reason: { type: string, nullable: true }
 */

const delegationBaseObjectSchema = z.object({
  delegates: z
    .array(DelegateSchema.omit({ id: true }))
    .optional()
    .default([]),
  driverEmployeeIds: z
    .array(z.number().int().positive('Driver Employee ID must be a positive integer.'))
    .optional()
    .default([]),
  durationFrom: z.string().datetime({ message: 'Invalid start date format' }),
  durationTo: z.string().datetime({ message: 'Invalid end date format' }),
  // NEW PHASE 1 ASSIGNMENT FIELDS
  escortEmployeeIds: z
    .array(z.number().int().positive('Escort Employee ID must be a positive integer.'))
    .optional()
    .default([]),
  eventName: z.string().min(1, 'Event name is required'),
  flightArrivalDetails: FlightDetailsSchema.nullable().optional(),
  flightDepartureDetails: FlightDetailsSchema.nullable().optional(),
  imageUrl: z // Removed .url() validation to allow broader URL formats
    .string()
    .optional()
    .nullable()
    .or(z.literal('')),
  invitationFrom: z.string().optional().nullable(),
  invitationTo: z.string().optional().nullable(),
  location: z.string().min(1, 'Location is required'),
  notes: z.string().optional().nullable(),
  status: DelegationStatusEnum.default('Planned'),
  vehicleIds: z
    .array(z.number().int().positive('Vehicle ID must be a positive integer.'))
    .optional()
    .default([]),
});

export const delegationCreateSchema = delegationBaseObjectSchema
  .refine(data => new Date(data.durationFrom) <= new Date(data.durationTo), {
    message: 'End date cannot be earlier than start date',
    path: ['durationTo'],
  })
  .refine(
    data => {
      // Business rule: vehicleIds only allowed if at least one driverEmployeeId is present
      if (
        data.vehicleIds &&
        data.vehicleIds.length > 0 &&
        (!data.driverEmployeeIds || data.driverEmployeeIds.length === 0)
      ) {
        return false;
      }
      return true;
    },
    {
      message: 'Vehicles can only be assigned when at least one driver is assigned',
      path: ['vehicleIds'],
    },
  );

export const delegationUpdateSchema = delegationBaseObjectSchema
  .partial()
  .extend({
    statusChangeReason: z.string().optional().nullable(),
  })
  .refine(
    data => {
      if (data.durationFrom && data.durationTo) {
        return new Date(data.durationFrom) <= new Date(data.durationTo);
      }
      return true;
    },
    {
      message: 'End date cannot be earlier than start date if both are provided',
      path: ['durationTo'],
    },
  )
  .refine(
    data => {
      // Business rule: vehicleIds only allowed if at least one driverEmployeeId is present
      if (
        data.vehicleIds &&
        data.vehicleIds.length > 0 &&
        (!data.driverEmployeeIds || data.driverEmployeeIds.length === 0)
      ) {
        return false;
      }
      return true;
    },
    {
      message: 'Vehicles can only be assigned when at least one driver is assigned',
      path: ['vehicleIds'],
    },
  );

export const delegationIdSchema = z.object({
  id: z.string().uuid('Invalid Delegation ID format (must be UUID)'),
});

export type DelegationCreate = z.infer<typeof delegationCreateSchema>;
export type DelegationUpdate = z.infer<typeof delegationUpdateSchema>;
