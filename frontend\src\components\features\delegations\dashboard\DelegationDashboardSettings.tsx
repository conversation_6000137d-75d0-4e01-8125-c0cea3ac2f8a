/**
 * @file Delegation-specific dashboard settings component
 * @module components/delegations/dashboard/DelegationDashboardSettings
 */

'use client';

import React from 'react';

import { DashboardSettings } from '@/components/dashboard/DashboardSettings';
import type { DashboardConfig } from '@/components/dashboard/types';
import type { Delegation } from '@/lib/types/domain';
import { useDashboardStore } from '@/hooks/domain/useDashboardStore';

/**
 * Delegation dashboard configuration
 */
const delegationDashboardConfig: DashboardConfig<Delegation> = {
  entityType: 'delegation',
  title: 'Delegation Dashboard',
  description: 'Track and manage all your events, trips, and delegate information.',
  viewModes: ['cards', 'table', 'list', 'calendar'],
  defaultViewMode: 'cards',
  enableBulkActions: true,
  enableExport: true,
  refreshInterval: 30000,
};

/**
 * Props for DelegationDashboardSettings component
 */
interface DelegationDashboardSettingsProps {
  className?: string;
}

/**
 * Delegation-specific dashboard settings component that uses the generic
 * DashboardSettings component with delegation-specific configuration.
 * 
 * This component:
 * - Uses the generic dashboard store for delegations
 * - Provides delegation-specific configuration
 * - Maintains all delegation-specific state and actions
 * 
 * @param props - Component props
 * @returns JSX element representing the delegation dashboard settings
 */
export const DelegationDashboardSettings: React.FC<DelegationDashboardSettingsProps> = ({
  className = '',
}) => {
  // Get delegation dashboard store
  const dashboardStore = useDashboardStore('delegation');
  const {
    layout,
    monitoring,
    setViewMode,
    setGridColumns,
    toggleCompactMode,
    setMonitoringEnabled,
    setRefreshInterval,
    toggleAutoRefresh,
    resetSettings,
  } = dashboardStore();

  return (
    <DashboardSettings
      config={delegationDashboardConfig}
      entityType="delegation"
      layout={layout}
      monitoring={monitoring}
      setViewMode={setViewMode}
      setGridColumns={setGridColumns}
      toggleCompactMode={toggleCompactMode}
      setMonitoringEnabled={setMonitoringEnabled}
      setRefreshInterval={setRefreshInterval}
      toggleAutoRefresh={toggleAutoRefresh}
      resetSettings={resetSettings}
      className={className}
    />
  );
};

export default DelegationDashboardSettings;
