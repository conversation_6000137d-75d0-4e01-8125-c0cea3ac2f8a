'use client';

import { Briefcase, PlusCircle, Search, Settings } from 'lucide-react';
import Link from 'next/link';
import React, { useState } from 'react';

import ErrorBoundary from '@/components/error-boundaries/ErrorBoundary';
import { DelegationDashboardSettings } from '@/components/features/delegations/dashboard';
import DelegationFilters, {
  type DelegationFilterValues,
} from '@/components/features/delegations/DelegationFilters';
import {
  DelegationListContainer,
  DelegationViewRenderer,
} from '@/components/features/delegations/list';
import { ViewReportButton } from '@/components/reports/ViewReportButton';
import { ActionButton } from '@/components/ui/action-button';
import { AppBreadcrumb } from '@/components/ui/app-breadcrumb';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { DataLoader, SkeletonLoader } from '@/components/ui/loading';
import { PageHeader } from '@/components/ui/PageHeader';
import { useDashboardStore } from '@/hooks/domain/useDashboardStore';
import { useDelegations } from '@/lib/stores/queries/useDelegations';
import { useEmployees } from '@/lib/stores/queries/useEmployees';
import { useVehicles } from '@/lib/stores/queries/useVehicles';

export default function DelegationsPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState<DelegationFilterValues>({
    dateRange: {},
    drivers: [],
    escorts: [],
    location: [],
    search: '',
    status: [],
    vehicles: [],
  });

  const getDelegationsReportUrl = () => {
    const queryParams = new URLSearchParams({
      searchTerm: filters.search || searchTerm,
    }).toString();
    return `/delegations/report/list?${queryParams}`;
  };

  // Handle filter changes
  const handleFiltersChange = (newFilters: DelegationFilterValues) => {
    setFilters(newFilters);
    setSearchTerm(newFilters.search); // Keep legacy search term in sync
  };

  // Use the generic dashboard store for delegations
  const dashboardStore = useDashboardStore('delegation');
  const { layout } = dashboardStore();

  // Fetch data for filter options
  const { data: allDelegations = [] } = useDelegations();
  const { data: employees = [] } = useEmployees();
  const { data: vehicles = [] } = useVehicles();

  // Extract unique locations from delegations
  const locationsList = [
    ...new Set(
      allDelegations.map(delegation => delegation.location).filter(Boolean)
    ),
  ];

  // Transform employees for filter component
  const employeesList = employees.map(employee => ({
    id: employee.id.toString(),
    name: employee.name,
    role: employee.role,
  }));

  // Transform vehicles for filter component
  const vehiclesList = vehicles.map(vehicle => ({
    id: vehicle.id.toString(),
    name: `${vehicle.make} ${vehicle.model}`,
    type: `${vehicle.year} ${vehicle.licensePlate}`, // Use year and make as type
  }));

  return (
    <ErrorBoundary>
      <div className="space-y-6">
        <AppBreadcrumb homeHref="/" homeLabel="Dashboard" />
        <PageHeader
          description="Track and manage all your events, trips, and delegate information."
          icon={Briefcase}
          title="Manage Delegations"
        >
          <div className="flex items-center gap-2">
            <ActionButton
              actionType="primary"
              asChild
              icon={<PlusCircle className="mr-2 size-4" />}
            >
              <Link href="/delegations/add">Add New Delegation</Link>
            </ActionButton>
            <ViewReportButton
              getReportUrl={getDelegationsReportUrl}
              isList={true}
            />
            {/* New Settings Button */}
            <Dialog>
              <DialogTrigger asChild>
                <ActionButton
                  actionType="secondary"
                  icon={<Settings className="size-4" />}
                >
                  Settings
                </ActionButton>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[600px]">
                <DialogTitle>Dashboard Settings</DialogTitle>
                <DialogDescription>
                  Customize the display and behavior of your delegation
                  dashboard.
                </DialogDescription>
                <DelegationDashboardSettings />
              </DialogContent>
            </Dialog>
          </div>
        </PageHeader>

        <div className="mb-6 rounded-lg bg-card p-4 shadow-md">
          <DelegationFilters
            employeesList={employeesList}
            initialFilters={{
              dateRange: {},
              drivers: [],
              escorts: [],
              location: [],
              search: searchTerm,
              status: [],
              vehicles: [],
            }}
            locationsList={locationsList}
            onFiltersChange={handleFiltersChange}
            vehiclesList={vehiclesList}
          />
        </div>

        <DelegationListContainer filters={filters} searchTerm={searchTerm}>
          {({ delegations, error, fetchDelegations, loading }) => (
            <DataLoader
              data={delegations}
              emptyComponent={
                <div className="rounded-lg bg-card py-12 text-center shadow-md">
                  <Briefcase className="mx-auto mb-6 size-16 text-muted-foreground" />
                  <h3 className="mb-2 text-2xl font-semibold text-foreground">
                    {searchTerm
                      ? 'No Delegations Match Your Search'
                      : 'No Delegations Yet!'}
                  </h3>
                  <p className="mx-auto mb-6 mt-2 max-w-md text-muted-foreground">
                    {searchTerm
                      ? 'Try adjusting your search terms or add a new delegation.'
                      : "It looks like you haven't added any delegations yet. Get started by adding one."}
                  </p>
                  {!searchTerm && (
                    <ActionButton
                      actionType="primary"
                      asChild
                      icon={<PlusCircle className="size-4" />}
                      size="lg"
                    >
                      <Link href="/delegations/add">
                        Add Your First Delegation
                      </Link>
                    </ActionButton>
                  )}
                </div>
              }
              error={error}
              isLoading={loading}
              loadingComponent={<SkeletonLoader count={3} variant="card" />}
              onRetry={fetchDelegations}
            >
              {delegationsData => (
                <DelegationViewRenderer
                  compactMode={layout.compactMode}
                  delegations={delegationsData}
                  gridColumns={layout.gridColumns}
                  viewMode={layout.viewMode}
                />
              )}
            </DataLoader>
          )}
        </DelegationListContainer>
      </div>
    </ErrorBoundary>
  );
}
