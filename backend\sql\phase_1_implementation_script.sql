-- ============================================================================
-- WorkHub Phase 1: Critical RLS Policy Implementation
-- Document Version: 2.0 | Date: January 2025
-- Supabase Project: abylqjnpaegeqwktcukn
-- ============================================================================
-- 
-- IMPORTANT: This script assumes private schema security functions are already deployed:
-- - private.is_admin_or_above()
-- - private.get_user_employee_id(auth.uid())
--
-- Execute this script in the following order (all commands included below):
-- 1. Enable RLS on unprotected tables
-- 2. Create RLS policies for all tables
-- 3. Remove duplicate public schema functions
-- 4. Legacy cleanup and optimization
-- ============================================================================

-- ============================================================================
-- Sub-task 1.1: Enable RLS on Unprotected Tables
-- ============================================================================
-- Enable Row Level Security on 8 tables that are currently completely open

ALTER TABLE public."Task" ENABLE ROW LEVEL SECURITY;
ALTER TABLE public."ServiceRecord" ENABLE ROW LEVEL SECURITY;
ALTER TABLE public."SubTask" ENABLE ROW LEVEL SECURITY;
ALTER TABLE public."FlightDetails" ENABLE ROW LEVEL SECURITY;
ALTER TABLE public."TaskStatusEntry" ENABLE ROW LEVEL SECURITY;
ALTER TABLE public."EmployeeStatusEntry" ENABLE ROW LEVEL SECURITY;
ALTER TABLE public."Delegate" ENABLE ROW LEVEL SECURITY;
ALTER TABLE public."DelegationStatusEntry" ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- Sub-task 1.2: Create Missing Policies for Employee Table
-- ============================================================================
-- Create comprehensive RLS policies for Employee table

-- Admin full access policy
CREATE POLICY "employee_admin_full_access" ON public."Employee"
  FOR ALL TO authenticated
  USING (private.is_admin_or_above());

-- Users can view their own employee record
CREATE POLICY "employee_user_view_own" ON public."Employee"
  FOR SELECT TO authenticated
  USING (
    id = private.get_user_employee_id(auth.uid())
    OR private.is_admin_or_above()
  );

-- Users can view colleagues they work with on delegations
CREATE POLICY "employee_user_view_colleagues" ON public."Employee"
  FOR SELECT TO authenticated
  USING (
    id IN (
      SELECT DISTINCT de."employeeId" FROM "DelegationEscort" de
      JOIN "DelegationEscort" de2 ON de."delegationId" = de2."delegationId"
      WHERE de2."employeeId" = private.get_user_employee_id(auth.uid())
      UNION
      SELECT DISTINCT dd."employeeId" FROM "DelegationDriver" dd
      JOIN "DelegationEscort" de ON dd."delegationId" = de."delegationId"
      WHERE de."employeeId" = private.get_user_employee_id(auth.uid())
      UNION
      SELECT DISTINCT de."employeeId" FROM "DelegationEscort" de
      JOIN "DelegationDriver" dd ON de."delegationId" = dd."delegationId"
      WHERE dd."employeeId" = private.get_user_employee_id(auth.uid())
    )
    OR private.is_admin_or_above()
  );

-- ============================================================================
-- Sub-task 1.3: Create Missing Policies for Vehicle Table
-- ============================================================================
-- Create comprehensive RLS policies for Vehicle table

-- Admin full access policy
CREATE POLICY "vehicle_admin_full_access" ON public."Vehicle"
  FOR ALL TO authenticated
  USING (private.is_admin_or_above());

-- Users can view vehicles assigned to their delegations
CREATE POLICY "vehicle_user_view_assigned" ON public."Vehicle"
  FOR SELECT TO authenticated
  USING (
    id IN (
      SELECT dv."vehicleId" FROM "DelegationVehicle" dv
      JOIN "DelegationEscort" de ON dv."delegationId" = de."delegationId"
      WHERE de."employeeId" = private.get_user_employee_id(auth.uid())
      UNION
      SELECT dv."vehicleId" FROM "DelegationVehicle" dv
      JOIN "DelegationDriver" dd ON dv."delegationId" = dd."delegationId"
      WHERE dd."employeeId" = private.get_user_employee_id(auth.uid())
    )
    OR private.is_admin_or_above()
  );

-- ============================================================================
-- Sub-task 1.4: Create Comprehensive Task Table Policies
-- ============================================================================
-- Create role-based RLS policies for Task table

-- Admin full access policy
CREATE POLICY "task_admin_full_access" ON public."Task"
  FOR ALL TO authenticated
  USING (private.is_admin_or_above());

-- Users can view tasks they're assigned to
CREATE POLICY "task_user_view_assigned" ON public."Task"
  FOR SELECT TO authenticated
  USING (
    "staffEmployeeId" = private.get_user_employee_id(auth.uid())
    OR "driverEmployeeId" = private.get_user_employee_id(auth.uid())
    OR private.is_admin_or_above()
  );

-- Users can update tasks they're assigned to
CREATE POLICY "task_user_update_assigned" ON public."Task"
  FOR UPDATE TO authenticated
  USING (
    "staffEmployeeId" = private.get_user_employee_id(auth.uid())
    OR "driverEmployeeId" = private.get_user_employee_id(auth.uid())
    OR private.is_admin_or_above()
  );

-- Only admins can insert new tasks
CREATE POLICY "task_admin_insert" ON public."Task"
  FOR INSERT TO authenticated
  WITH CHECK (private.is_admin_or_above());

-- Only admins can delete tasks
CREATE POLICY "task_admin_delete" ON public."Task"
  FOR DELETE TO authenticated
  USING (private.is_admin_or_above());

-- ============================================================================
-- Sub-task 1.5: Implement Remaining Table Policies
-- ============================================================================
-- Create RLS policies for ServiceRecord, SubTask, FlightDetails, and status entry tables

-- ServiceRecord policies
CREATE POLICY "service_record_admin_full_access" ON public."ServiceRecord"
  FOR ALL TO authenticated USING (private.is_admin_or_above());

CREATE POLICY "service_record_user_view_assigned_vehicles" ON public."ServiceRecord"
  FOR SELECT TO authenticated USING (
    "vehicleId" IN (
      SELECT dv."vehicleId" FROM "DelegationVehicle" dv
      JOIN "DelegationEscort" de ON dv."delegationId" = de."delegationId"
      WHERE de."employeeId" = private.get_user_employee_id(auth.uid())
      UNION
      SELECT dv."vehicleId" FROM "DelegationVehicle" dv
      JOIN "DelegationDriver" dd ON dv."delegationId" = dd."delegationId"
      WHERE dd."employeeId" = private.get_user_employee_id(auth.uid())
    )
    OR private.is_admin_or_above()
  );

-- SubTask policies
CREATE POLICY "subtask_admin_full_access" ON public."SubTask"
  FOR ALL TO authenticated USING (private.is_admin_or_above());

CREATE POLICY "subtask_user_view_assigned" ON public."SubTask"
  FOR SELECT TO authenticated USING (
    "taskId" IN (
      SELECT id FROM "Task"
      WHERE "staffEmployeeId" = private.get_user_employee_id(auth.uid())
         OR "driverEmployeeId" = private.get_user_employee_id(auth.uid())
    )
    OR private.is_admin_or_above()
  );

-- FlightDetails policies
CREATE POLICY "flight_details_admin_full_access" ON public."FlightDetails"
  FOR ALL TO authenticated USING (private.is_admin_or_above());

CREATE POLICY "flight_details_user_view_assigned" ON public."FlightDetails"
  FOR SELECT TO authenticated USING (
    "delegationId" IN (
      SELECT de."delegationId" FROM "DelegationEscort" de
      WHERE de."employeeId" = private.get_user_employee_id(auth.uid())
      UNION
      SELECT dd."delegationId" FROM "DelegationDriver" dd
      WHERE dd."employeeId" = private.get_user_employee_id(auth.uid())
    )
    OR private.is_admin_or_above()
  );

-- TaskStatusEntry policies
CREATE POLICY "task_status_entry_admin_full_access" ON public."TaskStatusEntry"
  FOR ALL TO authenticated USING (private.is_admin_or_above());

CREATE POLICY "task_status_entry_user_view_assigned" ON public."TaskStatusEntry"
  FOR SELECT TO authenticated USING (
    "taskId" IN (
      SELECT id FROM "Task"
      WHERE "staffEmployeeId" = private.get_user_employee_id(auth.uid())
         OR "driverEmployeeId" = private.get_user_employee_id(auth.uid())
    )
    OR private.is_admin_or_above()
  );

-- EmployeeStatusEntry policies
CREATE POLICY "employee_status_entry_admin_full_access" ON public."EmployeeStatusEntry"
  FOR ALL TO authenticated USING (private.is_admin_or_above());

CREATE POLICY "employee_status_entry_user_view_own" ON public."EmployeeStatusEntry"
  FOR SELECT TO authenticated USING (
    "employeeId" = private.get_user_employee_id(auth.uid())
    OR private.is_admin_or_above()
  );

-- Delegate policies
CREATE POLICY "delegate_admin_full_access" ON public."Delegate"
  FOR ALL TO authenticated USING (private.is_admin_or_above());

CREATE POLICY "delegate_user_view_assigned" ON public."Delegate"
  FOR SELECT TO authenticated USING (
    id IN (
      SELECT de."delegationId" FROM "DelegationEscort" de
      WHERE de."employeeId" = private.get_user_employee_id(auth.uid())
      UNION
      SELECT dd."delegationId" FROM "DelegationDriver" dd
      WHERE dd."employeeId" = private.get_user_employee_id(auth.uid())
    )
    OR private.is_admin_or_above()
  );

-- DelegationStatusEntry policies
CREATE POLICY "delegation_status_entry_admin_full_access" ON public."DelegationStatusEntry"
  FOR ALL TO authenticated USING (private.is_admin_or_above());

CREATE POLICY "delegation_status_entry_user_view_assigned" ON public."DelegationStatusEntry"
  FOR SELECT TO authenticated USING (
    "delegationId" IN (
      SELECT de."delegationId" FROM "DelegationEscort" de
      WHERE de."employeeId" = private.get_user_employee_id(auth.uid())
      UNION
      SELECT dd."delegationId" FROM "DelegationDriver" dd
      WHERE dd."employeeId" = private.get_user_employee_id(auth.uid())
    )
    OR private.is_admin_or_above()
  );

-- ============================================================================
-- Sub-task 1.6: Remove Duplicate Public Schema Functions
-- ============================================================================
-- Remove security functions from public schema to prevent API exposure

-- Remove duplicate security functions from public schema
DROP FUNCTION IF EXISTS public.get_user_employee_id(UUID);
DROP FUNCTION IF EXISTS public.get_user_role(UUID);
DROP FUNCTION IF EXISTS public.is_admin(UUID);
DROP FUNCTION IF EXISTS public.is_admin_user();

-- ============================================================================
-- Sub-task 1.7: Legacy Security Code Cleanup & Database Optimization
-- ============================================================================
-- Remove legacy security implementations and optimize database structure

-- Remove legacy security views if they exist
DROP VIEW IF EXISTS public.user_permissions_view;
DROP VIEW IF EXISTS public.legacy_user_access_view;
DROP VIEW IF EXISTS public.deprecated_security_view;

-- Remove legacy security tables that may have been used for manual access control
DROP TABLE IF EXISTS public.legacy_user_permissions;
DROP TABLE IF EXISTS public.deprecated_access_control;
DROP TABLE IF EXISTS public.old_security_settings;

-- Remove legacy triggers that may conflict with RLS
DROP TRIGGER IF EXISTS legacy_security_trigger ON public."Task";
DROP TRIGGER IF EXISTS old_access_control_trigger ON public."Employee";
DROP TRIGGER IF EXISTS deprecated_auth_trigger ON public."Vehicle";

-- Remove legacy functions that may bypass RLS
DROP FUNCTION IF EXISTS public.legacy_check_access(UUID, TEXT);
DROP FUNCTION IF EXISTS public.old_security_check(UUID);
DROP FUNCTION IF EXISTS public.deprecated_auth_function(UUID, TEXT);

-- Clean up legacy indexes that are no longer needed
DROP INDEX IF EXISTS idx_legacy_security_user_id;
DROP INDEX IF EXISTS idx_old_access_control;
DROP INDEX IF EXISTS idx_deprecated_permissions;

-- Remove legacy comments and update with current security model
COMMENT ON TABLE public."Task" IS 'Task management with RLS security - Updated Jan 2025';
COMMENT ON TABLE public."Employee" IS 'Employee data with role-based access - Updated Jan 2025';
COMMENT ON TABLE public."Vehicle" IS 'Vehicle information with delegation-based access - Updated Jan 2025';

-- Vacuum and analyze tables after cleanup for optimal performance
VACUUM ANALYZE public."Task";
VACUUM ANALYZE public."Employee";
VACUUM ANALYZE public."Vehicle";
VACUUM ANALYZE public."ServiceRecord";
VACUUM ANALYZE public."SubTask";
VACUUM ANALYZE public."FlightDetails";

-- ============================================================================
-- SCRIPT COMPLETION
-- ============================================================================
-- Phase 1 implementation script completed successfully.
--
-- Next steps:
-- 1. Execute the Phase 1 Completion Validation query (see implementation report)
-- 2. Verify all tables show "✅ PROTECTED" status
-- 3. Test application functionality with different user roles
-- 4. Monitor performance impact and optimize if needed
-- ============================================================================
