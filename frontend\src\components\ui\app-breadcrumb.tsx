'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import React from 'react';

import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { cn } from '@/lib/utils';

interface AppBreadcrumbProps {
  className?: string;
  homeHref?: string;
  homeLabel?: string;
  showContainer?: boolean;
}

/**
 * Enhanced AppBreadcrumb Component
 *
 * Professional breadcrumb navigation with improved styling, accessibility,
 * and responsive design. Automatically generates breadcrumbs from the current
 * pathname with intelligent segment formatting.
 *
 * Features:
 * - Professional visual design with subtle container styling
 * - Responsive layout that adapts to screen size
 * - Intelligent path segment formatting (handles IDs, special cases)
 * - Enhanced accessibility with proper ARIA labels
 * - Consistent integration with design system
 * - Smooth hover transitions and visual feedback
 */
export function AppBreadcrumb({
  className,
  homeHref = '/',
  homeLabel = 'Dashboard',
  showContainer = true,
}: AppBreadcrumbProps) {
  const pathname = usePathname();
  const pathSegments = pathname ? pathname.split('/').filter(Boolean) : [];

  /**
   * Format path segments with intelligent handling of different segment types
   */
  const formatSegment = (segment: string): string => {
    // Handle numeric IDs (don't capitalize, show as "ID: 123")
    if (/^\d+$/.test(segment)) {
      return `ID: ${segment}`;
    }

    // Handle UUIDs or long alphanumeric strings (show as "Details")
    if (segment.length > 10 && /^[a-zA-Z0-9-]+$/.test(segment)) {
      return 'Details';
    }

    // Handle special cases
    const specialCases: Record<string, string> = {
      add: 'Add New',
      admin: 'Administration',
      edit: 'Edit',
      reports: 'Reports',
      'service-history': 'Service History',
      settings: 'Settings',
    };

    if (specialCases[segment]) {
      return specialCases[segment];
    }

    // Default formatting: capitalize and replace dashes with spaces
    return segment
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  const breadcrumbItems = pathSegments.map((segment, index) => {
    const href = '/' + pathSegments.slice(0, index + 1).join('/');
    const isLast = index === pathSegments.length - 1;
    const displaySegment = formatSegment(segment);

    return (
      <React.Fragment key={href}>
        <BreadcrumbItem>
          {isLast ? (
            <BreadcrumbPage className="font-medium text-foreground">
              {displaySegment}
            </BreadcrumbPage>
          ) : (
            <BreadcrumbLink asChild>
              <Link
                className="rounded-sm transition-colors hover:text-primary focus:text-primary focus:outline-none focus:ring-2 focus:ring-primary/20 focus:ring-offset-2"
                href={href}
              >
                {displaySegment}
              </Link>
            </BreadcrumbLink>
          )}
        </BreadcrumbItem>
        {!isLast && <BreadcrumbSeparator />}
      </React.Fragment>
    );
  });

  const breadcrumbContent = (
    <Breadcrumb className={cn('text-sm', className)}>
      <BreadcrumbList className="flex-wrap">
        <BreadcrumbItem>
          <BreadcrumbLink asChild>
            <Link
              className="rounded-sm transition-colors hover:text-primary focus:text-primary focus:outline-none focus:ring-2 focus:ring-primary/20 focus:ring-offset-2"
              href={homeHref}
            >
              {homeLabel}
            </Link>
          </BreadcrumbLink>
        </BreadcrumbItem>
        {pathSegments.length > 0 && <BreadcrumbSeparator />}
        {breadcrumbItems}
      </BreadcrumbList>
    </Breadcrumb>
  );

  if (!showContainer) {
    return breadcrumbContent;
  }

  return (
    <div className="mb-6 rounded-lg border border-border/50 bg-muted/30 px-4 py-3 backdrop-blur-sm">
      <div className="flex items-center">{breadcrumbContent}</div>
    </div>
  );
}
