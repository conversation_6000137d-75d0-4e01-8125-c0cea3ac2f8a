/**
 * @file Performance Tests
 * @module api/security/__tests__/performance.test
 *
 * Phase 5: Testing & Validation
 * Performance tests to ensure no regression in the new architecture
 */

import { renderHook, act } from '@testing-library/react';
import { performance } from 'perf_hooks';
import React from 'react';
import { SecurityConfigProvider } from '../providers/SecurityConfigProvider';
import { useSecureApiClient } from '../hooks/useSecureApiClient';
import { createSecureApiClient } from '../secureApiClient';

// Mock dependencies for performance testing
jest.mock('../../../../contexts/AuthContext', () => ({
  useAuthContext: () => ({
    session: { access_token: 'mock-token' },
    user: { id: '123' },
    loading: false,
    signOut: jest.fn(),
  }),
}));

jest.mock('../hooks/useCSRFProtection', () => ({
  useCSRFProtection: () => ({
    attachCSRF: jest.fn(config => config),
  }),
}));

jest.mock('../hooks/useTokenManagement', () => ({
  useTokenManagement: () => ({
    isTokenValid: true,
    isTokenExpired: false,
    refreshToken: jest.fn().mockResolvedValue(true),
    updateActivity: jest.fn(),
  }),
}));

jest.mock('../hooks/useInputValidation', () => ({
  useInputValidation: () => ({
    sanitizeInput: jest.fn(input => input),
  }),
}));

jest.mock('../hooks/useSessionSecurity', () => ({
  useSessionSecurity: () => ({
    isSessionActive: true,
    clearSession: jest.fn(),
    updateActivity: jest.fn(),
  }),
}));

// Mock fetch for performance tests
global.fetch = jest.fn().mockResolvedValue({
  ok: true,
  status: 200,
  statusText: 'OK',
  headers: new Map(),
  json: jest.fn().mockResolvedValue({ success: true }),
});

describe('Performance Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Hook Initialization Performance', () => {
    it('should initialize useSecureApiClient quickly', () => {
      const wrapper = ({ children }: { children: React.ReactNode }) => (
        <SecurityConfigProvider>{children}</SecurityConfigProvider>
      );

      const startTime = performance.now();

      const { result } = renderHook(() => useSecureApiClient(), { wrapper });

      const endTime = performance.now();
      const initializationTime = endTime - startTime;

      expect(result.current.isInitialized).toBe(true);
      expect(initializationTime).toBeLessThan(50); // Should initialize in less than 50ms
    });

    it('should handle multiple hook instances efficiently', () => {
      const wrapper = ({ children }: { children: React.ReactNode }) => (
        <SecurityConfigProvider>{children}</SecurityConfigProvider>
      );

      const startTime = performance.now();

      // Create multiple hook instances
      const hooks = Array.from({ length: 10 }, () =>
        renderHook(() => useSecureApiClient(), { wrapper })
      );

      const endTime = performance.now();
      const totalTime = endTime - startTime;

      hooks.forEach(({ result }) => {
        expect(result.current.isInitialized).toBe(true);
      });

      expect(totalTime).toBeLessThan(200); // Should handle 10 instances in less than 200ms
    });
  });

  describe('Request Performance', () => {
    it('should process secure requests efficiently', async () => {
      const wrapper = ({ children }: { children: React.ReactNode }) => (
        <SecurityConfigProvider>{children}</SecurityConfigProvider>
      );

      const { result } = renderHook(() => useSecureApiClient(), { wrapper });

      const startTime = performance.now();

      await result.current.secureRequest({
        url: '/api/test',
        method: 'GET',
      });

      const endTime = performance.now();
      const requestTime = endTime - startTime;

      expect(requestTime).toBeLessThan(100); // Should process request in less than 100ms
    });

    it('should handle concurrent requests efficiently', async () => {
      const wrapper = ({ children }: { children: React.ReactNode }) => (
        <SecurityConfigProvider>{children}</SecurityConfigProvider>
      );

      const { result } = renderHook(() => useSecureApiClient(), { wrapper });

      const startTime = performance.now();

      // Make 20 concurrent requests
      const requests = Array.from({ length: 20 }, (_, i) =>
        result.current.secureRequest({
          url: `/api/test-${i}`,
          method: 'GET',
        })
      );

      await Promise.all(requests);

      const endTime = performance.now();
      const totalTime = endTime - startTime;

      expect(totalTime).toBeLessThan(500); // Should handle 20 concurrent requests in less than 500ms
    });

    it('should maintain performance with large payloads', async () => {
      const wrapper = ({ children }: { children: React.ReactNode }) => (
        <SecurityConfigProvider>{children}</SecurityConfigProvider>
      );

      const { result } = renderHook(() => useSecureApiClient(), { wrapper });

      // Create a large payload (1MB of data)
      const largePayload = {
        data: 'x'.repeat(1024 * 1024), // 1MB string
        metadata: Array.from({ length: 1000 }, (_, i) => ({
          id: i,
          value: `item-${i}`,
        })),
      };

      const startTime = performance.now();

      await result.current.secureRequest({
        url: '/api/large-data',
        method: 'POST',
        data: largePayload,
      });

      const endTime = performance.now();
      const requestTime = endTime - startTime;

      expect(requestTime).toBeLessThan(200); // Should handle large payload in less than 200ms
    });
  });

  describe('Memory Usage', () => {
    it('should not create memory leaks with repeated hook usage', () => {
      const wrapper = ({ children }: { children: React.ReactNode }) => (
        <SecurityConfigProvider>{children}</SecurityConfigProvider>
      );

      // Measure initial memory
      const initialMemory = process.memoryUsage().heapUsed;

      // Create and destroy hooks repeatedly
      for (let i = 0; i < 100; i++) {
        const { unmount } = renderHook(() => useSecureApiClient(), { wrapper });
        unmount();
      }

      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }

      const finalMemory = process.memoryUsage().heapUsed;
      const memoryIncrease = finalMemory - initialMemory;

      // Memory increase should be minimal (less than 10MB)
      expect(memoryIncrease).toBeLessThan(10 * 1024 * 1024);
    });

    it('should reuse instances efficiently', () => {
      const wrapper = ({ children }: { children: React.ReactNode }) => (
        <SecurityConfigProvider>{children}</SecurityConfigProvider>
      );

      const { result: result1 } = renderHook(() => useSecureApiClient(), {
        wrapper,
      });
      const { result: result2 } = renderHook(() => useSecureApiClient(), {
        wrapper,
      });

      // Different hook instances should have different clients
      // but should reuse underlying infrastructure efficiently
      expect(result1.current.client).toBeDefined();
      expect(result2.current.client).toBeDefined();
    });
  });

  describe('Security Processing Performance', () => {
    it('should apply security features efficiently', async () => {
      const wrapper = ({ children }: { children: React.ReactNode }) => (
        <SecurityConfigProvider
          initialConfig={{
            csrf: {
              enabled: true,
              tokenHeader: 'X-CSRF-Token',
              excludePaths: [],
            },
            tokenValidation: {
              enabled: true,
              refreshThreshold: 300,
              autoRefresh: true,
            },
            inputSanitization: { enabled: true, sanitizers: ['xss', 'sql'] },
            authentication: {
              enabled: true,
              autoLogout: true,
              redirectOnFailure: true,
            },
          }}
        >
          {children}
        </SecurityConfigProvider>
      );

      const { result } = renderHook(() => useSecureApiClient(), { wrapper });

      const startTime = performance.now();

      // Request with all security features enabled
      await result.current.secureRequest({
        url: '/api/secure-endpoint',
        method: 'POST',
        data: {
          message: 'Test message with <script>alert("xss")</script>',
          numbers: [1, 2, 3, 4, 5],
          nested: { deep: { value: 'test' } },
        },
      });

      const endTime = performance.now();
      const processingTime = endTime - startTime;

      expect(processingTime).toBeLessThan(150); // Security processing should be fast
    });

    it('should scale security processing with request size', async () => {
      const wrapper = ({ children }: { children: React.ReactNode }) => (
        <SecurityConfigProvider>{children}</SecurityConfigProvider>
      );

      const { result } = renderHook(() => useSecureApiClient(), { wrapper });

      const sizes = [1, 10, 100, 1000]; // Different payload sizes (KB)
      const times: number[] = [];

      for (const size of sizes) {
        const payload = {
          data: 'x'.repeat(size * 1024), // size KB of data
        };

        const startTime = performance.now();

        await result.current.secureRequest({
          url: '/api/test',
          method: 'POST',
          data: payload,
        });

        const endTime = performance.now();
        times.push(endTime - startTime);
      }

      // Processing time should scale reasonably with payload size
      // Larger payloads should not cause exponential slowdown
      const timeRatios = times
        .slice(1)
        .map((time, i) => time / (times[i] || 1));

      timeRatios.forEach(ratio => {
        expect(ratio).toBeLessThan(5); // Should not be more than 5x slower for 10x data
      });
    });
  });

  describe('Configuration Performance', () => {
    it('should handle configuration changes efficiently', () => {
      const wrapper = ({ children }: { children: React.ReactNode }) => (
        <SecurityConfigProvider>{children}</SecurityConfigProvider>
      );

      const { result } = renderHook(() => useSecureApiClient(), { wrapper });

      const startTime = performance.now();

      // Make multiple configuration updates
      for (let i = 0; i < 50; i++) {
        act(() => {
          result.current.updateSecurityConfig({
            csrf: {
              enabled: i % 2 === 0,
              tokenHeader: 'X-CSRF-Token',
              excludePaths: [],
            },
          });
        });
      }

      const endTime = performance.now();
      const updateTime = endTime - startTime;

      expect(updateTime).toBeLessThan(100); // Should handle 50 updates in less than 100ms
    });

    it('should validate configuration efficiently', () => {
      const configs = Array.from({ length: 100 }, (_, i) => ({
        csrf: { enabled: true, tokenHeader: 'X-CSRF-Token', excludePaths: [] },
        tokenValidation: {
          enabled: true,
          refreshThreshold: 300 + i,
          autoRefresh: true,
        },
        inputSanitization: { enabled: true, sanitizers: ['xss', 'sql'] },
        authentication: {
          enabled: true,
          autoLogout: true,
          redirectOnFailure: true,
        },
        http: { baseURL: '/api', timeout: 10000 + i, retryAttempts: 3 },
      }));

      const startTime = performance.now();

      configs.forEach(config => {
        const wrapper = ({ children }: { children: React.ReactNode }) => (
          <SecurityConfigProvider initialConfig={config}>
            {children}
          </SecurityConfigProvider>
        );

        const { result } = renderHook(() => useSecureApiClient(), { wrapper });
        expect(result.current.isInitialized).toBe(true);
      });

      const endTime = performance.now();
      const validationTime = endTime - startTime;

      expect(validationTime).toBeLessThan(300); // Should validate 100 configs in less than 300ms
    });
  });
});
