'use client';

import * as React from 'react';

import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Separator } from '@/components/ui/separator';
import { useDashboardStore } from '@/hooks/domain/useDashboardStore';

export function TaskDashboardSettings() {
  const dashboardStore = useDashboardStore('task');
  const { layout, setViewMode } = dashboardStore();

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium">View Options</h3>
        <p className="text-sm text-muted-foreground">
          Configure how tasks are displayed in the dashboard.
        </p>
      </div>
      <Separator />
      <div className="grid gap-4">
        <Label htmlFor="view-mode" className="text-base">
          Display Mode
        </Label>
        <RadioGroup
          id="view-mode"
          value={layout.viewMode}
          onValueChange={(value: 'cards' | 'table') => setViewMode(value)}
          className="grid grid-cols-2 gap-4"
        >
          <div>
            <RadioGroupItem value="cards" id="cards" className="peer sr-only" />
            <Label
              htmlFor="cards"
              className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary"
            >
              Cards View
            </Label>
          </div>
          <div>
            <RadioGroupItem value="table" id="table" className="peer sr-only" />
            <Label
              htmlFor="table"
              className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary"
            >
              Table View
            </Label>
          </div>
        </RadioGroup>
      </div>
    </div>
  );
}
