/**
 * Quick script to check users in the system before migration
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function checkUsers() {
  console.log('🔍 Checking users in the system...\n');

  try {
    // Check auth.users
    const { data: authData, error: authError } = await supabase.auth.admin.listUsers();

    if (authError) {
      console.error('❌ Error fetching auth users:', authError.message);
      return;
    }

    const users = authData.users || [];
    console.log(`📊 Found ${users.length} users in auth.users`);

    if (users.length > 0) {
      console.log('\n👥 User details:');
      users.forEach((user, index) => {
        console.log(`   ${index + 1}. ${user.email} (${user.id})`);
        console.log(`      - Created: ${user.created_at}`);
        console.log(`      - Role in metadata: ${user.raw_user_meta_data?.role || 'none'}`);
        console.log(`      - Confirmed: ${user.email_confirmed_at ? 'Yes' : 'No'}`);
      });
    }

    // Check user_profiles
    const { data: profiles, error: profileError } = await supabase
      .from('user_profiles')
      .select('*');

    if (profileError) {
      console.error('❌ Error fetching user profiles:', profileError.message);
      return;
    }

    console.log(`\n📊 Found ${profiles.length} users in user_profiles`);

    if (profiles.length > 0) {
      console.log('\n👤 Profile details:');
      profiles.forEach((profile, index) => {
        console.log(`   ${index + 1}. ${profile.id}`);
        console.log(`      - Role: ${profile.role}`);
        console.log(`      - Employee ID: ${profile.employee_id || 'none'}`);
        console.log(`      - Active: ${profile.is_active}`);
      });
    }

    // Check employees
    const { data: employees, error: empError } = await supabase
      .from('Employee')
      .select('id, contactEmail, name');

    if (empError) {
      console.error('❌ Error fetching employees:', empError.message);
      return;
    }

    console.log(`\n📊 Found ${employees.length} employees for potential matching`);
  } catch (error) {
    console.error('❌ Check failed:', error.message);
  }
}

checkUsers().catch(console.error);
