/**
 * Authentication Debug Utilities
 * Development-only utilities for debugging authentication flows
 */

export class AuthDebugUtils {
  /**
   * Check if backend is accessible and responding
   */
  static async checkBackendHealth(): Promise<{
    accessible: boolean;
    error?: string;
    status?: number;
  }> {
    if (typeof window === 'undefined') {
      return { accessible: false, error: 'Not in browser environment' };
    }

    try {
      const backendUrl =
        process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3001/api';
      const response = await fetch(`${backendUrl}/health`, {
        method: 'GET',
        cache: 'no-cache',
      });

      return {
        accessible: response.ok,
        status: response.status,
      };
    } catch (error) {
      return {
        accessible: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Debug current authentication cookies
   */
  static debugAuthCookies(): {
    hasCookies: boolean;
    cookieInfo: Record<string, string>;
  } {
    if (typeof window === 'undefined') {
      return { hasCookies: false, cookieInfo: {} };
    }

    const cookieInfo: Record<string, string> = {};
    const cookies = document.cookie.split(';');

    let hasCookies = false;

    for (const cookie of cookies) {
      const [name, value] = cookie.trim().split('=');
      if (name && value) {
        cookieInfo[name] =
          value.length > 50 ? `${value.substring(0, 50)}...` : value;
        if (
          name.includes('sb-') ||
          name.includes('access-token') ||
          name.includes('refresh-token')
        ) {
          hasCookies = true;
        }
      }
    }

    return { hasCookies, cookieInfo };
  }

  /**
   * Debug localStorage authentication state
   */
  static debugLocalStorageAuth(): {
    hasStorageAuth: boolean;
    storageInfo: Record<string, string>;
  } {
    if (typeof window === 'undefined') {
      return { hasStorageAuth: false, storageInfo: {} };
    }

    const storageInfo: Record<string, string> = {};
    let hasStorageAuth = false;

    try {
      // Check for Supabase storage
      const supabaseKey = Object.keys(localStorage).find(
        key => key.includes('supabase') || key.includes('sb-')
      );

      if (supabaseKey) {
        hasStorageAuth = true;
        const value = localStorage.getItem(supabaseKey);
        storageInfo[supabaseKey] = value
          ? `${value.substring(0, 100)}...`
          : 'null';
      }

      // Check for other auth-related storage
      const authKeys = Object.keys(localStorage).filter(
        key =>
          key.includes('auth') ||
          key.includes('token') ||
          key.includes('session')
      );

      authKeys.forEach(key => {
        const value = localStorage.getItem(key);
        storageInfo[key] = value ? `${value.substring(0, 100)}...` : 'null';
        if (value) hasStorageAuth = true;
      });
    } catch (error) {
      storageInfo.error = 'LocalStorage access failed';
    }

    return { hasStorageAuth, storageInfo };
  }

  /**
   * Comprehensive authentication debug report
   */
  static async generateDebugReport(): Promise<{
    backendHealth: Awaited<
      ReturnType<typeof AuthDebugUtils.checkBackendHealth>
    >;
    cookies: ReturnType<typeof AuthDebugUtils.debugAuthCookies>;
    localStorage: ReturnType<typeof AuthDebugUtils.debugLocalStorageAuth>;
    timestamp: string;
  }> {
    const [backendHealth] = await Promise.all([this.checkBackendHealth()]);

    return {
      backendHealth,
      cookies: this.debugAuthCookies(),
      localStorage: this.debugLocalStorageAuth(),
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Log debug report to console (development only)
   */
  static async logDebugReport(): Promise<void> {
    if (process.env.NODE_ENV !== 'development') {
      return;
    }

    console.group('🔍 Authentication Debug Report');

    try {
      const report = await this.generateDebugReport();

      console.log('Backend Health:', report.backendHealth);
      console.log('Cookies:', report.cookies);
      console.log('LocalStorage:', report.localStorage);
      console.log('Generated at:', report.timestamp);
    } catch (error) {
      console.error('Failed to generate debug report:', error);
    } finally {
      console.groupEnd();
    }
  }
}

// Development-only auto-debug on module load
if (process.env.NODE_ENV === 'development' && typeof window !== 'undefined') {
  // Auto-run debug report after a short delay to allow for initialization
  setTimeout(() => {
    AuthDebugUtils.logDebugReport();
  }, 2000);
}
