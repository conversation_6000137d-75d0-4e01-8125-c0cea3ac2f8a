/**
 * Set User Role Script
 *
 * This script sets the <NAME_EMAIL> to USER for NON-ADMIN testing
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey);

async function setUserRole() {
  console.log('🔧 Setting <EMAIL> role to USER for NON-ADMIN testing...\n');

  try {
    // Find the user by email
    const { data: authUsers, error: authError } = await supabaseAdmin.auth.admin.listUsers();

    if (authError) {
      console.error('❌ Error fetching auth users:', authError.message);
      return;
    }

    const targetUser = authUsers.users.find(user => user.email === '<EMAIL>');

    if (!targetUser) {
      console.error('❌ User <EMAIL> not found in auth.users');
      return;
    }

    console.log(`✅ Found user: ${targetUser.email} (${targetUser.id})`);

    // Update the user profile role
    const { data: updatedProfile, error: updateError } = await supabaseAdmin
      .from('user_profiles')
      .update({
        role: 'USER',
        updated_at: new Date().toISOString(),
      })
      .eq('id', targetUser.id)
      .select()
      .single();

    if (updateError) {
      console.error('❌ Error updating user profile:', updateError.message);
      return;
    }

    console.log('✅ User profile updated successfully:');
    console.log(`   - User ID: ${updatedProfile.id}`);
    console.log(`   - Role: ${updatedProfile.role}`);
    console.log(`   - Active: ${updatedProfile.is_active}`);
    console.log(`   - Updated: ${updatedProfile.updated_at}`);

    console.log('\n🎯 Next Steps:');
    console.log('1. Log out of any current admin session');
    console.log('2. Go to: http://localhost:9002/auth-test');
    console.log('3. Log in with:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: UserPassword123!');
    console.log('4. Run the NON-ADMIN test script in browser console');
  } catch (error) {
    console.error('❌ Script failed:', error.message);
  }
}

setUserRole().catch(console.error);
