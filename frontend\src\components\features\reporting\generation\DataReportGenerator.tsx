/**
 * @file DataReportGenerator.tsx
 * @description Comprehensive data report generation component for all entities
 */

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  FileText,
  Download,
  Settings,
  Calendar,
  Filter,
  Users,
  Car,
  ClipboardList,
  Building,
} from 'lucide-react';
import {
  useReportGeneration,
  useReportTemplates,
} from '../hooks/useReportGeneration';
import { DateRangePicker } from '../components/DateRangePicker';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { Alert, AlertDescription } from '@/components/ui/alert';

/**
 * Entity configuration for report generation
 */
const ENTITY_CONFIGS = [
  {
    id: 'delegations',
    name: 'Delegations',
    icon: ClipboardList,
    description: 'Delegation assignments and status tracking',
    color: 'bg-blue-100 text-blue-800',
  },
  {
    id: 'tasks',
    name: 'Tasks',
    icon: FileText,
    description: 'Task completion and performance metrics',
    color: 'bg-green-100 text-green-800',
  },
  {
    id: 'vehicles',
    name: 'Vehicles',
    icon: Car,
    description: 'Vehicle utilization and maintenance data',
    color: 'bg-orange-100 text-orange-800',
  },
  {
    id: 'employees',
    name: 'Employees',
    icon: Users,
    description: 'Employee performance and workload analysis',
    color: 'bg-purple-100 text-purple-800',
  },
];

/**
 * Export format options
 */
const EXPORT_FORMATS = [
  { id: 'pdf', name: 'PDF', description: 'Formatted document for printing' },
  {
    id: 'excel',
    name: 'Excel',
    description: 'Spreadsheet with multiple sheets',
  },
  {
    id: 'csv',
    name: 'CSV',
    description: 'Comma-separated values for data analysis',
  },
];

/**
 * DataReportGenerator Component
 *
 * Provides comprehensive interface for generating data reports across all entities.
 * Supports both individual and aggregate reporting with customizable templates.
 */
export const DataReportGenerator: React.FC = () => {
  // State management
  const [selectedEntities, setSelectedEntities] = useState<string[]>([
    'delegations',
  ]);
  const [selectedTemplate, setSelectedTemplate] =
    useState<string>('comprehensive');
  const [selectedFormat, setSelectedFormat] = useState<string>('pdf');
  const [dateRange, setDateRange] = useState<{ from: Date; to: Date } | null>(
    null
  );
  const [customFilters, setCustomFilters] = useState<Record<string, any>>({});
  const [reportName, setReportName] = useState<string>('');
  const [reportDescription, setReportDescription] = useState<string>('');

  // Hooks
  const {
    generateComprehensiveReport,
    isGenerating,
    error: generationError,
  } = useReportGeneration();
  const { templates, isLoading: templatesLoading } = useReportTemplates();

  /**
   * Handle entity selection toggle
   */
  const handleEntityToggle = (entityId: string) => {
    setSelectedEntities(prev =>
      prev.includes(entityId)
        ? prev.filter(id => id !== entityId)
        : [...prev, entityId]
    );
  };

  /**
   * Handle report generation
   */
  const handleGenerateReport = async () => {
    if (selectedEntities.length === 0) {
      return;
    }

    const reportConfig = {
      entityTypes: selectedEntities,
      template: selectedTemplate,
      format: selectedFormat,
      filters: {
        ...customFilters,
        ...(dateRange && {
          dateRange: {
            from: dateRange.from.toISOString(),
            to: dateRange.to.toISOString(),
          },
        }),
      },
      options: {
        name: reportName || `Report ${new Date().toLocaleDateString()}`,
        description: reportDescription,
        includeCharts: true,
        includeSummary: true,
      },
    };

    try {
      await generateComprehensiveReport(reportConfig);
    } catch (error) {
      console.error('Failed to generate report:', error);
    }
  };

  /**
   * Get selected template details
   */
  const selectedTemplateDetails = Array.isArray(templates)
    ? templates.find((t: any) => t.id === selectedTemplate)
    : null;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">
            Data Report Generator
          </h2>
          <p className="text-gray-600 mt-1">
            Generate comprehensive reports for delegations, tasks, vehicles, and
            employees
          </p>
        </div>
        <Badge variant="outline" className="flex items-center gap-2">
          <FileText className="h-4 w-4" />
          Report Builder
        </Badge>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Configuration Panel */}
        <div className="lg:col-span-2 space-y-6">
          {/* Entity Selection */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building className="h-5 w-5" />
                Select Data Sources
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {ENTITY_CONFIGS.map(entity => {
                  const Icon = entity.icon;
                  const isSelected = selectedEntities.includes(entity.id);

                  return (
                    <div
                      key={entity.id}
                      className={`
                        border rounded-lg p-4 cursor-pointer transition-all
                        ${
                          isSelected
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-200 hover:border-gray-300'
                        }
                      `}
                      onClick={() => handleEntityToggle(entity.id)}
                    >
                      <div className="flex items-start gap-3">
                        <Checkbox
                          checked={isSelected}
                          onCheckedChange={() => handleEntityToggle(entity.id)}
                        />
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <Icon className="h-5 w-5 text-gray-600" />
                            <span className="font-medium">{entity.name}</span>
                            <Badge className={entity.color} variant="secondary">
                              {entity.id}
                            </Badge>
                          </div>
                          <p className="text-sm text-gray-600">
                            {entity.description}
                          </p>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>

          {/* Template Selection */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Report Template
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {templatesLoading ? (
                <LoadingSpinner />
              ) : (
                <Select
                  value={selectedTemplate}
                  onValueChange={setSelectedTemplate}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a template" />
                  </SelectTrigger>
                  <SelectContent>
                    {Array.isArray(templates) &&
                      templates.map((template: any) => (
                        <SelectItem key={template.id} value={template.id}>
                          <div>
                            <div className="font-medium">{template.name}</div>
                            <div className="text-sm text-gray-600">
                              {template.description}
                            </div>
                          </div>
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
              )}

              {selectedTemplateDetails && (
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="font-medium mb-2">
                    {selectedTemplateDetails.name}
                  </h4>
                  <p className="text-sm text-gray-600 mb-3">
                    {selectedTemplateDetails.description}
                  </p>
                  <div className="flex flex-wrap gap-2">
                    {selectedTemplateDetails.sections?.map((section: any) => (
                      <Badge
                        key={section}
                        variant="outline"
                        className="text-xs"
                      >
                        {section}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Filters */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Filter className="h-5 w-5" />
                Filters & Options
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Date Range */}
              <div>
                <Label className="flex items-center gap-2 mb-2">
                  <Calendar className="h-4 w-4" />
                  Date Range
                </Label>
                <DateRangePicker
                  value={dateRange}
                  onChange={setDateRange}
                  placeholder="Select date range for data"
                />
              </div>

              {/* Export Format */}
              <div>
                <Label className="mb-2 block">Export Format</Label>
                <Select
                  value={selectedFormat}
                  onValueChange={setSelectedFormat}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {EXPORT_FORMATS.map(format => (
                      <SelectItem key={format.id} value={format.id}>
                        <div>
                          <div className="font-medium">{format.name}</div>
                          <div className="text-sm text-gray-600">
                            {format.description}
                          </div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Report Name */}
              <div>
                <Label htmlFor="reportName" className="mb-2 block">
                  Report Name
                </Label>
                <Input
                  id="reportName"
                  value={reportName}
                  onChange={e => setReportName(e.target.value)}
                  placeholder="Enter custom report name"
                />
              </div>

              {/* Report Description */}
              <div>
                <Label htmlFor="reportDescription" className="mb-2 block">
                  Description
                </Label>
                <Textarea
                  id="reportDescription"
                  value={reportDescription}
                  onChange={e => setReportDescription(e.target.value)}
                  placeholder="Optional description for the report"
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Summary Panel */}
        <div className="space-y-6">
          {/* Generation Summary */}
          <Card>
            <CardHeader>
              <CardTitle>Report Summary</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label className="text-sm font-medium text-gray-600">
                  Selected Entities
                </Label>
                <div className="mt-1 flex flex-wrap gap-1">
                  {selectedEntities.map(entityId => {
                    const entity = ENTITY_CONFIGS.find(e => e.id === entityId);
                    return entity ? (
                      <Badge
                        key={entityId}
                        className={entity.color}
                        variant="secondary"
                      >
                        {entity.name}
                      </Badge>
                    ) : null;
                  })}
                </div>
              </div>

              <Separator />

              <div>
                <Label className="text-sm font-medium text-gray-600">
                  Template
                </Label>
                <p className="mt-1 text-sm">
                  {selectedTemplateDetails?.name || 'None selected'}
                </p>
              </div>

              <div>
                <Label className="text-sm font-medium text-gray-600">
                  Format
                </Label>
                <p className="mt-1 text-sm">
                  {EXPORT_FORMATS.find(f => f.id === selectedFormat)?.name}
                </p>
              </div>

              {dateRange && (
                <div>
                  <Label className="text-sm font-medium text-gray-600">
                    Date Range
                  </Label>
                  <p className="mt-1 text-sm">
                    {dateRange.from.toLocaleDateString()} -{' '}
                    {dateRange.to.toLocaleDateString()}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Generate Button */}
          <Card>
            <CardContent className="pt-6">
              {generationError && (
                <Alert className="mb-4" variant="destructive">
                  <AlertDescription>{generationError}</AlertDescription>
                </Alert>
              )}

              <Button
                onClick={handleGenerateReport}
                disabled={selectedEntities.length === 0 || isGenerating}
                className="w-full"
                size="lg"
              >
                {isGenerating ? (
                  <>
                    <LoadingSpinner className="mr-2 h-4 w-4" />
                    Generating Report...
                  </>
                ) : (
                  <>
                    <Download className="mr-2 h-4 w-4" />
                    Generate Report
                  </>
                )}
              </Button>

              <p className="text-xs text-gray-500 mt-2 text-center">
                Report will be generated and available for download
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};
