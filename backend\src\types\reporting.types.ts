// backend/src/types/reporting.types.ts

/**
 * PHASE 1 ENHANCEMENT: Backend Reporting Types
 *
 * Following SRP: Dedicated file for reporting-related type definitions
 * Separation of Concerns: Types separated from business logic
 */

// Domain Types - Following SRP: Clear data contracts
export interface DelegationSummary {
  id: string;
  eventName: string;
  location: string;
  status: string;
  durationFrom: Date;
  durationTo: Date;
  createdAt: Date;
  updatedAt: Date;
  escorts: Array<{ id: number; name: string }>;
  drivers: Array<{ id: number; name: string }>;
  vehicles: Array<{ id: number; make: string; model: string }>;
}

export interface ReportingFilters {
  dateRange: {
    from: string;
    to: string;
  };
  employees?: number[];
  locations?: string[];
  status?: string[];
  vehicles?: number[];
  // PHASE 1: Additional entity filters
  taskStatus?: string[];
  taskPriority?: string[];
  serviceTypes?: string[];
  serviceStatus?: string[];
  costRange?: {
    min: number;
    max: number;
  };
  // PHASE 1: Cross-entity options
  includeServiceHistory?: boolean;
  includeTaskData?: boolean;
  includeEmployeeMetrics?: boolean;
  includeVehicleAnalytics?: boolean;
  includeCrossEntityCorrelations?: boolean;
}

// PHASE 1 ENHANCEMENT: New entity analytics interfaces
export interface TaskAnalytics {
  totalCount: number;
  statusDistribution: TaskStatusDistributionData[];
  priorityDistribution: TaskPriorityDistributionData[];
  completionRate: number;
  overdueCount: number;
  averageCompletionTime: number;
  assignmentMetrics: TaskAssignmentMetrics[];
  trendData?: TaskTrendData[];
}

export interface VehicleAnalytics {
  totalCount: number;
  serviceHistory: ServiceHistoryData[];
  costAnalysis: ServiceCostSummary;
  utilizationMetrics: VehicleUtilizationData[];
  maintenanceSchedule: MaintenanceScheduleData[];
  performanceMetrics?: VehiclePerformanceData[];
}

export interface EmployeeAnalytics {
  totalCount: number;
  performanceMetrics: EmployeePerformanceData[];
  delegationHistory: DelegationHistoryData[];
  taskAssignments: TaskAssignmentData[];
  availabilityMetrics: AvailabilityMetrics[];
  workloadDistribution?: WorkloadDistributionData[];
}

export interface CrossEntityAnalytics {
  delegations?: any; // DelegationAnalytics from existing service
  tasks?: TaskAnalytics;
  vehicles?: VehicleAnalytics;
  employees?: EmployeeAnalytics;
  correlations: CrossEntityCorrelations;
  summary: CrossEntitySummary;
}

// Supporting interfaces for new analytics
export interface TaskStatusDistributionData {
  status: string;
  count: number;
  percentage: number;
  color: string;
}

export interface TaskPriorityDistributionData {
  priority: string;
  count: number;
  percentage: number;
  color: string;
}

export interface TaskAssignmentMetrics {
  employeeId: number;
  employeeName: string;
  assignedTasks: number;
  completedTasks: number;
  completionRate: number;
  averageCompletionTime: number;
}

export interface TaskTrendData {
  date: string;
  created: number;
  completed: number;
  inProgress: number;
  overdue: number;
}

export interface VehicleUtilizationData {
  vehicleId: number;
  vehicleName: string;
  utilizationRate: number;
  totalDelegations: number;
  activeDelegations: number;
  maintenanceHours: number;
}

export interface MaintenanceScheduleData {
  vehicleId: number;
  vehicleName: string;
  nextMaintenanceDate: string;
  maintenanceType: string;
  priority: 'Low' | 'Medium' | 'High' | 'Critical';
  estimatedCost: number;
}

export interface VehiclePerformanceData {
  vehicleId: number;
  vehicleName: string;
  fuelEfficiency: number;
  maintenanceCost: number;
  downtime: number;
  reliabilityScore: number;
}

export interface EmployeePerformanceData {
  employeeId: number;
  employeeName: string;
  completedDelegations: number;
  completedTasks: number;
  averageRating: number;
  onTimePerformance: number;
  workloadScore: number;
}

export interface DelegationHistoryData {
  employeeId: number;
  employeeName: string;
  totalDelegations: number;
  completedDelegations: number;
  averageDuration: number;
  successRate: number;
}

export interface TaskAssignmentData {
  employeeId: number;
  employeeName: string;
  assignedTasks: number;
  completedTasks: number;
  pendingTasks: number;
  overdueTasksCount: number;
}

export interface AvailabilityMetrics {
  employeeId: number;
  employeeName: string;
  availableHours: number;
  scheduledHours: number;
  utilizationRate: number;
  overtimeHours: number;
}

export interface WorkloadDistributionData {
  employeeId: number;
  employeeName: string;
  currentWorkload: number;
  capacity: number;
  workloadPercentage: number;
  status: 'Underutilized' | 'Optimal' | 'Overloaded';
}

export interface ServiceHistoryData {
  id: string;
  vehicleId: number;
  vehicleName: string;
  serviceType: string;
  status: string;
  scheduledDate: string;
  completedDate?: string;
  cost: number;
  description: string;
  relatedDelegationId?: string;
  relatedTaskId?: string;
}

export interface ServiceCostSummary {
  totalCost: number;
  averageCostPerService: number;
  costByType: {
    type: string;
    cost: number;
    count: number;
  }[];
  monthlyTrend: {
    month: string;
    cost: number;
  }[];
}

export interface CrossEntityCorrelations {
  delegationTaskCorrelation: number;
  vehicleUtilizationCorrelation: number;
  employeePerformanceCorrelation: number;
  costEfficiencyCorrelation: number;
}

export interface CrossEntitySummary {
  totalEntities: number;
  activeEntities: number;
  performanceScore: number;
  efficiencyRating: number;
  recommendations: string[];
}

// Service Interface - Following Interface Segregation Principle
export interface IReportingDataService {
  getDelegationAnalytics(filters: ReportingFilters): Promise<DelegationAnalytics>;
  getTaskAnalytics(filters: ReportingFilters): Promise<TaskAnalytics>;
  getVehicleAnalytics(filters: ReportingFilters): Promise<VehicleAnalytics>;
  getEmployeeAnalytics(filters: ReportingFilters): Promise<EmployeeAnalytics>;
  getCrossEntityAnalytics(filters: ReportingFilters): Promise<CrossEntityAnalytics>;
  getTaskMetrics(delegationIds?: string[]): Promise<TaskMetrics>;
  getTrendData(filters: ReportingFilters): Promise<TrendData[]>;
  getLocationMetrics(filters: ReportingFilters): Promise<LocationMetrics[]>;
  getDelegations(
    filters: ReportingFilters,
    pagination: { page: number; pageSize: number },
  ): Promise<PaginatedDelegationsResponse>;
}

// Core Analytics Interfaces
export interface DelegationAnalytics {
  summary: {
    totalDelegations: number;
    activeDelegations: number;
    completedDelegations: number;
    completionRate: number;
    averageDuration: number;
    totalDelegates: number;
  };
  statusDistribution: Array<{
    status: string;
    count: number;
    percentage: number;
    color: string;
  }>;
  trendData: TrendData[];
  locationMetrics: LocationMetrics[];
  delegations: Array<{
    id: string;
    eventName: string;
    location: string;
    status: string;
    assignedTo: string;
    assignedBy: string;
    createdAt: string;
    completedAt?: string;
    actualHours?: number;
  }>;
}

export interface TaskMetrics {
  totalTasks: number;
  completedTasks: number;
  pendingTasks: number;
  overdueTasks: number;
  completionRate: number;
  averageCompletionTime: number;
}

export interface TrendData {
  date: string;
  count: number;
  completed: number;
  pending: number;
  inProgress: number;
}

export interface LocationMetrics {
  location: string;
  count: number;
  percentage: number;
  averageDuration: number;
}

export interface PaginatedDelegationsResponse {
  delegations: DelegationSummary[];
  totalCount: number;
  totalPages: number;
  currentPage: number;
  pageSize: number;
}
