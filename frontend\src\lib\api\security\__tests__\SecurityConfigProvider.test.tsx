/**
 * @file SecurityConfigProvider Tests
 * @module api/security/__tests__/SecurityConfigProvider.test
 *
 * Phase 5: Testing & Validation
 * Comprehensive tests for SecurityConfigProvider React context
 */

import React from 'react';
import { render, screen, act } from '@testing-library/react';
import { renderHook } from '@testing-library/react';
import {
  SecurityConfigProvider,
  useSecurityConfig,
  useSecurityConfigValue,
  DEFAULT_SECURITY_CONFIG,
} from '../providers/SecurityConfigProvider';
import { SECURITY_CONSTANTS } from '../../../security';

describe('SecurityConfigProvider', () => {
  describe('Default Configuration', () => {
    it('should provide default security configuration', () => {
      const wrapper = ({ children }: { children: React.ReactNode }) => (
        <SecurityConfigProvider>{children}</SecurityConfigProvider>
      );

      const { result } = renderHook(() => useSecurityConfig(), { wrapper });

      expect(result.current.config).toEqual(DEFAULT_SECURITY_CONFIG);
      expect(result.current.isConfigValid).toBe(true);
      expect(result.current.configVersion).toBe('1.0.0');
    });

    it('should use SECURITY_CONSTANTS for default values', () => {
      const wrapper = ({ children }: { children: React.ReactNode }) => (
        <SecurityConfigProvider>{children}</SecurityConfigProvider>
      );

      const { result } = renderHook(() => useSecurityConfig(), { wrapper });

      expect(result.current.config.tokenValidation.refreshThreshold).toBe(
        SECURITY_CONSTANTS.TOKEN_EXPIRY_THRESHOLD_MINUTES * 60
      );
    });
  });

  describe('Custom Configuration', () => {
    it('should merge custom configuration with defaults', () => {
      const customConfig = {
        csrf: {
          enabled: false,
          tokenHeader: 'X-Custom-CSRF',
          excludePaths: [],
        },
        http: {
          baseURL: '/api',
          timeout: 5000,
          retryAttempts: 3,
        },
      };

      const wrapper = ({ children }: { children: React.ReactNode }) => (
        <SecurityConfigProvider initialConfig={customConfig}>
          {children}
        </SecurityConfigProvider>
      );

      const { result } = renderHook(() => useSecurityConfig(), { wrapper });

      // Should merge custom config
      expect(result.current.config.csrf.enabled).toBe(false);
      expect(result.current.config.csrf.tokenHeader).toBe('X-Custom-CSRF');
      expect(result.current.config.http.timeout).toBe(5000);

      // Should preserve defaults for non-overridden values
      expect(result.current.config.tokenValidation.enabled).toBe(true);
      expect(result.current.config.inputSanitization.enabled).toBe(true);
    });

    it('should deep merge nested configuration objects', () => {
      const customConfig = {
        csrf: {
          enabled: true,
          tokenHeader: 'X-CSRF-Token',
          excludePaths: ['/api/custom'],
        },
      };

      const wrapper = ({ children }: { children: React.ReactNode }) => (
        <SecurityConfigProvider initialConfig={customConfig}>
          {children}
        </SecurityConfigProvider>
      );

      const { result } = renderHook(() => useSecurityConfig(), { wrapper });

      // Should preserve default csrf.enabled and csrf.tokenHeader
      expect(result.current.config.csrf.enabled).toBe(true);
      expect(result.current.config.csrf.tokenHeader).toBe('X-CSRF-Token');

      // Should use custom excludePaths
      expect(result.current.config.csrf.excludePaths).toEqual(['/api/custom']);
    });
  });

  describe('Configuration Validation', () => {
    it('should validate configuration by default', () => {
      const wrapper = ({ children }: { children: React.ReactNode }) => (
        <SecurityConfigProvider>{children}</SecurityConfigProvider>
      );

      const { result } = renderHook(() => useSecurityConfig(), { wrapper });

      expect(result.current.isConfigValid).toBe(true);
    });

    it('should detect invalid configuration', () => {
      const invalidConfig = {
        http: {
          baseURL: '', // Invalid: empty baseURL
          timeout: -1, // Invalid: negative timeout
          retryAttempts: 3,
        },
      };

      const wrapper = ({ children }: { children: React.ReactNode }) => (
        <SecurityConfigProvider
          initialConfig={invalidConfig}
          validateConfig={true}
        >
          {children}
        </SecurityConfigProvider>
      );

      const { result } = renderHook(() => useSecurityConfig(), { wrapper });

      expect(result.current.isConfigValid).toBe(false);
    });

    it('should skip validation when disabled', () => {
      const invalidConfig = {
        http: {
          baseURL: '',
          timeout: -1,
          retryAttempts: 3,
        },
      };

      const wrapper = ({ children }: { children: React.ReactNode }) => (
        <SecurityConfigProvider
          initialConfig={invalidConfig}
          validateConfig={false}
        >
          {children}
        </SecurityConfigProvider>
      );

      const { result } = renderHook(() => useSecurityConfig(), { wrapper });

      expect(result.current.isConfigValid).toBe(true);
    });
  });

  describe('Configuration Updates', () => {
    it('should call onConfigChange when configuration changes', () => {
      const onConfigChange = jest.fn();
      const newConfig = {
        csrf: {
          enabled: false,
          tokenHeader: 'X-CSRF-Token',
          excludePaths: [],
        },
      };

      const wrapper = ({ children }: { children: React.ReactNode }) => (
        <SecurityConfigProvider onConfigChange={onConfigChange}>
          {children}
        </SecurityConfigProvider>
      );

      const { result } = renderHook(() => useSecurityConfig(), { wrapper });

      act(() => {
        result.current.updateConfig(newConfig);
      });

      expect(onConfigChange).toHaveBeenCalledWith(
        expect.objectContaining({
          csrf: expect.objectContaining({ enabled: false }),
        })
      );
    });

    it('should reset configuration to defaults', () => {
      const onConfigChange = jest.fn();

      const wrapper = ({ children }: { children: React.ReactNode }) => (
        <SecurityConfigProvider onConfigChange={onConfigChange}>
          {children}
        </SecurityConfigProvider>
      );

      const { result } = renderHook(() => useSecurityConfig(), { wrapper });

      act(() => {
        result.current.resetConfig();
      });

      expect(onConfigChange).toHaveBeenCalledWith(DEFAULT_SECURITY_CONFIG);
    });
  });

  describe('Hook Usage', () => {
    it('should throw error when useSecurityConfig is used outside provider', () => {
      // Suppress console.error for this test
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      expect(() => {
        renderHook(() => useSecurityConfig());
      }).toThrow(
        'useSecurityConfig must be used within a SecurityConfigProvider'
      );

      consoleSpy.mockRestore();
    });

    it('should provide read-only config through useSecurityConfigValue', () => {
      const wrapper = ({ children }: { children: React.ReactNode }) => (
        <SecurityConfigProvider>{children}</SecurityConfigProvider>
      );

      const { result } = renderHook(() => useSecurityConfigValue(), {
        wrapper,
      });

      expect(result.current).toEqual(DEFAULT_SECURITY_CONFIG);
    });
  });

  describe('Component Integration', () => {
    it('should render children correctly', () => {
      render(
        <SecurityConfigProvider>
          <div data-testid="test-child">Test Content</div>
        </SecurityConfigProvider>
      );

      expect(screen.getByTestId('test-child')).toBeInTheDocument();
      expect(screen.getByText('Test Content')).toBeInTheDocument();
    });

    it('should provide context value to children', () => {
      const TestComponent = () => {
        const { config, isConfigValid } = useSecurityConfig();
        return (
          <div>
            <span data-testid="csrf-enabled">
              {config.csrf.enabled.toString()}
            </span>
            <span data-testid="config-valid">{isConfigValid.toString()}</span>
          </div>
        );
      };

      render(
        <SecurityConfigProvider>
          <TestComponent />
        </SecurityConfigProvider>
      );

      expect(screen.getByTestId('csrf-enabled')).toHaveTextContent('true');
      expect(screen.getByTestId('config-valid')).toHaveTextContent('true');
    });
  });
});
