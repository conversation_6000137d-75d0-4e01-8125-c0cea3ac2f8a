#!/bin/bash

# WorkHub Staging Troubleshooting and Restart Script
# This script will diagnose issues and restart services properly

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# Logging functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
}

info() {
    echo -e "${CYAN}ℹ️  $1${NC}"
}

echo -e "${BLUE}
╔══════════════════════════════════════════════════════════════════════════════╗
║                    WorkHub Staging Fix & Restart Script                     ║
║                                                                              ║
║  🔧 Fixes: Environment variables, Redis URL, Docker dependencies            ║
║  🚀 Restarts: All services with proper dependency order                     ║
╚══════════════════════════════════════════════════════════════════════════════╝
${NC}"

# Step 1: Verify we're in the correct directory
log "📁 Verifying project directory..."
if [[ ! -f "docker-compose.staging.yml" ]]; then
    error "docker-compose.staging.yml not found. Please run this script from the project root."
    exit 1
fi
success "Project directory verified"

# Step 2: Check environment file
log "🔧 Checking environment configuration..."
if [[ ! -f "backend/.env" ]]; then
    error "backend/.env file not found!"
    exit 1
fi

# Verify critical environment variables are set
log "🔍 Verifying critical environment variables..."
if grep -q "REDIS_URL=redis://redis:6379" backend/.env; then
    success "Redis URL is correctly configured for Docker network"
else
    warning "Redis URL might be incorrect. Expected: redis://redis:6379"
fi

if grep -q "NODE_ENV=staging" backend/.env; then
    success "Node environment is set to staging"
else
    warning "Node environment should be set to staging"
fi

if grep -q "SUPABASE_URL=https://abylqjnpaegeqwktcukn.supabase.co" backend/.env; then
    success "Supabase URL is configured"
else
    error "Supabase URL is missing or incorrect"
    exit 1
fi

# Step 3: Stop existing services
log "🛑 Stopping existing services..."
docker-compose -f docker-compose.staging.yml down --remove-orphans
success "All services stopped"

# Step 4: Clean up old containers and networks
log "🧹 Cleaning up Docker resources..."
docker system prune -f
success "Docker cleanup completed"

# Step 5: Build services
log "🏗️  Building services with latest changes..."
docker-compose -f docker-compose.staging.yml build --no-cache
success "Services built successfully"

# Step 6: Start services in proper order
log "🚀 Starting services in dependency order..."

# Start Redis first
log "Starting Redis service..."
docker-compose -f docker-compose.staging.yml up -d redis
sleep 10

# Wait for Redis to be healthy
log "⏳ Waiting for Redis to be healthy..."
for i in {1..30}; do
    if docker-compose -f docker-compose.staging.yml ps redis | grep -q "healthy"; then
        success "Redis is healthy"
        break
    fi
    if [[ $i -eq 30 ]]; then
        error "Redis failed to become healthy"
        docker-compose -f docker-compose.staging.yml logs redis
        exit 1
    fi
    echo -n "."
    sleep 2
done

# Start backend service
log "Starting backend service..."
docker-compose -f docker-compose.staging.yml up -d backend
sleep 15

# Wait for backend to be healthy
log "⏳ Waiting for backend to be healthy..."
for i in {1..30}; do
    if docker-compose -f docker-compose.staging.yml ps backend | grep -q "healthy"; then
        success "Backend is healthy"
        break
    fi
    if [[ $i -eq 30 ]]; then
        error "Backend failed to become healthy"
        echo "Backend logs:"
        docker-compose -f docker-compose.staging.yml logs --tail=50 backend
        exit 1
    fi
    echo -n "."
    sleep 2
done

# Start remaining services
log "Starting remaining services..."
docker-compose -f docker-compose.staging.yml up -d
sleep 10

# Step 7: Verify all services are running
log "🔍 Verifying service status..."
docker-compose -f docker-compose.staging.yml ps

# Step 8: Test API endpoints
log "🧪 Testing API endpoints..."

# Test health endpoint
if curl -f -s "http://localhost:3001/api/health" > /dev/null; then
    success "Backend health endpoint is responding"
else
    warning "Backend health endpoint is not responding"
fi

# Test frontend
if curl -f -s "http://localhost:3000" > /dev/null; then
    success "Frontend is responding"
else
    warning "Frontend is not responding"
fi

# Step 9: Show service logs for verification
log "📊 Recent service logs for verification..."

echo -e "${CYAN}=== Backend Logs (last 20 lines) ===${NC}"
docker-compose -f docker-compose.staging.yml logs --tail=20 backend

echo -e "${CYAN}=== Redis Logs (last 10 lines) ===${NC}"
docker-compose -f docker-compose.staging.yml logs --tail=10 redis

# Step 10: Final status check
log "🎯 Final status verification..."
echo -e "${GREEN}
╔══════════════════════════════════════════════════════════════════════════════╗
║                           🎉 RESTART COMPLETED! 🎉                          ║
╚══════════════════════════════════════════════════════════════════════════════╝
${NC}"

echo -e "${CYAN}📊 Service Status:${NC}"
echo "  🌐 Frontend: http://localhost:3000"
echo "  🔧 Backend API: http://localhost:3001"
echo "  📋 API Health: http://localhost:3001/api/health"
echo ""

echo -e "${CYAN}🔍 Monitoring Commands:${NC}"
echo "  📋 View all logs: docker-compose -f docker-compose.staging.yml logs -f"
echo "  🔧 Backend logs: docker-compose -f docker-compose.staging.yml logs -f backend"
echo "  🔴 Redis logs: docker-compose -f docker-compose.staging.yml logs -f redis"
echo "  📊 Service status: docker-compose -f docker-compose.staging.yml ps"
echo ""

echo -e "${CYAN}🧪 Test Commands:${NC}"
echo "  🏥 Health check: curl http://localhost:3001/api/health"
echo "  🔍 Redis ping: docker exec workhub-redis-staging redis-cli ping"
echo ""

success "Staging environment restart completed successfully!"
warning "Monitor the logs for a few minutes to ensure stability"
