// frontend/src/components/features/reporting/data/hooks/useReportingQueries.ts

import {
  useQuery,
  useQueries,
  useQueryClient,
  UseQueryOptions,
  QueryKey,
} from '@tanstack/react-query';
import { useCallback, useMemo } from 'react';
// PHASE 1 ENHANCEMENT: Import new entity analytics types
import {
  ReportingFilters,
  DelegationAnalytics,
  TaskMetrics,
  TrendData,
  LocationMetrics,
  ServiceHistoryData,
  ServiceCostSummary,
  // PHASE 1: New entity analytics types
  TaskAnalytics,
  VehicleAnalytics,
  EmployeeAnalytics,
  CrossEntityAnalytics,
} from '../types/reporting';
import { reportingDataService } from '../services/ReportingDataService';

/**
 * Query Keys Factory - Centralized query key management following React Query best practices
 *
 * Follows SRP by having one responsibility: managing query keys for reporting
 */
export const reportingQueryKeys = {
  all: ['reporting'] as const,
  analytics: () => [...reportingQueryKeys.all, 'analytics'] as const,
  delegationAnalytics: (filters: ReportingFilters) =>
    [...reportingQueryKeys.analytics(), 'delegations', filters] as const,
  taskMetrics: (delegationIds?: string[]) =>
    [...reportingQueryKeys.all, 'tasks', 'metrics', delegationIds] as const,
  trends: (filters: ReportingFilters) =>
    [...reportingQueryKeys.all, 'trends', filters] as const,
  locationMetrics: (filters: ReportingFilters) =>
    [...reportingQueryKeys.all, 'locations', 'metrics', filters] as const,

  // ENHANCED: Service history query keys
  serviceHistory: (filters: ReportingFilters) =>
    [...reportingQueryKeys.all, 'services', 'history', filters] as const,
  serviceCosts: (filters: ReportingFilters) =>
    [...reportingQueryKeys.all, 'services', 'costs', filters] as const,
  
  // PHASE 1: New entity analytics query keys
  taskAnalytics: (filters: ReportingFilters) =>
    [...reportingQueryKeys.analytics(), 'tasks', filters] as const,
  vehicleAnalytics: (filters: ReportingFilters) =>
    [...reportingQueryKeys.analytics(), 'vehicles', filters] as const,
  employeeAnalytics: (filters: ReportingFilters) =>
    [...reportingQueryKeys.analytics(), 'employees', filters] as const,
  crossEntityAnalytics: (filters: ReportingFilters) =>
    [...reportingQueryKeys.analytics(), 'cross-entity', filters] as const,
} as const;

/**
 * Hook for fetching delegation analytics with smart caching and error handling
 *
 * Follows Separation of Concerns: Only handles data fetching, not business logic or UI state
 *
 * @param filters - Reporting filters to apply
 * @param options - Additional React Query options
 * @returns Query result with delegation analytics data
 */
export const useDelegationAnalytics = (
  filters: ReportingFilters,
  options?: Omit<
    UseQueryOptions<DelegationAnalytics, Error>,
    'queryKey' | 'queryFn'
  >
) => {
  const queryKey = useMemo(
    () => reportingQueryKeys.delegationAnalytics(filters),
    [filters]
  );

  const queryFn = useCallback(
    () => reportingDataService.getDelegationAnalytics(filters),
    [filters]
  );

  return useQuery({
    queryKey,
    queryFn,
    staleTime: 5 * 60 * 1000, // 5 minutes - analytics data doesn't change frequently
    gcTime: 10 * 60 * 1000, // 10 minutes garbage collection
    retry: 3,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
    refetchOnWindowFocus: false, // Prevent unnecessary refetches on window focus
    refetchOnMount: true,
    ...options,
  });
};

/**
 * Hook for fetching task metrics with optimized caching
 *
 * @param delegationIds - Optional array of delegation IDs to filter by
 * @param options - Additional React Query options
 * @returns Query result with task metrics data
 */
export const useTaskMetrics = (
  delegationIds?: string[],
  options?: Omit<UseQueryOptions<TaskMetrics, Error>, 'queryKey' | 'queryFn'>
) => {
  const queryKey = useMemo(
    () => reportingQueryKeys.taskMetrics(delegationIds),
    [delegationIds]
  );

  const queryFn = useCallback(
    () => reportingDataService.getTaskMetrics(delegationIds),
    [delegationIds]
  );

  return useQuery({
    queryKey,
    queryFn,
    staleTime: 2 * 60 * 1000, // 2 minutes - task metrics change more frequently
    gcTime: 5 * 60 * 1000, // 5 minutes garbage collection
    retry: 3,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
    refetchOnWindowFocus: false,
    ...options,
  });
};

/**
 * Hook for fetching trend data with smart caching
 *
 * @param filters - Reporting filters to apply
 * @param options - Additional React Query options
 * @returns Query result with trend data
 */
export const useTrendData = (
  filters: ReportingFilters,
  options?: Omit<UseQueryOptions<TrendData[], Error>, 'queryKey' | 'queryFn'>
) => {
  const queryKey = useMemo(() => reportingQueryKeys.trends(filters), [filters]);

  const queryFn = useCallback(
    () => reportingDataService.getTrendData(filters),
    [filters]
  );

  return useQuery({
    queryKey,
    queryFn,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 3,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
    refetchOnWindowFocus: false,
    ...options,
  });
};

/**
 * Hook for fetching location metrics
 *
 * @param filters - Reporting filters to apply
 * @param options - Additional React Query options
 * @returns Query result with location metrics data
 */
export const useLocationMetrics = (
  filters: ReportingFilters,
  options?: Omit<
    UseQueryOptions<LocationMetrics[], Error>,
    'queryKey' | 'queryFn'
  >
) => {
  const queryKey = useMemo(
    () => reportingQueryKeys.locationMetrics(filters),
    [filters]
  );

  const queryFn = useCallback(
    () => reportingDataService.getLocationMetrics(filters),
    [filters]
  );

  return useQuery({
    queryKey,
    queryFn,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 3,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
    refetchOnWindowFocus: false,
    ...options,
  });
};

/**
 * Parallel queries hook for fetching multiple reporting data sets simultaneously
 *
 * Follows DRY principle by reusing individual query configurations
 * Optimizes performance by fetching data in parallel rather than sequentially
 *
 * @param filters - Reporting filters to apply
 * @param delegationIds - Optional delegation IDs for task metrics
 * @returns Array of query results for all reporting data
 */
export const useReportingData = (
  filters: ReportingFilters,
  delegationIds?: string[]
) => {
  return useQueries({
    queries: [
      {
        queryKey: reportingQueryKeys.delegationAnalytics(filters),
        queryFn: () => reportingDataService.getDelegationAnalytics(filters),
        staleTime: 5 * 60 * 1000,
        gcTime: 10 * 60 * 1000,
        retry: 3,
        retryDelay: (attemptIndex: number) =>
          Math.min(1000 * 2 ** attemptIndex, 30000),
      },
      {
        queryKey: reportingQueryKeys.taskMetrics(delegationIds),
        queryFn: () => reportingDataService.getTaskMetrics(delegationIds),
        staleTime: 2 * 60 * 1000,
        gcTime: 5 * 60 * 1000,
        retry: 3,
        retryDelay: (attemptIndex: number) =>
          Math.min(1000 * 2 ** attemptIndex, 30000),
      },
      {
        queryKey: reportingQueryKeys.trends(filters),
        queryFn: () => reportingDataService.getTrendData(filters),
        staleTime: 5 * 60 * 1000,
        gcTime: 10 * 60 * 1000,
        retry: 3,
        retryDelay: (attemptIndex: number) =>
          Math.min(1000 * 2 ** attemptIndex, 30000),
      },
      {
        queryKey: reportingQueryKeys.locationMetrics(filters),
        queryFn: () => reportingDataService.getLocationMetrics(filters),
        staleTime: 5 * 60 * 1000,
        gcTime: 10 * 60 * 1000,
        retry: 3,
        retryDelay: (attemptIndex: number) =>
          Math.min(1000 * 2 ** attemptIndex, 30000),
      },
    ],
  });
};

/**
 * Hook for prefetching reporting data to improve user experience
 *
 * @param filters - Reporting filters to prefetch data for
 * @param delegationIds - Optional delegation IDs for task metrics
 */
export const usePrefetchReportingData = () => {
  const queryClient = useQueryClient();

  const prefetchDelegationAnalytics = useCallback(
    (filters: ReportingFilters) => {
      return queryClient.prefetchQuery({
        queryKey: reportingQueryKeys.delegationAnalytics(filters),
        queryFn: () => reportingDataService.getDelegationAnalytics(filters),
        staleTime: 5 * 60 * 1000,
      });
    },
    [queryClient]
  );

  const prefetchTaskMetrics = useCallback(
    (delegationIds?: string[]) => {
      return queryClient.prefetchQuery({
        queryKey: reportingQueryKeys.taskMetrics(delegationIds),
        queryFn: () => reportingDataService.getTaskMetrics(delegationIds),
        staleTime: 2 * 60 * 1000,
      });
    },
    [queryClient]
  );

  return {
    prefetchDelegationAnalytics,
    prefetchTaskMetrics,
  };
};

/**
 * ENHANCED: Hook for fetching service history data
 *
 * @param filters - Reporting filters to apply
 * @param options - Additional React Query options
 * @returns Query result with service history data
 */
export const useServiceHistory = (
  filters: ReportingFilters,
  options?: Omit<
    UseQueryOptions<ServiceHistoryData[], Error>,
    'queryKey' | 'queryFn'
  >
) => {
  const queryKey = useMemo(
    () => reportingQueryKeys.serviceHistory(filters),
    [filters]
  );

  const queryFn = useCallback(
    () => reportingDataService.getServiceHistory(filters),
    [filters]
  );

  return useQuery({
    queryKey,
    queryFn,
    staleTime: 3 * 60 * 1000, // 3 minutes - service history changes moderately
    gcTime: 8 * 60 * 1000, // 8 minutes garbage collection
    retry: 3,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
    refetchOnWindowFocus: false,
    enabled: !!filters.includeServiceHistory, // Only fetch when service history is requested
    ...options,
  });
};

/**
 * PHASE 1 ENHANCEMENT: Hook for fetching task analytics data
 * Following existing patterns for consistency
 *
 * @param filters - Reporting filters to apply
 * @param options - Additional React Query options
 * @returns Query result with task analytics data
 */
export const useTaskAnalytics = (
  filters: ReportingFilters,
  options?: Omit<UseQueryOptions<TaskAnalytics, Error>, 'queryKey' | 'queryFn'>
) => {
  const queryKey = useMemo(
    () => reportingQueryKeys.taskAnalytics(filters),
    [filters]
  );

  const queryFn = useCallback(
    () => reportingDataService.getTaskAnalytics(filters),
    [filters]
  );

  return useQuery({
    queryKey,
    queryFn,
    staleTime: 3 * 60 * 1000, // 3 minutes - task analytics change moderately
    gcTime: 8 * 60 * 1000, // 8 minutes garbage collection
    retry: 3,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
    refetchOnWindowFocus: false,
    enabled: !!filters.includeTaskData, // Only fetch when task data is requested
    ...options,
  });
};

/**
 * PHASE 1 ENHANCEMENT: Hook for fetching vehicle analytics data
 *
 * @param filters - Reporting filters to apply
 * @param options - Additional React Query options
 * @returns Query result with vehicle analytics data
 */
export const useVehicleAnalytics = (
  filters: ReportingFilters,
  options?: Omit<UseQueryOptions<VehicleAnalytics, Error>, 'queryKey' | 'queryFn'>
) => {
  const queryKey = useMemo(
    () => reportingQueryKeys.vehicleAnalytics(filters),
    [filters]
  );

  const queryFn = useCallback(
    () => reportingDataService.getVehicleAnalytics(filters),
    [filters]
  );

  return useQuery({
    queryKey,
    queryFn,
    staleTime: 5 * 60 * 1000, // 5 minutes - vehicle analytics are relatively stable
    gcTime: 10 * 60 * 1000, // 10 minutes garbage collection
    retry: 3,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
    refetchOnWindowFocus: false,
    enabled: !!filters.includeVehicleAnalytics, // Only fetch when vehicle analytics is requested
    ...options,
  });
};

/**
 * PHASE 1 ENHANCEMENT: Hook for fetching employee analytics data
 *
 * @param filters - Reporting filters to apply
 * @param options - Additional React Query options
 * @returns Query result with employee analytics data
 */
export const useEmployeeAnalytics = (
  filters: ReportingFilters,
  options?: Omit<UseQueryOptions<EmployeeAnalytics, Error>, 'queryKey' | 'queryFn'>
) => {
  const queryKey = useMemo(
    () => reportingQueryKeys.employeeAnalytics(filters),
    [filters]
  );

  const queryFn = useCallback(
    () => reportingDataService.getEmployeeAnalytics(filters),
    [filters]
  );

  return useQuery({
    queryKey,
    queryFn,
    staleTime: 4 * 60 * 1000, // 4 minutes - employee analytics change moderately
    gcTime: 9 * 60 * 1000, // 9 minutes garbage collection
    retry: 3,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
    refetchOnWindowFocus: false,
    enabled: !!filters.includeEmployeeMetrics, // Only fetch when employee metrics is requested
    ...options,
  });
};

/**
 * PHASE 1 ENHANCEMENT: Hook for fetching cross-entity analytics data
 *
 * @param filters - Reporting filters to apply
 * @param options - Additional React Query options
 * @returns Query result with cross-entity analytics data
 */
export const useCrossEntityAnalytics = (
  filters: ReportingFilters,
  options?: Omit<UseQueryOptions<CrossEntityAnalytics, Error>, 'queryKey' | 'queryFn'>
) => {
  const queryKey = useMemo(
    () => reportingQueryKeys.crossEntityAnalytics(filters),
    [filters]
  );

  const queryFn = useCallback(
    () => reportingDataService.getCrossEntityAnalytics(filters),
    [filters]
  );

  return useQuery({
    queryKey,
    queryFn,
    staleTime: 6 * 60 * 1000, // 6 minutes - cross-entity analytics are complex and stable
    gcTime: 12 * 60 * 1000, // 12 minutes garbage collection
    retry: 3,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
    refetchOnWindowFocus: false,
    enabled: !!(
      filters.includeCrossEntityCorrelations ||
      filters.includeTaskData ||
      filters.includeEmployeeMetrics ||
      filters.includeVehicleAnalytics
    ), // Only fetch when cross-entity data is requested
    ...options,
  });
};

/**
 * ENHANCED: Hook for fetching service cost summary
 *
 * @param filters - Reporting filters to apply
 * @param options - Additional React Query options
 * @returns Query result with service cost summary
 */
export const useServiceCostSummary = (
  filters: ReportingFilters,
  options?: Omit<
    UseQueryOptions<ServiceCostSummary, Error>,
    'queryKey' | 'queryFn'
  >
) => {
  const queryKey = useMemo(
    () => reportingQueryKeys.serviceCosts(filters),
    [filters]
  );

  const queryFn = useCallback(
    () => reportingDataService.getServiceCostSummary(filters),
    [filters]
  );

  return useQuery({
    queryKey,
    queryFn,
    staleTime: 5 * 60 * 1000, // 5 minutes - cost data is relatively stable
    gcTime: 10 * 60 * 1000, // 10 minutes garbage collection
    retry: 3,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
    refetchOnWindowFocus: false,
    enabled: !!filters.includeServiceHistory, // Only fetch when service history is requested
    ...options,
  });
};
