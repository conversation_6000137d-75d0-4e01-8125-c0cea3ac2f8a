'use client';

import { useState } from 'react';

import type { Gift } from '@/lib/types/domain';

import { DataLoader, SkeletonLoader } from '@/components/ui/loading';
import { cn } from '@/lib/utils';

import { GiftCard } from './GiftCard';
import { GiftFilters } from './GiftFilters';

interface GiftListProps {
  gifts: Gift[];
  isLoading?: boolean;
  error?: Error | null;
  onEdit?: (gift: Gift) => void;
  onDelete?: (gift: Gift) => void;
  onRetry?: () => void;
  showRecipient?: boolean;
  className?: string;
}

interface FilterState {
  search: string;
  recipientId: string;
  occasion: string;
  senderName: string;
  dateRange: {
    from: string;
    to: string;
  };
}

const initialFilterState: FilterState = {
  search: '',
  recipientId: '',
  occasion: '',
  senderName: '',
  dateRange: {
    from: '',
    to: '',
  },
};

export const GiftList: React.FC<GiftListProps> = ({
  gifts,
  isLoading = false,
  error = null,
  onEdit,
  onDelete,
  onRetry,
  showRecipient = true,
  className,
}) => {
  const [filters, setFilters] = useState<FilterState>(initialFilterState);

  // Filter gifts based on current filter state
  const filteredGifts = gifts.filter(gift => {
    // Search filter (item description, sender name, recipient name)
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      const matchesDescription = gift.itemDescription.toLowerCase().includes(searchLower);
      const matchesSender = gift.senderName.toLowerCase().includes(searchLower);
      const matchesRecipient = gift.recipient?.name.toLowerCase().includes(searchLower);
      
      if (!matchesDescription && !matchesSender && !matchesRecipient) {
        return false;
      }
    }

    // Recipient filter
    if (filters.recipientId && gift.recipientId !== filters.recipientId) {
      return false;
    }

    // Occasion filter
    if (filters.occasion && gift.occasion !== filters.occasion) {
      return false;
    }

    // Sender name filter
    if (filters.senderName) {
      const senderLower = filters.senderName.toLowerCase();
      if (!gift.senderName.toLowerCase().includes(senderLower)) {
        return false;
      }
    }

    // Date range filter
    if (filters.dateRange.from || filters.dateRange.to) {
      const giftDate = new Date(gift.dateSent);
      
      if (filters.dateRange.from) {
        const fromDate = new Date(filters.dateRange.from);
        if (giftDate < fromDate) {
          return false;
        }
      }
      
      if (filters.dateRange.to) {
        const toDate = new Date(filters.dateRange.to);
        toDate.setHours(23, 59, 59, 999); // Include the entire end date
        if (giftDate > toDate) {
          return false;
        }
      }
    }

    return true;
  });

  const handleFilterChange = (newFilters: Partial<FilterState>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  const handleClearFilters = () => {
    setFilters(initialFilterState);
  };

  const hasActiveFilters = Object.values(filters).some(value => {
    if (typeof value === 'string') return value !== '';
    if (typeof value === 'object') return value.from !== '' || value.to !== '';
    return false;
  });

  return (
    <div className={cn('space-y-6', className)}>
      {/* Filters */}
      <GiftFilters
        filters={filters}
        onFilterChange={handleFilterChange}
        onClearFilters={handleClearFilters}
        hasActiveFilters={hasActiveFilters}
        gifts={gifts} // Pass all gifts for extracting unique values
      />

      {/* Results Summary */}
      <div className="flex items-center justify-between">
        <div className="text-sm text-muted-foreground">
          {hasActiveFilters ? (
            <>
              Showing {filteredGifts.length} of {gifts.length} gifts
              {filteredGifts.length !== gifts.length && (
                <span className="ml-2 text-primary">
                  ({gifts.length - filteredGifts.length} filtered out)
                </span>
              )}
            </>
          ) : (
            `${gifts.length} gift${gifts.length !== 1 ? 's' : ''} total`
          )}
        </div>
      </div>

      {/* Gift List */}
      <DataLoader
        data={filteredGifts}
        isLoading={isLoading}
        error={error}
        loadingComponent={<SkeletonLoader count={6} variant="card" />}
        emptyComponent={
          <div className="text-center py-12">
            <div className="text-muted-foreground">
              {hasActiveFilters ? (
                <>
                  <p className="text-lg font-medium mb-2">No gifts match your filters</p>
                  <p className="text-sm">Try adjusting your search criteria or clearing filters</p>
                </>
              ) : (
                <>
                  <p className="text-lg font-medium mb-2">No gifts found</p>
                  <p className="text-sm">Start by adding your first gift</p>
                </>
              )}
            </div>
          </div>
        }
        onRetry={onRetry}
      >
        {(gifts) => (
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {gifts.map((gift) => (
              <GiftCard
                key={gift.id}
                gift={gift}
                onEdit={onEdit}
                onDelete={onDelete}
                showRecipient={showRecipient}
              />
            ))}
          </div>
        )}
      </DataLoader>
    </div>
  );
};
