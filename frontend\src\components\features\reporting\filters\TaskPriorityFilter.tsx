/**
 * @file Task Priority Filter Component - Phase 2 Implementation
 * @description Task priority filter component following SOLID principles
 * 
 * SOLID Principles Applied:
 * - SRP: Single responsibility of filtering tasks by priority
 * - OCP: Open for extension via additional filter options
 * - DIP: Depends on existing filter store and component abstractions
 * 
 * Architecture Compliance:
 * - Follows existing filter component patterns
 * - Integrates with established filter store
 * - Uses consistent styling and behavior
 * - Maintains component composition principles
 */

'use client';

import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { TaskPriorityPrisma } from '@/lib/types/domain';
import { useReportingFilters, useReportingFiltersActions } from '../data/stores';
import { Flag, Filter, X, Al<PERSON><PERSON>riangle, Minus, ArrowUp } from 'lucide-react';

interface TaskPriorityFilterProps {
  className?: string;
  showLabel?: boolean;
  variant?: 'dropdown' | 'inline';
}

/**
 * @component TaskPriorityFilter
 * @description Task priority filter following existing filter patterns
 *
 * Responsibilities:
 * - Provide task priority filtering interface
 * - Integrate with existing filter store
 * - Follow established filter component patterns
 * - Maintain consistent styling and behavior
 *
 * SOLID Principles Applied:
 * - SRP: Single responsibility of task priority filtering
 * - OCP: Open for extension via props configuration
 * - DIP: Depends on filter store abstractions
 */
export const TaskPriorityFilter: React.FC<TaskPriorityFilterProps> = ({
  className = '',
  showLabel = true,
  variant = 'dropdown',
}) => {
  const filters = useReportingFilters();
  const { setFilters } = useReportingFiltersActions();

  // Available task priorities with display information
  const taskPriorities: Array<{
    value: TaskPriorityPrisma;
    label: string;
    color: string;
    icon: React.ReactNode;
    description: string;
    order: number;
  }> = [
    {
      value: 'High' as TaskPriorityPrisma,
      label: 'High Priority',
      color: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
      icon: <AlertTriangle className="h-3 w-3" />,
      description: 'Urgent tasks requiring immediate attention',
      order: 3,
    },
    {
      value: 'Medium' as TaskPriorityPrisma,
      label: 'Medium Priority',
      color: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
      icon: <Minus className="h-3 w-3" />,
      description: 'Standard priority tasks',
      order: 2,
    },
    {
      value: 'Low' as TaskPriorityPrisma,
      label: 'Low Priority',
      color: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
      icon: <ArrowUp className="h-3 w-3 rotate-180" />,
      description: 'Tasks that can be completed when time permits',
      order: 1,
    },
  ].sort((a, b) => b.order - a.order); // Sort by priority order (High to Low)

  // Get current selected priorities
  const selectedPriorities = filters.taskPriority || [];

  // Handle priority selection
  const handlePriorityToggle = (priority: TaskPriorityPrisma) => {
    const currentPriorities = selectedPriorities;
    const isSelected = currentPriorities.includes(priority);
    
    let newPriorities: TaskPriorityPrisma[];
    if (isSelected) {
      newPriorities = currentPriorities.filter(p => p !== priority);
    } else {
      newPriorities = [...currentPriorities, priority];
    }
    
    setFilters({
      taskPriority: newPriorities,
    });
  };

  // Clear all priority filters
  const handleClearAll = () => {
    setFilters({
      taskPriority: [],
    });
  };

  // Select all priorities
  const handleSelectAll = () => {
    setFilters({
      taskPriority: taskPriorities.map(p => p.value),
    });
  };

  // Render inline variant
  if (variant === 'inline') {
    return (
      <div className={`space-y-3 ${className}`}>
        {showLabel && (
          <div className="flex items-center justify-between">
            <label className="text-sm font-medium">Task Priority</label>
            {selectedPriorities.length > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleClearAll}
                className="h-6 px-2 text-xs"
              >
                <X className="h-3 w-3 mr-1" />
                Clear
              </Button>
            )}
          </div>
        )}
        
        <div className="space-y-2">
          {taskPriorities.map((priority) => {
            const isSelected = selectedPriorities.includes(priority.value);
            
            return (
              <div
                key={priority.value}
                className="flex items-center space-x-3 p-2 rounded-lg hover:bg-muted/50 cursor-pointer"
                onClick={() => handlePriorityToggle(priority.value)}
              >
                <Checkbox
                  checked={isSelected}
                  onChange={() => handlePriorityToggle(priority.value)}
                />
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2">
                    <Badge className={priority.color}>
                      {priority.icon}
                      <span className="ml-1">{priority.value}</span>
                    </Badge>
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">
                    {priority.description}
                  </p>
                </div>
              </div>
            );
          })}
        </div>
        
        {selectedPriorities.length > 0 && (
          <div className="pt-2 border-t">
            <p className="text-xs text-muted-foreground">
              {selectedPriorities.length} priorit{selectedPriorities.length !== 1 ? 'ies' : 'y'} selected
            </p>
          </div>
        )}
      </div>
    );
  }

  // Render dropdown variant (default)
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" className={className}>
          <Flag className="mr-2 h-4 w-4" />
          Priority
          {selectedPriorities.length > 0 && (
            <Badge variant="secondary" className="ml-2 h-5 px-1.5 text-xs">
              {selectedPriorities.length}
            </Badge>
          )}
        </Button>
      </DropdownMenuTrigger>
      
      <DropdownMenuContent className="w-64" align="start">
        <DropdownMenuLabel className="flex items-center justify-between">
          <span className="flex items-center gap-2">
            <Flag className="h-4 w-4" />
            Task Priority Filter
          </span>
          {selectedPriorities.length > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClearAll}
              className="h-6 px-2 text-xs"
            >
              Clear
            </Button>
          )}
        </DropdownMenuLabel>
        
        <DropdownMenuSeparator />
        
        <div className="p-2">
          <div className="flex gap-2 mb-3">
            <Button
              variant="outline"
              size="sm"
              onClick={handleSelectAll}
              className="flex-1 h-7 text-xs"
            >
              Select All
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleClearAll}
              className="flex-1 h-7 text-xs"
            >
              Clear All
            </Button>
          </div>
          
          <div className="space-y-2">
            {taskPriorities.map((priority) => {
              const isSelected = selectedPriorities.includes(priority.value);
              
              return (
                <DropdownMenuItem
                  key={priority.value}
                  className="flex items-center space-x-3 cursor-pointer"
                  onClick={() => handlePriorityToggle(priority.value)}
                >
                  <Checkbox
                    checked={isSelected}
                    onChange={() => handlePriorityToggle(priority.value)}
                  />
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2">
                      <Badge className={priority.color}>
                        {priority.icon}
                        <span className="ml-1">{priority.value}</span>
                      </Badge>
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">
                      {priority.description}
                    </p>
                  </div>
                </DropdownMenuItem>
              );
            })}
          </div>
          
          {selectedPriorities.length > 0 && (
            <>
              <DropdownMenuSeparator />
              <div className="p-2 text-xs text-muted-foreground">
                {selectedPriorities.length} priorit{selectedPriorities.length !== 1 ? 'ies' : 'y'} selected
              </div>
            </>
          )}
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};