'use client';

import React from 'react';

import { AuthProvider } from '../../contexts/AuthContext';
import { ProtectedRoute } from '../../components/auth';
import { UserProfile } from '../../components/user/UserProfile';

/**
 * EMERGENCY SECURITY TEST PAGE
 *
 * This page demonstrates the authentication components working together.
 * It shows the login form when not authenticated and user profile when authenticated.
 */
export default function AuthTestPage() {
  return (
    <AuthProvider>
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="container mx-auto px-4">
          <div className="mb-8 text-center">
            <h1 className="mb-2 text-3xl font-bold text-gray-900">
              🚨 Emergency Security Test
            </h1>
            <p className="text-gray-600">
              Testing Supabase Authentication Implementation
            </p>
          </div>

          <ProtectedRoute>
            <div className="mx-auto max-w-2xl space-y-6">
              <div className="rounded-lg bg-white p-6 shadow-md">
                <h2 className="mb-4 text-xl font-semibold">
                  ✅ Authentication Successful
                </h2>
                <p className="mb-4 text-gray-600">
                  You are now authenticated and can access protected content.
                </p>

                {/* User Profile Component */}
                <UserProfile variant="card" />
              </div>

              <div className="rounded-lg border border-green-200 bg-green-50 p-4">
                <h3 className="mb-2 font-medium text-green-800">
                  🎉 Emergency Security Implementation Status
                </h3>
                <ul className="space-y-1 text-sm text-green-700">
                  <li>✅ Supabase client configured</li>
                  <li>✅ Authentication hook implemented</li>
                  <li>✅ Login form functional</li>
                  <li>✅ Protected routes working</li>
                  <li>✅ User profile display active</li>
                  <li>✅ Session management operational</li>
                </ul>
              </div>
            </div>
          </ProtectedRoute>
        </div>
      </div>
    </AuthProvider>
  );
}
