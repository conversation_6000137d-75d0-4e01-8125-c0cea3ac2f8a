/**
 * @file useDelegations Hook
 * @description A hook for fetching paginated delegation data.
 */

import { useApiQuery } from '@/lib/hooks';
import { reportingDataService } from '../services/ReportingDataService';
import type { ReportingFilters, PaginatedDelegationsResponse } from '../types';
import { useQuery } from '@tanstack/react-query';
import { useMemo } from 'react';
import type { DelegationStatusPrisma } from '@/lib/types/domain';

/**
 * Interface for delegation data returned by analytics endpoint
 * This matches the transformed structure from the backend
 */
interface AnalyticsDelegation {
  id: number;
  title: string; // Mapped from eventName
  assignedTo: string; // Joined employee names
  location: string;
  status: DelegationStatusPrisma;
  createdAt: string;
  completedAt?: string;
  actualHours?: number;
  estimatedHours: number;
  dueDate: string;
  progress: number;
  priority: string;
  assignedBy: string;
}

/**
 * Fetches paginated delegations using the reporting service.
 * FIXED: Use analytics endpoint since /delegations endpoint doesn't exist
 *
 * @param {ReportingFilters} filters - The filters to apply to the query.
 * @param {{ page: number; pageSize: number }} pagination - The pagination parameters.
 * @param {object} [options] - Optional TanStack Query options.
 * @returns The result of the TanStack Query.
 */
export const useDelegations = (
  filters: ReportingFilters,
  pagination: { page: number; pageSize: number },
  options?: object
) => {
  // FIXED: Use analytics endpoint which includes delegations data
  const analyticsQuery = useApiQuery(
    ['delegations', filters, pagination], // Keep same query key for consistency
    () => reportingDataService.getDelegationAnalytics(filters),
    {
      placeholderData: prev => prev, // Smooth pagination experience
      showErrorToast: true, // Show error toasts for debugging
      ...options,
    }
  );

  // Transform analytics response to match expected PaginatedDelegationsResponse format
  const transformedData = useMemo(():
    | PaginatedDelegationsResponse
    | undefined => {
    if (!analyticsQuery.data?.delegations) {
      return undefined;
    }

    // Type assertion since we know the backend returns AnalyticsDelegation structure
    const allDelegations = analyticsQuery.data
      .delegations as unknown as AnalyticsDelegation[];
    const startIndex = (pagination.page - 1) * pagination.pageSize;
    const endIndex = startIndex + pagination.pageSize;
    const paginatedDelegations = allDelegations.slice(startIndex, endIndex);

    return {
      data: paginatedDelegations.map(delegation => ({
        id: delegation.id,
        delegationId: delegation.id.toString(),
        customerName: delegation.title, // Backend returns 'title' field (mapped from eventName)
        vehicleModel: 'N/A', // TODO: Add vehicle info to analytics response
        licensePlate: 'N/A', // TODO: Add license plate to analytics response
        status: delegation.status,
        assignedEmployee: delegation.assignedTo, // Backend returns 'assignedTo' field
        location: delegation.location,
        createdAt: delegation.createdAt,
        completedAt: delegation.completedAt || null, // Fix type: convert undefined to null
      })),
      meta: {
        total: allDelegations.length,
        page: pagination.page,
        pageSize: pagination.pageSize,
        totalPages: Math.ceil(allDelegations.length / pagination.pageSize),
      },
    };
  }, [analyticsQuery.data?.delegations, pagination.page, pagination.pageSize]);

  return {
    ...analyticsQuery,
    data: transformedData,
  };
};
