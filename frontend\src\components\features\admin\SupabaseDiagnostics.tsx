'use client';

import {
  Activity,
  AlertTriangle,
  ArrowRight,
  Database,
  HardDrive,
  Monitor,
} from 'lucide-react';
import Link from 'next/link';

import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

import { CacheStatus } from './CacheStatus';
import { ErrorLog } from './ErrorLog';
import { ErrorLogDebug } from './ErrorLogDebug';

export function SupabaseDiagnostics() {
  return (
    <Card className="border-none shadow-none">
      <CardHeader className="px-0 pt-0">
        <CardTitle className="text-2xl font-bold">
          Supabase Diagnostics
        </CardTitle>
        <CardDescription>
          Monitor and troubleshoot your Supabase database connection
        </CardDescription>
      </CardHeader>
      <CardContent className="w-full max-w-full overflow-hidden px-0">
        {/* Migration Notice */}
        <Alert className="mb-6">
          <Monitor className="h-4 w-4" />
          <AlertTitle>Enhanced Reliability Dashboard Available</AlertTitle>
          <AlertDescription>
            System health and performance monitoring has been moved to the new
            Reliability Dashboard with real-time updates, advanced metrics, and
            comprehensive monitoring capabilities.
            <div className="mt-3">
              <Button asChild variant="outline" size="sm">
                <Link href="/reliability" className="flex items-center gap-2">
                  <Monitor className="h-4 w-4" />
                  Go to Reliability Dashboard
                  <ArrowRight className="h-4 w-4" />
                </Link>
              </Button>
            </div>
          </AlertDescription>
        </Alert>

        <Tabs
          className="w-full max-w-full overflow-hidden"
          defaultValue="errors"
        >
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger className="flex items-center" value="errors">
              <AlertTriangle className="mr-2 size-4" />
              <span className="hidden sm:inline">Error Logs</span>
            </TabsTrigger>
            <TabsTrigger className="flex items-center" value="cache">
              <HardDrive className="mr-2 size-4" />
              <span className="hidden sm:inline">Cache Status</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent className="mt-4" value="errors">
            <div className="space-y-6">
              <ErrorLog />
              <ErrorLogDebug />
            </div>
          </TabsContent>

          <TabsContent className="mt-4" value="cache">
            <CacheStatus />
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
