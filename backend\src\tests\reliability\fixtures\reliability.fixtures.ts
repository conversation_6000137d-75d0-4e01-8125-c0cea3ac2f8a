// reliability.fixtures.ts - Test fixtures for reliability components

/**
 * Circuit Breaker Configuration Fixtures
 */
export const circuitBreakerFixtures = {
  customConfig: {
    errorThresholdPercentage: 75,
    name: 'custom-service',
    resetTimeout: 20000,
    timeout: 8000,
  },

  databaseConfig: {
    errorThresholdPercentage: 50,
    resetTimeout: 30000,
    serviceType: 'database' as const,
    timeout: 10000,
  },

  defaultConfig: {
    errorThresholdPercentage: 50,
    group: 'test-group',
    name: 'test-service',
    resetTimeout: 30000,
    rollingCountBuckets: 10,
    rollingCountTimeout: 10000,
    timeout: 10000,
  },

  externalApiConfig: {
    errorThresholdPercentage: 60,
    resetTimeout: 60000,
    serviceType: 'external_api' as const,
    timeout: 15000,
  },

  redisConfig: {
    errorThresholdPercentage: 40,
    resetTimeout: 15000,
    serviceType: 'redis' as const,
    timeout: 5000,
  },

  supabaseConfig: {
    errorThresholdPercentage: 50,
    resetTimeout: 45000,
    serviceType: 'supabase' as const,
    timeout: 12000,
  },
};

/**
 * Request Deduplication Configuration Fixtures
 */
export const deduplicationFixtures = {
  adminConfig: {
    enabled: true,
    includeBody: true,
    includeHeaders: true,
    includeQuery: true,
    includeUser: true,
    ttl: 300, // 5 minutes
  },

  apiConfig: {
    enabled: true,
    includeBody: true,
    includeHeaders: true,
    includeQuery: true,
    includeUser: true,
    ttl: 60, // 1 minute
  },

  disabledConfig: {
    enabled: false,
    includeBody: true,
    includeHeaders: false,
    includeQuery: true,
    includeUser: false,
    ttl: 60,
  },

  idempotentConfig: {
    enabled: true,
    includeBody: true,
    includeHeaders: true,
    includeQuery: true,
    includeUser: true,
    ttl: 600, // 10 minutes
  },

  performanceConfig: {
    enabled: true,
    includeBody: false,
    includeHeaders: false,
    includeQuery: true,
    includeUser: false,
    ttl: 30, // 30 seconds
  },
};

/**
 * Health Check Response Fixtures
 */
export const healthFixtures = {
  comprehensiveHealthy: {
    checks: {
      businessLogic: {
        status: 'healthy' as const,
      },
      cache: {
        details: { redis: { status: 'connected' } },
        status: 'healthy' as const,
      },
      circuitBreakers: {
        details: { openBreakers: 0 },
        status: 'healthy' as const,
      },
      database: {
        responseTime: 45,
        status: 'healthy' as const,
      },
      supabase: {
        responseTime: 120,
        status: 'healthy' as const,
      },
      systemResources: {
        details: { memory: { usagePercent: 65 } },
        status: 'healthy' as const,
      },
    },
    environment: 'test',
    status: 'healthy' as const,
    summary: {
      degradedChecks: 0,
      healthyChecks: 6,
      totalChecks: 6,
      unhealthyChecks: 0,
    },
    timestamp: '2024-01-15T10:30:00.000Z',
    uptime: 3600,
    version: '1.0.0',
  },

  degradedCircuitBreakers: {
    details: {
      breakerStates: [
        { failures: 0, name: 'database', state: 'CLOSED' },
        { failures: 0, name: 'supabase', state: 'CLOSED' },
        { failures: 8, name: 'redis', state: 'OPEN' },
        { failures: 2, name: 'external-api', state: 'CLOSED' },
      ],
      closedBreakers: 3,
      halfOpenBreakers: 0,
      openBreakerNames: ['redis'],
      openBreakers: 1,
      totalBreakers: 4,
    },
    message: 'Some circuit breakers are open',
    status: 'degraded' as const,
    timestamp: '2024-01-15T10:30:00.000Z',
  },

  degradedDatabase: {
    details: {
      connectionPool: {
        active: 12,
        idle: 3,
        total: 15,
      },
      lastQuery: '2024-01-15T10:29:55.000Z',
    },
    message: 'Database response time is slow',
    responseTime: 3500,
    status: 'degraded' as const,
    timestamp: '2024-01-15T10:30:00.000Z',
  },

  healthyCircuitBreakers: {
    details: {
      breakerStates: [
        { failures: 0, name: 'database', state: 'CLOSED' },
        { failures: 0, name: 'supabase', state: 'CLOSED' },
        { failures: 0, name: 'redis', state: 'CLOSED' },
        { failures: 0, name: 'external-api', state: 'CLOSED' },
      ],
      closedBreakers: 4,
      halfOpenBreakers: 0,
      openBreakers: 0,
      totalBreakers: 4,
    },
    status: 'healthy' as const,
    timestamp: '2024-01-15T10:30:00.000Z',
  },

  healthyDatabase: {
    details: {
      connectionPool: {
        active: 5,
        idle: 10,
        total: 15,
      },
      lastQuery: '2024-01-15T10:29:55.000Z',
    },
    responseTime: 45,
    status: 'healthy' as const,
    timestamp: '2024-01-15T10:30:00.000Z',
  },

  healthySupabase: {
    details: {
      endpoint: 'https://test.supabase.co',
      lastCheck: '2024-01-15T10:29:58.000Z',
      region: 'us-east-1',
    },
    responseTime: 120,
    status: 'healthy' as const,
    timestamp: '2024-01-15T10:30:00.000Z',
  },

  unhealthyDatabase: {
    details: {
      error: 'Connection timeout',
      lastSuccessfulConnection: '2024-01-15T10:25:00.000Z',
    },
    message: 'Database connection failed',
    responseTime: 0,
    status: 'unhealthy' as const,
    timestamp: '2024-01-15T10:30:00.000Z',
  },
};

/**
 * Metrics Data Fixtures
 */
export const metricsFixtures = {
  circuitBreakerMetrics: {
    help: 'Total circuit breaker requests',
    name: 'workhub_circuit_breaker_requests_total',
    type: 'counter',
    values: [
      {
        labels: { service_name: 'main-db', service_type: 'database', status: 'success' },
        value: 1250,
      },
      {
        labels: { service_name: 'cache', service_type: 'redis', status: 'failure' },
        value: 8,
      },
    ],
  },

  databaseMetrics: {
    help: 'Total number of database operations',
    name: 'workhub_database_operations_total',
    type: 'counter',
    values: [
      {
        labels: { operation: 'SELECT', status: 'success', table: 'users' },
        value: 150,
      },
      {
        labels: { operation: 'INSERT', status: 'success', table: 'tasks' },
        value: 45,
      },
    ],
  },

  deduplicationMetrics: {
    cacheHits: 750,
    cacheMisses: 250,
    errors: 5,
    hitRate: 75.0,
    lastReset: '2024-01-15T09:00:00.000Z',
    totalRequests: 1000,
  },

  httpRequestMetrics: {
    help: 'Duration of HTTP requests in seconds',
    name: 'workhub_http_request_duration_seconds',
    type: 'histogram',
    values: [
      {
        labels: { method: 'GET', route: '/api/test', status_code: '200', user_role: 'admin' },
        value: 0.125,
      },
      {
        labels: { method: 'POST', route: '/api/users', status_code: '201', user_role: 'user' },
        value: 0.25,
      },
    ],
  },

  systemMetrics: {
    connections: {
      active: 25,
      total: 100,
    },
    cpu: {
      loadAverage: [0.8, 0.9, 1.1],
      usage: 45.2,
    },
    memory: {
      free: 3221225472, // 3GB
      total: 8589934592, // 8GB
      usagePercent: 62.5,
      used: 5368709120, // 5GB
    },
  },
};

/**
 * Request/Response Fixtures
 */
export const requestFixtures = {
  errorResponse: {
    body: {
      code: 'DB_CONNECTION_ERROR',
      error: 'Internal server error',
      message: 'Database connection failed',
      timestamp: '2024-01-15T10:30:00.000Z',
    },
    headers: {
      'content-type': 'application/json',
      'x-error-id': 'err-789',
    },
    statusCode: 500,
  },

  getRequest: {
    body: {},
    headers: {
      accept: 'application/json',
      authorization: 'Bearer test-token',
      'user-agent': 'test-client/1.0',
    },
    method: 'GET',
    path: '/api/users',
    query: {
      limit: '10',
      page: '1',
    },
    url: '/api/users?page=1&limit=10',
    user: {
      email: '<EMAIL>',
      id: 'user-123',
      role: 'admin',
    },
  },

  postRequest: {
    body: {
      email: '<EMAIL>',
      name: 'John Doe',
      role: 'user',
    },
    headers: {
      authorization: 'Bearer test-token',
      'content-type': 'application/json',
      'user-agent': 'test-client/1.0',
    },
    method: 'POST',
    path: '/api/users',
    query: {},
    url: '/api/users',
    user: {
      email: '<EMAIL>',
      id: 'admin-456',
      role: 'admin',
    },
  },

  successResponse: {
    body: {
      data: {
        email: '<EMAIL>',
        id: 'user-123',
        name: 'John Doe',
      },
      meta: {
        timestamp: '2024-01-15T10:30:00.000Z',
        version: '1.0',
      },
    },
    headers: {
      'content-type': 'application/json',
      'x-cache': 'MISS',
      'x-response-time': '125ms',
    },
    statusCode: 200,
  },
};

/**
 * Performance Test Data Fixtures
 */
export const performanceFixtures = {
  baselineStats: {
    count: 100,
    max: 45.8,
    mean: 15.3,
    median: 14.1,
    min: 5.2,
    p95: 28.7,
    p99: 38.2,
  },

  cacheHitStats: {
    count: 50,
    max: 15.6,
    mean: 6.8,
    median: 6.2,
    min: 2.1,
    p95: 12.3,
    p99: 14.8,
  },

  cacheMissStats: {
    count: 10,
    max: 89.2,
    mean: 35.7,
    median: 32.1,
    min: 18.4,
    p95: 67.8,
    p99: 84.5,
  },

  middlewareStats: {
    count: 100,
    max: 67.3,
    mean: 22.8,
    median: 20.4,
    min: 8.5,
    p95: 42.1,
    p99: 58.9,
  },

  performanceTargets: {
    cacheHitRate: 80, // > 80% cache hit rate
    errorRate: 1, // < 1% error rate
    responseTime: {
      max: 5000, // Max under 5 seconds
      mean: 1000, // Average under 1 second
      p95: 2000, // 95% under 2 seconds
    },
    uptime: 99.9, // > 99.9% uptime
  },
};

/**
 * Error Scenario Fixtures
 */
export const errorFixtures = {
  circuitBreakerError: Object.assign(new Error('Circuit breaker is OPEN'), {
    code: 'EOPENBREAKER',
  }),
  databaseError: new Error('Database connection timeout'),
  networkError: new Error('Network unreachable'),
  redisError: new Error('Redis connection lost'),
  supabaseError: new Error('Supabase authentication failed'),
  timeoutError: new Error('Request timeout'),
  validationError: new Error('Invalid request data'),
};

/**
 * Test Utility Functions
 */
export const fixtureUtils = {
  /**
   * Create a deep copy of a fixture to avoid mutation
   */
  clone<T>(fixture: T): T {
    return JSON.parse(JSON.stringify(fixture));
  },

  /**
   * Generate test request with random variations
   */
  generateRandomRequest(baseRequest: any) {
    const timestamp = Date.now();
    return this.merge(baseRequest, {
      headers: {
        ...baseRequest.headers,
        'x-request-id': `req-${timestamp}`,
        'x-timestamp': timestamp.toString(),
      },
      query: {
        ...baseRequest.query,
        _t: timestamp.toString(),
      },
    });
  },

  /**
   * Generate random performance stats within realistic ranges
   */
  generateRandomStats(baseStats: any, variance = 0.2) {
    const vary = (value: number) => value * (1 + (Math.random() - 0.5) * variance);

    return {
      ...baseStats,
      max: vary(baseStats.max),
      mean: vary(baseStats.mean),
      median: vary(baseStats.median),
      min: vary(baseStats.min),
      p95: vary(baseStats.p95),
      p99: vary(baseStats.p99),
    };
  },

  /**
   * Merge fixtures with custom overrides
   */
  merge<T>(base: T, overrides: Partial<T>): T {
    return { ...this.clone(base), ...overrides };
  },
};
