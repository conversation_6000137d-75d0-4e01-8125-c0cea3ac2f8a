'use client';

import * as React from 'react';
import { ColumnDef } from '@tanstack/react-table';

import { DataTable } from '@/components/ui/tables/DataTable';
import {
  createDateColumn,
  createStatusColumn,
  createActionsColumn,
  createTextColumn,
  createSelectionColumn,
  statusConfigs,
} from '@/components/ui/tables/columnHelpers';
import { ViewMode, DashboardLayout } from '@/components/dashboard/types';
import { TaskCardView } from './TaskCardView'; // Assuming TaskCardView is in the same directory

import type { Task } from '@/lib/types/domain';

interface TaskViewRendererProps {
  tasks: Task[];
  viewMode: ViewMode;
  layout: DashboardLayout;
  // These props are part of the generic ViewRenderer type,
  // though not all might be directly used by DataTable or TaskCardView
  selectedItems: Set<string>;
  onItemSelect: (id: string) => void;
}

// Define columns for the Task DataTable
const taskColumns: ColumnDef<Task>[] = [
  createSelectionColumn<Task>(),
  createTextColumn('description', 'Task Description', { maxLength: 50 }),
  createStatusColumn('status', 'Status', statusConfigs.task),
  createDateColumn('deadline', 'Deadline'),
  {
    accessorKey: 'assigneeName', // A dummy accessorKey, as we'll use a custom cell
    header: 'Assignee',
    cell: ({ row }) => {
      const task = row.original;
      const assigneeName =
        task.staffEmployee?.fullName || task.driverEmployee?.fullName || '-';
      return <span>{assigneeName}</span>;
    },
  },
  createActionsColumn<Task>({
    viewHref: task => `/tasks/${task.id}`,
    editHref: task => `/tasks/${task.id}/edit`,
    // onDelete: (task) => console.log('Delete task:', task.id), // Example delete action
  }),
];

export function TaskViewRenderer({
  tasks,
  viewMode,
  layout,
  selectedItems, // Not directly used by DataTable or TaskCardView, but part of ViewRenderer type
  onItemSelect, // Not directly used by DataTable or TaskCardView, but part of ViewRenderer type
}: TaskViewRendererProps) {
  switch (viewMode) {
    case 'table':
      return (
        <DataTable
          data={tasks}
          columns={taskColumns}
          enableRowSelection={true} // Assuming row selection is desired for table view
          searchColumn="description" // Example search column
          emptyMessage="No tasks found."
        />
      );
    case 'cards':
      return <TaskCardView tasks={tasks} />;
    // Add other cases for 'calendar', 'list', 'grid' if needed in the future
    default:
      return (
        <div className="text-center text-muted-foreground py-8">
          Unsupported view mode: {viewMode}.
        </div>
      );
  }
}
