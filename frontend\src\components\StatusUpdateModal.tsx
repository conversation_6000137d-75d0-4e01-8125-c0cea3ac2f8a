import React, { useState } from 'react';

import type { DelegationStatusPrisma } from '../lib/types/domain'; // Adjust path as needed

import { formatDelegationStatusForDisplay } from '../lib/utils/formattingUtils';
import { Button } from './ui/button'; // Assuming shadcn/ui button component
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from './ui/dialog'; // Assuming shadcn/ui dialog components
import { Input } from './ui/input'; // Assuming shadcn/ui input component
import { Label } from './ui/label'; // Assuming shadcn/ui label component
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from './ui/select'; // Add Select components

interface StatusUpdateModalProps {
  currentStatus: DelegationStatusPrisma;
  delegationId: string;
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (status: DelegationStatusPrisma, reason: string) => void;
}

const StatusUpdateModal: React.FC<StatusUpdateModalProps> = ({
  currentStatus,
  delegationId,
  isOpen,
  onClose,
  onConfirm,
}) => {
  const [newStatus, setNewStatus] =
    useState<DelegationStatusPrisma>(currentStatus);
  const [reason, setReason] = useState<string>('');
  const [error, setError] = useState<null | string>(null);

  const handleConfirm = () => {
    if (!reason.trim()) {
      setError('Reason for status change is required.');
      return;
    }
    if (newStatus === currentStatus) {
      setError('Please select a different status.');
      return;
    }
    setError(null);
    onConfirm(newStatus, reason);
  };

  // Available status options
  const statusOptions: DelegationStatusPrisma[] = [
    'Planned',
    'Confirmed',
    'In_Progress',
    'Completed',
    'Cancelled',
    'No_details',
  ];

  return (
    <Dialog onOpenChange={onClose} open={isOpen}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Update Delegation Status</DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label className="text-right" htmlFor="current-status">
              Current Status
            </Label>
            <Input
              className="col-span-3"
              id="current-status"
              readOnly
              value={formatDelegationStatusForDisplay(currentStatus)}
            />
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label className="text-right" htmlFor="new-status">
              New Status
            </Label>
            <Select
              onValueChange={(value: DelegationStatusPrisma) =>
                setNewStatus(value)
              }
              value={newStatus}
            >
              <SelectTrigger className="col-span-3">
                <SelectValue placeholder="Select new status" />
              </SelectTrigger>
              <SelectContent>
                {statusOptions.map(status => (
                  <SelectItem
                    disabled={status === currentStatus}
                    key={status}
                    value={status}
                  >
                    {formatDelegationStatusForDisplay(status)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label className="text-right" htmlFor="reason">
              Reason
            </Label>
            <Input
              className="col-span-3"
              id="reason"
              onChange={e => setReason(e.target.value)}
              placeholder="Enter reason for status change"
              value={reason}
            />
          </div>
          {error && (
            <p className="col-span-4 text-center text-sm text-red-500">
              {error}
            </p>
          )}
        </div>
        <DialogFooter>
          <Button onClick={onClose} variant="outline">
            Cancel
          </Button>
          <Button onClick={handleConfirm}>Confirm</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default StatusUpdateModal;
