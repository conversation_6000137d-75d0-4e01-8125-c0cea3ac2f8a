/**
 * @file Theme debugging component to help diagnose theme issues
 * @module components/debug/ThemeDebug
 */

'use client';

import { Bug, Monitor, Moon, Sun } from 'lucide-react';
import { useTheme as useNextTheme } from 'next-themes';
import React from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useTheme } from '@/hooks/ui/useTheme';

/**
 * Theme debugging component
 */
export const ThemeDebug: React.FC = () => {
  const {
    resolvedTheme,
    setTheme: setNextTheme,
    systemTheme,
    theme: nextTheme,
  } = useNextTheme();
  const { currentTheme, isDark, setTheme: setZustandTheme } = useTheme();
  const [mounted, setMounted] = React.useState(false);

  React.useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return (
      <Card className="w-full max-w-2xl">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bug className="size-5" />
            Theme Debug (Loading...)
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p>Loading theme information...</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Bug className="size-5" />
          Theme Debug Information
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Current State */}
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <h3 className="font-medium">Next-themes State</h3>
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span>theme:</span>
                <Badge variant="outline">{nextTheme || 'undefined'}</Badge>
              </div>
              <div className="flex justify-between">
                <span>systemTheme:</span>
                <Badge variant="outline">{systemTheme || 'undefined'}</Badge>
              </div>
              <div className="flex justify-between">
                <span>resolvedTheme:</span>
                <Badge variant="outline">{resolvedTheme || 'undefined'}</Badge>
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <h3 className="font-medium">Zustand State</h3>
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span>currentTheme:</span>
                <Badge variant="outline">{currentTheme}</Badge>
              </div>
              <div className="flex justify-between">
                <span>isDark:</span>
                <Badge variant="outline">{isDark ? 'true' : 'false'}</Badge>
              </div>
            </div>
          </div>
        </div>

        {/* Visual Indicators */}
        <div className="space-y-2">
          <h3 className="font-medium">Visual State</h3>
          <div className="flex gap-4">
            <div className="flex items-center gap-2">
              <div className="size-4 rounded border bg-background" />
              <span className="text-sm">Background</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="size-4 rounded bg-foreground" />
              <span className="text-sm">Foreground</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="size-4 rounded bg-primary" />
              <span className="text-sm">Primary</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="size-4 rounded bg-secondary" />
              <span className="text-sm">Secondary</span>
            </div>
          </div>
        </div>

        {/* Test Controls */}
        <div className="space-y-4">
          <h3 className="font-medium">Test Controls</h3>

          <div className="space-y-2">
            <h4 className="text-sm font-medium">Next-themes Controls</h4>
            <div className="flex gap-2">
              <Button
                onClick={() => setNextTheme('light')}
                size="sm"
                variant={nextTheme === 'light' ? 'default' : 'outline'}
              >
                <Sun className="mr-1 size-4" />
                Light
              </Button>
              <Button
                onClick={() => setNextTheme('dark')}
                size="sm"
                variant={nextTheme === 'dark' ? 'default' : 'outline'}
              >
                <Moon className="mr-1 size-4" />
                Dark
              </Button>
              <Button
                onClick={() => setNextTheme('system')}
                size="sm"
                variant={nextTheme === 'system' ? 'default' : 'outline'}
              >
                <Monitor className="mr-1 size-4" />
                System
              </Button>
            </div>
          </div>

          <div className="space-y-2">
            <h4 className="text-sm font-medium">Zustand Controls</h4>
            <div className="flex gap-2">
              <Button
                onClick={() => setZustandTheme('light')}
                size="sm"
                variant={currentTheme === 'light' ? 'default' : 'outline'}
              >
                <Sun className="mr-1 size-4" />
                Light
              </Button>
              <Button
                onClick={() => setZustandTheme('dark')}
                size="sm"
                variant={currentTheme === 'dark' ? 'default' : 'outline'}
              >
                <Moon className="mr-1 size-4" />
                Dark
              </Button>
            </div>
          </div>
        </div>

        {/* Sync Status */}
        <div className="space-y-2">
          <h3 className="font-medium">Synchronization Status</h3>
          <div className="space-y-1 text-sm">
            <div className="flex justify-between">
              <span>Themes match:</span>
              <Badge
                variant={
                  resolvedTheme === currentTheme ? 'default' : 'destructive'
                }
              >
                {resolvedTheme === currentTheme ? 'Yes' : 'No'}
              </Badge>
            </div>
            <div className="flex justify-between">
              <span>Expected sync:</span>
              <span className="text-muted-foreground">
                {nextTheme === 'system'
                  ? `${systemTheme} (from system)`
                  : nextTheme}{' '}
                → {currentTheme}
              </span>
            </div>
          </div>
        </div>

        {/* Troubleshooting */}
        <div className="space-y-2">
          <h3 className="font-medium">Troubleshooting</h3>
          <div className="space-y-1 text-sm">
            {resolvedTheme !== currentTheme && (
              <div className="rounded border border-yellow-200 bg-yellow-50 p-2 dark:border-yellow-800 dark:bg-yellow-950">
                ⚠️ Themes are not synchronized. This may indicate a sync issue.
              </div>
            )}
            {!nextTheme && (
              <div className="rounded border border-red-200 bg-red-50 p-2 dark:border-red-800 dark:bg-red-950">
                ❌ Next-themes not initialized. Check ThemeProvider setup.
              </div>
            )}
            {resolvedTheme === currentTheme && (
              <div className="rounded border border-green-200 bg-green-50 p-2 dark:border-green-800 dark:bg-green-950">
                ✅ Themes are properly synchronized.
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
