# 📚 Delegation Component Catalog & Usage Guide

**Document Version:** 1.0  
**Date:** January 2025  
**Status:** ✅ **ACTIVE** - Component Reference Guide  
**Last Updated:** January 2025

---

## 📋 **Overview**

This document serves as a comprehensive catalog of all delegation-related components, providing usage examples, props documentation, and implementation patterns. This catalog establishes the foundation for consistent component usage across the WorkHub application and serves as a template for future entity implementations.

---

## 🎯 **Component Categories**

### **1. Page Components**
- Route handlers and main page orchestrators
- Handle data fetching, error states, and layout composition

### **2. Feature Components**  
- Business logic components specific to delegation functionality
- Handle user interactions and state management

### **3. Common Components**
- Reusable UI patterns shared across delegation features
- Can be adapted for other entities (tasks, employees, vehicles)

### **4. Assignment Components**
- Specialized components for managing delegation assignments
- Highly reusable pattern for entity relationships

---

## 📖 **Component Documentation**

### **Page Components**

#### **DelegationDetailPage**
```typescript
// Location: frontend/src/app/delegations/[id]/page.tsx
export default function DelegationDetailPage()
```

**Purpose:** Main route handler for delegation detail view  
**Responsibilities:**
- Route parameter extraction (`params.id`)
- Data fetching with `useDelegation(delegationId)`
- Error boundary and loading state management
- Layout composition with header, tabs, and sidebar

**Key Features:**
- Responsive layout with mobile-first design
- Comprehensive error handling with retry functionality
- Loading states with skeleton components
- Delete confirmation with toast notifications

---

### **Feature Components**

#### **DelegationDetailHeader**
```typescript
// Location: frontend/src/components/delegations/detail/DelegationDetailHeader.tsx
interface DelegationDetailHeaderProps {
  delegation: Delegation;
  onDelete?: () => void;
}

export function DelegationDetailHeader({ delegation, onDelete }: DelegationDetailHeaderProps)
```

**Purpose:** Professional page header with responsive action buttons  
**Features:**
- Responsive design (desktop buttons + mobile sheet)
- Status badge with professional styling
- Breadcrumb navigation with back button
- Action buttons: Edit, Report, Delete
- Mobile-optimized sheet navigation

**Usage Example:**
```typescript
<DelegationDetailHeader
  delegation={loadedDelegation}
  onDelete={handleDeleteDelegation}
/>
```

#### **DelegationTabs**
```typescript
// Location: frontend/src/components/delegations/detail/DelegationTabs.tsx
interface DelegationTabsProps {
  delegation: Delegation;
  onStatusUpdate: (status: DelegationStatusPrisma, reason: string) => void;
}

export function DelegationTabs({ delegation, onStatusUpdate }: DelegationTabsProps)
```

**Purpose:** Tabbed interface for organized content display  
**Tabs:**
- **Overview**: Key delegation information and metrics
- **Assignments**: Delegates, drivers, escorts, vehicles management
- **Status History**: Status tracking with update functionality

**Features:**
- Smooth tab transitions
- Lazy loading of tab content
- Status update modal integration
- Responsive tab layout

**Usage Example:**
```typescript
<DelegationTabs
  delegation={loadedDelegation}
  onStatusUpdate={handleStatusUpdateConfirm}
/>
```

#### **DelegationSidebar**
```typescript
// Location: frontend/src/components/delegations/detail/DelegationSidebar.tsx
interface DelegationSidebarProps {
  delegation: Delegation;
  className?: string;
}

export function DelegationSidebar({ delegation, className }: DelegationSidebarProps)
```

**Purpose:** Sidebar with quick actions and delegation metrics  
**Features:**
- Quick Actions card with Edit, Report, Print buttons
- Delegation metrics display
- Responsive design
- Print functionality integration

**Quick Actions:**
- **Edit Delegation**: Navigate to edit page
- **View Report**: Open report in new tab
- **Print Details**: Trigger window.print()

**Usage Example:**
```typescript
<DelegationSidebar 
  delegation={loadedDelegation}
  className="lg:col-span-1" 
/>
```

---

### **Common Components**

#### **DelegationCard**
```typescript
// Location: frontend/src/components/delegations/common/DelegationCard.tsx
interface DelegationCardProps {
  delegation: Delegation;
}

export default function DelegationCard({ delegation }: DelegationCardProps)
```

**Purpose:** Professional card display for delegation list views  
**Features:**
- Professional styling with hover effects
- Status badge integration
- Key information display (duration, delegates, assignments)
- View Details button with proper navigation
- Responsive design with mobile optimization

**Key Fix Applied:**
- Added `pointer-events-none` to overlay to prevent click blocking
- Resolved View Details button functionality

**Usage Example:**
```typescript
<DelegationCard delegation={delegation} />
```

#### **DelegationMetrics**
```typescript
// Location: frontend/src/components/delegations/common/DelegationMetrics.tsx
interface DelegationMetricsProps {
  delegation: Delegation;
  className?: string;
}

export function DelegationMetrics({ delegation, className }: DelegationMetricsProps)
```

**Purpose:** Display key delegation metrics in sidebar  
**Metrics Displayed:**
- Creation date and time
- Last update timestamp  
- Duration calculation
- Assignment counts (delegates, drivers, escorts, vehicles)
- Status information

**Usage Example:**
```typescript
<DelegationMetrics 
  delegation={delegation}
  className="mt-6" 
/>
```

#### **DetailItem**
```typescript
// Location: frontend/src/components/delegations/common/DetailItem.tsx
interface DetailItemProps {
  icon: React.ElementType;
  label: string;
  value?: string | number | null;
  children?: React.ReactNode;
  iconClassName?: string;
  valueClassName?: string;
}

export function DetailItem({ icon: Icon, label, value, children, iconClassName, valueClassName }: DetailItemProps)
```

**Purpose:** Highly reusable component for displaying key-value pairs  
**Features:**
- Flexible content display (value prop or children)
- Icon integration with customizable styling
- Consistent spacing and typography
- Responsive design

**Usage Examples:**
```typescript
// Simple value display
<DetailItem
  icon={Users}
  label="Delegates"
  value={`${delegation.delegates?.length ?? 0} People`}
  iconClassName="text-blue-600"
/>

// Complex content with children
<DetailItem
  icon={CalendarDays}
  label="Duration"
  iconClassName="text-green-600"
>
  <div>
    <div>{formatDelegationDate(delegation.durationFrom)}</div>
    <div className="text-sm text-gray-500">
      to {formatDelegationDate(delegation.durationTo)}
    </div>
  </div>
</DetailItem>
```

---

### **Assignment Components**

#### **AssignmentSection (Base)**
```typescript
// Location: frontend/src/components/delegations/detail/assignments/AssignmentSection.tsx
interface AssignmentSectionProps<T> {
  title: string;
  items: T[];
  onAdd: () => void;
  onRemove: (id: string) => void;
  renderItem: (item: T) => React.ReactNode;
  addButtonText?: string;
  emptyMessage?: string;
  className?: string;
}

export function AssignmentSection<T>({ title, items, onAdd, onRemove, renderItem, addButtonText = "Add", emptyMessage = "No items assigned", className }: AssignmentSectionProps<T>)
```

**Purpose:** Base component for all assignment management  
**Features:**
- Generic type support for any assignment type
- Add/remove functionality
- Custom item rendering
- Empty state handling
- Consistent styling across assignment types

#### **SearchableAssignmentSection (Enhanced)**
```typescript
// Location: frontend/src/components/delegations/detail/assignments/SearchableAssignmentSection.tsx
interface SearchableAssignmentSectionProps<T> extends AssignmentSectionProps<T> {
  searchPlaceholder: string;
  onSearch: (term: string) => void;
  filterOptions?: FilterOption[];
  onFilter?: (filter: string) => void;
}

export function SearchableAssignmentSection<T>({ searchPlaceholder, onSearch, filterOptions, onFilter, ...baseProps }: SearchableAssignmentSectionProps<T>)
```

**Purpose:** Enhanced assignment section with search and filter capabilities  
**Features:**
- Real-time search functionality
- Filter dropdown integration
- Debounced search input
- All base AssignmentSection features

#### **Specific Assignment Cards**

##### **DelegatesCard**
```typescript
// Location: frontend/src/components/delegations/detail/assignments/DelegatesCard.tsx
interface DelegatesCardProps {
  delegation: Delegation;
  onUpdate: () => void;
}

export function DelegatesCard({ delegation, onUpdate }: DelegatesCardProps)
```

**Purpose:** Manage delegation delegates  
**Features:**
- Add/remove delegates
- Display delegate information (name, title, notes)
- Search functionality
- Professional card styling

##### **DriversCard**
```typescript
// Location: frontend/src/components/delegations/detail/assignments/DriversCard.tsx
interface DriversCardProps {
  delegation: Delegation;
  onUpdate: () => void;
}

export function DriversCard({ delegation, onUpdate }: DriversCardProps)
```

**Purpose:** Manage driver assignments with employee integration  
**Features:**
- Employee search and selection
- Driver assignment management
- Employee information display
- Integration with employee data

##### **EscortsCard**
```typescript
// Location: frontend/src/components/delegations/detail/assignments/EscortsCard.tsx
interface EscortsCardProps {
  delegation: Delegation;
  onUpdate: () => void;
}

export function EscortsCard({ delegation, onUpdate }: EscortsCardProps)
```

**Purpose:** Manage escort assignments with employee integration  
**Features:**
- Employee search and selection
- Escort assignment management
- Employee information display
- Integration with employee data

##### **VehiclesCard**
```typescript
// Location: frontend/src/components/delegations/detail/assignments/VehiclesCard.tsx
interface VehiclesCardProps {
  delegation: Delegation;
  onUpdate: () => void;
}

export function VehiclesCard({ delegation, onUpdate }: VehiclesCardProps)
```

**Purpose:** Manage vehicle assignments  
**Features:**
- Vehicle search and selection
- Assignment date management
- Vehicle information display
- Integration with vehicle data

---

## 🎨 **Styling Patterns**

### **Consistent Card Styling**
```typescript
// Standard card classes used across components
const cardClasses = cn(
  "bg-white dark:bg-gray-900",
  "border border-gray-200 dark:border-gray-700", 
  "rounded-lg shadow-sm",
  "transition-all duration-300",
  "hover:shadow-md hover:border-gray-300 dark:hover:border-gray-600"
);
```

### **Icon Sizing Standards**
```typescript
// Consistent icon sizing across components
<Icon className="size-4" />        // Small icons (16px)
<Icon className="h-4 w-4" />       // Alternative syntax
<Icon className="size-5" />        // Medium icons (20px)  
<Icon className="size-6" />        // Large icons (24px)
```

### **Button Patterns**
```typescript
// ActionButton usage patterns
<ActionButton
  actionType="primary"     // Blue primary button
  actionType="secondary"   // Gray secondary button  
  actionType="tertiary"    // Minimal tertiary button
  actionType="danger"      // Red danger button
  size="sm"               // Small size
  size="default"          // Default size
  size="lg"               // Large size
  icon={<Icon className="size-4" />}
  asChild                 // Use with Link components
>
  Button Text
</ActionButton>
```

---

## 🔄 **Reusability Patterns**

### **Cross-Entity Component Adaptation**

#### **For Tasks Implementation:**
```typescript
// Adapt DelegationCard pattern
interface TaskCardProps {
  task: Task;
}

export function TaskCard({ task }: TaskCardProps) {
  // Follow DelegationCard structure
  // Adapt status colors and information display
  // Reuse DetailItem and InfoSection components
}

// Adapt DelegationDetailHeader pattern  
interface TaskDetailHeaderProps {
  task: Task;
  onDelete?: () => void;
}

export function TaskDetailHeader({ task, onDelete }: TaskDetailHeaderProps) {
  // Follow DelegationDetailHeader structure
  // Adapt action buttons for task-specific actions
  // Reuse responsive design patterns
}
```

#### **For Employees Implementation:**
```typescript
// Adapt assignment patterns
interface EmployeeAssignmentsProps {
  employee: Employee;
  onUpdate: () => void;
}

export function EmployeeAssignments({ employee, onUpdate }: EmployeeAssignmentsProps) {
  // Reuse SearchableAssignmentSection
  // Adapt for employee-specific assignments (tasks, delegations, vehicles)
  // Follow established patterns
}
```

#### **For Vehicles Implementation:**
```typescript
// Adapt metrics display
interface VehicleMetricsProps {
  vehicle: Vehicle;
  className?: string;
}

export function VehicleMetrics({ vehicle, className }: VehicleMetricsProps) {
  // Follow DelegationMetrics structure
  // Adapt metrics for vehicle-specific data
  // Reuse DetailItem components
}
```

### **Shared Component Library**

#### **Components to Reuse Across Entities:**
- `DetailItem` - Universal key-value display
- `InfoSection` - Enhanced information display with variants
- `ActionButton` - Consistent button styling
- `StatusBadge` - Status display (adapt colors per entity)
- `SearchableAssignmentSection` - Assignment management
- `ReportActions` - Print/export functionality

#### **Patterns to Replicate:**
- Responsive header design
- Tabbed interface structure  
- Sidebar layout with quick actions
- Card-based information display
- Assignment management workflows
- Status tracking and updates

---

## 📱 **Mobile Optimization**

### **Responsive Design Patterns**
```typescript
// Desktop vs Mobile navigation
{/* Desktop: Full buttons */}
<div className="hidden md:flex items-center gap-2">
  <ActionButton>Edit</ActionButton>
  <ActionButton>Report</ActionButton>
</div>

{/* Mobile: Sheet navigation */}
<div className="md:hidden">
  <Sheet>
    <SheetTrigger asChild>
      <Button variant="outline" size="sm">
        <MoreHorizontal className="h-4 w-4" />
      </Button>
    </SheetTrigger>
    <SheetContent side="bottom">
      <div className="grid gap-4 py-4">
        {/* Action buttons */}
      </div>
    </SheetContent>
  </Sheet>
</div>
```

### **Grid Layout Patterns**
```typescript
// Responsive grid for detail pages
<div className="grid gap-8 lg:grid-cols-4">
  {/* Main content: 3/4 width on desktop, full width on mobile */}
  <div className="lg:col-span-3">
    <MainContent />
  </div>
  
  {/* Sidebar: 1/4 width on desktop, full width on mobile */}
  <div className="lg:col-span-1">
    <Sidebar />
  </div>
</div>
```

---

## ✅ **Usage Checklist**

### **Before Using Components**
- [ ] Import required types from `@/lib/types/domain`
- [ ] Ensure proper data fetching with React Query hooks
- [ ] Implement error handling and loading states
- [ ] Add proper TypeScript types for props
- [ ] Follow established naming conventions

### **Component Implementation**
- [ ] Use consistent styling patterns
- [ ] Implement responsive design
- [ ] Add proper accessibility attributes
- [ ] Include error boundaries where needed
- [ ] Optimize for performance with memoization

### **Testing Considerations**
- [ ] Test responsive behavior across breakpoints
- [ ] Verify accessibility with screen readers
- [ ] Test error states and edge cases
- [ ] Validate TypeScript type safety
- [ ] Check performance with large datasets

---

**Document Maintainer:** Frontend Team  
**Review Schedule:** Monthly  
**Next Review:** February 2025
