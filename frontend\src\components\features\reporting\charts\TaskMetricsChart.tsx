// frontend/src/components/features/reporting/charts/TaskMetricsChart.tsx

import React, { useMemo } from 'react';
import {
  Bar<PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  Legend,
  RadialBarChart,
  RadialBar,
  TooltipProps,
} from 'recharts';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { <PERSON><PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { TaskMetrics } from '../data/types/reporting';
import { CheckCircle, Clock, AlertTriangle, Circle } from 'lucide-react';
import {
  LoadingSkeleton,
  ErrorDisplay,
  NoDataDisplay,
} from './shared/ChartStateComponents';

/**
 * Props for TaskMetricsChart component
 */
interface TaskMetricsChartProps {
  data: TaskMetrics;
  loading?: boolean;
  error?: string | undefined;
  height?: number;
  showLegend?: boolean;
  interactive?: boolean;
  className?: string;
}

/**
 * Custom tooltip for task metrics
 */
const TaskTooltip = ({
  active,
  payload,
  label,
}: TooltipProps<number, string>) => {
  if (active && payload && payload.length) {
    const data = payload[0];

    if (!data) return null;

    return (
      <div className="bg-white p-4 border border-gray-200 rounded-lg shadow-lg">
        <p className="font-semibold text-gray-900 mb-2">{data.name || label}</p>
        <div className="flex items-center gap-2">
          <div
            className="w-3 h-3 rounded-full"
            style={{ backgroundColor: data.color }}
          />
          <span className="text-sm text-gray-600">Count:</span>
          <Badge variant="secondary">{data.value}</Badge>
        </div>
        {data.payload?.percentage && (
          <div className="mt-1 text-sm text-gray-600">
            Percentage:{' '}
            <span className="font-medium">
              {data.payload.percentage.toFixed(1)}%
            </span>
          </div>
        )}
      </div>
    );
  }
  return null;
};

/**
 * TaskMetricsChart Component
 *
 * Displays comprehensive task metrics with multiple visualization options.
 */
export const TaskMetricsChart: React.FC<TaskMetricsChartProps> = ({
  data,
  loading = false,
  error,
  height = 400,
  showLegend = true,
  interactive = true,
  className = '',
}) => {
  const chartData = useMemo(() => {
    if (!data) return [];
    const total = data.totalTasks;
    if (total === 0) return [];

    return [
      {
        name: 'Completed',
        value: data.completedTasks,
        percentage: (data.completedTasks / total) * 100,
        color: '#10b981',
      },
      {
        name: 'In Progress',
        value: data.inProgressTasks,
        percentage: (data.inProgressTasks / total) * 100,
        color: '#f59e0b',
      },
      {
        name: 'Overdue',
        value: data.overdueTasks || 0,
        percentage: ((data.overdueTasks || 0) / total) * 100,
        color: '#ef4444',
      },
      {
        name: 'Pending',
        value: data.pendingTasks,
        percentage: (data.pendingTasks / total) * 100,
        color: '#6b7280',
      },
    ];
  }, [data]);

  const summaryData = useMemo(() => {
    if (!data) return [];
    return [
      {
        title: 'Total Tasks',
        value: data.totalTasks,
        icon: <Circle className="w-6 h-6 text-gray-500" />,
      },
      {
        title: 'Completed',
        value: data.completedTasks,
        icon: <CheckCircle className="w-6 h-6 text-green-500" />,
      },
      {
        title: 'In Progress',
        value: data.inProgressTasks,
        icon: <Clock className="w-6 h-6 text-yellow-500" />,
      },
      {
        title: 'Overdue',
        value: data.overdueTasks || 0,
        icon: <AlertTriangle className="w-6 h-6 text-red-500" />,
      },
    ];
  }, [data]);

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>Task Metrics</CardTitle>
          <CardDescription>Loading task metrics...</CardDescription>
        </CardHeader>
        <CardContent>
          <LoadingSkeleton height={height} />
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>Task Metrics</CardTitle>
          <CardDescription>Task metrics unavailable</CardDescription>
        </CardHeader>
        <CardContent>
          <ErrorDisplay error={error} />
        </CardContent>
      </Card>
    );
  }

  if (!data || data.totalTasks === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>Task Metrics</CardTitle>
          <CardDescription>No task data available</CardDescription>
        </CardHeader>
        <CardContent>
          <NoDataDisplay message="No task metrics to display" />
        </CardContent>
      </Card>
    );
  }

  const completionRate =
    data.totalTasks > 0 ? (data.completedTasks / data.totalTasks) * 100 : 0;

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>Task Metrics</CardTitle>
        <CardDescription>
          A summary of task status and completion rates.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Summary Cards */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {summaryData.map(item => (
            <Card key={item.title}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {item.title}
                </CardTitle>
                {item.icon}
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{item.value}</div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Completion Rate Progress */}
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium text-gray-700">
              Overall Completion Rate
            </span>
            <Badge
              variant={
                completionRate >= 80
                  ? 'default'
                  : completionRate >= 60
                    ? 'secondary'
                    : 'destructive'
              }
            >
              {completionRate.toFixed(1)}%
            </Badge>
          </div>
          <Progress value={completionRate} className="h-2" />
        </div>

        {/* Charts */}
        <Tabs defaultValue="pie" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="pie">Pie Chart</TabsTrigger>
            <TabsTrigger value="bar">Bar Chart</TabsTrigger>
            <TabsTrigger value="radial">Radial Chart</TabsTrigger>
          </TabsList>

          <TabsContent value="pie" className="mt-4">
            <ResponsiveContainer width="100%" height={height - 200}>
              <PieChart>
                <Pie
                  data={chartData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percentage }) =>
                    `${name}: ${percentage.toFixed(1)}%`
                  }
                  outerRadius={100}
                  fill="#8884d8"
                  dataKey="value"
                  animationDuration={800}
                >
                  {chartData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                {interactive && <Tooltip content={<TaskTooltip />} />}
                {showLegend && <Legend />}
              </PieChart>
            </ResponsiveContainer>
          </TabsContent>

          <TabsContent value="bar" className="mt-4">
            <ResponsiveContainer width="100%" height={height - 200}>
              <BarChart
                data={chartData}
                margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                <XAxis
                  dataKey="name"
                  fontSize={12}
                  tickLine={false}
                  axisLine={false}
                />
                <YAxis fontSize={12} tickLine={false} axisLine={false} />
                {interactive && <Tooltip content={<TaskTooltip />} />}
                <Bar dataKey="value" fill="#8884d8" animationDuration={800}>
                  {chartData.map((entry, index) => (
                    <Cell key={`bar-cell-${index}`} fill={entry.color} />
                  ))}
                </Bar>
              </BarChart>
            </ResponsiveContainer>
          </TabsContent>

          <TabsContent value="radial" className="mt-4">
            <ResponsiveContainer width="100%" height={height - 200}>
              <RadialBarChart
                cx="50%"
                cy="50%"
                innerRadius="20%"
                outerRadius="80%"
                data={chartData}
              >
                <RadialBar
                  dataKey="percentage"
                  cornerRadius={10}
                  fill="#8884d8"
                  animationDuration={800}
                >
                  {chartData.map((entry, index) => (
                    <Cell key={`radial-cell-${index}`} fill={entry.color} />
                  ))}
                </RadialBar>
                {interactive && <Tooltip content={<TaskTooltip />} />}
                {showLegend && <Legend />}
              </RadialBarChart>
            </ResponsiveContainer>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};
