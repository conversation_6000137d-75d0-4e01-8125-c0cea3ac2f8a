'use client';

import { format } from 'date-fns';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  ClipboardList,
  Clock,
  Save,
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect, useMemo } from 'react';
import {
  type ControllerRenderProps,
  type DefaultValues,
  useFormContext,
} from 'react-hook-form';

import type { Employee, Vehicle } from '@/lib/types/domain';

import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { BaseForm } from '@/components/ui/forms/baseForm';
import { FormField } from '@/components/ui/forms/formField';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { useFormToast } from '@/hooks/forms/useFormToast';
import {
  type TaskFormData,
  TaskPrioritySchema,
  TaskSchema,
  TaskStatusSchema,
} from '@/lib/schemas/taskSchemas';
import { useEmployeesByRole } from '@/lib/stores/queries/useEmployees';
import { useVehicles } from '@/lib/stores/queries/useVehicles';
import { formatDateForApi, formatDateForInput } from '@/lib/utils/dateUtils';

interface TaskFormProps {
  initialData?: TaskFormData;
  isEditing?: boolean;
  isLoading?: boolean;
  onSubmit: (data: TaskFormData) => Promise<void>;
}

export default function TaskForm({
  initialData,
  isEditing = false,
  isLoading = false, // Destructure isLoading and provide a default false
  onSubmit,
}: TaskFormProps) {
  const router = useRouter();

  // Prepare default values for BaseForm
  const defaultValues: DefaultValues<TaskFormData> = {
    dateTime: initialData?.dateTime
      ? formatDateForInput(initialData.dateTime, 'datetime-local')
      : format(new Date(), "yyyy-MM-dd'T'HH:mm"),
    deadline: initialData?.deadline
      ? formatDateForInput(initialData.deadline, 'datetime-local')
      : '',
    description: initialData?.description || '',
    driverEmployeeId: initialData?.driverEmployeeId || null,
    estimatedDuration: initialData?.estimatedDuration || 60,
    location: initialData?.location || '',
    notes: initialData?.notes || '',
    priority: initialData?.priority || 'Medium',
    requiredSkills: initialData?.requiredSkills || [],
    ...(initialData?.staffEmployeeId && {
      staffEmployeeId: initialData.staffEmployeeId,
    }),
    status: initialData?.status
      ? (initialData.status.replace('_', ' ') as TaskFormData['status'])
      : 'Pending',
    vehicleId: initialData?.vehicleId || null,
  };

  // Handle form submission with data transformation
  const handleFormSubmit = async (data: TaskFormData) => {
    try {
      const submissionData = {
        ...data,
        dateTime: formatDateForApi(data.dateTime),
        deadline: data.deadline ? formatDateForApi(data.deadline) : undefined,
        driverEmployeeId: data.driverEmployeeId
          ? Number(data.driverEmployeeId)
          : null,
        staffEmployeeId: Number(data.staffEmployeeId),
        vehicleId: data.vehicleId ? Number(data.vehicleId) : null,
        // Convert subtasks to subTasks for backend compatibility
        ...(data.subtasks && { subTasks: data.subtasks }),
      };
      await onSubmit(submissionData as TaskFormData);
    } catch (error) {
      // Error handling is managed by the parent component
      // Re-throw to let BaseForm handle the error state
      throw error;
    }
  };

  return (
    <BaseForm
      defaultValues={defaultValues}
      onSubmit={handleFormSubmit}
      schema={TaskSchema}
    >
      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle className="text-2xl text-primary">
            {isEditing ? 'Edit Task' : 'Add New Task'}
          </CardTitle>
          <CardDescription>Enter the details for the task.</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <TaskFormFields />
        </CardContent>
        <CardFooter className="flex justify-between gap-2 border-t pt-6">
          <Button onClick={() => router.back()} type="button" variant="outline">
            <ArrowLeft className="mr-2 size-4" />
            Cancel
          </Button>
          <Button
            className="bg-accent text-accent-foreground hover:bg-accent/90"
            type="submit"
            disabled={isLoading} // Disable button when isLoading is true
          >
            <Save className="mr-2 size-4" />
            {isEditing ? 'Save Changes' : 'Create Task'}
          </Button>
        </CardFooter>
      </Card>
    </BaseForm>
  );
}

// Separate component to access form context for complex form logic
const TaskFormFields: React.FC = () => {
  const { watch } = useFormContext<TaskFormData>();
  const { showFormError } = useFormToast();

  // Use optimized role-based employee fetching
  const { data: allActiveEmployees = [] } = useEmployeesByRole();
  const { data: driverEmployees = [] } = useEmployeesByRole('driver');

  // Filter non-driver employees for staff assignments
  const staffEmployees = allActiveEmployees.filter(
    emp => emp.role !== 'driver'
  );

  const { data: vehiclesData, error: vehiclesError } = useVehicles();
  const vehicles = useMemo(() => vehiclesData || [], [vehiclesData]);

  useEffect(() => {
    if (vehiclesError) {
      console.error('Failed to fetch vehicles for form:', vehiclesError);
      showFormError(
        (vehiclesError as Error).message || 'Could not load vehicles.',
        {
          errorTitle: 'Error Loading Vehicles',
        }
      );
    }
  }, [vehiclesError, showFormError]);

  return (
    <>
      {/* Task Details Section */}
      <section className="space-y-4 rounded-lg border bg-card p-4">
        <h3 className="flex items-center text-lg font-semibold text-foreground">
          <ClipboardList className="mr-2 size-5 text-accent" />
          Task Details
        </h3>
        <FormField
          label="Description"
          name="description"
          placeholder="e.g., Pick up package from Warehouse A"
          type="textarea"
        />
        <FormField
          label="Location"
          name="location"
          placeholder="e.g., 123 Main St, City Center"
        />
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <FormField
            label="Start Date & Time"
            name="dateTime"
            type="datetime-local"
          />
          <FormField
            label="Estimated Duration (minutes)"
            name="estimatedDuration"
            placeholder="e.g., 60"
            type="number"
          />
        </div>
      </section>

      {/* Scheduling & Assignment Section */}
      <section className="space-y-4 rounded-lg border bg-card p-4">
        <h3 className="flex items-center text-lg font-semibold text-foreground">
          <Clock className="mr-2 size-5 text-accent" />
          Scheduling & Assignment
        </h3>
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <FormField
            label="Priority"
            name="priority"
            render={({
              field,
            }: {
              field: ControllerRenderProps<TaskFormData, 'priority'>;
            }) => (
              <Select defaultValue={field.value} onValueChange={field.onChange}>
                <SelectTrigger>
                  <SelectValue placeholder="Select priority" />
                </SelectTrigger>
                <SelectContent>
                  {TaskPrioritySchema.options.map((priority: string) => (
                    <SelectItem key={priority} value={priority}>
                      {priority}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          />
          <FormField
            label="Deadline (Optional)"
            name="deadline"
            type="datetime-local"
          />
        </div>
        <FormField
          label="Status"
          name="status"
          render={({
            field,
          }: {
            field: ControllerRenderProps<TaskFormData, 'status'>;
          }) => (
            <Select defaultValue={field.value} onValueChange={field.onChange}>
              <SelectTrigger>
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent>
                {TaskStatusSchema.options.map((status: string) => (
                  <SelectItem key={status} value={status}>
                    {status}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}
        />

        {/* Employee and Vehicle Assignment */}
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <FormField
            label="Staff Employee (Required)"
            name="staffEmployeeId"
            render={({
              field,
            }: {
              field: ControllerRenderProps<TaskFormData, 'staffEmployeeId'>;
            }) => (
              <Select
                onValueChange={value => field.onChange(Number(value))}
                value={field.value ? String(field.value) : ''}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select staff employee" />
                </SelectTrigger>
                <SelectContent>
                  {staffEmployees.map((emp: Employee) => (
                    <SelectItem key={emp.id} value={String(emp.id)}>
                      {emp.fullName} ({emp.role}, {emp.status})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          />
          <FormField
            label="Driver Employee (Optional)"
            name="driverEmployeeId"
            render={({
              field,
            }: {
              field: ControllerRenderProps<TaskFormData, 'driverEmployeeId'>;
            }) => (
              <Select
                onValueChange={value =>
                  field.onChange(value === 'none' ? undefined : Number(value))
                }
                value={field.value === undefined ? 'none' : String(field.value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select driver (optional)" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">No Driver</SelectItem>
                  {driverEmployees.map((emp: Employee) => (
                    <SelectItem key={emp.id} value={String(emp.id)}>
                      {emp.fullName} ({emp.status})
                      {emp.availability &&
                        `, ${emp.availability.replace('_', ' ')}`}
                      {emp.currentLocation && `, @ ${emp.currentLocation}`}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          />
        </div>
        <FormField
          label="Vehicle (Optional - requires driver)"
          name="vehicleId"
          render={({
            field,
          }: {
            field: ControllerRenderProps<TaskFormData, 'vehicleId'>;
          }) => (
            <Select
              disabled={!watch('driverEmployeeId')}
              onValueChange={value =>
                field.onChange(value === 'none' ? undefined : Number(value))
              }
              value={field.value === undefined ? 'none' : String(field.value)}
            >
              <SelectTrigger>
                <SelectValue
                  placeholder={
                    watch('driverEmployeeId')
                      ? 'Select vehicle (optional)'
                      : 'Select a driver first'
                  }
                />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">No Vehicle</SelectItem>
                {vehicles.map((vehicle: Vehicle) => (
                  <SelectItem key={vehicle.id} value={String(vehicle.id)}>
                    {vehicle.make} {vehicle.model} ({vehicle.year}) -{' '}
                    {vehicle.licensePlate || 'N/A'}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}
        />
      </section>

      {/* Additional Information Section */}
      <section className="space-y-4 rounded-lg border bg-card p-4">
        <h3 className="flex items-center text-lg font-semibold text-foreground">
          <AlertTriangle className="mr-2 size-5 text-accent" />
          Additional Information
        </h3>
        <FormField
          label="Required Skills (Optional, comma-separated)"
          name="requiredSkills"
          render={({
            field,
          }: {
            field: ControllerRenderProps<TaskFormData, 'requiredSkills'>;
          }) => (
            <Input
              onChange={e =>
                field.onChange(
                  e.target.value
                    .split(',')
                    .map(skill => skill.trim())
                    .filter(Boolean)
                )
              }
              placeholder="e.g., Forklift License, Customer Service"
              value={Array.isArray(field.value) ? field.value.join(', ') : ''}
            />
          )}
        />
        <FormField
          label="Notes (Optional)"
          name="notes"
          placeholder="e.g., Gate code is 1234, contact person: Jane Smith"
          type="textarea"
        />
      </section>
    </>
  );
};
