/**
 * @file WidgetConfigurator.tsx
 * @description Widget configuration component for customizing widget settings
 */

import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Settings } from 'lucide-react';
import type { WidgetConfig } from '../data/types/reporting';

/**
 * Widget configuration schema
 */
const widgetConfigSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  span: z.string(),
  showTitle: z.boolean().default(true),
  showBorder: z.boolean().default(true),
  refreshInterval: z.number().min(0).max(1440).optional(),
  height: z.number().min(200).max(800).optional(),
  // Chart-specific configs
  chartType: z.string().optional(),
  xAxisField: z.string().optional(),
  yAxisField: z.string().optional(),
  colorScheme: z.string().optional(),
  showLegend: z.boolean().optional(),
  showGrid: z.boolean().optional(),
  // Table-specific configs
  pageSize: z.number().min(5).max(100).optional(),
  sortable: z.boolean().optional(),
  filterable: z.boolean().optional(),
  exportable: z.boolean().optional(),
  // Analytics-specific configs
  metricType: z.string().optional(),
  aggregationType: z.string().optional(),
  comparisonPeriod: z.string().optional(),
});

type WidgetConfigFormData = z.infer<typeof widgetConfigSchema>;

/**
 * Props interface for WidgetConfigurator
 */
interface WidgetConfiguratorProps {
  widget: WidgetConfig;
  onSave: (widget: WidgetConfig) => void;
  onCancel: () => void;
}

/**
 * Available span options
 */
const SPAN_OPTIONS = [
  { value: 'col-span-1', label: '1 Column' },
  { value: 'col-span-2', label: '2 Columns' },
  { value: 'col-span-3', label: '3 Columns' },
  { value: 'col-span-4', label: '4 Columns' },
  { value: 'col-span-full', label: 'Full Width' },
];

/**
 * Chart type options
 */
const CHART_TYPES = [
  { value: 'bar', label: 'Bar Chart' },
  { value: 'line', label: 'Line Chart' },
  { value: 'pie', label: 'Pie Chart' },
  { value: 'area', label: 'Area Chart' },
  { value: 'scatter', label: 'Scatter Plot' },
];

/**
 * Color scheme options
 */
const COLOR_SCHEMES = [
  { value: 'default', label: 'Default' },
  { value: 'blue', label: 'Blue' },
  { value: 'green', label: 'Green' },
  { value: 'red', label: 'Red' },
  { value: 'purple', label: 'Purple' },
  { value: 'orange', label: 'Orange' },
];

/**
 * WidgetConfigurator Component
 *
 * Provides configuration interface for widget settings.
 *
 * Responsibilities:
 * - Display widget-specific configuration options
 * - Validate configuration data
 * - Handle configuration save and cancel
 * - Adapt to different widget types
 *
 * SOLID Principles Applied:
 * - SRP: Single responsibility of configuring widgets
 * - OCP: Open for extension via widget type configurations
 * - DIP: Depends on form framework abstractions
 */
export const WidgetConfigurator: React.FC<WidgetConfiguratorProps> = ({
  widget,
  onSave,
  onCancel,
}) => {
  // Initialize form with widget configuration
  const form = useForm<WidgetConfigFormData>({
    resolver: zodResolver(widgetConfigSchema),
    defaultValues: {
      title: widget.title,
      span: widget.span,
      showTitle: widget.config?.showTitle ?? true,
      showBorder: widget.config?.showBorder ?? true,
      refreshInterval: widget.config?.refreshInterval || 60,
      height: widget.config?.height || 300,
      // Chart configs
      chartType: widget.config?.chartType || 'bar',
      xAxisField: widget.config?.xAxisField || '',
      yAxisField: widget.config?.yAxisField || '',
      colorScheme: widget.config?.colorScheme || 'default',
      showLegend: widget.config?.showLegend ?? true,
      showGrid: widget.config?.showGrid ?? true,
      // Table configs
      pageSize: widget.config?.pageSize || 10,
      sortable: widget.config?.sortable ?? true,
      filterable: widget.config?.filterable ?? true,
      exportable: widget.config?.exportable ?? true,
      // Analytics configs
      metricType: widget.config?.metricType || 'count',
      aggregationType: widget.config?.aggregationType || 'sum',
      comparisonPeriod: widget.config?.comparisonPeriod || 'previous-month',
    },
  });

  // Handle form submission
  const handleSubmit = (data: WidgetConfigFormData) => {
    const updatedWidget: WidgetConfig = {
      ...widget,
      title: data.title,
      span: data.span,
      config: {
        ...widget.config,
        ...data,
      },
    };

    onSave(updatedWidget);
  };

  // Check if widget type supports specific configurations
  const isChartWidget = ['bar-chart', 'pie-chart', 'line-chart'].includes(
    widget.type
  );
  const isTableWidget = widget.type === 'data-table';
  const isAnalyticsWidget = ['analytics', 'metrics'].includes(widget.type);

  return (
    <Dialog open={true} onOpenChange={onCancel}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Configure Widget: {widget.title}
          </DialogTitle>
          <DialogDescription>
            Customize the appearance and behavior of this widget.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className="space-y-6"
          >
            {/* Basic Configuration */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Basic Settings</h3>

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="title"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Widget Title</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter widget title" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="span"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Width</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select width" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {SPAN_OPTIONS.map(option => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="height"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Height (px)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="200"
                          max="800"
                          {...field}
                          onChange={e =>
                            field.onChange(parseInt(e.target.value) || 300)
                          }
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="refreshInterval"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Refresh Interval (minutes)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="0"
                          max="1440"
                          {...field}
                          onChange={e =>
                            field.onChange(parseInt(e.target.value) || 60)
                          }
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="showTitle"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                      <div className="space-y-0.5">
                        <FormLabel>Show Title</FormLabel>
                        <FormDescription>Display widget title</FormDescription>
                      </div>
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="showBorder"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                      <div className="space-y-0.5">
                        <FormLabel>Show Border</FormLabel>
                        <FormDescription>Display widget border</FormDescription>
                      </div>
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Chart-specific Configuration */}
            {isChartWidget && (
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Chart Settings</h3>

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="chartType"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Chart Type</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          value={field.value || ''}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select chart type" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {CHART_TYPES.map(type => (
                              <SelectItem key={type.value} value={type.value}>
                                {type.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="colorScheme"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Color Scheme</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          value={field.value || ''}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select color scheme" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {COLOR_SCHEMES.map(scheme => (
                              <SelectItem
                                key={scheme.value}
                                value={scheme.value}
                              >
                                {scheme.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="showLegend"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                        <div className="space-y-0.5">
                          <FormLabel>Show Legend</FormLabel>
                          <FormDescription>
                            Display chart legend
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Checkbox
                            checked={field.value || false}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="showGrid"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                        <div className="space-y-0.5">
                          <FormLabel>Show Grid</FormLabel>
                          <FormDescription>
                            Display chart grid lines
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Checkbox
                            checked={field.value || false}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            )}

            {/* Table-specific Configuration */}
            {isTableWidget && (
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Table Settings</h3>

                <FormField
                  control={form.control}
                  name="pageSize"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Page Size</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="5"
                          max="100"
                          {...field}
                          onChange={e =>
                            field.onChange(parseInt(e.target.value) || 10)
                          }
                        />
                      </FormControl>
                      <FormDescription>Number of rows per page</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-3 gap-4">
                  <FormField
                    control={form.control}
                    name="sortable"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                        <div className="space-y-0.5">
                          <FormLabel>Sortable</FormLabel>
                          <FormDescription>
                            Enable column sorting
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Checkbox
                            checked={field.value || false}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="filterable"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                        <div className="space-y-0.5">
                          <FormLabel>Filterable</FormLabel>
                          <FormDescription>
                            Enable column filters
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Checkbox
                            checked={field.value || false}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="exportable"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                        <div className="space-y-0.5">
                          <FormLabel>Exportable</FormLabel>
                          <FormDescription>Enable data export</FormDescription>
                        </div>
                        <FormControl>
                          <Checkbox
                            checked={field.value || false}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            )}

            <DialogFooter>
              <Button type="button" variant="outline" onClick={onCancel}>
                Cancel
              </Button>
              <Button type="submit">Save Configuration</Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default WidgetConfigurator;
