/**
 * Circuit Breaker Service
 *
 * Provides circuit breaker protection for external service calls to prevent
 * cascading failures and improve system resilience.
 *
 * Features:
 * - Multiple circuit breaker instances for different services
 * - Configurable failure thresholds and timeouts
 * - Integration with Winston logging
 * - Metrics collection for monitoring
 * - Graceful fallback mechanisms
 */

import CircuitBreaker from 'opossum';
import { promisify } from 'util';

import logger from '../utils/logger.js';
import { getUnifiedWebSocketService } from './UnifiedWebSocketService.js';
import { RELIABILITY_EVENTS } from './WebSocketEventManager.js';
import type { CircuitBreakerStatus } from '../types/websocketEvents.js';

// Circuit breaker configuration interface
interface CircuitBreakerConfig {
  errorThresholdPercentage: number;
  group?: string;
  name: string;
  resetTimeout: number;
  rollingCountBuckets: number;
  rollingCountTimeout: number;
  timeout: number;
}

// Default circuit breaker configurations for different service types
const DEFAULT_CONFIGS = {
  database: {
    errorThresholdPercentage: 50,
    group: 'persistence',
    name: 'database',
    resetTimeout: 30000, // 30 seconds
    rollingCountBuckets: 10,
    rollingCountTimeout: 10000, // 10 seconds
    timeout: 10000, // 10 seconds
  },

  external_api: {
    errorThresholdPercentage: 60,
    group: 'external',
    name: 'external_api',
    resetTimeout: 60000, // 1 minute
    rollingCountBuckets: 10,
    rollingCountTimeout: 20000, // 20 seconds
    timeout: 15000, // 15 seconds
  },

  redis: {
    errorThresholdPercentage: 40,
    group: 'cache',
    name: 'redis',
    resetTimeout: 15000, // 15 seconds
    rollingCountBuckets: 5,
    rollingCountTimeout: 5000, // 5 seconds
    timeout: 5000, // 5 seconds
  },

  supabase: {
    errorThresholdPercentage: 50,
    group: 'database',
    name: 'supabase',
    resetTimeout: 45000, // 45 seconds
    rollingCountBuckets: 10,
    rollingCountTimeout: 15000, // 15 seconds
    timeout: 12000, // 12 seconds
  },
} as const;

// Circuit breaker history tracking
interface CircuitBreakerHistoryEntry {
  timestamp: string;
  breakerName: string;
  previousState: string;
  newState: string;
  reason: string;
  metrics: {
    failureCount: number;
    successCount: number;
    totalRequests: number;
    errorRate: number;
  };
}

// In-memory storage for circuit breaker history (in production, this should use Redis or database)
const circuitBreakerHistory: CircuitBreakerHistoryEntry[] = [];
const MAX_HISTORY_STORAGE = 1000; // Keep last 1000 state changes

// Circuit breaker registry
class CircuitBreakerRegistry {
  private readonly breakers = new Map<string, CircuitBreaker>();
  private readonly metrics = new Map<string, any>();

  /**
   * Create or get a circuit breaker instance
   */
  getBreaker(name: string, config?: Partial<CircuitBreakerConfig>): CircuitBreaker {
    if (this.breakers.has(name)) {
      return this.breakers.get(name)!;
    }

    // Get default config based on service type or use external_api as fallback
    const defaultConfig =
      DEFAULT_CONFIGS[name as keyof typeof DEFAULT_CONFIGS] || DEFAULT_CONFIGS.external_api;
    const finalConfig = { ...defaultConfig, ...config, name };

    const breaker = new CircuitBreaker(this.executeFunction, {
      errorThresholdPercentage: finalConfig.errorThresholdPercentage,
      group: finalConfig.group,
      name: finalConfig.name,
      resetTimeout: finalConfig.resetTimeout,
      rollingCountBuckets: finalConfig.rollingCountBuckets,
      rollingCountTimeout: finalConfig.rollingCountTimeout,
      timeout: finalConfig.timeout,
    });

    // Set up event listeners for logging and metrics
    this.setupEventListeners(breaker, finalConfig.name);

    this.breakers.set(name, breaker);
    this.initializeMetrics(name);

    logger.info(`Circuit breaker created for service: ${name}`, {
      config: finalConfig,
      service: 'circuit-breaker',
    });

    return breaker;
  }

  /**
   * Get all registered circuit breaker names
   */
  getRegisteredBreakers(): string[] {
    return Array.from(this.breakers.keys());
  }

  /**
   * Get circuit breaker status for monitoring
   */
  getStatus(name?: string): any {
    if (name) {
      const breaker = this.breakers.get(name);
      const metrics = this.metrics.get(name);

      if (!breaker || !metrics) {
        return null;
      }

      return {
        config: {
          errorThresholdPercentage: (breaker as any).options?.errorThresholdPercentage || 'unknown',
          resetTimeout: (breaker as any).options?.resetTimeout || 'unknown',
          timeout: (breaker as any).options?.timeout || 'unknown',
        },
        metrics,
        name,
        state: breaker.opened ? 'OPEN' : breaker.halfOpen ? 'HALF_OPEN' : 'CLOSED',
        stats: breaker.stats,
      };
    }

    // Return status for all circuit breakers
    const allStatus: any = {};
    for (const [breakerName] of this.breakers) {
      allStatus[breakerName] = this.getStatus(breakerName);
    }
    return allStatus;
  }

  /**
   * Reset a specific circuit breaker or all circuit breakers
   */
  reset(name?: string): void {
    if (name) {
      const breaker = this.breakers.get(name);
      if (breaker) {
        breaker.close();
        logger.info(`Circuit breaker manually reset: ${name}`, {
          breakerName: name,
          service: 'circuit-breaker',
        });
      }
    } else {
      // Reset all circuit breakers
      for (const [breakerName, breaker] of this.breakers) {
        breaker.close();
        logger.info(`Circuit breaker manually reset: ${breakerName}`, {
          breakerName,
          service: 'circuit-breaker',
        });
      }
    }
  }

  /**
   * Execute function wrapper for circuit breaker
   */
  private async executeFunction(fn: () => Promise<any>): Promise<any> {
    return await fn();
  }

  /**
   * Initialize metrics for a circuit breaker
   */
  private initializeMetrics(name: string): void {
    this.metrics.set(name, {
      failureCount: 0,
      lastStateChange: new Date(),
      rejectCount: 0,
      state: 'CLOSED',
      successCount: 0,
      timeoutCount: 0,
      totalRequests: 0,
    });
  }

  /**
   * Set up event listeners for circuit breaker monitoring
   */
  private setupEventListeners(breaker: CircuitBreaker, name: string): void {
    breaker.on('open', () => {
      logger.warn(`Circuit breaker OPENED for service: ${name}`, {
        breakerName: name,
        service: 'circuit-breaker',
        state: 'OPEN',
      });
      this.updateMetrics(name, 'open');

      // Emit WebSocket event
      try {
        const unifiedSocketService = getUnifiedWebSocketService();
        if (unifiedSocketService && unifiedSocketService.reliability) {
          const statusPayload: CircuitBreakerStatus = {
            name,
            state: 'OPEN',
            lastChanged: new Date().toISOString(),
          };
          if (typeof (unifiedSocketService.reliability as any).emitCircuitBreakerUpdate === 'function') {
            (unifiedSocketService.reliability as any).emitCircuitBreakerUpdate(statusPayload);
          } else {
            logger.warn('emitCircuitBreakerUpdate method not found on reliability namespace, attempting direct emit.');
            unifiedSocketService.emitToRoom('reliability-monitoring', RELIABILITY_EVENTS.CIRCUIT_BREAKER_UPDATE, statusPayload);
          }
        }
      } catch (error) {
        logger.error('Failed to emit WebSocket event for circuit breaker OPEN state', { error, breakerName: name });
      }
    });

    breaker.on('halfOpen', () => {
      logger.info(`Circuit breaker HALF-OPEN for service: ${name}`, {
        breakerName: name,
        service: 'circuit-breaker',
        state: 'HALF_OPEN',
      });
      this.updateMetrics(name, 'half_open');

      // Emit WebSocket event
      try {
        const unifiedSocketService = getUnifiedWebSocketService();
        if (unifiedSocketService && unifiedSocketService.reliability) {
          const statusPayload: CircuitBreakerStatus = {
            name,
            state: 'HALF_OPEN',
            lastChanged: new Date().toISOString(),
          };
          if (typeof (unifiedSocketService.reliability as any).emitCircuitBreakerUpdate === 'function') {
            (unifiedSocketService.reliability as any).emitCircuitBreakerUpdate(statusPayload);
          } else {
            logger.warn('emitCircuitBreakerUpdate method not found on reliability namespace, attempting direct emit.');
            unifiedSocketService.emitToRoom('reliability-monitoring', RELIABILITY_EVENTS.CIRCUIT_BREAKER_UPDATE, statusPayload);
          }
        }
      } catch (error) {
        logger.error('Failed to emit WebSocket event for circuit breaker HALF_OPEN state', { error, breakerName: name });
      }
    });

    breaker.on('close', () => {
      logger.info(`Circuit breaker CLOSED for service: ${name}`, {
        breakerName: name,
        service: 'circuit-breaker',
        state: 'CLOSED',
      });
      this.updateMetrics(name, 'closed');

      // Emit WebSocket event
      try {
        const unifiedSocketService = getUnifiedWebSocketService();
        if (unifiedSocketService && unifiedSocketService.reliability) {
          const statusPayload: CircuitBreakerStatus = {
            name,
            state: 'CLOSED',
            lastChanged: new Date().toISOString(),
          };
          if (typeof (unifiedSocketService.reliability as any).emitCircuitBreakerUpdate === 'function') {
            (unifiedSocketService.reliability as any).emitCircuitBreakerUpdate(statusPayload);
          } else {
            logger.warn('emitCircuitBreakerUpdate method not found on reliability namespace, attempting direct emit.');
            unifiedSocketService.emitToRoom('reliability-monitoring', RELIABILITY_EVENTS.CIRCUIT_BREAKER_UPDATE, statusPayload);
          }
        }
      } catch (error) {
        logger.error('Failed to emit WebSocket event for circuit breaker CLOSED state', { error, breakerName: name });
      }
    });

    breaker.on('success', () => {
      logger.debug(`Circuit breaker SUCCESS for service: ${name}`, {
        breakerName: name,
        service: 'circuit-breaker',
      });
      this.updateMetrics(name, 'success');
    });

    breaker.on('failure', error => {
      logger.warn(`Circuit breaker FAILURE for service: ${name}`, {
        breakerName: name,
        error: error.message,
        service: 'circuit-breaker',
      });
      this.updateMetrics(name, 'failure');
    });

    breaker.on('timeout', () => {
      logger.warn(`Circuit breaker TIMEOUT for service: ${name}`, {
        breakerName: name,
        service: 'circuit-breaker',
      });
      this.updateMetrics(name, 'timeout');
    });

    breaker.on('reject', () => {
      logger.warn(`Circuit breaker REJECTED request for service: ${name}`, {
        breakerName: name,
        service: 'circuit-breaker',
      });
      this.updateMetrics(name, 'reject');
    });
  }

  /**
   * Update metrics for circuit breaker events
   */
  private updateMetrics(name: string, event: string): void {
    const metrics = this.metrics.get(name);
    if (!metrics) return;

    const previousState = metrics.state;

    switch (event) {
      case 'closed':
      case 'half_open':
      case 'open':
        const newState = event.toUpperCase();
        if (previousState !== newState) {
          // Record state change in history
          this.recordStateChange(name, previousState, newState, event);
        }
        metrics.state = newState;
        metrics.lastStateChange = new Date();
        break;
      case 'failure':
        metrics.failureCount++;
        metrics.totalRequests++;
        break;
      case 'reject':
        metrics.rejectCount++;
        break;
      case 'success':
        metrics.successCount++;
        metrics.totalRequests++;
        break;
      case 'timeout':
        metrics.timeoutCount++;
        metrics.totalRequests++;
        break;
    }
  }

  /**
   * Record circuit breaker state change in history
   */
  private recordStateChange(breakerName: string, previousState: string, newState: string, reason: string): void {
    try {
      const metrics = this.metrics.get(breakerName);
      if (!metrics) return;

      const errorRate = metrics.totalRequests > 0
        ? (metrics.failureCount / metrics.totalRequests) * 100
        : 0;

      const historyEntry: CircuitBreakerHistoryEntry = {
        timestamp: new Date().toISOString(),
        breakerName,
        previousState,
        newState,
        reason,
        metrics: {
          failureCount: metrics.failureCount,
          successCount: metrics.successCount,
          totalRequests: metrics.totalRequests,
          errorRate: Math.round(errorRate * 100) / 100,
        },
      };

      // Add to history storage
      circuitBreakerHistory.push(historyEntry);

      // Keep only the last MAX_HISTORY_STORAGE entries
      if (circuitBreakerHistory.length > MAX_HISTORY_STORAGE) {
        circuitBreakerHistory.shift();
      }

      logger.info('Circuit breaker state change recorded', {
        breakerName,
        previousState,
        newState,
        reason,
        errorRate: historyEntry.metrics.errorRate,
        service: 'circuit-breaker',
      });
    } catch (error) {
      logger.error('Failed to record circuit breaker state change', {
        error: error instanceof Error ? error.message : 'Unknown error',
        breakerName,
        service: 'circuit-breaker',
      });
    }
  }
}

// Global circuit breaker registry instance
export const circuitBreakerRegistry = new CircuitBreakerRegistry();

/**
 * Convenience function to execute a function with circuit breaker protection
 */
export async function executeWithCircuitBreaker<T>(
  serviceName: string,
  fn: () => Promise<T>,
  config?: Partial<CircuitBreakerConfig>,
): Promise<T> {
  const breaker = circuitBreakerRegistry.getBreaker(serviceName, config);
  return (await breaker.fire(fn)) as T;
}

/**
 * Convenience function to get circuit breaker status
 */
export function getCircuitBreakerStatus(name?: string): any {
  return circuitBreakerRegistry.getStatus(name);
}

/**
 * Get circuit breaker history for specified timeframe and breaker
 */
export function getCircuitBreakerHistory(
  timeframe: '1h' | '6h' | '24h' | '7d' = '24h',
  breakerName?: string
): {
  timeframe: string;
  breakerName?: string;
  entries: CircuitBreakerHistoryEntry[];
  summary: {
    totalStateChanges: number;
    stateDistribution: Record<string, number>;
    mostActiveBreaker?: string;
    averageErrorRate: number;
  };
} {
  try {
    const now = new Date();
    let cutoffTime: Date;

    // Calculate cutoff time based on timeframe
    switch (timeframe) {
      case '1h':
        cutoffTime = new Date(now.getTime() - 60 * 60 * 1000);
        break;
      case '6h':
        cutoffTime = new Date(now.getTime() - 6 * 60 * 60 * 1000);
        break;
      case '24h':
        cutoffTime = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        break;
      case '7d':
        cutoffTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      default:
        cutoffTime = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    }

    // Filter entries within timeframe and optionally by breaker name
    let filteredEntries = circuitBreakerHistory.filter(
      entry => new Date(entry.timestamp) >= cutoffTime
    );

    if (breakerName) {
      filteredEntries = filteredEntries.filter(
        entry => entry.breakerName === breakerName
      );
    }

    // Calculate summary statistics
    const totalStateChanges = filteredEntries.length;

    // State distribution
    const stateDistribution = filteredEntries.reduce((dist, entry) => {
      dist[entry.newState] = (dist[entry.newState] || 0) + 1;
      return dist;
    }, {} as Record<string, number>);

    // Most active breaker (if not filtering by specific breaker)
    let mostActiveBreaker: string | undefined;
    if (!breakerName && filteredEntries.length > 0) {
      const breakerCounts = filteredEntries.reduce((counts, entry) => {
        counts[entry.breakerName] = (counts[entry.breakerName] || 0) + 1;
        return counts;
      }, {} as Record<string, number>);

      mostActiveBreaker = Object.entries(breakerCounts).reduce(
        (max, [breaker, count]) => count > max.count ? { breaker, count } : max,
        { breaker: '', count: 0 }
      ).breaker;
    }

    // Average error rate
    const averageErrorRate = filteredEntries.length > 0
      ? filteredEntries.reduce((sum, entry) => sum + entry.metrics.errorRate, 0) / filteredEntries.length
      : 0;

    return {
      timeframe,
      breakerName,
      entries: filteredEntries,
      summary: {
        totalStateChanges,
        stateDistribution,
        mostActiveBreaker,
        averageErrorRate: Math.round(averageErrorRate * 100) / 100,
      },
    };
  } catch (error) {
    logger.error('Failed to get circuit breaker history', {
      error: error instanceof Error ? error.message : 'Unknown error',
      timeframe,
      breakerName,
      service: 'circuit-breaker',
    });

    // Return empty response on error
    return {
      timeframe,
      breakerName,
      entries: [],
      summary: {
        totalStateChanges: 0,
        stateDistribution: {},
        averageErrorRate: 0,
      },
    };
  }
}

/**
 * Convenience function to reset circuit breakers
 */
export function resetCircuitBreaker(name?: string): void {
  circuitBreakerRegistry.reset(name);
}

export default circuitBreakerRegistry;
