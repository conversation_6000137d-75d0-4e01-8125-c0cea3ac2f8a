/**
 * @file UI preferences management hook using Zustand UiStore
 * @module hooks/useUiPreferences
 */

import { useCallback } from 'react';

import { useUiStore } from '@/lib/stores/zustand/uiStore';

/**
 * Hook for UI preferences management
 * Provides convenient methods for managing user interface preferences
 */
export const useUiPreferences = () => {
  // Font size preferences
  const fontSize = useUiStore(state => state.fontSize);
  const setFontSize = useUiStore(state => state.setFontSize);

  // Notification preferences
  const notificationsEnabled = useUiStore(state => state.notificationsEnabled);
  const toggleNotifications = useUiStore(state => state.toggleNotifications);

  // WorkHub-specific preferences
  const tableDensity = useUiStore(state => state.tableDensity);
  const setTableDensity = useUiStore(state => state.setTableDensity);

  const mapViewPreference = useUiStore(state => state.mapViewPreference);
  const setMapViewPreference = useUiStore(state => state.setMapViewPreference);

  const dashboardLayout = useUiStore(state => state.dashboardLayout);
  const setDashboardLayout = useUiStore(state => state.setDashboardLayout);

  const autoRefreshInterval = useUiStore(state => state.autoRefreshInterval);
  const setAutoRefreshInterval = useUiStore(
    state => state.setAutoRefreshInterval
  );

  /**
   * Get font size CSS class
   */
  const getFontSizeClass = useCallback(() => {
    switch (fontSize) {
      case 'large': {
        return 'text-lg';
      }
      case 'small': {
        return 'text-sm';
      }
      default: {
        return 'text-base';
      }
    }
  }, [fontSize]);

  /**
   * Get table density CSS classes
   */
  const getTableDensityClasses = useCallback(() => {
    switch (tableDensity) {
      case 'compact': {
        return {
          cell: 'py-1 px-2',
          row: 'h-8',
          table: 'table-compact',
        };
      }
      case 'spacious': {
        return {
          cell: 'py-4 px-4',
          row: 'h-16',
          table: 'table-spacious',
        };
      }
      default: { // comfortable
        return {
          cell: 'py-2 px-3',
          row: 'h-12',
          table: 'table-comfortable',
        };
      }
    }
  }, [tableDensity]);

  /**
   * Get dashboard layout CSS classes
   */
  const getDashboardLayoutClasses = useCallback(() => {
    switch (dashboardLayout) {
      case 'cards': {
        return 'grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6';
      }
      case 'grid': {
        return 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4';
      }
      case 'list': {
        return 'flex flex-col space-y-4';
      }
      default: {
        return 'grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6';
      }
    }
  }, [dashboardLayout]);

  /**
   * Enable notifications
   */
  const enableNotifications = useCallback(() => {
    if (!notificationsEnabled) {
      toggleNotifications();
    }
  }, [notificationsEnabled, toggleNotifications]);

  /**
   * Disable notifications
   */
  const disableNotifications = useCallback(() => {
    if (notificationsEnabled) {
      toggleNotifications();
    }
  }, [notificationsEnabled, toggleNotifications]);

  /**
   * Reset all preferences to defaults
   */
  const resetPreferences = useCallback(() => {
    setFontSize('medium');
    setTableDensity('comfortable');
    setMapViewPreference('roadmap');
    setDashboardLayout('cards');
    setAutoRefreshInterval(30);
    // Note: We don't reset notifications as that's a user choice
  }, [
    setFontSize,
    setTableDensity,
    setMapViewPreference,
    setDashboardLayout,
    setAutoRefreshInterval,
  ]);

  /**
   * Get all preferences as an object
   */
  const getAllPreferences = useCallback(() => {
    return {
      autoRefreshInterval,
      dashboardLayout,
      fontSize,
      mapViewPreference,
      notificationsEnabled,
      tableDensity,
    };
  }, [
    fontSize,
    notificationsEnabled,
    tableDensity,
    mapViewPreference,
    dashboardLayout,
    autoRefreshInterval,
  ]);

  return {
    // Auto-refresh preferences
    autoRefreshInterval,
    // Dashboard preferences
    dashboardLayout,
    disableNotifications,

    enableNotifications,
    // Font size
    fontSize,
    getAllPreferences,
    getDashboardLayoutClasses,

    getFontSizeClass,
    getTableDensityClasses,
    // Map preferences
    mapViewPreference,

    // Notifications
    notificationsEnabled,
    // Utilities
    resetPreferences,

    setAutoRefreshInterval,
    setDashboardLayout,
    setFontSize,

    setMapViewPreference,
    setTableDensity,

    // Table preferences
    tableDensity,
    toggleNotifications,
  };
};
