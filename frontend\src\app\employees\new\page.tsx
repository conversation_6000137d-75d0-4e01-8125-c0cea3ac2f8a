'use client';

import { UsersRound } from 'lucide-react';
import { useRouter } from 'next/navigation';
import React from 'react'; // Removed useState

import type { EmployeeFormData } from '@/lib/schemas/employeeSchemas'; // Assuming this is compatible with CreateEmployeeData
import type {
  CreateEmployeeData,
  DriverAvailabilityPrisma, // Added
  Employee,
  EmployeeRolePrisma, // Added
  EmployeeStatusPrisma, // Added
} from '@/lib/types/domain'; // Added CreateEmployeeData

import { EmployeeForm } from '@/components/features/employees/forms/employeeForm';
import { PageHeader } from '@/components/ui/PageHeader';
import { usePredefinedEntityToast } from '@/hooks/forms/useFormToast';
import { useCreateEmployee } from '@/lib/stores/queries/useEmployees'; // Updated import

const AddEmployeePage = () => {
  const router = useRouter();
  const { showEntityCreated, showEntityCreationError } =
    usePredefinedEntityToast('employee');
  const createEmployee = useCreateEmployee();

  const handleSubmit = async (formData: EmployeeFormData) => {
    const createData: CreateEmployeeData = {
      // Note: Vehicle assignments are now context-specific (per task/delegation)
      availability: formData.availability ?? null,
      contactEmail: formData.contactEmail ?? null,
      contactInfo: formData.contactInfo,
      contactMobile: formData.contactMobile ?? null,
      contactPhone: formData.contactPhone ?? null,
      department: formData.department ?? null,
      employeeId: formData.employeeId,
      fullName: formData.fullName ?? null,
      generalAssignments: formData.generalAssignments ?? [],
      hireDate: formData.hireDate ?? null,
      name: formData.name,
      notes: formData.notes ?? null,
      position: formData.position ?? null,
      profileImageUrl: formData.profileImageUrl ?? null,
      role: formData.role,
      shiftSchedule: formData.shiftSchedule ?? null,
      skills: formData.skills ?? [],
      status: formData.status.replace(' ', '_') as EmployeeStatusPrisma,
    };

    try {
      const newEmployee = await createEmployee.mutateAsync(createData);
      showEntityCreated(newEmployee);
      router.push('/employees');
    } catch (error: any) {
      console.error('Failed to add employee:', error);
      const errorMessage =
        error.response?.data?.error ||
        error.message ||
        'An unexpected error occurred.';
      showEntityCreationError(errorMessage);
    }
  };

  return (
    <div className="container mx-auto space-y-8 py-8">
      <PageHeader
        description="Enter the details for the new employee."
        icon={UsersRound}
        title="Add New Employee"
      />
      {createEmployee.error && ( // Display error from the hook
        <div
          className="relative mb-4 rounded border border-red-400 bg-red-100 px-4 py-3 text-red-700"
          role="alert"
        >
          <strong className="font-bold">Error: </strong>
          <span className="block sm:inline">
            {(createEmployee.error as any)?.message ||
              'Failed to add employee.'}
          </span>
        </div>
      )}
      <EmployeeForm
        isEditing={false}
        isLoading={createEmployee.isPending} // Use isPending from the hook
        onSubmit={handleSubmit}
      />
    </div>
  );
};

export default AddEmployeePage;
