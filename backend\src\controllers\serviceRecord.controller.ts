import type { NextFunction, Request, Response } from 'express';

import type { Prisma, ServiceRecord as PrismaServiceRecord } from '../generated/prisma/index.js';
import type { ServiceRecordCreate } from '../schemas/serviceRecord.schema.js'; // For validated body type
import type {
  ServiceRecordCreatedPayload,
  ServiceRecordDeletedPayload,
  ServiceRecordUpdatedPayload,
  ServiceRecord as WebSocketServiceRecordPayload,
} from '../types/websocketEvents.js';

import prisma from '../models/index.js';
import * as serviceRecordModel from '../models/serviceRecord.model.js';
import { getUnifiedWebSocketService } from '../services/UnifiedWebSocketService.js';
import { CRUD_EVENTS } from '../services/WebSocketEventManager.js';
import HttpError from '../utils/HttpError.js';
import logger from '../utils/logger.js';

const CRUD_ROOM = 'entity-updates';

// Helper function to map Prisma ServiceRecord to our WebSocket ServiceRecord Payload
const mapPrismaServiceRecordToPayload = (
  prismaRecord: PrismaServiceRecord,
): WebSocketServiceRecordPayload => {
  return {
    id: prismaRecord.id,
    vehicleId: prismaRecord.vehicleId,
    serviceDate: prismaRecord.date.toISOString(),
    serviceType: Array.isArray(prismaRecord.servicePerformed)
      ? prismaRecord.servicePerformed.join(', ')
      : prismaRecord.servicePerformed,
    description: prismaRecord.notes || '',
    cost: prismaRecord.cost ? prismaRecord.cost.toNumber() : null,
    mechanicId: prismaRecord.employeeId,
  };
};

export const createServiceRecord = async (
  req: Request,
  res: Response,
  next: NextFunction,
): Promise<void> => {
  try {
    const vehicleId = parseInt(req.params.vehicleId, 10);
    if (isNaN(vehicleId)) {
      throw new HttpError('Invalid vehicle ID format', 400, 'INVALID_ID');
    }

    const validatedBody = req.body as ServiceRecordCreate;
    const {
      cost,
      date,
      employeeId: bodyEmployeeId,
      notes,
      odometer,
      servicePerformed,
    } = validatedBody;

    const createData: Prisma.ServiceRecordCreateInput = {
      cost: cost ? Number(cost) : undefined,
      date: new Date(date),
      id: `service_${vehicleId}_${Date.now()}`,
      notes,
      odometer: Number(odometer),
      servicePerformed,
      updatedAt: new Date(),
      Vehicle: { connect: { id: vehicleId } },
    };

    if (bodyEmployeeId) {
      createData.Employee = { connect: { id: Number(bodyEmployeeId) } };
    }

    const newServiceRecord = await serviceRecordModel.createServiceRecord(createData);
    if (!newServiceRecord) {
      throw new HttpError('Could not create service record.', 400, 'CREATE_FAILED');
    }

    try {
      const unifiedSocketService = getUnifiedWebSocketService();
      const payload: ServiceRecordCreatedPayload =
        mapPrismaServiceRecordToPayload(newServiceRecord);
      unifiedSocketService.emitToRoom(CRUD_ROOM, CRUD_EVENTS.SERVICE_RECORD_CREATED, payload);
    } catch (wsError) {
      logger.error('Failed to emit WebSocket event for service record creation', { wsError });
    }
    res.status(201).json(newServiceRecord);
  } catch (error: any) {
    if (error.message.includes('not found')) {
      next(new HttpError(error.message, 404, 'NOT_FOUND'));
    } else {
      next(error);
    }
  }
};

export const getAllServiceRecordsForVehicle = async (
  req: Request,
  res: Response,
  next: NextFunction,
): Promise<void> => {
  try {
    const vehicleId = parseInt(req.params.vehicleId, 10);
    if (isNaN(vehicleId)) {
      throw new HttpError('Invalid vehicle ID format', 400, 'INVALID_ID');
    }
    const records = await serviceRecordModel.getAllServiceRecords(vehicleId);
    res.status(200).json(records);
  } catch (error: any) {
    next(error);
  }
};

export const getServiceRecordById = async (
  req: Request,
  res: Response,
  next: NextFunction,
): Promise<void> => {
  try {
    const { id, vehicleId } = req.params;
    const vehicleIdNum = parseInt(vehicleId, 10);
    if (isNaN(vehicleIdNum)) {
      throw new HttpError('Invalid vehicle ID format', 400, 'INVALID_ID');
    }

    const record = await serviceRecordModel.getServiceRecordById(id);
    if (!record || record.vehicleId !== vehicleIdNum) {
      throw new HttpError(
        'Service record not found or does not belong to this vehicle',
        404,
        'NOT_FOUND',
      );
    }
    res.status(200).json(record);
  } catch (error: any) {
    next(error);
  }
};

export const updateServiceRecord = async (
  req: Request,
  res: Response,
  next: NextFunction,
): Promise<void> => {
  try {
    const { id, vehicleId } = req.params;
    const vehicleIdNum = parseInt(vehicleId, 10);
    if (isNaN(vehicleIdNum)) {
      throw new HttpError('Invalid vehicle ID format', 400, 'INVALID_ID');
    }

    const existingRecord = await serviceRecordModel.getServiceRecordById(id);
    if (!existingRecord || existingRecord.vehicleId !== vehicleIdNum) {
      throw new HttpError(
        'Service record not found or does not belong to this vehicle for update.',
        404,
        'NOT_FOUND',
      );
    }

    const serviceRecordDataFromRequest = req.body;
    const updateData: Prisma.ServiceRecordUpdateInput = {
      ...serviceRecordDataFromRequest,
    };

    if (serviceRecordDataFromRequest.date) {
      updateData.date = new Date(serviceRecordDataFromRequest.date);
    }

    if (serviceRecordDataFromRequest.employeeId) {
      updateData.Employee = {
        connect: { id: Number(serviceRecordDataFromRequest.employeeId) },
      };
      if ('employeeId' in updateData) delete (updateData as any).employeeId;
    } else if (
      serviceRecordDataFromRequest.hasOwnProperty('employeeId') &&
      serviceRecordDataFromRequest.employeeId === null
    ) {
      updateData.Employee = { disconnect: true };
      if ('employeeId' in updateData) delete (updateData as any).employeeId;
    }
    updateData.updatedAt = new Date();

    const updatedRecord = await serviceRecordModel.updateServiceRecord(id, updateData);

    if (!updatedRecord) {
      throw new HttpError(
        'Service record not found or could not be updated.',
        404,
        'UPDATE_FAILED',
      );
    }

    try {
      const unifiedSocketService = getUnifiedWebSocketService();
      const payload: ServiceRecordUpdatedPayload = mapPrismaServiceRecordToPayload(updatedRecord);
      unifiedSocketService.emitToRoom(CRUD_ROOM, CRUD_EVENTS.SERVICE_RECORD_UPDATED, payload);
    } catch (wsError) {
      logger.error('Failed to emit WebSocket event for service record update', { wsError });
    }
    res.status(200).json(updatedRecord);
  } catch (error: any) {
    next(error);
  }
};

export const deleteServiceRecord = async (
  req: Request,
  res: Response,
  next: NextFunction,
): Promise<void> => {
  try {
    const { id, vehicleId } = req.params;
    const vehicleIdNum = parseInt(vehicleId, 10);
    if (isNaN(vehicleIdNum)) {
      throw new HttpError('Invalid vehicle ID format', 400, 'INVALID_ID');
    }

    const existingRecord = await serviceRecordModel.getServiceRecordById(id);
    if (!existingRecord || existingRecord.vehicleId !== vehicleIdNum) {
      throw new HttpError(
        'Service record not found or does not belong to this vehicle for deletion.',
        404,
        'NOT_FOUND',
      );
    }

    const deletedRecord = await serviceRecordModel.deleteServiceRecord(id);
    if (!deletedRecord) {
      throw new HttpError('Service record not found or could not be deleted', 404, 'DELETE_FAILED');
    }

    try {
      const unifiedSocketService = getUnifiedWebSocketService();
      const payload: ServiceRecordDeletedPayload = { id: deletedRecord.id };
      unifiedSocketService.emitToRoom(CRUD_ROOM, CRUD_EVENTS.SERVICE_RECORD_DELETED, payload);
    } catch (wsError) {
      logger.error('Failed to emit WebSocket event for service record deletion', { wsError });
    }
    res.status(200).json({
      message: 'Service record deleted successfully',
      record: deletedRecord,
    });
  } catch (error: any) {
    next(error);
  }
};

/**
 * Get all service records without requiring a vehicle ID
 * This is used for the direct /api/servicerecords endpoint
 */
export const getAllServiceRecordsDirect = async (
  req: Request,
  res: Response,
  next: NextFunction,
): Promise<void> => {
  try {
    logger.info('Fetching all service records directly', {
      ip: req.ip,
      query: req.query,
      userAgent: req.headers['user-agent'],
    });

    // Call the model function without a vehicleId
    const records = await serviceRecordModel.getAllServiceRecords();

    res.status(200).json(records);
  } catch (error) {
    next(error);
  }
};

/**
 * Get a specific service record by ID without requiring a vehicle ID
 * This is used for the direct /api/servicerecords/:id endpoint
 */
export const getServiceRecordByIdDirect = async (
  req: Request,
  res: Response,
  next: NextFunction,
): Promise<void> => {
  try {
    const { id } = req.params;
    logger.info(`Fetching service record by ID directly: ${id}`);
    const record = await serviceRecordModel.getServiceRecordById(id);
    if (record) {
      res.status(200).json(record);
    } else {
      throw new HttpError('Service record not found', 404, 'NOT_FOUND');
    }
  } catch (error) {
    logger.error(`Error fetching service record by ID directly: ${req.params.id}`, {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
    });
    next(error);
  }
};

/**
 * Get all service records enriched with vehicle and employee information
 * This is used for the /api/servicerecords/enriched endpoint
 */
export const getEnrichedServiceRecords = async (
  req: Request,
  res: Response,
  next: NextFunction,
): Promise<void> => {
  try {
    logger.info('Fetching enriched service records', {
      ip: req.ip,
      query: req.query,
      userAgent: req.headers['user-agent'],
    });

    const serviceRecords = await serviceRecordModel.getAllServiceRecords();

    if (serviceRecords.length === 0) {
      res.status(200).json([]);
      return;
    }

    const vehicleIds = [...new Set(serviceRecords.map(record => record.vehicleId))];
    const employeeIds = [
      ...new Set(serviceRecords.flatMap(record => (record.employeeId ? [record.employeeId] : []))),
    ];

    const [vehicles, employees] = await Promise.all([
      prisma.vehicle.findMany({ where: { id: { in: vehicleIds } } }),
      prisma.employee.findMany({ where: { id: { in: employeeIds } } }),
    ]);

    const vehicleMap = new Map(vehicles.map(v => [v.id, v]));
    const employeeMap = new Map(employees.map(e => [e.id, e]));

    const enrichedRecords = serviceRecords.map(record => ({
      ...record,
      vehicleMake: vehicleMap.get(record.vehicleId)?.make,
      vehicleModel: vehicleMap.get(record.vehicleId)?.model,
      vehicleYear: vehicleMap.get(record.vehicleId)?.year,
      licensePlate: vehicleMap.get(record.vehicleId)?.licensePlate,
      employeeName: record.employeeId ? employeeMap.get(record.employeeId)?.name : undefined,
    }));

    res.status(200).json(enrichedRecords);
  } catch (error) {
    logger.error('Error fetching enriched service records', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
    });
    next(error);
  }
};
