/**
 * Form Toast Hook - Integrates generic toast service with form operations
 *
 * This hook provides a consistent interface for showing toast notifications
 * in form components, supporting both generic and entity-specific messaging.
 */

import { useCallback } from 'react';

import {
  type EntityToastConfig,
  type GenericEntityToastService,
  toastService,
  createEntityToastService,
} from '@/lib/services/toastService';

export interface FormToastOptions {
  successTitle?: string;
  successDescription?: string;
  errorTitle?: string;
  errorDescription?: string;
}

export interface EntityFormToastOptions<T> {
  entityConfig?: EntityToastConfig<T>;
  entityService?: GenericEntityToastService<T>;
}

/**
 * Hook for generic form toast notifications
 */
export function useFormToast() {
  const showSuccess = useCallback((title: string, description?: string) => {
    return toastService.success(title, description);
  }, []);

  const showError = useCallback((title: string, description?: string) => {
    return toastService.error(title, description);
  }, []);

  const showInfo = useCallback((title: string, description?: string) => {
    return toastService.info(title, description);
  }, []);

  const showFormSuccess = useCallback(
    (options?: FormToastOptions) => {
      return showSuccess(
        options?.successTitle || 'Success',
        options?.successDescription || 'Operation completed successfully'
      );
    },
    [showSuccess]
  );

  const showFormError = useCallback(
    (error: Error | string, options?: FormToastOptions) => {
      const errorMessage = error instanceof Error ? error.message : error;
      return showError(
        options?.errorTitle || 'Error',
        options?.errorDescription ||
          errorMessage ||
          'An unexpected error occurred'
      );
    },
    [showError]
  );

  return {
    showSuccess,
    showError,
    showInfo,
    showFormSuccess,
    showFormError,
  };
}

/**
 * Hook for entity-specific form toast notifications
 */
export function useEntityFormToast<T>(
  entityConfig?: EntityToastConfig<T>,
  entityService?: GenericEntityToastService<T>
) {
  const { showFormSuccess, showFormError } = useFormToast();

  // Create or use provided entity service
  const entityToastService =
    entityService ||
    (entityConfig ? createEntityToastService(entityConfig) : null);

  const showEntityCreated = useCallback(
    (entity: T) => {
      if (entityToastService) {
        return entityToastService.entityCreated(entity);
      }
      return showFormSuccess({
        successTitle: 'Created',
        successDescription: 'Item has been created successfully',
      });
    },
    [entityToastService, showFormSuccess]
  );

  const showEntityUpdated = useCallback(
    (entity: T) => {
      if (entityToastService) {
        return entityToastService.entityUpdated(entity);
      }
      return showFormSuccess({
        successTitle: 'Updated',
        successDescription: 'Item has been updated successfully',
      });
    },
    [entityToastService, showFormSuccess]
  );

  const showEntityDeleted = useCallback(
    (entity: T) => {
      if (entityToastService) {
        return entityToastService.entityDeleted(entity);
      }
      return showFormSuccess({
        successTitle: 'Deleted',
        successDescription: 'Item has been deleted successfully',
      });
    },
    [entityToastService, showFormSuccess]
  );

  const showEntityCreationError = useCallback(
    (error: Error | string) => {
      if (entityToastService) {
        const errorMessage = error instanceof Error ? error.message : error;
        return entityToastService.entityCreationError(errorMessage);
      }
      return showFormError(error, { errorTitle: 'Creation Failed' });
    },
    [entityToastService, showFormError]
  );

  const showEntityUpdateError = useCallback(
    (error: Error | string) => {
      if (entityToastService) {
        const errorMessage = error instanceof Error ? error.message : error;
        return entityToastService.entityUpdateError(errorMessage);
      }
      return showFormError(error, { errorTitle: 'Update Failed' });
    },
    [entityToastService, showFormError]
  );

  const showEntityDeletionError = useCallback(
    (error: Error | string) => {
      if (entityToastService) {
        const errorMessage = error instanceof Error ? error.message : error;
        return entityToastService.entityDeletionError(errorMessage);
      }
      return showFormError(error, { errorTitle: 'Deletion Failed' });
    },
    [entityToastService, showFormError]
  );

  return {
    showEntityCreated,
    showEntityUpdated,
    showEntityDeleted,
    showEntityCreationError,
    showEntityUpdateError,
    showEntityDeletionError,
    // Also expose generic methods
    showFormSuccess,
    showFormError,
  };
}

/**
 * Hook for predefined entity toast services
 */
export function usePredefinedEntityToast(
  entityType: 'employee' | 'vehicle' | 'task' | 'delegation'
) {
  let entityService: GenericEntityToastService<any>;

  // Lazy import to avoid circular dependencies
  switch (entityType) {
    case 'employee':
      // eslint-disable-next-line @typescript-eslint/no-var-requires
      entityService = require('@/lib/services/toastService').employeeToast;
      break;
    case 'vehicle':
      // eslint-disable-next-line @typescript-eslint/no-var-requires
      entityService = require('@/lib/services/toastService').vehicleToast;
      break;
    case 'task':
      // eslint-disable-next-line @typescript-eslint/no-var-requires
      entityService = require('@/lib/services/toastService').taskToast;
      break;
    case 'delegation':
      // eslint-disable-next-line @typescript-eslint/no-var-requires
      entityService = require('@/lib/services/toastService').delegationToast;
      break;
    default:
      throw new Error(`Unknown entity type: ${entityType}`);
  }

  return useEntityFormToast(undefined, entityService);
}
