/**
 * @file SearchableAssignmentSection component with search and filter capabilities
 * @module components/delegations/detail/assignments/SearchableAssignmentSection
 */

import React, { useState, useMemo } from 'react';
import { Search, Filter, X } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface SearchableAssignmentSectionProps<T> {
  title: string;
  icon: React.ElementType;
  items: T[];
  renderItem: (item: T, index: number) => React.ReactNode;
  emptyMessage: string;
  searchFields: (keyof T)[];
  filterOptions?: { label: string; value: string; filter: (item: T) => boolean }[];
  className?: string;
}

/**
 * SearchableAssignmentSection component with search and filter capabilities
 * Enhanced version of AssignmentSection with search and filtering functionality
 */
export function SearchableAssignmentSection<T>({
  title,
  icon: Icon,
  items,
  renderItem,
  emptyMessage,
  searchFields,
  filterOptions = [],
  className,
}: SearchableAssignmentSectionProps<T>) {
  const [searchTerm, setSearchTerm] = useState('');
  const [activeFilter, setActiveFilter] = useState<string | null>(null);

  const filteredItems = useMemo(() => {
    let filtered = items;

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(item =>
        searchFields.some(field => {
          const value = item[field];
          if (typeof value === 'string') {
            return value.toLowerCase().includes(searchTerm.toLowerCase());
          }
          if (typeof value === 'object' && value !== null) {
            return JSON.stringify(value).toLowerCase().includes(searchTerm.toLowerCase());
          }
          return false;
        })
      );
    }

    // Apply active filter
    if (activeFilter) {
      const filterOption = filterOptions.find(option => option.value === activeFilter);
      if (filterOption) {
        filtered = filtered.filter(filterOption.filter);
      }
    }

    return filtered;
  }, [items, searchTerm, activeFilter, searchFields, filterOptions]);

  const clearFilters = () => {
    setSearchTerm('');
    setActiveFilter(null);
  };

  const hasActiveFilters = searchTerm || activeFilter;

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Icon className="h-5 w-5 text-blue-600 dark:text-blue-400" />
            <span>{title}</span>
          </div>
          <div className="flex items-center space-x-2">
            {hasActiveFilters && (
              <Button
                variant="ghost"
                size="sm"
                onClick={clearFilters}
                className="h-8 px-2 text-xs"
              >
                <X className="h-3 w-3 mr-1" />
                Clear
              </Button>
            )}
            <Badge variant="secondary" className="bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900/30 dark:text-blue-300 dark:border-blue-800">
              {filteredItems.length} of {items.length}
            </Badge>
          </div>
        </CardTitle>
        
        {/* Search and Filter Controls */}
        {(items.length > 3 || filterOptions.length > 0) && (
          <div className="flex items-center space-x-2 mt-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder={`Search ${title.toLowerCase()}...`}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            
            {filterOptions.length > 0 && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm" className="shrink-0">
                    <Filter className="h-4 w-4 mr-2" />
                    Filter
                    {activeFilter && (
                      <Badge variant="secondary" className="ml-2 h-5 px-1 text-xs">
                        1
                      </Badge>
                    )}
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem
                    onClick={() => setActiveFilter(null)}
                    className={!activeFilter ? 'bg-blue-50 dark:bg-blue-900/30' : ''}
                  >
                    All {title}
                  </DropdownMenuItem>
                  {filterOptions.map((option) => (
                    <DropdownMenuItem
                      key={option.value}
                      onClick={() => setActiveFilter(option.value)}
                      className={activeFilter === option.value ? 'bg-blue-50 dark:bg-blue-900/30' : ''}
                    >
                      {option.label}
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            )}
          </div>
        )}
      </CardHeader>
      
      <CardContent>
        {filteredItems.length > 0 ? (
          <div className="space-y-3">
            {filteredItems.map(renderItem)}
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center py-8 text-center">
            <div className="rounded-full bg-gray-100 dark:bg-gray-800 p-3 mb-3">
              {hasActiveFilters ? (
                <Search className="h-6 w-6 text-gray-400" />
              ) : (
                <Icon className="h-6 w-6 text-gray-400" />
              )}
            </div>
            <p className="text-gray-600 dark:text-gray-400 font-medium">
              {hasActiveFilters ? `No ${title.toLowerCase()} match your search` : emptyMessage}
            </p>
            {hasActiveFilters && (
              <Button
                variant="ghost"
                size="sm"
                onClick={clearFilters}
                className="mt-2"
              >
                Clear filters
              </Button>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}

export default SearchableAssignmentSection;
