/**
 * @file TypeScript interfaces for WebSocket event payloads.
 * Defines the expected data structures for events emitted by the backend.
 * @module types/websocketEvents
 */

// --- Placeholder Domain Model Interfaces ---
// These would typically be imported from a central domain types definition file
// e.g., import { Vehicle, Employee, HealthCheck, Alert } from './domain';

export interface Vehicle {
  id: string | number;
  make: string;
  model: string;
  year: number;
  vin: string;
  // ... other vehicle properties
}

export interface Employee {
  id: string | number;
  firstName: string;
  lastName: string;
  email: string;
  role?: string | null;
  department?: string | null;
  position?: string | null;
  profileImageUrl?: string | null;
  fullName?: string | null;
  name?: string | null;
  contactEmail?: string | null;
  // ... other employee properties from Prisma model that should be in payload
}

export enum HealthStatus { // Mirrored from frontend for HealthCheck
  HEALTHY = 'healthy',
  DEGRADED = 'degraded',
  UNHEALTHY = 'unhealthy',
}

export interface HealthCheck { // Mirrored from frontend for now
  status: HealthStatus;
  timestamp: string;
  services: Array<{
    name: string;
    status: HealthStatus;
    message?: string;
  }>;
  // ... other health check properties
}

export enum AlertSeverity { // Mirrored from frontend for Alert
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

export enum AlertStatus { // Mirrored from frontend for Alert
  ACTIVE = 'active',
  ACKNOWLEDGED = 'acknowledged',
  RESOLVED = 'resolved',
}

export interface Alert { // Mirrored from frontend for now
  id: string;
  message: string;
  severity: AlertSeverity;
  status: AlertStatus;
  timestamp: string;
  source?: string;
  // ... other alert properties
}

export interface SystemMetrics {
  cpuUsage: number; // percentage
  memoryUsage: number; // percentage
  activeConnections: number;
  // ... other metrics
}

export interface CircuitBreakerStatus {
  name: string;
  state: 'CLOSED' | 'OPEN' | 'HALF_OPEN';
  lastChanged: string;
  // ... other circuit breaker properties
}

// --- CRUD Event Payloads ---

export interface VehicleCreatedPayload extends Vehicle {}
export interface VehicleUpdatedPayload extends Partial<Vehicle> { id: string | number; } // Must include ID
export interface VehicleDeletedPayload { id: string | number; }
export interface RefreshVehiclesPayload { // Optional: criteria for refresh
  filter?: Record<string, any>;
}

export interface EmployeeCreatedPayload extends Employee {}
export interface EmployeeUpdatedPayload extends Partial<Employee> { id: string | number; }
export interface EmployeeDeletedPayload { id: string | number; }
export interface RefreshEmployeesPayload {
  filter?: Record<string, any>;
}

// Add other CRUD entity payloads similarly (Task, Delegation, ServiceRecord)

// --- Reliability Event Payloads ---

export interface HealthUpdatePayload extends HealthCheck {}
export interface AlertCreatedPayload extends Alert {}
export interface MetricsUpdatePayload extends SystemMetrics {}
export interface CircuitBreakerUpdatePayload extends CircuitBreakerStatus {}

// For events like JOIN_RELIABILITY_MONITORING, the payload might be minimal or just a confirmation
export interface JoinReliabilityMonitoringAckPayload {
  status: 'success' | 'failure';
  message?: string;
  room: string;
}

export interface ReliabilityMessagePayload {
  type: string; // e.g., 'info', 'warning', 'error'
  content: string | Record<string, any>;
  timestamp: string;
}

// --- System Event Payloads ---

export interface UserNotificationPayload {
  userId: string;
  title: string;
  message: string;
  type: 'info' | 'warning' | 'error' | 'success';
  link?: string; // Optional link for action
}

export interface GlobalAnnouncementPayload {
  title: string;
  message: string;
  severity: 'info' | 'maintenance' | 'critical';
  effectiveUntil?: string;
}

// --- Task Event Payloads ---
// Base Task interface (mirroring essential fields, adjust as needed from Prisma Task model)
export interface Task {
  id: string; // Prisma Task id is string `task_${Date.now()}_${Math.random()}`
  description: string;
  status: string; // e.g., 'Pending', 'InProgress', 'Completed', 'Cancelled' (matches PrismaTaskStatus)
  priority: string; // e.g., 'Low', 'Medium', 'High', 'Critical' (matches PrismaTaskPriority)
  location?: string | null;
  dateTime: string; // ISO string format
  deadline?: string | null; // ISO string format
  estimatedDuration?: number | null; // in minutes or hours, define unit
  notes?: string | null;
  // Relational fields (IDs are often sufficient for event payloads)
  vehicleId?: number | null;
  staffEmployeeId?: number | null;
  driverEmployeeId?: number | null;
  // subTasks might be too complex for a simple payload, consider sending IDs or a count
}

export interface TaskCreatedPayload extends Task {}
export interface TaskUpdatedPayload extends Partial<Task> { id: string; } // Must include ID
export interface TaskDeletedPayload { id: string; }

// --- Delegation Event Payloads ---
// Base Delegation interface (mirroring essential fields, adjust as needed from Prisma model)
export interface Delegation {
  id: string; // Prisma Delegation id is a UUID string
  eventName: string;
  location: string;
  durationFrom: string; // ISO string format
  durationTo: string; // ISO string format
  status: string; // e.g., 'Planned', 'Active', 'Completed', 'Cancelled' (matches PrismaDelegationStatus)
  notes?: string | null;
  // Relational IDs - keep it simple for payloads
  vehicleIds?: number[];
  driverEmployeeIds?: number[];
  escortEmployeeIds?: number[];
  // More complex fields like delegates, flightDetails are omitted for event payload simplicity
}

export interface DelegationCreatedPayload extends Delegation {}
export interface DelegationUpdatedPayload extends Partial<Delegation> { id: string; } // Must include ID
export interface DelegationDeletedPayload { id: string; }

// --- Service Record Event Payloads ---
// Base ServiceRecord interface (mirroring essential fields from Prisma model)
export interface ServiceRecord {
  id: string; 
  vehicleId: number; 
  serviceDate: string; // ISO string
  serviceType: string; // Corresponds to servicePerformed
  description: string; // Corresponds to notes
  cost?: number | null;
  mechanicId?: number | null; // Corresponds to employeeId
}

export interface ServiceRecordCreatedPayload extends ServiceRecord {}
export interface ServiceRecordUpdatedPayload extends Partial<ServiceRecord> { id: string; }
export interface ServiceRecordDeletedPayload { id: string; }

console.log('WebSocket Event Payload Interfaces loaded.'); 