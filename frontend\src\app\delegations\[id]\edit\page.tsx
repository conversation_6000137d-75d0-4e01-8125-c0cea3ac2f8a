'use client';

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Briefcase } from 'lucide-react';
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import { useMemo } from 'react';

import type { DelegationFormData } from '@/lib/schemas/delegationSchemas';
import type { UpdateDelegationRequest } from '@/lib/types/apiContracts'; // Corrected import
import type {
  DelegationStatusPrisma,
  FlightDetails as DomainFlightDetails, // For initialData mapping
} from '@/lib/types/domain';

import ErrorBoundary from '@/components/error-boundaries/ErrorBoundary';
import { DelegationFormContainer } from '@/components/features/delegations/forms';
import { ActionButton } from '@/components/ui/action-button';
import { DataLoader, SkeletonLoader } from '@/components/ui/loading';
import { PageHeader } from '@/components/ui/PageHeader';

import {
  useDelegationWithAssignments,
  useUpdateDelegation,
} from '@/lib/stores/queries/useDelegations';
import { useEmployeesByRole } from '@/lib/stores/queries/useEmployees';
import { useVehicles } from '@/lib/stores/queries/useVehicles';
import {
  formatDateForApi,
  formatDateForInput,
  formatFlightDateTimeForApi,
} from '@/lib/utils/dateUtils';

// Helper to transform Domain FlightDetails to FormData FlightDetails part
const mapDomainFlightToFormFlight = (
  flight: DomainFlightDetails | null | undefined
) => {
  if (!flight) return null;
  return {
    airport: flight.airport || '',
    dateTime: formatDateForInput(flight.dateTime, 'datetime-local'),
    flightNumber: flight.flightNumber || '',
    notes: flight.notes ?? null, // Ensure null if empty
    terminal: flight.terminal ?? null, // Ensure null if empty
  };
};

export default function EditDelegationPage() {
  const router = useRouter();
  const params = useParams();

  const delegationId = params?.id as string;

  // ✅ PERFORMANCE: Prefetch form data in parallel for faster loading
  useEmployeesByRole(); // Prefetch all employees
  useEmployeesByRole('driver'); // Prefetch driver employees (lowercase)
  useVehicles(); // Prefetch vehicles

  const {
    data: delegation,
    error: fetchError,
    isLoading: isLoadingDelegation,
    refetch,
  } = useDelegationWithAssignments(delegationId);

  const { mutateAsync: updateDelegationMutation } = useUpdateDelegation();

  // Handle form submission - this will be called by the form container
  const handleSubmit = async (formData: DelegationFormData): Promise<void> => {
    console.log('📝 Edit page handleSubmit called', { delegationId, formData });

    if (!delegationId) {
      throw new Error('Delegation ID is missing.');
    }

    try {
      const updatePayload: UpdateDelegationRequest = {
        delegates:
          formData.delegates
            .filter(d => d.name && d.title)
            .map(delegate => ({
              name: delegate.name,
              title: delegate.title,
              ...(delegate.notes !== undefined && { notes: delegate.notes }),
            })) ?? [],
        driverEmployeeIds: formData.driverEmployeeIds ?? [],
        durationFrom: formatDateForApi(formData.durationFrom) ?? '',
        durationTo: formatDateForApi(formData.durationTo) ?? '',
        escortEmployeeIds: formData.escortEmployeeIds ?? [],
        eventName: formData.eventName,
        flightArrivalDetails: formData.flightArrivalDetails
          ? {
              airport: formData.flightArrivalDetails.airport,
              dateTime: formatFlightDateTimeForApi(
                formData.flightArrivalDetails.dateTime
              ),
              flightNumber: formData.flightArrivalDetails.flightNumber,
              notes: formData.flightArrivalDetails.notes ?? null,
              terminal: formData.flightArrivalDetails.terminal ?? null,
            }
          : undefined,
        flightDepartureDetails: formData.flightDepartureDetails
          ? {
              airport: formData.flightDepartureDetails.airport,
              dateTime: formatFlightDateTimeForApi(
                formData.flightDepartureDetails.dateTime
              ),
              flightNumber: formData.flightDepartureDetails.flightNumber,
              notes: formData.flightDepartureDetails.notes ?? null,
              terminal: formData.flightDepartureDetails.terminal ?? null,
            }
          : undefined,
        imageUrl: formData.imageUrl,
        invitationFrom: formData.invitationFrom,
        invitationTo: formData.invitationTo,
        location: formData.location,
        notes: formData.notes,
        status: formData.status.replace(' ', '_') as DelegationStatusPrisma,
        vehicleIds: formData.vehicleIds,
      };

      const cleanedUpdatePayload = Object.fromEntries(
        Object.entries(updatePayload).filter(([, value]) => value !== undefined)
      );

      console.log('🔄 Calling updateDelegationMutation', {
        cleanedUpdatePayload,
        delegationId,
      });

      await updateDelegationMutation({
        data: cleanedUpdatePayload as UpdateDelegationRequest,
        id: delegationId,
      });

      console.log('✅ Delegation updated successfully');

      // Navigate to delegation detail page on success
      router.push(`/delegations/${delegationId}`);
    } catch (error) {
      console.error('❌ Error in handleSubmit:', error);
      throw error; // Re-throw to let useFormSubmission handle it
    }
  };

  const initialFormData = useMemo((): DelegationFormData | undefined => {
    if (delegation) {
      return {
        delegates:
          delegation.delegates?.map(d => ({
            // Assuming form expects this structure
            id: d.id,
            name: d.name || 'Unknown Delegate',
            notes: d.notes ?? '',
            title: d.title ?? 'No Title Specified',
          })) ?? [],
        driverEmployeeIds:
          delegation.drivers
            ?.map(d => Number(d.employeeId))
            .filter(id => !isNaN(id) && id > 0) ?? [],
        durationFrom: formatDateForInput(delegation.durationFrom, 'date'),
        durationTo: formatDateForInput(delegation.durationTo, 'date'),
        escortEmployeeIds:
          delegation.escorts
            ?.map(e => Number(e.employeeId))
            .filter(id => !isNaN(id) && id > 0) ?? [],
        eventName: delegation.eventName || '',
        flightArrivalDetails: mapDomainFlightToFormFlight(
          delegation.arrivalFlight
        ),
        flightDepartureDetails: mapDomainFlightToFormFlight(
          delegation.departureFlight
        ),
        imageUrl: delegation.imageUrl || '',

        invitationFrom: delegation.invitationFrom || '',

        invitationTo: delegation.invitationTo || '',
        location: delegation.location || '',

        notes: delegation.notes || '',
        status: delegation.status.replace(
          '_',
          ' '
        ) as DelegationFormData['status'],
        statusChangeReason: '', // Not directly on DomainDelegation, default for form

        vehicleIds:
          delegation.vehicles
            ?.map(v => v.id) // Use vehicle.id (number)
            .filter(id => typeof id === 'number' && id > 0) ?? [],
      };
    }
    return undefined;
  }, [delegation]);

  return (
    <ErrorBoundary>
      <DataLoader
        data={initialFormData}
        emptyComponent={
          <div className="space-y-6 text-center">
            <PageHeader icon={AlertTriangle} title="Delegation Not Found" />
            <p>The requested delegation could not be found.</p>
            <ActionButton
              actionType="primary"
              icon={<ArrowLeft className="size-4" />}
              onClick={() => router.push('/delegations')}
            >
              Back to Delegations
            </ActionButton>
          </div>
        }
        error={fetchError ? (fetchError as Error).message : null}
        isLoading={isLoadingDelegation || false}
        loadingComponent={
          <div className="space-y-6">
            <PageHeader icon={Briefcase} title="Loading Delegation..." />
            <SkeletonLoader count={1} variant="card" />
          </div>
        }
        onRetry={refetch}
      >
        {loadedFormInitialData => (
          <div className="space-y-6">
            <PageHeader
              description="Modify the details for this delegation or event."
              icon={Briefcase}
              title={`Edit Delegation: ${loadedFormInitialData.eventName}`}
            />

            <DelegationFormContainer
              initialData={loadedFormInitialData as any} // Cast to any
              isEditing={true}
              onSubmit={handleSubmit}
            />
          </div>
        )}
      </DataLoader>
    </ErrorBoundary>
  );
}
