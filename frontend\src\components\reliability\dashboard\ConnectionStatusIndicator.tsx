/**
 * @file Real-time WebSocket connection status indicator component.
 * This component displays the current connection status with visual indicators and tooltips.
 * @module components/reliability/dashboard/ConnectionStatusIndicator
 */

'use client';

import { AlertCircle, CheckCircle, Loader2, WifiOff } from 'lucide-react';
import React from 'react';

import { Badge } from '@/components/ui/badge';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { useWebSocketState } from '@/lib/services/WebSocketManager';
import { cn } from '@/lib/utils';

/**
 * Props for the ConnectionStatusIndicator component
 */
export interface ConnectionStatusIndicatorProps {
  /** Optional CSS class name for styling */
  className?: string;
  /** Whether to show the status text (default: true) */
  showText?: boolean;
  /** Size variant for the indicator */
  size?: 'lg' | 'md' | 'sm';
}

/**
 * Real-time WebSocket connection status indicator component.
 *
 * This component provides visual feedback about the WebSocket connection status:
 * - Connected: Green indicator with checkmark
 * - Reconnecting: Yellow indicator with loading spinner
 * - Disconnected: Red indicator with warning icon
 *
 * Features:
 * - Real-time status updates
 * - Tooltip with detailed information
 * - Configurable size and text display
 * - Accessibility support with ARIA labels
 * - Responsive design
 *
 * @param props - Component props
 * @returns JSX element representing the connection status indicator
 *
 * @example
 * ```tsx
 * // Basic usage
 * <ConnectionStatusIndicator />
 *
 * // With custom styling
 * <ConnectionStatusIndicator
 *   size="lg"
 *   showText={false}
 *   className="ml-4"
 * />
 * ```
 */
export const ConnectionStatusIndicator: React.FC<
  ConnectionStatusIndicatorProps
> = ({ className = '', showText = true, size = 'md' }) => {
  const { connectionState, isConnected } = useWebSocketState();

  /**
   * Get status configuration based on connection state
   */
  const getStatusConfig = () => {
    switch (connectionState) {
      case 'connected': {
        return {
          bgColor: 'bg-green-100 dark:bg-green-900/20',
          borderColor: 'border-green-200 dark:border-green-800',
          color: 'text-green-600 dark:text-green-400',
          description: 'Real-time connection active',
          icon: CheckCircle,
          text: 'Connected',
          variant: 'default',
        } as const;
      }
      case 'connecting':
      case 'reconnecting': {
        return {
          animate: true,
          bgColor: 'bg-yellow-100 dark:bg-yellow-900/20',
          borderColor: 'border-yellow-200 dark:border-yellow-800',
          color: 'text-yellow-600 dark:text-yellow-400',
          description:
            connectionState === 'connecting'
              ? 'Establishing connection...'
              : 'Attempting to reconnect...',
          icon: Loader2,
          text:
            connectionState === 'connecting' ? 'Connecting' : 'Reconnecting',
          variant: 'secondary',
        } as const;
      }
      case 'disconnected':
      case 'error':
      default: {
        // In development mode, show a more informative status
        const isDevelopment = process.env.NODE_ENV === 'development';
        const isDisconnected = connectionState === 'disconnected';

        return {
          bgColor:
            isDevelopment && isDisconnected
              ? 'bg-blue-100 dark:bg-blue-900/20'
              : 'bg-red-100 dark:bg-red-900/20',
          borderColor:
            isDevelopment && isDisconnected
              ? 'border-blue-200 dark:border-blue-800'
              : 'border-red-200 dark:border-red-800',
          color:
            isDevelopment && isDisconnected
              ? 'text-blue-600 dark:text-blue-400'
              : 'text-red-600 dark:text-red-400',
          description:
            connectionState === 'error'
              ? 'Connection error occurred'
              : isDevelopment && isDisconnected
                ? 'Using polling fallback (WebSocket requires authentication)'
                : 'Real-time connection lost',
          icon: WifiOff,
          text:
            connectionState === 'error'
              ? 'Error'
              : isDevelopment && isDisconnected
                ? 'Polling'
                : 'Disconnected',
          variant:
            isDevelopment && isDisconnected ? 'secondary' : 'destructive',
        } as const;
      }
    }
  };

  const config = getStatusConfig();
  const Icon = config.icon;

  /**
   * Get size classes based on size prop
   */
  const getSizeClasses = () => {
    switch (size) {
      case 'lg': {
        return {
          badge: 'text-sm px-3 py-1.5',
          icon: 'h-5 w-5',
          text: 'text-sm',
        };
      }
      case 'sm': {
        return {
          badge: 'text-xs px-2 py-1',
          icon: 'h-3 w-3',
          text: 'text-xs',
        };
      }
      case 'md':
      default: {
        return {
          badge: 'text-xs px-2.5 py-1',
          icon: 'h-4 w-4',
          text: 'text-sm',
        };
      }
    }
  };

  const sizeClasses = getSizeClasses();

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div
            aria-label={`Connection status: ${config.text}`}
            className={cn('flex items-center gap-2', className)}
            role="status"
          >
            {/* Status Icon */}
            <div
              className={cn(
                'flex items-center justify-center rounded-full p-1',
                config.bgColor,
                config.borderColor,
                'border'
              )}
            >
              <Icon
                aria-hidden="true"
                className={cn(
                  sizeClasses.icon,
                  config.color,
                  config.animate && 'animate-spin'
                )}
              />
            </div>

            {/* Status Text */}
            {showText && (
              <Badge
                className={cn(sizeClasses.badge, 'font-medium')}
                variant={config.variant}
              >
                {config.text}
              </Badge>
            )}
          </div>
        </TooltipTrigger>

        <TooltipContent>
          <p className="text-sm font-medium">{config.text}</p>
          <p className="text-xs text-muted-foreground">{config.description}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

/**
 * Default export for the ConnectionStatusIndicator component
 */
export default ConnectionStatusIndicator;
