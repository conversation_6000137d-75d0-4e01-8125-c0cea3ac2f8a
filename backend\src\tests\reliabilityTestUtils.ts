// reliabilityTestUtils.ts - Enhanced test utilities for reliability components

import type { User } from '@supabase/supabase-js'; // Import User type from Supabase JS client
import type { NextFunction, Request, Response } from 'express';

import { jest } from '@jest/globals';

interface MetricConfig {
  help?: string;
  labelNames?: string[];
  name: string;
}

/**
 * Performance measurement utilities
 */
export class PerformanceMeasurer {
  private readonly measurements = new Map<string, number[]>();

  getAllStats() {
    const result: Record<string, any> = {};
    this.measurements.forEach((_, name) => {
      result[name] = this.getStats(name);
    });
    return result;
  }

  getStats(name: string) {
    const measurements = this.measurements.get(name) || [];
    if (measurements.length === 0) {
      return null;
    }

    const sorted = [...measurements].sort((a, b) => a - b);
    const sum = measurements.reduce((a, b) => a + b, 0);

    return {
      count: measurements.length,
      max: Math.max(...measurements),
      mean: sum / measurements.length,
      median: sorted[Math.floor(sorted.length / 2)],
      min: Math.min(...measurements),
      p95: sorted[Math.floor(sorted.length * 0.95)],
      p99: sorted[Math.floor(sorted.length * 0.99)],
    };
  }

  async measure<T>(name: string, fn: () => Promise<T>): Promise<T> {
    const start = process.hrtime.bigint();
    try {
      const result = await fn();
      const end = process.hrtime.bigint();
      const duration = Number(end - start) / 1_000_000; // Convert to milliseconds

      if (!this.measurements.has(name)) {
        this.measurements.set(name, []);
      }
      this.measurements.get(name)!.push(duration);

      return result;
    } catch (error) {
      const end = process.hrtime.bigint();
      const duration = Number(end - start) / 1_000_000;

      if (!this.measurements.has(name)) {
        this.measurements.set(name, []);
      }
      this.measurements.get(name)!.push(duration);

      throw error;
    }
  }

  reset() {
    this.measurements.clear();
  }
}

/**
 * Mock Circuit Breaker for testing
 */
export function createMockCircuitBreaker() {
  let state = 'CLOSED';
  let failureCount = 0;

  const eventListeners = new Map<string, Function[]>();

  const mockBreaker = {
    _setFailureCount: (count: number) => {
      failureCount = count;
    },

    // Test utilities
    _setState: (newState: string) => {
      state = newState;
    },

    _triggerEvent: (event: string, ...args: any[]) => {
      const listeners = eventListeners.get(event) || [];
      listeners.forEach(listener => listener(...args));
    },

    close: jest.fn<() => void>().mockImplementation(() => {
      state = 'CLOSED';
      failureCount = 0;
      const listeners = eventListeners.get('close') || [];
      listeners.forEach(listener => listener());
    }),

    fire: jest
      .fn<(...args: [Function]) => Promise<any>>()
      .mockImplementation(async (fn: Function) => {
        if (state === 'OPEN') {
          const error = new Error('Circuit breaker is OPEN');
          (error as any).code = 'EOPENBREAKER';
          throw error;
        }

        try {
          const result = await fn();
          failureCount = 0;
          state = 'CLOSED';
          return result;
        } catch (error) {
          failureCount++;
          if (failureCount >= 5) {
            state = 'OPEN';
          }
          throw error;
        }
      }),

    halfOpen: jest.fn<() => void>().mockImplementation(() => {
      state = 'HALF_OPEN';
      const listeners = eventListeners.get('halfOpen') || [];
      listeners.forEach(listener => listener());
    }),
    on: jest
      .fn<(...args: [string, Function]) => void>()
      .mockImplementation((event: string, listener: Function) => {
        if (!eventListeners.has(event)) {
          eventListeners.set(event, []);
        }
        eventListeners.get(event)!.push(listener);
      }),

    open: jest.fn<() => void>().mockImplementation(() => {
      state = 'OPEN';
      const listeners = eventListeners.get('open') || [];
      listeners.forEach(listener => listener());
    }),
    // Getters for testing
    get state() {
      return state;
    },
    get stats() {
      return {
        cacheHits: 0,
        cacheMisses: 0,
        failures: failureCount,
        fallbacks: 0,
        fires: 0,
        latencyMean: 0,
        percentiles: {},
        semaphoreRejections: 0,
        snapshot: [],
        timeouts: 0,
      };
    },
  };

  return mockBreaker;
}

/**
 * Create mock Next function for middleware testing
 */
export function createMockNext(): { helpers: any; next: NextFunction } {
  let calledWith: any = undefined;
  let callCount = 0;

  const next: NextFunction = jest.fn().mockImplementation((error?: any) => {
    calledWith = error;
    callCount++;
  });

  const helpers = {
    getCallCount: () => callCount,
    getCalledWith: () => calledWith,
    reset: () => {
      calledWith = undefined;
      callCount = 0;
      (next as jest.Mock).mockClear();
    },
    wasCalled: () => callCount > 0,
    wasCalledWithError: () => calledWith instanceof Error,
  };

  return { helpers, next };
}

/**
 * Mock Prometheus client for testing metrics
 */
export function createMockPrometheusClient() {
  const metrics = new Map<string, any>();

  const mockRegister = {
    clear: jest.fn<() => void>().mockImplementation(() => {
      metrics.clear();
    }),
    getMetricsAsJSON: jest.fn<() => any[]>().mockImplementation(() => Array.from(metrics.values())),
    getSingleMetric: jest
      .fn<(...args: [string]) => any>()
      .mockImplementation((name: string) => metrics.get(name)),
    metrics: jest.fn<() => Promise<string>>().mockResolvedValue('# Mock metrics data'),
    registerMetric: jest.fn<() => void>(),
  };

  const createMetric = (type: string) => ({
    dec: jest.fn<() => void>(),
    get: jest.fn<() => { values: any[] }>().mockReturnValue({ values: [] }),
    inc: jest.fn<() => void>(),
    labels: jest.fn<() => any>().mockReturnThis(),
    observe: jest.fn<() => void>(),
    remove: jest.fn<() => void>(),
    reset: jest.fn<() => void>(),
    set: jest.fn<() => void>(),
  });

  return {
    _getMetrics: () => metrics,
    collectDefaultMetrics: jest.fn<() => void>(),
    Counter: jest
      .fn<(...args: [MetricConfig]) => ReturnType<typeof createMetric>>()
      .mockImplementation((config: MetricConfig) => {
        const metric = createMetric('counter');
        metrics.set(config.name, metric);
        return metric;
      }),
    Gauge: jest
      .fn<(...args: [MetricConfig]) => ReturnType<typeof createMetric>>()
      .mockImplementation((config: MetricConfig) => {
        const metric = createMetric('gauge');
        metrics.set(config.name, metric);
        return metric;
      }),
    Histogram: jest
      .fn<(...args: [MetricConfig]) => ReturnType<typeof createMetric>>()
      .mockImplementation((config: MetricConfig) => {
        const metric = createMetric('histogram');
        metrics.set(config.name, metric);
        return metric;
      }),
    register: mockRegister,
    Summary: jest
      .fn<(...args: [MetricConfig]) => ReturnType<typeof createMetric>>()
      .mockImplementation((config: MetricConfig) => {
        const metric = createMetric('summary');
        metrics.set(config.name, metric);
        return metric;
      }),
  };
}

/**
 * Mock Redis client for testing request deduplication
 */
export function createMockRedis() {
  const store = new Map<string, { expiry?: number; value: any }>();

  return {
    // Internal methods for testing
    _getStore: () => store,

    _setStore: (newStore: Map<string, any>) => {
      store.clear();
      newStore.forEach((value, key) => store.set(key, value));
    },

    del: jest
      .fn<(...args: [string]) => Promise<0 | 1>>()
      .mockImplementation(async (key: string) => {
        const existed = store.has(key);
        store.delete(key);
        return existed ? 1 : 0;
      }),

    flushall: jest.fn<() => Promise<string>>().mockImplementation(async () => {
      store.clear();
      return 'OK';
    }),

    get: jest
      .fn<(...args: [string]) => Promise<null | string>>()
      .mockImplementation(async (key: string) => {
        const item = store.get(key);
        if (!item) return null;

        if (item.expiry && Date.now() > item.expiry) {
          store.delete(key);
          return null;
        }

        return JSON.stringify(item.value);
      }),

    keys: jest
      .fn<(...args: [string]) => Promise<string[]>>()
      .mockImplementation(async (pattern: string) => {
        const keys = Array.from(store.keys());
        if (pattern === '*') return keys;

        // Simple pattern matching for dedup:* pattern
        const regex = new RegExp(pattern.replace('*', '.*'));
        return keys.filter(key => regex.test(key));
      }),

    ping: jest.fn<() => Promise<string>>().mockResolvedValue('PONG'),
    set: jest
      .fn<(...args: [string, any, string?, number?]) => Promise<string>>()
      .mockImplementation(async (key: string, value: any, _mode?: string, duration?: number) => {
        const expiry = duration ? Date.now() + duration * 1000 : undefined;
        store.set(key, { expiry, value: JSON.parse(value) });
        return 'OK';
      }),
  };
}

/**
 * Create mock Express request with reliability testing features
 */
export function createMockRequest(overrides: Partial<Request> = {}): Partial<Request> {
  const defaults = {
    body: {},
    headers: {},
    ip: '127.0.0.1',
    method: 'GET',
    params: {},
    path: '/test',
    query: {},
    url: '/test',
    user: {
      app_metadata: {
        provider: 'email',
        providers: ['email'],
        user_role: 'admin', // Custom claim for testing
      },
      aud: 'authenticated',
      created_at: new Date().toISOString(),
      id: 'test-user',
      role: 'authenticated', // Supabase default role
      user_metadata: {},
    } as User, // Cast to User to ensure type compatibility
  };

  const result = { ...defaults, ...overrides };

  // If path is overridden but url/originalUrl are not, sync them
  if (overrides.path && !overrides.url && !overrides.originalUrl) {
    result.url = overrides.path;
    result.originalUrl = overrides.path;
  }

  return result;
}

/**
 * Create mock Express response with reliability testing features
 */
export function createMockResponse(): { helpers: any; res: Partial<Response> } {
  const headers = new Map<string, any>();
  let statusCode = 200;
  let sentData: any = null;

  const res: Partial<Response> = {
    getHeader: jest
      .fn<(...args: [string]) => number | string | string[] | undefined>()
      .mockImplementation((name: string) => {
        return headers.get(name);
      }),

    getHeaders: jest
      .fn<() => Record<string, number | string | string[] | undefined>>()
      .mockImplementation(() => {
        const result: Record<string, any> = {};
        headers.forEach((value, key) => {
          result[key] = value;
        });
        return result;
      }),

    json: jest.fn<(...args: [any]) => Response>().mockImplementation((data: any) => {
      sentData = data;
      return res as Response;
    }),

    send: jest.fn<(...args: [any]) => Response>().mockImplementation((data: any) => {
      sentData = data;
      return res as Response;
    }),

    set: jest.fn().mockImplementation((name: any, value: any) => {
      headers.set(name, value);
      return res as Response;
    }) as Response['set'], // Cast directly to Response['set'] after using any for implementation parameters

    status: jest.fn<(...args: [number]) => Response>().mockImplementation((code: number) => {
      statusCode = code;
      return res as Response;
    }),

    get statusCode() {
      return statusCode;
    },
    set statusCode(code: number) {
      statusCode = code;
    },
  };

  const helpers = {
    getHeader: (name: string) => headers.get(name),
    getHeaders: () => headers,
    getSentData: () => sentData,
    getStatusCode: () => statusCode,
  };

  return { helpers, res };
}

/**
 * Test data generators
 */
export const testDataGenerators = {
  generateHealthCheckData: () => ({
    circuitBreakers: { openBreakers: 0, status: 'healthy' },
    database: { responseTime: 50, status: 'healthy' },
    redis: { responseTime: 10, status: 'healthy' },
    supabase: { responseTime: 100, status: 'healthy' },
  }),

  generateRandomRequests: (count: number) => {
    return Array.from({ length: count }, (_, i) => ({
      body: { data: `test-data-${i}`, id: i },
      headers: { 'user-agent': 'test-agent', 'x-request-id': `req-${i}` },
      method: ['GET', 'POST', 'PUT', 'DELETE'][Math.floor(Math.random() * 4)],
      path: `/api/test/${i}`,
    }));
  },
};
