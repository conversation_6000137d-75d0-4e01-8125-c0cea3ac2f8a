// frontend/src/components/features/reporting/dashboard/layout/ReportingHeader.tsx
import React from 'react';

interface ReportingHeaderProps {
  title: string;
  description?: string;
  actions?: React.ReactNode;
}

/**
 * @component ReportingHeader
 * @description Displays the title, description, and optional actions for a reporting section.
 * Adheres to SRP by focusing solely on header presentation.
 * @param {ReportingHeaderProps} props - The component props.
 * @param {string} props.title - The main title of the header.
 * @param {string} [props.description] - An optional description for the header.
 * @param {React.ReactNode} [props.actions] - Optional action buttons or components to display in the header.
 */
export const ReportingHeader: React.FC<ReportingHeaderProps> = ({
  title,
  description,
  actions,
}) => {
  return (
    <header className="bg-white shadow-sm py-6 px-4 sm:px-6 lg:px-8">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold leading-tight text-gray-900">
            {title}
          </h1>
          {description && (
            <p className="mt-2 text-sm text-gray-500">{description}</p>
          )}
        </div>
        {actions && <div className="flex-shrink-0">{actions}</div>}
      </div>
    </header>
  );
};
