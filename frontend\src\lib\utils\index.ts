/**
 * @file Utils Index
 * @description Centralized exports for all utility functions following DRY principles
 */

// API Utilities
export {
  calculateBackoffDelay,
  type RetryOptions,
  withRetry,
} from './apiUtils';

// Date Utilities
export {
  formatDateForApi,
  formatDateForDisplay,
  formatDateForInput,
  isDateAfter,
  isValidDateString,
  isValidIsoDateString,
  parseDateFromApi,
} from './dateUtils';

// Error Handling Utilities
export {
  ERROR_CODE_MESSAGES,
  FIELD_ERROR_MESSAGES,
  formatErrorForUser,
  getErrorMessage,
  isAssignmentError,
  isRetryableError,
  isValidationError,
  logAndFormatError,
  mapErrorToFormField,
  retryRequest,
  retryWithValidationErrorHandling,
  type UserFriendlyError,
} from './errorHandling';

// File Utilities
export {
  downloadFile,
  extractTableDataForCsv,
  generateCsvFromData,
} from './fileUtils';

// Formatting Utilities
export {
  formatCurrency,
  formatDelegationStatusForDisplay,
  formatEmployeeStatusForDisplay,
  formatFileSize,
  formatNumber,
  formatPhoneNumber,
  formatTaskStatusForDisplay,
  titleCase,
  truncateText,
} from './formattingUtils';

// Response Adapter (legacy ApiResponse type removed)
export { adaptApiResponse, withResponseAdapter } from './responseAdapter';

// Circuit Breaker Utilities
export {
  CircuitBreaker,
  CircuitState as CircuitBreakerState,
  adminCircuitBreakers,
  getCircuitBreakerStatus,
  resetAllCircuitBreakers,
  type CircuitBreakerOptions,
} from './circuitBreaker';

// Request Cache Utilities
export {
  RequestCache,
  type CacheOptions,
  type CacheEntry,
} from './requestCache';

// Image Utilities
export {
  isValidImageUrl,
  getSafeImageUrl,
  generatePlaceholderImageUrl,
  getSafeDelegationImageUrl,
  getSafeVehicleImageUrl,
} from './imageUtils';

// Delegation Utilities
export {
  getStatusColor as getDelegationStatusColor,
  formatDelegationDate,
  formatDelegationTime,
  getInfoIconColor,
  needsAttention,
  getDelegationPriority,
} from './delegationUtils';

// Re-export types for convenience
export type { ApiValidationError } from '@/lib/types/api';

// Utility function from lib/utils.ts (cn function)
import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

/**
 * Utility function to merge Tailwind CSS classes
 * @param inputs - Class values to merge
 * @returns Merged class string
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// Utility constants for consistent usage
export const UTILS_CONSTANTS = {
  DEFAULT_RETRY_ATTEMPTS: 3,
  DEFAULT_RETRY_DELAY: 1000,
  DEFAULT_CACHE_TTL: 5 * 60 * 1000, // 5 minutes
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  SUPPORTED_IMAGE_TYPES: ['image/jpeg', 'image/png', 'image/webp'],
} as const;
