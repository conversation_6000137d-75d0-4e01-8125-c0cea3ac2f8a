import type {
  CreateGiftRequest,
  GiftApiResponse,
  GiftStatisticsApiResponse,
  UpdateGiftRequest,
} from '../../../types/apiContracts';
import type { Gift, GiftStatistics } from '../../../types/domain';
import type { ApiClient } from '../../core/apiClient';

import { GiftTransformer } from '../../../transformers/giftTransformer';
import {
  BaseApiService,
  type DataTransformer,
  type ServiceConfig,
} from '../../core/baseApiService';

const GiftApiTransformer: DataTransformer<Gift> = {
  fromApi: (data: GiftApiResponse) => GiftTransformer.fromApi(data),
  toApi: (data: any) => data,
};

export class GiftApiService extends BaseApiService<
  Gift,
  CreateGiftRequest,
  UpdateGiftRequest
> {
  protected endpoint = '/gifts';
  protected transformer: DataTransformer<Gift> = GiftApiTransformer;

  constructor(apiClient: ApiClient, config?: ServiceConfig) {
    super(apiClient, {
      cacheDuration: 5 * 60 * 1000, // 5 minutes for gifts
      circuitBreakerThreshold: 5,
      enableMetrics: true,
      retryAttempts: 3,
      ...config,
    });
  }

  /**
   * Get gifts sent within a date range
   * @param startDate - Start date (ISO string)
   * @param endDate - End date (ISO string)
   * @returns Promise resolving to array of gifts
   */
  async getByDateRange(startDate: string, endDate: string): Promise<Gift[]> {
    const result = await this.getAll({
      endDate,
      startDate,
    });
    return result.data;
  }

  /**
   * Get gifts by occasion
   * @param occasion - The occasion to filter by
   * @returns Promise resolving to array of gifts
   */
  async getByOccasion(occasion: string): Promise<Gift[]> {
    const result = await this.getAll({ occasion });
    return result.data;
  }

  /**
   * Get gifts by recipient ID
   * @param recipientId - The recipient ID to filter by
   * @returns Promise resolving to array of gifts
   */
  async getByRecipient(recipientId: string): Promise<Gift[]> {
    const result = await this.getAll({ recipientId });
    return result.data;
  }

  /**
   * Get gifts by sender name
   * @param senderName - The sender name to filter by
   * @returns Promise resolving to array of gifts
   */
  async getBySender(senderName: string): Promise<Gift[]> {
    const result = await this.getAll({ senderName });
    return result.data;
  }

  /**
   * Get recent gifts (last 30 days)
   * @returns Promise resolving to array of recent gifts
   */
  async getRecent(): Promise<Gift[]> {
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    return this.getByDateRange(
      thirtyDaysAgo.toISOString(),
      new Date().toISOString()
    );
  }

  /**
   * Get gift statistics
   * @returns Promise resolving to gift statistics
   */
  async getStatistics(): Promise<GiftStatistics> {
    return this.executeWithInfrastructure('getStatistics', async () => {
      const response = await this.apiClient.get<GiftStatisticsApiResponse>(
        `${this.endpoint}/statistics`
      );

      return {
        giftsThisMonth: response.giftsThisMonth,
        giftsThisYear: response.giftsThisYear,
        popularOccasions: response.popularOccasions,
        recentActivity: response.recentActivity,
        topRecipients: response.topRecipients,
        totalGifts: response.totalGifts,
      };
    });
  }

  /**
   * Search gifts by item description
   * @param searchTerm - The search term to filter by
   * @returns Promise resolving to array of gifts
   */
  async searchByDescription(searchTerm: string): Promise<Gift[]> {
    const result = await this.getAll({ search: searchTerm });
    return result.data;
  }
}
