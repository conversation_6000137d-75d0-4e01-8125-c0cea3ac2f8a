/**
 * Consolidated Admin Service
 *
 * This service orchestrates all admin operations, providing a unified
 * interface for health monitoring, user management, audit logging,
 * and system administration.
 *
 * Features:
 * - Centralized admin operations
 * - Circuit breaker protection for admin operations
 * - Request deduplication for admin endpoints
 * - Comprehensive audit logging
 * - Performance monitoring integration
 */

import type {
  AdminDashboardData,
  AdminServiceResponse,
  AuditDetails,
  AuditLogFilterOptions,
  CreateUserRequest,
  ErrorLogFilterOptions,
  GetAllUsersResult,
  GetAuditLogsResult,
  HealthResponse,
  PaginatedErrorLogResponse,
  PerformanceMetrics,
  UpdateUserRequest,
  UserFilterOptions,
  UserProfile,
  UserRole,
} from '../types/admin.types.js';

import * as auditService from '../../../services/auditLog.service.js';
import { executeWithCircuitBreaker } from '../../../services/circuitBreaker.service.js';
// Import existing services
import {
  getErrorLogs,
  getHealthStatus,
  getPerformanceMetrics,
} from '../../../services/health.service.js';
import { businessMetrics } from '../../../services/metrics.service.js';
import * as userService from '../../../services/userManagement.service.js';
import logger from '../../../utils/logger.js';
import {
  AdminError,
  AdminPermissionError,
  AdminStatistics,
  AdminValidationError,
  VALID_ROLES,
} from '../types/admin.types.js';

/**
 * Admin Service Class
 * Provides centralized admin operations with reliability enhancements
 */
export class AdminService {
  private static instance: AdminService;

  private constructor() {
    logger.info('AdminService initialized', { service: 'admin-module' });
  }

  public static getInstance(): AdminService {
    if (!AdminService.instance) {
      AdminService.instance = new AdminService();
    }
    return AdminService.instance;
  }

  /**
   * Create user with validation and circuit breaker protection
   */
  async createUser(
    userData: CreateUserRequest,
    auditDetails: AuditDetails,
  ): Promise<AdminServiceResponse<UserProfile>> {
    const startTime = Date.now();

    try {
      // Validate input
      this.validateUserData(userData);

      const user = await executeWithCircuitBreaker('admin-users', async () => {
        return await userService.createUser(
          userData.email,
          userData.role,
          userData.isActive ?? true,
          userData.emailVerified ?? false,
          auditDetails,
        );
      });

      const duration = (Date.now() - startTime) / 1000;
      businessMetrics.recordAdminOperation('CREATE_USER', auditDetails.userId, 'success');

      logger.info('User created successfully', {
        createdUserId: user.id,
        duration,
        email: userData.email,
        role: userData.role,
        service: 'admin-module',
        userId: auditDetails.userId,
      });

      return {
        data: user,
        message: `User ${userData.email} created successfully`,
        status: 'success',
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      const duration = (Date.now() - startTime) / 1000;
      businessMetrics.recordAdminOperation('CREATE_USER', auditDetails.userId, 'error');

      logger.error('Failed to create user', {
        duration,
        email: userData.email,
        error: error instanceof Error ? error.message : 'Unknown error',
        service: 'admin-module',
        userId: auditDetails.userId,
      });

      if (error instanceof AdminValidationError) {
        throw error;
      }

      throw new AdminError('Failed to create user', 'USER_CREATE_ERROR', 500, error);
    }
  }

  /**
   * Get all users with circuit breaker protection
   */
  async getAllUsers(
    options: UserFilterOptions,
    auditDetails: AuditDetails,
  ): Promise<AdminServiceResponse<GetAllUsersResult>> {
    const startTime = Date.now();

    try {
      logger.debug('AdminService: Attempting to fetch all users with options', {
        options,
        service: 'admin-module',
        userId: auditDetails.userId,
      });

      const users = await executeWithCircuitBreaker('admin-users', async () => {
        logger.debug('AdminService: Calling userService.getAllUsers...', {
          service: 'admin-module',
        });
        try {
          const result = await userService.getAllUsers(
            options.page || 1,
            options.limit || 10,
            options.search || '',
            options.role || '',
            options.isActive,
          );
          logger.debug('AdminService: userService.getAllUsers returned successfully.', {
            service: 'admin-module',
          });
          return result;
        } catch (innerError) {
          logger.error('AdminService: Error from userService.getAllUsers within circuit breaker', {
            innerError: innerError instanceof Error ? innerError.message : 'Unknown inner error',
            innerErrorStack: innerError instanceof Error ? innerError.stack : 'N/A',
            service: 'admin-module',
            userId: auditDetails.userId,
          });
          throw innerError; // Re-throw to be caught by outer catch block
        }
      });
      logger.debug('AdminService: Successfully fetched users via circuit breaker.', {
        service: 'admin-module',
      });

      const duration = (Date.now() - startTime) / 1000;
      businessMetrics.recordAdminOperation('VIEW_USERS', auditDetails.userId, 'success');

      logger.info('Users fetched successfully', {
        duration,
        service: 'admin-module',
        userId: auditDetails.userId,
      });

      return {
        data: users,
        status: 'success',
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      const duration = (Date.now() - startTime) / 1000;
      businessMetrics.recordAdminOperation('VIEW_USERS', auditDetails.userId, 'error');

      logger.error('AdminService: Failed to fetch users', {
        duration,
        error: error instanceof Error ? error.message : 'Unknown error',
        errorStack: error instanceof Error ? error.stack : 'N/A',
        fullError: error, // Log the full error object
        service: 'admin-module',
        userId: auditDetails.userId,
      });

      throw new AdminError('Failed to fetch users', 'USERS_FETCH_ERROR', 500, error);
    }
  }

  /**
   * Get audit logs with circuit breaker protection
   */
  async getAuditLogs(
    options: AuditLogFilterOptions,
    auditDetails: AuditDetails,
  ): Promise<AdminServiceResponse<GetAuditLogsResult>> {
    const startTime = Date.now();

    try {
      const auditLogs = await executeWithCircuitBreaker('admin-audit', async () => {
        return await auditService.getAuditLogs(
          options.page || 1,
          options.limit || 10,
          options.search || '',
          options.action || '',
          options.userId || '',
          options.startDate || '',
          options.endDate || '',
        );
      });

      const duration = (Date.now() - startTime) / 1000;
      businessMetrics.recordAdminOperation('VIEW_AUDIT_LOGS', auditDetails.userId, 'success');

      return {
        data: auditLogs,
        status: 'success',
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      const duration = (Date.now() - startTime) / 1000;
      businessMetrics.recordAdminOperation('VIEW_AUDIT_LOGS', auditDetails.userId, 'error');

      logger.error('Failed to fetch audit logs', {
        duration,
        error: error instanceof Error ? error.message : 'Unknown error',
        service: 'admin-module',
        userId: auditDetails.userId,
      });

      throw new AdminError('Failed to fetch audit logs', 'AUDIT_LOGS_FETCH_ERROR', 500, error);
    }
  }

  /**
   * Get comprehensive admin dashboard data
   */
  async getDashboardData(
    auditDetails: AuditDetails,
  ): Promise<AdminServiceResponse<AdminDashboardData>> {
    const startTime = Date.now();

    try {
      logger.info('Fetching admin dashboard data', {
        service: 'admin-module',
        userId: auditDetails.userId,
      });

      // Execute all dashboard queries with circuit breaker protection
      const [healthStatus, performanceMetrics, userStats, recentAuditLogs] = await Promise.all([
        executeWithCircuitBreaker('admin-health', () => this.getHealthStatus()),
        executeWithCircuitBreaker('admin-performance', () => this.getPerformanceMetrics()),
        executeWithCircuitBreaker('admin-users', () => this.getUserStatistics()),
        executeWithCircuitBreaker('admin-audit', () => this.getRecentAuditLogs(5)),
      ]);

      const dashboardData: AdminDashboardData = {
        healthStatus,
        performanceMetrics,
        recentAuditLogs: recentAuditLogs.data,
        systemMetrics: {
          cpuUsage: 0, // Would need additional monitoring for real CPU usage
          diskUsage: 0, // Would need additional monitoring for real disk usage
          memoryUsage: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
          uptime: Math.floor(process.uptime()),
        },
        userStats,
      };

      // Record successful admin operation
      const duration = (Date.now() - startTime) / 1000;
      businessMetrics.recordAdminOperation('VIEW_DASHBOARD', auditDetails.userId, 'success');

      logger.info('Admin dashboard data fetched successfully', {
        duration,
        service: 'admin-module',
        userId: auditDetails.userId,
      });

      return {
        data: dashboardData,
        status: 'success',
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      const duration = (Date.now() - startTime) / 1000;
      businessMetrics.recordAdminOperation('VIEW_DASHBOARD', auditDetails.userId, 'error');

      logger.error('Failed to fetch admin dashboard data', {
        duration,
        error: error instanceof Error ? error.message : 'Unknown error',
        service: 'admin-module',
        userId: auditDetails.userId,
      });

      throw new AdminError('Failed to fetch dashboard data', 'DASHBOARD_FETCH_ERROR', 500, error);
    }
  }

  /**
   * Get error logs with circuit breaker protection
   */
  async getErrorLogs(
    options: ErrorLogFilterOptions,
    auditDetails: AuditDetails,
  ): Promise<AdminServiceResponse<PaginatedErrorLogResponse>> {
    const startTime = Date.now();

    try {
      const errorLogs = await executeWithCircuitBreaker('admin-logs', async () => {
        return await getErrorLogs(options.page || 1, options.limit || 10, options.level);
      });

      const duration = (Date.now() - startTime) / 1000;
      businessMetrics.recordAdminOperation('VIEW_ERROR_LOGS', auditDetails.userId, 'success');

      return {
        data: errorLogs,
        status: 'success',
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      const duration = (Date.now() - startTime) / 1000;
      businessMetrics.recordAdminOperation('VIEW_ERROR_LOGS', auditDetails.userId, 'error');

      logger.error('Failed to fetch error logs', {
        duration,
        error: error instanceof Error ? error.message : 'Unknown error',
        service: 'admin-module',
        userId: auditDetails.userId,
      });

      throw new AdminError('Failed to fetch error logs', 'ERROR_LOGS_FETCH_ERROR', 500, error);
    }
  }

  /**
   * Get system health status with circuit breaker protection
   */
  async getHealthStatus(): Promise<HealthResponse> {
    return await executeWithCircuitBreaker('admin-health', async () => {
      return await getHealthStatus();
    });
  }

  /**
   * Get performance metrics with circuit breaker protection
   */
  async getPerformanceMetrics(): Promise<PerformanceMetrics> {
    return await executeWithCircuitBreaker('admin-performance', async () => {
      return await getPerformanceMetrics();
    });
  }

  /**
   * Update user with validation and circuit breaker protection
   */
  async updateUser(
    userId: string,
    userData: UpdateUserRequest,
    auditDetails: AuditDetails,
  ): Promise<AdminServiceResponse<UserProfile>> {
    const startTime = Date.now();

    try {
      // Validate input
      if (userData.role && !this.isValidRole(userData.role)) {
        throw new AdminValidationError(`Invalid role. Must be one of: ${VALID_ROLES.join(', ')}`);
      }

      if (userData.email && !this.isValidEmail(userData.email)) {
        throw new AdminValidationError('Invalid email format');
      }

      const user = await executeWithCircuitBreaker('admin-users', async () => {
        return await userService.updateUser(
          userId,
          userData.email,
          userData.role,
          userData.isActive,
          userData.emailVerified,
          auditDetails,
        );
      });

      const duration = (Date.now() - startTime) / 1000;
      businessMetrics.recordAdminOperation('UPDATE_USER', auditDetails.userId, 'success');

      logger.info('User updated successfully', {
        duration,
        service: 'admin-module',
        updatedUserId: userId,
        userId: auditDetails.userId,
      });

      return {
        data: user,
        message: `User ${userId} updated successfully`,
        status: 'success',
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      const duration = (Date.now() - startTime) / 1000;
      businessMetrics.recordAdminOperation('UPDATE_USER', auditDetails.userId, 'error');

      logger.error('Failed to update user', {
        duration,
        error: error instanceof Error ? error.message : 'Unknown error',
        service: 'admin-module',
        updatedUserId: userId,
        userId: auditDetails.userId,
      });

      if (error instanceof AdminValidationError) {
        throw error;
      }

      throw new AdminError('Failed to update user', 'USER_UPDATE_ERROR', 500, error);
    }
  }

  /**
   * Get recent audit logs for dashboard
   */
  private async getRecentAuditLogs(limit = 5): Promise<GetAuditLogsResult> {
    return await auditService.getAuditLogs(1, limit);
  }

  /**
   * Get user statistics for dashboard
   */
  private async getUserStatistics() {
    // This would typically query the database for user statistics
    // For now, we'll return mock data that would be replaced with real queries
    return {
      activeUsers: 0,
      inactiveUsers: 0,
      totalUsers: 0,
      usersByRole: {
        ADMIN: 0,
        EMPLOYEE: 0,
        MANAGER: 0,
        SUPER_ADMIN: 0,
        USER: 0,
      } as Record<UserRole, number>,
    };
  }

  /**
   * Validate email format
   */
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Validate user role
   */
  private isValidRole(role: string): role is UserRole {
    return (VALID_ROLES as readonly string[]).includes(role);
  }

  /**
   * Validate user data
   */
  private validateUserData(userData: CreateUserRequest): void {
    if (!userData.email || !this.isValidEmail(userData.email)) {
      throw new AdminValidationError('Valid email is required');
    }

    if (!userData.role || !this.isValidRole(userData.role)) {
      throw new AdminValidationError(
        `Valid role is required. Must be one of: ${VALID_ROLES.join(', ')}`,
      );
    }
  }
}

// Export singleton instance
export const adminService = AdminService.getInstance();
export default adminService;
