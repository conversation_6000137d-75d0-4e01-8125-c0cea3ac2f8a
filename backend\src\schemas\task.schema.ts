import { z } from 'zod';

export const TaskStatusEnum = z.enum([
  // Exported
  'Pending',
  'Assigned',
  'In_Progress',
  'Completed',
  'Cancelled',
]);

export const TaskPriorityEnum = z.enum(['Low', 'Medium', 'High']); // Exported

const SubTaskSchema = z.object({
  completed: z.boolean().default(false),
  id: z.string().uuid().optional(),
  title: z.string().min(1, 'Subtask title cannot be empty'),
});

/**
 * @openapi
 * components:
 *   schemas:
 *     SubTaskInput:
 *       type: object
 *       required: [title]
 *       properties:
 *         title: { type: string }
 *         completed: { type: boolean, default: false }
 *     TaskCreateInput:
 *       type: object
 *       required:
 *         - description
 *         - location
 *         - dateTime
 *         - estimatedDuration
 *       properties:
 *         description: { type: string }
 *         location: { type: string }
 *         dateTime: { type: string, format: date-time, description: "Start date and time of the task" }
 *         estimatedDuration: { type: integer, minimum: 1, description: "Duration in minutes" }
 *         requiredSkills: { type: array, items: { type: string }, default: [] }
 *         priority: { $ref: '#/components/schemas/TaskPriorityEnum', default: "Medium" }
 *         deadline: { type: string, format: date-time, nullable: true }
 *         status: { $ref: '#/components/schemas/TaskStatusEnum', default: "Pending" }
 *         notes: { type: string, nullable: true }
 *         vehicleId: { type: integer, nullable: true, description: "ID of the associated vehicle" }
 *         assignedEmployeeIds: { type: array, items: { type: integer }, default: [], description: "Array of Employee IDs to assign this task to" }
 *         subTasks: { type: array, items: { $ref: '#/components/schemas/SubTaskInput' }, default: [] }
 *     TaskUpdateInput:
 *       type: object
 *       properties:
 *         description: { type: string }
 *         location: { type: string }
 *         dateTime: { type: string, format: date-time }
 *         estimatedDuration: { type: integer, minimum: 1 }
 *         requiredSkills: { type: array, items: { type: string } }
 *         priority: { $ref: '#/components/schemas/TaskPriorityEnum' }
 *         deadline: { type: string, format: date-time, nullable: true }
 *         status: { $ref: '#/components/schemas/TaskStatusEnum' }
 *         statusChangeReason: { type: string, nullable: true, description: "Reason for status change, if status is updated." }
 *         notes: { type: string, nullable: true }
 *         vehicleId: { type: integer, nullable: true }
 *         assignedEmployeeIds: { type: array, items: { type: integer }, description: "Full new list of Employee IDs. Employees not in list will be unassigned." }
 *         subTasks: { type: array, items: { $ref: '#/components/schemas/SubTaskInput' }, description: "Full new list of subtasks. Existing subtasks not in the list might be removed or handled by specific update logic." }
 *     TaskStatusEnum:
 *       type: string
 *       enum: [Pending, Assigned, In_Progress, Completed, Cancelled]
 *     TaskPriorityEnum:
 *       type: string
 *       enum: [Low, Medium, High]
 *     SubTask:
 *       allOf:
 *         - $ref: '#/components/schemas/SubTaskInput'
 *         - type: object
 *           properties:
 *             id: { type: string, format: uuid }
 *     Task:
 *       allOf:
 *         - $ref: '#/components/schemas/TaskCreateInput'
 *         - type: object
 *           properties:
 *             id: { type: string, format: uuid }
 *             createdAt: { type: string, format: date-time }
 *             updatedAt: { type: string, format: date-time }
 *             statusHistory: { type: array, items: { $ref: '#/components/schemas/TaskStatusEntry' } }
 *             subTasks: { type: array, items: { $ref: '#/components/schemas/SubTask' } }
 *             assignedEmployees: { type: array, items: { $ref: '#/components/schemas/Employee' } }
 *             vehicle: { $ref: '#/components/schemas/Vehicle', nullable: true }
 *     TaskStatusEntry:
 *       type: object
 *       properties:
 *         id: { type: string, format: uuid }
 *         status: { $ref: '#/components/schemas/TaskStatusEnum' }
 *         changedAt: { type: string, format: date-time }
 *         reason: { type: string, nullable: true }
 */

export const taskCreateSchema = z
  .object({
    dateTime: z.string().datetime({ message: 'Invalid start date/time format' }),
    deadline: z
      .string()
      .datetime({ message: 'Invalid deadline date/time format' })
      .optional()
      .nullable(),
    description: z.string().min(1, 'Task description is required'),
    driverEmployeeId: z
      .number()
      .int()
      .positive('Driver Employee ID must be a positive integer.')
      .optional()
      .nullable(),
    estimatedDuration: z.coerce
      .number()
      .int()
      .min(1, 'Estimated duration must be at least 1 minute'),
    location: z.string().min(1, 'Location is required'),
    notes: z.string().optional().nullable(),
    priority: TaskPriorityEnum.default('Medium'),
    requiredSkills: z.array(z.string()).optional().default([]),
    // NEW PHASE 1 ASSIGNMENT FIELDS
    staffEmployeeId: z.number().int().positive('Staff Employee ID must be a positive integer.'),
    status: TaskStatusEnum.default('Pending'),
    subTasks: z
      .array(SubTaskSchema.omit({ id: true }))
      .optional()
      .default([]),
    vehicleId: z
      .number()
      .int()
      .positive('Vehicle ID must be a positive integer.')
      .optional()
      .nullable(),
  })
  .refine(
    data => {
      // Business rule: vehicleId only allowed if driverEmployeeId is present
      if (data.vehicleId && !data.driverEmployeeId) {
        return false;
      }
      return true;
    },
    {
      message: 'Vehicle can only be assigned when a driver is assigned',
      path: ['vehicleId'],
    },
  );

// Create a base schema without the refine for the update schema
const taskBaseSchema = z.object({
  dateTime: z.string().datetime({ message: 'Invalid start date/time format' }),
  deadline: z
    .string()
    .datetime({ message: 'Invalid deadline date/time format' })
    .optional()
    .nullable(),
  description: z.string().min(1, 'Task description is required'),
  driverEmployeeId: z
    .number()
    .int()
    .positive('Driver Employee ID must be a positive integer.')
    .optional()
    .nullable(),
  estimatedDuration: z.coerce.number().int().min(1, 'Estimated duration must be at least 1 minute'),
  location: z.string().min(1, 'Location is required'),
  notes: z.string().optional().nullable(),
  priority: TaskPriorityEnum.default('Medium'),
  requiredSkills: z.array(z.string()).optional().default([]),
  // NEW PHASE 1 ASSIGNMENT FIELDS
  staffEmployeeId: z.number().int().positive('Staff Employee ID must be a positive integer.'),
  status: TaskStatusEnum.default('Pending'),
  subTasks: z
    .array(SubTaskSchema.omit({ id: true }))
    .optional()
    .default([]),
  vehicleId: z
    .number()
    .int()
    .positive('Vehicle ID must be a positive integer.')
    .optional()
    .nullable(),
});

export const taskUpdateSchema = taskBaseSchema
  .partial()
  .extend({
    statusChangeReason: z.string().optional().nullable(),
  })
  .refine(
    data => {
      // Business rule: vehicleId only allowed if driverEmployeeId is present
      if (data.vehicleId && !data.driverEmployeeId) {
        return false;
      }
      return true;
    },
    {
      message: 'Vehicle can only be assigned when a driver is assigned',
      path: ['vehicleId'],
    },
  );

export const taskIdSchema = z.object({
  // Allow generic string IDs, not just UUIDs, to accommodate task_... format
  id: z.string().min(1, 'Task ID is required'),
});

export type TaskCreate = z.infer<typeof taskCreateSchema>;
export type TaskUpdate = z.infer<typeof taskUpdateSchema>;
