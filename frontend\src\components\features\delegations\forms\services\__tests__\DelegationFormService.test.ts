/**
 * DelegationFormService Unit Tests
 *
 * Tests for the delegation form business logic service
 */

import { DelegationFormService } from '../DelegationFormService';
import type { DelegationFormData } from '@/lib/schemas/delegationSchemas';

describe('DelegationFormService', () => {
  let service: DelegationFormService;

  beforeEach(() => {
    service = new DelegationFormService();
  });

  describe('validateFormData', () => {
    const validFormData: DelegationFormData = {
      eventName: 'Test Event',
      location: 'Test Location',
      durationFrom: '2025-01-01T00:00:00.000Z',
      durationTo: '2025-01-02T00:00:00.000Z',
      status: 'Planned',
      delegates: [{ name: '<PERSON>', title: 'Manager', notes: 'Test notes' }],
      driverEmployeeIds: [],
      escortEmployeeIds: [],
      vehicleIds: [],
      flightArrivalDetails: null,
      flightDepartureDetails: null,
      notes: undefined,
    };

    it('should validate valid form data', () => {
      const result = service.validateFormData(validFormData);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should require event name', () => {
      const invalidData = { ...validFormData, eventName: '' };
      const result = service.validateFormData(invalidData);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContainEqual({
        field: 'eventName',
        message: 'Event name is required',
        code: 'REQUIRED_FIELD',
      });
    });

    it('should require location', () => {
      const invalidData = { ...validFormData, location: '' };
      const result = service.validateFormData(invalidData);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContainEqual({
        field: 'location',
        message: 'Location is required',
        code: 'REQUIRED_FIELD',
      });
    });

    it('should validate date range', () => {
      const invalidData = {
        ...validFormData,
        durationFrom: '2025-01-02T00:00:00.000Z',
        durationTo: '2025-01-01T00:00:00.000Z',
      };
      const result = service.validateFormData(invalidData);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContainEqual({
        field: 'durationTo',
        message: 'End date cannot be earlier than start date',
        code: 'INVALID_DATE_RANGE',
      });
    });

    it('should enforce vehicles require drivers rule', () => {
      const invalidData = {
        ...validFormData,
        vehicleIds: [1, 2],
        driverEmployeeIds: [],
      };
      const result = service.validateFormData(invalidData);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContainEqual({
        field: 'vehicleIds',
        message:
          'Vehicles can only be assigned when at least one driver is assigned',
        code: 'VEHICLES_REQUIRE_DRIVERS',
      });
    });
  });

  describe('validateDelegates', () => {
    it('should require at least one delegate', () => {
      const result = service.validateDelegates([]);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContainEqual({
        field: 'delegates',
        message: 'At least one delegate is required',
        code: 'REQUIRED_FIELD',
      });
    });

    it('should validate delegate fields', () => {
      const delegates = [
        { name: '', title: 'Manager' },
        { name: 'John Doe', title: '' },
      ];
      const result = service.validateDelegates(delegates);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContainEqual({
        field: 'delegates.0.name',
        message: 'Delegate name is required',
        code: 'REQUIRED_FIELD',
      });
      expect(result.errors).toContainEqual({
        field: 'delegates.1.title',
        message: 'Delegate title is required',
        code: 'REQUIRED_FIELD',
      });
    });

    it('should detect duplicate delegate names', () => {
      const delegates = [
        { name: 'John Doe', title: 'Manager' },
        { name: 'john doe', title: 'Director' }, // Case insensitive duplicate
      ];
      const result = service.validateDelegates(delegates);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContainEqual({
        field: 'delegates.1.name',
        message: 'Delegate names must be unique',
        code: 'DUPLICATE_VALUE',
      });
    });
  });

  describe('validateFlightDetails', () => {
    it('should allow null/empty flight details', () => {
      const result = service.validateFlightDetails(null);
      expect(result.isValid).toBe(true);
    });

    it('should require all fields when any field is provided', () => {
      const incompleteDetails = {
        flightNumber: 'BA123',
        dateTime: '',
        airport: '',
      };
      const result = service.validateFlightDetails(incompleteDetails);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContainEqual({
        field: 'dateTime',
        message: 'Date and time are required when flight details are provided',
        code: 'REQUIRED_FIELD',
      });
      expect(result.errors).toContainEqual({
        field: 'airport',
        message: 'Airport is required when flight details are provided',
        code: 'REQUIRED_FIELD',
      });
    });

    it('should validate date format', () => {
      const invalidDetails = {
        flightNumber: 'BA123',
        dateTime: 'invalid-date',
        airport: 'LHR',
      };
      const result = service.validateFlightDetails(invalidDetails);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContainEqual({
        field: 'dateTime',
        message: 'Invalid date/time format',
        code: 'INVALID_FORMAT',
      });
    });
  });

  describe('validateDateRange', () => {
    it('should validate correct date range', () => {
      const result = service.validateDateRange(
        '2025-01-01T00:00:00.000Z',
        '2025-01-02T00:00:00.000Z'
      );
      expect(result).toBe(true);
    });

    it('should allow same dates', () => {
      const result = service.validateDateRange(
        '2025-01-01T00:00:00.000Z',
        '2025-01-01T00:00:00.000Z'
      );
      expect(result).toBe(true);
    });

    it('should reject invalid date range', () => {
      const result = service.validateDateRange(
        '2025-01-02T00:00:00.000Z',
        '2025-01-01T00:00:00.000Z'
      );
      expect(result).toBe(false);
    });

    it('should handle invalid dates', () => {
      const result = service.validateDateRange('invalid', 'also-invalid');
      expect(result).toBe(false);
    });
  });

  describe('calculateDuration', () => {
    it('should calculate duration in days', () => {
      const result = service.calculateDuration(
        '2025-01-01T00:00:00.000Z',
        '2025-01-03T00:00:00.000Z'
      );
      expect(result).toBe(2);
    });

    it('should handle same day', () => {
      const result = service.calculateDuration(
        '2025-01-01T00:00:00.000Z',
        '2025-01-01T23:59:59.000Z'
      );
      expect(result).toBe(1);
    });

    it('should handle invalid dates', () => {
      const result = service.calculateDuration('invalid', 'also-invalid');
      expect(result).toBe(0);
    });
  });

  describe('transformForSubmission', () => {
    it('should transform form data for submission', () => {
      const formData: DelegationFormData = {
        eventName: 'Test Event',
        location: 'Test Location',
        durationFrom: '2025-01-01T00:00:00.000Z',
        durationTo: '2025-01-03T00:00:00.000Z',
        status: 'Planned',
        delegates: [{ name: 'John Doe', title: 'Manager' }],
        driverEmployeeIds: [],
        escortEmployeeIds: [],
        vehicleIds: [],
        flightArrivalDetails: {
          flightNumber: 'BA123',
          dateTime: '2025-01-01T10:00:00.000Z',
          airport: 'London Heathrow (LHR)',
          terminal: '5',
          notes: 'Test flight',
        },
        flightDepartureDetails: null,
        notes: undefined,
      };

      const result = service.transformForSubmission(formData);

      expect(result.calculatedDuration).toBe(2);
      expect(result.processedFlightDetails?.arrival).toBeDefined();
      expect(result.processedFlightDetails?.arrival?.airportCode).toBe('LHR');
      expect(result.processedFlightDetails?.departure).toBeUndefined();
    });
  });
});
