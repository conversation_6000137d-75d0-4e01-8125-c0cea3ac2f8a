/**
 * @component EntityPDFDocument
 * @description React PDF component for generating entity-specific reports
 *
 * Uses react-pdf to create professional PDF documents for individual entities
 * Follows SOLID principles with single responsibility for entity PDF structure
 */

import React from 'react';
import { Document, Page, Text, View, StyleSheet } from '@react-pdf/renderer';
import type {
  EntityReportData,
  ReportTemplate,
  EntityType,
} from '../../data/types/export';

// Define styles with proper typing
const styles = StyleSheet.create({
  page: {
    flexDirection: 'column',
    backgroundColor: '#FFFFFF',
    padding: 30,
    fontFamily: 'Helvetica',
  },
  header: {
    fontSize: 24,
    marginBottom: 20,
    textAlign: 'center',
    color: '#2563eb',
    fontWeight: 'bold',
  },
  subHeader: {
    fontSize: 16,
    marginBottom: 15,
    textAlign: 'center',
    color: '#64748b',
  },
  section: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 16,
    marginBottom: 10,
    color: '#1e293b',
    fontWeight: 'bold',
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0',
    paddingBottom: 5,
  },
  table: {
    width: 'auto',
    borderStyle: 'solid',
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  tableRow: {
    margin: 'auto',
    flexDirection: 'row',
  },
  tableCol: {
    width: '50%',
    borderStyle: 'solid',
    borderWidth: 1,
    borderColor: '#e2e8f0',
    padding: 5,
  },
  tableColHeader: {
    width: '50%',
    borderStyle: 'solid',
    borderWidth: 1,
    borderColor: '#e2e8f0',
    backgroundColor: '#f8fafc',
    padding: 5,
  },
  tableCell: {
    fontSize: 10,
    textAlign: 'center',
  },
  tableCellHeader: {
    fontSize: 10,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  footer: {
    position: 'absolute',
    fontSize: 10,
    bottom: 30,
    left: 30,
    right: 30,
    textAlign: 'center',
    color: '#64748b',
  },
  smallTitle: {
    fontSize: 14,
    marginBottom: 10,
    color: '#1e293b',
    fontWeight: 'bold',
  },
  infoRow: {
    flexDirection: 'row',
    marginBottom: 5,
  },
  infoLabel: {
    fontSize: 12,
    fontWeight: 'bold',
    width: '30%',
    color: '#374151',
  },
  infoValue: {
    fontSize: 12,
    width: '70%',
    color: '#6b7280',
  },
  content: {
    fontSize: 12,
    marginBottom: 10,
  },
});

interface EntityPDFDocumentProps {
  entityType: EntityType;
  data: any;
  options?: any;
}

export const EntityPDFDocument: React.FC<EntityPDFDocumentProps> = ({
  entityType,
  data,
  options,
}) => {
  return (
    <Document>
      <Page size="A4" style={styles.page}>
        <Text style={styles.header}>
          {entityType.charAt(0).toUpperCase() + entityType.slice(1)} Report
        </Text>
        <Text style={styles.content}>Entity Type: {entityType}</Text>
        <Text style={styles.content}>
          Generated on: {new Date().toLocaleDateString()}
        </Text>
        <Text style={styles.content}>
          Data Records: {Array.isArray(data) ? data.length : 1}
        </Text>
      </Page>
    </Document>
  );
};
