/**
 * @file Reliability WebSocket Service for the WorkHub backend.
 * This service is responsible for emitting real-time reliability events such as
 * health updates, alert creations, metrics updates, and circuit breaker status changes.
 * It will be used by the UnifiedWebSocketService as its reliability namespace.
 * @module services/ReliabilityWebSocketService
 */

import type { Server as SocketIOServer } from 'socket.io';

// Placeholder types for domain models - these should be imported from a central types definition
// For now, using 'any'. These would correspond to HealthCheck, Alert, SystemMetrics, CircuitBreakerStatus.
// We will need to ensure these types are available and imported correctly later.
type HealthCheck = any;
type Alert = any;
type SystemMetrics = any;
type CircuitBreakerStatus = any;

// Define event names for reliability updates - these should be consistent with frontend expectations
export const RELIABILITY_EVENTS = {
  HEALTH_UPDATE: 'health-update',
  ALERT_CREATED: 'alert-created',
  METRICS_UPDATE: 'metrics-update',
  CIRCUIT_BREAKER_UPDATE: 'circuit-breaker-update',
};

export class ReliabilityWebSocketService {
  private io: SocketIOServer;
  private reliabilityRoom = 'reliability-monitoring'; // Example room for reliability updates

  constructor(io: SocketIOServer) {
    this.io = io;
    console.log('ReliabilityWebSocketService initialized.');
  }

  /**
   * Emits a health update to clients in the reliability monitoring room.
   * @param healthData - The health check data.
   */
  public emitHealthUpdate(healthData: HealthCheck): void {
    this.io.to(this.reliabilityRoom).emit(RELIABILITY_EVENTS.HEALTH_UPDATE, healthData);
    console.log(`Event [${RELIABILITY_EVENTS.HEALTH_UPDATE}] emitted to room [${this.reliabilityRoom}] with data:`, healthData);
  }

  /**
   * Emits an alert created event to clients in the reliability monitoring room.
   * @param alert - The alert data.
   */
  public emitAlertCreated(alert: Alert): void {
    this.io.to(this.reliabilityRoom).emit(RELIABILITY_EVENTS.ALERT_CREATED, alert);
    console.log(`Event [${RELIABILITY_EVENTS.ALERT_CREATED}] emitted to room [${this.reliabilityRoom}] with data:`, alert);
  }

  /**
   * Emits a system metrics update to clients in the reliability monitoring room.
   * @param metrics - The system metrics data.
   */
  public emitMetricsUpdate(metrics: SystemMetrics): void {
    this.io.to(this.reliabilityRoom).emit(RELIABILITY_EVENTS.METRICS_UPDATE, metrics);
    console.log(`Event [${RELIABILITY_EVENTS.METRICS_UPDATE}] emitted to room [${this.reliabilityRoom}] with data:`, metrics);
  }

  /**
   * Emits a circuit breaker status update to clients in the reliability monitoring room.
   * @param status - The circuit breaker status data.
   */
  public emitCircuitBreakerUpdate(status: CircuitBreakerStatus): void {
    this.io.to(this.reliabilityRoom).emit(RELIABILITY_EVENTS.CIRCUIT_BREAKER_UPDATE, status);
    console.log(`Event [${RELIABILITY_EVENTS.CIRCUIT_BREAKER_UPDATE}] emitted to room [${this.reliabilityRoom}] with data:`, status);
  }

  // TODO: Add methods for clients to join/leave the reliabilityRoom if not handled by UnifiedWebSocketService
} 