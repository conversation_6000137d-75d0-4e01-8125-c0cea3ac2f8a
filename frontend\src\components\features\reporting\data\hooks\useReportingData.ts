/**
 * @file Reporting Data Hooks
 * @description Contains custom hooks for fetching and managing reporting data.
 */

import { useApiQuery, type ApiQueryOptions } from '@/hooks/api';
import { reportingDataService } from '../services/ReportingDataService';

import type {
  DelegationAnalytics,
  ReportingFilters,
  TrendData,
  LocationMetrics,
} from '../types';

/**
 * Hook to fetch delegation analytics data.
 * @param filters - The filters to apply to the query.
 * @param options - Optional query options.
 * @returns The result of the API query.
 */
export const useDelegationAnalytics = (
  filters: ReportingFilters,
  options: ApiQueryOptions<DelegationAnalytics> = {}
) => {
  return useApiQuery(
    ['delegationAnalytics', filters],
    () => reportingDataService.getDelegationAnalytics(filters),
    options
  );
};

/**
 * Hook to fetch task metrics.
 * @param delegationIds - The IDs of the delegations to fetch metrics for.
 * @param options - Optional query options.
 * @returns The result of the API query.
 */
export const useTaskMetrics = (
  delegationIds: string[],
  options: ApiQueryOptions<any> = {} // Replace 'any' with TaskMetrics type
) => {
  return useApiQuery(
    ['taskMetrics', delegationIds],
    () => reportingDataService.getTaskMetrics(delegationIds),
    {
      enabled: !!delegationIds && delegationIds.length > 0, // Only run if IDs are provided
      ...options,
    }
  );
};

/**
 * Hook to fetch trend data.
 * @param filters - The filters to apply to the query.
 * @param options - Optional query options.
 * @returns The result of the API query.
 */
export const useTrendData = (
  filters: ReportingFilters,
  options: ApiQueryOptions<TrendData[]> = {}
) => {
  return useApiQuery(
    ['trendData', filters],
    () => reportingDataService.getTrendData(filters),
    options
  );
};

/**
 * Hook to fetch location metrics.
 * @param filters - The filters to apply to the query.
 * @param options - Optional query options.
 * @returns The result of the API query.
 */
export const useLocationMetrics = (
  filters: ReportingFilters,
  options: ApiQueryOptions<LocationMetrics[]> = {}
) => {
  return useApiQuery(
    ['locationMetrics', filters],
    () => reportingDataService.getLocationMetrics(filters),
    options
  );
};
