'use client';

import {
  <PERSON>ert<PERSON>riangle,
  Clock,
  Database,
  RefreshCw,
  Shield,
  Trash2,
} from 'lucide-react';
import React, { useEffect, useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { adminCache } from '@/lib/api/services/admin';
import { CircuitState } from '@/lib/utils/circuitBreaker';
import { CACHE_DURATIONS, requestCache } from '@/lib/utils/requestCache';

interface CacheEntry {
  age: number;
  expiresIn: number;
  key: string;
}

interface CacheStats {
  entries: CacheEntry[];
  size: number;
}

interface CircuitBreakerStatus {
  failureCount: number;
  lastFailureTime: number;
  state: CircuitState;
  timeUntilRetry?: number;
}

export function CacheStatus() {
  const [stats, setStats] = useState<CacheStats>({ entries: [], size: 0 });
  const [circuitBreakers, setCircuitBreakers] = useState<
    Record<string, CircuitBreakerStatus>
  >({});
  const [refreshing, setRefreshing] = useState(false);

  const refreshStats = () => {
    const newStats = requestCache.getStats();
    setStats(newStats);

    const cbStatus: Record<string, CircuitBreakerStatus> = {};
    setCircuitBreakers(cbStatus);
  };

  useEffect(() => {
    refreshStats();
    const interval = setInterval(refreshStats, 1000); // Update every second
    return () => clearInterval(interval);
  }, []);

  const handleClearCache = () => {
    adminCache.clearAll();
    refreshStats();
  };

  const handleResetCircuitBreakers = () => {
    // Mock implementation - just clear all cache (no resetCircuitBreakers available)
    adminCache.clearAll();
    refreshStats();
  };

  const handleRefreshStats = async () => {
    setRefreshing(true);
    refreshStats();
    setTimeout(() => setRefreshing(false), 500);
  };

  const formatDuration = (ms: number): string => {
    if (ms < 0) return 'Expired';
    if (ms < 1000) return `${Math.round(ms)}ms`;
    if (ms < 60_000) return `${Math.round(ms / 1000)}s`;
    return `${Math.round(ms / 60_000)}m`;
  };

  const getCacheType = (key: string): string => {
    if (key.includes('health')) return 'Health';
    if (key.includes('performance')) return 'Performance';
    if (key.includes('errors')) return 'Errors';
    return 'Other';
  };

  const getCacheTypeColor = (key: string): string => {
    if (key.includes('health')) return 'bg-green-100 text-green-800';
    if (key.includes('performance')) return 'bg-blue-100 text-blue-800';
    if (key.includes('errors')) return 'bg-red-100 text-red-800';
    return 'bg-gray-100 text-gray-800';
  };

  const adminEntries = stats.entries.filter(entry =>
    entry.key.startsWith('admin:')
  );

  return (
    <Card className="shadow-md">
      <CardHeader className="p-5">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2 text-xl font-semibold text-primary">
              <Database className="size-5" />
              Cache Status
            </CardTitle>
            <p className="mt-1 text-sm text-muted-foreground">
              Request cache performance and timing
            </p>
          </div>
          <div className="flex gap-2">
            <Button
              className="flex items-center gap-2"
              disabled={refreshing}
              onClick={handleRefreshStats}
              size="sm"
              variant="outline"
            >
              <RefreshCw
                className={`size-4 ${refreshing ? 'animate-spin' : ''}`}
              />
              Refresh
            </Button>
            <Button
              className="flex items-center gap-2 text-red-600 hover:text-red-700"
              onClick={handleClearCache}
              size="sm"
              variant="outline"
            >
              <Trash2 className="size-4" />
              Clear Cache
            </Button>
            <Button
              className="flex items-center gap-2 text-orange-600 hover:text-orange-700"
              onClick={handleResetCircuitBreakers}
              size="sm"
              variant="outline"
            >
              <Shield className="size-4" />
              Reset Breakers
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-5">
        <div className="space-y-4">
          {/* Cache Summary */}
          <div className="grid grid-cols-2 gap-4 md:grid-cols-4">
            <div className="rounded-lg bg-gray-50 p-3 text-center">
              <div className="text-2xl font-bold text-primary">
                {stats.size}
              </div>
              <div className="text-sm text-muted-foreground">Total Entries</div>
            </div>
            <div className="rounded-lg bg-gray-50 p-3 text-center">
              <div className="text-2xl font-bold text-green-600">
                {adminEntries.length}
              </div>
              <div className="text-sm text-muted-foreground">Admin Entries</div>
            </div>
            <div className="rounded-lg bg-gray-50 p-3 text-center">
              <div className="text-2xl font-bold text-blue-600">
                {formatDuration(CACHE_DURATIONS.HEALTH_STATUS)}
              </div>
              <div className="text-sm text-muted-foreground">Health Cache</div>
            </div>
            <div className="rounded-lg bg-gray-50 p-3 text-center">
              <div className="text-2xl font-bold text-purple-600">
                {formatDuration(CACHE_DURATIONS.PERFORMANCE_METRICS)}
              </div>
              <div className="text-sm text-muted-foreground">
                Performance Cache
              </div>
            </div>
          </div>

          {/* Cache Entries */}
          {adminEntries.length > 0 ? (
            <div className="space-y-2">
              <h4 className="text-sm font-semibold uppercase tracking-wide text-muted-foreground">
                Active Cache Entries
              </h4>
              {adminEntries.map((entry, index) => (
                <div
                  className="flex items-center justify-between rounded-lg bg-gray-50 p-3"
                  key={index}
                >
                  <div className="flex items-center gap-3">
                    <Badge className={getCacheTypeColor(entry.key)}>
                      {getCacheType(entry.key)}
                    </Badge>
                    <span className="font-mono text-sm text-gray-600">
                      {entry.key.replace('admin:', '')}
                    </span>
                  </div>
                  <div className="flex items-center gap-4 text-sm">
                    <div className="flex items-center gap-1 text-muted-foreground">
                      <Clock className="size-3" />
                      Age: {formatDuration(entry.age)}
                    </div>
                    <div
                      className={`font-medium ${
                        entry.expiresIn > 0 ? 'text-green-600' : 'text-red-600'
                      }`}
                    >
                      {entry.expiresIn > 0
                        ? `Expires in ${formatDuration(entry.expiresIn)}`
                        : 'Expired'}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="py-8 text-center text-muted-foreground">
              <Database className="mx-auto mb-3 size-12 opacity-50" />
              <p>No cache entries found</p>
              <p className="text-sm">
                Cache entries will appear here after API calls
              </p>
            </div>
          )}

          {/* Circuit Breaker Status */}
          <div className="mt-6 rounded-lg bg-orange-50 p-4">
            <h4 className="mb-3 flex items-center gap-2 font-semibold text-orange-900">
              <Shield className="size-4" />
              Circuit Breaker Status
            </h4>
            <div className="grid grid-cols-1 gap-3 md:grid-cols-2 lg:grid-cols-4">
              {Object.entries(circuitBreakers).map(([name, status]) => (
                <div
                  className={`rounded-lg border p-3 ${
                    status.state === CircuitState.CLOSED
                      ? 'border-green-200 bg-green-50'
                      : status.state === CircuitState.HALF_OPEN
                        ? 'border-yellow-200 bg-yellow-50'
                        : 'border-red-200 bg-red-50'
                  }`}
                  key={name}
                >
                  <div className="mb-2 flex items-center justify-between">
                    <span className="text-sm font-medium capitalize">
                      {name}
                    </span>
                    <Badge
                      className={
                        status.state === CircuitState.CLOSED
                          ? 'bg-green-100 text-green-800'
                          : status.state === CircuitState.HALF_OPEN
                            ? 'bg-yellow-100 text-yellow-800'
                            : 'bg-red-100 text-red-800'
                      }
                    >
                      {status.state === CircuitState.CLOSED && (
                        <Shield className="mr-1 size-3" />
                      )}
                      {status.state === CircuitState.OPEN && (
                        <AlertTriangle className="mr-1 size-3" />
                      )}
                      {status.state === CircuitState.HALF_OPEN && (
                        <RefreshCw className="mr-1 size-3" />
                      )}
                      {status.state}
                    </Badge>
                  </div>
                  <div className="text-xs text-muted-foreground">
                    <div>Failures: {status.failureCount}</div>
                    {status.timeUntilRetry && (
                      <div>
                        Retry in: {formatDuration(status.timeUntilRetry)}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Cache Configuration */}
          <div className="mt-6 rounded-lg bg-blue-50 p-4">
            <h4 className="mb-2 font-semibold text-blue-900">
              Cache Configuration
            </h4>
            <div className="grid grid-cols-2 gap-3 text-sm md:grid-cols-3">
              <div>
                <span className="font-medium text-blue-800">
                  Health Status:
                </span>
                <span className="ml-2 text-blue-600">
                  {formatDuration(CACHE_DURATIONS.HEALTH_STATUS)}
                </span>
              </div>
              <div>
                <span className="font-medium text-blue-800">Performance:</span>
                <span className="ml-2 text-blue-600">
                  {formatDuration(CACHE_DURATIONS.PERFORMANCE_METRICS)}
                </span>
              </div>
              <div>
                <span className="font-medium text-blue-800">Error Logs:</span>
                <span className="ml-2 text-blue-600">
                  {formatDuration(CACHE_DURATIONS.ERROR_LOGS)}
                </span>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
