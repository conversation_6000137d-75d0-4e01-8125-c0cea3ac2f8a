'use client';

import { Info, Settings } from 'lucide-react';

import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { AuditLogViewer } from '@/components/features/admin/auditLogViewer';
import { SupabaseDiagnostics } from '@/components/features/admin/SupabaseDiagnostics';
import { UserManagement } from '@/components/features/admin/UserManagement';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

export default function AdminPage() {
  return (
    <ProtectedRoute allowedRoles={['ADMIN', 'SUPER_ADMIN']}>
      <div className="space-y-6">
        <div className="mb-6 flex items-center space-x-2">
          <Settings className="size-8 text-primary" />
          <div>
            <h1 className="text-3xl font-bold text-primary">Admin Dashboard</h1>
            <p className="text-muted-foreground">
              System administration and diagnostics
            </p>
          </div>
        </div>

        <Alert>
          <Info className="size-4" />
          <AlertTitle>Information</AlertTitle>
          <AlertDescription>
            This admin dashboard provides system diagnostics and monitoring
            tools. Access is restricted to ADMIN and SUPER_ADMIN roles.
          </AlertDescription>
        </Alert>

        <Tabs className="w-full" defaultValue="system-diagnostics">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="system-diagnostics">
              System Diagnostics
            </TabsTrigger>
            <TabsTrigger value="user-management">User Management</TabsTrigger>
            <TabsTrigger value="audit-logs">Audit Logs</TabsTrigger>
          </TabsList>

          <TabsContent className="mt-6" value="system-diagnostics">
            <SupabaseDiagnostics />
          </TabsContent>

          <TabsContent className="mt-6" value="user-management">
            <UserManagement />
          </TabsContent>

          <TabsContent className="mt-6" value="audit-logs">
            <AuditLogViewer />
          </TabsContent>
        </Tabs>
      </div>
    </ProtectedRoute>
  );
}
