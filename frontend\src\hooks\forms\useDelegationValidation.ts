'use client';

import { useCallback, useMemo } from 'react';
import { useFormContext } from 'react-hook-form';

import type { DelegationFormData } from '@/lib/schemas/delegationSchemas';

// ============================================================================
// TYPES
// ============================================================================

export interface CrossFieldValidation {
  dependsOn: string[];
  field: string;
  validate: (data: Partial<DelegationFormData>) => {
    isValid: boolean;
    message?: string;
    type: 'error' | 'warning';
  };
}

export interface FieldValidationState {
  errorMessage?: string;
  hasError: boolean;
  hasWarning: boolean;
  isDirty: boolean;
  isRequired: boolean;
  isTouched: boolean;
  isValid: boolean;
  warningMessage?: string;
}

export interface SectionValidationState {
  completionPercentage: number;
  errorCount: number;
  hasErrors: boolean;
  hasWarnings: boolean;
  isComplete: boolean;
  isValid: boolean;
  warningCount: number;
}

export interface ValidationState {
  errorCount: number;
  hasErrors: boolean;
  hasWarnings: boolean;
  isValid: boolean;
  warningCount: number;
}

// ============================================================================
// VALIDATION RULES
// ============================================================================

const CROSS_FIELD_VALIDATIONS: CrossFieldValidation[] = [
  {
    dependsOn: ['driverEmployeeIds'],
    field: 'vehicleIds',
    validate: data => {
      const hasVehicles = data.vehicleIds && data.vehicleIds.length > 0;
      const hasDrivers =
        data.driverEmployeeIds && data.driverEmployeeIds.length > 0;

      if (hasVehicles && !hasDrivers) {
        return {
          isValid: false,
          message: 'Vehicles cannot be assigned without at least one driver',
          type: 'error',
        };
      }

      return { isValid: true, type: 'error' };
    },
  },
  {
    dependsOn: ['durationFrom'],
    field: 'durationTo',
    validate: data => {
      if (data.durationFrom && data.durationTo) {
        const startDate = new Date(data.durationFrom);
        const endDate = new Date(data.durationTo);

        if (startDate > endDate) {
          return {
            isValid: false,
            message: 'End date cannot be earlier than start date',
            type: 'error',
          };
        }

        // Warning for very long delegations
        const daysDiff =
          (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24);
        if (daysDiff > 30) {
          return {
            isValid: true,
            message:
              'Delegation duration exceeds 30 days. Please verify this is correct.',
            type: 'warning',
          };
        }
      }

      return { isValid: true, type: 'error' };
    },
  },
  {
    dependsOn: ['delegates'],
    field: 'delegates',
    validate: data => {
      if (data.delegates && data.delegates.length > 5) {
        return {
          isValid: true,
          message:
            'Large number of delegates may require additional coordination',
          type: 'warning',
        };
      }

      return { isValid: true, type: 'warning' };
    },
  },
];

// ============================================================================
// HOOK IMPLEMENTATION
// ============================================================================

/**
 * Enhanced validation hook for delegation forms
 * Provides comprehensive validation state management and cross-field validation
 */
export const useDelegationValidation = () => {
  const formContext = useFormContext<DelegationFormData>();

  const {
    formState: { dirtyFields, errors, touchedFields },
    trigger,
    watch,
  } = formContext; // Non-null assertion: formContext is guaranteed to be defined here due to the early return

  const watchedValues = watch();

  // Calculate overall validation state
  const validationState = useMemo((): ValidationState => {
    const errorCount = Object.keys(errors).length;
    const crossFieldErrors = CROSS_FIELD_VALIDATIONS.filter(
      validation => !validation.validate(watchedValues).isValid
    ).length;

    const crossFieldWarnings = CROSS_FIELD_VALIDATIONS.filter(validation => {
      const result = validation.validate(watchedValues);
      return result.isValid && result.type === 'warning' && result.message;
    }).length;

    return {
      errorCount: errorCount + crossFieldErrors,
      hasErrors: errorCount > 0 || crossFieldErrors > 0,
      hasWarnings: crossFieldWarnings > 0,
      isValid: errorCount === 0 && crossFieldErrors === 0,
      warningCount: crossFieldWarnings,
    };
  }, [errors, watchedValues]);

  // Get field validation state
  const getFieldValidationState = useCallback(
    (fieldName: keyof DelegationFormData): FieldValidationState => {
      const error = errors[fieldName];
      const isTouched = touchedFields[fieldName] ?? false;
      const isDirty = dirtyFields[fieldName] ?? false;

      // Check for cross-field validation
      const crossFieldValidation = CROSS_FIELD_VALIDATIONS.find(
        validation => validation.field === fieldName
      );

      let crossFieldResult = null;
      if (crossFieldValidation) {
        crossFieldResult = crossFieldValidation.validate(watchedValues);
      }

      return {
        errorMessage:
          error?.message ??
          (crossFieldResult && !crossFieldResult.isValid
            ? crossFieldResult.message
            : '') ??
          '',
        hasError: Boolean(
          !!error ||
            (crossFieldResult &&
              !crossFieldResult.isValid &&
              crossFieldResult.type === 'error')
        ),
        hasWarning: Boolean(
          crossFieldResult &&
            crossFieldResult.isValid &&
            crossFieldResult.type === 'warning' &&
            !!crossFieldResult.message
        ),
        isDirty: Boolean(isDirty),
        isRequired: isFieldRequired(fieldName),
        isTouched: Boolean(isTouched),
        isValid: !error && (!crossFieldResult || crossFieldResult.isValid),
        warningMessage:
          (crossFieldResult &&
          crossFieldResult.isValid &&
          crossFieldResult.type === 'warning'
            ? crossFieldResult.message
            : '') ?? '',
      };
    },
    [errors, touchedFields, dirtyFields, watchedValues]
  );

  // Get section validation state
  const getSectionValidationState = useCallback(
    (sectionId: string): SectionValidationState => {
      const sectionFields = getSectionFields(sectionId);
      const sectionErrors = sectionFields.filter(field => {
        const fieldState = getFieldValidationState(field);
        return fieldState.hasError;
      });

      const sectionWarnings = sectionFields.filter(field => {
        const fieldState = getFieldValidationState(field);
        return fieldState.hasWarning;
      });

      const completedFields = sectionFields.filter(field => {
        const value = watchedValues[field];
        return isFieldComplete(field, value);
      });

      const completionPercentage =
        sectionFields.length > 0
          ? Math.round((completedFields.length / sectionFields.length) * 100)
          : 0;

      return {
        completionPercentage,
        errorCount: sectionErrors.length,
        hasErrors: sectionErrors.length > 0,
        hasWarnings: sectionWarnings.length > 0,
        isComplete: isSectionComplete(sectionId, watchedValues),
        isValid: sectionErrors.length === 0,
        warningCount: sectionWarnings.length,
      };
    },
    [getFieldValidationState, watchedValues]
  );

  // Validate specific field
  const validateField = useCallback(
    async (fieldName: keyof DelegationFormData): Promise<boolean> => {
      const result = await trigger(fieldName);
      return result;
    },
    [trigger]
  );

  // Validate entire form
  const validateForm = useCallback(async (): Promise<boolean> => {
    const result = await trigger();
    return result;
  }, [trigger]);

  // Get all cross-field validation errors
  const getCrossFieldErrors = useCallback(() => {
    return CROSS_FIELD_VALIDATIONS.filter(
      validation => !validation.validate(watchedValues).isValid
    ).map(validation => ({
      field: validation.field,
      message: validation.validate(watchedValues).message ?? 'Validation error',
    }));
  }, [watchedValues]);

  // Get all cross-field validation warnings
  const getCrossFieldWarnings = useCallback(() => {
    return CROSS_FIELD_VALIDATIONS.filter(validation => {
      const result = validation.validate(watchedValues);
      return result.isValid && result.type === 'warning' && result.message;
    }).map(validation => ({
      field: validation.field,
      message:
        validation.validate(watchedValues).message ?? 'Validation warning',
    }));
  }, [watchedValues]);

  return {
    getCrossFieldErrors,
    getCrossFieldWarnings,
    getFieldValidationState,
    getSectionValidationState,
    validateField,
    validateForm,
    validationState,
  };
};

// ============================================================================
// HELPER FUNCTIONS
// ============================================================================

function getSectionFields(sectionId: string): (keyof DelegationFormData)[] {
  const sectionFieldMap: Record<string, (keyof DelegationFormData)[]> = {
    assignment: ['driverEmployeeIds', 'escortEmployeeIds'],
    basic: ['eventName', 'location', 'durationFrom', 'durationTo', 'status'],
    delegates: ['delegates'],
    logistics: ['vehicleIds', 'flightArrivalDetails', 'flightDepartureDetails'],
    notes: ['notes', 'invitationFrom', 'invitationTo', 'imageUrl'],
  };

  return sectionFieldMap[sectionId] ?? [];
}

function isFieldComplete(
  fieldName: keyof DelegationFormData,
  value: unknown // Change 'any' to 'unknown' for safer typing
): boolean {
  if (fieldName === 'delegates') {
    return (
      Array.isArray(value) &&
      value.length > 0 &&
      value.every(
        (d: { name?: string; title?: string }) => d.name && d.title // Explicitly type 'd'
      )
    );
  }

  if (Array.isArray(value)) {
    return value.length > 0;
  }

  if (typeof value === 'string') {
    return value.trim().length > 0;
  }

  return value != null;
}

function isFieldRequired(fieldName: keyof DelegationFormData): boolean {
  const requiredFields: (keyof DelegationFormData)[] = [
    'eventName',
    'location',
    'durationFrom',
    'durationTo',
    'delegates',
  ];

  return requiredFields.includes(fieldName);
}

function isSectionComplete(
  sectionId: string,
  data: Partial<DelegationFormData>
): boolean {
  switch (sectionId) {
    case 'assignment': {
      return !!(
        (data.driverEmployeeIds && data.driverEmployeeIds.length > 0) ??
        (data.escortEmployeeIds && data.escortEmployeeIds.length > 0)
      );
    }
    case 'basic': {
      return !!(
        data.eventName &&
        data.location &&
        data.durationFrom &&
        data.durationTo
      );
    }
    case 'delegates': {
      return !!(
        data.delegates &&
        data.delegates.length > 0 &&
        data.delegates.every(d => d.name && d.title)
      );
    }
    case 'logistics': {
      return !!(
        (data.vehicleIds && data.vehicleIds.length > 0) ??
        data.flightArrivalDetails?.flightNumber ??
        data.flightDepartureDetails?.flightNumber
      );
    }
    case 'notes': {
      return !!(
        data.notes?.trim() ??
        data.invitationFrom?.trim() ??
        data.invitationTo?.trim() ??
        data.imageUrl?.trim()
      );
    }
    default: {
      return false;
    }
  }
}
