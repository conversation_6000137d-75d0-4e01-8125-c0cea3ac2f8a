import {
  PrismaClient,
  EmployeeRole,
  EmployeeStatus,
  DriverAvailability,
  TaskPriority,
  TaskStatus,
  DelegationStatus,
} from '../src/generated/prisma/index.js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Log database connection info (without exposing credentials)
console.log(`Database Environment: ${process.env.DATABASE_ENV || 'not set'}`);
console.log(
  `Using database connection: ${
    process.env.DATABASE_URL?.replace(/\/\/.*?@/, '//****@') || 'not set'
  }`,
);

const prisma = new PrismaClient({
  log: ['warn', 'error'],
});

async function main() {
  console.log('Starting to seed database...');
  console.log(`Database Environment: ${process.env.DATABASE_ENV || 'local (default)'}`);

  // Verify database connection
  try {
    await prisma.$connect();
    console.log('Successfully connected to the database');
  } catch (connectionError) {
    console.error('Failed to connect to the database:', connectionError);
    process.exit(1);
  }

  try {
    // Clean up existing data to avoid conflicts during re-seeding
    await prisma.serviceRecord.deleteMany({});
    await prisma.subTask.deleteMany({});
    await prisma.taskStatusEntry.deleteMany({});
    await prisma.task.deleteMany({});
    await prisma.delegationStatusEntry.deleteMany({});
    await prisma.flightDetails.deleteMany({});
    await prisma.delegate.deleteMany({});
    await prisma.delegation.deleteMany({});
    await prisma.employeeStatusEntry.deleteMany({});
    // Dissociate tasks from employees before deleting employees
    const employeesWithTasks = await prisma.employee.findMany({
      where: { assignedTasks: { some: {} } },
    });
    for (const emp of employeesWithTasks) {
      await prisma.employee.update({
        where: { id: emp.id },
        data: { assignedTasks: { set: [] } },
      });
    }
    await prisma.employee.deleteMany({});
    await prisma.vehicle.deleteMany({});

    console.log('Cleaned existing data.');

    // Create sample vehicles
    const vehiclesData = [
      {
        make: 'Toyota',
        model: 'Camry',
        year: 2020,
        vin: '4T1BF1FK5CU123456',
        licensePlate: 'ABC123',
        ownerName: 'John Doe',
        ownerContact: '<EMAIL>',
        color: 'Blue',
        initialOdometer: 15000,
        imageUrl: 'https://picsum.photos/seed/vehicle1/600/400',
      },
      {
        make: 'Honda',
        model: 'Civic',
        year: 2019,
        vin: '2HGFC2F50KH123456',
        licensePlate: 'XYZ789',
        ownerName: 'Jane Smith',
        ownerContact: '555-123-4567',
        color: 'Red',
        initialOdometer: 22000,
        imageUrl: 'https://picsum.photos/seed/vehicle2/600/400',
      },
      {
        make: 'Ford',
        model: 'F-150',
        year: 2021,
        vin: '1FTEX1EP1MKE12345',
        licensePlate: 'TRK456',
        ownerName: 'Bob Johnson',
        ownerContact: '<EMAIL>',
        color: 'Black',
        initialOdometer: 5000,
        imageUrl: 'https://picsum.photos/seed/vehicle3/600/400',
      },
    ];

    const createdVehicles: any[] = [];
    for (const vehicle of vehiclesData) {
      const created = await prisma.vehicle.upsert({
        where: { vin: vehicle.vin },
        update: vehicle,
        create: vehicle,
      });
      createdVehicles.push(created);
    }
    console.log('Added sample vehicles');

    // Create sample employees
    const employeesData = [
      {
        name: 'Alice Technician',
        fullName: 'Alice Technician',
        role: EmployeeRole.technician,
        employeeId: 'EMP-1001',
        contactInfo: '<EMAIL>',
        position: 'Senior Technician',
        department: 'Maintenance',
        hireDate: new Date('2020-01-15T00:00:00.000Z'),
        status: EmployeeStatus.Active,
        skills: ['Engine Repair', 'Diagnostics'],
        profileImageUrl: 'https://picsum.photos/seed/emp1/200/200',
        shiftSchedule: 'Mon-Fri 9am-5pm',
      },
      {
        name: 'Bob Manager',
        fullName: 'Bob Manager',
        role: EmployeeRole.manager,
        employeeId: 'EMP-1002',
        contactInfo: '555-987-6543',
        position: 'Service Manager',
        department: 'Management',
        hireDate: new Date('2018-05-20T00:00:00.000Z'),
        status: EmployeeStatus.Active,
        shiftSchedule: 'Mon-Fri 9am-5pm',
      },
      {
        name: 'Charlie Driver',
        fullName: 'Charlie Driver',
        role: EmployeeRole.driver,
        employeeId: 'EMP-1003',
        contactInfo: '<EMAIL>',
        position: 'Delivery Driver',
        department: 'Logistics',
        hireDate: new Date('2022-03-10T00:00:00.000Z'),
        status: EmployeeStatus.Active,
        availability: DriverAvailability.On_Shift,
        currentLocation: 'Main Garage',
        workingHours: 'Mon-Fri 8am-4pm',
        assignedVehicleId: createdVehicles[0]?.id,
      },
      {
        name: 'Diana Mechanic',
        fullName: 'Diana Mechanic',
        role: EmployeeRole.mechanic,
        employeeId: 'EMP-1004',
        contactInfo: '<EMAIL>',
        position: 'Mechanic',
        department: 'Maintenance',
        hireDate: new Date('2021-07-01T00:00:00.000Z'),
        status: EmployeeStatus.On_Leave,
        skills: ['Brake Systems', 'Suspension'],
        notes: 'Expected back next month.',
      },
    ];

    const createdEmployees: any[] = [];
    for (const employee of employeesData) {
      const created = await prisma.employee.upsert({
        where: { employeeId: employee.employeeId },
        update: {
          ...employee,
          assignedVehicleId:
            employee.role === EmployeeRole.driver ? employee.assignedVehicleId : null,
        },
        create: {
          ...employee,
          assignedVehicleId:
            employee.role === EmployeeRole.driver ? employee.assignedVehicleId : null,
        },
      });
      createdEmployees.push(created);
    }
    console.log('Added sample employees');

    // Add Employee Status History
    for (const emp of createdEmployees) {
      await prisma.employeeStatusEntry.create({
        data: {
          employeeId: emp.id,
          status: emp.status ?? 'Active',
          reason: 'Initial status',
        },
      });
    }
    console.log('Added employee status history');

    // Create sample service records
    if (createdVehicles.length > 0 && createdEmployees.length > 0) {
      const mechanicEmployee = createdEmployees.find(
        e => e.role === EmployeeRole.mechanic || e.role === EmployeeRole.technician,
      );
      const serviceRecordsData = [
        {
          vehicleId: createdVehicles[0].id,
          employeeId: mechanicEmployee?.id,
          date: new Date('2023-05-15T00:00:00.000Z'),
          odometer: 18000,
          servicePerformed: ['Oil Change', 'Tire Rotation'],
          notes: 'Used synthetic oil.',
          cost: 75.5,
        },
        {
          vehicleId: createdVehicles[1].id,
          employeeId: mechanicEmployee?.id,
          date: new Date('2023-06-20T00:00:00.000Z'),
          odometer: 25000,
          servicePerformed: ['Brake Pad Replacement'],
          cost: 250.0,
        },
      ];
      for (const sr of serviceRecordsData) {
        if (sr.employeeId) {
          // Only create if a suitable employee was found
          await prisma.serviceRecord.create({ data: sr });
        }
      }
      console.log('Added sample service records');
    }

    // Create sample Delegations
    const delegationsData = [
      {
        eventName: 'Tech Conference 2024',
        location: 'San Francisco, CA',
        durationFrom: new Date('2024-09-10T00:00:00.000Z'),
        durationTo: new Date('2024-09-12T00:00:00.000Z'),
        status: DelegationStatus.Confirmed,
        delegates: {
          create: [
            { name: 'Alice Technician', title: 'Lead Speaker' },
            { name: 'Bob Manager', title: 'Attendee' },
          ],
        },
        flightArrivalDetails: {
          create: {
            flightNumber: 'UA234',
            dateTime: new Date('2024-09-09T14:00:00.000Z'),
            airport: 'SFO',
            terminal: 'T3',
          },
        },
        statusHistory: {
          create: [
            { status: DelegationStatus.Planned, reason: 'Initial planning' },
            {
              status: DelegationStatus.Confirmed,
              reason: 'All bookings confirmed',
              changedAt: new Date('2024-07-15T00:00:00.000Z'),
            },
          ],
        },
      },
      {
        eventName: 'Client Workshop Q4',
        location: 'Remote',
        durationFrom: new Date('2024-10-05T00:00:00.000Z'),
        durationTo: new Date('2024-10-05T00:00:00.000Z'),
        status: DelegationStatus.Planned,
        delegates: {
          create: [{ name: 'Charlie Driver', title: 'Logistics Lead' }],
        },
        statusHistory: {
          create: [
            {
              status: DelegationStatus.Planned,
              reason: 'Initial planning for remote workshop',
            },
          ],
        },
      },
    ];
    for (const delegation of delegationsData) {
      await prisma.delegation.create({ data: delegation });
    }
    console.log('Added sample delegations');

    // Create sample Tasks
    const driverEmployee = createdEmployees.find(e => e.role === EmployeeRole.driver);
    const mechanicForTask = createdEmployees.find(e => e.role === EmployeeRole.mechanic);

    if (driverEmployee && mechanicForTask) {
      const tasksData = [
        {
          description: 'Pick up parts from supplier',
          location: 'Supplier Warehouse, Downtown',
          dateTime: new Date('2024-07-28T10:00:00.000Z'),
          estimatedDuration: 120, // minutes
          priority: TaskPriority.High,
          status: TaskStatus.Assigned,
          assignedEmployees: { connect: { id: driverEmployee.id } },
          assignedEmployeeIds: [String(driverEmployee.id)],
          vehicleId: createdVehicles[2]?.id, // Assign the F-150
          subTasks: {
            create: [
              { title: 'Confirm order details before pickup', completed: false },
              { title: 'Inspect parts upon receipt', completed: false },
            ],
          },
          statusHistory: {
            create: [
              { status: TaskStatus.Pending, reason: 'Task created' },
              { status: TaskStatus.Assigned, reason: 'Assigned to driver' },
            ],
          },
        },
        {
          description: 'Perform annual vehicle inspection for Vehicle 1',
          location: 'Main Garage Bay 1',
          dateTime: new Date('2024-08-01T09:00:00.000Z'),
          estimatedDuration: 240,
          priority: TaskPriority.Medium,
          status: TaskStatus.Pending,
          requiredSkills: ['Vehicle Inspection Certified', 'Diagnostic Tools'],
          assignedEmployees: { connect: { id: mechanicForTask.id } },
          assignedEmployeeIds: [String(mechanicForTask.id)],
          vehicleId: createdVehicles[0]?.id,
          statusHistory: {
            create: [{ status: TaskStatus.Pending, reason: 'Task created' }],
          },
        },
      ];

      for (const task of tasksData) {
        await prisma.task.create({ data: task });
      }
      console.log('Added sample tasks');
    }

    console.log('Database seeding completed successfully');
  } catch (error) {
    console.error('Error seeding database:', error);
    throw error;
  }
}

main()
  .catch(e => {
    console.error('Error in seed script:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
