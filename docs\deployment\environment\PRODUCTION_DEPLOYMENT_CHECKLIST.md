# Production Deployment Checklist

## 🎯 Overview

This checklist ensures production-ready deployment with proper environment configuration, security, and cross-origin support.

## 📋 Pre-Deployment Checklist

### Environment Configuration

#### Frontend Environment Variables
- [ ] `NEXT_PUBLIC_API_URL` - Production backend URL
- [ ] `NEXT_PUBLIC_API_BASE_URL` - Production API endpoint
- [ ] `NEXT_PUBLIC_WS_URL` - Production WebSocket URL (WSS)
- [ ] `NEXT_PUBLIC_SUPABASE_URL` - Production Supabase URL
- [ ] `NEXT_PUBLIC_SUPABASE_ANON_KEY` - Production Supabase key
- [ ] `NODE_ENV=production`
- [ ] `NEXT_PUBLIC_DEBUG_MODE=false`
- [ ] `NEXT_PUBLIC_ENABLE_DEBUG_LOGGING=false`

#### Backend Environment Variables
- [ ] `FRONTEND_URL` - All allowed frontend origins
- [ ] `DATABASE_URL` - Production database connection
- [ ] `SUPABASE_URL` - Production Supabase URL
- [ ] `SUPABASE_SERVICE_ROLE_KEY` - Production service role key
- [ ] `NODE_ENV=production`
- [ ] `LOG_LEVEL=info`
- [ ] `JWT_SECRET` - Secure random secret
- [ ] `API_SECRET` - Secure random secret
- [ ] `SESSION_SECRET` - Secure random secret

### Security Configuration

#### SSL/TLS
- [ ] SSL certificates installed and valid
- [ ] HTTPS enforced for all connections
- [ ] WSS (WebSocket Secure) configured
- [ ] HTTP to HTTPS redirects in place

#### CORS Configuration
- [ ] Production frontend domains in `FRONTEND_URL`
- [ ] No wildcard (*) origins in production
- [ ] Specific allowed headers configured
- [ ] Credentials enabled for authenticated requests

#### Authentication Security
- [ ] Secure JWT secret (256-bit minimum)
- [ ] Token expiration times appropriate
- [ ] Refresh token rotation enabled
- [ ] Session timeout configured

### Infrastructure

#### Database
- [ ] Production database provisioned
- [ ] Database migrations applied
- [ ] Database backups configured
- [ ] Connection pooling configured
- [ ] Database monitoring enabled

#### Monitoring & Logging
- [ ] Application logging configured
- [ ] Error tracking enabled (Sentry, etc.)
- [ ] Performance monitoring enabled
- [ ] Health check endpoints working
- [ ] Uptime monitoring configured

## 🚀 Deployment Steps

### 1. Environment Setup

```bash
# Frontend production build
cd frontend
cp .env.production .env.local
npm run build

# Backend production setup
cd backend
# Ensure production .env file is configured
npm run build
```

### 2. Security Verification

```bash
# Test CORS configuration
curl -H "Origin: https://your-frontend-domain.com" \
     -H "Access-Control-Request-Method: POST" \
     -H "Access-Control-Request-Headers: Content-Type,Authorization" \
     -X OPTIONS \
     https://your-backend-domain.com/api/health

# Test authentication endpoint
curl -X POST https://your-backend-domain.com/api/auth/login \
     -H "Content-Type: application/json" \
     -d '{"email":"<EMAIL>","password":"test"}'
```

### 3. WebSocket Testing

```javascript
// Test WebSocket connection in browser console
const ws = new WebSocket('wss://your-backend-domain.com');
ws.onopen = () => console.log('WebSocket connected');
ws.onerror = (error) => console.error('WebSocket error:', error);
```

## 🔧 Environment-Specific Configurations

### Docker Deployment

```dockerfile
# Frontend Dockerfile
ARG NEXT_PUBLIC_API_BASE_URL
ARG NEXT_PUBLIC_WS_URL
ENV NEXT_PUBLIC_API_BASE_URL=$NEXT_PUBLIC_API_BASE_URL
ENV NEXT_PUBLIC_WS_URL=$NEXT_PUBLIC_WS_URL
```

### Cloud Platform Deployment

#### Vercel/Netlify
- [ ] Environment variables configured in platform dashboard
- [ ] Build commands updated for production
- [ ] Domain configuration completed
- [ ] SSL certificates auto-provisioned

#### AWS/GCP/Azure
- [ ] Load balancer configured
- [ ] Auto-scaling policies set
- [ ] Database connections secured
- [ ] CDN configured for static assets

## 🔍 Post-Deployment Validation

### Functional Testing
- [ ] Application loads successfully
- [ ] Authentication flow works end-to-end
- [ ] WebSocket connections establish properly
- [ ] Real-time features function correctly
- [ ] API endpoints respond correctly
- [ ] Database operations work properly

### Performance Testing
- [ ] Page load times acceptable (<3s)
- [ ] API response times acceptable (<500ms)
- [ ] WebSocket connection time acceptable (<1s)
- [ ] Database query performance optimized

### Security Testing
- [ ] HTTPS enforced everywhere
- [ ] No sensitive data in client-side code
- [ ] Authentication tokens secure
- [ ] CORS policies restrictive
- [ ] No debug information exposed

## 🚨 Rollback Plan

### Immediate Rollback
1. Revert to previous deployment
2. Restore previous environment variables
3. Verify functionality
4. Investigate issues

### Database Rollback
1. Stop application
2. Restore database backup
3. Revert migrations if necessary
4. Restart with previous version

## 📊 Monitoring & Alerts

### Key Metrics
- [ ] Application uptime (>99.9%)
- [ ] Response time (<500ms average)
- [ ] Error rate (<1%)
- [ ] WebSocket connection success rate (>95%)
- [ ] Authentication success rate (>98%)

### Alert Thresholds
- [ ] High error rate (>5% for 5 minutes)
- [ ] Slow response time (>2s for 5 minutes)
- [ ] Database connection failures
- [ ] Authentication service failures
- [ ] WebSocket connection failures

## 🔄 Maintenance

### Regular Tasks
- [ ] Security updates applied monthly
- [ ] Database maintenance scheduled
- [ ] Log rotation configured
- [ ] Backup verification weekly
- [ ] Performance review monthly

### Emergency Procedures
- [ ] Incident response plan documented
- [ ] Emergency contacts list updated
- [ ] Rollback procedures tested
- [ ] Communication plan established
