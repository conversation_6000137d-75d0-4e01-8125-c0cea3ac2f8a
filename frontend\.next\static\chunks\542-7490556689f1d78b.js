"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[542],{6560:(e,t,s)=>{s.d(t,{r:()=>o});var a=s(95155),r=s(50172),i=s(12115),l=s(30285),n=s(54036);let o=i.forwardRef((e,t)=>{let{actionType:s="primary",asChild:i=!1,children:o,className:d,disabled:c,icon:m,isLoading:u=!1,loadingText:g,...h}=e,{className:x,variant:f}={danger:{className:"shadow-md",variant:"destructive"},primary:{className:"shadow-md",variant:"default"},secondary:{className:"",variant:"secondary"},tertiary:{className:"",variant:"outline"}}[s];return(0,a.jsx)(l.$,{asChild:i,className:(0,n.cn)(x,d),disabled:u||c,ref:t,variant:f,...h,children:u?(0,a.jsxs)("span",{className:"inline-flex items-center",children:[" ",(0,a.jsx)(r.A,{className:"mr-2 size-4 animate-spin"}),g||o]}):(0,a.jsxs)("span",{className:"inline-flex items-center",children:[" ",m&&(0,a.jsx)("span",{className:"mr-2",children:m}),o]})})});o.displayName="ActionButton"},14636:(e,t,s)=>{s.d(t,{AM:()=>n,Wv:()=>o,hl:()=>d});var a=s(95155),r=s(20547),i=s(12115),l=s(54036);let n=r.bL,o=r.l9;r.bm;let d=i.forwardRef((e,t)=>{let{align:s="center",className:i,sideOffset:n=4,...o}=e;return(0,a.jsx)(r.ZL,{children:(0,a.jsx)(r.UC,{align:s,className:(0,l.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-popover-content-transform-origin]",i),ref:t,sideOffset:n,...o})})});d.displayName=r.UC.displayName},17759:(e,t,s)=>{s.d(t,{C5:()=>b,MJ:()=>f,Rr:()=>p,eI:()=>h,lR:()=>x,lV:()=>d,zB:()=>m});var a=s(95155),r=s(12115),i=s(99708),l=s(62177),n=s(54036),o=s(85057);let d=l.Op,c=r.createContext({}),m=e=>{let{...t}=e;return(0,a.jsx)(c.Provider,{value:{name:t.name},children:(0,a.jsx)(l.xI,{...t})})},u=()=>{let e=r.useContext(c),t=r.useContext(g),{getFieldState:s,formState:a}=(0,l.xW)(),i=s(e.name,a);if(!e)throw Error("useFormField should be used within <FormField>");let{id:n}=t;return{id:n,name:e.name,formItemId:"".concat(n,"-form-item"),formDescriptionId:"".concat(n,"-form-item-description"),formMessageId:"".concat(n,"-form-item-message"),...i}},g=r.createContext({}),h=r.forwardRef((e,t)=>{let{className:s,...i}=e,l=r.useId();return(0,a.jsx)(g.Provider,{value:{id:l},children:(0,a.jsx)("div",{ref:t,className:(0,n.cn)("space-y-2",s),...i})})});h.displayName="FormItem";let x=r.forwardRef((e,t)=>{let{className:s,...r}=e,{error:i,formItemId:l}=u();return(0,a.jsx)(o.J,{ref:t,className:(0,n.cn)(i&&"text-destructive",s),htmlFor:l,...r})});x.displayName="FormLabel";let f=r.forwardRef((e,t)=>{let{...s}=e,{error:r,formItemId:l,formDescriptionId:n,formMessageId:o}=u();return(0,a.jsx)(i.DX,{ref:t,id:l,"aria-describedby":r?"".concat(n," ").concat(o):"".concat(n),"aria-invalid":!!r,...s})});f.displayName="FormControl";let p=r.forwardRef((e,t)=>{let{className:s,...r}=e,{formDescriptionId:i}=u();return(0,a.jsx)("p",{ref:t,id:i,className:(0,n.cn)("text-sm text-muted-foreground",s),...r})});p.displayName="FormDescription";let b=r.forwardRef((e,t)=>{var s;let{className:r,children:i,...l}=e,{error:o,formMessageId:d}=u(),c=o?String(null!=(s=null==o?void 0:o.message)?s:""):i;return c?(0,a.jsx)("p",{ref:t,id:d,className:(0,n.cn)("text-sm font-medium text-destructive",r),...l,children:c}):null});b.displayName="FormMessage"},20317:(e,t,s)=>{s.d(t,{P:()=>r});var a=s(83940);class r{static showSuccessToast(e,t,s){if(!e.showSuccessToast)return;let{entityType:r,entity:i,successMessage:l}=e;try{switch(r){case"employee":i?a.Ok.entityCreated(i):a.JP.success("Employee Created",l);break;case"vehicle":i?a.G7.entityCreated(i):a.JP.success("Vehicle Added",l);break;case"task":i?a.z0.entityCreated(i):a.JP.success("Task Created",l);break;case"delegation":i?a.Qu.entityCreated(i):a.JP.success("Delegation Created",l);break;case"serviceRecord":i&&s?a.oz.serviceRecordCreated(i.vehicleName||"Vehicle",i.serviceType||"Service"):a.JP.success("Service Record Added",l);break;default:a.JP.success("Success",l||"Operation completed successfully")}}catch(e){a.JP.success("Success",l||"Operation completed successfully")}}static showErrorToast(e,t,s){if(!e.showErrorToast)return;let{entityType:r,errorMessage:i}=e,l=t.message||i||"An unexpected error occurred";try{switch(r){case"employee":a.Ok.entityCreationError(l);break;case"vehicle":a.G7.entityCreationError(l);break;case"task":a.z0.entityCreationError(l);break;case"delegation":a.Qu.entityCreationError(l);break;case"serviceRecord":a.oz.serviceRecordCreationError(l);break;default:a.JP.error("Error",l)}}catch(e){a.JP.error("Error",l)}}static showUpdateSuccessToast(e,t,s){if(!e.showSuccessToast)return;let{entityType:r,entity:i,successMessage:l}=e;try{switch(r){case"employee":i?a.Ok.entityUpdated(i):a.JP.success("Employee Updated",l);break;case"vehicle":i?a.G7.entityUpdated(i):a.JP.success("Vehicle Updated",l);break;case"task":i?a.z0.entityUpdated(i):a.JP.success("Task Updated",l);break;case"delegation":i?a.Qu.entityUpdated(i):a.JP.success("Delegation Updated",l);break;case"serviceRecord":i&&s?a.oz.serviceRecordUpdated(i.vehicleName||"Vehicle",i.serviceType||"Service"):a.JP.success("Service Record Updated",l);break;default:a.JP.success("Success",l||"Update completed successfully")}}catch(e){a.JP.success("Success",l||"Update completed successfully")}}static showUpdateErrorToast(e,t,s){if(!e.showErrorToast)return;let{entityType:r,errorMessage:i}=e,l=t.message||i||"An unexpected error occurred";try{switch(r){case"employee":a.Ok.entityUpdateError(l);break;case"vehicle":a.G7.entityUpdateError(l);break;case"task":a.z0.entityUpdateError(l);break;case"delegation":a.Qu.entityUpdateError(l);break;case"serviceRecord":a.oz.serviceRecordUpdateError(l);break;default:a.JP.error("Update Failed",l)}}catch(e){a.JP.error("Update Failed",l)}}static createCustomEntityToastService(e,t){return(0,a.Gb)(e,t)}}},24944:(e,t,s)=>{s.d(t,{k:()=>n});var a=s(95155),r=s(55863),i=s(12115),l=s(54036);let n=i.forwardRef((e,t)=>{let{className:s,value:i,...n}=e;return(0,a.jsx)(r.bL,{className:(0,l.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",s),ref:t,...n,children:(0,a.jsx)(r.C1,{className:"size-full flex-1 bg-primary transition-all",style:{transform:"translateX(-".concat(100-(i||0),"%)")}})})});n.displayName=r.bL.displayName},26126:(e,t,s)=>{s.d(t,{E:()=>n});var a=s(95155),r=s(74466);s(12115);var i=s(54036);let l=(0,r.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{defaultVariants:{variant:"default"},variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80"}}});function n(e){let{className:t,variant:s,...r}=e;return(0,a.jsx)("div",{className:(0,i.cn)(l({variant:s}),t),...r})}},39097:(e,t,s)=>{s.d(t,{k:()=>d});var a=s(12115),r=s(94141),i=s(20317);class l{announceStatus(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"polite";if(!this.config.announceStatus||!this.config.screenReaderAnnouncements)return;let s=document.getElementById("form-submission-announcements");s||((s=document.createElement("div")).id="form-submission-announcements",s.setAttribute("aria-live",t),s.setAttribute("aria-atomic","true"),s.className="sr-only absolute left-[-10000px] top-[-10000px] w-[1px] h-[1px] overflow-hidden",document.body.appendChild(s)),s.textContent=e,setTimeout(()=>{s&&s.textContent===e&&(s.textContent="")},1e3)}generateAriaAttributes(e,t,s){return{"aria-busy":e,"aria-invalid":t,"aria-describedby":this.config.errorDescribedBy||(t?"form-error":void 0),"aria-live":"submitting"===s||"validating"===s?"polite":"off"}}manageFocus(e,t){if("none"!==this.config.focusManagement)switch(e){case"error":"first-error"===this.config.focusManagement&&t&&t("first-error");break;case"success":if("success-message"===this.config.focusManagement){let e=document.getElementById("form-success-message");e&&e.focus()}else"next-field"===this.config.focusManagement&&t&&t("next-field");break;case"retry":t&&t("retry-button")}}createErrorMessage(e){let t=document.createElement("div");return t.id=this.config.errorDescribedBy||"form-error",t.setAttribute("role","alert"),t.setAttribute("aria-live","assertive"),t.className="sr-only",t.textContent=e,t}updateErrorMessage(e){let t=this.config.errorDescribedBy||"form-error",s=document.getElementById(t);e?s?s.textContent=e:(s=this.createErrorMessage(e),document.body.appendChild(s)):s&&s.remove()}getStatusMessage(e,t,s){switch(e){case"validating":return"Validating form data...";case"submitting":return"Submitting form...";case"retrying":return"Retrying submission... (Attempt ".concat(t||1,"/").concat(s||3,")");case"success":return"Form submitted successfully";case"error":return"Form submission failed";default:return""}}setupKeyboardNavigation(){let e=e=>{if("Escape"===e.key){let e=document.querySelector("[data-form-cancel]");e&&e.click()}if((e.ctrlKey||e.metaKey)&&"Enter"===e.key){let e=document.querySelector('[type="submit"]');e&&!e.disabled&&e.click()}};return document.addEventListener("keydown",e),()=>{document.removeEventListener("keydown",e)}}announceProgress(e,t,s){if(!this.config.screenReaderAnnouncements)return;let a="Step ".concat(e," of ").concat(t,": ").concat(s);this.announceStatus(a,"polite")}cleanup(){let e=document.getElementById("form-submission-announcements");e&&e.remove(),this.updateErrorMessage(null)}constructor(e){this.config=e}}class n{shouldRetry(e){return this.currentAttempt<this.config.maxAttempts&&(!this.config.retryCondition||this.config.retryCondition(e))}getRetryDelay(){let e=this.config.delay;return this.config.exponentialBackoff?e*Math.pow(2,this.currentAttempt):e}incrementAttempt(){return this.currentAttempt+=1,this.currentAttempt}resetAttempts(){this.currentAttempt=0}getCurrentAttempt(){return this.currentAttempt}getMaxAttempts(){return this.config.maxAttempts}async sleep(e){return new Promise(t=>setTimeout(t,e))}async executeRetry(e){if(!this.shouldRetry(Error("Manual retry")))throw Error("Maximum retry attempts exceeded");let t=this.getRetryDelay();return this.incrementAttempt(),await this.sleep(t),e()}getRetryStatus(){return{currentAttempt:this.currentAttempt,maxAttempts:this.config.maxAttempts,hasRetriesLeft:this.currentAttempt<this.config.maxAttempts,nextDelay:this.getRetryDelay()}}withConfig(e){return new n({...this.config,...e})}constructor(e){this.currentAttempt=0,this.config=e}}class o{startTiming(){this.submissionStartTime=Date.now()}endTiming(e){if(!this.submissionStartTime)return 0;let t=Date.now()-this.submissionStartTime;return this.updateMetrics(e,t),this.submissionStartTime=null,t}updateMetrics(e,t){let s=this.metrics.totalSubmissions+1,a=e?this.metrics.successfulSubmissions+1:this.metrics.successfulSubmissions,r=e?this.metrics.failedSubmissions:this.metrics.failedSubmissions+1,i=this.metrics.averageDuration*this.metrics.totalSubmissions+t;this.metrics={totalSubmissions:s,successfulSubmissions:a,failedSubmissions:r,averageDuration:i/s}}getMetrics(){return{...this.metrics}}resetMetrics(){this.metrics={totalSubmissions:0,successfulSubmissions:0,failedSubmissions:0,averageDuration:0}}debounce(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.config.debounceMs;var s=this;return function(){for(var a=arguments.length,r=Array(a),i=0;i<a;i++)r[i]=arguments[i];s.debounceTimer&&clearTimeout(s.debounceTimer),s.debounceTimer=setTimeout(()=>{e(...r)},t)}}clearDebounce(){this.debounceTimer&&(clearTimeout(this.debounceTimer),this.debounceTimer=null)}createTimeoutPromise(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.config.timeoutMs;return new Promise((t,s)=>{setTimeout(()=>{s(Error("Request timeout after ".concat(e,"ms")))},e)})}async withTimeout(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.config.timeoutMs;return Promise.race([e,this.createTimeoutPromise(t)])}getSuccessRate(){return 0===this.metrics.totalSubmissions?0:this.metrics.successfulSubmissions/this.metrics.totalSubmissions*100}getFailureRate(){return 0===this.metrics.totalSubmissions?0:this.metrics.failedSubmissions/this.metrics.totalSubmissions*100}isPerformanceAcceptable(e){let t={maxAverageDuration:5e3,minSuccessRate:95,...e};return this.metrics.averageDuration<=t.maxAverageDuration&&this.getSuccessRate()>=t.minSuccessRate}generateReport(){let e=this.getSuccessRate(),t=this.getFailureRate(),s=this.isPerformanceAcceptable(),a=[];return this.metrics.averageDuration>3e3&&a.push("Consider optimizing form validation or submission logic"),e<90&&a.push("High failure rate detected - review error handling"),this.metrics.totalSubmissions>100&&this.metrics.averageDuration>1e3&&a.push("Consider implementing caching for better performance"),{metrics:this.getMetrics(),successRate:e,failureRate:t,isAcceptable:s,recommendations:a}}cleanup(){this.clearDebounce(),this.submissionStartTime=null}constructor(e){this.submissionStartTime=null,this.debounceTimer=null,this.config=e,this.metrics={totalSubmissions:0,successfulSubmissions:0,failedSubmissions:0,averageDuration:0}}}let d=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s=r.SY.mergeRetryConfig(t.retry),d=r.SY.mergeAccessibilityConfig(t.accessibility),c=r.SY.mergePerformanceConfig(t.performance),m=r.SY.mergeToastConfig(t.toast),u=(0,a.useRef)(new l(d)).current,g=(0,a.useRef)(new n(s)).current,h=(0,a.useRef)(new o(c)).current,[x,f]=(0,a.useState)("idle"),[p,b]=(0,a.useState)(null),[v,j]=(0,a.useState)(null),[N,y]=(0,a.useState)(null),[w,A]=(0,a.useState)(null),[D,E]=(0,a.useState)(null),[S,C]=(0,a.useState)(null),T=(0,a.useRef)(null),k="submitting"===x||"validating"===x,I="success"===x,R="validating"===x,F="retrying"===x,z=g.getCurrentAttempt();(0,a.useEffect)(()=>()=>{T.current&&T.current.abort(),h.cleanup(),u.cleanup()},[h,u]);let P=(0,a.useCallback)(()=>{b(null),j(null),u.updateErrorMessage(null),"error"===x&&f("idle")},[x,u]),V=(0,a.useCallback)(()=>{f("idle"),b(null),j(null),y(null),A(null),E(null),C(null),g.resetAttempts(),h.resetMetrics(),u.updateErrorMessage(null)},[g,h,u]),M=(0,a.useCallback)(()=>{T.current&&T.current.abort(),f("idle"),u.announceStatus("Form submission cancelled")},[u]),q=(0,a.useCallback)(async function(a){let r=arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{h.startTiming(),T.current=new AbortController;let l=r?"retrying":"submitting";f(l);let n=u.getStatusMessage(l,z,s.maxAttempts);if(u.announceStatus(n),t.onSubmitStart&&await t.onSubmitStart(a),t.preSubmitValidation&&(f("validating"),u.announceStatus("Validating form data..."),!await t.preSubmitValidation(a)))throw Error("Validation failed");f(l);let o=a;t.transformData&&(o=await t.transformData(a));let d=e(o),c=await h.withTimeout(d),x=c;if(t.transformResult&&(x=await t.transformResult(c)),t.postSubmitValidation&&!await t.postSubmitValidation(x))throw Error("Post-submission validation failed");let p=h.endTiming(!0);f("success"),E(x),A(Date.now()),y(a),C(p),g.resetAttempts(),i.P.showSuccessToast(m,a,x),u.announceStatus("Form submitted successfully","assertive"),u.manageFocus("success",t.formFocus),t.resetOnSuccess&&t.formReset&&t.formReset(),t.onSuccess&&await t.onSuccess(a,x),t.onSubmitComplete&&await t.onSubmitComplete(a,!0)}catch(o){let e=o instanceof Error?o:Error(String(o)),l=h.endTiming(!1);if(!r&&g.shouldRetry(e)){f("retrying");let e=g.getRetryDelay();return g.incrementAttempt(),u.announceStatus("Retrying in ".concat(e,"ms... (Attempt ").concat(g.getCurrentAttempt(),"/").concat(s.maxAttempts,")")),await g.sleep(e),q(a,!0)}f("error");let n=e.message||m.errorMessage||"An unexpected error occurred";b(n),j(e),C(l),i.P.showErrorToast(m,e,a),u.updateErrorMessage(n),u.announceStatus("Error: ".concat(n),"assertive"),u.manageFocus("error",t.formFocus),t.onError&&await t.onError(e,a),t.onSubmitComplete&&await t.onSubmitComplete(a,!1)}},[e,t,g,h,u,m,s.maxAttempts,z]),U=(0,a.useCallback)(async(e,t)=>{t&&t.preventDefault(),h.debounce(()=>q(e),c.debounceMs)()},[q,h,c.debounceMs]),L=(0,a.useCallback)(async()=>{N&&(g.resetAttempts(),await q(N))},[N,q,g]),_=u.generateAriaAttributes(k,!!p,x);return{isLoading:k,state:x,error:p,errorObject:v,isSuccess:I,isValidating:R,isRetrying:F,lastSubmittedData:N,lastSubmitted:w,lastResult:D,retryAttempt:z,handleSubmit:U,clearError:P,reset:V,retry:L,cancel:M,ariaAttributes:_,submissionDuration:S,metrics:h.getMetrics()}}},54165:(e,t,s)=>{s.d(t,{Cf:()=>u,Es:()=>h,L3:()=>x,c7:()=>g,lG:()=>o,rr:()=>f,zM:()=>d});var a=s(95155),r=s(15452),i=s(25318),l=s(12115),n=s(54036);let o=r.bL,d=r.l9,c=r.ZL;r.bm;let m=l.forwardRef((e,t)=>{let{className:s,...i}=e;return(0,a.jsx)(r.hJ,{className:(0,n.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",s),ref:t,...i})});m.displayName=r.hJ.displayName;let u=l.forwardRef((e,t)=>{let{children:s,className:l,...o}=e;return(0,a.jsxs)(c,{children:[(0,a.jsx)(m,{}),(0,a.jsxs)(r.UC,{className:(0,n.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",l),ref:t,...o,children:[s,(0,a.jsxs)(r.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,a.jsx)(i.A,{className:"size-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});u.displayName=r.UC.displayName;let g=e=>{let{className:t,...s}=e;return(0,a.jsx)("div",{className:(0,n.cn)("flex flex-col space-y-1.5 text-center sm:text-left",t),...s})};g.displayName="DialogHeader";let h=e=>{let{className:t,...s}=e;return(0,a.jsx)("div",{className:(0,n.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",t),...s})};h.displayName="DialogFooter";let x=l.forwardRef((e,t)=>{let{className:s,...i}=e;return(0,a.jsx)(r.hE,{className:(0,n.cn)("text-lg font-semibold leading-none tracking-tight",s),ref:t,...i})});x.displayName=r.hE.displayName;let f=l.forwardRef((e,t)=>{let{className:s,...i}=e;return(0,a.jsx)(r.VY,{className:(0,n.cn)("text-sm text-muted-foreground",s),ref:t,...i})});f.displayName=r.VY.displayName},55365:(e,t,s)=>{s.d(t,{Fc:()=>o,TN:()=>c,XL:()=>d});var a=s(95155),r=s(74466),i=s(12115),l=s(54036);let n=(0,r.F)("relative w-full rounded-lg border p-4 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground [&>svg~*]:pl-7",{defaultVariants:{variant:"default"},variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}}}),o=i.forwardRef((e,t)=>{let{className:s,variant:r,...i}=e;return(0,a.jsx)("div",{className:(0,l.cn)(n({variant:r}),s),ref:t,role:"alert",...i})});o.displayName="Alert";let d=i.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("h5",{className:(0,l.cn)("mb-1 font-medium leading-none tracking-tight",s),ref:t,...r})});d.displayName="AlertTitle";let c=i.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{className:(0,l.cn)("text-sm [&_p]:leading-relaxed",s),ref:t,...r})});c.displayName="AlertDescription"},59409:(e,t,s)=>{s.d(t,{bq:()=>u,eb:()=>f,gC:()=>x,l6:()=>c,yv:()=>m});var a=s(95155),r=s(31992),i=s(79556),l=s(77381),n=s(10518),o=s(12115),d=s(54036);let c=r.bL;r.YJ;let m=r.WT,u=o.forwardRef((e,t)=>{let{children:s,className:l,...n}=e;return(0,a.jsxs)(r.l9,{className:(0,d.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",l),ref:t,...n,children:[s,(0,a.jsx)(r.In,{asChild:!0,children:(0,a.jsx)(i.A,{className:"size-4 opacity-50"})})]})});u.displayName=r.l9.displayName;let g=o.forwardRef((e,t)=>{let{className:s,...i}=e;return(0,a.jsx)(r.PP,{className:(0,d.cn)("flex cursor-default items-center justify-center py-1",s),ref:t,...i,children:(0,a.jsx)(l.A,{className:"size-4"})})});g.displayName=r.PP.displayName;let h=o.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,a.jsx)(r.wn,{className:(0,d.cn)("flex cursor-default items-center justify-center py-1",s),ref:t,...l,children:(0,a.jsx)(i.A,{className:"size-4"})})});h.displayName=r.wn.displayName;let x=o.forwardRef((e,t)=>{let{children:s,className:i,position:l="popper",...n}=e;return(0,a.jsx)(r.ZL,{children:(0,a.jsxs)(r.UC,{className:(0,d.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===l&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",i),position:l,ref:t,...n,children:[(0,a.jsx)(g,{}),(0,a.jsx)(r.LM,{className:(0,d.cn)("p-1","popper"===l&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:s}),(0,a.jsx)(h,{})]})})});x.displayName=r.UC.displayName,o.forwardRef((e,t)=>{let{className:s,...i}=e;return(0,a.jsx)(r.JU,{className:(0,d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",s),ref:t,...i})}).displayName=r.JU.displayName;let f=o.memo(o.forwardRef((e,t)=>{let{children:s,className:i,...l}=e,c=o.useCallback(e=>{"function"==typeof t?t(e):t&&(t.current=e)},[t]);return(0,a.jsxs)(r.q7,{className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",i),ref:c,...l,children:[(0,a.jsx)("span",{className:"absolute left-2 flex size-3.5 items-center justify-center",children:(0,a.jsx)(r.VF,{children:(0,a.jsx)(n.A,{className:"size-4"})})}),(0,a.jsx)(r.p4,{children:s})]})}));f.displayName=r.q7.displayName,o.forwardRef((e,t)=>{let{className:s,...i}=e;return(0,a.jsx)(r.wv,{className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",s),ref:t,...i})}).displayName=r.wv.displayName},62523:(e,t,s)=>{s.d(t,{p:()=>l});var a=s(95155),r=s(12115),i=s(54036);let l=r.forwardRef((e,t)=>{let{className:s,type:r,...l}=e;return(0,a.jsx)("input",{className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",s),ref:t,type:r,...l})});l.displayName="Input"},66424:(e,t,s)=>{s.d(t,{F:()=>n});var a=s(95155),r=s(47655),i=s(12115),l=s(54036);let n=i.forwardRef((e,t)=>{let{children:s,className:i,...n}=e;return(0,a.jsxs)(r.bL,{className:(0,l.cn)("relative overflow-hidden",i),ref:t,...n,children:[(0,a.jsx)(r.LM,{className:"size-full rounded-[inherit]",children:s}),(0,a.jsx)(o,{}),(0,a.jsx)(r.OK,{})]})});n.displayName=r.bL.displayName;let o=i.forwardRef((e,t)=>{let{className:s,orientation:i="vertical",...n}=e;return(0,a.jsx)(r.VM,{className:(0,l.cn)("flex touch-none select-none transition-colors","vertical"===i&&"h-full w-2.5 border-l border-l-transparent p-[1px]","horizontal"===i&&"h-2.5 flex-col border-t border-t-transparent p-[1px]",s),orientation:i,ref:t,...n,children:(0,a.jsx)(r.lr,{className:"relative flex-1 rounded-full bg-border"})})});o.displayName=r.VM.displayName},67428:(e,t,s)=>{s.d(t,{GK:()=>eF});var a=s(95155),r=s(11518),i=s.n(r),l=s(90221),n=s(51920),o=s(50286),d=s(83662),c=s(50594),m=s(8376),u=s(11133),g=s(12543),h=s(59119),x=s(35695),f=s(12115),p=s(62177),b=s(55365),v=s(26126),j=s(30285),N=s(66695),y=s(25318),w=s(31949);let A=[{dependsOn:["driverEmployeeIds"],field:"vehicleIds",validate:e=>{let t=e.vehicleIds&&e.vehicleIds.length>0,s=e.driverEmployeeIds&&e.driverEmployeeIds.length>0;return t&&!s?{isValid:!1,message:"Vehicles cannot be assigned without at least one driver",type:"error"}:{isValid:!0,type:"error"}}},{dependsOn:["durationFrom"],field:"durationTo",validate:e=>{if(e.durationFrom&&e.durationTo){let t=new Date(e.durationFrom),s=new Date(e.durationTo);if(t>s)return{isValid:!1,message:"End date cannot be earlier than start date",type:"error"};if((s.getTime()-t.getTime())/864e5>30)return{isValid:!0,message:"Delegation duration exceeds 30 days. Please verify this is correct.",type:"warning"}}return{isValid:!0,type:"error"}}},{dependsOn:["delegates"],field:"delegates",validate:e=>e.delegates&&e.delegates.length>5?{isValid:!0,message:"Large number of delegates may require additional coordination",type:"warning"}:{isValid:!0,type:"warning"}}],D=()=>{let{formState:{dirtyFields:e,errors:t,touchedFields:s},trigger:a,watch:r}=(0,p.xW)(),i=r(),l=(0,f.useMemo)(()=>{let e=Object.keys(t).length,s=A.filter(e=>!e.validate(i).isValid).length,a=A.filter(e=>{let t=e.validate(i);return t.isValid&&"warning"===t.type&&t.message}).length;return{errorCount:e+s,hasErrors:e>0||s>0,hasWarnings:a>0,isValid:0===e&&0===s,warningCount:a}},[t,i]),n=(0,f.useCallback)(a=>{var r,l,n,o,d;let c=t[a],m=null!=(r=s[a])&&r,u=null!=(l=e[a])&&l,g=A.find(e=>e.field===a),h=null;return g&&(h=g.validate(i)),{errorMessage:null!=(o=null!=(n=null==c?void 0:c.message)?n:h&&!h.isValid?h.message:"")?o:"",hasError:!!(c||h&&!h.isValid&&"error"===h.type),hasWarning:!!(h&&h.isValid&&"warning"===h.type&&h.message),isDirty:!!u,isRequired:["eventName","location","durationFrom","durationTo","delegates"].includes(a),isTouched:!!m,isValid:!c&&(!h||h.isValid),warningMessage:null!=(d=h&&h.isValid&&"warning"===h.type?h.message:"")?d:""}},[t,s,e,i]),o=(0,f.useCallback)(e=>{let t=function(e){var t;return null!=(t=({assignment:["driverEmployeeIds","escortEmployeeIds"],basic:["eventName","location","durationFrom","durationTo","status"],delegates:["delegates"],logistics:["vehicleIds","flightArrivalDetails","flightDepartureDetails"],notes:["notes","invitationFrom","invitationTo","imageUrl"]})[e])?t:[]}(e),s=t.filter(e=>n(e).hasError),a=t.filter(e=>n(e).hasWarning),r=t.filter(e=>{let t=i[e];return function(e,t){return"delegates"===e?Array.isArray(t)&&t.length>0&&t.every(e=>e.name&&e.title):Array.isArray(t)?t.length>0:"string"==typeof t?t.trim().length>0:null!=t}(e,t)});return{completionPercentage:t.length>0?Math.round(r.length/t.length*100):0,errorCount:s.length,hasErrors:s.length>0,hasWarnings:a.length>0,isComplete:function(e,t){var s,a,r,i,l,n,o,d,c,m,u,g;switch(e){case"assignment":return!!(null!=(s=t.driverEmployeeIds&&t.driverEmployeeIds.length>0)?s:t.escortEmployeeIds&&t.escortEmployeeIds.length>0);case"basic":return!!(t.eventName&&t.location&&t.durationFrom&&t.durationTo);case"delegates":return!!(t.delegates&&t.delegates.length>0&&t.delegates.every(e=>e.name&&e.title));case"logistics":return!!(null!=(l=null!=(i=t.vehicleIds&&t.vehicleIds.length>0)?i:null==(a=t.flightArrivalDetails)?void 0:a.flightNumber)?l:null==(r=t.flightDepartureDetails)?void 0:r.flightNumber);case"notes":return!!(null!=(g=null!=(u=null!=(m=null==(n=t.notes)?void 0:n.trim())?m:null==(o=t.invitationFrom)?void 0:o.trim())?u:null==(d=t.invitationTo)?void 0:d.trim())?g:null==(c=t.imageUrl)?void 0:c.trim());default:return!1}}(e,i),isValid:0===s.length,warningCount:a.length}},[n,i]),d=(0,f.useCallback)(async e=>await a(e),[a]),c=(0,f.useCallback)(async()=>await a(),[a]);return{getCrossFieldErrors:(0,f.useCallback)(()=>A.filter(e=>!e.validate(i).isValid).map(e=>{var t;return{field:e.field,message:null!=(t=e.validate(i).message)?t:"Validation error"}}),[i]),getCrossFieldWarnings:(0,f.useCallback)(()=>A.filter(e=>{let t=e.validate(i);return t.isValid&&"warning"===t.type&&t.message}).map(e=>{var t;return{field:e.field,message:null!=(t=e.validate(i).message)?t:"Validation warning"}}),[i]),getFieldValidationState:n,getSectionValidationState:o,validateField:d,validateForm:c,validationState:l}};var E=s(54036);function S(e){return"object"==typeof e&&null!==e&&"isValid"in e&&"boolean"==typeof e.isValid&&"hasErrors"in e&&"boolean"==typeof e.hasErrors&&"hasWarnings"in e&&"boolean"==typeof e.hasWarnings}let C=e=>{var t;return null!=(t=({delegates:"Delegates",driverEmployeeIds:"Drivers",durationFrom:"Start Date",durationTo:"End Date",escortEmployeeIds:"Escorts",eventName:"Event Name","flightArrivalDetails.flightNumber":"Arrival Flight Number","flightDepartureDetails.flightNumber":"Departure Flight Number",imageUrl:"Image URL",invitationFrom:"Invitation From",invitationTo:"Invitation To",location:"Location",notes:"Notes",vehicleIds:"Vehicles"})[e])?t:e.replaceAll(/([A-Z])/g," $1").replace(/^./,e=>e.toUpperCase())},T=e=>{let{className:t="",onDismiss:s,show:r,showFieldNavigation:i=!0,showSuccess:l=!1,showWarnings:n=!0}=e,{formState:{errors:o},setFocus:d}=(0,p.xW)(),{getCrossFieldErrors:c,getCrossFieldWarnings:g,validationState:h}=D();if(!r)return null;let x=c(),f=n?g():[],v=Object.entries(o),A=S(h)&&h.hasErrors||v.length>0,T=S(h)&&h.hasWarnings&&n,k=S(h)&&h.isValid&&0===v.length,I=e=>{d(e),s&&s()};return k&&l?(0,a.jsxs)(N.Zp,{className:(0,E.cn)("border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20",t),children:[(0,a.jsx)(N.aR,{className:"pb-3",children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2 text-green-700 dark:text-green-400",children:[(0,a.jsx)(m.A,{className:"size-5"}),"Form Validation Successful",s&&(0,a.jsxs)(j.$,{className:"ml-auto size-6 p-0 text-green-700 hover:text-green-800 dark:text-green-400",onClick:s,size:"sm",variant:"ghost",children:[(0,a.jsx)(y.A,{className:"size-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Dismiss"})]})]})}),(0,a.jsx)(N.Wu,{children:(0,a.jsx)("p",{className:"text-sm text-green-600 dark:text-green-300",children:"All required fields are completed and valid. You can now submit the delegation form."})})]}):A||T?(0,a.jsxs)(b.Fc,{className:(0,E.cn)(A?"border-destructive/50 text-destructive dark:border-destructive":"border-amber-200 bg-amber-50 text-amber-800 dark:border-amber-800 dark:bg-amber-900/20 dark:text-amber-200",t),variant:A?"destructive":"default",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[A?(0,a.jsx)(u.A,{className:"size-4"}):(0,a.jsx)(w.A,{className:"size-4"}),(0,a.jsx)("div",{className:"font-medium",children:A?"Please correct the following issues:":"Please review the following:"})]}),s&&(0,a.jsxs)(j.$,{className:"size-6 p-0 opacity-70 hover:opacity-100",onClick:s,size:"sm",variant:"ghost",children:[(0,a.jsx)(y.A,{className:"size-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Dismiss"})]})]}),(0,a.jsxs)(b.TN,{className:"mt-3",children:[v.length>0&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("div",{className:"text-sm font-medium",children:"Field Errors:"}),(0,a.jsx)("ul",{className:"list-inside list-disc space-y-1 text-sm",children:v.map(e=>{var t;let[s,r]=e;return(0,a.jsxs)("li",{className:"flex items-center justify-between",children:[(0,a.jsxs)("span",{children:[(0,a.jsxs)("strong",{children:[C(s),":"]})," ",null!=(t=r.message)?t:"Invalid value"]}),i&&(0,a.jsx)(j.$,{className:"ml-2 h-6 px-2 text-xs",onClick:()=>I(s),size:"sm",variant:"ghost",children:"Go to field"})]},s)})})]}),x.length>0&&(0,a.jsxs)("div",{className:"mt-4 space-y-2",children:[(0,a.jsx)("div",{className:"text-sm font-medium",children:"Validation Errors:"}),(0,a.jsx)("ul",{className:"list-inside list-disc space-y-1 text-sm",children:x.map((e,t)=>(0,a.jsxs)("li",{className:"flex items-center justify-between",children:[(0,a.jsxs)("span",{children:[(0,a.jsxs)("strong",{children:[C(e.field),":"]})," ",e.message]}),i&&(0,a.jsx)(j.$,{className:"ml-2 h-6 px-2 text-xs",onClick:()=>I(e.field),size:"sm",variant:"ghost",children:"Go to field"})]},t))})]}),f.length>0&&(0,a.jsxs)("div",{className:"mt-4 space-y-2",children:[(0,a.jsx)("div",{className:"text-sm font-medium",children:"Warnings:"}),(0,a.jsx)("ul",{className:"list-inside list-disc space-y-1 text-sm",children:f.map((e,t)=>(0,a.jsxs)("li",{className:"flex items-center justify-between",children:[(0,a.jsxs)("span",{children:[(0,a.jsxs)("strong",{children:[C(e.field),":"]})," ",e.message]}),i&&(0,a.jsx)(j.$,{className:"ml-2 h-6 px-2 text-xs",onClick:()=>I(e.field),size:"sm",variant:"ghost",children:"Go to field"})]},t))})]})]})]}):null},k=e=>{let{show:t,onDismiss:s,showWarnings:r=!0,showSuccess:i=!1,showFieldNavigation:l=!0}=e,{getCrossFieldErrors:n,getCrossFieldWarnings:o}=D();return(0,a.jsx)(T,{show:t,onDismiss:s,showWarnings:r,showSuccess:i,showFieldNavigation:l})};var I=s(24944),R=s(39097),F=s(71153),z=s(21876);let P=F.k5(["Planned","Confirmed","In_Progress","Completed","Cancelled","No_details"]);F.Ik({changedAt:F.Yj().datetime({message:"Invalid date/time format for status change"}),id:F.Yj().uuid(),reason:F.Yj().optional(),status:P});let V=F.Ik({id:F.Yj().optional(),name:F.Yj().min(1,"Delegate name is required"),notes:F.Yj().optional(),title:F.Yj().min(1,"Delegate title is required")}),M=F.vk(e=>null!=e&&("object"!=typeof e||e.flightNumber&&""!==e.flightNumber.trim()||e.dateTime&&""!==e.dateTime.trim()||e.airport&&""!==e.airport.trim())?e:null,F.Ik({airport:F.vk(e=>null!=e?e:"",F.Yj().min(1,"Airport is required")),dateTime:F.vk(e=>null!=e?e:"",F.Yj().min(1,"Date & Time is required").refine(e=>!isNaN(Date.parse(e)),{message:"Invalid date & time format"})),flightNumber:F.vk(e=>null!=e?e:"",F.Yj().min(1,"Flight number is required")),notes:F.Yj().trim().nullable().optional().transform(e=>""===e?null:e),terminal:F.Yj().trim().nullable().optional().transform(e=>""===e?null:e)}).nullable().optional()),q=F.Ik({delegates:F.YO(V).min(1,"At least one delegate is required"),driverEmployeeIds:F.YO(F.ai().int().positive("Driver Employee ID must be a positive integer.")).optional().default([]),durationFrom:F.Yj().refine(e=>(0,z.Qr)(e),{message:"Invalid start date format"}),durationTo:F.Yj().refine(e=>(0,z.Qr)(e),{message:"Invalid end date format"}),escortEmployeeIds:F.YO(F.ai().int().positive("Escort Employee ID must be a positive integer.")).optional().default([]).refine(e=>e.length<=10,{message:"Maximum 10 escort employees allowed"}),eventName:F.Yj().min(1,"Event name is required"),flightArrivalDetails:M.nullable().optional(),flightDepartureDetails:M.nullable().optional(),imageUrl:F.Yj().url("Invalid URL for image").optional().or(F.eu("")),invitationFrom:F.Yj().optional(),invitationTo:F.Yj().optional(),location:F.Yj().min(1,"Location is required"),notes:F.Yj().optional(),status:P.default("Planned"),statusChangeReason:F.Yj().optional(),vehicleIds:F.YO(F.ai().int().positive("Vehicle ID must be a positive integer.")).optional().default([])}).superRefine((e,t)=>{new Date(e.durationFrom)>new Date(e.durationTo)&&t.addIssue({code:F.eq.custom,message:"End date cannot be earlier than start date",path:["durationTo"]}),e.vehicleIds&&e.vehicleIds.length>0&&(!e.driverEmployeeIds||0===e.driverEmployeeIds.length)&&t.addIssue({code:F.eq.custom,message:"Vehicles cannot be assigned without at least one driver",path:["vehicleIds"]})});var U=s(79239),L=s(37648),_=s(68801);let O=e=>{let{name:t,label:s,type:r="text",placeholder:i,disabled:l=!1,className:n="",showValidationIcon:o=!0,showValidationMessage:d=!0,showRequiredIndicator:c=!0,options:g,icon:h,...x}=e,{getFieldValidationState:f}=D(),{formState:{errors:b}}=(0,p.xW)(),v=f(t);b[t];let j=c&&v.isRequired?"".concat(s," *"):s;return(0,a.jsxs)("div",{className:(0,E.cn)("space-y-1",n),children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(_.z,{name:t,label:j,type:r,placeholder:i||"",disabled:l,options:g||[],...h&&{icon:h},className:(0,E.cn)("transition-all duration-200",v.hasError?"border-destructive focus:border-destructive focus:ring-destructive/20":v.hasWarning?"border-amber-500 focus:border-amber-500 focus:ring-amber-500/20":v.isValid&&v.isTouched&&v.isDirty?"border-green-500 focus:border-green-500 focus:ring-green-500/20":""),"aria-invalid":v.hasError,"aria-describedby":v.hasError||v.hasWarning?"".concat(t,"-validation-message"):void 0,...x}),o&&(0,a.jsx)("div",{className:"absolute right-3 top-9 flex items-center",children:o?v.hasError?(0,a.jsx)(u.A,{className:"h-4 w-4 text-destructive","aria-label":"Error"}):v.hasWarning?(0,a.jsx)(w.A,{className:"h-4 w-4 text-amber-500","aria-label":"Warning"}):v.isValid&&v.isTouched&&v.isDirty?(0,a.jsx)(m.A,{className:"h-4 w-4 text-green-500","aria-label":"Valid"}):null:null})]}),d&&(0,a.jsx)("div",{id:"".concat(t,"-validation-message"),role:"alert","aria-live":"polite",children:d?v.hasError&&v.errorMessage?(0,a.jsxs)("div",{className:"flex items-center gap-1 text-sm text-destructive mt-1",children:[(0,a.jsx)(u.A,{className:"h-3 w-3"}),(0,a.jsx)("span",{children:v.errorMessage})]}):v.hasWarning&&v.warningMessage?(0,a.jsxs)("div",{className:"flex items-center gap-1 text-sm text-amber-600 mt-1",children:[(0,a.jsx)(w.A,{className:"h-3 w-3"}),(0,a.jsx)("span",{children:v.warningMessage})]}):null:null})]})};var Y=s(59409),W=s(99673);let J=e=>{switch(e){case"Completed":return"default";case"In_Progress":return"secondary";case"Cancelled":return"destructive";default:return"outline"}},G=(e,t)=>{if(!e||!t)return 0;let s=new Date(e);return Math.ceil(Math.abs(new Date(t).getTime()-s.getTime())/864e5)},Q=e=>{let{isSubmitting:t=!1,className:s="",userRole:r="user"}=e,{control:i,watch:l,formState:{errors:o}}=(0,p.xW)(),{getSectionValidationState:u}=D(),g=l(),h=G(g.durationFrom,g.durationTo),x=g.durationFrom&&g.durationTo,f=u("basic").isComplete;return(0,a.jsxs)("section",{className:"space-y-6 rounded-lg border bg-card p-6 ".concat(s),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-10 h-10 rounded-lg bg-accent/10",children:(0,a.jsx)(n.A,{className:"h-5 w-5 text-accent"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-foreground",children:"Event Details"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Information about the delegation event"})]})]}),f&&(0,a.jsxs)(v.E,{variant:"default",className:"flex items-center gap-1",children:[(0,a.jsx)(m.A,{className:"h-3 w-3"}),"Complete"]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm font-medium text-foreground mb-3",children:[(0,a.jsx)(U.A,{className:"h-4 w-4 text-accent"}),"Event Information"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2",children:[(0,a.jsx)(O,{name:"eventName",label:"Event Name",disabled:t,placeholder:"e.g., Board Meeting, Conference, Official Visit"}),(0,a.jsx)(O,{name:"location",label:"Location",disabled:t,placeholder:"e.g., Rabat, Morocco"})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm font-medium text-foreground mb-3",children:[(0,a.jsx)(L.A,{className:"h-4 w-4 text-accent"}),"Duration & Timing"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2",children:[(0,a.jsx)(O,{name:"durationFrom",label:"Start Date",type:"date",disabled:t}),(0,a.jsx)(O,{name:"durationTo",label:"End Date",type:"date",disabled:t})]}),x&&h>0&&(0,a.jsxs)(b.Fc,{children:[(0,a.jsx)(c.A,{className:"h-4 w-4"}),(0,a.jsx)(b.TN,{children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("span",{children:"Duration:"}),(0,a.jsxs)(v.E,{variant:"secondary",children:[h," ",1===h?"day":"days"]}),h>7&&(0,a.jsx)("span",{className:"text-sm text-muted-foreground",children:"(Extended delegation - consider additional planning)"})]})})]}),o.durationTo&&(0,a.jsx)(b.Fc,{variant:"destructive",children:(0,a.jsx)(b.TN,{children:o.durationTo.message})})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm font-medium text-foreground mb-3",children:[(0,a.jsx)(d.A,{className:"h-4 w-4 text-accent"}),"Additional Details"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2",children:[(0,a.jsx)(O,{name:"invitationFrom",label:"Invitation From",disabled:t,placeholder:"Optional: When invitations should be sent",showRequiredIndicator:!1}),(0,a.jsx)(O,{name:"invitationTo",label:"Invitation To",disabled:t,placeholder:"Optional: RSVP deadline",showRequiredIndicator:!1})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm font-medium text-foreground mb-3",children:[(0,a.jsx)("div",{className:"h-4 w-4 rounded-full ".concat("default"===J(g.status||"Planned")?"bg-green-500":"secondary"===J(g.status||"Planned")?"bg-blue-500":"destructive"===J(g.status||"Planned")?"bg-red-500":"bg-gray-400")}),"Status Management"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2",children:[(0,a.jsx)(_.z,{label:"Current Status",name:"status",render:e=>{let{field:s}=e;return(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(Y.l6,{value:s.value||"",onValueChange:s.onChange,disabled:t,children:[(0,a.jsx)(Y.bq,{children:(0,a.jsx)(Y.yv,{placeholder:"Select delegation status"})}),(0,a.jsx)(Y.gC,{children:P.options.map(e=>(0,a.jsx)(Y.eb,{value:e,children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"h-2 w-2 rounded-full ".concat("default"===J(e)?"bg-green-500":"secondary"===J(e)?"bg-blue-500":"destructive"===J(e)?"bg-red-500":"bg-gray-400")}),(0,W.fZ)(e)]})},e))})]}),s.value&&(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Planned"===s.value&&"Initial planning phase - details can be modified","Confirmed"===s.value&&"Event confirmed - notify all stakeholders","In_Progress"===s.value&&"Event is currently ongoing","Completed"===s.value&&"Event has been completed successfully","Cancelled"===s.value&&"Event has been cancelled - notify all parties","No_details"===s.value&&"Requires additional information"]})]})}}),(0,a.jsx)(O,{name:"imageUrl",label:"Supporting Image/Document URL",disabled:t,placeholder:"https://example.com/image.jpg",showRequiredIndicator:!1})]})]}),"user"===r&&!f&&(0,a.jsxs)(b.Fc,{children:[(0,a.jsx)(c.A,{className:"h-4 w-4"}),(0,a.jsx)(b.TN,{children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("div",{className:"font-medium",children:"Getting Started"}),(0,a.jsxs)("ul",{className:"text-sm space-y-1",children:[(0,a.jsx)("li",{children:"• Fill in the event name and location first"}),(0,a.jsx)("li",{children:"• Set accurate start and end times for proper planning"}),(0,a.jsx)("li",{children:"• Choose the appropriate status based on planning phase"}),(0,a.jsx)("li",{children:"• Optional fields can be completed later if needed"})]})]})})]})]})};var B=s(91721),Z=s(77223),$=s(34301);let K=e=>{var t,s;return!!((null==e||null==(t=e.name)?void 0:t.trim())&&(null==e||null==(s=e.title)?void 0:s.trim()))},H=e=>{var t,s,a,r;return(null==e||null==(t=e.name)?void 0:t.trim())||(null==e||null==(s=e.title)?void 0:s.trim())?(null==e||null==(a=e.name)?void 0:a.trim())&&(null==e||null==(r=e.title)?void 0:r.trim())?"complete":"incomplete":"empty"},X=e=>{let{isSubmitting:t=!1,className:s="",minDelegates:r=1,maxDelegates:i=10,userRole:l="user"}=e,{control:n,watch:d,formState:{errors:g}}=(0,p.xW)(),{getSectionValidationState:h}=D(),{fields:x,append:f,remove:y}=(0,p.jz)({control:n,name:"delegates"}),w=d("delegates"),A=(null==w?void 0:w.filter(e=>K(e)))||[],E=A.length>=r,S=e=>{x.length>r&&y(e)},C=x.length<i,T=x.length>r,k=Math.round(A.length/Math.max(x.length,1)*100);return(0,a.jsxs)("section",{className:"space-y-6 rounded-lg border bg-card p-6 ".concat(s),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-10 h-10 rounded-lg bg-accent/10",children:(0,a.jsx)(o.A,{className:"h-5 w-5 text-accent"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-foreground",children:"Delegates"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"People receiving delegated responsibilities and authority"})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(v.E,{variant:E?"default":"secondary",children:[A.length," of ",x.length," complete"]}),E&&(0,a.jsxs)(v.E,{variant:"default",className:"flex items-center gap-1",children:[(0,a.jsx)(m.A,{className:"h-3 w-3"}),"Valid"]})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Section Progress"}),(0,a.jsxs)("span",{className:"font-medium",children:[k,"%"]})]}),(0,a.jsxs)(b.Fc,{children:[(0,a.jsx)(c.A,{className:"h-4 w-4"}),(0,a.jsx)(b.TN,{children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("div",{className:"font-medium",children:"Delegate Guidelines"}),(0,a.jsxs)("ul",{className:"text-sm space-y-1",children:[(0,a.jsxs)("li",{children:["• At least ",r," delegate",r>1?"s are":" is"," required"]}),(0,a.jsx)("li",{children:"• Include full name and official title for each delegate"}),(0,a.jsx)("li",{children:"• Add notes for special responsibilities or requirements"}),(0,a.jsxs)("li",{children:["• Maximum ",i," delegates allowed per delegation"]})]})]})})]})]}),(0,a.jsx)("div",{className:"space-y-4",children:x.map((e,s)=>{var r,i,l,n,o,d,c,h;let x=H(null==w?void 0:w[s]),f=null==(r=g.delegates)?void 0:r[s];return(0,a.jsxs)(N.Zp,{className:"transition-colors ".concat("complete"===x?"border-green-200 bg-background":"incomplete"===x?"border-amber-200 bg-background":"border-border bg-background"),children:[(0,a.jsx)(N.aR,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-8 h-8 rounded-lg ".concat("complete"===x?"bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400":"incomplete"===x?"bg-amber-100 text-amber-700 dark:bg-amber-900/20 dark:text-amber-400":"bg-muted text-muted-foreground"),children:(0,a.jsx)(B.A,{className:"h-4 w-4"})}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h4",{className:"font-medium text-sm",children:["Delegate ",s+1,"complete"===x&&(null==w||null==(i=w[s])?void 0:i.name)&&" - ".concat(w[s].name)]}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:["complete"===x&&"Complete","incomplete"===x&&"Needs attention","empty"===x&&"Not started"]})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:["complete"===x&&(0,a.jsx)(m.A,{className:"h-4 w-4 text-green-600"}),"incomplete"===x&&(0,a.jsx)(u.A,{className:"h-4 w-4 text-amber-600"}),T&&(0,a.jsx)(j.$,{type:"button",variant:"ghost",size:"sm",onClick:()=>S(s),disabled:t,className:"h-8 w-8 p-0 text-destructive hover:text-destructive hover:bg-destructive/10",children:(0,a.jsx)(Z.A,{className:"h-4 w-4"})})]})]})}),(0,a.jsxs)(N.Wu,{className:"space-y-4 pt-0",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(_.z,{label:"Full Name",name:"delegates.".concat(s,".name"),disabled:t,placeholder:"e.g., Dr. Sarah Johnson",description:"Complete legal name"}),(null==w||null==(l=w[s])?void 0:l.name)&&w[s].name.length>0&&(0,a.jsx)(m.A,{className:"absolute top-8 right-3 h-4 w-4 text-green-500"}),(null==(o=g.delegates)||null==(n=o[s])?void 0:n.name)&&(0,a.jsx)("div",{className:"absolute top-8 right-3",children:(0,a.jsx)(u.A,{className:"h-4 w-4 text-red-500"})})]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(_.z,{label:"Official Title",name:"delegates.".concat(s,".title"),disabled:t,placeholder:"e.g., Director of Operations",description:"Current position or role"}),(null==w||null==(d=w[s])?void 0:d.title)&&w[s].title.length>0&&(0,a.jsx)(m.A,{className:"absolute top-8 right-3 h-4 w-4 text-green-500"}),(null==(h=g.delegates)||null==(c=h[s])?void 0:c.title)&&(0,a.jsx)("div",{className:"absolute top-8 right-3",children:(0,a.jsx)(u.A,{className:"h-4 w-4 text-red-500"})})]})]}),(0,a.jsx)(_.z,{label:"Special Notes or Requirements",name:"delegates.".concat(s,".notes"),disabled:t,placeholder:"Optional: Special responsibilities, dietary requirements, accessibility needs...",description:"Any additional information relevant to this delegate",type:"textarea",rows:2}),f&&(0,a.jsxs)(b.Fc,{variant:"destructive",children:[(0,a.jsx)(u.A,{className:"h-4 w-4"}),(0,a.jsx)(b.TN,{children:"Please complete the required fields for this delegate."})]})]})]},e.id)})}),(0,a.jsxs)("div",{className:"flex items-center justify-between pt-4 border-t",children:[(0,a.jsxs)("div",{className:"text-sm text-muted-foreground",children:[0===x.length&&"No delegates added yet",1===x.length&&"1 delegate",x.length>1&&"".concat(x.length," delegates"),x.length>0&&" • ".concat(A.length," completed")]}),(0,a.jsxs)(j.$,{type:"button",variant:"outline",onClick:()=>{x.length<i&&f({name:"",notes:"",title:""})},disabled:!C||t,className:"flex items-center gap-2",children:[(0,a.jsx)($.A,{className:"h-4 w-4"}),"Add Delegate",!C&&" (Max ".concat(i,")")]})]}),!E&&x.length>0&&(0,a.jsxs)(b.Fc,{variant:"destructive",children:[(0,a.jsx)(u.A,{className:"h-4 w-4"}),(0,a.jsxs)(b.TN,{children:["At least ",r," completed delegate",r>1?"s are":" is"," required. Please complete the name and title for all delegates."]})]}),E&&A.length===x.length&&(0,a.jsxs)(b.Fc,{children:[(0,a.jsx)(m.A,{className:"h-4 w-4 text-green-600"}),(0,a.jsx)(b.TN,{className:"text-green-700",children:"All delegates are properly configured. You can proceed to the next section or add more delegates if needed."})]})]})};var ee=s(28328),et=s(17759),es=s(83761);let ea=e=>{let{isSubmitting:t=!1,className:s=""}=e,{watch:r,setValue:i,getValues:l}=(0,p.xW)(),n=r("driverEmployeeIds")||[],{data:o=[],isLoading:d,error:c}=(0,es.sZ)("driver"),m=o.filter(e=>!n.includes(e.id)),u=o.filter(e=>n.includes(e.id)),g=e=>{n.includes(e)||i("driverEmployeeIds",[...n,e])},h=e=>{i("driverEmployeeIds",n.filter(t=>t!==e))};return(0,a.jsxs)("section",{className:"space-y-4 rounded-lg border bg-card p-6 ".concat(s),children:[(0,a.jsxs)("h3",{className:"flex items-center text-lg font-semibold text-foreground",children:[(0,a.jsx)(ee.A,{className:"mr-2 size-5 text-accent"}),"Drivers"]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{className:"text-sm font-medium text-foreground",children:"Select Drivers"}),(0,a.jsxs)("select",{className:"w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",onChange:e=>{let t=parseInt(e.target.value,10);t&&(g(t),e.target.value="")},disabled:t||d,children:[(0,a.jsx)("option",{value:"",children:d?"Loading drivers...":"Select a driver to add"}),m.map(e=>(0,a.jsxs)("option",{value:e.id,children:[e.fullName||e.name,e.employeeId&&" (".concat(e.employeeId,")"),e.status&&" - ".concat(e.status),e.availability&&" - ".concat(e.availability.replace("_"," ")),e.currentLocation&&" @ ".concat(e.currentLocation)]},e.id))]})]}),u.length>0&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("label",{className:"text-sm font-medium text-foreground",children:["Selected Drivers (",u.length,")"]}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:u.map(e=>{var s;return(0,a.jsxs)(v.E,{variant:"secondary",className:"inline-flex items-center gap-1 px-3 py-1",children:[(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("span",{className:"font-medium",children:e.fullName||e.name}),(e.status||e.availability)&&(0,a.jsxs)("span",{className:"text-xs text-muted-foreground",children:[e.status,e.status&&e.availability&&" • ",null==(s=e.availability)?void 0:s.replace("_"," ")]})]}),!t&&(0,a.jsx)("button",{type:"button",onClick:()=>h(e.id),className:"ml-1 text-muted-foreground hover:text-destructive","aria-label":"Remove ".concat(e.fullName||e.name),children:(0,a.jsx)(Z.A,{className:"size-3"})})]},e.id)})})]}),0===u.length&&(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"No drivers selected. Select drivers from the dropdown above."}),!d&&0===o.length&&(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"No drivers available. Please add drivers to the system first."}),(0,a.jsx)(et.C5,{})]})};var er=s(45731);let ei=e=>{let{isSubmitting:t=!1,className:s=""}=e,{watch:r,setValue:i}=(0,p.xW)(),l=r("escortEmployeeIds")||[],{data:n=[],isLoading:o,error:d}=(0,es.sZ)(),c=n.filter(e=>"driver"!==e.role),m=c.filter(e=>!l.includes(e.id)),u=c.filter(e=>l.includes(e.id)),g=e=>{l.includes(e)||i("escortEmployeeIds",[...l,e])},h=e=>{i("escortEmployeeIds",l.filter(t=>t!==e))};return(0,a.jsxs)("section",{className:"space-y-4 rounded-lg border bg-card p-6 ".concat(s),children:[(0,a.jsxs)("h3",{className:"flex items-center text-lg font-semibold text-foreground",children:[(0,a.jsx)(er.A,{className:"mr-2 size-5 text-accent"}),"Escorts"]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{className:"text-sm font-medium text-foreground",children:"Select Escorts"}),(0,a.jsxs)("select",{className:"w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",onChange:e=>{let t=parseInt(e.target.value,10);t&&(g(t),e.target.value="")},disabled:t||o,children:[(0,a.jsx)("option",{value:"",children:o?"Loading escorts...":"Select an escort to add"}),m.map(e=>(0,a.jsxs)("option",{value:e.id,children:[e.fullName||e.name,e.employeeId&&" (".concat(e.employeeId,")"),e.status&&" - ".concat(e.status),e.role&&" (".concat(e.role,")")]},e.id))]})]}),u.length>0&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("label",{className:"text-sm font-medium text-foreground",children:["Selected Escorts (",u.length,")"]}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:u.map(e=>(0,a.jsxs)(v.E,{variant:"secondary",className:"inline-flex items-center gap-1 px-3 py-1",children:[(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("span",{className:"font-medium",children:e.fullName||e.name}),(e.status||e.role)&&(0,a.jsxs)("span",{className:"text-xs text-muted-foreground",children:[e.role,e.role&&e.status&&" • ",e.status]})]}),!t&&(0,a.jsx)("button",{type:"button",onClick:()=>h(e.id),className:"ml-1 text-muted-foreground hover:text-destructive","aria-label":"Remove ".concat(e.fullName||e.name),children:(0,a.jsx)(Z.A,{className:"size-3"})})]},e.id))})]}),0===u.length&&(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"No escorts selected. Select escorts from the dropdown above."}),!o&&0===c.length&&(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"No escorts available. Please add escorts to the system first."}),(0,a.jsx)(et.C5,{})]})};var el=s(85081),en=s(80937);let eo=e=>{let{isSubmitting:t=!1,className:s=""}=e,{watch:r,setValue:i}=(0,p.xW)(),l=r("vehicleIds")||[],n=r("driverEmployeeIds")||[],{data:o=[],isLoading:d,error:c}=(0,en.T$)(),m=o.filter(e=>!l.includes(e.id)),u=o.filter(e=>l.includes(e.id)),g=e=>{l.includes(e)||i("vehicleIds",[...l,e])},h=e=>{i("vehicleIds",l.filter(t=>t!==e))},x=e=>{let t=[];return e.make&&t.push(e.make),e.model&&t.push(e.model),e.year&&t.push("(".concat(e.year,")")),e.licensePlate&&t.push("- ".concat(e.licensePlate)),t.length>0?t.join(" "):"Vehicle ".concat(e.id)};return(0,a.jsxs)("section",{className:"space-y-4 rounded-lg border bg-card p-6 ".concat(s),children:[(0,a.jsxs)("h3",{className:"flex items-center text-lg font-semibold text-foreground",children:[(0,a.jsx)(el.A,{className:"mr-2 size-5 text-accent"}),"Vehicles"]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("label",{className:"text-sm font-medium text-foreground",children:["Select Vehicles"," ",0===n.length&&"(Requires Driver)"]}),0===n.length&&(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Please select at least one driver before adding vehicles."}),(0,a.jsxs)("select",{className:"w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",onChange:e=>{let t=parseInt(e.target.value,10);t&&(g(t),e.target.value="")},disabled:t||d||0===n.length,children:[(0,a.jsx)("option",{value:"",children:d?"Loading vehicles...":0===n.length?"Select drivers first":"Select a vehicle to add"}),n.length>0&&m.map(e=>(0,a.jsx)("option",{value:e.id,children:x(e)},e.id))]})]}),u.length>0&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("label",{className:"text-sm font-medium text-foreground",children:["Selected Vehicles (",u.length,")"]}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:u.map(e=>(0,a.jsxs)(v.E,{variant:"secondary",className:"inline-flex items-center gap-1 px-3 py-1",children:[(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsxs)("span",{className:"font-medium",children:[e.make," ",e.model]}),(0,a.jsxs)("span",{className:"text-xs text-muted-foreground",children:[e.year," • ",e.licensePlate||"No License"]})]}),!t&&(0,a.jsx)("button",{type:"button",onClick:()=>h(e.id),className:"ml-1 text-muted-foreground hover:text-destructive","aria-label":"Remove ".concat(x(e)),children:(0,a.jsx)(Z.A,{className:"size-3"})})]},e.id))})]}),0===u.length&&(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"No vehicles selected. Select vehicles from the dropdown above."}),!d&&0===o.length&&(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"No vehicles available. Please add vehicles to the system first."}),(0,a.jsx)(et.C5,{})]})},ed=e=>{let{isSubmitting:t=!1,className:s="",employees:r=[],vehicles:i=[],userRole:l="user"}=e;return(0,a.jsxs)("section",{className:"space-y-6 ".concat(s),children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(o.A,{className:"size-6 text-accent"}),(0,a.jsx)("h2",{className:"text-xl font-semibold text-foreground",children:"Assignment Details"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-6 lg:grid-cols-3",children:[(0,a.jsx)(ea,{isSubmitting:t}),(0,a.jsx)(ei,{isSubmitting:t}),(0,a.jsx)(eo,{isSubmitting:t})]}),(0,a.jsxs)("div",{className:"rounded-lg bg-muted/50 p-4",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-foreground mb-2",children:"Assignment Guidelines"}),(0,a.jsxs)("ul",{className:"text-sm text-muted-foreground space-y-1",children:[(0,a.jsx)("li",{children:"• At least one escort is typically required for security"}),(0,a.jsx)("li",{children:"• Drivers are required before vehicles can be assigned"}),(0,a.jsx)("li",{children:"• Vehicle assignments should match the number of drivers"}),(0,a.jsx)("li",{children:"• Consider delegation size when selecting personnel"})]})]})]})};var ec=s(83082),em=s(75074),eu=s(74465),eg=s(6560),eh=s(41784),ex=s(50172),ef=s(58260),ep=s(85511),eb=s(54165),ev=s(62523),ej=s(14636),eN=s(66424),ey=s(40879);s(3619),s(1955);let ew=async(e,t)=>{throw Error("Please use FlightApiService instance instead of direct function call")};function eA(e){let{isOpen:t,onClose:s,onSelectFlight:r,type:i}=e,[l,o]=(0,f.useState)(""),[d,c]=(0,f.useState)(new Date),[m,u]=(0,f.useState)(!1),[g,h]=(0,f.useState)([]),[x,p]=(0,f.useState)(null),{toast:b}=(0,ey.dj)(),v=(0,f.useCallback)(function(e,t){let s=null,a=function(){for(var a=arguments.length,r=Array(a),i=0;i<a;i++)r[i]=arguments[i];let l=this;s&&clearTimeout(s),s=setTimeout(()=>{s=null,e.apply(l,r)},t)};return a.cancel=function(){s&&(clearTimeout(s),s=null)},a}(async(e,t)=>{if(e.length<2)return void h([]);if(!t){p("Please select a date to search."),b({description:"Please select a date before searching for flights.",title:"Date Required",variant:"destructive"}),h([]);return}u(!0),p(null);let s=(0,eh.GP)(t,"yyyy-MM-dd");try{let t=await ew(e,s);h(t),0===t.length&&b({description:'No flights found matching "'.concat(e,'" on ').concat(s,"."),title:"No Flights Found",variant:"default"})}catch(e){p(e.message||"Failed to search flights"),b({description:"Failed to search flights: ".concat(e.message,". Please try again."),title:"Error Searching Flights",variant:"destructive"})}finally{u(!1)}},500),[b]);(0,f.useEffect)(()=>(l&&d?v(l,d):h([]),()=>{v.cancel()}),[l,d,v]);let j=e=>{r(e),s()},N=e=>e?new Date(1e3*e).toLocaleString():"Unknown";return(0,a.jsx)(eb.lG,{onOpenChange:e=>!e&&s(),open:t,children:(0,a.jsxs)(eb.Cf,{className:"flex max-h-[80vh] flex-col sm:max-w-[600px]",children:[(0,a.jsxs)(eb.c7,{children:[(0,a.jsxs)(eb.L3,{className:"flex items-center",children:["arrival"===i?(0,a.jsx)(ec.A,{className:"mr-2 size-5 text-accent"}):(0,a.jsx)(eu.A,{className:"mr-2 size-5 text-accent"}),"Search ","arrival"===i?"Arrival":"Departure"," Flights"]}),(0,a.jsx)(eb.rr,{children:"Enter a flight callsign (e.g., BA123) and select a date to search for flights."})]}),(0,a.jsxs)("div",{className:"mb-4 grid grid-cols-1 gap-4 md:grid-cols-2",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(em.A,{className:"absolute left-3 top-1/2 size-4 -translate-y-1/2 text-muted-foreground"}),(0,a.jsx)(ev.p,{className:"pl-10",onChange:e=>o(e.target.value),placeholder:"Callsign (e.g., BA123)",value:l})]}),(0,a.jsx)("div",{children:(0,a.jsxs)(ej.AM,{children:[(0,a.jsx)(ej.Wv,{asChild:!0,children:(0,a.jsx)(eg.r,{actionType:"tertiary",className:(0,E.cn)("w-full justify-start text-left font-normal",!d&&"text-muted-foreground"),icon:(0,a.jsx)(n.A,{className:"size-4"}),children:d?(0,eh.GP)(d,"PPP"):(0,a.jsx)("span",{children:"Pick a date"})})}),(0,a.jsx)(ej.hl,{className:"w-auto p-0",children:(0,a.jsx)(ep.V,{initialFocus:!0,mode:"single",onSelect:c,selected:d})})]})})]}),m?(0,a.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,a.jsx)(ex.A,{className:"size-8 animate-spin text-primary"}),(0,a.jsx)("span",{className:"ml-2 text-muted-foreground",children:"Searching flights..."})]}):x?(0,a.jsxs)("div",{className:"py-8 text-center",children:[(0,a.jsxs)("div",{className:"mb-4 rounded-md bg-destructive/15 p-4 text-sm text-destructive",children:[(0,a.jsx)("h4",{className:"mb-2 font-bold",children:"Error Details:"}),(0,a.jsx)("p",{className:"mb-2",children:x}),x&&x.details&&(0,a.jsxs)("div",{className:"mt-3 border-t border-destructive/20 pt-3",children:[x.details.possibleReasons&&(0,a.jsxs)("div",{className:"mt-2",children:[(0,a.jsx)("h5",{className:"mb-1 text-xs font-semibold",children:"Possible Reasons:"}),(0,a.jsx)("ul",{className:"list-inside list-disc text-xs",children:x.details.possibleReasons.map((e,t)=>(0,a.jsx)("li",{className:"mb-1",children:e},t))})]}),x.details.apiInfo&&(0,a.jsxs)("div",{className:"mt-2 text-xs",children:[(0,a.jsx)("h5",{className:"mb-1 font-semibold",children:"API Information:"}),(0,a.jsx)("p",{children:x.details.apiInfo})]})]}),(0,a.jsxs)("p",{className:"mt-3 text-xs text-muted-foreground",children:["API URL:"," ","http://192.168.100.31:3001/api"]})]}),(0,a.jsx)("div",{className:"flex justify-center gap-2",children:x.toString().includes("future date")?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(eg.r,{actionType:"tertiary",onClick:()=>{c(new Date),setTimeout(()=>v(l,new Date),100)},children:"Try with Today's Date"}),(0,a.jsx)(eg.r,{actionType:"tertiary",onClick:()=>{let e=new Date;e.setDate(e.getDate()-1),c(e),setTimeout(()=>v(l,e),100)},children:"Try with Yesterday"})]}):(0,a.jsx)(eg.r,{actionType:"tertiary",onClick:()=>v(l,d),children:"Try Again"})})]}):0===g.length?(0,a.jsx)("div",{className:"py-8 text-center text-muted-foreground",children:l.length>0?(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"mb-2 font-medium text-amber-500",children:['No flights found matching "',l,'" on'," ",d?(0,eh.GP)(d,"PPP"):"selected date"]}),(0,a.jsxs)("div",{className:"mb-4 rounded-md bg-muted p-4 text-sm",children:[(0,a.jsx)("h4",{className:"mb-2 font-semibold",children:"Suggestions:"}),(0,a.jsxs)("ul",{className:"list-inside list-disc text-sm text-muted-foreground",children:[(0,a.jsx)("li",{className:"mb-1",children:'Check if the callsign format is correct (e.g., "RYR441J" for Ryanair flight 441J)'}),(0,a.jsx)("li",{className:"mb-1",children:"Try searching for a different date - OpenSky may not have data for all dates"}),(0,a.jsx)("li",{className:"mb-1",children:"OpenSky Network API primarily provides historical data, not future schedules"}),(0,a.jsx)("li",{className:"mb-1",children:"Some flights may not be tracked by OpenSky Network"})]})]}),(0,a.jsxs)("div",{className:"inline-block rounded-md bg-muted p-2 text-xs text-muted-foreground",children:[(0,a.jsxs)("p",{children:["API URL:"," ","http://192.168.100.31:3001/api","/flights/search"]}),(0,a.jsxs)("p",{children:["Search Term: ",l]}),(0,a.jsxs)("p",{children:["Date:"," ",d?(0,eh.GP)(d,"yyyy-MM-dd"):"Not selected"]})]})]}):"Enter a flight callsign to search."}):(0,a.jsx)(eN.F,{className:"max-h-[400px] flex-1 pr-4",children:(0,a.jsx)("div",{className:"space-y-2",children:g.map(e=>(0,a.jsxs)("div",{className:"cursor-pointer rounded-md border p-3 transition-colors hover:bg-accent hover:text-accent-foreground",onClick:()=>j(e),children:[(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-semibold",children:e.callsign}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:[e.departureAirport||"Unknown"," →"," ",e.arrivalAirport||"Unknown"]})]}),(0,a.jsxs)("div",{className:"text-right text-sm",children:[(0,a.jsxs)("p",{className:"flex items-center",children:[(0,a.jsx)(ef.A,{className:"mr-1 inline-block size-3 text-muted-foreground"}),e.icao24]}),void 0!==e.onGround&&(0,a.jsx)("p",{className:e.onGround?"text-amber-500":"text-green-500",children:e.onGround?"On Ground":"In Air"})]})]}),(e.departureTime||e.arrivalTime)&&(0,a.jsxs)("div",{className:"mt-2 text-xs text-muted-foreground",children:[e.departureTime&&(0,a.jsxs)("p",{children:["Departure: ",N(e.departureTime)]}),e.arrivalTime&&(0,a.jsxs)("p",{children:["Arrival: ",N(e.arrivalTime)]})]})]},"".concat(e.icao24,"-").concat(e.callsign)))})}),(0,a.jsx)(eb.Es,{className:"mt-4",children:(0,a.jsx)(eg.r,{actionType:"tertiary",onClick:s,children:"Cancel"})})]})})}let eD=e=>{let{isSubmitting:t=!1,className:s="",userRole:r="user"}=e,{setValue:i}=(0,p.xW)(),[l,n]=(0,f.useState)(!1),[o,d]=(0,f.useState)(!1),c=(e,t)=>{let s="arrival"===t?"flightArrivalDetails":"flightDepartureDetails";"arrival"===t&&e.arrivalTime&&e.arrivalAirport?(i("".concat(s,".flightNumber"),e.callsign),i("".concat(s,".dateTime"),new Date(e.arrivalTime).toISOString().slice(0,16)),i("".concat(s,".airport"),e.arrivalAirport)):"departure"===t&&e.departureTime&&e.departureAirport&&(i("".concat(s,".flightNumber"),e.callsign),i("".concat(s,".dateTime"),new Date(e.departureTime).toISOString().slice(0,16)),i("".concat(s,".airport"),e.departureAirport)),"arrival"===t?n(!1):d(!1)};return(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-6 lg:grid-cols-2 ".concat(s),children:[(0,a.jsxs)("section",{className:"space-y-4 rounded-lg border bg-card p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("h3",{className:"flex items-center text-lg font-semibold text-foreground",children:[(0,a.jsx)(ec.A,{className:"mr-2 size-5 text-accent"}),"Arrival Flight (Optional)"]}),(0,a.jsx)(eg.r,{actionType:"tertiary",icon:(0,a.jsx)(em.A,{className:"size-4"}),onClick:()=>n(!0),size:"sm",type:"button",disabled:t,children:"Search Flights"})]}),(0,a.jsx)(_.z,{label:"Flight Number",name:"flightArrivalDetails.flightNumber",disabled:t}),(0,a.jsx)(_.z,{label:"Date & Time",name:"flightArrivalDetails.dateTime",type:"datetime-local",disabled:t}),(0,a.jsx)(_.z,{label:"Airport",name:"flightArrivalDetails.airport",disabled:t}),(0,a.jsx)(_.z,{label:"Terminal (Optional)",name:"flightArrivalDetails.terminal",disabled:t}),(0,a.jsx)(_.z,{label:"Notes (Optional)",name:"flightArrivalDetails.notes",type:"textarea",disabled:t})]}),(0,a.jsxs)("section",{className:"space-y-4 rounded-lg border bg-card p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("h3",{className:"flex items-center text-lg font-semibold text-foreground",children:[(0,a.jsx)(eu.A,{className:"mr-2 size-5 text-accent"}),"Departure Flight (Optional)"]}),(0,a.jsx)(eg.r,{actionType:"tertiary",icon:(0,a.jsx)(em.A,{className:"size-4"}),onClick:()=>d(!0),size:"sm",type:"button",disabled:t,children:"Search Flights"})]}),(0,a.jsx)(_.z,{label:"Flight Number",name:"flightDepartureDetails.flightNumber",disabled:t}),(0,a.jsx)(_.z,{label:"Date & Time",name:"flightDepartureDetails.dateTime",type:"datetime-local",disabled:t}),(0,a.jsx)(_.z,{label:"Airport",name:"flightDepartureDetails.airport",disabled:t}),(0,a.jsx)(_.z,{label:"Terminal (Optional)",name:"flightDepartureDetails.terminal",disabled:t}),(0,a.jsx)(_.z,{label:"Notes (Optional)",name:"flightDepartureDetails.notes",type:"textarea",disabled:t})]}),(0,a.jsx)(eA,{isOpen:l,onClose:()=>n(!1),onSelectFlight:e=>c(e,"arrival"),type:"arrival"}),(0,a.jsx)(eA,{isOpen:o,onClose:()=>d(!1),onSelectFlight:e=>c(e,"departure"),type:"departure"})]})},eE=e=>{let{isSubmitting:t=!1,className:s="",maxLength:r=2e3,userRole:i="user"}=e,{watch:l}=(0,p.xW)(),n=(l("notes")||"").length,o=n>.8*r,d=n>r;return(0,a.jsxs)("section",{className:"space-y-4 rounded-lg border bg-card p-6 ".concat(s),children:[(0,a.jsxs)("h3",{className:"flex items-center text-lg font-semibold text-foreground",children:[(0,a.jsx)(c.A,{className:"mr-2 size-5 text-accent"}),"General Notes (Optional)"]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(_.z,{label:"Additional Notes for the Delegation",name:"notes",type:"textarea",disabled:t,placeholder:"Enter any additional information, special requirements, or notes about this delegation..."}),(0,a.jsxs)("div",{className:"flex justify-between items-center text-sm",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Additional context can help with delegation planning and execution"}),(0,a.jsxs)("span",{className:"font-medium ".concat(d?"text-destructive":o?"text-warning":"text-muted-foreground"),children:[n,"/",r]})]}),o&&!d&&(0,a.jsx)("p",{className:"text-sm text-warning",children:"Approaching character limit. Consider being more concise."}),d&&(0,a.jsx)("p",{className:"text-sm text-destructive",children:"Character limit exceeded. Please shorten your notes."})]})]})};class eS{validateFlightDetails(e){let t=[];if(!e)return{isValid:!0,errors:[]};let s=e.flightNumber&&""!==e.flightNumber.trim(),a=e.dateTime&&""!==e.dateTime.trim(),r=e.airport&&""!==e.airport.trim();if((s||a||r)&&(s||t.push({field:"flightNumber",message:"Flight number is required when flight details are provided",code:"REQUIRED_FIELD"}),a||t.push({field:"dateTime",message:"Date and time are required when flight details are provided",code:"REQUIRED_FIELD"}),r||t.push({field:"airport",message:"Airport is required when flight details are provided",code:"REQUIRED_FIELD"}),a))try{let s=new Date(e.dateTime);isNaN(s.getTime())&&t.push({field:"dateTime",message:"Invalid date/time format",code:"INVALID_FORMAT"})}catch(e){t.push({field:"dateTime",message:"Invalid date/time format",code:"INVALID_FORMAT"})}return{isValid:0===t.length,errors:t}}validateFormData(e){var t,s;let a=[],r=[];(null==(t=e.eventName)?void 0:t.trim())||a.push({field:"eventName",message:"Event name is required",code:"REQUIRED_FIELD"}),(null==(s=e.location)?void 0:s.trim())||a.push({field:"location",message:"Location is required",code:"REQUIRED_FIELD"}),this.validateDateRange(e.durationFrom,e.durationTo)||a.push({field:"durationTo",message:"End date cannot be earlier than start date",code:"INVALID_DATE_RANGE"});let i=this.validateDelegates(e.delegates||[]);if(i.isValid||a.push(...i.errors),e.vehicleIds&&e.vehicleIds.length>0&&(!e.driverEmployeeIds||0===e.driverEmployeeIds.length)&&a.push({field:"vehicleIds",message:"Vehicles can only be assigned when at least one driver is assigned",code:"VEHICLES_REQUIRE_DRIVERS"}),e.flightArrivalDetails){let t=this.validateFlightDetails(e.flightArrivalDetails);t.isValid||a.push(...t.errors.map(e=>({...e,field:"flightArrivalDetails.".concat(e.field)})))}if(e.flightDepartureDetails){let t=this.validateFlightDetails(e.flightDepartureDetails);t.isValid||a.push(...t.errors.map(e=>({...e,field:"flightDepartureDetails.".concat(e.field)})))}return e.delegates&&e.delegates.length>5&&r.push({field:"delegates",message:"Large number of delegates may require additional coordination",code:"MANY_DELEGATES"}),{isValid:0===a.length,errors:a,warnings:r}}validateSection(e,t){return{isValid:!0,errors:[]}}validateDelegateData(e){return{isValid:!0,errors:[]}}transformForSubmission(e){return{...e,calculatedDuration:this.calculateDuration(e.durationFrom,e.durationTo),processedFlightDetails:{arrival:e.flightArrivalDetails?this.processFlightDetailsForSubmission(e.flightArrivalDetails):void 0,departure:e.flightDepartureDetails?this.processFlightDetailsForSubmission(e.flightDepartureDetails):void 0}}}processFlightDetailsForSubmission(e){var t,s,a,r;return{flightNumber:(null==(t=e.flightNumber)?void 0:t.trim())||"",dateTime:e.dateTime||"",airport:(null==(s=e.airport)?void 0:s.trim())||"",terminal:(null==(a=e.terminal)?void 0:a.trim())||void 0,notes:(null==(r=e.notes)?void 0:r.trim())||void 0,formattedDateTime:e.dateTime?new Date(e.dateTime).toISOString():void 0,airportCode:this.extractAirportCode(e.airport)}}extractAirportCode(e){if(!e)return;let t=e.match(/\(([A-Z]{3})\)|([A-Z]{3})$/);return t?t[1]||t[2]:void 0}transformInitialData(e){return{}}calculateDuration(e,t){try{let s=new Date(e),a=new Date(t);if(isNaN(s.getTime())||isNaN(a.getTime()))return 0;let r=Math.abs(a.getTime()-s.getTime());return Math.ceil(r/864e5)}catch(e){return 0}}validateDateRange(e,t){try{let s=new Date(e),a=new Date(t);if(isNaN(s.getTime())||isNaN(a.getTime()))return!1;return s<=a}catch(e){return!1}}createDefaultDelegate(){return{name:"",title:"",notes:""}}validateDelegates(e){let t=[];return e&&0!==e.length?(e.forEach((s,a)=>{s.name&&""!==s.name.trim()||t.push({field:"delegates.".concat(a,".name"),message:"Delegate name is required",code:"REQUIRED_FIELD"}),s.title&&""!==s.title.trim()||t.push({field:"delegates.".concat(a,".title"),message:"Delegate title is required",code:"REQUIRED_FIELD"}),-1!==e.findIndex((e,t)=>t!==a&&e.name.trim().toLowerCase()===s.name.trim().toLowerCase())&&t.push({field:"delegates.".concat(a,".name"),message:"Delegate names must be unique",code:"DUPLICATE_VALUE"})}),{isValid:0===t.length,errors:t}):(t.push({field:"delegates",message:"At least one delegate is required",code:"REQUIRED_FIELD"}),{isValid:!1,errors:t})}getAvailableStatuses(){return[]}validateStatusTransition(e,t){return!0}}let eC=new eS,eT=[{icon:(0,a.jsx)(n.A,{className:"size-4"}),id:"basic",isComplete:e=>!!(e.eventName&&e.location&&e.durationFrom&&e.durationTo),name:"Event Details",required:!0},{icon:(0,a.jsx)(o.A,{className:"size-4"}),id:"delegates",isComplete:e=>!!(e.delegates&&e.delegates.length>0&&e.delegates.every(e=>e.name&&e.title)),name:"Delegates",required:!0},{icon:(0,a.jsx)(o.A,{className:"size-4"}),id:"assignment",isComplete:e=>!!(e.driverEmployeeIds&&e.driverEmployeeIds.length>0||e.escortEmployeeIds&&e.escortEmployeeIds.length>0),name:"Team Assignment",required:!1},{icon:(0,a.jsx)(d.A,{className:"size-4"}),id:"logistics",isComplete:e=>{var t,s;return!!(e.vehicleIds&&e.vehicleIds.length>0||(null==(t=e.flightArrivalDetails)?void 0:t.flightNumber)||(null==(s=e.flightDepartureDetails)?void 0:s.flightNumber))},name:"Travel & Logistics",required:!1},{icon:(0,a.jsx)(c.A,{className:"size-4"}),id:"notes",isComplete:e=>{var t,s,a,r;return!!((null==(t=e.notes)?void 0:t.trim())||(null==(s=e.invitationFrom)?void 0:s.trim())||(null==(a=e.invitationTo)?void 0:a.trim())||(null==(r=e.imageUrl)?void 0:r.trim()))},name:"Additional Information",required:!1}],ek=e=>e||{delegates:[{name:"",notes:"",title:""}],driverEmployeeIds:[],durationFrom:"",durationTo:"",escortEmployeeIds:[],eventName:"",flightArrivalDetails:null,flightDepartureDetails:null,imageUrl:"",invitationFrom:"",invitationTo:"",location:"",notes:"",status:"Planned",vehicleIds:[]},eI=e=>{let t=eT.filter(t=>t.isComplete(e)),s=eT.filter(e=>e.required),a=s.filter(t=>t.isComplete(e)),r=a.length/s.length*70,i=(t.length-a.length)/(eT.length-s.length)*30,l=Math.round(r+i);return console.log("Progress Calculation:",{optionalProgress:i,requiredProgress:r,totalProgress:l}),l},eR=(e,t)=>t?Object.keys(e).length>0?"invalid":"valid":"idle",eF=e=>{let{employees:t=[],initialData:s,isEditing:r=!1,onSubmit:o,userRole:d="user",vehicles:y=[]}=e,w=(0,x.useRouter)(),[A,D]=(0,f.useState)(!1),E=ek(s),S=(0,p.mN)({defaultValues:E,mode:"onChange",resolver:(0,l.u)(q)}),{formState:{errors:C,isDirty:T,isValid:F},watch:z}=S,P=z(),V=eI(P),M=eR(C,T),{ariaAttributes:U,clearError:L,error:_,handleSubmit:O,isLoading:Y,retry:W,state:J}=(0,R.k)(async e=>{if(!await S.trigger()){D(!0);let e=Object.keys(S.formState.errors)[0];throw e&&S.setFocus(e),Error("Please complete all required fields correctly")}let t=eC.validateFormData(e);if(!t.isValid)throw Error(t.errors.map(e=>e.message).join(", "));await o(e)},{accessibility:{announceStatus:!0,focusManagement:"first-error"},formFocus:e=>S.setFocus(e),formReset:S.reset,formValidate:()=>S.trigger(),performance:{debounceMs:500,timeoutMs:45e3},preSubmitValidation:async e=>{let t=await S.trigger();if(!t){D(!0);let e=Object.keys(S.formState.errors)[0];e&&S.setFocus(e)}return t},retry:{exponentialBackoff:!0,maxAttempts:3,retryCondition:e=>e.message.includes("network")||e.message.includes("timeout")||e.message.includes("fetch")},toast:{entity:e=>({eventName:e.eventName,location:e.location}),entityType:"delegation"},transformData:e=>{var t,s,a,r;return{...e,delegates:(null==(t=e.delegates)?void 0:t.filter(e=>{var t,s;return(null==(t=e.name)?void 0:t.trim())&&(null==(s=e.title)?void 0:s.trim())}))||[],driverEmployeeIds:(null==(s=e.driverEmployeeIds)?void 0:s.filter(e=>!isNaN(e)&&e>0))||[],escortEmployeeIds:(null==(a=e.escortEmployeeIds)?void 0:a.filter(e=>!isNaN(e)&&e>0))||[],vehicleIds:(null==(r=e.vehicleIds)?void 0:r.filter(e=>!isNaN(e)&&e>0))||[]}}});return(0,a.jsx)(p.Op,{...S,children:(0,a.jsxs)("form",{onSubmit:S.handleSubmit(e=>{var t,s,a,r;"error"===J&&L(),O({...e,delegates:(null==(t=e.delegates)?void 0:t.filter(e=>{var t,s;return(null==(t=e.name)?void 0:t.trim())&&(null==(s=e.title)?void 0:s.trim())}))||[],driverEmployeeIds:(null==(s=e.driverEmployeeIds)?void 0:s.filter(e=>!isNaN(e)&&e>0))||[],escortEmployeeIds:(null==(a=e.escortEmployeeIds)?void 0:a.filter(e=>!isNaN(e)&&e>0))||[],vehicleIds:(null==(r=e.vehicleIds)?void 0:r.filter(e=>!isNaN(e)&&e>0))||[]})}),...U,className:"jsx-b6970b4a3b531b80 "+(U&&null!=U.className&&U.className||""),children:[(0,a.jsx)(i(),{id:"b6970b4a3b531b80",children:".field-valid.jsx-b6970b4a3b531b80 input.jsx-b6970b4a3b531b80,.field-valid.jsx-b6970b4a3b531b80 select.jsx-b6970b4a3b531b80,.field-valid.jsx-b6970b4a3b531b80 textarea.jsx-b6970b4a3b531b80{border-color:rgb(34,197,94);-webkit-box-shadow:0 0 0 1px rgba(34,197,94,.1);-moz-box-shadow:0 0 0 1px rgba(34,197,94,.1);box-shadow:0 0 0 1px rgba(34,197,94,.1)}.field-invalid.jsx-b6970b4a3b531b80 input.jsx-b6970b4a3b531b80,.field-invalid.jsx-b6970b4a3b531b80 select.jsx-b6970b4a3b531b80,.field-invalid.jsx-b6970b4a3b531b80 textarea.jsx-b6970b4a3b531b80{border-color:rgb(239,68,68);-webkit-box-shadow:0 0 0 1px rgba(239,68,68,.1);-moz-box-shadow:0 0 0 1px rgba(239,68,68,.1);box-shadow:0 0 0 1px rgba(239,68,68,.1)}.field-pending.jsx-b6970b4a3b531b80 input.jsx-b6970b4a3b531b80,.field-pending.jsx-b6970b4a3b531b80 select.jsx-b6970b4a3b531b80,.field-pending.jsx-b6970b4a3b531b80 textarea.jsx-b6970b4a3b531b80{border-color:rgb(59,130,246);-webkit-box-shadow:0 0 0 1px rgba(59,130,246,.1);-moz-box-shadow:0 0 0 1px rgba(59,130,246,.1);box-shadow:0 0 0 1px rgba(59,130,246,.1)}"}),(0,a.jsxs)("div",{className:"jsx-b6970b4a3b531b80 space-y-6",children:[(0,a.jsx)(N.Zp,{children:(0,a.jsxs)(N.aR,{children:[(0,a.jsxs)("div",{className:"jsx-b6970b4a3b531b80 flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"jsx-b6970b4a3b531b80",children:[(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(n.A,{className:"size-5 text-accent"}),r?"Edit Delegation":"Create New Delegation"]}),(0,a.jsx)("p",{className:"jsx-b6970b4a3b531b80 mt-1 text-sm text-muted-foreground",children:r?"Update delegation details and assignments":"Create a new delegation with responsibilities and team assignments"})]}),(0,a.jsxs)("div",{className:"jsx-b6970b4a3b531b80 flex items-center gap-2",children:[(0,a.jsxs)(v.E,{variant:"valid"===M?"default":"invalid"===M?"destructive":"validating"===M?"secondary":"outline",children:["valid"===M&&(0,a.jsx)(m.A,{className:"mr-1 size-3"}),"invalid"===M&&(0,a.jsx)(u.A,{className:"mr-1 size-3"}),"validating"===M&&(0,a.jsx)(c.A,{className:"mr-1 size-3 animate-pulse"}),"idle"===M&&"Not Started","valid"===M&&"All Valid","invalid"===M&&"Has Errors","validating"===M&&"Validating..."]}),Object.keys(C).length>0&&(0,a.jsxs)(v.E,{className:"text-xs",variant:"destructive",children:[Object.keys(C).length," error",Object.keys(C).length>1?"s":""]})]})]}),(0,a.jsxs)("div",{className:"jsx-b6970b4a3b531b80 mt-4",children:[(0,a.jsxs)("div",{className:"jsx-b6970b4a3b531b80 mb-2 flex items-center justify-between text-sm",children:[(0,a.jsx)("span",{className:"jsx-b6970b4a3b531b80 text-muted-foreground",children:"Form Progress"}),(0,a.jsxs)("span",{className:"jsx-b6970b4a3b531b80 font-medium",children:[V,"% Complete"]})]}),(0,a.jsx)(I.k,{className:"h-2",value:V})]}),(0,a.jsx)("div",{className:"jsx-b6970b4a3b531b80 mt-4 grid grid-cols-2 gap-2 md:grid-cols-5",children:eT.map(e=>{let t=e.isComplete(P);return(0,a.jsxs)("div",{className:"jsx-b6970b4a3b531b80 "+"flex items-center gap-2 rounded-lg border p-2 transition-colors ".concat(t?"border-green-200 bg-green-50 text-green-700 dark:border-green-800 dark:bg-green-900/20 dark:text-green-400":e.required?"border-amber-200 bg-amber-50 text-amber-700 dark:border-amber-800 dark:bg-amber-900/20 dark:text-amber-400":"border-border bg-muted text-muted-foreground"),children:[e.icon,(0,a.jsx)("span",{className:"jsx-b6970b4a3b531b80 text-xs font-medium",children:e.name}),t&&(0,a.jsx)(m.A,{className:"ml-auto size-3"}),!t&&e.required&&(0,a.jsx)(u.A,{className:"ml-auto size-3"})]},e.id)})})]})}),(0,a.jsx)(k,{onDismiss:()=>D(!1),show:A,showFieldNavigation:!0,showSuccess:!1,showWarnings:!0}),(0,a.jsxs)(N.Zp,{className:"shadow-lg",children:[(0,a.jsxs)(N.Wu,{className:"space-y-8 pt-6",children:[(0,a.jsx)(Q,{isSubmitting:Y,userRole:d}),(0,a.jsx)(X,{isSubmitting:Y,userRole:d}),(0,a.jsx)(ed,{employees:t,isSubmitting:Y,userRole:d,vehicles:y}),(0,a.jsx)(eD,{isSubmitting:Y,userRole:d}),(0,a.jsx)(eE,{isSubmitting:Y,userRole:d}),_&&(0,a.jsxs)(b.Fc,{variant:"destructive",children:[(0,a.jsx)(u.A,{className:"size-4"}),(0,a.jsx)(b.TN,{children:(0,a.jsxs)("div",{className:"jsx-b6970b4a3b531b80 flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"jsx-b6970b4a3b531b80",children:[(0,a.jsx)("div",{className:"jsx-b6970b4a3b531b80 font-medium",children:"Submission Error"}),(0,a.jsx)("div",{className:"jsx-b6970b4a3b531b80 mt-1 text-sm",children:_})]}),"error"===J&&(0,a.jsx)(j.$,{onClick:W,size:"sm",type:"button",variant:"outline",children:"Retry"})]})})]}),"validating"===J&&(0,a.jsxs)(b.Fc,{children:[(0,a.jsx)(c.A,{className:"size-4"}),(0,a.jsx)(b.TN,{children:"Validating form data..."})]}),"retrying"===J&&(0,a.jsxs)(b.Fc,{children:[(0,a.jsx)(c.A,{className:"size-4"}),(0,a.jsx)(b.TN,{children:"Retrying submission..."})]})]}),(0,a.jsxs)(N.wL,{className:"flex justify-between gap-4 border-t pt-6",children:[(0,a.jsxs)("div",{className:"jsx-b6970b4a3b531b80 flex gap-2",children:[(0,a.jsxs)(j.$,{className:"min-w-[100px]",disabled:Y,onClick:()=>w.back(),type:"button",variant:"outline",children:[(0,a.jsx)(g.A,{className:"mr-2 size-4"}),"Cancel"]}),!1]}),(0,a.jsxs)("div",{className:"jsx-b6970b4a3b531b80 flex items-center gap-4",children:[V<100&&(0,a.jsx)("div",{className:"jsx-b6970b4a3b531b80 text-sm text-muted-foreground",children:"Complete required sections to enable submission"}),(0,a.jsxs)(j.$,{className:"min-w-[140px] bg-accent text-accent-foreground hover:bg-accent/90",disabled:Y||V<35||!F,type:"submit",children:[(0,a.jsx)(h.A,{className:"mr-2 size-4"}),Y?"Saving...":r?"Save Changes":"Create Delegation"]})]})]})]})]})]})})}},68801:(e,t,s)=>{s.d(t,{z:()=>d});var a=s(95155);s(12115);var r=s(62177),i=s(17759),l=s(62523),n=s(88539),o=s(59409);let d=e=>{let{className:t="",disabled:s=!1,label:d,name:c,placeholder:m,render:u,type:g="text",options:h=[],defaultValue:x,icon:f,...p}=e,{control:b}=(0,r.xW)();return(0,a.jsxs)(i.eI,{className:t,children:[(0,a.jsx)(i.lR,{htmlFor:c,children:d}),(0,a.jsx)(r.xI,{control:b,name:c,render:u||(e=>{var t,r;let{field:u,fieldState:{error:b}}=e;return(0,a.jsx)(i.MJ,{children:"select"===g?(0,a.jsxs)(o.l6,{onValueChange:u.onChange,value:u.value||x||"",disabled:s,children:[(0,a.jsx)(o.bq,{className:b?"border-red-500":"",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[f&&(0,a.jsx)(f,{className:"h-4 w-4 text-gray-500"}),(0,a.jsx)(o.yv,{placeholder:m||"Select ".concat(d.toLowerCase())})]})}),(0,a.jsx)(o.gC,{children:h.map(e=>(0,a.jsx)(o.eb,{value:String(e.value),disabled:e.disabled||!1,children:e.label},e.value))})]}):"textarea"===g?(0,a.jsxs)("div",{className:"relative",children:[f&&(0,a.jsx)(f,{className:"absolute left-3 top-3 h-4 w-4 text-gray-500"}),(0,a.jsx)(n.T,{...u,...p,value:null!=(t=u.value)?t:"",className:"".concat(b?"border-red-500":""," ").concat(f?"pl-10":""),disabled:s,id:c,placeholder:m})]}):(0,a.jsxs)("div",{className:"relative",children:[f&&(0,a.jsx)(f,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500"}),(0,a.jsx)(l.p,{...u,...p,value:null!=(r=u.value)?r:"",className:"".concat(b?"border-red-500":""," ").concat(f?"pl-10":""),disabled:s,id:c,placeholder:m,type:g})]})})})}),(0,a.jsx)(i.C5,{})]})}},80937:(e,t,s)=>{s.d(t,{NS:()=>h,T$:()=>c,W_:()=>m,Y1:()=>u,lR:()=>g});var a=s(26715),r=s(5041),i=s(90111),l=s(42366),n=s(99605),o=s(75908);let d={all:["vehicles"],detail:e=>["vehicles",e]},c=e=>(0,i.GK)([...d.all],async()=>(await o.vehicleApiService.getAll()).data,"vehicle",{staleTime:3e5,...e}),m=(e,t)=>{var s;return(0,i.GK)([...d.detail(e)],()=>o.vehicleApiService.getById(e),"vehicle",{enabled:!!e&&(null==(s=null==t?void 0:t.enabled)||s),staleTime:3e5,...t})},u=()=>{let e=(0,a.jE)(),{showError:t,showSuccess:s}=(0,l.useNotifications)();return(0,r.n)({mutationFn:e=>{let t=n.M.toCreateRequest(e);return o.vehicleApiService.create(t)},onError:e=>{t("Failed to create vehicle: ".concat(e.message||"Unknown error occurred"))},onSuccess:t=>{e.invalidateQueries({queryKey:d.all}),s('Vehicle "'.concat(t.licensePlate,'" has been created successfully!'))}})},g=()=>{let e=(0,a.jE)(),{showError:t,showSuccess:s}=(0,l.useNotifications)();return(0,r.n)({mutationFn:e=>{let{data:t,id:s}=e,a=n.M.toUpdateRequest(t);return o.vehicleApiService.update(s,a)},onError:e=>{t("Failed to update vehicle: ".concat(e.message||"Unknown error occurred"))},onSuccess:t=>{e.invalidateQueries({queryKey:d.all}),e.invalidateQueries({queryKey:d.detail(t.id)}),s('Vehicle "'.concat(t.licensePlate,'" has been updated successfully!'))}})},h=()=>{let e=(0,a.jE)(),{showError:t,showSuccess:s}=(0,l.useNotifications)();return(0,r.n)({mutationFn:e=>o.vehicleApiService.delete(e),onError:e=>{t("Failed to delete vehicle: ".concat(e.message||"Unknown error occurred"))},onSuccess:(t,a)=>{e.invalidateQueries({queryKey:d.all}),e.removeQueries({queryKey:d.detail(a)}),s("Vehicle has been deleted successfully!")}})}},85057:(e,t,s)=>{s.d(t,{J:()=>d});var a=s(95155),r=s(12115),i=s(40968),l=s(74466),n=s(54036);let o=(0,l.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(i.b,{ref:t,className:(0,n.cn)(o(),s),...r})});d.displayName=i.b.displayName},85511:(e,t,s)=>{s.d(t,{V:()=>d});var a=s(95155),r=s(965),i=s(73158);s(12115);var l=s(33683),n=s(30285),o=s(54036);function d(e){let{className:t,classNames:s,showOutsideDays:d=!0,...c}=e;return(0,a.jsx)(l.hv,{className:(0,o.cn)("p-3",t),classNames:{caption:"flex justify-center pt-1 relative items-center",caption_label:"text-sm font-medium",cell:"h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",day:(0,o.cn)((0,n.r)({variant:"ghost"}),"h-9 w-9 p-0 font-normal aria-selected:opacity-100"),day_disabled:"text-muted-foreground opacity-50",day_hidden:"invisible",day_outside:"day-outside text-muted-foreground aria-selected:bg-accent/50 aria-selected:text-muted-foreground",day_range_end:"day-range-end",day_range_middle:"aria-selected:bg-accent aria-selected:text-accent-foreground",day_selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",day_today:"bg-accent text-accent-foreground",head_cell:"text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]",head_row:"flex",month:"space-y-4",months:"flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",nav:"space-x-1 flex items-center",nav_button:(0,o.cn)((0,n.r)({variant:"outline"}),"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"),nav_button_next:"absolute right-1",nav_button_previous:"absolute left-1",row:"flex w-full mt-2",table:"w-full border-collapse space-y-1",...s},components:{IconLeft:e=>{let{className:t,...s}=e;return(0,a.jsx)(r.A,{className:(0,o.cn)("h-4 w-4",t),...s})},IconRight:e=>{let{className:t,...s}=e;return(0,a.jsx)(i.A,{className:(0,o.cn)("h-4 w-4",t),...s})}},showOutsideDays:d,...c})}d.displayName="Calendar"},88539:(e,t,s)=>{s.d(t,{T:()=>l});var a=s(95155),r=s(12115),i=s(54036);let l=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("textarea",{className:(0,i.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",s),ref:t,...r})});l.displayName="Textarea"},94141:(e,t,s)=>{s.d(t,{SY:()=>n});let a={maxAttempts:3,delay:1e3,exponentialBackoff:!0,retryCondition:e=>e.message.includes("network")||e.message.includes("timeout")||e.message.includes("502")||e.message.includes("503")||e.message.includes("504")},r={announceStatus:!0,focusManagement:"first-error",screenReaderAnnouncements:!0},i={debounceMs:300,enableDeduplication:!0,cacheResults:!1,timeoutMs:3e4},l={showSuccessToast:!0,showErrorToast:!0,successMessage:"Operation completed successfully",errorMessage:"An unexpected error occurred",entityType:"generic"};class n{static mergeRetryConfig(e){return{...a,...e}}static mergeAccessibilityConfig(e){return{...r,...e}}static mergePerformanceConfig(e){return{...i,...e}}static mergeToastConfig(e){return{...l,...e}}}},95647:(e,t,s)=>{s.d(t,{z:()=>r});var a=s(95155);function r(e){let{children:t,description:s,icon:r,title:i}=e;return(0,a.jsxs)("div",{className:"mb-6 flex items-center justify-between border-b border-border/50 pb-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[r&&(0,a.jsx)(r,{className:"size-8 text-primary"}),(0,a.jsx)("h1",{className:"text-3xl font-bold tracking-tight text-foreground",children:i})]}),s&&(0,a.jsx)("p",{className:"mt-1 text-muted-foreground",children:s})]}),t&&(0,a.jsx)("div",{className:"flex items-center gap-2",children:t})]})}s(12115)},99673:(e,t,s)=>{function a(e){switch(e){case"In_Progress":return"In Progress";case"No_details":return"No Details";default:return e}}function r(e){var t,s;if(null==(t=e.fullName)?void 0:t.trim())return e.fullName.trim();if(null==(s=e.name)?void 0:s.trim()){let t=e.name.trim();if(["office_staff","service_advisor","administrator","mechanic","driver","manager","technician","other"].includes(t.toLowerCase())||t.includes("_")){let e=t.replaceAll("_"," ").replaceAll(/\b\w/g,e=>e.toUpperCase());return"".concat(e," (Role)")}return t}if(e.role){let t=e.role.replaceAll("_"," ").replaceAll(/\b\w/g,e=>e.toUpperCase());return"".concat(t," (Role)")}return"Unknown Employee"}function i(e){return e.replaceAll("_"," ").replaceAll(/\b\w/g,e=>e.toUpperCase())}function l(e){return e.replaceAll("_"," ")}s.d(t,{DV:()=>r,fZ:()=>a,s:()=>i,vq:()=>l})}}]);