/**
 * @file Sidebar management hook using Zustand AppStore
 * @module hooks/useSidebar
 */

import { useCallback } from 'react';

import { useAppStore } from '@/lib/stores/zustand/appStore';

/**
 * Hook for sidebar management
 * Provides convenient methods for sidebar state control
 */
export const useSidebar = () => {
  const sidebarOpen = useAppStore(state => state.sidebarOpen);
  const toggleSidebar = useAppStore(state => state.toggleSidebar);

  /**
   * Open the sidebar if it's closed
   */
  const openSidebar = useCallback(() => {
    if (!sidebarOpen) {
      toggleSidebar();
    }
  }, [sidebarOpen, toggleSidebar]);

  /**
   * Close the sidebar if it's open
   */
  const closeSidebar = useCallback(() => {
    if (sidebarOpen) {
      toggleSidebar();
    }
  }, [sidebarOpen, toggleSidebar]);

  /**
   * Get sidebar-specific CSS classes
   */
  const getSidebarClasses = useCallback(() => {
    return {
      content: sidebarOpen ? 'content-shifted' : 'content-normal',
      overlay: sidebarOpen ? 'overlay-visible' : 'overlay-hidden',
      sidebar: sidebarOpen ? 'sidebar-open' : 'sidebar-closed',
      toggle: sidebarOpen ? 'toggle-close' : 'toggle-open',
    };
  }, [sidebarOpen]);

  /**
   * Get sidebar state for accessibility
   */
  const getAriaAttributes = useCallback(() => {
    return {
      'aria-expanded': sidebarOpen,
      'aria-label': sidebarOpen ? 'Close sidebar' : 'Open sidebar',
    };
  }, [sidebarOpen]);

  return {
    closeSidebar,
    getAriaAttributes,
    // Utilities
    getSidebarClasses,

    isClosed: !sidebarOpen,
    isOpen: sidebarOpen,
    openSidebar,

    // State
    sidebarOpen,
    // Actions
    toggleSidebar,
  };
};
