// frontend/src/components/features/reporting/tables/DelegationReportTable.tsx

import React, { useMemo } from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ReportingDataTable } from './ReportingDataTable';
import { format } from 'date-fns';
import { Eye, Edit, MoreHorizontal } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

// FIXED: Import centralized types instead of defining duplicate interface
import { DelegationReportData } from '../data/types/reporting';

/**
 * Props for DelegationReportTable component
 */
interface DelegationReportTableProps {
  data: DelegationReportData[];
  loading?: boolean;
  error?: string | undefined; // FIXED: Allow undefined for strict mode
  onViewDelegation?: (delegation: DelegationReportData) => void;
  onEditDelegation?: (delegation: DelegationReportData) => void;
  onExport?: (data: DelegationReportData[]) => void;
  className?: string;
}

/**
 * Status badge component with color coding
 */
const StatusBadge = ({
  status,
}: {
  status: DelegationReportData['status'];
}) => {
  // FIXED: Use correct Prisma enum values
  const statusConfig = {
    Planned: { variant: 'secondary' as const, label: 'Planned' },
    Confirmed: { variant: 'default' as const, label: 'Confirmed' },
    In_Progress: { variant: 'default' as const, label: 'In Progress' },
    Completed: { variant: 'default' as const, label: 'Completed' },
    Cancelled: { variant: 'outline' as const, label: 'Cancelled' },
    No_details: { variant: 'secondary' as const, label: 'No Details' },
  };

  const config = statusConfig[status];
  return <Badge variant={config.variant}>{config.label}</Badge>;
};

/**
 * Priority badge component with color coding
 */
const PriorityBadge = ({
  priority,
}: {
  priority: DelegationReportData['priority'];
}) => {
  // FIXED: Use correct Prisma enum values
  const priorityConfig = {
    Low: {
      variant: 'outline' as const,
      label: 'Low',
      className: 'text-green-600 border-green-600',
    },
    Medium: {
      variant: 'outline' as const,
      label: 'Medium',
      className: 'text-yellow-600 border-yellow-600',
    },
    High: {
      variant: 'outline' as const,
      label: 'High',
      className: 'text-orange-600 border-orange-600',
    },
  };

  if (!priority) {
    return (
      <Badge variant="outline" className="text-gray-600 border-gray-600">
        -
      </Badge>
    );
  }

  const config = priorityConfig[priority];
  if (!config) {
    return (
      <Badge variant="outline" className="text-gray-600 border-gray-600">
        {priority}
      </Badge>
    );
  }

  return (
    <Badge variant={config.variant} className={config.className}>
      {config.label}
    </Badge>
  );
};

/**
 * Progress bar component
 */
const ProgressBar = ({ progress }: { progress: number }) => (
  <div className="flex items-center gap-2">
    <div className="w-16 bg-gray-200 rounded-full h-2">
      <div
        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
        style={{ width: `${Math.min(progress, 100)}%` }}
      />
    </div>
    <span className="text-xs text-gray-600 min-w-[3rem]">{progress}%</span>
  </div>
);

/**
 * Actions dropdown component
 */
const ActionsDropdown = ({
  delegation,
  onView,
  onEdit,
}: {
  delegation: DelegationReportData;
  onView?: ((delegation: DelegationReportData) => void) | undefined;
  onEdit?: ((delegation: DelegationReportData) => void) | undefined;
}) => (
  <DropdownMenu>
    <DropdownMenuTrigger asChild>
      <Button variant="ghost" className="h-8 w-8 p-0">
        <MoreHorizontal className="h-4 w-4" />
      </Button>
    </DropdownMenuTrigger>
    <DropdownMenuContent align="end">
      {onView && (
        <DropdownMenuItem onClick={() => onView(delegation)}>
          <Eye className="mr-2 h-4 w-4" />
          View Details
        </DropdownMenuItem>
      )}
      {onEdit && (
        <DropdownMenuItem onClick={() => onEdit(delegation)}>
          <Edit className="mr-2 h-4 w-4" />
          Edit
        </DropdownMenuItem>
      )}
    </DropdownMenuContent>
  </DropdownMenu>
);

/**
 * DelegationReportTable Component
 *
 * A specialized data table for delegation reporting data.
 * Extends the base ReportingDataTable with delegation-specific features.
 *
 * Features:
 * - Status and priority badges
 * - Progress visualization
 * - Date formatting
 * - Action buttons
 * - Export functionality
 *
 * @param props - Component props
 * @returns JSX element
 */
export const DelegationReportTable: React.FC<DelegationReportTableProps> = ({
  data,
  loading = false,
  error,
  onViewDelegation,
  onEditDelegation,
  onExport,
  className = '',
}) => {
  // Define table columns
  const columns = useMemo<ColumnDef<DelegationReportData>[]>(
    () => [
      {
        accessorKey: 'title',
        header: 'Title',
        cell: ({ row }) => (
          <div
            className="font-medium max-w-[200px] truncate"
            title={row.getValue('title')}
          >
            {row.getValue('title')}
          </div>
        ),
      },
      {
        accessorKey: 'status',
        header: 'Status',
        cell: ({ row }) => <StatusBadge status={row.getValue('status')} />,
        filterFn: 'equals',
      },
      {
        accessorKey: 'priority',
        header: 'Priority',
        cell: ({ row }) => (
          <PriorityBadge priority={row.getValue('priority')} />
        ),
        filterFn: 'equals',
      },
      {
        accessorKey: 'assignedTo',
        header: 'Assigned To',
        cell: ({ row }) => (
          <div
            className="max-w-[150px] truncate"
            title={row.getValue('assignedTo')}
          >
            {row.getValue('assignedTo')}
          </div>
        ),
      },
      {
        accessorKey: 'location',
        header: 'Location',
        cell: ({ row }) => (
          <div
            className="max-w-[120px] truncate"
            title={row.getValue('location')}
          >
            {row.getValue('location')}
          </div>
        ),
      },
      {
        accessorKey: 'progress',
        header: 'Progress',
        cell: ({ row }) => <ProgressBar progress={row.getValue('progress')} />,
        sortingFn: 'basic',
      },
      {
        accessorKey: 'createdAt',
        header: 'Created',
        cell: ({ row }) => {
          const date = new Date(row.getValue('createdAt'));
          return <div className="text-sm">{format(date, 'MMM dd, yyyy')}</div>;
        },
        sortingFn: 'datetime',
      },
      {
        accessorKey: 'dueDate',
        header: 'Due Date',
        cell: ({ row }) => {
          const date = new Date(row.getValue('dueDate'));
          // FIXED: Use correct Prisma enum value
          const isOverdue =
            date < new Date() && row.original.status !== 'Completed';
          return (
            <div
              className={`text-sm ${isOverdue ? 'text-red-600 font-medium' : ''}`}
            >
              {format(date, 'MMM dd, yyyy')}
            </div>
          );
        },
        sortingFn: 'datetime',
      },
      {
        accessorKey: 'estimatedHours',
        header: 'Est. Hours',
        cell: ({ row }) => (
          <div className="text-sm text-center">
            {row.getValue('estimatedHours')}h
          </div>
        ),
      },
      {
        accessorKey: 'actualHours',
        header: 'Actual Hours',
        cell: ({ row }) => {
          const actualHours = row.getValue('actualHours') as number | undefined;
          return (
            <div className="text-sm text-center">
              {actualHours ? `${actualHours}h` : '-'}
            </div>
          );
        },
      },
      {
        id: 'actions',
        header: 'Actions',
        cell: ({ row }) => (
          <ActionsDropdown
            delegation={row.original}
            onView={onViewDelegation}
            onEdit={onEditDelegation}
          />
        ),
        enableSorting: false,
        enableHiding: false,
      },
    ],
    [onViewDelegation, onEditDelegation]
  );

  return (
    <ReportingDataTable
      data={data}
      columns={columns}
      title="Delegation Report"
      description="Comprehensive view of all delegations with status, progress, and performance metrics"
      loading={loading}
      error={error}
      searchable={true}
      filterable={true}
      exportable={true}
      pagination={true}
      pageSize={10}
      className={className}
      onRowClick={onViewDelegation}
      onExport={onExport}
    />
  );
};
