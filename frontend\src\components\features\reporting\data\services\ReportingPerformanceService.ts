/**
 * @service ReportingPerformanceService
 * @description Handles performance optimization for the reporting system
 *
 * Responsibilities:
 * - Manage data caching and invalidation
 * - Implement lazy loading strategies
 * - Optimize API queries and data fetching
 * - Handle memory management and cleanup
 *
 * SOLID Principles Applied:
 * - SRP: Single responsibility of performance optimization
 * - OCP: Open for extension via caching strategies
 * - DIP: Depends on cache abstraction interfaces
 */

export interface CacheEntry<T = any> {
  data: T;
  timestamp: Date;
  expiresAt: Date;
  accessCount: number;
  lastAccessed: Date;
  size: number;
}

export interface CacheOptions {
  ttl?: number; // Time to live in milliseconds
  maxSize?: number; // Maximum cache size in bytes
  maxEntries?: number; // Maximum number of entries
  strategy?: 'lru' | 'lfu' | 'fifo'; // Cache eviction strategy
}

export interface QueryOptimizationOptions {
  batchSize?: number;
  debounceMs?: number;
  enablePagination?: boolean;
  enableVirtualization?: boolean;
  prefetchThreshold?: number;
}

export interface PerformanceMetrics {
  cacheHitRate: number;
  averageQueryTime: number;
  memoryUsage: number;
  activeQueries: number;
  totalQueries: number;
  errorRate: number;
}

export class ReportingPerformanceService {
  private cache: Map<string, CacheEntry> = new Map();
  private queryQueue: Map<string, Promise<any>> = new Map();
  private performanceMetrics: PerformanceMetrics = {
    cacheHitRate: 0,
    averageQueryTime: 0,
    memoryUsage: 0,
    activeQueries: 0,
    totalQueries: 0,
    errorRate: 0,
  };

  private readonly defaultCacheOptions: Required<CacheOptions> = {
    ttl: 5 * 60 * 1000, // 5 minutes
    maxSize: 50 * 1024 * 1024, // 50MB
    maxEntries: 1000,
    strategy: 'lru',
  };

  private readonly defaultQueryOptions: Required<QueryOptimizationOptions> = {
    batchSize: 100,
    debounceMs: 300,
    enablePagination: true,
    enableVirtualization: true,
    prefetchThreshold: 0.8,
  };

  private cacheHits = 0;
  private cacheMisses = 0;
  private queryTimes: number[] = [];
  private totalErrors = 0;

  constructor(
    private cacheOptions: CacheOptions = {},
    private queryOptions: QueryOptimizationOptions = {}
  ) {
    this.cacheOptions = { ...this.defaultCacheOptions, ...cacheOptions };
    this.queryOptions = { ...this.defaultQueryOptions, ...queryOptions };

    // Set up periodic cache cleanup
    this.setupCacheCleanup();

    // Set up performance monitoring
    this.setupPerformanceMonitoring();
  }

  /**
   * Get data from cache or fetch if not available
   */
  async getOrFetch<T>(
    key: string,
    fetchFn: () => Promise<T>,
    options?: Partial<CacheOptions>
  ): Promise<T> {
    const startTime = Date.now();

    try {
      // Check cache first
      const cached = this.getFromCache<T>(key);
      if (cached) {
        this.cacheHits++;
        this.recordQueryTime(Date.now() - startTime);
        return cached;
      }

      this.cacheMisses++;

      // Check if query is already in progress
      const existingQuery = this.queryQueue.get(key);
      if (existingQuery) {
        const result = await existingQuery;
        this.recordQueryTime(Date.now() - startTime);
        return result;
      }

      // Execute fetch function
      const queryPromise = this.executeFetch(fetchFn);
      this.queryQueue.set(key, queryPromise);
      this.performanceMetrics.activeQueries++;

      try {
        const result = await queryPromise;

        // Cache the result
        this.setCache(key, result, options);

        this.recordQueryTime(Date.now() - startTime);
        return result;
      } finally {
        this.queryQueue.delete(key);
        this.performanceMetrics.activeQueries--;
      }
    } catch (error) {
      this.totalErrors++;
      this.recordQueryTime(Date.now() - startTime);
      throw error;
    }
  }

  /**
   * Batch multiple queries for efficiency
   */
  async batchQueries<T>(
    queries: Array<{
      key: string;
      fetchFn: () => Promise<T>;
      options?: Partial<CacheOptions>;
    }>
  ): Promise<T[]> {
    const batchSize =
      this.queryOptions.batchSize || this.defaultQueryOptions.batchSize;
    const results: T[] = [];

    // Process queries in batches
    for (let i = 0; i < queries.length; i += batchSize) {
      const batch = queries.slice(i, i + batchSize);
      const batchPromises = batch.map(query =>
        this.getOrFetch(query.key, query.fetchFn, query.options)
      );

      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);
    }

    return results;
  }

  /**
   * Prefetch data based on usage patterns
   */
  async prefetchData(
    prefetchConfigs: Array<{
      key: string;
      fetchFn: () => Promise<any>;
      priority?: number;
    }>
  ): Promise<void> {
    // Sort by priority (higher priority first)
    const sortedConfigs = prefetchConfigs.sort(
      (a, b) => (b.priority || 0) - (a.priority || 0)
    );

    // Execute prefetch operations with low priority
    const prefetchPromises = sortedConfigs.map(async config => {
      try {
        // Only prefetch if not already cached
        if (!this.hasValidCache(config.key)) {
          await this.getOrFetch(config.key, config.fetchFn);
        }
      } catch (error) {
        // Silently fail prefetch operations
        console.warn(`Prefetch failed for key ${config.key}:`, error);
      }
    });

    // Execute prefetch operations without blocking
    Promise.all(prefetchPromises).catch(() => {
      // Ignore prefetch errors
    });
  }

  /**
   * Invalidate cache entries
   */
  invalidateCache(pattern?: string | RegExp): void {
    if (!pattern) {
      // Clear all cache
      this.cache.clear();
      return;
    }

    const keysToDelete: string[] = [];

    if (typeof pattern === 'string') {
      // Exact match or wildcard pattern
      if (pattern.includes('*')) {
        const regex = new RegExp(pattern.replace(/\*/g, '.*'));
        for (const key of this.cache.keys()) {
          if (regex.test(key)) {
            keysToDelete.push(key);
          }
        }
      } else {
        keysToDelete.push(pattern);
      }
    } else {
      // RegExp pattern
      for (const key of this.cache.keys()) {
        if (pattern.test(key)) {
          keysToDelete.push(key);
        }
      }
    }

    keysToDelete.forEach(key => this.cache.delete(key));
  }

  /**
   * Get performance metrics
   */
  getPerformanceMetrics(): PerformanceMetrics {
    const totalQueries = this.cacheHits + this.cacheMisses;

    return {
      ...this.performanceMetrics,
      cacheHitRate: totalQueries > 0 ? this.cacheHits / totalQueries : 0,
      averageQueryTime:
        this.queryTimes.length > 0
          ? this.queryTimes.reduce((sum, time) => sum + time, 0) /
            this.queryTimes.length
          : 0,
      memoryUsage: this.calculateMemoryUsage(),
      totalQueries,
      errorRate: totalQueries > 0 ? this.totalErrors / totalQueries : 0,
    };
  }

  /**
   * Optimize query execution with debouncing
   */
  debounceQuery<T>(
    key: string,
    fetchFn: () => Promise<T>,
    debounceMs?: number
  ): Promise<T> {
    const delay = debounceMs || this.queryOptions.debounceMs;

    return new Promise((resolve, reject) => {
      // Clear existing timeout for this key
      const existingTimeout = this.queryQueue.get(`debounce_${key}`);
      if (existingTimeout) {
        clearTimeout(existingTimeout as any);
      }

      // Set new timeout
      const timeout = setTimeout(async () => {
        try {
          const result = await this.getOrFetch(key, fetchFn);
          resolve(result);
        } catch (error) {
          reject(error);
        } finally {
          this.queryQueue.delete(`debounce_${key}`);
        }
      }, delay);

      this.queryQueue.set(`debounce_${key}`, timeout as any);
    });
  }

  /**
   * Private helper methods
   */
  private getFromCache<T>(key: string): T | null {
    const entry = this.cache.get(key);
    if (!entry) {
      return null;
    }

    // Check if expired
    if (Date.now() > entry.expiresAt.getTime()) {
      this.cache.delete(key);
      return null;
    }

    // Update access statistics
    entry.accessCount++;
    entry.lastAccessed = new Date();

    return entry.data as T;
  }

  private setCache<T>(
    key: string,
    data: T,
    options?: Partial<CacheOptions>
  ): void {
    const mergedOptions = { ...this.cacheOptions, ...options };
    const now = new Date();

    const entry: CacheEntry<T> = {
      data,
      timestamp: now,
      expiresAt: new Date(now.getTime() + mergedOptions.ttl!),
      accessCount: 1,
      lastAccessed: now,
      size: this.estimateSize(data),
    };

    // Check cache limits before adding
    this.enforceCacheLimits();

    this.cache.set(key, entry);
  }

  private hasValidCache(key: string): boolean {
    const entry = this.cache.get(key);
    return entry !== undefined && Date.now() <= entry.expiresAt.getTime();
  }

  private async executeFetch<T>(fetchFn: () => Promise<T>): Promise<T> {
    this.performanceMetrics.totalQueries++;
    return await fetchFn();
  }

  private recordQueryTime(time: number): void {
    this.queryTimes.push(time);

    // Keep only last 1000 query times for memory efficiency
    if (this.queryTimes.length > 1000) {
      this.queryTimes = this.queryTimes.slice(-1000);
    }
  }

  private calculateMemoryUsage(): number {
    let totalSize = 0;
    for (const entry of this.cache.values()) {
      totalSize += entry.size;
    }
    return totalSize;
  }

  private estimateSize(data: any): number {
    // Simple size estimation - in production, use more sophisticated methods
    try {
      return JSON.stringify(data).length * 2; // Rough estimate for UTF-16
    } catch {
      return 1024; // Default size if estimation fails
    }
  }

  private enforceCacheLimits(): void {
    const options = this.cacheOptions;

    // Check entry count limit
    const maxEntries =
      options.maxEntries || this.defaultCacheOptions.maxEntries;
    if (this.cache.size >= maxEntries) {
      this.evictEntries(Math.floor(maxEntries * 0.1)); // Remove 10%
    }

    // Check memory limit
    const currentMemory = this.calculateMemoryUsage();
    const maxSize = options.maxSize || this.defaultCacheOptions.maxSize;
    if (currentMemory >= maxSize) {
      this.evictEntriesBySize(maxSize * 0.8); // Target 80% of max size
    }
  }

  private evictEntries(count: number): void {
    const entries = Array.from(this.cache.entries());

    // Sort by eviction strategy
    switch (this.cacheOptions.strategy) {
      case 'lru':
        entries.sort(
          ([, a], [, b]) => a.lastAccessed.getTime() - b.lastAccessed.getTime()
        );
        break;
      case 'lfu':
        entries.sort(([, a], [, b]) => a.accessCount - b.accessCount);
        break;
      case 'fifo':
        entries.sort(
          ([, a], [, b]) => a.timestamp.getTime() - b.timestamp.getTime()
        );
        break;
    }

    // Remove oldest entries
    for (let i = 0; i < count && i < entries.length; i++) {
      this.cache.delete(entries[i]![0]);
    }
  }

  private evictEntriesBySize(targetSize: number): void {
    const entries = Array.from(this.cache.entries());
    entries.sort(([, a], [, b]) => b.size - a.size); // Largest first

    let currentSize = this.calculateMemoryUsage();

    for (const [key, entry] of entries) {
      if (currentSize <= targetSize) {
        break;
      }

      this.cache.delete(key);
      currentSize -= entry.size;
    }
  }

  private setupCacheCleanup(): void {
    // Clean up expired entries every 5 minutes
    setInterval(
      () => {
        const now = Date.now();
        const keysToDelete: string[] = [];

        for (const [key, entry] of this.cache.entries()) {
          if (now > entry.expiresAt.getTime()) {
            keysToDelete.push(key);
          }
        }

        keysToDelete.forEach(key => this.cache.delete(key));
      },
      5 * 60 * 1000
    );
  }

  private setupPerformanceMonitoring(): void {
    // Reset performance counters periodically
    setInterval(
      () => {
        // Keep running averages but reset counters
        this.cacheHits = Math.floor(this.cacheHits * 0.9);
        this.cacheMisses = Math.floor(this.cacheMisses * 0.9);
        this.totalErrors = Math.floor(this.totalErrors * 0.9);

        // Trim query times array
        if (this.queryTimes.length > 500) {
          this.queryTimes = this.queryTimes.slice(-500);
        }
      },
      10 * 60 * 1000
    ); // Every 10 minutes
  }
}

// Singleton instance
export const reportingPerformanceService = new ReportingPerformanceService();
