// frontend/src/app/reports/layout.tsx

import type { Metadata } from 'next';

import { AppBreadcrumb } from '@/components/ui/app-breadcrumb';

export const metadata: Metadata = {
  description: 'Comprehensive reporting and analytics dashboard',
  title: 'Reports - WorkHub',
};

interface ReportsLayoutProps {
  children: React.ReactNode;
}

/**
 * Reports Layout - Simplified Hierarchy
 *
 * Provides clean, focused layout for all reporting pages.
 * Removed redundant navigation cards per UX recommendations.
 */
export default function ReportsLayout({ children }: ReportsLayoutProps) {
  return (
    <div className="space-y-6">
      {/* Streamlined Header */}
      <div className="flex flex-col space-y-4">
        <AppBreadcrumb homeHref="/" homeLabel="Dashboard" />
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              Reports & Analytics
            </h1>
            <p className="text-muted-foreground mt-2">
              Comprehensive delegation analytics and reporting
            </p>
          </div>
        </div>
      </div>

      {/* Main Content - Direct access to dashboard */}
      <div className="min-h-[600px]">{children}</div>
    </div>
  );
}
