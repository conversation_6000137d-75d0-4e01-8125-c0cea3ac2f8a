'use client';

import * as React from 'react';
import { format } from 'date-fns';
import { User, Calendar } from 'lucide-react';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { statusConfigs } from '@/components/ui/tables/columnHelpers'; // Reusing status configs

import type { Task } from '@/lib/types/domain';

interface TaskCardViewProps {
  tasks: Task[];
  className?: string;
}

export function TaskCardView({ tasks, className }: TaskCardViewProps) {
  if (!tasks || tasks.length === 0) {
    return (
      <div className="text-center text-muted-foreground py-8">
        No tasks to display in card view.
      </div>
    );
  }

  return (
    <div className={cn('grid gap-4 md:grid-cols-2 lg:grid-cols-3', className)}>
      {tasks.map(task => {
        const statusConfig = statusConfigs.task[task.status] || {
          variant: 'secondary',
          label: task.status,
        };
        const formattedDueDate = task.deadline
          ? format(new Date(task.deadline), 'MMM dd, yyyy')
          : 'No due date';

        return (
          <Card key={task.id} className="flex flex-col">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold line-clamp-1">
                {task.description}
              </CardTitle>
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Badge variant={statusConfig.variant as any}>
                  {statusConfig.label}
                </Badge>
                {(task.staffEmployee || task.driverEmployee) && (
                  <span className="flex items-center gap-1">
                    <User className="size-3" />{' '}
                    {task.staffEmployee?.fullName ||
                      task.driverEmployee?.fullName}
                  </span>
                )}
              </div>
            </CardHeader>
            <CardContent className="flex-grow">
              {task.description && (
                <p className="text-sm text-muted-foreground line-clamp-2 mb-2">
                  {task.description}
                </p>
              )}
              <div className="flex items-center gap-1 text-sm text-muted-foreground">
                <Calendar className="size-3" />{' '}
                {task.deadline
                  ? format(new Date(task.deadline), 'MMM dd, yyyy')
                  : 'No due date'}
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}
