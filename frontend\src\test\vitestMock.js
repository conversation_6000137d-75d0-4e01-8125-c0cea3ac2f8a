/**
 * This file provides Jest equivalents for Vitest functions
 * to make tests compatible with both testing frameworks.
 */

// Create a more robust mocked function
const mockedFn = fn => {
  // If fn is already a Jest mock, return it as is.
  if (
    typeof fn === 'function' &&
    typeof fn.mockImplementation === 'function' &&
    fn.mock !== undefined
  ) {
    return fn;
  }

  // If fn is a regular function, create a Jest mock that wraps it.
  // jest.fn(implementation) creates a mock that calls the implementation.
  const mock = jest.fn(fn);
  return mock;
};

// Map Vitest functions to Jest equivalents
module.exports = {
  afterAll: globalThis.afterAll,
  afterEach: globalThis.afterEach,
  beforeAll: globalThis.beforeAll,
  beforeEach: globalThis.beforeEach,
  describe: globalThis.describe,
  expect: globalThis.expect,
  it: globalThis.it,
  test: globalThis.test,
  vi: {
    advanceTimersByTime: ms => jest.advanceTimersByTime(ms),
    clearAllMocks: jest.clearAllMocks,
    fn: jest.fn,
    mock: jest.mock,
    mocked: mockedFn,
    resetAllMocks: jest.resetAllMocks,
    restoreAllMocks: jest.restoreAllMocks,
    runAllTimers: () => jest.runAllTimers(),
    spyOn: jest.spyOn,
    useFakeTimers: () => jest.useFakeTimers(),
    useRealTimers: () => jest.useRealTimers(),
  },
};
