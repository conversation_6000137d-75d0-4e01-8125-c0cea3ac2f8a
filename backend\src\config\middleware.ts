import express, { Application } from 'express';
import morgan from 'morgan';
import swaggerUi from 'swagger-ui-express';
import swaggerSpec from '../swaggerConfig.js';
import {
  additionalSecurity,
  developmentSecurity,
  productionSecurity,
  securityHeaders,
} from '../middleware/security.js';
import { addRateLimitSecurityHeaders, globalRateLimit } from '../middleware/rateLimiting.js';
import { metricsMiddleware } from '../services/metrics.service.js';
import { createEnhancedCorsMiddleware } from '../middleware/cors.js';
import { auditMiddleware } from '../middleware/auditMiddleware.js';
import { logStartup } from '../utils/logger.js';

export const configureGlobalMiddleware = (app: Application): void => {
  logStartup('⚙️ Configuring global middleware...');

  // Disable x-powered-by header
  app.disable('x-powered-by');
  logStartup('🚫 Disabled x-powered-by header');

  // Security Middleware
  logStartup('🛡️ Applying security middleware (headers, CSP, etc.)...');
  app.use(securityHeaders);
  app.use(additionalSecurity);
  app.use(developmentSecurity);
  app.use(productionSecurity);
  logStartup('✅ Security middleware applied.');

  // Rate Limiting
  logStartup('⏱️ Applying rate limiting protection...');
  app.use(addRateLimitSecurityHeaders);
  app.use(globalRateLimit);
  logStartup('✅ Rate limiting protection applied.');

  // Metrics
  logStartup('📊 Applying metrics collection middleware...');
  app.use(metricsMiddleware());
  logStartup('✅ Metrics collection middleware applied.');

  // Audit Logging (Phase 3)
  logStartup('📋 Applying comprehensive audit logging middleware...');
  app.use(auditMiddleware);
  logStartup('✅ Comprehensive audit logging middleware applied.');

  // Request Logging (Morgan)
  if (process.env.NODE_ENV === 'development') {
    app.use(morgan('dev'));
    logStartup('📝 Request logging (morgan) enabled for development.');
  }

  // CORS Configuration
  logStartup('🌐 Applying Enhanced CORS Configuration...');
  const corsMiddlewareStack = createEnhancedCorsMiddleware();
  corsMiddlewareStack.forEach(middleware => app.use(middleware));
  logStartup('✅ Enhanced CORS Configuration applied.');

  // Body Parsers
  logStartup('📦 Applying body parsers (JSON and URL-encoded)...');
  app.use(express.json());
  app.use(express.urlencoded({ extended: true }));
  logStartup('✅ Body parsers applied.');

  // Swagger UI
  logStartup('📚 Setting up Swagger API documentation at /api-docs...');
  app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerSpec));
  logStartup('✅ Swagger API documentation setup complete.');

  logStartup('👍 All global middleware configured successfully.');
};
