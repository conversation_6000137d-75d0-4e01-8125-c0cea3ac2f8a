/**
 * DelegationNotesSection Component - SOLID Principles Implementation
 *
 * Single Responsibility: Handles general notes management
 * - General notes field with character counting and validation
 * - Follows SRP by focusing only on notes functionality
 *
 * @module DelegationNotesSection
 */

import React from 'react';
import { useFormContext } from 'react-hook-form';
import { Info } from 'lucide-react';

import type { DelegationFormData } from '@/lib/schemas/delegationSchemas';

import { FormField } from '@/components/ui/forms/formField';

// ============================================================================
// INTERFACES
// ============================================================================

export interface DelegationNotesSectionProps {
  /** Whether the form is currently submitting */
  isSubmitting?: boolean;
  /** Additional CSS classes */
  className?: string;
  /** Maximum character limit for notes */
  maxLength?: number;
  /** User's role for permission-based UI */
  userRole?: string;
}

// ============================================================================
// COMPONENT IMPLEMENTATION
// ============================================================================

/**
 * Notes Section for Delegation Form
 *
 * Manages general notes field with character counting and validation.
 * This component follows SRP by focusing solely on notes functionality.
 */
export const DelegationNotesSection: React.FC<DelegationNotesSectionProps> = ({
  isSubmitting = false,
  className = '',
  maxLength = 2000,
  userRole = 'user',
}) => {
  const { watch } = useFormContext<DelegationFormData>();

  // Watch notes field for character counting
  const notesValue = watch('notes') || '';
  const characterCount = notesValue.length;
  const isNearLimit = characterCount > maxLength * 0.8;
  const isOverLimit = characterCount > maxLength;

  return (
    <section className={`space-y-4 rounded-lg border bg-card p-6 ${className}`}>
      <h3 className="flex items-center text-lg font-semibold text-foreground">
        <Info className="mr-2 size-5 text-accent" />
        General Notes (Optional)
      </h3>

      <div className="space-y-2">
        <FormField
          label="Additional Notes for the Delegation"
          name="notes"
          type="textarea"
          disabled={isSubmitting}
          placeholder="Enter any additional information, special requirements, or notes about this delegation..."
        />

        {/* Character Counter */}
        <div className="flex justify-between items-center text-sm">
          <span className="text-muted-foreground">
            Additional context can help with delegation planning and execution
          </span>
          <span
            className={`font-medium ${
              isOverLimit
                ? 'text-destructive'
                : isNearLimit
                  ? 'text-warning'
                  : 'text-muted-foreground'
            }`}
          >
            {characterCount}/{maxLength}
          </span>
        </div>

        {/* Warning messages */}
        {isNearLimit && !isOverLimit && (
          <p className="text-sm text-warning">
            Approaching character limit. Consider being more concise.
          </p>
        )}

        {isOverLimit && (
          <p className="text-sm text-destructive">
            Character limit exceeded. Please shorten your notes.
          </p>
        )}
      </div>
    </section>
  );
};

export default DelegationNotesSection;
