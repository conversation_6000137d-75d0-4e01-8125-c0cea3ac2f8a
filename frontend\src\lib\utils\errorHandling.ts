/**
 * Error Handling Utilities for Phase 3
 * Handles structured error responses from the backend API
 */

import type { ApiValidationError } from '@/lib/types/api';

import { ApiError } from '@/lib/api/core/errors'; // Import ApiError

/**
 * Maps backend error codes to user-friendly messages
 */
export const ERROR_CODE_MESSAGES: Record<string, string> = {
  DATABASE_ERROR:
    'A database error occurred. Please try again or contact support.',
  EMPLOYEE_NOT_ACTIVE:
    'The selected employee is not currently active. Please select an active employee.',
  EMPLOYEE_NOT_FOUND:
    'The selected employee could not be found. Please refresh and try again.',
  // Assignment validation errors
  INVALID_DRIVER_ROLE:
    'The selected employee does not have driver role. Please select a different employee.',
  INVALID_FORMAT: 'Please check the format of your input.',
  // Network errors
  NETWORK_ERROR: 'Network error. Please check your connection and try again.',

  REQUIRED_FIELD: 'This field is required.',
  SERVER_ERROR: 'Server error. Please try again later.',
  TIMEOUT_ERROR: 'Request timed out. Please try again.',

  // General validation errors
  VALIDATION_ERROR: 'Please check your input and try again.',
  VEHICLE_NOT_FOUND:
    'The selected vehicle could not be found. Please refresh and try again.',
  VEHICLE_WITHOUT_DRIVER:
    'A vehicle cannot be assigned without selecting a driver first.',
};

/**
 * Maps form field names to user-friendly error messages
 */
export const FIELD_ERROR_MESSAGES: Record<string, string> = {
  capacity: 'Capacity',
  delegates: 'Delegates',
  driverEmployeeId: 'Driver selection',
  durationFrom: 'Start date',

  durationTo: 'End date',
  email: 'Email address',
  escortEmployeeId: 'Escort selection',
  // Common fields
  eventName: 'Event name',
  flightArrivalDetails: 'Arrival flight details',
  flightDepartureDetails: 'Departure flight details',
  licensePlate: 'License plate',

  location: 'Location',
  // Vehicle fields
  make: 'Vehicle make',
  model: 'Vehicle model',
  // Employee fields
  name: 'Name',
  phone: 'Phone number',

  role: 'Role',
  // Assignment fields
  staffEmployeeId: 'Staff member selection',
  status: 'Status',
  vehicleId: 'Vehicle selection',
  year: 'Year',
};

/**
 * Creates a user-friendly error object for display
 */
export interface UserFriendlyError {
  code?: string;
  field?: string;
  message: string;
  title: string;
}

/**
 * Converts API error to user-friendly format
 */
export function formatErrorForUser(
  error: any,
  context?: string
): UserFriendlyError {
  const message =
    getErrorMessage(error) || 'An unexpected error occurred. Please try again.';

  // Determine appropriate title based on error type
  let title = 'Error';
  if (isAssignmentError(error)) {
    title = 'Assignment Error';
  } else if (isValidationError(error)) {
    title = 'Validation Error';
  } else if (context) {
    title = `${context} Error`;
  }

  return {
    code: error?.code,
    field: error?.field || error?.path?.[0],
    message,
    title,
  };
}

/**
 * Extracts user-friendly error message from API error response
 * Returns null if no error is provided
 */
export function getErrorMessage(error: any): null | string {
  // Return null for falsy values (null, undefined, false, 0, '')
  if (!error) {
    return null;
  }
  // Handle structured API error response
  if (error && typeof error === 'object') {
    // Check for specific error code
    if (error.code && ERROR_CODE_MESSAGES[error.code]) {
      return ERROR_CODE_MESSAGES[error.code] ?? null;
    }

    // Check for error message
    if (error.error && typeof error.error === 'string') {
      return error.error;
    }

    // Check for general message
    if (error.message && typeof error.message === 'string') {
      return error.message;
    }

    // Handle validation errors array
    if (
      error.errors &&
      Array.isArray(error.errors) &&
      error.errors.length > 0
    ) {
      return error.errors.map((err: any) => err.message || err).join(', ');
    }
  }

  // Handle string errors
  if (typeof error === 'string') {
    return error;
  }

  // Handle Error objects (including custom ApiError instances)
  if (error instanceof Error) {
    // If it's a custom ApiError, it might have a 'details' property
    if (error instanceof ApiError && error.details) {
      // Try to extract more specific messages from error.details,
      // especially for validation errors (e.g., from BadRequestError)
      if (typeof error.details === 'object') {
        const details = error.details as Record<string, any>;

        // Common pattern: array of errors
        if (Array.isArray(details.errors) && details.errors.length > 0) {
          return details.errors
            .map((err: any) => err.message || err)
            .join(', ');
        }

        // Common pattern: object with field-specific errors
        if (details.fieldErrors && typeof details.fieldErrors === 'object') {
          const fieldErrors = Object.values(details.fieldErrors)
            .flat()
            .map((msg: any) => msg.message || msg);
          if (fieldErrors.length > 0) {
            return fieldErrors.join(', ');
          }
        }

        // Prioritize a 'message' or 'error' field within the details
        if (details.message) {
          return details.message;
        }
        if (details.error) {
          return details.error;
        }
      }
      // If details contain a string, use that
      if (typeof error.details === 'string') {
        return error.details;
      }
    }
    return error.message; // For standard Errors or ApiErrors where details weren't useful
  }

  // Fallback
  return 'An unexpected error occurred. Please try again.';
}

/**
 * Checks if an error is related to assignment validation
 */
export function isAssignmentError(error: any): boolean {
  if (!isValidationError(error) || typeof error.code !== 'string') return false;

  const assignmentErrorCodes = [
    'INVALID_DRIVER_ROLE',
    'EMPLOYEE_NOT_FOUND',
    'EMPLOYEE_NOT_ACTIVE',
    'VEHICLE_NOT_FOUND',
    'VEHICLE_WITHOUT_DRIVER',
  ];

  return assignmentErrorCodes.includes(error.code);
}

/**
 * Checks if an error is retryable based on error type and status
 */
export function isRetryableError(error: any): boolean {
  // Don't retry validation errors
  if (isValidationError(error)) {
    return false;
  }

  // Don't retry client errors (4xx) except 429 (rate limit)
  if (error?.status >= 400 && error?.status < 500) {
    return error.status === 429; // Only retry rate limit errors
  }

  // Retry server errors (5xx) and network errors
  if (error?.status >= 500 || !error?.status) {
    return true;
  }

  // Check for network-related errors
  if (error instanceof TypeError && error.message.includes('network')) {
    return true;
  }

  // Check for timeout errors
  if (error instanceof DOMException && error.name === 'AbortError') {
    return true;
  }

  return false;
}

/**
 * Checks if an error is a validation error
 */
export function isValidationError(error: any): error is ApiValidationError {
  return (
    error &&
    typeof error === 'object' &&
    'code' in error &&
    'message' in error &&
    'error' in error
  );
}

/**
 * Logs errors for debugging while providing user-friendly messages
 */
export function logAndFormatError(
  error: any,
  context?: string
): UserFriendlyError {
  // Log full error for debugging
  console.error(`Error in ${context || 'unknown context'}:`, error);

  // Return user-friendly version
  return formatErrorForUser(error, context);
}

/**
 * Handles form field errors for React Hook Form
 */
export function mapErrorToFormField(
  error: any
): null | { field: string; message: string } {
  if (!error) return null;

  // Check for field-specific error
  if (error.field) {
    return {
      field: error.field,
      message: getErrorMessage(error) || 'Invalid field value',
    };
  } else if (error.path?.[0]) {
    return {
      field: String(error.path[0]), // Ensure it's a string
      message: getErrorMessage(error) || 'Invalid field value',
    };
  }

  // Map assignment errors to specific fields
  if (error.code) {
    switch (error.code) {
      case 'EMPLOYEE_NOT_ACTIVE':
      case 'INVALID_DRIVER_ROLE': {
        return {
          field: 'driverEmployeeId',
          message: getErrorMessage(error) || 'Invalid driver',
        };
      }
      case 'EMPLOYEE_NOT_FOUND': {
        // Could be staff or driver, need more context
        return {
          field: 'staffEmployeeId',
          message: getErrorMessage(error) || 'Employee not found',
        };
      }
      case 'VEHICLE_NOT_FOUND':
      case 'VEHICLE_WITHOUT_DRIVER': {
        return {
          field: 'vehicleId',
          message: getErrorMessage(error) || 'Invalid vehicle',
        };
      }
    }
  }

  return null;
}

/**
 * Retry utility for failed requests
 */
export async function retryRequest<T>(
  requestFn: () => Promise<T>,
  maxRetries = 3,
  delay = 1000
): Promise<T> {
  let lastError: any;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await requestFn();
    } catch (error) {
      lastError = error;

      // Don't retry validation errors
      if (isValidationError(error)) {
        throw error;
      }

      // Don't retry on last attempt
      if (attempt === maxRetries) {
        break;
      }

      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, delay * attempt));
    }
  }

  throw lastError;
}

/**
 * Enhanced retry utility with validation error handling
 */
export async function retryWithValidationErrorHandling<T>(
  requestFn: () => Promise<T>,
  maxRetries = 3,
  delay = 1000
): Promise<T> {
  let lastError: any;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await requestFn();
    } catch (error) {
      lastError = error;

      // Don't retry if error is not retryable
      if (!isRetryableError(error)) {
        throw error;
      }

      // Don't retry on last attempt
      if (attempt === maxRetries) {
        break;
      }

      // Calculate exponential backoff delay
      const backoffDelay = delay * Math.pow(2, attempt - 1);

      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, backoffDelay));
    }
  }

  throw lastError;
}
