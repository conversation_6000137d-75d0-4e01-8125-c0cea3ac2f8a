/**
 * Middleware to sanitize task data before it's processed by controllers
 * Ensures that array fields are always arrays, even if they're null or undefined
 */

import type { NextFunction, Request, Response } from 'express';

import logger, { createContextLogger } from '../utils/logger.js';

/**
 * Ensures that task data has proper array fields
 * This prevents "findIndex is not a function" errors in the frontend
 */
export const sanitizeTaskData = (req: Request, res: Response, next: NextFunction): void => {
  const contextLogger = createContextLogger({
    method: req.method,
    path: req.path,
    requestId: req.headers['x-request-id'] as string,
    service: 'task-data-sanitizer',
    userId: (req as any).userId,
  });

  try {
    // Only process if there's a body
    if (req.body) {
      const taskId = req.params.id || 'unknown';

      // Ensure assignedEmployeeIds is always an array
      if (!req.body.assignedEmployeeIds || !Array.isArray(req.body.assignedEmployeeIds)) {
        const originalValue =
          req.body.assignedEmployeeIds === null ? 'null' : typeof req.body.assignedEmployeeIds;

        contextLogger.warn('Task data sanitized: assignedEmployeeIds', {
          field: 'assignedEmployeeIds',
          originalValue,
          sanitizedTo: '[]',
          taskId,
        });
        req.body.assignedEmployeeIds = [];
      }

      // Ensure requiredSkills is always an array
      if (!req.body.requiredSkills || !Array.isArray(req.body.requiredSkills)) {
        const originalValue =
          req.body.requiredSkills === null ? 'null' : typeof req.body.requiredSkills;

        contextLogger.warn('Task data sanitized: requiredSkills', {
          field: 'requiredSkills',
          originalValue,
          sanitizedTo: '[]',
          taskId,
        });
        req.body.requiredSkills = [];
      }

      // Ensure subTasks is always an array
      if (!req.body.subTasks || !Array.isArray(req.body.subTasks)) {
        const originalValue = req.body.subTasks === null ? 'null' : typeof req.body.subTasks;

        contextLogger.warn('Task data sanitized: subTasks', {
          field: 'subTasks',
          originalValue,
          sanitizedTo: '[]',
          taskId,
        });
        req.body.subTasks = [];
      }
    }

    next();
  } catch (error) {
    // If any error occurs during sanitization, log it but don't block the request
    contextLogger.error('Error in task data sanitizer middleware', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
    });
    next();
  }
};
