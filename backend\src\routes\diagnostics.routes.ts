import { Router, Request, Response } from 'express';
import { getDatabaseConfig, testDatabaseConnections } from '../services/database.service.js';
import logger from '../utils/logger.js'; // Included for good practice, though not directly used in this handler

const router = Router();

// Original path: /api/diagnostics
router.get('/', async (req: Request, res: Response) => {
  try {
    // Get database configuration
    const config = getDatabaseConfig();

    // Test database connections
    const connectionResults = await testDatabaseConnections();

    // Prepare detailed diagnostics response
    const diagnosticsResponse = {
      config: {
        connectionMode: config.databaseUrl.includes(':6543/') ? 'transaction' : 'session',
        databaseUrl: config.databaseUrl
          ? config.databaseUrl.replace(/\/\/.*?@/, '//****@') // Mask credentials
          : 'not set',
        supabaseKeyProvided: !!config.supabaseKey,
        supabaseUrl: config.supabaseUrl ?? 'not set',
        useSupabase: config.useSupabase,
      },
      connectionResults: {
        ...connectionResults,
        // Remove sensitive data from the response if necessary, for now keeping structure as in app.ts
        details: {
          ...connectionResults.details,
          supabase: {
            ...connectionResults.details.supabase,
            data: connectionResults.details.supabase.data ? 'Data available' : null, // Mask sensitive data
          },
        },
      },
      environment: {
        NODE_ENV: process.env.NODE_ENV,
        PORT: process.env.PORT,
      },
      system: {
        arch: process.arch,
        memoryUsage: process.memoryUsage(),
        nodeVersion: process.version,
        platform: process.platform,
        uptime: process.uptime(),
      },
      timestamp: new Date().toISOString(),
    };

    res.status(200).json(diagnosticsResponse);
  } catch (error: unknown) {
    logger.error('Failed to retrieve diagnostics information', {
      error: (error as Error).message,
      endpoint: '/diagnostics',
    });
    res.status(500).json({
      error: (error as Error).message,
      message: 'Failed to retrieve diagnostics information',
      status: 'error',
      timestamp: new Date().toISOString(),
    });
  }
});

export default router;
