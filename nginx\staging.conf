# WorkHub Staging Environment - Nginx Configuration
# Security Hardened Reverse Proxy Configuration
# Created: June 3, 2025
# Purpose: Production-ready staging proxy with comprehensive security headers

# Main context configuration
user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

# Events block
events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

# HTTP context
http {
    # Basic settings
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    
    # Security: Hide nginx version
    server_tokens off;
    
    # Performance settings
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    
    # Rate limiting zones
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=login:10m rate=1r/s;
    limit_req_zone $binary_remote_addr zone=general:10m rate=30r/s;
    
    # Connection limiting
    limit_conn_zone $binary_remote_addr zone=addr:10m;
    
    # Logging format
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    'rt=$request_time uct="$upstream_connect_time" '
                    'uht="$upstream_header_time" urt="$upstream_response_time"';
    
    access_log /var/log/nginx/access.log main;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
    
    # Upstream definitions
    upstream backend {
        server backend:3001 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }
    
    upstream frontend {
        server frontend:3000 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }

    # Define cache zone for static assets
    proxy_cache_path /var/cache/nginx/static levels=1:2 keys_zone=static_cache:10m inactive=60m use_temp_path=off;
    # Define cache zone for API responses (if applicable)
    proxy_cache_path /var/cache/nginx/api levels=1:2 keys_zone=api_cache:10m inactive=10m use_temp_path=off;
    
    # Security headers map
    map $sent_http_content_type $security_headers {
        default "nosniff";
    }
    
    # Main server block
    server {
        listen 80;
        server_name localhost staging.workhub.local;

        # Redirect all HTTP requests to HTTPS
        return 301 https://$host$request_uri;
        
        # Security: Client body size limit
        client_max_body_size 10M;
        
        # Security: Connection limits
        limit_conn addr 20;
        
        # Security headers (applied to all responses)
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header Referrer-Policy "strict-origin-when-cross-origin" always;
        add_header Permissions-Policy "geolocation=(), microphone=(), camera=()" always;
        
        # Content Security Policy for staging
        add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'strict-dynamic' https:; object-src 'none'; base-uri 'self'; style-src 'self' https:; img-src 'self' data: https://*.supabase.co https://images.unsplash.com https://via.placeholder.com; font-src 'self' https://fonts.gstatic.com https://cdn.jsdelivr.net data:; connect-src 'self' https://*.supabase.co wss://*.supabase.co http://localhost:3001 http://backend:3001 https://api.github.com; frame-ancestors 'none'; form-action 'self'; upgrade-insecure-requests;" always;
        
        # Health check endpoint
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
        
        # API routes - proxy to backend
        location /api/ {
            # Rate limiting for API endpoints
            limit_req zone=api burst=20 nodelay;

            # Caching for API responses (adjust as needed)
            proxy_cache api_cache;
            proxy_cache_valid 200 1m; # Cache successful API responses for 1 minute
            proxy_cache_valid 404 1s; # Cache 404s for 1 second
            proxy_cache_revalidate on;
            proxy_cache_min_uses 1;
            proxy_cache_use_stale error timeout updating http_500 http_502 http_503 http_504;
            add_header X-Proxy-Cache $upstream_cache_status;

            # Proxy settings
            proxy_pass http://backend;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;

            # Timeouts
            proxy_connect_timeout 30s;
            proxy_send_timeout 30s;
            proxy_read_timeout 30s;

            # Buffer settings
            proxy_buffering on;
            proxy_buffer_size 4k;
            proxy_buffers 8 4k;
            proxy_busy_buffers_size 8k;
        }
        
        # Authentication endpoints - stricter rate limiting
        location ~ ^/api/(auth|login) {
            limit_req zone=login burst=3 nodelay;
            
            proxy_pass http://backend;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        # Static assets and frontend routes
        location / {
            # Rate limiting for general requests
            limit_req zone=general burst=50 nodelay;

            # Caching for static assets
            proxy_cache static_cache;
            proxy_cache_valid 200 302 10m;
            proxy_cache_valid 404 1m;
            proxy_cache_revalidate on;
            proxy_cache_min_uses 1;
            proxy_cache_use_stale error timeout updating http_500 http_502 http_503 http_504;
            add_header X-Proxy-Cache $upstream_cache_status;

            # Try to serve static files first, then proxy to frontend
            try_files $uri @frontend;
        }
        
        # Frontend proxy
        location @frontend {
            proxy_pass http://frontend;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
            
            # Timeouts
            proxy_connect_timeout 30s;
            proxy_send_timeout 30s;
            proxy_read_timeout 30s;
        }
        
        # Security: Block common attack patterns
        location ~ /\. {
            deny all;
            access_log off;
            log_not_found off;
        }
        
        location ~ ~$ {
            deny all;
            access_log off;
            log_not_found off;
        }
        
        # Security: Block access to sensitive files
        location ~* \.(env|git|svn|htaccess|htpasswd)$ {
            deny all;
            access_log off;
            log_not_found off;
        }
        
        # Error pages
        error_page 404 /404.html;
        error_page 500 502 503 504 /50x.html;
        
        location = /50x.html {
            root /usr/share/nginx/html;
        }
    }

    # HTTPS server block
    server {
        listen 443 ssl http2;
        server_name localhost staging.workhub.local;

        # Placeholder paths for SSL certificates.
        # You need to replace these with your actual certificate and key paths.
        ssl_certificate /etc/nginx/ssl/staging.workhub.local.crt;
        ssl_certificate_key /etc/nginx/ssl/staging.workhub.local.key;

        # Strong SSL/TLS protocols
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_prefer_server_ciphers on;
        ssl_ciphers 'ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-CHACHA20-POLY1305';
        ssl_session_cache shared:SSL:10m;
        ssl_session_timeout 1h;
        ssl_session_tickets off;
        ssl_stapling on;
        ssl_stapling_verify on;
        resolver ******* ******* valid=300s; # Google DNS, adjust as needed
        resolver_timeout 5s;

        # Security: Client body size limit
        client_max_body_size 10M;

        # Security: Connection limits
        limit_conn addr 20;

        # Security headers (applied to all responses)
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header Referrer-Policy "strict-origin-when-cross-origin" always;
        add_header Permissions-Policy "geolocation=(), microphone=(), camera=()" always;

        # Content Security Policy for staging (will be refined in next step)
        add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https:; object-src 'none'; base-uri 'self'; style-src 'self' 'unsafe-inline' https:; img-src 'self' data: https://*.supabase.co https://images.unsplash.com https://via.placeholder.com; font-src 'self' https://fonts.gstatic.com https://cdn.jsdelivr.net data:; connect-src 'self' https://*.supabase.co wss://*.supabase.co http://localhost:3001 http://backend:3001 https://api.github.com ws://localhost:3001 ws://backend:3001 wss://localhost wss://staging.workhub.local; frame-ancestors 'none'; form-action 'self'; upgrade-insecure-requests;" always;

        # Health check endpoint
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }

        # API routes - proxy to backend
        location /api/ {
            # Rate limiting for API endpoints
            limit_req zone=api burst=20 nodelay;

            # Proxy settings
            proxy_pass http://backend;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;

            # Timeouts
            proxy_connect_timeout 30s;
            proxy_send_timeout 30s;
            proxy_read_timeout 30s;

            # Buffer settings
            proxy_buffering on;
            proxy_buffer_size 4k;
            proxy_buffers 8 4k;
            proxy_busy_buffers_size 8k;
        }

        # Authentication endpoints - stricter rate limiting
        location ~ ^/api/(auth|login) {
            limit_req zone=login burst=3 nodelay;

            proxy_pass http://backend;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Static assets and frontend routes
        location / {
            # Rate limiting for general requests
            limit_req zone=general burst=50 nodelay;

            # Try to serve static files first, then proxy to frontend
            try_files $uri @frontend;
        }

        # Frontend proxy
        location @frontend {
            proxy_pass http://frontend;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;

            # Timeouts
            proxy_connect_timeout 30s;
            proxy_send_timeout 30s;
            proxy_read_timeout 30s;
        }

        # Security: Block common attack patterns
        location ~ /\. {
            deny all;
            access_log off;
            log_not_found off;
        }

        location ~ ~$ {
            deny all;
            access_log off;
            log_not_found off;
        }

        # Security: Block access to sensitive files
        location ~* \.(env|git|svn|htaccess|htpasswd)$ {
            deny all;
            access_log off;
            log_not_found off;
        }

        # Error pages
        error_page 404 /404.html;
        error_page 500 502 503 504 /50x.html;

        location = /50x.html {
            root /usr/share/nginx/html;
        }
    }
}
