/**
 * Print-specific styles for WorkHub reports
 * This file contains additional print styles that can be imported by report pages
 * Updated with modern typography and spacing standards
 */

/* Print-specific styles for report pages */
@media print {
  /* Ensure all tables use the standardized print styling */
  .report-table {
    page-break-inside: auto !important;
    width: 100% !important;
    border-collapse: collapse !important;
    margin-bottom: 8mm !important; /* Increased spacing */
    font-size: 10pt !important; /* Body text size */
    border: 1pt solid #333 !important; /* Consistent border */
    overflow: visible !important; /* Ensure content is not clipped */
  }

  .report-table thead {
    display: table-header-group !important;
  }

  .report-table tr {
    page-break-inside: avoid !important;
  }

  .report-table th {
    background-color: #f8f8f8 !important; /* Subtle header background */
    font-weight: bold !important;
    font-size: 10pt !important; /* Match body text size */
    border-bottom: 1pt solid #999 !important; /* Slightly thicker bottom border */
    padding: 4mm 5mm !important; /* Consistent padding */
    text-align: left !important;
    line-height: 1.3 !important; /* Improved line height */
  }

  .report-table td {
    border: 0.5pt solid #ddd !important; /* Lighter borders */
    padding: 4mm 5mm !important; /* Consistent padding */
    font-size: 10pt !important; /* Body text size */
    word-break: normal !important; /* Ensure no character splitting */
    hyphens: auto !important; /* Allow hyphenation */
    white-space: normal !important;
    line-height: 1.3 !important; /* Improved line height */
    overflow-wrap: anywhere !important; /* Allow breaking at any point for long words */
  }

  /* Alternating row colors */
  .report-table tr:nth-child(even) {
    background-color: #f9f9f9 !important; /* Subtle alternating row color */
  }

  /* Print-specific summary section */
  .report-summary {
    margin: 8mm 0 8mm 0 !important; /* Consistent 8mm spacing */
    border: 0.5pt solid #ddd !important; /* Lighter border */
    padding: 4mm 5mm !important; /* Consistent padding */
    background-color: #f9f9f9 !important; /* Subtle background */
    page-break-inside: avoid !important;
    border-radius: 2mm !important; /* Subtle rounded corners */
  }

  /* Print-specific section styling */
  .report-section {
    margin-bottom: 8mm !important; /* Consistent 8mm spacing */
    page-break-inside: avoid !important;
    background-color: #f9f9f9 !important; /* Subtle section background */
    border: 0.5pt solid #ddd !important; /* Lighter border */
    padding: 4mm 5mm !important; /* Consistent padding */
    border-radius: 2mm !important; /* Subtle rounded corners */
  }

  /* Print-specific section heading */
  .report-section-heading {
    font-size: 12pt !important; /* Subheader size */
    font-weight: bold !important;
    margin-bottom: 4mm !important; /* Slightly more spacing */
    border-bottom: 0.5pt solid #ddd !important; /* Lighter border */
    padding-bottom: 2mm !important; /* Slightly more padding */
    color: #333 !important;
    line-height: 1.3 !important; /* Improved line height */
  }

  /* Modern print layout enhancements */
  .print-layout-container {
    display: flex !important;
    flex-direction: column !important;
    gap: 8mm !important; /* Consistent 8mm spacing between sections */
  }

  /* Print-optimized header with logo, title, and date */
  .print-header-modern {
    display: flex !important;
    flex-direction: row !important;
    justify-content: space-between !important;
    align-items: flex-start !important;
    width: 100% !important;
    margin-bottom: 8mm !important;
    border-bottom: 1pt solid #999 !important;
    padding-bottom: 4mm !important;
    page-break-inside: avoid !important;
    page-break-after: avoid !important;
  }

  .print-header-left {
    text-align: left !important;
    width: 25% !important;
    /* font-family inherited from body */
  }

  .print-header-center {
    text-align: center !important;
    width: 50% !important;
    /* font-family inherited from body */
  }

  .print-header-right {
    text-align: right !important;
    width: 25% !important;
    font-size: 8pt !important;
    color: #666 !important;
    /* font-family inherited from body */
  }

  /* Compact filter summary for print */
  .print-filter-summary {
    display: flex !important;
    flex-wrap: wrap !important;
    gap: 5mm !important;
    margin-bottom: 8mm !important;
    font-size: 9pt !important;
    color: #444 !important;
    background-color: #f9f9f9 !important;
    border: 0.5pt solid #ddd !important;
    padding: 3mm 4mm !important;
    border-radius: 2mm !important;
  }

  .print-filter-item {
    display: flex !important;
    align-items: center !important;
    gap: 2mm !important;
  }

  .print-filter-label {
    font-weight: bold !important;
  }

  /* Optimized table column widths */
  .print-col-narrow {
    width: 10% !important;
  }

  .print-col-medium {
    width: 20% !important;
  }

  .print-col-wide {
    width: 30% !important;
  }

  .print-col-extra-wide {
    width: 40% !important;
  }

  /* Styles moved from delegation-report-list.css for consolidation */
  /* Reset and base styles */
  .delegation-report-container {
    max-width: 100%;
    margin: 0;
    padding: 0;
    background: white;
    color: black !important;
    /* font-size and line-height are inherited from body */
  }

  /* Header for print */
  .delegation-report-header {
    text-align: center !important;
    margin-bottom: 5mm !important; /* Changed from 15pt */
    padding-bottom: 3mm !important; /* Changed from 8pt */
    border-bottom: 1pt solid #333 !important;
    page-break-after: avoid !important;
  }

  .delegation-report-title {
    font-size: 16pt !important;
    font-weight: bold !important;
    color: black !important;
    margin-bottom: 2mm !important; /* Changed from 6pt */
  }

  .delegation-report-subtitle {
    font-size: 10pt !important;
    color: #333 !important;
    margin-bottom: 1mm !important; /* Changed from 3pt */
  }

  .delegation-report-date {
    font-size: 8pt !important;
    color: #666 !important;
  }

  /* Hide screen-only elements */
  .no-print {
    display: none !important;
  }

  .print-only {
    display: block !important;
  }

  /* Print summary section */
  .delegation-print-summary {
    margin: 4mm 0 !important; /* Changed from 10pt */
    padding: 3mm !important; /* Changed from 8pt */
    border: 1pt solid #ddd !important;
    background: #f9f9f9 !important;
    page-break-inside: avoid !important;
  }

  .delegation-print-summary-item {
    display: inline-block !important;
    margin-right: 5mm !important; /* Changed from 15pt */
    margin-bottom: 1mm !important; /* Changed from 3pt */
    font-size: 10pt !important; /* Changed from 9pt */
  }

  .delegation-print-summary-label {
    font-weight: bold !important;
  }

  .delegation-print-summary-value {
    color: #333 !important;
  }

  /* Table styles for print */
  .delegation-table-container {
    background: white !important;
    border: none !important;
    box-shadow: none !important;
    border-radius: 0 !important;
    overflow: visible !important;
  }

  /* Delegation table styles moved to consolidated section below */

  /* Badge styles for print */
  .badge {
    background: #e5e5e5 !important;
    color: #333 !important;
    border: 1pt solid #999 !important;
    padding: 0.5mm 1.5mm !important; /* Changed from 1pt 3pt */
    border-radius: 2pt !important;
    font-size: 9pt !important; /* Changed from 8pt */
    font-weight: normal !important;
  }

  /* Hide icons in print */
  .lucide,
  svg {
    display: none !important;
  }

  /* Footer for print */
  .delegation-report-footer {
    margin-top: 5mm !important; /* Changed from 15pt */
    padding-top: 3mm !important; /* Changed from 8pt */
    border-top: 1pt solid #ddd !important;
    text-align: center !important;
    font-size: 9pt !important; /* Changed from 8pt */
    color: #666 !important;
    page-break-inside: avoid !important;
  }

  /* Page numbering */
  .delegation-report-footer::after {
    content: 'Page ' counter(page) ' of ' counter(pages);
    display: block;
    margin-top: 1mm; /* Changed from 3pt */
  }

  /* Ensure proper page breaks */
  .page-break-before {
    page-break-before: always !important;
  }

  .page-break-after {
    page-break-after: always !important;
  }

  .page-break-inside-avoid {
    page-break-inside: avoid !important;
  }

  /* Final comprehensive border removal for any missed elements */
  .card,
  .border,
  .rounded,
  .rounded-lg,
  .shadow,
  .shadow-sm,
  .shadow-md,
  .shadow-lg {
    border: none !important;
    box-shadow: none !important;
    border-radius: 0 !important;
    outline: none !important;
  }

  /* Remove Tailwind utility borders */
  .border-t,
  .border-b,
  .border-l,
  .border-r,
  .border-x,
  .border-y {
    border: none !important;
  }

  /* Ensure report content containers have no borders */
  .report-content,
  .print-container,
  .space-y-6,
  .space-y-4,
  .space-y-2 {
    border: none !important;
    outline: none !important;
    box-shadow: none !important;
  }

  /* CONSOLIDATED DELEGATION TABLE STYLES - Single source of truth */

  /* Handle shadcn Table wrapper div for print */
  .delegation-table-container .relative {
    overflow: visible !important;
    width: 100% !important;
  }

  /* Primary delegation table styling with highest specificity */
  #delegations-table.delegation-table {
    width: 100% !important;
    border-collapse: collapse !important;
    margin: 0 !important;
    padding: 0 !important;
    font-size: 10pt !important;
    line-height: 1.4 !important;
    border: 1pt solid #333 !important;
    background: white !important;
  }

  /* Table header styling */
  #delegations-table.delegation-table thead th {
    background-color: #f5f5f5 !important;
    color: #333 !important;
    font-weight: bold !important;
    font-size: 10pt !important;
    padding: 6pt 8pt !important;
    border: 1pt solid #333 !important; /* Consistent border */
    text-align: left !important;
    vertical-align: top !important;
    page-break-after: avoid !important;
  }

  /* Table cell styling */
  #delegations-table.delegation-table tbody td {
    padding: 6pt 8pt !important;
    border: 1pt solid #333 !important; /* Consistent border */
    font-size: 10pt !important;
    line-height: 1.4 !important;
    vertical-align: top !important;
    text-align: left !important;
    /* Safe text wrapping - no aggressive word breaking */
    overflow-wrap: anywhere !important; /* Allow breaking at any point for long words */
    word-break: normal !important; /* Ensure no character splitting */
    hyphens: auto !important;
    white-space: normal !important;
  }

  /* Column width distribution */
  #delegations-table.delegation-table th:nth-child(1),
  #delegations-table.delegation-table td:nth-child(1) {
    width: 25% !important; /* Event Name */
  }

  #delegations-table.delegation-table th:nth-child(2),
  #delegations-table.delegation-table td:nth-child(2) {
    width: 15% !important; /* Location */
  }

  #delegations-table.delegation-table th:nth-child(3),
  #delegations-table.delegation-table td:nth-child(3) {
    width: 20% !important; /* Duration */
  }

  #delegations-table.delegation-table th:nth-child(4),
  #delegations-table.delegation-table td:nth-child(4) {
    width: 15% !important; /* Status */
  }

  #delegations-table.delegation-table th:nth-child(5),
  #delegations-table.delegation-table td:nth-child(5) {
    width: 25% !important; /* Delegates */
  }

  /* Row styling */
  #delegations-table.delegation-table tbody tr {
    page-break-inside: avoid !important;
    border: none !important;
  }

  #delegations-table.delegation-table tbody tr:nth-child(even) {
    background-color: #f9f9f9 !important;
  }

  /* Badge styling within table */
  #delegations-table.delegation-table .badge {
    display: inline-block !important;
    padding: 2pt 4pt !important;
    font-size: 9pt !important;
    background: #e5e5e5 !important;
    color: #333 !important;
    border: 0.5pt solid #999 !important;
    border-radius: 2pt !important;
    white-space: nowrap !important;
  }

  /* Hide icons and flex layouts in print */
  #delegations-table.delegation-table svg,
  #delegations-table.delegation-table .lucide {
    display: none !important;
  }

  #delegations-table.delegation-table .flex {
    display: block !important;
  }

  /* Remove any conflicting Tailwind borders */
  #delegations-table.delegation-table .border-b,
  #delegations-table.delegation-table .border-gray-100,
  #delegations-table.delegation-table .border-gray-200 {
    border: none !important;
  }
}
