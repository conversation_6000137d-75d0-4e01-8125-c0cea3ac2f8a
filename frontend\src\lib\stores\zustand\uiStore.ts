/**
 * @file Zustand store for general UI preferences and ephemeral UI states.
 * This store can be used for states that don't fit into appStore (e.g., user-specific UI settings, modal visibility).
 * @module stores/zustand/uiStore
 */

import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';

/**
 * Interface for the UiStore state and actions.
 */
interface UiStore {
  autoRefreshInterval: number; // in seconds
  closeModal: () => void;
  dashboardLayout: 'cards' | 'grid' | 'list';
  // Example UI preferences
  fontSize: 'large' | 'medium' | 'small';
  // Example ephemeral UI states
  isModalOpen: boolean;
  mapViewPreference: 'hybrid' | 'roadmap' | 'satellite';
  modalContent:
    | 'delegation-form'
    | 'employee-profile'
    | 'login'
    | 'settings'
    | 'signup'
    | 'task-assignment'
    | 'vehicle-details'
    | null;
  notificationsEnabled: boolean;

  openModal: (
    content:
      | 'delegation-form'
      | 'employee-profile'
      | 'login'
      | 'settings'
      | 'signup'
      | 'task-assignment'
      | 'vehicle-details'
  ) => void;
  setAutoRefreshInterval: (interval: number) => void;
  setDashboardLayout: (layout: 'cards' | 'grid' | 'list') => void;
  // Actions
  setFontSize: (size: 'large' | 'medium' | 'small') => void;
  setMapViewPreference: (view: 'hybrid' | 'roadmap' | 'satellite') => void;
  // WorkHub-specific actions
  setTableDensity: (density: 'comfortable' | 'compact' | 'spacious') => void;
  // WorkHub-specific preferences
  tableDensity: 'comfortable' | 'compact' | 'spacious';
  toggleNotifications: () => void;
}

/**
 * Zustand store for managing general UI preferences and ephemeral UI states.
 * Enhanced with persistence and devtools for better development experience.
 */
export const useUiStore = create<UiStore>()(
  devtools(
    persist(
      set => ({
        autoRefreshInterval: 30, // 30 seconds default
        closeModal: () => set({ isModalOpen: false, modalContent: null }),
        dashboardLayout: 'cards',
        // Initial state
        fontSize: 'medium',
        isModalOpen: false,
        mapViewPreference: 'roadmap',
        modalContent: null,
        notificationsEnabled: true,

        openModal: (
          content:
            | 'delegation-form'
            | 'employee-profile'
            | 'login'
            | 'settings'
            | 'signup'
            | 'task-assignment'
            | 'vehicle-details'
        ) => set({ isModalOpen: true, modalContent: content }),
        setAutoRefreshInterval: (interval: number) =>
          set({ autoRefreshInterval: interval }),
        setDashboardLayout: (layout: 'cards' | 'grid' | 'list') =>
          set({ dashboardLayout: layout }),
        // Actions
        setFontSize: (size: 'large' | 'medium' | 'small') =>
          set({ fontSize: size }),

        setMapViewPreference: (view: 'hybrid' | 'roadmap' | 'satellite') =>
          set({ mapViewPreference: view }),
        // WorkHub-specific actions
        setTableDensity: (density: 'comfortable' | 'compact' | 'spacious') =>
          set({ tableDensity: density }),
        tableDensity: 'comfortable',
        toggleNotifications: () =>
          set(state => ({ notificationsEnabled: !state.notificationsEnabled })),
      }),
      {
        name: 'workhub-ui-store', // Storage key
        partialize: state => ({
          autoRefreshInterval: state.autoRefreshInterval,
          dashboardLayout: state.dashboardLayout,
          fontSize: state.fontSize,
          mapViewPreference: state.mapViewPreference,
          notificationsEnabled: state.notificationsEnabled,
          tableDensity: state.tableDensity,
          // Don't persist modal state
        }),
      }
    ),
    {
      name: 'ui-store', // DevTools name
    }
  )
);
