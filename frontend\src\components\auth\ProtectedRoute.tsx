'use client';

import type { ReactNode } from 'react';

import { <PERSON><PERSON><PERSON><PERSON>gle, Loader2, Shield } from 'lucide-react';
import React from 'react';

import { useAuthContext } from '../../contexts/AuthContext';
import { Alert, AlertDescription } from '../ui/alert';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '../ui/card';
import { LoginForm } from './loginForm';

interface ProtectedRouteProps {
  allowedRoles?: string[];
  children: ReactNode;
  fallback?: ReactNode;
  requireEmailVerification?: boolean;
}

/**
 * Protected Route Component
 *
 * This component protects routes by requiring authentication and
 * optionally enforces email verification and role-based access.
 * It displays appropriate fallback UI for unauthenticated or unauthorized users.
 */
export function ProtectedRoute({
  allowedRoles = [],
  children,
  fallback,
  requireEmailVerification = true,
}: ProtectedRouteProps) {
  const { error, loading, session, user, userRole } = useAuthContext();

  // Show loading state while checking authentication
  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-50">
        <Card className="mx-auto w-full max-w-md">
          <CardContent className="flex flex-col items-center justify-center py-8">
            <Loader2 className="mb-4 size-8 animate-spin text-blue-600" />
            <p className="text-sm text-muted-foreground">
              Verifying security credentials...
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Show error state if there's an authentication error and no user
  if (error && !user) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-50 p-4">
        <Card className="mx-auto w-full max-w-md">
          <CardHeader>
            <CardTitle className="flex items-center text-red-600">
              <AlertTriangle className="mr-2 size-5" />
              Authentication Error
            </CardTitle>
            <CardDescription>
              There was a problem with the security system
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
            <div className="mt-4">
              <LoginForm />
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Show login form if user is not authenticated
  if (!user || !session) {
    if (fallback) {
      return <>{fallback}</>;
    }

    return (
      <LoginForm
        onSuccess={() => {
          // Redirect to main page after successful login
          globalThis.location.href = '/';
        }}
      />
    );
  }

  // Check email verification if required
  if (requireEmailVerification && !user.email_confirmed_at) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-50 p-4">
        <Card className="mx-auto w-full max-w-md">
          <CardHeader>
            <CardTitle className="flex items-center text-yellow-600">
              <Shield className="mr-2 size-5" />
              Email Verification Required
            </CardTitle>
            <CardDescription>
              Please verify your email address to continue
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Alert>
              <AlertTriangle className="size-4" />
              <AlertDescription>
                We've sent a verification email to <strong>{user.email}</strong>
                . Please check your inbox and click the verification link to
                access the system.
              </AlertDescription>
            </Alert>
            <div className="mt-4 text-center">
              <p className="text-sm text-muted-foreground">
                Didn't receive the email? Check your spam folder or contact your
                administrator.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Check role-based access if roles are specified
  if (allowedRoles.length > 0) {
    // Use the userRole from useAuthContext directly
    const currentUserRole = userRole || 'USER'; // Default to 'USER' if role is not set

    if (!allowedRoles.includes(currentUserRole)) {
      return (
        <div className="flex min-h-screen items-center justify-center bg-gray-50 p-4">
          <Card className="mx-auto w-full max-w-md">
            <CardHeader>
              <CardTitle className="flex items-center text-red-600">
                <Shield className="mr-2 size-5" />
                Access Denied
              </CardTitle>
              <CardDescription>
                Insufficient permissions to access this resource
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Alert variant="destructive">
                <AlertDescription>
                  Your account ({currentUserRole}) does not have permission to
                  access this area. Required roles: {allowedRoles.join(', ')}
                </AlertDescription>
              </Alert>
              <div className="mt-4 text-center">
                <p className="text-sm text-muted-foreground">
                  Contact your administrator if you believe this is an error.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      );
    }
  }

  // User is authenticated and authorized - render children
  return <>{children}</>;
}
