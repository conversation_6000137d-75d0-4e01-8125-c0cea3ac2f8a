/**
 * @file Reusable InfoSection component
 * @module components/ui/info-section
 */

import React from 'react';
import { cn } from '@/lib/utils';

interface InfoSectionProps {
  /** Icon component to display */
  icon: React.ReactNode;
  /** Label text for the section */
  label: string;
  /** Value or content to display */
  children: React.ReactNode;
  /** Additional CSS classes */
  className?: string;
  /** Icon container styling */
  iconClassName?: string;
  /** Whether to show as a warning/alert style */
  variant?: 'default' | 'warning';
}

/**
 * Reusable component for displaying information with icon, label, and value
 * Follows DRY principle by providing consistent layout for info sections
 */
export const InfoSection: React.FC<InfoSectionProps> = ({
  icon,
  label,
  children,
  className,
  iconClassName,
  variant = 'default',
}) => {
  const baseClasses = variant === 'warning' 
    ? 'flex items-start space-x-3 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800/30 rounded-lg'
    : 'flex items-start space-x-3';

  const iconBaseClasses = variant === 'warning'
    ? 'mt-0.5 rounded-full bg-red-100 dark:bg-red-900/50 p-2'
    : 'mt-0.5 rounded-full p-2';

  return (
    <div className={cn(baseClasses, className)}>
      <div className={cn(iconBaseClasses, iconClassName)}>
        {icon}
      </div>
      <div className="flex-1 min-w-0">
        <div className={cn(
          'text-xs font-medium uppercase tracking-wide mb-1',
          variant === 'warning' 
            ? 'text-red-600 dark:text-red-400' 
            : 'text-gray-500 dark:text-gray-400'
        )}>
          {label}
        </div>
        <div className={cn(
          'font-medium',
          variant === 'warning'
            ? 'text-red-700 dark:text-red-300'
            : 'text-gray-900 dark:text-white'
        )}>
          {children}
        </div>
      </div>
    </div>
  );
};

export default InfoSection;
