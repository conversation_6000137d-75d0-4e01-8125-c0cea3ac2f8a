/**
 * @file useReportGeneration.ts
 * @description Hook for managing data report generation
 */

import { useState, useCallback } from 'react';
import { useApiQuery } from '@/hooks/api/useApiQuery';
import { useSecureApiClient } from '@/lib/api/security';
import {
  createReportGenerationService,
  IReportGenerationService,
  IApiClient,
} from '../data/services/ReportGenerationService';

/**
 * Report generation configuration interface
 */
export interface ReportGenerationConfig {
  entityTypes: string[];
  template: string;
  format: string;
  filters?: Record<string, any>;
  options?: {
    name?: string;
    description?: string;
    includeCharts?: boolean;
    includeSummary?: boolean;
  };
}

/**
 * Individual report generation configuration
 */
export interface IndividualReportConfig {
  entityType: string;
  entityId: string;
  template?: string;
  format?: string;
  options?: Record<string, any>;
}

/**
 * Aggregate report generation configuration
 */
export interface AggregateReportConfig {
  entityType: string;
  filters?: Record<string, any>;
  template?: string;
  format?: string;
  options?: Record<string, any>;
}

/**
 * Report generation result
 */
export interface ReportGenerationResult {
  data?: any; // Main report data
  report?: any; // Legacy support
  metadata: {
    id: string;
    type: string;
    entityTypes?: string[];
    entityType?: string;
    entityId?: string;
    format: string;
    template: string;
    generatedAt: string;
    generatedBy: string;
    filters?: Record<string, any>;
    options?: Record<string, any>;
  };
}

/**
 * Report history item
 */
export interface ReportHistoryItem {
  id: string;
  type: 'comprehensive' | 'individual' | 'aggregate';
  entityTypes?: string[];
  entityType?: string;
  entityId?: string;
  format: string;
  template: string;
  status: 'completed' | 'failed' | 'processing';
  generatedAt: string;
  generatedBy: string;
  downloadUrl?: string;
  fileSize?: string;
}

/**
 * Hook for managing report generation
 *
 * Follows SOLID Principles:
 * - SRP: Single responsibility for React state management
 * - DIP: Depends on IReportGenerationService abstraction
 * - OCP: Open for extension with new report types
 */
export const useReportGeneration = () => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { client } = useSecureApiClient();

  // Create API client adapter for the service
  const apiClient: IApiClient = {
    request: async config => {
      const response = await client.request(config);
      return { data: response };
    },
  };

  // Create service instance with proper API client
  const service = createReportGenerationService(apiClient);

  /**
   * Generate comprehensive data report
   * Uses service layer following DIP principle
   */
  const generateReport = useCallback(
    async (config: ReportGenerationConfig): Promise<ReportGenerationResult> => {
      setIsGenerating(true);
      setError(null);

      try {
        return await service.generateComprehensiveReport(config);
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : 'Failed to generate report';
        setError(errorMessage);
        throw err;
      } finally {
        setIsGenerating(false);
      }
    },
    [service]
  );

  /**
   * Generate individual entity report
   * Uses service layer following DIP principle
   */
  const generateIndividualReport = useCallback(
    async (config: IndividualReportConfig): Promise<ReportGenerationResult> => {
      setIsGenerating(true);
      setError(null);

      try {
        return await service.generateIndividualReport(config);
      } catch (err) {
        const errorMessage =
          err instanceof Error
            ? err.message
            : 'Failed to generate individual report';
        setError(errorMessage);
        throw err;
      } finally {
        setIsGenerating(false);
      }
    },
    [service]
  );

  /**
   * Generate aggregate entity report
   * Uses service layer following DIP principle
   */
  const generateAggregateReport = useCallback(
    async (config: AggregateReportConfig): Promise<ReportGenerationResult> => {
      setIsGenerating(true);
      setError(null);

      try {
        return await service.generateAggregateReport(config);
      } catch (err) {
        const errorMessage =
          err instanceof Error
            ? err.message
            : 'Failed to generate aggregate report';
        setError(errorMessage);
        throw err;
      } finally {
        setIsGenerating(false);
      }
    },
    [service]
  );

  /**
   * Export generated report data to PDF/Excel/CSV
   */
  const exportReport = useCallback(
    async (
      reportData: any,
      format: 'pdf' | 'excel' | 'csv',
      entityType: 'delegations' | 'tasks' | 'vehicles' | 'employees',
      reportTitle?: string,
      filename?: string
    ): Promise<void> => {
      try {
        const { useExport } = await import('../exports/hooks/useExport');
        const { exportReportToPDF, exportReportToExcel, exportToCSV } =
          useExport(filename || 'report');

        switch (format) {
          case 'pdf':
            // Convert report data to PDF using entity-specific components
            await exportReportToPDF(
              reportData,
              entityType,
              reportTitle ||
                `${entityType.charAt(0).toUpperCase() + entityType.slice(1)} Report`,
              filename
            );
            break;

          case 'excel':
            // Convert to Excel format with proper sheets
            exportReportToExcel(reportData, entityType, filename);
            break;

          case 'csv':
            // Convert to CSV format (flatten data for CSV)
            const csvData = Array.isArray(reportData.data)
              ? reportData.data
              : [reportData.data || reportData];
            exportToCSV(csvData, { filename: filename || 'report' });
            break;

          default:
            throw new Error(`Unsupported export format: ${format}`);
        }
      } catch (error) {
        console.error('Export failed:', error);
        throw error;
      }
    },
    []
  );

  return {
    generateComprehensiveReport: generateReport,
    generateIndividualReport,
    generateAggregateReport,
    exportReport,
    isGenerating,
    error,
  };
};

/**
 * Hook for managing report history
 */
export const useReportHistory = (filters?: {
  type?: string;
  entityType?: string;
}) => {
  const { client } = useSecureApiClient();
  const queryParams = new URLSearchParams();
  if (filters?.type) queryParams.append('type', filters.type);
  if (filters?.entityType) queryParams.append('entityType', filters.entityType);

  const historyQuery = useApiQuery(
    ['report-history', filters],
    async (): Promise<{ reports: ReportHistoryItem[]; pagination: any }> => {
      const response = await client.get(
        `/api/reporting/reports/history?${queryParams.toString()}`
      );
      return response;
    },
    {
      cacheDuration: 2 * 60 * 1000, // 2 minutes
      enableRetry: true,
    }
  );

  return {
    reports: historyQuery.data?.reports || [],
    pagination: historyQuery.data?.pagination,
    isLoading: historyQuery.isLoading,
    error: historyQuery.error,
    refetch: historyQuery.refetch,
  };
};

/**
 * Hook for downloading reports
 */
export const useReportDownload = () => {
  const [isDownloading, setIsDownloading] = useState(false);
  const [downloadError, setDownloadError] = useState<string | null>(null);
  const { client } = useSecureApiClient();

  const downloadReport = useCallback(
    async (reportId: string): Promise<void> => {
      setIsDownloading(true);
      setDownloadError(null);

      try {
        const response = await client.get(
          `/api/reporting/reports/${reportId}/download`
        );

        // For now, just show the response since actual file download isn't implemented yet
        console.log('Download result:', response);

        // TODO: Implement actual file download when backend file storage is ready
        alert('Download functionality will be implemented with file storage');
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : 'Failed to download report';
        setDownloadError(errorMessage);
        throw err;
      } finally {
        setIsDownloading(false);
      }
    },
    [client]
  );

  return {
    downloadReport,
    isDownloading,
    downloadError,
  };
};

/**
 * Hook for report templates
 */
export const useReportTemplates = () => {
  const { client } = useSecureApiClient();

  const templatesQuery = useApiQuery(
    ['report-templates'],
    async () => {
      const response = await client.get('/api/reporting/reports/templates');

      // Ensure we return an array, handle different response structures
      const data = response;
      if (Array.isArray(data)) {
        return data;
      }

      // If response has nested data property (API wrapper format)
      if (data && Array.isArray(data.data)) {
        return data.data;
      }

      // Fallback to empty array if data is not in expected format
      console.warn('Report templates API returned unexpected format:', data);
      return [];
    },
    {
      cacheDuration: 10 * 60 * 1000, // 10 minutes
      enableRetry: true,
    }
  );

  return {
    templates: Array.isArray(templatesQuery.data) ? templatesQuery.data : [],
    isLoading: templatesQuery.isLoading,
    error: templatesQuery.error,
    refetch: templatesQuery.refetch,
  };
};
