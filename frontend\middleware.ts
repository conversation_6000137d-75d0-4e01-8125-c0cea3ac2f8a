import type { NextRequest } from 'next/server';

import { NextResponse } from 'next/server';

// SECURITY FIX: Removed insecure 'decrypt' import
// Session validation should be done server-side only with proper JWT validation
import {
  generateSecurityHeaders,
  generateStrictCSP,
} from '@/lib/security/cspConfig';

// Protected routes that require authentication
const protectedRoutes = [
  '/dashboard',
  '/admin',
  '/delegations',
  '/tasks',
  '/vehicles',
  '/employees',
  '/reports',
  '/reporting', // Add new reporting routes
  '/settings',
];

// Public routes that don't require authentication
const publicRoutes = new Set([
  '/',
  '/forgot-password',
  '/login',
  '/reset-password',
  '/signup',
]);

// Admin-only routes
const adminRoutes = ['/admin', '/settings/system', '/reports/admin'];

// Define JWT payload interface for type safety
interface JWTPayload {
  [key: string]: any;
  exp?: number;
  role?: string;
  sub?: string;
  user_role?: string;
}

/**
 * Main middleware function
 */
export async function middleware(request: NextRequest) {
  // SECURITY FIX: Remove production logging to prevent information disclosure
  // Only log in development environment
  if (process.env.NODE_ENV === 'development') {
    console.log(`🔧 Middleware triggered for: ${request.nextUrl.pathname}`);
  }

  // Generate nonce for CSP
  const nonce = generateNonce();

  // Handle CORS for API routes
  const corsResponse = handleCORS(request);
  if (corsResponse) {
    return corsResponse;
  }

  // Handle authentication
  const authResponse = await handleAuthentication(request);
  if (authResponse) {
    return authResponse;
  }

  // Create response with security headers
  const response = NextResponse.next();

  // Set nonce in both headers and cookies for use in components
  response.headers.set('x-nonce', nonce);
  response.cookies.set('x-nonce', nonce, {
    httpOnly: false, // Needs to be accessible on client side
    path: '/',
    sameSite: 'strict',
    secure: process.env.NODE_ENV === 'production',
  });

  // Debug logging
  if (process.env.NODE_ENV === 'development') {
    console.log(`🍪 Setting nonce cookie: ${nonce.slice(0, 16)}...`);
  }

  // Enhanced 2025 Security Headers with Strict CSP
  const cspConfig = {
    isDevelopment: process.env.NODE_ENV === 'development',
    nonce,
    reportUri: '/api/csp-report',
  };

  const securityHeaders = {
    'Content-Security-Policy': generateStrictCSP(cspConfig),
    ...generateSecurityHeaders(),
    // 2025 Standard: Report-To header for modern violation reporting
    'Report-To': JSON.stringify({
      endpoints: [{ url: '/api/csp-report' }],
      group: 'csp-endpoint',
      max_age: 10_886_400,
    }),
  };

  // Apply security headers
  for (const [key, value] of Object.entries(securityHeaders)) {
    response.headers.set(key, value);
  }

  // CORS headers for allowed origins
  const origin = request.headers.get('origin') ?? '';
  const allowedOrigins = [
    'https://workhub.company.com',
    'https://staging.workhub.company.com',
    ...(process.env.NODE_ENV === 'development'
      ? ['http://localhost:9002', 'http://localhost:3000']
      : []),
  ];

  if (allowedOrigins.includes(origin)) {
    response.headers.set('Access-Control-Allow-Origin', origin);
    response.headers.set('Access-Control-Allow-Credentials', 'true');
  }

  return response;
}

/**
 * Enhanced CSP implementation now handled by cspConfig.ts
 * This provides strict 2025 security standards with comprehensive directives
 */

/**
 * Generate cryptographically secure nonce
 */
function generateNonce(): string {
  // Generate 32 random bytes for a secure nonce
  const bytes = new Uint8Array(32);
  crypto.getRandomValues(bytes);
  return Buffer.from(bytes).toString('base64');
}

/**
 * Authentication middleware
 */
async function handleAuthentication(
  request: NextRequest
): Promise<NextResponse | null> {
  const path = request.nextUrl.pathname;
  const isProtectedRoute = protectedRoutes.some(route =>
    path.startsWith(route)
  );
  const isPublicRoute = publicRoutes.has(path);
  const isAdminRoute = adminRoutes.some(route => path.startsWith(route));

  // Skip auth for public routes and API routes
  if (isPublicRoute || path.startsWith('/api/') || path.startsWith('/_next/')) {
    return null;
  }

  // SECURITY FIX: Use proper JWT token validation instead of insecure session decryption
  // Get JWT token from secure httpOnly cookie (set by backend)
  const accessToken = request.cookies.get('sb-access-token')?.value;

  if (!accessToken && isProtectedRoute) {
    const loginUrl = new URL('/login', request.url);
    loginUrl.searchParams.set('redirect', path);
    return NextResponse.redirect(loginUrl);
  }

  if (accessToken) {
    try {
      // Basic JWT format validation (3 parts separated by dots)
      const tokenParts = accessToken.split('.');
      if (tokenParts.length !== 3) {
        throw new Error('Invalid JWT format');
      }

      // Decode JWT payload (this is safe as it's just base64 decoding for reading claims)
      // Note: This does NOT validate the signature - that must be done server-side
      const payloadPart = tokenParts[1];
      if (!payloadPart) {
        throw new Error('Invalid JWT payload');
      }
      const payload = JSON.parse(atob(payloadPart)) as JWTPayload;

      // Check if token is expired
      const now = Math.floor(Date.now() / 1000);
      if (payload.exp && payload.exp < now) {
        throw new Error('Token expired');
      }

      // Extract user role for admin route protection
      const userRole = payload.user_role ?? payload.role ?? 'USER';

      // Admin route protection
      if (isAdminRoute && !['ADMIN', 'SUPER_ADMIN'].includes(userRole)) {
        return NextResponse.redirect(new URL('/unauthorized', request.url));
      }

      // Redirect authenticated users from auth pages
      if (payload.sub && (path === '/login' || path === '/signup')) {
        return NextResponse.redirect(new URL('/dashboard', request.url));
      }
    } catch {
      // Invalid or expired token, redirect to login for protected routes
      if (isProtectedRoute) {
        const loginUrl = new URL('/login', request.url);
        loginUrl.searchParams.set('redirect', path);
        return NextResponse.redirect(loginUrl);
      }
    }
  }

  return null;
}

/**
 * CORS middleware for API routes
 */
function handleCORS(request: NextRequest): NextResponse | null {
  // Only apply CORS to API routes
  if (!request.nextUrl.pathname.startsWith('/api/')) {
    return null;
  }

  const allowedOrigins = [
    'https://workhub.company.com',
    'https://staging.workhub.company.com',
    ...(process.env.NODE_ENV === 'development'
      ? ['http://localhost:9002', 'http://localhost:3000']
      : []),
  ];

  const origin = request.headers.get('origin') ?? '';
  const isAllowedOrigin = allowedOrigins.includes(origin);

  // Handle preflight requests
  if (request.method === 'OPTIONS') {
    const preflightHeaders = {
      'Access-Control-Allow-Credentials': 'true',
      'Access-Control-Allow-Headers':
        'Content-Type, Authorization, X-CSRF-Token, X-Requested-With',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Max-Age': '86400',
      ...(isAllowedOrigin && { 'Access-Control-Allow-Origin': origin }),
    };

    return new NextResponse(null, { headers: preflightHeaders, status: 200 });
  }

  return null;
}

/**
 * Middleware configuration
 */
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!_next/static|_next/image|favicon.ico).*)',
  ],
};
