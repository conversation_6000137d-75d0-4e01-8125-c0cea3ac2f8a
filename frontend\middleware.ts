import type { NextRequest } from 'next/server';

import createIntlMiddleware from 'next-intl/middleware';
import { NextResponse } from 'next/server';

import { routing } from '@/i18n/routing';

// Create the intl middleware using the routing configuration
const handleI18nRouting = createIntlMiddleware(routing);

// Public routes that don't require authentication
const publicRoutes = new Set([
  '/',
  '/forgot-password',
  '/login',
  '/reset-password',
  '/signup',
]);

// Protected routes that require authentication
const protectedRoutes = [
  '/admin',
  '/delegations',
  '/tasks',
  '/vehicles',
  '/employees',
  '/reports',
  '/reporting',
  '/settings',
  '/reliability',
  '/profile',
  '/gifts',
  '/recipients',
];

// Define JWT payload interface for type safety
interface JWTPayload {
  exp?: number;
  role?: string;
  sub?: string;
  user_role?: string;
}

/**
 * Main middleware function following next-intl best practices
 */
export default function middleware(request: NextRequest) {
  const pathname = request.nextUrl.pathname;

  // Skip middleware for static files and API routes
  if (
    pathname.startsWith('/api/') ||
    pathname.startsWith('/_next/') ||
    pathname.startsWith('/favicon.ico') ||
    pathname.includes('.')
  ) {
    return handleI18nRouting(request);
  }

  // Extract locale and path without locale prefix
  const localeMatch = pathname.match(/^\/([a-z]{2}-[A-Z]{2})(\/.*)?$/);
  const locale = localeMatch?.[1];
  const pathWithoutLocale = localeMatch?.[2] || '/';

  // Check if it's a public route
  const isPublicRoute = publicRoutes.has(pathWithoutLocale);

  // Check if it's a protected route
  const isProtectedRoute = protectedRoutes.some(route =>
    pathWithoutLocale.startsWith(route)
  );

  // Handle authentication for protected routes
  if (isProtectedRoute && !isPublicRoute) {
    const accessToken = request.cookies.get('sb-access-token')?.value;

    if (!accessToken) {
      // Redirect to login with current locale
      const loginUrl = new URL(
        `/${locale || routing.defaultLocale}/login`,
        request.url
      );
      loginUrl.searchParams.set('redirect', pathname);
      return NextResponse.redirect(loginUrl);
    }

    // Basic token validation (signature validation should be done server-side)
    try {
      const tokenParts = accessToken.split('.');
      if (tokenParts.length === 3) {
        const payload = JSON.parse(atob(tokenParts[1])) as JWTPayload;
        const now = Math.floor(Date.now() / 1000);

        if (payload.exp && payload.exp < now) {
          // Token expired, redirect to login
          const loginUrl = new URL(
            `/${locale || routing.defaultLocale}/login`,
            request.url
          );
          loginUrl.searchParams.set('redirect', pathname);
          return NextResponse.redirect(loginUrl);
        }
      }
    } catch {
      // Invalid token, redirect to login
      const loginUrl = new URL(
        `/${locale || routing.defaultLocale}/login`,
        request.url
      );
      loginUrl.searchParams.set('redirect', pathname);
      return NextResponse.redirect(loginUrl);
    }
  }

  // Handle i18n routing
  return handleI18nRouting(request);
}

/**
 * Enhanced CSP implementation now handled by cspConfig.ts
 * This provides strict 2025 security standards with comprehensive directives
 */

/**
 * Generate cryptographically secure nonce
 */
function generateNonce(): string {
  // Generate 32 random bytes for a secure nonce
  const bytes = new Uint8Array(32);
  crypto.getRandomValues(bytes);
  return Buffer.from(bytes).toString('base64');
}

/**
 * Authentication middleware
 */
async function handleAuthentication(
  request: NextRequest
): Promise<NextResponse | null> {
  const path = request.nextUrl.pathname;

  // Extract the path without locale prefix for route matching
  const pathWithoutLocale = path.replace(/^\/[a-z]{2}-[A-Z]{2}/, '') || '/';

  const isProtectedRoute = protectedRoutes.some(route =>
    pathWithoutLocale.startsWith(route)
  );
  const isPublicRoute = publicRoutes.has(pathWithoutLocale);
  const isAdminRoute = adminRoutes.some(route =>
    pathWithoutLocale.startsWith(route)
  );

  // Skip auth for public routes and API routes
  if (isPublicRoute || path.startsWith('/api/') || path.startsWith('/_next/')) {
    return null;
  }

  // SECURITY FIX: Use proper JWT token validation instead of insecure session decryption
  // Get JWT token from secure httpOnly cookie (set by backend)
  const accessToken = request.cookies.get('sb-access-token')?.value;

  if (!accessToken && isProtectedRoute) {
    // Extract locale from path for proper redirect
    const localeMatch = path.match(/^\/([a-z]{2}-[A-Z]{2})/);
    const locale = localeMatch ? localeMatch[1] : defaultLocale;
    const loginUrl = new URL(`/${locale}/login`, request.url);
    loginUrl.searchParams.set('redirect', path);
    return NextResponse.redirect(loginUrl);
  }

  if (accessToken) {
    try {
      // Basic JWT format validation (3 parts separated by dots)
      const tokenParts = accessToken.split('.');
      if (tokenParts.length !== 3) {
        throw new Error('Invalid JWT format');
      }

      // Decode JWT payload (this is safe as it's just base64 decoding for reading claims)
      // Note: This does NOT validate the signature - that must be done server-side
      const payloadPart = tokenParts[1];
      if (!payloadPart) {
        throw new Error('Invalid JWT payload');
      }
      const payload = JSON.parse(atob(payloadPart)) as JWTPayload;

      // Check if token is expired
      const now = Math.floor(Date.now() / 1000);
      if (payload.exp && payload.exp < now) {
        throw new Error('Token expired');
      }

      // Extract user role for admin route protection
      const userRole = payload.user_role ?? payload.role ?? 'USER';

      // Admin route protection
      if (isAdminRoute && !['ADMIN', 'SUPER_ADMIN'].includes(userRole)) {
        // Extract locale for proper redirect
        const localeMatch = path.match(/^\/([a-z]{2}-[A-Z]{2})/);
        const locale = localeMatch ? localeMatch[1] : defaultLocale;
        return NextResponse.redirect(
          new URL(`/${locale}/unauthorized`, request.url)
        );
      }

      // Redirect authenticated users from auth pages
      if (
        payload.sub &&
        (pathWithoutLocale === '/login' || pathWithoutLocale === '/signup')
      ) {
        // Extract locale for proper redirect
        const localeMatch = path.match(/^\/([a-z]{2}-[A-Z]{2})/);
        const locale = localeMatch ? localeMatch[1] : defaultLocale;
        return NextResponse.redirect(new URL(`/${locale}`, request.url));
      }
    } catch {
      // Invalid or expired token, redirect to login for protected routes
      if (isProtectedRoute) {
        // Extract locale for proper redirect
        const localeMatch = path.match(/^\/([a-z]{2}-[A-Z]{2})/);
        const locale = localeMatch ? localeMatch[1] : defaultLocale;
        const loginUrl = new URL(`/${locale}/login`, request.url);
        loginUrl.searchParams.set('redirect', path);
        return NextResponse.redirect(loginUrl);
      }
    }
  }

  return null;
}

/**
 * CORS middleware for API routes
 */
function handleCORS(request: NextRequest): NextResponse | null {
  // Only apply CORS to API routes
  if (!request.nextUrl.pathname.startsWith('/api/')) {
    return null;
  }

  const allowedOrigins = [
    'https://workhub.company.com',
    'https://staging.workhub.company.com',
    ...(process.env.NODE_ENV === 'development'
      ? ['http://localhost:9002', 'http://localhost:3000']
      : []),
  ];

  const origin = request.headers.get('origin') ?? '';
  const isAllowedOrigin = allowedOrigins.includes(origin);

  // Handle preflight requests
  if (request.method === 'OPTIONS') {
    const preflightHeaders = {
      'Access-Control-Allow-Credentials': 'true',
      'Access-Control-Allow-Headers':
        'Content-Type, Authorization, X-CSRF-Token, X-Requested-With',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Max-Age': '86400',
      ...(isAllowedOrigin && { 'Access-Control-Allow-Origin': origin }),
    };

    return new NextResponse(null, { headers: preflightHeaders, status: 200 });
  }

  return null;
}

/**
 * Middleware configuration
 */
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!_next/static|_next/image|favicon.ico).*)',
  ],
};
