'use client';

import { Check, Globe, Languages } from 'lucide-react';
import { useState } from 'react';

import { useI18n } from '@/lib/i18n/hooks';
import type { Locale } from '@/src/i18n';

import { ActionButton } from '@/components/ui/action-button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';

interface LanguageSwitcherProps {
  variant?: 'default' | 'compact' | 'icon-only';
  className?: string;
}

export const LanguageSwitcher: React.FC<LanguageSwitcherProps> = ({
  variant = 'default',
  className,
}) => {
  const { locale, localeName, switchLocale, availableLocales, isRTL } = useI18n();
  const [isOpen, setIsOpen] = useState(false);

  const handleLocaleChange = (newLocale: Locale) => {
    switchLocale(newLocale);
    setIsOpen(false);
  };

  const renderTrigger = () => {
    switch (variant) {
      case 'icon-only':
        return (
          <ActionButton
            variant="ghost"
            size="sm"
            className={cn('h-8 w-8 p-0', className)}
            aria-label="Switch language"
          >
            <Languages className="h-4 w-4" />
          </ActionButton>
        );
      
      case 'compact':
        return (
          <ActionButton
            variant="ghost"
            size="sm"
            className={cn('h-8 px-2', className)}
          >
            <Globe className="h-4 w-4 mr-1" />
            <span className="text-xs font-medium uppercase">{locale}</span>
          </ActionButton>
        );
      
      default:
        return (
          <ActionButton
            variant="ghost"
            size="sm"
            className={cn('h-8 px-3', className)}
          >
            <Globe className="h-4 w-4 mr-2" />
            <span className="text-sm">{localeName}</span>
            {isRTL && (
              <Badge variant="secondary" className="ml-2 text-xs">
                RTL
              </Badge>
            )}
          </ActionButton>
        );
    }
  };

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        {renderTrigger()}
      </DropdownMenuTrigger>
      <DropdownMenuContent align={isRTL ? 'start' : 'end'} className="w-48">
        <DropdownMenuLabel className="flex items-center gap-2">
          <Languages className="h-4 w-4" />
          Language / اللغة
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        
        {availableLocales.map((localeOption) => (
          <DropdownMenuItem
            key={localeOption.code}
            onClick={() => handleLocaleChange(localeOption.code as Locale)}
            className={cn(
              'flex items-center justify-between cursor-pointer',
              localeOption.isActive && 'bg-accent'
            )}
          >
            <div className="flex items-center gap-3">
              <div className="flex flex-col">
                <span className="font-medium">{localeOption.name}</span>
                <span className="text-xs text-muted-foreground uppercase">
                  {localeOption.code}
                  {localeOption.direction === 'rtl' && ' • RTL'}
                </span>
              </div>
            </div>
            
            {localeOption.isActive && (
              <Check className="h-4 w-4 text-primary" />
            )}
          </DropdownMenuItem>
        ))}
        
        <DropdownMenuSeparator />
        <div className="px-2 py-1 text-xs text-muted-foreground">
          Current: {localeName} ({locale.toUpperCase()})
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
