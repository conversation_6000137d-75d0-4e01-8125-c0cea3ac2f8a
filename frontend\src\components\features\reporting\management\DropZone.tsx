/**
 * @file DropZone.tsx
 * @description Drop zone component for report builder drag-and-drop functionality
 */

import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useDroppable } from '@dnd-kit/core';
import { useSortable } from '@dnd-kit/sortable';
import {
  SortableContext,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { Settings, Trash2, GripVertical, Layout, Plus } from 'lucide-react';
import { cn } from '@/lib/utils';
import type { WidgetConfig } from '../data/types/reporting';

/**
 * Props interface for DropZone
 */
interface DropZoneProps {
  widgets: WidgetConfig[];
  columns: number;
  onWidgetConfigure: (widget: WidgetConfig) => void;
  onWidgetDelete: (widgetId: string) => void;
  className?: string;
}

/**
 * Sortable widget item component
 */
interface SortableWidgetProps {
  widget: WidgetConfig;
  onConfigure: () => void;
  onDelete: () => void;
}

const SortableWidget: React.FC<SortableWidgetProps> = ({
  widget,
  onConfigure,
  onDelete,
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: widget.id,
    data: {
      type: 'widget',
      widget,
    },
  });

  const style = {
    transform: transform
      ? `translate3d(${transform.x}px, ${transform.y}px, 0)`
      : undefined,
    transition,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={cn('group relative', isDragging && 'opacity-50')}
    >
      <Card className="border-2 border-dashed border-gray-200 hover:border-blue-300 transition-colors">
        <CardContent className="p-4">
          {/* Widget Header */}
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-2">
              <div
                {...attributes}
                {...listeners}
                className="cursor-grab active:cursor-grabbing p-1 hover:bg-gray-100 rounded"
              >
                <GripVertical className="h-4 w-4 text-gray-400" />
              </div>
              <h4 className="text-sm font-medium">{widget.title}</h4>
            </div>

            <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
              <Button
                variant="ghost"
                size="sm"
                onClick={onConfigure}
                className="h-8 w-8 p-0"
              >
                <Settings className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={onDelete}
                className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Widget Preview */}
          <div className="bg-gray-50 rounded-lg p-4 min-h-[120px] flex items-center justify-center">
            <div className="text-center text-gray-500">
              <Layout className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <div className="text-sm">{widget.type} Widget</div>
              <div className="text-xs mt-1">
                {widget.config && Object.keys(widget.config).length > 0
                  ? 'Configured'
                  : 'Click settings to configure'}
              </div>
            </div>
          </div>

          {/* Widget Info */}
          <div className="mt-3 flex items-center justify-between text-xs text-gray-500">
            <span>Type: {widget.type}</span>
            <span>Span: {widget.span}</span>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

/**
 * DropZone Component
 *
 * Provides drop zone for widgets in the report builder.
 *
 * Responsibilities:
 * - Accept dropped widgets from palette
 * - Display arranged widgets in grid layout
 * - Handle widget reordering via drag-and-drop
 * - Provide widget configuration and deletion
 *
 * SOLID Principles Applied:
 * - SRP: Single responsibility of managing widget drop zone
 * - OCP: Open for extension via widget types
 * - DIP: Depends on drag-and-drop framework abstractions
 */
export const DropZone: React.FC<DropZoneProps> = ({
  widgets,
  columns,
  onWidgetConfigure,
  onWidgetDelete,
  className = '',
}) => {
  const { isOver, setNodeRef } = useDroppable({
    id: 'drop-zone',
  });

  // Calculate grid columns class
  const gridColsClass =
    {
      1: 'grid-cols-1',
      2: 'grid-cols-1 md:grid-cols-2',
      3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
      4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
    }[columns] || 'grid-cols-2';

  return (
    <Card className={cn('min-h-[400px]', className)}>
      <CardContent className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium">Report Canvas</h3>
          <div className="text-sm text-gray-500">
            {widgets.length} widget{widgets.length !== 1 ? 's' : ''}
          </div>
        </div>

        <div
          ref={setNodeRef}
          className={cn(
            'min-h-[300px] border-2 border-dashed rounded-lg p-4 transition-colors',
            isOver ? 'border-blue-400 bg-blue-50' : 'border-gray-200',
            widgets.length === 0 && 'flex items-center justify-center'
          )}
        >
          {widgets.length === 0 ? (
            // Empty state
            <div className="text-center text-gray-500">
              <Layout className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <h4 className="text-lg font-medium mb-2">
                Start Building Your Report
              </h4>
              <p className="text-sm mb-4">
                Drag widgets from the palette on the left to create your custom
                report
              </p>
              <div className="flex items-center justify-center gap-2 text-xs">
                <Plus className="h-4 w-4" />
                <span>Drop widgets here</span>
              </div>
            </div>
          ) : (
            // Widgets grid
            <SortableContext
              items={widgets.map(w => w.id)}
              strategy={verticalListSortingStrategy}
            >
              <div className={cn('grid gap-4', gridColsClass)}>
                {widgets
                  .sort((a, b) => (a.position || 0) - (b.position || 0))
                  .map(widget => (
                    <SortableWidget
                      key={widget.id}
                      widget={widget}
                      onConfigure={() => onWidgetConfigure(widget)}
                      onDelete={() => onWidgetDelete(widget.id)}
                    />
                  ))}
              </div>
            </SortableContext>
          )}
        </div>

        {/* Drop zone instructions */}
        {isOver && (
          <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center gap-2 text-blue-700">
              <Plus className="h-4 w-4" />
              <span className="text-sm font-medium">
                Drop widget here to add to report
              </span>
            </div>
          </div>
        )}

        {/* Layout controls */}
        <div className="mt-4 pt-4 border-t">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-600">
              Layout: {columns} column{columns !== 1 ? 's' : ''}
            </div>
            <div className="text-xs text-gray-500">
              Drag widgets to reorder • Click settings to configure
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default DropZone;
