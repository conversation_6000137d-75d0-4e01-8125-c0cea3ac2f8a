/**
 * @file Dependency health widget component.
 * This component displays external service and dependency status.
 * @module components/reliability/widgets/alerts/DependencyHealth
 */

'use client';

import {
  AlertTriangle,
  CheckCircle,
  Clock,
  Database,
  Globe,
  Server,
  Wifi,
  XCircle,
} from 'lucide-react';
import React from 'react';

import type { HealthStatus } from '@/lib/types/domain';

import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { useDependencyHealth } from '@/lib/stores/queries/useReliability';
import { cn } from '@/lib/utils';

/**
 * Props for the DependencyHealth component
 */
export interface DependencyHealthProps {
  /** Optional CSS class name for styling */
  className?: string;
}

/**
 * Dependency health widget component.
 *
 * This component provides:
 * - External service health monitoring
 * - Dependency status indicators
 * - Response time tracking
 * - Overall dependency health score
 *
 * Features:
 * - Real-time health status updates
 * - Color-coded status indicators
 * - Response time metrics
 * - Responsive design
 *
 * @param props - Component props
 * @returns JSX element representing the dependency health widget
 */
export const DependencyHealth: React.FC<DependencyHealthProps> = ({
  className = '',
}) => {
  const { data: dependencyHealth, error, isLoading } = useDependencyHealth();

  /**
   * Get status configuration
   */
  const getStatusConfig = (status: HealthStatus) => {
    switch (status) {
      case 'degraded': {
        return {
          bgColor: 'bg-yellow-100 dark:bg-yellow-900/20',
          color: 'text-yellow-600 dark:text-yellow-400',
          icon: AlertTriangle,
          variant: 'secondary' as const,
        };
      }
      case 'healthy': {
        return {
          bgColor: 'bg-green-100 dark:bg-green-900/20',
          color: 'text-green-600 dark:text-green-400',
          icon: CheckCircle,
          variant: 'default' as const,
        };
      }
      case 'unhealthy': {
        return {
          bgColor: 'bg-red-100 dark:bg-red-900/20',
          color: 'text-red-600 dark:text-red-400',
          icon: XCircle,
          variant: 'destructive' as const,
        };
      }
      default: {
        return {
          bgColor: 'bg-gray-100 dark:bg-gray-900/20',
          color: 'text-gray-600 dark:text-gray-400',
          icon: Clock,
          variant: 'outline' as const,
        };
      }
    }
  };

  /**
   * Get dependency icon
   */
  const getDependencyIcon = (name: string) => {
    const lowerName = name.toLowerCase();
    if (lowerName.includes('database') || lowerName.includes('db'))
      return Database;
    if (lowerName.includes('api') || lowerName.includes('service'))
      return Server;
    if (lowerName.includes('network') || lowerName.includes('connection'))
      return Wifi;
    return Globe;
  };

  /**
   * Calculate overall health score
   */
  const getHealthScore = () => {
    if (!dependencyHealth?.summary) return 0;

    const { healthy, total } = dependencyHealth.summary;
    return total > 0 ? Math.round((healthy / total) * 100) : 100;
  };

  /**
   * Calculate overall status from summary
   */
  const getOverallStatus = (): HealthStatus => {
    if (!dependencyHealth?.summary) return 'unhealthy';

    const { degraded, healthy, unhealthy } = dependencyHealth.summary;

    if (unhealthy > 0) return 'unhealthy';
    if (degraded > 0) return 'degraded';
    return 'healthy';
  };

  // Handle loading state
  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="size-5" />
            Dependency Health
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Array.from({ length: 3 }).map((_, i) => (
              <div className="animate-pulse" key={i}>
                <div className="h-16 rounded bg-muted"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  // Handle error state
  if (error || !dependencyHealth) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="size-5" />
            Dependency Health
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="py-4 text-center">
            <AlertTriangle className="mx-auto mb-2 size-8 text-muted-foreground" />
            <p className="text-sm text-muted-foreground">
              Failed to load dependency health
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const healthScore = getHealthScore();
  const overallStatus = getOverallStatus();
  const overallConfig = getStatusConfig(overallStatus);

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Globe className="size-5" />
            Dependency Health
          </div>
          <Badge variant={overallConfig.variant}>{healthScore}%</Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Overall Health Score */}
          <div>
            <div className="mb-2 flex items-center justify-between">
              <span className="text-sm font-medium">Overall Health</span>
              <span className="text-sm text-muted-foreground">
                {healthScore}%
              </span>
            </div>
            <Progress className="h-2" value={healthScore} />
          </div>

          {/* Dependencies List */}
          <div className="space-y-3">
            {(dependencyHealth.dependencies || []).map(dependency => {
              const config = getStatusConfig(dependency.status);
              const Icon = config.icon;
              const DependencyIcon = getDependencyIcon(dependency.name);

              return (
                <div
                  className={cn(
                    'flex items-center justify-between p-3 rounded-lg border',
                    config.bgColor
                  )}
                  key={dependency.name}
                >
                  <div className="flex items-center gap-3">
                    <div className="flex items-center gap-2">
                      <DependencyIcon className="size-4 text-muted-foreground" />
                      <Icon className={cn('h-4 w-4', config.color)} />
                    </div>
                    <div>
                      <p className="font-medium capitalize">
                        {dependency.name.replaceAll(/([A-Z])/g, ' $1').trim()}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        {dependency.status === 'healthy'
                          ? 'Operating normally'
                          : dependency.status === 'degraded'
                            ? 'Performance issues'
                            : 'Service unavailable'}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <Badge variant={config.variant}>{dependency.status}</Badge>
                    {dependency.responseTime && (
                      <p className="mt-1 text-xs text-muted-foreground">
                        {dependency.responseTime}ms
                      </p>
                    )}
                  </div>
                </div>
              );
            })}
          </div>

          {/* Status Summary */}
          <div className={cn('p-3 rounded-lg', overallConfig.bgColor)}>
            <div className="flex items-center gap-2">
              <overallConfig.icon
                className={cn('h-4 w-4', overallConfig.color)}
              />
              <span className="font-medium">
                {overallStatus === 'healthy'
                  ? 'All dependencies healthy'
                  : overallStatus === 'degraded'
                    ? 'Some dependencies degraded'
                    : 'Critical dependencies failing'}
              </span>
            </div>
            <p className="mt-1 text-sm text-muted-foreground">
              Last checked: {new Date().toLocaleTimeString()}
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

/**
 * Default export for the DependencyHealth component
 */
export default DependencyHealth;
