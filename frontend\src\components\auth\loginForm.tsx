'use client';

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ArrowRight,
  Check,
  Eye,
  EyeOff,
  Loader2,
  Lock,
  Mail,
  Shield,
  Wifi,
  WifiOff,
} from 'lucide-react';
import React, { useEffect, useState } from 'react';

import { useAuthContext } from '../../contexts/AuthContext';
import { useLoginValidation } from '../../hooks/forms/useLoginValidation';
import { Alert, AlertDescription } from '../ui/alert';
import { Button } from '../ui/button';
import { Checkbox } from '../ui/checkbox';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { LoginLoading } from '../ui/login-loading';

interface LoginFormProps {
  onForgotPassword?: () => void;
  onSignUp?: () => void;
  onSuccess?: () => void;
}

/**
 * Modern Login Form with Advanced Features
 *
 * A comprehensive login interface featuring:
 * - Modern minimalist aesthetics
 * - Real-time validation with custom hooks
 * - Network status awareness
 * - Enhanced accessibility and keyboard navigation
 * - Progressive enhancement and error recovery
 * - Remember me functionality
 * - Advanced loading states with proper state management
 */
export function LoginForm({
  onForgotPassword,
  onSignUp,
  onSuccess,
}: LoginFormProps) {
  const { clearError, error, loading, signIn } = useAuthContext();
  const {
    clearAllErrors,
    clearFieldError,
    errors,
    isFieldValid,
    markFormTouched,
    validateForm,
  } = useLoginValidation();

  const [formData, setFormData] = useState({
    email: '',
    password: '',
    rememberMe: false,
  });
  const [showPassword, setShowPassword] = useState(false);
  const [loadingStage, setLoadingStage] = useState<
    'authenticating' | 'redirecting' | 'success' | 'verifying'
  >('authenticating');
  const [isOnline, setIsOnline] = useState(true); // Default to true for SSR
  const [focusedField, setFocusedField] = useState<null | string>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Network status monitoring
  useEffect(() => {
    // Check if we're in the browser environment
    if (globalThis.window !== undefined && typeof navigator !== 'undefined') {
      // Set initial online status
      setIsOnline(navigator.onLine);

      const handleOnline = () => setIsOnline(true);
      const handleOffline = () => setIsOnline(false);

      globalThis.addEventListener('online', handleOnline);
      globalThis.addEventListener('offline', handleOffline);

      return () => {
        globalThis.removeEventListener('online', handleOnline);
        globalThis.removeEventListener('offline', handleOffline);
      };
    }
    // Explicitly return an empty cleanup function if not in browser environment
    return () => {};
  }, []);

  // Load remembered email on component mount
  useEffect(() => {
    // Check if we're in the browser environment
    if (
      globalThis.window !== undefined &&
      typeof localStorage !== 'undefined'
    ) {
      const rememberedEmail = localStorage.getItem('workhub_remember_email');
      if (rememberedEmail) {
        setFormData(prev => ({
          ...prev,
          email: rememberedEmail,
          rememberMe: true,
        }));
      }
    }
  }, []);

  // Enhanced form submission with proper loading state management
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Prevent double submission
    if (isSubmitting || loading) {
      return;
    }

    markFormTouched();
    setIsSubmitting(true);

    // Clear previous errors
    clearError();
    clearAllErrors();

    // Validate form
    const validation = validateForm(formData);
    if (!validation.isValid) {
      setIsSubmitting(false);
      return;
    }

    // Check network connectivity
    if (!isOnline) {
      setIsSubmitting(false);
      return;
    }

    try {
      setLoadingStage('authenticating');

      const { error } = await signIn(formData.email, formData.password);

      if (error) {
        setIsSubmitting(false);
      } else {
        setLoadingStage('success');

        // Store remember me preference
        if (
          globalThis.window !== undefined &&
          typeof localStorage !== 'undefined'
        ) {
          if (formData.rememberMe) {
            localStorage.setItem('workhub_remember_email', formData.email);
          } else {
            localStorage.removeItem('workhub_remember_email');
          }
        }

        // Brief success state before redirect - prevent double loading
        setTimeout(() => {
          setIsSubmitting(false);
          onSuccess?.();
        }, 800);
      }
    } catch (error_) {
      console.error('Login error:', error_);
      setIsSubmitting(false);
    }
  };

  // Enhanced input handling with validation
  const handleInputChange = (field: string, value: boolean | string) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // Clear field error when user starts typing
    if (typeof value === 'string' && errors[field as keyof typeof errors]) {
      clearFieldError(field as keyof typeof errors);
    }

    // Clear global error when user starts typing
    if (error) {
      clearError();
    }
  };

  // Handle field focus for enhanced UX
  const handleFieldFocus = (fieldName: string) => {
    setFocusedField(fieldName);
  };

  const handleFieldBlur = () => {
    setFocusedField(null);
  };

  // Show loading overlay during authentication - fix double loading
  if (isSubmitting) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gradient-to-br from-background via-background to-muted/20 p-4">
        <div className="w-full max-w-md">
          <div className="rounded-2xl border border-border/60 bg-card shadow-xl backdrop-blur-sm">
            <LoginLoading stage={loadingStage} />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex min-h-screen items-center justify-center bg-gradient-to-br from-background via-background to-muted/20 p-4">
      <div className="w-full max-w-md">
        {/* Network Status Indicator */}
        {!isOnline && (
          <div className="mb-4 flex items-center gap-2 rounded-xl border border-destructive/20 bg-destructive/10 p-3 text-destructive">
            <WifiOff className="size-4" />
            <span className="text-sm font-medium">No internet connection</span>
          </div>
        )}

        {/* Header Section */}
        <div className="mb-8 text-center">
          {/* Logo with enhanced animation */}
          <div className="group mx-auto mb-6 flex size-16 items-center justify-center rounded-2xl bg-gradient-to-r from-primary to-accent shadow-lg ring-1 ring-primary/20 transition-all duration-300 hover:ring-primary/40">
            <Lock className="size-8 text-primary-foreground transition-transform group-hover:scale-110" />
          </div>

          {/* Enhanced title with connection status */}
          <h1 className="mb-2 text-3xl font-bold tracking-tight text-foreground">
            Welcome back
          </h1>
          <div className="flex items-center justify-center gap-2 text-muted-foreground">
            <span>Sign in to your WorkHub account</span>
            {isOnline && <Wifi className="size-4 text-green-600" />}
          </div>
        </div>

        {/* Main Form Card */}
        <div className="rounded-2xl border border-border/60 bg-card p-8 shadow-xl backdrop-blur-sm">
          {/* Global Error Alert */}
          {error && (
            <Alert
              className="mb-6 border-destructive/20 bg-destructive/5"
              variant="destructive"
            >
              <AlertCircle className="size-4" />
              <AlertDescription className="text-destructive">
                {error}
              </AlertDescription>
            </Alert>
          )}

          {/* Offline Warning */}
          {!isOnline && (
            <Alert className="mb-6 border-yellow-200 bg-yellow-50">
              <WifiOff className="size-4" />
              <AlertDescription>
                You're currently offline. Please check your internet connection
                to sign in.
              </AlertDescription>
            </Alert>
          )}

          <form className="space-y-6" onSubmit={handleSubmit}>
            {/* Email Field with Enhanced UX */}
            <div className="space-y-2">
              <Label
                className={`text-sm font-medium transition-colors ${
                  focusedField === 'email' ? 'text-primary' : 'text-foreground'
                }`}
                htmlFor="email"
              >
                Email address
              </Label>
              <div className="group relative">
                <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                  <Mail
                    className={`size-5 transition-colors ${
                      focusedField === 'email'
                        ? 'text-primary'
                        : 'text-muted-foreground'
                    }`}
                  />
                </div>
                <Input
                  autoComplete="email"
                  className={`h-12 pl-10 transition-all duration-200 ${
                    errors.email
                      ? 'border-destructive/50 focus-visible:ring-destructive/20'
                      : 'hover:border-primary/30 focus-visible:ring-primary/20'
                  } ${
                    isFieldValid('email', formData.email)
                      ? 'border-green-500/50 focus-visible:ring-green-500/20'
                      : ''
                  }`}
                  disabled={loading || !isOnline || isSubmitting}
                  id="email"
                  onBlur={handleFieldBlur}
                  onChange={e => handleInputChange('email', e.target.value)}
                  onFocus={() => handleFieldFocus('email')}
                  placeholder="Enter your email"
                  type="email"
                  value={formData.email}
                />
                {/* Enhanced success indicator */}
                {isFieldValid('email', formData.email) && (
                  <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                    <Check className="size-5 text-green-500 duration-200 animate-in fade-in-0 zoom-in-95" />
                  </div>
                )}
              </div>
              {errors.email && (
                <p className="mt-1 flex items-center gap-2 text-sm text-destructive duration-200 animate-in slide-in-from-top-1">
                  <AlertCircle className="size-4" />
                  {errors.email}
                </p>
              )}
            </div>

            {/* Password Field with Enhanced UX */}
            <div className="space-y-2">
              <Label
                className={`text-sm font-medium transition-colors ${
                  focusedField === 'password'
                    ? 'text-primary'
                    : 'text-foreground'
                }`}
                htmlFor="password"
              >
                Password
              </Label>
              <div className="group relative">
                <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                  <Lock
                    className={`size-5 transition-colors ${
                      focusedField === 'password'
                        ? 'text-primary'
                        : 'text-muted-foreground'
                    }`}
                  />
                </div>
                <Input
                  autoComplete="current-password"
                  className={`h-12 pl-10 pr-12 transition-all duration-200 ${
                    errors.password
                      ? 'border-destructive/50 focus-visible:ring-destructive/20'
                      : 'hover:border-primary/30 focus-visible:ring-primary/20'
                  } ${
                    isFieldValid('password', formData.password)
                      ? 'border-green-500/50 focus-visible:ring-green-500/20'
                      : ''
                  }`}
                  disabled={loading || !isOnline || isSubmitting}
                  id="password"
                  onBlur={handleFieldBlur}
                  onChange={e => handleInputChange('password', e.target.value)}
                  onFocus={() => handleFieldFocus('password')}
                  placeholder="Enter your password"
                  type={showPassword ? 'text' : 'password'}
                  value={formData.password}
                />
                {/* Password visibility toggle */}
                <button
                  aria-label={showPassword ? 'Hide password' : 'Show password'}
                  className="absolute inset-y-0 right-0 flex items-center pr-3 text-muted-foreground transition-colors hover:text-foreground"
                  disabled={loading || isSubmitting}
                  onClick={() => setShowPassword(!showPassword)}
                  type="button"
                >
                  {showPassword ? (
                    <EyeOff className="size-5" />
                  ) : (
                    <Eye className="size-5" />
                  )}
                </button>
              </div>
              {errors.password && (
                <p className="mt-1 flex items-center gap-2 text-sm text-destructive duration-200 animate-in slide-in-from-top-1">
                  <AlertCircle className="size-4" />
                  {errors.password}
                </p>
              )}
            </div>

            {/* Remember Me & Forgot Password */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Checkbox
                  checked={formData.rememberMe}
                  className="data-[state=checked]:border-primary data-[state=checked]:bg-primary"
                  disabled={loading || isSubmitting}
                  id="remember-me"
                  onCheckedChange={checked =>
                    handleInputChange('rememberMe', checked)
                  }
                />
                <Label
                  className="cursor-pointer text-sm text-muted-foreground transition-colors hover:text-foreground"
                  htmlFor="remember-me"
                >
                  Remember me
                </Label>
              </div>

              {onForgotPassword && (
                <button
                  className="text-sm font-medium text-primary transition-colors hover:text-primary/80"
                  disabled={loading || isSubmitting}
                  onClick={onForgotPassword}
                  type="button"
                >
                  Forgot password?
                </button>
              )}
            </div>

            {/* Submit Button */}
            <Button
              className="group h-12 w-full rounded-xl bg-gradient-to-r from-primary to-accent font-semibold text-primary-foreground shadow-lg transition-all duration-200 hover:from-primary/90 hover:to-accent/90 hover:shadow-xl disabled:opacity-50"
              disabled={loading || !isOnline || isSubmitting}
              type="submit"
            >
              {loading || isSubmitting ? (
                <>
                  <Loader2 className="mr-2 size-5 animate-spin" />
                  Signing in...
                </>
              ) : (
                <>
                  Sign in
                  <ArrowRight className="ml-2 size-5 transition-transform group-hover:translate-x-1" />
                </>
              )}
            </Button>

            {/* Demo Credentials */}
            <div className="rounded-xl border border-border/40 bg-muted/30 p-4">
              <div className="mb-2 flex items-center gap-2">
                <Shield className="size-4 text-muted-foreground" />
                <span className="text-sm font-medium text-muted-foreground">
                  Demo Access
                </span>
              </div>
              <div className="space-y-1 text-xs text-muted-foreground">
                <div className="font-mono"><EMAIL></div>
                <div className="font-mono">demo123</div>
              </div>
            </div>
          </form>
        </div>

        {/* Sign Up Link */}
        {onSignUp && (
          <div className="mt-6 text-center">
            <p className="text-sm text-muted-foreground">
              Don't have an account?{' '}
              <button
                className="font-medium text-primary transition-colors hover:text-primary/80"
                disabled={loading || isSubmitting}
                onClick={onSignUp}
              >
                Create one now
              </button>
            </p>
          </div>
        )}

        {/* Enhanced Security Notice */}
        <div className="mt-8 text-center">
          <div className="inline-flex items-center gap-2 rounded-full border border-border/40 bg-card px-4 py-2 text-xs text-muted-foreground">
            <Shield className="size-4 text-green-600" />
            <span>Protected by enterprise-grade security</span>
          </div>
        </div>

        {/* Footer */}
        <footer className="mt-8 text-center text-xs text-muted-foreground">
          <p>© 2024 WorkHub. All rights reserved.</p>
          <div className="mt-2 space-x-4">
            <a className="transition-colors hover:text-foreground" href="#">
              Terms
            </a>
            <a className="transition-colors hover:text-foreground" href="#">
              Privacy
            </a>
            <a className="transition-colors hover:text-foreground" href="#">
              Support
            </a>
          </div>
        </footer>
      </div>
    </div>
  );
}
