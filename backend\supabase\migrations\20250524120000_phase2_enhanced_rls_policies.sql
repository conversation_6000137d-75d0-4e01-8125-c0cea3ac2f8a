-- PHASE 2.1: Enhanced RLS Policies Implementation
-- This migration implements granular role-based access controls for all tables
-- Date: May 24, 2025
-- Phase: 2.1 - Enhanced RLS Policies & Basic Role UI Foundation

-- =====================================================
-- STEP 1: ENSURE USER_PROFILES TABLE IS PROPERLY CONFIGURED
-- =====================================================

-- Ensure user_profiles table exists with correct structure
CREATE TABLE IF NOT EXISTS public.user_profiles (
    id UUID PRIMARY KEY,                    -- Maps to auth.users.id
    role TEXT DEFAULT 'USER' CHECK (role IN ('SUPER_ADMIN', 'ADMIN', 'MANAGER', 'USER', 'READONLY')),
    employee_id INTEGER UNIQUE,            -- Optional link to Employee table
    is_active BOOLEAN DEFAULT true,        -- Account status
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on user_profiles if not already enabled
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;

-- Add foreign key constraint to Employee table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'user_profiles_employee_id_fkey'
    ) THEN
        ALTER TABLE public.user_profiles 
        ADD CONSTRAINT user_profiles_employee_id_fkey 
        FOREIGN KEY (employee_id) REFERENCES public."Employee"(id) ON DELETE SET NULL;
    END IF;
END $$;

-- =====================================================
-- STEP 2: UPDATE AUTH HELPER FUNCTIONS FOR ENHANCED RLS
-- =====================================================

-- Enhanced function to get user role from JWT custom claims
CREATE OR REPLACE FUNCTION auth.get_user_role()
RETURNS TEXT
LANGUAGE plpgsql
STABLE
SECURITY DEFINER
AS $$
DECLARE
    user_role TEXT;
BEGIN
    -- Get role from JWT custom claims
    SELECT COALESCE(
        auth.jwt() ->> 'user_role',
        (auth.jwt() -> 'app_metadata' -> 'custom_claims' ->> 'user_role'),
        'USER'
    ) INTO user_role;
    
    RETURN user_role;
END;
$$;

-- Enhanced function to check if user is active
CREATE OR REPLACE FUNCTION auth.is_user_active()
RETURNS BOOLEAN
LANGUAGE plpgsql
STABLE
SECURITY DEFINER
AS $$
DECLARE
    is_active BOOLEAN;
BEGIN
    -- Get active status from JWT custom claims
    SELECT COALESCE(
        (auth.jwt() ->> 'is_active')::BOOLEAN,
        (auth.jwt() -> 'app_metadata' -> 'custom_claims' ->> 'is_active')::BOOLEAN,
        true
    ) INTO is_active;
    
    RETURN is_active;
END;
$$;

-- Enhanced function to get user employee ID
CREATE OR REPLACE FUNCTION auth.get_user_employee_id()
RETURNS INTEGER
LANGUAGE plpgsql
STABLE
SECURITY DEFINER
AS $$
DECLARE
    employee_id INTEGER;
BEGIN
    -- Get employee_id from JWT custom claims
    SELECT COALESCE(
        (auth.jwt() ->> 'employee_id')::INTEGER,
        (auth.jwt() -> 'app_metadata' -> 'custom_claims' ->> 'employee_id')::INTEGER
    ) INTO employee_id;
    
    RETURN employee_id;
END;
$$;

-- Enhanced role hierarchy checking functions
CREATE OR REPLACE FUNCTION auth.is_super_admin()
RETURNS BOOLEAN
LANGUAGE plpgsql
STABLE
SECURITY DEFINER
AS $$
BEGIN
    RETURN auth.get_user_role() = 'SUPER_ADMIN' AND auth.is_user_active();
END;
$$;

CREATE OR REPLACE FUNCTION auth.is_admin_or_above()
RETURNS BOOLEAN
LANGUAGE plpgsql
STABLE
SECURITY DEFINER
AS $$
BEGIN
    RETURN auth.get_user_role() IN ('SUPER_ADMIN', 'ADMIN') AND auth.is_user_active();
END;
$$;

CREATE OR REPLACE FUNCTION auth.is_manager_or_above()
RETURNS BOOLEAN
LANGUAGE plpgsql
STABLE
SECURITY DEFINER
AS $$
BEGIN
    RETURN auth.get_user_role() IN ('SUPER_ADMIN', 'ADMIN', 'MANAGER') AND auth.is_user_active();
END;
$$;

CREATE OR REPLACE FUNCTION auth.has_role(required_roles TEXT[])
RETURNS BOOLEAN
LANGUAGE plpgsql
STABLE
SECURITY DEFINER
AS $$
BEGIN
    RETURN auth.get_user_role() = ANY(required_roles) AND auth.is_user_active();
END;
$$;

-- =====================================================
-- STEP 3: ENHANCED RLS POLICIES FOR USER_PROFILES
-- =====================================================

-- Drop existing user_profiles policies
DROP POLICY IF EXISTS "Users can manage own profile" ON public.user_profiles;
DROP POLICY IF EXISTS "Admins can view all profiles" ON public.user_profiles;

-- Users can view and update their own profile
CREATE POLICY "user_profiles_own_access" ON public.user_profiles
    FOR ALL USING (
        auth.uid() = id AND auth.is_user_active()
    );

-- Admins can view all profiles
CREATE POLICY "user_profiles_admin_view" ON public.user_profiles
    FOR SELECT USING (
        auth.is_admin_or_above()
    );

-- Super admins can modify all profiles
CREATE POLICY "user_profiles_super_admin_modify" ON public.user_profiles
    FOR INSERT, UPDATE, DELETE USING (
        auth.is_super_admin()
    );

-- =====================================================
-- STEP 4: ENHANCED RLS POLICIES FOR EMPLOYEE TABLE
-- =====================================================

-- Drop existing employee policies
DROP POLICY IF EXISTS "employee_select_policy" ON public."Employee";
DROP POLICY IF EXISTS "employee_insert_policy" ON public."Employee";
DROP POLICY IF EXISTS "employee_update_policy" ON public."Employee";
DROP POLICY IF EXISTS "employee_delete_policy" ON public."Employee";

-- Enhanced Employee SELECT policy with granular access
CREATE POLICY "employee_enhanced_select" ON public."Employee"
    FOR SELECT USING (
        -- Super admins can see all employees
        auth.is_super_admin()
        OR
        -- Admins can see all employees
        auth.is_admin_or_above()
        OR
        -- Managers can see all employees (for team management)
        auth.is_manager_or_above()
        OR
        -- Users can see their own employee record
        (auth.get_user_role() = 'USER' AND id = auth.get_user_employee_id())
        OR
        -- Readonly users can see all employees (read-only access)
        auth.get_user_role() = 'READONLY'
    );

-- Enhanced Employee INSERT policy (admin and above only)
CREATE POLICY "employee_enhanced_insert" ON public."Employee"
    FOR INSERT WITH CHECK (
        auth.is_admin_or_above()
    );

-- Enhanced Employee UPDATE policy with granular permissions
CREATE POLICY "employee_enhanced_update" ON public."Employee"
    FOR UPDATE USING (
        -- Super admins can update all employees
        auth.is_super_admin()
        OR
        -- Admins can update all employees
        auth.is_admin_or_above()
        OR
        -- Managers can update all employees (for team management)
        auth.is_manager_or_above()
        OR
        -- Users can update their own basic information only
        (auth.get_user_role() = 'USER' AND id = auth.get_user_employee_id())
    );

-- Enhanced Employee DELETE policy (super admin only)
CREATE POLICY "employee_enhanced_delete" ON public."Employee"
    FOR DELETE USING (
        auth.is_super_admin()
    );

-- =====================================================
-- STEP 5: ENHANCED RLS POLICIES FOR VEHICLE TABLE
-- =====================================================

-- Drop existing vehicle policies
DROP POLICY IF EXISTS "vehicle_select_policy" ON public."Vehicle";
DROP POLICY IF EXISTS "vehicle_insert_policy" ON public."Vehicle";
DROP POLICY IF EXISTS "vehicle_update_policy" ON public."Vehicle";
DROP POLICY IF EXISTS "vehicle_delete_policy" ON public."Vehicle";

-- Enhanced Vehicle SELECT policy
CREATE POLICY "vehicle_enhanced_select" ON public."Vehicle"
    FOR SELECT USING (
        -- Admins and above can see all vehicles
        auth.is_admin_or_above()
        OR
        -- Managers can see all vehicles
        auth.is_manager_or_above()
        OR
        -- Users and readonly can see all vehicles (operational need)
        auth.has_role(ARRAY['USER', 'READONLY'])
    );

-- Enhanced Vehicle INSERT policy (manager and above)
CREATE POLICY "vehicle_enhanced_insert" ON public."Vehicle"
    FOR INSERT WITH CHECK (
        auth.is_manager_or_above()
    );

-- Enhanced Vehicle UPDATE policy (manager and above)
CREATE POLICY "vehicle_enhanced_update" ON public."Vehicle"
    FOR UPDATE USING (
        auth.is_manager_or_above()
    );

-- Enhanced Vehicle DELETE policy (admin and above)
CREATE POLICY "vehicle_enhanced_delete" ON public."Vehicle"
    FOR DELETE USING (
        auth.is_admin_or_above()
    );

-- =====================================================
-- STEP 6: GRANT NECESSARY PERMISSIONS
-- =====================================================

-- Grant execute permissions on auth functions
GRANT EXECUTE ON FUNCTION auth.get_user_role() TO authenticated;
GRANT EXECUTE ON FUNCTION auth.is_user_active() TO authenticated;
GRANT EXECUTE ON FUNCTION auth.get_user_employee_id() TO authenticated;
GRANT EXECUTE ON FUNCTION auth.is_super_admin() TO authenticated;
GRANT EXECUTE ON FUNCTION auth.is_admin_or_above() TO authenticated;
GRANT EXECUTE ON FUNCTION auth.is_manager_or_above() TO authenticated;
GRANT EXECUTE ON FUNCTION auth.has_role(TEXT[]) TO authenticated;

-- =====================================================
-- STEP 7: VERIFICATION FUNCTION
-- =====================================================

-- Function to verify enhanced RLS policies are working
CREATE OR REPLACE FUNCTION public.verify_enhanced_rls_policies()
RETURNS TABLE(
    table_name TEXT,
    policy_count INTEGER,
    rls_enabled BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        t.tablename::TEXT,
        COALESCE(p.policy_count, 0) as policy_count,
        CASE WHEN t.rowsecurity THEN true ELSE false END as rls_enabled
    FROM pg_tables t
    LEFT JOIN (
        SELECT
            schemaname,
            tablename,
            COUNT(*) as policy_count
        FROM pg_policies
        WHERE schemaname = 'public'
        GROUP BY schemaname, tablename
    ) p ON t.schemaname = p.schemaname AND t.tablename = p.tablename
    WHERE t.schemaname = 'public'
    AND t.tablename IN ('user_profiles', 'Employee', 'Vehicle')
    ORDER BY t.tablename;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission on verification function
GRANT EXECUTE ON FUNCTION public.verify_enhanced_rls_policies() TO authenticated;

-- =====================================================
-- MIGRATION COMPLETE
-- =====================================================

-- Log completion
DO $$
BEGIN
    RAISE NOTICE 'PHASE 2.1: Enhanced RLS Policies migration completed successfully';
    RAISE NOTICE 'Enhanced policies created for: user_profiles, Employee, Vehicle';
    RAISE NOTICE 'Role hierarchy functions updated with granular permissions';
    RAISE NOTICE 'Run SELECT * FROM public.verify_enhanced_rls_policies() to verify';
END $$;
