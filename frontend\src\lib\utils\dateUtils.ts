import { format, isDate, isValid, parse, parseISO } from 'date-fns';

/**
 * Configuration for date format preferences
 * Can be set based on user locale or application settings
 */
export interface DateFormatConfig {
  /** Whether to attempt parsing both formats if primary fails */
  fallbackToBothFormats: boolean;
  /** Locale for display formatting */
  locale?: string;
  /** Primary date format preference: 'US' (MM/DD/YY) or 'EU' (DD/MM/YY) */
  primaryFormat: 'EU' | 'US';
}

/** Default configuration - can be overridden by user preferences */
export const DEFAULT_DATE_CONFIG: DateFormatConfig = {
  fallbackToBothFormats: true, // Try both formats for safety
  locale: 'en-GB', // Use British English locale for EU format
  primaryFormat: 'EU', // Default to EU format (DD/MM/YYYY)
};

/**
 * Smart date parser that handles multiple date formats safely
 * @param dateString - Date string in various formats
 * @param config - Date format configuration
 * @returns Date object or null if parsing fails
 */
export const parseSmartDate = (
  dateString: string,
  config: DateFormatConfig = DEFAULT_DATE_CONFIG
): Date | null => {
  if (!dateString || dateString.trim() === '') return null;

  const trimmed = dateString.trim();

  try {
    // 1. Try ISO format first (most reliable)
    if (
      trimmed.includes('T') ||
      trimmed.includes('Z') ||
      /^\d{4}-\d{2}-\d{2}/.test(trimmed)
    ) {
      const isoDate = parseISO(trimmed);
      if (isValid(isoDate)) return isoDate;
    }

    // 2. Try YYYY-MM-DD format (HTML date input)
    if (/^\d{4}-\d{2}-\d{2}$/.test(trimmed)) {
      const htmlDate = new Date(trimmed + 'T00:00:00');
      if (isValid(htmlDate)) return htmlDate;
    }

    // 3. Handle DD/MM/YYYY vs MM/DD/YYYY ambiguity
    const slashMatch = trimmed.match(/^(\d{1,2})\/(\d{1,2})\/(\d{2,4})$/);
    if (slashMatch) {
      const [, first, second] = slashMatch;

      // Determine which format to try first
      const formats =
        config.primaryFormat === 'EU'
          ? ['dd/MM/yyyy', 'MM/dd/yyyy']
          : ['MM/dd/yyyy', 'dd/MM/yyyy'];

      for (const formatStr of formats) {
        try {
          const parsedDate = parse(trimmed, formatStr, new Date());
          if (isValid(parsedDate)) {
            // Additional validation: check if the parsed values make sense, accounting for potential year ambiguity in 2-digit years
            const month = formatStr.startsWith('MM')
              ? Number.parseInt(first!, 10)
              : Number.parseInt(second!, 10);
            const day = formatStr.startsWith('MM')
              ? Number.parseInt(second!, 10)
              : Number.parseInt(first!, 10);

            if (month >= 1 && month <= 12 && day >= 1 && day <= 31) {
              return parsedDate;
            }
          }
        } catch (error) {
          // Log the error for debugging purposes
          console.debug('Error occurred while parsing date:', error);
          // Continue to next format
        }
      }

      // If fallback is disabled, only try primary format
      if (!config.fallbackToBothFormats) return null;
    }

    // 4. Handle other common formats
    const commonFormats = [
      'dd-MM-yyyy',
      'MM-dd-yyyy',
      'yyyy/MM/dd',
      'dd.MM.yyyy',
      'MM.dd.yyyy',
    ];

    for (const formatStr of commonFormats) {
      try {
        const parsedDate = parse(trimmed, formatStr, new Date());
        if (isValid(parsedDate)) return parsedDate;
      } catch {
        // Continue to next format
      }
    }

    // 5. Last resort: try native Date constructor (least reliable)
    const nativeDate = new Date(trimmed);
    if (isValid(nativeDate)) return nativeDate;

    return null;
  } catch {
    return null;
  }
};

/**
 * Formats a date for HTML datetime-local input
 * @param dateValue - ISO date string, Date object, or undefined
 * @returns Formatted string in the format required by datetime-local inputs (YYYY-MM-DDTHH:mm)
 */
export const formatDateForInput = (
  dateValue: Date | string | undefined,
  type: 'date' | 'datetime-local' = 'datetime-local'
): string => {
  if (!dateValue) return '';

  try {
    // Convert to Date object if it's a string
    const date =
      typeof dateValue === 'string' ? parseISO(dateValue) : dateValue;

    // Check if the date is valid
    if (!isValid(date)) {
      return '';
    }

    // Format based on input type
    if (type === 'date') {
      return format(date, 'yyyy-MM-dd');
    }

    // For datetime-local, format is 'yyyy-MM-ddTHH:mm'
    return format(date, "yyyy-MM-dd'T'HH:mm");
  } catch {
    return '';
  }
};

/**
 * Formats a date for display to users
 * @param dateValue - ISO date string, Date object, or undefined
 * @param includeTime - Whether to include time in the formatted string
 * @returns Formatted date string for display (e.g., "Jan 15, 2023" or "Jan 15, 2023, 14:30")
 */
export const formatDateForDisplay = (
  dateValue: Date | string | undefined,
  includeTime = false
): string => {
  if (!dateValue) return 'N/A';

  try {
    // Convert to Date object if it's a string
    const date =
      typeof dateValue === 'string' ? parseISO(dateValue) : dateValue;

    // Check if the date is valid
    if (!isValid(date)) {
      return 'Invalid Date';
    }

    // Format with or without time
    return format(date, includeTime ? 'MMM d, yyyy, HH:mm' : 'MMM d, yyyy');
  } catch {
    return 'Invalid Date';
  }
};

/**
 * Formats a date for API submission (ISO 8601 UTC format)
 * Enhanced with smart date parsing to handle DD/MM/YY vs MM/DD/YY ambiguity
 * @param dateValue - Date string from form input, Date object, or undefined
 * @param config - Date format configuration for parsing ambiguous formats
 * @returns ISO 8601 UTC formatted date string ending in 'Z' or empty string if input is invalid
 */
export const formatDateForApi = (
  dateValue: Date | string | undefined,
  config: DateFormatConfig = DEFAULT_DATE_CONFIG
): string => {
  if (!dateValue) return '';

  try {
    // If it's already a Date object, format it directly
    if (isDate(dateValue)) {
      // Use toISOString() to ensure UTC format ending in 'Z'
      return dateValue.toISOString();
    }

    // For string input, use smart parsing to handle multiple formats
    if (typeof dateValue === 'string') {
      const parsedDate = parseSmartDate(dateValue, config);
      if (parsedDate && isValid(parsedDate)) {
        // Use toISOString() to ensure UTC format ending in 'Z'
        // This is required by the backend's Zod z.string().datetime() validation
        return parsedDate.toISOString();
      }
    }

    return '';
  } catch {
    return '';
  }
};

/**
 * Parses a date string from API (ISO format) to a Date object
 * @param dateString - ISO date string from API
 * @returns Date object or null if parsing fails
 */
export const parseDateFromApi = (
  dateString: string | undefined
): Date | null => {
  if (!dateString) return null;

  try {
    const date = parseISO(dateString);
    return isValid(date) ? date : null;
  } catch {
    return null;
  }
};

/**
 * Validates if a string is a valid ISO date string
 * @param dateString - String to validate
 * @returns Boolean indicating if the string is a valid ISO date
 */
export const isValidIsoDateString = (
  dateString: string | undefined
): boolean => {
  if (!dateString) return false;

  try {
    const date = parseISO(dateString);
    return isValid(date);
  } catch {
    return false;
  }
};

/**
 * Validates if a string can be parsed as a valid date
 * @param dateString - String to validate
 * @returns Boolean indicating if the string can be parsed as a date
 */
export const isValidDateString = (dateString: string | undefined): boolean => {
  if (!dateString) return false;

  try {
    const date = new Date(dateString);
    return isValid(date);
  } catch {
    return false;
  }
};

/**
 * Checks if date1 is after date2
 * @param date1 - First date (the one being checked if it's after)
 * @param date2 - Second date (the reference date)
 * @returns Boolean indicating if date1 is after date2
 */
export const isDateAfter = (
  date1: Date | string | undefined,
  date2: Date | string | undefined
): boolean => {
  if (!date1 || !date2) return false;

  try {
    const parsedDate1 = typeof date1 === 'string' ? parseISO(date1) : date1;
    const parsedDate2 = typeof date2 === 'string' ? parseISO(date2) : date2;

    if (!isValid(parsedDate1) || !isValid(parsedDate2)) {
      return false;
    }

    return parsedDate1 > parsedDate2;
  } catch {
    return false;
  }
};

/**
 * Formats flight details date for API submission
 * Handles the specific case where flight dateTime might be in datetime-local format
 * @param dateTime - Flight date/time string from form
 * @returns ISO 8601 formatted string or empty string if invalid
 */
export const formatFlightDateTimeForApi = (
  dateTime: string | undefined
): string => {
  if (!dateTime || dateTime.trim() === '') return '';

  // Use the general formatDateForApi function which handles both ISO and datetime-local formats
  return formatDateForApi(dateTime);
};

/**
 * Safely formats a date with fallback to empty string
 * Useful for form validation where empty strings are preferred over null/undefined
 * @param dateValue - Date value to format
 * @param type - Type of formatting ('date' | 'datetime-local')
 * @returns Formatted date string or empty string
 */
export const safeFormatDateForInput = (
  dateValue: Date | null | string | undefined,
  type: 'date' | 'datetime-local' = 'datetime-local'
): string => {
  if (!dateValue) return '';
  return formatDateForInput(dateValue, type);
};

/**
 * Detects the likely date format from a date string
 * @param dateString - Date string to analyze
 * @returns Detected format or null if cannot determine
 */
export const detectDateFormat = (
  dateString: string
): 'EU' | 'ISO' | 'US' | null => {
  if (!dateString || dateString.trim() === '') return null;

  const trimmed = dateString.trim();

  // ISO format detection
  if (
    trimmed.includes('T') ||
    trimmed.includes('Z') ||
    /^\d{4}-\d{2}-\d{2}/.test(trimmed)
  ) {
    return 'ISO';
  }

  // Slash format analysis
  const slashMatch = trimmed.match(/^(\d{1,2})\/(\d{1,2})\/(\d{2,4})$/);
  if (slashMatch) {
    const [, first, second] = slashMatch;
    const firstNum = Number.parseInt(first!, 10);
    const secondNum = Number.parseInt(second!, 10);

    // If first number > 12, it must be day (EU format)
    if (firstNum > 12) return 'EU';

    // If second number > 12, it must be day (US format)
    if (secondNum > 12) return 'US';

    // If both are <= 12, it's ambiguous
    return null;
  }

  return null;
};

/**
 * Creates a date format configuration based on user locale
 * @param locale - User locale string (e.g., 'en-US', 'en-GB', 'de-DE')
 * @returns Date format configuration
 */
export const createDateConfigFromLocale = (
  locale?: string
): DateFormatConfig => {
  if (!locale) return DEFAULT_DATE_CONFIG;

  // US-style locales (MM/DD/YY)
  const usLocales = ['en-US', 'en-CA'];

  // EU-style locales (DD/MM/YY)
  const euLocales = [
    'en-GB',
    'en-AU',
    'de-DE',
    'fr-FR',
    'es-ES',
    'it-IT',
    'pt-PT',
    'nl-NL',
  ];

  if (usLocales.includes(locale)) {
    return {
      fallbackToBothFormats: true,
      locale,
      primaryFormat: 'US',
    };
  }

  if (euLocales.some(euLocale => locale.startsWith(euLocale.split('-')[0]!))) {
    return {
      fallbackToBothFormats: true,
      locale,
      primaryFormat: 'EU',
    };
  }

  // Default to US format for unknown locales
  return {
    ...DEFAULT_DATE_CONFIG,
    locale,
  };
};

/**
 * Enhanced date validation with format-specific feedback
 * @param dateString - Date string to validate
 * @param config - Date format configuration
 * @returns Validation result with format information
 */
export const validateDateString = (
  dateString: string,
  config: DateFormatConfig = DEFAULT_DATE_CONFIG
): {
  detectedFormat?: 'EU' | 'ISO' | 'US';
  isValid: boolean;
  parsedDate?: Date;
  suggestion?: string;
} => {
  if (!dateString || dateString.trim() === '') {
    return { isValid: false, suggestion: 'Date is required' };
  }

  const detectedFormat = detectDateFormat(dateString);
  const parsedDate = parseSmartDate(dateString, config);

  if (parsedDate && isValid(parsedDate)) {
    return {
      ...(detectedFormat && { detectedFormat }),
      isValid: true,
      parsedDate,
    };
  }

  // Provide helpful suggestions based on detected issues
  const slashMatch = dateString.match(/^(\d{1,2})\/(\d{1,2})\/(\d{2,4})$/);
  if (slashMatch) {
    const [, first, second] = slashMatch;
    const firstNum = Number.parseInt(first!, 10);
    const secondNum = Number.parseInt(second!, 10);

    if (firstNum > 12 && secondNum > 12) {
      return {
        isValid: false,
        suggestion:
          'Invalid date: both day and month cannot be greater than 12',
      };
    }

    if (firstNum > 31 || secondNum > 31) {
      return {
        isValid: false,
        suggestion: 'Invalid date: day cannot be greater than 31',
      };
    }

    return {
      isValid: false,
      suggestion: `Ambiguous date format. Try using ${config.primaryFormat === 'EU' ? 'DD/MM/YYYY' : 'MM/DD/YYYY'} format or YYYY-MM-DD`,
    };
  }

  return {
    isValid: false,
    suggestion:
      'Invalid date format. Use DD/MM/YYYY, MM/DD/YYYY, or YYYY-MM-DD',
  };
};

/**
 * Example usage and testing function for the enhanced date utilities
 * This demonstrates how the DD/MM/YY vs MM/DD/YY issue is resolved
 */
export const dateUtilsExamples = () => {
  // Example 1: Ambiguous date with EU preference (now default)
  console.log(
    'Default EU Config - "01/02/2025":',
    formatDateForApi('01/02/2025')
  );
  // Result: February 1, 2025 (DD/MM/YYYY format)

  // Example 2: Ambiguous date with US preference (explicit config)
  const usConfig: DateFormatConfig = {
    fallbackToBothFormats: true,
    primaryFormat: 'US',
  };
  console.log(
    'US Config - "01/02/2025":',
    formatDateForApi('01/02/2025', usConfig)
  );
  // Result: January 2, 2025 (MM/DD/YYYY format)

  // Example 3: Unambiguous date (day > 12) - same result regardless of config
  console.log('Unambiguous - "13/02/2025":', formatDateForApi('13/02/2025'));
  // Result: February 13, 2025 (automatically detected as DD/MM format)

  // Example 4: ISO format (always safe and unambiguous)
  console.log('ISO - "2025-02-01":', formatDateForApi('2025-02-01'));
  // Result: February 1, 2025

  // Example 5: Validation with helpful suggestions
  console.log('Validation - "32/13/2025":', validateDateString('32/13/2025'));
  // Result: { isValid: false, suggestion: "Invalid date: day cannot be greater than 31" }

  // Example 6: EU format validation suggestion
  console.log(
    'EU Validation - "01/02/2025":',
    validateDateString('01/02/2025')
  );
  // Result: { isValid: true, detectedFormat: undefined, parsedDate: Date(2025-02-01) }
};

/**
 * Helper function to get current date format configuration info
 * Useful for displaying format hints to users
 */
export const getDateFormatInfo = (
  config: DateFormatConfig = DEFAULT_DATE_CONFIG
) => {
  return {
    description:
      config.primaryFormat === 'EU'
        ? 'European format (Day/Month/Year)'
        : 'US format (Month/Day/Year)',
    exampleDate: config.primaryFormat === 'EU' ? '31/12/2025' : '12/31/2025',
    formatExample: config.primaryFormat === 'EU' ? 'DD/MM/YYYY' : 'MM/DD/YYYY',
    locale: config.locale,
    primaryFormat: config.primaryFormat,
  };
};

/**
 * Quick test function to verify EU format is working
 * Run this in console to test the date format behavior
 */
export const testEuDateFormat = () => {
  console.log('🇪🇺 Testing EU Date Format Configuration:');
  console.log('=====================================');

  const formatInfo = getDateFormatInfo();
  console.log('Current config:', formatInfo);

  console.log('\n📅 Test Cases:');
  console.log(
    '1. Ambiguous date "01/02/2025":',
    formatDateForApi('01/02/2025')
  );
  console.log(
    '   Expected: 2025-02-01T00:00:00.000Z (February 1st - EU format)'
  );

  console.log(
    '\n2. Unambiguous date "13/02/2025":',
    formatDateForApi('13/02/2025')
  );
  console.log(
    '   Expected: 2025-02-13T00:00:00.000Z (February 13th - auto-detected)'
  );

  console.log('\n3. ISO date "2025-02-01":', formatDateForApi('2025-02-01'));
  console.log(
    '   Expected: 2025-02-01T00:00:00.000Z (February 1st - ISO format)'
  );

  console.log('\n✅ EU format is now the default!');
  console.log('💡 Tip: Use YYYY-MM-DD format to avoid any ambiguity');
};
