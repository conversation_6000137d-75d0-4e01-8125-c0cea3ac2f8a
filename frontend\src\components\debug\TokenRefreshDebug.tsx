/**
 * @file Debug component for monitoring token refresh status
 * @module components/debug/TokenRefreshDebug
 */

'use client';

import React from 'react';

import { getTokenRefreshService } from '@/lib/services/TokenRefreshService';

import {
  useTokenRefreshMessages,
  useTokenRefreshStatus,
} from '../../lib/api/security/hooks/useTokenRefreshStatus';

/**
 * Debug component for token refresh monitoring
 * Only shown in development mode
 */
export function TokenRefreshDebug(): JSX.Element | null {
  const statusInfo = useTokenRefreshStatus();
  const { message, type } = useTokenRefreshMessages();

  // Only show in development
  if (process.env.NODE_ENV === 'production') {
    return null;
  }

  const handleManualRefresh = () => {
    const service = getTokenRefreshService();
    service.refreshNow();
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'failed': {
        return 'text-red-600';
      }
      case 'refreshing': {
        return 'text-yellow-600';
      }
      case 'scheduled': {
        return 'text-blue-600';
      }
      case 'success': {
        return 'text-green-600';
      }
      default: {
        return 'text-gray-600';
      }
    }
  };

  const getMessageColor = (messageType: null | string) => {
    switch (messageType) {
      case 'error': {
        return 'bg-red-100 text-red-800 border-red-200';
      }
      case 'info': {
        return 'bg-blue-100 text-blue-800 border-blue-200';
      }
      case 'success': {
        return 'bg-green-100 text-green-800 border-green-200';
      }
      case 'warning': {
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      }
      default: {
        return 'bg-gray-100 text-gray-800 border-gray-200';
      }
    }
  };

  return (
    <div className="fixed bottom-4 right-4 z-50 max-w-sm rounded-lg border border-gray-300 bg-white p-4 shadow-lg">
      <div className="mb-2 flex items-center justify-between">
        <h3 className="text-sm font-semibold text-gray-900">
          Token Refresh Debug
        </h3>
        <button
          className="rounded bg-blue-500 px-2 py-1 text-xs text-white hover:bg-blue-600"
          onClick={handleManualRefresh}
        >
          Manual Refresh
        </button>
      </div>

      <div className="space-y-2 text-xs">
        <div className="flex justify-between">
          <span className="text-gray-600">Status:</span>
          <span className={`font-medium ${getStatusColor(statusInfo.status)}`}>
            {statusInfo.status}
          </span>
        </div>

        {statusInfo.lastRefresh && (
          <div className="flex justify-between">
            <span className="text-gray-600">Last Refresh:</span>
            <span className="text-gray-900">
              {new Date(statusInfo.lastRefresh).toLocaleString()}
            </span>
          </div>
        )}

        {statusInfo.nextRefresh && (
          <div className="flex justify-between">
            <span className="text-gray-600">Next Refresh:</span>
            <span className="text-gray-900">
              {new Date(statusInfo.nextRefresh).toLocaleString()}
            </span>
          </div>
        )}

        {statusInfo.error && (
          <div className="flex justify-between">
            <span className="text-gray-600">Error:</span>
            <span className="text-red-600">
              {typeof statusInfo.error === 'string'
                ? statusInfo.error
                : (statusInfo.error as Error)?.message || 'Unknown error'}
            </span>
          </div>
        )}

        {message && (
          <div
            className={`rounded-md border p-2 ${getMessageColor(type)}`}
            role="alert"
          >
            <p className="font-medium">{message}</p>
          </div>
        )}
      </div>
    </div>
  );
}
