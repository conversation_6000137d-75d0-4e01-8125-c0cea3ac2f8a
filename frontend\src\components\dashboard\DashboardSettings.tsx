/**
 * @file Generic dashboard settings component
 * @module components/dashboard/DashboardSettings
 */

'use client';

import {
  Calendar,
  Grid3X3,
  LayoutGrid,
  List,
  Minimize,
  RefreshCw,
  Settings,
  Table,
  Zap,
} from 'lucide-react';
import React from 'react';

import type { DashboardConfig, ViewMode } from './types';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

/**
 * Props for DashboardSettings component
 */
interface DashboardSettingsProps {
  config: DashboardConfig;
  entityType: string;
  // Dashboard state
  layout: {
    viewMode: ViewMode;
    gridColumns: number;
    compactMode: boolean;
    showFilters: boolean;
    showSettings: boolean;
  };
  monitoring: {
    enabled: boolean;
    refreshInterval: number;
    autoRefresh: boolean;
    pausedDataTypes: Set<string>;
  };
  // Actions
  setViewMode: (mode: ViewMode) => void;
  setGridColumns: (columns: number) => void;
  toggleCompactMode: () => void;
  setMonitoringEnabled: (enabled: boolean) => void;
  setRefreshInterval: (interval: number) => void;
  toggleAutoRefresh: () => void;
  resetSettings: () => void;
  className?: string;
}

/**
 * View mode configuration with icons and labels
 */
const viewModeConfig = {
  cards: { icon: LayoutGrid, label: 'Cards' },
  table: { icon: Table, label: 'Table' },
  list: { icon: List, label: 'List' },
  calendar: { icon: Calendar, label: 'Calendar' },
  grid: { icon: Grid3X3, label: 'Grid' },
};

/**
 * Generic dashboard settings component that can be reused across different entities.
 *
 * Features:
 * - View mode selection (cards, table, list, calendar, grid)
 * - Grid column configuration
 * - Compact mode toggle
 * - Monitoring controls
 * - Refresh interval settings
 * - Reset to defaults
 *
 * @param props - Component props
 * @returns JSX element representing the dashboard settings
 */
export const DashboardSettings: React.FC<DashboardSettingsProps> = ({
  config,
  entityType,
  layout,
  monitoring,
  setViewMode,
  setGridColumns,
  toggleCompactMode,
  setMonitoringEnabled,
  setRefreshInterval,
  toggleAutoRefresh,
  resetSettings,
  className = '',
}) => {
  // Get available view modes from config
  const availableViewModes = config.viewModes || ['cards', 'table', 'list'];

  // Refresh interval options (in milliseconds)
  const refreshIntervals = [
    { label: '5 seconds', value: 5000 },
    { label: '10 seconds', value: 10000 },
    { label: '30 seconds', value: 30000 },
    { label: '1 minute', value: 60000 },
    { label: '5 minutes', value: 300000 },
  ];

  return (
    <div className={`space-y-6 p-4 ${className}`}>
      <div className="space-y-2">
        <h2 className="text-2xl font-bold flex items-center gap-2">
          <Settings className="size-6" />
          {config.title} Settings
        </h2>
        <p className="text-muted-foreground">
          Customize your {entityType} dashboard experience
        </p>
      </div>

      <Tabs className="w-full" defaultValue="layout">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="layout">Layout</TabsTrigger>
          <TabsTrigger value="display">Display</TabsTrigger>
          <TabsTrigger value="refresh">Refresh</TabsTrigger>
        </TabsList>

        {/* Layout Settings */}
        <TabsContent className="mt-4 space-y-6" value="layout">
          <div className="space-y-4">
            <div className="space-y-2">
              <Label className="text-lg font-semibold">View Mode</Label>
              <p className="text-sm text-muted-foreground">
                Choose how {entityType}s are displayed
              </p>
              <RadioGroup
                className="grid grid-cols-2 gap-4 pt-2"
                onValueChange={value => setViewMode(value as ViewMode)}
                value={layout.viewMode}
              >
                {availableViewModes.map(mode => {
                  const ModeIcon =
                    viewModeConfig[mode as keyof typeof viewModeConfig]?.icon ||
                    LayoutGrid;
                  const label =
                    viewModeConfig[mode as keyof typeof viewModeConfig]
                      ?.label || mode;

                  return (
                    <Label
                      key={mode}
                      className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground [&:has([data-state=checked])]:border-primary cursor-pointer"
                      htmlFor={`layout-${mode}`}
                    >
                      <RadioGroupItem
                        className="sr-only"
                        id={`layout-${mode}`}
                        value={mode}
                      />
                      <ModeIcon className="mb-3 size-6" />
                      {label}
                    </Label>
                  );
                })}
              </RadioGroup>
            </div>

            {(layout.viewMode === 'cards' || layout.viewMode === 'grid') && (
              <div className="space-y-2">
                <Label className="text-lg font-semibold">
                  Grid Columns: {layout.gridColumns}
                </Label>
                <Slider
                  defaultValue={[layout.gridColumns]}
                  max={6}
                  min={1}
                  onValueChange={([value]) =>
                    value !== undefined && setGridColumns(value)
                  }
                  step={1}
                />
                <div className="flex justify-between text-sm text-muted-foreground">
                  <span>1 column</span>
                  <span>6 columns</span>
                </div>
              </div>
            )}
          </div>
        </TabsContent>

        {/* Display Settings */}
        <TabsContent className="mt-4 space-y-6" value="display">
          <div className="space-y-4">
            <div className="flex items-center justify-between space-x-2">
              <div className="space-y-0.5">
                <Label className="text-lg font-semibold">Compact Mode</Label>
                <p className="text-sm text-muted-foreground">
                  Show more {entityType}s in less space
                </p>
              </div>
              <Switch
                checked={layout.compactMode}
                onCheckedChange={toggleCompactMode}
              />
            </div>

            {config.enableBulkActions && (
              <div className="flex items-center justify-between space-x-2">
                <div className="space-y-0.5">
                  <Label className="text-lg font-semibold">Bulk Actions</Label>
                  <p className="text-sm text-muted-foreground">
                    Enable selection and bulk operations
                  </p>
                </div>
                <Switch checked={true} disabled />
              </div>
            )}
          </div>
        </TabsContent>

        {/* Refresh Settings */}
        <TabsContent className="mt-4 space-y-6" value="refresh">
          <div className="space-y-4">
            <div className="flex items-center justify-between space-x-2">
              <div className="space-y-0.5">
                <Label className="text-lg font-semibold flex items-center gap-2">
                  <Zap className="size-4" />
                  Auto Refresh
                </Label>
                <p className="text-sm text-muted-foreground">
                  Automatically refresh {entityType} data
                </p>
              </div>
              <Switch
                checked={monitoring.autoRefresh}
                onCheckedChange={toggleAutoRefresh}
              />
            </div>

            {monitoring.autoRefresh && (
              <div className="space-y-2">
                <Label className="text-lg font-semibold flex items-center gap-2">
                  <RefreshCw className="size-4" />
                  Refresh Interval
                </Label>
                <div className="grid grid-cols-2 gap-2">
                  {refreshIntervals.map(interval => (
                    <Button
                      key={interval.value}
                      variant={
                        monitoring.refreshInterval === interval.value
                          ? 'default'
                          : 'outline'
                      }
                      size="sm"
                      onClick={() => setRefreshInterval(interval.value)}
                    >
                      {interval.label}
                    </Button>
                  ))}
                </div>
              </div>
            )}

            <div className="flex items-center justify-between space-x-2">
              <div className="space-y-0.5">
                <Label className="text-lg font-semibold">
                  Real-time Updates
                </Label>
                <p className="text-sm text-muted-foreground">
                  Enable live data updates
                </p>
              </div>
              <Switch
                checked={monitoring.enabled}
                onCheckedChange={setMonitoringEnabled}
              />
            </div>
          </div>
        </TabsContent>
      </Tabs>

      <div className="flex justify-end pt-4 border-t">
        <Button onClick={resetSettings} variant="outline">
          Reset to Defaults
        </Button>
      </div>
    </div>
  );
};

export default DashboardSettings;
