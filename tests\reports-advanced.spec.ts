import { test, expect } from '@playwright/test';

test.describe('Advanced Reports Page Testing', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('http://localhost:9002/reports');
    await page.waitForLoadState('networkidle');
  });

  test('should test responsive design', async ({ page }) => {
    // Test desktop view (default)
    await expect(page.getByRole('heading', { name: 'Reports & Analytics' })).toBeVisible();
    
    // Test tablet view
    await page.setViewportSize({ width: 768, height: 1024 });
    await page.waitForTimeout(500); // Wait for responsive changes
    await expect(page.getByRole('heading', { name: 'Reports & Analytics' })).toBeVisible();
    
    // Test mobile view
    await page.setViewportSize({ width: 375, height: 667 });
    await page.waitForTimeout(500);
    await expect(page.getByRole('heading', { name: 'Reports & Analytics' })).toBeVisible();
    
    // Reset to desktop
    await page.setViewportSize({ width: 1280, height: 720 });
  });

  test('should test keyboard navigation', async ({ page }) => {
    // Test tab navigation through main elements
    await page.keyboard.press('Tab');
    await page.keyboard.press('Tab');
    await page.keyboard.press('Tab');
    
    // Test Enter key on focused elements
    await page.getByRole('tab', { name: 'Analytics' }).focus();
    await page.keyboard.press('Enter');
    await expect(page.getByRole('tab', { name: 'Analytics', selected: true })).toBeVisible();
    
    // Test arrow key navigation in tab list
    await page.keyboard.press('ArrowRight');
    await page.keyboard.press('ArrowLeft');
  });

  test('should test accessibility features', async ({ page }) => {
    // Check for proper ARIA labels and roles
    await expect(page.getByRole('tablist')).toBeVisible();
    await expect(page.getByRole('tabpanel')).toBeVisible();
    
    // Check for proper heading hierarchy
    await expect(page.getByRole('heading', { level: 1 })).toBeVisible();
    await expect(page.getByRole('heading', { level: 3 })).toBeVisible();
    
    // Check for proper button labels
    const buttons = page.getByRole('button');
    const buttonCount = await buttons.count();
    
    for (let i = 0; i < Math.min(buttonCount, 10); i++) {
      const button = buttons.nth(i);
      const accessibleName = await button.getAttribute('aria-label') || await button.textContent();
      expect(accessibleName).toBeTruthy();
    }
  });

  test('should test data loading states', async ({ page }) => {
    // Navigate to different tabs and check for loading indicators
    const tabs = ['Analytics', 'Tasks', 'Vehicles', 'Employees'];
    
    for (const tabName of tabs) {
      await page.getByRole('tab', { name: tabName }).click();
      await page.waitForTimeout(100); // Brief wait for any loading states
      
      // Check that content is displayed (even if empty)
      await expect(page.getByRole('tabpanel')).toBeVisible();
    }
  });

  test('should test error handling', async ({ page }) => {
    // Test network error simulation
    await page.route('**/api/**', route => route.abort());
    
    // Try to refresh data
    await page.getByRole('button', { name: 'Refresh' }).click();
    
    // Reset network
    await page.unroute('**/api/**');
  });

  test('should test theme switching', async ({ page }) => {
    // Find and click theme toggle button
    const themeButton = page.getByRole('button', { name: /theme/i });
    if (await themeButton.isVisible()) {
      await themeButton.click();
      await page.waitForTimeout(300); // Wait for theme transition
      
      // Click again to toggle back
      await themeButton.click();
      await page.waitForTimeout(300);
    }
  });

  test('should test font size adjustment', async ({ page }) => {
    // Find and test font size button
    const fontButton = page.getByRole('button', { name: /font size/i });
    if (await fontButton.isVisible()) {
      await fontButton.click();
      await page.waitForTimeout(300);
    }
  });

  test('should test data export with file download', async ({ page }) => {
    // Set up download handling
    const downloadPromise = page.waitForEvent('download');
    
    // Navigate to Tasks tab
    await page.getByRole('tab', { name: 'Tasks' }).click();
    
    // Try to trigger a download (if data exists)
    await page.getByRole('button', { name: 'Export CSV' }).click();
    
    // Note: In a real test with data, you would wait for and verify the download
    // const download = await downloadPromise;
    // expect(download.suggestedFilename()).toContain('.csv');
  });

  test('should test table interactions', async ({ page }) => {
    // Navigate to Tasks tab
    await page.getByRole('tab', { name: 'Tasks' }).click();
    
    // Test column visibility toggle
    const columnsButton = page.getByRole('button', { name: 'Columns' });
    if (await columnsButton.isVisible()) {
      await columnsButton.click();
      await page.waitForTimeout(300);
      
      // Click outside to close (if dropdown opened)
      await page.click('body');
    }
    
    // Test table sorting by clicking headers
    const sortableHeaders = [
      'Task Title',
      'Status', 
      'Priority',
      'Assigned To',
      'Due Date'
    ];
    
    for (const header of sortableHeaders) {
      const headerButton = page.getByRole('button', { name: header });
      if (await headerButton.isVisible()) {
        await headerButton.click();
        await page.waitForTimeout(100);
      }
    }
  });

  test('should test search and filter functionality', async ({ page }) => {
    // Navigate to Tasks tab
    await page.getByRole('tab', { name: 'Tasks' }).click();
    
    const searchInput = page.getByRole('textbox', { name: 'Filter tasks...' });
    
    // Test various search terms
    const searchTerms = ['test', 'task', 'urgent', ''];
    
    for (const term of searchTerms) {
      await searchInput.fill(term);
      await page.waitForTimeout(300); // Wait for filter to apply
      await expect(searchInput).toHaveValue(term);
    }
    
    // Test special characters
    await searchInput.fill('!@#$%');
    await page.waitForTimeout(300);
    await searchInput.clear();
  });

  test('should test widget interactions', async ({ page }) => {
    // Test Overview tab widgets
    await page.getByRole('tab', { name: 'Overview' }).click();
    
    // Look for interactive elements in widgets
    const csvButtons = page.getByRole('button', { name: 'CSV' });
    const pdfButtons = page.getByRole('button', { name: 'PDF' });
    
    // Test CSV export buttons
    const csvCount = await csvButtons.count();
    for (let i = 0; i < csvCount; i++) {
      const button = csvButtons.nth(i);
      if (await button.isVisible()) {
        await button.click();
        await page.waitForTimeout(100);
      }
    }
    
    // Test PDF export buttons  
    const pdfCount = await pdfButtons.count();
    for (let i = 0; i < pdfCount; i++) {
      const button = pdfButtons.nth(i);
      if (await button.isVisible()) {
        await button.click();
        await page.waitForTimeout(100);
      }
    }
  });

  test('should test navigation and routing', async ({ page }) => {
    // Test internal navigation links
    const reportLinks = page.getByRole('link', { name: 'View Report' });
    const linkCount = await reportLinks.count();
    
    for (let i = 0; i < Math.min(linkCount, 3); i++) {
      const link = reportLinks.nth(i);
      const href = await link.getAttribute('href');
      
      if (href && href.startsWith('/')) {
        // Test that link has proper href
        expect(href).toMatch(/^\/reports/);
      }
    }
    
    // Test back navigation
    const backLink = page.getByRole('link', { name: 'Back to Dashboard' });
    if (await backLink.isVisible()) {
      const href = await backLink.getAttribute('href');
      expect(href).toBe('/');
    }
  });

  test('should test performance and loading', async ({ page }) => {
    // Measure page load time
    const startTime = Date.now();
    await page.goto('http://localhost:9002/reports');
    await page.waitForLoadState('networkidle');
    const loadTime = Date.now() - startTime;
    
    // Basic performance check (adjust threshold as needed)
    expect(loadTime).toBeLessThan(10000); // 10 seconds max
    
    // Check that essential elements load quickly
    await expect(page.getByRole('heading', { name: 'Reports & Analytics' })).toBeVisible();
    await expect(page.getByRole('tablist')).toBeVisible();
  });
});
