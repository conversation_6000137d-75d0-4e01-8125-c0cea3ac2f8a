/**
 * Phase 2: Data Migration Script - Hybrid RBAC Implementation
 *
 * This script migrates user roles from raw_user_meta_data to the user_profiles table
 * and attempts to link users with employee records based on email matching.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  console.error('Required: SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
  },
});

// Valid role mappings
const VALID_ROLES = ['USER', 'MANAGER', 'ADMIN', 'SUPER_ADMIN', 'READONLY'];
const DEFAULT_ROLE = 'USER';

// Migration state tracking
let migrationLog = {
  startTime: new Date().toISOString(),
  totalUsers: 0,
  processedUsers: 0,
  successfulMigrations: 0,
  skippedUsers: 0,
  errors: [],
  userMappings: [],
  rollbackData: [],
};

/**
 * Main migration function
 */
async function migrateUserRoles() {
  console.log('🚀 Starting Phase 2: Data Migration');
  console.log('===================================\n');

  try {
    // Step 1: Fetch all users from auth.users
    console.log('📋 Step 1: Fetching users from auth.users table...');
    const users = await fetchAllUsers();
    migrationLog.totalUsers = users.length;
    console.log(`✅ Found ${users.length} users to process\n`);

    // Step 2: Fetch all employees for email matching
    console.log('📋 Step 2: Fetching employees for email matching...');
    const employees = await fetchAllEmployees();
    console.log(`✅ Found ${employees.length} employees for matching\n`);

    // Step 3: Process each user
    console.log('📋 Step 3: Processing user migrations...');
    console.log('==========================================');

    for (const user of users) {
      await processUser(user, employees);
      migrationLog.processedUsers++;

      // Progress indicator
      if (migrationLog.processedUsers % 10 === 0) {
        console.log(
          `📊 Progress: ${migrationLog.processedUsers}/${migrationLog.totalUsers} users processed`,
        );
      }
    }

    // Step 4: Generate migration report
    await generateMigrationReport();

    console.log('\n🎉 Phase 2 Migration Complete!');
    console.log('==============================');
    console.log(`✅ Successfully migrated: ${migrationLog.successfulMigrations} users`);
    console.log(`⏭️  Skipped (already exists): ${migrationLog.skippedUsers} users`);
    console.log(`❌ Errors: ${migrationLog.errors.length} users`);

    if (migrationLog.errors.length > 0) {
      console.log('\n⚠️  Errors encountered:');
      migrationLog.errors.forEach((error, index) => {
        console.log(`   ${index + 1}. ${error.userId}: ${error.message}`);
      });
    }

    console.log(`\n📄 Detailed report saved to: logs/migration-report-${Date.now()}.json`);
  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    await generateMigrationReport();
    process.exit(1);
  }
}

/**
 * Fetch all users from auth.users table
 */
async function fetchAllUsers() {
  const { data: users, error } = await supabase.auth.admin.listUsers();

  if (error) {
    throw new Error(`Failed to fetch users: ${error.message}`);
  }

  return users.users || [];
}

/**
 * Fetch all employees for email matching
 */
async function fetchAllEmployees() {
  const { data: employees, error } = await supabase
    .from('Employee')
    .select('id, contactEmail, name, fullName');

  if (error) {
    throw new Error(`Failed to fetch employees: ${error.message}`);
  }

  return employees || [];
}

/**
 * Process a single user migration
 */
async function processUser(user, employees) {
  try {
    console.log(`👤 Processing user: ${user.email} (${user.id})`);

    // Check if user profile already exists
    const { data: existingProfile } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('id', user.id)
      .single();

    if (existingProfile) {
      console.log(`   ⏭️  User profile already exists, skipping...`);
      migrationLog.skippedUsers++;
      return;
    }

    // Extract role from raw_user_meta_data
    const rawRole = user.raw_user_meta_data?.role;
    const mappedRole = mapRole(rawRole);

    console.log(`   📝 Raw role: ${rawRole || 'none'} → Mapped role: ${mappedRole}`);

    // Attempt to match with employee record
    const matchedEmployee = findMatchingEmployee(user, employees);
    if (matchedEmployee) {
      console.log(
        `   🔗 Matched with employee: ${matchedEmployee.name} (ID: ${matchedEmployee.id})`,
      );
    }

    // Create user profile
    const profileData = {
      id: user.id,
      role: mappedRole,
      employee_id: matchedEmployee?.id || null,
      is_active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    const { data: newProfile, error: insertError } = await supabase
      .from('user_profiles')
      .insert(profileData)
      .select()
      .single();

    if (insertError) {
      throw new Error(`Failed to create user profile: ${insertError.message}`);
    }

    console.log(`   ✅ User profile created successfully`);

    // Track successful migration
    migrationLog.successfulMigrations++;
    migrationLog.userMappings.push({
      userId: user.id,
      email: user.email,
      originalRole: rawRole,
      mappedRole: mappedRole,
      employeeId: matchedEmployee?.id || null,
      employeeName: matchedEmployee?.name || null,
    });

    // Store rollback data
    migrationLog.rollbackData.push({
      userId: user.id,
      action: 'DELETE_PROFILE',
    });
  } catch (error) {
    console.log(`   ❌ Error: ${error.message}`);
    migrationLog.errors.push({
      userId: user.id,
      email: user.email,
      message: error.message,
    });
  }
}

/**
 * Map raw role to valid UserRole enum value
 */
function mapRole(rawRole) {
  if (!rawRole) return DEFAULT_ROLE;

  const upperRole = rawRole.toString().toUpperCase();

  // Direct mapping
  if (VALID_ROLES.includes(upperRole)) {
    return upperRole;
  }

  // Common variations mapping
  const roleMappings = {
    ADMINISTRATOR: 'ADMIN',
    ADMIN_USER: 'ADMIN',
    SUPER_USER: 'SUPER_ADMIN',
    SUPERADMIN: 'SUPER_ADMIN',
    MANAGER_USER: 'MANAGER',
    REGULAR_USER: 'USER',
    STANDARD_USER: 'USER',
    READ_ONLY: 'READONLY',
    VIEWER: 'READONLY',
  };

  return roleMappings[upperRole] || DEFAULT_ROLE;
}

/**
 * Find matching employee record based on email
 */
function findMatchingEmployee(user, employees) {
  if (!user.email) return null;

  // Direct email match
  const directMatch = employees.find(
    emp => emp.contactEmail && emp.contactEmail.toLowerCase() === user.email.toLowerCase(),
  );

  if (directMatch) return directMatch;

  // Fuzzy matching could be added here if needed
  // For now, we only do exact email matches

  return null;
}

/**
 * Generate comprehensive migration report
 */
async function generateMigrationReport() {
  migrationLog.endTime = new Date().toISOString();
  migrationLog.duration = new Date(migrationLog.endTime) - new Date(migrationLog.startTime);

  // Ensure logs directory exists
  const logsDir = path.join(process.cwd(), 'logs');
  if (!fs.existsSync(logsDir)) {
    fs.mkdirSync(logsDir, { recursive: true });
  }

  // Save detailed report
  const reportPath = path.join(logsDir, `migration-report-${Date.now()}.json`);
  fs.writeFileSync(reportPath, JSON.stringify(migrationLog, null, 2));

  // Save rollback script
  const rollbackPath = path.join(logsDir, `rollback-script-${Date.now()}.sql`);
  const rollbackSQL = generateRollbackSQL();
  fs.writeFileSync(rollbackPath, rollbackSQL);

  console.log(`\n📄 Reports generated:`);
  console.log(`   - Migration report: ${reportPath}`);
  console.log(`   - Rollback script: ${rollbackPath}`);
}

/**
 * Generate SQL rollback script
 */
function generateRollbackSQL() {
  let sql = '-- Rollback script for user roles migration\n';
  sql += `-- Generated: ${new Date().toISOString()}\n\n`;

  migrationLog.rollbackData.forEach(item => {
    if (item.action === 'DELETE_PROFILE') {
      sql += `DELETE FROM user_profiles WHERE id = '${item.userId}';\n`;
    }
  });

  return sql;
}

// Run migration if this script is executed directly
console.log('🚀 Starting migration script...');
migrateUserRoles().catch(error => {
  console.error('❌ Migration script failed:', error);
  process.exit(1);
});

export { migrateUserRoles };
