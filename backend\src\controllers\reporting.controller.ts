// backend/src/controllers/reporting.controller.ts

import { Request, Response } from 'express';
import ReportingService from '../services/reporting.service.js';

const reportingService = new ReportingService();
import { reportingValidationSchema } from '../schemas/reporting.schema.js';
import logger from '../utils/logger.js';
import { businessMetrics } from '../services/metrics.service.js';

/**
 * Reporting Controller
 *
 * Follows SRP: Handles HTTP requests/responses for reporting endpoints only.
 * Delegates business logic to reporting service.
 * Handles validation, error handling, and response formatting.
 */
class ReportingController {
  /**
   * Get delegation analytics data
   */
  async getDelegationAnalytics(req: Request, res: Response): Promise<void> {
    const startTime = Date.now();

    try {
      // Validate query parameters
      const validationResult = reportingValidationSchema.filtersSchema.safeParse(req.query);

      if (!validationResult.success) {
        res.status(400).json({
          status: 'error',
          message: 'Invalid filter parameters',
          errors: validationResult.error.errors,
          timestamp: new Date().toISOString(),
        });
        return;
      }

      const filters = validationResult.data;

      // Get analytics data from service
      const analyticsData = await reportingService.getDelegationAnalytics(filters);

      // Record metrics
      const duration = (Date.now() - startTime) / 1000;
      businessMetrics.recordApiEndpointDuration(
        '/reporting/delegations/analytics',
        'GET',
        '200',
        duration,
      );

      businessMetrics.recordApiEndpointRequest(
        '/reporting/delegations/analytics',
        'GET',
        '200',
        (req as any).user?.role || 'unknown',
      );

      res.json({
        status: 'success',
        data: analyticsData,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      const duration = (Date.now() - startTime) / 1000;
      businessMetrics.recordApiEndpointDuration(
        '/reporting/delegations/analytics',
        'GET',
        '500',
        duration,
      );

      logger.error('Error getting delegation analytics:', {
        error: error instanceof Error ? error.message : 'Unknown error',
        userId: (req as any).user?.id,
        filters: req.query,
      });

      res.status(500).json({
        status: 'error',
        message: 'Failed to get delegation analytics',
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * Get task metrics data
   */
  async getTaskMetrics(req: Request, res: Response): Promise<void> {
    const startTime = Date.now();

    try {
      const delegationIds = req.query.delegationIds
        ? (req.query.delegationIds as string).split(',')
        : undefined;

      const taskMetrics = await reportingService.getTaskMetrics(delegationIds);

      const duration = (Date.now() - startTime) / 1000;
      businessMetrics.recordApiEndpointDuration('/reporting/tasks/metrics', 'GET', '200', duration);

      res.json({
        status: 'success',
        data: taskMetrics,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      const duration = (Date.now() - startTime) / 1000;
      businessMetrics.recordApiEndpointDuration('/reporting/tasks/metrics', 'GET', '500', duration);

      logger.error('Error getting task metrics:', {
        error: error instanceof Error ? error.message : 'Unknown error',
        userId: (req as any).user?.id,
        delegationIds: req.query.delegationIds,
      });

      res.status(500).json({
        status: 'error',
        message: 'Failed to get task metrics',
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * Get task analytics data
   */
  async getTaskAnalytics(req: Request, res: Response): Promise<void> {
    const startTime = Date.now();

    try {
      const validationResult = reportingValidationSchema.filtersSchema.safeParse(req.query);

      if (!validationResult.success) {
        res.status(400).json({
          status: 'error',
          message: 'Invalid filter parameters',
          errors: validationResult.error.errors,
          timestamp: new Date().toISOString(),
        });
        return;
      }

      const filters = validationResult.data;
      const taskAnalytics = await reportingService.getTaskAnalytics(filters);

      const duration = (Date.now() - startTime) / 1000;
      businessMetrics.recordApiEndpointDuration(
        '/reporting/tasks/analytics',
        'GET',
        '200',
        duration,
      );

      res.json({
        status: 'success',
        data: taskAnalytics,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      const duration = (Date.now() - startTime) / 1000;
      businessMetrics.recordApiEndpointDuration(
        '/reporting/tasks/analytics',
        'GET',
        '500',
        duration,
      );

      logger.error('Error getting task analytics:', {
        error: error instanceof Error ? error.message : 'Unknown error',
        userId: (req as any).user?.id,
        filters: req.query,
      });

      res.status(500).json({
        status: 'error',
        message: 'Failed to get task analytics',
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * Get trend data
   */
  async getTrendData(req: Request, res: Response): Promise<void> {
    const startTime = Date.now();

    try {
      const validationResult = reportingValidationSchema.filtersSchema.safeParse(req.query);

      if (!validationResult.success) {
        res.status(400).json({
          status: 'error',
          message: 'Invalid filter parameters',
          errors: validationResult.error.errors,
          timestamp: new Date().toISOString(),
        });
        return;
      }

      const filters = validationResult.data;
      const trendData = await reportingService.getTrendData(filters);

      const duration = (Date.now() - startTime) / 1000;
      businessMetrics.recordApiEndpointDuration('/reporting/trends', 'GET', '200', duration);

      res.json({
        status: 'success',
        data: trendData,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      const duration = (Date.now() - startTime) / 1000;
      businessMetrics.recordApiEndpointDuration('/reporting/trends', 'GET', '500', duration);

      logger.error('Error getting trend data:', {
        error: error instanceof Error ? error.message : 'Unknown error',
        userId: (req as any).user?.id,
        filters: req.query,
      });

      res.status(500).json({
        status: 'error',
        message: 'Failed to get trend data',
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * Get location metrics
   */
  async getLocationMetrics(req: Request, res: Response): Promise<void> {
    const startTime = Date.now();

    try {
      const validationResult = reportingValidationSchema.filtersSchema.safeParse(req.query);

      if (!validationResult.success) {
        res.status(400).json({
          status: 'error',
          message: 'Invalid filter parameters',
          errors: validationResult.error.errors,
          timestamp: new Date().toISOString(),
        });
        return;
      }

      const filters = validationResult.data;
      const locationMetrics = await reportingService.getLocationMetrics(filters);

      const duration = (Date.now() - startTime) / 1000;
      businessMetrics.recordApiEndpointDuration(
        '/reporting/locations/metrics',
        'GET',
        '200',
        duration,
      );

      res.json({
        status: 'success',
        data: locationMetrics,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      const duration = (Date.now() - startTime) / 1000;
      businessMetrics.recordApiEndpointDuration(
        '/reporting/locations/metrics',
        'GET',
        '500',
        duration,
      );

      logger.error('Error getting location metrics:', {
        error: error instanceof Error ? error.message : 'Unknown error',
        userId: (req as any).user?.id,
        filters: req.query,
      });

      res.status(500).json({
        status: 'error',
        message: 'Failed to get location metrics',
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * Get service history data
   */
  async getServiceHistory(req: Request, res: Response): Promise<void> {
    // Implementation will be added in next chunk
    res.status(501).json({
      status: 'error',
      message: 'Service history endpoint not yet implemented',
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Get service cost summary
   */
  async getServiceCostSummary(req: Request, res: Response): Promise<void> {
    // Implementation will be added in next chunk
    res.status(501).json({
      status: 'error',
      message: 'Service cost summary endpoint not yet implemented',
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Get vehicle analytics data
   */
  async getVehicleAnalytics(req: Request, res: Response): Promise<void> {
    const startTime = Date.now();

    try {
      const validationResult = reportingValidationSchema.filtersSchema.safeParse(req.query);

      if (!validationResult.success) {
        res.status(400).json({
          status: 'error',
          message: 'Invalid filter parameters',
          errors: validationResult.error.errors,
          timestamp: new Date().toISOString(),
        });
        return;
      }

      const filters = validationResult.data;
      const vehicleAnalytics = await reportingService.getVehicleAnalytics(filters);

      const duration = (Date.now() - startTime) / 1000;
      businessMetrics.recordApiEndpointDuration(
        '/reporting/vehicles/analytics',
        'GET',
        '200',
        duration,
      );

      res.json({
        status: 'success',
        data: vehicleAnalytics,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      const duration = (Date.now() - startTime) / 1000;
      businessMetrics.recordApiEndpointDuration(
        '/reporting/vehicles/analytics',
        'GET',
        '500',
        duration,
      );

      logger.error('Error getting vehicle analytics:', {
        error: error instanceof Error ? error.message : 'Unknown error',
        userId: (req as any).user?.id,
        filters: req.query,
      });

      res.status(500).json({
        status: 'error',
        message: 'Failed to get vehicle analytics',
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * Get employee analytics data
   */
  async getEmployeeAnalytics(req: Request, res: Response): Promise<void> {
    const startTime = Date.now();

    try {
      const validationResult = reportingValidationSchema.filtersSchema.safeParse(req.query);

      if (!validationResult.success) {
        res.status(400).json({
          status: 'error',
          message: 'Invalid filter parameters',
          errors: validationResult.error.errors,
          timestamp: new Date().toISOString(),
        });
        return;
      }

      const filters = validationResult.data;
      const employeeAnalytics = await reportingService.getEmployeeAnalytics(filters);

      const duration = (Date.now() - startTime) / 1000;
      businessMetrics.recordApiEndpointDuration(
        '/reporting/employees/analytics',
        'GET',
        '200',
        duration,
      );

      res.json({
        status: 'success',
        data: employeeAnalytics,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      const duration = (Date.now() - startTime) / 1000;
      businessMetrics.recordApiEndpointDuration(
        '/reporting/employees/analytics',
        'GET',
        '500',
        duration,
      );

      logger.error('Error getting employee analytics:', {
        error: error instanceof Error ? error.message : 'Unknown error',
        userId: (req as any).user?.id,
        filters: req.query,
      });

      res.status(500).json({
        status: 'error',
        message: 'Failed to get employee analytics',
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * Get all report types
   */
  async getReportTypes(req: Request, res: Response): Promise<void> {
    const startTime = Date.now();

    try {
      const { category } = req.query;

      // For now, return mock data until we implement the database schema
      const mockReportTypes = [
        {
          id: '1',
          name: 'Delegation Summary Report',
          description: 'Comprehensive overview of delegation activities',
          category: 'delegation',
          dataSource: 'delegations',
          widgets: ['delegation-status', 'delegation-trend'],
          isActive: true,
          isPublic: true,
          filters: ['dateRange', 'status', 'location'],
          refreshInterval: 300000, // 5 minutes
          tags: ['summary', 'overview'],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: '2',
          name: 'Task Performance Report',
          description: 'Detailed analysis of task completion and performance',
          category: 'tasks',
          dataSource: 'tasks',
          widgets: ['task-metrics', 'task-status-chart'],
          isActive: true,
          isPublic: false,
          filters: ['dateRange', 'priority', 'assignee'],
          refreshInterval: 600000, // 10 minutes
          tags: ['performance', 'tasks'],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      ];

      let filteredReportTypes = mockReportTypes;
      if (category) {
        filteredReportTypes = mockReportTypes.filter(rt => rt.category === category);
      }

      const duration = (Date.now() - startTime) / 1000;
      businessMetrics.recordApiEndpointDuration('/reporting/report-types', 'GET', '200', duration);

      res.json({
        status: 'success',
        data: filteredReportTypes,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      const duration = (Date.now() - startTime) / 1000;
      businessMetrics.recordApiEndpointDuration('/reporting/report-types', 'GET', '500', duration);

      logger.error('Error getting report types:', {
        error: error instanceof Error ? error.message : 'Unknown error',
        userId: (req as any).user?.id,
      });

      res.status(500).json({
        status: 'error',
        message: 'Failed to get report types',
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * Create a new report type
   */
  async createReportType(req: Request, res: Response): Promise<void> {
    const startTime = Date.now();

    try {
      const reportTypeData = req.body;

      // For now, return mock created report type
      const newReportType = {
        id: Date.now().toString(),
        ...reportTypeData,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      const duration = (Date.now() - startTime) / 1000;
      businessMetrics.recordApiEndpointDuration('/reporting/report-types', 'POST', '201', duration);

      res.status(201).json({
        status: 'success',
        data: newReportType,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      const duration = (Date.now() - startTime) / 1000;
      businessMetrics.recordApiEndpointDuration('/reporting/report-types', 'POST', '500', duration);

      logger.error('Error creating report type:', {
        error: error instanceof Error ? error.message : 'Unknown error',
        userId: (req as any).user?.id,
        body: req.body,
      });

      res.status(500).json({
        status: 'error',
        message: 'Failed to create report type',
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * Get a specific report type by ID
   */
  async getReportType(req: Request, res: Response): Promise<void> {
    const startTime = Date.now();

    try {
      const { id } = req.params;

      // For now, return mock report type
      const mockReportType = {
        id,
        name: 'Sample Report Type',
        description: 'A sample report type for testing',
        category: 'general',
        dataSource: 'delegations',
        widgets: ['delegation-status'],
        isActive: true,
        isPublic: true,
        filters: ['dateRange'],
        refreshInterval: 300000,
        tags: ['sample'],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      const duration = (Date.now() - startTime) / 1000;
      businessMetrics.recordApiEndpointDuration(
        '/reporting/report-types/:id',
        'GET',
        '200',
        duration,
      );

      res.json({
        status: 'success',
        data: mockReportType,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      const duration = (Date.now() - startTime) / 1000;
      businessMetrics.recordApiEndpointDuration(
        '/reporting/report-types/:id',
        'GET',
        '500',
        duration,
      );

      logger.error('Error getting report type:', {
        error: error instanceof Error ? error.message : 'Unknown error',
        userId: (req as any).user?.id,
        reportTypeId: req.params.id,
      });

      res.status(500).json({
        status: 'error',
        message: 'Failed to get report type',
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * Update a report type
   */
  async updateReportType(req: Request, res: Response): Promise<void> {
    const startTime = Date.now();

    try {
      const { id } = req.params;
      const reportTypeData = req.body;

      // For now, return mock updated report type
      const updatedReportType = {
        id,
        ...reportTypeData,
        updatedAt: new Date().toISOString(),
      };

      const duration = (Date.now() - startTime) / 1000;
      businessMetrics.recordApiEndpointDuration(
        '/reporting/report-types/:id',
        'PUT',
        '200',
        duration,
      );

      res.json({
        status: 'success',
        data: updatedReportType,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      const duration = (Date.now() - startTime) / 1000;
      businessMetrics.recordApiEndpointDuration(
        '/reporting/report-types/:id',
        'PUT',
        '500',
        duration,
      );

      logger.error('Error updating report type:', {
        error: error instanceof Error ? error.message : 'Unknown error',
        userId: (req as any).user?.id,
        reportTypeId: req.params.id,
        body: req.body,
      });

      res.status(500).json({
        status: 'error',
        message: 'Failed to update report type',
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * Delete a report type
   */
  async deleteReportType(req: Request, res: Response): Promise<void> {
    const startTime = Date.now();

    try {
      const { id } = req.params;

      const duration = (Date.now() - startTime) / 1000;
      businessMetrics.recordApiEndpointDuration(
        '/reporting/report-types/:id',
        'DELETE',
        '204',
        duration,
      );

      res.status(204).json({
        status: 'success',
        message: 'Report type deleted successfully',
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      const duration = (Date.now() - startTime) / 1000;
      businessMetrics.recordApiEndpointDuration(
        '/reporting/report-types/:id',
        'DELETE',
        '500',
        duration,
      );

      logger.error('Error deleting report type:', {
        error: error instanceof Error ? error.message : 'Unknown error',
        userId: (req as any).user?.id,
        reportTypeId: req.params.id,
      });

      res.status(500).json({
        status: 'error',
        message: 'Failed to delete report type',
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * Duplicate a report type
   */
  async duplicateReportType(req: Request, res: Response): Promise<void> {
    const startTime = Date.now();

    try {
      const { id } = req.params;

      // For now, return mock duplicated report type
      const duplicatedReportType = {
        id: Date.now().toString(),
        name: 'Copy of Sample Report Type',
        description: 'A duplicated report type',
        category: 'general',
        dataSource: 'delegations',
        widgets: ['delegation-status'],
        isActive: true,
        isPublic: true,
        filters: ['dateRange'],
        refreshInterval: 300000,
        tags: ['sample', 'copy'],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      const duration = (Date.now() - startTime) / 1000;
      businessMetrics.recordApiEndpointDuration(
        '/reporting/report-types/:id/duplicate',
        'POST',
        '201',
        duration,
      );

      res.status(201).json({
        status: 'success',
        data: duplicatedReportType,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      const duration = (Date.now() - startTime) / 1000;
      businessMetrics.recordApiEndpointDuration(
        '/reporting/report-types/:id/duplicate',
        'POST',
        '500',
        duration,
      );

      logger.error('Error duplicating report type:', {
        error: error instanceof Error ? error.message : 'Unknown error',
        userId: (req as any).user?.id,
        reportTypeId: req.params.id,
      });

      res.status(500).json({
        status: 'error',
        message: 'Failed to duplicate report type',
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * Toggle report type active status
   */
  async toggleReportTypeActive(req: Request, res: Response): Promise<void> {
    const startTime = Date.now();

    try {
      const { id } = req.params;
      const { isActive } = req.body;

      // For now, return mock updated report type
      const updatedReportType = {
        id,
        name: 'Sample Report Type',
        description: 'A sample report type for testing',
        category: 'general',
        dataSource: 'delegations',
        widgets: ['delegation-status'],
        isActive,
        isPublic: true,
        filters: ['dateRange'],
        refreshInterval: 300000,
        tags: ['sample'],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      const duration = (Date.now() - startTime) / 1000;
      businessMetrics.recordApiEndpointDuration(
        '/reporting/report-types/:id/toggle-active',
        'PATCH',
        '200',
        duration,
      );

      res.json({
        status: 'success',
        data: updatedReportType,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      const duration = (Date.now() - startTime) / 1000;
      businessMetrics.recordApiEndpointDuration(
        '/reporting/report-types/:id/toggle-active',
        'PATCH',
        '500',
        duration,
      );

      logger.error('Error toggling report type active status:', {
        error: error instanceof Error ? error.message : 'Unknown error',
        userId: (req as any).user?.id,
        reportTypeId: req.params.id,
        body: req.body,
      });

      res.status(500).json({
        status: 'error',
        message: 'Failed to toggle report type active status',
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * Get cross-entity analytics
   */
  async getCrossEntityAnalytics(req: Request, res: Response): Promise<void> {
    const startTime = Date.now();

    try {
      const validationResult = reportingValidationSchema.filtersSchema.safeParse(req.query);

      if (!validationResult.success) {
        res.status(400).json({
          status: 'error',
          message: 'Invalid filter parameters',
          errors: validationResult.error.errors,
          timestamp: new Date().toISOString(),
        });
        return;
      }

      const filters = validationResult.data;
      const crossEntityAnalytics = await reportingService.getCrossEntityAnalytics(filters);

      const duration = (Date.now() - startTime) / 1000;
      businessMetrics.recordApiEndpointDuration(
        '/reporting/cross-entity/analytics',
        'GET',
        '200',
        duration,
      );

      res.json({
        status: 'success',
        data: crossEntityAnalytics,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      const duration = (Date.now() - startTime) / 1000;
      businessMetrics.recordApiEndpointDuration(
        '/reporting/cross-entity/analytics',
        'GET',
        '500',
        duration,
      );

      logger.error('Error getting cross-entity analytics:', {
        error: error instanceof Error ? error.message : 'Unknown error',
        userId: (req as any).user?.id,
        filters: req.query,
      });

      res.status(500).json({
        status: 'error',
        message: 'Failed to get cross-entity analytics',
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * Generate comprehensive data report
   */
  async generateDataReport(req: Request, res: Response): Promise<void> {
    const startTime = Date.now();

    try {
      const { entityTypes, filters, format, template, options } = req.body;

      // Validate entity types
      const validEntityTypes = ['delegations', 'tasks', 'vehicles', 'employees'];
      const requestedTypes = entityTypes || validEntityTypes;

      if (!requestedTypes.every((type: string) => validEntityTypes.includes(type))) {
        res.status(400).json({
          status: 'error',
          message: 'Invalid entity types provided',
          timestamp: new Date().toISOString(),
        });
        return;
      }

      // Generate report data for requested entities
      const reportData: any = {};

      if (requestedTypes.includes('delegations')) {
        reportData.delegations = await reportingService.getDelegationAnalytics(filters || {});
      }

      if (requestedTypes.includes('tasks')) {
        reportData.tasks = await reportingService.getTaskAnalytics(filters || {});
      }

      if (requestedTypes.includes('vehicles')) {
        reportData.vehicles = await reportingService.getVehicleAnalytics(filters || {});
      }

      if (requestedTypes.includes('employees')) {
        reportData.employees = await reportingService.getEmployeeAnalytics(filters || {});
      }

      // Generate report metadata
      const reportId = `report_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const reportMetadata = {
        id: reportId,
        type: 'comprehensive',
        entityTypes: requestedTypes,
        format: format || 'json',
        template: template || 'default',
        generatedAt: new Date().toISOString(),
        generatedBy: (req as any).user?.id || 'system',
        filters: filters || {},
        options: options || {},
      };

      const duration = (Date.now() - startTime) / 1000;
      businessMetrics.recordApiEndpointDuration(
        '/reporting/reports/generate',
        'POST',
        '200',
        duration,
      );

      res.json({
        status: 'success',
        data: {
          report: reportData,
          metadata: reportMetadata,
        },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      const duration = (Date.now() - startTime) / 1000;
      businessMetrics.recordApiEndpointDuration(
        '/reporting/reports/generate',
        'POST',
        '500',
        duration,
      );

      logger.error('Error generating data report:', {
        error: error instanceof Error ? error.message : 'Unknown error',
        userId: (req as any).user?.id,
        body: req.body,
      });

      res.status(500).json({
        status: 'error',
        message: 'Failed to generate data report',
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * Get available report templates
   */
  async getReportTemplates(req: Request, res: Response): Promise<void> {
    const startTime = Date.now();

    try {
      const templates = [
        {
          id: 'comprehensive',
          name: 'Comprehensive Report',
          description: 'Complete overview of all entities with analytics',
          entityTypes: ['delegations', 'tasks', 'vehicles', 'employees'],
          sections: ['summary', 'analytics', 'trends', 'details'],
          formats: ['pdf', 'excel', 'csv'],
        },
        {
          id: 'delegation-focused',
          name: 'Delegation-Focused Report',
          description: 'Detailed delegation analysis with related tasks',
          entityTypes: ['delegations', 'tasks'],
          sections: ['delegation-summary', 'task-breakdown', 'performance'],
          formats: ['pdf', 'excel'],
        },
        {
          id: 'operational',
          name: 'Operational Report',
          description: 'Vehicle and employee operational metrics',
          entityTypes: ['vehicles', 'employees'],
          sections: ['utilization', 'performance', 'maintenance'],
          formats: ['pdf', 'excel', 'csv'],
        },
        {
          id: 'executive-summary',
          name: 'Executive Summary',
          description: 'High-level overview for management',
          entityTypes: ['delegations', 'tasks', 'vehicles', 'employees'],
          sections: ['key-metrics', 'trends', 'recommendations'],
          formats: ['pdf'],
        },
      ];

      const duration = (Date.now() - startTime) / 1000;
      businessMetrics.recordApiEndpointDuration(
        '/reporting/reports/templates',
        'GET',
        '200',
        duration,
      );

      res.json({
        status: 'success',
        data: templates,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      const duration = (Date.now() - startTime) / 1000;
      businessMetrics.recordApiEndpointDuration(
        '/reporting/reports/templates',
        'GET',
        '500',
        duration,
      );

      logger.error('Error getting report templates:', {
        error: error instanceof Error ? error.message : 'Unknown error',
        userId: (req as any).user?.id,
      });

      res.status(500).json({
        status: 'error',
        message: 'Failed to get report templates',
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * Generate individual entity report
   */
  async generateIndividualReport(req: Request, res: Response): Promise<void> {
    const startTime = Date.now();

    try {
      const { entityType, entityId } = req.params;
      const { format, template, options } = req.body;

      // Validate entity type
      const validEntityTypes = ['delegations', 'tasks', 'vehicles', 'employees'];
      if (!validEntityTypes.includes(entityType)) {
        res.status(400).json({
          status: 'error',
          message: `Invalid entity type: ${entityType}`,
          timestamp: new Date().toISOString(),
        });
        return;
      }

      // Fetch individual entity data
      let entityData: any;
      switch (entityType) {
        case 'delegations':
          entityData = await this.getIndividualDelegation(entityId);
          break;
        case 'tasks':
          entityData = await this.getIndividualTask(entityId);
          break;
        case 'vehicles':
          entityData = await this.getIndividualVehicle(entityId);
          break;
        case 'employees':
          entityData = await this.getIndividualEmployee(entityId);
          break;
        default:
          throw new Error(`Unsupported entity type: ${entityType}`);
      }

      if (!entityData) {
        res.status(404).json({
          status: 'error',
          message: `${entityType.slice(0, -1)} not found`,
          timestamp: new Date().toISOString(),
        });
        return;
      }

      // Generate report metadata
      const reportId = `individual_${entityType}_${entityId}_${Date.now()}`;
      const reportMetadata = {
        id: reportId,
        type: 'individual',
        entityType,
        entityId,
        format: format || 'json',
        template: template || 'default',
        generatedAt: new Date().toISOString(),
        generatedBy: (req as any).user?.id || 'system',
        options: options || {},
      };

      const duration = (Date.now() - startTime) / 1000;
      businessMetrics.recordApiEndpointDuration(
        '/reporting/reports/individual',
        'POST',
        '200',
        duration,
      );

      res.json({
        status: 'success',
        data: {
          entity: entityData,
          metadata: reportMetadata,
        },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      const duration = (Date.now() - startTime) / 1000;
      businessMetrics.recordApiEndpointDuration(
        '/reporting/reports/individual',
        'POST',
        '500',
        duration,
      );

      logger.error('Error generating individual report:', {
        error: error instanceof Error ? error.message : 'Unknown error',
        userId: (req as any).user?.id,
        params: req.params,
        body: req.body,
      });

      res.status(500).json({
        status: 'error',
        message: 'Failed to generate individual report',
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * Generate aggregate entity report
   */
  async generateAggregateReport(req: Request, res: Response): Promise<void> {
    const startTime = Date.now();

    try {
      const { entityType } = req.params;
      const { filters, format, template, options } = req.body;

      // Validate entity type
      const validEntityTypes = ['delegations', 'tasks', 'vehicles', 'employees'];
      if (!validEntityTypes.includes(entityType)) {
        res.status(400).json({
          status: 'error',
          message: `Invalid entity type: ${entityType}`,
          timestamp: new Date().toISOString(),
        });
        return;
      }

      // Fetch aggregate data based on entity type
      let aggregateData: any;
      switch (entityType) {
        case 'delegations':
          aggregateData = await reportingService.getDelegationAnalytics(filters || {});
          break;
        case 'tasks':
          aggregateData = await reportingService.getTaskAnalytics(filters || {});
          break;
        case 'vehicles':
          aggregateData = await reportingService.getVehicleAnalytics(filters || {});
          break;
        case 'employees':
          aggregateData = await reportingService.getEmployeeAnalytics(filters || {});
          break;
        default:
          throw new Error(`Unsupported entity type: ${entityType}`);
      }

      // Generate report metadata
      const reportId = `aggregate_${entityType}_${Date.now()}`;
      const reportMetadata = {
        id: reportId,
        type: 'aggregate',
        entityType,
        format: format || 'json',
        template: template || 'default',
        generatedAt: new Date().toISOString(),
        generatedBy: (req as any).user?.id || 'system',
        filters: filters || {},
        options: options || {},
      };

      const duration = (Date.now() - startTime) / 1000;
      businessMetrics.recordApiEndpointDuration(
        '/reporting/reports/aggregate',
        'POST',
        '200',
        duration,
      );

      res.json({
        status: 'success',
        data: {
          aggregate: aggregateData,
          metadata: reportMetadata,
        },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      const duration = (Date.now() - startTime) / 1000;
      businessMetrics.recordApiEndpointDuration(
        '/reporting/reports/aggregate',
        'POST',
        '500',
        duration,
      );

      logger.error('Error generating aggregate report:', {
        error: error instanceof Error ? error.message : 'Unknown error',
        userId: (req as any).user?.id,
        params: req.params,
        body: req.body,
      });

      res.status(500).json({
        status: 'error',
        message: 'Failed to generate aggregate report',
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * Get report generation history
   */
  async getReportHistory(req: Request, res: Response): Promise<void> {
    const startTime = Date.now();

    try {
      const { page = 1, limit = 20, type, entityType } = req.query;

      // For now, return mock history data
      const mockHistory = [
        {
          id: 'report_1703123456789_abc123',
          type: 'comprehensive',
          entityTypes: ['delegations', 'tasks'],
          format: 'pdf',
          template: 'comprehensive',
          status: 'completed',
          generatedAt: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
          generatedBy: (req as any).user?.id || 'user123',
          downloadUrl: '/api/reporting/reports/report_1703123456789_abc123/download',
          fileSize: '2.4 MB',
        },
        {
          id: 'individual_delegations_del123_1703023456789',
          type: 'individual',
          entityType: 'delegations',
          entityId: 'del123',
          format: 'excel',
          template: 'default',
          status: 'completed',
          generatedAt: new Date(Date.now() - 172800000).toISOString(), // 2 days ago
          generatedBy: (req as any).user?.id || 'user123',
          downloadUrl:
            '/api/reporting/reports/individual_delegations_del123_1703023456789/download',
          fileSize: '1.2 MB',
        },
      ];

      // Apply filters
      let filteredHistory = mockHistory;
      if (type) {
        filteredHistory = filteredHistory.filter(report => report.type === type);
      }
      if (entityType) {
        filteredHistory = filteredHistory.filter(
          report =>
            report.entityType === entityType ||
            (report.entityTypes && report.entityTypes.includes(entityType as string)),
        );
      }

      // Apply pagination
      const startIndex = (Number(page) - 1) * Number(limit);
      const endIndex = startIndex + Number(limit);
      const paginatedHistory = filteredHistory.slice(startIndex, endIndex);

      const duration = (Date.now() - startTime) / 1000;
      businessMetrics.recordApiEndpointDuration(
        '/reporting/reports/history',
        'GET',
        '200',
        duration,
      );

      res.json({
        status: 'success',
        data: {
          reports: paginatedHistory,
          pagination: {
            page: Number(page),
            limit: Number(limit),
            total: filteredHistory.length,
            totalPages: Math.ceil(filteredHistory.length / Number(limit)),
          },
        },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      const duration = (Date.now() - startTime) / 1000;
      businessMetrics.recordApiEndpointDuration(
        '/reporting/reports/history',
        'GET',
        '500',
        duration,
      );

      logger.error('Error getting report history:', {
        error: error instanceof Error ? error.message : 'Unknown error',
        userId: (req as any).user?.id,
        query: req.query,
      });

      res.status(500).json({
        status: 'error',
        message: 'Failed to get report history',
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * Download generated report
   */
  async downloadReport(req: Request, res: Response): Promise<void> {
    const startTime = Date.now();

    try {
      const { reportId } = req.params;

      // For now, return a placeholder response
      // In a real implementation, this would fetch the file from storage
      const duration = (Date.now() - startTime) / 1000;
      businessMetrics.recordApiEndpointDuration(
        '/reporting/reports/:reportId/download',
        'GET',
        '200',
        duration,
      );

      res.json({
        status: 'success',
        message: 'Report download functionality will be implemented with file storage',
        data: {
          reportId,
          downloadUrl: `#download-${reportId}`,
        },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      const duration = (Date.now() - startTime) / 1000;
      businessMetrics.recordApiEndpointDuration(
        '/reporting/reports/:reportId/download',
        'GET',
        '500',
        duration,
      );

      logger.error('Error downloading report:', {
        error: error instanceof Error ? error.message : 'Unknown error',
        userId: (req as any).user?.id,
        reportId: req.params.reportId,
      });

      res.status(500).json({
        status: 'error',
        message: 'Failed to download report',
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * Helper method to get individual delegation data
   */
  private async getIndividualDelegation(delegationId: string): Promise<any> {
    // For now, return mock data
    // In a real implementation, this would fetch from the database
    return {
      id: delegationId,
      title: 'Sample Delegation',
      description: 'A sample delegation for testing',
      status: 'Active',
      createdAt: new Date().toISOString(),
      assignedTo: 'John Doe',
      location: 'Main Office',
      priority: 'High',
      tasks: [
        {
          id: 'task1',
          title: 'Task 1',
          status: 'Completed',
          assignedTo: 'Jane Smith',
        },
        {
          id: 'task2',
          title: 'Task 2',
          status: 'In Progress',
          assignedTo: 'Bob Johnson',
        },
      ],
    };
  }

  /**
   * Helper method to get individual task data
   */
  private async getIndividualTask(taskId: string): Promise<any> {
    // For now, return mock data
    return {
      id: taskId,
      title: 'Sample Task',
      description: 'A sample task for testing',
      status: 'In Progress',
      priority: 'Medium',
      createdAt: new Date().toISOString(),
      assignedTo: 'Alice Brown',
      delegationId: 'del123',
      estimatedHours: 8,
      actualHours: 5,
    };
  }

  /**
   * Helper method to get individual vehicle data
   */
  private async getIndividualVehicle(vehicleId: string): Promise<any> {
    // For now, return mock data
    return {
      id: vehicleId,
      make: 'Toyota',
      model: 'Camry',
      year: 2022,
      licensePlate: 'ABC-123',
      status: 'Active',
      mileage: 25000,
      lastMaintenance: new Date(Date.now() - **********).toISOString(), // 30 days ago
      nextMaintenance: new Date(Date.now() + **********).toISOString(), // 30 days from now
      assignedTo: 'Mike Wilson',
    };
  }

  /**
   * Helper method to get individual employee data
   */
  private async getIndividualEmployee(employeeId: string): Promise<any> {
    // For now, return mock data
    return {
      id: employeeId,
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      position: 'Senior Technician',
      department: 'Operations',
      hireDate: new Date(Date.now() - 31536000000).toISOString(), // 1 year ago
      status: 'Active',
      activeDelegations: 3,
      completedTasks: 45,
      averageTaskCompletionTime: 6.5,
    };
  }

  /**
   * Export to PDF
   */
  async exportToPDF(req: Request, res: Response): Promise<void> {
    const startTime = Date.now();

    try {
      const { reportType = 'delegation-analytics', filters = {}, filename = 'report' } = req.body;

      // Get the data based on report type
      let data;
      switch (reportType) {
        case 'delegation-analytics':
          data = await reportingService.getDelegationAnalytics(filters);
          break;
        case 'task-metrics':
          data = await reportingService.getTaskMetrics();
          break;
        default:
          throw new Error(`Unsupported report type: ${reportType}`);
      }

      // For now, return the data as JSON with PDF headers
      // In a full implementation, you would use a PDF library like puppeteer or jsPDF
      res.setHeader('Content-Type', 'application/pdf');
      res.setHeader('Content-Disposition', `attachment; filename="${filename}.pdf"`);

      // Placeholder: Return JSON data (in real implementation, generate actual PDF)
      res.json({
        status: 'success',
        message: 'PDF export functionality ready - implement with puppeteer or jsPDF',
        data: data,
        filename: `${filename}.pdf`,
        timestamp: new Date().toISOString(),
      });

      const duration = (Date.now() - startTime) / 1000;
      businessMetrics.recordApiEndpointDuration('/reporting/export/pdf', 'POST', '200', duration);
    } catch (error) {
      const duration = (Date.now() - startTime) / 1000;
      businessMetrics.recordApiEndpointDuration('/reporting/export/pdf', 'POST', '500', duration);

      logger.error('Error exporting to PDF:', {
        error: error instanceof Error ? error.message : 'Unknown error',
        userId: (req as any).user?.id,
        body: req.body,
      });

      res.status(500).json({
        status: 'error',
        message: 'Failed to export to PDF',
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * Export to Excel
   */
  async exportToExcel(req: Request, res: Response): Promise<void> {
    const startTime = Date.now();

    try {
      const { reportType = 'delegation-analytics', filters = {}, filename = 'report' } = req.body;

      // Get the data based on report type
      let data;
      let excelData;

      switch (reportType) {
        case 'delegation-analytics':
          data = await reportingService.getDelegationAnalytics(filters);
          // Transform delegation analytics data for Excel export
          excelData = {
            summary: {
              totalDelegations: (data as any).totalCount || 0,
              activeDelegations: (data as any).summary?.activeDelegations || 0,
              completedDelegations: (data as any).summary?.completedDelegations || 0,
              completionRate: (data as any).summary?.completionRate || 0,
            },
            statusDistribution: (data as any).statusDistribution || [],
            locationMetrics: (data as any).locationMetrics || [],
            trendData: (data as any).trendData || [],
            delegations: (data as any).delegations || [],
          };
          break;
        case 'task-metrics':
          data = await reportingService.getTaskMetrics();
          // Transform task metrics data for Excel export
          excelData = {
            summary: {
              totalTasks: (data as any).totalTasks || 0,
              completedTasks: (data as any).completedTasks || 0,
              pendingTasks: (data as any).pendingTasks || 0,
              overdueTasks: (data as any).overdueTasks || 0,
            },
            taskMetrics: data,
          };
          break;
        default:
          throw new Error(`Unsupported report type: ${reportType}`);
      }

      res.setHeader(
        'Content-Type',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      );
      res.setHeader('Content-Disposition', `attachment; filename="${filename}.xlsx"`);

      // Return structured data for frontend Excel generation
      res.json({
        status: 'success',
        data: excelData,
        filename: `${filename}.xlsx`,
        timestamp: new Date().toISOString(),
      });

      const duration = (Date.now() - startTime) / 1000;
      businessMetrics.recordApiEndpointDuration('/reporting/export/excel', 'POST', '200', duration);
    } catch (error) {
      const duration = (Date.now() - startTime) / 1000;
      businessMetrics.recordApiEndpointDuration('/reporting/export/excel', 'POST', '500', duration);

      logger.error('Error exporting to Excel:', {
        error: error instanceof Error ? error.message : 'Unknown error',
        userId: (req as any).user?.id,
        body: req.body,
      });

      res.status(500).json({
        status: 'error',
        message: 'Failed to export to Excel',
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * Export to CSV
   */
  async exportToCSV(req: Request, res: Response): Promise<void> {
    const startTime = Date.now();

    try {
      const {
        reportType = 'delegation-analytics',
        filters = {},
        filename = 'report',
        dataType = 'delegations',
      } = req.body;

      // Get the data based on report type
      let data;
      let csvData = [];

      switch (reportType) {
        case 'delegation-analytics':
          data = await reportingService.getDelegationAnalytics(filters);
          // Extract specific data type for CSV
          switch (dataType) {
            case 'delegations':
              csvData = (data as any).delegations || [];
              break;
            case 'status-distribution':
              csvData = (data as any).statusDistribution || [];
              break;
            case 'location-metrics':
              csvData = (data as any).locationMetrics || [];
              break;
            case 'trend-data':
              csvData = (data as any).trendData || [];
              break;
            default:
              csvData = (data as any).delegations || [];
          }
          break;
        case 'task-metrics':
          data = await reportingService.getTaskMetrics();
          // For task metrics, return the metrics as CSV data
          csvData = [data];
          break;
        default:
          throw new Error(`Unsupported report type: ${reportType}`);
      }

      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', `attachment; filename="${filename}.csv"`);

      // Return data for frontend CSV generation
      res.json({
        status: 'success',
        data: csvData,
        filename: `${filename}.csv`,
        timestamp: new Date().toISOString(),
      });

      const duration = (Date.now() - startTime) / 1000;
      businessMetrics.recordApiEndpointDuration('/reporting/export/csv', 'POST', '200', duration);
    } catch (error) {
      const duration = (Date.now() - startTime) / 1000;
      businessMetrics.recordApiEndpointDuration('/reporting/export/csv', 'POST', '500', duration);

      logger.error('Error exporting to CSV:', {
        error: error instanceof Error ? error.message : 'Unknown error',
        userId: (req as any).user?.id,
        body: req.body,
      });

      res.status(500).json({
        status: 'error',
        message: 'Failed to export to CSV',
        timestamp: new Date().toISOString(),
      });
    }
  }
}

export const reportingController = new ReportingController();
