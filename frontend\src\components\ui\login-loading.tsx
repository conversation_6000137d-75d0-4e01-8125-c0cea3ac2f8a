import * as React from 'react';
import { Loader2, Shield, Check } from 'lucide-react';
import { cn } from '@/lib/utils';

interface LoginLoadingProps {
  className?: string;
  stage?: 'authenticating' | 'verifying' | 'redirecting' | 'success';
  message?: string;
}

/**
 * Login Loading Component
 *
 * A sophisticated loading animation for the login process that provides
 * clear visual feedback about the authentication stage.
 */
export function LoginLoading({
  className,
  stage = 'authenticating',
  message,
}: LoginLoadingProps) {
  const getStageConfig = () => {
    switch (stage) {
      case 'authenticating':
        return {
          icon: <Loader2 className="size-6 animate-spin text-primary" />,
          text: message || 'Authenticating credentials...',
          description: 'Verifying your identity securely',
        };
      case 'verifying':
        return {
          icon: <Shield className="size-6 text-accent animate-pulse" />,
          text: message || 'Verifying security...',
          description: 'Checking account permissions',
        };
      case 'redirecting':
        return {
          icon: <Loader2 className="size-6 animate-spin text-primary" />,
          text: message || 'Preparing your dashboard...',
          description: 'Setting up your workspace',
        };
      case 'success':
        return {
          icon: <Check className="size-6 text-green-600" />,
          text: message || 'Welcome back!',
          description: 'Login successful',
        };
      default:
        return {
          icon: <Loader2 className="size-6 animate-spin text-primary" />,
          text: message || 'Loading...',
          description: 'Please wait',
        };
    }
  };

  const config = getStageConfig();

  return (
    <div
      className={cn(
        'flex flex-col items-center justify-center p-8 text-center space-y-4',
        className
      )}
    >
      {/* Animated Icon */}
      <div className="relative">
        <div className="absolute inset-0 rounded-full bg-primary/10 animate-ping" />
        <div className="relative flex items-center justify-center w-12 h-12 rounded-full bg-background border border-border/60 shadow-lg">
          {config.icon}
        </div>
      </div>

      {/* Loading Text */}
      <div className="space-y-2">
        <p className="text-lg font-medium text-foreground">{config.text}</p>
        <p className="text-sm text-muted-foreground">{config.description}</p>
      </div>

      {/* Progress Dots */}
      <div className="flex space-x-2">
        {[0, 1, 2].map(index => (
          <div
            key={index}
            className={cn(
              'w-2 h-2 rounded-full bg-primary/30 animate-pulse',
              'transition-all duration-300'
            )}
            style={{
              animationDelay: `${index * 0.2}s`,
              animationDuration: '1.5s',
            }}
          />
        ))}
      </div>
    </div>
  );
}
