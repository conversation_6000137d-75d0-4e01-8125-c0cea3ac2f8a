/**
 * @service ExportService
 * @description Enhanced export service supporting all entity types and formats
 *
 * Responsibilities:
 * - Coordinate export operations across all formats
 * - Manage export queue and progress tracking
 * - Handle export history and caching
 * - Provide unified export interface
 *
 * SOLID Principles Applied:
 * - SRP: Single responsibility of coordinating exports
 * - OCP: Open for extension via new export formats
 * - DIP: Depends on format-specific service abstractions
 */

import { PDFExportService } from './PDFExportService';
import { ExcelExportService } from './ExcelExportService';
import { CSVExportService } from './CSVExportService';
import { ReportTemplateService } from './ReportTemplateService';
import { ExportProgressTracker } from '../../utils/ExportProgressTracker';
import type {
  ComprehensiveExportOptions,
  EntityExportOptions,
  ExportResult,
  EntityType,
  ExportFormat,
  ExportHistory,
  ExportProgress,
} from '../../types/export';
import type {
  DelegationAnalytics,
  TaskAnalytics,
  VehicleAnalytics,
  EmployeeAnalytics,
  ReportingFilters,
} from '../../types/reporting';
import { ReportingDataService } from '../ReportingDataService';

export class ExportService {
  private readonly pdfService: PDFExportService;
  private readonly excelService: ExcelExportService;
  private readonly csvService: CSVExportService;
  private readonly templateService: ReportTemplateService;
  private readonly progressTracker: ExportProgressTracker;
  private readonly dataService: ReportingDataService;
  private readonly exportHistory: Map<string, ExportHistory> = new Map();

  constructor() {
    this.pdfService = new PDFExportService();
    this.excelService = new ExcelExportService();
    this.csvService = new CSVExportService();
    this.templateService = new ReportTemplateService();
    this.progressTracker = new ExportProgressTracker();
    this.dataService = new ReportingDataService();
  }

  /**
   * Export comprehensive report with all entity data
   */
  async exportComprehensiveReport(
    options: ComprehensiveExportOptions
  ): Promise<ExportResult> {
    const exportId = this.generateExportId();

    try {
      this.progressTracker.start(exportId, 'Preparing comprehensive report...');

      // Gather all entity data in parallel
      const [delegations, tasks, vehicles, employees] = await Promise.all([
        this.gatherDelegationData(options.filters),
        this.gatherTaskData(options.filters),
        this.gatherVehicleData(options.filters),
        this.gatherEmployeeData(options.filters),
      ]);

      this.progressTracker.update(
        exportId,
        40,
        'Data gathered, generating report...'
      );

      // Generate report based on format
      let result: ExportResult;
      switch (options.format) {
        case 'pdf':
          result = await this.pdfService.generateComprehensiveReport({
            delegations,
            tasks,
            vehicles,
            employees,
            template: options.template,
            options: options.pdfOptions,
          });
          break;

        case 'excel':
          result = await this.excelService.generateComprehensiveReport({
            delegations,
            tasks,
            vehicles,
            employees,
            template: options.template,
            options: options.excelOptions,
          });
          break;

        case 'csv':
          result = await this.csvService.generateComprehensiveReport({
            delegations,
            tasks,
            vehicles,
            employees,
            options: options.csvOptions,
          });
          break;

        default:
          throw new Error(`Unsupported export format: ${options.format}`);
      }

      this.progressTracker.complete(exportId, 'Report generated successfully');
      this.saveToHistory(exportId, options, result);

      return result;
    } catch (error: any) {
      this.progressTracker.error(exportId, error.message);
      throw error;
    }
  }

  /**
   * Export individual entity report
   */
  async exportEntityReport(
    entityType: EntityType,
    entityId: string,
    options: EntityExportOptions
  ): Promise<ExportResult> {
    const exportId = this.generateExportId();

    try {
      this.progressTracker.start(exportId, `Preparing ${entityType} report...`);

      // Get entity-specific data
      const entityData = await this.gatherEntityData(
        entityType,
        entityId,
        options.filters
      );

      this.progressTracker.update(exportId, 50, 'Generating report...');

      // Generate report using appropriate service
      let result: ExportResult;
      switch (options.format) {
        case 'pdf':
          result = await this.pdfService.generateEntityReport(
            entityType,
            entityData,
            options.pdfOptions
          );
          break;

        case 'excel':
          result = await this.excelService.generateEntityReport(
            entityType,
            entityData,
            options.excelOptions
          );
          break;

        case 'csv':
          result = await this.csvService.generateEntityReport(
            entityType,
            entityData,
            options.csvOptions
          );
          break;

        default:
          throw new Error(`Unsupported export format: ${options.format}`);
      }

      this.progressTracker.complete(exportId, 'Report generated successfully');
      this.saveToHistory(exportId, options, result);

      return result;
    } catch (error: any) {
      this.progressTracker.error(exportId, error.message);
      throw error;
    }
  }

  /**
   * Get export progress
   */
  getExportProgress(exportId: string): ExportProgress | null {
    return this.progressTracker.getProgress(exportId);
  }

  /**
   * Get export history
   */
  getExportHistory(): ExportHistory[] {
    return Array.from(this.exportHistory.values()).sort(
      (a, b) => b.timestamp.getTime() - a.timestamp.getTime()
    );
  }

  /**
   * Cancel export operation
   */
  cancelExport(exportId: string): boolean {
    return this.progressTracker.cancel(exportId);
  }

  /**
   * Private helper methods
   */
  private generateExportId(): string {
    return `export_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private async gatherDelegationData(
    filters: ReportingFilters
  ): Promise<DelegationAnalytics> {
    return await this.dataService.getDelegationAnalytics(filters);
  }

  private async gatherTaskData(
    filters: ReportingFilters
  ): Promise<TaskAnalytics> {
    return await this.dataService.getTaskAnalytics(filters);
  }

  private async gatherVehicleData(
    filters: ReportingFilters
  ): Promise<VehicleAnalytics> {
    return await this.dataService.getVehicleAnalytics(filters);
  }

  private async gatherEmployeeData(
    filters: ReportingFilters
  ): Promise<EmployeeAnalytics> {
    return await this.dataService.getEmployeeAnalytics(filters);
  }

  private async gatherEntityData(
    entityType: EntityType,
    entityId: string,
    filters: ReportingFilters
  ): Promise<any> {
    switch (entityType) {
      case 'delegation':
        return await this.dataService.getDelegationAnalytics({
          ...filters,
          // Use existing filter structure for delegations
        });
      case 'task':
        return await this.dataService.getTaskAnalytics({
          ...filters,
          // Use existing filter structure for tasks
        });
      case 'vehicle':
        return await this.dataService.getVehicleAnalytics({
          ...filters,
          vehicles: [parseInt(entityId)],
        });
      case 'employee':
        return await this.dataService.getEmployeeAnalytics({
          ...filters,
          employees: [parseInt(entityId)],
        });
      default:
        throw new Error(`Unsupported entity type: ${entityType}`);
    }
  }

  private saveToHistory(
    exportId: string,
    options: ComprehensiveExportOptions | EntityExportOptions,
    result: ExportResult
  ): void {
    const historyEntry: ExportHistory = {
      id: exportId,
      timestamp: new Date(),
      format: options.format,
      filename: result.filename,
      fileSize: result.fileSize,
      status: 'completed',
      options,
    };

    this.exportHistory.set(exportId, historyEntry);

    // Keep only last 50 exports
    if (this.exportHistory.size > 50) {
      const oldestKey = Array.from(this.exportHistory.keys())[0];
      if (oldestKey) {
        this.exportHistory.delete(oldestKey);
      }
    }
  }
}

// Singleton instance
export const exportService = new ExportService();
