import type { Request, Response, NextFunction } from 'express';

import type {
  Prisma,
  Task as PrismaTask,
  TaskPriority as PrismaTaskPriority,
  TaskStatus as PrismaTaskStatus,
} from '../generated/prisma/index.js';

import type {
  TaskCreatedPayload,
  TaskDeletedPayload,
  TaskUpdatedPayload,
  Task as WebSocketTaskPayload,
} from '../types/websocketEvents.js';

import * as taskModel from '../models/task.model.js';
import { validateTaskAssignment } from '../services/assignmentValidation.js';
import { getUnifiedWebSocketService } from '../services/UnifiedWebSocketService.js';
import { CRUD_EVENTS } from '../services/WebSocketEventManager.js';
import logger from '../utils/logger.js';
import HttpError from '../utils/HttpError.js';

const CRUD_ROOM = 'entity-updates';

const mapPrismaTaskToPayload = (prismaTask: PrismaTask): WebSocketTaskPayload => {
  return {
    id: prismaTask.id,
    description: prismaTask.description,
    status: prismaTask.status,
    priority: prismaTask.priority,
    location: prismaTask.location,
    dateTime: prismaTask.dateTime.toISOString(),
    deadline: prismaTask.deadline ? prismaTask.deadline.toISOString() : null,
    estimatedDuration: prismaTask.estimatedDuration,
    notes: prismaTask.notes,
    vehicleId: prismaTask.vehicleId,
    staffEmployeeId: prismaTask.staffEmployeeId,
    driverEmployeeId: prismaTask.driverEmployeeId,
  };
};

const processTaskData = (data: any): Prisma.TaskCreateInput => {
  const createInput: Prisma.TaskCreateInput = {
    dateTime: new Date(data.dateTime),
    description: data.description,
    Employee_Task_staffEmployeeIdToEmployee: {
      connect: { id: Number(data.staffEmployeeId) },
    },
    estimatedDuration: data.estimatedDuration,
    id: `task_${Date.now()}_${Math.random()}`,
    location: data.location,
    notes: data.notes,
    priority: data.priority as PrismaTaskPriority,
    requiredSkills: data.requiredSkills,
    status: (data.status || 'Pending') as PrismaTaskStatus,
    updatedAt: new Date(),
  };

  if (data.deadline) createInput.deadline = new Date(data.deadline);

  if (data.vehicleId) {
    createInput.Vehicle = { connect: { id: Number(data.vehicleId) } };
  }

  if (data.driverEmployeeId) {
    createInput.Employee_Task_driverEmployeeIdToEmployee = {
      connect: { id: Number(data.driverEmployeeId) },
    };
  }

  if (data.subTasks && Array.isArray(data.subTasks)) {
    createInput.SubTask = {
      create: data.subTasks.map((st: any) => ({
        completed: st.completed || false,
        id: `subtask_${Date.now()}_${Math.random()}`,
        title: st.title,
      })),
    };
  }
  return createInput;
};

const processTaskUpdateData = (data: any): Prisma.TaskUpdateInput => {
  const updateInput: Prisma.TaskUpdateInput = {
    description: data.description,
    estimatedDuration: data.estimatedDuration,
    location: data.location,
    notes: data.notes,
    priority: data.priority ? (data.priority as PrismaTaskPriority) : undefined,
    requiredSkills: data.requiredSkills,
  };

  if (data.dateTime) updateInput.dateTime = new Date(data.dateTime);
  if (data.hasOwnProperty('deadline')) {
    updateInput.deadline = data.deadline ? new Date(data.deadline) : null;
  }

  if (data.hasOwnProperty('vehicleId')) {
    if (data.vehicleId === null) {
      updateInput.Vehicle = { disconnect: true };
    } else if (data.vehicleId !== undefined) {
      updateInput.Vehicle = { connect: { id: Number(data.vehicleId) } };
    }
  }

  if (data.hasOwnProperty('staffEmployeeId')) {
    if (data.staffEmployeeId !== null && data.staffEmployeeId !== undefined) {
      updateInput.Employee_Task_staffEmployeeIdToEmployee = {
        connect: { id: Number(data.staffEmployeeId) },
      };
    }
  }

  if (data.hasOwnProperty('driverEmployeeId')) {
    if (data.driverEmployeeId === null) {
      updateInput.Employee_Task_driverEmployeeIdToEmployee = { disconnect: true };
    } else if (data.driverEmployeeId !== undefined) {
      updateInput.Employee_Task_driverEmployeeIdToEmployee = {
        connect: { id: Number(data.driverEmployeeId) },
      };
    }
  }

  if (data.subTasks && Array.isArray(data.subTasks)) {
    updateInput.SubTask = {
      create: data.subTasks.map((st: any) => ({
        completed: st.completed || false,
        id: `subtask_${Date.now()}_${Math.random()}`,
        title: st.title,
      })),
      deleteMany: {},
    };
  }

  if (data.status && typeof data.status === 'string') {
    updateInput.status = data.status as PrismaTaskStatus;
  }

  return updateInput;
};

export const createTask = async (
  req: Request,
  res: Response,
  next: NextFunction,
): Promise<void> => {
  try {
    const validation = await validateTaskAssignment(
      Number(req.body.staffEmployeeId),
      req.body.driverEmployeeId ? Number(req.body.driverEmployeeId) : null,
      req.body.vehicleId ? Number(req.body.vehicleId) : null,
      req.body.dateTime ? new Date(req.body.dateTime) : undefined,
      req.body.estimatedDuration ? Number(req.body.estimatedDuration) : undefined,
    );

    if (!validation.isValid) {
      next(
        new HttpError('Assignment validation failed', 400, validation.code, {
          error: validation.error,
        }),
      );
      return;
    }

    const taskData = processTaskData(req.body);
    const newTask = await taskModel.createTask(taskData);
    if (newTask) {
      try {
        const unifiedSocketService = getUnifiedWebSocketService();
        const payload: TaskCreatedPayload = mapPrismaTaskToPayload(newTask as any);
        unifiedSocketService.emitToRoom(CRUD_ROOM, CRUD_EVENTS.TASK_CREATED, payload);
      } catch (wsError) {
        logger.error('Failed to emit WebSocket event for task creation', { wsError });
      }
      res.status(201).json(newTask);
    } else {
      next(new HttpError('Could not create task.', 400, 'TASK_CREATION_FAILED'));
    }
  } catch (error: any) {
    if (error.message.includes('not found')) {
      next(new HttpError(error.message, 404, 'RESOURCE_NOT_FOUND'));
    } else {
      next(
        new HttpError('Error creating task', 500, 'TASK_CREATION_ERROR', {
          details: error.message,
        }),
      );
    }
  }
};

export const getAllTasks = async (
  req: Request,
  res: Response,
  next: NextFunction,
): Promise<void> => {
  try {
    const tasks = await taskModel.getAllTasks();
    res.status(200).json(tasks);
  } catch (error: any) {
    next(
      new HttpError('Error fetching tasks', 500, 'TASK_FETCH_ERROR', { details: error.message }),
    );
  }
};

export const getTaskById = async (
  req: Request,
  res: Response,
  next: NextFunction,
): Promise<void> => {
  try {
    const { id } = req.params;
    const task = await taskModel.getTaskById(id);
    if (task) {
      res.status(200).json(task);
    } else {
      next(new HttpError('Task not found', 404, 'TASK_NOT_FOUND'));
    }
  } catch (error: any) {
    next(new HttpError('Error fetching task', 500, 'TASK_FETCH_ERROR', { details: error.message }));
  }
};

export const updateTask = async (
  req: Request,
  res: Response,
  next: NextFunction,
): Promise<void> => {
  try {
    const { id } = req.params;
    const taskDataForProcessing = req.body;

    if (
      taskDataForProcessing.staffEmployeeId !== undefined ||
      taskDataForProcessing.driverEmployeeId !== undefined ||
      taskDataForProcessing.vehicleId !== undefined
    ) {
      const currentTask = await taskModel.getTaskById(id);
      if (!currentTask) {
        next(new HttpError('Task not found', 404, 'TASK_NOT_FOUND'));
        return;
      }
      const staffEmployeeIdForValidation =
        taskDataForProcessing.staffEmployeeId !== undefined
          ? Number(taskDataForProcessing.staffEmployeeId)
          : currentTask.staffEmployeeId;
      if (!staffEmployeeIdForValidation) {
        next(
          new HttpError('Assignment validation failed', 400, 'STAFF_EMPLOYEE_REQUIRED', {
            error:
              'staffEmployeeId is required for task assignment validation. Please provide a valid staff employee.',
          }),
        );
        return;
      }
      const driverEmployeeIdForValidation =
        taskDataForProcessing.driverEmployeeId !== undefined
          ? taskDataForProcessing.driverEmployeeId
            ? Number(taskDataForProcessing.driverEmployeeId)
            : null
          : currentTask.driverEmployeeId || null;
      const vehicleIdForValidation =
        taskDataForProcessing.vehicleId !== undefined
          ? taskDataForProcessing.vehicleId
            ? Number(taskDataForProcessing.vehicleId)
            : null
          : currentTask.vehicleId || null;
      const dateTimeForValidation = taskDataForProcessing.dateTime
        ? new Date(taskDataForProcessing.dateTime)
        : currentTask.dateTime;
      const estimatedDurationForValidation =
        taskDataForProcessing.estimatedDuration !== undefined
          ? Number(taskDataForProcessing.estimatedDuration)
          : currentTask.estimatedDuration;
      const validation = await validateTaskAssignment(
        staffEmployeeIdForValidation,
        driverEmployeeIdForValidation,
        vehicleIdForValidation,
        dateTimeForValidation,
        estimatedDurationForValidation,
        id,
      );
      if (!validation.isValid) {
        next(
          new HttpError('Assignment validation failed', 400, validation.code, {
            error: validation.error,
          }),
        );
        return;
      }
    }

    const taskUpdateData = processTaskUpdateData(taskDataForProcessing);
    const statusChangeReason = taskDataForProcessing.statusChangeReason;

    const updatedTask = await taskModel.updateTask(id, taskUpdateData, statusChangeReason);

    if (updatedTask) {
      try {
        const unifiedSocketService = getUnifiedWebSocketService();
        const payload: TaskUpdatedPayload = mapPrismaTaskToPayload(updatedTask as any);
        unifiedSocketService.emitToRoom(CRUD_ROOM, CRUD_EVENTS.TASK_UPDATED, payload);
      } catch (wsError) {
        logger.error('Failed to emit WebSocket event for task update', { wsError });
      }
      res.status(200).json(updatedTask);
    } else {
      const exists = await taskModel.getTaskById(id);
      if (!exists) {
        next(new HttpError('Task not found to update', 404, 'TASK_NOT_FOUND'));
      } else {
        next(new HttpError('Could not update task.', 400, 'TASK_UPDATE_FAILED'));
      }
    }
  } catch (error: any) {
    if (error.message.includes('not found')) {
      next(new HttpError(error.message, 404, 'RESOURCE_NOT_FOUND'));
    } else {
      next(
        new HttpError('Error updating task', 500, 'TASK_UPDATE_ERROR', { details: error.message }),
      );
    }
  }
};

export const deleteTask = async (
  req: Request,
  res: Response,
  next: NextFunction,
): Promise<void> => {
  try {
    const { id } = req.params;
    const deletedTask = await taskModel.deleteTask(id);
    if (deletedTask) {
      try {
        const unifiedSocketService = getUnifiedWebSocketService();
        const payload: TaskDeletedPayload = { id: deletedTask.id };
        unifiedSocketService.emitToRoom(CRUD_ROOM, CRUD_EVENTS.TASK_DELETED, payload);
      } catch (wsError) {
        logger.error('Failed to emit WebSocket event for task deletion', { wsError });
      }
      res.status(200).json({ message: 'Task deleted successfully', task: deletedTask });
    } else {
      next(new HttpError('Task not found or could not be deleted', 404, 'TASK_DELETE_FAILED'));
    }
  } catch (error: any) {
    next(
      new HttpError('Error deleting task', 500, 'TASK_DELETE_ERROR', { details: error.message }),
    );
  }
};
