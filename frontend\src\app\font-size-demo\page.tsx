'use client';

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Type } from 'lucide-react';
import Link from 'next/link';
import React from 'react';

import {
  FontSizeSelector,
  FontSizeSettings,
  FontSizeToggle,
} from '@/components/settings/FontSizeSettings';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { useUiPreferences } from '@/hooks/ui/useUiPreferences';

/**
 * Font Size Demo Page
 * Demonstrates font size functionality and provides testing interface
 */
export default function FontSizeDemoPage() {
  const { fontSize, getFontSizeClass, setFontSize } = useUiPreferences();

  const sampleContent = {
    heading: 'WorkHub Font Size Demonstration',
    list: [
      'Dashboard statistics and metrics',
      'Navigation menu items',
      'Form labels and input text',
      'Table data and headers',
      'Button text and descriptions',
    ],
    paragraph:
      'This page demonstrates how font size changes affect the entire application. The font size setting is applied globally and affects all text content throughout WorkHub.',
    quote: 'Good typography is invisible. Bad typography is everywhere.',
  };

  return (
    <div className="container mx-auto space-y-8 py-8">
      {/* Page Header */}
      <div className="flex items-center gap-4">
        <Link href="/">
          <Button className="flex items-center gap-2" size="sm" variant="ghost">
            <ArrowLeft className="size-4" />
            Back to Dashboard
          </Button>
        </Link>
        <div>
          <h1 className="flex items-center gap-2 text-3xl font-bold">
            <Type className="size-8" />
            Font Size Demo
          </h1>
          <p className="mt-1 text-muted-foreground">
            Test and preview font size changes in real-time
          </p>
        </div>
      </div>

      {/* Current Font Size Display */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Eye className="size-5" />
            Current Font Size
          </CardTitle>
          <CardDescription>
            The currently selected font size affects all content on this page
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-lg font-medium">Active Setting</p>
              <p className="text-sm text-muted-foreground">
                Applied globally across WorkHub
              </p>
            </div>
            <Badge className="px-4 py-2 text-lg capitalize" variant="secondary">
              {fontSize}
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Quick Controls */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Quick Toggle</CardTitle>
          </CardHeader>
          <CardContent>
            <FontSizeToggle />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Dropdown Selector</CardTitle>
          </CardHeader>
          <CardContent>
            <FontSizeSelector />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Button Controls</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex gap-1">
              {(['small', 'medium', 'large'] as const).map(size => (
                <Button
                  className="flex-1 capitalize"
                  key={size}
                  onClick={() => setFontSize(size)}
                  size="sm"
                  variant={fontSize === size ? 'default' : 'outline'}
                >
                  {size}
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Sample Content */}
      <Card>
        <CardHeader>
          <CardTitle>Sample Content Preview</CardTitle>
          <CardDescription>
            See how different content types are affected by font size changes
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Heading */}
          <div>
            <h3 className="mb-2 text-xl font-semibold">Heading Text</h3>
            <h2 className="text-2xl font-bold">{sampleContent.heading}</h2>
          </div>

          {/* Paragraph */}
          <div>
            <h3 className="mb-2 text-xl font-semibold">Paragraph Text</h3>
            <p className="leading-relaxed">{sampleContent.paragraph}</p>
          </div>

          {/* List */}
          <div>
            <h3 className="mb-2 text-xl font-semibold">List Items</h3>
            <ul className="list-inside list-disc space-y-1">
              {sampleContent.list.map((item, index) => (
                <li key={index}>{item}</li>
              ))}
            </ul>
          </div>

          {/* Quote */}
          <div>
            <h3 className="mb-2 text-xl font-semibold">Quote Text</h3>
            <blockquote className="border-l-4 border-primary pl-4 italic text-muted-foreground">
              {sampleContent.quote}
            </blockquote>
          </div>

          {/* Small Text */}
          <div>
            <h3 className="mb-2 text-xl font-semibold">Secondary Text</h3>
            <p className="text-muted-foreground">
              This is secondary text that appears in descriptions, captions, and
              helper text throughout the application.
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Full Settings Component */}
      <div>
        <h2 className="mb-4 text-2xl font-semibold">
          Complete Font Size Settings
        </h2>
        <FontSizeSettings />
      </div>

      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>How to Use Font Size Controls</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h4 className="mb-2 font-medium">1. Navigation Bar</h4>
            <p className="text-sm text-muted-foreground">
              Click the "A" icon in the navigation bar for quick font size
              controls.
            </p>
          </div>
          <div>
            <h4 className="mb-2 font-medium">2. Settings Page</h4>
            <p className="text-sm text-muted-foreground">
              Visit the Settings page for comprehensive font size management
              with preview.
            </p>
          </div>
          <div>
            <h4 className="mb-2 font-medium">3. This Demo Page</h4>
            <p className="text-sm text-muted-foreground">
              Use this page to test font size changes and see how they affect
              different content types.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
