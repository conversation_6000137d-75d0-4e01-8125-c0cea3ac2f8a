/**
 * Next.js 15 Utility Functions for Safe Parameter Handling
 *
 * These utilities provide safe access to params, searchParams, and pathname
 * with proper null checking and TypeScript support for Next.js 15.3.3+
 */

import { ReadonlyURLSearchParams } from 'next/navigation';

// Type definitions for Next.js 15 async params
export type AsyncParams = Promise<{
  [key: string]: string | string[] | undefined;
}>;
export type AsyncSearchParams = Promise<{
  [key: string]: string | string[] | undefined;
}>;

/**
 * Safely extracts a string parameter from async params
 * @param params - The async params promise from Next.js
 * @param key - The parameter key to extract
 * @param defaultValue - Default value if param is missing
 * @returns Promise<string>
 */
export async function getStringParam(
  params: AsyncParams,
  key: string,
  defaultValue: string = ''
): Promise<string> {
  try {
    const resolvedParams = await params;
    const value = resolvedParams?.[key];

    if (typeof value === 'string') {
      return value;
    }

    if (Array.isArray(value) && value.length > 0) {
      return value[0] || defaultValue;
    }

    return defaultValue;
  } catch (error) {
    console.warn(`Failed to extract param "${key}":`, error);
    return defaultValue;
  }
}

/**
 * Safely extracts a number parameter from async params
 * @param params - The async params promise from Next.js
 * @param key - The parameter key to extract
 * @param defaultValue - Default value if param is missing or invalid
 * @returns Promise<number | null>
 */
export async function getNumberParam(
  params: AsyncParams,
  key: string,
  defaultValue: number | null = null
): Promise<number | null> {
  try {
    const stringValue = await getStringParam(params, key);
    const numValue = Number(stringValue);

    return !isNaN(numValue) ? numValue : defaultValue;
  } catch (error) {
    console.warn(`Failed to extract number param "${key}":`, error);
    return defaultValue;
  }
}

/**
 * Safely extracts search parameters with default values
 * @param searchParams - The async searchParams promise from Next.js
 * @param key - The search parameter key
 * @param defaultValue - Default value if param is missing
 * @returns Promise<string>
 */
export async function getSearchParam(
  searchParams: AsyncSearchParams,
  key: string,
  defaultValue: string = ''
): Promise<string> {
  try {
    const resolvedSearchParams = await searchParams;
    const value = resolvedSearchParams?.[key];

    if (typeof value === 'string') {
      return value;
    }

    if (Array.isArray(value) && value.length > 0) {
      return value[0] || defaultValue;
    }

    return defaultValue;
  } catch (error) {
    console.warn(`Failed to extract search param "${key}":`, error);
    return defaultValue;
  }
}

/**
 * Safely extracts multiple search parameters at once
 * @param searchParams - The async searchParams promise from Next.js
 * @param keys - Object mapping keys to their default values
 * @returns Promise<Record<string, string>>
 */
export async function getMultipleSearchParams<T extends Record<string, string>>(
  searchParams: AsyncSearchParams,
  keys: T
): Promise<T> {
  try {
    const resolvedSearchParams = await searchParams;
    const result = {} as T;

    for (const [key, defaultValue] of Object.entries(keys)) {
      const value = resolvedSearchParams?.[key];

      if (typeof value === 'string') {
        (result as any)[key] = value;
      } else if (Array.isArray(value) && value.length > 0) {
        (result as any)[key] = value[0];
      } else {
        (result as any)[key] = defaultValue;
      }
    }

    return result;
  } catch (error) {
    console.warn('Failed to extract multiple search params:', error);
    return keys;
  }
}

/**
 * Safe pathname handler for client components
 * @param pathname - Pathname from usePathname() which can be null
 * @param defaultPath - Default path if pathname is null
 * @returns string
 */
export function safePathname(
  pathname: string | null,
  defaultPath: string = '/'
): string {
  return pathname ?? defaultPath;
}

/**
 * Safe URLSearchParams handler for client components
 * @param searchParams - ReadonlyURLSearchParams which can be null
 * @param key - The parameter key to get
 * @param defaultValue - Default value if param is missing
 * @returns string
 */
export function safeSearchParamsGet(
  searchParams: ReadonlyURLSearchParams | null,
  key: string,
  defaultValue: string = ''
): string {
  try {
    return searchParams?.get(key) ?? defaultValue;
  } catch (error) {
    console.warn(`Failed to get search param "${key}":`, error);
    return defaultValue;
  }
}

/**
 * Type guard to check if params are resolved
 * @param params - Params that might be a Promise or resolved object
 * @returns boolean
 */
export function areParamsResolved(
  params: any
): params is { [key: string]: string | string[] | undefined } {
  return params && typeof params === 'object' && !('then' in params);
}

/**
 * Type guard to check if searchParams are resolved
 * @param searchParams - SearchParams that might be a Promise or resolved object
 * @returns boolean
 */
export function areSearchParamsResolved(
  searchParams: any
): searchParams is { [key: string]: string | string[] | undefined } {
  return (
    searchParams &&
    typeof searchParams === 'object' &&
    !('then' in searchParams)
  );
}
