/**
 * @file Modern dashboard component with widgets and insights
 * @module components/dashboard/ModernDashboard
 */

'use client';

import {
  Activity,
  AlertTriangle,
  BarChart3,
  Bell,
  Calendar,
  Car,
  CheckCircle,
  Clock,
  DollarSign,
  Eye,
  MoreHorizontal,
  Plus,
  TrendingDown,
  TrendingUp,
  Users,
  Wrench,
} from 'lucide-react';
import Link from 'next/link';
import React, { useMemo } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useAuthContext } from '@/contexts/AuthContext';
import { useDelegations } from '@/lib/stores/queries/useDelegations';
import { useEmployees } from '@/lib/stores/queries/useEmployees';
import { useEnrichedServiceRecords } from '@/lib/stores/queries/useServiceRecords';
import { useTasks } from '@/lib/stores/queries/useTasks';
import { useVehicles } from '@/lib/stores/queries/useVehicles';
import { cn } from '@/lib/utils';

interface StatCardProps {
  title: string;
  value: string | number;
  description?: string;
  icon: React.ComponentType<React.SVGProps<SVGSVGElement>>;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  href?: string;
}

interface RecentActivityItem {
  id: string;
  title: string;
  description: string;
  timestamp: string;
  type: 'task' | 'vehicle' | 'maintenance' | 'delegation';
  priority?: 'high' | 'medium' | 'low';
}

interface QuickActionProps {
  title: string;
  description: string;
  icon: React.ComponentType<React.SVGProps<SVGSVGElement>>;
  href: string;
  variant?: 'default' | 'secondary' | 'outline';
}

const StatCard: React.FC<StatCardProps> = ({
  title,
  value,
  description,
  icon: Icon,
  trend,
  href,
}) => {
  const CardWrapper = href ? Link : 'div';

  return (
    <CardWrapper href={href || '#'} className={href ? 'block' : ''}>
      <Card
        className={cn(
          'transition-all duration-200',
          href && 'hover:shadow-md cursor-pointer'
        )}
      >
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">
                {title}
              </p>
              <p className="text-3xl font-bold">{value}</p>
              {description && (
                <p className="text-xs text-muted-foreground mt-1">
                  {description}
                </p>
              )}
            </div>
            <div className="flex flex-col items-end gap-2">
              <div className="rounded-md bg-primary/10 p-2">
                <Icon className="size-4 text-primary" />
              </div>
              {trend && (
                <div
                  className={cn(
                    'flex items-center text-xs',
                    trend.isPositive ? 'text-green-600' : 'text-red-600'
                  )}
                >
                  {trend.isPositive ? (
                    <TrendingUp className="size-3 mr-1" />
                  ) : (
                    <TrendingDown className="size-3 mr-1" />
                  )}
                  {Math.abs(trend.value)}%
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </CardWrapper>
  );
};

const QuickAction: React.FC<QuickActionProps> = ({
  title,
  description,
  icon: Icon,
  href,
  variant = 'outline',
}) => {
  return (
    <Card className="hover:shadow-md transition-all duration-200">
      <CardContent className="p-4">
        <div className="flex items-start gap-3">
          <div className="rounded-md bg-primary/10 p-2">
            <Icon className="size-4 text-primary" />
          </div>
          <div className="flex-1 min-w-0">
            <h4 className="text-sm font-medium">{title}</h4>
            <p className="text-xs text-muted-foreground mt-1">{description}</p>
            <Button asChild variant={variant} size="sm" className="mt-3 w-full">
              <Link href={href}>Get Started</Link>
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export const ModernDashboard: React.FC = () => {
  const { user, isInitialized, loading } = useAuthContext();

  // Only make API calls when user is authenticated and auth system is ready
  // MEMOIZED to prevent infinite re-renders caused by user object reference changes
  const isAuthReady = useMemo(() => {
    return isInitialized && !loading && !!user;
  }, [isInitialized, loading, user?.id]); // Use user.id instead of user object to prevent reference issues

  const { data: vehicles = [] } = useVehicles({ enabled: isAuthReady });
  const { data: delegations = [] } = useDelegations({ enabled: isAuthReady });
  const { data: tasks = [] } = useTasks({ enabled: isAuthReady });
  const { data: employees = [] } = useEmployees({ enabled: isAuthReady });
  const { data: serviceRecords = [] } = useEnrichedServiceRecords({
    enabled: isAuthReady,
  });

  // Show loading state while authentication is initializing
  if (!isInitialized || loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-sm text-muted-foreground">Initializing...</p>
        </div>
      </div>
    );
  }

  // Show login prompt if not authenticated
  if (!user) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <CardTitle>Welcome to WorkHub</CardTitle>
            <CardDescription>
              Please sign in to access your dashboard
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button asChild className="w-full">
              <Link href="/login">Sign In</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Calculate statistics
  const stats = {
    totalVehicles: vehicles.length,
    activeDelegations: delegations.filter(d => d.status === 'In_Progress')
      .length,
    pendingTasks: tasks.filter(
      t => t.status === 'Assigned' || t.status === 'In_Progress'
    ).length,
    maintenancesDue: serviceRecords.filter(r => {
      // This is a simplified check - you might want more sophisticated logic
      // Simplified maintenance due logic
      return Math.random() > 0.8; // Mock 20% of records as needing maintenance
    }).length,
    teamMembers: employees.length,
  };

  // Mock recent activity - you'd fetch this from your API
  const recentActivity: RecentActivityItem[] = [
    {
      id: '1',
      title: 'Vehicle VIN-123 maintenance completed',
      description: 'Oil change and tire rotation finished',
      timestamp: '2 hours ago',
      type: 'maintenance',
    },
    {
      id: '2',
      title: 'New task assigned: Fleet inspection',
      description: 'Quarterly safety inspection due next week',
      timestamp: '4 hours ago',
      type: 'task',
      priority: 'high',
    },
    {
      id: '3',
      title: 'Project Alpha milestone completed',
      description: 'Phase 2 deliverables submitted',
      timestamp: '1 day ago',
      type: 'delegation',
    },
  ];

  const quickActions: QuickActionProps[] = [
    {
      title: 'Add New Vehicle',
      description: 'Register a new asset to your fleet',
      icon: Plus,
      href: '/add-vehicle',
      variant: 'default',
    },
    {
      title: 'Schedule Maintenance',
      description: 'Plan upcoming service appointments',
      icon: Calendar,
      href: '/service-records',
    },
    {
      title: 'View Analytics',
      description: 'See detailed reports and insights',
      icon: BarChart3,
      href: '/reports',
    },
    {
      title: 'Assign Task',
      description: 'Create and delegate new tasks',
      icon: CheckCircle,
      href: '/tasks',
    },
  ];

  return (
    <div className="space-y-8">
      {/* Welcome Header */}
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">
          Welcome back
          {user?.user_metadata?.full_name
            ? `, ${user.user_metadata.full_name}`
            : ''}
          !
        </h1>
        <p className="text-muted-foreground">
          Here's what's happening with your operations today.
        </p>
      </div>

      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <StatCard
          title="Total Vehicles"
          value={stats.totalVehicles}
          description="Active fleet assets"
          icon={Car}
          href="/vehicles"
          trend={{ value: 5.2, isPositive: true }}
        />
        <StatCard
          title="Active Projects"
          value={stats.activeDelegations}
          description="In progress delegations"
          icon={Activity}
          href="/delegations"
        />
        <StatCard
          title="Pending Tasks"
          value={stats.pendingTasks}
          description="Awaiting completion"
          icon={Clock}
          href="/tasks"
          trend={{ value: 2.1, isPositive: false }}
        />
        <StatCard
          title="Maintenance Due"
          value={stats.maintenancesDue}
          description="Requires attention"
          icon={AlertTriangle}
          href="/service-history"
        />
      </div>

      <div className="grid gap-6 lg:grid-cols-3">
        {/* Recent Activity */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Recent Activity</CardTitle>
                <CardDescription>
                  Latest updates from your operations
                </CardDescription>
              </div>
              <Button variant="outline" size="sm" asChild>
                <Link href="/activity">
                  <Eye className="size-4 mr-2" />
                  View All
                </Link>
              </Button>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentActivity.map(item => (
                  <div
                    key={item.id}
                    className="flex items-start gap-3 p-3 rounded-lg bg-muted/30"
                  >
                    <div className="rounded-full bg-primary/10 p-1">
                      {item.type === 'maintenance' && (
                        <Wrench className="size-3 text-primary" />
                      )}
                      {item.type === 'task' && (
                        <CheckCircle className="size-3 text-primary" />
                      )}
                      {item.type === 'delegation' && (
                        <Users className="size-3 text-primary" />
                      )}
                      {item.type === 'vehicle' && (
                        <Car className="size-3 text-primary" />
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2">
                        <p className="text-sm font-medium">{item.title}</p>
                        {item.priority && (
                          <Badge
                            variant={
                              item.priority === 'high'
                                ? 'destructive'
                                : 'secondary'
                            }
                            className="text-xs"
                          >
                            {item.priority}
                          </Badge>
                        )}
                      </div>
                      <p className="text-xs text-muted-foreground">
                        {item.description}
                      </p>
                      <p className="text-xs text-muted-foreground mt-1">
                        {item.timestamp}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
              <CardDescription>Common tasks and shortcuts</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {quickActions.map((action, index) => (
                  <QuickAction key={index} {...action} />
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Performance Overview */}
      <Card>
        <CardHeader>
          <CardTitle>Fleet Performance</CardTitle>
          <CardDescription>
            Overview of fleet efficiency and maintenance status
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-6 md:grid-cols-3">
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Fleet Utilization</span>
                <span className="font-medium">87%</span>
              </div>
              <Progress value={87} className="h-2" />
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">
                  Maintenance Up-to-date
                </span>
                <span className="font-medium">92%</span>
              </div>
              <Progress value={92} className="h-2" />
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">
                  Task Completion Rate
                </span>
                <span className="font-medium">78%</span>
              </div>
              <Progress value={78} className="h-2" />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
