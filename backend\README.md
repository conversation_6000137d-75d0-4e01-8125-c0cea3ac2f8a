# Car Service Tracker - Backend

Backend service for the Car Service Tracking System application.

## Setup Instructions

### Environment Variables

The application requires certain environment variables to be set. Create a
`.env` file in the root of the backend directory with the following variables:

```
# Database Connection
DATABASE_URL="postgresql://postgres:password@localhost:5432/car_service_tracker"

# Frontend URL for CORS
FRONTEND_URL="http://localhost:3000"

# Node environment (development, production, test)
NODE_ENV="development"

# Server port
PORT=4000
```

Please adjust these values according to your local development environment. For
production, ensure these are set securely.

### Installation

1.  Install dependencies:
    ```bash
    npm install
    ```
2.  Ensure your PostgreSQL database server is running and accessible.

### Database Setup & Migrations with Prisma

This project uses Prisma for database management. It's crucial to manage your
database schema changes using migrations, especially when working in a team or
deploying to multiple environments.

**1. Initial Setup & Schema Synchronization:**

If you are setting up the project for the first time or if you have made changes
to `prisma/schema.prisma`:

- **Generate Prisma Client:** This command introspects your `schema.prisma` file
  and generates the TypeScript types for your database models.
  ```bash
  npx prisma generate
  ```
- **Create/Apply Migrations (Recommended):** This command creates a new SQL
  migration file based on the changes in your `schema.prisma` and applies it to
  your database. This is the preferred method for schema changes.

  ```bash
  npx prisma migrate dev --name your_migration_name
  ```

  Replace `your_migration_name` with a descriptive name for the changes (e.g.,
  `add_vehicle_color_field`, `initial_schema`). If this is the very first
  migration for the project, you might use a name like `init`.

- **Alternative for Prototyping (`prisma db push` - Use with Caution):** For
  rapid prototyping in early development, you _can_ use `prisma db push`. This
  command synchronizes your Prisma schema with the database schema directly,
  without creating migration files.
  ```bash
  npx prisma db push
  ```
  **Warning:** `prisma db push` is not suitable for production or collaborative
  environments as it doesn't track schema history. Switch to
  `prisma migrate dev` as soon as possible. If you've used `db push` and now
  want to use migrations, you might need to baseline your database. See Prisma
  documentation for "baselining an existing database".

**2. Seeding the Database (Optional):**

After your database schema is up to date, you can seed it with initial data
using:

```bash
npm run db:seed
```

The seed script (`prisma/seed.ts`) will populate your database with sample data.
You can customize this script as needed.

**3. Subsequent Schema Changes:**

Whenever you modify `prisma/schema.prisma`:

1.  Run `npx prisma migrate dev --name descriptive_migration_name` to create and
    apply a new migration.
2.  Run `npx prisma generate` to update your Prisma Client.

### Prisma Studio (Optional)

To view and manage your data directly in a GUI, you can use Prisma Studio:

```bash
npx prisma studio
```

### Running the Application

**Development mode:** Watches for file changes and restarts the server
automatically.

```bash
npm run dev
```

**Production build:**

```bash
npm run build
npm start
```

### API Documentation

API documentation (Swagger/OpenAPI) is available at `/api-docs` when the server
is running.

### Linting

To lint your code:

```bash
npm run lint
```
