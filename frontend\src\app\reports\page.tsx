// frontend/src/app/reports/page.tsx

import { Metadata } from 'next';
import { ReportingDashboard } from '@/components/features/reporting/dashboard/ReportingDashboard';

export const metadata: Metadata = {
  title: 'Reports - WorkHub',
  description: 'Comprehensive delegation analytics and reporting dashboard',
};

/**
 * Reports Page
 * 
 * Main entry point for the reporting system.
 * Uses the existing comprehensive reporting dashboard structure.
 */
export default function ReportsPage() {
  return <ReportingDashboard />;
}
