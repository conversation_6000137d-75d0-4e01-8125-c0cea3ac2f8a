export interface AuditLog {
  action: string;
  auth_user?: null | {
    email: string;
  };
  auth_user_id: string;
  details: string;
  id: string;
  timestamp: Date;
  userId: string;
  // Add other audit log properties as needed
}

export interface AuditLogApi {
  action: string;
  auth_user?: null | {
    email: string;
  };
  auth_user_id?: string;
  created_at?: string; // API might send created_at
  details: string;
  id: string;
  timestamp?: string; // API might send timestamp
  user_id?: string; // API might send user_id
  userId?: string; // API might send userId
  // Add other raw API properties as needed
}

export interface DataTransformer<T> {
  transform(data: any): T;
  transformCollection(data: any[]): T[];
}

export interface Filters {
  limit?: number;
  page?: number;
  search?: string;
  // Add other common filter properties as needed
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    /** Whether there is a next page */
    hasNext: boolean;
    /** Whether there is a previous page */
    hasPrevious: boolean;
    /** Number of items per page */
    limit: number;
    /** Current page number (1-indexed) */
    page: number;
    /** Total number of items */
    total: number;
    /** Total number of pages */
    totalPages: number;
  };
}

export interface User {
  created_at: string;
  email: string;
  email_confirmed_at: null | string;
  employee_id?: null | number;
  full_name?: string;
  id: string;
  isActive: boolean;
  last_sign_in_at?: null | string;
  phone?: null | string;
  phone_confirmed_at?: null | string;
  role: string;
  updated_at: string;
  users?: {
    email: string;
    email_confirmed_at: null | string;
  }[];
}

export interface UserFilters {
  isActive?: boolean;
  role?: string;
  search?: string;
}

export interface Vehicle {
  id?: string; // Make id optional for new vehicles
  make: string;
  model: string;
  year: number;
  // Add other vehicle properties as needed
}

export interface VehicleFormProps {
  initialData?: Partial<Vehicle>;
  onSubmit: (data: Vehicle) => Promise<void>;
}

// Placeholder for Zod schema, actual schema will be defined in the component or a schema file
import { z } from 'zod';

export const VehicleFormSchema = z.object({
  id: z.string().optional(), // Add optional id to schema
  make: z.string().min(1, 'Make is required'),
  model: z.string().min(1, 'Model is required'),
  year: z
    .number()
    .min(1900)
    .max(new Date().getFullYear() + 1),
});

// Admin service types
export interface AuditLogFilters extends Filters {
  action?: string;
  endDate?: Date | undefined; // Made optional
  startDate?: Date | undefined; // Made optional
  userId?: string;
}

export interface ErrorLogEntry {
  details?: Record<string, any>;
  id: string;
  level: LogLevel;
  message: string;
  metadata?: Record<string, any>;
  requestId?: string;
  source?: string;
  stack?: string;
  timestamp: string;
  userId?: string;
}

export interface HealthResponse {
  services: {
    api: { responseTime: number; status: string };
    cache: { responseTime: number; status: string };
    database: { responseTime: number; status: string };
  };
  status: 'degraded' | 'healthy' | 'unhealthy';
  timestamp: string;
  uptime: number;
}

export type LogLevel = 'DEBUG' | 'ERROR' | 'INFO' | 'TRACE' | 'WARNING';

export interface PerformanceMetrics {
  cpu: { cores: number; usage: number };
  errors: { rate: number; total: number };
  memory: { percentage: number; total: number; used: number };
  requests: { averageResponseTime: number; perSecond: number; total: number };
  timestamp: string;
}

export interface UserFilters extends Filters {
  // Add user-specific filter properties if any
}

export * from '../lib/types/apiContracts';
// Export all types from domain and apiContracts
export * from '../lib/types/domain';
