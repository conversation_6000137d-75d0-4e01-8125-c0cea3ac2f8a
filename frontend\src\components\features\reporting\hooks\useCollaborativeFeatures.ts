// frontend/src/components/features/reporting/hooks/useCollaborativeFeatures.ts

import { useCallback, useEffect, useState } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { getWebSocketManager } from '@/lib/services/WebSocketManager';
import { useAuthContext } from '@/contexts/AuthContext';

export interface DashboardComment {
  id: string;
  dashboardId: string;
  userId: string;
  userName: string;
  content: string;
  position?: { x: number; y: number };
  createdAt: string;
  updatedAt: string;
}

export interface UserPresence {
  userId: string;
  userName: string;
  avatar?: string;
  lastSeen: string;
  isActive: boolean;
}

export interface DashboardShare {
  id: string;
  dashboardId: string;
  sharedBy: string;
  sharedWith: string[];
  permissions: 'view' | 'edit' | 'admin';
  expiresAt?: string;
  createdAt: string;
}

/**
 * Collaborative Features Hook
 *
 * ENHANCED: Integrates with existing WebSocket infrastructure from WebSocketManager
 * Uses existing React Query for cache management
 * No duplication of real-time connection logic
 * Follows established patterns from existing hooks
 */
export const useCollaborativeFeatures = (dashboardId: string) => {
  const queryClient = useQueryClient();
  const wsManager = getWebSocketManager();
  const { user } = useAuthContext();

  const [comments, setComments] = useState<DashboardComment[]>([]);
  const [activeUsers, setActiveUsers] = useState<UserPresence[]>([]);
  const [shares, setShares] = useState<DashboardShare[]>([]);

  // Initialize with mock data for development when WebSocket is not available
  useEffect(() => {
    if (!wsManager.isConnected() && process.env.NODE_ENV === 'development') {
      // Add some mock comments for development
      setComments([
        {
          id: 'mock-1',
          dashboardId,
          userId: 'user-1',
          userName: 'John Doe',
          content:
            'This dashboard looks great! The metrics are very insightful.',
          createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
          updatedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        },
        {
          id: 'mock-2',
          dashboardId,
          userId: 'user-2',
          userName: 'Jane Smith',
          content:
            'Could we add a filter for the date range on the performance chart?',
          createdAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(), // 30 minutes ago
          updatedAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
        },
      ]);

      // Add mock active users
      setActiveUsers([
        {
          userId: 'user-3',
          userName: 'Alice Johnson',
          lastSeen: new Date().toISOString(),
          isActive: true,
        },
        {
          userId: 'user-4',
          userName: 'Bob Wilson',
          lastSeen: new Date().toISOString(),
          isActive: true,
        },
      ]);
    }
  }, [dashboardId, wsManager]);

  // Get current user info for collaborative features
  const currentUser = {
    userId: user?.id || 'anonymous',
    userName: user?.email || user?.user_metadata?.name || 'Anonymous User',
    avatar: user?.user_metadata?.avatar_url,
  };

  // Join dashboard room for real-time collaboration
  useEffect(() => {
    if (!dashboardId || !wsManager.isConnected() || !user) return;

    // Join dashboard-specific room with user context
    wsManager.joinRoom(`dashboard:${dashboardId}`);

    // Announce user presence
    wsManager.emit('notifications', 'dashboard:user_joined', {
      dashboardId,
      user: currentUser,
    });

    // Subscribe to collaborative events using existing WebSocket manager
    const unsubscribeCommentAdded = wsManager.subscribe(
      'notifications',
      'dashboard:comment_added',
      (comment: DashboardComment) => {
        setComments(prev => [...prev, comment]);
        // Invalidate comments query to refresh data
        queryClient.invalidateQueries({
          queryKey: ['dashboard', dashboardId, 'comments'],
        });
      }
    );

    const unsubscribeCommentUpdated = wsManager.subscribe(
      'notifications',
      'dashboard:comment_updated',
      (comment: DashboardComment) => {
        setComments(prev => prev.map(c => (c.id === comment.id ? comment : c)));
      }
    );

    const unsubscribeCommentDeleted = wsManager.subscribe(
      'notifications',
      'dashboard:comment_deleted',
      (commentId: string) => {
        setComments(prev => prev.filter(c => c.id !== commentId));
      }
    );

    const unsubscribeUserJoined = wsManager.subscribe(
      'notifications',
      'dashboard:user_joined',
      (user: UserPresence) => {
        setActiveUsers(prev => [
          ...prev.filter(u => u.userId !== user.userId),
          user,
        ]);
      }
    );

    const unsubscribeUserLeft = wsManager.subscribe(
      'notifications',
      'dashboard:user_left',
      (userId: string) => {
        setActiveUsers(prev => prev.filter(u => u.userId !== userId));
      }
    );

    const unsubscribeShareUpdated = wsManager.subscribe(
      'notifications',
      'dashboard:share_updated',
      (share: DashboardShare) => {
        setShares(prev => [...prev.filter(s => s.id !== share.id), share]);
      }
    );

    // Cleanup on unmount
    return () => {
      // Announce user leaving
      wsManager.emit('notifications', 'dashboard:user_left', {
        dashboardId,
        userId: currentUser.userId,
      });

      wsManager.leaveRoom(`dashboard:${dashboardId}`);
      unsubscribeCommentAdded();
      unsubscribeCommentUpdated();
      unsubscribeCommentDeleted();
      unsubscribeUserJoined();
      unsubscribeUserLeft();
      unsubscribeShareUpdated();
    };
  }, [dashboardId, wsManager, queryClient, user, currentUser]);

  // Comment management
  const addComment = useCallback(
    (content: string, position?: { x: number; y: number }) => {
      if (!wsManager.isConnected()) {
        console.warn('Cannot add comment - WebSocket not connected');
        return;
      }

      if (!user) {
        console.warn('Cannot add comment - User not authenticated');
        return;
      }

      wsManager.emit('notifications', 'dashboard:add_comment', {
        dashboardId,
        content,
        position,
        user: currentUser,
      });
    },
    [wsManager, dashboardId, user, currentUser]
  );

  const updateComment = useCallback(
    (commentId: string, content: string) => {
      if (!wsManager.isConnected()) return;

      wsManager.emit('notifications', 'dashboard:update_comment', {
        commentId,
        content,
      });
    },
    [wsManager]
  );

  const deleteComment = useCallback(
    (commentId: string) => {
      if (!wsManager.isConnected()) return;

      wsManager.emit('notifications', 'dashboard:delete_comment', {
        commentId,
      });
    },
    [wsManager]
  );

  // Sharing management
  const shareDashboard = useCallback(
    (
      sharedWith: string[],
      permissions: 'view' | 'edit' | 'admin',
      expiresAt?: string
    ) => {
      if (!wsManager.isConnected()) return;

      wsManager.emit('notifications', 'dashboard:share', {
        dashboardId,
        sharedWith,
        permissions,
        expiresAt,
      });
    },
    [wsManager, dashboardId]
  );

  const updateSharePermissions = useCallback(
    (shareId: string, permissions: 'view' | 'edit' | 'admin') => {
      if (!wsManager.isConnected()) return;

      wsManager.emit('notifications', 'dashboard:update_share', {
        shareId,
        permissions,
      });
    },
    [wsManager]
  );

  const revokeShare = useCallback(
    (shareId: string) => {
      if (!wsManager.isConnected()) return;

      wsManager.emit('notifications', 'dashboard:revoke_share', { shareId });
    },
    [wsManager]
  );

  // Presence management
  const updatePresence = useCallback(() => {
    if (!wsManager.isConnected()) return;

    wsManager.emit('notifications', 'dashboard:update_presence', {
      dashboardId,
    });
  }, [wsManager, dashboardId]);

  return {
    // State
    comments,
    activeUsers,
    shares,
    isConnected: wsManager.isConnected(),

    // Comment actions
    addComment,
    updateComment,
    deleteComment,

    // Sharing actions
    shareDashboard,
    updateSharePermissions,
    revokeShare,

    // Presence actions
    updatePresence,
  };
};
