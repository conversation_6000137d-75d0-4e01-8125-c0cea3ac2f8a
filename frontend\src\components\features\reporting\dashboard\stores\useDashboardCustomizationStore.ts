// frontend/src/components/features/reporting/dashboard/stores/useDashboardCustomizationStore.ts

import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export interface WidgetConfig {
  id: string;
  type: string;
  title: string;
  position: { x: number; y: number };
  size: { width: number; height: number };
  settings: Record<string, any>;
  visible: boolean;
}

export interface DashboardLayout {
  id: string;
  name: string;
  widgets: WidgetConfig[];
  createdAt: string;
  updatedAt: string;
}

interface DashboardCustomizationState {
  currentLayout: DashboardLayout | null;
  savedLayouts: DashboardLayout[];
  isEditMode: boolean;
  selectedWidget: string | null;
}

interface DashboardCustomizationActions {
  // Layout management
  setCurrentLayout: (layout: DashboardLayout) => void;
  saveCurrentLayout: (name: string) => void;
  loadLayout: (layoutId: string) => void;
  deleteLayout: (layoutId: string) => void;
  
  // Widget management
  addWidget: (widget: WidgetConfig) => void;
  updateWidget: (widgetId: string, updates: Partial<WidgetConfig>) => void;
  removeWidget: (widgetId: string) => void;
  moveWidget: (widgetId: string, position: { x: number; y: number }) => void;
  resizeWidget: (widgetId: string, size: { width: number; height: number }) => void;
  
  // UI state
  setEditMode: (enabled: boolean) => void;
  setSelectedWidget: (widgetId: string | null) => void;
}

/**
 * Dashboard Customization Store
 * 
 * ENHANCED: Extends existing Zustand pattern for dashboard layout management
 * Integrates with existing architecture without creating new global state system
 * Follows the same patterns as useReportingFiltersStore
 */
export const useDashboardCustomizationStore = create<
  DashboardCustomizationState & DashboardCustomizationActions
>()(
  persist(
    (set, get) => ({
      // Initial state
      currentLayout: null,
      savedLayouts: [],
      isEditMode: false,
      selectedWidget: null,

      // Layout management actions
      setCurrentLayout: (layout) => {
        set({ currentLayout: layout });
      },

      saveCurrentLayout: (name) => {
        const { currentLayout, savedLayouts } = get();
        if (!currentLayout) return;

        const newLayout: DashboardLayout = {
          ...currentLayout,
          id: Date.now().toString(),
          name,
          updatedAt: new Date().toISOString(),
        };

        set({
          savedLayouts: [...savedLayouts, newLayout],
          currentLayout: newLayout,
        });
      },

      loadLayout: (layoutId) => {
        const { savedLayouts } = get();
        const layout = savedLayouts.find(l => l.id === layoutId);
        if (layout) {
          set({ currentLayout: layout });
        }
      },

      deleteLayout: (layoutId) => {
        const { savedLayouts, currentLayout } = get();
        const updatedLayouts = savedLayouts.filter(l => l.id !== layoutId);
        
        set({
          savedLayouts: updatedLayouts,
          currentLayout: currentLayout?.id === layoutId ? null : currentLayout,
        });
      },

      // Widget management actions
      addWidget: (widget) => {
        const { currentLayout } = get();
        if (!currentLayout) return;

        const updatedLayout = {
          ...currentLayout,
          widgets: [...currentLayout.widgets, widget],
          updatedAt: new Date().toISOString(),
        };

        set({ currentLayout: updatedLayout });
      },

      updateWidget: (widgetId, updates) => {
        const { currentLayout } = get();
        if (!currentLayout) return;

        const updatedWidgets = currentLayout.widgets.map(widget =>
          widget.id === widgetId ? { ...widget, ...updates } : widget
        );

        const updatedLayout = {
          ...currentLayout,
          widgets: updatedWidgets,
          updatedAt: new Date().toISOString(),
        };

        set({ currentLayout: updatedLayout });
      },

      removeWidget: (widgetId) => {
        const { currentLayout } = get();
        if (!currentLayout) return;

        const updatedWidgets = currentLayout.widgets.filter(w => w.id !== widgetId);
        const updatedLayout = {
          ...currentLayout,
          widgets: updatedWidgets,
          updatedAt: new Date().toISOString(),
        };

        set({ currentLayout: updatedLayout });
      },

      moveWidget: (widgetId, position) => {
        get().updateWidget(widgetId, { position });
      },

      resizeWidget: (widgetId, size) => {
        get().updateWidget(widgetId, { size });
      },

      // UI state actions
      setEditMode: (enabled) => {
        set({ isEditMode: enabled, selectedWidget: enabled ? null : get().selectedWidget });
      },

      setSelectedWidget: (widgetId) => {
        set({ selectedWidget: widgetId });
      },
    }),
    {
      name: 'dashboard-customization',
      partialize: (state) => ({
        savedLayouts: state.savedLayouts,
        currentLayout: state.currentLayout,
      }),
    }
  )
);

// ENHANCED: Convenience hooks following existing patterns
export const useDashboardLayout = () => 
  useDashboardCustomizationStore(state => state.currentLayout);

export const useDashboardEditMode = () => 
  useDashboardCustomizationStore(state => ({
    isEditMode: state.isEditMode,
    setEditMode: state.setEditMode,
    selectedWidget: state.selectedWidget,
    setSelectedWidget: state.setSelectedWidget,
  }));

export const useDashboardLayoutActions = () => 
  useDashboardCustomizationStore(state => ({
    saveCurrentLayout: state.saveCurrentLayout,
    loadLayout: state.loadLayout,
    deleteLayout: state.deleteLayout,
    addWidget: state.addWidget,
    updateWidget: state.updateWidget,
    removeWidget: state.removeWidget,
    moveWidget: state.moveWidget,
    resizeWidget: state.resizeWidget,
  }));
