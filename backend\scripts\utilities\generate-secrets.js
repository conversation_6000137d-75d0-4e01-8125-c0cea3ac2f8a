#!/usr/bin/env node

/**
 * PHASE 1 SECURITY HARDENING: Secrets Generation Script
 *
 * This script generates cryptographically secure secrets for the WorkHub application.
 * It ensures all secrets meet security requirements and provides guidance for setup.
 */

import crypto from 'crypto';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Required secrets with their minimum lengths
const REQUIRED_SECRETS = {
  JWT_SECRET: 32,
  API_SECRET: 32,
  SESSION_SECRET: 32,
  ENCRYPTION_KEY: 32,
};

// Optional but recommended secrets
const RECOMMENDED_SECRETS = {
  RATE_LIMIT_SECRET: 32,
  WEBHOOK_SECRET: 32,
};

/**
 * Generates a cryptographically secure secret
 */
function generateSecureSecret(length = 32) {
  return crypto.randomBytes(length).toString('base64');
}

/**
 * Generates all required and recommended secrets
 */
function generateAllSecrets() {
  const secrets = {};

  // Generate required secrets
  for (const [name, length] of Object.entries(REQUIRED_SECRETS)) {
    secrets[name] = generateSecureSecret(length);
  }

  // Generate recommended secrets
  for (const [name, length] of Object.entries(RECOMMENDED_SECRETS)) {
    secrets[name] = generateSecureSecret(length);
  }

  return secrets;
}

/**
 * Displays generated secrets in a formatted way
 */
function displaySecrets(secrets) {
  console.log('');
  console.log('🔐 PHASE 1 SECURITY HARDENING: Generated Secrets');
  console.log('='.repeat(60));
  console.log('');
  console.log('📋 REQUIRED SECRETS (add these to your .env file):');
  console.log('-'.repeat(60));

  for (const [name, value] of Object.entries(secrets)) {
    if (REQUIRED_SECRETS[name]) {
      console.log(`${name}=${value}`);
    }
  }

  console.log('');
  console.log('💡 RECOMMENDED SECRETS (optional but advised):');
  console.log('-'.repeat(60));

  for (const [name, value] of Object.entries(secrets)) {
    if (RECOMMENDED_SECRETS[name]) {
      console.log(`${name}=${value}`);
    }
  }

  console.log('');
  console.log('='.repeat(60));
  console.log('');
}

/**
 * Provides setup instructions
 */
function displayInstructions() {
  console.log('🚀 SETUP INSTRUCTIONS:');
  console.log('');
  console.log('1. Copy the REQUIRED secrets above to your .env file');
  console.log('2. Replace any existing weak secrets with the generated ones');
  console.log('3. Add RECOMMENDED secrets for enhanced security');
  console.log('4. Restart your application to validate the new secrets');
  console.log('5. Test the application to ensure everything works');
  console.log('');
  console.log('⚠️  SECURITY NOTES:');
  console.log('');
  console.log('• Never commit real secrets to version control');
  console.log('• Use different secrets for different environments');
  console.log('• Rotate secrets regularly in production');
  console.log('• Store production secrets securely (e.g., environment variables)');
  console.log('• Keep a secure backup of your secrets');
  console.log('');
  console.log('🔍 VERIFICATION:');
  console.log('');
  console.log('Run the application to verify secrets validation:');
  console.log('  npm start');
  console.log('');
  console.log('You should see: "✅ All required secrets validated successfully"');
  console.log('');
}

/**
 * Creates a secure .env template with generated secrets
 */
function createSecureEnvTemplate(secrets) {
  const envPath = path.join(__dirname, '..', '.env.secure-template');

  let content = `# =============================================================================
# PHASE 1 SECURITY HARDENING: Generated Secure Environment Configuration
# =============================================================================
#
# 🔐 Generated on: ${new Date().toISOString()}
# 🚨 SECURITY WARNING: These are real secrets - handle with care!
# 📋 Copy required values to your .env file
#
# =============================================================================

# =============================================================================
# SECURITY SECRETS (GENERATED)
# =============================================================================

`;

  // Add required secrets
  for (const [name, value] of Object.entries(secrets)) {
    if (REQUIRED_SECRETS[name]) {
      content += `# ${name} - REQUIRED (${REQUIRED_SECRETS[name]}+ chars)\n`;
      content += `${name}=${value}\n\n`;
    }
  }

  content += `# =============================================================================
# RECOMMENDED SECRETS (OPTIONAL)
# =============================================================================

`;

  // Add recommended secrets
  for (const [name, value] of Object.entries(secrets)) {
    if (RECOMMENDED_SECRETS[name]) {
      content += `# ${name} - RECOMMENDED (${RECOMMENDED_SECRETS[name]}+ chars)\n`;
      content += `${name}=${value}\n\n`;
    }
  }

  content += `# =============================================================================
# CONFIGURATION TEMPLATE
# =============================================================================
# Copy your existing configuration values below:

# Database Configuration
USE_SUPABASE=true
DATABASE_URL=REPLACE_WITH_YOUR_DATABASE_URL

# Supabase Configuration
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_ANON_KEY=REPLACE_WITH_YOUR_SUPABASE_ANON_KEY
SUPABASE_SERVICE_ROLE_KEY=REPLACE_WITH_YOUR_SUPABASE_SERVICE_ROLE_KEY

# Application Configuration
PORT=3001
NODE_ENV=development
LOG_LEVEL=info

# CORS Configuration
FRONTEND_URL=http://localhost:3000,http://localhost:9002

# Rate Limiting Configuration
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# =============================================================================
# SECURITY NOTES
# =============================================================================
# 1. Copy required secrets to your .env file
# 2. Replace placeholder values with your actual configuration
# 3. Never commit this file to version control
# 4. Validate secrets by running: npm start
# =============================================================================
`;

  try {
    fs.writeFileSync(envPath, content);
    console.log(`📄 Secure template created: ${envPath}`);
    console.log('   (This file contains real secrets - handle with care!)');
    console.log('');
  } catch (error) {
    console.error('❌ Failed to create secure template:', error.message);
  }
}

/**
 * Main function
 */
function main() {
  console.log('🔐 PHASE 1 SECURITY HARDENING: Secrets Generator');
  console.log('='.repeat(60));
  console.log('');
  console.log('Generating cryptographically secure secrets...');

  const secrets = generateAllSecrets();

  displaySecrets(secrets);
  displayInstructions();
  createSecureEnvTemplate(secrets);

  console.log('✅ Secrets generation completed successfully!');
  console.log('');
}

// Run the script
if (import.meta.url.endsWith(process.argv[1]) || import.meta.url.includes('generate-secrets.js')) {
  main();
}

export { generateSecureSecret, generateAllSecrets };
