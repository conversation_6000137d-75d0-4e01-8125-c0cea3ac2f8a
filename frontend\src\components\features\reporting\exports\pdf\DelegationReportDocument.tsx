import {
  Document,
  Font,
  Page,
  StyleSheet,
  Text,
  View,
} from '@react-pdf/renderer';
// frontend/src/components/features/reporting/exports/pdf/DelegationReportDocument.tsx
import React from 'react';

import type { ReportDocumentProps } from '../../data/types/export';

// Register a font if needed (e.g., for emojis or specific styling)
// Font.register({ family: 'Roboto', src: '/fonts/Roboto-Regular.ttf' });

const styles = StyleSheet.create({
  header: {
    color: '#333',
    fontSize: 24,
    marginBottom: 10,
    textAlign: 'center',
  },
  page: {
    backgroundColor: '#E4E4E4',
    flexDirection: 'column',
    padding: 30,
  },
  section: {
    flexGrow: 1,
    margin: 10,
    padding: 10,
  },
  subheader: {
    color: '#555',
    fontSize: 16,
    marginBottom: 5,
  },
  text: {
    fontSize: 12,
    marginBottom: 3,
  },
  // Add more styles for tables, charts, etc.
});

/**
 * @component DelegationReportDocument
 * @description React-PDF component for generating the Delegation Analytics Report.
 * Adheres to SRP by focusing solely on the PDF document structure and rendering.
 * @param {ReportDocumentProps} props - The data and metadata for the report.
 */
export const DelegationReportDocument: React.FC<ReportDocumentProps> = ({
  data,
  filters,
  reportDate,
  reportTitle,
}) => {
  // Defensive programming for robust data handling
  const { metadata, reportData, summary, totalCount } = React.useMemo(() => {
    // Handle case where data might be null/undefined
    if (!data) {
      return {
        metadata: {},
        reportData: {},
        summary: { message: 'No data available' },
        totalCount: 0,
      };
    }

    const rawData = data?.data ?? data ?? {};
    const meta = data?.metadata ?? {};

    // Ensure rawData is an object before destructuring
    const safeData =
      typeof rawData === 'object' && rawData !== null && !Array.isArray(rawData)
        ? rawData
        : {};

    return {
      metadata: meta,
      reportData: safeData,
      summary: safeData.summary ?? { message: 'No summary available' },
      totalCount:
        safeData.totalCount ?? (Array.isArray(safeData) ? safeData.length : 0),
    };
  }, [data]);

  // Ensure filters is always an object with proper typing
  const safeFilters = filters ?? {};
  const safeDateRange = (safeFilters as any)?.dateRange ?? {};
  const safeStatus = (safeFilters as any)?.status;
  const safeReportTitle = reportTitle ?? 'Delegation Report';
  const safeReportDate = reportDate ?? new Date().toLocaleString();

  return (
    <Document>
      <Page size="A4" style={styles.page}>
        <View style={styles.section}>
          <Text style={styles.header}>{safeReportTitle}</Text>
          <Text style={styles.text}>Report Generated: {safeReportDate}</Text>
          <Text style={styles.text}>Total Delegations: {totalCount}</Text>

          {/* Summary Section */}
          {summary && Object.keys(summary).length > 0 && (
            <View style={{ marginTop: 15 }}>
              <Text style={styles.subheader}>Summary:</Text>
              {Object.entries(summary).map(([key, value]) => (
                <Text key={key} style={styles.text}>
                  {key
                    .replaceAll(/([A-Z])/g, ' $1')
                    .replace(/^./, str => str.toUpperCase())}
                  : {String(value ?? 'N/A')}
                </Text>
              ))}
            </View>
          )}

          {/* Filters Section */}
          {safeFilters && Object.keys(safeFilters).length > 0 && (
            <View style={{ marginTop: 20 }}>
              <Text style={styles.subheader}>Filters Applied:</Text>
              <Text style={styles.text}>
                Date Range:{' '}
                {safeDateRange.from?.toLocaleDateString?.() ?? 'N/A'} -{' '}
                {safeDateRange.to?.toLocaleDateString?.() ?? 'N/A'}
                <Text style={styles.text}>
                  Status: {safeStatus?.join?.(', ') ?? 'All'}
                </Text>
              </Text>
            </View>
          )}

          {/* Metadata Section */}
          {metadata.id && (
            <View style={{ marginTop: 15 }}>
              <Text style={styles.subheader}>Report Details:</Text>
              <Text style={styles.text}>Report ID: {metadata.id}</Text>
              <Text style={styles.text}>
                Generated At:{' '}
                {metadata.generatedAt
                  ? new Date(metadata.generatedAt).toLocaleString()
                  : 'N/A'}
              </Text>
            </View>
          )}
        </View>
      </Page>
    </Document>
  );
};
