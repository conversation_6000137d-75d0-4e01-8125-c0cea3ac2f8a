/**
 * @file Unit tests for Reliability Zustand store
 * @module stores/zustand/__tests__/reliabilityStore
 */

import { act, renderHook } from '@testing-library/react';

import {
  useReliabilityStore,
  type DashboardLayout,
  type WidgetId,
  type DataType,
  type ConnectionStatus,
  type DashboardTab,
  type TimeRange,
  type AlertFilters,
} from '../reliabilityStore';

// Mock localStorage for persistence testing
const localStorageMock = (() => {
  let store: Record<string, string> = {};

  return {
    getItem: (key: string) => store[key] || null,
    setItem: (key: string, value: string) => {
      store[key] = value.toString();
    },
    removeItem: (key: string) => {
      delete store[key];
    },
    clear: () => {
      store = {};
    },
  };
})();

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

describe('useReliabilityStore', () => {
  beforeEach(() => {
    // Clear localStorage before each test
    localStorageMock.clear();
    // Reset store state
    useReliabilityStore.getState().resetPreferencesToDefaults();
  });

  describe('Initial State', () => {
    it('should have correct default preferences', () => {
      const { result } = renderHook(() => useReliabilityStore());

      expect(result.current.preferences.refreshIntervals.health).toBe(15000);
      expect(result.current.preferences.refreshIntervals.alerts).toBe(10000);
      expect(result.current.preferences.dashboardLayout.layout).toBe('grid');
      expect(result.current.preferences.dashboardLayout.gridColumns).toBe(3);
      expect(result.current.preferences.notifications.soundEnabled).toBe(true);
      expect(result.current.preferences.defaultTimeRange).toBe('24h');
    });

    it('should have correct default UI state', () => {
      const { result } = renderHook(() => useReliabilityStore());

      expect(result.current.ui.activeTab).toBe('overview');
      expect(result.current.ui.selectedAlerts.size).toBe(0);
      expect(result.current.ui.isFilterPanelOpen).toBe(false);
      expect(result.current.ui.filters.severities.size).toBe(4);
      expect(result.current.ui.filters.statuses.size).toBe(2);
    });

    it('should have correct default monitoring state', () => {
      const { result } = renderHook(() => useReliabilityStore());

      expect(result.current.monitoring.isEnabled).toBe(true);
      expect(result.current.monitoring.connectionStatus).toBe('disconnected');
      expect(result.current.monitoring.pausedDataTypes.size).toBe(0);
    });
  });

  describe('Preference Actions', () => {
    it('should update refresh intervals', () => {
      const { result } = renderHook(() => useReliabilityStore());

      act(() => {
        result.current.setRefreshInterval('health', 30000);
      });

      expect(result.current.preferences.refreshIntervals.health).toBe(30000);
      expect(result.current.preferences.refreshIntervals.alerts).toBe(10000); // Should remain unchanged
    });

    it('should update dashboard layout', () => {
      const { result } = renderHook(() => useReliabilityStore());

      act(() => {
        result.current.setDashboardLayout('list');
      });

      expect(result.current.preferences.dashboardLayout.layout).toBe('list');
    });

    it('should update grid columns with clamping', () => {
      const { result } = renderHook(() => useReliabilityStore());

      // Test normal value
      act(() => {
        result.current.setGridColumns(4);
      });
      expect(result.current.preferences.dashboardLayout.gridColumns).toBe(4);

      // Test clamping to minimum
      act(() => {
        result.current.setGridColumns(0);
      });
      expect(result.current.preferences.dashboardLayout.gridColumns).toBe(1);

      // Test clamping to maximum
      act(() => {
        result.current.setGridColumns(10);
      });
      expect(result.current.preferences.dashboardLayout.gridColumns).toBe(6);
    });

    it('should toggle widget visibility', () => {
      const { result } = renderHook(() => useReliabilityStore());

      const initialVisibleWidgets = new Set(result.current.preferences.dashboardLayout.visibleWidgets);
      const testWidget: WidgetId = 'system-health';

      // Widget should be visible initially
      expect(initialVisibleWidgets.has(testWidget)).toBe(true);

      // Toggle off
      act(() => {
        result.current.toggleWidget(testWidget);
      });
      expect(result.current.preferences.dashboardLayout.visibleWidgets.has(testWidget)).toBe(false);

      // Toggle back on
      act(() => {
        result.current.toggleWidget(testWidget);
      });
      expect(result.current.preferences.dashboardLayout.visibleWidgets.has(testWidget)).toBe(true);
    });

    it('should set widget expanded state', () => {
      const { result } = renderHook(() => useReliabilityStore());

      const testWidget: WidgetId = 'performance-metrics';

      // Initially not expanded
      expect(result.current.preferences.dashboardLayout.expandedWidgets.has(testWidget)).toBe(false);

      // Expand widget
      act(() => {
        result.current.setWidgetExpanded(testWidget, true);
      });
      expect(result.current.preferences.dashboardLayout.expandedWidgets.has(testWidget)).toBe(true);

      // Collapse widget
      act(() => {
        result.current.setWidgetExpanded(testWidget, false);
      });
      expect(result.current.preferences.dashboardLayout.expandedWidgets.has(testWidget)).toBe(false);
    });

    it('should reorder widgets', () => {
      const { result } = renderHook(() => useReliabilityStore());

      const newOrder: WidgetId[] = ['active-alerts', 'system-health', 'circuit-breakers'];

      act(() => {
        result.current.reorderWidgets(newOrder);
      });

      expect(result.current.preferences.dashboardLayout.widgetOrder).toEqual(newOrder);
    });

    it('should update notification preferences', () => {
      const { result } = renderHook(() => useReliabilityStore());

      act(() => {
        result.current.setNotificationPreferences({
          soundEnabled: false,
          minimumSeverity: 'high',
          autoDismissTimeout: 10000,
        });
      });

      expect(result.current.preferences.notifications.soundEnabled).toBe(false);
      expect(result.current.preferences.notifications.minimumSeverity).toBe('high');
      expect(result.current.preferences.notifications.autoDismissTimeout).toBe(10000);
      expect(result.current.preferences.notifications.desktopEnabled).toBe(true); // Should remain unchanged
    });

    it('should reset preferences to defaults', () => {
      const { result } = renderHook(() => useReliabilityStore());

      // Make some changes
      act(() => {
        result.current.setDashboardLayout('list');
        result.current.setRefreshInterval('health', 60000);
        result.current.setNotificationPreferences({ soundEnabled: false });
      });

      // Verify changes were made
      expect(result.current.preferences.dashboardLayout.layout).toBe('list');
      expect(result.current.preferences.refreshIntervals.health).toBe(60000);
      expect(result.current.preferences.notifications.soundEnabled).toBe(false);

      // Reset to defaults
      act(() => {
        result.current.resetPreferencesToDefaults();
      });

      // Verify reset
      expect(result.current.preferences.dashboardLayout.layout).toBe('grid');
      expect(result.current.preferences.refreshIntervals.health).toBe(15000);
      expect(result.current.preferences.notifications.soundEnabled).toBe(true);
    });
  });

  describe('UI State Actions', () => {
    it('should set active tab', () => {
      const { result } = renderHook(() => useReliabilityStore());

      act(() => {
        result.current.setActiveTab('alerts');
      });

      expect(result.current.ui.activeTab).toBe('alerts');
    });

    it('should update alert filters', () => {
      const { result } = renderHook(() => useReliabilityStore());

      const newFilters: Partial<AlertFilters> = {
        searchText: 'database',
        timeRange: '7d',
        severities: new Set(['high', 'critical']),
      };

      act(() => {
        result.current.setAlertFilters(newFilters);
      });

      expect(result.current.ui.filters.searchText).toBe('database');
      expect(result.current.ui.filters.timeRange).toBe('7d');
      expect(result.current.ui.filters.severities).toEqual(new Set(['high', 'critical']));
    });

    it('should clear alert filters', () => {
      const { result } = renderHook(() => useReliabilityStore());

      // Set some filters first
      act(() => {
        result.current.setAlertFilters({
          searchText: 'test',
          timeRange: '7d',
        });
      });

      // Clear filters
      act(() => {
        result.current.clearAlertFilters();
      });

      expect(result.current.ui.filters.searchText).toBe('');
      expect(result.current.ui.filters.timeRange).toBe('24h');
      expect(result.current.ui.filters.severities.size).toBe(4);
    });

    it('should manage batch operation loading states', () => {
      const { result } = renderHook(() => useReliabilityStore());

      act(() => {
        result.current.setBatchOperationLoading('resolve-multiple-alerts', true);
      });

      expect(result.current.ui.batchOperations['resolve-multiple-alerts']).toBe(true);
      expect(result.current.ui.batchOperations['acknowledge-multiple-alerts']).toBe(false);

      act(() => {
        result.current.setBatchOperationLoading('resolve-multiple-alerts', false);
      });

      expect(result.current.ui.batchOperations['resolve-multiple-alerts']).toBe(false);
    });

    it('should manage alert selection', () => {
      const { result } = renderHook(() => useReliabilityStore());

      const alertId1 = 'alert-1';
      const alertId2 = 'alert-2';

      // Toggle selection
      act(() => {
        result.current.toggleAlertSelection(alertId1);
      });
      expect(result.current.ui.selectedAlerts.has(alertId1)).toBe(true);

      // Toggle again to deselect
      act(() => {
        result.current.toggleAlertSelection(alertId1);
      });
      expect(result.current.ui.selectedAlerts.has(alertId1)).toBe(false);

      // Select all alerts
      act(() => {
        result.current.selectAllAlerts([alertId1, alertId2]);
      });
      expect(result.current.ui.selectedAlerts.has(alertId1)).toBe(true);
      expect(result.current.ui.selectedAlerts.has(alertId2)).toBe(true);

      // Clear selection
      act(() => {
        result.current.clearAlertSelection();
      });
      expect(result.current.ui.selectedAlerts.size).toBe(0);
    });

    it('should toggle filter panel', () => {
      const { result } = renderHook(() => useReliabilityStore());

      expect(result.current.ui.isFilterPanelOpen).toBe(false);

      act(() => {
        result.current.toggleFilterPanel();
      });
      expect(result.current.ui.isFilterPanelOpen).toBe(true);

      act(() => {
        result.current.toggleFilterPanel();
      });
      expect(result.current.ui.isFilterPanelOpen).toBe(false);
    });
  });

  describe('Monitoring State Actions', () => {
    it('should set monitoring enabled state', () => {
      const { result } = renderHook(() => useReliabilityStore());

      act(() => {
        result.current.setMonitoringEnabled(false);
      });
      expect(result.current.monitoring.isEnabled).toBe(false);

      act(() => {
        result.current.setMonitoringEnabled(true);
      });
      expect(result.current.monitoring.isEnabled).toBe(true);
    });

    it('should set connection status', () => {
      const { result } = renderHook(() => useReliabilityStore());

      const statuses: ConnectionStatus[] = ['connected', 'reconnecting', 'disconnected'];

      statuses.forEach(status => {
        act(() => {
          result.current.setConnectionStatus(status);
        });
        expect(result.current.monitoring.connectionStatus).toBe(status);
      });
    });

    it('should update last refresh timestamps', () => {
      const { result } = renderHook(() => useReliabilityStore());

      const testTimestamp = '2024-01-15T10:30:00.000Z';

      act(() => {
        result.current.updateLastRefresh('health', testTimestamp);
      });

      expect(result.current.monitoring.lastRefresh.health).toBe(testTimestamp);

      // Test auto-timestamp
      act(() => {
        result.current.updateLastRefresh('alerts');
      });

      expect(result.current.monitoring.lastRefresh.alerts).toBeDefined();
      expect(typeof result.current.monitoring.lastRefresh.alerts).toBe('string');
    });

    it('should manage paused data types', () => {
      const { result } = renderHook(() => useReliabilityStore());

      const dataType: DataType = 'circuit-breakers';

      // Pause data type
      act(() => {
        result.current.pauseDataType(dataType);
      });
      expect(result.current.monitoring.pausedDataTypes.has(dataType)).toBe(true);

      // Resume data type
      act(() => {
        result.current.resumeDataType(dataType);
      });
      expect(result.current.monitoring.pausedDataTypes.has(dataType)).toBe(false);
    });

    it('should pause and resume all monitoring', () => {
      const { result } = renderHook(() => useReliabilityStore());

      // Pause all
      act(() => {
        result.current.pauseAllMonitoring();
      });
      expect(result.current.monitoring.isEnabled).toBe(false);
      expect(result.current.monitoring.pausedDataTypes.size).toBe(7); // All data types

      // Resume all
      act(() => {
        result.current.resumeAllMonitoring();
      });
      expect(result.current.monitoring.isEnabled).toBe(true);
      expect(result.current.monitoring.pausedDataTypes.size).toBe(0);
    });
  });

  describe('Computed Selectors', () => {
    it('should get visible widgets in correct order', () => {
      const { result } = renderHook(() => useReliabilityStore());

      // Hide some widgets
      act(() => {
        result.current.toggleWidget('dependency-health');
        result.current.toggleWidget('deduplication-metrics');
      });

      const visibleWidgets = result.current.getVisibleWidgets();
      
      // Should only include visible widgets in the correct order
      expect(visibleWidgets).toEqual([
        'system-health',
        'circuit-breakers',
        'performance-metrics',
        'active-alerts',
        'alert-statistics',
      ]);
    });

    it('should get active filters correctly', () => {
      const { result } = renderHook(() => useReliabilityStore());

      // Set some non-default filters
      act(() => {
        result.current.setAlertFilters({
          searchText: 'database',
          timeRange: '7d',
          severities: new Set(['high', 'critical']),
        });
      });

      const activeFilters = result.current.getActiveFilters();

      expect(activeFilters.searchText).toBe('database');
      expect(activeFilters.timeRange).toBe('7d');
      expect(activeFilters.severities).toEqual(new Set(['high', 'critical']));
      expect(activeFilters.statuses).toBeUndefined(); // Should not include default values
    });

    it('should check if data type is paused', () => {
      const { result } = renderHook(() => useReliabilityStore());

      const dataType: DataType = 'metrics';

      // Initially not paused
      expect(result.current.isDataTypePaused(dataType)).toBe(false);

      // Pause specific data type
      act(() => {
        result.current.pauseDataType(dataType);
      });
      expect(result.current.isDataTypePaused(dataType)).toBe(true);

      // Resume specific data type
      act(() => {
        result.current.resumeDataType(dataType);
      });
      expect(result.current.isDataTypePaused(dataType)).toBe(false);

      // Disable all monitoring
      act(() => {
        result.current.setMonitoringEnabled(false);
      });
      expect(result.current.isDataTypePaused(dataType)).toBe(true); // Should be paused when monitoring disabled
    });

    it('should get selected alert count', () => {
      const { result } = renderHook(() => useReliabilityStore());

      expect(result.current.getSelectedAlertCount()).toBe(0);

      act(() => {
        result.current.selectAllAlerts(['alert-1', 'alert-2', 'alert-3']);
      });

      expect(result.current.getSelectedAlertCount()).toBe(3);
    });
  });
});
