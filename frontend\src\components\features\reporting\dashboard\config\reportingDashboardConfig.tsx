/**
 * @file Reporting Dashboard Configuration
 * @description Defines the layout, tabs, and widgets for the reporting dashboard.
 */

import React from 'react';
import type { DashboardConfig, ViewMode } from '@/components/dashboard/types';
import {
  DelegationStatusWidget,
  DelegationTrendWidget,
  LocationDistributionWidget,
  TaskMetricsWidget,
  ReportingTableWidget,
} from '../widgets';
import { BarChart3, TrendingUp, Table } from 'lucide-react';

// Defines the structure for a widget in the dashboard grid
interface WidgetConfig {
  id: string;
  component: React.ComponentType<any>;
  span: string; // Tailwind CSS class for column span
}

// Defines the structure for a tab in the dashboard (extended from base TabConfig)
interface ReportingTabConfig {
  id: string;
  label: string;
  icon: React.ReactNode;
  description: string;
  widgets: WidgetConfig[];
}

// Custom dashboard config for reporting
interface ReportingDashboardConfig extends Omit<DashboardConfig, 'tabs'> {
  tabs: ReportingTabConfig[];
}

export const reportingDashboardConfig: ReportingDashboardConfig = {
  entityType: 'reporting',
  title: 'Reporting Dashboard',
  description: 'Comprehensive reporting and analytics dashboard',
  viewModes: ['grid', 'list'] as ViewMode[],
  defaultViewMode: 'grid' as ViewMode,
  tabs: [
    {
      id: 'overview',
      label: 'Overview',
      icon: <BarChart3 className="h-4 w-4" />,
      description: 'High-level analytics and status distribution.',
      widgets: [
        {
          id: 'delegation-status',
          component: DelegationStatusWidget,
          span: 'lg:col-span-1',
        },
        {
          id: 'delegation-trend',
          component: DelegationTrendWidget,
          span: 'lg:col-span-2',
        },
        {
          id: 'location-distribution',
          component: LocationDistributionWidget,
          span: 'lg:col-span-2',
        },
        {
          id: 'task-metrics',
          component: TaskMetricsWidget,
          span: 'lg:col-span-1',
        },
      ],
    },
    {
      id: 'details',
      label: 'Detailed Report',
      icon: <Table className="h-4 w-4" />,
      description: 'In-depth data table with filtering and sorting.',
      widgets: [
        {
          id: 'reporting-table',
          component: ReportingTableWidget,
          span: 'lg:col-span-3 xl:col-span-4',
        },
      ],
    },
  ],
};

// Widget configurations for backward compatibility
export const widgetConfigurations = reportingDashboardConfig.tabs.flatMap(
  tab => tab.widgets
);

// Filter presets for common reporting scenarios
export const filterPresets = {
  lastWeek: {
    dateRange: {
      from: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
      to: new Date(),
    },
    status: [],
    locations: [],
    employees: [],
    vehicles: [],
  },
  lastMonth: {
    dateRange: {
      from: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      to: new Date(),
    },
    status: [],
    locations: [],
    employees: [],
    vehicles: [],
  },
  currentYear: {
    dateRange: {
      from: new Date(new Date().getFullYear(), 0, 1),
      to: new Date(),
    },
    status: [],
    locations: [],
    employees: [],
    vehicles: [],
  },
};
