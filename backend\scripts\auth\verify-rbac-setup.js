/**
 * Phase 3: RBAC Setup Verification
 *
 * This script provides a comprehensive verification of the RBAC setup
 * using direct SQL queries and Supabase-specific methods.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey);

async function verifyRBACSetup() {
  console.log('🔍 RBAC Setup Verification');
  console.log('==========================\n');

  let allTestsPassed = true;

  try {
    // Test 1: Verify user_profiles table and data
    console.log('📋 Test 1: User Profiles Table');
    console.log('==============================');

    const { data: profiles, error: profileError } = await supabaseAdmin
      .from('user_profiles')
      .select('*');

    if (profileError) {
      console.log('❌ Cannot access user_profiles table:', profileError.message);
      allTestsPassed = false;
    } else {
      console.log(`✅ Found ${profiles.length} user profiles`);

      profiles.forEach((profile, index) => {
        console.log(`   ${index + 1}. ID: ${profile.id}`);
        console.log(`      Role: ${profile.role}`);
        console.log(`      Employee ID: ${profile.employee_id || 'none'}`);
        console.log(`      Active: ${profile.is_active}`);
        console.log(`      Created: ${profile.created_at}`);
      });
    }

    // Test 2: Test UserRole enum by trying to insert each role
    console.log('\n📋 Test 2: UserRole Enum Values');
    console.log('===============================');

    const testRoles = ['USER', 'MANAGER', 'ADMIN', 'SUPER_ADMIN', 'READONLY'];
    const validRoles = [];

    for (const role of testRoles) {
      const testId = `test-enum-${Date.now()}-${Math.random().toString(36).substr(2, 5)}`;

      try {
        const { error } = await supabaseAdmin.from('user_profiles').insert({
          id: testId,
          role: role,
          is_active: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        });

        if (!error) {
          validRoles.push(role);

          // Clean up immediately
          await supabaseAdmin.from('user_profiles').delete().eq('id', testId);

          console.log(`   ✅ ${role}: Valid`);
        } else {
          console.log(`   ❌ ${role}: Invalid - ${error.message}`);
          allTestsPassed = false;
        }
      } catch (e) {
        console.log(`   ❌ ${role}: Error - ${e.message}`);
        allTestsPassed = false;
      }
    }

    console.log(`\n   Summary: ${validRoles.length}/${testRoles.length} roles are valid`);

    // Test 3: Test auth hook function
    console.log('\n📋 Test 3: Auth Hook Function');
    console.log('=============================');

    try {
      // Create a test user profile first
      const testUserId = '12345678-1234-1234-1234-123456789012';

      await supabaseAdmin.from('user_profiles').upsert({
        id: testUserId,
        role: 'USER',
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      });

      // Test the auth hook function
      const testEvent = {
        user_id: testUserId,
        claims: { sub: testUserId },
      };

      const { data: hookResult, error: hookError } = await supabaseAdmin.rpc(
        'custom_access_token_hook',
        { event: testEvent },
      );

      if (hookError) {
        console.log('❌ Auth hook function error:', hookError.message);
        allTestsPassed = false;
      } else {
        console.log('✅ Auth hook function is working');
        console.log('   Hook result:', JSON.stringify(hookResult, null, 2));
      }

      // Clean up test user
      await supabaseAdmin.from('user_profiles').delete().eq('id', testUserId);
    } catch (error) {
      console.log('❌ Auth hook test failed:', error.message);
      allTestsPassed = false;
    }

    // Test 4: Check auth users and their profiles
    console.log('\n📋 Test 4: Auth Users and Profiles Mapping');
    console.log('==========================================');

    const { data: authUsers, error: authError } = await supabaseAdmin.auth.admin.listUsers();

    if (authError) {
      console.log('❌ Cannot access auth users:', authError.message);
      allTestsPassed = false;
    } else {
      console.log(`✅ Found ${authUsers.users.length} auth users`);

      for (const user of authUsers.users) {
        console.log(`\n   👤 User: ${user.email} (${user.id})`);
        console.log(`      Created: ${user.created_at}`);
        console.log(`      Confirmed: ${user.email_confirmed_at ? 'Yes' : 'No'}`);
        console.log(`      Metadata role: ${user.raw_user_meta_data?.role || 'none'}`);

        // Check if user has a profile
        const { data: userProfile, error: profileError } = await supabaseAdmin
          .from('user_profiles')
          .select('*')
          .eq('id', user.id)
          .single();

        if (profileError) {
          console.log(`      ❌ No user profile found`);
          allTestsPassed = false;
        } else {
          console.log(
            `      ✅ Profile exists: role=${userProfile.role}, active=${userProfile.is_active}`,
          );
        }
      }
    }

    // Test 5: Check employee linking
    console.log('\n📋 Test 5: Employee Linking');
    console.log('===========================');

    const { data: employees, error: empError } = await supabaseAdmin
      .from('Employee')
      .select('id, name, contactEmail');

    if (empError) {
      console.log('❌ Cannot access employees:', empError.message);
      allTestsPassed = false;
    } else {
      console.log(`✅ Found ${employees.length} employees`);

      const { data: linkedProfiles, error: linkError } = await supabaseAdmin
        .from('user_profiles')
        .select(
          `
                    id,
                    role,
                    employee_id,
                    Employee:employee_id (
                        id,
                        name,
                        contactEmail
                    )
                `,
        )
        .not('employee_id', 'is', null);

      if (linkError) {
        console.log('❌ Cannot check employee linking:', linkError.message);
        allTestsPassed = false;
      } else {
        console.log(`✅ ${linkedProfiles.length} user profiles linked to employees`);

        linkedProfiles.forEach(profile => {
          console.log(
            `   🔗 Profile ${profile.id} → Employee ${profile.Employee?.name} (${profile.Employee?.contactEmail})`,
          );
        });
      }
    }

    // Test 6: Verify middleware helper functions exist
    console.log('\n📋 Test 6: Database Helper Functions');
    console.log('====================================');

    const helperFunctions = [
      'auth.get_user_role',
      'auth.is_admin',
      'auth.is_manager_or_above',
      'auth.get_user_employee_id',
    ];

    for (const funcName of helperFunctions) {
      try {
        // Try to call each function with a test UUID
        const testId = '12345678-1234-1234-1234-123456789012';

        let result;
        if (funcName === 'auth.get_user_role') {
          result = await supabaseAdmin.rpc('get_user_role', { user_id: testId });
        } else if (funcName === 'auth.is_admin') {
          result = await supabaseAdmin.rpc('is_admin', { user_id: testId });
        } else if (funcName === 'auth.is_manager_or_above') {
          result = await supabaseAdmin.rpc('is_manager_or_above', {
            user_id: testId,
          });
        } else if (funcName === 'auth.get_user_employee_id') {
          result = await supabaseAdmin.rpc('get_user_employee_id', {
            user_id: testId,
          });
        }

        if (result.error) {
          console.log(`   ❌ ${funcName}: ${result.error.message}`);
          allTestsPassed = false;
        } else {
          console.log(`   ✅ ${funcName}: Available`);
        }
      } catch (error) {
        console.log(`   ❌ ${funcName}: ${error.message}`);
        allTestsPassed = false;
      }
    }

    // Final summary
    console.log('\n📊 Verification Summary');
    console.log('=======================');

    if (allTestsPassed) {
      console.log('✅ All RBAC components are properly configured');
      console.log('✅ System is ready for user session testing');
      console.log('✅ Ready to proceed to Phase 4 (System Cleanup)');
    } else {
      console.log('❌ Some RBAC components have issues');
      console.log('⚠️  Please address the issues before proceeding');
    }

    console.log('\n🎯 Manual Testing Steps:');
    console.log('1. Sign in through your frontend application');
    console.log('2. Check browser dev tools for JWT token with custom claims');
    console.log('3. Test protected routes and API endpoints');
    console.log('4. Verify role-based access control in the UI');
  } catch (error) {
    console.error('❌ Verification failed:', error.message);
    process.exit(1);
  }
}

// Run verification
verifyRBACSetup().catch(console.error);
