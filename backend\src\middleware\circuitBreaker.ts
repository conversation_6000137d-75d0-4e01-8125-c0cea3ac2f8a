/**
 * Circuit Breaker Middleware
 *
 * Express middleware wrapper for circuit breaker functionality.
 * Provides easy integration of circuit breaker protection for routes.
 *
 * Features:
 * - Route-level circuit breaker protection
 * - Automatic fallback responses
 * - Integration with metrics collection
 * - Configurable per-route settings
 */

import type { NextFunction, Request, Response } from 'express';

import {
  circuitBreakerRegistry,
  executeWithCircuitBreaker,
} from '../services/circuitBreaker.service.js';
import { businessMetrics } from '../services/metrics.service.js';
import logger from '../utils/logger.js';

// Circuit breaker middleware configuration
interface CircuitBreakerMiddlewareConfig {
  enabled?: boolean;
  errorThresholdPercentage?: number;
  fallbackResponse?: any;
  fallbackStatusCode?: number;
  resetTimeout?: number;
  serviceName: string;
  timeout?: number;
}

/**
 * Create circuit breaker middleware for protecting routes
 */
export function createCircuitBreakerMiddleware(config: CircuitBreakerMiddlewareConfig) {
  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    // Skip if circuit breaker is disabled
    if (config.enabled === false) {
      next();
      return;
    }

    const serviceName = config.serviceName;

    try {
      // Get circuit breaker instance
      const breaker = circuitBreakerRegistry.getBreaker(serviceName, {
        errorThresholdPercentage: config.errorThresholdPercentage,
        name: serviceName,
        resetTimeout: config.resetTimeout,
        timeout: config.timeout,
      });

      // Check if circuit breaker is open
      if (breaker.opened) {
        logger.warn(`Circuit breaker is OPEN for service: ${serviceName}`, {
          method: req.method,
          path: req.path,
          service: 'circuit-breaker-middleware',
          serviceName,
        });

        // Record circuit breaker rejection
        businessMetrics.recordCircuitBreakerRequest(serviceName, serviceName, 'reject');

        // Return fallback response
        const fallbackResponse = config.fallbackResponse || {
          code: 'CIRCUIT_BREAKER_OPEN',
          error: 'Service temporarily unavailable',
          message: `The ${serviceName} service is currently experiencing issues. Please try again later.`,
          retryAfter: Math.ceil(((breaker as any).options?.resetTimeout || 60000) / 1000),
        };

        const statusCode = config.fallbackStatusCode || 503;

        res.status(statusCode).json(fallbackResponse);
        return;
      }

      // Circuit breaker is closed or half-open, continue with request
      next();
    } catch (error) {
      logger.error(`Circuit breaker middleware error for service: ${serviceName}`, {
        error: error instanceof Error ? error.message : 'Unknown error',
        method: req.method,
        path: req.path,
        service: 'circuit-breaker-middleware',
        serviceName,
      });

      // Continue without circuit breaker protection on middleware error
      next();
    }
  };
}

/**
 * Wrap async route handlers with circuit breaker protection
 */
export function withCircuitBreaker(
  serviceName: string,
  handler: (req: Request, res: Response, next: NextFunction) => Promise<any>,
  config?: Partial<CircuitBreakerMiddlewareConfig>,
) {
  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const fullConfig = {
      enabled: true,
      serviceName,
      ...config,
    };

    try {
      // Execute handler with circuit breaker protection
      await executeWithCircuitBreaker(serviceName, () => handler(req, res, next), {
        errorThresholdPercentage: fullConfig.errorThresholdPercentage,
        name: serviceName,
        resetTimeout: fullConfig.resetTimeout,
        timeout: fullConfig.timeout,
      });

      // Record successful execution
      businessMetrics.recordCircuitBreakerRequest(serviceName, serviceName, 'success');
    } catch (error) {
      logger.error(`Circuit breaker protected handler failed for service: ${serviceName}`, {
        error: error instanceof Error ? error.message : 'Unknown error',
        method: req.method,
        path: req.path,
        service: 'circuit-breaker-middleware',
        serviceName,
      });

      // Record failure
      businessMetrics.recordCircuitBreakerRequest(serviceName, serviceName, 'failure');

      // Check if it's a circuit breaker rejection
      if (error instanceof Error && error.message.includes('Circuit breaker is OPEN')) {
        const fallbackResponse = fullConfig.fallbackResponse || {
          code: 'CIRCUIT_BREAKER_OPEN',
          error: 'Service temporarily unavailable',
          message: `The ${serviceName} service is currently experiencing issues. Please try again later.`,
        };

        const statusCode = fullConfig.fallbackStatusCode || 503;
        res.status(statusCode).json(fallbackResponse);
        return;
      }

      // For other errors, pass to error handler
      next(error);
    }
  };
}

/**
 * Pre-configured circuit breaker middleware for common services
 */

// Database operations circuit breaker
export const databaseCircuitBreaker = createCircuitBreakerMiddleware({
  errorThresholdPercentage: 50,
  fallbackResponse: {
    code: 'DATABASE_CIRCUIT_BREAKER_OPEN',
    error: 'Database temporarily unavailable',
    message: 'Database operations are currently experiencing issues. Please try again later.',
  },
  fallbackStatusCode: 503,
  resetTimeout: 30000,
  serviceName: 'database',
  timeout: 10000,
});

// Supabase operations circuit breaker
export const supabaseCircuitBreaker = createCircuitBreakerMiddleware({
  errorThresholdPercentage: 50,
  fallbackResponse: {
    code: 'SUPABASE_CIRCUIT_BREAKER_OPEN',
    error: 'Authentication service temporarily unavailable',
    message: 'Authentication operations are currently experiencing issues. Please try again later.',
  },
  fallbackStatusCode: 503,
  resetTimeout: 45000,
  serviceName: 'supabase',
  timeout: 12000,
});

// Redis operations circuit breaker
export const redisCircuitBreaker = createCircuitBreakerMiddleware({
  errorThresholdPercentage: 40,
  fallbackResponse: {
    code: 'REDIS_CIRCUIT_BREAKER_OPEN',
    error: 'Cache service temporarily unavailable',
    message: 'Cache operations are currently experiencing issues. Functionality may be limited.',
  },
  fallbackStatusCode: 200, // Non-critical service, don't fail the request
  resetTimeout: 15000,
  serviceName: 'redis',
  timeout: 5000,
});

// External API circuit breaker
export const externalApiCircuitBreaker = createCircuitBreakerMiddleware({
  errorThresholdPercentage: 60,
  fallbackResponse: {
    code: 'EXTERNAL_API_CIRCUIT_BREAKER_OPEN',
    error: 'External service temporarily unavailable',
    message:
      'External service integration is currently experiencing issues. Please try again later.',
  },
  fallbackStatusCode: 503,
  resetTimeout: 60000,
  serviceName: 'external_api',
  timeout: 15000,
});

/**
 * Circuit breaker reset middleware for admin operations
 */
export function circuitBreakerResetMiddleware() {
  return (req: Request, res: Response): void => {
    try {
      const { serviceName } = req.body;

      if (serviceName) {
        circuitBreakerRegistry.reset(serviceName);
        logger.info(`Circuit breaker manually reset: ${serviceName}`, {
          adminUser: (req as any).user?.id,
          service: 'circuit-breaker-middleware',
          serviceName,
        });
      } else {
        circuitBreakerRegistry.reset();
        logger.info('All circuit breakers manually reset', {
          adminUser: (req as any).user?.id,
          service: 'circuit-breaker-middleware',
        });
      }

      res.json({
        message: serviceName
          ? `Circuit breaker reset for service: ${serviceName}`
          : 'All circuit breakers reset',
        status: 'success',
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      logger.error('Failed to reset circuit breaker', {
        error: error instanceof Error ? error.message : 'Unknown error',
        service: 'circuit-breaker-middleware',
      });

      res.status(500).json({
        message: 'Failed to reset circuit breaker',
        status: 'error',
      });
    }
  };
}

/**
 * Circuit breaker status middleware for monitoring endpoints
 */
export function circuitBreakerStatusMiddleware() {
  return (req: Request, res: Response): void => {
    try {
      const status = circuitBreakerRegistry.getStatus();

      res.json({
        data: {
          circuitBreakers: status,
          registeredBreakers: circuitBreakerRegistry.getRegisteredBreakers(),
          timestamp: new Date().toISOString(),
        },
        status: 'success',
      });
    } catch (error) {
      logger.error('Failed to get circuit breaker status', {
        error: error instanceof Error ? error.message : 'Unknown error',
        service: 'circuit-breaker-middleware',
      });

      res.status(500).json({
        message: 'Failed to retrieve circuit breaker status',
        status: 'error',
      });
    }
  };
}

export default {
  circuitBreakerResetMiddleware,
  circuitBreakerStatusMiddleware,
  createCircuitBreakerMiddleware,
  databaseCircuitBreaker,
  externalApiCircuitBreaker,
  redisCircuitBreaker,
  supabaseCircuitBreaker,
  withCircuitBreaker,
};
