import * as z from 'zod';

import { isValidDateString } from '@/lib/utils/dateUtils';

export const TaskStatusSchema = z.enum([
  'Pending',
  'Assigned',
  'In_Progress',
  'Completed',
  'Cancelled',
]);

export const TaskPrioritySchema = z.enum(['Low', 'Medium', 'High']);

// Define SubTask schema for use in TaskSchema
export const SubTaskSchema = z.object({
  completed: z.boolean(),
  id: z.string().optional(),
  taskId: z.string(),
  title: z.string(),
});

export const TaskSchema = z
  .object({
    dateTime: z
      .string()
      .min(1, 'Start date & time is required')
      .refine(val => isValidDateString(val), {
        message:
          'Please enter a valid date and time in YYYY-MM-DD HH:MM format',
      }),
    deadline: z
      .string()
      .refine(val => val === '' || isValidDateString(val), {
        // Allow empty string for optional or valid date
        message:
          'Please enter a valid deadline date and time in YYYY-MM-DD HH:MM format',
      })
      .optional()
      .transform(val => (val === '' ? undefined : val)), // Transform empty string to undefined
    description: z.string().min(1, 'Task description is required'),
    driverEmployeeId: z
      .number()
      .int()
      .positive('Driver Employee ID must be a positive integer')
      .optional()
      .nullable(),
    estimatedDuration: z.coerce
      .number()
      .int() // Ensure it's an integer
      .min(1, 'Estimated duration must be at least 1 minute'),
    id: z.string().uuid().optional(), // Optional for new tasks, will be generated
    location: z.string().min(1, 'Location is required'),
    notes: z.string().optional().or(z.literal('')),
    priority: TaskPrioritySchema,
    requiredSkills: z.array(z.string()),
    // NEW PHASE 3 ASSIGNMENT FIELDS
    staffEmployeeId: z
      .number({
        required_error: 'Staff Employee is required',
        invalid_type_error: 'Staff Employee must be a valid selection',
      })
      .int()
      .positive('Staff Employee ID must be a positive integer'),
    status: TaskStatusSchema,
    statusChangeReason: z.string().optional(), // This field is not part of the core Task domain model for creation/update
    subtasks: z.array(SubTaskSchema).optional(), // Frontend uses 'subtasks' for domain consistency
    // vehicleId should be a number if it's a foreign key to Vehicle.id (Int)
    vehicleId: z
      .number()
      .int()
      .positive('Vehicle ID must be a positive integer.')
      .nullable()
      .optional(),
  })
  // Add a superRefine to check if deadline is after dateTime when both are provided
  .superRefine((data, ctx) => {
    if (data.dateTime && data.deadline) {
      const startDate = new Date(data.dateTime);
      const deadlineDate = new Date(data.deadline);

      if (deadlineDate < startDate) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Deadline cannot be earlier than the start date & time',
          path: ['deadline'],
        });
      }
    }

    // NEW PHASE 3: Vehicle assignment validation
    if (data.vehicleId && !data.driverEmployeeId) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Vehicle cannot be assigned without a driver',
        path: ['vehicleId'],
      });
    }
  });

// Create alias for the form schema to match index.ts expectations
export const TaskFormSchema = TaskSchema;

// Export types
export type TaskFormData = z.infer<typeof TaskSchema>;
export type TaskStatus = z.infer<typeof TaskStatusSchema>;
export type TaskPriority = z.infer<typeof TaskPrioritySchema>;
export type SubTask = z.infer<typeof SubTaskSchema>;
