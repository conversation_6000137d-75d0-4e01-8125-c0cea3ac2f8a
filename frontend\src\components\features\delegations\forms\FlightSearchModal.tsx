'use client';

import { format } from 'date-fns';
import {
  Calendar as CalendarIcon,
  Loader2,
  Plane,
  PlaneLanding,
  PlaneTakeoff,
  Search,
} from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';

import type {
  FlightData} from '@/lib/api/services/flight.service';

import { ActionButton } from '@/components/ui/action-button'; // Changed
import { Calendar } from '@/components/ui/calendar';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useToast } from '@/hooks/utils/use-toast';
import {
  searchFlightsByCallsignAndDate,
} from '@/lib/api/services/flight.service';
import { cn } from '@/lib/utils';

interface FlightSearchModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectFlight: (flight: FlightData) => void;
  type: 'arrival' | 'departure';
}

export default function FlightSearchModal({
  isOpen,
  onClose,
  onSelectFlight,
  type,
}: FlightSearchModalProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(
    new Date()
  );
  const [isLoading, setIsLoading] = useState(false);
  const [flights, setFlights] = useState<FlightData[]>([]);
  const [error, setError] = useState<null | string>(null);
  const { toast } = useToast();

  const debouncedSearch = useCallback(
    debounce(async (term: string, date: Date | undefined) => {
      if (term.length < 2) {
        setFlights([]);
        return;
      }
      if (!date) {
        setError('Please select a date to search.');
        toast({
          description: 'Please select a date before searching for flights.',
          title: 'Date Required',
          variant: 'destructive',
        });
        setFlights([]);
        return;
      }

      setIsLoading(true);
      setError(null);
      const formattedDate = format(date, 'yyyy-MM-dd');
      try {
        const data = await searchFlightsByCallsignAndDate(term, formattedDate);
        setFlights(data);
        if (data.length === 0) {
          toast({
            description: `No flights found matching "${term}" on ${formattedDate}.`,
            title: 'No Flights Found',
            variant: 'default',
          });
        }
      } catch (error_: any) {
        setError(error_.message || 'Failed to search flights');
        toast({
          description: `Failed to search flights: ${error_.message}. Please try again.`,
          title: 'Error Searching Flights',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    }, 500),
    [toast]
  );

  useEffect(() => {
    if (searchTerm && selectedDate) {
      debouncedSearch(searchTerm, selectedDate);
    } else {
      setFlights([]);
    }
    return () => {
      debouncedSearch.cancel();
    };
  }, [searchTerm, selectedDate, debouncedSearch]);

  const handleSelectFlight = (flight: FlightData) => {
    onSelectFlight(flight);
    onClose();
  };

  const formatTimestamp = (timestamp?: number) => {
    if (!timestamp) return 'Unknown';
    return new Date(timestamp * 1000).toLocaleString();
  };

  return (
    <Dialog onOpenChange={open => !open && onClose()} open={isOpen}>
      <DialogContent className="flex max-h-[80vh] flex-col sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            {type === 'arrival' ? (
              <PlaneLanding className="mr-2 size-5 text-accent" />
            ) : (
              <PlaneTakeoff className="mr-2 size-5 text-accent" />
            )}
            Search {type === 'arrival' ? 'Arrival' : 'Departure'} Flights
          </DialogTitle>
          <DialogDescription>
            Enter a flight callsign (e.g., BA123) and select a date to search
            for flights.
          </DialogDescription>
        </DialogHeader>

        <div className="mb-4 grid grid-cols-1 gap-4 md:grid-cols-2">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 size-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              className="pl-10"
              onChange={e => setSearchTerm(e.target.value)}
              placeholder="Callsign (e.g., BA123)"
              value={searchTerm}
            />
          </div>
          <div>
            <Popover>
              <PopoverTrigger asChild>
                <ActionButton
                  actionType="tertiary"
                  className={cn(
                    'w-full justify-start text-left font-normal',
                    !selectedDate && 'text-muted-foreground'
                  )}
                  icon={<CalendarIcon className="size-4" />}
                >
                  {selectedDate ? (
                    format(selectedDate, 'PPP')
                  ) : (
                    <span>Pick a date</span>
                  )}
                </ActionButton>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar
                  initialFocus
                  mode="single"
                  onSelect={setSelectedDate}
                  selected={selectedDate}
                />
              </PopoverContent>
            </Popover>
          </div>
        </div>

        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="size-8 animate-spin text-primary" />
            <span className="ml-2 text-muted-foreground">
              Searching flights...
            </span>
          </div>
        ) : error ? (
          // ErrorDisplay can be used here if preferred
          <div className="py-8 text-center">
            <div className="mb-4 rounded-md bg-destructive/15 p-4 text-sm text-destructive">
              <h4 className="mb-2 font-bold">Error Details:</h4>
              <p className="mb-2">{error}</p>
              {error && (error as any).details && (
                <div className="mt-3 border-t border-destructive/20 pt-3">
                  {(error as any).details.possibleReasons && (
                    <div className="mt-2">
                      <h5 className="mb-1 text-xs font-semibold">
                        Possible Reasons:
                      </h5>
                      <ul className="list-inside list-disc text-xs">
                        {(error as any).details.possibleReasons.map(
                          (reason: string, idx: number) => (
                            <li className="mb-1" key={idx}>
                              {reason}
                            </li>
                          )
                        )}
                      </ul>
                    </div>
                  )}
                  {(error as any).details.apiInfo && (
                    <div className="mt-2 text-xs">
                      <h5 className="mb-1 font-semibold">API Information:</h5>
                      <p>{(error as any).details.apiInfo}</p>
                    </div>
                  )}
                </div>
              )}
              <p className="mt-3 text-xs text-muted-foreground">
                API URL:{' '}
                {process.env.NEXT_PUBLIC_API_BASE_URL ||
                  'http://localhost:3001/api'}
              </p>
            </div>
            <div className="flex justify-center gap-2">
              {error.toString().includes('future date') ? (
                <>
                  <ActionButton
                    actionType="tertiary"
                    onClick={() => {
                      setSelectedDate(new Date());
                      setTimeout(
                        () => debouncedSearch(searchTerm, new Date()),
                        100
                      );
                    }}
                  >
                    Try with Today's Date
                  </ActionButton>
                  <ActionButton
                    actionType="tertiary"
                    onClick={() => {
                      const yesterday = new Date();
                      yesterday.setDate(yesterday.getDate() - 1);
                      setSelectedDate(yesterday);
                      setTimeout(
                        () => debouncedSearch(searchTerm, yesterday),
                        100
                      );
                    }}
                  >
                    Try with Yesterday
                  </ActionButton>
                </>
              ) : (
                <ActionButton
                  actionType="tertiary"
                  onClick={() => debouncedSearch(searchTerm, selectedDate)}
                >
                  Try Again
                </ActionButton>
              )}
            </div>
          </div>
        ) : flights.length === 0 ? (
          <div className="py-8 text-center text-muted-foreground">
            {searchTerm.length > 0 ? (
              <div>
                <p className="mb-2 font-medium text-amber-500">
                  No flights found matching "{searchTerm}" on{' '}
                  {selectedDate ? format(selectedDate, 'PPP') : 'selected date'}
                </p>
                <div className="mb-4 rounded-md bg-muted p-4 text-sm">
                  <h4 className="mb-2 font-semibold">Suggestions:</h4>
                  <ul className="list-inside list-disc text-sm text-muted-foreground">
                    <li className="mb-1">
                      Check if the callsign format is correct (e.g., "RYR441J"
                      for Ryanair flight 441J)
                    </li>
                    <li className="mb-1">
                      Try searching for a different date - OpenSky may not have
                      data for all dates
                    </li>
                    <li className="mb-1">
                      OpenSky Network API primarily provides historical data,
                      not future schedules
                    </li>
                    <li className="mb-1">
                      Some flights may not be tracked by OpenSky Network
                    </li>
                  </ul>
                </div>
                <div className="inline-block rounded-md bg-muted p-2 text-xs text-muted-foreground">
                  <p>
                    API URL:{' '}
                    {process.env.NEXT_PUBLIC_API_BASE_URL ||
                      'http://localhost:3001/api'}
                    /flights/search
                  </p>
                  <p>Search Term: {searchTerm}</p>
                  <p>
                    Date:{' '}
                    {selectedDate
                      ? format(selectedDate, 'yyyy-MM-dd')
                      : 'Not selected'}
                  </p>
                </div>
              </div>
            ) : (
              'Enter a flight callsign to search.'
            )}
          </div>
        ) : (
          <ScrollArea className="max-h-[400px] flex-1 pr-4">
            <div className="space-y-2">
              {flights.map(flight => (
                <div
                  className="cursor-pointer rounded-md border p-3 transition-colors hover:bg-accent hover:text-accent-foreground"
                  key={`${flight.icao24}-${flight.callsign}`}
                  onClick={() => handleSelectFlight(flight)}
                >
                  <div className="flex items-start justify-between">
                    <div>
                      <p className="font-semibold">{flight.callsign}</p>
                      <p className="text-sm text-muted-foreground">
                        {flight.departureAirport || 'Unknown'} →{' '}
                        {flight.arrivalAirport || 'Unknown'}
                      </p>
                    </div>
                    <div className="text-right text-sm">
                      <p className="flex items-center">
                        <Plane className="mr-1 inline-block size-3 text-muted-foreground" />
                        {flight.icao24}
                      </p>
                      {flight.onGround !== undefined && (
                        <p
                          className={
                            flight.onGround
                              ? 'text-amber-500'
                              : 'text-green-500'
                          }
                        >
                          {flight.onGround ? 'On Ground' : 'In Air'}
                        </p>
                      )}
                    </div>
                  </div>
                  {(flight.departureTime || flight.arrivalTime) && (
                    <div className="mt-2 text-xs text-muted-foreground">
                      {flight.departureTime && (
                        <p>
                          Departure: {formatTimestamp(flight.departureTime)}
                        </p>
                      )}
                      {flight.arrivalTime && (
                        <p>Arrival: {formatTimestamp(flight.arrivalTime)}</p>
                      )}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </ScrollArea>
        )}
        <DialogFooter className="mt-4">
          <ActionButton actionType="tertiary" onClick={onClose}>
            Cancel
          </ActionButton>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): T & { cancel: () => void } {
  let timeout: null | ReturnType<typeof setTimeout> = null;
  const debounced = function (this: any, ...args: Parameters<T>) {
    const context = this;
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(() => {
      timeout = null;
      func.apply(context, args);
    }, wait);
  } as T & { cancel: () => void };
  debounced.cancel = function () {
    if (timeout) {
      clearTimeout(timeout);
      timeout = null;
    }
  };
  return debounced;
}
