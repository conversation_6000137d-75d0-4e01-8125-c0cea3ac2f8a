/**
 * @file SecureApiClient - Enhanced API client with comprehensive security features
 * @module api/security/secureApiClient
 *
 * Enhanced to use SecurityComposer with moved security hooks directly:
 * - CSRF Protection (via moved useCSRFProtection hook)
 * - Input Sanitization (via moved useInputValidation hook)
 * - Token Validation & Refresh (via moved useTokenManagement hook)
 * - Session Security (via moved useSessionSecurity hook)
 * - Automatic Logout on Auth Failures
 * - Enhanced Error Handling
 *
 * Phase 2 Enhancement: Now uses SecurityComposer with existing security infrastructure
 * - Integrates with SecurityUtils and SECURITY_CONSTANTS
 * - Enhanced configuration management and validation
 * - Improved error handling and recovery mechanisms
 * - Comprehensive security status reporting
 */

import type {
  IHttpClient,
  SecurityConfig,
  SecurityContext,
} from '../core/interfaces';
import type { ApiClientConfig, HttpMethod, RequestConfig } from '../core/types';

import { SECURITY_CONSTANTS, SecurityUtils } from '../../security';
import { ApiClient } from '../core/apiClient';
import { ApiError, AuthenticationError } from '../core/errors';
import { SecurityComposer, type SecurityFeatures } from './composer';

export interface SecureApiClientConfig extends ApiClientConfig {
  enableAutoLogout?: boolean;
  enableCSRF?: boolean;
  enableInputSanitization?: boolean;
  enableTokenValidation?: boolean;
  // Enhanced configuration options using existing infrastructure
  securityConfig?: Partial<SecurityConfig>;
  validateSecurityFeatures?: boolean;
}

/**
 * SecureApiClient - Enhanced API client with comprehensive security
 *
 * Phase 2 Enhancement: Now uses SecurityComposer with existing security infrastructure
 * - Integrates with SecurityUtils and SECURITY_CONSTANTS
 * - Maintains ApiClient's reliability and performance
 * - Uses SecurityComposer for all security features
 * - Provides unified interface for all API calls
 * - Enhanced configuration management and validation
 */
export class SecureApiClient extends ApiClient {
  private readonly enhancedSecurityConfig: Partial<SecurityConfig>;
  private lastSecurityCheck: Date = new Date();
  private securityComposer: SecurityComposer | undefined;
  private readonly securityConfig: Required<
    Omit<SecureApiClientConfig, keyof ApiClientConfig>
  >;
  private securityFeatures?: SecurityFeatures;
  private securityInitialized = false;

  constructor(
    config: SecureApiClientConfig,
    securityComposer?: SecurityComposer
  ) {
    // Initialize base ApiClient
    super(config);

    // Security configuration with defaults using SECURITY_CONSTANTS
    this.securityConfig = {
      enableAutoLogout: config.enableAutoLogout ?? true,
      enableCSRF: config.enableCSRF ?? true,
      enableInputSanitization: config.enableInputSanitization ?? true,
      enableTokenValidation: config.enableTokenValidation ?? true,
      securityConfig: config.securityConfig ?? {},
      validateSecurityFeatures: config.validateSecurityFeatures ?? true,
    };

    // Enhanced security configuration using existing infrastructure
    this.enhancedSecurityConfig = {
      authentication: {
        autoLogout: true,
        enabled: this.securityConfig.enableAutoLogout,
        redirectOnFailure: true,
      },
      csrf: {
        enabled: this.securityConfig.enableCSRF,
        excludePaths: [],
        tokenHeader: 'X-CSRF-Token',
      },
      http: {
        baseURL: config.baseURL || '/api',
        retryAttempts: config.retryAttempts || 3,
        timeout: config.timeout || 10_000,
      },
      inputSanitization: {
        enabled: this.securityConfig.enableInputSanitization,
        sanitizers: ['xss', 'sql'],
      },
      tokenValidation: {
        autoRefresh: true,
        enabled: this.securityConfig.enableTokenValidation,
        refreshThreshold:
          SECURITY_CONSTANTS.TOKEN_EXPIRY_THRESHOLD_MINUTES * 60, // Convert to seconds
      },
      // Merge with user-provided security config
      ...config.securityConfig,
    };

    this.securityComposer = securityComposer;

    console.log(
      '🔐 SecureApiClient: Initialized with SecurityUtils integration',
      {
        constants: {
          sessionTimeout: SECURITY_CONSTANTS.SESSION_TIMEOUT_MINUTES,
          tokenThreshold: SECURITY_CONSTANTS.TOKEN_EXPIRY_THRESHOLD_MINUTES,
        },
        securityFeatures: Object.keys(this.securityConfig).filter(
          key => this.securityConfig[key as keyof typeof this.securityConfig]
        ),
      }
    );
  }

  /**
   * Get comprehensive security status information via SecurityComposer and SecurityUtils
   */
  public getSecurityStatus(): {
    hasValidToken: boolean;
    isAuthenticated: boolean;
    lastSecurityCheck: Date;
    securityConstants: typeof SECURITY_CONSTANTS;
    securityFeaturesEnabled: any;
    securityFeaturesInitialized: boolean;
    sessionInfo: any;
    threatLevel: 'critical' | 'high' | 'low' | 'medium';
    userInfo: any;
  } {
    const baseStatus = {
      lastSecurityCheck: this.lastSecurityCheck,
      securityConstants: SECURITY_CONSTANTS,
      securityFeaturesEnabled: this.securityConfig,
      securityFeaturesInitialized: this.securityInitialized,
      sessionInfo: this.getSessionInfo(),
      userInfo: this.extractUserFromToken(),
    };

    if (this.securityComposer) {
      const composerStatus = this.securityComposer.getSecurityStatus();
      return {
        ...baseStatus,
        hasValidToken: composerStatus.hasValidToken,
        isAuthenticated: composerStatus.isAuthenticated,
        threatLevel: this.assessThreatLevel(composerStatus),
      };
    }

    // Fallback if SecurityComposer not initialized
    return {
      ...baseStatus,
      hasValidToken: false,
      isAuthenticated: false,
      threatLevel: 'critical' as const, // Not initialized is critical
    };
  }

  /**
   * Initialize security features from useSecureApi hook
   * Enhanced with validation and SecurityUtils integration
   */
  public initializeSecurity(features: SecurityFeatures): void {
    try {
      // Validate security features if enabled
      if (this.securityConfig.validateSecurityFeatures) {
        this.validateSecurityFeatures(features);
      }

      this.securityFeatures = features;

      // Create SecurityComposer if not provided in constructor
      if (this.securityComposer) {
        // Update existing SecurityComposer with new features
        this.securityComposer.updateSecurityFeatures(features);
        this.securityComposer.updateConfig(this.enhancedSecurityConfig);
      } else {
        this.securityComposer = new SecurityComposer(
          features,
          this.enhancedSecurityConfig
        );
      }

      this.securityInitialized = true;
      this.lastSecurityCheck = new Date();

      console.log(
        '🔐 SecureApiClient: Security features initialized with SecurityUtils integration',
        {
          featuresInitialized: Object.keys(features).filter(
            key => features[key as keyof SecurityFeatures]
          ),
          securityConfig: this.enhancedSecurityConfig,
          timestamp: this.lastSecurityCheck.toISOString(),
        }
      );
    } catch (error) {
      console.error(
        'SecureApiClient: Failed to initialize security features:',
        error
      );
      throw new Error(
        `Security initialization failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * Force security feature refresh
   */
  public refreshSecurityFeatures(): void {
    if (this.securityFeatures && this.securityComposer) {
      this.securityComposer.updateSecurityFeatures(this.securityFeatures);
      this.lastSecurityCheck = new Date();
      console.log('🔄 SecureApiClient: Security features refreshed');
    } else {
      console.warn(
        'SecureApiClient: Cannot refresh - security features not initialized'
      );
    }
  }

  /**
   * Enhanced request method with security middleware via SecurityComposer
   * Integrates with SecurityUtils for enhanced security processing
   */
  public async secureRequest<T>(
    method: HttpMethod,
    endpoint: string,
    data?: any,
    config?: RequestConfig
  ): Promise<T> {
    // Ensure security is initialized
    if (!this.securityInitialized || !this.securityComposer) {
      throw new AuthenticationError(
        'Security features not initialized. Call initializeSecurity() first.',
        'SECURITY_NOT_INITIALIZED'
      );
    }

    // Update last security check timestamp
    this.lastSecurityCheck = new Date();

    // Enhanced SecurityContext with SecurityUtils integration
    const securityContext: SecurityContext = {
      hasValidToken:
        this.securityFeatures?.tokenManagement?.isTokenValid ?? false,
      isAuthenticated:
        this.securityFeatures?.sessionSecurity?.isSessionActive ?? false,
      session: this.getSessionInfo(),
      timestamp: this.lastSecurityCheck,
      user: this.extractUserFromToken(),
    };

    // Create RequestConfig for SecurityComposer
    const requestConfig: RequestConfig & {
      body?: any;
      method?: string;
      url?: string;
    } = {
      ...config,
      body: data,
      method: method,
      url: endpoint,
    };

    try {
      // Pre-request security checks using SecurityUtils
      await this.performPreRequestSecurityChecks(securityContext);

      // Process request through SecurityComposer
      const processedConfig = await this.securityComposer.processRequest(
        requestConfig,
        securityContext
      );

      // Extract processed data and config for parent request
      const { body: processedData, ...restConfig } = processedConfig;

      // Use parent ApiClient for the actual HTTP request
      const response = await super[
        method.toLowerCase() as 'delete' | 'get' | 'patch' | 'post' | 'put'
      ]<T>(endpoint, processedData, restConfig);

      // Post-request security processing
      await this.performPostRequestSecurityChecks(securityContext);

      return response;
    } catch (error) {
      // Enhanced error handling via SecurityComposer and SecurityUtils
      await this.handleSecurityError(error, securityContext);
      throw error;
    }
  }

  /**
   * Update security configuration
   */
  public updateSecurityConfig(newConfig: Partial<SecurityConfig>): void {
    if (this.securityComposer) {
      this.securityComposer.updateConfig(newConfig);
      console.log(
        '🔐 SecureApiClient: Security configuration updated',
        newConfig
      );
    } else {
      console.warn(
        'SecureApiClient: Cannot update config - SecurityComposer not initialized'
      );
    }
  }

  /**
   * Assess current threat level based on security status
   */
  private assessThreatLevel(
    status: any
  ): 'critical' | 'high' | 'low' | 'medium' {
    try {
      // Critical: Security not initialized or major auth issues
      if (!this.securityInitialized || !status.securityFeaturesInitialized) {
        return 'critical';
      }

      // High: Authentication issues
      if (!status.isAuthenticated || !status.hasValidToken) {
        return 'high';
      }

      // Medium: Session or token issues
      const currentToken = SecurityUtils.getSecureItem('auth_token');
      if (
        SecurityUtils.detectTimeout() ||
        (currentToken && SecurityUtils.willExpireSoon(currentToken))
      ) {
        return 'medium';
      }

      // Low: All security features working normally
      return 'low';
    } catch (error) {
      console.warn('SecureApiClient: Failed to assess threat level:', error);
      return 'medium'; // Default to medium on assessment failure
    }
  }

  /**
   * Extract user information from token using SecurityUtils
   */
  private extractUserFromToken(): any {
    try {
      if (!this.securityFeatures?.tokenManagement?.isTokenValid) {
        return undefined;
      }

      // Get current token from secure storage
      const currentToken = SecurityUtils.getSecureItem('auth_token');
      if (!currentToken) {
        return undefined;
      }

      // Use SecurityUtils to extract user information
      const employeeId = SecurityUtils.extractEmployeeId(currentToken);
      const userRole = SecurityUtils.extractUserRole(currentToken);

      return employeeId || userRole ? { employeeId, userRole } : undefined;
    } catch (error) {
      console.warn(
        'SecureApiClient: Failed to extract user from token:',
        error
      );
      return undefined;
    }
  }

  /**
   * Get session information using SecurityUtils
   */
  private getSessionInfo(): any {
    try {
      const sessionId = SecurityUtils.getCurrentSessionId();
      const isTimeout = SecurityUtils.detectTimeout();

      return sessionId ? { isTimeout, sessionId } : undefined;
    } catch (error) {
      console.warn('SecureApiClient: Failed to get session info:', error);
      return undefined;
    }
  }

  /**
   * Enhanced error handling using SecurityComposer and SecurityUtils with circuit breaker integration
   */
  private async handleSecurityError(
    error: unknown,
    context: SecurityContext
  ): Promise<void> {
    // Circuit breaker check for error handling
    if (!SecurityUtils.canPerformSecurityCheck()) {
      console.debug('🔒 Security error handling blocked by circuit breaker');
      return;
    }

    const operationId = 'api-error-handling';
    if (!SecurityUtils.startSecurityOperation(operationId)) {
      console.debug('🔄 Security error handling already in progress');
      return;
    }

    try {
      // Use SecurityComposer for primary error handling
      if (this.securityComposer) {
        await this.securityComposer.handleError(error, context);
      }

      // Additional error handling using SecurityUtils
      if (error instanceof Error) {
        // Handle authentication errors
        if (
          error.message.includes('401') ||
          error.message.includes('Unauthorized') ||
          error.message.includes('Authentication')
        ) {
          console.warn('🔐 SecureApiClient: Authentication error detected');
          SecurityUtils.recordSecurityAttempt();

          // Attempt session recovery first
          const recovered = await this.attemptSessionRecovery();
          if (!recovered) {
            // Clear secure storage on auth failure
            SecurityUtils.clearAllCookies();
            console.log('🧹 Cleared secure storage due to auth failure');
          }
        }

        // Handle CSRF errors
        if (error.message.includes('CSRF') || error.message.includes('403')) {
          console.warn('🛡️ SecureApiClient: CSRF error detected');
          SecurityUtils.recordSecurityAttempt();
          // CSRF token might need refresh - this will be handled by the hook
        }

        // Handle network errors
        if (
          error.message.includes('Network') ||
          error.message.includes('fetch')
        ) {
          console.warn('🌐 SecureApiClient: Network error detected');
          // Don't record as security attempt for network issues
        }

        // Handle timeout errors
        if (
          error.message.includes('timeout') ||
          error.message.includes('Timeout')
        ) {
          console.warn('⏰ SecureApiClient: Timeout error detected');
          SecurityUtils.recordSecurityAttempt();
        }
      }
    } catch (handlingError) {
      console.error(
        'SecureApiClient: Error in security error handling:',
        handlingError
      );
      SecurityUtils.recordSecurityAttempt();
    } finally {
      SecurityUtils.endSecurityOperation(operationId);
    }
  }

  /**
   * Perform post-request security checks using SecurityUtils
   */
  private async performPostRequestSecurityChecks(
    context: SecurityContext
  ): Promise<void> {
    try {
      // Update activity timestamp if session is active
      if (context.isAuthenticated && this.securityFeatures?.sessionSecurity) {
        this.securityFeatures.sessionSecurity.updateActivity();
      }
    } catch (error) {
      console.warn(
        'SecureApiClient: Post-request security check failed:',
        error
      );
      // Don't throw here as the main request succeeded
    }
  }

  /**
   * Perform pre-request security checks using SecurityUtils with circuit breaker integration
   */
  private async performPreRequestSecurityChecks(
    context: SecurityContext
  ): Promise<void> {
    // Circuit breaker check for pre-request security checks
    if (!SecurityUtils.canPerformSecurityCheck()) {
      console.debug(
        '🔒 Pre-request security checks blocked by circuit breaker'
      );
      return;
    }

    const operationId = 'pre-request-security-check';
    if (!SecurityUtils.startSecurityOperation(operationId)) {
      console.debug('🔄 Pre-request security check already in progress');
      return;
    }

    try {
      // Check for session timeout using SecurityUtils
      if (SecurityUtils.detectTimeout()) {
        console.warn('⏰ Session timeout detected in pre-request check');
        SecurityUtils.recordSecurityAttempt();

        // Attempt session recovery before throwing error
        const recovered = await this.attemptSessionRecovery();
        if (!recovered) {
          throw new AuthenticationError(
            'Session has timed out',
            'SESSION_TIMEOUT'
          );
        }
      }

      // Validate token if available using SecurityUtils
      if (context.hasValidToken && this.securityFeatures?.tokenManagement) {
        const { isTokenExpired } = this.securityFeatures.tokenManagement;
        const currentToken = SecurityUtils.getSecureItem('auth_token');

        if (
          isTokenExpired ||
          (currentToken && SecurityUtils.willExpireSoon(currentToken))
        ) {
          console.warn(
            '🔄 SecureApiClient: Token will expire soon, attempting refresh'
          );

          // Attempt token refresh
          try {
            const refreshed =
              await this.securityFeatures.tokenManagement.refreshToken();
            if (refreshed) {
              console.log('✅ Token refreshed successfully');
              SecurityUtils.recordSecuritySuccess();
            } else {
              console.warn('❌ Token refresh failed');
              SecurityUtils.recordSecurityAttempt();
            }
          } catch (refreshError) {
            console.error('❌ Token refresh error:', refreshError);
            SecurityUtils.recordSecurityAttempt();
          }
        }
      }

      // Record successful pre-request check
      SecurityUtils.recordSecuritySuccess();
    } catch (error) {
      console.error(
        'SecureApiClient: Pre-request security check failed:',
        error
      );
      SecurityUtils.recordSecurityAttempt();
      throw error;
    } finally {
      SecurityUtils.endSecurityOperation(operationId);
    }
  }

  /**
   * Attempt session recovery using SessionManager
   */
  private async attemptSessionRecovery(): Promise<boolean> {
    try {
      console.log('🔧 Attempting session recovery...');

      // Import SessionManager dynamically to avoid circular dependencies
      const { SessionManager } = await import('../../security/SessionManager');

      // Attempt integrity check and recovery
      const integrityCheck = await SessionManager.performIntegrityCheck();
      if (integrityCheck) {
        console.log('✅ Session integrity check passed');
        return true;
      }

      // Attempt state recovery
      const recovered = SessionManager.recoverFromCorruptedState();
      if (recovered) {
        console.log('✅ Session state recovered successfully');
        return true;
      }

      console.warn('❌ Session recovery failed');
      return false;
    } catch (error) {
      console.error('❌ Session recovery error:', error);
      return false;
    }
  }

  /**
   * Validate security features using SecurityUtils
   */
  private validateSecurityFeatures(features: SecurityFeatures): void {
    const validationErrors: string[] = [];

    // Validate token management if enabled
    if (this.securityConfig.enableTokenValidation && features.tokenManagement) {
      const { isTokenExpired, isTokenValid } = features.tokenManagement;
      if (
        typeof isTokenValid !== 'boolean' ||
        typeof isTokenExpired !== 'boolean'
      ) {
        validationErrors.push(
          'Token management features must provide boolean status indicators'
        );
      }
    }

    // Validate CSRF protection if enabled
    if (
      this.securityConfig.enableCSRF &&
      features.csrfProtection &&
      typeof features.csrfProtection.attachCSRF !== 'function'
    ) {
      validationErrors.push('CSRF protection must provide attachCSRF function');
    }

    // Validate input sanitization if enabled
    if (
      this.securityConfig.enableInputSanitization &&
      features.inputValidation &&
      typeof features.inputValidation.sanitizeInput !== 'function'
    ) {
      validationErrors.push(
        'Input validation must provide sanitizeInput function'
      );
    }

    // Validate session security if enabled
    if (this.securityConfig.enableAutoLogout && features.sessionSecurity) {
      const { clearSession, isSessionActive } = features.sessionSecurity;
      if (
        typeof isSessionActive !== 'boolean' ||
        typeof clearSession !== 'function'
      ) {
        validationErrors.push(
          'Session security must provide boolean status and clearSession function'
        );
      }
    }

    if (validationErrors.length > 0) {
      throw new Error(
        `Security feature validation failed: ${validationErrors.join(', ')}`
      );
    }
  }
}

/**
 * Factory function to create SecureApiClient with enhanced security configuration
 * Integrates with SecurityUtils and SECURITY_CONSTANTS for optimal security
 */
export function createSecureApiClient(
  config: SecureApiClientConfig,
  securityFeatures?: SecurityFeatures
): SecureApiClient {
  // Default security configuration
  const defaultSecurityConfig = {
    authentication: {
      autoLogout: true,
      enabled: true,
      redirectOnFailure: true,
    },
    csrf: {
      enabled: true,
      excludePaths: [],
      tokenHeader: 'X-CSRF-Token',
    },
    http: {
      baseURL: '/api',
      retryAttempts: 3,
      timeout: 10_000,
    },
    inputSanitization: {
      enabled: true,
      sanitizers: ['xss', 'sql'],
    },
    tokenValidation: {
      autoRefresh: true,
      enabled: true,
      refreshThreshold: SECURITY_CONSTANTS.TOKEN_EXPIRY_THRESHOLD_MINUTES * 60,
    },
  };

  const mergedConfig: SecureApiClientConfig = {
    enableAutoLogout: true,
    // Default security-focused configuration using SECURITY_CONSTANTS
    enableCSRF: true,
    enableInputSanitization: true,
    enableTokenValidation: true,
    validateSecurityFeatures: true,
    // Merge with provided config (user config takes precedence)
    ...config,
    // Deep merge security config if provided
    securityConfig: {
      ...defaultSecurityConfig,
      ...config.securityConfig,
    },
  };

  // Create SecurityComposer if security features are provided
  let securityComposer: SecurityComposer | undefined;
  if (securityFeatures) {
    securityComposer = new SecurityComposer(
      securityFeatures,
      mergedConfig.securityConfig
    );
  }

  const client = new SecureApiClient(mergedConfig, securityComposer);

  console.log(
    '🏭 SecureApiClient Factory: Created enhanced secure API client',
    {
      config: {
        autoLogout: mergedConfig.enableAutoLogout,
        csrf: mergedConfig.enableCSRF,
        inputSanitization: mergedConfig.enableInputSanitization,
        tokenValidation: mergedConfig.enableTokenValidation,
      },
      securityConstants: SECURITY_CONSTANTS,
      securityFeatures: securityFeatures ? Object.keys(securityFeatures) : [],
    }
  );

  return client;
}
