'use client';

import { format, parseISO } from 'date-fns';
import { FileEdit } from 'lucide-react';
import { useParams, useRouter } from 'next/navigation';
import React, { useMemo } from 'react'; // Removed useEffect, useState

import type { TaskFormData } from '@/lib/schemas/taskSchemas';
import type {
  CreateSubtaskData, // Added import
  CreateTaskData,
  Task as DomainTask,
  Subtask,
  TaskPriorityPrisma,
  TaskStatusPrisma,
} from '@/lib/types/domain';

import TaskForm from '@/components/features/tasks/TaskForm';
import { PageHeader } from '@/components/ui/PageHeader';
import { Skeleton } from '@/components/ui/skeleton';
import { usePredefinedEntityToast } from '@/hooks/forms/useFormToast';
import { useTask, useUpdateTask } from '@/lib/stores/queries/useTasks';

// Helper to transform DomainTask to TaskFormData
const mapDomainTaskToFormTask = (task: DomainTask): TaskFormData => {
  return {
    dateTime: task.dateTime
      ? format(parseISO(task.dateTime), "yyyy-MM-dd'T'HH:mm")
      : '',
    deadline: task.deadline
      ? format(parseISO(task.deadline), "yyyy-MM-dd'T'HH:mm")
      : '',
    description: task.description || '',
    driverEmployeeId: task.driverEmployeeId || null, // Ensure null if not present
    estimatedDuration: task.estimatedDuration || 0,
    location: task.location || '',
    notes: task.notes || '',
    priority: task.priority, // Direct mapping
    requiredSkills: task.requiredSkills || [],
    staffEmployeeId: task.staffEmployeeId, // staffEmployeeId is required in TaskFormSchema
    status: task.status.replace('_', ' ') as TaskFormData['status'], // Replace underscores with spaces
    // Corrected casing
    subtasks:
      task.subtasks?.map((s: Subtask) => ({
        completed: s.completed,
        // Explicitly type s as Subtask
        id: s.id,
        taskId: s.taskId, // Add taskId
        title: s.title,
      })) || [],
    vehicleId: task.vehicleId || null,
  };
};

export default function EditTaskPage() {
  const router = useRouter();
  const params = useParams();
  const { showEntityUpdated, showEntityUpdateError } =
    usePredefinedEntityToast('task');

  const taskId = params?.id as string;

  const {
    data: domainTask, // Renamed to avoid confusion, this is DomainTask
    error: taskError,
    isLoading: isTaskLoading,
  } = useTask(taskId);
  const {
    error: updateError,
    isPending: isUpdating,
    mutateAsync: updateTaskMutation,
  } = useUpdateTask();

  // useEffect(() => { // Removed useEffect for error handling, DataLoader handles it
  //   if (taskError) {
  //     console.error('Failed to fetch employee:', taskError);
  //     toast({
  //       title: 'Error Loading Employee',
  //       description:
  //         (taskError as any)?.message || 'Failed to load employee data.',
  //       variant: 'destructive',
  //     });
  //     router.push('/employees');
  //   }
  // }, [taskError, router, toast]);

  const handleSubmit = async (data: TaskFormData) => {
    if (!taskId || !domainTask) return; // Check domainTask

    // Validate required fields
    if (!data.staffEmployeeId) {
      showEntityUpdateError('Staff employee is required for task updates.');
      return;
    }

    try {
      const updateTaskData: Partial<CreateTaskData> = {
        dateTime: data.dateTime,
        deadline: data.deadline,
        description: data.description,
        driverEmployeeId: data.driverEmployeeId || undefined, // Ensure undefined if null
        estimatedDuration: data.estimatedDuration,
        location: data.location,
        notes: data.notes,
        priority: data.priority, // Direct mapping
        requiredSkills: data.requiredSkills,
        staffEmployeeId: data.staffEmployeeId, // Required field, validated above
        status: data.status.replace(' ', '_') as TaskStatusPrisma, // Replace spaces with underscores
        subtasks: data.subtasks?.map((s: CreateSubtaskData) => {
          // Explicitly type s as CreateSubtaskData
          return {
            completed: s.completed ?? false,
            taskId: taskId, // Assign the current task's ID as subtask's taskId
            title: s.title,
          };
        }),
        vehicleId: data.vehicleId || undefined, // Ensure undefined if null
      };

      const cleanedData = Object.fromEntries(
        Object.entries(updateTaskData).filter(
          ([, value]) => value !== undefined
        )
      ) as Partial<CreateTaskData>;

      const updatedTask = await updateTaskMutation({
        data: cleanedData,
        id: taskId,
      });

      if (updatedTask) {
        const taskForToast = {
          title:
            updatedTask.description.slice(0, 30) +
            (updatedTask.description.length > 30 ? '...' : ''),
          name:
            updatedTask.description.slice(0, 30) +
            (updatedTask.description.length > 30 ? '...' : ''),
        };
        showEntityUpdated(taskForToast);
        router.push(`/tasks/${taskId}`);
      } else {
        showEntityUpdateError('Failed to update task (no data returned).');
      }
    } catch (error: any) {
      console.error('Error updating task:', error);
      showEntityUpdateError(
        error.message ||
          updateError?.message ||
          'Failed to update task. Please try again.'
      );
    }
  };

  const initialFormData = useMemo((): TaskFormData | undefined => {
    if (domainTask) {
      return mapDomainTaskToFormTask(domainTask);
    }
    return undefined;
  }, [domainTask]);

  if (isTaskLoading) {
    return (
      <div className="space-y-6">
        <PageHeader icon={FileEdit} title="Loading Task..." />
        <Skeleton className="h-[600px] w-full rounded-lg bg-card" />
      </div>
    );
  }

  if (!domainTask || !initialFormData) {
    return (
      <div className="space-y-6 text-center">
        <PageHeader
          description="The requested task could not be found."
          icon={FileEdit}
          title="Task Not Found"
        />
        <button className="text-blue-500" onClick={() => router.push('/tasks')}>
          Back to Tasks
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <PageHeader
        description="Modify the details for this task."
        icon={FileEdit}
        title={`Edit Task: ${domainTask.description.slice(0, 50)}${
          domainTask.description.length > 50 ? '...' : ''
        }`}
      />
      {updateError && (
        <p className="rounded-md bg-red-100 p-3 text-red-500">
          Error updating: {updateError.message}
        </p>
      )}
      <TaskForm
        initialData={initialFormData}
        isEditing={true}
        isLoading={isUpdating}
        onSubmit={handleSubmit}
      />
    </div>
  );
}
