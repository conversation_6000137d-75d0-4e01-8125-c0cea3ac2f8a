/**
 * DelegationFlightSection Component - SOLID Principles Implementation
 *
 * Single Responsibility: Handles flight details management
 * - Arrival and departure flight information with search functionality
 * - Follows SRP by focusing only on flight details
 *
 * @module DelegationFlightSection
 */

import React, { useState } from 'react';
import { useFormContext } from 'react-hook-form';
import { PlaneLanding, PlaneTakeoff, Search } from 'lucide-react';

import type { DelegationFormData } from '@/lib/schemas/delegationSchemas';

import { ActionButton } from '@/components/ui/action-button';
import { FormField } from '@/components/ui/forms/formField';
import FlightSearchModal from '../FlightSearchModal';

// ============================================================================
// INTERFACES
// ============================================================================

export interface DelegationFlightSectionProps {
  /** Whether the form is currently submitting */
  isSubmitting?: boolean;
  /** Additional CSS classes */
  className?: string;
  /** User's role for permission-based UI */
  userRole?: string;
}

interface FlightSearchResult {
  arrivalAirport?: string;
  arrivalTime?: number;
  callsign: string;
  departureAirport?: string;
  departureTime?: number;
}

// ============================================================================
// COMPONENT IMPLEMENTATION
// ============================================================================

/**
 * Flight Section for Delegation Form
 *
 * Manages arrival and departure flight details with search functionality.
 * This component follows SRP by focusing solely on flight information.
 */
export const DelegationFlightSection: React.FC<
  DelegationFlightSectionProps
> = ({ isSubmitting = false, className = '', userRole = 'user' }) => {
  const { setValue } = useFormContext<DelegationFormData>();

  // Modal state management
  const [isArrivalModalOpen, setIsArrivalModalOpen] = useState(false);
  const [isDepartureModalOpen, setIsDepartureModalOpen] = useState(false);

  const handleFlightSelection = (
    flight: FlightSearchResult,
    type: 'arrival' | 'departure'
  ) => {
    const fieldPrefix =
      type === 'arrival' ? 'flightArrivalDetails' : 'flightDepartureDetails';

    // Set flight details based on type
    if (type === 'arrival' && flight.arrivalTime && flight.arrivalAirport) {
      setValue(`${fieldPrefix}.flightNumber`, flight.callsign);
      setValue(
        `${fieldPrefix}.dateTime`,
        new Date(flight.arrivalTime).toISOString().slice(0, 16)
      );
      setValue(`${fieldPrefix}.airport`, flight.arrivalAirport);
    } else if (
      type === 'departure' &&
      flight.departureTime &&
      flight.departureAirport
    ) {
      setValue(`${fieldPrefix}.flightNumber`, flight.callsign);
      setValue(
        `${fieldPrefix}.dateTime`,
        new Date(flight.departureTime).toISOString().slice(0, 16)
      );
      setValue(`${fieldPrefix}.airport`, flight.departureAirport);
    }

    // Close the modal
    if (type === 'arrival') {
      setIsArrivalModalOpen(false);
    } else {
      setIsDepartureModalOpen(false);
    }
  };

  return (
    <div className={`grid grid-cols-1 gap-6 lg:grid-cols-2 ${className}`}>
      {/* Arrival Flight Section */}
      <section className="space-y-4 rounded-lg border bg-card p-6">
        <div className="flex items-center justify-between">
          <h3 className="flex items-center text-lg font-semibold text-foreground">
            <PlaneLanding className="mr-2 size-5 text-accent" />
            Arrival Flight (Optional)
          </h3>
          <ActionButton
            actionType="tertiary"
            icon={<Search className="size-4" />}
            onClick={() => setIsArrivalModalOpen(true)}
            size="sm"
            type="button"
            disabled={isSubmitting}
          >
            Search Flights
          </ActionButton>
        </div>

        <FormField
          label="Flight Number"
          name="flightArrivalDetails.flightNumber"
          disabled={isSubmitting}
        />
        <FormField
          label="Date & Time"
          name="flightArrivalDetails.dateTime"
          type="datetime-local"
          disabled={isSubmitting}
        />
        <FormField
          label="Airport"
          name="flightArrivalDetails.airport"
          disabled={isSubmitting}
        />
        <FormField
          label="Terminal (Optional)"
          name="flightArrivalDetails.terminal"
          disabled={isSubmitting}
        />
        <FormField
          label="Notes (Optional)"
          name="flightArrivalDetails.notes"
          type="textarea"
          disabled={isSubmitting}
        />
      </section>

      {/* Departure Flight Section */}
      <section className="space-y-4 rounded-lg border bg-card p-6">
        <div className="flex items-center justify-between">
          <h3 className="flex items-center text-lg font-semibold text-foreground">
            <PlaneTakeoff className="mr-2 size-5 text-accent" />
            Departure Flight (Optional)
          </h3>
          <ActionButton
            actionType="tertiary"
            icon={<Search className="size-4" />}
            onClick={() => setIsDepartureModalOpen(true)}
            size="sm"
            type="button"
            disabled={isSubmitting}
          >
            Search Flights
          </ActionButton>
        </div>

        <FormField
          label="Flight Number"
          name="flightDepartureDetails.flightNumber"
          disabled={isSubmitting}
        />
        <FormField
          label="Date & Time"
          name="flightDepartureDetails.dateTime"
          type="datetime-local"
          disabled={isSubmitting}
        />
        <FormField
          label="Airport"
          name="flightDepartureDetails.airport"
          disabled={isSubmitting}
        />
        <FormField
          label="Terminal (Optional)"
          name="flightDepartureDetails.terminal"
          disabled={isSubmitting}
        />
        <FormField
          label="Notes (Optional)"
          name="flightDepartureDetails.notes"
          type="textarea"
          disabled={isSubmitting}
        />
      </section>

      {/* Flight Search Modals */}
      <FlightSearchModal
        isOpen={isArrivalModalOpen}
        onClose={() => setIsArrivalModalOpen(false)}
        onSelectFlight={flight => handleFlightSelection(flight, 'arrival')}
        type="arrival"
      />

      <FlightSearchModal
        isOpen={isDepartureModalOpen}
        onClose={() => setIsDepartureModalOpen(false)}
        onSelectFlight={flight => handleFlightSelection(flight, 'departure')}
        type="departure"
      />
    </div>
  );
};

export default DelegationFlightSection;
