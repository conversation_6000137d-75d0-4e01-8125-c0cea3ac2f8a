import { supabaseAdmin } from '../lib/supabase.js';
import HttpError from '../utils/HttpError.js';
import logger from '../utils/logger.js';
import { createAuditLogEntry } from './auditLog.service.js'; // Import from auditLog.service.js

interface AuditDetails {
  ipAddress?: string;
  userAgent?: string;
  userId: string;
}

interface GetAllUsersResult {
  data: UserProfile[];
  pagination: Pagination;
}

interface Pagination {
  hasNext: boolean;
  hasPrev: boolean;
  limit: number;
  page: number;
  total: number;
  totalPages: number;
}

// Define interfaces for user management
interface UserProfile {
  created_at: string;
  employee_id: null | string;
  id: string;
  is_active: boolean;
  role: string;
  updated_at: string;
  users: {
    email: string;
    email_confirmed_at: null | string;
  }[];
}

const VALID_ROLES = ['SUPER_ADMIN', 'ADMIN', 'MANAGER', 'EMPLOYEE', 'USER'] as const;
type UserRole = (typeof VALID_ROLES)[number];

function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

function validateRole(role: string): role is UserRole {
  return (VALID_ROLES as readonly string[]).includes(role);
}

/**
 * Get all users with pagination and search
 * @param page Current page number
 * @param limit Number of items per page
 * @param search Search query for email or role
 * @param role Filter by role
 * @param isActive Filter by active status
 * @returns Paginated list of user profiles
 */
export const getAllUsers = async (
  page = 1,
  limit = 10,
  search = '',
  role = '',
  isActive?: boolean,
): Promise<GetAllUsersResult> => {
  logger.debug('Attempting to fetch users with parameters', {
    isActive,
    limit,
    page,
    role,
    search,
    service: 'userManagement-service',
  });

  // Use Supabase Admin API to list users (cannot query auth.users directly)
  logger.debug('Fetching users using Supabase Admin API...');

  try {
    const { data: authUsers, error } = await supabaseAdmin.auth.admin.listUsers({
      page: Number(page),
      perPage: Number(limit),
    });

    if (error) {
      logger.error('Supabase Admin API failed to fetch users', {
        errorCode: error.code,
        errorMessage: error.message,
        service: 'userManagement-service',
      });
      throw new HttpError(`Failed to fetch users from database: ${error.message}`, 500);
    }

    logger.debug('Supabase Admin API result:', {
      sampleUser: authUsers?.users?.[0]
        ? {
            created_at: authUsers.users[0].created_at,
            email: authUsers.users[0].email,
            email_confirmed_at: authUsers.users[0].email_confirmed_at,
            id: authUsers.users[0].id,
            updated_at: authUsers.users[0].updated_at,
            user_metadata: authUsers.users[0].user_metadata,
          }
        : null,
      service: 'userManagement-service',
      userCount: authUsers?.users?.length || 0,
    });

    // Filter users based on criteria (since Admin API doesn't support filtering)
    let filteredUsers = authUsers?.users || [];

    // Apply role filter
    if (role) {
      filteredUsers = filteredUsers.filter(user => user.user_metadata?.role === role);
    }

    // Apply active status filter
    if (isActive !== undefined) {
      filteredUsers = filteredUsers.filter(user => {
        const userIsActive = user.user_metadata?.is_active ?? true;
        return userIsActive === isActive;
      });
    }

    // Apply search filter
    if (search) {
      const searchLower = search.toLowerCase();
      filteredUsers = filteredUsers.filter(
        user =>
          user.email?.toLowerCase().includes(searchLower) ||
          (user.user_metadata?.role || '').toLowerCase().includes(searchLower),
      );
    }

    // Calculate pagination for filtered results
    const totalCount = filteredUsers.length;
    const offset = (Number(page) - 1) * Number(limit);
    const paginatedUsers = filteredUsers.slice(offset, offset + Number(limit));

    // Transform paginated users data to UserProfile format
    // Note: Supabase Admin API returns user objects with direct properties
    const transformedUsers = paginatedUsers.map(user => {
      logger.debug('Transforming user from Admin API:', {
        created_at: user.created_at,
        email: user.email,
        service: 'userManagement-service',
        updated_at: user.updated_at,
        user_metadata: user.user_metadata,
        userId: user.id,
      });

      return {
        created_at: user.created_at || new Date().toISOString(),
        employee_id: user.user_metadata?.employee_id || null,
        id: user.id,
        is_active: user.user_metadata?.is_active ?? true,
        role: user.user_metadata?.role || 'USER',
        updated_at: user.updated_at || user.created_at || new Date().toISOString(),
        users: [
          {
            email: user.email || '',
            email_confirmed_at: user.email_confirmed_at || null,
          },
        ],
      };
    });

    const totalPages = Math.ceil(totalCount / Number(limit));

    logger.debug('User data transformation complete.', {
      filteredCount: totalCount,
      service: 'userManagement-service',
      totalUsers: authUsers.users.length,
      transformedUserCount: transformedUsers.length,
    });

    return {
      data: transformedUsers,
      pagination: {
        hasNext: Number(page) < totalPages,
        hasPrev: Number(page) > 1,
        limit: Number(limit),
        page: Number(page),
        total: totalCount,
        totalPages,
      },
    };
  } catch (error) {
    logger.error('Error in getAllUsers:', {
      error: error instanceof Error ? error.message : 'Unknown error',
      service: 'userManagement-service',
    });
    throw error;
  }
};

/**
 * Create a new user
 * @param email User's email
 * @param role User's role
 * @param isActive User's active status
 * @param emailVerified Whether user's email is verified
 * @param auditDetails Audit details for logging
 * @returns Created user profile
 */
export const createUser = async (
  email: string,
  role: string,
  isActive = true,
  emailVerified = false,
  auditDetails: AuditDetails,
): Promise<UserProfile> => {
  logger.info(`Creating new user: ${email} with role: ${role}`);

  if (!validateEmail(email)) {
    throw new HttpError('Invalid email format', 400);
  }
  if (!validateRole(role)) {
    throw new HttpError(`Invalid role. Must be one of: ${VALID_ROLES.join(', ')}`, 400);
  }

  // Create user with metadata in auth.users.raw_user_meta_data
  const { data: authUser, error: authError } = await supabaseAdmin.auth.admin.createUser({
    email,
    email_confirm: emailVerified,
    user_metadata: {
      employee_id: null,
      is_active: isActive,
      role,
    },
  });

  if (authError) {
    throw new HttpError(`Failed to create user: ${authError.message}`, 400);
  }

  // Create audit log entry
  await createAuditLogEntry(
    auditDetails.userId,
    'CREATE_USER',
    `Created user: ${email} with role: ${role}`,
    auditDetails.ipAddress,
    auditDetails.userAgent,
  );

  // Return user profile in expected format
  const userProfile: UserProfile = {
    created_at: authUser.user.created_at,
    employee_id: null,
    id: authUser.user.id,
    is_active: isActive,
    role,
    updated_at: authUser.user.updated_at || authUser.user.created_at,
    users: [
      {
        email: authUser.user.email || email,
        email_confirmed_at: authUser.user.email_confirmed_at || null,
      },
    ],
  };

  logger.info(`Successfully created user: ${email} with ID: ${authUser.user.id}`);
  return userProfile;
};

/**
 * Update user details
 * @param id User ID
 * @param email New email (optional)
 * @param role New role (optional)
 * @param isActive New active status (optional)
 * @param emailVerified New email verified status (optional)
 * @param auditDetails Audit details for logging
 * @returns Updated user profile
 */
export const updateUser = async (
  id: string,
  email?: string,
  role?: string,
  isActive?: boolean,
  emailVerified?: boolean,
  auditDetails?: AuditDetails,
): Promise<UserProfile> => {
  logger.info(`Updating user: ${id}`);

  // Get current user data using Admin API
  const { data: currentUser, error: getUserError } = await supabaseAdmin.auth.admin.getUserById(id);

  if (getUserError || !currentUser?.user) {
    throw new HttpError(`User not found: ${id}`, 404);
  }

  const user = currentUser.user;

  // Prepare auth update data
  const authUpdateData: any = {};
  if (email !== undefined) authUpdateData.email = email;
  if (emailVerified !== undefined) authUpdateData.email_confirm = emailVerified;

  // Update user_metadata with new values
  const currentMetadata = user.user_metadata || {};
  const updatedMetadata = {
    ...currentMetadata,
    employee_id: currentMetadata.employee_id || null,
    is_active: isActive !== undefined ? isActive : (currentMetadata.is_active ?? true),
    role: role !== undefined ? role : currentMetadata.role || 'USER',
  };

  authUpdateData.user_metadata = updatedMetadata;

  // Update auth.users
  const { error: authError } = await supabaseAdmin.auth.admin.updateUserById(id, authUpdateData);

  if (authError) {
    throw new HttpError(`Failed to update user: ${authError.message}`, 500);
  }

  // Create audit log entry
  if (auditDetails) {
    await createAuditLogEntry(
      auditDetails.userId,
      'UPDATE_USER',
      `Updated user: ${user.email || id}`,
      auditDetails.ipAddress,
      auditDetails.userAgent,
    );
  }

  // Return updated user profile
  const userProfile: UserProfile = {
    created_at: user.created_at,
    employee_id: updatedMetadata.employee_id,
    id: user.id,
    is_active: updatedMetadata.is_active,
    role: updatedMetadata.role,
    updated_at: new Date().toISOString(),
    users: [
      {
        email: email || user.email || '',
        email_confirmed_at: user.email_confirmed_at || null,
      },
    ],
  };

  logger.info(`Successfully updated user: ${id}`);
  return userProfile;
};

/**
 * Delete a user
 * @param id User ID
 * @param auditDetails Audit details for logging
 */
export const deleteUser = async (id: string, auditDetails?: AuditDetails): Promise<void> => {
  logger.info(`Deleting user: ${id}`);

  // Get user email for audit log before deletion
  const { data: authUser } = await supabaseAdmin.auth.admin.getUserById(id);

  // Delete user from auth.users (this is the only place user data exists now)
  const { error: authError } = await supabaseAdmin.auth.admin.deleteUser(id);

  if (authError) {
    throw new HttpError(`Failed to delete user: ${authError.message}`, 500);
  }

  // Create audit log entry
  if (auditDetails) {
    await createAuditLogEntry(
      auditDetails.userId,
      'DELETE_USER',
      `Deleted user: ${authUser?.user?.email || 'unknown'}`,
      auditDetails.ipAddress,
      auditDetails.userAgent,
    );
  }

  logger.info(`Successfully deleted user: ${id}`);
};

/**
 * Toggle user activation status
 * @param id User ID
 * @param isActive New activation status
 * @param auditDetails Audit details for logging
 * @returns Updated user profile
 */
export const toggleUserActivation = async (
  id: string,
  isActive: boolean,
  auditDetails?: AuditDetails,
): Promise<UserProfile> => {
  logger.info(`Toggling user activation: ${id} to ${String(isActive)}`);

  // Get current user data
  const { data: currentUser, error: getUserError } = await supabaseAdmin.auth.admin.getUserById(id);

  if (getUserError || !currentUser?.user) {
    throw new HttpError(`User not found: ${id}`, 404);
  }

  const user = currentUser.user;

  // Update user_metadata with new activation status
  const currentMetadata = user.user_metadata || {};
  const updatedMetadata = {
    ...currentMetadata,
    employee_id: currentMetadata.employee_id || null,
    is_active: isActive,
    role: currentMetadata.role || 'USER',
  };

  // Update auth.users
  const { error: authError } = await supabaseAdmin.auth.admin.updateUserById(id, {
    user_metadata: updatedMetadata,
  });

  if (authError) {
    throw new HttpError(`Failed to update user activation: ${authError.message}`, 500);
  }

  // Create audit log entry
  if (auditDetails) {
    await createAuditLogEntry(
      auditDetails.userId,
      'TOGGLE_USER_ACTIVATION',
      `${isActive ? 'Activated' : 'Deactivated'} user: ${user.email || 'unknown'}`,
      auditDetails.ipAddress,
      auditDetails.userAgent,
    );
  }

  // Return updated user profile
  const userProfile: UserProfile = {
    created_at: user.created_at,
    employee_id: updatedMetadata.employee_id,
    id: user.id,
    is_active: isActive,
    role: updatedMetadata.role,
    updated_at: new Date().toISOString(),
    users: [
      {
        email: user.email || '',
        email_confirmed_at: user.email_confirmed_at || null,
      },
    ],
  };

  logger.info(`Successfully toggled user activation: ${id} to ${String(isActive)}`);
  return userProfile;
};
