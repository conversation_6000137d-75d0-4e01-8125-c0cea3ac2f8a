'use client';

import React, { useState } from 'react';
import {
  Filter,
  X,
  Search,
  CalendarDays,
  CheckCircle,
  Flag,
  Users,
  MapPin,
  Briefcase,
  Car,
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Calendar as CalendarComponent } from '@/components/ui/calendar';
import { Separator } from '@/components/ui/separator';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/sheet';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';
import type { DateRange } from 'react-day-picker';

// Delegation filter values interface
export interface DelegationFilterValues {
  search: string;
  status: string[];
  dateRange: {
    from?: Date | undefined;
    to?: Date | undefined;
  };
  location: string[];
  drivers: string[];
  escorts: string[];
  vehicles: string[];
}

// Employee interface for filtering
interface EmployeeOption {
  id: string;
  name: string;
  role: string;
}

// Vehicle interface for filtering
interface VehicleOption {
  id: string;
  name: string;
  type: string;
}

// DelegationFilters props interface
interface DelegationFiltersProps {
  onFiltersChange?: (filters: DelegationFilterValues) => void;
  className?: string;
  initialFilters?: Partial<DelegationFilterValues>;
  employeesList?: EmployeeOption[];
  vehiclesList?: VehicleOption[];
  locationsList?: string[];
}

// Delegation status options
const DELEGATION_STATUS_OPTIONS = [
  {
    value: 'Planned',
    label: 'Planned',
    icon: Flag,
    color: 'border-blue-200 text-blue-700',
  },
  {
    value: 'Confirmed',
    label: 'Confirmed',
    icon: CheckCircle,
    color: 'border-green-200 text-green-700',
  },
  {
    value: 'In_Progress',
    label: 'In Progress',
    icon: Flag,
    color: 'border-yellow-200 text-yellow-700',
  },
  {
    value: 'Completed',
    label: 'Completed',
    icon: CheckCircle,
    color: 'border-emerald-200 text-emerald-700',
  },
  {
    value: 'Cancelled',
    label: 'Cancelled',
    icon: X,
    color: 'border-red-200 text-red-700',
  },
  {
    value: 'No_details',
    label: 'No Details',
    icon: Flag,
    color: 'border-gray-200 text-gray-700',
  },
];

/**
 * Modern delegation filters component with popover and sheet layouts
 */
export const DelegationFilters: React.FC<DelegationFiltersProps> = ({
  onFiltersChange,
  className,
  initialFilters = {},
  employeesList = [],
  vehiclesList = [],
  locationsList = [],
}) => {
  const [isSheetOpen, setIsSheetOpen] = useState(false);

  // Initialize filters with defaults
  const [filters, setFilters] = useState<DelegationFilterValues>({
    search: '',
    status: [],
    dateRange: {},
    location: [],
    drivers: [],
    escorts: [],
    vehicles: [],
    ...initialFilters,
  });

  // Update filters and notify parent
  const updateFilters = (newFilters: Partial<DelegationFilterValues>) => {
    const updatedFilters = { ...filters, ...newFilters };
    setFilters(updatedFilters);
    onFiltersChange?.(updatedFilters);
  };

  // Clear all filters
  const clearAllFilters = () => {
    const clearedFilters: DelegationFilterValues = {
      search: '',
      status: [],
      dateRange: {},
      location: [],
      drivers: [],
      escorts: [],
      vehicles: [],
    };
    setFilters(clearedFilters);
    onFiltersChange?.(clearedFilters);
  };

  // Toggle status filter
  const toggleStatus = (status: string) => {
    const newStatus = filters.status.includes(status)
      ? filters.status.filter(s => s !== status)
      : [...filters.status, status];
    updateFilters({ status: newStatus });
  };

  // Toggle location filter
  const toggleLocation = (location: string) => {
    const newLocation = filters.location.includes(location)
      ? filters.location.filter(l => l !== location)
      : [...filters.location, location];
    updateFilters({ location: newLocation });
  };

  // Toggle driver filter
  const toggleDriver = (driverId: string) => {
    const newDrivers = filters.drivers.includes(driverId)
      ? filters.drivers.filter(d => d !== driverId)
      : [...filters.drivers, driverId];
    updateFilters({ drivers: newDrivers });
  };

  // Toggle escort filter
  const toggleEscort = (escortId: string) => {
    const newEscorts = filters.escorts.includes(escortId)
      ? filters.escorts.filter(e => e !== escortId)
      : [...filters.escorts, escortId];
    updateFilters({ escorts: newEscorts });
  };

  // Toggle vehicle filter
  const toggleVehicle = (vehicleId: string) => {
    const newVehicles = filters.vehicles.includes(vehicleId)
      ? filters.vehicles.filter(v => v !== vehicleId)
      : [...filters.vehicles, vehicleId];
    updateFilters({ vehicles: newVehicles });
  };

  // Handle date range selection
  const handleDateRangeSelect = (range: DateRange | undefined) => {
    updateFilters({
      dateRange: {
        from: range?.from ?? undefined,
        to: range?.to ?? undefined,
      },
    });
  };

  // Count active filters
  const activeFiltersCount =
    (filters.search ? 1 : 0) +
    filters.status.length +
    filters.location.length +
    filters.drivers.length +
    filters.escorts.length +
    filters.vehicles.length +
    (filters.dateRange.from || filters.dateRange.to ? 1 : 0);

  // Render status filter popover
  const StatusFilterPopover = () => (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="outline" className="gap-2">
          <CheckCircle className="size-4" />
          Status
          {filters.status.length > 0 && (
            <Badge
              variant="secondary"
              className="ml-1 h-5 min-w-5 px-1.5 text-xs"
            >
              {filters.status.length}
            </Badge>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-56 p-3" align="start">
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h4 className="font-medium text-sm">Delegation Status</h4>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => updateFilters({ status: [] })}
              className="h-auto p-1 text-xs"
            >
              Clear
            </Button>
          </div>
          <Separator />
          <div className="space-y-2">
            {DELEGATION_STATUS_OPTIONS.map(option => {
              const IconComponent = option.icon;
              return (
                <div key={option.value} className="flex items-center gap-2">
                  <Checkbox
                    id={`status-${option.value}`}
                    checked={filters.status.includes(option.value)}
                    onCheckedChange={() => toggleStatus(option.value)}
                  />
                  <Label
                    htmlFor={`status-${option.value}`}
                    className="flex items-center gap-2 cursor-pointer text-sm flex-1"
                  >
                    <IconComponent className="size-3" />
                    <Badge
                      variant="outline"
                      className={cn('text-xs border', option.color)}
                    >
                      {option.label}
                    </Badge>
                  </Label>
                </div>
              );
            })}
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );

  // Render date range filter popover
  const DateRangeFilterPopover = () => {
    return (
      <Popover>
        <PopoverTrigger asChild>
          <Button variant="outline" className="gap-2">
            <CalendarDays className="size-4" />
            Date Range
            {(filters.dateRange.from || filters.dateRange.to) && (
              <Badge
                variant="secondary"
                className="ml-1 h-5 min-w-5 px-1.5 text-xs"
              >
                1
              </Badge>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <div className="p-3">
            <div className="flex items-center justify-between mb-3">
              <h4 className="font-medium text-sm">Delegation Date Range</h4>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => updateFilters({ dateRange: {} })}
                className="h-auto p-1 text-xs"
              >
                Clear
              </Button>
            </div>
            <CalendarComponent
              mode="range"
              selected={{
                from: filters.dateRange.from,
                to: filters.dateRange.to,
              }}
              onSelect={handleDateRangeSelect}
              numberOfMonths={2}
              className="rounded-md border-0"
            />
            {/* Helper text */}
            <div className="mt-3 text-xs text-muted-foreground text-center">
              {filters.dateRange.from && !filters.dateRange.to
                ? 'Select end date to complete range'
                : 'Click start date, then end date'}
            </div>
          </div>
        </PopoverContent>
      </Popover>
    );
  };

  // Render location filter popover
  const LocationFilterPopover = () => (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="outline" className="gap-2">
          <MapPin className="size-4" />
          Location
          {filters.location.length > 0 && (
            <Badge
              variant="secondary"
              className="ml-1 h-5 min-w-5 px-1.5 text-xs"
            >
              {filters.location.length}
            </Badge>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-64 p-3" align="start">
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h4 className="font-medium text-sm">Delegation Location</h4>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => updateFilters({ location: [] })}
              className="h-auto p-1 text-xs"
            >
              Clear
            </Button>
          </div>
          <Separator />
          <div className="space-y-2 max-h-48 overflow-y-auto">
            {locationsList.map(location => (
              <div key={location} className="flex items-center gap-2">
                <Checkbox
                  id={`location-${location}`}
                  checked={filters.location.includes(location)}
                  onCheckedChange={() => toggleLocation(location)}
                />
                <Label
                  htmlFor={`location-${location}`}
                  className="flex items-center gap-2 cursor-pointer text-sm flex-1"
                >
                  <MapPin className="size-3" />
                  <span>{location}</span>
                </Label>
              </div>
            ))}
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );

  // Render drivers filter popover
  const DriversFilterPopover = () => (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="outline" className="gap-2">
          <Users className="size-4" />
          Drivers
          {filters.drivers.length > 0 && (
            <Badge
              variant="secondary"
              className="ml-1 h-5 min-w-5 px-1.5 text-xs"
            >
              {filters.drivers.length}
            </Badge>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-64 p-3" align="start">
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h4 className="font-medium text-sm">Assigned Drivers</h4>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => updateFilters({ drivers: [] })}
              className="h-auto p-1 text-xs"
            >
              Clear
            </Button>
          </div>
          <Separator />
          <div className="space-y-2 max-h-48 overflow-y-auto">
            {employeesList
              .filter(emp => emp.role === 'driver')
              .map(driver => (
                <div key={driver.id} className="flex items-center gap-2">
                  <Checkbox
                    id={`driver-${driver.id}`}
                    checked={filters.drivers.includes(driver.id)}
                    onCheckedChange={() => toggleDriver(driver.id)}
                  />
                  <Label
                    htmlFor={`driver-${driver.id}`}
                    className="flex items-center gap-2 cursor-pointer text-sm flex-1"
                  >
                    <Users className="size-3" />
                    <div className="flex flex-col">
                      <span>{driver.name}</span>
                      <span className="text-xs text-muted-foreground capitalize">
                        {driver.role}
                      </span>
                    </div>
                  </Label>
                </div>
              ))}
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );

  // Render vehicles filter popover
  const VehiclesFilterPopover = () => (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="outline" className="gap-2">
          <Car className="size-4" />
          Vehicles
          {filters.vehicles.length > 0 && (
            <Badge
              variant="secondary"
              className="ml-1 h-5 min-w-5 px-1.5 text-xs"
            >
              {filters.vehicles.length}
            </Badge>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-64 p-3" align="start">
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h4 className="font-medium text-sm">Assigned Vehicles</h4>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => updateFilters({ vehicles: [] })}
              className="h-auto p-1 text-xs"
            >
              Clear
            </Button>
          </div>
          <Separator />
          <div className="space-y-2 max-h-48 overflow-y-auto">
            {vehiclesList.map(vehicle => (
              <div key={vehicle.id} className="flex items-center gap-2">
                <Checkbox
                  id={`vehicle-${vehicle.id}`}
                  checked={filters.vehicles.includes(vehicle.id)}
                  onCheckedChange={() => toggleVehicle(vehicle.id)}
                />
                <Label
                  htmlFor={`vehicle-${vehicle.id}`}
                  className="flex items-center gap-2 cursor-pointer text-sm flex-1"
                >
                  <Car className="size-3" />
                  <div className="flex flex-col">
                    <span>{vehicle.name}</span>
                    <span className="text-xs text-muted-foreground">
                      {vehicle.type}
                    </span>
                  </div>
                </Label>
              </div>
            ))}
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );

  return (
    <div className={cn('flex flex-col gap-4', className)}>
      {/* Search Bar */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 size-4 -translate-y-1/2 text-muted-foreground" />
        <Input
          placeholder="Search delegations (Event, Location, Delegate, Status...)"
          value={filters.search}
          onChange={e => updateFilters({ search: e.target.value })}
          className="pl-10"
        />
      </div>

      {/* Filter Controls */}
      <div className="flex flex-wrap items-center gap-3">
        {/* Desktop Filter Buttons */}
        <div className="hidden md:flex items-center gap-2">
          <StatusFilterPopover />
          <DateRangeFilterPopover />
          {locationsList.length > 0 && <LocationFilterPopover />}
          {employeesList.some(emp => emp.role === 'driver') && (
            <DriversFilterPopover />
          )}
          {vehiclesList.length > 0 && <VehiclesFilterPopover />}
        </div>

        {/* Mobile Filter Sheet */}
        <div className="md:hidden">
          <Sheet open={isSheetOpen} onOpenChange={setIsSheetOpen}>
            <SheetTrigger asChild>
              <Button variant="outline" className="gap-2">
                <Filter className="size-4" />
                Filters
                {activeFiltersCount > 0 && (
                  <Badge
                    variant="secondary"
                    className="ml-1 h-5 min-w-5 px-1.5 text-xs"
                  >
                    {activeFiltersCount}
                  </Badge>
                )}
              </Button>
            </SheetTrigger>
            <SheetContent
              side="bottom"
              className="max-h-[80vh] overflow-y-auto"
            >
              <SheetHeader>
                <SheetTitle>Filter Delegations</SheetTitle>
                <SheetDescription>
                  Apply filters to find specific delegations
                </SheetDescription>
              </SheetHeader>
              <div className="mt-6 space-y-6">
                {/* Mobile Status Filter */}
                <div className="space-y-3">
                  <Label className="text-sm font-medium">Status</Label>
                  <div className="grid gap-2">
                    {DELEGATION_STATUS_OPTIONS.map(option => {
                      const IconComponent = option.icon;
                      return (
                        <div
                          key={option.value}
                          className="flex items-center gap-2 p-2 border rounded-md"
                        >
                          <Checkbox
                            id={`mobile-status-${option.value}`}
                            checked={filters.status.includes(option.value)}
                            onCheckedChange={() => toggleStatus(option.value)}
                          />
                          <Label
                            htmlFor={`mobile-status-${option.value}`}
                            className="cursor-pointer text-sm flex-1 flex items-center gap-2"
                          >
                            <IconComponent className="size-3" />
                            {option.label}
                          </Label>
                        </div>
                      );
                    })}
                  </div>
                </div>

                {/* Mobile Date Range Filter */}
                <div className="space-y-3">
                  <Label className="text-sm font-medium">Date Range</Label>
                  <div className="border rounded-md p-3">
                    <CalendarComponent
                      mode="range"
                      selected={{
                        from: filters.dateRange.from,
                        to: filters.dateRange.to,
                      }}
                      onSelect={handleDateRangeSelect}
                      numberOfMonths={1}
                      className="rounded-md border-0"
                    />
                  </div>
                </div>

                {/* Mobile Clear All Button */}
                {activeFiltersCount > 0 && (
                  <Button
                    variant="outline"
                    onClick={clearAllFilters}
                    className="w-full gap-2"
                  >
                    <X className="size-4" />
                    Clear All Filters ({activeFiltersCount})
                  </Button>
                )}
              </div>
            </SheetContent>
          </Sheet>
        </div>

        {/* Clear All Filters (Desktop) */}
        {activeFiltersCount > 0 && (
          <Button
            variant="ghost"
            size="sm"
            onClick={clearAllFilters}
            className="gap-1 text-muted-foreground hover:text-foreground hidden md:flex"
          >
            <X className="size-3" />
            Clear ({activeFiltersCount})
          </Button>
        )}
      </div>

      {/* Active Filters Display */}
      {activeFiltersCount > 0 && (
        <div className="flex flex-wrap items-center gap-2">
          <span className="text-sm text-muted-foreground">Active filters:</span>

          {filters.status.map(status => {
            const option = DELEGATION_STATUS_OPTIONS.find(
              opt => opt.value === status
            );
            if (!option) return null;
            return (
              <Badge key={status} variant="secondary" className="gap-1">
                {option.label}
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-auto p-0 ml-1 hover:bg-transparent"
                  onClick={() => toggleStatus(status)}
                >
                  <X className="size-3" />
                </Button>
              </Badge>
            );
          })}

          {filters.location.map(location => (
            <Badge key={location} variant="secondary" className="gap-1">
              Location: {location}
              <Button
                variant="ghost"
                size="sm"
                className="h-auto p-0 ml-1 hover:bg-transparent"
                onClick={() => toggleLocation(location)}
              >
                <X className="size-3" />
              </Button>
            </Badge>
          ))}

          {filters.drivers.map(driverId => {
            const driver = employeesList.find(emp => emp.id === driverId);
            return (
              <Badge key={driverId} variant="secondary" className="gap-1">
                Driver: {driver?.name || 'Unknown'}
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-auto p-0 ml-1 hover:bg-transparent"
                  onClick={() => toggleDriver(driverId)}
                >
                  <X className="size-3" />
                </Button>
              </Badge>
            );
          })}

          {filters.vehicles.map(vehicleId => {
            const vehicle = vehiclesList.find(v => v.id === vehicleId);
            return (
              <Badge key={vehicleId} variant="secondary" className="gap-1">
                Vehicle: {vehicle?.name || 'Unknown'}
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-auto p-0 ml-1 hover:bg-transparent"
                  onClick={() => toggleVehicle(vehicleId)}
                >
                  <X className="size-3" />
                </Button>
              </Badge>
            );
          })}

          {(filters.dateRange.from || filters.dateRange.to) && (
            <Badge variant="secondary" className="gap-1">
              Date:{' '}
              {filters.dateRange.from
                ? format(filters.dateRange.from, 'MMM d')
                : '?'}{' '}
              -{' '}
              {filters.dateRange.to
                ? format(filters.dateRange.to, 'MMM d, yyyy')
                : '?'}
              <Button
                variant="ghost"
                size="sm"
                className="h-auto p-0 ml-1 hover:bg-transparent"
                onClick={() => updateFilters({ dateRange: {} })}
              >
                <X className="size-3" />
              </Button>
            </Badge>
          )}
        </div>
      )}
    </div>
  );
};

// Export as default to maintain compatibility
export default DelegationFilters;
