# =============================================================================
# PHASE 1 SECURITY HARDENING: Generated Secure Environment Configuration
# =============================================================================
#
# 🔐 Generated on: 2025-05-24T03:25:36.595Z
# 🚨 SECURITY WARNING: These are real secrets - handle with care!
# 📋 Copy required values to your .env file
#
# =============================================================================

# =============================================================================
# SECURITY SECRETS (GENERATED)
# =============================================================================

# JWT_SECRET - REQUIRED (32+ chars)
JWT_SECRET=K+xnK2XVcNwCAzyl9ZtDmyGaM9eEQ67DeFby4svlRZo=

# API_SECRET - REQUIRED (32+ chars)
API_SECRET=9aUEckG6pkIhVQMAsVpSMDtqCMgQ85Ee5+7nvML0j5k=

# SESSION_SECRET - REQUIRED (32+ chars)
SESSION_SECRET=cOxikNEhnzmmlfCre44DzHyI+Bxk5NEYEnTbLtrRdSI=

# ENCRYPTION_KEY - REQUIRED (32+ chars)
ENCRYPTION_KEY=xLMiuvR/i60KkKFyeFteBM/W5Kzthk3VLsrmffvCVnA=

# =============================================================================
# RECOMMENDED SECRETS (OPTIONAL)
# =============================================================================

# RATE_LIMIT_SECRET - RECOMMENDED (32+ chars)
RATE_LIMIT_SECRET=ElO1WRuV40LP5C9e/0VP+q59C/ESiMzsu3A6pmNpht4=

# WEBHOOK_SECRET - RECOMMENDED (32+ chars)
WEBHOOK_SECRET=Lx00zCyaJTxg2oxrWS/kNP7luzf2j26eYYvt0B/fQV8=

# =============================================================================
# CONFIGURATION TEMPLATE
# =============================================================================
# Copy your existing configuration values below:

# Database Configuration
USE_SUPABASE=true
DATABASE_URL=REPLACE_WITH_YOUR_DATABASE_URL

# Supabase Configuration
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_ANON_KEY=REPLACE_WITH_YOUR_SUPABASE_ANON_KEY
SUPABASE_SERVICE_ROLE_KEY=REPLACE_WITH_YOUR_SUPABASE_SERVICE_ROLE_KEY

# Application Configuration
PORT=3001
NODE_ENV=development
LOG_LEVEL=info

# CORS Configuration
FRONTEND_URL=http://localhost:3000,http://localhost:9002

# Rate Limiting Configuration
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Redis Configuration (for distributed rate limiting and request deduplication)
# REDIS_URL=redis://your-redis-host:6379

# Request Deduplication Configuration
REQUEST_DEDUP_ENABLED=true
REQUEST_DEDUP_DEFAULT_TTL=300

# Service-Specific Deduplication TTL Settings (in seconds)
ADMIN_DEDUP_TTL=300
API_DEDUP_TTL=60
PERFORMANCE_DEDUP_TTL=30
IDEMPOTENT_DEDUP_TTL=600

# Nginx IP Allowlist Configuration (for reverse proxy support)
# NGINX_PROXY_IPS=10.0.0.0/8,**********/12,***********/16

# =============================================================================
# SECURITY NOTES
# =============================================================================
# 1. Copy required secrets to your .env file
# 2. Replace placeholder values with your actual configuration
# 3. Never commit this file to version control
# 4. Validate secrets by running: npm start
# =============================================================================
