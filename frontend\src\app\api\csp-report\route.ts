/**
 * @file Enhanced CSP Violation Reporting Endpoint - 2025 Security Standards
 * @module api/csp-report
 *
 * Advanced CSP violation reporting with comprehensive analysis,
 * threat detection, and monitoring integration following OWASP 2025 guidelines.
 */

import { NextRequest, NextResponse } from 'next/server';
import { headers } from 'next/headers';

interface CSPViolationReport {
  'csp-report': {
    'document-uri': string;
    referrer: string;
    'violated-directive': string;
    'effective-directive': string;
    'original-policy': string;
    disposition: string;
    'blocked-uri': string;
    'line-number': number;
    'column-number': number;
    'source-file': string;
    'status-code': number;
    'script-sample': string;
    // Enhanced 2025 fields
    timestamp?: string;
    'user-agent'?: string;
    'violation-count'?: number;
    'nonce-valid'?: boolean;
    'strict-csp'?: boolean;
  };
}

interface ViolationAnalysis {
  severity: 'low' | 'medium' | 'high' | 'critical';
  category:
    | 'script'
    | 'style'
    | 'image'
    | 'font'
    | 'connect'
    | 'frame'
    | 'other';
  isKnownIssue: boolean;
  recommendation: string;
  threatLevel: number; // 1-10 scale
}

/**
 * Handle CSP violation reports
 * Single responsibility: CSP violation logging only
 */
export async function POST(request: NextRequest) {
  try {
    // Verify content type
    const contentType = request.headers.get('content-type');
    if (
      !contentType?.includes('application/csp-report') &&
      !contentType?.includes('application/json')
    ) {
      return NextResponse.json(
        { error: 'Invalid content type' },
        { status: 400 }
      );
    }

    // Parse violation report
    const report: CSPViolationReport = await request.json();
    const violation = report['csp-report'];

    if (!violation) {
      return NextResponse.json(
        { error: 'Invalid CSP report format' },
        { status: 400 }
      );
    }

    // Extract relevant information
    const violationData = {
      timestamp: new Date().toISOString(),
      documentUri: violation['document-uri'],
      violatedDirective: violation['violated-directive'],
      effectiveDirective: violation['effective-directive'],
      blockedUri: violation['blocked-uri'],
      sourceFile: violation['source-file'],
      lineNumber: violation['line-number'],
      columnNumber: violation['column-number'],
      scriptSample: violation['script-sample'],
      userAgent: request.headers.get('user-agent'),
      ip:
        request.headers.get('x-forwarded-for') ||
        request.headers.get('x-real-ip'),
    };

    // Log violation (in production, send to monitoring service)
    if (process.env.NODE_ENV === 'development') {
      console.warn('🚨 CSP Violation Detected:', {
        directive: violationData.violatedDirective,
        blockedUri: violationData.blockedUri,
        sourceFile: violationData.sourceFile,
        line: violationData.lineNumber,
        sample: violationData.scriptSample,
      });
    } else {
      // In production, send to monitoring service
      // Example: await sendToMonitoringService(violationData);
      console.error('CSP Violation:', violationData);
    }

    // Filter out common false positives
    const isKnownFalsePositive = [
      'chrome-extension:',
      'moz-extension:',
      'safari-extension:',
      'about:blank',
      'data:text/html,chromewebdata',
    ].some(pattern => violationData.blockedUri?.includes(pattern));

    if (isKnownFalsePositive) {
      return NextResponse.json({ status: 'ignored' }, { status: 200 });
    }

    // Alert on critical violations
    const isCriticalViolation = [
      'script-src',
      'object-src',
      'base-uri',
    ].includes(violationData.effectiveDirective);

    if (isCriticalViolation) {
      // In production, trigger immediate alert
      console.error('🚨 CRITICAL CSP VIOLATION:', violationData);
    }

    return NextResponse.json({ status: 'received' }, { status: 200 });
  } catch (error) {
    console.error('Error processing CSP report:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * Handle preflight requests
 */
export async function OPTIONS() {
  return NextResponse.json(
    {},
    {
      status: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type',
      },
    }
  );
}

/**
 * Reject other methods
 */
export async function GET() {
  return NextResponse.json({ error: 'Method not allowed' }, { status: 405 });
}
