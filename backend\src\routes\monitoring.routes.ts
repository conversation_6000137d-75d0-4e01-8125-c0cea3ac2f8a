import type { Request, Response } from 'express';

import { NextFunction, Router } from 'express';

import { circuitBreakerStatusMiddleware } from '../middleware/circuitBreaker.js';
import { circuitBreakerResetMiddleware } from '../middleware/circuitBreaker.js'; // Import for circuit breaker reset
import { enhancedAuthenticateUser } from '../middleware/jwtAuth.middleware.js';
import { getRateLimitStatus } from '../middleware/rateLimiting.js';
import { getDeduplicationMetrics } from '../middleware/requestDeduplication.js';
import { requireRole } from '../middleware/supabaseAuth.js'; // For admin-only routes
import { getMetrics, getHttpRequestMetrics } from '../services/metrics.service.js';
import { getCircuitBreakerHistory } from '../services/circuitBreaker.service.js';
import logger from '../utils/logger.js'; // For placeholder handlers

const router = Router();

// Original path: /api/rate-limit-status
router.get('/rate-limit-status', getRateLimitStatus);

// Original path: /api/metrics
router.get('/metrics', async (req: Request, res: Response): Promise<void> => {
  try {
    const metrics = await getMetrics();
    const acceptHeader = req.headers.accept ?? '';
    const wantsJson = acceptHeader.includes('application/json');

    if (wantsJson) {
      // Return structured dummy metrics data for the dashboard
      res.json({
        data: [
          { name: 'cpu_usage', unit: 'percent', value: 0.5 },
          { name: 'memory_usage', unit: 'percent', value: 0.7 },
          { name: 'disk_io', unit: 'ops/sec', value: 100 },
          { name: 'network_traffic', unit: 'kb/s', value: 500 },
        ],
        message: 'Dummy metrics data for dashboard.',
        status: 'success',
      });
    } else {
      res.set('Content-Type', 'text/plain');
      res.send(metrics);
    }
  } catch (error: unknown) {
    res.status(500).json({
      error: (error as Error).message,
      message: 'Failed to retrieve metrics',
      status: 'error',
    });
  }
});

// Endpoint to get current system health (example: can be public or lightly authenticated)
router.get('/health', (req: Request, res: Response) => {
  // In a real scenario, this would call a controller or service method.
  logger.info('System health endpoint called');
  res.status(200).json({ status: 'UP', timestamp: new Date().toISOString() });
});

// Endpoint to get performance metrics (ADMIN or MANAGER only)
router.get(
  '/performance',
  enhancedAuthenticateUser,
  requireRole(['ADMIN', 'MANAGER']),
  (req: Request, res: Response) => {
    logger.info('Performance metrics endpoint called by user:', req.userId);
    res.status(200).json({ metrics: { cpuUsage: 'low', memory: 'stable' } });
  },
);

// Endpoint to get error logs (ADMIN only)
router.get(
  '/errors',
  enhancedAuthenticateUser,
  requireRole(['ADMIN']),
  (req: Request, res: Response) => {
    logger.info('Error logs endpoint called by user:', req.userId);
    res.status(200).json({ logs: ['Error log entry 1', 'Error log entry 2'] });
  },
);

// Endpoint to get circuit breaker status (accessible to authenticated users for monitoring)
router.get(
  '/circuit-breakers',
  enhancedAuthenticateUser, // Require authentication but allow all authenticated users
  circuitBreakerStatusMiddleware(), // Ensure this middleware sends a response
);

// Reset a circuit breaker (ADMIN only)
// Note: The original file had this as /monitoring/circuit-breakers/reset
// The circuitBreakerResetMiddleware might also handle its own response.
router.post(
  '/circuit-breakers/reset', // Path from original structure, could be /circuit-breakers/:name/reset if middleware handles :name
  enhancedAuthenticateUser,
  requireRole(['ADMIN']),
  circuitBreakerResetMiddleware(), // Ensure this middleware sends a response
);

// GET /api/monitoring/circuit-breakers/history - Get circuit breaker history
router.get('/circuit-breakers/history', enhancedAuthenticateUser, async (req: Request, res: Response): Promise<void> => {
  try {
    const timeframe = req.query.timeframe as '1h' | '6h' | '24h' | '7d' || '24h';
    const breakerName = req.query.breakerName as string | undefined;

    // Validate timeframe parameter
    const validTimeframes = ['1h', '6h', '24h', '7d'];
    if (!validTimeframes.includes(timeframe)) {
      res.status(400).json({
        status: 'error',
        message: 'Invalid timeframe. Must be one of: 1h, 6h, 24h, 7d',
        timestamp: new Date().toISOString(),
      });
      return;
    }

    const historyData = getCircuitBreakerHistory(timeframe, breakerName);

    res.status(200).json({
      data: historyData,
      status: 'success',
      timestamp: new Date().toISOString(),
    });
  } catch (error: unknown) {
    logger.error('Circuit breaker history endpoint failed', {
      endpoint: '/api/monitoring/circuit-breakers/history',
      error: (error as Error).message,
      service: 'monitoring',
    });

    res.status(500).json({
      status: 'error',
      message: 'Failed to retrieve circuit breaker history',
      error: (error as Error).message,
      timestamp: new Date().toISOString(),
    });
  }
});

// GET /api/monitoring/http-request-metrics - Get HTTP request metrics
router.get('/http-request-metrics', enhancedAuthenticateUser, async (req: Request, res: Response): Promise<void> => {
  try {
    const metricsData = await getHttpRequestMetrics();

    res.status(200).json({
      data: metricsData,
      status: 'success',
      timestamp: new Date().toISOString(),
    });
  } catch (error: unknown) {
    logger.error('HTTP request metrics endpoint failed', {
      endpoint: '/api/monitoring/http-request-metrics',
      error: (error as Error).message,
      service: 'monitoring',
    });

    res.status(500).json({
      status: 'error',
      message: 'Failed to retrieve HTTP request metrics',
      error: (error as Error).message,
      timestamp: new Date().toISOString(),
    });
  }
});

// Original path: /api/monitoring/deduplication
router.get('/monitoring/deduplication', (req: Request, res: Response): void => {
  try {
    const metrics = getDeduplicationMetrics();
    res.json({
      data: metrics,
      status: 'success',
      timestamp: new Date().toISOString(),
    });
  } catch (error: unknown) {
    res.status(500).json({
      error: (error as Error).message,
      message: 'Failed to retrieve deduplication metrics',
      status: 'error',
    });
  }
});

export default router;
