'use client';

import { Calendar, Filter, Search, X } from 'lucide-react';
import { useMemo } from 'react';

import type { Gift } from '@/lib/types/domain';

import { ActionButton } from '@/components/ui/action-button';
import { Badge } from '@/components/ui/badge';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface FilterState {
  search: string;
  recipientId: string;
  occasion: string;
  senderName: string;
  dateRange: {
    from: string;
    to: string;
  };
}

interface GiftFiltersProps {
  filters: FilterState;
  onFilterChange: (filters: Partial<FilterState>) => void;
  onClearFilters: () => void;
  hasActiveFilters: boolean;
  gifts: Gift[]; // For extracting unique values
}

export const GiftFilters: React.FC<GiftFiltersProps> = ({
  filters,
  onFilterChange,
  onClearFilters,
  hasActiveFilters,
  gifts,
}) => {
  // Extract unique values from gifts for filter options
  const filterOptions = useMemo(() => {
    const recipients = new Map<string, string>();
    const occasions = new Set<string>();
    const senders = new Set<string>();

    gifts.forEach(gift => {
      // Recipients
      if (gift.recipient) {
        recipients.set(gift.recipient.id, gift.recipient.name);
      }

      // Occasions
      if (gift.occasion) {
        occasions.add(gift.occasion);
      }

      // Senders
      senders.add(gift.senderName);
    });

    return {
      recipients: Array.from(recipients.entries()).map(([id, name]) => ({ id, name })),
      occasions: Array.from(occasions).sort(),
      senders: Array.from(senders).sort(),
    };
  }, [gifts]);

  const handleSearchChange = (value: string) => {
    onFilterChange({ search: value });
  };

  const handleRecipientChange = (value: string) => {
    onFilterChange({ recipientId: value === 'all' ? '' : value });
  };

  const handleOccasionChange = (value: string) => {
    onFilterChange({ occasion: value === 'all' ? '' : value });
  };

  const handleSenderChange = (value: string) => {
    onFilterChange({ senderName: value === 'all' ? '' : value });
  };

  const handleDateFromChange = (value: string) => {
    onFilterChange({
      dateRange: {
        ...filters.dateRange,
        from: value,
      },
    });
  };

  const handleDateToChange = (value: string) => {
    onFilterChange({
      dateRange: {
        ...filters.dateRange,
        to: value,
      },
    });
  };

  const activeFilterCount = [
    filters.search,
    filters.recipientId,
    filters.occasion,
    filters.senderName,
    filters.dateRange.from,
    filters.dateRange.to,
  ].filter(Boolean).length;

  return (
    <Card>
      <Collapsible>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <CollapsibleTrigger asChild>
                <ActionButton variant="ghost" size="sm" className="p-0">
                  <Filter className="h-4 w-4" />
                </ActionButton>
              </CollapsibleTrigger>
              <CardTitle className="text-base">Filters</CardTitle>
              {activeFilterCount > 0 && (
                <Badge variant="secondary" className="ml-2">
                  {activeFilterCount}
                </Badge>
              )}
            </div>
            {hasActiveFilters && (
              <ActionButton
                variant="ghost"
                size="sm"
                onClick={onClearFilters}
                className="text-muted-foreground hover:text-foreground"
              >
                <X className="h-4 w-4 mr-1" />
                Clear All
              </ActionButton>
            )}
          </div>
          <CardDescription>
            Filter gifts by search terms, recipients, occasions, and dates
          </CardDescription>
        </CardHeader>

        <CollapsibleContent>
          <CardContent className="space-y-4">
            {/* Search */}
            <div className="space-y-2">
              <Label htmlFor="search">Search</Label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                <Input
                  id="search"
                  placeholder="Search gifts, recipients, or senders..."
                  value={filters.search}
                  onChange={(e) => handleSearchChange(e.target.value)}
                  className="pl-9"
                />
              </div>
            </div>

            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              {/* Recipient Filter */}
              <div className="space-y-2">
                <Label htmlFor="recipient">Recipient</Label>
                <Select
                  value={filters.recipientId || 'all'}
                  onValueChange={handleRecipientChange}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All recipients" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All recipients</SelectItem>
                    {filterOptions.recipients.map((recipient) => (
                      <SelectItem key={recipient.id} value={recipient.id}>
                        {recipient.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Occasion Filter */}
              <div className="space-y-2">
                <Label htmlFor="occasion">Occasion</Label>
                <Select
                  value={filters.occasion || 'all'}
                  onValueChange={handleOccasionChange}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All occasions" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All occasions</SelectItem>
                    {filterOptions.occasions.map((occasion) => (
                      <SelectItem key={occasion} value={occasion}>
                        {occasion}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Sender Filter */}
              <div className="space-y-2">
                <Label htmlFor="sender">Sender</Label>
                <Select
                  value={filters.senderName || 'all'}
                  onValueChange={handleSenderChange}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All senders" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All senders</SelectItem>
                    {filterOptions.senders.map((sender) => (
                      <SelectItem key={sender} value={sender}>
                        {sender}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Date Range */}
              <div className="space-y-2">
                <Label>Date Range</Label>
                <div className="flex items-center gap-2">
                  <div className="relative flex-1">
                    <Calendar className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                    <Input
                      type="date"
                      value={filters.dateRange.from}
                      onChange={(e) => handleDateFromChange(e.target.value)}
                      className="pl-9"
                      placeholder="From"
                    />
                  </div>
                  <span className="text-muted-foreground">to</span>
                  <div className="relative flex-1">
                    <Calendar className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                    <Input
                      type="date"
                      value={filters.dateRange.to}
                      onChange={(e) => handleDateToChange(e.target.value)}
                      className="pl-9"
                      placeholder="To"
                    />
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </Card>
  );
};
