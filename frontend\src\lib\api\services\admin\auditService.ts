import type { ApiClient } from '@/lib/api/core/apiClient';
import type { AuditLog, Filters, PaginatedResponse } from '@/types';

import {
  BaseApiService,
  type DataTransformer,
  type ServiceConfig,
} from '@/lib/api/core/baseApiService';

// Define AuditLogTransformer for consistent data transformation
const AuditLogTransformer: DataTransformer<AuditLog> = {
  fromApi: (data: any): AuditLog => ({
    action: data.action || '',
    details: data.details || '',
    id: data.id || '',
    timestamp: new Date(data.created_at || data.timestamp || new Date()),
    userId: data.user_id || data.userId || data.auth_user_id || '',
    auth_user_id: data.auth_user_id || '',
    auth_user: data.auth_user || null,
    // Add other audit log properties as needed
  }),
  toApi: (data: any) => data,
};

/**
 * Enhanced Audit Service using BaseApiService infrastructure
 * Provides audit log management operations with production-grade reliability
 */
export class AuditService extends BaseApiService<
  AuditLog,
  Partial<AuditLog>,
  Partial<AuditLog>
> {
  protected endpoint = '/admin/audit-logs';
  protected transformer: DataTransformer<AuditLog> = AuditLogTransformer;

  constructor(apiClient: ApiClient, config?: ServiceConfig) {
    super(apiClient, {
      cacheDuration: 2 * 60 * 1000, // 2 minutes for audit logs
      circuitBreakerThreshold: 5,
      enableMetrics: true,
      retryAttempts: 3,
      ...config,
    });
  }

  /**
   * Get audit logs by action type
   */
  async getByAction(action: string, filters?: Filters): Promise<AuditLog[]> {
    const result = await this.getAll({ ...filters, action });
    return result.data;
  }

  /**
   * Get audit logs by date range with filtering
   */
  async getByDateRange(
    startDate: Date,
    endDate: Date,
    filters?: Filters
  ): Promise<PaginatedResponse<AuditLog>> {
    const dateFilters = {
      endDate: endDate.toISOString(),
      startDate: startDate.toISOString(),
      ...filters,
    };

    return this.getAll(dateFilters);
  }

  /**
   * Get audit logs by user ID
   */
  async getByUserId(userId: string, filters?: Filters): Promise<AuditLog[]> {
    const result = await this.getAll({ ...filters, userId });
    return result.data;
  }
}
