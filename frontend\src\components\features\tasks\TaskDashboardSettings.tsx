/**
 * Task Dashboard Settings Component
 * 
 * Provides task-specific dashboard configuration and settings management
 * using the generic DashboardSettings component.
 */

'use client';

import React from 'react';

import { DashboardSettings } from '@/components/dashboard/DashboardSettings';
import type { DashboardConfig } from '@/components/dashboard/types';
import type { Task } from '@/lib/types/domain';
import { useDashboardStore } from '@/hooks/domain/useDashboardStore';

/**
 * Task dashboard configuration
 */
const taskDashboardConfig: DashboardConfig<Task> = {
  entityType: 'task',
  title: 'Task Dashboard',
  description: 'Oversee all tasks, assignments, and progress.',
  viewModes: ['cards', 'table', 'list'],
  defaultViewMode: 'cards',
  enableBulkActions: true,
  enableExport: true,
  refreshInterval: 30000,
};

/**
 * Props for the TaskDashboardSettings component
 */
interface TaskDashboardSettingsProps {
  /** Additional CSS class names */
  className?: string;
}

/**
 * Task-specific dashboard settings component that uses the generic
 * DashboardSettings component with task-specific configuration.
 * 
 * This component:
 * - Uses the generic dashboard store for tasks
 * - Provides task-specific configuration
 * - Maintains all task-specific state and actions
 * 
 * @param props - Component props
 * @returns JSX element representing the task dashboard settings
 */
export const TaskDashboardSettings: React.FC<TaskDashboardSettingsProps> = ({
  className = '',
}) => {
  // Get task dashboard store
  const dashboardStore = useDashboardStore('task');
  const {
    layout,
    monitoring,
    setViewMode,
    setGridColumns,
    toggleCompactMode,
    setMonitoringEnabled,
    setRefreshInterval,
    toggleAutoRefresh,
    resetSettings,
  } = dashboardStore();

  return (
    <DashboardSettings
      config={taskDashboardConfig}
      entityType="task"
      layout={layout}
      monitoring={monitoring}
      setViewMode={setViewMode}
      setGridColumns={setGridColumns}
      toggleCompactMode={toggleCompactMode}
      setMonitoringEnabled={setMonitoringEnabled}
      setRefreshInterval={setRefreshInterval}
      toggleAutoRefresh={toggleAutoRefresh}
      resetSettings={resetSettings}
      className={className}
    />
  );
};

export default TaskDashboardSettings;
