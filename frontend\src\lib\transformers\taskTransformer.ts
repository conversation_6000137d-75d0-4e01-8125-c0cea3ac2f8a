/**
 * @file Data transformer for Task domain models.
 * @module transformers/taskTransformer
 */

import type {
  CreateSubtaskRequest,
  CreateTaskRequest,
  SubtaskApiResponse,
  TaskApiResponse,
  UpdateTaskRequest,
} from '../types/apiContracts';
import type {
  CreateSubtaskData,
  CreateTaskData,
  Subtask,
  Task,
  TaskPriorityPrisma,
  TaskStatusPrisma,
} from '../types/domain';

import {
  // Added SubtaskApiResponse
  EmployeeApiResponse,
  VehicleApiResponse,
} from '../types/apiContracts'; // Changed import path to apiContracts
import { Employee, Vehicle } from '../types/domain';
import { EmployeeTransformer } from './employeeTransformer';
import { VehicleTransformer } from './vehicleTransformer';

export const SubtaskTransformer = {
  /**
   * Transforms a subtask API response into a domain.Subtask model.
   * @param apiData - The subtask data as received from the API.
   * @returns A domain.Subtask object.
   */
  fromApi(apiData: SubtaskApiResponse): Subtask {
    return {
      completed: apiData.completed,
      id: apiData.id,
      taskId: apiData.taskId, // Added taskId
      title: apiData.title,
    };
  },

  /**
   * Transforms a domain.CreateSubtaskData into an API CreateSubtaskRequest.
   * @param domainData - The subtask data from the domain.
   * @returns An API CreateSubtaskRequest object.
   */
  toApiRequest(domainData: CreateSubtaskData): CreateSubtaskRequest {
    return {
      completed:
        domainData.completed === undefined ? false : domainData.completed,
      taskId: domainData.taskId, // Added taskId
      title: domainData.title.trim(),
    };
  },
};

/**
 * Transforms task data between API response formats and frontend domain models.
 */
export const TaskTransformer = {
  fromApi(apiData: TaskApiResponse): Task {
    return {
      createdAt: apiData.createdAt,
      dateTime: apiData.dateTime, // REQUIRED - no null fallback
      deadline: apiData.deadline ?? null, // Optional field
      description: apiData.description, // REQUIRED - no null fallback
      driverEmployee: apiData.Employee_Task_driverEmployeeIdToEmployee
        ? EmployeeTransformer.fromApi(
            apiData.Employee_Task_driverEmployeeIdToEmployee
          )
        : null,
      driverEmployeeId: apiData.driverEmployeeId ?? null,
      estimatedDuration: apiData.estimatedDuration, // REQUIRED - no null fallback
      id: apiData.id,
      location: apiData.location, // REQUIRED - no null fallback
      notes: apiData.notes ?? null, // Optional field
      priority: apiData.priority as TaskPriorityPrisma,
      requiredSkills: apiData.requiredSkills, // REQUIRED - no null fallback
      // Transform relation objects from backend Prisma includes
      staffEmployee: apiData.Employee_Task_staffEmployeeIdToEmployee
        ? EmployeeTransformer.fromApi(
            apiData.Employee_Task_staffEmployeeIdToEmployee
          )
        : null,
      staffEmployeeId: apiData.staffEmployeeId, // REQUIRED - no null fallback
      status: apiData.status as TaskStatusPrisma,
      subtasks: Array.isArray(apiData.SubTask)
        ? apiData.SubTask.map((st: SubtaskApiResponse) =>
            SubtaskTransformer.fromApi(st)
          )
        : [],

      updatedAt: apiData.updatedAt,
      vehicle: apiData.Vehicle
        ? VehicleTransformer.fromApi(apiData.Vehicle)
        : null,
      vehicleId: apiData.vehicleId ?? null,
    };
  },

  toCreateRequest(taskData: CreateTaskData): CreateTaskRequest {
    return {
      dateTime: taskData.dateTime, // REQUIRED
      deadline: taskData.deadline ?? null, // Optional
      description: taskData.description, // REQUIRED
      driverEmployeeId: taskData.driverEmployeeId ?? null, // Optional
      estimatedDuration: taskData.estimatedDuration, // REQUIRED
      location: taskData.location, // REQUIRED
      notes: taskData.notes ?? null, // Optional
      priority: taskData.priority as string, // REQUIRED
      requiredSkills: taskData.requiredSkills, // REQUIRED
      staffEmployeeId: taskData.staffEmployeeId, // REQUIRED
      status: taskData.status as string, // REQUIRED
      subTasks: taskData.subtasks?.map(SubtaskTransformer.toApiRequest) ?? [], // Backend expects 'subTasks'
      vehicleId: taskData.vehicleId ?? null, // Optional
    };
  },

  toUpdateRequest(taskData: Partial<CreateTaskData>): UpdateTaskRequest {
    const request: UpdateTaskRequest = {};

    if (taskData.description !== undefined) {
      request.description = taskData.description; // Direct mapping - domain description to API description
    }
    if (taskData.notes !== undefined) request.notes = taskData.notes; // Direct mapping - domain notes to API notes
    if (taskData.location !== undefined) request.location = taskData.location;
    if (taskData.dateTime !== undefined) request.dateTime = taskData.dateTime;
    if (taskData.estimatedDuration !== undefined)
      request.estimatedDuration = taskData.estimatedDuration;
    if (taskData.priority !== undefined)
      request.priority = taskData.priority as string;
    if (taskData.status !== undefined)
      request.status = taskData.status as string;
    if (taskData.deadline !== undefined) request.deadline = taskData.deadline; // Backend expects 'deadline'
    if (taskData.requiredSkills !== undefined)
      request.requiredSkills = taskData.requiredSkills;
    if (taskData.vehicleId !== undefined)
      request.vehicleId = taskData.vehicleId;
    if (taskData.staffEmployeeId !== undefined)
      request.staffEmployeeId = taskData.staffEmployeeId;
    if (taskData.driverEmployeeId !== undefined)
      request.driverEmployeeId = taskData.driverEmployeeId;
    if (taskData.subtasks !== undefined) {
      (request as any).subTasks = taskData.subtasks.map(
        SubtaskTransformer.toApiRequest
      ); // Cast to any to allow subTasks
    }

    return request;
  },
};
