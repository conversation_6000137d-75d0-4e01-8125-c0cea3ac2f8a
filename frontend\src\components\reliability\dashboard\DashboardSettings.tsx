/**
 * @file Dashboard settings panel for user preferences and configuration.
 * This component provides controls for customizing dashboard layout, refresh intervals, and notifications.
 * @module components/reliability/dashboard/DashboardSettings
 */

'use client';

import {
  Bell,
  Grid3X3,
  Layout,
  List,
  RefreshCw,
  Settings,
  SquareStack,
} from 'lucide-react';
import React from 'react';

import { ActionButton } from '@/components/ui/action-button';
import { Badge } from '@/components/ui/badge';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { useReliabilityStore } from '@/lib/hooks';
import type {
  DashboardLayout,
  DataType,
  WidgetId,
} from '@/lib/stores/zustand/reliabilityStore';

/**
 * Props for the DashboardSettings component
 */
export interface DashboardSettingsProps {
  /** Optional CSS class name for styling */
  className?: string;
}

/**
 * Dashboard settings panel for user preferences and configuration.
 *
 * This component provides controls for:
 * - Dashboard layout options (grid, list, compact)
 * - Grid column configuration
 * - Widget visibility management
 * - Refresh interval settings
 * - Notification preferences
 * - Monitoring controls
 *
 * Features:
 * - Real-time preference updates
 * - Persistent settings via localStorage
 * - Responsive design
 * - Accessibility support
 * - Reset to defaults functionality
 *
 * @param props - Component props
 * @returns JSX element representing the dashboard settings panel
 */
export const DashboardSettings: React.FC<DashboardSettingsProps> = ({
  className = '',
}) => {
  // Dashboard preferences and actions
  const preferences = useReliabilityStore(state => state.preferences);
  const setDashboardLayout = useReliabilityStore(
    state => state.setDashboardLayout
  );
  const setGridColumns = useReliabilityStore(state => state.setGridColumns);
  const setRefreshInterval = useReliabilityStore(
    state => state.setRefreshInterval
  );
  const setNotificationPreferences = useReliabilityStore(
    state => state.setNotificationPreferences
  );
  const toggleWidget = useReliabilityStore(state => state.toggleWidget);
  const resetPreferencesToDefaults = useReliabilityStore(
    state => state.resetPreferencesToDefaults
  );

  // Monitoring state
  const isMonitoringEnabled = useReliabilityStore(
    state => state.monitoring.isEnabled
  );
  const setMonitoringEnabled = useReliabilityStore(
    state => state.setMonitoringEnabled
  );

  /**
   * Widget configuration for visibility controls
   */
  const widgetConfig = [
    {
      id: 'system-health',
      name: 'System Health',
      description: 'Overall system status',
    },
    {
      id: 'health-status-indicators',
      name: 'Health Status Indicators',
      description: 'Real-time component health status',
    },
    {
      id: 'circuit-breakers',
      name: 'Circuit Breakers Overview',
      description: 'Circuit breaker status and failure protection',
    },
    {
      id: 'circuit-breaker-metrics',
      name: 'Circuit Breaker Metrics',
      description: 'Performance metrics and failure rate charts',
    },
    {
      id: 'performance-overview',
      name: 'Performance Overview',
      description: 'Comprehensive performance metrics and scoring',
    },
    {
      id: 'system-metrics',
      name: 'System Performance',
      description: 'Performance-focused system metrics monitoring',
    },
    {
      id: 'http-metrics',
      name: 'HTTP Request Metrics',
      description: 'Request performance and throughput analysis',
    },
    {
      id: 'performance-metrics',
      name: 'Performance Metrics',
      description: 'System performance monitoring and analysis',
    },
    {
      id: 'deduplication-metrics',
      name: 'Deduplication Metrics',
      description: 'Cache efficiency and request deduplication analysis',
    },
    {
      id: 'active-alerts',
      name: 'Active Alerts',
      description: 'Current system alerts and notifications',
    },
    {
      id: 'alert-statistics',
      name: 'Alert Statistics',
      description: 'Alert trends and statistical analysis',
    },
    {
      id: 'dependency-status',
      name: 'Dependency Status',
      description: 'External dependency health monitoring',
    },
    {
      id: 'dependency-health',
      name: 'Dependency Health',
      description: 'External service and dependency status',
    },
    {
      id: 'health-trends',
      name: 'Health Trends',
      description: 'Historical health trend visualization',
    },
  ];

  /**
   * Refresh interval options
   */
  const refreshIntervalOptions = [
    { value: 5000, label: '5 seconds' },
    { value: 10000, label: '10 seconds' },
    { value: 15000, label: '15 seconds' },
    { value: 30000, label: '30 seconds' },
    { value: 60000, label: '1 minute' },
    { value: 300000, label: '5 minutes' },
  ];

  /**
   * Handle layout change
   */
  const handleLayoutChange = (layout: DashboardLayout) => {
    setDashboardLayout(layout);
  };

  /**
   * Handle grid columns change
   */
  const handleGridColumnsChange = (columns: number[]) => {
    const value = columns[0];
    if (value !== undefined) {
      setGridColumns(value);
    }
  };

  /**
   * Handle refresh interval change
   */
  const handleRefreshIntervalChange = (
    dataType: DataType,
    interval: string
  ) => {
    setRefreshInterval(dataType, parseInt(interval));
  };

  /**
   * Handle notification setting change
   */
  const handleNotificationChange = (setting: string, value: any) => {
    setNotificationPreferences({ [setting]: value });
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Settings className="h-5 w-5" />
          Dashboard Settings
        </CardTitle>
        <CardDescription>
          Customize your reliability monitoring dashboard
        </CardDescription>
      </CardHeader>

      <CardContent>
        <Tabs defaultValue="layout" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="layout">Layout</TabsTrigger>
            <TabsTrigger value="widgets">Widgets</TabsTrigger>
            <TabsTrigger value="refresh">Refresh</TabsTrigger>
            <TabsTrigger value="notifications">Alerts</TabsTrigger>
          </TabsList>

          {/* Layout Settings */}
          <TabsContent value="layout" className="space-y-6">
            <div className="space-y-4">
              <div>
                <Label className="text-base font-medium">
                  Dashboard Layout
                </Label>
                <p className="text-sm text-muted-foreground">
                  Choose how widgets are arranged on the dashboard
                </p>
              </div>

              <RadioGroup
                value={preferences.dashboardLayout.layout}
                onValueChange={handleLayoutChange}
                className="grid grid-cols-1 gap-4 sm:grid-cols-3"
              >
                <div className="flex items-center space-x-2 rounded-lg border p-4">
                  <RadioGroupItem value="grid" id="grid" />
                  <div className="flex items-center gap-2">
                    <Grid3X3 className="h-4 w-4" />
                    <Label htmlFor="grid">Grid</Label>
                  </div>
                </div>

                <div className="flex items-center space-x-2 rounded-lg border p-4">
                  <RadioGroupItem value="list" id="list" />
                  <div className="flex items-center gap-2">
                    <List className="h-4 w-4" />
                    <Label htmlFor="list">List</Label>
                  </div>
                </div>

                <div className="flex items-center space-x-2 rounded-lg border p-4">
                  <RadioGroupItem value="compact" id="compact" />
                  <div className="flex items-center gap-2">
                    <SquareStack className="h-4 w-4" />
                    <Label htmlFor="compact">Compact</Label>
                  </div>
                </div>
              </RadioGroup>

              {preferences.dashboardLayout.layout === 'grid' && (
                <div className="space-y-3">
                  <Label className="text-sm font-medium">
                    Grid Columns: {preferences.dashboardLayout.gridColumns}
                  </Label>
                  <Slider
                    value={[preferences.dashboardLayout.gridColumns]}
                    onValueChange={handleGridColumnsChange}
                    max={6}
                    min={1}
                    step={1}
                    className="w-full"
                  />
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>1 column</span>
                    <span>6 columns</span>
                  </div>
                </div>
              )}
            </div>
          </TabsContent>

          {/* Widget Settings */}
          <TabsContent value="widgets" className="space-y-6">
            <div className="space-y-4">
              <div>
                <Label className="text-base font-medium">
                  Widget Visibility
                </Label>
                <p className="text-sm text-muted-foreground">
                  Choose which widgets to display on the dashboard
                </p>
              </div>

              <div className="space-y-3">
                {widgetConfig.map(widget => (
                  <div
                    key={widget.id}
                    className="flex items-center justify-between rounded-lg border p-3"
                  >
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        <Label className="font-medium">{widget.name}</Label>
                        {preferences.dashboardLayout.visibleWidgets.has(
                          widget.id as WidgetId
                        ) && (
                          <Badge variant="secondary" className="text-xs">
                            Visible
                          </Badge>
                        )}
                      </div>
                      <p className="text-xs text-muted-foreground">
                        {widget.description}
                      </p>
                    </div>
                    <Switch
                      checked={preferences.dashboardLayout.visibleWidgets.has(
                        widget.id as WidgetId
                      )}
                      onCheckedChange={() =>
                        toggleWidget(widget.id as WidgetId)
                      }
                    />
                  </div>
                ))}
              </div>
            </div>
          </TabsContent>

          {/* Refresh Settings */}
          <TabsContent value="refresh" className="space-y-6">
            <div className="space-y-4">
              <div>
                <Label className="text-base font-medium">
                  Refresh Intervals
                </Label>
                <p className="text-sm text-muted-foreground">
                  Configure how often data is refreshed for different components
                </p>
              </div>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label className="font-medium">Health Monitoring</Label>
                    <p className="text-xs text-muted-foreground">
                      System health checks
                    </p>
                  </div>
                  <Select
                    value={preferences.refreshIntervals.health.toString()}
                    onValueChange={value =>
                      handleRefreshIntervalChange('health', value)
                    }
                  >
                    <SelectTrigger className="w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {refreshIntervalOptions.map(option => (
                        <SelectItem
                          key={option.value}
                          value={option.value.toString()}
                        >
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label className="font-medium">Circuit Breakers</Label>
                    <p className="text-xs text-muted-foreground">
                      Circuit breaker status
                    </p>
                  </div>
                  <Select
                    value={preferences.refreshIntervals[
                      'circuit-breakers'
                    ].toString()}
                    onValueChange={value =>
                      handleRefreshIntervalChange('circuit-breakers', value)
                    }
                  >
                    <SelectTrigger className="w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {refreshIntervalOptions.map(option => (
                        <SelectItem
                          key={option.value}
                          value={option.value.toString()}
                        >
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label className="font-medium">Performance Metrics</Label>
                    <p className="text-xs text-muted-foreground">
                      System performance data
                    </p>
                  </div>
                  <Select
                    value={preferences.refreshIntervals.metrics.toString()}
                    onValueChange={value =>
                      handleRefreshIntervalChange('metrics', value)
                    }
                  >
                    <SelectTrigger className="w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {refreshIntervalOptions.map(option => (
                        <SelectItem
                          key={option.value}
                          value={option.value.toString()}
                        >
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label className="font-medium">Alerts</Label>
                    <p className="text-xs text-muted-foreground">
                      Alert notifications
                    </p>
                  </div>
                  <Select
                    value={preferences.refreshIntervals.alerts.toString()}
                    onValueChange={value =>
                      handleRefreshIntervalChange('alerts', value)
                    }
                  >
                    <SelectTrigger className="w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {refreshIntervalOptions.map(option => (
                        <SelectItem
                          key={option.value}
                          value={option.value.toString()}
                        >
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          </TabsContent>

          {/* Notification Settings */}
          <TabsContent value="notifications" className="space-y-6">
            <div className="space-y-4">
              <div>
                <Label className="text-base font-medium">
                  Alert Notifications
                </Label>
                <p className="text-sm text-muted-foreground">
                  Configure how you receive alert notifications
                </p>
              </div>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label className="font-medium">Sound Notifications</Label>
                    <p className="text-xs text-muted-foreground">
                      Play sound for new alerts
                    </p>
                  </div>
                  <Switch
                    checked={preferences.notifications.soundEnabled}
                    onCheckedChange={checked =>
                      handleNotificationChange('soundEnabled', checked)
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label className="font-medium">Desktop Notifications</Label>
                    <p className="text-xs text-muted-foreground">
                      Show browser notifications
                    </p>
                  </div>
                  <Switch
                    checked={preferences.notifications.desktopEnabled}
                    onCheckedChange={checked =>
                      handleNotificationChange('desktopEnabled', checked)
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label className="font-medium">Minimum Severity</Label>
                    <p className="text-xs text-muted-foreground">
                      Only notify for alerts above this level
                    </p>
                  </div>
                  <Select
                    value={preferences.notifications.minimumSeverity}
                    onValueChange={value =>
                      handleNotificationChange('minimumSeverity', value)
                    }
                  >
                    <SelectTrigger className="w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="low">Low</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="high">High</SelectItem>
                      <SelectItem value="critical">Critical</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>

        <Separator className="my-6" />

        {/* Global Controls */}
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <Label className="font-medium">Monitoring Status</Label>
            <p className="text-xs text-muted-foreground">
              {isMonitoringEnabled
                ? 'Real-time monitoring active'
                : 'Monitoring paused'}
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Switch
              checked={isMonitoringEnabled}
              onCheckedChange={setMonitoringEnabled}
            />
            <ActionButton
              actionType="secondary"
              size="sm"
              onClick={resetPreferencesToDefaults}
            >
              Reset to Defaults
            </ActionButton>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

/**
 * Default export for the DashboardSettings component
 */
export default DashboardSettings;
