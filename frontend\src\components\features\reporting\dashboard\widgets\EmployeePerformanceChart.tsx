/**
 * @file EmployeePerformanceChart.tsx
 * @description Employee performance chart following existing chart patterns and SOLID principles
 */

import { Award, TrendingUp, Users } from 'lucide-react';
import React, { useMemo } from 'react';
import {
  Bar,
  BarChart,
  CartesianGrid,
  Cell,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from 'recharts';

import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { DataLoader } from '@/components/ui/data-loader';
import { ErrorDisplay } from '@/components/ui/error-display';
import { cn } from '@/lib/utils';

import type { ReportingFilters } from '../../data/types/reporting';
import type { EmployeePerformanceData } from '../../data/types/reporting';

import { useEmployeeAnalytics } from '../../hooks/useEmployeeAnalytics';

/**
 * Custom tooltip component for the chart
 */
interface CustomTooltipProps {
  active?: boolean;
  label?: string;
  payload?: any[];
}

/**
 * Props interface for EmployeePerformanceChart
 */
interface EmployeePerformanceChartProps {
  className?: string;
  data?: EmployeePerformanceData[];
  filters?: ReportingFilters;
  height?: number;
  interactive?: boolean;
  maxEmployees?: number;
  showLegend?: boolean;
}

const CustomTooltip: React.FC<CustomTooltipProps> = ({
  active,
  label,
  payload,
}) => {
  if (active && payload?.length) {
    const data = payload[0].payload;
    return (
      <div className="rounded-lg border bg-white p-3 shadow-lg">
        <p className="font-medium">{data.employeeName}</p>
        <p className="text-sm text-gray-600">
          Performance Score:{' '}
          <span className="font-medium">{data.averageRating}/10</span>
        </p>
        <p className="text-sm text-gray-600">
          Completed Delegations:{' '}
          <span className="font-medium">{data.completedDelegations}</span>
        </p>
        <p className="text-sm text-gray-600">
          Completed Tasks:{' '}
          <span className="font-medium">{data.completedTasks}</span>
        </p>
        <p className="text-sm text-gray-600">
          On-Time Rate:{' '}
          <span className="font-medium">{data.onTimePerformance}%</span>
        </p>
      </div>
    );
  }
  return null;
};

/**
 * Utility function to get color based on performance score
 */
const getPerformanceColor = (score: number): string => {
  if (score >= 9) return '#10b981'; // Green - excellent
  if (score >= 8) return '#3b82f6'; // Blue - good
  if (score >= 7) return '#f59e0b'; // Orange - average
  if (score >= 6) return '#ef4444'; // Red - below average
  return '#6b7280'; // Gray - poor
};

/**
 * EmployeePerformanceChart Component
 *
 * Displays employee performance data in a bar chart format following existing chart patterns.
 *
 * Responsibilities:
 * - Visualize employee performance distribution
 * - Follow existing chart component patterns
 * - Integrate with existing chart utilities
 * - Maintain consistent chart styling
 *
 * SOLID Principles Applied:
 * - SRP: Single responsibility of displaying performance data
 * - OCP: Open for extension via props configuration
 * - DIP: Depends on chart framework abstractions
 */
export const EmployeePerformanceChart: React.FC<
  EmployeePerformanceChartProps
> = ({
  className = '',
  data: propData,
  filters,
  height = 300,
  interactive = true,
  maxEmployees = 10,
  showLegend = true,
}) => {
  // Use hook if data not provided via props
  const { data: hookData, error, isLoading } = useEmployeeAnalytics(filters);
  const performanceData = propData || hookData?.performanceMetrics;

  // Transform data for chart display
  const chartData = useMemo(() => {
    if (!performanceData) return [];

    return performanceData
      .map(item => ({
        averageRating: item.averageRating,
        color: getPerformanceColor(item.averageRating),
        completedDelegations: item.completedDelegations,
        completedTasks: item.completedTasks,
        // Truncate long names for display
        displayName:
          item.employeeName.length > 12
            ? `${item.employeeName.slice(0, 9)}...`
            : item.employeeName,
        employeeName: item.employeeName,
        onTimePerformance: item.onTimePerformance,
        // Calculate overall performance score
        overallScore: Math.round(
          item.averageRating * 0.4 +
            item.onTimePerformance * 0.01 * 10 * 0.3 +
            item.workloadScore * 0.3
        ),
        workloadScore: item.workloadScore,
      }))
      .sort((a, b) => b.overallScore - a.overallScore) // Sort by overall performance
      .slice(0, maxEmployees); // Show top performers
  }, [performanceData, maxEmployees]);

  // Handle loading state
  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="size-5" />
            Employee Performance
          </CardTitle>
        </CardHeader>
        <CardContent>
          <DataLoader data={null} error={null} isLoading={true}>
            {() => null}
          </DataLoader>
        </CardContent>
      </Card>
    );
  }

  // Handle error state
  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="size-5" />
            Employee Performance
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ErrorDisplay error={error} />
        </CardContent>
      </Card>
    );
  }

  // Calculate summary stats
  const avgPerformance =
    chartData.length > 0
      ? Math.round(
          (chartData.reduce((sum, item) => sum + item.averageRating, 0) /
            chartData.length) *
            10
        ) / 10
      : 0;

  const topPerformers = chartData.filter(
    item => item.averageRating >= 8
  ).length;

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Users className="size-5" />
            Employee Performance
          </CardTitle>
          <div className="flex items-center gap-2">
            <Badge className="text-xs" variant="secondary">
              Avg: {avgPerformance}/10
            </Badge>
            {topPerformers > 0 && (
              <Badge className="text-xs" variant="default">
                <Award className="mr-1 size-3" />
                {topPerformers} top performers
              </Badge>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent>
        {chartData.length === 0 ? (
          <div className="flex h-64 items-center justify-center text-gray-500">
            <div className="text-center">
              <Users className="mx-auto mb-2 size-12 opacity-50" />
              <p>No employee performance data available</p>
            </div>
          </div>
        ) : (
          <>
            <ResponsiveContainer height={height} width="100%">
              <BarChart
                data={chartData}
                margin={{
                  bottom: 60,
                  left: 20,
                  right: 30,
                  top: 20,
                }}
              >
                <CartesianGrid stroke="#f0f0f0" strokeDasharray="3 3" />
                <XAxis
                  angle={-45}
                  dataKey="displayName"
                  fontSize={12}
                  height={60}
                  textAnchor="end"
                />
                <YAxis
                  domain={[0, 10]}
                  fontSize={12}
                  tickFormatter={value => `${value}/10`}
                />
                {interactive && <Tooltip content={<CustomTooltip />} />}
                <Bar
                  dataKey="averageRating"
                  name="Performance Score"
                  radius={[4, 4, 0, 0]}
                >
                  {chartData.map((entry, index) => (
                    <Cell fill={entry.color} key={`cell-${index}`} />
                  ))}
                </Bar>
              </BarChart>
            </ResponsiveContainer>

            {showLegend && (
              <div className="mt-4 flex flex-wrap gap-4 text-xs">
                <div className="flex items-center gap-1">
                  <div className="size-3 rounded bg-green-500"></div>
                  <span>Excellent (9-10)</span>
                </div>
                <div className="flex items-center gap-1">
                  <div className="size-3 rounded bg-blue-500"></div>
                  <span>Good (8-8.9)</span>
                </div>
                <div className="flex items-center gap-1">
                  <div className="size-3 rounded bg-orange-500"></div>
                  <span>Average (7-7.9)</span>
                </div>
                <div className="flex items-center gap-1">
                  <div className="size-3 rounded bg-red-500"></div>
                  <span>Below Average (6-6.9)</span>
                </div>
                <div className="flex items-center gap-1">
                  <div className="size-3 rounded bg-gray-500"></div>
                  <span>Poor (&lt;6)</span>
                </div>
              </div>
            )}

            {/* Top Performers List */}
            <div className="mt-6 space-y-2">
              <h4 className="text-sm font-medium">Top Performers</h4>
              <div className="space-y-2">
                {chartData.slice(0, 3).map((employee, index) => (
                  <div
                    className="flex items-center justify-between rounded bg-gray-50 p-2"
                    key={employee.employeeName}
                  >
                    <div className="flex items-center gap-2">
                      <Badge
                        className="flex size-6 items-center justify-center p-0 text-xs"
                        variant="outline"
                      >
                        {index + 1}
                      </Badge>
                      <Avatar className="size-6">
                        <AvatarFallback className="text-xs">
                          {employee.employeeName
                            .split(' ')
                            .map(n => n[0])
                            .join('')}
                        </AvatarFallback>
                      </Avatar>
                      <span className="text-sm font-medium">
                        {employee.employeeName}
                      </span>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-bold">
                        {employee.averageRating}/10
                      </div>
                      <div className="text-xs text-gray-500">
                        {employee.onTimePerformance}% on-time
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
};

export default EmployeePerformanceChart;
