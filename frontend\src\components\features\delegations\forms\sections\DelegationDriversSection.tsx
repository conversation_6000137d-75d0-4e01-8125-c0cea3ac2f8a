/**
 * DelegationDriversSection Component - SOLID Principles Implementation
 *
 * Single Responsibility: Handles driver selection and management
 * - Multi-select driver functionality with filtering
 * - Follows SRP by focusing only on driver selection
 *
 * @module DelegationDriversSection
 */

import React from 'react';
import { useFormContext } from 'react-hook-form';
import { Car, Trash2 } from 'lucide-react';

import type { DelegationFormData } from '@/lib/schemas/delegationSchemas';

import { Badge } from '@/components/ui/badge';
import { FormMessage } from '@/components/ui/form';
import { useEmployeesByRole } from '@/lib/stores/queries/useEmployees';

// ============================================================================
// INTERFACES
// ============================================================================

export interface DelegationDriversSectionProps {
  /** Whether the form is currently submitting */
  isSubmitting?: boolean;
  /** Additional CSS classes */
  className?: string;
}

// ============================================================================
// COMPONENT IMPLEMENTATION
// ============================================================================

/**
 * Drivers Section for Delegation Form
 *
 * Manages driver selection with multi-select functionality and filtering.
 * This component follows SRP by focusing solely on driver selection.
 */
export const DelegationDriversSection: React.FC<
  DelegationDriversSectionProps
> = ({ isSubmitting = false, className = '' }) => {
  const { watch, setValue, getValues } = useFormContext<DelegationFormData>();

  // Watch selected drivers
  const selectedDriverIds = watch('driverEmployeeIds') || [];

  // Fetch available drivers (lowercase as expected by backend)
  const {
    data: drivers = [],
    isLoading: driversLoading,
    error: driversError,
  } = useEmployeesByRole('driver');

  // Filter out already selected drivers for the dropdown
  const availableDrivers = drivers.filter(
    driver => !selectedDriverIds.includes(driver.id)
  );

  // Get selected driver details
  const selectedDrivers = drivers.filter(driver =>
    selectedDriverIds.includes(driver.id)
  );

  const handleAddDriver = (driverId: number) => {
    if (!selectedDriverIds.includes(driverId)) {
      const newDriverIds = [...selectedDriverIds, driverId];
      setValue('driverEmployeeIds', newDriverIds);
    }
  };

  const handleRemoveDriver = (driverId: number) => {
    const newDriverIds = selectedDriverIds.filter(id => id !== driverId);
    setValue('driverEmployeeIds', newDriverIds);
  };

  return (
    <section className={`space-y-4 rounded-lg border bg-card p-6 ${className}`}>
      <h3 className="flex items-center text-lg font-semibold text-foreground">
        <Car className="mr-2 size-5 text-accent" />
        Drivers
      </h3>

      {/* Driver Selection Dropdown */}
      <div className="space-y-2">
        <label className="text-sm font-medium text-foreground">
          Select Drivers
        </label>
        <select
          className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
          onChange={e => {
            const driverId = parseInt(e.target.value, 10);
            if (driverId) {
              handleAddDriver(driverId);
              e.target.value = ''; // Reset selection
            }
          }}
          disabled={isSubmitting || driversLoading}
        >
          <option value="">
            {driversLoading ? 'Loading drivers...' : 'Select a driver to add'}
          </option>
          {availableDrivers.map(driver => (
            <option key={driver.id} value={driver.id}>
              {driver.fullName || driver.name}
              {driver.employeeId && ` (${driver.employeeId})`}
              {driver.status && ` - ${driver.status}`}
              {driver.availability &&
                ` - ${driver.availability.replace('_', ' ')}`}
              {driver.currentLocation && ` @ ${driver.currentLocation}`}
            </option>
          ))}
        </select>
      </div>

      {/* Selected Drivers Display */}
      {selectedDrivers.length > 0 && (
        <div className="space-y-2">
          <label className="text-sm font-medium text-foreground">
            Selected Drivers ({selectedDrivers.length})
          </label>
          <div className="flex flex-wrap gap-2">
            {selectedDrivers.map(driver => (
              <Badge
                key={driver.id}
                variant="secondary"
                className="inline-flex items-center gap-1 px-3 py-1"
              >
                <div className="flex flex-col">
                  <span className="font-medium">
                    {driver.fullName || driver.name}
                  </span>
                  {(driver.status || driver.availability) && (
                    <span className="text-xs text-muted-foreground">
                      {driver.status}
                      {driver.status && driver.availability && ' • '}
                      {driver.availability?.replace('_', ' ')}
                    </span>
                  )}
                </div>
                {!isSubmitting && (
                  <button
                    type="button"
                    onClick={() => handleRemoveDriver(driver.id)}
                    className="ml-1 text-muted-foreground hover:text-destructive"
                    aria-label={`Remove ${driver.fullName || driver.name}`}
                  >
                    <Trash2 className="size-3" />
                  </button>
                )}
              </Badge>
            ))}
          </div>
        </div>
      )}

      {/* No drivers selected message */}
      {selectedDrivers.length === 0 && (
        <p className="text-sm text-muted-foreground">
          No drivers selected. Select drivers from the dropdown above.
        </p>
      )}

      {/* No available drivers message */}
      {!driversLoading && drivers.length === 0 && (
        <p className="text-sm text-muted-foreground">
          No drivers available. Please add drivers to the system first.
        </p>
      )}

      <FormMessage />
    </section>
  );
};

export default DelegationDriversSection;
