# =============================================================================
# WORKHUB FRONTEND ENVIRONMENT CONFIGURATION
# =============================================================================
#
# 🔐 PHASE 3 SECURITY HARDENING: Client-Side Security Configuration
# 📋 Copy this file to .env.local and update the values for your environment
#
# =============================================================================

# =============================================================================
# NEXT.JS CONFIGURATION
# =============================================================================

# Application Environment
NODE_ENV=development

# Next.js Configuration
NEXT_PUBLIC_APP_NAME=WorkHub
NEXT_PUBLIC_APP_VERSION=1.0.0

# =============================================================================
# BACKEND API CONFIGURATION
# =============================================================================

# Backend API URL (adjust for your environment)
NEXT_PUBLIC_API_URL=http://localhost:3001
NEXT_PUBLIC_API_BASE_URL=http://localhost:3001/api

# WebSocket Configuration
NEXT_PUBLIC_WS_URL=ws://localhost:3001

# =============================================================================
# SUPABASE CONFIGURATION (Client-Side)
# =============================================================================
#
# ⚠️  SECURITY WARNING: Supabase configuration should be handled securely
#
# For WorkHub, Supabase authentication is handled entirely on the backend
# for enhanced security. The frontend communicates with the backend API
# which then handles all Supabase interactions.
#
# If you need direct Supabase access in frontend (not recommended for security):
# NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
# NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key-here
#
# 🔐 RECOMMENDED APPROACH: Use backend API endpoints instead
# The backend handles all Supabase authentication and data access securely

# =============================================================================
# PHASE 3 SECURITY HARDENING: Client-Side Security Configuration
# =============================================================================

# Content Security Policy Configuration
# Comma-separated list of allowed connect sources for CSP
NEXT_PUBLIC_ALLOWED_CSP_CONNECT_SRC=https://api.example.com,https://cdn.example.com

# Docker Environment Detection (for CSP configuration)
NEXT_PUBLIC_DOCKER_ENV=false

# Security Headers Configuration
NEXT_PUBLIC_SECURITY_HEADERS_ENABLED=true

# CSRF Protection Configuration
NEXT_PUBLIC_CSRF_PROTECTION_ENABLED=true

# Session Security Configuration
NEXT_PUBLIC_SESSION_TIMEOUT=3600000
NEXT_PUBLIC_SESSION_WARNING_TIME=300000

# =============================================================================
# DEVELOPMENT CONFIGURATION
# =============================================================================

# Development Tools
NEXT_PUBLIC_DEV_TOOLS_ENABLED=true

# Debug Configuration
NEXT_PUBLIC_DEBUG_MODE=false
NEXT_PUBLIC_LOG_LEVEL=info

# =============================================================================
# ANALYTICS & MONITORING (Optional)
# =============================================================================

# Analytics Configuration (if using)
# NEXT_PUBLIC_ANALYTICS_ID=your-analytics-id
# NEXT_PUBLIC_MONITORING_ENABLED=false

# =============================================================================
# FEATURE FLAGS (Optional)
# =============================================================================

# Feature Toggles
NEXT_PUBLIC_FEATURE_REPORTING=true
NEXT_PUBLIC_FEATURE_ANALYTICS=true
NEXT_PUBLIC_FEATURE_NOTIFICATIONS=true

# =============================================================================
# PRODUCTION OVERRIDES
# =============================================================================
# For production deployment, override these values:
#
# NODE_ENV=production
# NEXT_PUBLIC_API_URL=https://your-production-api.com
# NEXT_PUBLIC_WS_URL=wss://your-production-api.com
# NEXT_PUBLIC_DOCKER_ENV=true
# NEXT_PUBLIC_DEBUG_MODE=false
# NEXT_PUBLIC_DEV_TOOLS_ENABLED=false
#
# =============================================================================

# =============================================================================
# SECURITY NOTES
# =============================================================================
#
# 🔐 SECURITY BEST PRACTICES:
# 1. Never include sensitive secrets in frontend environment variables
# 2. All NEXT_PUBLIC_ variables are exposed to the browser
# 3. Use backend environment variables for sensitive configuration
# 4. Validate all environment variables in your application
# 5. Use different values for development, staging, and production
#
# 🛡️ CSP CONFIGURATION:
# - Configure NEXT_PUBLIC_ALLOWED_CSP_CONNECT_SRC for your API endpoints
# - Update CSP policies in middleware.ts for your specific needs
# - Test CSP configuration thoroughly before production deployment
#
# 🚀 DEPLOYMENT:
# - Copy this file to .env.local for local development
# - Use .env.production for production builds
# - Configure environment variables in your deployment platform
#
# =============================================================================
