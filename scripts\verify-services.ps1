# WorkHub Service Health Verification Script
Write-Host "WorkHub Service Health Verification" -ForegroundColor Blue

# Check if services are running
Write-Host "`nChecking if services are running..." -ForegroundColor Cyan
docker-compose -f docker-compose.staging.yml ps

# Check backend health
Write-Host "`nChecking backend health..." -ForegroundColor Cyan
try {
    $backendResponse = Invoke-WebRequest -Uri "http://localhost:3001/api/health" -UseBasicParsing
    Write-Host "Backend health status: $($backendResponse.StatusCode) $($backendResponse.StatusDescription)" -ForegroundColor Green
}
catch {
    Write-Host "Backend health check failed: $_" -ForegroundColor Red
}

# Check frontend health
Write-Host "`nChecking frontend health..." -ForegroundColor Cyan
try {
    $frontendResponse = Invoke-WebRequest -Uri "http://localhost:3000" -UseBasicParsing
    Write-Host "Frontend health status: $($frontendResponse.StatusCode) $($frontendResponse.StatusDescription)" -ForegroundColor Green
}
catch {
    Write-Host "Frontend health check failed: $_" -ForegroundColor Red
}

# Check if containers are healthy
Write-Host "`nDetailed container health status:" -ForegroundColor Cyan
docker ps --format "table {{.Names}}\t{{.Status}}"

# Show container logs
Write-Host "`nBackend logs:" -ForegroundColor Cyan
docker-compose -f docker-compose.staging.yml logs --tail=20 backend

Write-Host "`nFrontend logs:" -ForegroundColor Cyan
docker-compose -f docker-compose.staging.yml logs --tail=20 frontend

Write-Host "`nNginx logs:" -ForegroundColor Cyan
docker-compose -f docker-compose.staging.yml logs --tail=20 nginx 