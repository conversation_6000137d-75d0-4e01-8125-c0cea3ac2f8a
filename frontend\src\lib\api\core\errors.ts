/**
 * @file Custom error classes for the API client.
 * @module api/base/errors
 */

// Import the enhanced ApiError and ApiErrorType from the central types definition
import { ApiError, ApiErrorType } from '../../types/api';

/**
 * Represents an error due to authentication failure (e.g., invalid token, expired token).
 */
export class AuthenticationError extends ApiError {
  constructor(message = 'Authentication Failed', details?: any) {
    super(message, {
      details,
      errorType: ApiErrorType.AUTHENTICATION_ERROR,
      status: 401,
    });
    this.name = 'AuthenticationError';
    Object.setPrototypeOf(this, AuthenticationError.prototype);
  }
}

/**
 * Represents an error due to invalid input or bad request.
 */
export class BadRequestError extends ApiError {
  constructor(message = 'Bad Request', details?: any) {
    super(message, {
      details,
      errorType: ApiErrorType.CLIENT_ERROR, // Or VALIDATION_ERROR if details imply it
      status: 400,
    });
    this.name = 'BadRequestError';
    Object.setPrototypeOf(this, BadRequestError.prototype);
  }
}

/**
 * Represents an error due to server-side issues.
 */
export class InternalServerError extends ApiError {
  constructor(message = 'Internal Server Error', details?: any) {
    super(message, {
      details,
      errorType: ApiErrorType.SERVER_ERROR,
      status: 500,
    });
    this.name = 'InternalServerError';
    Object.setPrototypeOf(this, InternalServerError.prototype);
  }
}

/**
 * Represents an error that occurs due to network issues (e.g., no internet connection, DNS lookup failure).
 */
export class NetworkError extends ApiError {
  constructor(message = 'Network Error', details?: any) {
    super(message, {
      details,
      errorType: ApiErrorType.NETWORK_ERROR,
      status: 0, // Using 0 for network errors where no HTTP status is received
    });
    this.name = 'NetworkError';
    Object.setPrototypeOf(this, NetworkError.prototype);
  }
}

/**
 * Represents an error when a requested resource is not found.
 */
export class NotFoundError extends ApiError {
  constructor(message = 'Resource Not Found', details?: any) {
    super(message, {
      details,
      errorType: ApiErrorType.NOT_FOUND,
      status: 404,
    });
    this.name = 'NotFoundError';
    Object.setPrototypeOf(this, NotFoundError.prototype);
  }
}

/**
 * Service error class for standardized service layer error handling
 */
export class ServiceError extends ApiError {
  constructor(
    message: string,
    public code: string,
    public statusCode?: number,
    public context?: Record<string, any>
  ) {
    super(message, {
      details: context,
      errorType: ApiErrorType.CLIENT_ERROR,
      status: statusCode || 500,
    });
    this.name = 'ServiceError';
    Object.setPrototypeOf(this, ServiceError.prototype);
  }
}

// Re-export ApiError and ApiErrorType for convenience if other modules import them from here
export { ApiError, ApiErrorType } from '../../types/api';
