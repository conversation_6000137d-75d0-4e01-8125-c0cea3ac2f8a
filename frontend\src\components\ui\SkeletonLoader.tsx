'use client';

import React from 'react';

import { Skeleton } from '@/components/ui/skeleton';
import { cn } from '@/lib/utils';

interface SkeletonLoaderProps {
  circle?: boolean;
  className?: string;
  count?: number;
  height?: number | string;
  testId?: string;
  variant?: 'card' | 'default' | 'list' | 'table';
  width?: number | string;
}

/**
 * Skeleton loader component for content placeholders
 * Supports different variants for common UI patterns
 */
const SkeletonLoader: React.FC<SkeletonLoaderProps> = ({
  circle = false,
  className,
  count = 1,
  height = 20,
  testId = 'loading-skeleton',
  variant = 'default',
  width = '100%',
}) => {
  // Convert height/width to string with px if they're numbers
  const heightStyle = typeof height === 'number' ? `${height}px` : height;
  const widthStyle = typeof width === 'number' ? `${width}px` : width;

  // Generate skeleton items based on count
  const renderDefaultSkeletons = () => {
    return new Array(count)
      .fill(0)
      .map((_, index) => (
        <Skeleton
          className={cn(circle ? 'rounded-full' : 'rounded-md', className)}
          key={index}
          style={{ height: heightStyle, width: widthStyle }}
        />
      ));
  };

  // Render card skeleton with header, content, and footer
  const renderCardSkeleton = () => (
    <div className="space-y-3" data-testid={testId}>
      <Skeleton className="h-8 w-3/4 rounded-md" />
      <Skeleton className="h-32 w-full rounded-md" />
      <div className="flex justify-between">
        <Skeleton className="h-6 w-24 rounded-md" />
        <Skeleton className="h-6 w-16 rounded-md" />
      </div>
    </div>
  );

  // Render table skeleton with header and rows
  const renderTableSkeleton = () => (
    <div className="space-y-3" data-testid={testId}>
      <div className="flex gap-4">
        {Array.from({length: 3})
          .fill(0)
          .map((_, index) => (
            <Skeleton className="h-8 flex-1 rounded-md" key={index} />
          ))}
      </div>
      {new Array(count)
        .fill(0)
        .map((_, index) => (
          <div className="flex gap-4" key={index}>
            {Array.from({length: 3})
              .fill(0)
              .map((_, cellIndex) => (
                <Skeleton className="h-6 flex-1 rounded-md" key={cellIndex} />
              ))}
          </div>
        ))}
    </div>
  );

  // Render list skeleton with items
  const renderListSkeleton = () => (
    <div className="space-y-3" data-testid={testId}>
      {new Array(count)
        .fill(0)
        .map((_, index) => (
          <div className="flex items-center gap-4" key={index}>
            <Skeleton className="size-12 rounded-full" />
            <div className="flex-1 space-y-2">
              <Skeleton className="h-4 w-1/3 rounded-md" />
              <Skeleton className="h-4 w-full rounded-md" />
            </div>
          </div>
        ))}
    </div>
  );

  // Render the appropriate skeleton based on variant
  switch (variant) {
    case 'card': {
      return renderCardSkeleton();
    }
    case 'list': {
      return renderListSkeleton();
    }
    case 'table': {
      return renderTableSkeleton();
    }
    default: {
      return (
        <div className="space-y-2" data-testid={testId}>
          {renderDefaultSkeletons()}
        </div>
      );
    }
  }
};

export default SkeletonLoader;
