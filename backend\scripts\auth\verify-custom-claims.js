/**
 * Verify Custom Claims Injection
 *
 * This script tests if the auth hook is properly injecting custom claims
 * by signing in with test credentials and examining the JWT token.
 */

import { createClient } from '@supabase/supabase-js';
import jwt from 'jsonwebtoken';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseAnonKey = process.env.SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing Supabase environment variables');
  console.error('Required: SUPABASE_URL, SUPABASE_ANON_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function verifyCustomClaims() {
  console.log('🔍 Verifying Custom Claims Injection\n');

  try {
    // Test with known user credentials
    const testEmail = '<EMAIL>';
    const testPassword = 'password123'; // You may need to adjust this

    console.log(`📧 Attempting to sign in with: ${testEmail}`);

    const { data: authData, error: signInError } = await supabase.auth.signInWithPassword({
      email: testEmail,
      password: testPassword,
    });

    if (signInError) {
      console.log('⚠️  Sign-in failed (expected if password is incorrect)');
      console.log('   This is normal - we just need to test with existing session');
      console.log('   Please sign in through your frontend and run this test again\n');

      // Try to get current session instead
      const { data: sessionData } = await supabase.auth.getSession();

      if (!sessionData.session) {
        console.log('❌ No active session found');
        console.log('   Please sign in through your frontend application first');
        return;
      }

      console.log('✅ Found existing session, analyzing token...\n');
      await analyzeToken(sessionData.session.access_token);
      return;
    }

    if (!authData.session) {
      console.log('❌ No session returned from sign-in');
      return;
    }

    console.log('✅ Sign-in successful, analyzing token...\n');
    await analyzeToken(authData.session.access_token);
  } catch (error) {
    console.error('❌ Error during verification:', error.message);
  }
}

async function analyzeToken(accessToken) {
  try {
    // Decode JWT without verification (we just want to see the payload)
    const decoded = jwt.decode(accessToken);

    if (!decoded) {
      console.log('❌ Failed to decode JWT token');
      return;
    }

    console.log('📋 JWT Token Analysis:');
    console.log('======================');
    console.log(`👤 User ID: ${decoded.sub}`);
    console.log(`📧 Email: ${decoded.email}`);
    console.log(`⏰ Expires: ${new Date(decoded.exp * 1000).toISOString()}`);

    // Check for custom claims
    if (decoded.user_role || decoded.custom_claims) {
      console.log('\n✅ CUSTOM CLAIMS FOUND:');

      if (decoded.custom_claims) {
        console.log('   📋 Custom Claims Object:');
        console.log(`      - user_role: ${decoded.custom_claims.user_role}`);
        console.log(`      - is_active: ${decoded.custom_claims.is_active}`);
        console.log(`      - employee_id: ${decoded.custom_claims.employee_id || 'null'}`);
      }

      if (decoded.user_role) {
        console.log(`   📋 Direct user_role: ${decoded.user_role}`);
      }

      console.log('\n🎉 AUTH HOOK IS WORKING CORRECTLY!');
      console.log('   Custom claims are being properly injected into JWT tokens');
      console.log('   The database fallback in supabaseAuth.ts can be safely removed');
    } else {
      console.log('\n❌ NO CUSTOM CLAIMS FOUND');
      console.log('   The auth hook may not be working properly');
      console.log('   Database fallback should remain in place');

      console.log('\n🔍 Available claims in token:');
      Object.keys(decoded).forEach(key => {
        if (!['iat', 'exp', 'iss', 'aud'].includes(key)) {
          console.log(`   - ${key}: ${JSON.stringify(decoded[key])}`);
        }
      });
    }
  } catch (error) {
    console.error('❌ Error analyzing token:', error.message);
  }
}

// Run the verification
verifyCustomClaims().catch(console.error);
