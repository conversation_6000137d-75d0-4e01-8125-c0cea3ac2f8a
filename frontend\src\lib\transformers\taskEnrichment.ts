/**
 * @file Task enrichment transformer following established patterns
 * @description Handles the enrichment of task data with employee and vehicle details
 * @module transformers/taskEnrichment
 */

import type { Employee, Task, Vehicle } from '../types/domain';

/**
 * Transformer class for enriching task data with related entities
 * Follows the same pattern as DelegationEnrichmentTransformer
 */
export class TaskEnrichmentTransformer {
  /**
   * Main enrichment method that combines task data with employee and vehicle details
   * @param task - Base task data
   * @param employees - Array of employees
   * @param vehicles - Array of vehicles
   * @returns Fully enriched task
   */
  static enrich(task: Task, employees: Employee[], vehicles: Vehicle[]): Task {
    const { employeeMap, vehicleMap } = this.createLookupMaps(
      employees,
      vehicles
    );

    // Apply all enrichments sequentially
    let enrichedTask = this.enrichStaffEmployee(task, employeeMap);
    enrichedTask = this.enrichDriverEmployee(enrichedTask, employeeMap);
    enrichedTask = this.enrichVehicle(enrichedTask, vehicleMap);

    return enrichedTask;
  }

  /**
   * Creates optimized lookup maps for O(1) performance
   * @param employees - Array of employees
   * @param vehicles - Array of vehicles
   * @returns Object containing employee and vehicle maps
   */
  private static createLookupMaps(employees: Employee[], vehicles: Vehicle[]) {
    // Defensive programming: Ensure inputs are arrays
    const safeEmployees = Array.isArray(employees) ? employees : [];
    const safeVehicles = Array.isArray(vehicles) ? vehicles : [];

    return {
      employeeMap: new Map(safeEmployees.map(emp => [emp.id, emp])),
      vehicleMap: new Map(safeVehicles.map(veh => [veh.id, veh])),
    };
  }

  /**
   * Enriches driver employee assignment with employee details
   * @param task - Base task data
   * @param employeeMap - Map of employees for O(1) lookup
   * @returns Task with enriched driver employee data
   */
  private static enrichDriverEmployee(
    task: Task,
    employeeMap: Map<number, Employee>
  ): Task {
    if (!task.driverEmployeeId) {
      return task;
    }

    const driverEmployee =
      task.driverEmployee ?? employeeMap.get(task.driverEmployeeId) ?? null;

    return {
      ...task,
      driverEmployee,
    };
  }

  /**
   * Enriches staff employee assignment with employee details
   * @param task - Base task data
   * @param employeeMap - Map of employees for O(1) lookup
   * @returns Task with enriched staff employee data
   */
  private static enrichStaffEmployee(
    task: Task,
    employeeMap: Map<number, Employee>
  ): Task {
    if (!task.staffEmployeeId) {
      return task;
    }

    const staffEmployee =
      task.staffEmployee ?? employeeMap.get(task.staffEmployeeId) ?? null;

    return {
      ...task,
      staffEmployee,
    };
  }

  /**
   * Enriches vehicle assignment with vehicle details
   * @param task - Base task data
   * @param vehicleMap - Map of vehicles for O(1) lookup
   * @returns Task with enriched vehicle data
   */
  private static enrichVehicle(
    task: Task,
    vehicleMap: Map<number, Vehicle>
  ): Task {
    if (!task.vehicleId) {
      return task;
    }

    const vehicle = task.vehicle ?? vehicleMap.get(task.vehicleId) ?? null;

    return {
      ...task,
      vehicle,
    };
  }
}

// Export the main enrichment function for backward compatibility
export const enrichTask = (
  task: Task,
  employees: Employee[],
  vehicles: Vehicle[]
): Task => {
  return TaskEnrichmentTransformer.enrich(task, employees, vehicles);
};
