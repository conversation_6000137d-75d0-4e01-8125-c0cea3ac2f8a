import * as z from 'zod';
import type { CreateServiceRecordPayload } from '@/lib/stores/queries/useServiceRecords';
import { formatDateForApi } from '@/lib/utils/dateUtils'; // Import formatDateForApi

/**
 * @file Service Record Validation Schemas
 * @description Centralized validation schemas for service record forms and API payloads
 * Following established architectural patterns and SOLID principles
 */

// Base service record validation schema for forms
const serviceRecordBaseSchema = {
  date: z.string().min(1, 'Date is required'),
  servicePerformed: z
    .array(z.string().min(1, 'Service cannot be empty'))
    .min(1, 'At least one service must be performed')
    .max(10, 'Maximum 10 services allowed'),
  odometer: z.coerce
    .number({ required_error: 'Odometer reading is required' })
    .min(0, 'Odometer cannot be negative')
    .max(9999999, 'Odometer reading seems unrealistic'),
  cost: z.coerce
    .number({ required_error: 'Cost is required' })
    .min(0, 'Cost cannot be negative')
    .max(999999, 'Cost seems unrealistic'),
  notes: z.string().max(1000, 'Notes cannot exceed 1000 characters').optional(),
  employeeId: z.coerce.number().nullable().optional(),
};

/**
 * Schema for service record form validation
 * Used in edit and create forms
 */
export const ServiceRecordFormSchema = z.object(serviceRecordBaseSchema);

/**
 * Schema for service record creation
 * Includes vehicleId which is required for creation
 */
export const ServiceRecordCreateSchema = z.object({
  ...serviceRecordBaseSchema,
  vehicleId: z.number().positive('Vehicle ID is required'),
});

/**
 * Schema for service record updates
 * All fields are optional for partial updates
 */
export const ServiceRecordUpdateSchema = z
  .object(serviceRecordBaseSchema)
  .partial();

// Type exports
export type ServiceRecordFormData = z.infer<typeof ServiceRecordFormSchema>;
export type ServiceRecordCreateData = z.infer<typeof ServiceRecordCreateSchema>;
export type ServiceRecordUpdateData = z.infer<typeof ServiceRecordUpdateSchema>;

/**
 * Service Record Schema Utilities
 * Provides transformation and validation helpers
 */
export const ServiceRecordSchemaUtils = {
  /**
   * Validates form data against the service record schema
   */
  validateFormData: (data: unknown) => {
    return ServiceRecordFormSchema.safeParse(data);
  },

  /**
   * Validates and transforms form data to API payload format
   * Ensures compatibility with CreateServiceRecordPayload interface
   * Handles exactOptionalPropertyTypes by only including defined values
   */
  transformToApiPayload: (
    formData: ServiceRecordFormData,
    vehicleId: number
  ): CreateServiceRecordPayload => {
    // Build payload with required fields
    const payload: CreateServiceRecordPayload = {
      date: formData.date,
      servicePerformed: formData.servicePerformed,
      odometer: formData.odometer,
      vehicleId,
    };

    // Only include cost if it's a valid number
    if (typeof formData.cost === 'number' && formData.cost >= 0) {
      payload.cost = formData.cost;
    }

    // Only include notes if it's a non-empty string
    if (formData.notes && formData.notes.trim().length > 0) {
      payload.notes = formData.notes.trim();
    }

    // Handle employeeId - can be null or number
    if (formData.employeeId !== undefined) {
      payload.employeeId = formData.employeeId;
    }

    return payload;
  },

  /**
   * Transforms form data to update payload format
   * Only includes changed/defined fields for partial updates
   */
  transformToUpdatePayload: (
    formData: Partial<ServiceRecordFormData>
  ): Partial<CreateServiceRecordPayload> => {
    const payload: Partial<CreateServiceRecordPayload> = {};

    // Only include fields that are defined
    if (formData.date) {
      // Convert YYYY-MM-DD to ISO string using the utility function
      payload.date = formatDateForApi(formData.date, {
        primaryFormat: 'US',
        fallbackToBothFormats: true,
      });
    }

    if (formData.servicePerformed !== undefined) {
      payload.servicePerformed = formData.servicePerformed;
    }

    if (formData.odometer !== undefined) {
      payload.odometer = formData.odometer;
    }

    // Only include cost if it's a valid number and not 0 (to align with optional/nullable backend)
    if (typeof formData.cost === 'number' && formData.cost > 0) {
      payload.cost = formData.cost;
    } else if (formData.cost === 0) {
      // If cost is 0, send undefined to align with backend's optional/nullable
      payload.cost = undefined;
    }

    if (formData.notes !== undefined) {
      if (formData.notes && formData.notes.trim().length > 0) {
        payload.notes = formData.notes.trim();
      }
      // Note: We don't set notes to undefined to avoid exactOptionalPropertyTypes issues
    }

    if (formData.employeeId !== undefined) {
      payload.employeeId = formData.employeeId;
    }

    return payload;
  },

  /**
   * Validates service performed array
   */
  validateServicePerformed: (services: string[]) => {
    return (
      services.length > 0 &&
      services.length <= 10 &&
      services.every(s => s.trim().length > 0)
    );
  },

  /**
   * Formats service performed string for display
   */
  formatServicePerformed: (services: string[]) => {
    return services.join(', ');
  },

  /**
   * Parses comma-separated service string into array
   */
  parseServicePerformed: (serviceString: string) => {
    return serviceString
      .split(',')
      .map(s => s.trim())
      .filter(s => s.length > 0);
  },
};
