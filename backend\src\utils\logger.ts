/**
 * Enhanced Logger utility for WorkHub application
 *
 * This module provides a centralized, enterprise-grade logging mechanism.
 * Features:
 * - Structured JSON logging for production
 * - Colorized console output for development
 * - Request correlation IDs
 * - Performance monitoring
 * - Security event logging
 * - Automatic log rotation
 */

import fs from 'fs';
import os from 'os';
import path from 'path';
import winston from 'winston';

// Create logs directory if it doesn't exist
const logsDir = path.join(process.cwd(), 'logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Enhanced structured format for production
const structuredFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss.SSS' }),
  winston.format.errors({ stack: true }),
  winston.format.splat(),
  winston.format.metadata({
    fillExcept: ['message', 'level', 'timestamp', 'service'],
  }),
  winston.format.json(),
);

// Enhanced console format for development
const consoleFormat = winston.format.combine(
  winston.format.colorize({ all: true }),
  winston.format.timestamp({ format: 'HH:mm:ss.SSS' }),
  winston.format.errors({ stack: true }),
  winston.format.printf((info: winston.Logform.TransformableInfo) => {
    const { level, message, requestId, service, timestamp, userId, ...meta } = info;

    // Build context string
    const context = [];
    if (service && service !== 'workhub-backend') context.push(`[${service}]`);
    if (requestId && typeof requestId === 'string') context.push(`req:${requestId.slice(-8)}`);
    if (userId && typeof userId === 'string') context.push(`user:${userId.slice(-8)}`);

    const contextStr = context.length > 0 ? ` [${context.join('][')}]` : '';
    const metaStr = Object.keys(meta).length > 0 ? ` ${JSON.stringify(meta)}` : '';

    return `${String(timestamp)} ${level} $string{message}${contextStr}${metaStr}`;
  }),
);

// Create the enhanced logger
const logger = winston.createLogger({
  defaultMeta: {
    environment: process.env.NODE_ENV ?? 'development',
    hostname: process.env.HOSTNAME ?? os.hostname(),
    service: 'workhub-backend',
    version: process.env.npm_package_version ?? '1.0.0',
  },
  exceptionHandlers: [
    new winston.transports.File({
      filename: path.join(logsDir, 'exceptions.log'),
      maxFiles: 5,
      maxsize: 10485760, // 10MB
    }),
  ],
  format: structuredFormat,
  level: process.env.LOG_LEVEL || 'info',
  rejectionHandlers: [
    new winston.transports.File({
      filename: path.join(logsDir, 'rejections.log'),
      maxFiles: 5,
      maxsize: 10485760, // 10MB
    }),
  ],
  transports: [
    // Error logs
    new winston.transports.File({
      filename: path.join(logsDir, 'error.log'),
      level: 'error',
      maxFiles: 10,
      maxsize: 10485760, // 10MB
      tailable: true,
    }),
    // Combined logs
    new winston.transports.File({
      filename: path.join(logsDir, 'combined.log'),
      maxFiles: 10,
      maxsize: 10485760, // 10MB
      tailable: true,
    }),
    // Security logs
    new winston.transports.File({
      filename: path.join(logsDir, 'security.log'),
      level: 'warn',
      maxFiles: 5,
      maxsize: 10485760, // 10MB
      tailable: true,
    }),
  ],
});

// Add console transport for non-production environments
if (process.env.NODE_ENV !== 'production') {
  logger.add(
    new winston.transports.Console({
      format: consoleFormat,
      level: 'debug', // More verbose in development
    }),
  );
}

// Enhanced logging methods with context
export const createContextLogger = (context: {
  [key: string]: any;
  requestId?: string;
  service?: string;
  userId?: string;
}) => {
  return {
    debug: (message: string, meta?: any) => logger.debug(message, { ...context, ...meta }),
    error: (message: string, meta?: any) => logger.error(message, { ...context, ...meta }),
    http: (message: string, meta?: any) => logger.http(message, { ...context, ...meta }),
    info: (message: string, meta?: any) => logger.info(message, { ...context, ...meta }),
    warn: (message: string, meta?: any) => logger.warn(message, { ...context, ...meta }),
  };
};

// Security event logger
export const logSecurityEvent = (
  event: string,
  details: any,
  severity: 'error' | 'warn' = 'warn',
) => {
  logger.log(severity, `Security Event: ${event}`, {
    details,
    event,
    securityEvent: true,
    timestamp: new Date().toISOString(),
  });
};

// Performance logger
export const logPerformance = (operation: string, duration: number, meta?: any) => {
  logger.info(`Performance: ${operation}`, {
    duration,
    operation,
    performance: true,
    ...meta,
  });
};

// Startup logger
export const logStartup = (message: string, meta?: any, p0?: string) => {
  logger.info(`Startup: ${message}`, {
    startup: true,
    ...meta,
  });
};

export { logger };
export default logger;
