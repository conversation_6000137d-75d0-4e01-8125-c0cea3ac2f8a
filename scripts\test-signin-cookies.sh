#!/bin/bash
# 🍪 Complete Signin Cookies Testing Script
# WorkHub Authentication System Test Suite

echo "🍪 Testing WorkHub Signin Cookies Implementation"
echo "================================================"

# Configuration
BASE_URL="http://localhost:3001"
TEST_EMAIL="<EMAIL>"
TEST_PASSWORD="TestPassword123!"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
success() {
    echo -e "${GREEN}✅ $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
}

warning() {
    echo -e "${YELLOW}⚠️ $1${NC}"
}

info() {
    echo -e "${BLUE}ℹ️ $1${NC}"
}

# Test 1: Server Health Check
echo -e "\n${BLUE}1. Checking Server Health...${NC}"
if curl -s $BASE_URL/api/health > /dev/null; then
    success "Server is running on $BASE_URL"
else
    error "Server is not responding on $BASE_URL"
    echo "Please start the backend server first:"
    echo "  cd backend && npm run dev"
    exit 1
fi

# Test 2: Login with Cookie Setting
echo -e "\n${BLUE}2. Testing Login with Cookie Setting...${NC}"
LOGIN_RESPONSE=$(curl -X POST $BASE_URL/api/auth/login \
  -H "Content-Type: application/json" \
  -d "{\"email\":\"$TEST_EMAIL\",\"password\":\"$TEST_PASSWORD\"}" \
  -c cookies.txt \
  -w "%{http_code}" \
  -s \
  -o login_response.json)

if [ "$LOGIN_RESPONSE" = "200" ]; then
    success "Login successful (HTTP 200)"
    if [ -f login_response.json ]; then
        MESSAGE=$(cat login_response.json | jq -r '.message // "No message"')
        info "Response: $MESSAGE"
    fi
else
    error "Login failed (HTTP $LOGIN_RESPONSE)"
    if [ -f login_response.json ]; then
        cat login_response.json | jq .
    fi
    exit 1
fi

# Test 3: Verify Cookies Were Set
echo -e "\n${BLUE}3. Verifying Cookies Were Set...${NC}"
if [ -f cookies.txt ]; then
    success "Cookies file created"
    
    if grep -q "sb-access-token" cookies.txt; then
        success "Access token cookie found"
        ACCESS_COOKIE=$(grep "sb-access-token" cookies.txt | cut -f7)
        info "Access token format: ${ACCESS_COOKIE:0:50}..."
    else
        error "Access token cookie not found"
    fi
    
    if grep -q "sb-refresh-token" cookies.txt; then
        success "Refresh token cookie found"
    else
        warning "Refresh token cookie not found"
    fi
else
    error "No cookies file created"
    exit 1
fi

# Test 4: Test API with Cookies
echo -e "\n${BLUE}4. Testing API with Cookies...${NC}"
API_RESPONSE=$(curl -X GET $BASE_URL/api/vehicles \
  -b cookies.txt \
  -w "%{http_code}" \
  -s \
  -o vehicles_response.json)

if [ "$API_RESPONSE" = "200" ]; then
    success "API call with cookies successful (HTTP 200)"
    if [ -f vehicles_response.json ]; then
        VEHICLE_COUNT=$(cat vehicles_response.json | jq '. | length // 0')
        info "Vehicles returned: $VEHICLE_COUNT"
    fi
else
    error "API call with cookies failed (HTTP $API_RESPONSE)"
    if [ -f vehicles_response.json ]; then
        cat vehicles_response.json | jq .
    fi
fi

# Test 5: Test API without Cookies (should fail)
echo -e "\n${BLUE}5. Testing API without Cookies (should fail)...${NC}"
NO_COOKIE_RESPONSE=$(curl -X GET $BASE_URL/api/vehicles \
  -w "%{http_code}" \
  -s \
  -o no_cookie_response.json)

if [ "$NO_COOKIE_RESPONSE" = "401" ]; then
    success "API correctly rejected request without cookies (HTTP 401)"
else
    warning "API did not reject request without cookies (HTTP $NO_COOKIE_RESPONSE)"
    info "This might be due to Authorization header fallback"
fi

# Test 6: Test Auth Test Endpoint
echo -e "\n${BLUE}6. Testing Auth Test Endpoint...${NC}"
AUTH_TEST_RESPONSE=$(curl -X GET $BASE_URL/api/auth/test \
  -b cookies.txt \
  -w "%{http_code}" \
  -s \
  -o auth_test_response.json)

if [ "$AUTH_TEST_RESPONSE" = "200" ]; then
    success "Auth test endpoint successful (HTTP 200)"
    if [ -f auth_test_response.json ]; then
        USER_EMAIL=$(cat auth_test_response.json | jq -r '.user.email // "No email"')
        USER_ROLE=$(cat auth_test_response.json | jq -r '.user.roleFromMiddleware // "No role"')
        info "Authenticated as: $USER_EMAIL (Role: $USER_ROLE)"
    fi
else
    error "Auth test endpoint failed (HTTP $AUTH_TEST_RESPONSE)"
fi

# Test 7: Test Logout (Cookie Clearing)
echo -e "\n${BLUE}7. Testing Logout (Cookie Clearing)...${NC}"
LOGOUT_RESPONSE=$(curl -X POST $BASE_URL/api/auth/logout \
  -b cookies.txt \
  -c cookies_after_logout.txt \
  -w "%{http_code}" \
  -s \
  -o logout_response.json)

if [ "$LOGOUT_RESPONSE" = "200" ]; then
    success "Logout successful (HTTP 200)"
else
    error "Logout failed (HTTP $LOGOUT_RESPONSE)"
fi

# Test 8: Verify Cookies Were Cleared
echo -e "\n${BLUE}8. Verifying Cookies Were Cleared...${NC}"
if [ -f cookies_after_logout.txt ]; then
    if grep -q "sb-access-token" cookies_after_logout.txt; then
        error "Access token cookie still present after logout"
    else
        success "Access token cookie cleared"
    fi
    
    if grep -q "sb-refresh-token" cookies_after_logout.txt; then
        error "Refresh token cookie still present after logout"
    else
        success "Refresh token cookie cleared"
    fi
else
    success "No cookies after logout"
fi

# Test 9: Verify API Fails After Logout
echo -e "\n${BLUE}9. Testing API After Logout (should fail)...${NC}"
POST_LOGOUT_RESPONSE=$(curl -X GET $BASE_URL/api/vehicles \
  -b cookies_after_logout.txt \
  -w "%{http_code}" \
  -s \
  -o post_logout_response.json)

if [ "$POST_LOGOUT_RESPONSE" = "401" ]; then
    success "API correctly rejected request after logout (HTTP 401)"
else
    warning "API did not reject request after logout (HTTP $POST_LOGOUT_RESPONSE)"
fi

# Summary
echo -e "\n${BLUE}🎯 Testing Complete!${NC}"
echo "================================================"

# Check if all critical tests passed
CRITICAL_TESTS=0
PASSED_TESTS=0

# Login test
if [ "$LOGIN_RESPONSE" = "200" ]; then
    ((PASSED_TESTS++))
fi
((CRITICAL_TESTS++))

# Cookie setting test
if [ -f cookies.txt ] && grep -q "sb-access-token" cookies.txt; then
    ((PASSED_TESTS++))
fi
((CRITICAL_TESTS++))

# API with cookies test
if [ "$API_RESPONSE" = "200" ]; then
    ((PASSED_TESTS++))
fi
((CRITICAL_TESTS++))

# Logout test
if [ "$LOGOUT_RESPONSE" = "200" ]; then
    ((PASSED_TESTS++))
fi
((CRITICAL_TESTS++))

echo "Critical Tests Passed: $PASSED_TESTS/$CRITICAL_TESTS"

if [ $PASSED_TESTS -eq $CRITICAL_TESTS ]; then
    success "All critical tests passed! 🎉"
    success "Signin cookies implementation is working correctly!"
else
    error "Some critical tests failed. Please check the implementation."
fi

echo ""
info "Check backend logs for detailed authentication debug output"
info "Look for '🔍 BACKEND AUTH DEBUG' sections in the server console"

# Cleanup
echo -e "\n${BLUE}Cleaning up test files...${NC}"
rm -f cookies.txt cookies_after_logout.txt
rm -f login_response.json vehicles_response.json no_cookie_response.json
rm -f auth_test_response.json logout_response.json post_logout_response.json

success "Cleanup complete"
