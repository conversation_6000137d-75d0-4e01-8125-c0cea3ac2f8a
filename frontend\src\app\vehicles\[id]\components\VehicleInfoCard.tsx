'use client';

import { CalendarDays, Gauge, Palette, Tag, Wrench } from 'lucide-react';
import Image from 'next/image';

import type { Vehicle } from '@/lib/types/domain';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
// Unused imports removed since edit button is commented out
// import { Edit } from 'lucide-react';
// import { Button } from '@/components/ui/button';
// import Link from 'next/link';

interface InfoItemProps {
  icon: React.ElementType;
  label: string;
  value: string;
}

interface VehicleInfoCardProps {
  vehicle: Vehicle;
}

export default function VehicleInfoCard({ vehicle }: VehicleInfoCardProps) {
  const serviceHistory = vehicle.serviceHistory || [];
  const hasServiceHistory = serviceHistory.length > 0;
  const hasInitialOdometer =
    vehicle.initialOdometer !== null && vehicle.initialOdometer !== undefined;

  const latestOdometer = hasServiceHistory
    ? Math.max(
        ...serviceHistory.map(s => s.odometer),
        vehicle.initialOdometer || 0
      )
    : vehicle.initialOdometer || 0;

  const formatInitialOdometer = (): string => {
    return hasInitialOdometer
      ? `${vehicle.initialOdometer!.toLocaleString()} miles`
      : 'Not recorded';
  };

  const formatLatestOdometer = (): string => {
    if (hasServiceHistory) {
      return `${latestOdometer.toLocaleString()} miles`;
    } else if (hasInitialOdometer) {
      return `${vehicle.initialOdometer!.toLocaleString()} miles`;
    } else {
      return 'No odometer data';
    }
  };

  return (
    <Card className="bg-card shadow-lg">
      <CardHeader className="flex flex-row items-start justify-between gap-4 border-b p-5">
        <div>
          <CardTitle className="mb-1 text-2xl font-bold text-primary">
            {vehicle.make} {vehicle.model}
          </CardTitle>
          <p className="text-sm text-muted-foreground">Vehicle Overview</p>
        </div>
        {/* Placeholder for Edit Button - functionality to be fully implemented if needed */}
        {/* <ActionButton actionType="secondary" size="sm" asChild icon={<Edit className="mr-2 h-3 w-3" />}>
          <Link href={`/vehicles/edit/${vehicle.id}`}> Edit </Link>
        </ActionButton> */}
      </CardHeader>
      <CardContent className="grid gap-x-8 gap-y-4 p-5 md:grid-cols-2">
        <div className="relative mb-4 aspect-[16/10] w-full overflow-hidden rounded-lg shadow-sm md:col-span-2 lg:col-span-1 lg:mb-0">
          <Image
            alt={`${vehicle.make} ${vehicle.model}`}
            className="bg-muted object-cover"
            data-ai-hint="car side"
            fill
            priority
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            src={
              vehicle.imageUrl ||
              `https://picsum.photos/seed/${vehicle.id}/600/375`
            }
          />
        </div>
        <div className="space-y-3 text-foreground lg:col-span-1">
          <InfoItem
            icon={CalendarDays}
            label="Year"
            value={vehicle.year.toString()}
          />
          {vehicle.licensePlate && (
            <InfoItem
              icon={Tag}
              label="License Plate"
              value={vehicle.licensePlate}
            />
          )}
          {vehicle.color && (
            <InfoItem icon={Palette} label="Color" value={vehicle.color} />
          )}
          <InfoItem
            icon={Gauge}
            label="Initial Odometer"
            value={formatInitialOdometer()}
          />
          <InfoItem
            icon={Gauge}
            label="Latest Odometer"
            value={formatLatestOdometer()}
          />
          <InfoItem
            icon={Wrench}
            label="Services Logged"
            value={serviceHistory.length.toString()}
          />
        </div>
      </CardContent>
    </Card>
  );
}

function InfoItem({ icon: Icon, label, value }: InfoItemProps) {
  return (
    <div className="flex items-center">
      <Icon className="mr-3 size-5 shrink-0 text-accent" />
      <div>
        <p className="text-sm font-medium text-muted-foreground">{label}</p>
        <p className="text-md font-semibold">{value}</p>
      </div>
    </div>
  );
}
