/**
 * Metrics Collection Service
 *
 * Provides comprehensive metrics collection using Prometheus-style metrics
 * for monitoring application performance, business logic, and system health.
 *
 * Features:
 * - Prometheus-compatible metrics (counters, gauges, histograms, summaries)
 * - Express middleware for automatic HTTP metrics
 * - Custom business metrics
 * - Circuit breaker metrics integration
 * - Request deduplication metrics
 * - System resource metrics
 */

import type { NextFunction, Request, Response } from 'express';

import os from 'os';
import process from 'process';
import promClient from 'prom-client';

import logger from '../utils/logger.js';
import { getUnifiedWebSocketService } from './UnifiedWebSocketService.js';
import { RELIABILITY_EVENTS } from './WebSocketEventManager.js';
import type { SystemMetrics } from '../types/websocketEvents.js';

// Initialize Prometheus client
const register = new promClient.Registry();

// Add default metrics (CPU, memory, etc.)
promClient.collectDefaultMetrics({
  gcDurationBuckets: [0.001, 0.01, 0.1, 1, 2, 5],
  prefix: 'workhub_',
  register,
});

// HTTP Request Metrics
const httpRequestDuration = new promClient.Histogram({
  buckets: [0.1, 0.3, 0.5, 0.7, 1, 3, 5, 7, 10],
  help: 'Duration of HTTP requests in seconds',
  labelNames: ['method', 'route', 'status_code', 'user_role'],
  name: 'workhub_http_request_duration_seconds',
});

const httpRequestTotal = new promClient.Counter({
  help: 'Total number of HTTP requests',
  labelNames: ['method', 'route', 'status_code', 'user_role'],
  name: 'workhub_http_requests_total',
});

const httpRequestSize = new promClient.Histogram({
  buckets: [100, 1000, 10000, 100000, 1000000],
  help: 'Size of HTTP requests in bytes',
  labelNames: ['method', 'route'],
  name: 'workhub_http_request_size_bytes',
});

const httpResponseSize = new promClient.Histogram({
  buckets: [100, 1000, 10000, 100000, 1000000],
  help: 'Size of HTTP responses in bytes',
  labelNames: ['method', 'route', 'status_code'],
  name: 'workhub_http_response_size_bytes',
});

// Database Metrics
const databaseConnectionsActive = new promClient.Gauge({
  help: 'Number of active database connections',
  name: 'workhub_database_connections_active',
});

const databaseQueryDuration = new promClient.Histogram({
  buckets: [0.01, 0.05, 0.1, 0.5, 1, 2, 5],
  help: 'Duration of database queries in seconds',
  labelNames: ['operation', 'table', 'status'],
  name: 'workhub_database_query_duration_seconds',
});

const databaseQueryTotal = new promClient.Counter({
  help: 'Total number of database queries',
  labelNames: ['operation', 'table', 'status'],
  name: 'workhub_database_queries_total',
});

// Circuit Breaker Metrics
const circuitBreakerState = new promClient.Gauge({
  help: 'Circuit breaker state (0=closed, 1=half-open, 2=open)',
  labelNames: ['service', 'breaker_name'],
  name: 'workhub_circuit_breaker_state',
});

const circuitBreakerRequests = new promClient.Counter({
  help: 'Total circuit breaker requests',
  labelNames: ['service', 'breaker_name', 'result'],
  name: 'workhub_circuit_breaker_requests_total',
});

// Request Deduplication Metrics
const deduplicationRequests = new promClient.Counter({
  help: 'Total requests processed by deduplication middleware',
  labelNames: ['result'], // hit, miss, error
  name: 'workhub_deduplication_requests_total',
});

const deduplicationCacheSize = new promClient.Gauge({
  help: 'Number of items in deduplication cache',
  name: 'workhub_deduplication_cache_size',
});

// Business Logic Metrics
const userOperations = new promClient.Counter({
  help: 'Total user operations',
  labelNames: ['operation', 'user_role', 'status'],
  name: 'workhub_user_operations_total',
});

const adminOperations = new promClient.Counter({
  help: 'Total admin operations',
  labelNames: ['operation', 'admin_role', 'status'],
  name: 'workhub_admin_operations_total',
});

const delegationOperations = new promClient.Counter({
  help: 'Total delegation operations',
  labelNames: ['operation', 'status'],
  name: 'workhub_delegation_operations_total',
});

// Enhanced WorkHub Business Metrics

// Employee Metrics
const employeeOperations = new promClient.Counter({
  help: 'Total employee operations',
  labelNames: ['operation', 'employee_role', 'status'],
  name: 'workhub_employee_operations_total',
});

const employeeUtilization = new promClient.Gauge({
  help: 'Employee utilization ratio (0-1)',
  labelNames: ['employee_id', 'employee_role', 'department'],
  name: 'workhub_employee_utilization_ratio',
});

const employeeAvailability = new promClient.Gauge({
  help: 'Number of available employees by role',
  labelNames: ['employee_role', 'department'],
  name: 'workhub_employee_availability_total',
});

// Task Metrics
const taskOperations = new promClient.Counter({
  help: 'Total task operations',
  labelNames: ['operation', 'task_priority', 'status'],
  name: 'workhub_task_operations_total',
});

const taskDuration = new promClient.Histogram({
  buckets: [15, 30, 60, 120, 240, 480, 960], // 15min to 16 hours
  help: 'Task completion duration in minutes',
  labelNames: ['task_priority', 'employee_role', 'status'],
  name: 'workhub_task_duration_minutes',
});

const taskAssignments = new promClient.Gauge({
  help: 'Number of active task assignments',
  labelNames: ['task_priority', 'employee_role'],
  name: 'workhub_task_assignments_active',
});

// Vehicle Metrics
const vehicleOperations = new promClient.Counter({
  help: 'Total vehicle operations',
  labelNames: ['operation', 'vehicle_type', 'status'],
  name: 'workhub_vehicle_operations_total',
});

const vehicleUtilization = new promClient.Gauge({
  help: 'Vehicle utilization ratio (0-1)',
  labelNames: ['vehicle_id', 'vehicle_type'],
  name: 'workhub_vehicle_utilization_ratio',
});

const vehicleAssignments = new promClient.Gauge({
  help: 'Number of active vehicle assignments',
  labelNames: ['vehicle_type', 'assignment_type'], // task, delegation
  name: 'workhub_vehicle_assignments_active',
});

// Delegation Enhanced Metrics
const delegationDuration = new promClient.Histogram({
  buckets: [1, 2, 4, 8, 12, 24, 48, 72], // 1 hour to 3 days
  help: 'Delegation duration in hours',
  labelNames: ['delegation_type', 'status'],
  name: 'workhub_delegation_duration_hours',
});

const delegationAssignments = new promClient.Gauge({
  help: 'Number of active delegation assignments',
  labelNames: ['delegation_type', 'assignment_role'], // escort, driver
  name: 'workhub_delegation_assignments_active',
});

// User Session Metrics
const userSessions = new promClient.Counter({
  help: 'Total user sessions',
  labelNames: ['user_role', 'session_type'], // login, logout, timeout
  name: 'workhub_user_sessions_total',
});

const userSessionDuration = new promClient.Histogram({
  buckets: [5, 15, 30, 60, 120, 240, 480], // 5min to 8 hours
  help: 'User session duration in minutes',
  labelNames: ['user_role'],
  name: 'workhub_user_session_duration_minutes',
});

const activeUserSessions = new promClient.Gauge({
  help: 'Number of active user sessions',
  labelNames: ['user_role'],
  name: 'workhub_user_sessions_active',
});

// API Performance Metrics (Enhanced)
const apiEndpointDuration = new promClient.Histogram({
  buckets: [0.1, 0.3, 0.5, 1, 2, 5, 10],
  help: 'API endpoint response time in seconds',
  labelNames: ['endpoint', 'method', 'status_code'],
  name: 'workhub_api_endpoint_duration_seconds',
});

const apiEndpointRequests = new promClient.Counter({
  help: 'Total API endpoint requests',
  labelNames: ['endpoint', 'method', 'status_code', 'user_role'],
  name: 'workhub_api_endpoint_requests_total',
});

// System Resource Metrics
const systemLoad = new promClient.Gauge({
  help: 'System load average',
  labelNames: ['period'], // 1m, 5m, 15m
  name: 'workhub_system_load_average',
});

const memoryUsage = new promClient.Gauge({
  help: 'Memory usage in bytes',
  labelNames: ['type'], // rss, heapTotal, heapUsed, external
  name: 'workhub_memory_usage_bytes',
});

// Register all metrics
register.registerMetric(httpRequestDuration);
register.registerMetric(httpRequestTotal);
register.registerMetric(httpRequestSize);
register.registerMetric(httpResponseSize);
register.registerMetric(databaseConnectionsActive);
register.registerMetric(databaseQueryDuration);
register.registerMetric(databaseQueryTotal);
register.registerMetric(circuitBreakerState);
register.registerMetric(circuitBreakerRequests);
register.registerMetric(deduplicationRequests);
register.registerMetric(deduplicationCacheSize);
register.registerMetric(userOperations);
register.registerMetric(adminOperations);
register.registerMetric(delegationOperations);

// Register new WorkHub business metrics
register.registerMetric(employeeOperations);
register.registerMetric(employeeUtilization);
register.registerMetric(employeeAvailability);
register.registerMetric(taskOperations);
register.registerMetric(taskDuration);
register.registerMetric(taskAssignments);
register.registerMetric(vehicleOperations);
register.registerMetric(vehicleUtilization);
register.registerMetric(vehicleAssignments);
register.registerMetric(delegationDuration);
register.registerMetric(delegationAssignments);
register.registerMetric(userSessions);
register.registerMetric(userSessionDuration);
register.registerMetric(activeUserSessions);
register.registerMetric(apiEndpointDuration);
register.registerMetric(apiEndpointRequests);
register.registerMetric(systemLoad);
register.registerMetric(memoryUsage);

/**
 * Express middleware for collecting HTTP metrics
 */
export function metricsMiddleware() {
  return (req: Request, res: Response, next: NextFunction): void => {
    const startTime = Date.now();
    const startHrTime = process.hrtime();

    // Get request size
    const requestSize = parseInt(req.headers['content-length'] || '0', 10);
    if (requestSize > 0) {
      httpRequestSize.labels(req.method, req.route?.path || req.path).observe(requestSize);
    }

    // Override res.end to capture response metrics
    const originalEnd = res.end;
    res.end = function (chunk?: any, encoding?: any) {
      // Calculate duration
      const diff = process.hrtime(startHrTime);
      const duration = diff[0] + diff[1] * 1e-9;

      // Get user role if available
      const userRole = (req as any).user?.role || 'anonymous';

      // Get route path (fallback to req.path if route not available)
      const route = req.route?.path || req.path;

      // Record metrics
      httpRequestDuration
        .labels(req.method, route, res.statusCode.toString(), userRole)
        .observe(duration);

      httpRequestTotal.labels(req.method, route, res.statusCode.toString(), userRole).inc();

      // Get response size
      const responseSize = parseInt((res.getHeader('content-length') as string) || '0', 10);
      if (responseSize > 0) {
        httpResponseSize.labels(req.method, route, res.statusCode.toString()).observe(responseSize);
      }

      // Log slow requests
      if (duration > 1) {
        logger.warn('Slow HTTP request detected', {
          duration,
          method: req.method,
          path: req.path,
          service: 'metrics',
          statusCode: res.statusCode,
          userRole,
        });
      }

      return originalEnd.call(this, chunk, encoding);
    };

    next();
  };
}

// Function to update system metrics (CPU, memory, etc.)
function updateSystemMetrics(): void {
  try {
    const load = os.loadavg();
    systemLoad.set({ period: '1m' }, load[0]);
    systemLoad.set({ period: '5m' }, load[1]);
    systemLoad.set({ period: '15m' }, load[2]);

    const memUsage = process.memoryUsage();
    memoryUsage.set({ type: 'rss' }, memUsage.rss);
    memoryUsage.set({ type: 'heapTotal' }, memUsage.heapTotal);
    memoryUsage.set({ type: 'heapUsed' }, memUsage.heapUsed);
    memoryUsage.set({ type: 'external' }, memUsage.external);
    // V8 space metrics if available (Node.js specific)
    if (typeof (process as any).constrainedMemory === 'function') {
      const v8HeapSpaceStats = (process as any).constrainedMemory();
      if (v8HeapSpaceStats) {
        // Add relevant v8 heap space stats to metrics if needed
      }
    }

    // Construct SystemMetrics object for WebSocket emission
    // Note: Prometheus collectDefaultMetrics already covers CPU and memory.
    // This is for a specific WebSocket push of a simplified metrics view if needed.
    const currentMetrics: SystemMetrics = {
      cpuUsage: load[0], // Using 1m load average as a proxy for CPU usage percentage here. Refine if specific CPU % is available.
      memoryUsage: (memUsage.heapUsed / memUsage.heapTotal) * 100, // Example: heap used percentage
      activeConnections: 0, // Placeholder: This would need to be sourced, e.g., from io.sockets.sockets.size in UnifiedWebSocketService
                            // Or by querying database active connections if relevant.
    };

    // Emit WebSocket event
    const unifiedSocketService = getUnifiedWebSocketService();
    if (unifiedSocketService && unifiedSocketService.reliability) {
      if (typeof (unifiedSocketService.reliability as any).emitMetricsUpdate === 'function') {
        (unifiedSocketService.reliability as any).emitMetricsUpdate(currentMetrics);
      } else {
        logger.warn('emitMetricsUpdate method not found on reliability namespace, attempting direct emit for system metrics.');
        unifiedSocketService.emitToRoom('reliability-monitoring', RELIABILITY_EVENTS.METRICS_UPDATE, currentMetrics);
      }
    } else {
      logger.error('UnifiedWebSocketService or its reliability namespace is not available. WebSocket event for system metrics not sent.');
    }

  } catch (error) {
    logger.error('Error updating system metrics:', { error, service: 'metrics' });
  }
}

// Start periodic updates for system metrics (if not already handled by collectDefaultMetrics scheduler)
// collectDefaultMetrics usually sets up its own timers. If more frequent or specific updates are needed
// for WebSocket, this interval is appropriate.
// To ensure this is called, we can set an interval here or ensure it's called from an initialization block.
if (process.env.NODE_ENV !== 'test') { // Avoid running timers during tests unless specifically needed
  setInterval(updateSystemMetrics, 30000); // Update every 30 seconds, adjust as needed
  updateSystemMetrics(); // Initial call
}

/**
 * Business logic metric helpers
 */
export const businessMetrics = {
  recordAdminOperation(operation: string, adminRole: string, status: 'error' | 'success'): void {
    adminOperations.labels(operation, adminRole, status).inc();
  },

  // API Performance Metrics
  recordApiEndpointDuration(
    endpoint: string,
    method: string,
    statusCode: string,
    durationSeconds: number,
  ): void {
    apiEndpointDuration.labels(endpoint, method, statusCode).observe(durationSeconds);
  },

  recordApiEndpointRequest(
    endpoint: string,
    method: string,
    statusCode: string,
    userRole: string,
  ): void {
    apiEndpointRequests.labels(endpoint, method, statusCode, userRole).inc();
  },

  recordCircuitBreakerRequest(
    service: string,
    breakerName: string,
    result: 'failure' | 'reject' | 'success' | 'timeout',
  ): void {
    circuitBreakerRequests.labels(service, breakerName, result).inc();
  },

  recordCircuitBreakerState(
    service: string,
    breakerName: string,
    state: 'closed' | 'half-open' | 'open',
  ): void {
    const stateValue = state === 'closed' ? 0 : state === 'half-open' ? 1 : 2;
    circuitBreakerState.labels(service, breakerName).set(stateValue);
  },

  recordDatabaseQuery(
    operation: string,
    table: string,
    duration: number,
    status: 'error' | 'success',
  ): void {
    databaseQueryDuration.labels(operation, table, status).observe(duration);
    databaseQueryTotal.labels(operation, table, status).inc();
  },

  recordDeduplicationRequest(result: 'error' | 'hit' | 'miss'): void {
    deduplicationRequests.labels(result).inc();
  },

  // Enhanced Delegation Metrics
  recordDelegationDuration(
    delegationType: string,
    durationHours: number,
    status: 'cancelled' | 'completed',
  ): void {
    delegationDuration.labels(delegationType, status).observe(durationHours);
  },

  recordDelegationOperation(operation: string, status: 'error' | 'success'): void {
    delegationOperations.labels(operation, status).inc();
  },

  // Enhanced WorkHub Business Metrics

  // Employee Metrics
  recordEmployeeOperation(
    operation: string,
    employeeRole: string,
    status: 'error' | 'success',
  ): void {
    employeeOperations.labels(operation, employeeRole, status).inc();
  },

  recordTaskDuration(
    taskPriority: string,
    employeeRole: string,
    durationMinutes: number,
    status: 'cancelled' | 'completed',
  ): void {
    taskDuration.labels(taskPriority, employeeRole, status).observe(durationMinutes);
  },

  // Task Metrics
  recordTaskOperation(operation: string, taskPriority: string, status: 'error' | 'success'): void {
    taskOperations.labels(operation, taskPriority, status).inc();
  },

  recordUserOperation(operation: string, userRole: string, status: 'error' | 'success'): void {
    userOperations.labels(operation, userRole, status).inc();
  },

  // User Session Metrics
  recordUserSession(userRole: string, sessionType: 'login' | 'logout' | 'timeout'): void {
    userSessions.labels(userRole, sessionType).inc();
  },

  recordUserSessionDuration(userRole: string, durationMinutes: number): void {
    userSessionDuration.labels(userRole).observe(durationMinutes);
  },

  // Vehicle Metrics
  recordVehicleOperation(
    operation: string,
    vehicleType: string,
    status: 'error' | 'success',
  ): void {
    vehicleOperations.labels(operation, vehicleType, status).inc();
  },

  setActiveUserSessions(userRole: string, activeCount: number): void {
    activeUserSessions.labels(userRole).set(activeCount);
  },

  setDatabaseConnections(count: number): void {
    databaseConnectionsActive.set(count);
  },

  setDeduplicationCacheSize(size: number): void {
    deduplicationCacheSize.set(size);
  },

  setDelegationAssignments(
    delegationType: string,
    assignmentRole: 'driver' | 'escort',
    activeCount: number,
  ): void {
    delegationAssignments.labels(delegationType, assignmentRole).set(activeCount);
  },

  setEmployeeAvailability(employeeRole: string, department: string, availableCount: number): void {
    employeeAvailability.labels(employeeRole, department).set(availableCount);
  },

  setEmployeeUtilization(
    employeeId: string,
    employeeRole: string,
    department: string,
    utilizationRatio: number,
  ): void {
    employeeUtilization.labels(employeeId, employeeRole, department).set(utilizationRatio);
  },

  setTaskAssignments(taskPriority: string, employeeRole: string, activeCount: number): void {
    taskAssignments.labels(taskPriority, employeeRole).set(activeCount);
  },

  setVehicleAssignments(
    vehicleType: string,
    assignmentType: 'delegation' | 'task',
    activeCount: number,
  ): void {
    vehicleAssignments.labels(vehicleType, assignmentType).set(activeCount);
  },

  setVehicleUtilization(vehicleId: string, vehicleType: string, utilizationRatio: number): void {
    vehicleUtilization.labels(vehicleId, vehicleType).set(utilizationRatio);
  },
};

/**
 * Get metrics in Prometheus format
 */
export async function getMetrics(): Promise<string> {
  try {
    return await register.metrics();
  } catch (error) {
    logger.error('Failed to get metrics', {
      error: error instanceof Error ? error.message : 'Unknown error',
      service: 'metrics',
    });
    throw error;
  }
}

/**
 * Get HTTP request metrics in structured format for dashboard widgets
 */
export async function getHttpRequestMetrics(): Promise<{
  name: string;
  help: string;
  type: string;
  values: Array<{
    labels: {
      method: string;
      route: string;
      statusCode: string;
      userRole: string;
    };
    value: number;
  }>;
}> {
  try {
    // Get the HTTP request duration metric
    const metricValues = await httpRequestDuration.get();

    return {
      name: 'http_request_duration_seconds',
      help: 'Duration of HTTP requests in seconds',
      type: 'histogram',
      values: metricValues.values.map((value: any) => ({
        labels: {
          method: value.labels.method || 'unknown',
          route: value.labels.route || 'unknown',
          statusCode: value.labels.status_code || 'unknown',
          userRole: value.labels.user_role || 'anonymous',
        },
        value: value.value || 0,
      })),
    };
  } catch (error) {
    logger.error('Failed to get HTTP request metrics', {
      error: error instanceof Error ? error.message : 'Unknown error',
      service: 'metrics',
    });

    // Return empty structure on error
    return {
      name: 'http_request_duration_seconds',
      help: 'Duration of HTTP requests in seconds',
      type: 'histogram',
      values: [],
    };
  }
}

/**
 * Get metrics summary for admin dashboard
 */
export async function getMetricsSummary(): Promise<any> {
  try {
    const metrics = await register.getMetricsAsJSON();

    // Process metrics into a summary format
    const summary: any = {
      circuitBreakers: {},
      database: {
        activeConnections: 0,
        averageQueryTime: 0,
        queryCount: 0,
      },
      deduplication: {
        cacheSize: 0,
        hitRate: 0,
        totalRequests: 0,
      },
      http: {
        averageResponseTime: 0,
        errorRate: 0,
        totalRequests: 0,
      },
      system: {
        loadAverage: os.loadavg(),
        memoryUsage: process.memoryUsage(),
        uptime: process.uptime(),
      },
      timestamp: new Date().toISOString(),
    };

    // Extract relevant metrics
    for (const metric of metrics) {
      if (metric.name === 'workhub_http_requests_total') {
        summary.http.totalRequests = metric.values.reduce(
          (sum: number, v: any) => sum + v.value,
          0,
        );
      }
      // Add more metric processing as needed
    }

    return summary;
  } catch (error) {
    logger.error('Failed to get metrics summary', {
      error: error instanceof Error ? error.message : 'Unknown error',
      service: 'metrics',
    });
    throw error;
  }
}

/**
 * Get Prometheus registry
 */
export function getRegistry(): promClient.Registry {
  return register;
}

/**
 * Reset all metrics (useful for testing)
 */
export function resetMetrics(): void {
  register.resetMetrics();
  logger.info('All metrics reset', { service: 'metrics' });
}

export default {
  businessMetrics,
  getMetrics,
  getMetricsSummary,
  getRegistry,
  metricsMiddleware,
  resetMetrics,
};
