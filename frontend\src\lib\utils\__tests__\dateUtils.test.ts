import {
  formatDateForApi,
  formatDateForDisplay,
  formatDateForInput,
  isDateAfter,
  isValidDateString,
  isValidIsoDateString,
  parseDateFromApi,
} from '../dateUtils';

describe('Date Utility Functions', () => {
  // Test formatDateForInput
  describe('formatDateForInput', () => {
    it('should format a Date object for datetime-local input', () => {
      const date = new Date('2023-05-15T14:30:00Z');
      const result = formatDateForInput(date);
      // Note: The exact output might vary based on timezone, so we check the format pattern
      expect(result).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}$/);
    });

    it('should format an ISO string for datetime-local input', () => {
      const dateString = '2023-05-15T14:30:00Z';
      const result = formatDateForInput(dateString);
      expect(result).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}$/);
    });

    it('should format a Date object for date input', () => {
      const date = new Date('2023-05-15T14:30:00Z');
      const result = formatDateForInput(date, 'date');
      expect(result).toMatch(/^\d{4}-\d{2}-\d{2}$/);
    });

    it('should return empty string for undefined input', () => {
      const result = formatDateForInput(undefined);
      expect(result).toBe('');
    });

    it('should return empty string for invalid date input', () => {
      const result = formatDateForInput('not-a-date');
      expect(result).toBe('');
    });
  });

  // Test formatDateForDisplay
  describe('formatDateForDisplay', () => {
    it('should format a Date object for display without time', () => {
      const date = new Date('2023-05-15T14:30:00Z');
      const result = formatDateForDisplay(date);
      expect(result).toMatch(/[A-Z][a-z]{2} \d{1,2}, \d{4}/); // e.g., "May 15, 2023"
    });

    it('should format a Date object for display with time', () => {
      const date = new Date('2023-05-15T14:30:00Z');
      const result = formatDateForDisplay(date, true);
      expect(result).toMatch(/[A-Z][a-z]{2} \d{1,2}, \d{4}, \d{2}:\d{2}/); // e.g., "May 15, 2023, 14:30"
    });

    it('should format an ISO string for display', () => {
      const dateString = '2023-05-15T14:30:00Z';
      const result = formatDateForDisplay(dateString);
      expect(result).toMatch(/[A-Z][a-z]{2} \d{1,2}, \d{4}/);
    });

    it('should return "N/A" for undefined input', () => {
      const result = formatDateForDisplay(undefined);
      expect(result).toBe('N/A');
    });

    it('should return "Invalid Date" for invalid date input', () => {
      const result = formatDateForDisplay('not-a-date');
      expect(result).toBe('Invalid Date');
    });
  });

  // Test formatDateForApi
  describe('formatDateForApi', () => {
    it('should format a Date object for API submission', () => {
      const date = new Date('2023-05-15T14:30:00Z');
      const result = formatDateForApi(date);
      expect(result).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/); // ISO format
    });

    it('should format a date string for API submission', () => {
      const dateString = '2023-05-15T14:30';
      const result = formatDateForApi(dateString);
      expect(result).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/);
    });

    it('should return empty string for undefined input', () => {
      const result = formatDateForApi(undefined);
      expect(result).toBe('');
    });

    it('should return empty string for invalid date input', () => {
      const result = formatDateForApi('not-a-date');
      expect(result).toBe('');
    });
  });

  // Test parseDateFromApi
  describe('parseDateFromApi', () => {
    it('should parse an ISO string to a Date object', () => {
      const dateString = '2023-05-15T14:30:00Z';
      const result = parseDateFromApi(dateString);
      expect(result).toBeInstanceOf(Date);
      expect(result?.toISOString()).toMatch(/^2023-05-15T14:30:00/);
    });

    it('should return null for undefined input', () => {
      const result = parseDateFromApi(undefined);
      expect(result).toBeNull();
    });

    it('should return null for invalid date string', () => {
      const result = parseDateFromApi('not-a-date');
      expect(result).toBeNull();
    });
  });

  // Test isValidIsoDateString
  describe('isValidIsoDateString', () => {
    it('should return true for valid ISO date string', () => {
      const result = isValidIsoDateString('2023-05-15T14:30:00Z');
      expect(result).toBe(true);
    });

    it('should return false for invalid ISO date string', () => {
      const result = isValidIsoDateString('not-a-date');
      expect(result).toBe(false);
    });

    it('should return false for undefined input', () => {
      const result = isValidIsoDateString(undefined);
      expect(result).toBe(false);
    });
  });

  // Test isValidDateString
  describe('isValidDateString', () => {
    it('should return true for valid date string', () => {
      const result = isValidDateString('2023-05-15');
      expect(result).toBe(true);
    });

    it('should return true for valid datetime string', () => {
      const result = isValidDateString('2023-05-15T14:30');
      expect(result).toBe(true);
    });

    it('should return false for invalid date string', () => {
      const result = isValidDateString('not-a-date');
      expect(result).toBe(false);
    });

    it('should return false for undefined input', () => {
      const result = isValidDateString(undefined);
      expect(result).toBe(false);
    });
  });

  // Test isDateAfter
  describe('isDateAfter', () => {
    it('should return true when first date is after second date', () => {
      const date1 = new Date('2023-05-15T14:30:00Z');
      const date2 = new Date('2023-05-14T14:30:00Z');
      const result = isDateAfter(date1, date2);
      expect(result).toBe(true);
    });

    it('should return false when first date is before second date', () => {
      const date1 = new Date('2023-05-14T14:30:00Z');
      const date2 = new Date('2023-05-15T14:30:00Z');
      const result = isDateAfter(date1, date2);
      expect(result).toBe(false);
    });

    it('should return false when dates are equal', () => {
      const date1 = new Date('2023-05-15T14:30:00Z');
      const date2 = new Date('2023-05-15T14:30:00Z');
      const result = isDateAfter(date1, date2);
      expect(result).toBe(false);
    });

    it('should handle string dates', () => {
      const date1 = '2023-05-15T14:30:00Z';
      const date2 = '2023-05-14T14:30:00Z';
      const result = isDateAfter(date1, date2);
      expect(result).toBe(true);
    });

    it('should return false when either date is undefined', () => {
      const date1 = new Date('2023-05-15T14:30:00Z');
      const result1 = isDateAfter(date1, undefined);
      const result2 = isDateAfter(undefined, date1);
      expect(result1).toBe(false);
      expect(result2).toBe(false);
    });

    it('should return false when either date is invalid', () => {
      const date1 = new Date('2023-05-15T14:30:00Z');
      const result1 = isDateAfter(date1, 'not-a-date');
      const result2 = isDateAfter('not-a-date', date1);
      expect(result1).toBe(false);
      expect(result2).toBe(false);
    });
  });
});
