@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Modern Luxury Palette - Blue, Gray, Light Accents */
    --background: 220 20% 95%; /* #EEF0F7 - Very light almost white-blue */
    --foreground: 215 25% 27%; /* #36454F - Charcoal Gray */

    --card: 220 20% 100%; /* #FFFFFF - White */
    --card-foreground: 215 25% 27%; /* #36454F - Charcoal Gray */

    --popover: 220 20% 100%; /* #FFFFFF - White */
    --popover-foreground: 215 25% 27%; /* #36454F - Charcoal Gray */

    --primary: 210 40% 50%; /* #4D6B99 - Medium Steel Blue */
    --primary-foreground: 220 20% 98%; /* #F7F8FA - Very Light Gray for text on primary */

    --secondary: 210 30% 75%; /* #AFC2D8 - Light Desaturated Blue/Gray */
    --secondary-foreground: 215 25% 27%; /* #36454F - Charcoal Gray */

    --muted: 220 20% 90%; /* #DADFEA - Light Gray-Blue */
    --muted-foreground: 215 20% 45%; /* #65737E - Slate Gray */

    --accent: 200 60% 60%; /* #66A3D9 - Soft Sky Blue */
    --accent-foreground: 215 25% 27%; /* #36454F - Charcoal Gray */

    --destructive: 0 72% 51%; /* #D94646 - Standard Red */
    --destructive-foreground: 0 0% 100%; /* #FFFFFF - White */

    --border: 220 20% 85%; /* #CCD3E0 - Light Gray-Blue Border */
    --input: 220 20% 92%; /* #E3E7F0 - Very Light Gray-Blue Input */
    --ring: 200 60% 70%; /* #85BDE0 - Lighter Sky Blue for focus rings */

    --radius: 0.5rem;

    /* Chart colors */
    --chart-1: 210 40% 50%; /* Medium Steel Blue */
    --chart-2: 200 60% 60%; /* Soft Sky Blue */
    --chart-3: 210 30% 65%; /* Medium-Light Desaturated Blue */
    --chart-4: 200 50% 75%; /* Lighter Sky Blue */
    --chart-5: 215 25% 40%; /* Darker Charcoal/Slate */

    /* Sidebar specific vars - kept distinct for potential separate styling */
    --sidebar-background: 215 28% 22%; /* Dark Charcoal Blue #2A3B47 */
    --sidebar-foreground: 220 20% 90%; /* #DADFEA - Light Gray-Blue */
    --sidebar-primary: 200 60% 60%; /* Soft Sky Blue */
    --sidebar-primary-foreground: 215 28% 15%; /* Very Dark Charcoal Blue #1C2830 */
    --sidebar-accent: 200 60% 70%; /* Lighter Sky Blue */
    --sidebar-accent-foreground: 215 28% 15%; /* Very Dark Charcoal Blue */
    --sidebar-border: 215 28% 30%; /* Mid Charcoal Blue border #3B4E5A */
    --sidebar-ring: 200 60% 60%; /* Soft Sky Blue */
  }

  .dark {
    /* Modern Luxury Palette - Dark Mode */
    --background: 215 28% 15%; /* #1C2830 - Very Dark Charcoal Blue */
    --foreground: 220 20% 90%; /* #DADFEA - Light Gray-Blue */

    --card: 215 28% 20%; /* #263640 - Dark Charcoal Blue */
    --card-foreground: 220 20% 90%; /* #DADFEA - Light Gray-Blue */

    --popover: 215 28% 18%; /* #21303A - Slightly Darker Charcoal Blue */
    --popover-foreground: 220 20% 90%; /* #DADFEA - Light Gray-Blue */

    --primary: 200 60% 60%; /* #66A3D9 - Soft Sky Blue */
    --primary-foreground: 215 28% 10%; /* #111A1F - Near Black for text on primary */

    --secondary: 215 25% 35%; /* #4A5B68 - Dark Slate Gray */
    --secondary-foreground: 220 20% 90%; /* #DADFEA - Light Gray-Blue */

    --muted: 215 28% 25%; /* #30414D - Muted Dark Blue/Gray */
    --muted-foreground: 220 20% 70%; /* #AAB4C3 - Medium-Light Gray-Blue */

    --accent: 200 50% 70%; /* #85B3D9 - Brighter Soft Sky Blue */
    --accent-foreground: 215 28% 10%; /* #111A1F - Near Black */

    --destructive: 0 65% 55%; /* #E05D5D - Slightly softer red */
    --destructive-foreground: 0 0% 100%; /* #FFFFFF - White */

    --border: 215 28% 28%; /* #384955 - Dark Gray-Blue Border */
    --input: 215 28% 22%; /* #2A3B47 - Dark Charcoal Blue Input */
    --ring: 200 50% 60%; /* #66A3D9 - Soft Sky Blue for focus rings */

    /* Chart colors */
    --chart-1: 200 60% 60%; /* Soft Sky Blue */
    --chart-2: 220 20% 80%; /* Lighter Gray-Blue */
    --chart-3: 200 50% 75%; /* Lighter Sky Blue */
    --chart-4: 210 30% 75%; /* Light Desaturated Blue/Gray */
    --chart-5: 200 70% 50%; /* Brighter Sky Blue */

    /* Sidebar specific vars - kept distinct for potential separate styling */
    --sidebar-background: 215 30% 12%; /* #151E25 - Even Darker Charcoal Blue */
    --sidebar-foreground: 200 60% 70%; /* #85BDE0 - Lighter Sky Blue text */
    --sidebar-primary: 200 60% 60%; /* Soft Sky Blue */
    --sidebar-primary-foreground: 215 30% 8%; /* #0D1318 - Almost Black */
    --sidebar-accent: 220 20% 90%; /* Light Gray-Blue */
    --sidebar-accent-foreground: 215 30% 8%; /* #0D1318 - Almost Black */
    --sidebar-border: 215 28% 20%; /* Dark Charcoal Blue */
    --sidebar-ring: 200 60% 60%; /* Soft Sky Blue */
  }
}

@layer base {
  * {
    border-color: hsl(var(--border));
  }
  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
  }
}

/* Custom scrollbar styles */
@layer utilities {
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--border)) transparent;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: hsl(var(--border));
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: hsl(var(--muted-foreground));
  }

  .scrollbar-thumb-border {
    scrollbar-color: hsl(var(--border)) transparent;
  }

  .scrollbar-track-transparent {
    scrollbar-color: hsl(var(--border)) transparent;
  }
}

/* Global print styles */
@media print {
  /* Reset margins and ensure proper page setup */
  * {
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
    color-adjust: exact !important;
    box-sizing: border-box !important;
  }

  html {
    margin: 0 !important;
    padding: 0 !important;
    box-sizing: border-box !important; /* Ensure consistent box model */
  }

  /* Comprehensive page setup to eliminate black borders */
  @page {
    margin: 0.5in !important;
    padding: 0 !important;
    border: none !important;
    box-shadow: none !important;
    size: auto !important;
  }

  html,
  body {
    border: none !important;
    outline: none !important;
    box-shadow: none !important;
    background: white !important;
    width: 100% !important;
  }

  /* Reset all container elements that might cause borders - PRINT ONLY */
  #__next,
  #root,
  main,
  .container,
  .mx-auto,
  .max-w-4xl,
  .max-w-6xl,
  .max-w-7xl {
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
    outline: none !important;
    box-shadow: none !important;
    background: transparent !important;
    width: 100% !important;
  }

  /* Remove borders from all UI components - PRINT ONLY */
  .card,
  .border,
  .border-t,
  .border-b,
  .border-l,
  .border-r,
  .border-gray-200,
  .border-gray-300,
  .border-border,
  .rounded,
  .rounded-lg,
  .shadow,
  .shadow-sm,
  .shadow-md,
  .shadow-lg {
    border: none !important;
    box-shadow: none !important;
    border-radius: 0 !important;
    outline: none !important;
  }

  body {
    background: white !important;
    color: black !important;
    font-family:
      system-ui,
      -apple-system,
      BlinkMacSystemFont,
      'Segoe UI',
      Roboto,
      Oxygen,
      Ubuntu,
      Cantarell,
      'Open Sans',
      'Helvetica Neue',
      sans-serif !important;
    font-size: 10pt !important;
    line-height: 1.4 !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  /* Hide elements that shouldn't be printed */
  .no-print {
    display: none !important;
  }

  .print-only {
    display: block !important;
  }

  /* Ensure proper page breaks */
  .page-break-before {
    page-break-before: always !important;
  }

  .page-break-after {
    page-break-after: always !important;
  }

  .page-break-inside-avoid {
    page-break-inside: avoid !important;
  }

  /* Ensure table containers don't have borders */
  .table-container,
  .delegation-table-container {
    border: none !important;
    box-shadow: none !important;
    outline: none !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  /* Note: Delegation table styles are now consolidated in print.css */

  /* Links should show URL in print */
  a[href]:after {
    content: ' (' attr(href) ')';
    font-size: 8pt;
    color: #666;
  }

  /* Remove link styling for print */
  a {
    color: inherit !important;
    text-decoration: none !important;
  }
}
