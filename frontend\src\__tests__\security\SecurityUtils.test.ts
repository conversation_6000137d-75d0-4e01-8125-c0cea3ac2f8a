/**
 * @file SecurityUtils Circuit Breaker Tests
 * @description Comprehensive tests for verification loop prevention system
 */

import { SecurityUtils } from '../../lib/security';

// Mock localStorage for testing
const mockLocalStorage = (() => {
  let store: Record<string, string> = {};
  return {
    getItem: jest.fn((key: string) => store[key] || null),
    setItem: jest.fn((key: string, value: string) => {
      store[key] = value;
    }),
    removeItem: jest.fn((key: string) => {
      delete store[key];
    }),
    clear: jest.fn(() => {
      store = {};
    }),
  };
})();

// Mock console methods
const mockConsole = {
  log: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
  debug: jest.fn(),
};

describe('SecurityUtils Circuit Breaker', () => {
  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    mockLocalStorage.clear();

    // Mock global objects
    Object.defineProperty(window, 'localStorage', {
      value: mockLocalStorage,
      writable: true,
    });

    Object.defineProperty(global, 'console', {
      value: mockConsole,
      writable: true,
    });

    // Initialize circuit breaker
    SecurityUtils.initializeCircuitBreaker();
  });

  afterEach(() => {
    // Reset security state for testing
    SecurityUtils.resetCircuitBreakerForTesting();
  });

  describe('Circuit Breaker Basic Functionality', () => {
    test('should allow security checks when circuit is closed', () => {
      const canPerform = SecurityUtils.canPerformSecurityCheck();
      expect(canPerform).toBe(true);
    });

    test('should track security attempts correctly', () => {
      // Record multiple attempts
      SecurityUtils.recordSecurityAttempt();
      SecurityUtils.recordSecurityAttempt();
      SecurityUtils.recordSecurityAttempt();

      const state = SecurityUtils.getCircuitBreakerState();
      expect(state.attemptCount).toBe(3);
      expect(state.isOpen).toBe(false); // Should still be closed
    });

    test('should open circuit breaker after max attempts', () => {
      // Record max attempts (5)
      for (let i = 0; i < 5; i++) {
        SecurityUtils.recordSecurityAttempt();
      }

      const state = SecurityUtils.getCircuitBreakerState();
      expect(state.isOpen).toBe(true);
      expect(mockConsole.error).toHaveBeenCalledWith(
        expect.stringContaining('Security verification loop detected')
      );
    });

    test('should block security checks when circuit is open', () => {
      // Open the circuit
      for (let i = 0; i < 5; i++) {
        SecurityUtils.recordSecurityAttempt();
      }

      const canPerform = SecurityUtils.canPerformSecurityCheck();
      expect(canPerform).toBe(false);
      expect(mockConsole.warn).toHaveBeenCalledWith(
        expect.stringContaining('Circuit breaker OPEN')
      );
    });

    test('should reset circuit breaker on success', () => {
      // Record some attempts
      SecurityUtils.recordSecurityAttempt();
      SecurityUtils.recordSecurityAttempt();

      // Record success
      SecurityUtils.recordSecuritySuccess();

      const state = SecurityUtils.getCircuitBreakerState();
      expect(state.attemptCount).toBe(0);
      expect(state.isOpen).toBe(false);
    });
  });

  describe('Security Operation Coordination', () => {
    test('should allow starting new security operation', () => {
      const operationId = 'test-operation';
      const started = SecurityUtils.startSecurityOperation(operationId);

      expect(started).toBe(true);

      const state = SecurityUtils.getCircuitBreakerState();
      expect(state.activeOperations).toContain(operationId);
    });

    test('should prevent duplicate security operations', () => {
      const operationId = 'test-operation';

      // Start first operation
      const firstStart = SecurityUtils.startSecurityOperation(operationId);
      expect(firstStart).toBe(true);

      // Try to start same operation again
      const secondStart = SecurityUtils.startSecurityOperation(operationId);
      expect(secondStart).toBe(false);
    });

    test('should end security operation correctly', () => {
      const operationId = 'test-operation';

      // Start and end operation
      SecurityUtils.startSecurityOperation(operationId);
      SecurityUtils.endSecurityOperation(operationId);

      const state = SecurityUtils.getCircuitBreakerState();
      expect(state.activeOperations).not.toContain(operationId);
    });

    test('should prevent operations when circuit is open', () => {
      // Open the circuit
      for (let i = 0; i < 5; i++) {
        SecurityUtils.recordSecurityAttempt();
      }

      const operationId = 'test-operation';
      const started = SecurityUtils.startSecurityOperation(operationId);

      expect(started).toBe(false);
    });
  });

  describe('Circuit Breaker State Persistence', () => {
    test('should persist attempts to localStorage', () => {
      SecurityUtils.recordSecurityAttempt();

      expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
        'workhub-verification-attempts',
        expect.stringContaining('"attempts":1')
      );
    });

    test('should restore state from localStorage on initialization', () => {
      // Manually set localStorage data
      const storedData = {
        attempts: 3,
        lastAttemptTime: Date.now() - 1000, // 1 second ago
      };
      mockLocalStorage.setItem(
        'workhub-verification-attempts',
        JSON.stringify(storedData)
      );

      // Re-initialize
      SecurityUtils.initializeCircuitBreaker();

      const state = SecurityUtils.getCircuitBreakerState();
      expect(state.attemptCount).toBe(3);
    });

    test('should clear old attempts on initialization', () => {
      // Set old attempts (older than reset timeout)
      const storedData = {
        attempts: 3,
        lastAttemptTime: Date.now() - 400000, // 6+ minutes ago
      };
      mockLocalStorage.setItem(
        'workhub-verification-attempts',
        JSON.stringify(storedData)
      );

      // Re-initialize
      SecurityUtils.initializeCircuitBreaker();

      const state = SecurityUtils.getCircuitBreakerState();
      expect(state.attemptCount).toBe(0);
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith(
        'workhub-verification-attempts'
      );
    });
  });

  describe('Circuit Breaker Reset Functionality', () => {
    test('should reset circuit after timeout', () => {
      // Open the circuit
      for (let i = 0; i < 5; i++) {
        SecurityUtils.recordSecurityAttempt();
      }

      // Mock time passage (6 minutes)
      const originalNow = Date.now;
      Date.now = jest.fn(() => originalNow() + 360000);

      try {
        const canPerform = SecurityUtils.canPerformSecurityCheck();
        expect(canPerform).toBe(true);

        const state = SecurityUtils.getCircuitBreakerState();
        expect(state.isOpen).toBe(false);
        expect(state.attemptCount).toBe(0);
      } finally {
        Date.now = originalNow;
      }
    });

    test('should force reset security state', () => {
      // Open circuit and add active operations
      for (let i = 0; i < 5; i++) {
        SecurityUtils.recordSecurityAttempt();
      }
      SecurityUtils.startSecurityOperation('test-op');

      // Force reset
      SecurityUtils.resetCircuitBreakerForTesting();

      const state = SecurityUtils.getCircuitBreakerState();
      expect(state.attemptCount).toBe(0);
      expect(state.isOpen).toBe(false);
      expect(state.activeOperations).toHaveLength(0);
    });
  });

  describe('Error Handling', () => {
    test('should handle localStorage errors gracefully', () => {
      // Mock localStorage to throw error
      mockLocalStorage.setItem.mockImplementation(() => {
        throw new Error('Storage quota exceeded');
      });

      // Should not throw
      expect(() => {
        SecurityUtils.recordSecurityAttempt();
      }).not.toThrow();

      expect(mockConsole.warn).toHaveBeenCalledWith(
        expect.stringContaining('Failed to store verification attempts')
      );
    });

    test('should handle corrupted localStorage data', () => {
      // Set invalid JSON
      mockLocalStorage.getItem.mockReturnValue('invalid-json');

      // Should not throw and should reset to safe state
      expect(() => {
        SecurityUtils.initializeCircuitBreaker();
      }).not.toThrow();

      const state = SecurityUtils.getCircuitBreakerState();
      expect(state.attemptCount).toBe(0);
      expect(state.isOpen).toBe(false);
    });
  });

  describe('Integration Scenarios', () => {
    test('should handle rapid successive operations', () => {
      const operations = ['op1', 'op2', 'op3', 'op4', 'op5'];

      // Start all operations
      const results = operations.map(op =>
        SecurityUtils.startSecurityOperation(op)
      );

      // All should succeed
      expect(results.every(result => result === true)).toBe(true);

      const state = SecurityUtils.getCircuitBreakerState();
      expect(state.activeOperations).toHaveLength(5);
    });

    test('should prevent verification loops in realistic scenario', () => {
      // Simulate token validation loop
      for (let i = 0; i < 10; i++) {
        if (SecurityUtils.canPerformSecurityCheck()) {
          const operationId = `token-validation-${i}`;
          if (SecurityUtils.startSecurityOperation(operationId)) {
            // Simulate failure
            SecurityUtils.recordSecurityAttempt();
            SecurityUtils.endSecurityOperation(operationId);
          }
        }
      }

      // Circuit should be open after 5 attempts
      const canPerform = SecurityUtils.canPerformSecurityCheck();
      expect(canPerform).toBe(false);

      const state = SecurityUtils.getCircuitBreakerState();
      expect(state.isOpen).toBe(true);
      expect(state.attemptCount).toBe(5);
    });
  });
});
