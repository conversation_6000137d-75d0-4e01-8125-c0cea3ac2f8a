/**
 * @file Form Submission Toast Service
 * @description Handles toast notifications for form submissions following SRP
 * @version 2.0.0
 * <AUTHOR> Development Team
 */

import type { ToastConfig } from '../types/FormSubmissionTypes';
import {
  toastService,
  employeeToast,
  vehicleToast,
  taskToast,
  delegationToast,
  serviceRecordToast,
  createSimpleEntityToastService,
} from '@/lib/services/toastService';

/**
 * Form submission toast service following SRP
 * Responsible only for handling toast notifications during form submission
 */
export class FormSubmissionToastService {
  /**
   * Show success toast based on entity type and configuration
   */
  static showSuccessToast(config: ToastConfig, data?: any, result?: any): void {
    if (!config.showSuccessToast) return;

    const { entityType, entity, successMessage } = config;

    try {
      switch (entityType) {
        case 'employee':
          if (entity) {
            employeeToast.entityCreated(entity);
          } else {
            toastService.success('Employee Created', successMessage);
          }
          break;

        case 'vehicle':
          if (entity) {
            vehicleToast.entityCreated(entity);
          } else {
            toastService.success('Vehicle Added', successMessage);
          }
          break;

        case 'task':
          if (entity) {
            taskToast.entityCreated(entity);
          } else {
            toastService.success('Task Created', successMessage);
          }
          break;

        case 'delegation':
          if (entity) {
            delegationToast.entityCreated(entity);
          } else {
            toastService.success('Delegation Created', successMessage);
          }
          break;

        case 'serviceRecord':
          if (entity && result) {
            serviceRecordToast.serviceRecordCreated(
              entity.vehicleName || 'Vehicle',
              entity.serviceType || 'Service'
            );
          } else {
            toastService.success('Service Record Added', successMessage);
          }
          break;

        case 'generic':
        default:
          toastService.success(
            'Success',
            successMessage || 'Operation completed successfully'
          );
          break;
      }
    } catch (error) {
      // Fallback to generic success toast if entity-specific fails
      toastService.success(
        'Success',
        successMessage || 'Operation completed successfully'
      );
    }
  }

  /**
   * Show error toast based on entity type and configuration
   */
  static showErrorToast(config: ToastConfig, error: Error, data?: any): void {
    if (!config.showErrorToast) return;

    const { entityType, errorMessage } = config;
    const errorMsg =
      error.message || errorMessage || 'An unexpected error occurred';

    try {
      switch (entityType) {
        case 'employee':
          employeeToast.entityCreationError(errorMsg);
          break;

        case 'vehicle':
          vehicleToast.entityCreationError(errorMsg);
          break;

        case 'task':
          taskToast.entityCreationError(errorMsg);
          break;

        case 'delegation':
          delegationToast.entityCreationError(errorMsg);
          break;

        case 'serviceRecord':
          serviceRecordToast.serviceRecordCreationError(errorMsg);
          break;

        case 'generic':
        default:
          toastService.error('Error', errorMsg);
          break;
      }
    } catch (toastError) {
      // Fallback to generic error toast if entity-specific fails
      toastService.error('Error', errorMsg);
    }
  }

  /**
   * Show update success toast for existing entities
   */
  static showUpdateSuccessToast(
    config: ToastConfig,
    data?: any,
    result?: any
  ): void {
    if (!config.showSuccessToast) return;

    const { entityType, entity, successMessage } = config;

    try {
      switch (entityType) {
        case 'employee':
          if (entity) {
            employeeToast.entityUpdated(entity);
          } else {
            toastService.success('Employee Updated', successMessage);
          }
          break;

        case 'vehicle':
          if (entity) {
            vehicleToast.entityUpdated(entity);
          } else {
            toastService.success('Vehicle Updated', successMessage);
          }
          break;

        case 'task':
          if (entity) {
            taskToast.entityUpdated(entity);
          } else {
            toastService.success('Task Updated', successMessage);
          }
          break;

        case 'delegation':
          if (entity) {
            delegationToast.entityUpdated(entity);
          } else {
            toastService.success('Delegation Updated', successMessage);
          }
          break;

        case 'serviceRecord':
          if (entity && result) {
            serviceRecordToast.serviceRecordUpdated(
              entity.vehicleName || 'Vehicle',
              entity.serviceType || 'Service'
            );
          } else {
            toastService.success('Service Record Updated', successMessage);
          }
          break;

        case 'generic':
        default:
          toastService.success(
            'Success',
            successMessage || 'Update completed successfully'
          );
          break;
      }
    } catch (error) {
      // Fallback to generic success toast
      toastService.success(
        'Success',
        successMessage || 'Update completed successfully'
      );
    }
  }

  /**
   * Show update error toast for existing entities
   */
  static showUpdateErrorToast(
    config: ToastConfig,
    error: Error,
    data?: any
  ): void {
    if (!config.showErrorToast) return;

    const { entityType, errorMessage } = config;
    const errorMsg =
      error.message || errorMessage || 'An unexpected error occurred';

    try {
      switch (entityType) {
        case 'employee':
          employeeToast.entityUpdateError(errorMsg);
          break;

        case 'vehicle':
          vehicleToast.entityUpdateError(errorMsg);
          break;

        case 'task':
          taskToast.entityUpdateError(errorMsg);
          break;

        case 'delegation':
          delegationToast.entityUpdateError(errorMsg);
          break;

        case 'serviceRecord':
          serviceRecordToast.serviceRecordUpdateError(errorMsg);
          break;

        case 'generic':
        default:
          toastService.error('Update Failed', errorMsg);
          break;
      }
    } catch (toastError) {
      // Fallback to generic error toast
      toastService.error('Update Failed', errorMsg);
    }
  }

  /**
   * Create a dynamic toast service for custom entity types
   */
  static createCustomEntityToastService<T>(
    entityName: string,
    getDisplayName: (entity: T) => string
  ) {
    return createSimpleEntityToastService<T>(entityName, getDisplayName);
  }
}
