'use client';

import { Clip<PERSON><PERSON>he<PERSON>, Plus<PERSON>ir<PERSON>, Settings } from 'lucide-react';
import Link from 'next/link';
import React, { useCallback, useMemo, useState } from 'react';

import ErrorBoundary from '@/components/error-boundaries/ErrorBoundary';
import { TaskDashboardSettings } from '@/components/features/tasks/TaskDashboardSettings';
import TaskFilters, {
  type TaskFilterValues,
} from '@/components/features/tasks/TaskFilters';
import { TaskViewRenderer } from '@/components/features/tasks/TaskViewRenderer';
import { ViewReportButton } from '@/components/reports/ViewReportButton';
import { ActionButton } from '@/components/ui/action-button';
import { AppBreadcrumb } from '@/components/ui/app-breadcrumb';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { DataLoader, SkeletonLoader } from '@/components/ui/loading';
import { <PERSON>Header } from '@/components/ui/PageHeader';
import { useDashboardStore } from '@/hooks/domain/useDashboardStore';
import { useEmployees } from '@/lib/stores/queries/useEmployees'; // Import useEmployees hook
import { useTasks } from '@/lib/stores/queries/useTasks'; // Import useTasks hook
import { useVehicles } from '@/lib/stores/queries/useVehicles'; // Import useVehicles hook
import { enrichTask } from '@/lib/transformers/taskEnrichment';

function TaskCardSkeleton() {
  return (
    <div className="flex h-full flex-col overflow-hidden rounded-lg border-border/60 bg-card shadow-md">
      <div className="flex grow flex-col p-5">
        <div className="flex items-start justify-between">
          <SkeletonLoader
            className="mb-1 h-7 w-3/5 bg-muted/50"
            count={1}
            variant="default"
          />
          <SkeletonLoader
            className="mb-1 h-5 w-1/4 rounded-full bg-muted/50"
            count={1}
            variant="default"
          />
        </div>
        <SkeletonLoader
          className="mb-3 h-4 w-1/2 bg-muted/50"
          count={1}
          variant="default"
        />
        <SkeletonLoader
          className="my-3 h-px w-full bg-border/50"
          count={1}
          variant="default"
        />
        <div className="grow space-y-2.5">
          {Array.from({ length: 3 }).map((_, i) => (
            <div className="flex items-center" key={i}>
              <SkeletonLoader
                className="mr-2.5 size-5 rounded-full bg-muted/50"
                count={1}
                variant="default"
              />
              <SkeletonLoader
                className="h-5 w-2/3 bg-muted/50"
                count={1}
                variant="default"
              />
            </div>
          ))}
        </div>
      </div>
      <div className="border-t border-border/60 bg-muted/20 p-4">
        <SkeletonLoader
          className="h-10 w-full bg-muted/50"
          count={1}
          variant="default"
        />
      </div>
    </div>
  );
}

const TasksPageContent = () => {
  // Dashboard store for view management
  const dashboardStore = useDashboardStore('task');
  const { layout } = dashboardStore();

  const {
    data: tasks = [],
    error: tasksError,
    isLoading: isLoadingTasks,
    refetch: refetchTasks,
  } = useTasks();
  const {
    data: employees = [],
    error: employeesError,
    isLoading: isLoadingEmployees,
    refetch: refetchEmployees,
  } = useEmployees();
  const {
    data: vehicles = [],
    error: vehiclesError,
    isLoading: isLoadingVehicles,
    refetch: refetchVehicles,
  } = useVehicles();

  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [priorityFilter, setPriorityFilter] = useState<string>('all');
  const [employeeFilter, setEmployeeFilter] = useState<string>('all');
  const [dateRangeFilter, setDateRangeFilter] = useState<{
    from?: Date | undefined;
    to?: Date | undefined;
  }>({});

  const employeesList = useMemo(() => {
    return employees.map(e => ({
      id: String(e.id),
      name: e.fullName ?? e.name,
      role: e.role,
    }));
  }, [employees]);

  // 🔧 FIX: Replace useEffect + useState with useMemo to prevent infinite loop
  // This prevents the "Maximum update depth exceeded" error by avoiding unstable array references
  const filteredTasks = useMemo(() => {
    // First enrich tasks with employee and vehicle data
    const enrichedTasks = tasks.map(task =>
      enrichTask(task, employees, vehicles)
    );

    let tempTasks = [...enrichedTasks];
    const lowercasedSearch = searchTerm.toLowerCase();

    if (statusFilter !== 'all') {
      tempTasks = tempTasks.filter(task => task.status === statusFilter);
    }
    if (priorityFilter !== 'all') {
      tempTasks = tempTasks.filter(task => task.priority === priorityFilter);
    }
    if (employeeFilter !== 'all') {
      tempTasks = tempTasks.filter(
        task =>
          ((task.staffEmployeeId &&
            String(task.staffEmployeeId) === employeeFilter) ||
            (task.driverEmployeeId &&
              String(task.driverEmployeeId) === employeeFilter)) ??
          (employeeFilter === 'unassigned' &&
            !task.staffEmployeeId &&
            !task.driverEmployeeId)
      );
    }

    if (lowercasedSearch) {
      tempTasks = tempTasks.filter(task => {
        const staffEmployee = task.staffEmployeeId
          ? employeesList.find(e => e.id === String(task.staffEmployeeId))
          : null;
        const driverEmployee = task.driverEmployeeId
          ? employeesList.find(e => e.id === String(task.driverEmployeeId))
          : null;

        return (
          (task.description.toLowerCase().includes(lowercasedSearch) ||
            task.location.toLowerCase().includes(lowercasedSearch) ||
            task.notes?.toLowerCase().includes(lowercasedSearch)) ??
          staffEmployee?.name.toLowerCase().includes(lowercasedSearch) ??
          driverEmployee?.name.toLowerCase().includes(lowercasedSearch)
        );
      });
    }

    // Date range filtering
    if (dateRangeFilter.from || dateRangeFilter.to) {
      tempTasks = tempTasks.filter(task => {
        const taskDate = new Date(task.createdAt);

        if (dateRangeFilter.from && dateRangeFilter.to) {
          // Both dates selected - filter by range
          return (
            taskDate >= dateRangeFilter.from && taskDate <= dateRangeFilter.to
          );
        } else if (dateRangeFilter.from) {
          // Only start date selected
          return taskDate >= dateRangeFilter.from;
        } else if (dateRangeFilter.to) {
          // Only end date selected
          return taskDate <= dateRangeFilter.to;
        }
        return true;
      });
    }

    return tempTasks;
  }, [
    searchTerm,
    tasks,
    employees,
    vehicles,
    statusFilter,
    priorityFilter,
    employeeFilter,
    employeesList,
    dateRangeFilter.from,
    dateRangeFilter.to,
  ]);

  const handleRetry = useCallback(async () => {
    await Promise.all([refetchTasks(), refetchEmployees(), refetchVehicles()]);
  }, [refetchTasks, refetchEmployees, refetchVehicles]);

  const activeFilters =
    searchTerm ||
    statusFilter !== 'all' ||
    priorityFilter !== 'all' ||
    employeeFilter !== 'all';

  const getTasksReportUrl = () => {
    const queryParams = new URLSearchParams({
      employee: employeeFilter,
      priority: priorityFilter,
      searchTerm,
      status: statusFilter,
    }).toString();

    return `/tasks/report?${queryParams}`;
  };

  return (
    <div className="space-y-6">
      <AppBreadcrumb homeHref="/" homeLabel="Dashboard" />
      <PageHeader
        description="Oversee all tasks, assignments, and progress."
        icon={ClipboardCheck}
        title="Manage Tasks"
      >
        <div className="flex items-center gap-2">
          <ActionButton
            actionType="primary"
            asChild
            icon={<PlusCircle className="size-4" />}
          >
            <Link href="/tasks/add">Add New Task</Link>
          </ActionButton>
          <ViewReportButton getReportUrl={getTasksReportUrl} isList={true} />
          {/* Settings Button */}
          <Dialog>
            <DialogTrigger asChild>
              <ActionButton
                actionType="secondary"
                icon={<Settings className="size-4" />}
              >
                Settings
              </ActionButton>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[600px]">
              <DialogTitle>Dashboard Settings</DialogTitle>
              <DialogDescription>
                Customize how tasks are displayed and managed.
              </DialogDescription>
              <TaskDashboardSettings />
            </DialogContent>
          </Dialog>
        </div>
      </PageHeader>
      <TaskFilters
        employeesList={employeesList}
        initialFilters={{
          assignee: employeeFilter === 'all' ? [] : [employeeFilter],
          dateRange: dateRangeFilter,
          priority: priorityFilter === 'all' ? [] : [priorityFilter],
          search: searchTerm,
          status: statusFilter === 'all' ? [] : [statusFilter],
        }}
        onFiltersChange={(filters: TaskFilterValues) => {
          setSearchTerm(filters.search);
          setStatusFilter(
            filters.status.length > 0 ? filters.status[0]! : 'all'
          );
          setPriorityFilter(
            filters.priority.length > 0 ? filters.priority[0]! : 'all'
          );
          setEmployeeFilter(
            filters.assignee.length > 0 ? filters.assignee[0]! : 'all'
          );
          setDateRangeFilter(filters.dateRange);
        }}
      />

      <DataLoader
        data={filteredTasks}
        emptyComponent={
          <div className="rounded-lg bg-card py-12 text-center shadow-md">
            <ClipboardCheck className="mx-auto mb-6 size-16 text-muted-foreground" />
            <h3 className="mb-2 text-2xl font-semibold text-foreground">
              {activeFilters
                ? 'No Tasks Match Your Filters'
                : 'No Tasks Created Yet'}
            </h3>
            <p className="mx-auto mb-6 mt-2 max-w-md text-muted-foreground">
              {activeFilters
                ? 'Try adjusting your search or filter criteria.'
                : "It looks like you haven't created any tasks yet. Get started by adding one."}
            </p>
            {!activeFilters && (
              <ActionButton
                actionType="primary"
                asChild
                icon={<PlusCircle className="size-4" />}
                size="lg"
              >
                <Link href="/tasks/add">Create Your First Task</Link>
              </ActionButton>
            )}
          </div>
        }
        error={
          tasksError?.message ??
          employeesError?.message ??
          vehiclesError?.message ??
          null
        }
        isLoading={isLoadingTasks || isLoadingEmployees || isLoadingVehicles}
        loadingComponent={
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
            {Array.from({ length: 3 }).map((_, i) => (
              <TaskCardSkeleton key={i} />
            ))}
          </div>
        }
        onRetry={handleRetry}
      >
        {tasksData => (
          <TaskViewRenderer
            compactMode={layout.compactMode}
            gridColumns={layout.gridColumns}
            tasks={tasksData}
            viewMode={
              layout.viewMode === 'calendar' ? 'cards' : layout.viewMode
            }
          />
        )}
      </DataLoader>
    </div>
  );
};

export default function TasksPage() {
  return (
    <ErrorBoundary>
      <TasksPageContent />
    </ErrorBoundary>
  );
}
