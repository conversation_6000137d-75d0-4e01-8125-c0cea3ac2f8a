'use server';

import {
  suggestMaintenanceSchedule as suggestMaintenanceScheduleFlow,
  type SuggestMaintenanceScheduleInput,
  type SuggestMaintenanceScheduleOutput,
} from '@/ai/flows/suggest-maintenance-schedule';

export async function getMaintenanceSuggestions(
  input: SuggestMaintenanceScheduleInput
): Promise<{
  data?: SuggestMaintenanceScheduleOutput;
  error?: string;
  success: boolean;
}> {
  try {
    // Basic validation (more thorough validation can be done with <PERSON><PERSON> if needed here too)
    if (
      !input.vehicleMake ||
      !input.vehicleModel ||
      !input.vehicleYear ||
      input.currentOdometer == null
    ) {
      return { error: 'Missing required vehicle information.', success: false };
    }
    if (input.currentOdometer < 0) {
      return { error: 'Current odometer cannot be negative.', success: false };
    }
    if (
      input.vehicleYear < 1886 ||
      input.vehicleYear > new Date().getFullYear() + 1
    ) {
      // Benz Patent-Motorwagen to next year
      return { error: 'Invalid vehicle year.', success: false };
    }

    const result = await suggestMaintenanceScheduleFlow(input);
    return { data: result, success: true };
  } catch (error) {
    console.error('Error getting maintenance suggestions:', error);
    const errorMessage =
      error instanceof Error
        ? error.message
        : 'An unknown error occurred while fetching suggestions.';
    // Check for specific Genkit/AI errors if possible, e.g., quota issues, model errors
    if (errorMessage.includes(' candidats')) {
      // Crude check for "No candidates" or similar errors from AI
      return {
        error:
          'The AI model could not generate a suggestion with the provided data. Please ensure the service history is detailed enough.',
        success: false,
      };
    }
    return { error: errorMessage, success: false };
  }
}
