import { useState, useCallback } from 'react';

interface LoginFormData {
  email: string;
  password: string;
  rememberMe: boolean;
}

interface ValidationErrors {
  email?: string;
  password?: string;
}

interface ValidationResult {
  isValid: boolean;
  errors: ValidationErrors;
}

/**
 * Custom hook for login form validation
 *
 * Provides real-time validation with enhanced UX features:
 * - Email format validation
 * - Password strength checking
 * - Real-time error clearing
 * - Enhanced user feedback
 */
export function useLoginValidation() {
  const [errors, setErrors] = useState<ValidationErrors>({});
  const [isFormTouched, setIsFormTouched] = useState(false);

  // Enhanced email validation
  const validateEmail = useCallback((email: string): string | undefined => {
    if (!email) {
      return 'Email address is required';
    }

    // More comprehensive email validation
    const emailRegex =
      /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;

    if (!emailRegex.test(email)) {
      return 'Please enter a valid email address';
    }

    return undefined;
  }, []);

  // Enhanced password validation
  const validatePassword = useCallback(
    (password: string): string | undefined => {
      if (!password) {
        return 'Password is required';
      }

      if (password.length < 6) {
        return 'Password must be at least 6 characters long';
      }

      // Additional password strength checks can be added here
      return undefined;
    },
    []
  );

  // Validate entire form
  const validateForm = useCallback(
    (formData: LoginFormData): ValidationResult => {
      const newErrors: ValidationErrors = {};

      const emailError = validateEmail(formData.email);
      const passwordError = validatePassword(formData.password);

      if (emailError) newErrors.email = emailError;
      if (passwordError) newErrors.password = passwordError;

      setErrors(newErrors);

      return {
        isValid: Object.keys(newErrors).length === 0,
        errors: newErrors,
      };
    },
    [validateEmail, validatePassword]
  );

  // Validate single field
  const validateField = useCallback(
    (fieldName: keyof LoginFormData, value: string) => {
      let fieldError: string | undefined;

      switch (fieldName) {
        case 'email':
          fieldError = validateEmail(value);
          break;
        case 'password':
          fieldError = validatePassword(value);
          break;
        default:
          return;
      }

      setErrors(prev => ({
        ...prev,
        [fieldName]: fieldError,
      }));
    },
    [validateEmail, validatePassword]
  );

  // Clear specific field error
  const clearFieldError = useCallback((fieldName: keyof ValidationErrors) => {
    setErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[fieldName];
      return newErrors;
    });
  }, []);

  // Clear all errors
  const clearAllErrors = useCallback(() => {
    setErrors({});
  }, []);

  // Mark form as touched
  const markFormTouched = useCallback(() => {
    setIsFormTouched(true);
  }, []);

  // Reset validation state
  const resetValidation = useCallback(() => {
    setErrors({});
    setIsFormTouched(false);
  }, []);

  // Check if field has been validated successfully
  const isFieldValid = useCallback(
    (fieldName: keyof ValidationErrors, value: string) => {
      return isFormTouched && value && !errors[fieldName];
    },
    [errors, isFormTouched]
  );

  return {
    errors,
    isFormTouched,
    validateForm,
    validateField,
    clearFieldError,
    clearAllErrors,
    markFormTouched,
    resetValidation,
    isFieldValid,
  };
}
