'use client';

import { format, parseISO } from 'date-fns';
import {
  AlertTriangle,
  ArrowLeft,
  CalendarDays,
  Car,
  CheckCircle,
  ClipboardList,
  Clock,
  Edit,
  FileText,
  MapPin,
  RefreshCw,
  Trash2,
  User,
  UserMinus,
  UserPlus,
  Users,
  XCircle,
} from 'lucide-react';
import Link from 'next/link';
import { useParams, useRouter } from 'next/navigation';
import React, { useCallback, useState } from 'react';

import type {
  CreateTaskData, // For updateTaskMutation
  Task as DomainTask, // Renamed for clarity
  Employee,
  Subtask,
  TaskPriorityPrisma, // For priority color
  TaskStatusPrisma, // For status comparisons
} from '@/lib/types/domain';

import { ActionButton } from '@/components/ui/action-button';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { AppBreadcrumb } from '@/components/ui/app-breadcrumb';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { DataLoader, SkeletonLoader } from '@/components/ui/loading';
import { PageHeader } from '@/components/ui/PageHeader';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { usePredefinedEntityToast } from '@/hooks/forms/useFormToast';
import { useEmployee, useEmployees } from '@/lib/stores/queries/useEmployees';
import {
  useDeleteTask,
  useTask,
  useTaskWithAssignments,
  useUpdateTask,
} from '@/lib/stores/queries/useTasks';
import { cn } from '@/lib/utils';
import { formatEmployeeName } from '@/lib/utils/formattingUtils';

const getStatusColor = (status: TaskStatusPrisma | undefined) => {
  switch (status) {
    case 'Assigned': {
      return 'bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-800/30';
    }
    case 'Cancelled': {
      return 'bg-red-100 text-red-800 border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800/30';
    }
    case 'Completed': {
      return 'bg-green-100 text-green-800 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800/30';
    }
    case 'In_Progress': {
      return 'bg-indigo-100 text-indigo-800 border-indigo-200 dark:bg-indigo-900/20 dark:text-indigo-400 dark:border-indigo-800/30';
    }
    case 'Pending': {
      return 'bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900/20 dark:text-blue-400 dark:border-blue-800/30';
    }
    default: {
      return 'bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-900/20 dark:text-gray-400 dark:border-gray-800/30';
    }
  }
};

const getPriorityColor = (priority: TaskPriorityPrisma | undefined) => {
  switch (priority) {
    case 'High': {
      return 'bg-red-100 text-red-800 border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800/30';
    }
    case 'Low': {
      return 'bg-green-100 text-green-800 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800/30';
    }
    case 'Medium': {
      return 'bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-800/30';
    }
    default: {
      return 'bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-900/20 dark:text-gray-400 dark:border-gray-800/30';
    }
  }
};

const formatDate = (dateString: string | undefined, includeTime = false) => {
  if (!dateString) return 'N/A';
  try {
    return format(
      parseISO(dateString),
      includeTime ? 'MMM d, yyyy, HH:mm' : 'MMM d, yyyy'
    );
  } catch {
    return 'Invalid Date';
  }
};

interface AssignedEmployeeDisplayProps {
  employee: Employee | null | undefined;
  employeeId?: null | number;
  isTaskCompleted?: boolean;
  onUnassign?: (employeeId: number) => void;
}

interface KeyValueItemProps {
  children?: React.ReactNode;
  className?: string;
  icon: React.ElementType;
  label: string;
  value?: null | number | string;
}

export default function TaskDetailPage() {
  const params = useParams();
  const router = useRouter();
  const {
    showEntityDeleted,
    showEntityDeletionError,
    showEntityUpdated,
    showEntityUpdateError,
    showFormError,
    showFormSuccess,
  } = usePredefinedEntityToast('task');
  const taskId = params?.id as string;

  const {
    data: task, // This is enriched Task | undefined
    error: taskError,
    isLoading: isLoadingTask,
    refetch: refetchTaskData,
  } = useTaskWithAssignments(taskId);

  const { data: employeesData } = useEmployees();
  const availableEmployees =
    employeesData?.filter(e => e.status === 'Active') || []; // Changed e.isActive

  const { mutateAsync: deleteTaskMutation } = useDeleteTask();
  const { mutateAsync: updateTaskMutation } = useUpdateTask();

  const [selectedAssigneeId, setSelectedAssigneeId] = useState<string>('');

  const isLoadingData = isLoadingTask ?? false;
  const error = taskError;

  const handleDeleteTask = async () => {
    if (task?.id) {
      try {
        await deleteTaskMutation(task.id);
        const taskForToast = {
          name:
            task.description.slice(0, 30) +
            (task.description.length > 30 ? '...' : ''),
          title:
            task.description.slice(0, 30) +
            (task.description.length > 30 ? '...' : ''),
        };
        showEntityDeleted(taskForToast);
        router.push('/tasks');
      } catch (error_: any) {
        console.error('Error deleting task:', error_);
        showEntityDeletionError(
          error_.message || 'Failed to delete task. Please try again.'
        );
      }
    }
  };

  const handleAssign = async () => {
    if (task?.id && selectedAssigneeId) {
      try {
        // Use updateTaskMutation to assign employee
        await updateTaskMutation({
          data: {
            driverEmployeeId: Number.parseInt(selectedAssigneeId), // Convert to number for Employee.id
          },
          id: task.id,
        });
        const updatedTask = {
          name: `Task assigned to employee ID ${selectedAssigneeId}`,
          title: `Task assigned to employee ID ${selectedAssigneeId}`,
        };
        showEntityUpdated(updatedTask);
        refetchTaskData();
        setSelectedAssigneeId('');
      } catch (error_: any) {
        console.error('Error assigning task:', error_);
        showEntityUpdateError(
          error_.message || 'Failed to assign task. Please try again.'
        );
      }
    }
  };

  const handleUnassign = async (employeeIdToUnassign: number) => {
    if (task?.id) {
      const updatePayload: Partial<CreateTaskData> = {};
      if (task.driverEmployeeId === employeeIdToUnassign) {
        updatePayload.driverEmployeeId = undefined; // Set to undefined to clear
      } else if (task.staffEmployeeId === employeeIdToUnassign) {
        showFormError(
          'Cannot unassign the primary staff member directly. Please reassign.',
          {
            errorTitle: 'Action Not Allowed',
          }
        );
        return;
      } else {
        showFormError('Employee not found in task assignments.');
        return;
      }

      if (Object.keys(updatePayload).length === 0) {
        showFormSuccess({
          successDescription: 'No changes to apply for unassignment.',
          successTitle: 'Info',
        });
        return;
      }

      try {
        await updateTaskMutation({
          data: updatePayload,
          id: task.id,
        });
        const updatedTask = {
          name: 'Employee unassigned from task',
          title: 'Employee unassigned from task',
        };
        showEntityUpdated(updatedTask);
        refetchTaskData();
      } catch (error_: any) {
        console.error('Error updating task assignment:', error_);
        showEntityUpdateError(
          error_.message || 'Failed to unassign task. Please try again.'
        );
      }
    }
  };

  const isTaskCompleted =
    task?.status === 'Completed' || task?.status === 'Cancelled'; // Use Prisma enum values

  return (
    <div className="space-y-6">
      <AppBreadcrumb />
      <DataLoader
        data={task}
        emptyComponent={
          <div className="py-10 text-center">
            <PageHeader icon={AlertTriangle} title="Task Not Found" />
            <p className="mb-4">The requested task could not be found.</p>
          </div>
        }
        error={error ? (error as Error).message : null}
        isLoading={isLoadingData}
        loadingComponent={
          <div className="space-y-6">
            <PageHeader icon={ClipboardList} title="Loading Task..." />
            <SkeletonLoader count={1} variant="card" />
            <div className="grid gap-6 lg:grid-cols-3">
              <SkeletonLoader
                className="lg:col-span-2"
                count={1}
                variant="card"
              />
              <SkeletonLoader count={1} variant="card" />
            </div>
          </div>
        }
        onRetry={refetchTaskData}
      >
        {(loadedTask: DomainTask) => (
          <>
            <PageHeader
              description="Manage details and assignment for this task."
              icon={ClipboardList}
              title={loadedTask.description || 'Task Details'} // Use description
            >
              <div className="flex flex-wrap items-center gap-2">
                <Button asChild className="gap-2" variant="default">
                  <Link href={`/tasks/${loadedTask.id}/edit`}>
                    <Edit className="size-4" />
                    Edit
                  </Link>
                </Button>
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button className="gap-2" variant="destructive">
                      <Trash2 className="size-4" />
                      Delete Task
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                      <AlertDialogDescription>
                        This action cannot be undone. This will permanently
                        delete the task.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                      <AlertDialogAction
                        className="bg-destructive hover:bg-destructive/90"
                        onClick={handleDeleteTask}
                      >
                        Delete
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </div>
            </PageHeader>

            <div className="grid gap-6 lg:grid-cols-3">
              <Card className="shadow-sm lg:col-span-2">
                <CardHeader className="border-b">
                  <div className="flex items-start justify-between gap-4">
                    <div className="space-y-1">
                      <CardTitle className="text-2xl font-bold leading-tight">
                        {loadedTask.description} {/* Use description */}
                      </CardTitle>
                      {/* <CardDescription>{loadedTask.description}</CardDescription> */}{' '}
                      {/* Redundant if title is description */}
                    </div>
                    <div className="flex flex-col gap-2">
                      <Badge
                        className={cn(
                          'justify-center',
                          getStatusColor(loadedTask.status)
                        )}
                      >
                        {loadedTask.status}
                      </Badge>
                      <Badge
                        className={cn(
                          'justify-center',
                          getPriorityColor(loadedTask.priority)
                        )}
                        variant="outline"
                      >
                        {loadedTask.priority} Priority
                      </Badge>
                    </div>
                  </div>
                </CardHeader>

                <CardContent className="space-y-6 p-6">
                  {/* Task Location */}
                  <KeyValueItem
                    icon={MapPin}
                    label="Location"
                    value={loadedTask.location}
                  />

                  {/* Task Start Date/Time */}
                  <KeyValueItem
                    icon={CalendarDays}
                    label="Start Date & Time"
                    value={formatDate(loadedTask.dateTime, true)}
                  />

                  {/* Task Duration */}
                  <KeyValueItem
                    icon={Clock}
                    label="Estimated Duration"
                    value={`${loadedTask.estimatedDuration} minutes`}
                  />

                  {loadedTask.deadline && ( // Changed from dueDate
                    <KeyValueItem
                      icon={Clock}
                      label="Deadline" // Changed from Due Date
                      value={formatDate(loadedTask.deadline, false)}
                    />
                  )}

                  {/* Required Skills */}
                  {loadedTask.requiredSkills &&
                    loadedTask.requiredSkills.length > 0 && (
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <Users className="size-4 text-muted-foreground" />
                          <span className="text-sm font-medium">
                            Required Skills
                          </span>
                        </div>
                        <div className="flex flex-wrap gap-2">
                          {loadedTask.requiredSkills.map((skill, index) => (
                            <Badge
                              className="text-xs"
                              key={index}
                              variant="secondary"
                            >
                              {skill}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}

                  {/* Task Notes */}
                  {loadedTask.notes && (
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <FileText className="size-4 text-muted-foreground" />
                        <span className="text-sm font-medium">Notes</span>
                      </div>
                      <p className="rounded-lg bg-muted/30 p-3 text-sm text-muted-foreground">
                        {loadedTask.notes}
                      </p>
                    </div>
                  )}

                  {/* Vehicle Assignment */}
                  {loadedTask.vehicleId && (
                    <KeyValueItem icon={Car} label="Assigned Vehicle">
                      <div className="rounded-lg border bg-muted/30 p-3">
                        <p className="text-sm font-medium">
                          {loadedTask.vehicle
                            ? `${loadedTask.vehicle.make} ${loadedTask.vehicle.model}`
                            : `Vehicle ID: ${loadedTask.vehicleId}`}
                        </p>
                        {loadedTask.vehicle?.licensePlate && (
                          <p className="mt-1 text-xs text-muted-foreground">
                            License Plate: {loadedTask.vehicle.licensePlate}
                          </p>
                        )}
                        {loadedTask.vehicle?.year && (
                          <p className="text-xs text-muted-foreground">
                            Year: {loadedTask.vehicle.year}
                          </p>
                        )}
                      </div>
                    </KeyValueItem>
                  )}

                  {loadedTask.subtasks && loadedTask.subtasks.length > 0 && (
                    <div className="space-y-3">
                      <Separator />
                      <div>
                        <h3 className="mb-3 flex items-center gap-2 text-lg font-semibold">
                          <ClipboardList className="size-4" />
                          Sub-Tasks
                        </h3>
                        <ul className="space-y-2">
                          {loadedTask.subtasks.map((sub: Subtask) => (
                            <li
                              className={cn(
                                'flex items-center gap-2 text-sm',
                                sub.completed &&
                                  'line-through text-muted-foreground' // Use completed
                              )}
                              key={sub.id}
                            >
                              {sub.completed ? ( // Use completed
                                <CheckCircle className="size-4 text-green-500" />
                              ) : (
                                <div className="size-4 rounded-sm border" />
                              )}
                              {sub.title}
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  )}
                </CardContent>

                <CardFooter className="flex items-center justify-center border-t bg-muted/20">
                  <div className="flex w-full flex-col gap-2 text-xs text-muted-foreground sm:flex-row sm:items-center sm:justify-between">
                    <div className="flex items-center gap-1">
                      <span className="font-medium">Created:</span>
                      <span>{formatDate(loadedTask.createdAt, true)}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <span className="font-medium">Updated:</span>
                      <span>{formatDate(loadedTask.updatedAt, true)}</span>
                    </div>
                  </div>
                </CardFooter>
              </Card>

              <Card className="h-fit shadow-sm">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Users className="size-5 text-primary" />
                    Task Assignment
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Staff Assignment */}
                  {loadedTask.staffEmployeeId && (
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <User className="size-4 text-muted-foreground" />
                        <span className="text-sm font-medium text-muted-foreground">
                          Staff Assignment
                        </span>
                      </div>
                      <AssignedEmployeeDisplay
                        employee={loadedTask.staffEmployee}
                        employeeId={loadedTask.staffEmployeeId}
                        isTaskCompleted={isTaskCompleted}
                        onUnassign={() => {
                          if (loadedTask.staffEmployeeId) {
                            handleUnassign(loadedTask.staffEmployeeId);
                          }
                        }}
                      />
                    </div>
                  )}

                  {/* Driver Assignment */}
                  {loadedTask.driverEmployeeId && (
                    <>
                      {loadedTask.staffEmployeeId && (
                        <Separator className="my-4" />
                      )}
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <Users className="size-4 text-muted-foreground" />
                          <span className="text-sm font-medium text-muted-foreground">
                            Driver Assignment
                          </span>
                        </div>
                        <AssignedEmployeeDisplay
                          employee={loadedTask.driverEmployee}
                          employeeId={loadedTask.driverEmployeeId}
                          isTaskCompleted={isTaskCompleted}
                          onUnassign={() => {
                            if (loadedTask.driverEmployeeId) {
                              handleUnassign(loadedTask.driverEmployeeId);
                            }
                          }}
                        />
                      </div>
                    </>
                  )}

                  {/* No assignments message */}
                  {!loadedTask.staffEmployeeId &&
                    !loadedTask.driverEmployeeId && (
                      <div className="py-4 text-center">
                        <p className="text-sm text-muted-foreground">
                          This task is currently unassigned.
                        </p>
                      </div>
                    )}

                  {!isTaskCompleted &&
                    !(
                      loadedTask.staffEmployeeId || loadedTask.driverEmployeeId
                    ) && (
                      <div className="space-y-4 border-t pt-4">
                        <p className="text-sm text-muted-foreground">
                          Assign task to an employee:
                        </p>
                        <div className="space-y-3">
                          <Label
                            className="text-sm font-medium"
                            htmlFor="assignee-select"
                          >
                            Select Employee
                          </Label>
                          <Select
                            onValueChange={setSelectedAssigneeId}
                            value={selectedAssigneeId}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select an employee" />
                            </SelectTrigger>
                            <SelectContent>
                              {availableEmployees.length > 0 ? (
                                availableEmployees.map((e: Employee) => (
                                  <SelectItem key={e.id} value={String(e.id)}>
                                    {e.fullName || e.name} (
                                    {e.position || e.role})
                                  </SelectItem>
                                ))
                              ) : (
                                <div className="p-2 text-sm text-muted-foreground">
                                  No available employees.
                                </div>
                              )}
                            </SelectContent>
                          </Select>
                          <Button
                            className="w-full gap-2"
                            disabled={!selectedAssigneeId}
                            onClick={handleAssign}
                          >
                            <UserPlus className="size-4" />
                            Assign to Selected Employee
                          </Button>
                        </div>
                      </div>
                    )}

                  {loadedTask.status === 'Completed' && ( // Use Prisma enum value
                    <div className="flex items-center gap-2 rounded-lg border border-green-200 bg-green-50 p-3 dark:border-green-800 dark:bg-green-900/20">
                      <CheckCircle className="size-4 text-green-600" />
                      <p className="text-sm text-green-700 dark:text-green-400">
                        Task completed.
                      </p>
                    </div>
                  )}
                  {loadedTask.status === 'Cancelled' && ( // Use Prisma enum value
                    <div className="flex items-center gap-2 rounded-lg border border-red-200 bg-red-50 p-3 dark:border-red-800 dark:bg-red-900/20">
                      <XCircle className="size-4 text-red-600" />
                      <p className="text-sm text-red-700 dark:text-red-400">
                        Task cancelled.
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </>
        )}
      </DataLoader>
    </div>
  );
}

function AssignedEmployeeDisplay({
  employee,
  employeeId,
  isTaskCompleted,
  onUnassign,
}: AssignedEmployeeDisplayProps) {
  if (!employeeId && employeeId !== 0) {
    return (
      <p className="text-sm text-muted-foreground">
        This task is currently unassigned.
      </p>
    );
  }

  if (!employee) {
    return (
      <p className="text-sm text-destructive">
        Could not load assignee (ID: {employeeId}).
      </p>
    );
  }

  const fullName = formatEmployeeName(employee);

  return (
    <div className="space-y-3">
      <div className="flex items-center gap-2 rounded-lg border bg-muted/30 p-2">
        <Avatar className="size-8">
          <AvatarFallback className="text-xs font-medium">
            {fullName
              .split(' ')
              .map((n: string) => n[0])
              .join('')
              .toUpperCase()
              .slice(0, 2)}
          </AvatarFallback>
        </Avatar>
        <div className="flex flex-col">
          <Link
            className="text-sm font-medium hover:text-primary"
            href={`/employees/${employee.employeeId}`}
          >
            {fullName}
          </Link>
          <span className="text-xs capitalize text-muted-foreground">
            {employee.position || employee.role}
          </span>
        </div>
        {!isTaskCompleted && onUnassign && employeeId && (
          <Button
            className="size-6 p-0 text-muted-foreground hover:text-destructive"
            onClick={() => onUnassign(employeeId)}
            size="sm"
            variant="ghost"
          >
            <UserMinus className="size-3" />
          </Button>
        )}
      </div>
    </div>
  );
}

function KeyValueItem({
  children,
  className,
  icon: Icon,
  label,
  value,
}: KeyValueItemProps) {
  if (value === undefined && !children) return null;
  return (
    <div className={cn('flex items-start gap-3', className)}>
      <Icon className="mt-1 size-4 shrink-0 text-muted-foreground" />
      <div className="space-y-1">
        <p className="text-sm text-muted-foreground">{label}</p>
        {value !== undefined && value !== null && (
          <p className="text-base font-medium text-foreground">{value}</p>
        )}
        {children}
      </div>
    </div>
  );
}
