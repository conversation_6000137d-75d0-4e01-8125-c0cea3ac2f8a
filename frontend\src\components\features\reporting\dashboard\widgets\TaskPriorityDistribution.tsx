/**
 * @file Task Priority Distribution Component - Phase 2 Implementation
 * @description Task priority distribution chart following existing chart patterns
 *
 * SOLID Principles Applied:
 * - SRP: Single responsibility of visualizing task priority distribution
 * - OCP: Open for extension via props configuration and styling options
 * - DIP: Depends on existing chart utilities and component abstractions
 *
 * Architecture Compliance:
 * - Follows existing chart component patterns
 * - Uses established chart utilities and styling
 * - Integrates with existing chart framework
 * - Maintains consistent visual design
 */

'use client';

import React, { useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Cell,
} from 'recharts';
import { TaskPriorityPrisma } from '@/lib/types/domain';
import { TaskPriorityDistributionData } from '../../data/types/reporting';
import { Flag } from 'lucide-react';

interface TaskPriorityDistributionProps {
  data?: TaskPriorityDistributionData[];
  className?: string;
  showLegend?: boolean;
  interactive?: boolean;
  height?: number;
}

/**
 * @component TaskPriorityDistribution
 * @description Task priority distribution chart following existing chart patterns
 *
 * Responsibilities:
 * - Visualize task priority distribution using bar chart
 * - Follow existing chart component patterns
 * - Integrate with existing chart utilities
 * - Maintain consistent chart styling
 * - Provide interactive features when enabled
 *
 * SOLID Principles Applied:
 * - SRP: Single responsibility of displaying task priority chart
 * - OCP: Open for extension via props configuration
 * - DIP: Depends on chart library abstractions
 */
export const TaskPriorityDistribution: React.FC<
  TaskPriorityDistributionProps
> = ({
  data = [],
  className = '',
  showLegend = true,
  interactive = true,
  height = 300,
}) => {
  // Transform data for chart consumption following existing patterns
  const chartData = useMemo(() => {
    return data.map(item => ({
      name: formatPriorityLabel(item.priority),
      value: item.count,
      color: item.color || getTaskPriorityColor(item.priority),
      percentage: Math.round(item.percentage),
      priority: item.priority,
    }));
  }, [data]);

  // Calculate total for display
  const totalTasks = useMemo(() => {
    return chartData.reduce((sum, item) => sum + item.value, 0);
  }, [chartData]);

  // Custom tooltip component following existing patterns
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white dark:bg-gray-800 p-3 border rounded-lg shadow-lg">
          <p className="font-medium">{label}</p>
          <p className="text-sm text-muted-foreground">
            Count: <span className="font-medium">{data.value}</span>
          </p>
          <p className="text-sm text-muted-foreground">
            Percentage: <span className="font-medium">{data.percentage}%</span>
          </p>
        </div>
      );
    }
    return null;
  };

  // Custom bar component with priority-based colors
  const CustomBar = (props: any) => {
    const { fill, ...rest } = props;
    const data = props.payload;
    return <Bar {...rest} fill={data?.color || fill} />;
  };

  // Handle empty data state
  if (!data || data.length === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Flag className="h-5 w-5" />
            Task Priority Distribution
          </CardTitle>
        </CardHeader>
        <CardContent
          className="flex items-center justify-center"
          style={{ height }}
        >
          <div className="text-center text-muted-foreground">
            <Flag className="h-12 w-12 mx-auto mb-2 opacity-50" />
            <p>No task priority data available</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Flag className="h-5 w-5" />
          Task Priority Distribution
        </CardTitle>
        <p className="text-sm text-muted-foreground">
          Total Tasks: {totalTasks}
        </p>
      </CardHeader>

      <CardContent>
        <ResponsiveContainer width="100%" height={height}>
          <BarChart
            data={chartData}
            margin={{
              top: 20,
              right: 30,
              left: 20,
              bottom: 5,
            }}
          >
            <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
            <XAxis
              dataKey="name"
              tick={{ fontSize: 12 }}
              className="text-muted-foreground"
            />
            <YAxis tick={{ fontSize: 12 }} className="text-muted-foreground" />
            {interactive && <Tooltip content={<CustomTooltip />} />}
            <Bar dataKey="value" radius={[4, 4, 0, 0]} fill="#8884d8">
              {chartData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.color} />
              ))}
            </Bar>
          </BarChart>
        </ResponsiveContainer>

        {/* Priority Legend */}
        {showLegend && (
          <div className="flex flex-wrap justify-center gap-4 mt-4">
            {chartData.map((entry, index) => (
              <div key={index} className="flex items-center gap-2">
                <div
                  className="w-3 h-3 rounded"
                  style={{ backgroundColor: entry.color }}
                />
                <span className="text-sm text-muted-foreground">
                  {entry.name}: {entry.value} ({entry.percentage}%)
                </span>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

/**
 * Utility function to format priority labels for display
 * Following existing patterns for priority formatting
 */
const formatPriorityLabel = (priority: TaskPriorityPrisma): string => {
  const priorityLabels: Record<TaskPriorityPrisma, string> = {
    Low: 'Low',
    Medium: 'Medium',
    High: 'High',
  };
  return priorityLabels[priority] || priority;
};

/**
 * Utility function to get consistent colors for task priorities
 * Following existing color patterns and accessibility guidelines
 */
const getTaskPriorityColor = (priority: TaskPriorityPrisma): string => {
  const colorMap: Record<TaskPriorityPrisma, string> = {
    Low: '#10b981', // Green - low priority
    Medium: '#f59e0b', // Amber - medium priority
    High: '#ef4444', // Red - high priority
  };
  return colorMap[priority] || '#6b7280'; // Gray fallback
};
