import * as z from 'zod';

import { DriverAvailabilitySchema } from './driverSchemas'; // Import for reuse

export const EmployeeStatusSchema = z.enum([
  'Active',
  'On_Leave',
  'Terminated',
  'Inactive', // Added Inactive based on seed data
]);

export const EmployeeRoleSchema = z.enum([
  'driver',
  'mechanic',
  'administrator',
  'office_staff',
  'manager',
  'service_advisor',
  'technician', // Added Technician based on seed data
  'other',
]);

export const EmployeeFormSchema = z.object({
  availability: DriverAvailabilitySchema.optional().nullable(),
  contactEmail: z.string().email('Invalid email address').nullable().optional(),
  contactInfo: z.string().min(1, 'Contact Info is required'),
  contactMobile: z.string().nullable().optional(),
  contactPhone: z.string().nullable().optional(),
  currentLocation: z.string().nullable().optional(),
  department: z.string().nullable().optional(),
  employeeId: z.string().min(1, 'Employee ID (unique business ID) is required'),
  fullName: z.string().nullable().optional(),
  generalAssignments: z.array(z.string()),
  hireDate: z
    .string()
    .nullable()
    .optional()
    .refine(
      val => {
        if (val === null || val === '') return true; // Allow null or empty string
        return (
          val === undefined ||
          val === null ||
          val === '' ||
          !isNaN(Date.parse(val))
        );
      },
      {
        message: 'Invalid hire date',
      }
    ),
  name: z.string().min(1, 'Name is required'),
  notes: z.string().nullable().optional(),
  position: z.string().nullable().optional(),
  profileImageUrl: z
    .string()
    .optional()
    .nullable()
    .refine(
      val => {
        // Allow empty string, null, or undefined (optional)
        if (!val || val.trim() === '') return true;
        // If value exists, validate it as URL
        try {
          new URL(val);
          return true;
        } catch {
          return false;
        }
      },
      {
        message: 'Invalid URL for profile image',
      }
    ),
  role: EmployeeRoleSchema,
  shiftSchedule: z.string().nullable().optional(),
  // Note: Vehicle assignments are now context-specific (per task/delegation)
  skills: z.array(z.string()),
  status: EmployeeStatusSchema,
  workingHours: z.string().nullable().optional(),
});

// Export types
export type EmployeeFormData = z.infer<typeof EmployeeFormSchema>;
export type EmployeeStatus = z.infer<typeof EmployeeStatusSchema>;
export type EmployeeRole = z.infer<typeof EmployeeRoleSchema>;
