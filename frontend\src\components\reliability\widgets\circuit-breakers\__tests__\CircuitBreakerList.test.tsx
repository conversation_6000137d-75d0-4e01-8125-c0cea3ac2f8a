/**
 * @file Tests for CircuitBreakerList component.
 * @module components/reliability/widgets/circuit-breakers/__tests__/CircuitBreakerList.test
 */

import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import React from 'react';

import { CircuitBreakerList } from '../CircuitBreakerList';
import { useCircuitBreakerStatus } from '@/lib/stores/queries/useReliability';
import type { CircuitBreakerStatus } from '@/lib/types/domain';

// Mock the useCircuitBreakerStatus hook
jest.mock('@/lib/stores/queries/useReliability', () => ({
  useCircuitBreakerStatus: jest.fn(),
}));

const mockUseCircuitBreakerStatus =
  useCircuitBreakerStatus as jest.MockedFunction<
    typeof useCircuitBreakerStatus
  >;

describe('CircuitBreakerList', () => {
  const mockCircuitBreakerData: CircuitBreakerStatus = {
    circuitBreakers: [
      {
        name: 'database-service',
        state: 'CLOSED',
        failureCount: 0,
        successCount: 150,
        timeout: 5000,
      },
      {
        name: 'payment-service',
        state: 'OPEN',
        failureCount: 8,
        lastFailureTime: '2024-01-15T10:30:00Z',
        nextAttempt: '2024-01-15T10:35:00Z',
        timeout: 10000,
      },
      {
        name: 'notification-service',
        state: 'HALF_OPEN',
        failureCount: 3,
        successCount: 2,
        lastFailureTime: '2024-01-15T10:25:00Z',
        timeout: 7500,
      },
    ],
    summary: {
      total: 3,
      closed: 1,
      open: 1,
      halfOpen: 1,
    },
  };

  const mockRefetch = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    mockRefetch.mockResolvedValue({});
  });

  it('renders loading state correctly', () => {
    mockUseCircuitBreakerStatus.mockReturnValue({
      data: undefined,
      isLoading: true,
      error: null,
      refetch: mockRefetch,
    } as any);

    render(<CircuitBreakerList />);

    expect(screen.getByText('Circuit Breakers')).toBeInTheDocument();
    // Check for loading skeletons
    expect(screen.getAllByRole('generic')).toHaveLength(5); // 5 skeleton rows
  });

  it('renders error state correctly', () => {
    mockUseCircuitBreakerStatus.mockReturnValue({
      data: undefined,
      isLoading: false,
      error: new Error('Failed to fetch'),
      refetch: mockRefetch,
    } as any);

    render(<CircuitBreakerList />);

    expect(
      screen.getByText('Failed to load circuit breaker data')
    ).toBeInTheDocument();
    expect(screen.getByText('Retry')).toBeInTheDocument();
  });

  it('renders circuit breaker table correctly', async () => {
    mockUseCircuitBreakerStatus.mockReturnValue({
      data: mockCircuitBreakerData,
      isLoading: false,
      error: null,
      refetch: mockRefetch,
    } as any);

    render(<CircuitBreakerList />);

    // Check table headers
    expect(screen.getByText('Name')).toBeInTheDocument();
    expect(screen.getByText('Status')).toBeInTheDocument();
    expect(screen.getByText('Failures')).toBeInTheDocument();
    expect(screen.getByText('Last Failure')).toBeInTheDocument();
    expect(screen.getByText('Next Attempt')).toBeInTheDocument();

    // Check circuit breaker data
    expect(screen.getByText('database-service')).toBeInTheDocument();
    expect(screen.getByText('payment-service')).toBeInTheDocument();
    expect(screen.getByText('notification-service')).toBeInTheDocument();

    // Check status badges
    expect(screen.getByText('Healthy')).toBeInTheDocument();
    expect(screen.getByText('Failed')).toBeInTheDocument();
    expect(screen.getByText('Testing')).toBeInTheDocument();
  });

  it('filters circuit breakers by search term', async () => {
    const user = userEvent.setup();

    mockUseCircuitBreakerStatus.mockReturnValue({
      data: mockCircuitBreakerData,
      isLoading: false,
      error: null,
      refetch: mockRefetch,
    } as any);

    render(<CircuitBreakerList />);

    const searchInput = screen.getByPlaceholderText(
      'Search circuit breakers...'
    );
    await user.type(searchInput, 'payment');

    // Should only show payment-service
    expect(screen.getByText('payment-service')).toBeInTheDocument();
    expect(screen.queryByText('database-service')).not.toBeInTheDocument();
    expect(screen.queryByText('notification-service')).not.toBeInTheDocument();
  });

  it('filters circuit breakers by state', async () => {
    const user = userEvent.setup();

    mockUseCircuitBreakerStatus.mockReturnValue({
      data: mockCircuitBreakerData,
      isLoading: false,
      error: null,
      refetch: mockRefetch,
    } as any);

    render(<CircuitBreakerList />);

    // Click on state filter dropdown
    const stateFilter = screen.getByRole('combobox');
    await user.click(stateFilter);

    // Select "Failed" option
    const failedOption = screen.getByText('Failed');
    await user.click(failedOption);

    // Should only show payment-service (OPEN state)
    expect(screen.getByText('payment-service')).toBeInTheDocument();
    expect(screen.queryByText('database-service')).not.toBeInTheDocument();
    expect(screen.queryByText('notification-service')).not.toBeInTheDocument();
  });

  it('calls refetch when refresh button is clicked', async () => {
    const user = userEvent.setup();

    mockUseCircuitBreakerStatus.mockReturnValue({
      data: mockCircuitBreakerData,
      isLoading: false,
      error: null,
      refetch: mockRefetch,
    } as any);

    render(<CircuitBreakerList />);

    const refreshButton = screen.getByText('Refresh');
    await user.click(refreshButton);

    expect(mockRefetch).toHaveBeenCalledTimes(1);
  });

  it('calls refetch when retry button is clicked in error state', async () => {
    const user = userEvent.setup();

    mockUseCircuitBreakerStatus.mockReturnValue({
      data: undefined,
      isLoading: false,
      error: new Error('Failed to fetch'),
      refetch: mockRefetch,
    } as any);

    render(<CircuitBreakerList />);

    const retryButton = screen.getByText('Retry');
    await user.click(retryButton);

    expect(mockRefetch).toHaveBeenCalledTimes(1);
  });

  it('shows timeout information for circuit breakers', async () => {
    mockUseCircuitBreakerStatus.mockReturnValue({
      data: mockCircuitBreakerData,
      isLoading: false,
      error: null,
      refetch: mockRefetch,
    } as any);

    render(<CircuitBreakerList />);

    // Check for timeout display
    expect(screen.getByText('Timeout: 5000ms')).toBeInTheDocument();
    expect(screen.getByText('Timeout: 10000ms')).toBeInTheDocument();
    expect(screen.getByText('Timeout: 7500ms')).toBeInTheDocument();
  });

  it('shows success count tooltips', async () => {
    const user = userEvent.setup();

    mockUseCircuitBreakerStatus.mockReturnValue({
      data: mockCircuitBreakerData,
      isLoading: false,
      error: null,
      refetch: mockRefetch,
    } as any);

    render(<CircuitBreakerList />);

    // Find success count indicators
    const successIndicators = screen.getAllByText(/^\+\d+$/);
    expect(successIndicators).toHaveLength(2); // database-service and notification-service

    // Hover over success indicator to show tooltip
    if (successIndicators[0]) {
      await user.hover(successIndicators[0]);

      await waitFor(() => {
        expect(screen.getByText('150 successful requests')).toBeInTheDocument();
      });
    }
  });

  it('respects maxItems prop', async () => {
    const manyBreakersData: CircuitBreakerStatus = {
      circuitBreakers: Array.from({ length: 15 }, (_, i) => ({
        name: `service-${i + 1}`,
        state: 'CLOSED' as const,
        failureCount: 0,
      })),
      summary: { total: 15, closed: 15, open: 0, halfOpen: 0 },
    };

    mockUseCircuitBreakerStatus.mockReturnValue({
      data: manyBreakersData,
      isLoading: false,
      error: null,
      refetch: mockRefetch,
    } as any);

    render(<CircuitBreakerList maxItems={5} />);

    // Should only show first 5 services
    expect(screen.getByText('service-1')).toBeInTheDocument();
    expect(screen.getByText('service-5')).toBeInTheDocument();
    expect(screen.queryByText('service-6')).not.toBeInTheDocument();

    // Should show count message
    expect(
      screen.getByText('Showing 5 of 15 circuit breakers')
    ).toBeInTheDocument();
  });

  it('shows empty state when no circuit breakers match filters', async () => {
    const user = userEvent.setup();

    mockUseCircuitBreakerStatus.mockReturnValue({
      data: mockCircuitBreakerData,
      isLoading: false,
      error: null,
      refetch: mockRefetch,
    } as any);

    render(<CircuitBreakerList />);

    const searchInput = screen.getByPlaceholderText(
      'Search circuit breakers...'
    );
    await user.type(searchInput, 'nonexistent');

    expect(
      screen.getByText('No circuit breakers match your filters')
    ).toBeInTheDocument();
  });

  it('shows empty state when no circuit breakers exist', async () => {
    const emptyData: CircuitBreakerStatus = {
      circuitBreakers: [],
      summary: { total: 0, closed: 0, open: 0, halfOpen: 0 },
    };

    mockUseCircuitBreakerStatus.mockReturnValue({
      data: emptyData,
      isLoading: false,
      error: null,
      refetch: mockRefetch,
    } as any);

    render(<CircuitBreakerList />);

    expect(screen.getByText('No circuit breakers found')).toBeInTheDocument();
  });

  it('applies custom className', () => {
    mockUseCircuitBreakerStatus.mockReturnValue({
      data: mockCircuitBreakerData,
      isLoading: false,
      error: null,
      refetch: mockRefetch,
    } as any);

    const { container } = render(
      <CircuitBreakerList className="custom-class" />
    );

    expect(container.firstChild).toHaveClass('custom-class');
  });
});
