# WorkHub Staging Environment - Security Enhanced Configuration
# Phase 1 Security Hardening Complete: Docker Security, Headers, Input Validation, Rate Limiting

services:
  # Backend API service - Staging Configuration with Phase 1 Security Hardening
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: runtime # Use security-hardened runtime stage
    image: workhub-backend:staging-security-hardened
    ports:
      - '3001:3001'
    env_file:
      - ./backend/.env
    environment:
      - NODE_ENV=staging
      - PORT=3001
      - USE_SUPABASE=true
      - LOG_LEVEL=info
      # Circuit Breaker and Health Check Environment Variables
      - HEALTH_CHECK_TIMEOUT=5000
      - HEALTH_CHECK_RETRY_ATTEMPTS=3
      - HEALTH_CHECK_RETRY_DELAY=1000
      - CIRCUIT_BREAKER_FAILURE_THRESHOLD=5
      - CIRCUIT_BREAKER_RESET_TIMEOUT=60000
      - SUPABASE_HEALTH_TIMEOUT=5000
      - SUPABASE_RETRY_ATTEMPTS=3
      - SUPABASE_CIRCUIT_BREAKER_THRESHOLD=3
    depends_on:
      redis:
        condition: service_healthy
    restart: unless-stopped
    # Enhanced health check with security verification
    healthcheck:
      test:
        [
          'CMD',
          'wget',
          '--quiet',
          '--tries=1',
          '--timeout=5',
          '--spider',
          'http://localhost:3001/api/health',
        ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    # Security: Resource limits to prevent DoS
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 512M
        reservations:
          cpus: '0.5'
          memory: 256M
    # Security: Read-only root filesystem (where possible)
    read_only: false # Set to true if application supports it
    # Security: Drop unnecessary capabilities
    cap_drop:
      - ALL
    cap_add:
      - NET_BIND_SERVICE # Only if binding to privileged ports
    # Security: No new privileges
    security_opt:
      - no-new-privileges:true
    networks:
      - workhub-staging

  # Frontend web application - Staging Configuration with Phase 1 Security Hardening
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: runtime # Use security-hardened runtime stage
      args:
        NEXT_PUBLIC_SUPABASE_URL: ${SUPABASE_URL}
        NEXT_PUBLIC_SUPABASE_ANON_KEY: ${SUPABASE_ANON_KEY}
        NEXT_PUBLIC_API_BASE_URL: https://localhost/api
        NEXT_PUBLIC_API_URL: https://localhost
        NEXT_PUBLIC_DOCKER_ENV: true
        BACKEND_URL: http://backend:3001 # Internal Docker communication
    image: workhub-frontend:staging-security-hardened
    ports:
      - '3000:3000'
    environment:
      - NODE_ENV=production
      # Docker/Staging environment identifier
      - NEXT_PUBLIC_DOCKER_ENV=true
      # Frontend Supabase Configuration
      - NEXT_PUBLIC_SUPABASE_URL=${SUPABASE_URL}
      - NEXT_PUBLIC_SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
      # API Configuration - Frontend connects to Nginx proxy over HTTPS
      - NEXT_PUBLIC_API_BASE_URL=https://localhost/api
      - NEXT_PUBLIC_API_URL=https://localhost
      # Backend URL for Docker container-to-container communication (internal)
      - BACKEND_URL=http://backend:3001
    depends_on:
      backend:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    # Enhanced health check
    healthcheck:
      test: ['CMD', 'wget', '-q', '--spider', 'http://localhost:3000']
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    # Security: Resource limits to prevent DoS
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 256M
        reservations:
          cpus: '0.25'
          memory: 128M
    # Security: Read-only root filesystem (where possible)
    read_only: false # Set to true if application supports it
    # Security: Drop unnecessary capabilities
    cap_drop:
      - ALL
    cap_add:
      - NET_BIND_SERVICE # Only if binding to privileged ports
    # Security: No new privileges
    security_opt:
      - no-new-privileges:true
    networks:
      - workhub-staging
  # Nginx reverse proxy for staging
  nginx:
    image: nginx:alpine
    ports:
      - '80:80'
      - '443:443'
    volumes:
      - ./nginx/staging.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - backend
      - frontend
      - redis
    restart: unless-stopped
    networks:
      - workhub-staging

  # Redis service for distributed caching and rate limiting - Staging Environment
  # CRITICAL ISSUES RESOLUTION: Fixed Redis container startup issues
  redis:
    image: redis:7-alpine
    container_name: workhub-redis-staging
    hostname: redis
    ports:
      - '6379:6379'
    # FIXED: Simplified Redis configuration for reliable startup
    command:
      [
        'redis-server',
        '--appendonly',
        'yes',
        '--maxmemory',
        '256mb',
        '--maxmemory-policy',
        'allkeys-lru',
        '--save',
        '900',
        '1',
        '--save',
        '300',
        '10',
        '--save',
        '60',
        '10000',
        '--tcp-keepalive',
        '60',
        '--timeout',
        '300',
      ]
    volumes:
      - redis_staging_data:/data
    # FIXED: Simplified health check for reliable container startup
    healthcheck:
      test: ['CMD', 'redis-cli', 'ping']
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 60s
    restart: unless-stopped
    # FIXED: Resource limits to prevent resource exhaustion
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M
    networks:
      - workhub-staging
    # FIXED: Environment variables for Redis configuration
    environment:
      - REDIS_REPLICATION_MODE=master

networks:
  workhub-staging:
    driver: bridge
    name: workhub-staging-network

volumes:
  staging_logs:
    name: workhub-staging-logs
  redis_staging_data:
    name: workhub-redis-staging-data
