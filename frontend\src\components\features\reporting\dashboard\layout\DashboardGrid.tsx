// frontend/src/components/features/reporting/dashboard/layout/DashboardGrid.tsx
import React from 'react';
import { cn } from '@/lib/utils';

interface DashboardGridProps {
  children: React.ReactNode;
  columns?: number; // Number of columns for the grid, defaults to responsive
  gap?: string; // Tailwind CSS gap utility class, e.g., 'gap-6'
  className?: string; // Additional CSS classes
  layout?: 'grid' | 'masonry' | 'flex'; // Layout type
  minItemWidth?: string; // Minimum width for grid items
  autoFit?: boolean; // Auto-fit columns based on content
  spacing?: 'tight' | 'normal' | 'relaxed'; // Standardized spacing system
}

/**
 * @component DashboardGrid
 * @description Professional 12-column grid system for dashboard widgets
 *
 * Implements strict grid alignment following UX recommendations:
 * - Consistent 12-column grid system
 * - Professional spacing scale (4px/8px increments)
 * - Eliminates awkward gaps and misalignment
 * - Responsive design with proper breakpoints
 *
 * SOLID Principles Applied:
 * - SRP: Single responsibility of managing grid layout concerns
 * - OCP: Open for extension via layout types and responsive options
 * - LSP: Can be substituted with other grid implementations
 */
export const DashboardGrid: React.FC<DashboardGridProps> = ({
  children,
  columns,
  gap,
  className = '',
  layout = 'grid',
  minItemWidth = '320px',
  autoFit = false,
  spacing = 'normal',
}) => {
  // Professional spacing system based on 8px increments
  const getSpacing = () => {
    if (gap) return gap; // Allow override

    switch (spacing) {
      case 'tight':
        return 'gap-4'; // 16px
      case 'normal':
        return 'gap-6'; // 24px
      case 'relaxed':
        return 'gap-8'; // 32px
      default:
        return 'gap-6';
    }
  };

  // Generate responsive grid classes with proper 12-column system
  const getGridClasses = () => {
    if (layout === 'flex') {
      return `flex flex-wrap ${getSpacing()}`;
    }

    if (layout === 'masonry') {
      return `columns-1 md:columns-2 lg:columns-3 xl:columns-4 ${getSpacing()}`;
    }

    // Professional 12-column grid layout
    if (autoFit) {
      return `grid ${getSpacing()}`;
    }

    if (columns) {
      // Strict grid mappings for professional layouts
      const colsMap: Record<number, string> = {
        1: 'grid-cols-1',
        2: 'grid-cols-1 lg:grid-cols-2',
        3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
        4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
        6: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6',
        12: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-12',
      };
      return `grid ${colsMap[columns] || colsMap[3]} ${getSpacing()}`;
    }

    // Default: Professional 12-column responsive grid
    return `grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 ${getSpacing()}`;
  };

  // Generate inline styles for auto-fit with professional constraints
  const getGridStyles = () => {
    if (layout === 'grid' && autoFit) {
      return {
        gridTemplateColumns: `repeat(auto-fit, minmax(${minItemWidth}, 1fr))`,
      };
    }
    return {};
  };

  return (
    <div
      className={cn(
        getGridClasses(),
        // Ensure consistent alignment with layout
        'w-full max-w-full',
        // Professional container constraints
        'container-fluid',
        className
      )}
      style={getGridStyles()}
      role="grid"
      aria-label="Dashboard widgets grid"
    >
      {children}
    </div>
  );
};

// Pre-configured grid variants for common use cases
export const CompactDashboardGrid: React.FC<
  Omit<DashboardGridProps, 'spacing'>
> = props => <DashboardGrid {...props} spacing="tight" />;

export const RelaxedDashboardGrid: React.FC<
  Omit<DashboardGridProps, 'spacing'>
> = props => <DashboardGrid {...props} spacing="relaxed" />;

export default DashboardGrid;
