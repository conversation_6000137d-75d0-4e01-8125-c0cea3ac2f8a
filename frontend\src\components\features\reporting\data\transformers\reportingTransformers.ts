// PHASE 1 ENHANCEMENT: Import new entity analytics types
import {
  DelegationAnalytics,
  DelegationReportData,
  TaskMetrics,
  TrendData,
  LocationMetrics,
  DelegationStatusPrisma,
  TaskPriorityPrisma,
  TaskStatusPrisma,
  StatusDistributionData,
  ServiceHistoryData,
  ServiceCostSummary,
  TaskAnalyticsData,
  RawDelegationAnalytics,
  RawTaskMetrics,
  RawTrendData,
  RawLocationMetrics,
  RawServiceHistoryData,
  RawServiceCostSummary,
  RawTaskAnalyticsData,
  // PHASE 1: New entity analytics types
  TaskAnalytics,
  VehicleAnalytics,
  EmployeeAnalytics,
  CrossEntityAnalytics,
  TaskStatusDistributionData,
  TaskPriorityDistributionData,
} from '../types/reporting';
import {
  ServiceTypePrisma,
  ServiceStatusPrisma,
} from '../types/vehicleService';

/**
 * Helper function to get color for delegation status.
 * FIXED: Use correct Prisma enum values instead of incorrect UPPERCASE versions.
 * @param status - Delegation status
 * @returns Color string for the status
 */
const getStatusColor = (status: string): string => {
  const colorMap: Record<string, string> = {
    Planned: '#3b82f6', // Blue
    Confirmed: '#10b981', // Green
    In_Progress: '#f59e0b', // Amber
    Completed: '#22c55e', // Green
    Cancelled: '#ef4444', // Red
    No_details: '#6b7280', // Gray
  };
  return colorMap[status] || '#6b7280';
};

/**
 * PHASE 1 ENHANCEMENT: Helper function to get color for task status
 * @param status - Task status
 * @returns Color string for the status
 */
const getTaskStatusColor = (status: string): string => {
  const colorMap: Record<string, string> = {
    Pending: '#6b7280', // Gray
    Assigned: '#3b82f6', // Blue
    In_Progress: '#f59e0b', // Amber
    Completed: '#22c55e', // Green
    Cancelled: '#ef4444', // Red
  };
  return colorMap[status] || '#6b7280';
};

/**
 * PHASE 1 ENHANCEMENT: Helper function to get color for task priority
 * @param priority - Task priority
 * @returns Color string for the priority
 */
const getTaskPriorityColor = (priority: string): string => {
  const colorMap: Record<string, string> = {
    Low: '#10b981', // Green
    Medium: '#f59e0b', // Amber
    High: '#ef4444', // Red
  };
  return colorMap[priority] || '#6b7280';
};

/**
 * Transforms raw API response to DelegationAnalytics format
 * @param rawData - Raw API response data
 * @returns Transformed delegation analytics data
 */
export const transformDelegationAnalytics = (
  rawData: RawDelegationAnalytics
): DelegationAnalytics => {
  const result: DelegationAnalytics = {
    totalCount: rawData.totalCount || 0,
    statusDistribution:
      rawData.statusDistribution?.map((item: any) => ({
        status: item.status,
        count: item.count || 0,
        percentage: item.percentage || 0,
        color: getStatusColor(item.status), // Use the helper function
      })) || [],
    trendData:
      rawData.trendData?.map((item: any) => ({
        date: item.date,
        created: item.created || 0,
        completed: item.completed || 0,
        inProgress: item.inProgress || 0,
      })) || [],
    locationMetrics: rawData.locationMetrics || [],
    summary: {
      totalDelegations: rawData.summary?.totalDelegations || 0,
      activeDelegations: rawData.summary?.activeDelegations || 0,
      completedDelegations: rawData.summary?.completedDelegations || 0,
      totalDelegates: rawData.summary?.totalDelegates || 0,
      averageDuration: rawData.summary?.averageDuration || 0,
      completionRate: rawData.summary?.completionRate || 0,
    },
    // FIXED: Initialize delegations array
    delegations: [],
  };

  // ENHANCED: Optional service history data transformation
  if (rawData.serviceHistory) {
    result.serviceHistory = transformServiceHistory(rawData.serviceHistory);
  }

  if (
    rawData.serviceCosts &&
    typeof rawData.serviceCosts === 'object' &&
    !Array.isArray(rawData.serviceCosts)
  ) {
    result.serviceCosts = transformServiceCostSummary(rawData.serviceCosts);
  }

  if (
    rawData.taskData &&
    typeof rawData.taskData === 'object' &&
    !Array.isArray(rawData.taskData)
  ) {
    result.taskData = transformTaskAnalyticsData(rawData.taskData);
  }

  // FIXED: Add real delegations data from API response
  result.delegations =
    rawData.delegations?.map((item: any) => ({
      id: (item.id || 0).toString(),
      delegationId: item.delegationId || item.id?.toString() || '',
      customerName:
        item.customerName || item.customer?.name || 'Unknown Customer',
      vehicleModel:
        item.vehicleModel || item.vehicle?.model || 'Unknown Vehicle',
      licensePlate:
        item.licensePlate || item.vehicle?.licensePlate || 'Unknown',
      status: item.status,
      assignedEmployee:
        item.driverEmployee?.name || item.staffEmployee?.name || 'Unassigned',
      location: item.location || '',
      createdAt: item.createdAt || '',
      completedAt: item.completedAt,
    })) || [];

  return result;
};

/**
 * Transforms raw API response to TaskMetrics format
 * @param rawData - Raw API response data
 * @returns Transformed task metrics data
 */
export const transformTaskMetrics = (rawData: RawTaskMetrics): TaskMetrics => {
  return {
    totalTasks: rawData.totalTasks || 0,
    completedTasks: rawData.completedTasks || 0,
    pendingTasks: rawData.pendingTasks || 0,
    inProgressTasks: rawData.inProgressTasks || 0,
    overdueTasks: rawData.overdueTasks || 0,
    averageCompletionTime: rawData.averageCompletionTime || 0,
    tasksByPriority:
      rawData.tasksByPriority?.map((item: any) => ({
        priority: item.priority,
        count: item.count || 0,
      })) || [],
    tasksByStatus:
      rawData.tasksByStatus?.map((item: any) => ({
        status: item.status,
        count: item.count || 0,
      })) || [],
  };
};

/**
 * Transforms raw API response to TrendData array format
 * @param rawData - Raw API response data
 * @returns Transformed trend data array
 */
export const transformTrendData = (rawData: RawTrendData[]): TrendData[] => {
  return (
    rawData?.map((item: any) => ({
      date: item.date,
      created: item.created || 0,
      completed: item.completed || 0,
      inProgress: item.inProgress || 0,
    })) || []
  );
};

/**
 * Transforms raw API response to LocationMetrics array format
 * @param rawData - Raw API response data
 * @returns Transformed location metrics array
 */
export const transformLocationMetrics = (
  rawData: RawLocationMetrics[]
): LocationMetrics[] => {
  return (
    rawData?.map((item: any) => ({
      location: item.location || '',
      delegationCount:
        item.delegationCount || item.delegationsCount || item.count || 0,
      averageDuration: item.averageDuration || 0,
      completionRate: item.completionRate || 0,
    })) || []
  );
};

/**
 * ENHANCED: Transforms raw API response to ServiceHistoryData array format
 * @param rawData - Raw API response data
 * @returns Transformed service history data array
 */
export const transformServiceHistory = (rawData: any): ServiceHistoryData => {
  return {
    id: rawData.id || '',
    vehicleId: rawData.vehicleId || 0,
    vehicleName: rawData.vehicleName || '',
    serviceType: rawData.serviceType as ServiceTypePrisma,
    status: rawData.status as ServiceStatusPrisma,
    scheduledDate: rawData.scheduledDate || '',
    completedDate: rawData.completedDate,
    cost: rawData.cost || 0,
    description: rawData.description || '',
    relatedDelegationId: rawData.relatedDelegationId,
    relatedTaskId: rawData.relatedTaskId,
  };
};

/**
 * ENHANCED: Transforms raw API response to ServiceCostSummary format
 * @param rawData - Raw API response data
 * @returns Transformed service cost summary data
 */
export const transformServiceCostSummary = (
  rawData: RawServiceCostSummary
): ServiceCostSummary => {
  return {
    totalCost: rawData.totalCost || 0,
    averageCostPerService: rawData.averageCostPerService || 0,
    costByType:
      rawData.costByType?.map((item: any) => ({
        type: item.type as ServiceTypePrisma,
        cost: item.cost || 0,
        count: item.count || 0,
      })) || [],
    monthlyTrend:
      rawData.monthlyTrend?.map((item: any) => ({
        month: item.month || '',
        cost: item.cost || 0,
      })) || [],
  };
};

/**
 * ENHANCED: Transforms raw API response to TaskAnalyticsData format
 * @param rawData - Raw API response data
 * @returns Transformed task analytics data
 */
export const transformTaskAnalyticsData = (
  rawData: RawTaskAnalyticsData
): TaskAnalyticsData => {
  return {
    totalTasks: rawData.totalTasks || 0,
    completedTasks: rawData.completedTasks || 0,
    pendingTasks: rawData.pendingTasks || 0,
    overdueTasks: rawData.overdueTasks || 0,
    averageCompletionTime: rawData.averageCompletionTime || 0,
    tasksByPriority:
      rawData.tasksByPriority?.map((item: any) => ({
        priority: item.priority,
        count: item.count || 0,
      })) || [],
  };
};

/**
 * PHASE 1 ENHANCEMENT: Transforms raw API response to TaskAnalytics format
 * @param rawData - Raw API response data
 * @returns Transformed task analytics data
 */
export const transformTaskAnalytics = (rawData: any): TaskAnalytics => {
  const totalCount = rawData.totalCount || 0;

  return {
    totalCount,
    statusDistribution:
      rawData.statusDistribution?.map((item: any) => ({
        status: item.status as TaskStatusPrisma,
        count: item.count || 0,
        percentage: totalCount > 0 ? ((item.count || 0) / totalCount) * 100 : 0,
        color: getTaskStatusColor(item.status),
      })) || [],
    priorityDistribution:
      rawData.priorityDistribution?.map((item: any) => ({
        priority: item.priority as TaskPriorityPrisma,
        count: item.count || 0,
        percentage: totalCount > 0 ? ((item.count || 0) / totalCount) * 100 : 0,
        color: getTaskPriorityColor(item.priority),
      })) || [],
    completionRate: rawData.completionRate || 0,
    overdueCount: rawData.overdueCount || 0,
    averageCompletionTime: rawData.averageCompletionTime || 0,
    assignmentMetrics:
      rawData.assignmentMetrics?.map((item: any) => ({
        employeeId: item.employeeId || 0,
        employeeName: item.employeeName || '',
        assignedTasks: item.assignedTasks || 0,
        completedTasks: item.completedTasks || 0,
        completionRate: item.completionRate || 0,
        averageCompletionTime: item.averageCompletionTime || 0,
      })) || [],
    trendData:
      rawData.trendData?.map((item: any) => ({
        date: item.date || '',
        created: item.created || 0,
        completed: item.completed || 0,
        inProgress: item.inProgress || 0,
        overdue: item.overdue || 0,
      })) || [],
  };
};

/**
 * PHASE 1 ENHANCEMENT: Transforms raw API response to VehicleAnalytics format
 * @param rawData - Raw API response data
 * @returns Transformed vehicle analytics data
 */
export const transformVehicleAnalytics = (rawData: any): VehicleAnalytics => {
  return {
    totalCount: rawData.totalCount || 0,
    serviceHistory:
      rawData.serviceHistory?.map((item: any) =>
        transformServiceHistory(item)
      ) || [],
    costAnalysis: rawData.costAnalysis
      ? transformServiceCostSummary(rawData.costAnalysis)
      : {
          totalCost: 0,
          averageCostPerService: 0,
          costByType: [],
          monthlyTrend: [],
        },
    utilizationMetrics:
      rawData.utilizationMetrics?.map((item: any) => ({
        vehicleId: item.vehicleId || 0,
        vehicleName: item.vehicleName || '',
        utilizationRate: item.utilizationRate || 0,
        totalDelegations: item.totalDelegations || 0,
        activeDelegations: item.activeDelegations || 0,
        maintenanceHours: item.maintenanceHours || 0,
      })) || [],
    maintenanceSchedule:
      rawData.maintenanceSchedule?.map((item: any) => ({
        vehicleId: item.vehicleId || 0,
        vehicleName: item.vehicleName || '',
        nextMaintenanceDate: item.nextMaintenanceDate || '',
        maintenanceType: item.maintenanceType as ServiceTypePrisma,
        priority: item.priority || 'Medium',
        estimatedCost: item.estimatedCost || 0,
      })) || [],
    performanceMetrics:
      rawData.performanceMetrics?.map((item: any) => ({
        vehicleId: item.vehicleId || 0,
        vehicleName: item.vehicleName || '',
        fuelEfficiency: item.fuelEfficiency || 0,
        maintenanceCost: item.maintenanceCost || 0,
        downtime: item.downtime || 0,
        reliabilityScore: item.reliabilityScore || 0,
      })) || [],
  };
};

/**
 * PHASE 1 ENHANCEMENT: Transforms raw API response to EmployeeAnalytics format
 * @param rawData - Raw API response data
 * @returns Transformed employee analytics data
 */
export const transformEmployeeAnalytics = (rawData: any): EmployeeAnalytics => {
  return {
    totalCount: rawData.totalCount || 0,
    performanceMetrics:
      rawData.performanceMetrics?.map((item: any) => ({
        employeeId: item.employeeId || 0,
        employeeName: item.employeeName || '',
        completedDelegations: item.completedDelegations || 0,
        completedTasks: item.completedTasks || 0,
        averageRating: item.averageRating || 0,
        onTimePerformance: item.onTimePerformance || 0,
        workloadScore: item.workloadScore || 0,
      })) || [],
    delegationHistory:
      rawData.delegationHistory?.map((item: any) => ({
        employeeId: item.employeeId || 0,
        employeeName: item.employeeName || '',
        totalDelegations: item.totalDelegations || 0,
        completedDelegations: item.completedDelegations || 0,
        averageDuration: item.averageDuration || 0,
        successRate: item.successRate || 0,
      })) || [],
    taskAssignments:
      rawData.taskAssignments?.map((item: any) => ({
        employeeId: item.employeeId || 0,
        employeeName: item.employeeName || '',
        assignedTasks: item.assignedTasks || 0,
        completedTasks: item.completedTasks || 0,
        pendingTasks: item.pendingTasks || 0,
        overdueTasksCount: item.overdueTasksCount || 0,
      })) || [],
    availabilityMetrics:
      rawData.availabilityMetrics?.map((item: any) => ({
        employeeId: item.employeeId || 0,
        employeeName: item.employeeName || '',
        availableHours: item.availableHours || 0,
        scheduledHours: item.scheduledHours || 0,
        utilizationRate: item.utilizationRate || 0,
        overtimeHours: item.overtimeHours || 0,
      })) || [],
    workloadDistribution:
      rawData.workloadDistribution?.map((item: any) => ({
        employeeId: item.employeeId || 0,
        employeeName: item.employeeName || '',
        currentWorkload: item.currentWorkload || 0,
        capacity: item.capacity || 0,
        workloadPercentage: item.workloadPercentage || 0,
        status: item.status || 'Optimal',
      })) || [],
  };
};

/**
 * PHASE 1 ENHANCEMENT: Transforms raw API response to CrossEntityAnalytics format
 * @param rawData - Raw API response data
 * @returns Transformed cross-entity analytics data
 */
export const transformCrossEntityAnalytics = (
  rawData: any
): CrossEntityAnalytics => {
  const result: CrossEntityAnalytics = {
    correlations: {
      employeeVehicle: rawData.correlations?.employeeVehicle || [],
      taskDelegation: rawData.correlations?.taskDelegation || [],
      performanceWorkload: rawData.correlations?.performanceWorkload || [],
      overall: rawData.correlations?.overall || [],
    },
    metrics: {
      employeeVehicle: rawData.metrics?.employeeVehicle || 0,
      taskDelegation: rawData.metrics?.taskDelegation || 0,
      performanceWorkload: rawData.metrics?.performanceWorkload || 0,
      overallEfficiency: rawData.metrics?.overallEfficiency || 0,
    },
  };

  // Add optional properties only if they exist
  if (rawData.network) {
    result.network = {
      nodes: rawData.network.nodes || [],
      edges: rawData.network.edges || [],
    };
  }

  if (rawData.insights) {
    result.insights = rawData.insights || [];
  }

  return result;
};
