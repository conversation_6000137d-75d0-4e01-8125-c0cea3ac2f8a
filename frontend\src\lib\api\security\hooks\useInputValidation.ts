/**
 * @file Input Validation Hook - DRY + Single Responsibility Principle (SRP)
 * @module hooks/useInputValidation
 *
 * This hook provides centralized input validation functionality following DRY principles.
 * It handles form validation, real-time validation, and security sanitization.
 *
 * SECURITY NOTE: This is the single source of input validation for all forms and user inputs.
 */

'use client';

import { useCallback, useState } from 'react';
import { InputValidator } from '../../../security/InputValidator';
import type {
  ValidationResult,
  ValidationRule,
  ValidationSchema,
} from '../../../security/InputValidator';
import {
  undefinedToNull,
  isNotNullOrUndefined,
} from '../../../utils/typeHelpers';

export interface ValidationState {
  isValid: boolean;
  errors: Record<string, string[]>;
  touched: Record<string, boolean>;
  isValidating: boolean;
}

export interface ValidationActions {
  validateField: (
    field: string,
    value: any,
    rules: ValidationRule
  ) => ValidationResult;
  validateForm: (
    data: Record<string, any>,
    schema: ValidationSchema
  ) => ValidationResult;
  setFieldTouched: (field: string, touched?: boolean) => void;
  clearFieldErrors: (field: string) => void;
  clearAllErrors: () => void;
  resetValidation: () => void;
  sanitizeInput: (input: any) => any;
}

export interface UseInputValidationReturn
  extends ValidationState,
    ValidationActions {
  getFieldError: (field: string) => string | null;
  hasFieldError: (field: string) => boolean;
  isFieldTouched: (field: string) => boolean;
}

/**
 * Input Validation Hook - DRY Pattern for All Form Validation
 *
 * Provides centralized input validation functionality for all components.
 * Ensures all forms follow the same validation patterns and security standards.
 */
export function useInputValidation(
  initialSchema?: ValidationSchema
): UseInputValidationReturn {
  const [state, setState] = useState<ValidationState>({
    isValid: true,
    errors: {},
    touched: {},
    isValidating: false,
  });

  /**
   * Validate single field
   * Single responsibility: Field validation only
   */
  const validateField = useCallback(
    (field: string, value: any, rules: ValidationRule): ValidationResult => {
      setState(prev => ({ ...prev, isValidating: true }));

      const result = InputValidator.validateValue(value, rules);

      setState(prev => ({
        ...prev,
        isValidating: false,
        errors: {
          ...prev.errors,
          [field]: result.isValid ? [] : result.errors,
        },
        isValid:
          result.isValid &&
          Object.values({
            ...prev.errors,
            [field]: result.isValid ? [] : result.errors,
          }).every(fieldErrors => fieldErrors.length === 0),
      }));

      return result;
    },
    []
  );

  /**
   * Validate entire form
   * Single responsibility: Form validation only
   */
  const validateForm = useCallback(
    (data: Record<string, any>, schema: ValidationSchema): ValidationResult => {
      setState(prev => ({ ...prev, isValidating: true }));

      const result = InputValidator.validateObject(data, schema);

      // Parse field-specific errors from result
      const fieldErrors: Record<string, string[]> = {};
      result.errors.forEach(error => {
        const [field, ...messageParts] = error.split(': ');
        const message = messageParts.join(': ');

        if (field) {
          if (!fieldErrors[field]) {
            fieldErrors[field] = [];
          }
          fieldErrors[field].push(message);
        }
      });

      setState(prev => ({
        ...prev,
        isValidating: false,
        errors: fieldErrors,
        isValid: result.isValid,
      }));

      return result;
    },
    []
  );

  /**
   * Set field as touched
   * Single responsibility: Touch state management only
   */
  const setFieldTouched = useCallback((field: string, touched = true) => {
    setState(prev => ({
      ...prev,
      touched: {
        ...prev.touched,
        [field]: touched,
      },
    }));
  }, []);

  /**
   * Clear errors for specific field
   * Single responsibility: Field error clearing only
   */
  const clearFieldErrors = useCallback((field: string) => {
    setState(prev => ({
      ...prev,
      errors: {
        ...prev.errors,
        [field]: [],
      },
      isValid: Object.values({
        ...prev.errors,
        [field]: [],
      }).every(fieldErrors => fieldErrors.length === 0),
    }));
  }, []);

  /**
   * Clear all validation errors
   * Single responsibility: All errors clearing only
   */
  const clearAllErrors = useCallback(() => {
    setState(prev => ({
      ...prev,
      errors: {},
      isValid: true,
    }));
  }, []);

  /**
   * Reset validation state
   * Single responsibility: State reset only
   */
  const resetValidation = useCallback(() => {
    setState({
      isValid: true,
      errors: {},
      touched: {},
      isValidating: false,
    });
  }, []);

  /**
   * Sanitize input for security
   * Single responsibility: Input sanitization only
   */
  const sanitizeInput = useCallback((input: any): any => {
    if (typeof input === 'string') {
      return InputValidator.sanitizeForXSS(input);
    }

    if (Array.isArray(input)) {
      return input.map(item => sanitizeInput(item));
    }

    if (input && typeof input === 'object') {
      const sanitized: any = {};
      for (const [key, value] of Object.entries(input)) {
        sanitized[key] = sanitizeInput(value);
      }
      return sanitized;
    }

    return input;
  }, []);

  /**
   * Get error message for specific field
   * Single responsibility: Error retrieval only
   */
  const getFieldError = useCallback(
    (field: string): string | null => {
      const fieldErrors = state.errors[field];
      return fieldErrors && fieldErrors.length > 0
        ? fieldErrors[0] || null
        : null;
    },
    [state.errors]
  );

  /**
   * Check if field has errors
   * Single responsibility: Error existence check only
   */
  const hasFieldError = useCallback(
    (field: string): boolean => {
      const fieldErrors = state.errors[field];
      return fieldErrors ? fieldErrors.length > 0 : false;
    },
    [state.errors]
  );

  /**
   * Check if field has been touched
   * Single responsibility: Touch state check only
   */
  const isFieldTouched = useCallback(
    (field: string): boolean => {
      return state.touched[field] || false;
    },
    [state.touched]
  );

  return {
    // State
    isValid: state.isValid,
    errors: state.errors,
    touched: state.touched,
    isValidating: state.isValidating,

    // Actions
    validateField,
    validateForm,
    setFieldTouched,
    clearFieldErrors,
    clearAllErrors,
    resetValidation,
    sanitizeInput,

    // Utility functions
    getFieldError,
    hasFieldError,
    isFieldTouched,
  };
}

/**
 * Pre-configured validation hook for employee forms
 */
export function useEmployeeValidation() {
  const schema = InputValidator.createEmployeeValidationSchema();
  return useInputValidation(schema);
}

/**
 * Pre-configured validation hook for vehicle forms
 */
export function useVehicleValidation() {
  const schema = InputValidator.createVehicleValidationSchema();
  return useInputValidation(schema);
}

/**
 * Hook for real-time field validation
 */
export function useFieldValidation(rules: ValidationRule) {
  const [fieldState, setFieldState] = useState<{
    value: any;
    error: string | null;
    isValid: boolean;
    isTouched: boolean;
  }>({
    value: '',
    error: null,
    isValid: true,
    isTouched: false,
  });

  const validateAndSet = useCallback(
    (value: any) => {
      const result = InputValidator.validateValue(value, rules);

      setFieldState({
        value: result.sanitizedValue || value,
        error: result.errors.length > 0 ? result.errors[0] || null : null,
        isValid: result.isValid,
        isTouched: true,
      });

      return result;
    },
    [rules]
  );

  const setValue = useCallback((value: any) => {
    setFieldState(prev => ({
      ...prev,
      value,
      isTouched: true,
    }));
  }, []);

  const reset = useCallback(() => {
    setFieldState({
      value: '',
      error: null,
      isValid: true,
      isTouched: false,
    });
  }, []);

  return {
    ...fieldState,
    validateAndSet,
    setValue,
    reset,
  };
}
