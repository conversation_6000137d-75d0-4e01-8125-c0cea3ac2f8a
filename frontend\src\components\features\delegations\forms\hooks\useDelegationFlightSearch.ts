/**
 * useDelegationFlightSearch Hook - Flight Search Management
 * 
 * Manages flight search functionality, modal states, and flight selection for delegation forms.
 * Provides centralized flight operations following SOLID principles.
 * 
 * @module useDelegationFlightSearch
 */

import { useState, useCallback } from 'react';
import { flightSearchService, type FlightData } from '../services/FlightSearchService';

// ============================================================================
// INTERFACES
// ============================================================================

export interface UseDelegationFlightSearchReturn {
  // Modal state
  isArrivalModalOpen: boolean;
  isDepartureModalOpen: boolean;
  openArrivalModal: () => void;
  openDepartureModal: () => void;
  closeModals: () => void;
  closeArrivalModal: () => void;
  closeDepartureModal: () => void;
  
  // Flight search
  searchFlights: (callsign: string, date: string) => Promise<FlightData[]>;
  isSearching: boolean;
  searchError: Error | null;
  clearSearchError: () => void;
  
  // Flight selection
  handleFlightSelection: (flight: FlightData, type: 'arrival' | 'departure') => void;
  selectedArrivalFlight: FlightData | null;
  selectedDepartureFlight: FlightData | null;
  clearFlightSelection: (type?: 'arrival' | 'departure') => void;
  
  // Search history
  searchHistory: FlightSearchHistoryItem[];
  clearSearchHistory: () => void;
}

export interface FlightSearchHistoryItem {
  callsign: string;
  date: string;
  timestamp: number;
  resultCount: number;
}

export interface DelegationFlightSearchOptions {
  // Search options
  enableSearchHistory?: boolean;
  maxHistoryItems?: number;
  
  // Modal options
  closeOnSelection?: boolean;
  
  // Callbacks
  onFlightSelected?: (flight: FlightData, type: 'arrival' | 'departure') => void;
  onSearchError?: (error: Error) => void;
}

// ============================================================================
// HOOK IMPLEMENTATION
// ============================================================================

/**
 * Custom hook for managing delegation flight search operations
 * 
 * Provides flight search, modal management, and selection functionality.
 * This is a placeholder implementation that will be fully developed in Phase 2.
 * 
 * @param options - Configuration options for flight search
 * @returns Flight search management interface
 */
export const useDelegationFlightSearch = (
  options: DelegationFlightSearchOptions = {}
): UseDelegationFlightSearchReturn => {
  
  const {
    enableSearchHistory = true,
    maxHistoryItems = 10,
    closeOnSelection = true,
    onFlightSelected,
    onSearchError
  } = options;
  
  // ============================================================================
  // STATE MANAGEMENT
  // ============================================================================
  
  // Modal states
  const [isArrivalModalOpen, setIsArrivalModalOpen] = useState(false);
  const [isDepartureModalOpen, setIsDepartureModalOpen] = useState(false);
  
  // Search states
  const [isSearching, setIsSearching] = useState(false);
  const [searchError, setSearchError] = useState<Error | null>(null);
  
  // Flight selection states
  const [selectedArrivalFlight, setSelectedArrivalFlight] = useState<FlightData | null>(null);
  const [selectedDepartureFlight, setSelectedDepartureFlight] = useState<FlightData | null>(null);
  
  // Search history
  const [searchHistory, setSearchHistory] = useState<FlightSearchHistoryItem[]>([]);
  
  // ============================================================================
  // MODAL MANAGEMENT
  // ============================================================================
  
  const openArrivalModal = useCallback(() => {
    setIsArrivalModalOpen(true);
  }, []);
  
  const openDepartureModal = useCallback(() => {
    setIsDepartureModalOpen(true);
  }, []);
  
  const closeArrivalModal = useCallback(() => {
    setIsArrivalModalOpen(false);
  }, []);
  
  const closeDepartureModal = useCallback(() => {
    setIsDepartureModalOpen(false);
  }, []);
  
  const closeModals = useCallback(() => {
    setIsArrivalModalOpen(false);
    setIsDepartureModalOpen(false);
  }, []);
  
  // ============================================================================
  // FLIGHT SEARCH
  // ============================================================================
  
  const searchFlights = useCallback(async (callsign: string, date: string): Promise<FlightData[]> => {
    try {
      setIsSearching(true);
      setSearchError(null);
      
      // TODO: Implement actual flight search using service
      const results = await flightSearchService.searchFlights(callsign, date);
      
      // Add to search history if enabled
      if (enableSearchHistory) {
        const historyItem: FlightSearchHistoryItem = {
          callsign,
          date,
          timestamp: Date.now(),
          resultCount: results.length
        };
        
        setSearchHistory(prev => {
          const newHistory = [historyItem, ...prev.slice(0, maxHistoryItems - 1)];
          return newHistory;
        });
      }
      
      return results;
    } catch (error) {
      const searchError = error instanceof Error ? error : new Error('Flight search failed');
      setSearchError(searchError);
      
      if (onSearchError) {
        onSearchError(searchError);
      }
      
      return [];
    } finally {
      setIsSearching(false);
    }
  }, [enableSearchHistory, maxHistoryItems, onSearchError]);
  
  // ============================================================================
  // FLIGHT SELECTION
  // ============================================================================
  
  const handleFlightSelection = useCallback((flight: FlightData, type: 'arrival' | 'departure') => {
    // Update selected flight state
    if (type === 'arrival') {
      setSelectedArrivalFlight(flight);
    } else {
      setSelectedDepartureFlight(flight);
    }
    
    // Close modal if configured to do so
    if (closeOnSelection) {
      if (type === 'arrival') {
        closeArrivalModal();
      } else {
        closeDepartureModal();
      }
    }
    
    // Call callback if provided
    if (onFlightSelected) {
      onFlightSelected(flight, type);
    }
  }, [closeOnSelection, closeArrivalModal, closeDepartureModal, onFlightSelected]);
  
  const clearFlightSelection = useCallback((type?: 'arrival' | 'departure') => {
    if (!type || type === 'arrival') {
      setSelectedArrivalFlight(null);
    }
    if (!type || type === 'departure') {
      setSelectedDepartureFlight(null);
    }
  }, []);
  
  // ============================================================================
  // UTILITY FUNCTIONS
  // ============================================================================
  
  const clearSearchError = useCallback(() => {
    setSearchError(null);
  }, []);
  
  const clearSearchHistory = useCallback(() => {
    setSearchHistory([]);
  }, []);
  
  // ============================================================================
  // RETURN INTERFACE
  // ============================================================================
  
  return {
    // Modal state
    isArrivalModalOpen,
    isDepartureModalOpen,
    openArrivalModal,
    openDepartureModal,
    closeModals,
    closeArrivalModal,
    closeDepartureModal,
    
    // Flight search
    searchFlights,
    isSearching,
    searchError,
    clearSearchError,
    
    // Flight selection
    handleFlightSelection,
    selectedArrivalFlight,
    selectedDepartureFlight,
    clearFlightSelection,
    
    // Search history
    searchHistory,
    clearSearchHistory,
  };
};

// ============================================================================
// EXPORTS
// ============================================================================

export default useDelegationFlightSearch;
