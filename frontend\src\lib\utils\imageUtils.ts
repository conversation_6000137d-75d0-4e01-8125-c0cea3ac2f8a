/**
 * Image utility functions for handling image URLs and fallbacks
 */

/**
 * Validates if an image URL is valid and non-empty
 * @param imageUrl - The image URL to validate
 * @returns true if the URL is valid and non-empty, false otherwise
 */
export function isValidImageUrl(imageUrl: string | null | undefined): boolean {
  return Boolean(imageUrl && typeof imageUrl === 'string' && imageUrl.trim() !== '');
}

/**
 * Gets a safe image URL with fallback
 * @param imageUrl - The primary image URL
 * @param fallbackUrl - The fallback URL to use if primary is invalid
 * @returns A valid image URL (either primary or fallback)
 */
export function getSafeImageUrl(
  imageUrl: string | null | undefined,
  fallbackUrl: string
): string {
  return isValidImageUrl(imageUrl) ? imageUrl! : fallbackUrl;
}

/**
 * Generates a placeholder image URL using picsum.photos
 * @param seed - Unique seed for consistent placeholder generation
 * @param width - Image width (default: 400)
 * @param height - Image height (default: 250)
 * @returns A picsum.photos URL
 */
export function generatePlaceholderImageUrl(
  seed: string | number,
  width: number = 400,
  height: number = 250
): string {
  return `https://picsum.photos/seed/${seed}/${width}/${height}`;
}

/**
 * Gets a safe delegation image URL with consistent fallback
 * @param imageUrl - The delegation image URL
 * @param delegationId - The delegation ID for generating consistent placeholder
 * @param size - Size preset for the image ('card' | 'detail' | 'report')
 * @returns A valid image URL
 */
export function getSafeDelegationImageUrl(
  imageUrl: string | null | undefined,
  delegationId: string,
  size: 'card' | 'detail' | 'report' = 'card'
): string {
  const dimensions = {
    card: { width: 400, height: 250 },
    detail: { width: 600, height: 375 },
    report: { width: 600, height: 375 },
  };

  const { width, height } = dimensions[size];
  const fallbackUrl = generatePlaceholderImageUrl(delegationId, width, height);
  
  return getSafeImageUrl(imageUrl, fallbackUrl);
}

/**
 * Gets a safe vehicle image URL with consistent fallback
 * @param imageUrl - The vehicle image URL
 * @param vehicleId - The vehicle ID for generating consistent placeholder
 * @returns A valid image URL
 */
export function getSafeVehicleImageUrl(
  imageUrl: string | null | undefined,
  vehicleId: string | number
): string {
  const fallbackUrl = generatePlaceholderImageUrl(vehicleId, 600, 375);
  return getSafeImageUrl(imageUrl, fallbackUrl);
}
