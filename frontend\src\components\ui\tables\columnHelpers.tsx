/**
 * Column Helper Utilities for DataTable
 *
 * Provides reusable column definitions and helper functions for common
 * table column patterns, reducing duplication across different tables.
 */

import type { ColumnDef } from '@tanstack/react-table';
import { format } from 'date-fns';
import {
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  Edit,
  Eye,
  MoreHorizontal,
  Trash,
} from 'lucide-react';
import Link from 'next/link';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';

/**
 * Creates a sortable header component with proper sorting state indicators
 */
export const createSortableHeader = (title: string) => {
  return ({ column }: { column: any }) => {
    const sortDirection = column.getIsSorted();

    return (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        className="h-auto p-0 font-semibold hover:bg-transparent"
      >
        {title}
        {sortDirection === 'asc' ? (
          <ArrowUp className="ml-2 h-4 w-4" />
        ) : sortDirection === 'desc' ? (
          <ArrowDown className="ml-2 h-4 w-4" />
        ) : (
          <ArrowUpDown className="ml-2 h-4 w-4 opacity-50" />
        )}
      </Button>
    );
  };
};

/**
 * Creates a date column with consistent formatting
 */
export const createDateColumn = <T,>(
  accessorKey: keyof T,
  header: string,
  dateFormat: string = 'MMM dd, yyyy'
): ColumnDef<T> => ({
  accessorKey: accessorKey as string,
  header: createSortableHeader(header),
  cell: ({ getValue }) => {
    const date = getValue() as string | Date;
    if (!date) return '-';

    try {
      const dateObj = typeof date === 'string' ? new Date(date) : date;
      return format(dateObj, dateFormat);
    } catch {
      return '-';
    }
  },
});

/**
 * Creates a status badge column
 */
export const createStatusColumn = <T,>(
  accessorKey: keyof T,
  header: string = 'Status',
  statusConfig?: Record<string, { variant: string; label?: string }>
): ColumnDef<T> => ({
  accessorKey: accessorKey as string,
  header: createSortableHeader(header),
  cell: ({ getValue }) => {
    const status = getValue() as string;
    if (!status) return '-';

    const config = statusConfig?.[status] || { variant: 'secondary' };
    const label = config.label || status;

    return <Badge variant={config.variant as any}>{label}</Badge>;
  },
});

/**
 * Creates an actions column with common CRUD operations
 */
export const createActionsColumn = <
  T extends { id: number | string },
>(options: {
  onView?: (item: T) => void;
  onEdit?: (item: T) => void;
  onDelete?: (item: T) => void;
  viewHref?: (item: T) => string;
  editHref?: (item: T) => string;
  customActions?: Array<{
    label: string;
    icon?: React.ComponentType<{ className?: string }>;
    onClick: (item: T) => void;
    variant?: 'default' | 'destructive';
  }>;
}): ColumnDef<T> => ({
  id: 'actions',
  header: 'Actions',
  cell: ({ row }) => {
    const item = row.original;

    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <span className="sr-only">Open menu</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          {/* View Action */}
          {(options.onView || options.viewHref) && (
            <>
              {options.viewHref ? (
                <DropdownMenuItem asChild>
                  <Link href={options.viewHref(item)}>
                    <Eye className="mr-2 h-4 w-4" />
                    View
                  </Link>
                </DropdownMenuItem>
              ) : (
                <DropdownMenuItem onClick={() => options.onView?.(item)}>
                  <Eye className="mr-2 h-4 w-4" />
                  View
                </DropdownMenuItem>
              )}
            </>
          )}

          {/* Edit Action */}
          {(options.onEdit || options.editHref) && (
            <>
              {options.editHref ? (
                <DropdownMenuItem asChild>
                  <Link href={options.editHref(item)}>
                    <Edit className="mr-2 h-4 w-4" />
                    Edit
                  </Link>
                </DropdownMenuItem>
              ) : (
                <DropdownMenuItem onClick={() => options.onEdit?.(item)}>
                  <Edit className="mr-2 h-4 w-4" />
                  Edit
                </DropdownMenuItem>
              )}
            </>
          )}

          {/* Custom Actions */}
          {options.customActions?.map((action, index) => (
            <DropdownMenuItem
              key={index}
              onClick={() => action.onClick(item)}
              className={
                action.variant === 'destructive' ? 'text-destructive' : ''
              }
            >
              {action.icon && <action.icon className="mr-2 h-4 w-4" />}
              {action.label}
            </DropdownMenuItem>
          ))}

          {/* Delete Action */}
          {options.onDelete && (
            <>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() => options.onDelete?.(item)}
                className="text-destructive"
              >
                <Trash className="mr-2 h-4 w-4" />
                Delete
              </DropdownMenuItem>
            </>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
    );
  },
});

/**
 * Creates a text column with optional truncation
 */
export const createTextColumn = <T,>(
  accessorKey: keyof T,
  header: string,
  options?: {
    maxLength?: number;
    className?: string;
  }
): ColumnDef<T> => ({
  accessorKey: accessorKey as string,
  header: createSortableHeader(header),
  cell: ({ getValue }) => {
    const value = getValue() as string;
    if (!value) return '-';

    const truncated =
      options?.maxLength && value.length > options.maxLength
        ? `${value.substring(0, options.maxLength)}...`
        : value;

    return (
      <span className={options?.className} title={value}>
        {truncated}
      </span>
    );
  },
});

/**
 * Creates a numeric column with optional formatting
 */
export const createNumericColumn = <T,>(
  accessorKey: keyof T,
  header: string,
  options?: {
    format?: 'currency' | 'percentage' | 'decimal';
    decimals?: number;
    prefix?: string;
    suffix?: string;
  }
): ColumnDef<T> => ({
  accessorKey: accessorKey as string,
  header: createSortableHeader(header),
  cell: ({ getValue }) => {
    const rawValue = getValue();
    const value =
      typeof rawValue === 'string'
        ? parseFloat(rawValue)
        : (rawValue as number);

    if (value === null || value === undefined || isNaN(value)) return '-';

    let formatted = value.toString();

    if (options?.format === 'currency') {
      formatted = new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: options.decimals ?? 2,
      }).format(value);
    } else if (options?.format === 'percentage') {
      formatted = `${(value * 100).toFixed(options.decimals ?? 1)}%`;
    } else if (options?.decimals !== undefined) {
      formatted = value.toFixed(options.decimals);
    }

    if (options?.prefix) formatted = options.prefix + formatted;
    if (options?.suffix) formatted = formatted + options.suffix;

    return <span className="font-mono">{formatted}</span>;
  },
});

/**
 * Creates a boolean column with checkmark/cross display
 */
export const createBooleanColumn = <T,>(
  accessorKey: keyof T,
  header: string,
  labels?: { true: string; false: string }
): ColumnDef<T> => ({
  accessorKey: accessorKey as string,
  header: createSortableHeader(header),
  cell: ({ getValue }) => {
    const value = getValue() as boolean;

    if (labels) {
      return (
        <Badge variant={value ? 'default' : 'secondary'}>
          {value ? labels.true : labels.false}
        </Badge>
      );
    }

    return (
      <span className={value ? 'text-green-600' : 'text-gray-400'}>
        {value ? '✓' : '✗'}
      </span>
    );
  },
});

/**
 * Creates a row selection column (checkbox)
 */
export const createSelectionColumn = <T,>(): ColumnDef<T> => ({
  id: 'select',
  header: ({ table }) => (
    <Checkbox
      checked={
        table.getIsAllPageRowsSelected() ||
        (table.getIsSomePageRowsSelected() && 'indeterminate')
      }
      onCheckedChange={value => table.toggleAllPageRowsSelected(!!value)}
      aria-label="Select all"
    />
  ),
  cell: ({ row }) => (
    <Checkbox
      checked={row.getIsSelected()}
      onCheckedChange={value => row.toggleSelected(!!value)}
      aria-label="Select row"
    />
  ),
  enableSorting: false,
  enableHiding: false,
});

/**
 * Creates a complex column with title and subtitle (like DelegationTable eventName)
 */
export const createTitleSubtitleColumn = <T,>(
  titleKey: keyof T,
  subtitleKey: keyof T,
  header: string
): ColumnDef<T> => ({
  accessorKey: titleKey as string,
  header: createSortableHeader(header),
  cell: ({ row }) => {
    const title = row.getValue(titleKey as string) as string;
    const subtitle = (row.getValue(subtitleKey as string) || '') as string;

    return (
      <div className="space-y-1">
        <div className="font-semibold text-foreground">{title || '-'}</div>
        {subtitle && (
          <div className="line-clamp-1 text-xs text-muted-foreground">
            {subtitle}
          </div>
        )}
      </div>
    );
  },
});

/**
 * Creates a column with icon and text (like DelegationTable delegates count)
 */
export const createIconTextColumn = <T,>(
  accessorKey: keyof T,
  header: string,
  icon: React.ComponentType<{ className?: string }>,
  options?: {
    formatter?: (value: any) => string;
    className?: string;
  }
): ColumnDef<T> => ({
  accessorKey: accessorKey as string,
  header: createSortableHeader(header),
  cell: ({ row }) => {
    const value = row.getValue(accessorKey as string);
    const Icon = icon;
    const displayValue = options?.formatter ? options.formatter(value) : value;

    return (
      <div
        className={cn(
          'flex items-center justify-center gap-1 text-sm',
          options?.className
        )}
      >
        <Icon className="size-3 text-muted-foreground" />
        {String(displayValue)}
      </div>
    );
  },
});

/**
 * Enhanced actions column with professional styling (like DelegationTable)
 */
export const createEnhancedActionsColumn = <
  T extends { id: number | string },
>(options: {
  onView?: (item: T) => void;
  onEdit?: (item: T) => void;
  onDelete?: (item: T) => void;
  viewHref?: (item: T) => string;
  editHref?: (item: T) => string;
  customActions?: Array<{
    label: string;
    icon?: React.ComponentType<{ className?: string }>;
    onClick: (item: T) => void;
    variant?: 'default' | 'destructive';
  }>;
  showCopyId?: boolean;
}): ColumnDef<T> => ({
  id: 'actions',
  header: 'Actions',
  cell: ({ row }) => {
    const item = row.original;

    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="size-8 p-0">
            <span className="sr-only">Open menu</span>
            <MoreHorizontal className="size-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuLabel>Actions</DropdownMenuLabel>

          {/* View Action */}
          {(options.onView || options.viewHref) && (
            <>
              {options.viewHref ? (
                <DropdownMenuItem asChild>
                  <Link href={options.viewHref(item)}>
                    <Eye className="mr-2 size-4" />
                    View Details
                  </Link>
                </DropdownMenuItem>
              ) : (
                <DropdownMenuItem onClick={() => options.onView?.(item)}>
                  <Eye className="mr-2 size-4" />
                  View Details
                </DropdownMenuItem>
              )}
            </>
          )}

          {/* Edit Action */}
          {(options.onEdit || options.editHref) && (
            <>
              {options.editHref ? (
                <DropdownMenuItem asChild>
                  <Link href={options.editHref(item)}>
                    <Edit className="mr-2 size-4" />
                    Edit
                  </Link>
                </DropdownMenuItem>
              ) : (
                <DropdownMenuItem onClick={() => options.onEdit?.(item)}>
                  <Edit className="mr-2 size-4" />
                  Edit
                </DropdownMenuItem>
              )}
            </>
          )}

          {/* Custom Actions */}
          {options.customActions?.map((action, index) => (
            <DropdownMenuItem
              key={index}
              onClick={() => action.onClick(item)}
              className={
                action.variant === 'destructive' ? 'text-destructive' : ''
              }
            >
              {action.icon && <action.icon className="mr-2 size-4" />}
              {action.label}
            </DropdownMenuItem>
          ))}

          {/* Copy ID */}
          {options.showCopyId && (
            <DropdownMenuItem
              onClick={() => navigator.clipboard.writeText(String(item.id))}
            >
              Copy ID
            </DropdownMenuItem>
          )}

          {/* Delete Action */}
          {options.onDelete && (
            <>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() => options.onDelete?.(item)}
                className="text-destructive"
              >
                <Trash className="mr-2 size-4" />
                Delete
              </DropdownMenuItem>
            </>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
    );
  },
});

/**
 * Status configuration presets for common entities
 */
export const statusConfigs = {
  delegation: {
    Planned: { variant: 'secondary', label: 'Planned' },
    'In Progress': { variant: 'default', label: 'In Progress' },
    Completed: { variant: 'success', label: 'Completed' },
    Cancelled: { variant: 'destructive', label: 'Cancelled' },
  },
  employee: {
    Active: { variant: 'success', label: 'Active' },
    Inactive: { variant: 'secondary', label: 'Inactive' },
    'On Leave': { variant: 'warning', label: 'On Leave' },
  },
  task: {
    Pending: { variant: 'secondary', label: 'Pending' },
    In_Progress: { variant: 'default', label: 'In Progress' }, // Changed from 'In Progress'
    Completed: { variant: 'success', label: 'Completed' },
    Overdue: { variant: 'destructive', label: 'Overdue' },
    Cancelled: { variant: 'destructive', label: 'Cancelled' },
    Assigned: { variant: 'default', label: 'Assigned' },
  },
} as const;
