import { test, expect } from '@playwright/test';

test.describe('Reports Page Test Suite', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the reports page
    await page.goto('http://localhost:9002/reports');
    
    // Wait for the page to load completely
    await page.waitForLoadState('networkidle');
  });

  test('should load reports page with correct title and heading', async ({ page }) => {
    // Verify page title
    await expect(page).toHaveTitle('Reports - WorkHub');
    
    // Verify main heading
    await expect(page.getByRole('heading', { name: 'Reports & Analytics', level: 1 })).toBeVisible();
    
    // Verify description
    await expect(page.getByText('Comprehensive delegation analytics and reporting')).toBeVisible();
  });

  test('should navigate between different tabs', async ({ page }) => {
    // Test Overview tab (should be selected by default)
    await expect(page.getByRole('tab', { name: 'Overview', selected: true })).toBeVisible();
    
    // Click Analytics tab
    await page.getByRole('tab', { name: 'Analytics' }).click();
    await expect(page.getByRole('tab', { name: 'Analytics', selected: true })).toBeVisible();
    await expect(page.getByText('Detailed analytics and trend analysis')).toBeVisible();
    
    // Click Tasks tab
    await page.getByRole('tab', { name: 'Tasks' }).click();
    await expect(page.getByRole('tab', { name: 'Tasks', selected: true })).toBeVisible();
    await expect(page.getByText('Task metrics and performance analysis')).toBeVisible();
    
    // Click Vehicles tab
    await page.getByRole('tab', { name: 'Vehicles' }).click();
    await expect(page.getByRole('tab', { name: 'Vehicles', selected: true })).toBeVisible();
    
    // Click Employees tab
    await page.getByRole('tab', { name: 'Employees' }).click();
    await expect(page.getByRole('tab', { name: 'Employees', selected: true })).toBeVisible();
  });

  test('should test export functionality', async ({ page }) => {
    // Navigate to Tasks tab first
    await page.getByRole('tab', { name: 'Tasks' }).click();
    
    // Test CSV export button
    await expect(page.getByRole('button', { name: 'Export CSV' })).toBeVisible();
    await page.getByRole('button', { name: 'Export CSV' }).click();
    
    // Test PDF export buttons in Task Analytics widget
    await expect(page.getByRole('button', { name: 'CSV' }).first()).toBeVisible();
    await expect(page.getByRole('button', { name: 'PDF' }).first()).toBeVisible();
    
    // Click PDF button
    await page.getByRole('button', { name: 'PDF' }).first().click();
  });

  test('should test filter functionality', async ({ page }) => {
    // Navigate to Tasks tab
    await page.getByRole('tab', { name: 'Tasks' }).click();
    
    // Test search filter
    const filterInput = page.getByRole('textbox', { name: 'Filter tasks...' });
    await expect(filterInput).toBeVisible();
    
    // Type in filter
    await filterInput.fill('test task');
    await expect(filterInput).toHaveValue('test task');
    
    // Clear filter
    await filterInput.clear();
    await expect(filterInput).toHaveValue('');
  });

  test('should test table sorting functionality', async ({ page }) => {
    // Navigate to Tasks tab
    await page.getByRole('tab', { name: 'Tasks' }).click();
    
    // Test table column headers are clickable for sorting
    await expect(page.getByRole('button', { name: 'Task Title' })).toBeVisible();
    await expect(page.getByRole('button', { name: 'Status' })).toBeVisible();
    await expect(page.getByRole('button', { name: 'Priority' })).toBeVisible();
    await expect(page.getByRole('button', { name: 'Assigned To' })).toBeVisible();
    await expect(page.getByRole('button', { name: 'Due Date' })).toBeVisible();
    
    // Click on Task Title to sort
    await page.getByRole('button', { name: 'Task Title' }).click();
    
    // Click on Status to sort
    await page.getByRole('button', { name: 'Status' }).click();
  });

  test('should test pagination controls', async ({ page }) => {
    // Navigate to Tasks tab
    await page.getByRole('tab', { name: 'Tasks' }).click();
    
    // Check pagination controls exist (they should be disabled when no data)
    await expect(page.getByRole('button', { name: 'Previous' })).toBeDisabled();
    await expect(page.getByRole('button', { name: 'Next' })).toBeDisabled();
    
    // Check row count display
    await expect(page.getByText('0 of 0 row(s) selected.')).toBeVisible();
  });

  test('should test quick access dashboard buttons', async ({ page }) => {
    // Test Quick Access Dashboard section
    await expect(page.getByRole('heading', { name: '🚀 Quick Access Dashboard', level: 3 })).toBeVisible();
    
    // Test quick access buttons
    await expect(page.getByRole('button', { name: 'Analytics' }).first()).toBeVisible();
    await expect(page.getByRole('button', { name: 'Data' }).first()).toBeVisible();
    await expect(page.getByRole('button', { name: 'Export' }).first()).toBeVisible();
    
    // Click Analytics quick access button
    await page.getByRole('button', { name: 'Analytics' }).first().click();
  });

  test('should test main action buttons', async ({ page }) => {
    // Test main action buttons in header
    await expect(page.getByRole('button', { name: 'Filters', exact: true })).toBeVisible();
    await expect(page.getByRole('button', { name: 'Refresh' })).toBeVisible();
    await expect(page.getByRole('button', { name: 'Export', exact: true })).toBeVisible();
    
    // Click Filters button
    await page.getByRole('button', { name: 'Filters', exact: true }).click();
    
    // Click Refresh button
    await page.getByRole('button', { name: 'Refresh' }).click();
  });

  test('should test navigation links', async ({ page }) => {
    // Test "Back to Dashboard" link
    await expect(page.getByRole('link', { name: 'Back to Dashboard' })).toBeVisible();
    await expect(page.getByRole('link', { name: 'Back to Dashboard' })).toHaveAttribute('href', '/');
    
    // Test report type links
    await expect(page.getByRole('link', { name: 'View Report' }).first()).toBeVisible();
    await expect(page.getByRole('link', { name: 'View Report' }).first()).toHaveAttribute('href', '/reports');
  });

  test('should display correct widget content', async ({ page }) => {
    // Check Overview tab widgets
    await expect(page.getByText('Delegation Status')).toBeVisible();
    await expect(page.getByText('Delegation Trends')).toBeVisible();
    await expect(page.getByText('Location Distribution')).toBeVisible();
    await expect(page.getByText('Task Analytics')).toBeVisible();
    
    // Switch to Analytics tab and check widgets
    await page.getByRole('tab', { name: 'Analytics' }).click();
    await expect(page.getByText('Delegation Trends')).toBeVisible();
    await expect(page.getByText('Location Distribution')).toBeVisible();
    await expect(page.getByText('Task Analytics')).toBeVisible();
    await expect(page.getByText('Delegation Status')).toBeVisible();
  });

  test('should handle empty data states', async ({ page }) => {
    // Navigate to Tasks tab
    await page.getByRole('tab', { name: 'Tasks' }).click();
    
    // Check empty state messages
    await expect(page.getByText('No task status data available')).toBeVisible();
    await expect(page.getByText('No task priority data available')).toBeVisible();
    await expect(page.getByText('No task assignment data available')).toBeVisible();
    await expect(page.getByText('No tasks found.')).toBeVisible();
    await expect(page.getByText('0 tasks found')).toBeVisible();
  });
});
