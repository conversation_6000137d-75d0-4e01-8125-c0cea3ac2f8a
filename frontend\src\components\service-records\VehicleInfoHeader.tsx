/**
 * @file Vehicle Information Header Component
 * @description Compact vehicle information display for page headers
 * Follows SOLID principles with single responsibility
 */

'use client';

import React from 'react';
import { Car } from 'lucide-react';

import { Skeleton } from '@/components/ui/skeleton';
import { useVehicleInfo, type VehicleInfo } from '@/hooks/api/useVehicleInfo';

interface VehicleInfoHeaderProps {
  vehicleId: number | null | undefined;
  className?: string;
}

interface VehicleInfoDisplayProps {
  vehicleInfo: VehicleInfo;
}

/**
 * Component to display vehicle information in header format
 */
function VehicleInfoDisplay({ vehicleInfo }: VehicleInfoDisplayProps) {
  return (
    <div className="flex flex-wrap items-center gap-4 text-sm text-muted-foreground">
      <div className="flex items-center gap-2">
        <Car className="h-4 w-4" />
        <span className="font-medium">Vehicle:</span>
        <span className="font-semibold text-foreground">
          {vehicleInfo.make} {vehicleInfo.model} ({vehicleInfo.year})
        </span>
      </div>

      <div className="flex items-center gap-2">
        <span className="font-medium">License Plate:</span>
        <span className="font-mono font-semibold text-foreground">
          {vehicleInfo.licensePlate}
        </span>
      </div>
    </div>
  );
}

/**
 * Loading state component
 */
function VehicleInfoLoading() {
  return (
    <div className="flex flex-wrap items-center gap-4 text-sm text-muted-foreground">
      <div className="flex items-center gap-2">
        <Car className="h-4 w-4" />
        <span className="font-medium">Vehicle:</span>
        <Skeleton className="h-4 w-32" />
      </div>

      <div className="flex items-center gap-2">
        <span className="font-medium">License Plate:</span>
        <Skeleton className="h-4 w-20" />
      </div>
    </div>
  );
}

/**
 * Error state component
 */
function VehicleInfoError() {
  return (
    <div className="flex items-center gap-2 text-sm text-muted-foreground">
      <Car className="h-4 w-4" />
      <span>Vehicle information unavailable</span>
    </div>
  );
}

/**
 * Main Vehicle Information Header Component
 */
export function VehicleInfoHeader({
  vehicleId,
  className,
}: VehicleInfoHeaderProps) {
  const { vehicleInfo, isLoading, error } = useVehicleInfo(vehicleId);

  return (
    <div className={className}>
      {isLoading && <VehicleInfoLoading />}
      {error && <VehicleInfoError />}
      {vehicleInfo && <VehicleInfoDisplay vehicleInfo={vehicleInfo} />}
    </div>
  );
}

export default VehicleInfoHeader;
