import swaggerJsdoc from 'swagger-jsdoc';

const options: swaggerJsdoc.Options = {
  // Path to the API docs (routes or controllers with JSDoc comments)
  apis: ['./src/routes/*.ts', './src/controllers/*.ts'], // Adjust as needed
  definition: {
    components: {
      // Future: Define securitySchemes like <PERSON><PERSON>th here
      // securitySchemes: {
      //   bearerAuth: {
      //     type: 'http',
      //     scheme: 'bearer',
      //     bearerFormat: 'JWT',
      //   }
      // }
    },
    info: {
      description: 'API documentation for the Car Service Tracking System backend.',
      title: 'Car Service Tracking API',
      version: '1.0.0',
    },
    openapi: '3.0.0',
    servers: [
      {
        description: 'Development server',
        url: 'http://localhost:3001/api', // Adjust if your port/base path is different
      },
    ],
    // security: [
    //   {
    //     bearerAuth: []
    //   }
    // ]
  },
};

const swaggerSpec = swaggerJsdoc(options);

export default swaggerSpec;
