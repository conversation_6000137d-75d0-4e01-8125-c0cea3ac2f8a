/**
 * @file Delegation Status Widget - UX Enhanced with Clear Legend
 * @description Professional pie chart with prominent legend for better usability
 *
 * UX Improvements Applied:
 * - Enhanced legend positioning and styling for clarity
 * - Better color contrast and accessibility
 * - Professional spacing and typography
 * - Clear status mapping with percentages
 */

'use client';

import React from 'react';
import {
  PieChart,
  Pie,
  Cell,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useDelegationAnalyticsQuery } from '../../data/hooks';
import { useReportingFilters } from '../../data/stores';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { BarChart3, CheckCircle } from 'lucide-react';

// Enhanced custom legend component for better visibility
const CustomLegend = ({ payload }: any) => {
  if (!payload || payload.length === 0) return null;

  return (
    <div className="flex flex-wrap justify-center gap-4 mt-6 pt-4 border-t border-border">
      {payload.map((entry: any, index: number) => (
        <div
          key={`legend-${index}`}
          className="flex items-center gap-2 text-sm"
        >
          <div
            className="w-4 h-4 rounded-full border border-gray-300"
            style={{ backgroundColor: entry.color }}
          />
          <span className="font-medium text-gray-700 dark:text-gray-300">
            {entry.value}
          </span>
          {entry.payload && (
            <span className="text-muted-foreground">
              ({entry.payload.count})
            </span>
          )}
        </div>
      ))}
    </div>
  );
};

// Enhanced tooltip with percentage information
const CustomTooltip = ({ active, payload }: any) => {
  if (active && payload && payload.length) {
    const data = payload[0];
    const total = payload[0].payload.total || 100;
    const percentage = total > 0 ? Math.round((data.value / total) * 100) : 0;

    return (
      <div className="bg-white dark:bg-gray-800 p-4 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg">
        <p className="font-semibold text-gray-900 dark:text-gray-100 mb-2">
          {data.name}
        </p>
        <div className="space-y-1">
          <div className="flex justify-between items-center gap-4">
            <span className="text-sm text-gray-600 dark:text-gray-400">
              Count:
            </span>
            <span className="font-bold text-lg" style={{ color: data.color }}>
              {data.value}
            </span>
          </div>
          <div className="flex justify-between items-center gap-4">
            <span className="text-sm text-gray-600 dark:text-gray-400">
              Percentage:
            </span>
            <span className="font-medium text-primary">{percentage}%</span>
          </div>
        </div>
      </div>
    );
  }
  return null;
};

export const DelegationStatusWidget = () => {
  const filters = useReportingFilters();
  const { data, isLoading, error } = useDelegationAnalyticsQuery(filters);

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <Skeleton className="h-7 w-48" />
        </CardHeader>
        <CardContent>
          <Skeleton className="h-[400px] w-full rounded-lg" />
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-3">
            <BarChart3 className="h-6 w-6" />
            Delegation Status
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive">
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error.message}</AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  const chartData = data?.statusDistribution ?? [];

  // Calculate total for percentage calculations
  const total = chartData.reduce((sum, item) => sum + item.count, 0);
  const enrichedData = chartData.map(item => ({
    ...item,
    total,
  }));

  // Default colors if not provided in data
  const defaultColors = ['#22c55e', '#f59e0b', '#ef4444', '#3b82f6', '#8b5cf6'];
  const dataWithColors = enrichedData.map((item, index) => ({
    ...item,
    color: item.color || defaultColors[index % defaultColors.length],
  }));

  return (
    <Card>
      <CardHeader className="pb-6">
        <CardTitle className="flex items-center gap-3 text-xl">
          <BarChart3 className="h-6 w-6 text-primary" />
          Delegation Status
        </CardTitle>
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <CheckCircle className="h-4 w-4" />
          <span>Distribution of delegation statuses</span>
        </div>
      </CardHeader>
      <CardContent className="pt-6">
        <ResponsiveContainer width="100%" height={300}>
          <PieChart>
            <Pie
              data={dataWithColors}
              cx="50%"
              cy="50%"
              labelLine={false}
              outerRadius={100}
              innerRadius={40} // Create a donut chart for better visual appeal
              fill="#8884d8"
              dataKey="count"
              nameKey="status"
              stroke="#fff"
              strokeWidth={2}
            >
              {dataWithColors.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.color} />
              ))}
            </Pie>
            <Tooltip content={<CustomTooltip />} />
          </PieChart>
        </ResponsiveContainer>

        {/* Clear, prominent legend */}
        <CustomLegend
          payload={dataWithColors.map(item => ({
            value: item.status,
            color: item.color,
            payload: item,
          }))}
        />

        {/* Summary statistics */}
        {total > 0 && (
          <div className="mt-6 pt-4 border-t border-border">
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">Total Delegations:</span>
              <span className="font-semibold text-primary">{total}</span>
            </div>
            {dataWithColors.length > 0 && dataWithColors[0] && (
              <div className="flex items-center justify-between text-sm mt-2">
                <span className="text-muted-foreground">
                  Most Common Status:
                </span>
                <span
                  className="font-semibold"
                  style={{ color: dataWithColors[0].color }}
                >
                  {dataWithColors[0].status}
                </span>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};
