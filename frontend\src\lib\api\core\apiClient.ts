/**
 * @file Base HTTP client for making API requests.
 * @module api/base/apiClient
 */

import type { ApiClientConfig, HttpMethod, RequestConfig } from './types';
import type { IHttpClient } from './interfaces';

import { undefinedToNull } from '../../utils/typeHelpers';
import {
  ApiError,
  AuthenticationError,
  BadRequestError,
  InternalServerError,
  NetworkError,
  NotFoundError,
} from './errors';

// Helper functions (can be private static or module-level)
const sleep = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

const getRetryDelay = (attempt: number, baseDelay = 1000) => {
  return Math.min(
    baseDelay * Math.pow(2, attempt), // Exponential backoff
    30_000 // Max 30 seconds
  );
};

// Removed categorizeError function, will use error.retryable directly from ApiError instances

/**
 * A base HTTP client for interacting with RESTful APIs.
 * Provides methods for GET, POST, PUT, DELETE requests with built-in
 * retry logic, error handling, and authentication token management.
 * Implements IHttpClient interface for composition pattern support.
 */
export class ApiClient implements IHttpClient {
  private readonly baseURL: string;
  private readonly defaultHeaders: Record<string, string>;
  private readonly retryAttempts: number;
  private readonly timeout: number;
  private readonly getAuthToken: (() => string | null) | undefined;

  /**
   * Creates an instance of ApiClient.
   * @param config - Configuration object for the API client.
   */
  constructor(config: ApiClientConfig) {
    this.baseURL = config.baseURL;
    this.timeout = config.timeout || 10_000; // Default to 10 seconds
    this.retryAttempts = config.retryAttempts || 3; // Default to 3 retry attempts
    this.getAuthToken = config.getAuthToken;
    this.defaultHeaders = {
      'Content-Type': 'application/json',
      ...config.headers,
    };
  }

  /**
   * Performs a DELETE request.
   * @template T - The expected response data type (can be void if no content).
   * @param endpoint - The API endpoint.
   * @param config - Optional request configuration.
   * @returns A Promise that resolves when the request is complete.
   */
  public async delete<T = void>(
    endpoint: string,
    config?: RequestConfig
  ): Promise<T> {
    return this.request<T>('DELETE', endpoint, undefined, config);
  }

  /**
   * Performs a GET request.
   * @template T - The expected response data type.
   * @param endpoint - The API endpoint (e.g., '/users').
   * @param config - Optional request configuration.
   * @returns A Promise that resolves with the response data.
   */
  public async get<T>(endpoint: string, config?: RequestConfig): Promise<T> {
    return this.request<T>('GET', endpoint, undefined, config);
  }

  /**
   * Performs a PATCH request.
   * @template T - The expected response data type.
   * @param endpoint - The API endpoint.
   * @param data - The request body.
   * @param config - Optional request configuration.
   * @returns A Promise that resolves with the response data.
   */
  public async patch<T>(
    endpoint: string,
    data?: any,
    config?: RequestConfig
  ): Promise<T> {
    return this.request<T>('PATCH', endpoint, data, config);
  }

  /**
   * Performs a POST request.
   * @template T - The expected response data type.
   * @param endpoint - The API endpoint.
   * @param data - The request body.
   * @param config - Optional request configuration.
   * @returns A Promise that resolves with the response data.
   */
  public async post<T>(
    endpoint: string,
    data?: any,
    config?: RequestConfig
  ): Promise<T> {
    return this.request<T>('POST', endpoint, data, config);
  }

  /**
   * Performs a PUT request.
   * @template T - The expected response data type.
   * @param endpoint - The API endpoint.
   * @param data - The request body.
   * @param config - Optional request configuration.
   * @returns A Promise that resolves with the response data.
   */
  public async put<T>(
    endpoint: string,
    data?: any,
    config?: RequestConfig
  ): Promise<T> {
    return this.request<T>('PUT', endpoint, data, config);
  }

  /**
   * Internal method to perform the actual HTTP request with retry logic and error handling.
   * @private
   * @template T - The expected response data type.
   * @param method - The HTTP method.
   * @param endpoint - The API endpoint.
   * @param data - The request body for POST, PUT, PATCH.
   * @param config - Optional request configuration, including specific timeout or retry parameters.
   * @returns A Promise that resolves with the (unwrapped, if applicable) response data of type T.
   * @throws {ApiError} For API-specific errors (e.g., 4xx, 5xx status codes).
   * @throws {NetworkError} For network connectivity issues or if the request times out after retries.
   * @throws {AuthenticationError} Specifically for 401 Unauthorized errors.
   */
  private async request<T>(
    method: HttpMethod,
    endpoint: string,
    data?: any,
    config?: RequestConfig
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;
    const headers = {
      ...this.defaultHeaders,
      ...config?.headers,
    };

    // 🔑 JWT Authentication: Add Authorization header if token is available
    if (this.getAuthToken) {
      const token = this.getAuthToken();
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }
    }

    // 🔑 JWT Authentication: Using JWT tokens for authentication instead of CSRF tokens
    // CSRF protection is not needed when using stateless JWT authentication
    // as JWTs are immune to CSRF attacks when stored properly (httpOnly cookies)
    if (process.env.NODE_ENV !== 'production') {
      console.debug(
        '🔐 Using JWT authentication for request:',
        method,
        endpoint
      );
    }

    const requestOptions: RequestInit = {
      body: data ? JSON.stringify(data) : null,
      credentials: 'include',
      headers,
      method,
      signal: undefinedToNull(config?.signal),
    };

    // Implement timeout logic
    const controller = new AbortController();
    const timeoutId = setTimeout(
      () => controller.abort(),
      config?.timeout || this.timeout
    );
    requestOptions.signal = config?.signal || controller.signal;

    for (let attempt = 0; attempt < this.retryAttempts; attempt++) {
      try {
        const response = await fetch(url, requestOptions);
        clearTimeout(timeoutId);

        const responseBody = await response.json().catch(() => null);

        if (!response.ok) {
          // Use the standardized error structure if available
          if (responseBody && responseBody.status === 'error') {
            throw new ApiError(responseBody.message, {
              code: responseBody.code,
              details: responseBody.error,
              status: response.status,
            });
          }
          // Fallback to old error handling for non-standard responses
          const errorMessage = responseBody?.message || response.statusText;
          switch (response.status) {
            case 400: {
              throw new BadRequestError(errorMessage, responseBody);
            }
            case 401: {
              throw new AuthenticationError(errorMessage, responseBody);
            }
            case 404: {
              throw new NotFoundError(errorMessage, responseBody);
            }
            case 500: {
              throw new InternalServerError(errorMessage, responseBody);
            }
            default: {
              throw new ApiError(errorMessage, {
                details: responseBody,
                status: response.status,
              });
            }
          }
        }

        // Handle standardized success response format
        if (
          responseBody &&
          typeof responseBody === 'object' &&
          responseBody.status === 'success'
        ) {
          // Return the data property for standardized success responses
          return responseBody.data as T;
        }

        // Handle 204 No Content responses
        if (response.status === 204) {
          return undefined as T;
        }

        // Fallback for legacy non-standard success responses
        // This should become increasingly rare as backend migration completes
        if (process.env.NODE_ENV !== 'production') {
          console.warn('⚠️ Non-standard success response detected:', {
            endpoint,
            method,
            responseBody,
            status: response.status,
          });
        }
        return responseBody as T;
      } catch (error) {
        if (error instanceof ApiError) {
          // Re-throw known API errors immediately
          throw error;
        }
        // Handle network errors or other fetch-related issues
        if (attempt === this.retryAttempts - 1) {
          const errorMessage =
            error instanceof Error ? error.message : String(error);
          throw new NetworkError(
            `Request failed after ${this.retryAttempts} attempts: ${errorMessage}`
          );
        }
        const delay = getRetryDelay(attempt);
        await sleep(delay);
      }
    }
    // This part should be unreachable if retry logic is correct
    throw new NetworkError('Request failed after multiple retries.');
  }
}
