/**
 * @file Recipient Management Components Index
 * @description Centralized exports for all recipient-related components
 */

// Main components
export { RecipientCard } from './RecipientCard';
export { RecipientList } from './RecipientList';
export { RecipientFilters } from './RecipientFilters';

// Form components
export { RecipientForm } from './forms/RecipientForm';

// Re-export types for convenience
export type { Recipient, CreateRecipientData, UpdateRecipientData } from '@/lib/types/domain';
