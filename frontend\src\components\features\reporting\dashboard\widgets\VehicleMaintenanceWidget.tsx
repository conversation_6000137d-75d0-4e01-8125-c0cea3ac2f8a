/**
 * @file VehicleMaintenanceWidget.tsx
 * @description Vehicle maintenance tracking widget following existing patterns and SOLID principles
 */

import { addDays, format, isAfter, isBefore } from 'date-fns';
import {
  AlertTriangle,
  Calendar,
  Car,
  CheckCircle,
  Clock,
  MoreHorizontal,
  Wrench,
} from 'lucide-react';
import React from 'react';

import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { DataLoader } from '@/components/ui/data-loader';
import { ErrorDisplay } from '@/components/ui/error-display';
import { cn } from '@/lib/utils';

import type { ReportingFilters } from '../../data/types/reporting';
import type { MaintenanceScheduleData } from '../../data/types/reporting';

import { useVehicleAnalytics } from '../../hooks/useVehicleAnalytics';

/**
 * Maintenance item component
 */
interface MaintenanceItemProps {
  compact?: boolean;
  item: MaintenanceScheduleData;
}

/**
 * Props interface for VehicleMaintenanceWidget
 */
interface VehicleMaintenanceWidgetProps {
  className?: string;
  compact?: boolean;
  filters?: ReportingFilters;
  maxItems?: number;
}

const MaintenanceItem: React.FC<MaintenanceItemProps> = ({
  compact = false,
  item,
}) => {
  const scheduledDate = new Date(item.nextMaintenanceDate);
  const today = new Date();
  const isOverdue = isBefore(scheduledDate, today);
  const isDueSoon =
    isAfter(scheduledDate, today) && isBefore(scheduledDate, addDays(today, 7));

  const getStatusIcon = () => {
    // MaintenanceScheduleData doesn't have status, determine from dates
    if (isOverdue) return <AlertTriangle className="size-4 text-red-600" />;
    if (isDueSoon) return <Clock className="size-4 text-orange-600" />;
    return <Calendar className="size-4 text-blue-600" />;
  };

  const getStatusBadge = () => {
    // MaintenanceScheduleData doesn't have status, determine from dates
    if (isOverdue) return <Badge variant="destructive">Overdue</Badge>;
    if (isDueSoon)
      return (
        <Badge className="bg-orange-100 text-orange-800" variant="secondary">
          Due Soon
        </Badge>
      );
    return <Badge variant="outline">Scheduled</Badge>;
  };

  return (
    <div
      className={cn(
        'flex items-center justify-between p-3 border rounded-lg',
        isOverdue && 'border-red-200 bg-red-50',
        isDueSoon && 'border-orange-200 bg-orange-50'
        // MaintenanceScheduleData doesn't have status, so no completed styling
      )}
    >
      <div className="flex items-center space-x-3">
        {getStatusIcon()}
        <div className="flex-1">
          <div className="flex items-center gap-2">
            <p className="text-sm font-medium">{item.maintenanceType}</p>
            {!compact && getStatusBadge()}
          </div>
          <div className="mt-1 flex items-center gap-4 text-xs text-gray-500">
            <span className="flex items-center gap-1">
              <Car className="size-3" />
              {item.vehicleName}
            </span>
            <span className="flex items-center gap-1">
              <Calendar className="size-3" />
              {format(scheduledDate, 'MMM dd, yyyy')}
            </span>
            {item.estimatedCost && (
              <span>${item.estimatedCost.toLocaleString()}</span>
            )}
          </div>
        </div>
      </div>

      {compact && <div className="text-right">{getStatusBadge()}</div>}
    </div>
  );
};

/**
 * VehicleMaintenanceWidget Component
 *
 * Displays vehicle maintenance schedule and status following existing widget patterns.
 *
 * Responsibilities:
 * - Display maintenance schedule in widget format
 * - Show overdue and upcoming maintenance items
 * - Follow established widget composition patterns
 * - Maintain consistent styling and behavior
 *
 * SOLID Principles Applied:
 * - SRP: Single responsibility of displaying maintenance data
 * - OCP: Open for extension via props configuration
 * - DIP: Depends on existing widget framework abstractions
 */
export const VehicleMaintenanceWidget: React.FC<
  VehicleMaintenanceWidgetProps
> = ({ className = '', compact = false, filters, maxItems = 5 }) => {
  // Use existing hook patterns
  const {
    data: vehicleAnalytics,
    error,
    isLoading,
  } = useVehicleAnalytics(filters);
  const maintenanceData = vehicleAnalytics?.maintenanceSchedule || [];

  // Handle loading state
  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Wrench className="size-5" />
            Vehicle Maintenance
          </CardTitle>
        </CardHeader>
        <CardContent>
          <DataLoader data={null} error={null} isLoading={true}>
            {() => null}
          </DataLoader>
        </CardContent>
      </Card>
    );
  }

  // Handle error state
  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Wrench className="size-5" />
            Vehicle Maintenance
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ErrorDisplay error={error} />
        </CardContent>
      </Card>
    );
  }

  // Process maintenance data
  const today = new Date();
  const overdueItems = maintenanceData.filter(item =>
    isBefore(new Date(item.nextMaintenanceDate), today)
  );
  const dueSoonItems = maintenanceData.filter(
    item =>
      isAfter(new Date(item.nextMaintenanceDate), today) &&
      isBefore(new Date(item.nextMaintenanceDate), addDays(today, 7))
  );
  // MaintenanceScheduleData doesn't have status, so we can't filter completed items
  const completedItems: MaintenanceScheduleData[] = [];

  // Sort and limit items for display
  const displayItems = maintenanceData
    .sort((a, b) => {
      // Prioritize overdue, then due soon, then by date
      const aDate = new Date(a.nextMaintenanceDate);
      const bDate = new Date(b.nextMaintenanceDate);
      const aOverdue = isBefore(aDate, today);
      const bOverdue = isBefore(bDate, today);

      if (aOverdue && !bOverdue) return -1;
      if (!aOverdue && bOverdue) return 1;

      return aDate.getTime() - bDate.getTime();
    })
    .slice(0, maxItems);

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Wrench className="size-5" />
            Vehicle Maintenance
          </CardTitle>
          <div className="flex items-center gap-2">
            {overdueItems.length > 0 && (
              <Badge className="text-xs" variant="destructive">
                {overdueItems.length} overdue
              </Badge>
            )}
            {dueSoonItems.length > 0 && (
              <Badge
                className="bg-orange-100 text-xs text-orange-800"
                variant="secondary"
              >
                {dueSoonItems.length} due soon
              </Badge>
            )}
            <Button size="sm" variant="ghost">
              <MoreHorizontal className="size-4" />
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Summary Stats */}
        <div className="grid grid-cols-3 gap-4">
          <div className="rounded-lg bg-red-50 p-3 text-center">
            <div className="text-2xl font-bold text-red-700">
              {overdueItems.length}
            </div>
            <div className="text-xs text-red-600">Overdue</div>
          </div>
          <div className="rounded-lg bg-orange-50 p-3 text-center">
            <div className="text-2xl font-bold text-orange-700">
              {dueSoonItems.length}
            </div>
            <div className="text-xs text-orange-600">Due Soon</div>
          </div>
          <div className="rounded-lg bg-green-50 p-3 text-center">
            <div className="text-2xl font-bold text-green-700">
              {completedItems.length}
            </div>
            <div className="text-xs text-green-600">Completed</div>
          </div>
        </div>

        {/* Maintenance Items List */}
        <div className="space-y-3">
          {displayItems.length === 0 ? (
            <div className="py-8 text-center text-gray-500">
              <Wrench className="mx-auto mb-2 size-12 opacity-50" />
              <p>No maintenance items scheduled</p>
            </div>
          ) : (
            displayItems.map((item, index) => (
              <MaintenanceItem
                compact={compact}
                item={item}
                key={`${item.vehicleId}-${item.maintenanceType}-${index}`}
              />
            ))
          )}
        </div>

        {!compact && maintenanceData.length > maxItems && (
          <div className="border-t pt-4">
            <Button className="w-full" size="sm" variant="outline">
              View All Maintenance ({maintenanceData.length} total)
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default VehicleMaintenanceWidget;
