/**
 * @file Tests for circuit breaker history functionality
 * @description Tests to ensure circuit breaker history tracking works correctly
 */

import { getCircuitBreakerHistory } from '../circuitBreaker.service.js';

describe('Circuit Breaker History', () => {
  beforeEach(() => {
    // Clear any existing history before each test
    jest.clearAllMocks();
  });

  it('should return empty history when no data exists', () => {
    const history = getCircuitBreakerHistory('24h');
    
    expect(history).toEqual({
      timeframe: '24h',
      breakerName: undefined,
      entries: [],
      summary: {
        totalStateChanges: 0,
        stateDistribution: {},
        averageErrorRate: 0,
      },
    });
  });

  it('should handle different timeframes correctly', () => {
    const timeframes: Array<'1h' | '6h' | '24h' | '7d'> = ['1h', '6h', '24h', '7d'];
    
    timeframes.forEach(timeframe => {
      const history = getCircuitBreakerHistory(timeframe);
      expect(history.timeframe).toBe(timeframe);
      expect(history.entries).toEqual([]);
    });
  });

  it('should filter by breaker name when provided', () => {
    const breakerName = 'database';
    const history = getCircuitBreakerHistory('24h', breakerName);
    
    expect(history.breakerName).toBe(breakerName);
    expect(history.entries).toEqual([]);
  });

  it('should handle invalid timeframes gracefully', () => {
    // Test with default fallback
    const history = getCircuitBreakerHistory('invalid' as any);
    
    expect(history.timeframe).toBe('invalid');
    expect(history.entries).toEqual([]);
  });

  it('should return correct structure for empty data', () => {
    const history = getCircuitBreakerHistory('24h');
    
    // Verify the structure matches expected interface
    expect(history).toHaveProperty('timeframe');
    expect(history).toHaveProperty('entries');
    expect(history).toHaveProperty('summary');
    expect(history.summary).toHaveProperty('totalStateChanges');
    expect(history.summary).toHaveProperty('stateDistribution');
    expect(history.summary).toHaveProperty('averageErrorRate');
  });
});
