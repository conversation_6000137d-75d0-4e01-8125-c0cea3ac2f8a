'use client';

import React from 'react';

import { useUiPreferences } from '@/hooks/ui/useUiPreferences';

/**
 * Font Size Provider Component
 * Applies the selected font size globally to the application
 */
export const FontSizeProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const { getFontSizeClass } = useUiPreferences();

  return (
    <div className={`font-size-provider ${getFontSizeClass()}`}>{children}</div>
  );
};

/**
 * Font Size Aware Layout Component
 * Layout component that responds to font size changes
 */
export const FontSizeAwareLayout: React.FC<{
  children: React.ReactNode;
  className?: string;
}> = ({ children, className = '' }) => {
  const { fontSize, getFontSizeClass } = useUiPreferences();

  // Additional spacing adjustments based on font size
  const getSpacingClass = () => {
    switch (fontSize) {
      case 'large': {
        return 'space-y-6';
      } // More spacing for large text
      case 'small': {
        return 'space-y-3';
      } // Tighter spacing for small text
      default: {
        return 'space-y-4';
      } // Default spacing
    }
  };

  return (
    <div className={`${getFontSizeClass()} ${getSpacingClass()} ${className}`}>
      {children}
    </div>
  );
};
