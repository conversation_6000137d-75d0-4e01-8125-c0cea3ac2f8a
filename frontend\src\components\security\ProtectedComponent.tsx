/**
 * @file Protected Component - Single Responsibility Principle (SRP)
 * @module components/security/ProtectedComponent
 *
 * This component handles ONLY conditional rendering based on permissions following SRP principles.
 * It provides role-based access control for UI elements.
 *
 * SECURITY NOTE: This is the primary component for implementing RBAC in the UI.
 */

'use client';

import React from 'react';
import { usePermissions } from '../../lib/api/security';
import type {
  Permission,
  UserRole,
} from '../../lib/security/PermissionManager';

export interface ProtectedComponentProps {
  children: React.ReactNode;

  // Permission-based protection
  requiredPermission?: Permission;
  requiredPermissions?: Permission[];
  anyPermission?: Permission[];

  // Role-based protection
  requiredRole?: UserRole;
  minimumRole?: UserRole;

  // Fallback content
  fallback?: React.ReactNode;
  showFallbackWhenLoading?: boolean;

  // Custom permission check
  customCheck?: (permissions: ReturnType<typeof usePermissions>) => boolean;

  // Debug mode
  debug?: boolean;
}

/**
 * ProtectedComponent - Single Responsibility: Conditional Rendering Based on Permissions
 *
 * Renders children only if user has required permissions or roles.
 * Does NOT handle authentication logic or permission management.
 */
export function ProtectedComponent({
  children,
  requiredPermission,
  requiredPermissions,
  anyPermission,
  requiredRole,
  minimumRole,
  fallback = null,
  showFallbackWhenLoading = false,
  customCheck,
  debug = false,
}: ProtectedComponentProps): React.ReactElement | null {
  const permissions = usePermissions();

  // Debug logging
  if (debug && process.env.NODE_ENV === 'development') {
    console.log('🔒 ProtectedComponent Debug:', {
      userRole: permissions.userRole,
      isAuthenticated: permissions.isAuthenticated,
      requiredPermission,
      requiredPermissions,
      anyPermission,
      requiredRole,
      minimumRole,
    });
  }

  // Show fallback during loading if requested
  if (!permissions.isAuthenticated && showFallbackWhenLoading) {
    return <>{fallback}</>;
  }

  // Must be authenticated for any protection
  if (!permissions.isAuthenticated) {
    return null;
  }

  // Custom permission check takes precedence
  if (customCheck) {
    const hasCustomPermission = customCheck(permissions);
    if (debug && process.env.NODE_ENV === 'development') {
      console.log('🔒 Custom check result:', hasCustomPermission);
    }
    return hasCustomPermission ? <>{children}</> : <>{fallback}</>;
  }

  // Check specific role requirement
  if (requiredRole) {
    const hasRole = permissions.userRole === requiredRole;
    if (debug && process.env.NODE_ENV === 'development') {
      console.log('🔒 Required role check:', {
        requiredRole,
        userRole: permissions.userRole,
        hasRole,
      });
    }
    return hasRole ? <>{children}</> : <>{fallback}</>;
  }

  // Check minimum role requirement
  if (minimumRole) {
    const hasMinimumRole = permissions.hasMinimumRole(minimumRole);
    if (debug && process.env.NODE_ENV === 'development') {
      console.log('🔒 Minimum role check:', {
        minimumRole,
        userRole: permissions.userRole,
        hasMinimumRole,
      });
    }
    return hasMinimumRole ? <>{children}</> : <>{fallback}</>;
  }

  // Check single permission requirement
  if (requiredPermission) {
    const hasPermission = permissions.hasPermission(requiredPermission);
    if (debug && process.env.NODE_ENV === 'development') {
      console.log('🔒 Single permission check:', {
        requiredPermission,
        hasPermission,
      });
    }
    return hasPermission ? <>{children}</> : <>{fallback}</>;
  }

  // Check all permissions requirement
  if (requiredPermissions && requiredPermissions.length > 0) {
    const hasAllPermissions =
      permissions.hasAllPermissions(requiredPermissions);
    if (debug && process.env.NODE_ENV === 'development') {
      console.log('🔒 All permissions check:', {
        requiredPermissions,
        hasAllPermissions,
      });
    }
    return hasAllPermissions ? <>{children}</> : <>{fallback}</>;
  }

  // Check any permission requirement
  if (anyPermission && anyPermission.length > 0) {
    const hasAnyPermission = permissions.hasAnyPermission(anyPermission);
    if (debug && process.env.NODE_ENV === 'development') {
      console.log('🔒 Any permission check:', {
        anyPermission,
        hasAnyPermission,
      });
    }
    return hasAnyPermission ? <>{children}</> : <>{fallback}</>;
  }

  // If no specific requirements, show content (authenticated users only)
  return <>{children}</>;
}

/**
 * Convenience wrapper for admin-only content
 */
export function AdminOnly({
  children,
  fallback = null,
}: {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}): React.ReactElement | null {
  return (
    <ProtectedComponent minimumRole="ADMIN" fallback={fallback}>
      {children}
    </ProtectedComponent>
  );
}

/**
 * Convenience wrapper for super admin-only content
 */
export function SuperAdminOnly({
  children,
  fallback = null,
}: {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}): React.ReactElement | null {
  return (
    <ProtectedComponent requiredRole="SUPER_ADMIN" fallback={fallback}>
      {children}
    </ProtectedComponent>
  );
}

/**
 * Convenience wrapper for write permission content
 */
export function WriteProtected({
  children,
  entity,
  fallback = null,
}: {
  children: React.ReactNode;
  entity?: string;
  fallback?: React.ReactNode;
}): React.ReactElement | null {
  const permission = entity ? (`${entity}:write` as Permission) : 'write';

  return (
    <ProtectedComponent requiredPermission={permission} fallback={fallback}>
      {children}
    </ProtectedComponent>
  );
}

/**
 * Convenience wrapper for delete permission content
 */
export function DeleteProtected({
  children,
  entity,
  fallback = null,
}: {
  children: React.ReactNode;
  entity?: string;
  fallback?: React.ReactNode;
}): React.ReactElement | null {
  const permission = entity ? (`${entity}:delete` as Permission) : 'delete';

  return (
    <ProtectedComponent requiredPermission={permission} fallback={fallback}>
      {children}
    </ProtectedComponent>
  );
}
