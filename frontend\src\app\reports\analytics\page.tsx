// frontend/src/app/reports/analytics/page.tsx

import { Metadata } from 'next';
import { ReportingDashboard } from '@/components/features/reporting/dashboard/ReportingDashboard';

export const metadata: Metadata = {
  title: 'Analytics - Reports - WorkHub',
  description: 'Detailed delegation analytics and performance insights',
};

/**
 * Analytics Page
 * 
 * Focused analytics view of the reporting dashboard.
 * Uses the existing reporting dashboard with analytics tab pre-selected.
 */
export default function AnalyticsPage() {
  return <ReportingDashboard />;
}
