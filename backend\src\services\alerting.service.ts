/**
 * Alerting Service
 *
 * Provides comprehensive alerting capabilities for WorkHub application including:
 * - Webhook-based alert notifications
 * - Configurable alert thresholds
 * - Alert escalation policies
 * - Multiple notification channels (email, Slack, webhook)
 * - Alert management and history
 * - Integration with metrics and health monitoring
 */

import logger from '../utils/logger.js';
import { circuitBreakerRegistry } from './circuitBreaker.service.js';
import { getComprehensiveHealthReport, HealthStatus } from './health.service.js';
import { businessMetrics } from './metrics.service.js';
import { getUnifiedWebSocketService } from './UnifiedWebSocketService.js';
import { RELIABILITY_EVENTS } from './WebSocketEventManager.js';

// Alert configuration from environment variables
const ALERTS_ENABLED = process.env.ALERTS_ENABLED !== 'false';
const ALERT_WEBHOOK_URL = process.env.ALERT_WEBHOOK_URL;
const ALERT_EMAIL_RECIPIENTS = process.env.ALERT_EMAIL_RECIPIENTS?.split(',') || [];
const ALERT_SLACK_WEBHOOK = process.env.ALERT_SLACK_WEBHOOK;

// Alert thresholds configuration
const RESPONSE_TIME_ALERT_THRESHOLD = parseInt(process.env.RESPONSE_TIME_ALERT_THRESHOLD || '2000');
const ERROR_RATE_ALERT_THRESHOLD = parseInt(process.env.ERROR_RATE_ALERT_THRESHOLD || '5');
const CPU_USAGE_ALERT_THRESHOLD = parseInt(process.env.CPU_USAGE_ALERT_THRESHOLD || '80');
const MEMORY_USAGE_ALERT_THRESHOLD = parseInt(process.env.MEMORY_USAGE_ALERT_THRESHOLD || '85');

export interface Alert {
  acknowledgedAt?: string;
  acknowledgedBy?: string;
  createdAt: string;
  details?: any;
  escalationLevel: number;
  id: string;
  message: string;
  metadata?: {
    [key: string]: any;
    component: string;
    currentValue?: number;
    source: string;
    threshold?: number;
  };
  notificationChannels: string[];
  resolvedAt?: string;
  severity: AlertSeverity;
  status: AlertStatus;
  title: string;
  type: AlertType;
  updatedAt: string;
}
export interface AlertRule {
  condition: string;
  enabled: boolean;
  escalationPolicy: EscalationPolicy;
  evaluationInterval: number; // seconds
  id: string;
  metadata?: any;
  name: string;
  notificationChannels: NotificationChannel[];
  severity: AlertSeverity;
  threshold: number;
  type: AlertType;
}
// Alert types and severity levels
export type AlertSeverity = 'critical' | 'high' | 'low' | 'medium';

export type AlertStatus = 'acknowledged' | 'active' | 'resolved';

export type AlertType = 'business' | 'error' | 'health' | 'performance' | 'security';

export interface EscalationLevel {
  channels: string[];
  delay: number; // minutes
  level: number;
}

export interface EscalationPolicy {
  escalationInterval: number; // minutes
  levels: EscalationLevel[];
  maxEscalations: number;
}

export interface NotificationChannel {
  config: {
    [key: string]: any;
    recipients?: string[];
    url?: string;
  };
  enabled: boolean;
  id: string;
  name: string;
  type: 'email' | 'slack' | 'webhook';
}

// In-memory alert storage (in production, this would be a database)
const activeAlerts = new Map<string, Alert>();
const alertHistory: Alert[] = [];
const alertRules = new Map<string, AlertRule>();
const notificationChannels = new Map<string, NotificationChannel>();

/**
 * Acknowledge an alert
 */
export async function acknowledgeAlert(
  alertId: string,
  acknowledgedBy: string,
): Promise<Alert | null> {
  const alert = activeAlerts.get(alertId);
  if (!alert) {
    return null;
  }

  alert.status = 'acknowledged';
  alert.acknowledgedAt = new Date().toISOString();
  alert.acknowledgedBy = acknowledgedBy;
  alert.updatedAt = new Date().toISOString();

  // Record alert acknowledgment metric
  businessMetrics.recordUserOperation('alert_acknowledged', acknowledgedBy, 'success');

  logger.info('Alert acknowledged', {
    acknowledgedBy,
    alertId,
    service: 'alerting',
  });

  return alert;
}

/**
 * Create a new alert
 */
export async function createAlert(
  type: AlertType,
  severity: AlertSeverity,
  title: string,
  message: string,
  details?: any,
  metadata?: Alert['metadata'],
): Promise<Alert> {
  const alertId = `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

  const alert: Alert = {
    createdAt: new Date().toISOString(),
    details,
    escalationLevel: 0,
    id: alertId,
    message,
    metadata,
    notificationChannels: [],
    severity,
    status: 'active',
    title,
    type,
    updatedAt: new Date().toISOString(),
  };

  activeAlerts.set(alertId, alert);
  alertHistory.push(alert);

  // Send initial notification
  await sendAlertNotification(alert);

  // Emit WebSocket event for real-time client updates
  try {
    const unifiedSocketService = getUnifiedWebSocketService();
    if (unifiedSocketService && unifiedSocketService.reliability) {
      if (typeof (unifiedSocketService.reliability as any).emitAlertCreated === 'function') {
        (unifiedSocketService.reliability as any).emitAlertCreated(alert);
      } else {
        logger.warn(
          'emitAlertCreated method not found on reliability namespace, attempting direct emit via UnifiedWebSocketService for alert.',
        );
        unifiedSocketService.emitToRoom(
          'reliability-monitoring',
          RELIABILITY_EVENTS.ALERT_CREATED,
          alert,
        );
      }
    } else {
      logger.error(
        'UnifiedWebSocketService or its reliability namespace is not available. WebSocket event for alert not sent.',
      );
    }
  } catch (error) {
    logger.error('Failed to emit WebSocket event for alert creation', { error, alertId });
  }

  // Record alert creation metric
  businessMetrics.recordUserOperation('alert_created', 'system', 'success');

  logger.warn('Alert created', {
    alertId,
    metadata,
    service: 'alerting',
    severity,
    title,
    type,
  });

  return alert;
}

/**
 * Get all active alerts
 */
export function getActiveAlerts(): Alert[] {
  return Array.from(activeAlerts.values());
}

/**
 * Get alert history with pagination
 */
export function getAlertHistory(
  page = 1,
  limit = 50,
): {
  alerts: Alert[];
  page: number;
  total: number;
  totalPages: number;
} {
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + limit;

  const sortedHistory = alertHistory.sort(
    (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime(),
  );

  const paginatedAlerts = sortedHistory.slice(startIndex, endIndex);

  return {
    alerts: paginatedAlerts,
    page,
    total: alertHistory.length,
    totalPages: Math.ceil(alertHistory.length / limit),
  };
}

/**
 * Get alert statistics
 */
export function getAlertStatistics(): {
  acknowledged: number;
  active: number;
  averageResolutionTime: number;
  bySeverity: Record<AlertSeverity, number>;
  byType: Record<AlertType, number>;
  recentTrends: {
    last24Hours: number;
    last7Days: number;
    last30Days: number;
  };
  resolved: number;
  total: number;
} {
  const active = activeAlerts.size;
  const resolved = alertHistory.filter(a => a.status === 'resolved').length;
  const acknowledged = alertHistory.filter(a => a.status === 'acknowledged').length;
  const total = active + resolved + acknowledged;

  const byType: Record<AlertType, number> = {
    business: 0,
    error: 0,
    health: 0,
    performance: 0,
    security: 0,
  };

  const bySeverity: Record<AlertSeverity, number> = {
    critical: 0,
    high: 0,
    low: 0,
    medium: 0,
  };

  // Calculate time-based statistics
  const now = new Date();
  const last24Hours = new Date(now.getTime() - 24 * 60 * 60 * 1000);
  const last7Days = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
  const last30Days = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

  let totalResolutionTime = 0;
  let resolvedCount = 0;
  let last24HoursCount = 0;
  let last7DaysCount = 0;
  let last30DaysCount = 0;

  for (const alert of alertHistory) {
    byType[alert.type]++;
    bySeverity[alert.severity]++;

    const alertDate = new Date(alert.createdAt);

    // Count alerts by time period
    if (alertDate >= last24Hours) last24HoursCount++;
    if (alertDate >= last7Days) last7DaysCount++;
    if (alertDate >= last30Days) last30DaysCount++;

    // Calculate resolution time for resolved alerts
    if (alert.status === 'resolved' && alert.resolvedAt) {
      const resolutionTime =
        new Date(alert.resolvedAt).getTime() - new Date(alert.createdAt).getTime();
      totalResolutionTime += resolutionTime;
      resolvedCount++;
    }
  }

  const averageResolutionTime =
    resolvedCount > 0 ? Math.round(totalResolutionTime / resolvedCount) : 0;

  return {
    acknowledged,
    active,
    averageResolutionTime,
    bySeverity,
    byType,
    recentTrends: {
      last24Hours: last24HoursCount,
      last7Days: last7DaysCount,
      last30Days: last30DaysCount,
    },
    resolved,
    total,
  };
}

/**
 * Initialize default alert rules and notification channels
 */
export function initializeAlertingSystem(): void {
  if (!ALERTS_ENABLED) {
    logger.info('Alerting system is disabled', { service: 'alerting' });
    return;
  }

  // Initialize default notification channels
  if (ALERT_WEBHOOK_URL) {
    notificationChannels.set('default-webhook', {
      config: { url: ALERT_WEBHOOK_URL },
      enabled: true,
      id: 'default-webhook',
      name: 'Default Webhook',
      type: 'webhook',
    });
  }

  if (ALERT_SLACK_WEBHOOK) {
    notificationChannels.set('slack', {
      config: { url: ALERT_SLACK_WEBHOOK },
      enabled: true,
      id: 'slack',
      name: 'Slack Notifications',
      type: 'slack',
    });
  }

  if (ALERT_EMAIL_RECIPIENTS.length > 0) {
    notificationChannels.set('email', {
      config: { recipients: ALERT_EMAIL_RECIPIENTS },
      enabled: true,
      id: 'email',
      name: 'Email Notifications',
      type: 'email',
    });
  }

  // Initialize default alert rules
  initializeDefaultAlertRules();

  logger.info('Alerting system initialized', {
    alertRules: Array.from(alertRules.keys()),
    alertsEnabled: ALERTS_ENABLED,
    notificationChannels: Array.from(notificationChannels.keys()),
    service: 'alerting',
  });
}

/**
 * Monitor system health and trigger alerts
 */
export async function monitorSystemHealth(): Promise<void> {
  if (!ALERTS_ENABLED) {
    return;
  }

  try {
    const healthReport = await getComprehensiveHealthReport();

    // Check for unhealthy components
    for (const [component, health] of Object.entries(healthReport.checks)) {
      if (health.status === 'unhealthy') {
        await createAlert(
          'health',
          'critical',
          `${component.charAt(0).toUpperCase() + component.slice(1)} Health Critical`,
          `${component} health check failed: ${health.message}`,
          health.details,
          {
            component,
            currentValue: 0,
            source: 'health-monitor',
            threshold: 1,
          },
        );
      } else if (health.status === 'degraded') {
        await createAlert(
          'health',
          'medium',
          `${component.charAt(0).toUpperCase() + component.slice(1)} Health Degraded`,
          `${component} health check degraded: ${health.message}`,
          health.details,
          {
            component,
            currentValue: 0.5,
            source: 'health-monitor',
            threshold: 1,
          },
        );
      }
    }

    // Check circuit breaker states
    const circuitBreakerStatus = circuitBreakerRegistry.getStatus();
    for (const [breakerName, status] of Object.entries(circuitBreakerStatus)) {
      if (
        status &&
        typeof status === 'object' &&
        'state' in status &&
        (status as any).state === 'open'
      ) {
        await createAlert(
          'health',
          'high',
          'Circuit Breaker Open',
          `Circuit breaker ${breakerName} is open`,
          status,
          {
            component: breakerName,
            currentValue: 1,
            source: 'circuit-breaker-monitor',
            threshold: 1,
          },
        );
      }
    }

    // Check system resources
    if (healthReport.checks.systemResources.details) {
      const { cpu, memory } = healthReport.checks.systemResources.details;

      if (memory.utilization > MEMORY_USAGE_ALERT_THRESHOLD) {
        await createAlert(
          'performance',
          memory.utilization > 95 ? 'critical' : 'medium',
          'High Memory Usage',
          `Memory usage is ${memory.utilization.toFixed(1)}%`,
          memory,
          {
            component: 'memory',
            currentValue: memory.utilization,
            source: 'resource-monitor',
            threshold: MEMORY_USAGE_ALERT_THRESHOLD,
          },
        );
      }

      if (cpu.utilization > CPU_USAGE_ALERT_THRESHOLD) {
        await createAlert(
          'performance',
          cpu.utilization > 95 ? 'critical' : 'medium',
          'High CPU Usage',
          `CPU usage is ${cpu.utilization.toFixed(1)}%`,
          cpu,
          {
            component: 'cpu',
            currentValue: cpu.utilization,
            source: 'resource-monitor',
            threshold: CPU_USAGE_ALERT_THRESHOLD,
          },
        );
      }
    }
  } catch (error) {
    logger.error('Failed to monitor system health for alerts', {
      error: error instanceof Error ? error.message : 'Unknown error',
      service: 'alerting',
    });
  }
}

/**
 * Resolve an alert
 */
export async function resolveAlert(alertId: string, resolvedBy?: string): Promise<Alert | null> {
  const alert = activeAlerts.get(alertId);
  if (!alert) {
    return null;
  }

  alert.status = 'resolved';
  alert.resolvedAt = new Date().toISOString();
  alert.updatedAt = new Date().toISOString();

  activeAlerts.delete(alertId);

  // Record alert resolution metric
  businessMetrics.recordUserOperation('alert_resolved', resolvedBy || 'system', 'success');

  logger.info('Alert resolved', {
    alertId,
    duration: new Date(alert.resolvedAt).getTime() - new Date(alert.createdAt).getTime(),
    resolvedBy,
    service: 'alerting',
  });

  return alert;
}

/**
 * Start periodic health monitoring
 */
export function startHealthMonitoring(): void {
  if (!ALERTS_ENABLED) {
    logger.info('Health monitoring disabled (alerts disabled)', {
      service: 'alerting',
    });
    return;
  }

  // Monitor health every 2 minutes
  setInterval(monitorSystemHealth, 120000);

  // Initial health check
  setTimeout(monitorSystemHealth, 10000); // Wait 10 seconds after startup

  logger.info('Health monitoring started', {
    interval: '2 minutes',
    service: 'alerting',
  });
}

/**
 * Test alert system by creating a test alert
 */
export async function testAlertSystem(): Promise<Alert> {
  return await createAlert(
    'health',
    'low',
    'Alert System Test',
    'This is a test alert to verify the alerting system is working correctly',
    {
      test: true,
      timestamp: new Date().toISOString(),
    },
    {
      component: 'alerting-system',
      currentValue: 1,
      source: 'test',
      threshold: 1,
    },
  );
}

/**
 * Initialize default alert rules
 */
function initializeDefaultAlertRules(): void {
  const defaultEscalationPolicy: EscalationPolicy = {
    escalationInterval: 15,
    levels: [
      { channels: ['default-webhook'], delay: 0, level: 1 },
      { channels: ['slack', 'email'], delay: 15, level: 2 },
      { channels: ['email'], delay: 60, level: 3 },
    ],
    maxEscalations: 3,
  };

  // High response time alert
  alertRules.set('high-response-time', {
    condition: 'response_time > threshold',
    enabled: true,
    escalationPolicy: defaultEscalationPolicy,
    evaluationInterval: 60,
    id: 'high-response-time',
    name: 'High Response Time',
    notificationChannels: Array.from(notificationChannels.values()),
    severity: 'medium',
    threshold: RESPONSE_TIME_ALERT_THRESHOLD,
    type: 'performance',
  });

  // High error rate alert
  alertRules.set('high-error-rate', {
    condition: 'error_rate > threshold',
    enabled: true,
    escalationPolicy: defaultEscalationPolicy,
    evaluationInterval: 30,
    id: 'high-error-rate',
    name: 'High Error Rate',
    notificationChannels: Array.from(notificationChannels.values()),
    severity: 'high',
    threshold: ERROR_RATE_ALERT_THRESHOLD,
    type: 'error',
  });

  // Circuit breaker open alert
  alertRules.set('circuit-breaker-open', {
    condition: 'circuit_breaker_state = open',
    enabled: true,
    escalationPolicy: defaultEscalationPolicy,
    evaluationInterval: 30,
    id: 'circuit-breaker-open',
    name: 'Circuit Breaker Open',
    notificationChannels: Array.from(notificationChannels.values()),
    severity: 'high',
    threshold: 1,
    type: 'health',
  });

  // High CPU usage alert
  alertRules.set('high-cpu-usage', {
    condition: 'cpu_usage > threshold',
    enabled: true,
    escalationPolicy: defaultEscalationPolicy,
    evaluationInterval: 120,
    id: 'high-cpu-usage',
    name: 'High CPU Usage',
    notificationChannels: Array.from(notificationChannels.values()),
    severity: 'medium',
    threshold: CPU_USAGE_ALERT_THRESHOLD,
    type: 'performance',
  });

  // High memory usage alert
  alertRules.set('high-memory-usage', {
    condition: 'memory_usage > threshold',
    enabled: true,
    escalationPolicy: defaultEscalationPolicy,
    evaluationInterval: 120,
    id: 'high-memory-usage',
    name: 'High Memory Usage',
    notificationChannels: Array.from(notificationChannels.values()),
    severity: 'medium',
    threshold: MEMORY_USAGE_ALERT_THRESHOLD,
    type: 'performance',
  });

  // Database connection failure alert
  alertRules.set('database-connection-failure', {
    condition: 'database_health = unhealthy',
    enabled: true,
    escalationPolicy: {
      ...defaultEscalationPolicy,
      levels: [{ channels: ['default-webhook', 'slack', 'email'], delay: 0, level: 1 }],
    },
    evaluationInterval: 30,
    id: 'database-connection-failure',
    name: 'Database Connection Failure',
    notificationChannels: Array.from(notificationChannels.values()),
    severity: 'critical',
    threshold: 1,
    type: 'health',
  });
}

/**
 * Send alert notification through configured channels
 */
async function sendAlertNotification(alert: Alert): Promise<void> {
  if (!ALERTS_ENABLED) {
    return;
  }

  const notificationPromises: Promise<void>[] = [];

  for (const channel of notificationChannels.values()) {
    if (!channel.enabled) continue;

    try {
      switch (channel.type) {
        case 'email':
          notificationPromises.push(sendEmailNotification(alert, channel));
          break;
        case 'slack':
          notificationPromises.push(sendSlackNotification(alert, channel));
          break;
        case 'webhook':
          notificationPromises.push(sendWebhookNotification(alert, channel));
          break;
      }
    } catch (error) {
      logger.error('Failed to send alert notification', {
        alertId: alert.id,
        channelId: channel.id,
        channelType: channel.type,
        error: error instanceof Error ? error.message : 'Unknown error',
        service: 'alerting',
      });
    }
  }

  await Promise.allSettled(notificationPromises);
}

/**
 * Send email notification (placeholder - would integrate with email service)
 */
async function sendEmailNotification(alert: Alert, channel: NotificationChannel): Promise<void> {
  // This is a placeholder implementation
  // In production, this would integrate with an email service like SendGrid, AWS SES, etc.

  logger.info('Email notification would be sent', {
    alertId: alert.id,
    recipients: channel.config.recipients,
    service: 'alerting',
    severity: alert.severity,
    subject: `WorkHub Alert: ${alert.title}`,
  });

  // Simulate email sending
  await new Promise(resolve => setTimeout(resolve, 100));
}

/**
 * Send Slack notification
 */
async function sendSlackNotification(alert: Alert, channel: NotificationChannel): Promise<void> {
  if (!channel.config.url) {
    throw new Error('Slack webhook URL not configured');
  }

  const severityEmoji = {
    critical: '🚨',
    high: '🔴',
    low: '🟡',
    medium: '🟠',
  };

  const payload = {
    attachments: [
      {
        color:
          alert.severity === 'critical' ? 'danger' : alert.severity === 'high' ? 'warning' : 'good',
        fields: [
          {
            short: true,
            title: 'Type',
            value: alert.type,
          },
          {
            short: true,
            title: 'Severity',
            value: alert.severity.toUpperCase(),
          },
          {
            short: false,
            title: 'Message',
            value: alert.message,
          },
          {
            short: true,
            title: 'Time',
            value: new Date(alert.createdAt).toLocaleString(),
          },
        ],
      },
    ],
    text: `${severityEmoji[alert.severity]} WorkHub Alert: ${alert.title}`,
  };

  const response = await fetch(channel.config.url, {
    body: JSON.stringify(payload),
    headers: {
      'Content-Type': 'application/json',
    },
    method: 'POST',
  });

  if (!response.ok) {
    throw new Error(`Slack notification failed: ${response.status} ${response.statusText}`);
  }

  logger.info('Slack notification sent', {
    alertId: alert.id,
    responseStatus: response.status,
    service: 'alerting',
  });
}

/**
 * Send webhook notification
 */
async function sendWebhookNotification(alert: Alert, channel: NotificationChannel): Promise<void> {
  if (!channel.config.url) {
    throw new Error('Webhook URL not configured');
  }

  const payload = {
    alert: {
      createdAt: alert.createdAt,
      id: alert.id,
      message: alert.message,
      metadata: alert.metadata,
      severity: alert.severity,
      status: alert.status,
      title: alert.title,
      type: alert.type,
    },
    source: 'WorkHub',
    timestamp: new Date().toISOString(),
  };

  const response = await fetch(channel.config.url, {
    body: JSON.stringify(payload),
    headers: {
      'Content-Type': 'application/json',
      'User-Agent': 'WorkHub-Alerting/1.0',
    },
    method: 'POST',
  });

  if (!response.ok) {
    throw new Error(`Webhook notification failed: ${response.status} ${response.statusText}`);
  }

  logger.info('Webhook notification sent', {
    alertId: alert.id,
    responseStatus: response.status,
    service: 'alerting',
    webhookUrl: channel.config.url,
  });
}

/**
 * Create security-specific alert for Phase 3 audit logging
 */
export async function createSecurityAlert(
  action: string,
  userId: string,
  severity: AlertSeverity,
  details: any,
  metadata?: any,
): Promise<Alert> {
  const title = `Security Alert: ${action}`;
  const message = `Security event detected: ${action} by user ${userId}`;

  return await createAlert('security', severity, title, message, details, {
    ...metadata,
    component: 'security-monitoring',
    source: 'audit-system',
    userId,
    securityEvent: true,
  });
}

/**
 * Process audit event for security alerting
 */
export async function processAuditEventForAlerting(auditEvent: any): Promise<void> {
  if (!ALERTS_ENABLED) {
    return;
  }

  try {
    // Check for multiple failed login attempts
    if (
      auditEvent.action?.includes('LOGIN_FAILURE') ||
      auditEvent.action?.includes('AUTHENTICATION_FAILURE')
    ) {
      await createSecurityAlert(
        'Multiple Failed Login Attempts',
        auditEvent.userId || 'Unknown',
        'high',
        auditEvent,
        { alertType: 'failed_login', riskLevel: auditEvent.riskLevel },
      );
    }

    // Check for privilege escalation attempts
    if (auditEvent.eventType === 'AUTHZ' && auditEvent.outcome === 'FAILURE') {
      await createSecurityAlert(
        'Authorization Failure',
        auditEvent.userId || 'Unknown',
        'medium',
        auditEvent,
        { alertType: 'authz_failure', riskLevel: auditEvent.riskLevel },
      );
    }

    // Check for admin actions outside business hours
    if (auditEvent.action?.includes('ADMIN_') && isOutsideBusinessHours()) {
      await createSecurityAlert(
        'Admin Action Outside Business Hours',
        auditEvent.userId || 'Unknown',
        'medium',
        auditEvent,
        { alertType: 'after_hours_admin', riskLevel: 'MEDIUM' },
      );
    }

    // Check for high-risk events
    if (auditEvent.riskLevel === 'HIGH' || auditEvent.riskLevel === 'CRITICAL') {
      await createSecurityAlert(
        `High Risk Security Event: ${auditEvent.action}`,
        auditEvent.userId || 'Unknown',
        auditEvent.riskLevel === 'CRITICAL' ? 'critical' : 'high',
        auditEvent,
        { alertType: 'high_risk_event', riskLevel: auditEvent.riskLevel },
      );
    }

    // Check for unusual data access patterns
    if (auditEvent.eventType === 'DATA_ACCESS' && auditEvent.personalDataInvolved) {
      await createSecurityAlert(
        'Personal Data Access',
        auditEvent.userId || 'Unknown',
        'low',
        auditEvent,
        { alertType: 'personal_data_access', gdprRelevant: true },
      );
    }
  } catch (error) {
    logger.error('Failed to process audit event for alerting:', error);
  }
}

/**
 * Check if current time is outside business hours (9 AM - 6 PM weekdays)
 */
function isOutsideBusinessHours(): boolean {
  const now = new Date();
  const hour = now.getHours();
  const day = now.getDay(); // 0 = Sunday, 6 = Saturday

  // Weekend or outside 9 AM - 6 PM
  return day === 0 || day === 6 || hour < 9 || hour >= 18;
}

// Initialize the alerting system
initializeAlertingSystem();
