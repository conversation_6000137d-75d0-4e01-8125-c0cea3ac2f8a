{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/ui/progress.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as ProgressPrimitive from '@radix-ui/react-progress';\r\nimport * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst Progress = React.forwardRef<\r\n  React.ElementRef<typeof ProgressPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>\r\n>(({ className, value, ...props }, ref) => (\r\n  <ProgressPrimitive.Root\r\n    className={cn(\r\n      'relative h-4 w-full overflow-hidden rounded-full bg-secondary',\r\n      className\r\n    )}\r\n    ref={ref}\r\n    {...props}\r\n  >\r\n    <ProgressPrimitive.Indicator\r\n      className=\"size-full flex-1 bg-primary transition-all\"\r\n      style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\r\n    />\r\n  </ProgressPrimitive.Root>\r\n));\r\nProgress.displayName = ProgressPrimitive.Root.displayName;\r\n\r\nexport { Progress };\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AAAA;AALA;;;;;AAOA,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG9B,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,6LAAC,uKAAA,CAAA,OAAsB;QACrB,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAEF,KAAK;QACJ,GAAG,KAAK;kBAET,cAAA,6LAAC,uKAAA,CAAA,YAA2B;YAC1B,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;;AAIhE,SAAS,WAAW,GAAG,uKAAA,CAAA,OAAsB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 54, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/lib/services/WebSocketManager.ts"], "sourcesContent": ["/**\r\n * @file Unified WebSocket Manager for WorkHub Application\r\n * Provides centralized WebSocket connection management with domain-specific channels\r\n * Follows SRP and DRY principles with smart fallback strategies\r\n * @module services/WebSocketManager\r\n */\r\n\r\nimport type { Socket } from 'socket.io-client';\r\n\r\nimport { io } from 'socket.io-client';\r\n\r\nimport { getEnvironmentConfig } from '../config/environment';\r\nimport { supabase } from '../supabase';\r\nimport { getTokenRefreshService } from './TokenRefreshService';\r\n/*import logger from '../utils/logger';\r\n\r\n/**\r\n * WebSocket connection states\r\n */\r\nexport type ConnectionState =\r\n  | 'connected'\r\n  | 'connecting'\r\n  | 'disconnected'\r\n  | 'error'\r\n  | 'reconnecting';\r\n\r\n/**\r\n * Domain-specific channels for organized event management\r\n */\r\nexport type DomainChannel = 'crud' | 'notifications' | 'reliability' | 'system';\r\n\r\n/**\r\n * Event subscription callback type\r\n */\r\nexport type EventCallback<T = any> = (data: T) => void;\r\n\r\n/**\r\n * WebSocket configuration options\r\n */\r\nexport interface WebSocketConfig {\r\n  autoConnect?: boolean;\r\n  reconnectAttempts?: number;\r\n  reconnectDelay?: number;\r\n  timeout?: number;\r\n  url?: string;\r\n}\r\n\r\n/**\r\n * Unified WebSocket Manager\r\n * Implements Singleton pattern for single connection per application\r\n * Provides domain-specific channels and centralized subscription management\r\n */\r\nexport class WebSocketManager {\r\n  private static instance: null | WebSocketManager = null;\r\n  private readonly config: Required<WebSocketConfig>;\r\n  private connectionState: ConnectionState = 'disconnected';\r\n  private reconnectAttempts = 0;\r\n  private socket: null | Socket = null;\r\n  private readonly stateListeners = new Set<(state: ConnectionState) => void>();\r\n  private readonly subscriptions = new Map<string, Set<EventCallback>>();\r\n\r\n  private constructor(config: WebSocketConfig = {}) {\r\n    this.config = {\r\n      autoConnect: config.autoConnect ?? true,\r\n      reconnectAttempts: config.reconnectAttempts ?? 5,\r\n      reconnectDelay: config.reconnectDelay ?? 1000,\r\n      timeout: config.timeout ?? 10_000,\r\n      url:\r\n        config.url ??\r\n        process.env.NEXT_PUBLIC_WEBSOCKET_URL ??\r\n        getEnvironmentConfig()\r\n          .wsUrl.replace('ws://', 'http://')\r\n          .replace('wss://', 'https://'),\r\n    };\r\n\r\n    if (this.config.autoConnect) {\r\n      this.connect();\r\n    }\r\n\r\n    // Subscribe to token refresh events\r\n    this.setupTokenRefreshHandling();\r\n  }\r\n\r\n  /**\r\n   * Get singleton instance\r\n   */\r\n  public static getInstance(config?: WebSocketConfig): WebSocketManager {\r\n    WebSocketManager.instance ??= new WebSocketManager(config);\r\n    return WebSocketManager.instance;\r\n  }\r\n\r\n  /**\r\n   * Connect to WebSocket server\r\n   */\r\n  public async connect(): Promise<void> {\r\n    if (this.socket?.connected) {\r\n      console.debug('WebSocket already connected');\r\n      return;\r\n    }\r\n\r\n    this.setConnectionState('connecting');\r\n\r\n    try {\r\n      // Get current session and token for authentication\r\n      const {\r\n        data: { session },\r\n        error,\r\n      } = await supabase.auth.getSession();\r\n\r\n      if (error) {\r\n        console.warn('Failed to get session for WebSocket connection:', error);\r\n      }\r\n\r\n      const connectionOptions: any = {\r\n        forceNew: true,\r\n        timeout: this.config.timeout,\r\n        transports: ['websocket', 'polling'],\r\n        withCredentials: true, // Ensure cookies are sent with WebSocket handshake\r\n      };\r\n\r\n      // Add authentication token if available\r\n      if (session?.access_token) {\r\n        connectionOptions.auth = {\r\n          token: session.access_token,\r\n        };\r\n        console.debug('🔐 WebSocket connecting with authentication token');\r\n\r\n        // Validate token expiration\r\n        const tokenExpiry = session.expires_at ? session.expires_at * 1000 : 0;\r\n        const now = Date.now();\r\n        const timeUntilExpiry = tokenExpiry - now;\r\n\r\n        if (timeUntilExpiry <= 60_000) {\r\n          // Less than 1 minute\r\n          console.warn('⚠️ WebSocket token expires soon, may need refresh');\r\n        }\r\n      } else {\r\n        console.warn(\r\n          '⚠️ WebSocket connecting without authentication token - connection may fail'\r\n        );\r\n      }\r\n\r\n      this.socket = io(this.config.url, connectionOptions);\r\n\r\n      this.setupEventHandlers();\r\n    } catch (error) {\r\n      console.error('Failed to connect WebSocket:', error);\r\n      this.setConnectionState('error');\r\n      this.scheduleReconnect();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Cleanup resources\r\n   */\r\n  public destroy(): void {\r\n    this.disconnect();\r\n    this.subscriptions.clear();\r\n    this.stateListeners.clear();\r\n    WebSocketManager.instance = null;\r\n  }\r\n\r\n  /**\r\n   * Disconnect from WebSocket server\r\n   */\r\n  public disconnect(): void {\r\n    if (this.socket) {\r\n      this.socket.disconnect();\r\n      this.socket = null;\r\n    }\r\n    this.setConnectionState('disconnected');\r\n    this.reconnectAttempts = 0;\r\n  }\r\n\r\n  /**\r\n   * Emit event to specific domain channel\r\n   */\r\n  public emit(channel: DomainChannel, event: string, data?: any): void {\r\n    if (!this.socket?.connected) {\r\n      console.warn(`Cannot emit ${channel}:${event} - WebSocket not connected`);\r\n      return;\r\n    }\r\n\r\n    this.socket.emit(event, data);\r\n  }\r\n\r\n  /**\r\n   * Get current connection state\r\n   */\r\n  public getConnectionState(): ConnectionState {\r\n    return this.connectionState;\r\n  }\r\n\r\n  /**\r\n   * Check if WebSocket is connected\r\n   */\r\n  public isConnected(): boolean {\r\n    return (\r\n      this.connectionState === 'connected' && this.socket?.connected === true\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Join domain-specific room\r\n   */\r\n  public joinRoom(room: string): void {\r\n    if (!this.socket?.connected) {\r\n      console.warn(`Cannot join room ${room} - WebSocket not connected`);\r\n      return;\r\n    }\r\n\r\n    this.socket.emit('join-room', room);\r\n  }\r\n\r\n  /**\r\n   * Leave domain-specific room\r\n   */\r\n  public leaveRoom(room: string): void {\r\n    if (!this.socket?.connected) {\r\n      return;\r\n    }\r\n\r\n    this.socket.emit('leave-room', room);\r\n  }\r\n\r\n  /**\r\n   * Subscribe to connection state changes\r\n   */\r\n  public onStateChange(callback: (state: ConnectionState) => void): () => void {\r\n    this.stateListeners.add(callback);\r\n\r\n    return () => {\r\n      this.stateListeners.delete(callback);\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Subscribe to domain-specific events\r\n   */\r\n  public subscribe<T = any>(\r\n    channel: DomainChannel,\r\n    event: string,\r\n    callback: EventCallback<T>\r\n  ): () => void {\r\n    const eventKey = `${channel}:${event}`;\r\n\r\n    if (!this.subscriptions.has(eventKey)) {\r\n      this.subscriptions.set(eventKey, new Set());\r\n    }\r\n\r\n    this.subscriptions.get(eventKey)!.add(callback);\r\n\r\n    // Set up socket listener if connected\r\n    if (this.socket?.connected && event) {\r\n      this.socket.on(event, callback);\r\n    }\r\n\r\n    // Return unsubscribe function\r\n    return () => {\r\n      const callbacks = this.subscriptions.get(eventKey);\r\n      if (callbacks) {\r\n        callbacks.delete(callback);\r\n        if (callbacks.size === 0) {\r\n          this.subscriptions.delete(eventKey);\r\n        }\r\n      }\r\n\r\n      if (this.socket && event) {\r\n        this.socket.off(event, callback);\r\n      }\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Handle authentication errors by triggering token refresh\r\n   */\r\n  private handleAuthenticationError(): void {\r\n    const tokenRefreshService = getTokenRefreshService();\r\n\r\n    console.log('🔐 Handling WebSocket authentication error...');\r\n\r\n    // Disconnect current socket to prevent further auth errors\r\n    if (this.socket) {\r\n      this.socket.disconnect();\r\n      this.socket = null;\r\n    }\r\n\r\n    // Attempt to refresh token\r\n    tokenRefreshService\r\n      .refreshNow()\r\n      .then(success => {\r\n        if (success) {\r\n          console.log(\r\n            '🔄 Token refresh successful, retrying WebSocket connection'\r\n          );\r\n          // The reconnection will be handled by setupTokenRefreshHandling\r\n        } else {\r\n          console.error('🔄 Token refresh failed, scheduling normal reconnect');\r\n          this.scheduleReconnect();\r\n        }\r\n      })\r\n      .catch(error => {\r\n        console.error('🔄 Token refresh error:', error);\r\n        this.scheduleReconnect();\r\n      });\r\n  }\r\n\r\n  /**\r\n   * Resubscribe to all events after reconnection\r\n   */\r\n  private resubscribeToEvents(): void {\r\n    if (!this.socket) return;\r\n\r\n    for (const [eventKey, callbacks] of this.subscriptions) {\r\n      const [, event] = eventKey.split(':');\r\n      for (const callback of callbacks) {\r\n        if (event) {\r\n          this.socket!.on(event, callback);\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Schedule reconnection with exponential backoff\r\n   */\r\n  private scheduleReconnect(): void {\r\n    if (this.reconnectAttempts >= this.config.reconnectAttempts) {\r\n      console.error('Max reconnection attempts reached');\r\n      this.setConnectionState('error');\r\n      return;\r\n    }\r\n\r\n    this.setConnectionState('reconnecting');\r\n    this.reconnectAttempts++;\r\n\r\n    const delay =\r\n      this.config.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);\r\n\r\n    setTimeout(() => {\r\n      console.info(\r\n        `Attempting to reconnect (${this.reconnectAttempts}/${this.config.reconnectAttempts})`\r\n      );\r\n      this.connect();\r\n    }, delay);\r\n  }\r\n\r\n  /**\r\n   * Set connection state and notify listeners\r\n   */\r\n  private setConnectionState(state: ConnectionState): void {\r\n    if (this.connectionState !== state) {\r\n      this.connectionState = state;\r\n      for (const listener of this.stateListeners) listener(state);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Setup socket event handlers\r\n   */\r\n  private setupEventHandlers(): void {\r\n    if (!this.socket) return;\r\n\r\n    this.socket.on('connect', () => {\r\n      console.info('WebSocket connected');\r\n      this.setConnectionState('connected');\r\n      this.reconnectAttempts = 0;\r\n      this.resubscribeToEvents();\r\n    });\r\n\r\n    this.socket.on('disconnect', reason => {\r\n      console.warn('WebSocket disconnected:', reason);\r\n      this.setConnectionState('disconnected');\r\n\r\n      if (reason === 'io server disconnect') {\r\n        // Server initiated disconnect, don't reconnect automatically\r\n        return;\r\n      }\r\n\r\n      this.scheduleReconnect();\r\n    });\r\n\r\n    this.socket.on('connect_error', error => {\r\n      console.error('WebSocket connection error:', error);\r\n      this.setConnectionState('error');\r\n\r\n      // Check if error is authentication-related\r\n      if (\r\n        error.message?.includes('Authentication') ||\r\n        error.message?.includes('token') ||\r\n        error.message?.includes('No token provided') ||\r\n        error.message?.includes('Unauthorized')\r\n      ) {\r\n        console.warn(\r\n          '🔐 Authentication error detected, attempting token refresh'\r\n        );\r\n        this.handleAuthenticationError();\r\n      } else {\r\n        this.scheduleReconnect();\r\n      }\r\n    });\r\n\r\n    // Listen for authentication errors from the server\r\n    this.socket.on('auth_error', errorData => {\r\n      console.error('🔐 Server authentication error:', errorData);\r\n      this.handleAuthenticationError();\r\n    });\r\n\r\n    // Listen for token refresh requests from server\r\n    this.socket.on('token_refresh_required', () => {\r\n      console.warn('🔄 Server requested token refresh');\r\n      this.handleAuthenticationError();\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Setup token refresh event handling\r\n   */\r\n  private setupTokenRefreshHandling(): void {\r\n    const tokenRefreshService = getTokenRefreshService();\r\n\r\n    tokenRefreshService.subscribe((event, _data) => {\r\n      switch (event) {\r\n        case 'critical_refresh_failed': {\r\n          console.error(\r\n            '🔄 Critical token refresh failure, disconnecting WebSocket'\r\n          );\r\n          this.disconnect();\r\n          this.setConnectionState('error');\r\n          break;\r\n        }\r\n\r\n        case 'refresh_failed': {\r\n          console.error(\r\n            '🔄 Token refresh failed, WebSocket may lose connection'\r\n          );\r\n          break;\r\n        }\r\n\r\n        case 'refresh_success': {\r\n          console.log(\r\n            '🔄 Token refreshed, reconnecting WebSocket with new token'\r\n          );\r\n          // Disconnect current connection and reconnect with new token\r\n          if (this.socket) {\r\n            this.socket.disconnect();\r\n            this.socket = null;\r\n          }\r\n          // Reconnect with fresh token\r\n          setTimeout(() => this.connect(), 500);\r\n          break;\r\n        }\r\n      }\r\n    });\r\n  }\r\n}\r\n\r\n/**\r\n * Get the singleton WebSocket manager instance\r\n */\r\nexport const getWebSocketManager = (\r\n  config?: WebSocketConfig\r\n): WebSocketManager => {\r\n  return WebSocketManager.getInstance(config);\r\n};\r\n\r\n/**\r\n * Hook for WebSocket connection state\r\n */\r\nexport const useWebSocketState = () => {\r\n  const manager = getWebSocketManager();\r\n  return {\r\n    connectionState: manager.getConnectionState(),\r\n    isConnected: manager.isConnected(),\r\n  };\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;;AAgEO;AA5DR;AAAA;AAEA;AACA;AACA;;;;;AAuCO,MAAM;IACX,OAAe,WAAoC,KAAK;IACvC,OAAkC;IAC3C,kBAAmC,eAAe;IAClD,oBAAoB,EAAE;IACtB,SAAwB,KAAK;IACpB,iBAAiB,IAAI,MAAwC;IAC7D,gBAAgB,IAAI,MAAkC;IAEvE,YAAoB,SAA0B,CAAC,CAAC,CAAE;QAChD,IAAI,CAAC,MAAM,GAAG;YACZ,aAAa,OAAO,WAAW,IAAI;YACnC,mBAAmB,OAAO,iBAAiB,IAAI;YAC/C,gBAAgB,OAAO,cAAc,IAAI;YACzC,SAAS,OAAO,OAAO,IAAI;YAC3B,KACE,OAAO,GAAG,IACV,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,yBAAyB,IACrC,CAAA,GAAA,sIAAA,CAAA,uBAAoB,AAAD,IAChB,KAAK,CAAC,OAAO,CAAC,SAAS,WACvB,OAAO,CAAC,UAAU;QACzB;QAEA,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;YAC3B,IAAI,CAAC,OAAO;QACd;QAEA,oCAAoC;QACpC,IAAI,CAAC,yBAAyB;IAChC;IAEA;;GAEC,GACD,OAAc,YAAY,MAAwB,EAAoB;QACpE,iBAAiB,QAAQ,KAAK,IAAI,iBAAiB;QACnD,OAAO,iBAAiB,QAAQ;IAClC;IAEA;;GAEC,GACD,MAAa,UAAyB;QACpC,IAAI,IAAI,CAAC,MAAM,EAAE,WAAW;YAC1B,QAAQ,KAAK,CAAC;YACd;QACF;QAEA,IAAI,CAAC,kBAAkB,CAAC;QAExB,IAAI;YACF,mDAAmD;YACnD,MAAM,EACJ,MAAM,EAAE,OAAO,EAAE,EACjB,KAAK,EACN,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,UAAU;YAElC,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,mDAAmD;YAClE;YAEA,MAAM,oBAAyB;gBAC7B,UAAU;gBACV,SAAS,IAAI,CAAC,MAAM,CAAC,OAAO;gBAC5B,YAAY;oBAAC;oBAAa;iBAAU;gBACpC,iBAAiB;YACnB;YAEA,wCAAwC;YACxC,IAAI,SAAS,cAAc;gBACzB,kBAAkB,IAAI,GAAG;oBACvB,OAAO,QAAQ,YAAY;gBAC7B;gBACA,QAAQ,KAAK,CAAC;gBAEd,4BAA4B;gBAC5B,MAAM,cAAc,QAAQ,UAAU,GAAG,QAAQ,UAAU,GAAG,OAAO;gBACrE,MAAM,MAAM,KAAK,GAAG;gBACpB,MAAM,kBAAkB,cAAc;gBAEtC,IAAI,mBAAmB,QAAQ;oBAC7B,qBAAqB;oBACrB,QAAQ,IAAI,CAAC;gBACf;YACF,OAAO;gBACL,QAAQ,IAAI,CACV;YAEJ;YAEA,IAAI,CAAC,MAAM,GAAG,CAAA,GAAA,kLAAA,CAAA,KAAE,AAAD,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE;YAElC,IAAI,CAAC,kBAAkB;QACzB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,IAAI,CAAC,kBAAkB,CAAC;YACxB,IAAI,CAAC,iBAAiB;QACxB;IACF;IAEA;;GAEC,GACD,AAAO,UAAgB;QACrB,IAAI,CAAC,UAAU;QACf,IAAI,CAAC,aAAa,CAAC,KAAK;QACxB,IAAI,CAAC,cAAc,CAAC,KAAK;QACzB,iBAAiB,QAAQ,GAAG;IAC9B;IAEA;;GAEC,GACD,AAAO,aAAmB;QACxB,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,UAAU;YACtB,IAAI,CAAC,MAAM,GAAG;QAChB;QACA,IAAI,CAAC,kBAAkB,CAAC;QACxB,IAAI,CAAC,iBAAiB,GAAG;IAC3B;IAEA;;GAEC,GACD,AAAO,KAAK,OAAsB,EAAE,KAAa,EAAE,IAAU,EAAQ;QACnE,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,WAAW;YAC3B,QAAQ,IAAI,CAAC,CAAC,YAAY,EAAE,QAAQ,CAAC,EAAE,MAAM,0BAA0B,CAAC;YACxE;QACF;QAEA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO;IAC1B;IAEA;;GAEC,GACD,AAAO,qBAAsC;QAC3C,OAAO,IAAI,CAAC,eAAe;IAC7B;IAEA;;GAEC,GACD,AAAO,cAAuB;QAC5B,OACE,IAAI,CAAC,eAAe,KAAK,eAAe,IAAI,CAAC,MAAM,EAAE,cAAc;IAEvE;IAEA;;GAEC,GACD,AAAO,SAAS,IAAY,EAAQ;QAClC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,WAAW;YAC3B,QAAQ,IAAI,CAAC,CAAC,iBAAiB,EAAE,KAAK,0BAA0B,CAAC;YACjE;QACF;QAEA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa;IAChC;IAEA;;GAEC,GACD,AAAO,UAAU,IAAY,EAAQ;QACnC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,WAAW;YAC3B;QACF;QAEA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc;IACjC;IAEA;;GAEC,GACD,AAAO,cAAc,QAA0C,EAAc;QAC3E,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC;QAExB,OAAO;YACL,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC;QAC7B;IACF;IAEA;;GAEC,GACD,AAAO,UACL,OAAsB,EACtB,KAAa,EACb,QAA0B,EACd;QACZ,MAAM,WAAW,GAAG,QAAQ,CAAC,EAAE,OAAO;QAEtC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,WAAW;YACrC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,UAAU,IAAI;QACvC;QAEA,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,UAAW,GAAG,CAAC;QAEtC,sCAAsC;QACtC,IAAI,IAAI,CAAC,MAAM,EAAE,aAAa,OAAO;YACnC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,OAAO;QACxB;QAEA,8BAA8B;QAC9B,OAAO;YACL,MAAM,YAAY,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC;YACzC,IAAI,WAAW;gBACb,UAAU,MAAM,CAAC;gBACjB,IAAI,UAAU,IAAI,KAAK,GAAG;oBACxB,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;gBAC5B;YACF;YAEA,IAAI,IAAI,CAAC,MAAM,IAAI,OAAO;gBACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO;YACzB;QACF;IACF;IAEA;;GAEC,GACD,AAAQ,4BAAkC;QACxC,MAAM,sBAAsB,CAAA,GAAA,gJAAA,CAAA,yBAAsB,AAAD;QAEjD,QAAQ,GAAG,CAAC;QAEZ,2DAA2D;QAC3D,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,UAAU;YACtB,IAAI,CAAC,MAAM,GAAG;QAChB;QAEA,2BAA2B;QAC3B,oBACG,UAAU,GACV,IAAI,CAAC,CAAA;YACJ,IAAI,SAAS;gBACX,QAAQ,GAAG,CACT;YAEF,gEAAgE;YAClE,OAAO;gBACL,QAAQ,KAAK,CAAC;gBACd,IAAI,CAAC,iBAAiB;YACxB;QACF,GACC,KAAK,CAAC,CAAA;YACL,QAAQ,KAAK,CAAC,2BAA2B;YACzC,IAAI,CAAC,iBAAiB;QACxB;IACJ;IAEA;;GAEC,GACD,AAAQ,sBAA4B;QAClC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;QAElB,KAAK,MAAM,CAAC,UAAU,UAAU,IAAI,IAAI,CAAC,aAAa,CAAE;YACtD,MAAM,GAAG,MAAM,GAAG,SAAS,KAAK,CAAC;YACjC,KAAK,MAAM,YAAY,UAAW;gBAChC,IAAI,OAAO;oBACT,IAAI,CAAC,MAAM,CAAE,EAAE,CAAC,OAAO;gBACzB;YACF;QACF;IACF;IAEA;;GAEC,GACD,AAAQ,oBAA0B;QAChC,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAAE;YAC3D,QAAQ,KAAK,CAAC;YACd,IAAI,CAAC,kBAAkB,CAAC;YACxB;QACF;QAEA,IAAI,CAAC,kBAAkB,CAAC;QACxB,IAAI,CAAC,iBAAiB;QAEtB,MAAM,QACJ,IAAI,CAAC,MAAM,CAAC,cAAc,GAAG,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,iBAAiB,GAAG;QAEpE,WAAW;YACT,QAAQ,IAAI,CACV,CAAC,yBAAyB,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC,CAAC;YAExF,IAAI,CAAC,OAAO;QACd,GAAG;IACL;IAEA;;GAEC,GACD,AAAQ,mBAAmB,KAAsB,EAAQ;QACvD,IAAI,IAAI,CAAC,eAAe,KAAK,OAAO;YAClC,IAAI,CAAC,eAAe,GAAG;YACvB,KAAK,MAAM,YAAY,IAAI,CAAC,cAAc,CAAE,SAAS;QACvD;IACF;IAEA;;GAEC,GACD,AAAQ,qBAA2B;QACjC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;QAElB,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,WAAW;YACxB,QAAQ,IAAI,CAAC;YACb,IAAI,CAAC,kBAAkB,CAAC;YACxB,IAAI,CAAC,iBAAiB,GAAG;YACzB,IAAI,CAAC,mBAAmB;QAC1B;QAEA,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,cAAc,CAAA;YAC3B,QAAQ,IAAI,CAAC,2BAA2B;YACxC,IAAI,CAAC,kBAAkB,CAAC;YAExB,IAAI,WAAW,wBAAwB;gBACrC,6DAA6D;gBAC7D;YACF;YAEA,IAAI,CAAC,iBAAiB;QACxB;QAEA,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,iBAAiB,CAAA;YAC9B,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,IAAI,CAAC,kBAAkB,CAAC;YAExB,2CAA2C;YAC3C,IACE,MAAM,OAAO,EAAE,SAAS,qBACxB,MAAM,OAAO,EAAE,SAAS,YACxB,MAAM,OAAO,EAAE,SAAS,wBACxB,MAAM,OAAO,EAAE,SAAS,iBACxB;gBACA,QAAQ,IAAI,CACV;gBAEF,IAAI,CAAC,yBAAyB;YAChC,OAAO;gBACL,IAAI,CAAC,iBAAiB;YACxB;QACF;QAEA,mDAAmD;QACnD,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,cAAc,CAAA;YAC3B,QAAQ,KAAK,CAAC,mCAAmC;YACjD,IAAI,CAAC,yBAAyB;QAChC;QAEA,gDAAgD;QAChD,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,0BAA0B;YACvC,QAAQ,IAAI,CAAC;YACb,IAAI,CAAC,yBAAyB;QAChC;IACF;IAEA;;GAEC,GACD,AAAQ,4BAAkC;QACxC,MAAM,sBAAsB,CAAA,GAAA,gJAAA,CAAA,yBAAsB,AAAD;QAEjD,oBAAoB,SAAS,CAAC,CAAC,OAAO;YACpC,OAAQ;gBACN,KAAK;oBAA2B;wBAC9B,QAAQ,KAAK,CACX;wBAEF,IAAI,CAAC,UAAU;wBACf,IAAI,CAAC,kBAAkB,CAAC;wBACxB;oBACF;gBAEA,KAAK;oBAAkB;wBACrB,QAAQ,KAAK,CACX;wBAEF;oBACF;gBAEA,KAAK;oBAAmB;wBACtB,QAAQ,GAAG,CACT;wBAEF,6DAA6D;wBAC7D,IAAI,IAAI,CAAC,MAAM,EAAE;4BACf,IAAI,CAAC,MAAM,CAAC,UAAU;4BACtB,IAAI,CAAC,MAAM,GAAG;wBAChB;wBACA,6BAA6B;wBAC7B,WAAW,IAAM,IAAI,CAAC,OAAO,IAAI;wBACjC;oBACF;YACF;QACF;IACF;AACF;AAKO,MAAM,sBAAsB,CACjC;IAEA,OAAO,iBAAiB,WAAW,CAAC;AACtC;AAKO,MAAM,oBAAoB;IAC/B,MAAM,UAAU;IAChB,OAAO;QACL,iBAAiB,QAAQ,kBAAkB;QAC3C,aAAa,QAAQ,WAAW;IAClC;AACF", "debugId": null}}, {"offset": {"line": 393, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/hooks/api/useSmartQuery.ts"], "sourcesContent": ["/**\r\n * @file Smart Query Hook with WebSocket Integration\r\n * Automatically disables polling when WebSocket is connected\r\n * Follows modern best practices for real-time data management\r\n * @module hooks/useSmartQuery\r\n */\r\n\r\nimport type { UseQueryOptions, UseQueryResult } from '@tanstack/react-query';\r\n\r\nimport { useQuery, useQueryClient } from '@tanstack/react-query';\r\nimport { useEffect, useState } from 'react';\r\n\r\nimport type { DomainChannel } from '../../lib/services/WebSocketManager';\r\n\r\nimport { getWebSocketManager } from '../../lib/services/WebSocketManager';\r\n\r\n/**\r\n * Mapping of domain channels to Socket.IO room names\r\n * This ensures the frontend joins the correct rooms that the backend emits events to\r\n */\r\nconst CHANNEL_ROOM_MAPPING: Record<DomainChannel, string> = {\r\n  crud: 'entity-updates',\r\n  notifications: 'notifications-monitoring',\r\n  reliability: 'reliability-monitoring',\r\n  system: 'system-monitoring',\r\n} as const;\r\n\r\n/**\r\n * Smart query configuration for WebSocket integration\r\n * @template T - The data type returned by the query function\r\n */\r\nexport interface SmartQueryConfig<T = unknown> {\r\n  /**\r\n   * Domain channel for WebSocket events\r\n   * Automatically maps to appropriate Socket.IO room via CHANNEL_ROOM_MAPPING\r\n   */\r\n  channel: DomainChannel;\r\n  /** Whether to enable fallback polling when WebSocket is disconnected */\r\n  enableFallback?: boolean;\r\n  /** Whether to enable WebSocket integration and room joining */\r\n  enableWebSocket?: boolean;\r\n  /** Events that should trigger data refetch when received via WebSocket */\r\n  events: string[];\r\n  /** Fallback polling interval when WebSocket is disconnected (ms) */\r\n  fallbackInterval?: number;\r\n}\r\n\r\n/**\r\n * Hook for CRUD operations with smart real-time updates\r\n */\r\nexport function useCrudQuery<T = unknown, E = Error>(\r\n  queryKey: unknown[],\r\n  queryFn: () => Promise<T>,\r\n  entityType: string,\r\n  options?: Omit<UseQueryOptions<T, E>, 'queryFn' | 'queryKey'>\r\n) {\r\n  return useSmartQuery(\r\n    queryKey,\r\n    queryFn,\r\n    {\r\n      channel: 'crud',\r\n      events: [\r\n        `${entityType}:created`,\r\n        `${entityType}:updated`,\r\n        `${entityType}:deleted`,\r\n        `refresh:${entityType}`,\r\n      ],\r\n      fallbackInterval: 30_000,\r\n    },\r\n    options\r\n  );\r\n}\r\n\r\n/**\r\n * Hook for system notifications with smart real-time updates\r\n */\r\nexport function useNotificationQuery<T = unknown, E = Error>(\r\n  queryKey: unknown[],\r\n  queryFn: () => Promise<T>,\r\n  options?: Omit<UseQueryOptions<T, E>, 'queryFn' | 'queryKey'>\r\n) {\r\n  return useSmartQuery(\r\n    queryKey,\r\n    queryFn,\r\n    {\r\n      channel: 'notifications',\r\n      events: ['notification-created', 'notification-updated'],\r\n      fallbackInterval: 60_000, // 1 minute for notifications\r\n    },\r\n    options\r\n  );\r\n}\r\n\r\n/**\r\n * Hook for reliability monitoring with smart real-time updates\r\n */\r\nexport function useReliabilityQuery<T = unknown, E = Error>(\r\n  queryKey: unknown[],\r\n  queryFn: () => Promise<T>,\r\n  monitoringType: 'alerts' | 'circuit-breakers' | 'health' | 'metrics',\r\n  options?: Omit<UseQueryOptions<T, E>, 'queryFn' | 'queryKey'>\r\n) {\r\n  // Increased intervals to reduce aggressive polling and cancellations\r\n  const intervalMap = {\r\n    alerts: 30_000, // 30 seconds for alerts (was 10s)\r\n    'circuit-breakers': 60_000, // 60 seconds for circuit breakers (was 30s)\r\n    health: 45_000, // 45 seconds for health (was 15s)\r\n    metrics: 60_000, // 60 seconds for metrics (was 30s)\r\n  };\r\n\r\n  const webSocketManager = getWebSocketManager();\r\n\r\n  // Join reliability monitoring room when WebSocket is connected\r\n  useEffect(() => {\r\n    if (webSocketManager.isConnected()) {\r\n      console.debug(\r\n        `[ReliabilityQuery] Joining reliability-monitoring room for ${monitoringType}`\r\n      );\r\n      webSocketManager.joinRoom('reliability-monitoring');\r\n    }\r\n\r\n    // Subscribe to connection state changes to join room when connected\r\n    const unsubscribe = webSocketManager.onStateChange(state => {\r\n      if (state === 'connected') {\r\n        console.debug(\r\n          `[ReliabilityQuery] WebSocket connected, joining reliability-monitoring room for ${monitoringType}`\r\n        );\r\n        webSocketManager.joinRoom('reliability-monitoring');\r\n      }\r\n    });\r\n\r\n    return () => {\r\n      unsubscribe();\r\n      // Leave room when component unmounts\r\n      if (webSocketManager.isConnected()) {\r\n        webSocketManager.leaveRoom('reliability-monitoring');\r\n      }\r\n    };\r\n  }, [webSocketManager, monitoringType]);\r\n\r\n  return useSmartQuery(\r\n    queryKey,\r\n    queryFn,\r\n    {\r\n      channel: 'reliability',\r\n      events: [\r\n        `${monitoringType}-update`,\r\n        `${monitoringType}-created`,\r\n        `${monitoringType}-resolved`,\r\n      ],\r\n      fallbackInterval: intervalMap[monitoringType],\r\n    },\r\n    options\r\n  );\r\n}\r\n\r\n/**\r\n * Smart Query Hook with Socket.IO Room Management\r\n *\r\n * Combines React Query with WebSocket real-time updates and automatic Socket.IO room joining.\r\n * This hook automatically:\r\n * - Joins the appropriate Socket.IO room based on the domain channel\r\n * - Subscribes to WebSocket events for real-time data updates\r\n * - Switches between WebSocket and polling based on connection state\r\n * - Handles room cleanup when component unmounts\r\n *\r\n * **Room Mapping:**\r\n * - `crud` channel → `entity-updates` room\r\n * - `reliability` channel → `reliability-monitoring` room\r\n * - `notifications` channel → `notifications-monitoring` room\r\n * - `system` channel → `system-monitoring` room\r\n *\r\n * @template T - The data type returned by the query function\r\n * @template E - The error type for failed queries\r\n * @param queryKey - React Query key for caching and invalidation\r\n * @param queryFn - Data fetching function that returns a Promise<T>\r\n * @param config - Smart query configuration including channel and events\r\n * @param options - Additional React Query options (merged with smart defaults)\r\n * @returns Enhanced query result with WebSocket integration and connection state\r\n */\r\nexport function useSmartQuery<T = unknown, E = Error>(\r\n  queryKey: unknown[],\r\n  queryFn: () => Promise<T>,\r\n  config: SmartQueryConfig<T>,\r\n  options?: Omit<UseQueryOptions<T, E>, 'queryFn' | 'queryKey'>\r\n): UseQueryResult<T, E> & {\r\n  isUsingFallback: boolean;\r\n  isWebSocketConnected: boolean;\r\n} {\r\n  const {\r\n    channel,\r\n    enableFallback = true,\r\n    enableWebSocket = true,\r\n    events,\r\n    fallbackInterval = 30_000,\r\n  } = config;\r\n\r\n  const [isWebSocketConnected, setIsWebSocketConnected] = useState(false);\r\n  const webSocketManager = getWebSocketManager();\r\n\r\n  // Track WebSocket connection state\r\n  useEffect(() => {\r\n    const updateConnectionState = () => {\r\n      setIsWebSocketConnected(webSocketManager.isConnected());\r\n    };\r\n\r\n    // Initial state\r\n    updateConnectionState();\r\n\r\n    // Subscribe to state changes\r\n    const unsubscribe = webSocketManager.onStateChange(updateConnectionState);\r\n\r\n    return unsubscribe;\r\n  }, [webSocketManager]);\r\n\r\n  // Determine if we should use fallback polling\r\n  const isUsingFallback =\r\n    enableFallback && (!enableWebSocket || !isWebSocketConnected);\r\n\r\n  // Configure React Query options based on WebSocket state\r\n  const queryOptions: UseQueryOptions<T, E> = {\r\n    // Longer cache time for better performance\r\n    gcTime: 10 * 60 * 1000, // 10 minutes\r\n    queryFn,\r\n    queryKey,\r\n    // Disable polling when WebSocket is connected\r\n    refetchInterval: isUsingFallback ? fallbackInterval : false,\r\n    refetchOnReconnect: true, // Always refetch on network reconnect\r\n    // Enable background refetch only when using fallback\r\n    refetchOnWindowFocus: isUsingFallback,\r\n    // Shorter stale time when using WebSocket (real-time updates)\r\n    staleTime: isWebSocketConnected ? 0 : 30_000,\r\n    ...options,\r\n  };\r\n\r\n  const queryClient = useQueryClient();\r\n  const queryResult = useQuery<T, E>(queryOptions);\r\n\r\n  // Manage Socket.IO room joining/leaving based on channel\r\n  useEffect(() => {\r\n    if (!enableWebSocket || !isWebSocketConnected) {\r\n      return;\r\n    }\r\n\r\n    const roomName = CHANNEL_ROOM_MAPPING[channel];\r\n    if (!roomName) {\r\n      console.warn(\r\n        `[SmartQuery] No room mapping found for channel: ${channel}`\r\n      );\r\n      return;\r\n    }\r\n\r\n    // Join the appropriate room for this channel\r\n    try {\r\n      webSocketManager.joinRoom(roomName);\r\n      console.log(\r\n        `[SmartQuery] Joined room: ${roomName} for channel: ${channel}`\r\n      );\r\n    } catch (error) {\r\n      console.error(`[SmartQuery] Failed to join room ${roomName}:`, error);\r\n    }\r\n\r\n    // Cleanup: leave room when component unmounts or dependencies change\r\n    return () => {\r\n      try {\r\n        webSocketManager.leaveRoom(roomName);\r\n        console.log(\r\n          `[SmartQuery] Left room: ${roomName} for channel: ${channel}`\r\n        );\r\n      } catch (error) {\r\n        console.error(`[SmartQuery] Failed to leave room ${roomName}:`, error);\r\n      }\r\n    };\r\n  }, [enableWebSocket, isWebSocketConnected, channel, webSocketManager]);\r\n\r\n  // Subscribe to WebSocket events for real-time updates\r\n  useEffect(() => {\r\n    if (!enableWebSocket || !isWebSocketConnected || events.length === 0) {\r\n      return;\r\n    }\r\n\r\n    const unsubscribers: (() => void)[] = [];\r\n\r\n    // Subscribe to each event\r\n    for (const event of events) {\r\n      const unsubscribe = webSocketManager.subscribe(\r\n        channel,\r\n        event,\r\n        (data: unknown) => {\r\n          console.log(\r\n            `[SmartQuery] WebSocket event received: ${channel}:${event}`,\r\n            data\r\n          );\r\n\r\n          // Invalidate the specific query to trigger refetch\r\n          queryClient.invalidateQueries({ queryKey });\r\n        }\r\n      );\r\n\r\n      unsubscribers.push(unsubscribe);\r\n    }\r\n\r\n    return () => {\r\n      for (const unsubscribe of unsubscribers) unsubscribe();\r\n    };\r\n  }, [\r\n    enableWebSocket,\r\n    isWebSocketConnected,\r\n    events,\r\n    channel,\r\n    webSocketManager,\r\n    queryClient,\r\n    queryKey,\r\n  ]);\r\n\r\n  return {\r\n    ...queryResult,\r\n    isUsingFallback,\r\n    isWebSocketConnected,\r\n  };\r\n}\r\n\r\n/**\r\n * Hook for system-wide events with smart real-time updates\r\n */\r\nexport function useSystemQuery<T = unknown, E = Error>(\r\n  queryKey: unknown[],\r\n  queryFn: () => Promise<T>,\r\n  options?: Omit<UseQueryOptions<T, E>, 'queryFn' | 'queryKey'>\r\n) {\r\n  return useSmartQuery(\r\n    queryKey,\r\n    queryFn,\r\n    {\r\n      channel: 'system',\r\n      events: ['system-update', 'config-changed'],\r\n      fallbackInterval: 120_000, // 2 minutes for system events\r\n    },\r\n    options\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;;;;AAID;AAAA;AACA;AAIA;;;;;AAEA;;;CAGC,GACD,MAAM,uBAAsD;IAC1D,MAAM;IACN,eAAe;IACf,aAAa;IACb,QAAQ;AACV;AAyBO,SAAS,aACd,QAAmB,EACnB,OAAyB,EACzB,UAAkB,EAClB,OAA6D;;IAE7D,OAAO,cACL,UACA,SACA;QACE,SAAS;QACT,QAAQ;YACN,GAAG,WAAW,QAAQ,CAAC;YACvB,GAAG,WAAW,QAAQ,CAAC;YACvB,GAAG,WAAW,QAAQ,CAAC;YACvB,CAAC,QAAQ,EAAE,YAAY;SACxB;QACD,kBAAkB;IACpB,GACA;AAEJ;GArBgB;;QAMP;;;AAoBF,SAAS,qBACd,QAAmB,EACnB,OAAyB,EACzB,OAA6D;;IAE7D,OAAO,cACL,UACA,SACA;QACE,SAAS;QACT,QAAQ;YAAC;YAAwB;SAAuB;QACxD,kBAAkB;IACpB,GACA;AAEJ;IAfgB;;QAKP;;;AAeF,SAAS,oBACd,QAAmB,EACnB,OAAyB,EACzB,cAAoE,EACpE,OAA6D;;IAE7D,qEAAqE;IACrE,MAAM,cAAc;QAClB,QAAQ;QACR,oBAAoB;QACpB,QAAQ;QACR,SAAS;IACX;IAEA,MAAM,mBAAmB,CAAA,GAAA,6IAAA,CAAA,sBAAmB,AAAD;IAE3C,+DAA+D;IAC/D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,IAAI,iBAAiB,WAAW,IAAI;gBAClC,QAAQ,KAAK,CACX,CAAC,2DAA2D,EAAE,gBAAgB;gBAEhF,iBAAiB,QAAQ,CAAC;YAC5B;YAEA,oEAAoE;YACpE,MAAM,cAAc,iBAAiB,aAAa;6DAAC,CAAA;oBACjD,IAAI,UAAU,aAAa;wBACzB,QAAQ,KAAK,CACX,CAAC,gFAAgF,EAAE,gBAAgB;wBAErG,iBAAiB,QAAQ,CAAC;oBAC5B;gBACF;;YAEA;iDAAO;oBACL;oBACA,qCAAqC;oBACrC,IAAI,iBAAiB,WAAW,IAAI;wBAClC,iBAAiB,SAAS,CAAC;oBAC7B;gBACF;;QACF;wCAAG;QAAC;QAAkB;KAAe;IAErC,OAAO,cACL,UACA,SACA;QACE,SAAS;QACT,QAAQ;YACN,GAAG,eAAe,OAAO,CAAC;YAC1B,GAAG,eAAe,QAAQ,CAAC;YAC3B,GAAG,eAAe,SAAS,CAAC;SAC7B;QACD,kBAAkB,WAAW,CAAC,eAAe;IAC/C,GACA;AAEJ;IA1DgB;;QA4CP;;;AAwCF,SAAS,cACd,QAAmB,EACnB,OAAyB,EACzB,MAA2B,EAC3B,OAA6D;;IAK7D,MAAM,EACJ,OAAO,EACP,iBAAiB,IAAI,EACrB,kBAAkB,IAAI,EACtB,MAAM,EACN,mBAAmB,MAAM,EAC1B,GAAG;IAEJ,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,mBAAmB,CAAA,GAAA,6IAAA,CAAA,sBAAmB,AAAD;IAE3C,mCAAmC;IACnC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM;iEAAwB;oBAC5B,wBAAwB,iBAAiB,WAAW;gBACtD;;YAEA,gBAAgB;YAChB;YAEA,6BAA6B;YAC7B,MAAM,cAAc,iBAAiB,aAAa,CAAC;YAEnD,OAAO;QACT;kCAAG;QAAC;KAAiB;IAErB,8CAA8C;IAC9C,MAAM,kBACJ,kBAAkB,CAAC,CAAC,mBAAmB,CAAC,oBAAoB;IAE9D,yDAAyD;IACzD,MAAM,eAAsC;QAC1C,2CAA2C;QAC3C,QAAQ,KAAK,KAAK;QAClB;QACA;QACA,8CAA8C;QAC9C,iBAAiB,kBAAkB,mBAAmB;QACtD,oBAAoB;QACpB,qDAAqD;QACrD,sBAAsB;QACtB,8DAA8D;QAC9D,WAAW,uBAAuB,IAAI;QACtC,GAAG,OAAO;IACZ;IAEA,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,cAAc,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAQ;IAEnC,yDAAyD;IACzD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,CAAC,mBAAmB,CAAC,sBAAsB;gBAC7C;YACF;YAEA,MAAM,WAAW,oBAAoB,CAAC,QAAQ;YAC9C,IAAI,CAAC,UAAU;gBACb,QAAQ,IAAI,CACV,CAAC,gDAAgD,EAAE,SAAS;gBAE9D;YACF;YAEA,6CAA6C;YAC7C,IAAI;gBACF,iBAAiB,QAAQ,CAAC;gBAC1B,QAAQ,GAAG,CACT,CAAC,0BAA0B,EAAE,SAAS,cAAc,EAAE,SAAS;YAEnE,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,CAAC,iCAAiC,EAAE,SAAS,CAAC,CAAC,EAAE;YACjE;YAEA,qEAAqE;YACrE;2CAAO;oBACL,IAAI;wBACF,iBAAiB,SAAS,CAAC;wBAC3B,QAAQ,GAAG,CACT,CAAC,wBAAwB,EAAE,SAAS,cAAc,EAAE,SAAS;oBAEjE,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,CAAC,kCAAkC,EAAE,SAAS,CAAC,CAAC,EAAE;oBAClE;gBACF;;QACF;kCAAG;QAAC;QAAiB;QAAsB;QAAS;KAAiB;IAErE,sDAAsD;IACtD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,CAAC,mBAAmB,CAAC,wBAAwB,OAAO,MAAM,KAAK,GAAG;gBACpE;YACF;YAEA,MAAM,gBAAgC,EAAE;YAExC,0BAA0B;YAC1B,KAAK,MAAM,SAAS,OAAQ;gBAC1B,MAAM,cAAc,iBAAiB,SAAS,CAC5C,SACA;2DACA,CAAC;wBACC,QAAQ,GAAG,CACT,CAAC,uCAAuC,EAAE,QAAQ,CAAC,EAAE,OAAO,EAC5D;wBAGF,mDAAmD;wBACnD,YAAY,iBAAiB,CAAC;4BAAE;wBAAS;oBAC3C;;gBAGF,cAAc,IAAI,CAAC;YACrB;YAEA;2CAAO;oBACL,KAAK,MAAM,eAAe,cAAe;gBAC3C;;QACF;kCAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,OAAO;QACL,GAAG,WAAW;QACd;QACA;IACF;AACF;IA5IgB;;QAuDM,yLAAA,CAAA,iBAAc;QACd,8KAAA,CAAA,WAAQ;;;AAyFvB,SAAS,eACd,QAAmB,EACnB,OAAyB,EACzB,OAA6D;;IAE7D,OAAO,cACL,UACA,SACA;QACE,SAAS;QACT,QAAQ;YAAC;YAAiB;SAAiB;QAC3C,kBAAkB;IACpB,GACA;AAEJ;IAfgB;;QAKP", "debugId": null}}, {"offset": {"line": 660, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/lib/api/services/domain/reliabilityApi.ts"], "sourcesContent": ["import type {\r\n  AlertApiResponse,\r\n  AlertHistoryApiResponse,\r\n  AlertStatisticsApiResponse,\r\n  CircuitBreakerStatusApiResponse,\r\n  DeduplicationMetricsApiResponse,\r\n  DependencyHealthApiResponse,\r\n  DetailedHealthApiResponse,\r\n  HealthCheckApiResponse,\r\n  MetricsApiResponse,\r\n  TestAlertsApiResponse,\r\n} from '../../../types/api';\r\nimport type {\r\n  Alert,\r\n  AlertHistory,\r\n  AlertStatistics,\r\n  CircuitBreakerStatus,\r\n  DeduplicationMetrics,\r\n  DependencyHealth,\r\n  DetailedHealthCheck,\r\n  HealthCheck,\r\n  SystemMetrics,\r\n  TestAlertsResult,\r\n} from '../../../types/domain';\r\nimport type { ApiClient } from '../../core/apiClient';\r\n\r\nimport logger from '../../../utils/logger';\r\nimport {\r\n  BaseApiService,\r\n  type DataTransformer,\r\n  type ServiceConfig,\r\n} from '../../core/baseApiService';\r\n\r\nconst ReliabilityTransformer: DataTransformer<any> = {\r\n  fromApi: (data: any) => data,\r\n  toApi: (data: any) => data,\r\n};\r\n\r\nexport class ReliabilityApiService extends BaseApiService<any, any, any> {\r\n  protected endpoint = '/reliability';\r\n  protected transformer: DataTransformer<any> = ReliabilityTransformer;\r\n\r\n  constructor(apiClient: ApiClient, config?: ServiceConfig) {\r\n    super(apiClient, {\r\n      cacheDuration: 1 * 60 * 1000, // 1 minute for reliability data\r\n      circuitBreakerThreshold: 5,\r\n      enableMetrics: true,\r\n      retryAttempts: 3,\r\n      ...config,\r\n    });\r\n  }\r\n\r\n  async acknowledgeAlert(\r\n    alertId: string,\r\n    note?: string,\r\n    acknowledgedBy?: string\r\n  ): Promise<Alert> {\r\n    return this.executeWithInfrastructure(null, async () => {\r\n      const response = await this.apiClient.post<AlertApiResponse>(\r\n        `/alerts/${alertId}/acknowledge`,\r\n        {\r\n          acknowledgedBy,\r\n          note,\r\n        }\r\n      );\r\n\r\n      this.cache.invalidatePattern(new RegExp('^alerts:'));\r\n\r\n      return response;\r\n    });\r\n  }\r\n\r\n  async getActiveAlerts(): Promise<Alert[]> {\r\n    return this.executeWithInfrastructure('alerts:active', async () => {\r\n      try {\r\n        const apiResponse = await this.apiClient.get<any>('/alerts');\r\n\r\n        return apiResponse?.alerts || [];\r\n      } catch (error) {\r\n        console.error('Failed to get active alerts:', error);\r\n        return [];\r\n      }\r\n    });\r\n  }\r\n\r\n  async getAlertHistory(page = 1, limit = 50): Promise<AlertHistory> {\r\n    return this.executeWithInfrastructure(\r\n      `alerts:history:${page}:${limit}`,\r\n      async () => {\r\n        const queryParams = new URLSearchParams({\r\n          limit: limit.toString(),\r\n          page: page.toString(),\r\n        });\r\n        const response = await this.apiClient.get<AlertHistoryApiResponse>(\r\n          `/alerts/history?${queryParams.toString()}`\r\n        );\r\n        return response;\r\n      }\r\n    );\r\n  }\r\n\r\n  async getAlertStatistics(): Promise<AlertStatistics> {\r\n    return this.executeWithInfrastructure('alerts:statistics', async () => {\r\n      try {\r\n        const response =\r\n          await this.apiClient.get<AlertStatisticsApiResponse>(\r\n            '/alerts/statistics'\r\n          );\r\n        return response;\r\n      } catch (error) {\r\n        console.error('Failed to get alert statistics:', error);\r\n        return {\r\n          acknowledged: 0,\r\n          active: 0,\r\n          averageResolutionTime: 0,\r\n          bySeverity: { critical: 0, high: 0, low: 0, medium: 0 },\r\n          recentTrends: { last7Days: 0, last24Hours: 0, last30Days: 0 },\r\n          resolved: 0,\r\n          total: 0,\r\n        };\r\n      }\r\n    });\r\n  }\r\n\r\n  async getCircuitBreakerHistory(\r\n    timeframe: '1h' | '6h' | '7d' | '24h' = '24h',\r\n    breakerName?: string\r\n  ): Promise<any> {\r\n    return this.executeWithInfrastructure(\r\n      `circuit-breakers:history:${timeframe}:${breakerName || 'all'}`,\r\n      async () => {\r\n        const params = new URLSearchParams({ timeframe });\r\n        if (breakerName) {\r\n          params.append('breakerName', breakerName);\r\n        }\r\n        const response = await this.apiClient.get<any>(\r\n          `/monitoring/circuit-breakers/history?${params.toString()}`\r\n        );\r\n        return response;\r\n      }\r\n    );\r\n  }\r\n\r\n  async getCircuitBreakerStatus(): Promise<CircuitBreakerStatus> {\r\n    return this.executeWithInfrastructure(\r\n      'monitoring:circuit-breakers',\r\n      async () => {\r\n        try {\r\n          const apiResponse =\r\n            await this.apiClient.get<any>('/circuit-breakers');\r\n\r\n          const circuitBreakers = apiResponse?.circuitBreakers || [];\r\n\r\n          return {\r\n            circuitBreakers: circuitBreakers || [],\r\n            summary: {\r\n              closed:\r\n                circuitBreakers?.filter((cb: any) => cb.state === 'CLOSED')\r\n                  .length || 0,\r\n              halfOpen:\r\n                circuitBreakers?.filter((cb: any) => cb.state === 'HALF_OPEN')\r\n                  .length || 0,\r\n              open:\r\n                circuitBreakers?.filter((cb: any) => cb.state === 'OPEN')\r\n                  .length || 0,\r\n              total: circuitBreakers?.length || 0,\r\n            },\r\n          };\r\n        } catch (error) {\r\n          console.error('Failed to get circuit breaker status:', error);\r\n          return {\r\n            circuitBreakers: [],\r\n            summary: { closed: 0, halfOpen: 0, open: 0, total: 0 },\r\n          };\r\n        }\r\n      }\r\n    );\r\n  }\r\n\r\n  async getCriticalAlertCount(): Promise<number> {\r\n    try {\r\n      const statistics = await this.getAlertStatistics();\r\n      return statistics.bySeverity.critical;\r\n    } catch {\r\n      return 0;\r\n    }\r\n  }\r\n\r\n  async getDeduplicationMetrics(): Promise<DeduplicationMetrics> {\r\n    return this.executeWithInfrastructure(\r\n      'monitoring:deduplication',\r\n      async () => {\r\n        const response =\r\n          await this.apiClient.get<DeduplicationMetricsApiResponse>(\r\n            '/monitoring/deduplication'\r\n          );\r\n        return response as any;\r\n      }\r\n    );\r\n  }\r\n\r\n  async getDependencyHealth(): Promise<DependencyHealth> {\r\n    return this.executeWithInfrastructure('health:dependencies', async () => {\r\n      const response = await this.apiClient.get<DependencyHealthApiResponse>(\r\n        '/health/dependencies'\r\n      );\r\n      return response as any;\r\n    });\r\n  }\r\n\r\n  async getDetailedHealth(): Promise<DetailedHealthCheck> {\r\n    return this.executeWithInfrastructure('health:detailed', async () => {\r\n      const response =\r\n        await this.apiClient.get<DetailedHealthApiResponse>('/health/detailed');\r\n      return response as any;\r\n    });\r\n  }\r\n\r\n  async getHealthTrends(\r\n    timeframe: '1h' | '6h' | '7d' | '24h' = '24h'\r\n  ): Promise<any> {\r\n    return this.executeWithInfrastructure(\r\n      `health:trends:${timeframe}`,\r\n      async () => {\r\n        const response = await this.apiClient.get<any>(\r\n          `/health/trends?timeframe=${timeframe}`\r\n        );\r\n        return response;\r\n      }\r\n    );\r\n  }\r\n\r\n  async getHttpRequestMetrics(): Promise<any> {\r\n    return this.executeWithInfrastructure('http:metrics', async () => {\r\n      const response = await this.apiClient.get<any>('/http-request-metrics');\r\n      return response;\r\n    });\r\n  }\r\n\r\n  async getMetrics(): Promise<SystemMetrics> {\r\n    return this.executeWithInfrastructure('metrics:system', async () => {\r\n      const response = await this.apiClient.get<MetricsApiResponse>(\r\n        '/metrics',\r\n        {\r\n          headers: { Accept: 'application/json' },\r\n        }\r\n      );\r\n      return response as any;\r\n    });\r\n  }\r\n\r\n  async getReliabilityDashboardData(): Promise<{\r\n    activeAlerts: Alert[];\r\n    alertStatistics: AlertStatistics;\r\n    circuitBreakers: CircuitBreakerStatus;\r\n    detailedHealth: DetailedHealthCheck;\r\n    metrics: SystemMetrics;\r\n    systemHealth: HealthCheck;\r\n  }> {\r\n    const [\r\n      systemHealth,\r\n      detailedHealth,\r\n      circuitBreakers,\r\n      metrics,\r\n      activeAlerts,\r\n      alertStatistics,\r\n    ] = await Promise.all([\r\n      this.getSystemHealth(),\r\n      this.getDetailedHealth(),\r\n      this.getCircuitBreakerStatus(),\r\n      this.getMetrics(),\r\n      this.getActiveAlerts(),\r\n      this.getAlertStatistics(),\r\n    ]);\r\n\r\n    return {\r\n      activeAlerts,\r\n      alertStatistics,\r\n      circuitBreakers,\r\n      detailedHealth,\r\n      metrics,\r\n      systemHealth,\r\n    };\r\n  }\r\n\r\n  async getSystemHealth(): Promise<HealthCheck> {\r\n    return this.executeWithInfrastructure('health:system', async () => {\r\n      const response =\r\n        await this.apiClient.get<HealthCheckApiResponse>('/health');\r\n      return response as any;\r\n    });\r\n  }\r\n\r\n  async isSystemHealthy(): Promise<boolean> {\r\n    try {\r\n      const health = await this.getSystemHealth();\r\n      return health.status === 'healthy';\r\n    } catch {\r\n      return false;\r\n    }\r\n  }\r\n\r\n  async resolveAlert(\r\n    alertId: string,\r\n    reason?: string,\r\n    resolvedBy?: string\r\n  ): Promise<Alert> {\r\n    return this.executeWithInfrastructure(null, async () => {\r\n      const response = await this.apiClient.post<AlertApiResponse>(\r\n        `/alerts/${alertId}/resolve`,\r\n        {\r\n          reason,\r\n          resolvedBy,\r\n        }\r\n      );\r\n\r\n      this.cache.invalidatePattern(new RegExp('^alerts:'));\r\n\r\n      return response;\r\n    });\r\n  }\r\n\r\n  async testAlerts(): Promise<TestAlertsResult> {\r\n    return this.executeWithInfrastructure(null, async () => {\r\n      const response = await this.apiClient.post<any>('/alerts/test');\r\n      return {\r\n        message: response?.message || 'Test alert triggered',\r\n        success: response?.status === 'success',\r\n        testAlertId: response?.data?.id,\r\n      };\r\n    });\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;AA2BA;;AAMA,MAAM,yBAA+C;IACnD,SAAS,CAAC,OAAc;IACxB,OAAO,CAAC,OAAc;AACxB;AAEO,MAAM,8BAA8B,8IAAA,CAAA,iBAAc;IAC7C,WAAW,eAAe;IAC1B,cAAoC,uBAAuB;IAErE,YAAY,SAAoB,EAAE,MAAsB,CAAE;QACxD,KAAK,CAAC,WAAW;YACf,eAAe,IAAI,KAAK;YACxB,yBAAyB;YACzB,eAAe;YACf,eAAe;YACf,GAAG,MAAM;QACX;IACF;IAEA,MAAM,iBACJ,OAAe,EACf,IAAa,EACb,cAAuB,EACP;QAChB,OAAO,IAAI,CAAC,yBAAyB,CAAC,MAAM;YAC1C,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CACxC,CAAC,QAAQ,EAAE,QAAQ,YAAY,CAAC,EAChC;gBACE;gBACA;YACF;YAGF,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,OAAO;YAExC,OAAO;QACT;IACF;IAEA,MAAM,kBAAoC;QACxC,OAAO,IAAI,CAAC,yBAAyB,CAAC,iBAAiB;YACrD,IAAI;gBACF,MAAM,cAAc,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CAAM;gBAElD,OAAO,aAAa,UAAU,EAAE;YAClC,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,gCAAgC;gBAC9C,OAAO,EAAE;YACX;QACF;IACF;IAEA,MAAM,gBAAgB,OAAO,CAAC,EAAE,QAAQ,EAAE,EAAyB;QACjE,OAAO,IAAI,CAAC,yBAAyB,CACnC,CAAC,eAAe,EAAE,KAAK,CAAC,EAAE,OAAO,EACjC;YACE,MAAM,cAAc,IAAI,gBAAgB;gBACtC,OAAO,MAAM,QAAQ;gBACrB,MAAM,KAAK,QAAQ;YACrB;YACA,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CACvC,CAAC,gBAAgB,EAAE,YAAY,QAAQ,IAAI;YAE7C,OAAO;QACT;IAEJ;IAEA,MAAM,qBAA+C;QACnD,OAAO,IAAI,CAAC,yBAAyB,CAAC,qBAAqB;YACzD,IAAI;gBACF,MAAM,WACJ,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CACtB;gBAEJ,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,mCAAmC;gBACjD,OAAO;oBACL,cAAc;oBACd,QAAQ;oBACR,uBAAuB;oBACvB,YAAY;wBAAE,UAAU;wBAAG,MAAM;wBAAG,KAAK;wBAAG,QAAQ;oBAAE;oBACtD,cAAc;wBAAE,WAAW;wBAAG,aAAa;wBAAG,YAAY;oBAAE;oBAC5D,UAAU;oBACV,OAAO;gBACT;YACF;QACF;IACF;IAEA,MAAM,yBACJ,YAAwC,KAAK,EAC7C,WAAoB,EACN;QACd,OAAO,IAAI,CAAC,yBAAyB,CACnC,CAAC,yBAAyB,EAAE,UAAU,CAAC,EAAE,eAAe,OAAO,EAC/D;YACE,MAAM,SAAS,IAAI,gBAAgB;gBAAE;YAAU;YAC/C,IAAI,aAAa;gBACf,OAAO,MAAM,CAAC,eAAe;YAC/B;YACA,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CACvC,CAAC,qCAAqC,EAAE,OAAO,QAAQ,IAAI;YAE7D,OAAO;QACT;IAEJ;IAEA,MAAM,0BAAyD;QAC7D,OAAO,IAAI,CAAC,yBAAyB,CACnC,+BACA;YACE,IAAI;gBACF,MAAM,cACJ,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CAAM;gBAEhC,MAAM,kBAAkB,aAAa,mBAAmB,EAAE;gBAE1D,OAAO;oBACL,iBAAiB,mBAAmB,EAAE;oBACtC,SAAS;wBACP,QACE,iBAAiB,OAAO,CAAC,KAAY,GAAG,KAAK,KAAK,UAC/C,UAAU;wBACf,UACE,iBAAiB,OAAO,CAAC,KAAY,GAAG,KAAK,KAAK,aAC/C,UAAU;wBACf,MACE,iBAAiB,OAAO,CAAC,KAAY,GAAG,KAAK,KAAK,QAC/C,UAAU;wBACf,OAAO,iBAAiB,UAAU;oBACpC;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,yCAAyC;gBACvD,OAAO;oBACL,iBAAiB,EAAE;oBACnB,SAAS;wBAAE,QAAQ;wBAAG,UAAU;wBAAG,MAAM;wBAAG,OAAO;oBAAE;gBACvD;YACF;QACF;IAEJ;IAEA,MAAM,wBAAyC;QAC7C,IAAI;YACF,MAAM,aAAa,MAAM,IAAI,CAAC,kBAAkB;YAChD,OAAO,WAAW,UAAU,CAAC,QAAQ;QACvC,EAAE,OAAM;YACN,OAAO;QACT;IACF;IAEA,MAAM,0BAAyD;QAC7D,OAAO,IAAI,CAAC,yBAAyB,CACnC,4BACA;YACE,MAAM,WACJ,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CACtB;YAEJ,OAAO;QACT;IAEJ;IAEA,MAAM,sBAAiD;QACrD,OAAO,IAAI,CAAC,yBAAyB,CAAC,uBAAuB;YAC3D,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CACvC;YAEF,OAAO;QACT;IACF;IAEA,MAAM,oBAAkD;QACtD,OAAO,IAAI,CAAC,yBAAyB,CAAC,mBAAmB;YACvD,MAAM,WACJ,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CAA4B;YACtD,OAAO;QACT;IACF;IAEA,MAAM,gBACJ,YAAwC,KAAK,EAC/B;QACd,OAAO,IAAI,CAAC,yBAAyB,CACnC,CAAC,cAAc,EAAE,WAAW,EAC5B;YACE,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CACvC,CAAC,yBAAyB,EAAE,WAAW;YAEzC,OAAO;QACT;IAEJ;IAEA,MAAM,wBAAsC;QAC1C,OAAO,IAAI,CAAC,yBAAyB,CAAC,gBAAgB;YACpD,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CAAM;YAC/C,OAAO;QACT;IACF;IAEA,MAAM,aAAqC;QACzC,OAAO,IAAI,CAAC,yBAAyB,CAAC,kBAAkB;YACtD,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CACvC,YACA;gBACE,SAAS;oBAAE,QAAQ;gBAAmB;YACxC;YAEF,OAAO;QACT;IACF;IAEA,MAAM,8BAOH;QACD,MAAM,CACJ,cACA,gBACA,iBACA,SACA,cACA,gBACD,GAAG,MAAM,QAAQ,GAAG,CAAC;YACpB,IAAI,CAAC,eAAe;YACpB,IAAI,CAAC,iBAAiB;YACtB,IAAI,CAAC,uBAAuB;YAC5B,IAAI,CAAC,UAAU;YACf,IAAI,CAAC,eAAe;YACpB,IAAI,CAAC,kBAAkB;SACxB;QAED,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;QACF;IACF;IAEA,MAAM,kBAAwC;QAC5C,OAAO,IAAI,CAAC,yBAAyB,CAAC,iBAAiB;YACrD,MAAM,WACJ,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CAAyB;YACnD,OAAO;QACT;IACF;IAEA,MAAM,kBAAoC;QACxC,IAAI;YACF,MAAM,SAAS,MAAM,IAAI,CAAC,eAAe;YACzC,OAAO,OAAO,MAAM,KAAK;QAC3B,EAAE,OAAM;YACN,OAAO;QACT;IACF;IAEA,MAAM,aACJ,OAAe,EACf,MAAe,EACf,UAAmB,EACH;QAChB,OAAO,IAAI,CAAC,yBAAyB,CAAC,MAAM;YAC1C,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CACxC,CAAC,QAAQ,EAAE,QAAQ,QAAQ,CAAC,EAC5B;gBACE;gBACA;YACF;YAGF,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,OAAO;YAExC,OAAO;QACT;IACF;IAEA,MAAM,aAAwC;QAC5C,OAAO,IAAI,CAAC,yBAAyB,CAAC,MAAM;YAC1C,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAM;YAChD,OAAO;gBACL,SAAS,UAAU,WAAW;gBAC9B,SAAS,UAAU,WAAW;gBAC9B,aAAa,UAAU,MAAM;YAC/B;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 890, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/lib/api/services/factory.ts"], "sourcesContent": ["/**\r\n * @file Factory for creating and managing API service instances.\r\n * @module api/services/apiServiceFactory\r\n */\r\n\r\nimport { ApiClient } from '../core/apiClient';\r\nimport { DelegationApiService } from './domain/delegationApi';\r\nimport { EmployeeApiService } from './domain/employeeApi';\r\nimport { ReliabilityApiService } from './domain/reliabilityApi';\r\nimport { TaskApiService } from './domain/taskApi';\r\nimport { VehicleApiService } from './domain/vehicleApi';\r\nimport { getEnvironmentConfig } from '../../config/environment';\r\n// Import secure auth token provider\r\nimport { getSecureAuthTokenProvider } from '../index';\r\n\r\n/**\r\n * Get the current auth token from the secure provider\r\n * Uses the single source of truth for authentication tokens\r\n */\r\nfunction getSecureAuthToken(): string | null {\r\n  const provider = getSecureAuthTokenProvider();\r\n  if (!provider) {\r\n    if (process.env.NODE_ENV === 'development') {\r\n      console.warn('⚠️ Factory: Secure Auth Token Provider not initialized');\r\n    }\r\n    return null;\r\n  }\r\n\r\n  try {\r\n    return provider();\r\n  } catch (error) {\r\n    console.error(\r\n      '❌ Factory: Error getting auth token from secure provider:',\r\n      error\r\n    );\r\n    return null;\r\n  }\r\n}\r\n\r\n/**\r\n * Legacy compatibility - maintains backward compatibility\r\n * @deprecated Use setSecureAuthTokenProvider from main API module instead\r\n */\r\nexport function setFactoryAuthTokenProvider(\r\n  provider: () => string | null\r\n): void {\r\n  console.warn(\r\n    '⚠️ setFactoryAuthTokenProvider is deprecated. Use setSecureAuthTokenProvider from @/lib/api instead.'\r\n  );\r\n  // This function is now a no-op since we use the secure provider\r\n  // The warning guides developers to use the correct function\r\n}\r\n\r\n/**\r\n * Configuration for the API service factory.\r\n */\r\nexport interface ApiServiceFactoryConfig {\r\n  authToken?: string;\r\n  baseURL: string;\r\n  headers?: Record<string, string>;\r\n  retryAttempts?: number;\r\n  timeout?: number;\r\n}\r\n\r\n/**\r\n * Factory class for creating and managing API service instances.\r\n * Provides a centralized way to configure and access all API services.\r\n */\r\nexport class ApiServiceFactory {\r\n  private readonly apiClient: ApiClient;\r\n  private delegationService?: DelegationApiService;\r\n  private employeeService?: EmployeeApiService;\r\n  private reliabilityService?: ReliabilityApiService;\r\n  private taskService?: TaskApiService;\r\n  private vehicleService?: VehicleApiService;\r\n\r\n  /**\r\n   * Creates an instance of ApiServiceFactory.\r\n   * @param config - Configuration for the API services.\r\n   */\r\n  constructor(config: ApiServiceFactoryConfig) {\r\n    this.apiClient = new ApiClient({\r\n      ...config,\r\n      getAuthToken: getSecureAuthToken, // Use consistent secure naming\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Gets the underlying ApiClient instance.\r\n   * @returns The ApiClient instance.\r\n   */\r\n  public getApiClient(): ApiClient {\r\n    return this.apiClient;\r\n  }\r\n\r\n  /**\r\n   * Gets the Delegation API service instance.\r\n   * @returns The DelegationApiService instance.\r\n   */\r\n  public getDelegationService(): DelegationApiService {\r\n    if (!this.delegationService) {\r\n      this.delegationService = new DelegationApiService(this.apiClient);\r\n    }\r\n    return this.delegationService;\r\n  }\r\n\r\n  /**\r\n   * Gets the Employee API service instance.\r\n   * @returns The EmployeeApiService instance.\r\n   */\r\n  public getEmployeeService(): EmployeeApiService {\r\n    if (!this.employeeService) {\r\n      this.employeeService = new EmployeeApiService(this.apiClient);\r\n    }\r\n    return this.employeeService;\r\n  }\r\n\r\n  /**\r\n   * Gets the Reliability API service instance.\r\n   * @returns The ReliabilityApiService instance.\r\n   */\r\n  public getReliabilityService(): ReliabilityApiService {\r\n    if (!this.reliabilityService) {\r\n      this.reliabilityService = new ReliabilityApiService(this.apiClient);\r\n    }\r\n    return this.reliabilityService;\r\n  }\r\n\r\n  /**\r\n   * Gets the Task API service instance.\r\n   * @returns The TaskApiService instance.\r\n   */\r\n  public getTaskService(): TaskApiService {\r\n    if (!this.taskService) {\r\n      this.taskService = new TaskApiService(this.apiClient);\r\n    }\r\n    return this.taskService;\r\n  }\r\n\r\n  /**\r\n   * Gets the Vehicle API service instance.\r\n   * @returns The VehicleApiService instance.\r\n   */\r\n  public getVehicleService(): VehicleApiService {\r\n    if (!this.vehicleService) {\r\n      this.vehicleService = new VehicleApiService(this.apiClient);\r\n    }\r\n    return this.vehicleService;\r\n  }\r\n}\r\n\r\n// Create a default factory instance for the application with environment-aware configuration\r\nconst envConfig = getEnvironmentConfig();\r\nconst defaultConfig: ApiServiceFactoryConfig = {\r\n  baseURL: envConfig.apiBaseUrl, // Use environment-aware configuration\r\n  headers: {\r\n    'Content-Type': 'application/json',\r\n  },\r\n  retryAttempts: 3,\r\n  timeout: 10_000,\r\n};\r\n\r\nexport const apiServiceFactory = new ApiServiceFactory(defaultConfig);\r\n\r\n// Export individual service instances for convenience\r\nexport const vehicleApiService = apiServiceFactory.getVehicleService();\r\nexport const delegationApiService = apiServiceFactory.getDelegationService();\r\nexport const taskApiService = apiServiceFactory.getTaskService();\r\nexport const employeeApiService = apiServiceFactory.getEmployeeService();\r\nexport const reliabilityApiService = apiServiceFactory.getReliabilityService();\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;AAmBO;AAjBR;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC;AACpC;AAAA;;;;;;;;;AAEA;;;CAGC,GACD,SAAS;IACP,MAAM,WAAW,CAAA,GAAA,6IAAA,CAAA,6BAA0B,AAAD;IAC1C,IAAI,CAAC,UAAU;QACb,wCAA4C;YAC1C,QAAQ,IAAI,CAAC;QACf;QACA,OAAO;IACT;IAEA,IAAI;QACF,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CACX,6DACA;QAEF,OAAO;IACT;AACF;AAMO,SAAS,4BACd,QAA6B;IAE7B,QAAQ,IAAI,CACV;AAEF,gEAAgE;AAChE,4DAA4D;AAC9D;AAiBO,MAAM;IACM,UAAqB;IAC9B,kBAAyC;IACzC,gBAAqC;IACrC,mBAA2C;IAC3C,YAA6B;IAC7B,eAAmC;IAE3C;;;GAGC,GACD,YAAY,MAA+B,CAAE;QAC3C,IAAI,CAAC,SAAS,GAAG,IAAI,yIAAA,CAAA,YAAS,CAAC;YAC7B,GAAG,MAAM;YACT,cAAc;QAChB;IACF;IAEA;;;GAGC,GACD,AAAO,eAA0B;QAC/B,OAAO,IAAI,CAAC,SAAS;IACvB;IAEA;;;GAGC,GACD,AAAO,uBAA6C;QAClD,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;YAC3B,IAAI,CAAC,iBAAiB,GAAG,IAAI,2JAAA,CAAA,uBAAoB,CAAC,IAAI,CAAC,SAAS;QAClE;QACA,OAAO,IAAI,CAAC,iBAAiB;IAC/B;IAEA;;;GAGC,GACD,AAAO,qBAAyC;QAC9C,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACzB,IAAI,CAAC,eAAe,GAAG,IAAI,yJAAA,CAAA,qBAAkB,CAAC,IAAI,CAAC,SAAS;QAC9D;QACA,OAAO,IAAI,CAAC,eAAe;IAC7B;IAEA;;;GAGC,GACD,AAAO,wBAA+C;QACpD,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;YAC5B,IAAI,CAAC,kBAAkB,GAAG,IAAI,4JAAA,CAAA,wBAAqB,CAAC,IAAI,CAAC,SAAS;QACpE;QACA,OAAO,IAAI,CAAC,kBAAkB;IAChC;IAEA;;;GAGC,GACD,AAAO,iBAAiC;QACtC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrB,IAAI,CAAC,WAAW,GAAG,IAAI,qJAAA,CAAA,iBAAc,CAAC,IAAI,CAAC,SAAS;QACtD;QACA,OAAO,IAAI,CAAC,WAAW;IACzB;IAEA;;;GAGC,GACD,AAAO,oBAAuC;QAC5C,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACxB,IAAI,CAAC,cAAc,GAAG,IAAI,wJAAA,CAAA,oBAAiB,CAAC,IAAI,CAAC,SAAS;QAC5D;QACA,OAAO,IAAI,CAAC,cAAc;IAC5B;AACF;AAEA,6FAA6F;AAC7F,MAAM,YAAY,CAAA,GAAA,sIAAA,CAAA,uBAAoB,AAAD;AACrC,MAAM,gBAAyC;IAC7C,SAAS,UAAU,UAAU;IAC7B,SAAS;QACP,gBAAgB;IAClB;IACA,eAAe;IACf,SAAS;AACX;AAEO,MAAM,oBAAoB,IAAI,kBAAkB;AAGhD,MAAM,oBAAoB,kBAAkB,iBAAiB;AAC7D,MAAM,uBAAuB,kBAAkB,oBAAoB;AACnE,MAAM,iBAAiB,kBAAkB,cAAc;AACvD,MAAM,qBAAqB,kBAAkB,kBAAkB;AAC/D,MAAM,wBAAwB,kBAAkB,qBAAqB", "debugId": null}}, {"offset": {"line": 1038, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/lib/api/services/apiServiceFactory.ts"], "sourcesContent": ["/**\r\n * @file API Service Factory - Backward Compatibility Export\r\n * @module api/services/apiServiceFactory\r\n *\r\n * This file provides backward compatibility for imports that expect\r\n * apiServiceFactory.ts instead of factory.ts\r\n */\r\n\r\n// Re-export everything from the factory module\r\nexport * from './factory';\r\n\r\n// Ensure all the commonly used exports are available\r\nexport {\r\n  ApiServiceFactory,\r\n  apiServiceFactory,\r\n  setFactoryAuthTokenProvider, // Legacy compatibility - deprecated\r\n  vehicleApiService,\r\n  delegationApiService,\r\n  taskApiService,\r\n  employeeApiService,\r\n  reliabilityApiService,\r\n} from './factory';\r\n\r\n// Re-export secure auth provider for convenience\r\nexport { setSecureAuthTokenProvider } from '../index';\r\n\r\nexport type { ApiServiceFactoryConfig } from './factory';\r\n"], "names": [], "mappings": "AAAA;;;;;;CAMC,GAED,+CAA+C;;AAC/C;AAcA,iDAAiD;AACjD", "debugId": null}}, {"offset": {"line": 1071, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/lib/transformers/delegationEnrichment.ts"], "sourcesContent": ["/**\r\n * @file Delegation enrichment transformer following established patterns\r\n * @description Handles the enrichment of delegation data with employee and vehicle details\r\n * @module transformers/delegationEnrichment\r\n */\r\n\r\nimport type {\r\n  Delegation,\r\n  DelegationDriver,\r\n  DelegationEscort,\r\n  DelegationVehicleAssignment,\r\n  Employee,\r\n  Vehicle,\r\n} from '../types/domain';\r\n\r\n/**\r\n * Transformer class for enriching delegation data with related entities\r\n * Follows the same pattern as other transformers in the codebase\r\n */\r\nexport class DelegationEnrichmentTransformer {\r\n  /**\r\n   * Main enrichment method that combines delegation data with employee and vehicle details\r\n   * @param delegation - Base delegation data\r\n   * @param employees - Array of employees\r\n   * @param vehicles - Array of vehicles\r\n   * @returns Fully enriched delegation\r\n   */\r\n  static enrich(\r\n    delegation: Delegation,\r\n    employees: Employee[],\r\n    vehicles: Vehicle[]\r\n  ): Delegation {\r\n    const { employeeMap, vehicleMap } = this.createLookupMaps(\r\n      employees,\r\n      vehicles\r\n    );\r\n\r\n    return {\r\n      ...delegation,\r\n      drivers: this.enrichDrivers(delegation.drivers, employeeMap) ?? [],\r\n      escorts: this.enrichEscorts(delegation.escorts, employeeMap) ?? [],\r\n      vehicles: this.enrichVehicles(delegation.vehicles, vehicleMap) ?? [],\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Creates optimized lookup maps for O(1) performance\r\n   * @param employees - Array of employees\r\n   * @param vehicles - Array of vehicles\r\n   * @returns Object containing employee and vehicle maps\r\n   */\r\n  private static createLookupMaps(employees: Employee[], vehicles: Vehicle[]) {\r\n    return {\r\n      employeeMap: new Map(employees.map(emp => [emp.id, emp])),\r\n      vehicleMap: new Map(vehicles.map(veh => [veh.id, veh])),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Enriches driver assignments with employee details\r\n   * @param drivers - Array of driver assignments\r\n   * @param employeeMap - Map of employees for O(1) lookup\r\n   * @returns Enriched driver assignments\r\n   */\r\n  private static enrichDrivers(\r\n    drivers: DelegationDriver[] | undefined,\r\n    employeeMap: Map<number, Employee>\r\n  ): DelegationDriver[] | undefined {\r\n    return drivers?.map(driver => {\r\n      const employee =\r\n        driver.employee || employeeMap.get(Number(driver.employeeId));\r\n      return {\r\n        ...driver,\r\n        ...(employee && { employee }),\r\n      };\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Enriches escort assignments with employee details\r\n   * @param escorts - Array of escort assignments\r\n   * @param employeeMap - Map of employees for O(1) lookup\r\n   * @returns Enriched escort assignments\r\n   */\r\n  private static enrichEscorts(\r\n    escorts: DelegationEscort[] | undefined,\r\n    employeeMap: Map<number, Employee>\r\n  ): DelegationEscort[] | undefined {\r\n    return escorts?.map(escort => {\r\n      const employee =\r\n        escort.employee || employeeMap.get(Number(escort.employeeId));\r\n      return {\r\n        ...escort,\r\n        ...(employee && { employee }),\r\n      };\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Enriches vehicle assignments with vehicle details\r\n   * @param vehicles - Array of vehicle assignments\r\n   * @param vehicleMap - Map of vehicles for O(1) lookup\r\n   * @returns Enriched vehicle assignments\r\n   */\r\n  private static enrichVehicles(\r\n    vehicles: DelegationVehicleAssignment[] | undefined,\r\n    vehicleMap: Map<number, Vehicle>\r\n  ): DelegationVehicleAssignment[] | undefined {\r\n    return vehicles?.map(vehicleAssignment => {\r\n      const vehicle =\r\n        vehicleAssignment.vehicle ||\r\n        vehicleMap.get(vehicleAssignment.vehicleId);\r\n      return {\r\n        ...vehicleAssignment,\r\n        ...(vehicle && { vehicle }),\r\n      };\r\n    });\r\n  }\r\n}\r\n\r\n// Export the main enrichment function for backward compatibility\r\nexport const enrichDelegation = (\r\n  delegation: Delegation,\r\n  employees: Employee[],\r\n  vehicles: Vehicle[]\r\n): Delegation => {\r\n  return DelegationEnrichmentTransformer.enrich(\r\n    delegation,\r\n    employees,\r\n    vehicles\r\n  );\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;AAeM,MAAM;IACX;;;;;;GAMC,GACD,OAAO,OACL,UAAsB,EACtB,SAAqB,EACrB,QAAmB,EACP;QACZ,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC,gBAAgB,CACvD,WACA;QAGF,OAAO;YACL,GAAG,UAAU;YACb,SAAS,IAAI,CAAC,aAAa,CAAC,WAAW,OAAO,EAAE,gBAAgB,EAAE;YAClE,SAAS,IAAI,CAAC,aAAa,CAAC,WAAW,OAAO,EAAE,gBAAgB,EAAE;YAClE,UAAU,IAAI,CAAC,cAAc,CAAC,WAAW,QAAQ,EAAE,eAAe,EAAE;QACtE;IACF;IAEA;;;;;GAKC,GACD,OAAe,iBAAiB,SAAqB,EAAE,QAAmB,EAAE;QAC1E,OAAO;YACL,aAAa,IAAI,IAAI,UAAU,GAAG,CAAC,CAAA,MAAO;oBAAC,IAAI,EAAE;oBAAE;iBAAI;YACvD,YAAY,IAAI,IAAI,SAAS,GAAG,CAAC,CAAA,MAAO;oBAAC,IAAI,EAAE;oBAAE;iBAAI;QACvD;IACF;IAEA;;;;;GAKC,GACD,OAAe,cACb,OAAuC,EACvC,WAAkC,EACF;QAChC,OAAO,SAAS,IAAI,CAAA;YAClB,MAAM,WACJ,OAAO,QAAQ,IAAI,YAAY,GAAG,CAAC,OAAO,OAAO,UAAU;YAC7D,OAAO;gBACL,GAAG,MAAM;gBACT,GAAI,YAAY;oBAAE;gBAAS,CAAC;YAC9B;QACF;IACF;IAEA;;;;;GAKC,GACD,OAAe,cACb,OAAuC,EACvC,WAAkC,EACF;QAChC,OAAO,SAAS,IAAI,CAAA;YAClB,MAAM,WACJ,OAAO,QAAQ,IAAI,YAAY,GAAG,CAAC,OAAO,OAAO,UAAU;YAC7D,OAAO;gBACL,GAAG,MAAM;gBACT,GAAI,YAAY;oBAAE;gBAAS,CAAC;YAC9B;QACF;IACF;IAEA;;;;;GAKC,GACD,OAAe,eACb,QAAmD,EACnD,UAAgC,EACW;QAC3C,OAAO,UAAU,IAAI,CAAA;YACnB,MAAM,UACJ,kBAAkB,OAAO,IACzB,WAAW,GAAG,CAAC,kBAAkB,SAAS;YAC5C,OAAO;gBACL,GAAG,iBAAiB;gBACpB,GAAI,WAAW;oBAAE;gBAAQ,CAAC;YAC5B;QACF;IACF;AACF;AAGO,MAAM,mBAAmB,CAC9B,YACA,WACA;IAEA,OAAO,gCAAgC,MAAM,CAC3C,YACA,WACA;AAEJ", "debugId": null}}, {"offset": {"line": 1173, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/lib/stores/queries/delegationQueries.ts"], "sourcesContent": ["/**\r\n * @file Delegation query configurations following Single Responsibility Principle\r\n * @description Centralized query configurations for delegation-related data fetching\r\n */\r\n\r\nimport type { UseQueryOptions } from '@tanstack/react-query';\r\n\r\nimport type { Delegation } from '../../types/domain';\r\n\r\nimport {\r\n  delegationApiService,\r\n  employeeApiService,\r\n  vehicleApiService,\r\n} from '../../api/services/apiServiceFactory'; // Use centralized services\r\nimport { DelegationTransformer } from '../../transformers/delegationTransformer';\r\n\r\n/**\r\n * Query keys for delegation-related queries\r\n */\r\nexport const delegationQueryKeys = {\r\n  all: ['delegations'] as const,\r\n  detail: (id: string) => ['delegations', id] as const,\r\n  withAssignments: (id: string) =>\r\n    ['delegations', id, 'with-assignments'] as const,\r\n};\r\n\r\n/**\r\n * Creates query configuration for fetching a single delegation\r\n */\r\nexport const createDelegationQuery = (id: string) => ({\r\n  enabled: !!id,\r\n  queryFn: () => delegationApiService.getById(id),\r\n  queryKey: delegationQueryKeys.detail(id),\r\n  staleTime: 5 * 60 * 1000, // 5 minutes\r\n});\r\n\r\n/**\r\n * Creates query configuration for fetching all employees\r\n */\r\nexport const createEmployeesQuery = () => ({\r\n  queryFn: () => employeeApiService.getAll(),\r\n  queryKey: ['employees'] as const,\r\n  staleTime: 10 * 60 * 1000, // 10 minutes - employees change less frequently\r\n});\r\n\r\n/**\r\n * Creates query configuration for fetching all vehicles\r\n */\r\nexport const createVehiclesQuery = () => ({\r\n  queryFn: () => vehicleApiService.getAll(),\r\n  queryKey: ['vehicles'] as const,\r\n  staleTime: 10 * 60 * 1000, // 10 minutes - vehicles change less frequently\r\n});\r\n\r\n/**\r\n * Creates parallel query configurations for delegation with assignments\r\n */\r\nexport const createDelegationWithAssignmentsQueries = (id: string) => [\r\n  createDelegationQuery(id),\r\n  createEmployeesQuery(),\r\n  createVehiclesQuery(),\r\n];\r\n\r\n/**\r\n * Standard query options for delegation queries\r\n */\r\nexport const delegationQueryOptions: Partial<\r\n  UseQueryOptions<Delegation, Error>\r\n> = {\r\n  gcTime: 10 * 60 * 1000, // 10 minutes garbage collection time\r\n  retry: 3,\r\n  retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30_000),\r\n  staleTime: 5 * 60 * 1000,\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;AAMD,sTAI+C,2BAA2B;AAJ1E;;AAUO,MAAM,sBAAsB;IACjC,KAAK;QAAC;KAAc;IACpB,QAAQ,CAAC,KAAe;YAAC;YAAe;SAAG;IAC3C,iBAAiB,CAAC,KAChB;YAAC;YAAe;YAAI;SAAmB;AAC3C;AAKO,MAAM,wBAAwB,CAAC,KAAe,CAAC;QACpD,SAAS,CAAC,CAAC;QACX,SAAS,IAAM,2IAAA,CAAA,uBAAoB,CAAC,OAAO,CAAC;QAC5C,UAAU,oBAAoB,MAAM,CAAC;QACrC,WAAW,IAAI,KAAK;IACtB,CAAC;AAKM,MAAM,uBAAuB,IAAM,CAAC;QACzC,SAAS,IAAM,2IAAA,CAAA,qBAAkB,CAAC,MAAM;QACxC,UAAU;YAAC;SAAY;QACvB,WAAW,KAAK,KAAK;IACvB,CAAC;AAKM,MAAM,sBAAsB,IAAM,CAAC;QACxC,SAAS,IAAM,2IAAA,CAAA,oBAAiB,CAAC,MAAM;QACvC,UAAU;YAAC;SAAW;QACtB,WAAW,KAAK,KAAK;IACvB,CAAC;AAKM,MAAM,yCAAyC,CAAC,KAAe;QACpE,sBAAsB;QACtB;QACA;KACD;AAKM,MAAM,yBAET;IACF,QAAQ,KAAK,KAAK;IAClB,OAAO;IACP,YAAY,CAAA,eAAgB,KAAK,GAAG,CAAC,OAAO,KAAK,cAAc;IAC/D,WAAW,IAAI,KAAK;AACtB", "debugId": null}}, {"offset": {"line": 1241, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/lib/stores/queries/useDelegations.ts"], "sourcesContent": ["/**\r\n * @file TanStack Query hooks for Delegation-related data.\r\n * These hooks manage fetching, caching, and mutating delegation data,\r\n * integrating with the DelegationApiService and DelegationTransformer.\r\n * @module stores/queries/useDelegations\r\n */\r\n\r\nimport type { UseQueryOptions } from '@tanstack/react-query';\r\n\r\nimport { useMutation, useQueries, useQueryClient } from '@tanstack/react-query';\r\nimport { useCallback, useMemo } from 'react';\r\n\r\nimport { undefinedToNull } from '@/lib/utils/typeHelpers';\r\n\r\n// import { DelegationFormData } from '../../schemas/delegationSchemas'; // Not directly used by hooks' public API\r\nimport type { UpdateDelegationRequest } from '../../types/api'; // For useUpdateDelegation\r\nimport type {\r\n  CreateDelegationData,\r\n  Delegation,\r\n  DelegationStatusPrisma,\r\n  FlightDetails,\r\n  // DelegationEscort, // Not directly used in optimistic updates in a way that needs separate import here\r\n  // DelegationDriver,\r\n  // DelegationVehicleAssignment,\r\n} from '../../types/domain';\r\n\r\nimport { useCrudQuery } from '../../../hooks/api/useSmartQuery'; // Adjusted import path\r\nimport { delegationApiService } from '../../api/services/apiServiceFactory'; // Use centralized service\r\nimport { enrichDelegation } from '../../transformers/delegationEnrichment';\r\nimport { DelegationTransformer } from '../../transformers/delegationTransformer';\r\nimport {\r\n  createDelegationWithAssignmentsQueries,\r\n  delegationQueryKeys,\r\n} from './delegationQueries';\r\n\r\nexport const useDelegations = (\r\n  options?: Omit<\r\n    UseQueryOptions<Delegation[], Error>,\r\n    'queryFn' | 'queryKey'\r\n  > & { enabled?: boolean }\r\n) => {\r\n  return useCrudQuery<Delegation[], Error>(\r\n    [...delegationQueryKeys.all], // queryKey - spread for mutability\r\n    async () => {\r\n      const result = await delegationApiService.getAll();\r\n      return result.data;\r\n    },\r\n    'delegation', // entityType\r\n    {\r\n      staleTime: 0, // Existing staleTime\r\n      ...options, // Spread additional options\r\n    }\r\n  );\r\n};\r\n\r\nexport const useDelegation = (id: string) => {\r\n  return useCrudQuery<Delegation, Error>(\r\n    [...delegationQueryKeys.detail(id)],\r\n    async () => {\r\n      return await delegationApiService.getById(id);\r\n    },\r\n    'delegation', // entityType for WebSocket events\r\n    {\r\n      enabled: !!id,\r\n      staleTime: 5 * 60 * 1000,\r\n    }\r\n  );\r\n};\r\n\r\n// ✅ OPTIMIZED: Fast parallel data fetching for delegation with assignments\r\nexport const useDelegationWithAssignments = (id: string) => {\r\n  // Execute all queries in parallel using useQueries for maximum performance\r\n  const results = useQueries({\r\n    queries: createDelegationWithAssignmentsQueries(id),\r\n  });\r\n\r\n  const [delegationQuery, employeesQuery, vehiclesQuery] = results;\r\n\r\n  // Compute enriched delegation when all data is available\r\n  const enrichedDelegation = useMemo(() => {\r\n    if (\r\n      !delegationQuery?.data ||\r\n      !employeesQuery?.data ||\r\n      !vehiclesQuery?.data\r\n    ) {\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // ✅ PRODUCTION FIX: delegationQuery.data is already transformed by the service layer\r\n      // No need to apply DelegationTransformer.fromApi() again\r\n      const delegation = delegationQuery.data as Delegation;\r\n      return enrichDelegation(\r\n        delegation,\r\n        employeesQuery.data as any,\r\n        vehiclesQuery.data as any\r\n      );\r\n    } catch (error) {\r\n      console.error('Error enriching delegation data:', error);\r\n      throw error;\r\n    }\r\n  }, [delegationQuery?.data, employeesQuery?.data, vehiclesQuery?.data]);\r\n\r\n  // ✅ PRODUCTION FIX: Memoize refetch function to prevent infinite re-renders\r\n  const refetch = useCallback(() => {\r\n    delegationQuery?.refetch();\r\n    employeesQuery?.refetch();\r\n    vehiclesQuery?.refetch();\r\n  }, [\r\n    delegationQuery?.refetch,\r\n    employeesQuery?.refetch,\r\n    vehiclesQuery?.refetch,\r\n  ]);\r\n\r\n  // Return combined state with optimized loading states\r\n  return {\r\n    data: enrichedDelegation,\r\n    error:\r\n      delegationQuery?.error || employeesQuery?.error || vehiclesQuery?.error,\r\n    isError:\r\n      delegationQuery?.isError ||\r\n      employeesQuery?.isError ||\r\n      vehiclesQuery?.isError,\r\n    isLoading:\r\n      delegationQuery?.isLoading ||\r\n      employeesQuery?.isLoading ||\r\n      vehiclesQuery?.isLoading,\r\n    isPending:\r\n      delegationQuery?.isPending ||\r\n      employeesQuery?.isPending ||\r\n      vehiclesQuery?.isPending,\r\n    refetch,\r\n  };\r\n};\r\n\r\n// ✅ BACKWARD COMPATIBILITY: Alias for the optimized hook\r\nexport const useDelegationEnriched = useDelegationWithAssignments;\r\n\r\nexport const useCreateDelegation = () => {\r\n  const queryClient = useQueryClient();\r\n  interface CreateContext {\r\n    previousDelegations: Delegation[] | undefined;\r\n  }\r\n\r\n  return useMutation<Delegation, Error, CreateDelegationData, CreateContext>({\r\n    mutationFn: async (delegationData: CreateDelegationData) => {\r\n      const apiPayload = DelegationTransformer.toCreateRequest(delegationData);\r\n      // ✅ PRODUCTION FIX: delegationApiService.create() already applies transformation\r\n      return await delegationApiService.create(apiPayload);\r\n    },\r\n    onError: (err, _delegationData, context) => {\r\n      if (context?.previousDelegations) {\r\n        queryClient.setQueryData(\r\n          delegationQueryKeys.all,\r\n          context.previousDelegations\r\n        );\r\n      }\r\n      console.error('Failed to create delegation:', err);\r\n      // Invalidate to refetch correct data on error\r\n      queryClient.invalidateQueries({ queryKey: delegationQueryKeys.all });\r\n    },\r\n    onMutate: async (delegationData: CreateDelegationData) => {\r\n      await queryClient.cancelQueries({ queryKey: delegationQueryKeys.all });\r\n      const previousDelegations = queryClient.getQueryData<Delegation[]>(\r\n        delegationQueryKeys.all\r\n      );\r\n\r\n      queryClient.setQueryData<Delegation[]>(\r\n        delegationQueryKeys.all,\r\n        (old = []) => {\r\n          const tempId = `optimistic-${Date.now()}`;\r\n          const now = new Date().toISOString();\r\n\r\n          const optimisticArrivalFlight: FlightDetails | null =\r\n            delegationData.flightArrivalDetails\r\n              ? {\r\n                  id: `optimistic-flight-arr-${Date.now()}`,\r\n                  ...delegationData.flightArrivalDetails,\r\n                }\r\n              : null;\r\n\r\n          const optimisticDepartureFlight: FlightDetails | null =\r\n            delegationData.flightDepartureDetails\r\n              ? {\r\n                  id: `optimistic-flight-dep-${Date.now() + 1}`,\r\n                  ...delegationData.flightDepartureDetails,\r\n                }\r\n              : null;\r\n\r\n          const optimisticDelegates: Delegation['delegates'] =\r\n            delegationData.delegates?.map((d, index) => ({\r\n              id: `optimistic-delegate-${tempId}-${index}`,\r\n              name: d.name, // Use d.name directly\r\n              notes: d.notes ?? null,\r\n              title: d.title, // Use d.title directly\r\n            })) || [];\r\n\r\n          const optimisticDelegation: Delegation = {\r\n            arrivalFlight: optimisticArrivalFlight ?? null,\r\n            createdAt: now,\r\n            delegates: optimisticDelegates,\r\n            departureFlight: optimisticDepartureFlight ?? null,\r\n            drivers:\r\n              delegationData.drivers?.map(d => ({\r\n                createdAt: now, // Placeholder timestamp\r\n                createdBy: null, // Placeholder\r\n                delegationId: tempId, // Link to optimistic delegation\r\n                employeeId: d.employeeId, // Keep as number\r\n                id: `optimistic-driver-${tempId}-${d.employeeId}`, // Placeholder ID\r\n                notes: d.notes ?? null, // Include notes if available in CreateDelegationData\r\n                updatedAt: now, // Placeholder timestamp\r\n              })) || [],\r\n            durationFrom: delegationData.durationFrom,\r\n            durationTo: delegationData.durationTo,\r\n            escorts:\r\n              delegationData.escorts?.map(e => ({\r\n                createdAt: now, // Placeholder timestamp\r\n                createdBy: null, // Placeholder\r\n                delegationId: tempId, // Link to optimistic delegation\r\n                employeeId: e.employeeId, // Keep as number\r\n                id: `optimistic-escort-${tempId}-${e.employeeId}`, // Placeholder ID\r\n                notes: e.notes ?? null, // Include notes if available in CreateDelegationData\r\n                updatedAt: now, // Placeholder timestamp\r\n              })) || [],\r\n            eventName: delegationData.eventName,\r\n            id: tempId,\r\n            imageUrl: delegationData.imageUrl ?? null,\r\n            invitationFrom: delegationData.invitationFrom ?? null,\r\n            invitationTo: delegationData.invitationTo ?? null,\r\n            location: delegationData.location,\r\n            notes: delegationData.notes ?? null,\r\n            status: delegationData.status || 'Planned',\r\n            statusHistory: [],\r\n            updatedAt: now,\r\n            vehicles:\r\n              delegationData.vehicles?.map(v => ({\r\n                assignedDate: v.assignedDate,\r\n                createdAt: now, // Placeholder timestamp\r\n                createdBy: null, // Placeholder\r\n                delegationId: tempId, // Link to optimistic delegation\r\n                id: `optimistic-vehicle-${tempId}-${v.vehicleId}`, // Placeholder ID\r\n                notes: v.notes ?? null,\r\n                returnDate: v.returnDate ?? null,\r\n                updatedAt: now, // Placeholder timestamp\r\n                vehicleId: v.vehicleId,\r\n              })) || [],\r\n          };\r\n          return [...old, optimisticDelegation];\r\n        }\r\n      );\r\n      return { previousDelegations };\r\n    },\r\n    onSettled: () => {\r\n      // Invalidate to ensure consistency after success or failure\r\n      queryClient.invalidateQueries({ queryKey: delegationQueryKeys.all });\r\n    },\r\n  });\r\n};\r\n\r\nexport const useUpdateDelegation = () => {\r\n  const queryClient = useQueryClient();\r\n  interface UpdateContext {\r\n    previousDelegation: Delegation | undefined;\r\n    previousDelegationsList: Delegation[] | undefined;\r\n  }\r\n\r\n  return useMutation<\r\n    Delegation,\r\n    Error,\r\n    { data: UpdateDelegationRequest; id: string }, // Corrected: data is UpdateDelegationRequest\r\n    UpdateContext\r\n  >({\r\n    mutationFn: async ({ data, id }) => {\r\n      // ✅ PRODUCTION FIX: delegationApiService.update() already applies transformation\r\n      return await delegationApiService.update(id, data);\r\n    },\r\n    onError: (err, variables, context) => {\r\n      if (context?.previousDelegation) {\r\n        queryClient.setQueryData(\r\n          delegationQueryKeys.detail(variables.id),\r\n          context.previousDelegation\r\n        );\r\n      }\r\n      if (context?.previousDelegationsList) {\r\n        queryClient.setQueryData(\r\n          delegationQueryKeys.all,\r\n          context.previousDelegationsList\r\n        );\r\n      }\r\n      console.error('Failed to update delegation:', err);\r\n      // Invalidate to refetch correct data on error\r\n      queryClient.invalidateQueries({\r\n        queryKey: delegationQueryKeys.detail(variables.id),\r\n      });\r\n      queryClient.invalidateQueries({ queryKey: delegationQueryKeys.all });\r\n    },\r\n    onMutate: async ({ data, id }) => {\r\n      // data is UpdateDelegationRequest\r\n      await queryClient.cancelQueries({ queryKey: delegationQueryKeys.all });\r\n      await queryClient.cancelQueries({\r\n        queryKey: delegationQueryKeys.detail(id),\r\n      });\r\n\r\n      const previousDelegation = queryClient.getQueryData<Delegation>(\r\n        delegationQueryKeys.detail(id)\r\n      );\r\n      const previousDelegationsList = queryClient.getQueryData<Delegation[]>(\r\n        delegationQueryKeys.all\r\n      );\r\n\r\n      queryClient.setQueryData<Delegation>(\r\n        delegationQueryKeys.detail(id),\r\n        old => {\r\n          if (!old) return;\r\n          const now = new Date().toISOString();\r\n\r\n          // ✅ PRODUCTION FIX: Use correct field mappings for UpdateDelegationRequest\r\n          const updatedOptimistic: Delegation = {\r\n            ...old,\r\n            // Handle flight details updates\r\n            arrivalFlight: undefinedToNull(\r\n              data.flightArrivalDetails === null\r\n                ? null // Explicitly set to null if requested\r\n                : data.flightArrivalDetails === undefined\r\n                  ? old.arrivalFlight\r\n                  : {\r\n                      airport:\r\n                        data.flightArrivalDetails.airport ||\r\n                        old.arrivalFlight?.airport ||\r\n                        '',\r\n                      dateTime:\r\n                        data.flightArrivalDetails.dateTime ||\r\n                        old.arrivalFlight?.dateTime ||\r\n                        '',\r\n                      flightNumber:\r\n                        data.flightArrivalDetails.flightNumber ||\r\n                        old.arrivalFlight?.flightNumber ||\r\n                        '',\r\n                      id:\r\n                        old.arrivalFlight?.id || `optimistic-arr-${Date.now()}`, // Keep old ID or generate new\r\n                      notes:\r\n                        data.flightArrivalDetails.notes ??\r\n                        old.arrivalFlight?.notes ??\r\n                        null,\r\n                      terminal:\r\n                        data.flightArrivalDetails.terminal ??\r\n                        old.arrivalFlight?.terminal ??\r\n                        null,\r\n                    } // Keep old value if not in request\r\n            ),\r\n            departureFlight: undefinedToNull(\r\n              data.flightDepartureDetails === null\r\n                ? null // Explicitly set to null if requested\r\n                : data.flightDepartureDetails === undefined\r\n                  ? old.departureFlight\r\n                  : {\r\n                      airport:\r\n                        data.flightDepartureDetails.airport ||\r\n                        old.departureFlight?.airport ||\r\n                        '',\r\n                      dateTime:\r\n                        data.flightDepartureDetails.dateTime ||\r\n                        old.departureFlight?.dateTime ||\r\n                        '',\r\n                      flightNumber:\r\n                        data.flightDepartureDetails.flightNumber ||\r\n                        old.departureFlight?.flightNumber ||\r\n                        '',\r\n                      id:\r\n                        old.departureFlight?.id ||\r\n                        `optimistic-dep-${Date.now()}`, // Keep old ID or generate new\r\n                      notes:\r\n                        data.flightDepartureDetails.notes ??\r\n                        old.departureFlight?.notes ??\r\n                        null,\r\n                      terminal:\r\n                        data.flightDepartureDetails.terminal ??\r\n                        old.departureFlight?.terminal ??\r\n                        null,\r\n                    } // Keep old value if not in request\r\n            ),\r\n            durationFrom: data.durationFrom ?? old.durationFrom, // ✅ Direct mapping\r\n            durationTo: data.durationTo ?? old.durationTo, // ✅ Direct mapping\r\n            // Direct field mappings (no transformation needed)\r\n            eventName: data.eventName ?? old.eventName, // ✅ Direct mapping\r\n            imageUrl: undefinedToNull(data.imageUrl ?? old.imageUrl),\r\n            invitationFrom: undefinedToNull(\r\n              data.invitationFrom ?? old.invitationFrom\r\n            ),\r\n            invitationTo: undefinedToNull(\r\n              data.invitationTo ?? old.invitationTo\r\n            ),\r\n            location: data.location ?? old.location,\r\n            notes: undefinedToNull(data.notes ?? old.notes),\r\n            status: (data.status as DelegationStatusPrisma) ?? old.status, // Cast status\r\n            updatedAt: now,\r\n            // Note: Nested assignments (escorts, drivers, vehicles) are typically managed via separate mutations,\r\n            // so they are not included in the main delegation update optimistic logic here.\r\n          };\r\n          return updatedOptimistic;\r\n        }\r\n      );\r\n\r\n      queryClient.setQueryData<Delegation[]>(\r\n        delegationQueryKeys.all,\r\n        (oldList = []) =>\r\n          oldList.map(delegation =>\r\n            delegation.id === id\r\n              ? queryClient.getQueryData<Delegation>(\r\n                  delegationQueryKeys.detail(id)\r\n                ) || delegation\r\n              : delegation\r\n          )\r\n      );\r\n\r\n      return { previousDelegation, previousDelegationsList };\r\n    },\r\n    onSettled: (_data, _error, variables) => {\r\n      // Always refetch after error or success\r\n      queryClient.invalidateQueries({\r\n        queryKey: delegationQueryKeys.detail(variables.id),\r\n      });\r\n      queryClient.invalidateQueries({ queryKey: delegationQueryKeys.all });\r\n    },\r\n  });\r\n};\r\n\r\nexport const useUpdateDelegationStatus = () => {\r\n  const queryClient = useQueryClient();\r\n  interface StatusUpdateContext {\r\n    previousDelegation: Delegation | undefined;\r\n  }\r\n\r\n  return useMutation<\r\n    Delegation,\r\n    Error,\r\n    { id: string; status: DelegationStatusPrisma; statusChangeReason?: string },\r\n    StatusUpdateContext\r\n  >({\r\n    mutationFn: async ({ id, status, statusChangeReason }) => {\r\n      const response = await delegationApiService.updateStatus(\r\n        id,\r\n        status,\r\n        statusChangeReason\r\n      );\r\n      return response;\r\n    },\r\n    onError: (err, variables, context) => {\r\n      if (context?.previousDelegation) {\r\n        queryClient.setQueryData(\r\n          delegationQueryKeys.detail(variables.id),\r\n          context.previousDelegation\r\n        );\r\n      }\r\n      console.error('Failed to update delegation status:', err);\r\n    },\r\n    onMutate: async ({ id, status }) => {\r\n      await queryClient.cancelQueries({\r\n        queryKey: delegationQueryKeys.detail(id),\r\n      });\r\n      const previousDelegation = queryClient.getQueryData<Delegation>(\r\n        delegationQueryKeys.detail(id)\r\n      );\r\n      queryClient.setQueryData<Delegation>(\r\n        delegationQueryKeys.detail(id),\r\n        old => (old ? { ...old, status: status } : undefined)\r\n      );\r\n      return { previousDelegation };\r\n    },\r\n    onSettled: (_data, _error, variables) => {\r\n      queryClient.invalidateQueries({\r\n        queryKey: delegationQueryKeys.detail(variables.id),\r\n      });\r\n      queryClient.invalidateQueries({ queryKey: delegationQueryKeys.all });\r\n    },\r\n  });\r\n};\r\n\r\nexport const useManageDelegationFlightDetails = () => {\r\n  const queryClient = useQueryClient();\r\n  interface FlightDetailsContext {\r\n    previousDelegation: Delegation | undefined;\r\n  }\r\n\r\n  return useMutation<\r\n    Delegation,\r\n    Error,\r\n    { flightDetails: FlightDetails; id: string },\r\n    FlightDetailsContext\r\n  >({\r\n    mutationFn: async ({ flightDetails, id }) => {\r\n      // This service method might need adjustment if flightDetails from form is Omit<FlightDetails, 'id'>\r\n      // For now, assuming it expects full FlightDetails (including ID for existing, or will generate for new)\r\n      const response = await delegationApiService.manageFlightDetails(\r\n        id,\r\n        flightDetails\r\n      );\r\n      return response;\r\n    },\r\n    onError: (err, variables, context) => {\r\n      if (context?.previousDelegation) {\r\n        queryClient.setQueryData(\r\n          delegationQueryKeys.detail(variables.id),\r\n          context.previousDelegation\r\n        );\r\n      }\r\n      console.error('Failed to manage delegation flight details:', err);\r\n    },\r\n    onMutate: async ({ flightDetails, id }) => {\r\n      await queryClient.cancelQueries({\r\n        queryKey: delegationQueryKeys.detail(id),\r\n      });\r\n      const previousDelegation = queryClient.getQueryData<Delegation>(\r\n        delegationQueryKeys.detail(id)\r\n      );\r\n      queryClient.setQueryData<Delegation>(\r\n        delegationQueryKeys.detail(id),\r\n        old => {\r\n          if (!old) return;\r\n          // This optimistic update assumes flightDetails is for arrival.\r\n          // A more robust solution would need to know if it's arrival or departure.\r\n          return { ...old, arrivalFlight: flightDetails };\r\n        }\r\n      );\r\n      return { previousDelegation };\r\n    },\r\n    onSettled: (_data, _error, variables) => {\r\n      queryClient.invalidateQueries({\r\n        queryKey: delegationQueryKeys.detail(variables.id),\r\n      });\r\n      queryClient.invalidateQueries({ queryKey: delegationQueryKeys.all });\r\n    },\r\n  });\r\n};\r\n\r\nexport const useDeleteDelegation = () => {\r\n  const queryClient = useQueryClient();\r\n  interface DeleteContext {\r\n    previousDelegationsList: Delegation[] | undefined;\r\n  }\r\n\r\n  return useMutation<string, Error, string, DeleteContext>({\r\n    mutationFn: async (id: string) => {\r\n      await delegationApiService.delete(id);\r\n      return id;\r\n    },\r\n    onError: (err, _id, context) => {\r\n      if (context?.previousDelegationsList) {\r\n        queryClient.setQueryData(\r\n          delegationQueryKeys.all,\r\n          context.previousDelegationsList\r\n        );\r\n      }\r\n      console.error('Failed to delete delegation:', err);\r\n    },\r\n    onMutate: async id => {\r\n      await queryClient.cancelQueries({ queryKey: delegationQueryKeys.all });\r\n      await queryClient.cancelQueries({\r\n        queryKey: delegationQueryKeys.detail(id),\r\n      });\r\n\r\n      const previousDelegationsList = queryClient.getQueryData<Delegation[]>(\r\n        delegationQueryKeys.all\r\n      );\r\n\r\n      queryClient.setQueryData<Delegation[]>(\r\n        delegationQueryKeys.all,\r\n        (old = []) => old.filter(delegation => delegation.id !== id)\r\n      );\r\n\r\n      queryClient.removeQueries({ queryKey: delegationQueryKeys.detail(id) });\r\n\r\n      return { previousDelegationsList };\r\n    },\r\n    onSettled: () => {\r\n      queryClient.invalidateQueries({ queryKey: delegationQueryKeys.all });\r\n    },\r\n  });\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;;;;;;;;AAID;AAAA;AAAA;AACA;AAEA;AAcA,6OAAiE,uBAAuB;AACxF,sTAA6E,0BAA0B;AAAvG;AACA;AACA;AACA;;;;;;;;;;AAKO,MAAM,iBAAiB,CAC5B;;IAKA,OAAO,CAAA,GAAA,uIAAA,CAAA,eAAY,AAAD,EAChB;WAAI,uJAAA,CAAA,sBAAmB,CAAC,GAAG;KAAC;uCAC5B;YACE,MAAM,SAAS,MAAM,2IAAA,CAAA,uBAAoB,CAAC,MAAM;YAChD,OAAO,OAAO,IAAI;QACpB;sCACA,cACA;QACE,WAAW;QACX,GAAG,OAAO;IACZ;AAEJ;GAlBa;;QAMJ,uIAAA,CAAA,eAAY;;;AAcd,MAAM,gBAAgB,CAAC;;IAC5B,OAAO,CAAA,GAAA,uIAAA,CAAA,eAAY,AAAD,EAChB;WAAI,uJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC;KAAI;sCACnC;YACE,OAAO,MAAM,2IAAA,CAAA,uBAAoB,CAAC,OAAO,CAAC;QAC5C;qCACA,cACA;QACE,SAAS,CAAC,CAAC;QACX,WAAW,IAAI,KAAK;IACtB;AAEJ;IAZa;;QACJ,uIAAA,CAAA,eAAY;;;AAcd,MAAM,+BAA+B,CAAC;;IAC3C,2EAA2E;IAC3E,MAAM,UAAU,CAAA,GAAA,gLAAA,CAAA,aAAU,AAAD,EAAE;QACzB,SAAS,CAAA,GAAA,uJAAA,CAAA,yCAAsC,AAAD,EAAE;IAClD;IAEA,MAAM,CAAC,iBAAiB,gBAAgB,cAAc,GAAG;IAEzD,yDAAyD;IACzD,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;oEAAE;YACjC,IACE,CAAC,iBAAiB,QAClB,CAAC,gBAAgB,QACjB,CAAC,eAAe,MAChB;gBACA;YACF;YAEA,IAAI;gBACF,qFAAqF;gBACrF,yDAAyD;gBACzD,MAAM,aAAa,gBAAgB,IAAI;gBACvC,OAAO,CAAA,GAAA,qJAAA,CAAA,mBAAgB,AAAD,EACpB,YACA,eAAe,IAAI,EACnB,cAAc,IAAI;YAEtB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,oCAAoC;gBAClD,MAAM;YACR;QACF;mEAAG;QAAC,iBAAiB;QAAM,gBAAgB;QAAM,eAAe;KAAK;IAErE,4EAA4E;IAC5E,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6DAAE;YAC1B,iBAAiB;YACjB,gBAAgB;YAChB,eAAe;QACjB;4DAAG;QACD,iBAAiB;QACjB,gBAAgB;QAChB,eAAe;KAChB;IAED,sDAAsD;IACtD,OAAO;QACL,MAAM;QACN,OACE,iBAAiB,SAAS,gBAAgB,SAAS,eAAe;QACpE,SACE,iBAAiB,WACjB,gBAAgB,WAChB,eAAe;QACjB,WACE,iBAAiB,aACjB,gBAAgB,aAChB,eAAe;QACjB,WACE,iBAAiB,aACjB,gBAAgB,aAChB,eAAe;QACjB;IACF;AACF;IA/Da;;QAEK,gLAAA,CAAA,aAAU;;;AAgErB,MAAM,wBAAwB;AAE9B,MAAM,sBAAsB;;IACjC,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAKjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAA0D;QACzE,UAAU;+CAAE,OAAO;gBACjB,MAAM,aAAa,sJAAA,CAAA,wBAAqB,CAAC,eAAe,CAAC;gBACzD,iFAAiF;gBACjF,OAAO,MAAM,2IAAA,CAAA,uBAAoB,CAAC,MAAM,CAAC;YAC3C;;QACA,OAAO;+CAAE,CAAC,KAAK,iBAAiB;gBAC9B,IAAI,SAAS,qBAAqB;oBAChC,YAAY,YAAY,CACtB,uJAAA,CAAA,sBAAmB,CAAC,GAAG,EACvB,QAAQ,mBAAmB;gBAE/B;gBACA,QAAQ,KAAK,CAAC,gCAAgC;gBAC9C,8CAA8C;gBAC9C,YAAY,iBAAiB,CAAC;oBAAE,UAAU,uJAAA,CAAA,sBAAmB,CAAC,GAAG;gBAAC;YACpE;;QACA,QAAQ;+CAAE,OAAO;gBACf,MAAM,YAAY,aAAa,CAAC;oBAAE,UAAU,uJAAA,CAAA,sBAAmB,CAAC,GAAG;gBAAC;gBACpE,MAAM,sBAAsB,YAAY,YAAY,CAClD,uJAAA,CAAA,sBAAmB,CAAC,GAAG;gBAGzB,YAAY,YAAY,CACtB,uJAAA,CAAA,sBAAmB,CAAC,GAAG;uDACvB,CAAC,MAAM,EAAE;wBACP,MAAM,SAAS,CAAC,WAAW,EAAE,KAAK,GAAG,IAAI;wBACzC,MAAM,MAAM,IAAI,OAAO,WAAW;wBAElC,MAAM,0BACJ,eAAe,oBAAoB,GAC/B;4BACE,IAAI,CAAC,sBAAsB,EAAE,KAAK,GAAG,IAAI;4BACzC,GAAG,eAAe,oBAAoB;wBACxC,IACA;wBAEN,MAAM,4BACJ,eAAe,sBAAsB,GACjC;4BACE,IAAI,CAAC,sBAAsB,EAAE,KAAK,GAAG,KAAK,GAAG;4BAC7C,GAAG,eAAe,sBAAsB;wBAC1C,IACA;wBAEN,MAAM,sBACJ,eAAe,SAAS,EAAE;+DAAI,CAAC,GAAG,QAAU,CAAC;oCAC3C,IAAI,CAAC,oBAAoB,EAAE,OAAO,CAAC,EAAE,OAAO;oCAC5C,MAAM,EAAE,IAAI;oCACZ,OAAO,EAAE,KAAK,IAAI;oCAClB,OAAO,EAAE,KAAK;gCAChB,CAAC;iEAAM,EAAE;wBAEX,MAAM,uBAAmC;4BACvC,eAAe,2BAA2B;4BAC1C,WAAW;4BACX,WAAW;4BACX,iBAAiB,6BAA6B;4BAC9C,SACE,eAAe,OAAO,EAAE;mEAAI,CAAA,IAAK,CAAC;wCAChC,WAAW;wCACX,WAAW;wCACX,cAAc;wCACd,YAAY,EAAE,UAAU;wCACxB,IAAI,CAAC,kBAAkB,EAAE,OAAO,CAAC,EAAE,EAAE,UAAU,EAAE;wCACjD,OAAO,EAAE,KAAK,IAAI;wCAClB,WAAW;oCACb,CAAC;qEAAM,EAAE;4BACX,cAAc,eAAe,YAAY;4BACzC,YAAY,eAAe,UAAU;4BACrC,SACE,eAAe,OAAO,EAAE;mEAAI,CAAA,IAAK,CAAC;wCAChC,WAAW;wCACX,WAAW;wCACX,cAAc;wCACd,YAAY,EAAE,UAAU;wCACxB,IAAI,CAAC,kBAAkB,EAAE,OAAO,CAAC,EAAE,EAAE,UAAU,EAAE;wCACjD,OAAO,EAAE,KAAK,IAAI;wCAClB,WAAW;oCACb,CAAC;qEAAM,EAAE;4BACX,WAAW,eAAe,SAAS;4BACnC,IAAI;4BACJ,UAAU,eAAe,QAAQ,IAAI;4BACrC,gBAAgB,eAAe,cAAc,IAAI;4BACjD,cAAc,eAAe,YAAY,IAAI;4BAC7C,UAAU,eAAe,QAAQ;4BACjC,OAAO,eAAe,KAAK,IAAI;4BAC/B,QAAQ,eAAe,MAAM,IAAI;4BACjC,eAAe,EAAE;4BACjB,WAAW;4BACX,UACE,eAAe,QAAQ,EAAE;mEAAI,CAAA,IAAK,CAAC;wCACjC,cAAc,EAAE,YAAY;wCAC5B,WAAW;wCACX,WAAW;wCACX,cAAc;wCACd,IAAI,CAAC,mBAAmB,EAAE,OAAO,CAAC,EAAE,EAAE,SAAS,EAAE;wCACjD,OAAO,EAAE,KAAK,IAAI;wCAClB,YAAY,EAAE,UAAU,IAAI;wCAC5B,WAAW;wCACX,WAAW,EAAE,SAAS;oCACxB,CAAC;qEAAM,EAAE;wBACb;wBACA,OAAO;+BAAI;4BAAK;yBAAqB;oBACvC;;gBAEF,OAAO;oBAAE;gBAAoB;YAC/B;;QACA,SAAS;+CAAE;gBACT,4DAA4D;gBAC5D,YAAY,iBAAiB,CAAC;oBAAE,UAAU,uJAAA,CAAA,sBAAmB,CAAC,GAAG;gBAAC;YACpE;;IACF;AACF;IAvHa;;QACS,yLAAA,CAAA,iBAAc;QAK3B,iLAAA,CAAA,cAAW;;;AAmHb,MAAM,sBAAsB;;IACjC,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAMjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAKf;QACA,UAAU;+CAAE,OAAO,EAAE,IAAI,EAAE,EAAE,EAAE;gBAC7B,iFAAiF;gBACjF,OAAO,MAAM,2IAAA,CAAA,uBAAoB,CAAC,MAAM,CAAC,IAAI;YAC/C;;QACA,OAAO;+CAAE,CAAC,KAAK,WAAW;gBACxB,IAAI,SAAS,oBAAoB;oBAC/B,YAAY,YAAY,CACtB,uJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC,UAAU,EAAE,GACvC,QAAQ,kBAAkB;gBAE9B;gBACA,IAAI,SAAS,yBAAyB;oBACpC,YAAY,YAAY,CACtB,uJAAA,CAAA,sBAAmB,CAAC,GAAG,EACvB,QAAQ,uBAAuB;gBAEnC;gBACA,QAAQ,KAAK,CAAC,gCAAgC;gBAC9C,8CAA8C;gBAC9C,YAAY,iBAAiB,CAAC;oBAC5B,UAAU,uJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC,UAAU,EAAE;gBACnD;gBACA,YAAY,iBAAiB,CAAC;oBAAE,UAAU,uJAAA,CAAA,sBAAmB,CAAC,GAAG;gBAAC;YACpE;;QACA,QAAQ;+CAAE,OAAO,EAAE,IAAI,EAAE,EAAE,EAAE;gBAC3B,kCAAkC;gBAClC,MAAM,YAAY,aAAa,CAAC;oBAAE,UAAU,uJAAA,CAAA,sBAAmB,CAAC,GAAG;gBAAC;gBACpE,MAAM,YAAY,aAAa,CAAC;oBAC9B,UAAU,uJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC;gBACvC;gBAEA,MAAM,qBAAqB,YAAY,YAAY,CACjD,uJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC;gBAE7B,MAAM,0BAA0B,YAAY,YAAY,CACtD,uJAAA,CAAA,sBAAmB,CAAC,GAAG;gBAGzB,YAAY,YAAY,CACtB,uJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC;uDAC3B,CAAA;wBACE,IAAI,CAAC,KAAK;wBACV,MAAM,MAAM,IAAI,OAAO,WAAW;wBAElC,2EAA2E;wBAC3E,MAAM,oBAAgC;4BACpC,GAAG,GAAG;4BACN,gCAAgC;4BAChC,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EAC3B,KAAK,oBAAoB,KAAK,OAC1B,KAAK,sCAAsC;+BAC3C,KAAK,oBAAoB,KAAK,YAC5B,IAAI,aAAa,GACjB;gCACE,SACE,KAAK,oBAAoB,CAAC,OAAO,IACjC,IAAI,aAAa,EAAE,WACnB;gCACF,UACE,KAAK,oBAAoB,CAAC,QAAQ,IAClC,IAAI,aAAa,EAAE,YACnB;gCACF,cACE,KAAK,oBAAoB,CAAC,YAAY,IACtC,IAAI,aAAa,EAAE,gBACnB;gCACF,IACE,IAAI,aAAa,EAAE,MAAM,CAAC,eAAe,EAAE,KAAK,GAAG,IAAI;gCACzD,OACE,KAAK,oBAAoB,CAAC,KAAK,IAC/B,IAAI,aAAa,EAAE,SACnB;gCACF,UACE,KAAK,oBAAoB,CAAC,QAAQ,IAClC,IAAI,aAAa,EAAE,YACnB;4BACJ,EAAE,mCAAmC;;4BAE7C,iBAAiB,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EAC7B,KAAK,sBAAsB,KAAK,OAC5B,KAAK,sCAAsC;+BAC3C,KAAK,sBAAsB,KAAK,YAC9B,IAAI,eAAe,GACnB;gCACE,SACE,KAAK,sBAAsB,CAAC,OAAO,IACnC,IAAI,eAAe,EAAE,WACrB;gCACF,UACE,KAAK,sBAAsB,CAAC,QAAQ,IACpC,IAAI,eAAe,EAAE,YACrB;gCACF,cACE,KAAK,sBAAsB,CAAC,YAAY,IACxC,IAAI,eAAe,EAAE,gBACrB;gCACF,IACE,IAAI,eAAe,EAAE,MACrB,CAAC,eAAe,EAAE,KAAK,GAAG,IAAI;gCAChC,OACE,KAAK,sBAAsB,CAAC,KAAK,IACjC,IAAI,eAAe,EAAE,SACrB;gCACF,UACE,KAAK,sBAAsB,CAAC,QAAQ,IACpC,IAAI,eAAe,EAAE,YACrB;4BACJ,EAAE,mCAAmC;;4BAE7C,cAAc,KAAK,YAAY,IAAI,IAAI,YAAY;4BACnD,YAAY,KAAK,UAAU,IAAI,IAAI,UAAU;4BAC7C,mDAAmD;4BACnD,WAAW,KAAK,SAAS,IAAI,IAAI,SAAS;4BAC1C,UAAU,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,QAAQ,IAAI,IAAI,QAAQ;4BACvD,gBAAgB,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EAC5B,KAAK,cAAc,IAAI,IAAI,cAAc;4BAE3C,cAAc,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EAC1B,KAAK,YAAY,IAAI,IAAI,YAAY;4BAEvC,UAAU,KAAK,QAAQ,IAAI,IAAI,QAAQ;4BACvC,OAAO,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,KAAK,IAAI,IAAI,KAAK;4BAC9C,QAAQ,AAAC,KAAK,MAAM,IAA+B,IAAI,MAAM;4BAC7D,WAAW;wBAGb;wBACA,OAAO;oBACT;;gBAGF,YAAY,YAAY,CACtB,uJAAA,CAAA,sBAAmB,CAAC,GAAG;uDACvB,CAAC,UAAU,EAAE,GACX,QAAQ,GAAG;+DAAC,CAAA,aACV,WAAW,EAAE,KAAK,KACd,YAAY,YAAY,CACtB,uJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC,QACxB,aACL;;;gBAIV,OAAO;oBAAE;oBAAoB;gBAAwB;YACvD;;QACA,SAAS;+CAAE,CAAC,OAAO,QAAQ;gBACzB,wCAAwC;gBACxC,YAAY,iBAAiB,CAAC;oBAC5B,UAAU,uJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC,UAAU,EAAE;gBACnD;gBACA,YAAY,iBAAiB,CAAC;oBAAE,UAAU,uJAAA,CAAA,sBAAmB,CAAC,GAAG;gBAAC;YACpE;;IACF;AACF;IAtKa;;QACS,yLAAA,CAAA,iBAAc;QAM3B,iLAAA,CAAA,cAAW;;;AAiKb,MAAM,4BAA4B;;IACvC,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAKjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAKf;QACA,UAAU;qDAAE,OAAO,EAAE,EAAE,EAAE,MAAM,EAAE,kBAAkB,EAAE;gBACnD,MAAM,WAAW,MAAM,2IAAA,CAAA,uBAAoB,CAAC,YAAY,CACtD,IACA,QACA;gBAEF,OAAO;YACT;;QACA,OAAO;qDAAE,CAAC,KAAK,WAAW;gBACxB,IAAI,SAAS,oBAAoB;oBAC/B,YAAY,YAAY,CACtB,uJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC,UAAU,EAAE,GACvC,QAAQ,kBAAkB;gBAE9B;gBACA,QAAQ,KAAK,CAAC,uCAAuC;YACvD;;QACA,QAAQ;qDAAE,OAAO,EAAE,EAAE,EAAE,MAAM,EAAE;gBAC7B,MAAM,YAAY,aAAa,CAAC;oBAC9B,UAAU,uJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC;gBACvC;gBACA,MAAM,qBAAqB,YAAY,YAAY,CACjD,uJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC;gBAE7B,YAAY,YAAY,CACtB,uJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC;6DAC3B,CAAA,MAAQ,MAAM;4BAAE,GAAG,GAAG;4BAAE,QAAQ;wBAAO,IAAI;;gBAE7C,OAAO;oBAAE;gBAAmB;YAC9B;;QACA,SAAS;qDAAE,CAAC,OAAO,QAAQ;gBACzB,YAAY,iBAAiB,CAAC;oBAC5B,UAAU,uJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC,UAAU,EAAE;gBACnD;gBACA,YAAY,iBAAiB,CAAC;oBAAE,UAAU,uJAAA,CAAA,sBAAmB,CAAC,GAAG;gBAAC;YACpE;;IACF;AACF;IAjDa;;QACS,yLAAA,CAAA,iBAAc;QAK3B,iLAAA,CAAA,cAAW;;;AA6Cb,MAAM,mCAAmC;;IAC9C,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAKjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAKf;QACA,UAAU;4DAAE,OAAO,EAAE,aAAa,EAAE,EAAE,EAAE;gBACtC,oGAAoG;gBACpG,wGAAwG;gBACxG,MAAM,WAAW,MAAM,2IAAA,CAAA,uBAAoB,CAAC,mBAAmB,CAC7D,IACA;gBAEF,OAAO;YACT;;QACA,OAAO;4DAAE,CAAC,KAAK,WAAW;gBACxB,IAAI,SAAS,oBAAoB;oBAC/B,YAAY,YAAY,CACtB,uJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC,UAAU,EAAE,GACvC,QAAQ,kBAAkB;gBAE9B;gBACA,QAAQ,KAAK,CAAC,+CAA+C;YAC/D;;QACA,QAAQ;4DAAE,OAAO,EAAE,aAAa,EAAE,EAAE,EAAE;gBACpC,MAAM,YAAY,aAAa,CAAC;oBAC9B,UAAU,uJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC;gBACvC;gBACA,MAAM,qBAAqB,YAAY,YAAY,CACjD,uJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC;gBAE7B,YAAY,YAAY,CACtB,uJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC;oEAC3B,CAAA;wBACE,IAAI,CAAC,KAAK;wBACV,+DAA+D;wBAC/D,0EAA0E;wBAC1E,OAAO;4BAAE,GAAG,GAAG;4BAAE,eAAe;wBAAc;oBAChD;;gBAEF,OAAO;oBAAE;gBAAmB;YAC9B;;QACA,SAAS;4DAAE,CAAC,OAAO,QAAQ;gBACzB,YAAY,iBAAiB,CAAC;oBAC5B,UAAU,uJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC,UAAU,EAAE;gBACnD;gBACA,YAAY,iBAAiB,CAAC;oBAAE,UAAU,uJAAA,CAAA,sBAAmB,CAAC,GAAG;gBAAC;YACpE;;IACF;AACF;IAvDa;;QACS,yLAAA,CAAA,iBAAc;QAK3B,iLAAA,CAAA,cAAW;;;AAmDb,MAAM,sBAAsB;;IACjC,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAKjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAwC;QACvD,UAAU;+CAAE,OAAO;gBACjB,MAAM,2IAAA,CAAA,uBAAoB,CAAC,MAAM,CAAC;gBAClC,OAAO;YACT;;QACA,OAAO;+CAAE,CAAC,KAAK,KAAK;gBAClB,IAAI,SAAS,yBAAyB;oBACpC,YAAY,YAAY,CACtB,uJAAA,CAAA,sBAAmB,CAAC,GAAG,EACvB,QAAQ,uBAAuB;gBAEnC;gBACA,QAAQ,KAAK,CAAC,gCAAgC;YAChD;;QACA,QAAQ;+CAAE,OAAM;gBACd,MAAM,YAAY,aAAa,CAAC;oBAAE,UAAU,uJAAA,CAAA,sBAAmB,CAAC,GAAG;gBAAC;gBACpE,MAAM,YAAY,aAAa,CAAC;oBAC9B,UAAU,uJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC;gBACvC;gBAEA,MAAM,0BAA0B,YAAY,YAAY,CACtD,uJAAA,CAAA,sBAAmB,CAAC,GAAG;gBAGzB,YAAY,YAAY,CACtB,uJAAA,CAAA,sBAAmB,CAAC,GAAG;uDACvB,CAAC,MAAM,EAAE,GAAK,IAAI,MAAM;+DAAC,CAAA,aAAc,WAAW,EAAE,KAAK;;;gBAG3D,YAAY,aAAa,CAAC;oBAAE,UAAU,uJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC;gBAAI;gBAErE,OAAO;oBAAE;gBAAwB;YACnC;;QACA,SAAS;+CAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU,uJAAA,CAAA,sBAAmB,CAAC,GAAG;gBAAC;YACpE;;IACF;AACF;IA3Ca;;QACS,yLAAA,CAAA,iBAAc;QAK3B,iLAAA,CAAA,cAAW", "debugId": null}}, {"offset": {"line": 1788, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/hooks/ui/useNotifications.ts"], "sourcesContent": ["/**\r\n * @file Custom hook for notification management using Zustand AppStore\r\n * @module hooks/useNotifications\r\n */\r\n\r\nimport { useCallback } from 'react';\r\n\r\nimport { useAppStore } from '@/lib/stores/zustand/appStore';\r\nimport { undefinedToNull } from '../../lib/utils/typeHelpers';\r\n\r\n/**\r\n * Custom hook for simplified notification management\r\n * Provides convenient methods for showing different types of notifications\r\n */\r\nexport const useNotifications = () => {\r\n  const addNotification = useAppStore(state => state.addNotification);\r\n  const removeNotification = useAppStore(state => state.removeNotification);\r\n  const clearAllNotifications = useAppStore(\r\n    state => state.clearAllNotifications\r\n  );\r\n  const unreadCount = useAppStore(state => state.unreadNotificationCount);\r\n\r\n  /**\r\n   * Show a success notification\r\n   */\r\n  const showSuccess = useCallback(\r\n    (message: string) => {\r\n      addNotification({\r\n        message,\r\n        type: 'success',\r\n      });\r\n    },\r\n    [addNotification]\r\n  );\r\n\r\n  /**\r\n   * Show an error notification\r\n   */\r\n  const showError = useCallback(\r\n    (message: string) => {\r\n      addNotification({\r\n        message,\r\n        type: 'error',\r\n      });\r\n    },\r\n    [addNotification]\r\n  );\r\n\r\n  /**\r\n   * Show a warning notification\r\n   */\r\n  const showWarning = useCallback(\r\n    (message: string) => {\r\n      addNotification({\r\n        message,\r\n        type: 'warning',\r\n      });\r\n    },\r\n    [addNotification]\r\n  );\r\n\r\n  /**\r\n   * Show an info notification\r\n   */\r\n  const showInfo = useCallback(\r\n    (message: string) => {\r\n      addNotification({\r\n        message,\r\n        type: 'info',\r\n      });\r\n    },\r\n    [addNotification]\r\n  );\r\n\r\n  /**\r\n   * Show a notification for API operation results\r\n   */\r\n  const showApiResult = useCallback(\r\n    (success: boolean, successMessage: string, errorMessage: string) => {\r\n      if (success) {\r\n        showSuccess(successMessage);\r\n      } else {\r\n        showError(errorMessage);\r\n      }\r\n    },\r\n    [showSuccess, showError]\r\n  );\r\n\r\n  /**\r\n   * Show a notification with auto-dismiss after specified time\r\n   */\r\n  const showTemporary = useCallback(\r\n    (\r\n      type: 'error' | 'info' | 'success' | 'warning',\r\n      message: string,\r\n      dismissAfter = 5000\r\n    ) => {\r\n      addNotification({ message, type });\r\n\r\n      // Auto-dismiss after specified time\r\n      setTimeout(() => {\r\n        // Note: This is a simplified approach. In a real implementation,\r\n        // you might want to store the notification ID and remove specifically that one\r\n        const notifications = useAppStore.getState().notifications;\r\n        const latestNotification = notifications.at(-1);\r\n        if (latestNotification && latestNotification.message === message) {\r\n          removeNotification(latestNotification.id);\r\n        }\r\n      }, dismissAfter);\r\n    },\r\n    [addNotification, removeNotification]\r\n  );\r\n\r\n  /**\r\n   * Show a loading notification that can be updated\r\n   */\r\n  const showLoading = useCallback(\r\n    (message = 'Loading...') => {\r\n      addNotification({\r\n        message,\r\n        type: 'info',\r\n      });\r\n\r\n      // Return the notification ID for potential updates\r\n      const notifications = useAppStore.getState().notifications;\r\n      return notifications.at(-1)?.id;\r\n    },\r\n    [addNotification]\r\n  );\r\n\r\n  /**\r\n   * Update a loading notification to success or error\r\n   */\r\n  const updateLoadingNotification = useCallback(\r\n    (notificationId: string, success: boolean, message: string) => {\r\n      removeNotification(notificationId);\r\n      if (success) {\r\n        showSuccess(message);\r\n      } else {\r\n        showError(message);\r\n      }\r\n    },\r\n    [removeNotification, showSuccess, showError]\r\n  );\r\n\r\n  return {\r\n    clearAllNotifications,\r\n    // Store methods\r\n    removeNotification,\r\n    // Advanced methods\r\n    showApiResult,\r\n    showError,\r\n\r\n    showInfo,\r\n    showLoading,\r\n    // Basic notification methods\r\n    showSuccess,\r\n    showTemporary,\r\n\r\n    showWarning,\r\n    unreadCount,\r\n    updateLoadingNotification,\r\n  };\r\n};\r\n\r\n/**\r\n * Enhanced notification hook with WorkHub-specific notification types\r\n */\r\nexport const useWorkHubNotifications = () => {\r\n  const {\r\n    clearAllNotifications,\r\n    removeNotification,\r\n    showError,\r\n    showInfo,\r\n    showSuccess,\r\n    showWarning,\r\n    unreadCount,\r\n  } = useNotifications();\r\n\r\n  /**\r\n   * Show delegation-related notifications\r\n   */\r\n  const showDelegationUpdate = useCallback(\r\n    (message: string, actionUrl?: string) => {\r\n      const addNotification = useAppStore.getState().addNotification;\r\n      addNotification({\r\n        ...(actionUrl && { actionUrl }),\r\n        category: 'delegation',\r\n        message,\r\n        type: 'delegation-update',\r\n      });\r\n    },\r\n    []\r\n  );\r\n\r\n  /**\r\n   * Show vehicle maintenance notifications\r\n   */\r\n  const showVehicleMaintenance = useCallback(\r\n    (message: string, actionUrl?: string) => {\r\n      const addNotification = useAppStore.getState().addNotification;\r\n      addNotification({\r\n        ...(actionUrl && { actionUrl }),\r\n        category: 'vehicle',\r\n        message,\r\n        type: 'vehicle-maintenance',\r\n      });\r\n    },\r\n    []\r\n  );\r\n\r\n  /**\r\n   * Show task assignment notifications\r\n   */\r\n  const showTaskAssigned = useCallback(\r\n    (message: string, actionUrl?: string) => {\r\n      const addNotification = useAppStore.getState().addNotification;\r\n      addNotification({\r\n        ...(actionUrl && { actionUrl }),\r\n        category: 'task',\r\n        message,\r\n        type: 'task-assigned',\r\n      });\r\n    },\r\n    []\r\n  );\r\n\r\n  /**\r\n   * Show employee update notifications\r\n   */\r\n  const showEmployeeUpdate = useCallback(\r\n    (message: string, actionUrl?: string) => {\r\n      const addNotification = useAppStore.getState().addNotification;\r\n      addNotification({\r\n        ...(actionUrl && { actionUrl }),\r\n        category: 'employee',\r\n        message,\r\n        type: 'employee-update',\r\n      });\r\n    },\r\n    []\r\n  );\r\n\r\n  return {\r\n    clearAllNotifications,\r\n    // Management\r\n    removeNotification,\r\n    // WorkHub-specific notifications\r\n    showDelegationUpdate,\r\n    showEmployeeUpdate,\r\n\r\n    showError,\r\n    showInfo,\r\n    // Basic notifications\r\n    showSuccess,\r\n    showTaskAssigned,\r\n\r\n    showVehicleMaintenance,\r\n    showWarning,\r\n    unreadCount,\r\n  };\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAED;AAEA;;;;AAOO,MAAM,mBAAmB;;IAC9B,MAAM,kBAAkB,CAAA,GAAA,8IAAA,CAAA,cAAW,AAAD;yDAAE,CAAA,QAAS,MAAM,eAAe;;IAClE,MAAM,qBAAqB,CAAA,GAAA,8IAAA,CAAA,cAAW,AAAD;4DAAE,CAAA,QAAS,MAAM,kBAAkB;;IACxE,MAAM,wBAAwB,CAAA,GAAA,8IAAA,CAAA,cAAW,AAAD;+DACtC,CAAA,QAAS,MAAM,qBAAqB;;IAEtC,MAAM,cAAc,CAAA,GAAA,8IAAA,CAAA,cAAW,AAAD;qDAAE,CAAA,QAAS,MAAM,uBAAuB;;IAEtE;;GAEC,GACD,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAC5B,CAAC;YACC,gBAAgB;gBACd;gBACA,MAAM;YACR;QACF;oDACA;QAAC;KAAgB;IAGnB;;GAEC,GACD,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAC1B,CAAC;YACC,gBAAgB;gBACd;gBACA,MAAM;YACR;QACF;kDACA;QAAC;KAAgB;IAGnB;;GAEC,GACD,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAC5B,CAAC;YACC,gBAAgB;gBACd;gBACA,MAAM;YACR;QACF;oDACA;QAAC;KAAgB;IAGnB;;GAEC,GACD,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDACzB,CAAC;YACC,gBAAgB;gBACd;gBACA,MAAM;YACR;QACF;iDACA;QAAC;KAAgB;IAGnB;;GAEC,GACD,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAC9B,CAAC,SAAkB,gBAAwB;YACzC,IAAI,SAAS;gBACX,YAAY;YACd,OAAO;gBACL,UAAU;YACZ;QACF;sDACA;QAAC;QAAa;KAAU;IAG1B;;GAEC,GACD,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAC9B,CACE,MACA,SACA,eAAe,IAAI;YAEnB,gBAAgB;gBAAE;gBAAS;YAAK;YAEhC,oCAAoC;YACpC;+DAAW;oBACT,iEAAiE;oBACjE,+EAA+E;oBAC/E,MAAM,gBAAgB,8IAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,aAAa;oBAC1D,MAAM,qBAAqB,cAAc,EAAE,CAAC,CAAC;oBAC7C,IAAI,sBAAsB,mBAAmB,OAAO,KAAK,SAAS;wBAChE,mBAAmB,mBAAmB,EAAE;oBAC1C;gBACF;8DAAG;QACL;sDACA;QAAC;QAAiB;KAAmB;IAGvC;;GAEC,GACD,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAC5B,CAAC,UAAU,YAAY;YACrB,gBAAgB;gBACd;gBACA,MAAM;YACR;YAEA,mDAAmD;YACnD,MAAM,gBAAgB,8IAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,aAAa;YAC1D,OAAO,cAAc,EAAE,CAAC,CAAC,IAAI;QAC/B;oDACA;QAAC;KAAgB;IAGnB;;GAEC,GACD,MAAM,4BAA4B,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mEAC1C,CAAC,gBAAwB,SAAkB;YACzC,mBAAmB;YACnB,IAAI,SAAS;gBACX,YAAY;YACd,OAAO;gBACL,UAAU;YACZ;QACF;kEACA;QAAC;QAAoB;QAAa;KAAU;IAG9C,OAAO;QACL;QACA,gBAAgB;QAChB;QACA,mBAAmB;QACnB;QACA;QAEA;QACA;QACA,6BAA6B;QAC7B;QACA;QAEA;QACA;QACA;IACF;AACF;GArJa;;QACa,8IAAA,CAAA,cAAW;QACR,8IAAA,CAAA,cAAW;QACR,8IAAA,CAAA,cAAW;QAGrB,8IAAA,CAAA,cAAW;;;AAoJ1B,MAAM,0BAA0B;;IACrC,MAAM,EACJ,qBAAqB,EACrB,kBAAkB,EAClB,SAAS,EACT,QAAQ,EACR,WAAW,EACX,WAAW,EACX,WAAW,EACZ,GAAG;IAEJ;;GAEC,GACD,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qEACrC,CAAC,SAAiB;YAChB,MAAM,kBAAkB,8IAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,eAAe;YAC9D,gBAAgB;gBACd,GAAI,aAAa;oBAAE;gBAAU,CAAC;gBAC9B,UAAU;gBACV;gBACA,MAAM;YACR;QACF;oEACA,EAAE;IAGJ;;GAEC,GACD,MAAM,yBAAyB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uEACvC,CAAC,SAAiB;YAChB,MAAM,kBAAkB,8IAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,eAAe;YAC9D,gBAAgB;gBACd,GAAI,aAAa;oBAAE;gBAAU,CAAC;gBAC9B,UAAU;gBACV;gBACA,MAAM;YACR;QACF;sEACA,EAAE;IAGJ;;GAEC,GACD,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iEACjC,CAAC,SAAiB;YAChB,MAAM,kBAAkB,8IAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,eAAe;YAC9D,gBAAgB;gBACd,GAAI,aAAa;oBAAE;gBAAU,CAAC;gBAC9B,UAAU;gBACV;gBACA,MAAM;YACR;QACF;gEACA,EAAE;IAGJ;;GAEC,GACD,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mEACnC,CAAC,SAAiB;YAChB,MAAM,kBAAkB,8IAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,eAAe;YAC9D,gBAAgB;gBACd,GAAI,aAAa;oBAAE;gBAAU,CAAC;gBAC9B,UAAU;gBACV;gBACA,MAAM;YACR;QACF;kEACA,EAAE;IAGJ,OAAO;QACL;QACA,aAAa;QACb;QACA,iCAAiC;QACjC;QACA;QAEA;QACA;QACA,sBAAsB;QACtB;QACA;QAEA;QACA;QACA;IACF;AACF;IA7Fa;;QASP", "debugId": null}}, {"offset": {"line": 2051, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/lib/stores/queries/useEmployees.ts"], "sourcesContent": ["/**\r\n * @file TanStack Query hooks for Employee-related data.\r\n * These hooks manage fetching, caching, and mutating employee data,\r\n * integrating with the EmployeeApiService and EmployeeTransformer.\r\n * @module stores/queries/useEmployees\r\n */\r\n\r\nimport type { UseQueryOptions } from '@tanstack/react-query';\r\n\r\nimport { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';\r\n\r\nimport type {\r\n  CreateEmployeeData,\r\n  DriverAvailabilityPrisma,\r\n  Employee,\r\n} from '@/lib/types/domain';\r\n\r\nimport { useCrudQuery } from '@/hooks/api/useSmartQuery'; // Adjusted import path\r\nimport { useNotifications } from '@/hooks/ui/useNotifications';\r\nimport { EmployeeTransformer } from '@/lib/transformers/employeeTransformer';\r\nimport { undefinedToNull } from '@/lib/utils/typeHelpers';\r\n\r\nimport { employeeApiService } from '../../api/services/apiServiceFactory'; // Use centralized service\r\n\r\n/**\r\n * Centralized query keys for employees to ensure consistency.\r\n */\r\nexport const employeeQueryKeys = {\r\n  all: ['employees'] as const,\r\n  detail: (id: string) => ['employees', id] as const,\r\n  // Add other specific query keys as needed\r\n};\r\n\r\n/**\r\n * Custom hook to fetch all employees.\r\n * @param options - Optional React Query options\r\n * @returns Query result containing an array of Employee domain models.\r\n */\r\nexport const useEmployees = (\r\n  options?: Omit<UseQueryOptions<Employee[], Error>, 'queryFn' | 'queryKey'>\r\n) => {\r\n  return useCrudQuery<Employee[], Error>(\r\n    [...employeeQueryKeys.all], // queryKey - spread for mutability\r\n    async () => {\r\n      const response = await employeeApiService.getAll();\r\n      return response.data;\r\n    },\r\n    'employee', // entityType\r\n    {\r\n      staleTime: 5 * 60 * 1000, // 5 minutes\r\n      ...options,\r\n    }\r\n  );\r\n};\r\n\r\n/**\r\n * Custom hook to fetch employees filtered by role.\r\n * @param role - Optional role filter (e.g., 'driver', 'manager')\r\n * @param options - Optional React Query options\r\n * @returns Query result containing an array of Employee domain models filtered by role.\r\n */\r\nexport const useEmployeesByRole = (\r\n  role?: string,\r\n  options?: Omit<UseQueryOptions<Employee[], Error>, 'queryFn' | 'queryKey'>\r\n) => {\r\n  return useCrudQuery<Employee[], Error>(\r\n    role ? ['employees', 'role', role] : [...employeeQueryKeys.all],\r\n    async () => {\r\n      if (role) {\r\n        const response = await employeeApiService.getByRole(role);\r\n        return response;\r\n      } else {\r\n        const response = await employeeApiService.getAll();\r\n        return response.data;\r\n      }\r\n    },\r\n    'employee', // entityType for WebSocket events\r\n    {\r\n      staleTime: 5 * 60 * 1000, // 5 minutes\r\n      ...options,\r\n    }\r\n  );\r\n};\r\n\r\n/**\r\n * Custom hook to fetch a single employee by their ID.\r\n * @param id - The ID of the employee to fetch.\r\n * @returns Query result containing a single Employee domain model or undefined.\r\n */\r\nexport const useEmployee = (id: string) => {\r\n  return useCrudQuery<Employee, Error>(\r\n    [...employeeQueryKeys.detail(id)],\r\n    async () => {\r\n      return await employeeApiService.getById(id);\r\n    },\r\n    'employee', // entityType for WebSocket events\r\n    {\r\n      enabled: !!id, // Only run query if id is truthy\r\n      staleTime: 5 * 60 * 1000, // 5 minutes\r\n    }\r\n  );\r\n};\r\n\r\n/**\r\n * Custom hook for creating a new employee.\r\n * Includes optimistic updates and cache invalidation.\r\n * @returns Mutation result for creating an employee.\r\n */\r\nexport const useCreateEmployee = () => {\r\n  const queryClient = useQueryClient();\r\n  const { showError, showSuccess } = useNotifications();\r\n\r\n  interface CreateContext {\r\n    previousEmployees: Employee[] | undefined;\r\n  }\r\n\r\n  return useMutation<Employee, Error, CreateEmployeeData, CreateContext>({\r\n    mutationFn: async (employeeData: CreateEmployeeData) => {\r\n      const request = EmployeeTransformer.toCreateRequest(employeeData);\r\n      return await employeeApiService.create(request); // Removed redundant EmployeeTransformer.fromApi\r\n    },\r\n    onError: (err, _newEmployeeData, context) => {\r\n      if (context?.previousEmployees) {\r\n        queryClient.setQueryData(\r\n          employeeQueryKeys.all,\r\n          context.previousEmployees\r\n        );\r\n      }\r\n      showError(\r\n        `Failed to create employee: ${err.message || 'Unknown error occurred'}`\r\n      );\r\n    },\r\n    onMutate: async newEmployeeData => {\r\n      await queryClient.cancelQueries({ queryKey: employeeQueryKeys.all });\r\n      const previousEmployees = queryClient.getQueryData<Employee[]>(\r\n        employeeQueryKeys.all\r\n      );\r\n\r\n      queryClient.setQueryData<Employee[]>(\r\n        employeeQueryKeys.all,\r\n        (old = []) => {\r\n          const tempId = 'optimistic-' + Date.now().toString();\r\n          const now = new Date().toISOString();\r\n          const optimisticEmployee: Employee = {\r\n            availability: undefinedToNull(newEmployeeData.availability),\r\n            contactEmail: undefinedToNull(newEmployeeData.contactEmail),\r\n            contactInfo: newEmployeeData.contactInfo,\r\n            contactMobile: undefinedToNull(newEmployeeData.contactMobile),\r\n            contactPhone: undefinedToNull(newEmployeeData.contactPhone),\r\n            createdAt: now,\r\n            currentLocation: undefinedToNull(newEmployeeData.currentLocation),\r\n            department: undefinedToNull(newEmployeeData.department),\r\n            employeeId: newEmployeeData.employeeId,\r\n            fullName: newEmployeeData.fullName || newEmployeeData.name,\r\n            generalAssignments: newEmployeeData.generalAssignments || [],\r\n            hireDate: undefinedToNull(newEmployeeData.hireDate),\r\n            id: Number(tempId.replace('optimistic-', '')), // Attempt number ID\r\n            name: newEmployeeData.name,\r\n            notes: undefinedToNull(newEmployeeData.notes),\r\n            position: undefinedToNull(newEmployeeData.position),\r\n            profileImageUrl: undefinedToNull(newEmployeeData.profileImageUrl),\r\n            role: newEmployeeData.role,\r\n            shiftSchedule: undefinedToNull(newEmployeeData.shiftSchedule),\r\n            skills: newEmployeeData.skills || [],\r\n            status: undefinedToNull(newEmployeeData.status),\r\n            updatedAt: now,\r\n            ...(newEmployeeData.workingHours !== undefined && {\r\n              workingHours: undefinedToNull(newEmployeeData.workingHours),\r\n            }),\r\n          };\r\n          return [...old, optimisticEmployee];\r\n        }\r\n      );\r\n\r\n      return { previousEmployees };\r\n    },\r\n    onSettled: () => {\r\n      queryClient.invalidateQueries({ queryKey: employeeQueryKeys.all });\r\n    },\r\n    onSuccess: data => {\r\n      showSuccess(`Employee \"${data.name}\" has been created successfully!`);\r\n    },\r\n  });\r\n};\r\n\r\n/**\r\n * Custom hook for updating an existing employee.\r\n * Includes optimistic updates and rollback on error.\r\n * @returns Mutation result for updating an employee.\r\n */\r\nexport const useUpdateEmployee = () => {\r\n  const queryClient = useQueryClient();\r\n\r\n  interface UpdateContext {\r\n    previousEmployee: Employee | undefined;\r\n    previousEmployeesList: Employee[] | undefined;\r\n  }\r\n\r\n  return useMutation<\r\n    Employee,\r\n    Error,\r\n    { data: Partial<CreateEmployeeData>; id: string },\r\n    UpdateContext\r\n  >({\r\n    mutationFn: async ({ data, id }) => {\r\n      const request = EmployeeTransformer.toUpdateRequest(data); // Removed cast\r\n      return await employeeApiService.update(id, request); // Removed redundant EmployeeTransformer.fromApi\r\n    },\r\n    onError: (err, variables, context) => {\r\n      if (context?.previousEmployee) {\r\n        queryClient.setQueryData(\r\n          employeeQueryKeys.detail(variables.id),\r\n          context.previousEmployee\r\n        );\r\n      }\r\n      if (context?.previousEmployeesList) {\r\n        queryClient.setQueryData(\r\n          employeeQueryKeys.all,\r\n          context.previousEmployeesList\r\n        );\r\n      }\r\n      console.error('Failed to update employee:', err);\r\n    },\r\n    onMutate: async ({ data, id }) => {\r\n      await queryClient.cancelQueries({ queryKey: employeeQueryKeys.all });\r\n      await queryClient.cancelQueries({\r\n        queryKey: employeeQueryKeys.detail(id),\r\n      });\r\n\r\n      const previousEmployee = queryClient.getQueryData<Employee>(\r\n        employeeQueryKeys.detail(id)\r\n      );\r\n      const previousEmployeesList = queryClient.getQueryData<Employee[]>(\r\n        employeeQueryKeys.all\r\n      );\r\n\r\n      queryClient.setQueryData<Employee>(employeeQueryKeys.detail(id), old => {\r\n        if (!old) return old;\r\n        const now = new Date().toISOString();\r\n\r\n        // Explicitly map updated fields to avoid issues with spread operator on different types\r\n        const updatedOptimistic: Employee = {\r\n          ...old,\r\n          availability: undefinedToNull(\r\n            data.availability === undefined\r\n              ? old.availability\r\n              : data.availability\r\n          ),\r\n          contactEmail: undefinedToNull(\r\n            data.contactEmail === undefined\r\n              ? old.contactEmail\r\n              : data.contactEmail\r\n          ),\r\n          contactInfo: data.contactInfo ?? old.contactInfo,\r\n          contactMobile: undefinedToNull(\r\n            data.contactMobile === undefined\r\n              ? old.contactMobile\r\n              : data.contactMobile\r\n          ),\r\n          contactPhone: undefinedToNull(\r\n            data.contactPhone === undefined\r\n              ? old.contactPhone\r\n              : data.contactPhone\r\n          ),\r\n          currentLocation: undefinedToNull(\r\n            data.currentLocation === undefined\r\n              ? old.currentLocation\r\n              : data.currentLocation\r\n          ),\r\n          department: undefinedToNull(data.department ?? old.department),\r\n          employeeId: data.employeeId ?? old.employeeId,\r\n          fullName: undefinedToNull(data.fullName ?? old.fullName),\r\n          generalAssignments: data.generalAssignments ?? old.generalAssignments,\r\n          hireDate: undefinedToNull(data.hireDate ?? old.hireDate),\r\n          name: data.name ?? old.name,\r\n          notes: undefinedToNull(\r\n            data.notes === undefined ? old.notes : data.notes\r\n          ),\r\n          position: undefinedToNull(data.position ?? old.position),\r\n          profileImageUrl: undefinedToNull(\r\n            data.profileImageUrl === undefined\r\n              ? old.profileImageUrl\r\n              : data.profileImageUrl\r\n          ),\r\n          role: data.role ?? old.role,\r\n          shiftSchedule: undefinedToNull(\r\n            data.shiftSchedule === undefined\r\n              ? old.shiftSchedule\r\n              : data.shiftSchedule\r\n          ),\r\n          skills: data.skills ?? old.skills,\r\n          status: undefinedToNull(data.status ?? old.status),\r\n          updatedAt: now,\r\n          ...(data.workingHours !== undefined && {\r\n            workingHours: undefinedToNull(data.workingHours),\r\n          }),\r\n        };\r\n        return updatedOptimistic;\r\n      });\r\n\r\n      queryClient.setQueryData<Employee[]>(\r\n        employeeQueryKeys.all,\r\n        (old = []) => {\r\n          return old.map(employee => {\r\n            if (employee.id === Number(id)) {\r\n              const now = new Date().toISOString();\r\n              return {\r\n                ...employee,\r\n                availability: undefinedToNull(\r\n                  data.availability ?? employee.availability\r\n                ),\r\n                contactEmail: undefinedToNull(\r\n                  data.contactEmail ?? employee.contactEmail\r\n                ),\r\n                contactInfo: data.contactInfo ?? employee.contactInfo,\r\n                contactMobile: undefinedToNull(\r\n                  data.contactMobile ?? employee.contactMobile\r\n                ),\r\n                contactPhone: undefinedToNull(\r\n                  data.contactPhone ?? employee.contactPhone\r\n                ),\r\n                currentLocation: undefinedToNull(\r\n                  data.currentLocation ?? employee.currentLocation\r\n                ),\r\n                department: undefinedToNull(\r\n                  data.department ?? employee.department\r\n                ),\r\n                employeeId: data.employeeId ?? employee.employeeId,\r\n                fullName: undefinedToNull(data.fullName ?? employee.fullName),\r\n                generalAssignments:\r\n                  data.generalAssignments ?? employee.generalAssignments,\r\n                hireDate: undefinedToNull(data.hireDate ?? employee.hireDate),\r\n                name: data.name ?? employee.name,\r\n                notes: undefinedToNull(data.notes ?? employee.notes),\r\n                position: undefinedToNull(data.position ?? employee.position),\r\n                profileImageUrl: undefinedToNull(\r\n                  data.profileImageUrl ?? employee.profileImageUrl\r\n                ),\r\n                role: data.role ?? employee.role,\r\n                shiftSchedule: undefinedToNull(\r\n                  data.shiftSchedule ?? employee.shiftSchedule\r\n                ),\r\n                skills: data.skills ?? employee.skills,\r\n                status: undefinedToNull(data.status ?? employee.status),\r\n                updatedAt: now,\r\n                ...(data.workingHours !== undefined && {\r\n                  workingHours: undefinedToNull(data.workingHours),\r\n                }),\r\n              };\r\n            }\r\n            return employee;\r\n          });\r\n        }\r\n      );\r\n\r\n      return { previousEmployee, previousEmployeesList };\r\n    },\r\n    onSettled: (_data, _error, variables) => {\r\n      queryClient.invalidateQueries({\r\n        queryKey: employeeQueryKeys.detail(variables.id),\r\n      });\r\n      queryClient.invalidateQueries({ queryKey: employeeQueryKeys.all });\r\n    },\r\n  });\r\n};\r\n\r\n/**\r\n * Custom hook for deleting an existing employee.\r\n * Includes cache updates.\r\n * @returns Mutation result for deleting an employee.\r\n */\r\nexport const useDeleteEmployee = () => {\r\n  const queryClient = useQueryClient();\r\n\r\n  interface DeleteContext {\r\n    previousEmployeesList: Employee[] | undefined;\r\n  }\r\n\r\n  return useMutation<string, Error, string, DeleteContext>({\r\n    mutationFn: async (id: string) => {\r\n      await employeeApiService.delete(id);\r\n      return id;\r\n    },\r\n    onError: (err, _id, context) => {\r\n      if (context?.previousEmployeesList) {\r\n        queryClient.setQueryData(\r\n          employeeQueryKeys.all,\r\n          context.previousEmployeesList\r\n        );\r\n      }\r\n      console.error('Failed to delete employee:', err);\r\n    },\r\n    onMutate: async id => {\r\n      await queryClient.cancelQueries({ queryKey: employeeQueryKeys.all });\r\n      await queryClient.cancelQueries({\r\n        queryKey: employeeQueryKeys.detail(id),\r\n      });\r\n\r\n      const previousEmployeesList = queryClient.getQueryData<Employee[]>(\r\n        employeeQueryKeys.all\r\n      );\r\n\r\n      queryClient.setQueryData<Employee[]>(\r\n        employeeQueryKeys.all,\r\n        (old = []) => old.filter(employee => employee.id !== Number(id)) // Compare number ID\r\n      );\r\n\r\n      queryClient.removeQueries({ queryKey: employeeQueryKeys.detail(id) });\r\n\r\n      return { previousEmployeesList };\r\n    },\r\n    onSettled: () => {\r\n      queryClient.invalidateQueries({ queryKey: employeeQueryKeys.all });\r\n    },\r\n  });\r\n};\r\n\r\n// Removed useAssignVehicleToEmployee hook as its functionality is now\r\n// handled by the main update employee mutation.\r\n\r\n/**\r\n * Custom hook for updating an employee's availability status.\r\n * @returns Mutation result for updating availability status.\r\n */\r\nexport const useUpdateEmployeeAvailabilityStatus = () => {\r\n  const queryClient = useQueryClient();\r\n\r\n  interface UpdateAvailabilityContext {\r\n    previousEmployee: Employee | undefined;\r\n  }\r\n\r\n  // The input 'status' should align with DriverAvailabilityPrisma\r\n  return useMutation<\r\n    Employee,\r\n    Error,\r\n    { employeeId: string; status: DriverAvailabilityPrisma },\r\n    UpdateAvailabilityContext\r\n  >({\r\n    mutationFn: async ({ employeeId, status }) => {\r\n      // employeeApiService.updateAvailabilityStatus now expects DriverAvailabilityPrisma directly\r\n      const response = await employeeApiService.updateAvailabilityStatus(\r\n        employeeId,\r\n        status\r\n      );\r\n      return response; // Removed redundant EmployeeTransformer.fromApi\r\n    },\r\n    onError: (err, variables, context) => {\r\n      if (context?.previousEmployee) {\r\n        queryClient.setQueryData(\r\n          employeeQueryKeys.detail(variables.employeeId),\r\n          context.previousEmployee\r\n        );\r\n      }\r\n      console.error('Failed to update employee availability status:', err);\r\n    },\r\n    onMutate: async ({ employeeId, status }) => {\r\n      await queryClient.cancelQueries({\r\n        queryKey: employeeQueryKeys.detail(employeeId),\r\n      });\r\n      const previousEmployee = queryClient.getQueryData<Employee>(\r\n        employeeQueryKeys.detail(employeeId)\r\n      );\r\n\r\n      queryClient.setQueryData<Employee>(\r\n        employeeQueryKeys.detail(employeeId),\r\n        old => {\r\n          // Update the 'availability' field in the domain model\r\n          return old ? { ...old, availability: status } : old;\r\n        }\r\n      );\r\n\r\n      return { previousEmployee };\r\n    },\r\n    onSettled: (_data, _error, variables) => {\r\n      queryClient.invalidateQueries({\r\n        queryKey: employeeQueryKeys.detail(variables.employeeId),\r\n      });\r\n      queryClient.invalidateQueries({ queryKey: employeeQueryKeys.all });\r\n    },\r\n  });\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;;;;;;;AAID;AAAA;AAQA,6OAA0D,uBAAuB;AACjF;AACA;AACA;AAEA,sTAA2E,0BAA0B;AAArG;;;;;;;;AAKO,MAAM,oBAAoB;IAC/B,KAAK;QAAC;KAAY;IAClB,QAAQ,CAAC,KAAe;YAAC;YAAa;SAAG;AAE3C;AAOO,MAAM,eAAe,CAC1B;;IAEA,OAAO,CAAA,GAAA,uIAAA,CAAA,eAAY,AAAD,EAChB;WAAI,kBAAkB,GAAG;KAAC;qCAC1B;YACE,MAAM,WAAW,MAAM,2IAAA,CAAA,qBAAkB,CAAC,MAAM;YAChD,OAAO,SAAS,IAAI;QACtB;oCACA,YACA;QACE,WAAW,IAAI,KAAK;QACpB,GAAG,OAAO;IACZ;AAEJ;GAfa;;QAGJ,uIAAA,CAAA,eAAY;;;AAoBd,MAAM,qBAAqB,CAChC,MACA;;IAEA,OAAO,CAAA,GAAA,uIAAA,CAAA,eAAY,AAAD,EAChB,OAAO;QAAC;QAAa;QAAQ;KAAK,GAAG;WAAI,kBAAkB,GAAG;KAAC;2CAC/D;YACE,IAAI,MAAM;gBACR,MAAM,WAAW,MAAM,2IAAA,CAAA,qBAAkB,CAAC,SAAS,CAAC;gBACpD,OAAO;YACT,OAAO;gBACL,MAAM,WAAW,MAAM,2IAAA,CAAA,qBAAkB,CAAC,MAAM;gBAChD,OAAO,SAAS,IAAI;YACtB;QACF;0CACA,YACA;QACE,WAAW,IAAI,KAAK;QACpB,GAAG,OAAO;IACZ;AAEJ;IArBa;;QAIJ,uIAAA,CAAA,eAAY;;;AAwBd,MAAM,cAAc,CAAC;;IAC1B,OAAO,CAAA,GAAA,uIAAA,CAAA,eAAY,AAAD,EAChB;WAAI,kBAAkB,MAAM,CAAC;KAAI;oCACjC;YACE,OAAO,MAAM,2IAAA,CAAA,qBAAkB,CAAC,OAAO,CAAC;QAC1C;mCACA,YACA;QACE,SAAS,CAAC,CAAC;QACX,WAAW,IAAI,KAAK;IACtB;AAEJ;IAZa;;QACJ,uIAAA,CAAA,eAAY;;;AAkBd,MAAM,oBAAoB;;IAC/B,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,mBAAgB,AAAD;IAMlD,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAsD;QACrE,UAAU;6CAAE,OAAO;gBACjB,MAAM,UAAU,oJAAA,CAAA,sBAAmB,CAAC,eAAe,CAAC;gBACpD,OAAO,MAAM,2IAAA,CAAA,qBAAkB,CAAC,MAAM,CAAC,UAAU,gDAAgD;YACnG;;QACA,OAAO;6CAAE,CAAC,KAAK,kBAAkB;gBAC/B,IAAI,SAAS,mBAAmB;oBAC9B,YAAY,YAAY,CACtB,kBAAkB,GAAG,EACrB,QAAQ,iBAAiB;gBAE7B;gBACA,UACE,CAAC,2BAA2B,EAAE,IAAI,OAAO,IAAI,0BAA0B;YAE3E;;QACA,QAAQ;6CAAE,OAAM;gBACd,MAAM,YAAY,aAAa,CAAC;oBAAE,UAAU,kBAAkB,GAAG;gBAAC;gBAClE,MAAM,oBAAoB,YAAY,YAAY,CAChD,kBAAkB,GAAG;gBAGvB,YAAY,YAAY,CACtB,kBAAkB,GAAG;qDACrB,CAAC,MAAM,EAAE;wBACP,MAAM,SAAS,gBAAgB,KAAK,GAAG,GAAG,QAAQ;wBAClD,MAAM,MAAM,IAAI,OAAO,WAAW;wBAClC,MAAM,qBAA+B;4BACnC,cAAc,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EAAE,gBAAgB,YAAY;4BAC1D,cAAc,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EAAE,gBAAgB,YAAY;4BAC1D,aAAa,gBAAgB,WAAW;4BACxC,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EAAE,gBAAgB,aAAa;4BAC5D,cAAc,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EAAE,gBAAgB,YAAY;4BAC1D,WAAW;4BACX,iBAAiB,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EAAE,gBAAgB,eAAe;4BAChE,YAAY,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EAAE,gBAAgB,UAAU;4BACtD,YAAY,gBAAgB,UAAU;4BACtC,UAAU,gBAAgB,QAAQ,IAAI,gBAAgB,IAAI;4BAC1D,oBAAoB,gBAAgB,kBAAkB,IAAI,EAAE;4BAC5D,UAAU,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EAAE,gBAAgB,QAAQ;4BAClD,IAAI,OAAO,OAAO,OAAO,CAAC,eAAe;4BACzC,MAAM,gBAAgB,IAAI;4BAC1B,OAAO,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EAAE,gBAAgB,KAAK;4BAC5C,UAAU,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EAAE,gBAAgB,QAAQ;4BAClD,iBAAiB,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EAAE,gBAAgB,eAAe;4BAChE,MAAM,gBAAgB,IAAI;4BAC1B,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EAAE,gBAAgB,aAAa;4BAC5D,QAAQ,gBAAgB,MAAM,IAAI,EAAE;4BACpC,QAAQ,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EAAE,gBAAgB,MAAM;4BAC9C,WAAW;4BACX,GAAI,gBAAgB,YAAY,KAAK,aAAa;gCAChD,cAAc,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EAAE,gBAAgB,YAAY;4BAC5D,CAAC;wBACH;wBACA,OAAO;+BAAI;4BAAK;yBAAmB;oBACrC;;gBAGF,OAAO;oBAAE;gBAAkB;YAC7B;;QACA,SAAS;6CAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU,kBAAkB,GAAG;gBAAC;YAClE;;QACA,SAAS;6CAAE,CAAA;gBACT,YAAY,CAAC,UAAU,EAAE,KAAK,IAAI,CAAC,gCAAgC,CAAC;YACtE;;IACF;AACF;IA3Ea;;QACS,yLAAA,CAAA,iBAAc;QACC,yIAAA,CAAA,mBAAgB;QAM5C,iLAAA,CAAA,cAAW;;;AA0Eb,MAAM,oBAAoB;;IAC/B,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAOjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAKf;QACA,UAAU;6CAAE,OAAO,EAAE,IAAI,EAAE,EAAE,EAAE;gBAC7B,MAAM,UAAU,oJAAA,CAAA,sBAAmB,CAAC,eAAe,CAAC,OAAO,eAAe;gBAC1E,OAAO,MAAM,2IAAA,CAAA,qBAAkB,CAAC,MAAM,CAAC,IAAI,UAAU,gDAAgD;YACvG;;QACA,OAAO;6CAAE,CAAC,KAAK,WAAW;gBACxB,IAAI,SAAS,kBAAkB;oBAC7B,YAAY,YAAY,CACtB,kBAAkB,MAAM,CAAC,UAAU,EAAE,GACrC,QAAQ,gBAAgB;gBAE5B;gBACA,IAAI,SAAS,uBAAuB;oBAClC,YAAY,YAAY,CACtB,kBAAkB,GAAG,EACrB,QAAQ,qBAAqB;gBAEjC;gBACA,QAAQ,KAAK,CAAC,8BAA8B;YAC9C;;QACA,QAAQ;6CAAE,OAAO,EAAE,IAAI,EAAE,EAAE,EAAE;gBAC3B,MAAM,YAAY,aAAa,CAAC;oBAAE,UAAU,kBAAkB,GAAG;gBAAC;gBAClE,MAAM,YAAY,aAAa,CAAC;oBAC9B,UAAU,kBAAkB,MAAM,CAAC;gBACrC;gBAEA,MAAM,mBAAmB,YAAY,YAAY,CAC/C,kBAAkB,MAAM,CAAC;gBAE3B,MAAM,wBAAwB,YAAY,YAAY,CACpD,kBAAkB,GAAG;gBAGvB,YAAY,YAAY,CAAW,kBAAkB,MAAM,CAAC;qDAAK,CAAA;wBAC/D,IAAI,CAAC,KAAK,OAAO;wBACjB,MAAM,MAAM,IAAI,OAAO,WAAW;wBAElC,wFAAwF;wBACxF,MAAM,oBAA8B;4BAClC,GAAG,GAAG;4BACN,cAAc,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EAC1B,KAAK,YAAY,KAAK,YAClB,IAAI,YAAY,GAChB,KAAK,YAAY;4BAEvB,cAAc,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EAC1B,KAAK,YAAY,KAAK,YAClB,IAAI,YAAY,GAChB,KAAK,YAAY;4BAEvB,aAAa,KAAK,WAAW,IAAI,IAAI,WAAW;4BAChD,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EAC3B,KAAK,aAAa,KAAK,YACnB,IAAI,aAAa,GACjB,KAAK,aAAa;4BAExB,cAAc,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EAC1B,KAAK,YAAY,KAAK,YAClB,IAAI,YAAY,GAChB,KAAK,YAAY;4BAEvB,iBAAiB,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EAC7B,KAAK,eAAe,KAAK,YACrB,IAAI,eAAe,GACnB,KAAK,eAAe;4BAE1B,YAAY,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,UAAU,IAAI,IAAI,UAAU;4BAC7D,YAAY,KAAK,UAAU,IAAI,IAAI,UAAU;4BAC7C,UAAU,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,QAAQ,IAAI,IAAI,QAAQ;4BACvD,oBAAoB,KAAK,kBAAkB,IAAI,IAAI,kBAAkB;4BACrE,UAAU,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,QAAQ,IAAI,IAAI,QAAQ;4BACvD,MAAM,KAAK,IAAI,IAAI,IAAI,IAAI;4BAC3B,OAAO,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EACnB,KAAK,KAAK,KAAK,YAAY,IAAI,KAAK,GAAG,KAAK,KAAK;4BAEnD,UAAU,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,QAAQ,IAAI,IAAI,QAAQ;4BACvD,iBAAiB,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EAC7B,KAAK,eAAe,KAAK,YACrB,IAAI,eAAe,GACnB,KAAK,eAAe;4BAE1B,MAAM,KAAK,IAAI,IAAI,IAAI,IAAI;4BAC3B,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EAC3B,KAAK,aAAa,KAAK,YACnB,IAAI,aAAa,GACjB,KAAK,aAAa;4BAExB,QAAQ,KAAK,MAAM,IAAI,IAAI,MAAM;4BACjC,QAAQ,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,MAAM,IAAI,IAAI,MAAM;4BACjD,WAAW;4BACX,GAAI,KAAK,YAAY,KAAK,aAAa;gCACrC,cAAc,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,YAAY;4BACjD,CAAC;wBACH;wBACA,OAAO;oBACT;;gBAEA,YAAY,YAAY,CACtB,kBAAkB,GAAG;qDACrB,CAAC,MAAM,EAAE;wBACP,OAAO,IAAI,GAAG;6DAAC,CAAA;gCACb,IAAI,SAAS,EAAE,KAAK,OAAO,KAAK;oCAC9B,MAAM,MAAM,IAAI,OAAO,WAAW;oCAClC,OAAO;wCACL,GAAG,QAAQ;wCACX,cAAc,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EAC1B,KAAK,YAAY,IAAI,SAAS,YAAY;wCAE5C,cAAc,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EAC1B,KAAK,YAAY,IAAI,SAAS,YAAY;wCAE5C,aAAa,KAAK,WAAW,IAAI,SAAS,WAAW;wCACrD,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EAC3B,KAAK,aAAa,IAAI,SAAS,aAAa;wCAE9C,cAAc,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EAC1B,KAAK,YAAY,IAAI,SAAS,YAAY;wCAE5C,iBAAiB,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EAC7B,KAAK,eAAe,IAAI,SAAS,eAAe;wCAElD,YAAY,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EACxB,KAAK,UAAU,IAAI,SAAS,UAAU;wCAExC,YAAY,KAAK,UAAU,IAAI,SAAS,UAAU;wCAClD,UAAU,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,QAAQ,IAAI,SAAS,QAAQ;wCAC5D,oBACE,KAAK,kBAAkB,IAAI,SAAS,kBAAkB;wCACxD,UAAU,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,QAAQ,IAAI,SAAS,QAAQ;wCAC5D,MAAM,KAAK,IAAI,IAAI,SAAS,IAAI;wCAChC,OAAO,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,KAAK,IAAI,SAAS,KAAK;wCACnD,UAAU,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,QAAQ,IAAI,SAAS,QAAQ;wCAC5D,iBAAiB,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EAC7B,KAAK,eAAe,IAAI,SAAS,eAAe;wCAElD,MAAM,KAAK,IAAI,IAAI,SAAS,IAAI;wCAChC,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EAC3B,KAAK,aAAa,IAAI,SAAS,aAAa;wCAE9C,QAAQ,KAAK,MAAM,IAAI,SAAS,MAAM;wCACtC,QAAQ,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,MAAM,IAAI,SAAS,MAAM;wCACtD,WAAW;wCACX,GAAI,KAAK,YAAY,KAAK,aAAa;4CACrC,cAAc,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,YAAY;wCACjD,CAAC;oCACH;gCACF;gCACA,OAAO;4BACT;;oBACF;;gBAGF,OAAO;oBAAE;oBAAkB;gBAAsB;YACnD;;QACA,SAAS;6CAAE,CAAC,OAAO,QAAQ;gBACzB,YAAY,iBAAiB,CAAC;oBAC5B,UAAU,kBAAkB,MAAM,CAAC,UAAU,EAAE;gBACjD;gBACA,YAAY,iBAAiB,CAAC;oBAAE,UAAU,kBAAkB,GAAG;gBAAC;YAClE;;IACF;AACF;IA9Ka;;QACS,yLAAA,CAAA,iBAAc;QAO3B,iLAAA,CAAA,cAAW;;;AA6Kb,MAAM,oBAAoB;;IAC/B,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAMjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAwC;QACvD,UAAU;6CAAE,OAAO;gBACjB,MAAM,2IAAA,CAAA,qBAAkB,CAAC,MAAM,CAAC;gBAChC,OAAO;YACT;;QACA,OAAO;6CAAE,CAAC,KAAK,KAAK;gBAClB,IAAI,SAAS,uBAAuB;oBAClC,YAAY,YAAY,CACtB,kBAAkB,GAAG,EACrB,QAAQ,qBAAqB;gBAEjC;gBACA,QAAQ,KAAK,CAAC,8BAA8B;YAC9C;;QACA,QAAQ;6CAAE,OAAM;gBACd,MAAM,YAAY,aAAa,CAAC;oBAAE,UAAU,kBAAkB,GAAG;gBAAC;gBAClE,MAAM,YAAY,aAAa,CAAC;oBAC9B,UAAU,kBAAkB,MAAM,CAAC;gBACrC;gBAEA,MAAM,wBAAwB,YAAY,YAAY,CACpD,kBAAkB,GAAG;gBAGvB,YAAY,YAAY,CACtB,kBAAkB,GAAG;qDACrB,CAAC,MAAM,EAAE,GAAK,IAAI,MAAM;6DAAC,CAAA,WAAY,SAAS,EAAE,KAAK,OAAO;4DAAK,oBAAoB;;gBAGvF,YAAY,aAAa,CAAC;oBAAE,UAAU,kBAAkB,MAAM,CAAC;gBAAI;gBAEnE,OAAO;oBAAE;gBAAsB;YACjC;;QACA,SAAS;6CAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU,kBAAkB,GAAG;gBAAC;YAClE;;IACF;AACF;IA5Ca;;QACS,yLAAA,CAAA,iBAAc;QAM3B,iLAAA,CAAA,cAAW;;;AA8Cb,MAAM,sCAAsC;;IACjD,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAMjC,gEAAgE;IAChE,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAKf;QACA,UAAU;+DAAE,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE;gBACvC,4FAA4F;gBAC5F,MAAM,WAAW,MAAM,2IAAA,CAAA,qBAAkB,CAAC,wBAAwB,CAChE,YACA;gBAEF,OAAO,UAAU,gDAAgD;YACnE;;QACA,OAAO;+DAAE,CAAC,KAAK,WAAW;gBACxB,IAAI,SAAS,kBAAkB;oBAC7B,YAAY,YAAY,CACtB,kBAAkB,MAAM,CAAC,UAAU,UAAU,GAC7C,QAAQ,gBAAgB;gBAE5B;gBACA,QAAQ,KAAK,CAAC,kDAAkD;YAClE;;QACA,QAAQ;+DAAE,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE;gBACrC,MAAM,YAAY,aAAa,CAAC;oBAC9B,UAAU,kBAAkB,MAAM,CAAC;gBACrC;gBACA,MAAM,mBAAmB,YAAY,YAAY,CAC/C,kBAAkB,MAAM,CAAC;gBAG3B,YAAY,YAAY,CACtB,kBAAkB,MAAM,CAAC;uEACzB,CAAA;wBACE,sDAAsD;wBACtD,OAAO,MAAM;4BAAE,GAAG,GAAG;4BAAE,cAAc;wBAAO,IAAI;oBAClD;;gBAGF,OAAO;oBAAE;gBAAiB;YAC5B;;QACA,SAAS;+DAAE,CAAC,OAAO,QAAQ;gBACzB,YAAY,iBAAiB,CAAC;oBAC5B,UAAU,kBAAkB,MAAM,CAAC,UAAU,UAAU;gBACzD;gBACA,YAAY,iBAAiB,CAAC;oBAAE,UAAU,kBAAkB,GAAG;gBAAC;YAClE;;IACF;AACF;IAxDa;;QACS,yLAAA,CAAA,iBAAc;QAO3B,iLAAA,CAAA,cAAW", "debugId": null}}, {"offset": {"line": 2492, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/lib/stores/queries/useServiceRecords.ts"], "sourcesContent": ["import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';\r\n\r\nimport type { EnrichedServiceRecord } from '@/lib/types/domain';\r\nimport type { ServiceRecord as DomainServiceRecord } from '@/lib/types/domain';\r\nimport type { ServiceRecordApiResponse } from '@/lib/types/apiContracts';\r\n\r\nimport { ApiClient } from '@/lib/api/core/apiClient';\r\nimport {\r\n  BaseApiService,\r\n  type DataTransformer,\r\n  type ServiceConfig,\r\n} from '@/lib/api/core/baseApiService';\r\nimport { useCrudQuery } from '@/hooks/api/useSmartQuery';\r\n\r\nexport interface CreateServiceRecordPayload {\r\n  cost?: number | undefined; // Allow undefined explicitly due to exactOptionalPropertyTypes\r\n  date: string;\r\n  employeeId?: null | number;\r\n  notes?: string;\r\n  odometer: number;\r\n  servicePerformed: string[];\r\n  vehicleId: number;\r\n}\r\n\r\nconst ServiceRecordTransformer: DataTransformer<DomainServiceRecord> = {\r\n  fromApi(apiData: any): DomainServiceRecord {\r\n    // Handle both regular service records and enriched service records\r\n    const baseRecord = {\r\n      cost: apiData.cost,\r\n      createdAt: apiData.createdAt,\r\n      date: apiData.date,\r\n      employeeId: apiData.employeeId,\r\n      id: apiData.id,\r\n      notes: apiData.notes,\r\n      odometer: apiData.odometer,\r\n      servicePerformed: Array.isArray(apiData.servicePerformed)\r\n        ? apiData.servicePerformed\r\n        : [],\r\n      updatedAt: apiData.updatedAt,\r\n      vehicleId: apiData.vehicleId,\r\n    };\r\n\r\n    // Add enriched fields if available (for EnrichedServiceRecord)\r\n    if (apiData.vehicleMake || apiData.vehicleModel || apiData.vehicleYear) {\r\n      return {\r\n        ...baseRecord,\r\n        vehicleMake: apiData.vehicleMake || 'Unknown',\r\n        vehicleModel: apiData.vehicleModel || 'Unknown',\r\n        vehicleYear: apiData.vehicleYear || new Date().getFullYear(),\r\n        licensePlate: apiData.licensePlate || null,\r\n        employeeName: apiData.employeeName || null,\r\n      } as any; // Cast to handle both ServiceRecord and EnrichedServiceRecord\r\n    }\r\n\r\n    return baseRecord;\r\n  },\r\n  toApi: (data: any) => data,\r\n};\r\n\r\nclass ServiceRecordApiService extends BaseApiService<\r\n  DomainServiceRecord,\r\n  CreateServiceRecordPayload,\r\n  Partial<CreateServiceRecordPayload>\r\n> {\r\n  protected endpoint = '/servicerecords';\r\n  protected transformer: DataTransformer<DomainServiceRecord> =\r\n    ServiceRecordTransformer;\r\n\r\n  constructor(apiClient: ApiClient, config?: ServiceConfig) {\r\n    super(apiClient, {\r\n      cacheDuration: 5 * 60 * 1000, // 5 minutes for service records\r\n      retryAttempts: 3,\r\n      circuitBreakerThreshold: 5,\r\n      enableMetrics: true,\r\n      ...config,\r\n    });\r\n  }\r\n\r\n  async getById(id: string): Promise<EnrichedServiceRecord> {\r\n    return this.executeWithInfrastructure(\r\n      `${this.endpoint}:${id}`,\r\n      async () => {\r\n        const response = await this.apiClient.get<EnrichedServiceRecord>(\r\n          `${this.endpoint}/${id}`\r\n        );\r\n\r\n        // Apply transformer if available\r\n        const transformedResponse = this.transformer.fromApi\r\n          ? this.transformer.fromApi(response)\r\n          : response;\r\n\r\n        return transformedResponse as EnrichedServiceRecord;\r\n      }\r\n    );\r\n  }\r\n\r\n  // Custom update method for service records, handling vehicleId in path\r\n  async updateRecord(\r\n    id: string,\r\n    vehicleId: number, // Keep vehicleId as a separate parameter for clarity in this specific method\r\n    data: Partial<CreateServiceRecordPayload>\r\n  ): Promise<DomainServiceRecord> {\r\n    return this.executeWithInfrastructure(null, async () => {\r\n      // Remove vehicleId from data if present to avoid conflicts, use the parameter instead\r\n      const { vehicleId: _, ...updateData } = data;\r\n      const response = await this.apiClient.put<any>(\r\n        `/vehicles/${vehicleId}/servicerecords/${id}`,\r\n        updateData // Send only the actual update data\r\n      );\r\n      this.cache.invalidatePattern(new RegExp(`^${this.endpoint}:`)); // Invalidate all service records\r\n      this.cache.invalidatePattern(new RegExp(`^${this.endpoint}:${id}`)); // Invalidate specific record\r\n      this.cache.invalidatePattern(new RegExp(`^vehicles:${vehicleId}:`)); // Invalidate vehicle-specific caches\r\n      return this.transformer.fromApi\r\n        ? this.transformer.fromApi(response)\r\n        : response;\r\n    });\r\n  }\r\n\r\n  // Custom delete method for service records, handling vehicleId in path\r\n  async deleteRecord(id: string, vehicleId: number): Promise<void> {\r\n    return this.executeWithInfrastructure(null, async () => {\r\n      await this.apiClient.delete(\r\n        `/vehicles/${vehicleId}/servicerecords/${id}`\r\n      );\r\n      this.cache.invalidatePattern(new RegExp(`^${this.endpoint}:`)); // Invalidate all service records\r\n      this.cache.invalidatePattern(new RegExp(`^${this.endpoint}:${id}`)); // Invalidate specific record\r\n      this.cache.invalidatePattern(new RegExp(`^vehicles:${vehicleId}:`)); // Invalidate vehicle-specific caches\r\n    });\r\n  }\r\n\r\n  async getVehicleServiceRecords(\r\n    vehicleId: number\r\n  ): Promise<EnrichedServiceRecord[]> {\r\n    return this.executeWithInfrastructure(\r\n      `vehicles:${vehicleId}:servicerecords`,\r\n      async () => {\r\n        const response = await this.apiClient.get<EnrichedServiceRecord[]>(\r\n          `/vehicles/${vehicleId}/servicerecords`\r\n        );\r\n        return response;\r\n      }\r\n    );\r\n  }\r\n\r\n  async createVehicleServiceRecord(\r\n    vehicleId: number,\r\n    data: CreateServiceRecordPayload\r\n  ): Promise<DomainServiceRecord> {\r\n    return this.executeWithInfrastructure(null, async () => {\r\n      const response = await this.apiClient.post<any>(\r\n        `/vehicles/${vehicleId}/servicerecords`,\r\n        data\r\n      );\r\n\r\n      this.cache.invalidatePattern(new RegExp(`^${this.endpoint}:`));\r\n      this.cache.invalidatePattern(new RegExp(`^vehicles:${vehicleId}:`));\r\n\r\n      return this.transformer.fromApi\r\n        ? this.transformer.fromApi(response)\r\n        : response;\r\n    });\r\n  }\r\n\r\n  // Method to fetch enriched service records\r\n  async getAllEnriched(): Promise<EnrichedServiceRecord[]> {\r\n    return this.executeWithInfrastructure(\r\n      `${this.endpoint}:enriched`,\r\n      async () => {\r\n        const response = await this.apiClient.get<EnrichedServiceRecord[]>(\r\n          `${this.endpoint}/enriched`\r\n        );\r\n\r\n        // Apply transformer to each record\r\n        return response.map(record => {\r\n          const transformed = this.transformer.fromApi\r\n            ? this.transformer.fromApi(record)\r\n            : record;\r\n          return transformed as EnrichedServiceRecord;\r\n        });\r\n      }\r\n    );\r\n  }\r\n}\r\n\r\nimport { apiClient } from '../../api';\r\nconst serviceRecordApiService = new ServiceRecordApiService(apiClient);\r\n\r\nexport const SERVICE_RECORD_QUERY_KEY = 'serviceRecords';\r\n\r\nexport const useServiceRecord = (\r\n  id: string,\r\n  options?: { enabled?: boolean }\r\n) => {\r\n  return useCrudQuery<EnrichedServiceRecord, Error>(\r\n    [SERVICE_RECORD_QUERY_KEY, id],\r\n    () => serviceRecordApiService.getById(id),\r\n    'serviceRecord',\r\n    {\r\n      enabled: options?.enabled ?? !!id, // Only enable if ID is present\r\n      staleTime: 1000 * 60 * 5,\r\n    }\r\n  );\r\n};\r\n\r\nexport const useEnrichedServiceRecords = (options?: { enabled?: boolean }) => {\r\n  return useCrudQuery<EnrichedServiceRecord[], Error>(\r\n    [SERVICE_RECORD_QUERY_KEY, 'allEnriched'],\r\n    () => serviceRecordApiService.getAllEnriched(),\r\n    'serviceRecord',\r\n    {\r\n      enabled: options?.enabled ?? true,\r\n      staleTime: 1000 * 60 * 5,\r\n    }\r\n  );\r\n};\r\n\r\nexport const useVehicleServiceRecords = (\r\n  vehicleId: number,\r\n  options?: { enabled?: boolean }\r\n) => {\r\n  return useCrudQuery<EnrichedServiceRecord[], Error>(\r\n    [SERVICE_RECORD_QUERY_KEY, 'forVehicle', vehicleId],\r\n    () => serviceRecordApiService.getVehicleServiceRecords(vehicleId),\r\n    'serviceRecord',\r\n    {\r\n      enabled: options?.enabled ?? true,\r\n      staleTime: 1000 * 60 * 5,\r\n    }\r\n  );\r\n};\r\n\r\nexport const useCreateServiceRecord = () => {\r\n  const queryClient = useQueryClient();\r\n  return useMutation<DomainServiceRecord, Error, CreateServiceRecordPayload>({\r\n    mutationFn: async (\r\n      newRecordData: CreateServiceRecordPayload\r\n    ): Promise<DomainServiceRecord> => {\r\n      const { vehicleId } = newRecordData;\r\n      return serviceRecordApiService.createVehicleServiceRecord(\r\n        vehicleId,\r\n        newRecordData\r\n      );\r\n    },\r\n    onSuccess: (data, variables) => {\r\n      queryClient.invalidateQueries({\r\n        queryKey: [SERVICE_RECORD_QUERY_KEY, 'allEnriched'],\r\n      });\r\n      queryClient.invalidateQueries({\r\n        queryKey: [SERVICE_RECORD_QUERY_KEY, 'forVehicle', variables.vehicleId],\r\n      });\r\n      queryClient.invalidateQueries({\r\n        queryKey: ['vehicle', variables.vehicleId],\r\n      });\r\n    },\r\n  });\r\n};\r\n\r\nexport const useUpdateServiceRecord = () => {\r\n  const queryClient = useQueryClient();\r\n  return useMutation<\r\n    DomainServiceRecord,\r\n    Error,\r\n    { id: string; vehicleId: number; data: Partial<CreateServiceRecordPayload> }\r\n  >({\r\n    mutationFn: async ({\r\n      id,\r\n      vehicleId,\r\n      data,\r\n    }): Promise<DomainServiceRecord> => {\r\n      // Call the custom updateRecord method\r\n      return serviceRecordApiService.updateRecord(id, vehicleId, data);\r\n    },\r\n    onSuccess: (_, variables) => {\r\n      queryClient.invalidateQueries({\r\n        queryKey: [SERVICE_RECORD_QUERY_KEY, 'allEnriched'],\r\n      });\r\n      queryClient.invalidateQueries({\r\n        queryKey: [SERVICE_RECORD_QUERY_KEY, variables.id],\r\n      }); // Invalidate specific record\r\n      // Use variables.vehicleId directly as it's now part of the mutation variables\r\n      queryClient.invalidateQueries({\r\n        queryKey: [SERVICE_RECORD_QUERY_KEY, 'forVehicle', variables.vehicleId],\r\n      });\r\n      queryClient.invalidateQueries({\r\n        queryKey: ['vehicle', variables.vehicleId],\r\n      });\r\n    },\r\n  });\r\n};\r\n\r\nexport const useDeleteServiceRecord = () => {\r\n  const queryClient = useQueryClient();\r\n  return useMutation<void, Error, { id: string; vehicleId: number }>({\r\n    mutationFn: async ({ id, vehicleId }): Promise<void> => {\r\n      // Call the custom deleteRecord method\r\n      return serviceRecordApiService.deleteRecord(id, vehicleId);\r\n    },\r\n    onSuccess: (_, variables) => {\r\n      // Use variables instead of id for consistency\r\n      queryClient.invalidateQueries({\r\n        queryKey: [SERVICE_RECORD_QUERY_KEY, 'allEnriched'],\r\n      });\r\n      queryClient.invalidateQueries({\r\n        queryKey: [SERVICE_RECORD_QUERY_KEY, variables.id],\r\n      });\r\n      // Invalidate all vehicle-specific caches since we don't know which vehicle this record belonged to\r\n      queryClient.invalidateQueries({\r\n        queryKey: [SERVICE_RECORD_QUERY_KEY, 'forVehicle'],\r\n      });\r\n      queryClient.invalidateQueries({\r\n        queryKey: ['vehicle'],\r\n      });\r\n    },\r\n  });\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAAA;AAOA;AAKA;AA4KA;AAAA;;;;;AAhKA,MAAM,2BAAiE;IACrE,SAAQ,OAAY;QAClB,mEAAmE;QACnE,MAAM,aAAa;YACjB,MAAM,QAAQ,IAAI;YAClB,WAAW,QAAQ,SAAS;YAC5B,MAAM,QAAQ,IAAI;YAClB,YAAY,QAAQ,UAAU;YAC9B,IAAI,QAAQ,EAAE;YACd,OAAO,QAAQ,KAAK;YACpB,UAAU,QAAQ,QAAQ;YAC1B,kBAAkB,MAAM,OAAO,CAAC,QAAQ,gBAAgB,IACpD,QAAQ,gBAAgB,GACxB,EAAE;YACN,WAAW,QAAQ,SAAS;YAC5B,WAAW,QAAQ,SAAS;QAC9B;QAEA,+DAA+D;QAC/D,IAAI,QAAQ,WAAW,IAAI,QAAQ,YAAY,IAAI,QAAQ,WAAW,EAAE;YACtE,OAAO;gBACL,GAAG,UAAU;gBACb,aAAa,QAAQ,WAAW,IAAI;gBACpC,cAAc,QAAQ,YAAY,IAAI;gBACtC,aAAa,QAAQ,WAAW,IAAI,IAAI,OAAO,WAAW;gBAC1D,cAAc,QAAQ,YAAY,IAAI;gBACtC,cAAc,QAAQ,YAAY,IAAI;YACxC,GAAU,8DAA8D;QAC1E;QAEA,OAAO;IACT;IACA,OAAO,CAAC,OAAc;AACxB;AAEA,MAAM,gCAAgC,8IAAA,CAAA,iBAAc;IAKxC,WAAW,kBAAkB;IAC7B,cACR,yBAAyB;IAE3B,YAAY,SAAoB,EAAE,MAAsB,CAAE;QACxD,KAAK,CAAC,WAAW;YACf,eAAe,IAAI,KAAK;YACxB,eAAe;YACf,yBAAyB;YACzB,eAAe;YACf,GAAG,MAAM;QACX;IACF;IAEA,MAAM,QAAQ,EAAU,EAAkC;QACxD,OAAO,IAAI,CAAC,yBAAyB,CACnC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,EACxB;YACE,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CACvC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI;YAG1B,iCAAiC;YACjC,MAAM,sBAAsB,IAAI,CAAC,WAAW,CAAC,OAAO,GAChD,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,YACzB;YAEJ,OAAO;QACT;IAEJ;IAEA,uEAAuE;IACvE,MAAM,aACJ,EAAU,EACV,SAAiB,EACjB,IAAyC,EACX;QAC9B,OAAO,IAAI,CAAC,yBAAyB,CAAC,MAAM;YAC1C,sFAAsF;YACtF,MAAM,EAAE,WAAW,CAAC,EAAE,GAAG,YAAY,GAAG;YACxC,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CACvC,CAAC,UAAU,EAAE,UAAU,gBAAgB,EAAE,IAAI,EAC7C,WAAW,mCAAmC;;YAEhD,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,iCAAiC;YACjG,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,IAAI,6BAA6B;YAClG,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,OAAO,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC,IAAI,qCAAqC;YAC1G,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,GAC3B,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,YACzB;QACN;IACF;IAEA,uEAAuE;IACvE,MAAM,aAAa,EAAU,EAAE,SAAiB,EAAiB;QAC/D,OAAO,IAAI,CAAC,yBAAyB,CAAC,MAAM;YAC1C,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,CACzB,CAAC,UAAU,EAAE,UAAU,gBAAgB,EAAE,IAAI;YAE/C,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,iCAAiC;YACjG,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,IAAI,6BAA6B;YAClG,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,OAAO,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC,IAAI,qCAAqC;QAC5G;IACF;IAEA,MAAM,yBACJ,SAAiB,EACiB;QAClC,OAAO,IAAI,CAAC,yBAAyB,CACnC,CAAC,SAAS,EAAE,UAAU,eAAe,CAAC,EACtC;YACE,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CACvC,CAAC,UAAU,EAAE,UAAU,eAAe,CAAC;YAEzC,OAAO;QACT;IAEJ;IAEA,MAAM,2BACJ,SAAiB,EACjB,IAAgC,EACF;QAC9B,OAAO,IAAI,CAAC,yBAAyB,CAAC,MAAM;YAC1C,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CACxC,CAAC,UAAU,EAAE,UAAU,eAAe,CAAC,EACvC;YAGF,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;YAC5D,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,OAAO,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;YAEjE,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,GAC3B,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,YACzB;QACN;IACF;IAEA,2CAA2C;IAC3C,MAAM,iBAAmD;QACvD,OAAO,IAAI,CAAC,yBAAyB,CACnC,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,EAC3B;YACE,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CACvC,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC;YAG7B,mCAAmC;YACnC,OAAO,SAAS,GAAG,CAAC,CAAA;gBAClB,MAAM,cAAc,IAAI,CAAC,WAAW,CAAC,OAAO,GACxC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,UACzB;gBACJ,OAAO;YACT;QACF;IAEJ;AACF;;AAGA,MAAM,0BAA0B,IAAI,wBAAwB,6IAAA,CAAA,YAAS;AAE9D,MAAM,2BAA2B;AAEjC,MAAM,mBAAmB,CAC9B,IACA;;IAEA,OAAO,CAAA,GAAA,uIAAA,CAAA,eAAY,AAAD,EAChB;QAAC;QAA0B;KAAG;yCAC9B,IAAM,wBAAwB,OAAO,CAAC;wCACtC,iBACA;QACE,SAAS,SAAS,WAAW,CAAC,CAAC;QAC/B,WAAW,OAAO,KAAK;IACzB;AAEJ;GAba;;QAIJ,uIAAA,CAAA,eAAY;;;AAWd,MAAM,4BAA4B,CAAC;;IACxC,OAAO,CAAA,GAAA,uIAAA,CAAA,eAAY,AAAD,EAChB;QAAC;QAA0B;KAAc;kDACzC,IAAM,wBAAwB,cAAc;iDAC5C,iBACA;QACE,SAAS,SAAS,WAAW;QAC7B,WAAW,OAAO,KAAK;IACzB;AAEJ;IAVa;;QACJ,uIAAA,CAAA,eAAY;;;AAWd,MAAM,2BAA2B,CACtC,WACA;;IAEA,OAAO,CAAA,GAAA,uIAAA,CAAA,eAAY,AAAD,EAChB;QAAC;QAA0B;QAAc;KAAU;iDACnD,IAAM,wBAAwB,wBAAwB,CAAC;gDACvD,iBACA;QACE,SAAS,SAAS,WAAW;QAC7B,WAAW,OAAO,KAAK;IACzB;AAEJ;IAba;;QAIJ,uIAAA,CAAA,eAAY;;;AAWd,MAAM,yBAAyB;;IACpC,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IACjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAA0D;QACzE,UAAU;kDAAE,OACV;gBAEA,MAAM,EAAE,SAAS,EAAE,GAAG;gBACtB,OAAO,wBAAwB,0BAA0B,CACvD,WACA;YAEJ;;QACA,SAAS;kDAAE,CAAC,MAAM;gBAChB,YAAY,iBAAiB,CAAC;oBAC5B,UAAU;wBAAC;wBAA0B;qBAAc;gBACrD;gBACA,YAAY,iBAAiB,CAAC;oBAC5B,UAAU;wBAAC;wBAA0B;wBAAc,UAAU,SAAS;qBAAC;gBACzE;gBACA,YAAY,iBAAiB,CAAC;oBAC5B,UAAU;wBAAC;wBAAW,UAAU,SAAS;qBAAC;gBAC5C;YACF;;IACF;AACF;IAxBa;;QACS,yLAAA,CAAA,iBAAc;QAC3B,iLAAA,CAAA,cAAW;;;AAwBb,MAAM,yBAAyB;;IACpC,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IACjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAIf;QACA,UAAU;kDAAE,OAAO,EACjB,EAAE,EACF,SAAS,EACT,IAAI,EACL;gBACC,sCAAsC;gBACtC,OAAO,wBAAwB,YAAY,CAAC,IAAI,WAAW;YAC7D;;QACA,SAAS;kDAAE,CAAC,GAAG;gBACb,YAAY,iBAAiB,CAAC;oBAC5B,UAAU;wBAAC;wBAA0B;qBAAc;gBACrD;gBACA,YAAY,iBAAiB,CAAC;oBAC5B,UAAU;wBAAC;wBAA0B,UAAU,EAAE;qBAAC;gBACpD,IAAI,6BAA6B;gBACjC,8EAA8E;gBAC9E,YAAY,iBAAiB,CAAC;oBAC5B,UAAU;wBAAC;wBAA0B;wBAAc,UAAU,SAAS;qBAAC;gBACzE;gBACA,YAAY,iBAAiB,CAAC;oBAC5B,UAAU;wBAAC;wBAAW,UAAU,SAAS;qBAAC;gBAC5C;YACF;;IACF;AACF;IA/Ba;;QACS,yLAAA,CAAA,iBAAc;QAC3B,iLAAA,CAAA,cAAW;;;AA+Bb,MAAM,yBAAyB;;IACpC,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IACjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAkD;QACjE,UAAU;kDAAE,OAAO,EAAE,EAAE,EAAE,SAAS,EAAE;gBAClC,sCAAsC;gBACtC,OAAO,wBAAwB,YAAY,CAAC,IAAI;YAClD;;QACA,SAAS;kDAAE,CAAC,GAAG;gBACb,8CAA8C;gBAC9C,YAAY,iBAAiB,CAAC;oBAC5B,UAAU;wBAAC;wBAA0B;qBAAc;gBACrD;gBACA,YAAY,iBAAiB,CAAC;oBAC5B,UAAU;wBAAC;wBAA0B,UAAU,EAAE;qBAAC;gBACpD;gBACA,mGAAmG;gBACnG,YAAY,iBAAiB,CAAC;oBAC5B,UAAU;wBAAC;wBAA0B;qBAAa;gBACpD;gBACA,YAAY,iBAAiB,CAAC;oBAC5B,UAAU;wBAAC;qBAAU;gBACvB;YACF;;IACF;AACF;IAxBa;;QACS,yLAAA,CAAA,iBAAc;QAC3B,iLAAA,CAAA,cAAW", "debugId": null}}, {"offset": {"line": 2809, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/lib/transformers/taskEnrichment.ts"], "sourcesContent": ["/**\r\n * @file Task enrichment transformer following established patterns\r\n * @description Handles the enrichment of task data with employee and vehicle details\r\n * @module transformers/taskEnrichment\r\n */\r\n\r\nimport type { Employee, Task, Vehicle } from '../types/domain';\r\n\r\n/**\r\n * Transformer class for enriching task data with related entities\r\n * Follows the same pattern as DelegationEnrichmentTransformer\r\n */\r\nexport class TaskEnrichmentTransformer {\r\n  /**\r\n   * Main enrichment method that combines task data with employee and vehicle details\r\n   * @param task - Base task data\r\n   * @param employees - Array of employees\r\n   * @param vehicles - Array of vehicles\r\n   * @returns Fully enriched task\r\n   */\r\n  static enrich(task: Task, employees: Employee[], vehicles: Vehicle[]): Task {\r\n    const { employeeMap, vehicleMap } = this.createLookupMaps(\r\n      employees,\r\n      vehicles\r\n    );\r\n\r\n    // Apply all enrichments sequentially\r\n    let enrichedTask = this.enrichStaffEmployee(task, employeeMap);\r\n    enrichedTask = this.enrichDriverEmployee(enrichedTask, employeeMap);\r\n    enrichedTask = this.enrichVehicle(enrichedTask, vehicleMap);\r\n\r\n    return enrichedTask;\r\n  }\r\n\r\n  /**\r\n   * Creates optimized lookup maps for O(1) performance\r\n   * @param employees - Array of employees\r\n   * @param vehicles - Array of vehicles\r\n   * @returns Object containing employee and vehicle maps\r\n   */\r\n  private static createLookupMaps(employees: Employee[], vehicles: Vehicle[]) {\r\n    // Defensive programming: Ensure inputs are arrays\r\n    const safeEmployees = Array.isArray(employees) ? employees : [];\r\n    const safeVehicles = Array.isArray(vehicles) ? vehicles : [];\r\n\r\n    return {\r\n      employeeMap: new Map(safeEmployees.map(emp => [emp.id, emp])),\r\n      vehicleMap: new Map(safeVehicles.map(veh => [veh.id, veh])),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Enriches driver employee assignment with employee details\r\n   * @param task - Base task data\r\n   * @param employeeMap - Map of employees for O(1) lookup\r\n   * @returns Task with enriched driver employee data\r\n   */\r\n  private static enrichDriverEmployee(\r\n    task: Task,\r\n    employeeMap: Map<number, Employee>\r\n  ): Task {\r\n    if (!task.driverEmployeeId) {\r\n      return task;\r\n    }\r\n\r\n    const driverEmployee =\r\n      task.driverEmployee ?? employeeMap.get(task.driverEmployeeId) ?? null;\r\n\r\n    return {\r\n      ...task,\r\n      driverEmployee,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Enriches staff employee assignment with employee details\r\n   * @param task - Base task data\r\n   * @param employeeMap - Map of employees for O(1) lookup\r\n   * @returns Task with enriched staff employee data\r\n   */\r\n  private static enrichStaffEmployee(\r\n    task: Task,\r\n    employeeMap: Map<number, Employee>\r\n  ): Task {\r\n    if (!task.staffEmployeeId) {\r\n      return task;\r\n    }\r\n\r\n    const staffEmployee =\r\n      task.staffEmployee ?? employeeMap.get(task.staffEmployeeId) ?? null;\r\n\r\n    return {\r\n      ...task,\r\n      staffEmployee,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Enriches vehicle assignment with vehicle details\r\n   * @param task - Base task data\r\n   * @param vehicleMap - Map of vehicles for O(1) lookup\r\n   * @returns Task with enriched vehicle data\r\n   */\r\n  private static enrichVehicle(\r\n    task: Task,\r\n    vehicleMap: Map<number, Vehicle>\r\n  ): Task {\r\n    if (!task.vehicleId) {\r\n      return task;\r\n    }\r\n\r\n    const vehicle = task.vehicle ?? vehicleMap.get(task.vehicleId) ?? null;\r\n\r\n    return {\r\n      ...task,\r\n      vehicle,\r\n    };\r\n  }\r\n}\r\n\r\n// Export the main enrichment function for backward compatibility\r\nexport const enrichTask = (\r\n  task: Task,\r\n  employees: Employee[],\r\n  vehicles: Vehicle[]\r\n): Task => {\r\n  return TaskEnrichmentTransformer.enrich(task, employees, vehicles);\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;AAQM,MAAM;IACX;;;;;;GAMC,GACD,OAAO,OAAO,IAAU,EAAE,SAAqB,EAAE,QAAmB,EAAQ;QAC1E,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC,gBAAgB,CACvD,WACA;QAGF,qCAAqC;QACrC,IAAI,eAAe,IAAI,CAAC,mBAAmB,CAAC,MAAM;QAClD,eAAe,IAAI,CAAC,oBAAoB,CAAC,cAAc;QACvD,eAAe,IAAI,CAAC,aAAa,CAAC,cAAc;QAEhD,OAAO;IACT;IAEA;;;;;GAKC,GACD,OAAe,iBAAiB,SAAqB,EAAE,QAAmB,EAAE;QAC1E,kDAAkD;QAClD,MAAM,gBAAgB,MAAM,OAAO,CAAC,aAAa,YAAY,EAAE;QAC/D,MAAM,eAAe,MAAM,OAAO,CAAC,YAAY,WAAW,EAAE;QAE5D,OAAO;YACL,aAAa,IAAI,IAAI,cAAc,GAAG,CAAC,CAAA,MAAO;oBAAC,IAAI,EAAE;oBAAE;iBAAI;YAC3D,YAAY,IAAI,IAAI,aAAa,GAAG,CAAC,CAAA,MAAO;oBAAC,IAAI,EAAE;oBAAE;iBAAI;QAC3D;IACF;IAEA;;;;;GAKC,GACD,OAAe,qBACb,IAAU,EACV,WAAkC,EAC5B;QACN,IAAI,CAAC,KAAK,gBAAgB,EAAE;YAC1B,OAAO;QACT;QAEA,MAAM,iBACJ,KAAK,cAAc,IAAI,YAAY,GAAG,CAAC,KAAK,gBAAgB,KAAK;QAEnE,OAAO;YACL,GAAG,IAAI;YACP;QACF;IACF;IAEA;;;;;GAKC,GACD,OAAe,oBACb,IAAU,EACV,WAAkC,EAC5B;QACN,IAAI,CAAC,KAAK,eAAe,EAAE;YACzB,OAAO;QACT;QAEA,MAAM,gBACJ,KAAK,aAAa,IAAI,YAAY,GAAG,CAAC,KAAK,eAAe,KAAK;QAEjE,OAAO;YACL,GAAG,IAAI;YACP;QACF;IACF;IAEA;;;;;GAKC,GACD,OAAe,cACb,IAAU,EACV,UAAgC,EAC1B;QACN,IAAI,CAAC,KAAK,SAAS,EAAE;YACnB,OAAO;QACT;QAEA,MAAM,UAAU,KAAK,OAAO,IAAI,WAAW,GAAG,CAAC,KAAK,SAAS,KAAK;QAElE,OAAO;YACL,GAAG,IAAI;YACP;QACF;IACF;AACF;AAGO,MAAM,aAAa,CACxB,MACA,WACA;IAEA,OAAO,0BAA0B,MAAM,CAAC,MAAM,WAAW;AAC3D", "debugId": null}}, {"offset": {"line": 2910, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/lib/stores/queries/taskQueries.ts"], "sourcesContent": ["/**\r\n * @file Task query configurations following Single Responsibility Principle\r\n * @description Centralized query configurations for task-related data fetching\r\n */\r\n\r\nimport type { UseQueryOptions } from '@tanstack/react-query';\r\n\r\nimport type { Task } from '../../types/domain';\r\n\r\nimport {\r\n  taskApiService,\r\n  employeeApiService,\r\n  vehicleApiService,\r\n} from '../../api/services/apiServiceFactory';\r\nimport { TaskTransformer } from '../../transformers/taskTransformer';\r\n\r\n/**\r\n * Query keys for task-related queries\r\n */\r\nexport const taskQueryKeys = {\r\n  all: ['tasks'] as const,\r\n  detail: (id: string) => ['tasks', id] as const,\r\n  withAssignments: (id: string) => ['tasks', id, 'with-assignments'] as const,\r\n};\r\n\r\n/**\r\n * Creates query configuration for fetching a single task\r\n */\r\nexport const createTaskQuery = (id: string) => ({\r\n  enabled: !!id,\r\n  queryFn: () => taskApiService.getById(id),\r\n  queryKey: taskQueryKeys.detail(id),\r\n  staleTime: 5 * 60 * 1000, // 5 minutes\r\n});\r\n\r\n/**\r\n * Creates query configuration for fetching all employees\r\n */\r\nexport const createEmployeesQuery = () => ({\r\n  queryFn: () => employeeApiService.getAll(),\r\n  queryKey: ['employees'] as const,\r\n  staleTime: 10 * 60 * 1000, // 10 minutes - employees change less frequently\r\n});\r\n\r\n/**\r\n * Creates query configuration for fetching all vehicles\r\n */\r\nexport const createVehiclesQuery = () => ({\r\n  queryFn: () => vehicleApiService.getAll(),\r\n  queryKey: ['vehicles'] as const,\r\n  staleTime: 10 * 60 * 1000, // 10 minutes - vehicles change less frequently\r\n});\r\n\r\n/**\r\n * Creates parallel query configurations for task with assignments\r\n */\r\nexport const createTaskWithAssignmentsQueries = (id: string) => [\r\n  createTaskQuery(id),\r\n  createEmployeesQuery(),\r\n  createVehiclesQuery(),\r\n];\r\n\r\n/**\r\n * Standard query options for task queries\r\n */\r\nexport const taskQueryOptions: Partial<UseQueryOptions<Task, Error>> = {\r\n  gcTime: 10 * 60 * 1000, // 10 minutes garbage collection time\r\n  retry: 3,\r\n  retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30_000),\r\n  staleTime: 5 * 60 * 1000,\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;AAMD;AAAA;;AAUO,MAAM,gBAAgB;IAC3B,KAAK;QAAC;KAAQ;IACd,QAAQ,CAAC,KAAe;YAAC;YAAS;SAAG;IACrC,iBAAiB,CAAC,KAAe;YAAC;YAAS;YAAI;SAAmB;AACpE;AAKO,MAAM,kBAAkB,CAAC,KAAe,CAAC;QAC9C,SAAS,CAAC,CAAC;QACX,SAAS,IAAM,2IAAA,CAAA,iBAAc,CAAC,OAAO,CAAC;QACtC,UAAU,cAAc,MAAM,CAAC;QAC/B,WAAW,IAAI,KAAK;IACtB,CAAC;AAKM,MAAM,uBAAuB,IAAM,CAAC;QACzC,SAAS,IAAM,2IAAA,CAAA,qBAAkB,CAAC,MAAM;QACxC,UAAU;YAAC;SAAY;QACvB,WAAW,KAAK,KAAK;IACvB,CAAC;AAKM,MAAM,sBAAsB,IAAM,CAAC;QACxC,SAAS,IAAM,2IAAA,CAAA,oBAAiB,CAAC,MAAM;QACvC,UAAU;YAAC;SAAW;QACtB,WAAW,KAAK,KAAK;IACvB,CAAC;AAKM,MAAM,mCAAmC,CAAC,KAAe;QAC9D,gBAAgB;QAChB;QACA;KACD;AAKM,MAAM,mBAA0D;IACrE,QAAQ,KAAK,KAAK;IAClB,OAAO;IACP,YAAY,CAAA,eAAgB,KAAK,GAAG,CAAC,OAAO,KAAK,cAAc;IAC/D,WAAW,IAAI,KAAK;AACtB", "debugId": null}}, {"offset": {"line": 2978, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/lib/stores/queries/useTasks.ts"], "sourcesContent": ["/**\r\n * @file TanStack Query hooks for Task-related data.\r\n * These hooks manage fetching, caching, and mutating task data,\r\n * integrating with the TaskApiService and TaskTransformer.\r\n * @module stores/queries/useTasks\r\n */\r\n\r\nimport type { UseQueryOptions } from '@tanstack/react-query';\r\n\r\nimport { useMutation, useQueries, useQueryClient } from '@tanstack/react-query';\r\nimport { useCallback, useMemo } from 'react';\r\nimport { useCrudQuery } from '@/hooks/api/useSmartQuery'; // Adjusted import path\r\n\r\nimport type { CreateTaskData, Task } from '@/lib/types/domain';\r\n\r\nimport { taskApiService } from '../../api/services/apiServiceFactory'; // Use centralized service\r\nimport { enrichTask } from '../../transformers/taskEnrichment';\r\nimport { TaskTransformer } from '@/lib/transformers/taskTransformer';\r\nimport { createTaskWithAssignmentsQueries, taskQueryKeys } from './taskQueries';\r\nimport { undefinedToNull } from '@/lib/utils/typeHelpers';\r\n\r\n// Re-export query keys from taskQueries for backward compatibility\r\nexport { taskQueryKeys } from './taskQueries';\r\n\r\n/**\r\n * Custom hook to fetch all tasks.\r\n * @param options - Optional React Query options\r\n * @returns Query result containing an array of Task domain models.\r\n */\r\nexport const useTasks = (\r\n  options?: Omit<UseQueryOptions<Task[], Error>, 'queryFn' | 'queryKey'>\r\n) => {\r\n  return useCrudQuery<Task[], Error>(\r\n    [...taskQueryKeys.all], // queryKey - spread for mutability\r\n    async () => {\r\n      const response = await taskApiService.getAll();\r\n      return response.data;\r\n    },\r\n    'task', // entityType\r\n    {\r\n      staleTime: 0, // Existing staleTime\r\n      ...options,\r\n    }\r\n  );\r\n};\r\n\r\n/**\r\n * Custom hook to fetch a single task by its ID.\r\n * @param id - The ID of the task to fetch.\r\n * @returns Query result containing a single Task domain model or undefined.\r\n */\r\nexport const useTask = (id: string) => {\r\n  return useCrudQuery<Task, Error>(\r\n    [...taskQueryKeys.detail(id)],\r\n    async () => {\r\n      return await taskApiService.getById(id);\r\n    },\r\n    'task', // entityType for WebSocket events\r\n    {\r\n      enabled: !!id, // Only run query if id is truthy\r\n      staleTime: 5 * 60 * 1000, // 5 minutes\r\n    }\r\n  );\r\n};\r\n\r\n// ✅ OPTIMIZED: Fast parallel data fetching for task with assignments\r\nexport const useTaskWithAssignments = (id: string) => {\r\n  // Execute all queries in parallel using useQueries for maximum performance\r\n  const results = useQueries({\r\n    queries: createTaskWithAssignmentsQueries(id),\r\n  });\r\n\r\n  const [taskQuery, employeesQuery, vehiclesQuery] = results;\r\n\r\n  // Compute enriched task when all data is available\r\n  const enrichedTask = useMemo(() => {\r\n    if (!taskQuery?.data || !employeesQuery?.data || !vehiclesQuery?.data) {\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const task = TaskTransformer.fromApi(taskQuery.data as any);\r\n\r\n      // Defensive programming: Ensure employees and vehicles are arrays\r\n      const employees = Array.isArray(employeesQuery.data)\r\n        ? employeesQuery.data\r\n        : [];\r\n      const vehicles = Array.isArray(vehiclesQuery.data)\r\n        ? vehiclesQuery.data\r\n        : [];\r\n\r\n      return enrichTask(task, employees, vehicles);\r\n    } catch (error) {\r\n      console.error('Error enriching task data:', error);\r\n      throw error;\r\n    }\r\n  }, [taskQuery?.data, employeesQuery?.data, vehiclesQuery?.data]);\r\n\r\n  // ✅ PRODUCTION FIX: Memoize refetch function to prevent infinite re-renders\r\n  const refetch = useCallback(() => {\r\n    taskQuery?.refetch();\r\n    employeesQuery?.refetch();\r\n    vehiclesQuery?.refetch();\r\n  }, [taskQuery?.refetch, employeesQuery?.refetch, vehiclesQuery?.refetch]);\r\n\r\n  // Return combined state with optimized loading states\r\n  return {\r\n    data: enrichedTask,\r\n    error: taskQuery?.error || employeesQuery?.error || vehiclesQuery?.error,\r\n    isError:\r\n      taskQuery?.isError || employeesQuery?.isError || vehiclesQuery?.isError,\r\n    isLoading:\r\n      taskQuery?.isLoading ||\r\n      employeesQuery?.isLoading ||\r\n      vehiclesQuery?.isLoading,\r\n    isPending:\r\n      taskQuery?.isPending ||\r\n      employeesQuery?.isPending ||\r\n      vehiclesQuery?.isPending,\r\n    refetch,\r\n  };\r\n};\r\n\r\n// ✅ BACKWARD COMPATIBILITY: Alias for the optimized hook\r\nexport const useTaskEnriched = useTaskWithAssignments;\r\n\r\n/**\r\n * Custom hook for creating a new task.\r\n * Includes optimistic updates and cache invalidation.\r\n * @returns Mutation result for creating a task.\r\n */\r\nexport const useCreateTask = () => {\r\n  const queryClient = useQueryClient();\r\n\r\n  interface CreateContext {\r\n    previousTasks: Task[] | undefined;\r\n  }\r\n\r\n  return useMutation<Task, Error, CreateTaskData, CreateContext>({\r\n    mutationFn: async (taskData: CreateTaskData) => {\r\n      const request = TaskTransformer.toCreateRequest(taskData);\r\n      return await taskApiService.create(request); // Removed redundant TaskTransformer.fromApi\r\n    },\r\n    onError: (err, newTaskData, context) => {\r\n      if (context?.previousTasks) {\r\n        queryClient.setQueryData(taskQueryKeys.all, context.previousTasks);\r\n      }\r\n      console.error('Failed to create task:', err);\r\n    },\r\n    onMutate: async newTaskData => {\r\n      await queryClient.cancelQueries({ queryKey: taskQueryKeys.all });\r\n      const previousTasks = queryClient.getQueryData<Task[]>(taskQueryKeys.all);\r\n\r\n      queryClient.setQueryData<Task[]>(taskQueryKeys.all, (old = []) => {\r\n        const tempId = 'optimistic-' + Date.now().toString();\r\n        const now = new Date().toISOString();\r\n        const optimisticTask: Task = {\r\n          createdAt: now,\r\n          dateTime: newTaskData.dateTime ?? null,\r\n          deadline: newTaskData.deadline ?? null,\r\n          description: newTaskData.description, // Direct mapping\r\n          driverEmployee: null,\r\n          driverEmployeeId: newTaskData.driverEmployeeId ?? null,\r\n          estimatedDuration: newTaskData.estimatedDuration ?? null,\r\n          id: tempId,\r\n          location: newTaskData.location ?? null,\r\n          notes: newTaskData.notes ?? null,\r\n          priority: newTaskData.priority,\r\n          requiredSkills: newTaskData.requiredSkills ?? null,\r\n          staffEmployee: null,\r\n          staffEmployeeId: newTaskData.staffEmployeeId ?? null,\r\n          status: newTaskData.status || 'Pending',\r\n          subtasks:\r\n            newTaskData.subtasks?.map(s => ({\r\n              completed: s.completed || false,\r\n              id: `optimistic-subtask-${Date.now()}-${Math.random().toString(36).slice(2, 7)}`,\r\n              taskId: tempId, // Assign the optimistic task's ID as subtask's taskId\r\n              title: s.title,\r\n            })) || [],\r\n          updatedAt: now,\r\n          vehicle: null,\r\n          vehicleId: newTaskData.vehicleId ?? null,\r\n        };\r\n        return [...old, optimisticTask];\r\n      });\r\n\r\n      return { previousTasks };\r\n    },\r\n    onSettled: () => {\r\n      queryClient.invalidateQueries({ queryKey: taskQueryKeys.all });\r\n    },\r\n  });\r\n};\r\n\r\n/**\r\n * Custom hook for updating an existing task.\r\n * Includes optimistic updates and rollback on error.\r\n * @returns Mutation result for updating a task.\r\n */\r\nexport const useUpdateTask = () => {\r\n  const queryClient = useQueryClient();\r\n\r\n  interface UpdateContext {\r\n    previousTask: Task | undefined;\r\n    previousTasksList: Task[] | undefined;\r\n  }\r\n\r\n  return useMutation<\r\n    Task,\r\n    Error,\r\n    { data: Partial<CreateTaskData>; id: string }, // Corrected data type\r\n    UpdateContext\r\n  >({\r\n    mutationFn: async ({ data, id }) => {\r\n      const request = TaskTransformer.toUpdateRequest(data); // Removed cast\r\n      return await taskApiService.update(id, request); // Removed redundant TaskTransformer.fromApi\r\n    },\r\n    onError: (err, variables, context) => {\r\n      if (context?.previousTask) {\r\n        queryClient.setQueryData(\r\n          taskQueryKeys.detail(variables.id),\r\n          context.previousTask\r\n        );\r\n      }\r\n      if (context?.previousTasksList) {\r\n        queryClient.setQueryData(taskQueryKeys.all, context.previousTasksList);\r\n      }\r\n      console.error('Failed to update task:', err);\r\n    },\r\n    onMutate: async ({ data, id }) => {\r\n      await queryClient.cancelQueries({ queryKey: taskQueryKeys.all });\r\n      await queryClient.cancelQueries({ queryKey: taskQueryKeys.detail(id) });\r\n\r\n      const previousTask = queryClient.getQueryData<Task>(\r\n        taskQueryKeys.detail(id)\r\n      );\r\n      const previousTasksList = queryClient.getQueryData<Task[]>(\r\n        taskQueryKeys.all\r\n      );\r\n\r\n      queryClient.setQueryData<Task>(taskQueryKeys.detail(id), old => {\r\n        if (!old) return old;\r\n        const now = new Date().toISOString();\r\n\r\n        // Explicitly map updated fields to avoid issues with spread operator on different types\r\n        const updatedOptimistic: Task = {\r\n          ...old,\r\n          dateTime: data.dateTime !== undefined ? data.dateTime : old.dateTime,\r\n          deadline: undefinedToNull(\r\n            data.deadline !== undefined ? data.deadline : old.deadline\r\n          ),\r\n          description: data.description ?? old.description,\r\n          driverEmployeeId: undefinedToNull(\r\n            data.driverEmployeeId !== undefined\r\n              ? data.driverEmployeeId\r\n              : old.driverEmployeeId\r\n          ),\r\n          estimatedDuration:\r\n            data.estimatedDuration !== undefined\r\n              ? data.estimatedDuration\r\n              : old.estimatedDuration,\r\n          location: data.location !== undefined ? data.location : old.location,\r\n          notes: undefinedToNull(\r\n            data.notes !== undefined ? data.notes : old.notes\r\n          ),\r\n          priority: data.priority ?? old.priority,\r\n          requiredSkills:\r\n            data.requiredSkills !== undefined\r\n              ? data.requiredSkills\r\n              : old.requiredSkills,\r\n          staffEmployeeId:\r\n            data.staffEmployeeId !== undefined\r\n              ? data.staffEmployeeId\r\n              : old.staffEmployeeId,\r\n          status: data.status ?? old.status,\r\n          subtasks:\r\n            data.subtasks?.map(s => ({\r\n              completed: s.completed ?? false,\r\n              id: `optimistic-subtask-${Date.now()}-${Math.random().toString(36).slice(2, 7)}`,\r\n              taskId: id, // Assign the current task's ID as subtask's taskId\r\n              title: s.title,\r\n            })) ||\r\n            old.subtasks ||\r\n            [],\r\n          updatedAt: now,\r\n          vehicleId: undefinedToNull(\r\n            data.vehicleId !== undefined ? data.vehicleId : old.vehicleId\r\n          ),\r\n        };\r\n        return updatedOptimistic;\r\n      });\r\n\r\n      queryClient.setQueryData<Task[]>(taskQueryKeys.all, (old = []) => {\r\n        return old.map(task => {\r\n          if (task.id === id) {\r\n            const now = new Date().toISOString();\r\n            const optimisticSubtasks =\r\n              data.subtasks?.map(s => ({\r\n                completed: s.completed ?? false,\r\n                id: `optimistic-subtask-${Date.now()}-${Math.random().toString(36).slice(2, 7)}`,\r\n                taskId: id, // Assign the current task's ID as subtask's taskId\r\n                title: s.title,\r\n              })) ||\r\n              task.subtasks ||\r\n              [];\r\n            return {\r\n              ...task,\r\n              dateTime:\r\n                data.dateTime !== undefined ? data.dateTime : task.dateTime,\r\n              deadline: undefinedToNull(\r\n                data.deadline !== undefined ? data.deadline : task.deadline\r\n              ),\r\n              description: data.description ?? task.description,\r\n              driverEmployeeId: undefinedToNull(\r\n                data.driverEmployeeId !== undefined\r\n                  ? data.driverEmployeeId\r\n                  : task.driverEmployeeId\r\n              ),\r\n              estimatedDuration:\r\n                data.estimatedDuration !== undefined\r\n                  ? data.estimatedDuration\r\n                  : task.estimatedDuration,\r\n              location:\r\n                data.location !== undefined ? data.location : task.location,\r\n              notes: undefinedToNull(\r\n                data.notes !== undefined ? data.notes : task.notes\r\n              ),\r\n              priority: data.priority ?? task.priority,\r\n              requiredSkills:\r\n                data.requiredSkills !== undefined\r\n                  ? data.requiredSkills\r\n                  : task.requiredSkills,\r\n              staffEmployeeId:\r\n                data.staffEmployeeId !== undefined\r\n                  ? data.staffEmployeeId\r\n                  : task.staffEmployeeId,\r\n              status: data.status ?? task.status,\r\n              subtasks: optimisticSubtasks,\r\n              updatedAt: now,\r\n              vehicleId: undefinedToNull(\r\n                data.vehicleId !== undefined ? data.vehicleId : task.vehicleId\r\n              ),\r\n            };\r\n          }\r\n          return task;\r\n        });\r\n      });\r\n\r\n      return { previousTask, previousTasksList };\r\n    },\r\n    onSettled: (data, error, variables) => {\r\n      queryClient.invalidateQueries({\r\n        queryKey: taskQueryKeys.detail(variables.id),\r\n      });\r\n      queryClient.invalidateQueries({ queryKey: taskQueryKeys.all });\r\n    },\r\n  });\r\n};\r\n\r\n/**\r\n * Custom hook for deleting an existing task.\r\n * Includes cache updates.\r\n * @returns Mutation result for deleting a task.\r\n */\r\nexport const useDeleteTask = () => {\r\n  const queryClient = useQueryClient();\r\n\r\n  interface DeleteContext {\r\n    previousTasksList: Task[] | undefined;\r\n  }\r\n\r\n  return useMutation<string, Error, string, DeleteContext>({\r\n    mutationFn: async (id: string) => {\r\n      await taskApiService.delete(id);\r\n      return id;\r\n    },\r\n    onError: (err, id, context) => {\r\n      if (context?.previousTasksList) {\r\n        queryClient.setQueryData(taskQueryKeys.all, context.previousTasksList);\r\n      }\r\n      console.error('Failed to delete task:', err);\r\n    },\r\n    onMutate: async id => {\r\n      await queryClient.cancelQueries({ queryKey: taskQueryKeys.all });\r\n      await queryClient.cancelQueries({ queryKey: taskQueryKeys.detail(id) });\r\n\r\n      const previousTasksList = queryClient.getQueryData<Task[]>(\r\n        taskQueryKeys.all\r\n      );\r\n\r\n      queryClient.setQueryData<Task[]>(taskQueryKeys.all, (old = []) =>\r\n        old.filter(task => task.id !== id)\r\n      );\r\n\r\n      queryClient.removeQueries({ queryKey: taskQueryKeys.detail(id) });\r\n\r\n      return { previousTasksList };\r\n    },\r\n    onSettled: () => {\r\n      queryClient.invalidateQueries({ queryKey: taskQueryKeys.all });\r\n    },\r\n  });\r\n};\r\n\r\n// Removed useAssignTask and useManageSubtasks hooks as their functionality is now\r\n// handled by the main create/update task mutations.\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;;;;;;AAID;AAAA;AAAA;AACA;AACA,6OAA0D,uBAAuB;AAIjF,sTAAuE,0BAA0B;AAAjG;AACA;AACA;AACA;AACA;;;;;;;;;;;AAUO,MAAM,WAAW,CACtB;;IAEA,OAAO,CAAA,GAAA,uIAAA,CAAA,eAAY,AAAD,EAChB;WAAI,iJAAA,CAAA,gBAAa,CAAC,GAAG;KAAC;iCACtB;YACE,MAAM,WAAW,MAAM,2IAAA,CAAA,iBAAc,CAAC,MAAM;YAC5C,OAAO,SAAS,IAAI;QACtB;gCACA,QACA;QACE,WAAW;QACX,GAAG,OAAO;IACZ;AAEJ;GAfa;;QAGJ,uIAAA,CAAA,eAAY;;;AAmBd,MAAM,UAAU,CAAC;;IACtB,OAAO,CAAA,GAAA,uIAAA,CAAA,eAAY,AAAD,EAChB;WAAI,iJAAA,CAAA,gBAAa,CAAC,MAAM,CAAC;KAAI;gCAC7B;YACE,OAAO,MAAM,2IAAA,CAAA,iBAAc,CAAC,OAAO,CAAC;QACtC;+BACA,QACA;QACE,SAAS,CAAC,CAAC;QACX,WAAW,IAAI,KAAK;IACtB;AAEJ;IAZa;;QACJ,uIAAA,CAAA,eAAY;;;AAcd,MAAM,yBAAyB,CAAC;;IACrC,2EAA2E;IAC3E,MAAM,UAAU,CAAA,GAAA,gLAAA,CAAA,aAAU,AAAD,EAAE;QACzB,SAAS,CAAA,GAAA,iJAAA,CAAA,mCAAgC,AAAD,EAAE;IAC5C;IAEA,MAAM,CAAC,WAAW,gBAAgB,cAAc,GAAG;IAEnD,mDAAmD;IACnD,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;wDAAE;YAC3B,IAAI,CAAC,WAAW,QAAQ,CAAC,gBAAgB,QAAQ,CAAC,eAAe,MAAM;gBACrE;YACF;YAEA,IAAI;gBACF,MAAM,OAAO,gJAAA,CAAA,kBAAe,CAAC,OAAO,CAAC,UAAU,IAAI;gBAEnD,kEAAkE;gBAClE,MAAM,YAAY,MAAM,OAAO,CAAC,eAAe,IAAI,IAC/C,eAAe,IAAI,GACnB,EAAE;gBACN,MAAM,WAAW,MAAM,OAAO,CAAC,cAAc,IAAI,IAC7C,cAAc,IAAI,GAClB,EAAE;gBAEN,OAAO,CAAA,GAAA,+IAAA,CAAA,aAAU,AAAD,EAAE,MAAM,WAAW;YACrC,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,8BAA8B;gBAC5C,MAAM;YACR;QACF;uDAAG;QAAC,WAAW;QAAM,gBAAgB;QAAM,eAAe;KAAK;IAE/D,4EAA4E;IAC5E,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE;YAC1B,WAAW;YACX,gBAAgB;YAChB,eAAe;QACjB;sDAAG;QAAC,WAAW;QAAS,gBAAgB;QAAS,eAAe;KAAQ;IAExE,sDAAsD;IACtD,OAAO;QACL,MAAM;QACN,OAAO,WAAW,SAAS,gBAAgB,SAAS,eAAe;QACnE,SACE,WAAW,WAAW,gBAAgB,WAAW,eAAe;QAClE,WACE,WAAW,aACX,gBAAgB,aAChB,eAAe;QACjB,WACE,WAAW,aACX,gBAAgB,aAChB,eAAe;QACjB;IACF;AACF;IAvDa;;QAEK,gLAAA,CAAA,aAAU;;;AAwDrB,MAAM,kBAAkB;AAOxB,MAAM,gBAAgB;;IAC3B,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAMjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAA8C;QAC7D,UAAU;yCAAE,OAAO;gBACjB,MAAM,UAAU,gJAAA,CAAA,kBAAe,CAAC,eAAe,CAAC;gBAChD,OAAO,MAAM,2IAAA,CAAA,iBAAc,CAAC,MAAM,CAAC,UAAU,4CAA4C;YAC3F;;QACA,OAAO;yCAAE,CAAC,KAAK,aAAa;gBAC1B,IAAI,SAAS,eAAe;oBAC1B,YAAY,YAAY,CAAC,iJAAA,CAAA,gBAAa,CAAC,GAAG,EAAE,QAAQ,aAAa;gBACnE;gBACA,QAAQ,KAAK,CAAC,0BAA0B;YAC1C;;QACA,QAAQ;yCAAE,OAAM;gBACd,MAAM,YAAY,aAAa,CAAC;oBAAE,UAAU,iJAAA,CAAA,gBAAa,CAAC,GAAG;gBAAC;gBAC9D,MAAM,gBAAgB,YAAY,YAAY,CAAS,iJAAA,CAAA,gBAAa,CAAC,GAAG;gBAExE,YAAY,YAAY,CAAS,iJAAA,CAAA,gBAAa,CAAC,GAAG;iDAAE,CAAC,MAAM,EAAE;wBAC3D,MAAM,SAAS,gBAAgB,KAAK,GAAG,GAAG,QAAQ;wBAClD,MAAM,MAAM,IAAI,OAAO,WAAW;wBAClC,MAAM,iBAAuB;4BAC3B,WAAW;4BACX,UAAU,YAAY,QAAQ,IAAI;4BAClC,UAAU,YAAY,QAAQ,IAAI;4BAClC,aAAa,YAAY,WAAW;4BACpC,gBAAgB;4BAChB,kBAAkB,YAAY,gBAAgB,IAAI;4BAClD,mBAAmB,YAAY,iBAAiB,IAAI;4BACpD,IAAI;4BACJ,UAAU,YAAY,QAAQ,IAAI;4BAClC,OAAO,YAAY,KAAK,IAAI;4BAC5B,UAAU,YAAY,QAAQ;4BAC9B,gBAAgB,YAAY,cAAc,IAAI;4BAC9C,eAAe;4BACf,iBAAiB,YAAY,eAAe,IAAI;4BAChD,QAAQ,YAAY,MAAM,IAAI;4BAC9B,UACE,YAAY,QAAQ,EAAE;6DAAI,CAAA,IAAK,CAAC;wCAC9B,WAAW,EAAE,SAAS,IAAI;wCAC1B,IAAI,CAAC,mBAAmB,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,KAAK,CAAC,GAAG,IAAI;wCAChF,QAAQ;wCACR,OAAO,EAAE,KAAK;oCAChB,CAAC;+DAAM,EAAE;4BACX,WAAW;4BACX,SAAS;4BACT,WAAW,YAAY,SAAS,IAAI;wBACtC;wBACA,OAAO;+BAAI;4BAAK;yBAAe;oBACjC;;gBAEA,OAAO;oBAAE;gBAAc;YACzB;;QACA,SAAS;yCAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU,iJAAA,CAAA,gBAAa,CAAC,GAAG;gBAAC;YAC9D;;IACF;AACF;IA7Da;;QACS,yLAAA,CAAA,iBAAc;QAM3B,iLAAA,CAAA,cAAW;;;AA6Db,MAAM,gBAAgB;;IAC3B,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAOjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAKf;QACA,UAAU;yCAAE,OAAO,EAAE,IAAI,EAAE,EAAE,EAAE;gBAC7B,MAAM,UAAU,gJAAA,CAAA,kBAAe,CAAC,eAAe,CAAC,OAAO,eAAe;gBACtE,OAAO,MAAM,2IAAA,CAAA,iBAAc,CAAC,MAAM,CAAC,IAAI,UAAU,4CAA4C;YAC/F;;QACA,OAAO;yCAAE,CAAC,KAAK,WAAW;gBACxB,IAAI,SAAS,cAAc;oBACzB,YAAY,YAAY,CACtB,iJAAA,CAAA,gBAAa,CAAC,MAAM,CAAC,UAAU,EAAE,GACjC,QAAQ,YAAY;gBAExB;gBACA,IAAI,SAAS,mBAAmB;oBAC9B,YAAY,YAAY,CAAC,iJAAA,CAAA,gBAAa,CAAC,GAAG,EAAE,QAAQ,iBAAiB;gBACvE;gBACA,QAAQ,KAAK,CAAC,0BAA0B;YAC1C;;QACA,QAAQ;yCAAE,OAAO,EAAE,IAAI,EAAE,EAAE,EAAE;gBAC3B,MAAM,YAAY,aAAa,CAAC;oBAAE,UAAU,iJAAA,CAAA,gBAAa,CAAC,GAAG;gBAAC;gBAC9D,MAAM,YAAY,aAAa,CAAC;oBAAE,UAAU,iJAAA,CAAA,gBAAa,CAAC,MAAM,CAAC;gBAAI;gBAErE,MAAM,eAAe,YAAY,YAAY,CAC3C,iJAAA,CAAA,gBAAa,CAAC,MAAM,CAAC;gBAEvB,MAAM,oBAAoB,YAAY,YAAY,CAChD,iJAAA,CAAA,gBAAa,CAAC,GAAG;gBAGnB,YAAY,YAAY,CAAO,iJAAA,CAAA,gBAAa,CAAC,MAAM,CAAC;iDAAK,CAAA;wBACvD,IAAI,CAAC,KAAK,OAAO;wBACjB,MAAM,MAAM,IAAI,OAAO,WAAW;wBAElC,wFAAwF;wBACxF,MAAM,oBAA0B;4BAC9B,GAAG,GAAG;4BACN,UAAU,KAAK,QAAQ,KAAK,YAAY,KAAK,QAAQ,GAAG,IAAI,QAAQ;4BACpE,UAAU,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EACtB,KAAK,QAAQ,KAAK,YAAY,KAAK,QAAQ,GAAG,IAAI,QAAQ;4BAE5D,aAAa,KAAK,WAAW,IAAI,IAAI,WAAW;4BAChD,kBAAkB,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EAC9B,KAAK,gBAAgB,KAAK,YACtB,KAAK,gBAAgB,GACrB,IAAI,gBAAgB;4BAE1B,mBACE,KAAK,iBAAiB,KAAK,YACvB,KAAK,iBAAiB,GACtB,IAAI,iBAAiB;4BAC3B,UAAU,KAAK,QAAQ,KAAK,YAAY,KAAK,QAAQ,GAAG,IAAI,QAAQ;4BACpE,OAAO,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EACnB,KAAK,KAAK,KAAK,YAAY,KAAK,KAAK,GAAG,IAAI,KAAK;4BAEnD,UAAU,KAAK,QAAQ,IAAI,IAAI,QAAQ;4BACvC,gBACE,KAAK,cAAc,KAAK,YACpB,KAAK,cAAc,GACnB,IAAI,cAAc;4BACxB,iBACE,KAAK,eAAe,KAAK,YACrB,KAAK,eAAe,GACpB,IAAI,eAAe;4BACzB,QAAQ,KAAK,MAAM,IAAI,IAAI,MAAM;4BACjC,UACE,KAAK,QAAQ,EAAE;6DAAI,CAAA,IAAK,CAAC;wCACvB,WAAW,EAAE,SAAS,IAAI;wCAC1B,IAAI,CAAC,mBAAmB,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,KAAK,CAAC,GAAG,IAAI;wCAChF,QAAQ;wCACR,OAAO,EAAE,KAAK;oCAChB,CAAC;+DACD,IAAI,QAAQ,IACZ,EAAE;4BACJ,WAAW;4BACX,WAAW,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EACvB,KAAK,SAAS,KAAK,YAAY,KAAK,SAAS,GAAG,IAAI,SAAS;wBAEjE;wBACA,OAAO;oBACT;;gBAEA,YAAY,YAAY,CAAS,iJAAA,CAAA,gBAAa,CAAC,GAAG;iDAAE,CAAC,MAAM,EAAE;wBAC3D,OAAO,IAAI,GAAG;yDAAC,CAAA;gCACb,IAAI,KAAK,EAAE,KAAK,IAAI;oCAClB,MAAM,MAAM,IAAI,OAAO,WAAW;oCAClC,MAAM,qBACJ,KAAK,QAAQ,EAAE;qEAAI,CAAA,IAAK,CAAC;gDACvB,WAAW,EAAE,SAAS,IAAI;gDAC1B,IAAI,CAAC,mBAAmB,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,KAAK,CAAC,GAAG,IAAI;gDAChF,QAAQ;gDACR,OAAO,EAAE,KAAK;4CAChB,CAAC;uEACD,KAAK,QAAQ,IACb,EAAE;oCACJ,OAAO;wCACL,GAAG,IAAI;wCACP,UACE,KAAK,QAAQ,KAAK,YAAY,KAAK,QAAQ,GAAG,KAAK,QAAQ;wCAC7D,UAAU,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EACtB,KAAK,QAAQ,KAAK,YAAY,KAAK,QAAQ,GAAG,KAAK,QAAQ;wCAE7D,aAAa,KAAK,WAAW,IAAI,KAAK,WAAW;wCACjD,kBAAkB,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EAC9B,KAAK,gBAAgB,KAAK,YACtB,KAAK,gBAAgB,GACrB,KAAK,gBAAgB;wCAE3B,mBACE,KAAK,iBAAiB,KAAK,YACvB,KAAK,iBAAiB,GACtB,KAAK,iBAAiB;wCAC5B,UACE,KAAK,QAAQ,KAAK,YAAY,KAAK,QAAQ,GAAG,KAAK,QAAQ;wCAC7D,OAAO,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EACnB,KAAK,KAAK,KAAK,YAAY,KAAK,KAAK,GAAG,KAAK,KAAK;wCAEpD,UAAU,KAAK,QAAQ,IAAI,KAAK,QAAQ;wCACxC,gBACE,KAAK,cAAc,KAAK,YACpB,KAAK,cAAc,GACnB,KAAK,cAAc;wCACzB,iBACE,KAAK,eAAe,KAAK,YACrB,KAAK,eAAe,GACpB,KAAK,eAAe;wCAC1B,QAAQ,KAAK,MAAM,IAAI,KAAK,MAAM;wCAClC,UAAU;wCACV,WAAW;wCACX,WAAW,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EACvB,KAAK,SAAS,KAAK,YAAY,KAAK,SAAS,GAAG,KAAK,SAAS;oCAElE;gCACF;gCACA,OAAO;4BACT;;oBACF;;gBAEA,OAAO;oBAAE;oBAAc;gBAAkB;YAC3C;;QACA,SAAS;yCAAE,CAAC,MAAM,OAAO;gBACvB,YAAY,iBAAiB,CAAC;oBAC5B,UAAU,iJAAA,CAAA,gBAAa,CAAC,MAAM,CAAC,UAAU,EAAE;gBAC7C;gBACA,YAAY,iBAAiB,CAAC;oBAAE,UAAU,iJAAA,CAAA,gBAAa,CAAC,GAAG;gBAAC;YAC9D;;IACF;AACF;IA9Ja;;QACS,yLAAA,CAAA,iBAAc;QAO3B,iLAAA,CAAA,cAAW;;;AA6Jb,MAAM,gBAAgB;;IAC3B,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAMjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAwC;QACvD,UAAU;yCAAE,OAAO;gBACjB,MAAM,2IAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;gBAC5B,OAAO;YACT;;QACA,OAAO;yCAAE,CAAC,KAAK,IAAI;gBACjB,IAAI,SAAS,mBAAmB;oBAC9B,YAAY,YAAY,CAAC,iJAAA,CAAA,gBAAa,CAAC,GAAG,EAAE,QAAQ,iBAAiB;gBACvE;gBACA,QAAQ,KAAK,CAAC,0BAA0B;YAC1C;;QACA,QAAQ;yCAAE,OAAM;gBACd,MAAM,YAAY,aAAa,CAAC;oBAAE,UAAU,iJAAA,CAAA,gBAAa,CAAC,GAAG;gBAAC;gBAC9D,MAAM,YAAY,aAAa,CAAC;oBAAE,UAAU,iJAAA,CAAA,gBAAa,CAAC,MAAM,CAAC;gBAAI;gBAErE,MAAM,oBAAoB,YAAY,YAAY,CAChD,iJAAA,CAAA,gBAAa,CAAC,GAAG;gBAGnB,YAAY,YAAY,CAAS,iJAAA,CAAA,gBAAa,CAAC,GAAG;iDAAE,CAAC,MAAM,EAAE,GAC3D,IAAI,MAAM;yDAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;;;gBAGjC,YAAY,aAAa,CAAC;oBAAE,UAAU,iJAAA,CAAA,gBAAa,CAAC,MAAM,CAAC;gBAAI;gBAE/D,OAAO;oBAAE;gBAAkB;YAC7B;;QACA,SAAS;yCAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU,iJAAA,CAAA,gBAAa,CAAC,GAAG;gBAAC;YAC9D;;IACF;AACF,GAEA,kFAAkF;CAClF,oDAAoD;IAzCvC;;QACS,yLAAA,CAAA,iBAAc;QAM3B,iLAAA,CAAA,cAAW", "debugId": null}}, {"offset": {"line": 3393, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/lib/stores/queries/useVehicles.ts"], "sourcesContent": ["/**\r\n * @file TanStack Query hooks for Vehicle-related data.\r\n * @module stores/queries/useVehicles\r\n */\r\n\r\nimport type { UseQueryOptions } from '@tanstack/react-query';\r\n\r\nimport { useMutation, useQueryClient } from '@tanstack/react-query';\r\n\r\nimport type { ApiError } from '@/lib/types/api';\r\nimport type { CreateVehicleData, Vehicle } from '@/lib/types/domain';\r\n\r\nimport { useCrudQuery } from '@/hooks/api/useSmartQuery';\r\nimport { useNotifications } from '@/hooks/ui/useNotifications';\r\nimport { VehicleTransformer } from '@/lib/transformers/vehicleTransformer';\r\n\r\nimport { vehicleApiService } from '../../api/services/apiServiceFactory'; // Use centralized service from factory\r\n\r\nexport const vehicleQueryKeys = {\r\n  all: ['vehicles'] as const,\r\n  detail: (id: number) => ['vehicles', id] as const,\r\n};\r\n\r\n/**\r\n * Hook to fetch all vehicles.\r\n */\r\nexport const useVehicles = (\r\n  options?: Omit<\r\n    UseQueryOptions<Vehicle[], ApiError>,\r\n    'queryFn' | 'queryKey'\r\n  > & { enabled?: boolean }\r\n) => {\r\n  return useCrudQuery<Vehicle[], ApiError>(\r\n    [...vehicleQueryKeys.all], // Spread to create a mutable array\r\n    async () => {\r\n      const response = await vehicleApiService.getAll();\r\n      return response.data;\r\n    },\r\n    'vehicle', // entityType for WebSocket events\r\n    {\r\n      staleTime: 5 * 60 * 1000, // 5 minutes\r\n      ...options,\r\n    }\r\n  );\r\n};\r\n\r\n/**\r\n * Hook to fetch a single vehicle by its ID.\r\n */\r\nexport const useVehicle = (\r\n  id: null | number,\r\n  options?: Omit<\r\n    UseQueryOptions<Vehicle, ApiError>,\r\n    'enabled' | 'queryFn' | 'queryKey'\r\n  > & { enabled?: boolean }\r\n) => {\r\n  return useCrudQuery<Vehicle, ApiError>(\r\n    [...vehicleQueryKeys.detail(id!)],\r\n    () => vehicleApiService.getById(id!),\r\n    'vehicle', // entityType for WebSocket events\r\n    {\r\n      enabled: !!id && (options?.enabled ?? true), // Only run query if id is not null AND options.enabled is true (or undefined)\r\n      staleTime: 5 * 60 * 1000,\r\n      ...options, // Spread additional options\r\n    }\r\n  );\r\n};\r\n\r\n/**\r\n * Hook to create a new vehicle.\r\n */\r\nexport const useCreateVehicle = () => {\r\n  const queryClient = useQueryClient();\r\n  const { showError, showSuccess } = useNotifications();\r\n\r\n  return useMutation<Vehicle, ApiError, CreateVehicleData>({\r\n    mutationFn: (newVehicleData: CreateVehicleData) => {\r\n      const transformedData =\r\n        VehicleTransformer.toCreateRequest(newVehicleData);\r\n      return vehicleApiService.create(transformedData);\r\n    },\r\n    onError: error => {\r\n      showError(\r\n        `Failed to create vehicle: ${error.message || 'Unknown error occurred'}`\r\n      );\r\n    },\r\n    onSuccess: data => {\r\n      // Invalidate and refetch all vehicles query after a successful creation\r\n      queryClient.invalidateQueries({ queryKey: vehicleQueryKeys.all });\r\n      showSuccess(\r\n        `Vehicle \"${data.licensePlate}\" has been created successfully!`\r\n      );\r\n    },\r\n  });\r\n};\r\n\r\n/**\r\n * Hook to update an existing vehicle.\r\n */\r\nexport const useUpdateVehicle = () => {\r\n  const queryClient = useQueryClient();\r\n  const { showError, showSuccess } = useNotifications();\r\n\r\n  return useMutation<\r\n    Vehicle,\r\n    ApiError,\r\n    { data: Partial<CreateVehicleData>; id: number }\r\n  >({\r\n    mutationFn: ({ data, id }) => {\r\n      const transformedData = VehicleTransformer.toUpdateRequest(data);\r\n      return vehicleApiService.update(id, transformedData);\r\n    },\r\n    onError: error => {\r\n      showError(\r\n        `Failed to update vehicle: ${error.message || 'Unknown error occurred'}`\r\n      );\r\n    },\r\n    onSuccess: updatedVehicle => {\r\n      queryClient.invalidateQueries({ queryKey: vehicleQueryKeys.all });\r\n      queryClient.invalidateQueries({\r\n        queryKey: vehicleQueryKeys.detail(updatedVehicle.id),\r\n      });\r\n      showSuccess(\r\n        `Vehicle \"${updatedVehicle.licensePlate}\" has been updated successfully!`\r\n      );\r\n    },\r\n  });\r\n};\r\n\r\n/**\r\n * Hook to delete a vehicle.\r\n */\r\nexport const useDeleteVehicle = () => {\r\n  const queryClient = useQueryClient();\r\n  const { showError, showSuccess } = useNotifications();\r\n\r\n  return useMutation<void, ApiError, number>({\r\n    mutationFn: (id: number) => vehicleApiService.delete(id),\r\n    onError: error => {\r\n      showError(\r\n        `Failed to delete vehicle: ${error.message || 'Unknown error occurred'}`\r\n      );\r\n    },\r\n    onSuccess: (_data, id) => {\r\n      queryClient.invalidateQueries({ queryKey: vehicleQueryKeys.all });\r\n      queryClient.removeQueries({ queryKey: vehicleQueryKeys.detail(id) });\r\n      showSuccess('Vehicle has been deleted successfully!');\r\n    },\r\n  });\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;AAID;AAAA;AAKA;AACA;AACA;AAEA,sTAA0E,uCAAuC;AAAjH;;;;;;;AAEO,MAAM,mBAAmB;IAC9B,KAAK;QAAC;KAAW;IACjB,QAAQ,CAAC,KAAe;YAAC;YAAY;SAAG;AAC1C;AAKO,MAAM,cAAc,CACzB;;IAKA,OAAO,CAAA,GAAA,uIAAA,CAAA,eAAY,AAAD,EAChB;WAAI,iBAAiB,GAAG;KAAC;oCACzB;YACE,MAAM,WAAW,MAAM,2IAAA,CAAA,oBAAiB,CAAC,MAAM;YAC/C,OAAO,SAAS,IAAI;QACtB;mCACA,WACA;QACE,WAAW,IAAI,KAAK;QACpB,GAAG,OAAO;IACZ;AAEJ;GAlBa;;QAMJ,uIAAA,CAAA,eAAY;;;AAiBd,MAAM,aAAa,CACxB,IACA;;IAKA,OAAO,CAAA,GAAA,uIAAA,CAAA,eAAY,AAAD,EAChB;WAAI,iBAAiB,MAAM,CAAC;KAAK;mCACjC,IAAM,2IAAA,CAAA,oBAAiB,CAAC,OAAO,CAAC;kCAChC,WACA;QACE,SAAS,CAAC,CAAC,MAAM,CAAC,SAAS,WAAW,IAAI;QAC1C,WAAW,IAAI,KAAK;QACpB,GAAG,OAAO;IACZ;AAEJ;IAjBa;;QAOJ,uIAAA,CAAA,eAAY;;;AAed,MAAM,mBAAmB;;IAC9B,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,mBAAgB,AAAD;IAElD,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAwC;QACvD,UAAU;4CAAE,CAAC;gBACX,MAAM,kBACJ,mJAAA,CAAA,qBAAkB,CAAC,eAAe,CAAC;gBACrC,OAAO,2IAAA,CAAA,oBAAiB,CAAC,MAAM,CAAC;YAClC;;QACA,OAAO;4CAAE,CAAA;gBACP,UACE,CAAC,0BAA0B,EAAE,MAAM,OAAO,IAAI,0BAA0B;YAE5E;;QACA,SAAS;4CAAE,CAAA;gBACT,wEAAwE;gBACxE,YAAY,iBAAiB,CAAC;oBAAE,UAAU,iBAAiB,GAAG;gBAAC;gBAC/D,YACE,CAAC,SAAS,EAAE,KAAK,YAAY,CAAC,gCAAgC,CAAC;YAEnE;;IACF;AACF;IAvBa;;QACS,yLAAA,CAAA,iBAAc;QACC,yIAAA,CAAA,mBAAgB;QAE5C,iLAAA,CAAA,cAAW;;;AAwBb,MAAM,mBAAmB;;IAC9B,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,mBAAgB,AAAD;IAElD,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAIf;QACA,UAAU;4CAAE,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE;gBACvB,MAAM,kBAAkB,mJAAA,CAAA,qBAAkB,CAAC,eAAe,CAAC;gBAC3D,OAAO,2IAAA,CAAA,oBAAiB,CAAC,MAAM,CAAC,IAAI;YACtC;;QACA,OAAO;4CAAE,CAAA;gBACP,UACE,CAAC,0BAA0B,EAAE,MAAM,OAAO,IAAI,0BAA0B;YAE5E;;QACA,SAAS;4CAAE,CAAA;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU,iBAAiB,GAAG;gBAAC;gBAC/D,YAAY,iBAAiB,CAAC;oBAC5B,UAAU,iBAAiB,MAAM,CAAC,eAAe,EAAE;gBACrD;gBACA,YACE,CAAC,SAAS,EAAE,eAAe,YAAY,CAAC,gCAAgC,CAAC;YAE7E;;IACF;AACF;IA5Ba;;QACS,yLAAA,CAAA,iBAAc;QACC,yIAAA,CAAA,mBAAgB;QAE5C,iLAAA,CAAA,cAAW;;;AA6Bb,MAAM,mBAAmB;;IAC9B,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,mBAAgB,AAAD;IAElD,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAA0B;QACzC,UAAU;4CAAE,CAAC,KAAe,2IAAA,CAAA,oBAAiB,CAAC,MAAM,CAAC;;QACrD,OAAO;4CAAE,CAAA;gBACP,UACE,CAAC,0BAA0B,EAAE,MAAM,OAAO,IAAI,0BAA0B;YAE5E;;QACA,SAAS;4CAAE,CAAC,OAAO;gBACjB,YAAY,iBAAiB,CAAC;oBAAE,UAAU,iBAAiB,GAAG;gBAAC;gBAC/D,YAAY,aAAa,CAAC;oBAAE,UAAU,iBAAiB,MAAM,CAAC;gBAAI;gBAClE,YAAY;YACd;;IACF;AACF;IAjBa;;QACS,yLAAA,CAAA,iBAAc;QACC,yIAAA,CAAA,mBAAgB;QAE5C,iLAAA,CAAA,cAAW", "debugId": null}}, {"offset": {"line": 3574, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/dashboard/ModernDashboard.tsx"], "sourcesContent": ["/**\r\n * @file Modern dashboard component with widgets and insights\r\n * @module components/dashboard/ModernDashboard\r\n */\r\n\r\n'use client';\r\n\r\nimport {\r\n  Activity,\r\n  AlertTriangle,\r\n  BarChart3,\r\n  Bell,\r\n  Calendar,\r\n  Car,\r\n  CheckCircle,\r\n  Clock,\r\n  DollarSign,\r\n  Eye,\r\n  MoreHorizontal,\r\n  Plus,\r\n  TrendingDown,\r\n  TrendingUp,\r\n  Users,\r\n  Wrench,\r\n} from 'lucide-react';\r\nimport Link from 'next/link';\r\nimport React, { useMemo } from 'react';\r\n\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Button } from '@/components/ui/button';\r\nimport {\r\n  Card,\r\n  CardContent,\r\n  CardDescription,\r\n  CardHeader,\r\n  CardTitle,\r\n} from '@/components/ui/card';\r\nimport { Progress } from '@/components/ui/progress';\r\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';\r\nimport { useAuthContext } from '@/contexts/AuthContext';\r\nimport { useDelegations } from '@/lib/stores/queries/useDelegations';\r\nimport { useEmployees } from '@/lib/stores/queries/useEmployees';\r\nimport { useEnrichedServiceRecords } from '@/lib/stores/queries/useServiceRecords';\r\nimport { useTasks } from '@/lib/stores/queries/useTasks';\r\nimport { useVehicles } from '@/lib/stores/queries/useVehicles';\r\nimport { cn } from '@/lib/utils';\r\n\r\ninterface StatCardProps {\r\n  title: string;\r\n  value: string | number;\r\n  description?: string;\r\n  icon: React.ComponentType<React.SVGProps<SVGSVGElement>>;\r\n  trend?: {\r\n    value: number;\r\n    isPositive: boolean;\r\n  };\r\n  href?: string;\r\n}\r\n\r\ninterface RecentActivityItem {\r\n  id: string;\r\n  title: string;\r\n  description: string;\r\n  timestamp: string;\r\n  type: 'task' | 'vehicle' | 'maintenance' | 'delegation';\r\n  priority?: 'high' | 'medium' | 'low';\r\n}\r\n\r\ninterface QuickActionProps {\r\n  title: string;\r\n  description: string;\r\n  icon: React.ComponentType<React.SVGProps<SVGSVGElement>>;\r\n  href: string;\r\n  variant?: 'default' | 'secondary' | 'outline';\r\n}\r\n\r\nconst StatCard: React.FC<StatCardProps> = ({\r\n  title,\r\n  value,\r\n  description,\r\n  icon: Icon,\r\n  trend,\r\n  href,\r\n}) => {\r\n  const CardWrapper = href ? Link : 'div';\r\n\r\n  return (\r\n    <CardWrapper href={href || '#'} className={href ? 'block' : ''}>\r\n      <Card\r\n        className={cn(\r\n          'transition-all duration-200',\r\n          href && 'hover:shadow-md cursor-pointer'\r\n        )}\r\n      >\r\n        <CardContent className=\"p-6\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <div>\r\n              <p className=\"text-sm font-medium text-muted-foreground\">\r\n                {title}\r\n              </p>\r\n              <p className=\"text-3xl font-bold\">{value}</p>\r\n              {description && (\r\n                <p className=\"text-xs text-muted-foreground mt-1\">\r\n                  {description}\r\n                </p>\r\n              )}\r\n            </div>\r\n            <div className=\"flex flex-col items-end gap-2\">\r\n              <div className=\"rounded-md bg-primary/10 p-2\">\r\n                <Icon className=\"size-4 text-primary\" />\r\n              </div>\r\n              {trend && (\r\n                <div\r\n                  className={cn(\r\n                    'flex items-center text-xs',\r\n                    trend.isPositive ? 'text-green-600' : 'text-red-600'\r\n                  )}\r\n                >\r\n                  {trend.isPositive ? (\r\n                    <TrendingUp className=\"size-3 mr-1\" />\r\n                  ) : (\r\n                    <TrendingDown className=\"size-3 mr-1\" />\r\n                  )}\r\n                  {Math.abs(trend.value)}%\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n    </CardWrapper>\r\n  );\r\n};\r\n\r\nconst QuickAction: React.FC<QuickActionProps> = ({\r\n  title,\r\n  description,\r\n  icon: Icon,\r\n  href,\r\n  variant = 'outline',\r\n}) => {\r\n  return (\r\n    <Card className=\"hover:shadow-md transition-all duration-200\">\r\n      <CardContent className=\"p-4\">\r\n        <div className=\"flex items-start gap-3\">\r\n          <div className=\"rounded-md bg-primary/10 p-2\">\r\n            <Icon className=\"size-4 text-primary\" />\r\n          </div>\r\n          <div className=\"flex-1 min-w-0\">\r\n            <h4 className=\"text-sm font-medium\">{title}</h4>\r\n            <p className=\"text-xs text-muted-foreground mt-1\">{description}</p>\r\n            <Button asChild variant={variant} size=\"sm\" className=\"mt-3 w-full\">\r\n              <Link href={href}>Get Started</Link>\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n};\r\n\r\nexport const ModernDashboard: React.FC = () => {\r\n  const { user, isInitialized, loading } = useAuthContext();\r\n\r\n  // Only make API calls when user is authenticated and auth system is ready\r\n  // MEMOIZED to prevent infinite re-renders caused by user object reference changes\r\n  const isAuthReady = useMemo(() => {\r\n    return isInitialized && !loading && !!user;\r\n  }, [isInitialized, loading, user?.id]); // Use user.id instead of user object to prevent reference issues\r\n\r\n  const { data: vehicles = [] } = useVehicles({ enabled: isAuthReady });\r\n  const { data: delegations = [] } = useDelegations({ enabled: isAuthReady });\r\n  const { data: tasks = [] } = useTasks({ enabled: isAuthReady });\r\n  const { data: employees = [] } = useEmployees({ enabled: isAuthReady });\r\n  const { data: serviceRecords = [] } = useEnrichedServiceRecords({\r\n    enabled: isAuthReady,\r\n  });\r\n\r\n  // Show loading state while authentication is initializing\r\n  if (!isInitialized || loading) {\r\n    return (\r\n      <div className=\"flex items-center justify-center min-h-screen\">\r\n        <div className=\"text-center\">\r\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto\"></div>\r\n          <p className=\"mt-4 text-sm text-muted-foreground\">Initializing...</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Show login prompt if not authenticated\r\n  if (!user) {\r\n    return (\r\n      <div className=\"flex items-center justify-center min-h-screen\">\r\n        <Card className=\"w-full max-w-md\">\r\n          <CardHeader className=\"text-center\">\r\n            <CardTitle>Welcome to WorkHub</CardTitle>\r\n            <CardDescription>\r\n              Please sign in to access your dashboard\r\n            </CardDescription>\r\n          </CardHeader>\r\n          <CardContent>\r\n            <Button asChild className=\"w-full\">\r\n              <Link href=\"/login\">Sign In</Link>\r\n            </Button>\r\n          </CardContent>\r\n        </Card>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Calculate statistics\r\n  const stats = {\r\n    totalVehicles: vehicles.length,\r\n    activeDelegations: delegations.filter(d => d.status === 'In_Progress')\r\n      .length,\r\n    pendingTasks: tasks.filter(\r\n      t => t.status === 'Assigned' || t.status === 'In_Progress'\r\n    ).length,\r\n    maintenancesDue: serviceRecords.filter(r => {\r\n      // This is a simplified check - you might want more sophisticated logic\r\n      // Simplified maintenance due logic\r\n      return Math.random() > 0.8; // Mock 20% of records as needing maintenance\r\n    }).length,\r\n    teamMembers: employees.length,\r\n  };\r\n\r\n  // Mock recent activity - you'd fetch this from your API\r\n  const recentActivity: RecentActivityItem[] = [\r\n    {\r\n      id: '1',\r\n      title: 'Vehicle VIN-123 maintenance completed',\r\n      description: 'Oil change and tire rotation finished',\r\n      timestamp: '2 hours ago',\r\n      type: 'maintenance',\r\n    },\r\n    {\r\n      id: '2',\r\n      title: 'New task assigned: Fleet inspection',\r\n      description: 'Quarterly safety inspection due next week',\r\n      timestamp: '4 hours ago',\r\n      type: 'task',\r\n      priority: 'high',\r\n    },\r\n    {\r\n      id: '3',\r\n      title: 'Project Alpha milestone completed',\r\n      description: 'Phase 2 deliverables submitted',\r\n      timestamp: '1 day ago',\r\n      type: 'delegation',\r\n    },\r\n  ];\r\n\r\n  const quickActions: QuickActionProps[] = [\r\n    {\r\n      title: 'Add New Vehicle',\r\n      description: 'Register a new asset to your fleet',\r\n      icon: Plus,\r\n      href: '/add-vehicle',\r\n      variant: 'default',\r\n    },\r\n    {\r\n      title: 'Schedule Maintenance',\r\n      description: 'Plan upcoming service appointments',\r\n      icon: Calendar,\r\n      href: '/service-records',\r\n    },\r\n    {\r\n      title: 'View Analytics',\r\n      description: 'See detailed reports and insights',\r\n      icon: BarChart3,\r\n      href: '/reports',\r\n    },\r\n    {\r\n      title: 'Assign Task',\r\n      description: 'Create and delegate new tasks',\r\n      icon: CheckCircle,\r\n      href: '/tasks',\r\n    },\r\n  ];\r\n\r\n  return (\r\n    <div className=\"space-y-8\">\r\n      {/* Welcome Header */}\r\n      <div className=\"space-y-2\">\r\n        <h1 className=\"text-3xl font-bold tracking-tight\">\r\n          Welcome back\r\n          {user?.user_metadata?.full_name\r\n            ? `, ${user.user_metadata.full_name}`\r\n            : ''}\r\n          !\r\n        </h1>\r\n        <p className=\"text-muted-foreground\">\r\n          Here's what's happening with your operations today.\r\n        </p>\r\n      </div>\r\n\r\n      {/* Key Metrics */}\r\n      <div className=\"grid gap-4 md:grid-cols-2 lg:grid-cols-4\">\r\n        <StatCard\r\n          title=\"Total Vehicles\"\r\n          value={stats.totalVehicles}\r\n          description=\"Active fleet assets\"\r\n          icon={Car}\r\n          href=\"/vehicles\"\r\n          trend={{ value: 5.2, isPositive: true }}\r\n        />\r\n        <StatCard\r\n          title=\"Active Projects\"\r\n          value={stats.activeDelegations}\r\n          description=\"In progress delegations\"\r\n          icon={Activity}\r\n          href=\"/delegations\"\r\n        />\r\n        <StatCard\r\n          title=\"Pending Tasks\"\r\n          value={stats.pendingTasks}\r\n          description=\"Awaiting completion\"\r\n          icon={Clock}\r\n          href=\"/tasks\"\r\n          trend={{ value: 2.1, isPositive: false }}\r\n        />\r\n        <StatCard\r\n          title=\"Maintenance Due\"\r\n          value={stats.maintenancesDue}\r\n          description=\"Requires attention\"\r\n          icon={AlertTriangle}\r\n          href=\"/service-history\"\r\n        />\r\n      </div>\r\n\r\n      <div className=\"grid gap-6 lg:grid-cols-3\">\r\n        {/* Recent Activity */}\r\n        <div className=\"lg:col-span-2\">\r\n          <Card>\r\n            <CardHeader className=\"flex flex-row items-center justify-between\">\r\n              <div>\r\n                <CardTitle>Recent Activity</CardTitle>\r\n                <CardDescription>\r\n                  Latest updates from your operations\r\n                </CardDescription>\r\n              </div>\r\n              <Button variant=\"outline\" size=\"sm\" asChild>\r\n                <Link href=\"/activity\">\r\n                  <Eye className=\"size-4 mr-2\" />\r\n                  View All\r\n                </Link>\r\n              </Button>\r\n            </CardHeader>\r\n            <CardContent>\r\n              <div className=\"space-y-4\">\r\n                {recentActivity.map(item => (\r\n                  <div\r\n                    key={item.id}\r\n                    className=\"flex items-start gap-3 p-3 rounded-lg bg-muted/30\"\r\n                  >\r\n                    <div className=\"rounded-full bg-primary/10 p-1\">\r\n                      {item.type === 'maintenance' && (\r\n                        <Wrench className=\"size-3 text-primary\" />\r\n                      )}\r\n                      {item.type === 'task' && (\r\n                        <CheckCircle className=\"size-3 text-primary\" />\r\n                      )}\r\n                      {item.type === 'delegation' && (\r\n                        <Users className=\"size-3 text-primary\" />\r\n                      )}\r\n                      {item.type === 'vehicle' && (\r\n                        <Car className=\"size-3 text-primary\" />\r\n                      )}\r\n                    </div>\r\n                    <div className=\"flex-1 min-w-0\">\r\n                      <div className=\"flex items-center gap-2\">\r\n                        <p className=\"text-sm font-medium\">{item.title}</p>\r\n                        {item.priority && (\r\n                          <Badge\r\n                            variant={\r\n                              item.priority === 'high'\r\n                                ? 'destructive'\r\n                                : 'secondary'\r\n                            }\r\n                            className=\"text-xs\"\r\n                          >\r\n                            {item.priority}\r\n                          </Badge>\r\n                        )}\r\n                      </div>\r\n                      <p className=\"text-xs text-muted-foreground\">\r\n                        {item.description}\r\n                      </p>\r\n                      <p className=\"text-xs text-muted-foreground mt-1\">\r\n                        {item.timestamp}\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n        </div>\r\n\r\n        {/* Quick Actions */}\r\n        <div>\r\n          <Card>\r\n            <CardHeader>\r\n              <CardTitle>Quick Actions</CardTitle>\r\n              <CardDescription>Common tasks and shortcuts</CardDescription>\r\n            </CardHeader>\r\n            <CardContent>\r\n              <div className=\"space-y-3\">\r\n                {quickActions.map((action, index) => (\r\n                  <QuickAction key={index} {...action} />\r\n                ))}\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Performance Overview */}\r\n      <Card>\r\n        <CardHeader>\r\n          <CardTitle>Fleet Performance</CardTitle>\r\n          <CardDescription>\r\n            Overview of fleet efficiency and maintenance status\r\n          </CardDescription>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <div className=\"grid gap-6 md:grid-cols-3\">\r\n            <div className=\"space-y-2\">\r\n              <div className=\"flex items-center justify-between text-sm\">\r\n                <span className=\"text-muted-foreground\">Fleet Utilization</span>\r\n                <span className=\"font-medium\">87%</span>\r\n              </div>\r\n              <Progress value={87} className=\"h-2\" />\r\n            </div>\r\n            <div className=\"space-y-2\">\r\n              <div className=\"flex items-center justify-between text-sm\">\r\n                <span className=\"text-muted-foreground\">\r\n                  Maintenance Up-to-date\r\n                </span>\r\n                <span className=\"font-medium\">92%</span>\r\n              </div>\r\n              <Progress value={92} className=\"h-2\" />\r\n            </div>\r\n            <div className=\"space-y-2\">\r\n              <div className=\"flex items-center justify-between text-sm\">\r\n                <span className=\"text-muted-foreground\">\r\n                  Task Completion Rate\r\n                </span>\r\n                <span className=\"font-medium\">78%</span>\r\n              </div>\r\n              <Progress value={78} className=\"h-2\" />\r\n            </div>\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAID;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAkBA;AACA;AAEA;AACA;AACA;AAOA;AAEA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AAAA;;;AAxCA;;;;;;;;;;;;;;;AAuEA,MAAM,WAAoC,CAAC,EACzC,KAAK,EACL,KAAK,EACL,WAAW,EACX,MAAM,IAAI,EACV,KAAK,EACL,IAAI,EACL;IACC,MAAM,cAAc,OAAO,+JAAA,CAAA,UAAI,GAAG;IAElC,qBACE,6LAAC;QAAY,MAAM,QAAQ;QAAK,WAAW,OAAO,UAAU;kBAC1D,cAAA,6LAAC,mIAAA,CAAA,OAAI;YACH,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,+BACA,QAAQ;sBAGV,cAAA,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAE,WAAU;8CACV;;;;;;8CAEH,6LAAC;oCAAE,WAAU;8CAAsB;;;;;;gCAClC,6BACC,6LAAC;oCAAE,WAAU;8CACV;;;;;;;;;;;;sCAIP,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;;;;;;;;;;;gCAEjB,uBACC,6LAAC;oCACC,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,6BACA,MAAM,UAAU,GAAG,mBAAmB;;wCAGvC,MAAM,UAAU,iBACf,6LAAC,qNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;iEAEtB,6LAAC,yNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;wCAEzB,KAAK,GAAG,CAAC,MAAM,KAAK;wCAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASzC;KAxDM;AA0DN,MAAM,cAA0C,CAAC,EAC/C,KAAK,EACL,WAAW,EACX,MAAM,IAAI,EACV,IAAI,EACJ,UAAU,SAAS,EACpB;IACC,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAU;kBACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;YAAC,WAAU;sBACrB,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,WAAU;;;;;;;;;;;kCAElB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAuB;;;;;;0CACrC,6LAAC;gCAAE,WAAU;0CAAsC;;;;;;0CACnD,6LAAC,qIAAA,CAAA,SAAM;gCAAC,OAAO;gCAAC,SAAS;gCAAS,MAAK;gCAAK,WAAU;0CACpD,cAAA,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAM;8CAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOhC;MAzBM;AA2BC,MAAM,kBAA4B;;IACvC,MAAM,EAAE,IAAI,EAAE,aAAa,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD;IAEtD,0EAA0E;IAC1E,kFAAkF;IAClF,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;gDAAE;YAC1B,OAAO,iBAAiB,CAAC,WAAW,CAAC,CAAC;QACxC;+CAAG;QAAC;QAAe;QAAS,MAAM;KAAG,GAAG,iEAAiE;IAEzG,MAAM,EAAE,MAAM,WAAW,EAAE,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,cAAW,AAAD,EAAE;QAAE,SAAS;IAAY;IACnE,MAAM,EAAE,MAAM,cAAc,EAAE,EAAE,GAAG,CAAA,GAAA,oJAAA,CAAA,iBAAc,AAAD,EAAE;QAAE,SAAS;IAAY;IACzE,MAAM,EAAE,MAAM,QAAQ,EAAE,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,SAAS;IAAY;IAC7D,MAAM,EAAE,MAAM,YAAY,EAAE,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,eAAY,AAAD,EAAE;QAAE,SAAS;IAAY;IACrE,MAAM,EAAE,MAAM,iBAAiB,EAAE,EAAE,GAAG,CAAA,GAAA,uJAAA,CAAA,4BAAyB,AAAD,EAAE;QAC9D,SAAS;IACX;IAEA,0DAA0D;IAC1D,IAAI,CAAC,iBAAiB,SAAS;QAC7B,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAqC;;;;;;;;;;;;;;;;;IAI1D;IAEA,yCAAyC;IACzC,IAAI,CAAC,MAAM;QACT,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6LAAC,mIAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,6LAAC,mIAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,6LAAC,mIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC,qIAAA,CAAA,SAAM;4BAAC,OAAO;4BAAC,WAAU;sCACxB,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;0CAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;IAMhC;IAEA,uBAAuB;IACvB,MAAM,QAAQ;QACZ,eAAe,SAAS,MAAM;QAC9B,mBAAmB,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,eACrD,MAAM;QACT,cAAc,MAAM,MAAM,CACxB,CAAA,IAAK,EAAE,MAAM,KAAK,cAAc,EAAE,MAAM,KAAK,eAC7C,MAAM;QACR,iBAAiB,eAAe,MAAM,CAAC,CAAA;YACrC,uEAAuE;YACvE,mCAAmC;YACnC,OAAO,KAAK,MAAM,KAAK,KAAK,6CAA6C;QAC3E,GAAG,MAAM;QACT,aAAa,UAAU,MAAM;IAC/B;IAEA,wDAAwD;IACxD,MAAM,iBAAuC;QAC3C;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,WAAW;YACX,MAAM;QACR;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,WAAW;YACX,MAAM;YACN,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,WAAW;YACX,MAAM;QACR;KACD;IAED,MAAM,eAAmC;QACvC;YACE,OAAO;YACP,aAAa;YACb,MAAM,qMAAA,CAAA,OAAI;YACV,MAAM;YACN,SAAS;QACX;QACA;YACE,OAAO;YACP,aAAa;YACb,MAAM,6MAAA,CAAA,WAAQ;YACd,MAAM;QACR;QACA;YACE,OAAO;YACP,aAAa;YACb,MAAM,qNAAA,CAAA,YAAS;YACf,MAAM;QACR;QACA;YACE,OAAO;YACP,aAAa;YACb,MAAM,8NAAA,CAAA,cAAW;YACjB,MAAM;QACR;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;;4BAAoC;4BAE/C,MAAM,eAAe,YAClB,CAAC,EAAE,EAAE,KAAK,aAAa,CAAC,SAAS,EAAE,GACnC;4BAAG;;;;;;;kCAGT,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;0BAMvC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,OAAM;wBACN,OAAO,MAAM,aAAa;wBAC1B,aAAY;wBACZ,MAAM,mMAAA,CAAA,MAAG;wBACT,MAAK;wBACL,OAAO;4BAAE,OAAO;4BAAK,YAAY;wBAAK;;;;;;kCAExC,6LAAC;wBACC,OAAM;wBACN,OAAO,MAAM,iBAAiB;wBAC9B,aAAY;wBACZ,MAAM,6MAAA,CAAA,WAAQ;wBACd,MAAK;;;;;;kCAEP,6LAAC;wBACC,OAAM;wBACN,OAAO,MAAM,YAAY;wBACzB,aAAY;wBACZ,MAAM,uMAAA,CAAA,QAAK;wBACX,MAAK;wBACL,OAAO;4BAAE,OAAO;4BAAK,YAAY;wBAAM;;;;;;kCAEzC,6LAAC;wBACC,OAAM;wBACN,OAAO,MAAM,eAAe;wBAC5B,aAAY;wBACZ,MAAM,2NAAA,CAAA,gBAAa;wBACnB,MAAK;;;;;;;;;;;;0BAIT,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,6LAAC;;8DACC,6LAAC,mIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,mIAAA,CAAA,kBAAe;8DAAC;;;;;;;;;;;;sDAInB,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;4CAAK,OAAO;sDACzC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;;kEACT,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;oDAAgB;;;;;;;;;;;;;;;;;;8CAKrC,6LAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC;wCAAI,WAAU;kDACZ,eAAe,GAAG,CAAC,CAAA,qBAClB,6LAAC;gDAEC,WAAU;;kEAEV,6LAAC;wDAAI,WAAU;;4DACZ,KAAK,IAAI,KAAK,+BACb,6LAAC,yMAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;4DAEnB,KAAK,IAAI,KAAK,wBACb,6LAAC,8NAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;4DAExB,KAAK,IAAI,KAAK,8BACb,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;4DAElB,KAAK,IAAI,KAAK,2BACb,6LAAC,mMAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;;;;;;;kEAGnB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAE,WAAU;kFAAuB,KAAK,KAAK;;;;;;oEAC7C,KAAK,QAAQ,kBACZ,6LAAC,oIAAA,CAAA,QAAK;wEACJ,SACE,KAAK,QAAQ,KAAK,SACd,gBACA;wEAEN,WAAU;kFAET,KAAK,QAAQ;;;;;;;;;;;;0EAIpB,6LAAC;gEAAE,WAAU;0EACV,KAAK,WAAW;;;;;;0EAEnB,6LAAC;gEAAE,WAAU;0EACV,KAAK,SAAS;;;;;;;;;;;;;+CArCd,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;kCAgDxB,6LAAC;kCACC,cAAA,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;;sDACT,6LAAC,mIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,6LAAC,mIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAEnB,6LAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC;wCAAI,WAAU;kDACZ,aAAa,GAAG,CAAC,CAAC,QAAQ,sBACzB,6LAAC;gDAAyB,GAAG,MAAM;+CAAjB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS9B,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;;0CACT,6LAAC,mIAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,6LAAC,mIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAwB;;;;;;8DACxC,6LAAC;oDAAK,WAAU;8DAAc;;;;;;;;;;;;sDAEhC,6LAAC,uIAAA,CAAA,WAAQ;4CAAC,OAAO;4CAAI,WAAU;;;;;;;;;;;;8CAEjC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAwB;;;;;;8DAGxC,6LAAC;oDAAK,WAAU;8DAAc;;;;;;;;;;;;sDAEhC,6LAAC,uIAAA,CAAA,WAAQ;4CAAC,OAAO;4CAAI,WAAU;;;;;;;;;;;;8CAEjC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAwB;;;;;;8DAGxC,6LAAC;oDAAK,WAAU;8DAAc;;;;;;;;;;;;sDAEhC,6LAAC,uIAAA,CAAA,WAAQ;4CAAC,OAAO;4CAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO7C;GAzSa;;QAC8B,kIAAA,CAAA,iBAAc;QAQvB,iJAAA,CAAA,cAAW;QACR,oJAAA,CAAA,iBAAc;QACpB,8JAAA,CAAA,WAAQ;QACJ,kJAAA,CAAA,eAAY;QACP,uJAAA,CAAA,4BAAyB;;;MAbpD", "debugId": null}}, {"offset": {"line": 4556, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/app/%5Blocale%5D/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { ModernDashboard } from '@/components/dashboard/ModernDashboard';\r\n\r\n// Set page metadata\r\nif (typeof document !== 'undefined') {\r\n  document.title = 'Dashboard - WorkHub';\r\n}\r\n\r\nexport default function DashboardPage() {\r\n  return <ModernDashboard />;\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIA,oBAAoB;AACpB,IAAI,OAAO,aAAa,aAAa;IACnC,SAAS,KAAK,GAAG;AACnB;AAEe,SAAS;IACtB,qBAAO,6LAAC,qJAAA,CAAA,kBAAe;;;;;AACzB;KAFwB", "debugId": null}}]}