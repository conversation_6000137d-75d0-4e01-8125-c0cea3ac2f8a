import type { PostgrestSingleResponse } from '@supabase/supabase-js';

import { supabaseAdmin } from '../lib/supabase.js';
import HttpError from '../utils/HttpError.js';
import logger from '../utils/logger.js';

// Define interfaces for audit logs
interface AuditLog {
  action: string;
  created_at: string;
  details: string;
  id: string;
  ip_address: null | string;
  user_agent: null | string;
  user_id: string;
  auth_user_id: string;
  auth_user: null | {
    email: string;
  };
}

interface GetAuditLogsResult {
  data: AuditLog[];
  pagination: Pagination;
}

interface Pagination {
  hasNext: boolean;
  hasPrevious: boolean; // Fixed: changed from hasPrev to hasPrevious to match responseWrapper
  limit: number;
  page: number;
  total: number;
  totalPages: number;
}

/**
 * Get audit logs with pagination and filtering
 * @param page Current page number
 * @param limit Number of items per page
 * @param search Search query for action, details, or user email
 * @param action Filter by action type
 * @param userId Filter by user ID
 * @param startDate Filter by start date
 * @param endDate Filter by end date
 * @returns Paginated list of audit logs
 */
export const getAuditLogs = async (
  page = 1,
  limit = 10,
  search = '',
  action = '',
  userId = '',
  startDate = '',
  endDate = '',
): Promise<GetAuditLogsResult> => {
  logger.info(`Fetching audit logs - page: ${String(page)}, limit: ${String(limit)}`);

  // First get audit logs without user data
  let query = supabaseAdmin
    .from('audit_logs')
    .select('*', { count: 'exact' })
    .order('created_at', { ascending: false });

  if (action) {
    query = query.eq('action', action);
  }

  if (userId) {
    query = query.eq('auth_user_id', userId);
  }

  if (startDate) {
    query = query.gte('created_at', startDate);
  }
  if (endDate) {
    query = query.lte('created_at', endDate);
  }

  const offset = (page - 1) * limit;
  query = query.range(offset, offset + limit - 1);

  const { count, data: logs, error } = await query;

  if (error) {
    throw new HttpError(`Failed to fetch audit logs: ${error.message}`, 500);
  }

  // Get user emails for the audit logs
  let enrichedLogs = logs || [];
  if (logs && logs.length > 0) {
    const userIds = [...new Set(logs.map(log => log.auth_user_id).filter(Boolean))];

    if (userIds.length > 0) {
      const { data: users } = await supabaseAdmin
        .from('auth.users')
        .select('id, email')
        .in('id', userIds);

      // Create a map for quick lookup
      const userMap = new Map(users?.map(user => [user.id, user]) || []);

      // Enrich logs with user data
      enrichedLogs = logs.map(log => ({
        ...log,
        auth_user: log.auth_user_id ? userMap.get(log.auth_user_id) : null,
      }));
    }
  }

  // Apply search filter if provided (after enriching with user data)
  if (search) {
    enrichedLogs = enrichedLogs.filter(
      log =>
        log.action.toLowerCase().includes(search.toLowerCase()) ||
        log.details.toLowerCase().includes(search.toLowerCase()) ||
        (log.auth_user?.email && log.auth_user.email.toLowerCase().includes(search.toLowerCase())),
    );
  }

  const totalPages = Math.ceil((count ?? 0) / limit);

  return {
    data: enrichedLogs as AuditLog[],
    pagination: {
      hasNext: page < totalPages,
      hasPrevious: page > 1, // Fixed: changed from hasPrev to hasPrevious
      limit: limit,
      page: page,
      total: count ?? 0,
      totalPages,
    },
  };
};

/**
 * Create audit log entry (from controller)
 * @param userId User ID performing the action
 * @param action Action performed
 * @param details Details of the action
 * @param ipAddress IP address of the user
 * @param userAgent User agent of the user
 * @returns Created audit log entry
 */
export const createAuditLog = async (
  userId: string,
  action: string,
  details: string,
  ipAddress?: string,
  userAgent?: string,
): Promise<AuditLog> => {
  logger.info(`Creating audit log: ${action} for user: ${userId}`);

  const { data: auditLog, error }: PostgrestSingleResponse<AuditLog> = await supabaseAdmin
    .from('audit_logs')
    .insert({
      action,
      created_at: new Date().toISOString(),
      details,
      ip_address: ipAddress,
      user_agent: userAgent,
      user_id: userId,
      auth_user_id: userId, // Use the same userId for auth_user_id
    })
    .select()
    .single();

  if (error) {
    throw new HttpError(`Failed to create audit log: ${error.message}`, 500);
  }

  return auditLog;
};

/**
 * Helper function to create audit log entries (non-throwing)
 * @param userId User ID performing the action
 * @param action Action performed
 * @param details Details of the action
 * @param ipAddress IP address of the user
 * @param userAgent User agent of the user
 */
export const createAuditLogEntry = async (
  userId: string,
  action: string,
  details: string,
  ipAddress?: string,
  userAgent?: string,
): Promise<void> => {
  try {
    await supabaseAdmin.from('audit_logs').insert({
      action,
      created_at: new Date().toISOString(),
      details,
      ip_address: ipAddress,
      user_agent: userAgent,
      user_id: userId,
      auth_user_id: userId, // Use the same userId for auth_user_id
    });
  } catch (error: any) {
    logger.error('Failed to create audit log entry:', error);
    // Don't throw error here to avoid breaking the main operation
  }
};
