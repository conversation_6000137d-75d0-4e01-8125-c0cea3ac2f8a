/**
 * @file Input Validation Service - Single Responsibility Principle (SRP)
 * @module lib/security/InputValidator
 *
 * This class handles ONLY input validation and sanitization following SRP principles.
 * It provides comprehensive validation for different data types and security threats.
 *
 * SECURITY NOTE: This is the single source of truth for input validation across the application.
 */

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  sanitizedValue?: any;
}

export interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  customValidator?: (value: any) => boolean;
  sanitizer?: (value: any) => any;
}

export interface ValidationSchema {
  [key: string]: ValidationRule;
}

/**
 * InputValidator - Single Responsibility: Input Validation and Sanitization Only
 *
 * Handles comprehensive input validation, sanitization, and security checks.
 * Does NOT handle business logic or data transformation.
 */
export class InputValidator {
  /**
   * Common validation patterns
   */
  private static readonly PATTERNS = {
    EMAIL: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
    PHONE: /^\+?[\d\s\-\(\)]{10,}$/,
    URL: /^https?:\/\/[^\s/$.?#].[^\s]*$/,
    ALPHANUMERIC: /^[a-zA-Z0-9]+$/,
    ALPHA: /^[a-zA-Z]+$/,
    NUMERIC: /^\d+$/,
    UUID: /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i,
    SQL_INJECTION:
      /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)|('|('')|;|--|\/\*|\*\/)/i,
    XSS: /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
    HTML_TAGS: /<[^>]*>/g,
  } as const;

  /**
   * Dangerous strings that should be blocked
   */
  private static readonly DANGEROUS_STRINGS = [
    'javascript:',
    'vbscript:',
    'onload=',
    'onerror=',
    'onclick=',
    'onmouseover=',
    'onfocus=',
    'onblur=',
    'onchange=',
    'onsubmit=',
    'data:text/html',
    'eval(',
    'expression(',
    'setTimeout(',
    'setInterval(',
  ];

  /**
   * Validate single value against rules
   * Single responsibility: Single value validation only
   */
  static validateValue(value: any, rules: ValidationRule): ValidationResult {
    const errors: string[] = [];
    let sanitizedValue = value;

    // Apply sanitizer first if provided
    if (rules.sanitizer) {
      sanitizedValue = rules.sanitizer(value);
    }

    // Required check
    if (
      rules.required &&
      (value === null || value === undefined || value === '')
    ) {
      errors.push('This field is required');
      return { isValid: false, errors };
    }

    // Skip other validations if value is empty and not required
    if (
      !rules.required &&
      (value === null || value === undefined || value === '')
    ) {
      return { isValid: true, errors: [], sanitizedValue };
    }

    // String validations
    if (typeof value === 'string') {
      // Length validations
      if (rules.minLength && value.length < rules.minLength) {
        errors.push(`Minimum length is ${rules.minLength} characters`);
      }

      if (rules.maxLength && value.length > rules.maxLength) {
        errors.push(`Maximum length is ${rules.maxLength} characters`);
      }

      // Pattern validation
      if (rules.pattern && !rules.pattern.test(value)) {
        errors.push('Invalid format');
      }

      // Security checks
      if (this.containsDangerousContent(value)) {
        errors.push('Contains potentially dangerous content');
      }
    }

    // Custom validator
    if (rules.customValidator && !rules.customValidator(sanitizedValue)) {
      errors.push('Custom validation failed');
    }

    return {
      isValid: errors.length === 0,
      errors,
      sanitizedValue,
    };
  }

  /**
   * Validate object against schema
   * Single responsibility: Object validation only
   */
  static validateObject(
    data: Record<string, any>,
    schema: ValidationSchema
  ): ValidationResult {
    const errors: string[] = [];
    const sanitizedValue: Record<string, any> = {};

    for (const [field, rules] of Object.entries(schema)) {
      const fieldValue = data[field];
      const fieldResult = this.validateValue(fieldValue, rules);

      if (!fieldResult.isValid) {
        errors.push(...fieldResult.errors.map(error => `${field}: ${error}`));
      } else {
        sanitizedValue[field] = fieldResult.sanitizedValue;
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      sanitizedValue,
    };
  }

  /**
   * Sanitize string for XSS prevention
   * Single responsibility: XSS sanitization only
   */
  static sanitizeForXSS(input: string): string {
    if (typeof input !== 'string') return input;

    return (
      input
        // Remove script tags
        .replace(this.PATTERNS.XSS, '')
        // Remove dangerous protocols
        .replace(/javascript:/gi, '')
        .replace(/vbscript:/gi, '')
        .replace(/data:text\/html/gi, 'data:text/plain')
        // Remove event handlers
        .replace(/on\w+\s*=/gi, '')
        // Encode HTML entities
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#x27;')
        .replace(/\//g, '&#x2F;')
    );
  }

  /**
   * SECURITY FIX: Renamed from sanitizeForSQL to clarify purpose
   *
   * ⚠️  CRITICAL SECURITY WARNING:
   * - This method is for DISPLAY PURPOSES ONLY
   * - This does NOT provide SQL injection protection
   * - Client-side sanitization can ALWAYS be bypassed by attackers
   * - SQL injection protection MUST be handled server-side with parameterized queries
   *
   * Escapes SQL-like characters for safe display in UI
   * Single responsibility: Display formatting only
   */
  static escapeForDisplay(input: string): string {
    if (typeof input !== 'string') return input;

    return (
      input
        // Escape single quotes for display
        .replace(/'/g, "''")
        // Remove potentially confusing SQL-like characters for display
        .replace(/;/g, '')
        .replace(/--/g, '')
        .replace(/\/\*/g, '')
        .replace(/\*\//g, '')
        // Remove SQL keywords that might confuse users in display
        .replace(
          /\b(UNION|SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC)\b/gi,
          ''
        )
    );
  }

  /**
   * Validate email format
   * Single responsibility: Email validation only
   */
  static validateEmail(email: string): ValidationResult {
    return this.validateValue(email, {
      required: true,
      pattern: this.PATTERNS.EMAIL,
      maxLength: 254, // RFC 5321 limit
      sanitizer: (value: string) => value.toLowerCase().trim(),
    });
  }

  /**
   * Validate phone number format
   * Single responsibility: Phone validation only
   */
  static validatePhone(phone: string): ValidationResult {
    return this.validateValue(phone, {
      required: true,
      pattern: this.PATTERNS.PHONE,
      sanitizer: (value: string) => value.replace(/\s/g, ''),
    });
  }

  /**
   * Validate URL format
   * Single responsibility: URL validation only
   */
  static validateURL(url: string): ValidationResult {
    return this.validateValue(url, {
      required: true,
      pattern: this.PATTERNS.URL,
      customValidator: (value: string) => {
        try {
          new URL(value);
          return true;
        } catch {
          return false;
        }
      },
    });
  }

  /**
   * Validate UUID format
   * Single responsibility: UUID validation only
   */
  static validateUUID(uuid: string): ValidationResult {
    return this.validateValue(uuid, {
      required: true,
      pattern: this.PATTERNS.UUID,
    });
  }

  /**
   * Check if string contains dangerous content
   * Single responsibility: Dangerous content detection only
   */
  private static containsDangerousContent(input: string): boolean {
    const lowerInput = input.toLowerCase();

    // Check for dangerous strings
    for (const dangerous of this.DANGEROUS_STRINGS) {
      if (lowerInput.includes(dangerous.toLowerCase())) {
        return true;
      }
    }

    // Check for SQL injection patterns
    if (this.PATTERNS.SQL_INJECTION.test(input)) {
      return true;
    }

    // Check for XSS patterns
    if (this.PATTERNS.XSS.test(input)) {
      return true;
    }

    return false;
  }

  /**
   * Create validation schema for common WorkHub entities
   * Single responsibility: Schema creation only
   */
  static createEmployeeValidationSchema(): ValidationSchema {
    return {
      firstName: {
        required: true,
        minLength: 2,
        maxLength: 50,
        pattern: this.PATTERNS.ALPHA,
        sanitizer: this.sanitizeForXSS,
      },
      lastName: {
        required: true,
        minLength: 2,
        maxLength: 50,
        pattern: this.PATTERNS.ALPHA,
        sanitizer: this.sanitizeForXSS,
      },
      email: {
        required: true,
        pattern: this.PATTERNS.EMAIL,
        maxLength: 254,
        sanitizer: (value: string) =>
          this.sanitizeForXSS(value.toLowerCase().trim()),
      },
      phone: {
        required: false,
        pattern: this.PATTERNS.PHONE,
        sanitizer: (value: string) => value?.replace(/\s/g, '') || '',
      },
      position: {
        required: true,
        minLength: 2,
        maxLength: 100,
        sanitizer: this.sanitizeForXSS,
      },
    };
  }

  /**
   * Create validation schema for vehicle entities
   * Single responsibility: Vehicle schema creation only
   */
  static createVehicleValidationSchema(): ValidationSchema {
    return {
      make: {
        required: true,
        minLength: 2,
        maxLength: 50,
        sanitizer: this.sanitizeForXSS,
      },
      model: {
        required: true,
        minLength: 1,
        maxLength: 50,
        sanitizer: this.sanitizeForXSS,
      },
      year: {
        required: true,
        pattern: /^\d{4}$/,
        customValidator: (value: number) => {
          const year = parseInt(value.toString());
          return year >= 1900 && year <= new Date().getFullYear() + 1;
        },
      },
      licensePlate: {
        required: true,
        minLength: 2,
        maxLength: 20,
        sanitizer: (value: string) =>
          this.sanitizeForXSS(value.toUpperCase().trim()),
      },
      vin: {
        required: false,
        minLength: 17,
        maxLength: 17,
        pattern: /^[A-HJ-NPR-Z0-9]{17}$/i,
        sanitizer: (value: string) => value?.toUpperCase().trim() || '',
      },
    };
  }
}
