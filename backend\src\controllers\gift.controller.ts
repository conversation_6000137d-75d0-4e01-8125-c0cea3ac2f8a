import type { NextFunction, Request, Response } from 'express';

import type { Prisma } from '../generated/prisma/index.js';
import type { GiftCreate, GiftQuery, GiftUpdate } from '../schemas/gift.schema.js';

import * as giftModel from '../models/gift.model.js';
import { getUnifiedWebSocketService } from '../services/UnifiedWebSocketService.js';
import { CRUD_EVENTS } from '../services/WebSocketEventManager.js';
import HttpError from '../utils/HttpError.js';
import logger from '../utils/logger.js';

const CRUD_ROOM = 'entity-updates'; // General room for CRUD updates

/**
 * Create a new gift
 */
export const createGift = async (
  req: Request,
  res: Response,
  next: NextFunction,
): Promise<void> => {
  try {
    const validatedBody = req.body as GiftCreate;

    const giftData: Prisma.GiftCreateInput = {
      dateSent: validatedBody.dateSent ? new Date(validatedBody.dateSent) : null,
      itemDescription: validatedBody.itemDescription,
      notes: validatedBody.notes,
      occasion: validatedBody.occasion,
      recipient: {
        connect: { id: validatedBody.recipientId },
      },
      senderName: validatedBody.senderName,
      updatedAt: new Date(),
    };

    const newGift = await giftModel.createGift(giftData);
    if (newGift) {
      try {
        const unifiedSocketService = getUnifiedWebSocketService();
        unifiedSocketService.emitToRoom(CRUD_ROOM, CRUD_EVENTS.GIFT_CREATED, newGift);
      } catch (wsError) {
        logger.error('Failed to emit WebSocket event for gift creation', { wsError });
      }
      res.status(201).json(newGift);
    } else {
      next(
        new HttpError(
          'Could not create gift, input may be invalid or another error occurred.',
          400,
          'GIFT_CREATION_FAILED',
        ),
      );
      return;
    }
  } catch (error: any) {
    if (error.code === 'P2025') {
      next(new HttpError('Recipient not found.', 404, 'RECIPIENT_NOT_FOUND'));
      return;
    }
    logger.error('Error creating gift:', error);
    next(new HttpError('An error occurred while creating the gift.', 500, 'GIFT_CREATION_ERROR'));
    return;
  }
};

/**
 * Get all gifts with optional filtering
 */
export const getAllGifts = async (
  req: Request,
  res: Response,
  next: NextFunction,
): Promise<void> => {
  try {
    const query = req.query as GiftQuery;
    const gifts = await giftModel.getAllGifts(query);
    res.json(gifts);
  } catch (error: any) {
    logger.error('Error fetching gifts:', error);
    next(new HttpError('An error occurred while fetching gifts.', 500, 'GIFTS_FETCH_ERROR'));
    return;
  }
};

/**
 * Get a single gift by ID
 */
export const getGiftById = async (
  req: Request,
  res: Response,
  next: NextFunction,
): Promise<void> => {
  try {
    const { id } = req.params;
    const gift = await giftModel.getGiftById(id);

    if (!gift) {
      next(new HttpError('Gift not found.', 404, 'GIFT_NOT_FOUND'));
      return;
    }

    res.json(gift);
  } catch (error: any) {
    logger.error('Error fetching gift:', error);
    next(new HttpError('An error occurred while fetching the gift.', 500, 'GIFT_FETCH_ERROR'));
    return;
  }
};

/**
 * Update a gift
 */
export const updateGift = async (
  req: Request,
  res: Response,
  next: NextFunction,
): Promise<void> => {
  try {
    const { id } = req.params;
    const validatedBody = req.body as GiftUpdate;

    // Extract recipientId separately to handle the relationship
    const { recipientId, ...giftData } = validatedBody;

    const updateData: Prisma.GiftUpdateInput = {
      ...giftData,
      updatedAt: new Date(),
    };

    // Handle recipient relationship update if recipientId is provided
    if (recipientId) {
      updateData.recipient = {
        connect: { id: recipientId },
      };
    }

    // Handle date conversion
    if (validatedBody.dateSent) {
      updateData.dateSent = new Date(validatedBody.dateSent);
    }

    const updatedGift = await giftModel.updateGift(id, updateData);

    if (!updatedGift) {
      next(new HttpError('Gift not found.', 404, 'GIFT_NOT_FOUND'));
      return;
    }

    try {
      const unifiedSocketService = getUnifiedWebSocketService();
      unifiedSocketService.emitToRoom(CRUD_ROOM, CRUD_EVENTS.GIFT_UPDATED, updatedGift);
    } catch (wsError) {
      logger.error('Failed to emit WebSocket event for gift update', { wsError });
    }

    res.json(updatedGift);
  } catch (error: any) {
    if (error.code === 'P2025') {
      next(new HttpError('Gift or recipient not found.', 404, 'GIFT_OR_RECIPIENT_NOT_FOUND'));
      return;
    }
    logger.error('Error updating gift:', error);
    next(new HttpError('An error occurred while updating the gift.', 500, 'GIFT_UPDATE_ERROR'));
    return;
  }
};

/**
 * Delete a gift
 */
export const deleteGift = async (
  req: Request,
  res: Response,
  next: NextFunction,
): Promise<void> => {
  try {
    const { id } = req.params;

    const deletedGift = await giftModel.deleteGift(id);

    if (!deletedGift) {
      next(new HttpError('Gift not found.', 404, 'GIFT_NOT_FOUND'));
      return;
    }

    try {
      const unifiedSocketService = getUnifiedWebSocketService();
      unifiedSocketService.emitToRoom(CRUD_ROOM, CRUD_EVENTS.GIFT_DELETED, { id });
    } catch (wsError) {
      logger.error('Failed to emit WebSocket event for gift deletion', { wsError });
    }

    res.status(204).send();
  } catch (error: any) {
    logger.error('Error deleting gift:', error);
    next(new HttpError('An error occurred while deleting the gift.', 500, 'GIFT_DELETE_ERROR'));
    return;
  }
};

/**
 * Get gifts for a specific recipient
 */
export const getGiftsByRecipient = async (
  req: Request,
  res: Response,
  next: NextFunction,
): Promise<void> => {
  try {
    const { recipientId } = req.params;
    const query = req.query as Omit<GiftQuery, 'recipientId'>;

    const gifts = await giftModel.getGiftsByRecipient(recipientId, query);
    res.json(gifts);
  } catch (error: any) {
    logger.error('Error fetching gifts by recipient:', error);
    next(
      new HttpError(
        'An error occurred while fetching gifts for the recipient.',
        500,
        'GIFTS_BY_RECIPIENT_FETCH_ERROR',
      ),
    );
    return;
  }
};
