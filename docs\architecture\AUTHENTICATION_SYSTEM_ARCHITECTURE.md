# 🔐 WorkHub Authentication System Architecture

**Document Version:** 1.0  
**Date:** January 2025  
**Status:** ✅ **ACTIVE** - Production Ready  
**Last Updated:** January 2025

---

## 📋 **Executive Summary**

This document provides comprehensive documentation for the WorkHub
authentication system, detailing its architecture, components, security
features, and implementation patterns. The system is built on Supabase
authentication with JWT tokens, React Context for state management, and a secure
API client architecture.

**Current Status**: ✅ **IMPLEMENTED** - Fully functional authentication
system  
**Security Level**: **Enterprise-grade** with JWT, RLS, and RBAC  
**Integration**: **Complete** - Frontend, Backend, and Database layers

---

## 🏗️ **System Architecture Overview**

### **High-Level Architecture**

```mermaid
graph TB
    subgraph "Frontend Layer"
        A[AuthContext Provider] --> B[Login Components]
        A --> C[Protected Routes]
        A --> D[API Client]
        B --> E[Supabase Auth]
        C --> F[Role-based Access]
        D --> G[JWT Token Management]
    end

    subgraph "Backend Layer"
        H[JWT Middleware] --> I[Role Validation]
        I --> J[RLS Policies]
        H --> K[API Endpoints]
        K --> L[Business Logic]
    end

    subgraph "Database Layer"
        M[Supabase Auth] --> N[User Profiles]
        N --> O[Role Assignments]
        O --> P[RLS Policies]
        P --> Q[Data Access Control]
    end

    E --> M
    G --> H
    F --> I
```

### **Core Principles**

1. **Security-First Design** - JWT tokens with automatic refresh and HMAC
   integrity
2. **Zero-Trust Architecture** - Every request validated with circuit breaker
   protection
3. **Role-Based Access Control** - Granular permissions with state coordination
4. **Stateless Authentication** - JWT-based, no server sessions, with loop
   prevention
5. **Real-time Synchronization** - Cross-tab logout support with monitoring
6. **Defensive Programming** - Comprehensive error handling and recovery
7. **Verification Loop Prevention** - Circuit breaker pattern and state
   coordination
8. **Security Event Monitoring** - Real-time threat detection and response

---

## 🔧 **Core Components**

### **1. AuthContext Provider**

**Location**: `frontend/src/contexts/AuthContext.tsx`

**Purpose**: Centralized authentication state management

**Key Features**:

- Supabase authentication integration
- JWT token management with automatic refresh
- Cross-tab logout synchronization
- Real-time auth state updates
- Secure session management

**Interface**:

```typescript
interface AuthContextType {
	user: User | null;
	session: {access_token?: string; user?: User | null} | null;
	loading: boolean;
	error: string | null;
	isInitialized: boolean;
	signIn: (email: string, password: string) => Promise<AuthResponse>;
	signOut: () => Promise<void>;
	clearError: () => void;
}
```

### **2. Secure API Client Architecture**

**Location**: `frontend/src/lib/api/core/`

**Components**:

- **ApiClient** - Core HTTP client with JWT integration
- **SecureApiClient** - Enhanced security features
- **Service Factory** - Centralized service creation
- **Authentication Middleware** - Request/response interceptors

**Security Features**:

- Automatic JWT token injection
- Token refresh on expiry
- CSRF protection (when needed)
- Input sanitization
- Auto-logout on 401 errors

### **3. Protected Route System**

**Location**: `frontend/src/components/auth/ProtectedRoute.tsx`

**Features**:

- Role-based access control
- Automatic redirect to login
- Loading states during auth check
- Graceful error handling
- Modern split-screen login UI

---

## 🔑 **Authentication Flow**

### **Login Process**

```mermaid
sequenceDiagram
    participant U as User
    participant L as LoginForm
    participant A as AuthContext
    participant S as Supabase
    participant API as API Client

    U->>L: Enter credentials
    L->>A: signIn(email, password)
    A->>S: supabase.auth.signInWithPassword()
    S-->>A: { user, session, error }
    A->>A: setUser(user), setSession(session)
    A->>API: setGlobalAuthTokenProvider()
    A-->>L: Success/Error response
    L->>U: Redirect to dashboard or show error
```

### **API Request Flow**

```mermaid
sequenceDiagram
    participant C as Component
    participant API as ApiClient
    participant M as JWT Middleware
    participant B as Backend
    participant DB as Database

    C->>API: makeRequest()
    API->>API: getAuthToken()
    API->>API: Add Authorization header
    API->>M: HTTP Request with JWT
    M->>M: Validate JWT token
    M->>M: Extract user role
    M->>B: Authorized request
    B->>DB: Query with RLS context
    DB-->>B: Filtered data
    B-->>API: Response
    API-->>C: Data
```

### **Token Refresh Flow**

```mermaid
sequenceDiagram
    participant API as ApiClient
    participant A as AuthContext
    participant S as Supabase
    participant T as TokenService

    API->>API: Check token expiry
    API->>A: Token near expiry
    A->>T: refreshToken()
    T->>S: supabase.auth.refreshSession()
    S-->>T: New session
    T->>A: Update session
    A->>API: New token available
    API->>API: Retry original request
```

---

## 🛡️ **Security Features**

### **JWT Token Security**

**Token Structure**:

```json
{
	"sub": "user-uuid",
	"email": "<EMAIL>",
	"role": "ADMIN",
	"iat": 1640995200,
	"exp": 1640998800,
	"aud": "authenticated",
	"iss": "supabase"
}
```

**Security Measures**:

- **Short-lived access tokens** (1 hour)
- **Secure refresh tokens** (30 days)
- **Automatic token rotation**
- **HttpOnly cookie storage** (when applicable)
- **CSRF protection** for state-changing operations

### **Role-Based Access Control (RBAC)**

**Role Hierarchy**:

```typescript
enum UserRole {
	SUPER_ADMIN = 'SUPER_ADMIN', // Full system access
	ADMIN = 'ADMIN', // Administrative access
	MANAGER = 'MANAGER', // Management operations
	EMPLOYEE = 'EMPLOYEE', // Basic user access
	VIEWER = 'VIEWER', // Read-only access
}
```

**Permission Matrix**: | Resource | SUPER_ADMIN | ADMIN | MANAGER | EMPLOYEE |
VIEWER | |----------|-------------|-------|---------|----------|--------| |
Users | CRUD | CRUD | R | R | R | | Vehicles | CRUD | CRUD | CRUD | RU | R | |
Tasks | CRUD | CRUD | CRUD | CRUD | R | | Reports | CRUD | CRUD | CR | R | R |

### **Row-Level Security (RLS)**

**Database Policies**:

```sql
-- Example: Users can only see their own delegations
CREATE POLICY "Users can view own delegations" ON delegations
  FOR SELECT USING (auth.uid() = employee_id);

-- Example: Managers can see team delegations
CREATE POLICY "Managers can view team delegations" ON delegations
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM user_roles
      WHERE user_id = auth.uid()
      AND role IN ('MANAGER', 'ADMIN', 'SUPER_ADMIN')
    )
  );
```

---

## 🔄 **State Management**

### **Authentication State**

**Global State Structure**:

```typescript
interface AuthState {
	user: User | null;
	session: Session | null;
	loading: boolean;
	error: string | null;
	isInitialized: boolean;
}
```

**State Transitions**:

- **LOADING** → **AUTHENTICATED** (successful login)
- **LOADING** → **UNAUTHENTICATED** (no session)
- **AUTHENTICATED** → **UNAUTHENTICATED** (logout/token expiry)
- **ERROR** → **LOADING** (retry authentication)

### **Cross-Tab Synchronization**

**Implementation**:

```typescript
// Listen for logout events across tabs
window.addEventListener('storage', (event) => {
	if (event.key === 'workhub-logout-event') {
		// Synchronize logout across all tabs
		handleCrossTabLogout();
	}
});
```

**Benefits**:

- Consistent auth state across browser tabs
- Automatic logout propagation
- Enhanced security for shared computers
- Improved user experience

---

## 🛡️ **Verification Loop Prevention System**

### **Circuit Breaker Pattern Implementation**

**Purpose**: Prevent infinite authentication verification loops that can occur
during token refresh failures or session validation errors.

**Architecture**:

```mermaid
graph TB
    subgraph "Circuit Breaker States"
        A[CLOSED - Normal Operation] --> B[OPEN - Loop Detected]
        B --> C[HALF-OPEN - Testing Recovery]
        C --> A
        C --> B
    end

    subgraph "Security Operations"
        D[Token Validation] --> E[Circuit Check]
        F[Session Verification] --> E
        G[Auth State Update] --> E
        E --> H{Circuit Open?}
        H -->|Yes| I[Block Operation]
        H -->|No| J[Allow Operation]
    end
```

**Key Components**:

1. **SecurityUtils Circuit Breaker**

   - Tracks verification attempts across all security operations
   - Implements exponential backoff for failed attempts
   - Provides state coordination between concurrent operations

2. **Session State Coordination**

   - Prevents multiple simultaneous session validations
   - Implements integrity checks with automatic recovery
   - Coordinates cross-tab authentication state

3. **Real-time Security Monitoring**
   - Detects verification loop patterns
   - Reports security events for analysis
   - Provides threat level assessment

### **Implementation Details**

**Circuit Breaker Configuration**:

```typescript
interface CircuitBreakerConfig {
	maxAttempts: 5; // Maximum attempts before opening circuit
	resetTimeout: 300000; // 5 minutes before allowing retry
	operationCooldown: 1000; // 1 second between operations
	monitoringInterval: 30000; // 30 seconds monitoring frequency
}
```

**Security Operation Flow**:

```typescript
// Example: Token validation with circuit breaker
async function validateToken(token: string): Promise<boolean> {
	// 1. Check circuit breaker state
	if (!SecurityUtils.canPerformSecurityCheck()) {
		console.debug('🔒 Token validation blocked by circuit breaker');
		return false;
	}

	// 2. Start coordinated operation
	const operationId = 'token-validation';
	if (!SecurityUtils.startSecurityOperation(operationId)) {
		console.debug('🔄 Token validation already in progress');
		return false;
	}

	try {
		// 3. Perform validation
		const isValid = await performTokenValidation(token);

		if (isValid) {
			SecurityUtils.recordSecuritySuccess();
		} else {
			SecurityUtils.recordSecurityAttempt();
		}

		return isValid;
	} finally {
		// 4. Clean up operation
		SecurityUtils.endSecurityOperation(operationId);
	}
}
```

**Security Event Types**:

- `verification_loop` - Potential infinite loop detected
- `circuit_breaker_triggered` - Circuit breaker activated
- `session_integrity_failure` - Session state corruption
- `concurrent_security_operation` - Multiple operations detected

---

## 📁 **File Structure**

````
frontend/src/
├── contexts/
│   └── AuthContext.tsx              # Main auth provider
├── components/auth/
│   ├── LoginForm.tsx               # Modern login UI
│   ├── ProtectedRoute.tsx          # Route protection
│   └── index.ts                    # Auth exports
├── lib/api/
│   ├── core/
│   │   ├── apiClient.ts           # Core HTTP client
│   │   └── baseApiService.ts      # Service base class
│   ├── security/
│   │   ├── secureApiClient.ts     # Enhanced security
│   │   └── hooks/                 # Security hooks
│   └── services/
│       ├── factory.ts             # Service factory
│       └── domain/                # Domain services
├── hooks/
│   └── security/
│       └── useSecureApi.ts        # Legacy compatibility
└── types/
    ├── auth.ts                    # Auth type definitions
    └── domain.ts                  # Domain types

backend/src/
├── middleware/
│   ├── jwtAuth.middleware.ts      # JWT validation
│   └── roleAuth.middleware.ts     # Role checking
├── routes/
│   └── auth.routes.ts             # Auth endpoints
└── services/
    └── auth.service.ts            # Auth business logic

---

## 🚀 **Implementation Examples**

### **Using the Authentication System**

**1. Basic Authentication Check**:
```typescript
import { useAuthContext } from '@/contexts/AuthContext';

function MyComponent() {
  const { user, loading, isInitialized } = useAuthContext();

  if (!isInitialized || loading) {
    return <LoadingSpinner />;
  }

  if (!user) {
    return <LoginRequired />;
  }

  return <AuthenticatedContent user={user} />;
}
````

**2. Role-Based Component Rendering**:

```typescript
import {useAuthContext} from '@/contexts/AuthContext';

function AdminPanel() {
	const {user} = useAuthContext();

	const isAdmin = user?.user_metadata?.role === 'ADMIN';

	if (!isAdmin) {
		return <AccessDenied />;
	}

	return <AdminDashboard />;
}
```

**3. Secure API Calls**:

```typescript
import {reliabilityApiService} from '@/lib/api/services';

async function fetchDashboardData() {
	try {
		// Automatically includes JWT token
		const data = await reliabilityApiService.getReliabilityDashboardData();
		return data;
	} catch (error) {
		// Auto-logout on 401 errors
		console.error('Failed to fetch data:', error);
		throw error;
	}
}
```

**4. Protected Route Usage**:

```typescript
import {ProtectedRoute} from '@/components/auth';

function App() {
	return (
		<Routes>
			<Route path='/login' element={<LoginPage />} />
			<Route
				path='/dashboard'
				element={
					<ProtectedRoute allowedRoles={['ADMIN', 'MANAGER']}>
						<Dashboard />
					</ProtectedRoute>
				}
			/>
		</Routes>
	);
}
```

---

## 🔧 **Configuration**

### **Environment Variables**

**Frontend (.env.local)**:

```bash
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key

# API Configuration
NEXT_PUBLIC_API_BASE_URL=http://localhost:3001/api
NEXT_PUBLIC_FRONTEND_URL=http://localhost:9002

# Security Configuration
NEXT_PUBLIC_JWT_EXPIRY_THRESHOLD=300  # 5 minutes
```

**Backend (.env)**:

```bash
# Supabase Configuration
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# JWT Configuration
JWT_SECRET=your-jwt-secret
JWT_EXPIRY=1h
REFRESH_TOKEN_EXPIRY=30d

# Security Configuration
CORS_ORIGINS=http://localhost:9002,http://localhost:3000
```

### **Supabase Configuration**

**Auth Settings**:

```sql
-- Enable email authentication
UPDATE auth.config SET
  enable_signup = true,
  enable_email_confirmations = false,
  jwt_expiry = 3600;  -- 1 hour

-- Custom claims for roles
CREATE OR REPLACE FUNCTION auth.custom_claims(user_id uuid)
RETURNS jsonb
LANGUAGE sql
STABLE
AS $$
  SELECT jsonb_build_object(
    'role', COALESCE(up.role, 'EMPLOYEE'),
    'employee_id', up.employee_id
  )
  FROM user_profiles up
  WHERE up.user_id = $1;
$$;
```

---

## 🧪 **Testing Strategy**

### **Unit Tests**

**AuthContext Tests**:

```typescript
describe('AuthContext', () => {
	it('should handle successful login', async () => {
		const {result} = renderHook(() => useAuthContext());

		await act(async () => {
			await result.current.signIn('<EMAIL>', 'password');
		});

		expect(result.current.user).toBeTruthy();
		expect(result.current.error).toBeNull();
	});

	it('should handle login errors', async () => {
		const {result} = renderHook(() => useAuthContext());

		await act(async () => {
			await result.current.signIn('<EMAIL>', 'wrong');
		});

		expect(result.current.user).toBeNull();
		expect(result.current.error).toBeTruthy();
	});
});
```

**API Client Tests**:

```typescript
describe('ApiClient', () => {
	it('should include JWT token in requests', async () => {
		const mockToken = 'mock-jwt-token';
		setGlobalAuthTokenProvider(() => mockToken);

		const client = new ApiClient({baseURL: '/api'});
		const spy = jest.spyOn(global, 'fetch');

		await client.get('/test');

		expect(spy).toHaveBeenCalledWith(
			expect.any(String),
			expect.objectContaining({
				headers: expect.objectContaining({
					Authorization: `Bearer ${mockToken}`,
				}),
			})
		);
	});
});
```

### **Integration Tests**

**End-to-End Authentication Flow**:

```typescript
describe('Authentication Flow', () => {
	it('should complete full login flow', async () => {
		// Navigate to login page
		await page.goto('/auth-test');

		// Fill login form
		await page.fill('[data-testid="email-input"]', '<EMAIL>');
		await page.fill('[data-testid="password-input"]', 'password123');

		// Submit form
		await page.click('[data-testid="login-button"]');

		// Verify redirect to dashboard
		await expect(page).toHaveURL('/dashboard');

		// Verify user is authenticated
		const userInfo = await page.textContent('[data-testid="user-info"]');
		expect(userInfo).toContain('<EMAIL>');
	});
});
```

---

## 🚨 **Security Considerations**

### **Common Vulnerabilities & Mitigations**

**1. JWT Token Exposure**:

- ✅ **Mitigation**: Short-lived tokens (1 hour)
- ✅ **Mitigation**: Secure storage practices
- ✅ **Mitigation**: Automatic token rotation

**2. Cross-Site Scripting (XSS)**:

- ✅ **Mitigation**: Input sanitization
- ✅ **Mitigation**: Content Security Policy
- ✅ **Mitigation**: HttpOnly cookies for sensitive data

**3. Cross-Site Request Forgery (CSRF)**:

- ✅ **Mitigation**: JWT-based authentication (stateless)
- ✅ **Mitigation**: SameSite cookie attributes
- ✅ **Mitigation**: Origin validation

**4. Session Fixation**:

- ✅ **Mitigation**: Token regeneration on login
- ✅ **Mitigation**: Secure session management
- ✅ **Mitigation**: Cross-tab logout synchronization

**5. Verification Loop Attacks**:

- ✅ **Mitigation**: Circuit breaker pattern implementation
- ✅ **Mitigation**: Security operation coordination
- ✅ **Mitigation**: Real-time loop detection and prevention
- ✅ **Mitigation**: Automatic state recovery mechanisms

**6. Cookie Tampering**:

- ✅ **Mitigation**: HMAC-SHA256 signature verification
- ✅ **Mitigation**: Timestamp-based expiration validation
- ✅ **Mitigation**: Secure cookie configuration

### **Security Best Practices**

1. **Never store sensitive data in localStorage**
2. **Always validate tokens on the backend**
3. **Implement proper CORS policies**
4. **Use HTTPS in production**
5. **Regular security audits and updates**
6. **Monitor for suspicious authentication patterns**
7. **Implement circuit breaker patterns for security operations**
8. **Use HMAC signatures for cookie integrity**
9. **Coordinate concurrent security operations**
10. **Monitor and respond to security events in real-time**

---

## 📊 **Performance Considerations**

### **Optimization Strategies**

**1. Token Caching**:

- Cache valid tokens to reduce API calls
- Implement intelligent refresh logic
- Use memory-based caching for short sessions

**2. Lazy Loading**:

- Load authentication components on demand
- Defer non-critical auth checks
- Optimize bundle size with code splitting

**3. Request Batching**:

- Batch multiple API calls when possible
- Implement request deduplication
- Use React Query for intelligent caching

### **Performance Metrics**

- **Login Time**: < 2 seconds
- **Token Refresh**: < 500ms
- **Auth Check**: < 100ms
- **Bundle Size**: < 50KB (auth components)

---

## 🔗 **Related Documentation**

- [`../security/SECURITY_ENHANCEMENT_PLAN_V3.md`](../security/SECURITY_ENHANCEMENT_PLAN_V3.md) -
  Security implementation details
- [`../api/EMERGENCY_API_SECURITY_SUMMARY.md`](../api/EMERGENCY_API_SECURITY_SUMMARY.md) -
  API security guidelines
- [`../../frontend/HANDOFF_SECURE_API_ARCHITECTURE.md`](../../frontend/HANDOFF_SECURE_API_ARCHITECTURE.md) -
  API architecture details
- [`../current/development/GIT_WORKFLOW_STRATEGY.md`](../current/development/GIT_WORKFLOW_STRATEGY.md) -
  Development workflow

---

## 📞 **Support and Troubleshooting**

### **Common Issues**

**1. "Invalid or expired token" errors**:

- Check token expiry settings
- Verify JWT secret configuration
- Ensure proper token refresh logic

**2. Cross-tab logout not working**:

- Verify localStorage event listeners
- Check browser compatibility
- Ensure proper event key usage

**3. Role-based access not working**:

- Verify user role assignment in database
- Check RLS policies
- Validate JWT custom claims

### **Debug Tools**

```typescript
// Enable auth debugging
localStorage.setItem('auth-debug', 'true');

// Check current auth state
console.log('Auth State:', useAuthContext());

// Verify JWT token
console.log('JWT Token:', getAuthToken());
```

---

**Document Maintainer:** Development Team **Review Schedule:** Quarterly **Next
Review:** April 2025

```

```
