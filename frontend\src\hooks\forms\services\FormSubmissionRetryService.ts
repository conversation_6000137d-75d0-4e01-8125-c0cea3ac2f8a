/**
 * @file Form Submission Retry Service
 * @description Handles retry logic for form submissions following SRP
 * @version 2.0.0
 * <AUTHOR> Development Team
 */

import type { RetryConfig } from '../types/FormSubmissionTypes';

/**
 * Form submission retry service following SRP
 * Responsible only for handling retry logic and timing
 */
export class FormSubmissionRetryService {
  private config: RetryConfig;
  private currentAttempt: number = 0;

  constructor(config: RetryConfig) {
    this.config = config;
  }

  /**
   * Check if retry should be attempted
   */
  shouldRetry(error: Error): boolean {
    return (
      this.currentAttempt < this.config.maxAttempts &&
      (this.config.retryCondition ? this.config.retryCondition(error) : true)
    );
  }

  /**
   * Get the delay for the next retry attempt
   */
  getRetryDelay(): number {
    const baseDelay = this.config.delay;

    if (this.config.exponentialBackoff) {
      return baseDelay * Math.pow(2, this.currentAttempt);
    }

    return baseDelay;
  }

  /**
   * Increment the retry attempt counter
   */
  incrementAttempt(): number {
    this.currentAttempt += 1;
    return this.currentAttempt;
  }

  /**
   * Reset the retry attempt counter
   */
  resetAttempts(): void {
    this.currentAttempt = 0;
  }

  /**
   * Get current attempt number
   */
  getCurrentAttempt(): number {
    return this.currentAttempt;
  }

  /**
   * Get maximum attempts
   */
  getMaxAttempts(): number {
    return this.config.maxAttempts;
  }

  /**
   * Sleep utility for retry delays
   */
  async sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Execute retry with proper delay
   */
  async executeRetry<T>(retryFn: () => Promise<T>): Promise<T> {
    if (!this.shouldRetry(new Error('Manual retry'))) {
      throw new Error('Maximum retry attempts exceeded');
    }

    const delay = this.getRetryDelay();
    this.incrementAttempt();

    await this.sleep(delay);
    return retryFn();
  }

  /**
   * Get retry status information
   */
  getRetryStatus(): {
    currentAttempt: number;
    maxAttempts: number;
    hasRetriesLeft: boolean;
    nextDelay: number;
  } {
    return {
      currentAttempt: this.currentAttempt,
      maxAttempts: this.config.maxAttempts,
      hasRetriesLeft: this.currentAttempt < this.config.maxAttempts,
      nextDelay: this.getRetryDelay(),
    };
  }

  /**
   * Create a new retry service with updated configuration
   */
  withConfig(newConfig: Partial<RetryConfig>): FormSubmissionRetryService {
    return new FormSubmissionRetryService({
      ...this.config,
      ...newConfig,
    });
  }
}
