-- Migration: Prepare for user_profiles Table Removal
-- This migration removes foreign key constraints and prepares for user_profiles table removal

-- Step 1: Update audit_logs to reference auth.users instead of user_profiles
-- First, add a new column to reference auth.users directly
ALTER TABLE public.audit_logs ADD COLUMN IF NOT EXISTS auth_user_id UUID;

-- Populate the new column with existing user_id values (they should be the same)
UPDATE public.audit_logs 
SET auth_user_id = user_id::UUID 
WHERE auth_user_id IS NULL;

-- Add foreign key constraint to auth.users
ALTER TABLE public.audit_logs 
ADD CONSTRAINT audit_logs_auth_user_id_fkey 
FOREIGN KEY (auth_user_id) REFERENCES auth.users(id) ON DELETE CASCADE;

-- Step 2: Remove foreign key constraints from user_profiles table
-- Remove the constraint linking user_profiles.employee_id to Employee.id
ALTER TABLE public.user_profiles 
DROP CONSTRAINT IF EXISTS user_profiles_employee_id_fkey;

-- Remove the constraint linking user_profiles.id to auth.users.id
ALTER TABLE public.user_profiles 
DROP CONSTRAINT IF EXISTS user_profiles_id_fkey;

-- Step 3: Remove foreign key constraint from audit_logs to user_profiles
ALTER TABLE public.audit_logs 
DROP CONSTRAINT IF EXISTS fk_audit_logs_user_id;

-- Step 4: Drop RLS policies on user_profiles (they will be recreated for auth.users if needed)
DROP POLICY IF EXISTS "user_profiles_own_access" ON public.user_profiles;
DROP POLICY IF EXISTS "user_profiles_admin_view" ON public.user_profiles;
DROP POLICY IF EXISTS "user_profiles_admin_manage" ON public.user_profiles;
DROP POLICY IF EXISTS "user_profiles_manager_view" ON public.user_profiles;

-- Step 5: Create helper function to get employee_id from auth.users.user_metadata
CREATE OR REPLACE FUNCTION public.get_employee_id_from_auth(user_id UUID)
RETURNS INTEGER
LANGUAGE plpgsql
STABLE
SECURITY DEFINER
AS $$
DECLARE
    employee_id INTEGER;
    user_metadata JSONB;
BEGIN
    SELECT user_metadata INTO user_metadata
    FROM auth.users
    WHERE id = user_id;
    
    employee_id := (user_metadata->>'employee_id')::INTEGER;
    
    RETURN employee_id;
END;
$$;

-- Grant permissions for the helper function
GRANT EXECUTE ON FUNCTION public.get_employee_id_from_auth(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_employee_id_from_auth(UUID) TO service_role;

-- Step 6: Create view for backward compatibility during transition
CREATE OR REPLACE VIEW public.user_profiles_view AS
SELECT 
    u.id,
    COALESCE((u.user_metadata->>'role')::TEXT, 'USER') as role,
    COALESCE((u.user_metadata->>'is_active')::BOOLEAN, true) as is_active,
    (u.user_metadata->>'employee_id')::INTEGER as employee_id,
    u.created_at,
    u.updated_at
FROM auth.users u;

-- Grant permissions on the view
GRANT SELECT ON public.user_profiles_view TO authenticated;
GRANT SELECT ON public.user_profiles_view TO service_role;

-- Step 7: Update audit_logs to use the new auth_user_id column
-- Make auth_user_id NOT NULL after populating it
ALTER TABLE public.audit_logs ALTER COLUMN auth_user_id SET NOT NULL;

-- Add index for performance
CREATE INDEX IF NOT EXISTS idx_audit_logs_auth_user_id ON public.audit_logs(auth_user_id);

-- Step 8: Create function to migrate employee_id to auth.users.user_metadata
CREATE OR REPLACE FUNCTION public.migrate_employee_id_to_metadata()
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    profile_record RECORD;
    updated_count INTEGER := 0;
    current_metadata JSONB;
BEGIN
    -- Loop through all user_profiles with employee_id
    FOR profile_record IN 
        SELECT id, employee_id, role, is_active 
        FROM public.user_profiles 
        WHERE employee_id IS NOT NULL
    LOOP
        -- Get current user_metadata
        SELECT user_metadata INTO current_metadata
        FROM auth.users
        WHERE id = profile_record.id;
        
        -- Initialize metadata if null
        IF current_metadata IS NULL THEN
            current_metadata := '{}'::JSONB;
        END IF;
        
        -- Update user_metadata with employee_id, role, and is_active
        current_metadata := jsonb_set(current_metadata, '{employee_id}', to_jsonb(profile_record.employee_id));
        current_metadata := jsonb_set(current_metadata, '{role}', to_jsonb(profile_record.role));
        current_metadata := jsonb_set(current_metadata, '{is_active}', to_jsonb(profile_record.is_active));
        
        -- Update auth.users
        UPDATE auth.users 
        SET user_metadata = current_metadata
        WHERE id = profile_record.id;
        
        updated_count := updated_count + 1;
    END LOOP;
    
    RETURN updated_count;
END;
$$;

-- Grant permissions for the migration function
GRANT EXECUTE ON FUNCTION public.migrate_employee_id_to_metadata() TO service_role;

-- Comment: This migration prepares the database for user_profiles table removal
-- by removing foreign key constraints and creating compatibility structures
