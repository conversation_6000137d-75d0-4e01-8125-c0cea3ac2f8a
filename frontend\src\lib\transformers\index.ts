/**
 * @file Transformers Index
 * @description Centralized exports for all data transformers following DRY principles
 */

// Delegation transformers
export { DelegationTransformer } from './delegationTransformer';

export {
  DelegationEnrichmentTransformer,
  enrichDelegation,
} from './delegationEnrichment';

// Employee transformers
export { EmployeeTransformer } from './employeeTransformer';

// Task transformers
export { TaskTransformer } from './taskTransformer';

export { TaskEnrichmentTransformer, enrichTask } from './taskEnrichment';

// Vehicle transformers
export { VehicleTransformer } from './vehicleTransformer';

// Reliability transformers
export { ReliabilityTransformer } from './reliabilityTransformer';

// Transformer utilities for consistent data transformation patterns
export const TransformerUtils = {
  // Common transformation patterns
  toApiFormat: <T>(data: T): T => {
    // Convert frontend format to API format
    return data;
  },

  fromApiFormat: <T>(data: T): T => {
    // Convert API format to frontend format
    return data;
  },

  // Date transformation helpers
  formatDateForApi: (date: string | Date): string => {
    return new Date(date).toISOString();
  },

  formatDateFromApi: (dateString: string): Date => {
    return new Date(dateString);
  },

  // ID transformation helpers
  ensureStringId: (id: string | number): string => {
    return String(id);
  },

  ensureNumberId: (id: string | number): number => {
    return typeof id === 'string' ? parseInt(id, 10) : id;
  },

  // Array transformation helpers
  ensureArray: <T>(value: T | T[]): T[] => {
    return Array.isArray(value) ? value : [value];
  },

  filterNullish: <T>(array: (T | null | undefined)[]): T[] => {
    return array.filter((item): item is T => item != null);
  },

  // Object transformation helpers
  removeNullish: <T extends Record<string, any>>(obj: T): Partial<T> => {
    const result: Partial<T> = {};
    for (const [key, value] of Object.entries(obj)) {
      if (value != null) {
        result[key as keyof T] = value;
      }
    }
    return result;
  },

  // Status transformation helpers
  normalizeStatus: (status: string): string => {
    return status.toLowerCase().replace(/\s+/g, '_');
  },

  denormalizeStatus: (status: string): string => {
    return status.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  },
} as const;

// Base transformer interface for consistency
export interface BaseTransformer<TInput, TOutput> {
  fromApi(data: TInput): TOutput;
  toApi(data: TOutput): TInput;
}

// Base enrichment interface for consistency
export interface BaseEnrichment<TInput, TOutput> {
  enrich(data: TInput, options?: any): Promise<TOutput>;
}
