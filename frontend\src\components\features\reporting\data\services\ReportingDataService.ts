// frontend/src/components/features/reporting/data/services/ReportingDataService.ts

import { apiClient } from '@/lib/api';

// PHASE 1 ENHANCEMENT: Import new entity analytics types
import type {
  CrossEntityAnalytics,
  DelegationAnalytics,
  EmployeeAnalytics,
  IReportingDataService,
  LocationMetrics,
  PaginatedDelegationsResponse,
  ReportingFilters,
  ServiceCostSummary,
  ServiceHistoryData,
  // PHASE 1: New entity analytics types
  TaskAnalytics,
  TaskMetrics,
  TrendData,
  VehicleAnalytics,
} from '../types/reporting';

import {
  transformDelegationAnalytics,
  transformLocationMetrics,
  transformTaskMetrics,
  transformTrendData,
} from '../transformers/reportingTransformers';
import { TaskAnalyticsData } from '../types/reporting';

// API Response interface for consistent typing
interface ApiResponse<T = any> {
  data?: T;
  status?: string;
  timestamp?: string;
}

/**
 * ReportingDataService - Single Responsibility: Data fetching and transformation for reporting
 *
 * This service abstracts all API calls and basic data transformation for the reporting module.
 * It does NOT manage component state or server cache - that's handled by React Query hooks.
 *
 * Follows SRP by having one clear purpose: data access layer for reporting.
 */
export class ReportingDataService implements IReportingDataService {
  private readonly baseUrl: string;

  constructor(baseUrl = '/api/reporting') {
    this.baseUrl = baseUrl;
  }

  /**
   * PHASE 1 ENHANCEMENT: Get cross-entity analytics data
   * @param filters - Reporting filters to apply
   * @returns Promise resolving to cross-entity analytics data
   */
  async getCrossEntityAnalytics(
    filters: ReportingFilters
  ): Promise<CrossEntityAnalytics> {
    try {
      const queryParamsString = this.buildQueryParams(filters);
      const queryParams = new URLSearchParams(queryParamsString);

      // Add cross-entity parameters
      if (filters.includeCrossEntityCorrelations) {
        queryParams.append('includeCrossEntityCorrelations', 'true');
      }

      const apiResponse = (await apiClient.get(
        `/reporting/cross-entity/analytics?${queryParams.toString()}`
      )) as any;
      return apiResponse.data || apiResponse;
    } catch (error) {
      console.error('Error fetching cross-entity analytics:', error);
      throw new Error(
        `Failed to load cross-entity analytics: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * Fetches delegation analytics data based on provided filters
   * ENHANCED: Now supports service history and task data integration
   * @param filters - Reporting filters to apply
   * @returns Promise resolving to delegation analytics data
   */
  async getDelegationAnalytics(
    filters: ReportingFilters
  ): Promise<DelegationAnalytics> {
    try {
      const queryParamsString = this.buildQueryParams(filters);
      const queryParams = new URLSearchParams(queryParamsString);

      // ENHANCED: Add service history and task data parameters
      if (filters.includeServiceHistory) {
        queryParams.append('includeServiceHistory', 'true');
      }
      if (filters.includeTaskData) {
        queryParams.append('includeTaskData', 'true');
      }

      const apiResponse = (await apiClient.get(
        `/reporting/delegations/analytics?${queryParams.toString()}`
      )) as any;
      // Backend returns { status: 'success', data: {...}, timestamp: '...' }
      return transformDelegationAnalytics(apiResponse.data || apiResponse);
    } catch (error) {
      console.error('Error fetching delegation analytics:', error);
      throw new Error(
        `Failed to load delegation analytics: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * Fetches paginated delegations data based on provided filters
   * @param filters - Reporting filters to apply
   * @param pagination - Pagination parameters
   * @returns Promise resolving to paginated delegations response
   */
  async getDelegations(
    filters: ReportingFilters,
    pagination: { page: number; pageSize: number }
  ): Promise<PaginatedDelegationsResponse> {
    try {
      const queryParams = this.buildQueryParams(filters);
      const paginationParams = new URLSearchParams(queryParams);

      // Add pagination parameters
      paginationParams.append('page', pagination.page.toString());
      paginationParams.append('pageSize', pagination.pageSize.toString());

      const apiResponse = (await apiClient.get(
        `/reporting/delegations?${paginationParams.toString()}`
      )) as any;
      // Backend returns { status: 'success', data: {...}, timestamp: '...' }
      return apiResponse.data || apiResponse;
    } catch (error) {
      console.error('Error fetching delegations:', error);
      throw new Error(
        `Failed to load delegations: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * PHASE 1 ENHANCEMENT: Get employee analytics data
   * @param filters - Reporting filters to apply
   * @returns Promise resolving to employee analytics data
   */
  async getEmployeeAnalytics(
    filters: ReportingFilters
  ): Promise<EmployeeAnalytics> {
    try {
      const queryParamsString = this.buildQueryParams(filters);
      const apiResponse = (await apiClient.get(
        `/reporting/employee/analytics?${queryParamsString}`
      )) as any;
      return apiResponse.data || apiResponse;
    } catch (error) {
      console.error('Error fetching employee analytics:', error);
      throw new Error(
        `Failed to load employee analytics: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * Fetches location metrics based on provided filters
   * @param filters - Reporting filters to apply
   * @returns Promise resolving to location metrics array
   */
  async getLocationMetrics(
    filters: ReportingFilters
  ): Promise<LocationMetrics[]> {
    try {
      const queryParams = this.buildQueryParams(filters);
      const apiResponse = (await apiClient.get(
        `/reporting/locations/metrics?${queryParams}`
      )) as any;
      // Backend returns { status: 'success', data: [...], timestamp: '...' }
      return transformLocationMetrics(apiResponse.data || apiResponse);
    } catch (error) {
      console.error('Error fetching location metrics:', error);
      throw new Error(
        `Failed to load location metrics: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  // Private helper methods for data transformation and utility functions

  /**
   * ENHANCED: Fetches service cost summary
   * @param filters - Reporting filters to apply
   * @returns Promise resolving to service cost summary
   */
  async getServiceCostSummary(
    filters: ReportingFilters
  ): Promise<ServiceCostSummary> {
    try {
      const queryParamsString = this.buildQueryParams(filters);
      const rawData = (await apiClient.get(
        `/reporting/services/costs?${queryParamsString}`
      )) as any;
      return (
        rawData.data || {
          averageCostPerService: 0,
          costByType: [],
          monthlyTrend: [],
          totalCost: 0,
        }
      );
    } catch (error) {
      console.error('Error fetching service costs:', error);
      throw new Error(
        `Failed to load service costs: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * ENHANCED: Fetches service history data for vehicles
   * @param filters - Reporting filters to apply
   * @returns Promise resolving to service history data
   */
  async getServiceHistory(
    filters: ReportingFilters
  ): Promise<ServiceHistoryData[]> {
    try {
      const queryParamsString = this.buildQueryParams(filters);
      const rawData = (await apiClient.get(
        `/reporting/services/history?${queryParamsString}`
      )) as any;
      // Transform service history data if needed
      return rawData.data || [];
    } catch (error) {
      console.error('Error fetching service history:', error);
      throw new Error(
        `Failed to load service history: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * PHASE 1 ENHANCEMENT: Get task analytics data
   * @param filters - Reporting filters to apply
   * @returns Promise resolving to task analytics data
   */
  async getTaskAnalytics(filters: ReportingFilters): Promise<TaskAnalytics> {
    try {
      const queryParamsString = this.buildQueryParams(filters);
      const queryParams = new URLSearchParams(queryParamsString);

      // Add task-specific parameters
      if (filters.taskStatus && filters.taskStatus.length > 0) {
        queryParams.append('taskStatus', filters.taskStatus.join(','));
      }
      if (filters.taskPriority && filters.taskPriority.length > 0) {
        queryParams.append('taskPriority', filters.taskPriority.join(','));
      }

      const apiResponse = (await apiClient.get(
        `/reporting/tasks/analytics?${queryParams.toString()}`
      )) as any;
      return apiResponse.data || apiResponse;
    } catch (error) {
      console.error('Error fetching task analytics:', error);
      throw new Error(
        `Failed to load task analytics: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * Fetches task metrics for specific delegations or all tasks
   * @param delegationIds - Optional array of delegation IDs to filter by
   * @returns Promise resolving to task metrics data
   */
  async getTaskMetrics(delegationIds?: string[]): Promise<TaskMetrics> {
    try {
      const queryParams = delegationIds?.length
        ? `delegationIds=${delegationIds.join(',')}`
        : '';

      const apiResponse = (await apiClient.get(
        `/reporting/tasks/metrics?${queryParams}`
      )) as any;
      // Backend returns { status: 'success', data: {...}, timestamp: '...' }
      return transformTaskMetrics(apiResponse.data || apiResponse);
    } catch (error) {
      console.error('Error fetching task metrics:', error);
      throw new Error(
        `Failed to load task metrics: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * Fetches trend data based on provided filters
   * @param filters - Reporting filters to apply
   * @returns Promise resolving to trend data array
   */
  async getTrendData(filters: ReportingFilters): Promise<TrendData[]> {
    try {
      const queryParams = this.buildQueryParams(filters);
      const apiResponse = (await apiClient.get(
        `/reporting/trends?${queryParams}`
      )) as any;
      // Backend returns { status: 'success', data: [...], timestamp: '...' }
      return transformTrendData(apiResponse.data || apiResponse);
    } catch (error) {
      console.error('Error fetching trend data:', error);
      throw new Error(
        `Failed to load trend data: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * PHASE 1 ENHANCEMENT: Get vehicle analytics data
   * @param filters - Reporting filters to apply
   * @returns Promise resolving to vehicle analytics data
   */
  async getVehicleAnalytics(
    filters: ReportingFilters
  ): Promise<VehicleAnalytics> {
    try {
      const queryParamsString = this.buildQueryParams(filters);
      const queryParams = new URLSearchParams(queryParamsString);

      // Add vehicle-specific parameters
      if (filters.vehicles && filters.vehicles.length > 0) {
        queryParams.append('vehicles', filters.vehicles.join(','));
      }
      if (filters.serviceTypes && filters.serviceTypes.length > 0) {
        queryParams.append('serviceTypes', filters.serviceTypes.join(','));
      }
      if (filters.serviceStatus && filters.serviceStatus.length > 0) {
        queryParams.append('serviceStatus', filters.serviceStatus.join(','));
      }

      const url = `/reporting/vehicles/analytics${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
      const result = (await apiClient.get(url)) as any;
      return result.data || result;
    } catch (error) {
      console.error('Error fetching vehicle analytics:', error);
      throw error;
    }
  }

  /**
   * PHASE 1 ENHANCEMENT: Helper method for appending array parameters (DRY principle)
   * @param params - URLSearchParams object
   * @param key - Parameter key
   * @param values - Array of values to append
   */
  private appendArrayParams(
    params: URLSearchParams,
    key: string,
    values: any[]
  ): void {
    if (values && values.length > 0) {
      params.append(key, values.join(','));
    }
  }

  /**
   * PHASE 1 ENHANCEMENT: Builds query parameters string from filters object
   * Following DRY principle: Reusable query building logic
   * @param filters - Reporting filters
   * @returns URL-encoded query parameters string
   */
  private buildQueryParams(filters: ReportingFilters): string {
    const params = new URLSearchParams();

    // Date range - use the format expected by backend validation
    // Defensive programming: Ensure dates are Date objects
    try {
      const fromDate =
        filters.dateRange.from instanceof Date
          ? filters.dateRange.from
          : new Date(filters.dateRange.from);
      const toDate =
        filters.dateRange.to instanceof Date
          ? filters.dateRange.to
          : new Date(filters.dateRange.to);

      // Validate that dates are valid
      if (isNaN(fromDate.getTime()) || isNaN(toDate.getTime())) {
        throw new TypeError('Invalid date range provided');
      }

      params.append(
        'dateRange.from',
        fromDate.toISOString().split('T')[0] || fromDate.toISOString()
      );
      params.append(
        'dateRange.to',
        toDate.toISOString().split('T')[0] || toDate.toISOString()
      );
    } catch (error) {
      console.error('Error processing date range:', error);
      // Fallback to default date range (last 30 days)
      const defaultToDate = new Date();
      const defaultFromDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      params.append(
        'dateRange.from',
        defaultFromDate.toISOString().split('T')[0] ||
          defaultFromDate.toISOString()
      );
      params.append(
        'dateRange.to',
        defaultToDate.toISOString().split('T')[0] || defaultToDate.toISOString()
      );
    }

    // Common array handling using helper method (DRY principle)
    this.appendArrayParams(params, 'status', filters.status);
    this.appendArrayParams(params, 'locations', filters.locations);
    this.appendArrayParams(params, 'employees', filters.employees);
    this.appendArrayParams(params, 'vehicles', filters.vehicles);

    // PHASE 1: Additional entity parameters
    if (filters.taskStatus) {
      this.appendArrayParams(params, 'taskStatus', filters.taskStatus);
    }
    if (filters.taskPriority) {
      this.appendArrayParams(params, 'taskPriority', filters.taskPriority);
    }
    if (filters.serviceTypes) {
      this.appendArrayParams(params, 'serviceTypes', filters.serviceTypes);
    }
    if (filters.serviceStatus) {
      this.appendArrayParams(params, 'serviceStatus', filters.serviceStatus);
    }

    // Cost range handling
    if (filters.costRange) {
      params.append('minCost', filters.costRange.min.toString());
      params.append('maxCost', filters.costRange.max.toString());
    }

    return params.toString();
  }
}

// Export singleton instance for use throughout the application
export const reportingDataService = new ReportingDataService();
