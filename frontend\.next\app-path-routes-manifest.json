{"/api/csp-report/route": "/api/csp-report", "/_not-found/page": "/_not-found", "/favicon.ico/route": "/favicon.ico", "/page": "/", "/[locale]/add-vehicle/page": "/[locale]/add-vehicle", "/[locale]/delegations/[id]/page": "/[locale]/delegations/[id]", "/[locale]/auth-test/page": "/[locale]/auth-test", "/[locale]/delegations/[id]/edit/page": "/[locale]/delegations/[id]/edit", "/[locale]/delegations/add/page": "/[locale]/delegations/add", "/[locale]/delegations/page": "/[locale]/delegations", "/[locale]/employees/[id]/edit/page": "/[locale]/employees/[id]/edit", "/[locale]/employees/add/page": "/[locale]/employees/add", "/[locale]/employees/[id]/page": "/[locale]/employees/[id]", "/[locale]/employees/new/page": "/[locale]/employees/new", "/[locale]/font-size-demo/page": "/[locale]/font-size-demo", "/[locale]/employees/page": "/[locale]/employees", "/[locale]/login/page": "/[locale]/login", "/[locale]/service-history/page": "/[locale]/service-history", "/[locale]/profile/page": "/[locale]/profile", "/[locale]/service-records/[id]/edit/page": "/[locale]/service-records/[id]/edit", "/[locale]/service-records/[id]/page": "/[locale]/service-records/[id]", "/[locale]/settings/page": "/[locale]/settings", "/[locale]/tasks/add/page": "/[locale]/tasks/add", "/[locale]/page": "/[locale]", "/[locale]/tasks/page": "/[locale]/tasks", "/[locale]/tasks/[id]/page": "/[locale]/tasks/[id]", "/[locale]/vehicles/[id]/page": "/[locale]/vehicles/[id]", "/[locale]/vehicles/new/page": "/[locale]/vehicles/new", "/[locale]/vehicles/page": "/[locale]/vehicles", "/[locale]/vehicles/edit/[id]/page": "/[locale]/vehicles/edit/[id]", "/[locale]/zustand-test/page": "/[locale]/zustand-test", "/[locale]/tasks/[id]/edit/page": "/[locale]/tasks/[id]/edit", "/[locale]/reliability/page": "/[locale]/reliability", "/[locale]/delegations/report/list/page": "/[locale]/delegations/report/list", "/[locale]/tasks/report/page": "/[locale]/tasks/report", "/[locale]/vehicles/[id]/report/service-history/page": "/[locale]/vehicles/[id]/report/service-history", "/[locale]/reports/analytics/page": "/[locale]/reports/analytics", "/[locale]/delegations/[id]/report/page": "/[locale]/delegations/[id]/report", "/[locale]/vehicles/[id]/report/page": "/[locale]/vehicles/[id]/report", "/[locale]/reports/page": "/[locale]/reports", "/[locale]/reports/data/page": "/[locale]/reports/data", "/[locale]/admin/page": "/[locale]/admin"}