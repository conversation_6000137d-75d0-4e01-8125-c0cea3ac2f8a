{"/_not-found/page": "/_not-found", "/favicon.ico/route": "/favicon.ico", "/api/csp-report/route": "/api/csp-report", "/page": "/", "/[locale]/add-vehicle/page": "/[locale]/add-vehicle", "/[locale]/auth-test/page": "/[locale]/auth-test", "/[locale]/delegations/add/page": "/[locale]/delegations/add", "/[locale]/delegations/page": "/[locale]/delegations", "/[locale]/delegations/[id]/edit/page": "/[locale]/delegations/[id]/edit", "/[locale]/employees/[id]/page": "/[locale]/employees/[id]", "/[locale]/employees/[id]/edit/page": "/[locale]/employees/[id]/edit", "/[locale]/employees/new/page": "/[locale]/employees/new", "/[locale]/delegations/[id]/page": "/[locale]/delegations/[id]", "/[locale]/page": "/[locale]", "/[locale]/employees/add/page": "/[locale]/employees/add", "/[locale]/font-size-demo/page": "/[locale]/font-size-demo", "/[locale]/employees/page": "/[locale]/employees", "/[locale]/service-history/page": "/[locale]/service-history", "/[locale]/profile/page": "/[locale]/profile", "/[locale]/login/page": "/[locale]/login", "/[locale]/service-records/[id]/page": "/[locale]/service-records/[id]", "/[locale]/settings/page": "/[locale]/settings", "/[locale]/tasks/page": "/[locale]/tasks", "/[locale]/tasks/add/page": "/[locale]/tasks/add", "/[locale]/vehicles/edit/[id]/page": "/[locale]/vehicles/edit/[id]", "/[locale]/vehicles/[id]/page": "/[locale]/vehicles/[id]", "/[locale]/tasks/[id]/page": "/[locale]/tasks/[id]", "/[locale]/service-records/[id]/edit/page": "/[locale]/service-records/[id]/edit", "/[locale]/vehicles/new/page": "/[locale]/vehicles/new", "/[locale]/tasks/[id]/edit/page": "/[locale]/tasks/[id]/edit", "/[locale]/vehicles/page": "/[locale]/vehicles", "/[locale]/zustand-test/page": "/[locale]/zustand-test", "/[locale]/admin/page": "/[locale]/admin", "/[locale]/delegations/[id]/report/page": "/[locale]/delegations/[id]/report", "/[locale]/tasks/report/page": "/[locale]/tasks/report", "/[locale]/delegations/report/list/page": "/[locale]/delegations/report/list", "/[locale]/vehicles/[id]/report/service-history/page": "/[locale]/vehicles/[id]/report/service-history", "/[locale]/vehicles/[id]/report/page": "/[locale]/vehicles/[id]/report", "/[locale]/reports/data/page": "/[locale]/reports/data", "/[locale]/reliability/page": "/[locale]/reliability", "/[locale]/reports/analytics/page": "/[locale]/reports/analytics", "/[locale]/reports/page": "/[locale]/reports"}