# WorkHub Reliability Configuration Guide

## Overview

This guide provides comprehensive configuration information for the WorkHub reliability enhancements, including environment variables, deployment considerations, and operational procedures.

## Environment Variables

### Circuit Breaker Configuration

```env
# Circuit Breaker Global Settings
CIRCUIT_BREAKER_ENABLED=true
CIRCUIT_BREAKER_FAILURE_THRESHOLD=5
CIRCUIT_BREAKER_TIMEOUT=60000
CIRCUIT_BREAKER_RESET_TIMEOUT=30000

# Service-Specific Circuit Breaker Settings
# Database Circuit Breaker
DB_CIRCUIT_BREAKER_TIMEOUT=10000
DB_CIRCUIT_BREAKER_ERROR_THRESHOLD=50
DB_CIRCUIT_BREAKER_RESET_TIMEOUT=30000

# Supabase Circuit Breaker
SUPABASE_CIRCUIT_BREAKER_TIMEOUT=12000
SUPABASE_CIRCUIT_BREAKER_ERROR_THRESHOLD=50
SUPABASE_CIRCUIT_BREAKER_RESET_TIMEOUT=45000

# Redis Circuit Breaker
REDIS_CIRCUIT_BREAKER_TIMEOUT=5000
REDIS_CIRCUIT_BREAKER_ERROR_THRESHOLD=40
REDIS_CIRCUIT_BREAKER_RESET_TIMEOUT=15000

# External API Circuit Breaker
EXTERNAL_API_CIRCUIT_BREAKER_TIMEOUT=15000
EXTERNAL_API_CIRCUIT_BREAKER_ERROR_THRESHOLD=60
EXTERNAL_API_CIRCUIT_BREAKER_RESET_TIMEOUT=60000
```

### Request Deduplication Configuration

```env
# Request Deduplication Global Settings
REQUEST_DEDUP_ENABLED=true
REQUEST_DEDUP_DEFAULT_TTL=300

# Service-Specific Deduplication Settings
ADMIN_DEDUP_TTL=300
API_DEDUP_TTL=60
PERFORMANCE_DEDUP_TTL=30
IDEMPOTENT_DEDUP_TTL=600

# Cache Configuration
DEDUP_CACHE_MAX_SIZE=10000
DEDUP_CACHE_CHECK_PERIOD=60
```

### Monitoring and Metrics Configuration

```env
# Metrics Collection
METRICS_ENABLED=true
METRICS_PORT=9090
METRICS_COLLECTION_INTERVAL=30000
METRICS_DEFAULT_BUCKETS=0.1,0.3,0.5,0.7,1,3,5,7,10

# System Monitoring
SYSTEM_METRICS_ENABLED=true
SYSTEM_METRICS_INTERVAL=30000

# Health Checks
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_TIMEOUT=5000
HEALTH_CHECK_INTERVAL=30000
```

### Alerting Configuration

```env
# Alert System
ALERTS_ENABLED=true
ALERT_WEBHOOK_URL=https://your-alert-endpoint.com
ALERT_EMAIL_RECIPIENTS=<EMAIL>,<EMAIL>
ALERT_SLACK_WEBHOOK=https://hooks.slack.com/services/...

# Alert Thresholds
RESPONSE_TIME_ALERT_THRESHOLD=2000
ERROR_RATE_ALERT_THRESHOLD=5
CPU_USAGE_ALERT_THRESHOLD=80
MEMORY_USAGE_ALERT_THRESHOLD=85
DISK_USAGE_ALERT_THRESHOLD=90

# Alert Escalation
ALERT_ESCALATION_ENABLED=true
ALERT_ESCALATION_DELAY=300000
ALERT_MAX_ESCALATIONS=3
```

## Configuration Validation

### Required Environment Variables

The following environment variables are required for the reliability features to function properly:

```bash
# Validate required variables
required_vars=(
    "CIRCUIT_BREAKER_ENABLED"
    "REQUEST_DEDUP_ENABLED"
    "METRICS_ENABLED"
    "HEALTH_CHECK_ENABLED"
)

for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        echo "ERROR: Required environment variable $var is not set"
        exit 1
    fi
done
```

### Configuration File Template

Create a `.env.reliability` file for reliability-specific configurations:

```env
# WorkHub Reliability Configuration
# Copy this file to .env.reliability and customize as needed

# === CIRCUIT BREAKER CONFIGURATION ===
CIRCUIT_BREAKER_ENABLED=true
CIRCUIT_BREAKER_FAILURE_THRESHOLD=5
CIRCUIT_BREAKER_TIMEOUT=60000
CIRCUIT_BREAKER_RESET_TIMEOUT=30000

# === REQUEST DEDUPLICATION ===
REQUEST_DEDUP_ENABLED=true
REQUEST_DEDUP_DEFAULT_TTL=300
ADMIN_DEDUP_TTL=300
API_DEDUP_TTL=60

# === MONITORING ===
METRICS_ENABLED=true
METRICS_PORT=9090
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_TIMEOUT=5000

# === ALERTING ===
ALERTS_ENABLED=false
ALERT_WEBHOOK_URL=
ALERT_EMAIL_RECIPIENTS=
```

## Deployment Considerations

### Development Environment

```bash
# Install dependencies
npm install

# Set development-specific configurations
export NODE_ENV=development
export CIRCUIT_BREAKER_ENABLED=true
export REQUEST_DEDUP_ENABLED=true
export METRICS_ENABLED=true
export ALERTS_ENABLED=false

# Start the application
npm run dev
```

### Staging Environment

```bash
# Production-like configuration with relaxed thresholds
export NODE_ENV=staging
export CIRCUIT_BREAKER_ENABLED=true
export CIRCUIT_BREAKER_FAILURE_THRESHOLD=10
export REQUEST_DEDUP_ENABLED=true
export METRICS_ENABLED=true
export ALERTS_ENABLED=true
export ALERT_WEBHOOK_URL=https://staging-alerts.workhub.com

# Start with monitoring
npm run start
```

### Production Environment

```bash
# Strict production configuration
export NODE_ENV=production
export CIRCUIT_BREAKER_ENABLED=true
export CIRCUIT_BREAKER_FAILURE_THRESHOLD=5
export REQUEST_DEDUP_ENABLED=true
export METRICS_ENABLED=true
export ALERTS_ENABLED=true
export ALERT_WEBHOOK_URL=https://alerts.workhub.com
export ALERT_EMAIL_RECIPIENTS=<EMAIL>,<EMAIL>

# Start with process manager
pm2 start ecosystem.config.js
```

## Monitoring Endpoints

### Health Check Endpoints

```bash
# Basic health check
curl http://localhost:3000/api/health

# Detailed health check
curl http://localhost:3000/api/health/detailed

# Circuit breaker status
curl http://localhost:3000/api/monitoring/circuit-breakers

# Request deduplication metrics
curl http://localhost:3000/api/monitoring/deduplication

# Prometheus metrics
curl http://localhost:3000/api/metrics
```

### Admin Operations

```bash
# Reset all circuit breakers (requires admin auth)
curl -X POST http://localhost:3000/api/monitoring/circuit-breakers/reset \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{}'

# Reset specific circuit breaker
curl -X POST http://localhost:3000/api/monitoring/circuit-breakers/reset \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"serviceName": "database"}'
```

## Performance Tuning

### Circuit Breaker Tuning

```javascript
// Adjust thresholds based on service characteristics
const serviceConfigs = {
  database: {
    timeout: 10000,        // Database queries can be slow
    errorThreshold: 50,    // 50% error rate triggers circuit
    resetTimeout: 30000    // 30 seconds before retry
  },
  
  external_api: {
    timeout: 15000,        // External APIs can be slower
    errorThreshold: 60,    // More tolerant of external failures
    resetTimeout: 60000    // Longer recovery time
  },
  
  redis: {
    timeout: 5000,         // Cache should be fast
    errorThreshold: 40,    // Less tolerant of cache failures
    resetTimeout: 15000    // Quick recovery for cache
  }
};
```

### Request Deduplication Tuning

```javascript
// Adjust TTL based on operation characteristics
const deduplicationConfigs = {
  admin: {
    ttl: 300,              // 5 minutes for admin operations
    includeBody: true,     // Admin operations are unique
    includeQuery: true
  },
  
  api: {
    ttl: 60,               // 1 minute for API operations
    includeBody: true,     // API operations vary by body
    includeQuery: true
  },
  
  performance: {
    ttl: 30,               // 30 seconds for performance metrics
    includeBody: false,    // Performance queries are similar
    includeQuery: true
  }
};
```

## Troubleshooting

### Common Issues

#### Circuit Breaker Not Triggering
```bash
# Check circuit breaker configuration
curl http://localhost:3000/api/monitoring/circuit-breakers

# Verify error thresholds are appropriate
# Check logs for circuit breaker events
grep "circuit-breaker" logs/app.log
```

#### Request Deduplication Not Working
```bash
# Check deduplication metrics
curl http://localhost:3000/api/monitoring/deduplication

# Verify Redis connection
# Check cache hit rates in metrics
```

#### High Memory Usage
```bash
# Monitor metrics collection overhead
curl http://localhost:3000/api/metrics | grep memory

# Check deduplication cache size
# Consider reducing TTL values
```

### Log Analysis

```bash
# Circuit breaker events
grep "Circuit breaker" logs/app.log | tail -20

# Request deduplication events
grep "request-deduplication" logs/app.log | tail -20

# Performance metrics
grep "Slow HTTP request" logs/app.log | tail -10

# Error patterns
grep "ERROR" logs/app.log | grep -E "(circuit|dedup|metrics)" | tail -20
```

## Security Considerations

### Endpoint Protection

```javascript
// Ensure monitoring endpoints are properly protected
app.get('/api/monitoring/*', authenticateAdmin, ...);
app.post('/api/monitoring/*', authenticateAdmin, ...);

// Metrics endpoint should be internal only
app.get('/api/metrics', internalNetworkOnly, ...);
```

### Sensitive Data Handling

```javascript
// Ensure no sensitive data in metrics labels
const sanitizeLabel = (value) => {
  // Remove sensitive information from metric labels
  return value.replace(/password|token|key/gi, '[REDACTED]');
};
```

## Backup and Recovery

### Configuration Backup

```bash
# Backup current configuration
cp .env .env.backup.$(date +%Y%m%d_%H%M%S)

# Backup reliability configuration
cp .env.reliability .env.reliability.backup.$(date +%Y%m%d_%H%M%S)
```

### Recovery Procedures

```bash
# Disable all reliability features quickly
export CIRCUIT_BREAKER_ENABLED=false
export REQUEST_DEDUP_ENABLED=false
export METRICS_ENABLED=false
export ALERTS_ENABLED=false

# Restart application
pm2 restart workhub-backend
```

This configuration guide provides the foundation for deploying and managing the WorkHub reliability enhancements in various environments while maintaining security and performance standards.
