// jest-dom adds custom jest matchers for asserting on DOM nodes.
// allows you to do things like:
// expect(element).toHaveTextContent(/react/i)
// learn more: https://github.com/testing-library/jest-dom
import '@testing-library/jest-dom';
import { TextDecoder, TextEncoder } from 'node:util';
import React from 'react';

// Mock global fetch
globalThis.fetch = jest.fn();

// Mock global TextEncoder/TextDecoder
globalThis.TextEncoder = TextEncoder;
globalThis.TextDecoder = TextDecoder as any;

// Mock Next.js components and hooks
jest.mock('next/navigation', () => ({
  usePathname: () => '/test-path',
  useRouter: () => ({
    back: jest.fn(),
    forward: jest.fn(),
    pathname: '/test-path',
    prefetch: jest.fn(),
    push: jest.fn(),
    query: {},
    refresh: jest.fn(),
    replace: jest.fn(),
  }),
  useSearchParams: () => new URLSearchParams(),
}));

// Mock next/link
jest.mock('next/link', () => {
  const mockLink = function (props: any) {
    return require('react').createElement('a', props, props.children);
  };
  mockLink.displayName = 'Link';
  return mockLink;
});

// Mock IntersectionObserver
class MockIntersectionObserver {
  disconnect = jest.fn();
  observe = jest.fn();
  unobserve = jest.fn();
}

Object.defineProperty(globalThis, 'IntersectionObserver', {
  configurable: true,
  value: MockIntersectionObserver,
  writable: true,
});

// Mock ResizeObserver
class MockResizeObserver {
  disconnect = jest.fn();
  observe = jest.fn();
  unobserve = jest.fn();
}

Object.defineProperty(globalThis, 'ResizeObserver', {
  configurable: true,
  value: MockResizeObserver,
  writable: true,
});

// Mock window.matchMedia
Object.defineProperty(globalThis, 'matchMedia', {
  value: jest.fn().mockImplementation(query => ({
    addEventListener: jest.fn(),
    addListener: jest.fn(), // Deprecated
    dispatchEvent: jest.fn(),
    matches: false,
    media: query,
    onchange: null,
    removeEventListener: jest.fn(),
    removeListener: jest.fn(), // Deprecated
  })),
  writable: true,
});

// Mock IntersectionObserver
globalThis.IntersectionObserver = class IntersectionObserver {
  constructor(callback: any) {}
  disconnect() {
    return null;
  }
  observe() {
    return null;
  }
  takeRecords() {
    return [];
  }
  unobserve() {
    return null;
  }
} as any;

// Suppress console errors during tests
const originalConsoleError = console.error;
console.error = (...args) => {
  // Filter out React-specific warnings that pollute test output
  if (
    typeof args[0] === 'string' &&
    (args[0].includes('Warning: ReactDOM.render') ||
      args[0].includes('Warning: React.createElement'))
  ) {
    return;
  }
  originalConsoleError(...args);
};
