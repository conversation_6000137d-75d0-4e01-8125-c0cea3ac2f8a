'use client';

import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Separator } from '@/components/ui/separator';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import Link from 'next/link';
import {
  ArrowLeft,
  Edit,
  Trash2,
  Calendar,
  DollarSign,
  Gauge,
  FileText,
  Car,
  AlertTriangle,
  CheckCircle2,
} from 'lucide-react';
import { useToast } from '@/hooks/utils/use-toast';
import {
  useServiceRecord,
  useDeleteServiceRecord,
} from '@/lib/stores/queries/useServiceRecords';
import { VehicleInfoCard } from '@/components/service-records/VehicleInfoCard';

// Types
import type { EnrichedServiceRecord } from '@/lib/types/domain';

export default function ServiceRecordDetailPage() {
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();

  // The ID is essential for this page. If it's not present or not a string,
  // we can't fetch the service record.
  if (!params || typeof params.id !== 'string') {
    return (
      <div className="container mx-auto py-8 text-center text-red-500">
        <p>Error: Invalid or missing Service Record ID.</p>
        <Button asChild className="mt-4">
          <Link href="/service-history">
            <ArrowLeft className="mr-2 h-4 w-4" /> Back to Service History
          </Link>
        </Button>
      </div>
    );
  }

  const recordId = params.id;

  const {
    data: record,
    isLoading: loading,
    error: fetchError,
  } = useServiceRecord(recordId, {
    enabled: !!recordId,
  });

  const deleteMutation = useDeleteServiceRecord();

  // Delete handler with confirmation
  const handleDelete = async () => {
    if (!record) return;

    try {
      await deleteMutation.mutateAsync({
        id: recordId,
        vehicleId: record.vehicleId,
      });

      toast({
        title: 'Deleted!',
        description: 'Service record deleted successfully.',
        variant: 'default',
      });

      router.push('/service-history');
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to delete service record. Please try again.',
        variant: 'destructive',
      });
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto py-8">
        <Card>
          <CardHeader>
            <Skeleton className="h-8 w-3/4" />
          </CardHeader>
          <CardContent className="space-y-4">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-2/3" />
          </CardContent>
        </Card>
      </div>
    );
  }

  if (fetchError) {
    return (
      <div className="container mx-auto py-8 text-center text-red-500">
        <p>Error loading service record: {fetchError.message}</p>
        <Button asChild className="mt-4">
          <Link href="/service-history">
            <ArrowLeft className="mr-2 h-4 w-4" /> Back to Service History
          </Link>
        </Button>
      </div>
    );
  }

  if (!record && !loading) {
    // If not loading and no record, it means not found
    return (
      <div className="container mx-auto py-8 text-center text-gray-500">
        <p>No service record data available.</p>
        <Button asChild className="mt-4">
          <Link href="/service-history">
            <ArrowLeft className="mr-2 h-4 w-4" /> Back to Service History
          </Link>
        </Button>
      </div>
    );
  }

  // Ensure record is defined before proceeding to render details
  if (!record) {
    return null; // Or a more specific loading/empty state if needed
  }

  return (
    <div className="container mx-auto py-8 space-y-6">
      {/* Header with navigation and actions */}
      <div className="flex items-center justify-between">
        <Button asChild variant="outline" size="sm">
          <Link href="/service-history">
            <ArrowLeft className="mr-2 h-4 w-4" /> Back to Service History
          </Link>
        </Button>

        <div className="flex items-center gap-2">
          <Badge variant="secondary" className="flex items-center gap-1">
            <Car className="h-3 w-3" />
            {record.vehicleMake} {record.vehicleModel}
          </Badge>

          <Button asChild size="sm">
            <Link href={`/service-records/${record.id}/edit`}>
              <Edit className="mr-2 h-4 w-4" />
              Edit
            </Link>
          </Button>

          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button
                variant="destructive"
                size="sm"
                disabled={deleteMutation.isPending}
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle className="flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5 text-destructive" />
                  Delete Service Record
                </AlertDialogTitle>
                <AlertDialogDescription>
                  Are you sure you want to delete this service record? This
                  action cannot be undone.
                  <div className="mt-2 p-3 bg-muted rounded-md">
                    <p className="text-sm font-medium">Record Details:</p>
                    <p className="text-sm text-muted-foreground">
                      {record.servicePerformed?.join(', ')} -{' '}
                      {new Date(record.date).toLocaleDateString()}
                    </p>
                  </div>
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction
                  onClick={handleDelete}
                  className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                >
                  Delete Record
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      </div>

      {/* Success indicator */}
      <Alert className="border-green-200 bg-green-50">
        <CheckCircle2 className="h-4 w-4 text-green-600" />
        <AlertDescription className="text-green-800">
          Service record loaded successfully. All information is up to date.
        </AlertDescription>
      </Alert>

      {/* Main content */}
      <div className="grid gap-6 lg:grid-cols-3">
        {/* Service Details */}
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Service Details
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                  <Calendar className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      Service Date
                    </p>
                    <p className="font-semibold">
                      {new Date(record.date).toLocaleDateString()}
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                  <DollarSign className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      Total Cost
                    </p>
                    <p className="font-semibold">
                      ${(record.cost ?? 0).toFixed(2)}
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                  <Gauge className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      Odometer
                    </p>
                    <p className="font-semibold">
                      {record.odometer.toLocaleString()} miles
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                  <FileText className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      Record ID
                    </p>
                    <p className="font-semibold font-mono text-sm">
                      {record.id}
                    </p>
                  </div>
                </div>
              </div>

              <Separator />

              <div>
                <p className="text-sm font-medium text-muted-foreground mb-2">
                  Services Performed
                </p>
                <div className="flex flex-wrap gap-2">
                  {record.servicePerformed.map((service, index) => (
                    <Badge key={index} variant="outline">
                      {service}
                    </Badge>
                  ))}
                </div>
              </div>

              {record.notes && (
                <>
                  <Separator />
                  <div>
                    <p className="text-sm font-medium text-muted-foreground mb-2">
                      Notes
                    </p>
                    <p className="text-sm bg-muted/50 p-3 rounded-lg">
                      {record.notes}
                    </p>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Vehicle Information */}
        <div className="space-y-6">
          <VehicleInfoCard vehicleId={record.vehicleId} />
        </div>
      </div>
    </div>
  );
}
