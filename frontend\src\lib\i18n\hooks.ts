/**
 * @file i18n Utility Hooks
 * @description Custom hooks for internationalization functionality
 */

import { useLocale, useTranslations } from 'next-intl';
import { usePathname, useRouter } from 'next/navigation';

import type { Locale } from '@/i18n';

import { localeDirections, localeNames, locales } from '@/i18n';

/**
 * Hook for gift-specific translations
 */
export function useGiftTranslations() {
  const t = useTranslations('gifts');
  const commonT = useTranslations('common');
  const validationT = useTranslations('validation');

  return {
    commonT,
    occasions: {
      anniversary: t('occasions.anniversary'),
      birthday: t('occasions.birthday'),
      christmas: t('occasions.christmas'),
      fathersDay: t('occasions.fathersDay'),
      graduation: t('occasions.graduation'),
      holiday: t('occasions.holiday'),
      mothersDay: t('occasions.mothersDay'),
      newYear: t('occasions.newYear'),
      other: t('occasions.other'),
      thanksgiving: t('occasions.thanksgiving'),
      valentines: t('occasions.valentines'),
      wedding: t('occasions.wedding'),
    },
    t,
    validationT,
  };
}

/**
 * Hook for accessing translation functions with type safety
 */
export function useI18n() {
  const t = useTranslations();
  const locale = useLocale() as Locale;
  const router = useRouter();
  const pathname = usePathname();

  const isRTL = localeDirections[locale] === 'rtl';
  const direction = localeDirections[locale];
  const localeName = localeNames[locale];

  /**
   * Switch to a different locale
   */
  const switchLocale = (newLocale: Locale) => {
    // Remove current locale from pathname if it exists
    const pathWithoutLocale = pathname.replace(/^\/[a-z]{2}/, '') || '/';

    // Add new locale prefix if it's not the default locale
    const newPath =
      newLocale === 'en'
        ? pathWithoutLocale
        : `/${newLocale}${pathWithoutLocale}`;

    router.push(newPath);
  };

  /**
   * Get available locales with their display names
   */
  const availableLocales = locales.map((loc: Locale) => ({
    code: loc,
    direction: localeDirections[loc],
    isActive: loc === locale,
    name: localeNames[loc],
  }));

  /**
   * Format a date according to the current locale
   */
  const formatDate = (
    date: Date | string,
    options?: Intl.DateTimeFormatOptions
  ) => {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    const defaultOptions: Intl.DateTimeFormatOptions = {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
    };

    return new Intl.DateTimeFormat(locale, {
      ...defaultOptions,
      ...options,
    }).format(dateObj);
  };

  /**
   * Format a number according to the current locale
   */
  const formatNumber = (number: number, options?: Intl.NumberFormatOptions) => {
    return new Intl.NumberFormat(locale, options).format(number);
  };

  /**
   * Format currency according to the current locale
   */
  const formatCurrency = (amount: number) => {
    const currency = locale === 'ar' ? 'IQD' : 'USD';
    return new Intl.NumberFormat(locale, {
      currency,
      style: 'currency',
    }).format(amount);
  };

  /**
   * Get relative time format (e.g., "2 days ago")
   */
  const formatRelativeTime = (date: Date | string) => {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    const now = new Date();
    const diffInSeconds = Math.floor(
      (now.getTime() - dateObj.getTime()) / 1000
    );

    const rtf = new Intl.RelativeTimeFormat(locale, { numeric: 'auto' });

    if (Math.abs(diffInSeconds) < 60) {
      return rtf.format(-diffInSeconds, 'second');
    } else if (Math.abs(diffInSeconds) < 3600) {
      return rtf.format(-Math.floor(diffInSeconds / 60), 'minute');
    } else if (Math.abs(diffInSeconds) < 86_400) {
      return rtf.format(-Math.floor(diffInSeconds / 3600), 'hour');
    } else if (Math.abs(diffInSeconds) < 2_592_000) {
      return rtf.format(-Math.floor(diffInSeconds / 86_400), 'day');
    } else if (Math.abs(diffInSeconds) < 31_536_000) {
      return rtf.format(-Math.floor(diffInSeconds / 2_592_000), 'month');
    } else {
      return rtf.format(-Math.floor(diffInSeconds / 31_536_000), 'year');
    }
  };

  return {
    availableLocales,
    direction,
    formatCurrency,
    formatDate,
    formatNumber,
    formatRelativeTime,
    isRTL,
    locale,
    localeName,
    switchLocale,
    t,
  };
}

/**
 * Hook for navigation translations
 */
export function useNavigationTranslations() {
  const t = useTranslations('navigation');

  return {
    dashboard: t('dashboard'),
    gifts: t('gifts'),
    giftTracking: t('giftTracking'),
    recipients: t('recipients'),
    t,
  };
}

/**
 * Hook for recipient-specific translations
 */
export function useRecipientTranslations() {
  const t = useTranslations('recipients');
  const commonT = useTranslations('common');
  const validationT = useTranslations('validation');

  return {
    commonT,
    t,
    validationT,
  };
}
