#!/bin/bash

# WorkHub Staging Security Verification Script - Enhanced
# Phase 1 Security Hardening Verification: Docker Security, Headers, Input Validation, Rate Limiting
# Version: 2.0 - Enhanced with Context7 Security Best Practices

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Test counters
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# URLs
BACKEND_URL="http://localhost:3001"
FRONTEND_URL="http://localhost:3000"

# Logging functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✅ PASS${NC} $1"
    ((PASSED_TESTS++))
}

fail() {
    echo -e "${RED}❌ FAIL${NC} $1"
    ((FAILED_TESTS++))
}

warning() {
    echo -e "${YELLOW}⚠️  WARN${NC} $1"
}

test_header() {
    ((TOTAL_TESTS++))
    echo -e "\n${BLUE}🔍 Test $TOTAL_TESTS: $1${NC}"
}

# Script header
echo -e "${BLUE}
╔══════════════════════════════════════════════════════════════════════════════╗
║                   WorkHub Staging Security Verification                     ║
║                     Phase 1 Security Hardening Complete                    ║
║                                                                              ║
║  🔒 Testing: Authentication, Authorization, Docker Security                 ║
║  🛡️  Headers, Input Validation, Rate Limiting, RBAC                        ║
╚══════════════════════════════════════════════════════════════════════════════╝
${NC}"

# Test 1: Service Availability
test_header "Service Availability Check"
if curl -f -s "$BACKEND_URL/api/health" > /dev/null; then
    success "Backend service is available"
else
    fail "Backend service is not available"
fi

if curl -f -s "$FRONTEND_URL" > /dev/null; then
    success "Frontend service is available"
else
    fail "Frontend service is not available"
fi

# Test 2: Authentication Protection
test_header "Authentication Protection Verification"

# Test protected endpoints without authentication
ENDPOINTS=(
    "/api/employees"
    "/api/vehicles" 
    "/api/delegations"
    "/api/tasks"
    "/api/servicerecords"
    "/api/admin/diagnostics"
)

for endpoint in "${ENDPOINTS[@]}"; do
    response=$(curl -s -w "%{http_code}" -o /dev/null "$BACKEND_URL$endpoint")
    if [[ "$response" == "401" ]]; then
        success "Endpoint $endpoint properly requires authentication (401)"
    else
        fail "Endpoint $endpoint does not require authentication (got $response, expected 401)"
    fi
done

# Test 3: Security Headers Verification
test_header "Security Headers Verification"

# Check for security headers
headers_response=$(curl -s -I "$BACKEND_URL/api/health")

# Check for key security headers
if echo "$headers_response" | grep -qi "x-content-type-options"; then
    success "X-Content-Type-Options header present"
else
    fail "X-Content-Type-Options header missing"
fi

if echo "$headers_response" | grep -qi "x-frame-options"; then
    success "X-Frame-Options header present"
else
    fail "X-Frame-Options header missing"
fi

if echo "$headers_response" | grep -qi "x-xss-protection"; then
    success "X-XSS-Protection header present"
else
    fail "X-XSS-Protection header missing"
fi

# Test 4: Docker Security Verification
test_header "Docker Security Configuration Verification"

# Check if containers are running as non-root
backend_user=$(docker-compose -f docker-compose.staging.yml exec -T backend whoami 2>/dev/null || echo "unknown")
if [[ "$backend_user" == "workhub" ]]; then
    success "Backend container running as non-root user (workhub)"
else
    fail "Backend container not running as expected non-root user (got: $backend_user)"
fi

frontend_user=$(docker-compose -f docker-compose.staging.yml exec -T frontend whoami 2>/dev/null || echo "unknown")
if [[ "$frontend_user" == "nextjs" ]]; then
    success "Frontend container running as non-root user (nextjs)"
else
    fail "Frontend container not running as expected non-root user (got: $frontend_user)"
fi

# Check security options
backend_security=$(docker inspect "$(docker-compose -f docker-compose.staging.yml ps -q backend)" --format='{{.HostConfig.SecurityOpt}}' 2>/dev/null || echo "[]")
if echo "$backend_security" | grep -q "no-new-privileges:true"; then
    success "Backend container has no-new-privileges security option"
else
    fail "Backend container missing no-new-privileges security option"
fi

# Test 5: Resource Limits Verification
test_header "Resource Limits Verification"

# Check memory limits
backend_memory=$(docker inspect "$(docker-compose -f docker-compose.staging.yml ps -q backend)" --format='{{.HostConfig.Memory}}' 2>/dev/null || echo "0")
if [[ "$backend_memory" -gt 0 ]]; then
    success "Backend container has memory limits configured"
else
    warning "Backend container memory limits not detected"
fi

# Test 6: Input Validation Testing
test_header "Input Validation Testing"

# Test XSS protection
xss_payload='<script>alert("xss")</script>'
response=$(curl -s -X POST "$BACKEND_URL/api/employees" \
    -H "Content-Type: application/json" \
    -d "{\"name\":\"$xss_payload\"}" \
    -w "%{http_code}" -o /dev/null)

if [[ "$response" == "401" ]]; then
    success "XSS payload properly rejected (authentication required)"
elif [[ "$response" == "400" ]]; then
    success "XSS payload properly rejected (validation error)"
else
    warning "XSS payload response unclear (got $response)"
fi

# Test 7: Rate Limiting Verification
test_header "Rate Limiting Verification"

# Make multiple rapid requests to test rate limiting
rate_limit_responses=()
for i in {1..10}; do
    response=$(curl -s -w "%{http_code}" -o /dev/null "$BACKEND_URL/api/health")
    rate_limit_responses+=("$response")
done

# Check if any requests were rate limited (429)
if printf '%s\n' "${rate_limit_responses[@]}" | grep -q "429"; then
    success "Rate limiting is active (429 responses detected)"
else
    warning "Rate limiting not detected in rapid requests"
fi

# Test 8: CORS Configuration
test_header "CORS Configuration Verification"

# Test CORS headers
cors_response=$(curl -s -H "Origin: http://localhost:3000" -I "$BACKEND_URL/api/health")
if echo "$cors_response" | grep -qi "access-control-allow-origin"; then
    success "CORS headers present"
else
    fail "CORS headers missing"
fi

# Test 9: Environment Security
test_header "Environment Security Verification"

# Check that sensitive environment variables are not exposed in logs
if docker-compose -f docker-compose.staging.yml logs backend 2>/dev/null | grep -qi "password\|secret\|key"; then
    warning "Potential sensitive information in backend logs"
else
    success "No obvious sensitive information in backend logs"
fi

# Test 10: Health Check Functionality
test_header "Health Check Functionality"

# Verify health checks are working
backend_health=$(docker-compose -f docker-compose.staging.yml ps backend | grep -o "healthy\|unhealthy\|starting" || echo "unknown")
if [[ "$backend_health" == "healthy" ]]; then
    success "Backend health check is healthy"
else
    fail "Backend health check status: $backend_health"
fi

frontend_health=$(docker-compose -f docker-compose.staging.yml ps frontend | grep -o "healthy\|unhealthy\|starting" || echo "unknown")
if [[ "$frontend_health" == "healthy" ]]; then
    success "Frontend health check is healthy"
else
    fail "Frontend health check status: $frontend_health"
fi

# Test Summary
echo -e "\n${BLUE}
╔══════════════════════════════════════════════════════════════════════════════╗
║                           SECURITY VERIFICATION SUMMARY                     ║
╚══════════════════════════════════════════════════════════════════════════════╝
${NC}"

echo -e "${BLUE}📊 Test Results:${NC}"
echo "  🧪 Total Tests: $TOTAL_TESTS"
echo -e "  ${GREEN}✅ Passed: $PASSED_TESTS${NC}"
echo -e "  ${RED}❌ Failed: $FAILED_TESTS${NC}"

# Calculate success rate
if [[ $TOTAL_TESTS -gt 0 ]]; then
    success_rate=$((PASSED_TESTS * 100 / TOTAL_TESTS))
    echo "  📈 Success Rate: ${success_rate}%"
else
    echo "  📈 Success Rate: N/A"
fi

echo ""

# Security status assessment
if [[ $FAILED_TESTS -eq 0 ]]; then
    echo -e "${GREEN}🎉 EXCELLENT: All security tests passed!${NC}"
    echo -e "${GREEN}🛡️  Your staging environment has strong security posture.${NC}"
elif [[ $FAILED_TESTS -le 2 ]]; then
    echo -e "${YELLOW}⚠️  GOOD: Most security tests passed with minor issues.${NC}"
    echo -e "${YELLOW}🔧 Review failed tests and address issues before production.${NC}"
else
    echo -e "${RED}🚨 ATTENTION REQUIRED: Multiple security tests failed.${NC}"
    echo -e "${RED}🛠️  Address security issues before proceeding to production.${NC}"
fi

echo ""
echo -e "${BLUE}📋 Next Steps:${NC}"
echo "  1. Review any failed tests above"
echo "  2. Test authentication flow manually in browser"
echo "  3. Verify RBAC system with different user roles"
echo "  4. Monitor application logs for security events"
echo "  5. Proceed to Phase 2 security enhancements if all tests pass"
echo ""

echo -e "${BLUE}🔗 Useful Commands:${NC}"
echo "  • View logs: docker-compose -f docker-compose.staging.yml logs -f"
echo "  • Check container status: docker-compose -f docker-compose.staging.yml ps"
echo "  • Stop services: docker-compose -f docker-compose.staging.yml down"
echo ""

# Exit with appropriate code
if [[ $FAILED_TESTS -eq 0 ]]; then
    exit 0
else
    exit 1
fi
