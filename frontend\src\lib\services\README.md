# Generic Toast Service System

This system provides a comprehensive, type-safe solution for displaying toast notifications across all forms in the application. It eliminates code duplication and ensures consistent messaging patterns.

## Features

- **Generic Entity Support**: Works with any entity type `T` using TypeScript generics
- **Predefined Configurations**: Ready-to-use services for Employee, Vehicle, Task, and Delegation entities
- **Flexible Integration**: Multiple ways to integrate depending on your needs
- **Type Safety**: Full TypeScript support with proper type inference
- **DRY Principle**: Eliminates code duplication across forms
- **Consistent UI/UX**: Standardized toast messages throughout the application

## Architecture Overview

The system consists of three main layers:

1. **Core Service Layer** (`toastService.ts`)

   - `ToastService`: Base service for generic toasts
   - `GenericEntityToastService<T>`: Generic service for entity-specific toasts
   - `EntityToastConfig<T>`: Configuration interface for entity messages

2. **React Hooks Layer** (`useFormToast.ts`)

   - `useFormToast()`: For generic form notifications
   - `useEntityFormToast<T>()`: For entity-specific form notifications
   - `usePredefinedEntityToast()`: For predefined entity types

3. **Form Components Layer** (`EntityForm.tsx`, `baseForm.tsx`)
   - `EntityForm<T, E>`: Enhanced form component with automatic toast integration
   - `BaseForm<T>`: Updated base form using the new toast system

## Quick Start

### 1. Using Predefined Entity Services

For the main entities (Employee, Vehicle, Task, Delegation), use the predefined services:

```tsx
import { usePredefinedEntityToast } from '@/hooks/forms/useFormToast';

function EmployeeCreatePage() {
  const { showEntityCreated, showEntityCreationError } =
    usePredefinedEntityToast('employee');

  const handleSubmit = async (data: EmployeeFormData) => {
    try {
      const newEmployee = await createEmployee(data);
      showEntityCreated(newEmployee); // Shows: "Employee Added - John Doe has been successfully created."
    } catch (error) {
      showEntityCreationError(error); // Shows: "Failed to Create Employee - [error message]"
    }
  };
}
```

### 2. Using the Enhanced Entity Form

The `EntityForm` component handles all toast logic automatically:

```tsx
import { EntityForm } from '@/components/ui/forms/EntityForm';

function EmployeeCreateForm() {
  return (
    <EntityForm
      schema={employeeSchema}
      onSubmit={handleCreateEmployee}
      entityName="Employee"
      getEntityDisplayName={employee => employee.name}
    >
      {/* Your form fields here */}
    </EntityForm>
  );
}
```

### 3. Creating Custom Entity Services

For new entity types, create a simple service:

```tsx
import { createSimpleEntityToastService } from '@/lib/services/toastService';

// For a new entity type
const projectToast = createSimpleEntityToastService(
  'Project',
  project => project.name
);

// Use it in your form
function ProjectForm() {
  const handleSubmit = async data => {
    try {
      const newProject = await createProject(data);
      projectToast.entityCreated(newProject);
    } catch (error) {
      projectToast.entityCreationError(error.message);
    }
  };
}
```

## Detailed Usage Examples

### Example 1: Form Component with Predefined Entity

```tsx
// pages/employees/new.tsx
'use client';

import { usePredefinedEntityToast } from '@/hooks/forms/useFormToast';
import { useCreateEmployee } from '@/lib/stores/queries/useEmployees';

export default function CreateEmployeePage() {
  const { showEntityCreated, showEntityCreationError } =
    usePredefinedEntityToast('employee');
  const createEmployee = useCreateEmployee();

  const handleSubmit = async (data: EmployeeFormData) => {
    try {
      const newEmployee = await createEmployee.mutateAsync(data);
      showEntityCreated(newEmployee);
      router.push('/employees');
    } catch (error) {
      showEntityCreationError(error);
    }
  };

  return <EmployeeForm onSubmit={handleSubmit} />;
}
```

### Example 2: Custom Entity Configuration

```tsx
import { useEntityFormToast } from '@/hooks/forms/useFormToast';

const customEntityConfig = {
  entityName: 'Service Record',
  getDisplayName: record => `${record.serviceType} for ${record.vehicleName}`,
  messages: {
    created: {
      title: 'Service Logged',
      description: displayName =>
        `${displayName} has been successfully recorded.`,
    },
    // ... other messages
  },
};

function ServiceRecordForm() {
  const { showEntityCreated } = useEntityFormToast(customEntityConfig);

  const handleSubmit = async data => {
    try {
      const record = await createServiceRecord(data);
      showEntityCreated(record); // Shows: "Service Logged - Oil Change for Toyota Camry has been successfully recorded."
    } catch (error) {
      // Handle error
    }
  };
}
```

### Example 3: Generic Form Toast

```tsx
import { useFormToast } from '@/hooks/forms/useFormToast';

function GenericForm() {
  const { showFormSuccess, showFormError } = useFormToast();

  const handleSubmit = async data => {
    try {
      await submitData(data);
      showFormSuccess({
        successTitle: 'Data Saved',
        successDescription: 'Your information has been updated.',
      });
    } catch (error) {
      showFormError(error, {
        errorTitle: 'Save Failed',
        errorDescription: 'Could not save your changes. Please try again.',
      });
    }
  };
}
```

## Updating Existing Forms

To update existing forms to use the new toast system:

### Before (Old Pattern)

```tsx
import { useToast } from '@/hooks/utils/use-toast';

function OldForm() {
  const { toast } = useToast();

  const handleSubmit = async data => {
    try {
      const result = await submitData(data);
      toast({
        title: 'Employee Added',
        description: `${result.name} has been successfully created.`,
        variant: 'default',
      });
    } catch (error) {
      toast({
        title: 'Error Adding Employee',
        description: error.message || 'An unexpected error occurred.',
        variant: 'destructive',
      });
    }
  };
}
```

### After (New Pattern)

```tsx
import { usePredefinedEntityToast } from '@/hooks/forms/useFormToast';

function NewForm() {
  const { showEntityCreated, showEntityCreationError } =
    usePredefinedEntityToast('employee');

  const handleSubmit = async data => {
    try {
      const result = await submitData(data);
      showEntityCreated(result); // Automatic message generation
    } catch (error) {
      showEntityCreationError(error); // Automatic error handling
    }
  };
}
```

## Advanced Features

### Custom Entity Display Names

```tsx
const vehicleConfig = {
  entityName: 'Vehicle',
  getDisplayName: vehicle =>
    `${vehicle.year} ${vehicle.make} ${vehicle.model} (${vehicle.plateNumber})`,
  // ... messages
};
```

### Conditional Toast Display

```tsx
function ConditionalToastForm() {
  const { showEntityCreated } = useEntityFormToast(config);

  const handleSubmit = async (data, showToast = true) => {
    try {
      const result = await submitData(data);
      if (showToast) {
        showEntityCreated(result);
      }
    } catch (error) {
      // Always show errors
      showEntityCreationError(error);
    }
  };
}
```

### Multiple Operations in One Form

```tsx
function MultiOperationForm() {
  const { showEntityCreated, showEntityUpdated, showEntityDeleted } =
    usePredefinedEntityToast('employee');

  const handleCreate = async data => {
    const result = await createEmployee(data);
    showEntityCreated(result);
  };

  const handleUpdate = async (id, data) => {
    const result = await updateEmployee(id, data);
    showEntityUpdated(result);
  };

  const handleDelete = async employee => {
    await deleteEmployee(employee.id);
    showEntityDeleted(employee);
  };
}
```

## Migration Guide

### Step 1: Update Imports

Replace `useToast` imports with the appropriate toast hook:

```tsx
// Old
import { useToast } from '@/hooks/utils/use-toast';

// New - For predefined entities
import { usePredefinedEntityToast } from '@/hooks/forms/useFormToast';

// New - For custom entities
import { useEntityFormToast } from '@/hooks/forms/useFormToast';

// New - For generic forms
import { useFormToast } from '@/hooks/forms/useFormToast';
```

### Step 2: Update Hook Usage

Replace `toast` calls with entity-specific methods:

```tsx
// Old
const { toast } = useToast();

// New
const { showEntityCreated, showEntityCreationError } =
  usePredefinedEntityToast('employee');
```

### Step 3: Simplify Toast Calls

Replace manual toast configuration with automatic methods:

```tsx
// Old
toast({
  title: 'Employee Added',
  description: `${employee.name} has been successfully created.`,
  variant: 'default',
});

// New
showEntityCreated(employee);
```

## Best Practices

1. **Use Predefined Services**: For Employee, Vehicle, Task, and Delegation entities, always use `usePredefinedEntityToast`.

2. **Consistent Display Names**: Ensure your `getDisplayName` function returns consistent, user-friendly names.

3. **Error Handling**: Always handle both success and error cases with appropriate toast notifications.

4. **Type Safety**: Leverage TypeScript generics to ensure type safety across your forms.

5. **DRY Principle**: Avoid creating duplicate toast configurations; use the factory functions instead.

6. **User Experience**: Keep toast messages concise but informative.

## Troubleshooting

### Common Issues

1. **Missing Entity Display**: Ensure your entity has the required properties for `getDisplayName`.

2. **Type Errors**: Make sure your entity types match the expected interface for the toast configuration.

3. **Toast Not Showing**: Check that you're calling the toast methods after successful operations.

4. **Circular Dependencies**: Use lazy imports in `usePredefinedEntityToast` to avoid circular dependencies.

### Debug Tips

1. Check the console for error messages
2. Verify that your entity objects have the expected properties
3. Ensure toast providers are properly set up in your app root
4. Test with simple generic toasts first, then move to entity-specific ones

## API Reference

### Core Services

- `ToastService`: Base toast service with `success()`, `error()`, `info()`, and `show()` methods
- `GenericEntityToastService<T>`: Entity-specific service with CRUD operation methods
- `createSimpleEntityToastService<T>()`: Factory for creating basic entity services

### React Hooks

- `useFormToast()`: Generic form toast notifications
- `useEntityFormToast<T>()`: Entity-specific form notifications
- `usePredefinedEntityToast()`: Predefined entity services

### Form Components

- `EntityForm<T, E>`: Enhanced form with automatic toast integration
- `BaseForm<T>`: Updated base form using the toast system

For more detailed API documentation, refer to the TypeScript interfaces and JSDoc comments in the source files.
