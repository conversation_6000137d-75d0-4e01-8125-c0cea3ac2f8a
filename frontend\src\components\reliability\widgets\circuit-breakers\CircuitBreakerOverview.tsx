/**
 * @file Circuit breaker overview widget component.
 * This component provides a comprehensive overview of all circuit breakers with status indicators,
 * summary statistics, and real-time state monitoring.
 * @module components/reliability/widgets/circuit-breakers/CircuitBreakerOverview
 */

'use client';

import {
  AlertTriangle,
  CheckCircle,
  Clock,
  Shield,
  ShieldAlert,
  ShieldCheck,
  TrendingDown,
  TrendingUp,
} from 'lucide-react';
import React from 'react';

import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { useCircuitBreakerStatus } from '@/lib/stores/queries/useReliability';
import type { CircuitBreakerState } from '@/lib/types/domain';
import { cn } from '@/lib/utils';

/**
 * Props for the CircuitBreakerOverview component
 */
export interface CircuitBreakerOverviewProps {
  /** Optional CSS class name for styling */
  className?: string;
  /** Whether to show detailed metrics */
  showDetails?: boolean;
}

/**
 * Circuit breaker overview widget component.
 * 
 * This component provides:
 * - Real-time circuit breaker status summary
 * - Visual indicators for each circuit breaker state
 * - Health percentage and trend indicators
 * - Quick access to detailed circuit breaker information
 * 
 * Features:
 * - Real-time WebSocket updates
 * - Color-coded status indicators
 * - Responsive design
 * - Accessibility support
 * - Error handling and loading states
 * 
 * @param props - Component props
 * @returns JSX element representing the circuit breaker overview
 */
export const CircuitBreakerOverview: React.FC<CircuitBreakerOverviewProps> = ({
  className = '',
  showDetails = true,
}) => {
  const { data: circuitBreakerData, isLoading, error } = useCircuitBreakerStatus();

  /**
   * Get status configuration for circuit breaker states
   */
  const getStateConfig = (state: CircuitBreakerState) => {
    switch (state) {
      case 'CLOSED':
        return {
          icon: ShieldCheck,
          color: 'text-green-600 dark:text-green-400',
          bgColor: 'bg-green-100 dark:bg-green-900/20',
          label: 'Healthy',
          description: 'Operating normally',
        };
      case 'HALF_OPEN':
        return {
          icon: Shield,
          color: 'text-yellow-600 dark:text-yellow-400',
          bgColor: 'bg-yellow-100 dark:bg-yellow-900/20',
          label: 'Testing',
          description: 'Testing recovery',
        };
      case 'OPEN':
        return {
          icon: ShieldAlert,
          color: 'text-red-600 dark:text-red-400',
          bgColor: 'bg-red-100 dark:bg-red-900/20',
          label: 'Failed',
          description: 'Blocking requests',
        };
    }
  };

  /**
   * Calculate health percentage
   */
  const getHealthPercentage = () => {
    if (!circuitBreakerData?.summary) return 0;
    const { total, closed } = circuitBreakerData.summary;
    return total > 0 ? Math.round((closed / total) * 100) : 100;
  };

  /**
   * Get overall health status
   */
  const getOverallStatus = () => {
    const healthPercentage = getHealthPercentage();
    if (healthPercentage >= 90) return 'healthy';
    if (healthPercentage >= 70) return 'degraded';
    return 'unhealthy';
  };

  // Handle loading state
  if (isLoading) {
    return (
      <div className={cn('space-y-4', className)}>
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
          {Array.from({ length: 4 }).map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader className="pb-2">
                <div className="h-4 w-20 bg-muted rounded"></div>
              </CardHeader>
              <CardContent>
                <div className="h-8 w-12 bg-muted rounded mb-2"></div>
                <div className="h-3 w-16 bg-muted rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  // Handle error state
  if (error || !circuitBreakerData) {
    return (
      <div className={cn('flex items-center justify-center py-8', className)}>
        <div className="text-center">
          <AlertTriangle className="mx-auto h-8 w-8 text-muted-foreground mb-2" />
          <p className="text-sm text-muted-foreground">
            Failed to load circuit breaker data
          </p>
        </div>
      </div>
    );
  }

  const { summary, circuitBreakers } = circuitBreakerData;
  const healthPercentage = getHealthPercentage();
  const overallStatus = getOverallStatus();

  return (
    <div className={cn('space-y-4', className)}>
      {/* Summary Cards */}
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
        {/* Total Circuit Breakers */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{summary.total}</div>
            <p className="text-xs text-muted-foreground">Circuit breakers</p>
          </CardContent>
        </Card>

        {/* Healthy (Closed) */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Healthy</CardTitle>
            <ShieldCheck className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{summary.closed}</div>
            <p className="text-xs text-muted-foreground">Operating normally</p>
          </CardContent>
        </Card>

        {/* Testing (Half-Open) */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Testing</CardTitle>
            <Clock className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{summary.halfOpen}</div>
            <p className="text-xs text-muted-foreground">Testing recovery</p>
          </CardContent>
        </Card>

        {/* Failed (Open) */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Failed</CardTitle>
            <ShieldAlert className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{summary.open}</div>
            <p className="text-xs text-muted-foreground">Blocking requests</p>
          </CardContent>
        </Card>
      </div>

      {/* Health Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className={cn(
              'h-5 w-5',
              overallStatus === 'healthy' ? 'text-green-600' :
              overallStatus === 'degraded' ? 'text-yellow-600' : 'text-red-600'
            )} />
            Overall Health
            <Badge variant={
              overallStatus === 'healthy' ? 'default' :
              overallStatus === 'degraded' ? 'secondary' : 'destructive'
            }>
              {healthPercentage}%
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <Progress 
              value={healthPercentage} 
              className="h-2"
              aria-label={`Circuit breaker health: ${healthPercentage}%`}
            />
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">
                {summary.closed} of {summary.total} circuit breakers healthy
              </span>
              {summary.open > 0 && (
                <span className="flex items-center gap-1 text-red-600">
                  <TrendingDown className="h-3 w-3" />
                  {summary.open} failing
                </span>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Circuit Breaker List (if showing details) */}
      {showDetails && circuitBreakers.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Circuit Breaker Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {circuitBreakers.slice(0, 5).map((breaker) => {
                const config = getStateConfig(breaker.state);
                const Icon = config.icon;
                
                return (
                  <div
                    key={breaker.name}
                    className="flex items-center justify-between p-3 rounded-lg border"
                  >
                    <div className="flex items-center gap-3">
                      <div className={cn('p-2 rounded-full', config.bgColor)}>
                        <Icon className={cn('h-4 w-4', config.color)} />
                      </div>
                      <div>
                        <p className="font-medium">{breaker.name}</p>
                        <p className="text-sm text-muted-foreground">
                          {config.description}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <Badge variant={
                        breaker.state === 'CLOSED' ? 'default' :
                        breaker.state === 'HALF_OPEN' ? 'secondary' : 'destructive'
                      }>
                        {config.label}
                      </Badge>
                      {breaker.failureCount > 0 && (
                        <p className="text-xs text-muted-foreground mt-1">
                          {breaker.failureCount} failures
                        </p>
                      )}
                    </div>
                  </div>
                );
              })}
              
              {circuitBreakers.length > 5 && (
                <div className="text-center pt-2">
                  <p className="text-sm text-muted-foreground">
                    +{circuitBreakers.length - 5} more circuit breakers
                  </p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

/**
 * Default export for the CircuitBreakerOverview component
 */
export default CircuitBreakerOverview;
