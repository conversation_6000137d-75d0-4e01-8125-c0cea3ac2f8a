// metrics.service.test.ts - Unit tests for Metrics Collection Service

import { describe, test, expect, beforeEach, afterEach, jest } from '@jest/globals';
import {
  createMockPrometheusClient,
  createMockRequest,
  createMockResponse,
  createMockNext,
} from '../../reliabilityTestUtils.js';

// Mock dependencies
const mockPrometheusClient = createMockPrometheusClient();

jest.mock('prom-client', () => mockPrometheusClient);

jest.mock('../../../utils/logger', () => ({
  default: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
}));

jest.mock('os', () => ({
  cpus: jest.fn().mockReturnValue([{}, {}, {}, {}]), // 4 CPUs
  totalmem: jest.fn().mockReturnValue(8 * 1024 * 1024 * 1024), // 8GB
  freemem: jest.fn().mockReturnValue(4 * 1024 * 1024 * 1024), // 4GB free
  loadavg: jest.fn().mockReturnValue([0.5, 0.7, 0.9]),
}));

jest.mock('process', () => ({
  hrtime: {
    bigint: jest.fn().mockReturnValue(BigInt(Date.now() * 1000000)),
  },
  memoryUsage: jest.fn().mockReturnValue({
    rss: 100 * 1024 * 1024, // 100MB
    heapTotal: 80 * 1024 * 1024, // 80MB
    heapUsed: 60 * 1024 * 1024, // 60MB
    external: 10 * 1024 * 1024, // 10MB
  }),
}));

// Import after mocking
import {
  getMetrics,
  metricsMiddleware,
  businessMetrics,
} from '../../../services/metrics.service.js';

describe('Metrics Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockPrometheusClient._getMetrics().clear();
  });

  describe('Metrics Export', () => {
    test('should export metrics in Prometheus format', async () => {
      const metrics = await getMetrics();

      expect(typeof metrics).toBe('string');
      expect(metrics).toBe('# Mock metrics data');
      expect(mockPrometheusClient.register.metrics).toHaveBeenCalled();
    });

    test('should handle metrics export errors', async () => {
      mockPrometheusClient.register.metrics.mockRejectedValueOnce(new Error('Export failed'));

      await expect(getMetrics()).rejects.toThrow('Export failed');
    });
  });

  describe('HTTP Metrics Middleware', () => {
    test('should collect HTTP request metrics', async () => {
      const req = createMockRequest({
        method: 'GET',
        path: '/api/test',
        user: { role: 'admin' },
      });
      const { res } = createMockResponse();
      const { next } = createMockNext();

      // Execute middleware
      metricsMiddleware(req as any, res as any, next);

      // Simulate response completion
      res.status!(200);
      (res as any).end();

      // Verify metrics were recorded
      const metricsMap = mockPrometheusClient._getMetrics();
      expect(metricsMap.has('workhub_http_request_duration_seconds')).toBe(true);
      expect(metricsMap.has('workhub_http_requests_total')).toBe(true);
    });

    test('should handle requests without user context', async () => {
      const req = createMockRequest({
        method: 'GET',
        path: '/api/public',
        // No user property
      });
      const { res } = createMockResponse();
      const { next } = createMockNext();

      metricsMiddleware(req as any, res as any, next);
      (res as any).end();

      // Should not throw error and should use 'anonymous' role
      expect(true).toBe(true);
    });

    test('should record different status codes', async () => {
      const req = createMockRequest({ method: 'POST', path: '/api/error' });
      const { res } = createMockResponse();
      const { next } = createMockNext();

      metricsMiddleware(req as any, res as any, next);
      res.status!(500);
      (res as any).end();

      // Metrics should be recorded with 500 status code
      const metricsMap = mockPrometheusClient._getMetrics();
      const httpRequestsMetric = metricsMap.get('workhub_http_requests_total');
      expect(httpRequestsMetric.labels).toHaveBeenCalledWith(
        'POST',
        '/api/error',
        '500',
        'anonymous',
      );
    });

    test('should measure response time accurately', async () => {
      const req = createMockRequest({ method: 'GET', path: '/api/slow' });
      const { res } = createMockResponse();
      const { next } = createMockNext();

      // Mock hrtime to simulate time passage
      let callCount = 0;
      (process.hrtime as jest.Mock).mockImplementation(() => {
        callCount++;
        return callCount === 1 ? [0, 0] : [0, 100000000]; // 100ms difference
      });

      metricsMiddleware(req as any, res as any, next);
      (res as any).end();

      const metricsMap = mockPrometheusClient._getMetrics();
      const durationMetric = metricsMap.get('workhub_http_request_duration_seconds');
      expect(durationMetric.observe).toHaveBeenCalledWith(0.1); // 100ms = 0.1s
    });
  });

  describe('Business Metrics', () => {
    test('should record database operations', () => {
      businessMetrics.recordDatabaseOperation('users', 'SELECT', 'success', 50);

      const metricsMap = mockPrometheusClient._getMetrics();
      const dbMetric = metricsMap.get('workhub_database_operations_total');
      expect(dbMetric.labels).toHaveBeenCalledWith('users', 'SELECT', 'success');
      expect(dbMetric.inc).toHaveBeenCalled();

      const durationMetric = metricsMap.get('workhub_database_operation_duration_seconds');
      expect(durationMetric.labels).toHaveBeenCalledWith('users', 'SELECT');
      expect(durationMetric.observe).toHaveBeenCalledWith(0.05); // 50ms = 0.05s
    });

    test('should record circuit breaker requests', () => {
      businessMetrics.recordCircuitBreakerRequest('database', 'user-service', 'success');

      const metricsMap = mockPrometheusClient._getMetrics();
      const cbMetric = metricsMap.get('workhub_circuit_breaker_requests_total');
      expect(cbMetric.labels).toHaveBeenCalledWith('database', 'user-service', 'success');
      expect(cbMetric.inc).toHaveBeenCalled();
    });

    test('should record circuit breaker state changes', () => {
      businessMetrics.recordCircuitBreakerStateChange('database', 'user-service', 'OPEN');

      const metricsMap = mockPrometheusClient._getMetrics();
      const stateMetric = metricsMap.get('workhub_circuit_breaker_state_changes_total');
      expect(stateMetric.labels).toHaveBeenCalledWith('database', 'user-service', 'OPEN');
      expect(stateMetric.inc).toHaveBeenCalled();
    });

    test('should record cache operations', () => {
      businessMetrics.recordCacheOperation('redis', 'GET', 'hit');

      const metricsMap = mockPrometheusClient._getMetrics();
      const cacheMetric = metricsMap.get('workhub_cache_operations_total');
      expect(cacheMetric.labels).toHaveBeenCalledWith('redis', 'GET', 'hit');
      expect(cacheMetric.inc).toHaveBeenCalled();
    });

    test('should record admin operations', () => {
      businessMetrics.recordAdminOperation('VIEW_DASHBOARD', 'admin-user-123', 'success');

      const metricsMap = mockPrometheusClient._getMetrics();
      const adminMetric = metricsMap.get('workhub_admin_operations_total');
      expect(adminMetric.labels).toHaveBeenCalledWith('VIEW_DASHBOARD', 'success');
      expect(adminMetric.inc).toHaveBeenCalled();
    });

    test('should record API endpoint requests', () => {
      businessMetrics.recordApiEndpointRequest('/api/users', 'GET', '200', 'admin');

      const metricsMap = mockPrometheusClient._getMetrics();
      const apiMetric = metricsMap.get('workhub_api_endpoint_requests_total');
      expect(apiMetric.labels).toHaveBeenCalledWith('/api/users', 'GET', '200', 'admin');
      expect(apiMetric.inc).toHaveBeenCalled();
    });
  });

  describe('System Metrics', () => {
    test('should collect CPU usage metrics', () => {
      systemMetrics.recordCpuUsage(75.5);

      const metricsMap = mockPrometheusClient._getMetrics();
      const cpuMetric = metricsMap.get('workhub_system_cpu_usage_percent');
      expect(cpuMetric.set).toHaveBeenCalledWith(75.5);
    });

    test('should collect memory usage metrics', () => {
      systemMetrics.recordMemoryUsage(60, 80);

      const metricsMap = mockPrometheusClient._getMetrics();
      const memUsedMetric = metricsMap.get('workhub_system_memory_used_bytes');
      const memTotalMetric = metricsMap.get('workhub_system_memory_total_bytes');

      expect(memUsedMetric.set).toHaveBeenCalledWith(60);
      expect(memTotalMetric.set).toHaveBeenCalledWith(80);
    });

    test('should collect load average metrics', () => {
      systemMetrics.recordLoadAverage(0.5, 0.7, 0.9);

      const metricsMap = mockPrometheusClient._getMetrics();
      const load1Metric = metricsMap.get('workhub_system_load_average_1m');
      const load5Metric = metricsMap.get('workhub_system_load_average_5m');
      const load15Metric = metricsMap.get('workhub_system_load_average_15m');

      expect(load1Metric.set).toHaveBeenCalledWith(0.5);
      expect(load5Metric.set).toHaveBeenCalledWith(0.7);
      expect(load15Metric.set).toHaveBeenCalledWith(0.9);
    });

    test('should collect active connections metrics', () => {
      systemMetrics.recordActiveConnections(25);

      const metricsMap = mockPrometheusClient._getMetrics();
      const connectionsMetric = metricsMap.get('workhub_active_connections');
      expect(connectionsMetric.set).toHaveBeenCalledWith(25);
    });
  });

  describe('Metric Labels and Dimensions', () => {
    test('should handle metrics with multiple labels', () => {
      businessMetrics.recordDatabaseOperation('users', 'SELECT', 'success', 50);

      const metricsMap = mockPrometheusClient._getMetrics();
      const dbMetric = metricsMap.get('workhub_database_operations_total');

      // Verify all expected labels were used
      expect(dbMetric.labels).toHaveBeenCalledWith('users', 'SELECT', 'success');
    });

    test('should handle metrics without labels', () => {
      systemMetrics.recordCpuUsage(50);

      const metricsMap = mockPrometheusClient._getMetrics();
      const cpuMetric = metricsMap.get('workhub_system_cpu_usage_percent');

      // Should call set directly without labels
      expect(cpuMetric.set).toHaveBeenCalledWith(50);
    });
  });

  describe('Error Handling', () => {
    test('should handle metric recording errors gracefully', () => {
      const metricsMap = mockPrometheusClient._getMetrics();
      const mockMetric = {
        labels: jest.fn().mockReturnThis(),
        inc: jest.fn().mockImplementation(() => {
          throw new Error('Metric recording failed');
        }),
      };
      metricsMap.set('workhub_database_operations_total', mockMetric);

      // Should not throw error
      expect(() => {
        businessMetrics.recordDatabaseOperation('users', 'SELECT', 'success', 50);
      }).not.toThrow();
    });

    test('should handle missing metrics gracefully', () => {
      // Clear all metrics to simulate missing metric
      mockPrometheusClient._getMetrics().clear();

      // Should not throw error when metric doesn't exist
      expect(() => {
        businessMetrics.recordDatabaseOperation('users', 'SELECT', 'success', 50);
      }).not.toThrow();
    });
  });

  describe('Performance Considerations', () => {
    test('should handle high-frequency metric updates', () => {
      // Simulate many rapid metric updates
      for (let i = 0; i < 1000; i++) {
        businessMetrics.recordApiEndpointRequest('/api/test', 'GET', '200', 'user');
      }

      const metricsMap = mockPrometheusClient._getMetrics();
      const apiMetric = metricsMap.get('workhub_api_endpoint_requests_total');

      // Should handle all updates without error
      expect(apiMetric.inc).toHaveBeenCalledTimes(1000);
    });

    test('should handle concurrent metric updates', async () => {
      // Simulate concurrent metric updates
      const promises = Array.from({ length: 100 }, (_, i) =>
        Promise.resolve().then(() =>
          businessMetrics.recordDatabaseOperation('users', 'SELECT', 'success', i),
        ),
      );

      await Promise.all(promises);

      // Should handle all concurrent updates
      expect(true).toBe(true); // Test passes if no errors thrown
    });
  });

  describe('Metric Types and Behavior', () => {
    test('should use Counter for cumulative metrics', () => {
      businessMetrics.recordDatabaseOperation('users', 'SELECT', 'success', 50);

      expect(mockPrometheusClient.Counter).toHaveBeenCalledWith(
        expect.objectContaining({
          name: 'workhub_database_operations_total',
        }),
      );
    });

    test('should use Gauge for point-in-time metrics', () => {
      systemMetrics.recordCpuUsage(75);

      expect(mockPrometheusClient.Gauge).toHaveBeenCalledWith(
        expect.objectContaining({
          name: 'workhub_system_cpu_usage_percent',
        }),
      );
    });

    test('should use Histogram for duration metrics', () => {
      businessMetrics.recordDatabaseOperation('users', 'SELECT', 'success', 50);

      expect(mockPrometheusClient.Histogram).toHaveBeenCalledWith(
        expect.objectContaining({
          name: 'workhub_database_operation_duration_seconds',
        }),
      );
    });
  });
});
