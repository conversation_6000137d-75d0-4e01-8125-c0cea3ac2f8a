'use client';

import { Settings, Type, User } from 'lucide-react';
import React from 'react';

import {
  FontSizeSelector,
  FontSizeToggle,
} from '@/components/settings/FontSizeSettings';
import { ThemeToggle } from '@/components/theme-toggle';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useModal } from '@/lib/hooks/useModal';
import { useUiPreferences } from '@/hooks/ui/useUiPreferences';

/**
 * Application Header Component
 * Includes navigation, user controls, and quick settings
 */
export const AppHeader: React.FC = () => {
  const { fontSize, setFontSize } = useUiPreferences();
  const { openSettingsModal } = useModal();

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-14 items-center justify-between px-4">
        {/* Logo/Brand */}
        <div className="flex items-center gap-2">
          <h1 className="text-xl font-bold">WorkHub</h1>
        </div>

        {/* Navigation */}
        <nav className="hidden items-center space-x-6 md:flex">
          <a
            className="text-sm font-medium hover:text-primary"
            href="/dashboard"
          >
            Dashboard
          </a>
          <a
            className="text-sm font-medium hover:text-primary"
            href="/delegations"
          >
            Delegations
          </a>
          <a
            className="text-sm font-medium hover:text-primary"
            href="/employees"
          >
            Employees
          </a>
          <a
            className="text-sm font-medium hover:text-primary"
            href="/vehicles"
          >
            Vehicles
          </a>
        </nav>

        {/* Right Side Controls */}
        <div className="flex items-center gap-2">
          {/* Font Size Quick Toggle */}
          <FontSizeToggle />

          {/* Theme Toggle */}
          <ThemeToggle />

          {/* Settings Dropdown */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button size="sm" variant="ghost">
                <Settings className="size-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <DropdownMenuLabel>Quick Settings</DropdownMenuLabel>
              <DropdownMenuSeparator />

              {/* Font Size Controls */}
              <div className="px-2 py-1.5">
                <div className="mb-2 flex items-center justify-between">
                  <span className="text-sm font-medium">Font Size</span>
                  <Badge className="text-xs capitalize" variant="secondary">
                    {fontSize}
                  </Badge>
                </div>
                <div className="flex gap-1">
                  {(['small', 'medium', 'large'] as const).map(size => (
                    <Button
                      className="flex-1 text-xs capitalize"
                      key={size}
                      onClick={() => setFontSize(size)}
                      size="sm"
                      variant={fontSize === size ? 'default' : 'outline'}
                    >
                      {size}
                    </Button>
                  ))}
                </div>
              </div>

              <DropdownMenuSeparator />

              <DropdownMenuItem onClick={openSettingsModal}>
                <Settings className="mr-2 size-4" />
                All Settings
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          {/* User Menu */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button size="sm" variant="ghost">
                <User className="size-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>My Account</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem>Profile</DropdownMenuItem>
              <DropdownMenuItem>Preferences</DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem>Sign out</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  );
};

/**
 * Simple Header with Font Size Selector
 * Minimal version for specific pages
 */
export const SimpleHeader: React.FC<{ title: string }> = ({ title }) => {
  return (
    <header className="flex items-center justify-between border-b p-4">
      <h1 className="text-2xl font-bold">{title}</h1>
      <div className="flex items-center gap-4">
        <FontSizeSelector />
        <ThemeToggle />
      </div>
    </header>
  );
};
