// frontend/src/components/features/reporting/dashboard/index.ts

/**
 * Reporting Dashboard Export Index
 *
 * Centralized exports for the complete reporting dashboard system following DRY principles.
 * This module provides all components needed to implement a comprehensive reporting dashboard.
 */

// Main Dashboard Components
export { ReportingDashboard } from './ReportingDashboard';
export { ReportingOverview } from './ReportingOverview';

// Layout Components
export * from './layout';

// Widget Components
export * from './widgets';

// Filter Components
export * from './filters';

// Configuration
export {
  reportingDashboardConfig,
  widgetConfigurations,
  filterPresets,
} from './config/reportingDashboardConfig';

// ENHANCED: Phase 5 Advanced Features
export { WidgetConfigurationModal } from './components/WidgetConfigurationModal';

// Dashboard Customization Store
export * from './stores/useDashboardCustomizationStore';

// Types (re-export from data layer for convenience)
export type {
  ReportingFilters,
  DelegationAnalytics,
  TaskMetrics,
  TrendData,
  LocationMetrics,
  BaseWidgetProps,
  BaseChartProps,
} from '../data/types/reporting';
