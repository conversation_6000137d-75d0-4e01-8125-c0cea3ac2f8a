/**
 * @file Security Monitoring Hook - 2025 Standards
 * @module hooks/useSecurityMonitoring
 *
 * This hook provides real-time security monitoring capabilities following 2025 best practices.
 * It monitors for security events, violations, and suspicious activities.
 */

'use client';

import { useCallback, useEffect, useState } from 'react';
import { useAuthContext } from '../../../../contexts/AuthContext';
import { SecurityUtils, SessionManager } from '../../../security';

export interface SecurityEvent {
  id: string;
  type:
    | 'csp_violation'
    | 'auth_failure'
    | 'suspicious_activity'
    | 'rate_limit'
    | 'xss_attempt'
    | 'verification_loop'
    | 'circuit_breaker_triggered'
    | 'session_integrity_failure'
    | 'token_validation_failure'
    | 'concurrent_security_operation';
  severity: 'low' | 'medium' | 'high' | 'critical';
  timestamp: string;
  details: Record<string, any>;
  userAgent?: string;
  ip?: string;
}

export interface SecurityMetrics {
  totalEvents: number;
  criticalEvents: number;
  recentEvents: SecurityEvent[];
  threatLevel: 'low' | 'medium' | 'high' | 'critical';
}

export interface UseSecurityMonitoringReturn {
  metrics: SecurityMetrics;
  reportSecurityEvent: (event: Omit<SecurityEvent, 'id' | 'timestamp'>) => void;
  clearEvents: () => void;
  isMonitoring: boolean;
  startMonitoring: () => void;
  stopMonitoring: () => void;
}

/**
 * Security Monitoring Hook - Real-time Security Event Tracking
 *
 * Provides comprehensive security monitoring for the application.
 * Follows 2025 security monitoring best practices.
 */
export function useSecurityMonitoring(): UseSecurityMonitoringReturn {
  const { user } = useAuthContext();
  const [events, setEvents] = useState<SecurityEvent[]>([]);
  const [isMonitoring, setIsMonitoring] = useState(false);

  /**
   * Generate unique event ID
   */
  const generateEventId = useCallback((): string => {
    return `sec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }, []);

  /**
   * Calculate threat level based on recent events with enhanced verification loop detection
   */
  const calculateThreatLevel = useCallback(
    (events: SecurityEvent[]): SecurityMetrics['threatLevel'] => {
      const recentEvents = events.filter(
        event =>
          Date.now() - new Date(event.timestamp).getTime() < 5 * 60 * 1000 // Last 5 minutes
      );

      const criticalCount = recentEvents.filter(
        e => e.severity === 'critical'
      ).length;
      const highCount = recentEvents.filter(e => e.severity === 'high').length;
      const mediumCount = recentEvents.filter(
        e => e.severity === 'medium'
      ).length;

      // Enhanced threat detection for verification loops
      const verificationLoopEvents = recentEvents.filter(
        e =>
          e.type === 'verification_loop' ||
          e.type === 'circuit_breaker_triggered'
      ).length;

      const sessionIntegrityEvents = recentEvents.filter(
        e => e.type === 'session_integrity_failure'
      ).length;

      const concurrentOpEvents = recentEvents.filter(
        e => e.type === 'concurrent_security_operation'
      ).length;

      // Critical threat conditions
      if (criticalCount > 0 || verificationLoopEvents > 0) return 'critical';

      // High threat conditions
      if (highCount >= 3 || (highCount >= 1 && sessionIntegrityEvents > 2))
        return 'high';

      // Medium threat conditions
      if (
        highCount >= 1 ||
        mediumCount >= 5 ||
        sessionIntegrityEvents > 1 ||
        concurrentOpEvents > 3
      )
        return 'medium';

      return 'low';
    },
    []
  );

  /**
   * Report a security event
   */
  const reportSecurityEvent = useCallback(
    (eventData: Omit<SecurityEvent, 'id' | 'timestamp'>) => {
      const event: SecurityEvent = {
        ...eventData,
        id: generateEventId(),
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
      };

      setEvents(prev => {
        const updated = [event, ...prev].slice(0, 100); // Keep last 100 events

        // Log critical events immediately
        if (event.severity === 'critical') {
          console.error('🚨 CRITICAL SECURITY EVENT:', event);
        } else if (event.severity === 'high') {
          console.warn('⚠️ HIGH SECURITY EVENT:', event);
        }

        return updated;
      });

      // Send to backend in production
      if (process.env.NODE_ENV === 'production') {
        fetch('/api/security/events', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(event),
        }).catch(error => {
          console.error('Failed to report security event:', error);
        });
      }
    },
    [generateEventId]
  );

  /**
   * Monitor for CSP violations
   */
  const monitorCSPViolations = useCallback(() => {
    const handleCSPViolation = (event: SecurityPolicyViolationEvent) => {
      reportSecurityEvent({
        type: 'csp_violation',
        severity:
          event.effectiveDirective === 'script-src' ? 'critical' : 'high',
        details: {
          violatedDirective: event.violatedDirective,
          effectiveDirective: event.effectiveDirective,
          blockedURI: event.blockedURI,
          sourceFile: event.sourceFile,
          lineNumber: event.lineNumber,
          columnNumber: event.columnNumber,
          originalPolicy: event.originalPolicy,
        },
      });
    };

    document.addEventListener('securitypolicyviolation', handleCSPViolation);

    return () => {
      document.removeEventListener(
        'securitypolicyviolation',
        handleCSPViolation
      );
    };
  }, [reportSecurityEvent]);

  /**
   * Monitor for suspicious DOM manipulation
   */
  const monitorDOMChanges = useCallback(() => {
    const observer = new MutationObserver(mutations => {
      mutations.forEach(mutation => {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach(node => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const element = node as Element;

              // Check for suspicious script injections
              if (element.tagName === 'SCRIPT') {
                const src = element.getAttribute('src');
                const content = element.textContent;

                if (
                  src &&
                  !src.startsWith(window.location.origin) &&
                  !src.startsWith('https://')
                ) {
                  reportSecurityEvent({
                    type: 'xss_attempt',
                    severity: 'critical',
                    details: {
                      type: 'script_injection',
                      src,
                      content: content?.substring(0, 100),
                    },
                  });
                }
              }

              // Check for suspicious iframe injections
              if (element.tagName === 'IFRAME') {
                const src = element.getAttribute('src');
                if (src && !src.startsWith(window.location.origin)) {
                  reportSecurityEvent({
                    type: 'xss_attempt',
                    severity: 'high',
                    details: {
                      type: 'iframe_injection',
                      src,
                    },
                  });
                }
              }
            }
          });
        }
      });
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
    });

    return () => observer.disconnect();
  }, [reportSecurityEvent]);

  /**
   * Monitor for authentication failures
   */
  const monitorAuthFailures = useCallback(() => {
    let failureCount = 0;
    const maxFailures = 5;
    const timeWindow = 5 * 60 * 1000; // 5 minutes

    const handleAuthFailure = () => {
      failureCount++;

      if (failureCount >= maxFailures) {
        reportSecurityEvent({
          type: 'auth_failure',
          severity: 'high',
          details: {
            failureCount,
            timeWindow: timeWindow / 1000,
            userId: user?.id,
          },
        });
      }

      // Reset counter after time window
      setTimeout(() => {
        failureCount = Math.max(0, failureCount - 1);
      }, timeWindow);
    };

    // Listen for auth failure events
    window.addEventListener('auth:failure', handleAuthFailure);

    return () => {
      window.removeEventListener('auth:failure', handleAuthFailure);
    };
  }, [reportSecurityEvent, user?.id]);

  /**
   * Monitor for verification loop detection
   */
  const monitorVerificationLoops = useCallback(() => {
    let lastCircuitBreakerCheck = 0;
    const checkInterval = 30000; // Check every 30 seconds

    const checkVerificationLoops = () => {
      const now = Date.now();
      if (now - lastCircuitBreakerCheck < checkInterval) return;

      lastCircuitBreakerCheck = now;

      try {
        // Check circuit breaker state
        const circuitBreakerState = SecurityUtils.getCircuitBreakerState();

        if (circuitBreakerState.isOpen) {
          reportSecurityEvent({
            type: 'circuit_breaker_triggered',
            severity: 'high',
            details: {
              attemptCount: circuitBreakerState.attemptCount,
              lastAttempt: circuitBreakerState.lastAttempt,
              isOpen: circuitBreakerState.isOpen,
              reason: 'Too many security verification attempts',
            },
          });
        }

        // Check for excessive security attempts
        if (circuitBreakerState.attemptCount > 10) {
          reportSecurityEvent({
            type: 'verification_loop',
            severity: 'critical',
            details: {
              attemptCount: circuitBreakerState.attemptCount,
              timeWindow: '5 minutes',
              pattern: 'Potential verification loop detected',
            },
          });
        }

        // Check session integrity
        const sessionState = SessionManager.getSessionState();
        if (sessionState && !SessionManager.validateSessionConsistency()) {
          reportSecurityEvent({
            type: 'session_integrity_failure',
            severity: 'medium',
            details: {
              sessionId: sessionState.sessionId,
              lastActivity: sessionState.lastActivity,
              isActive: sessionState.isActive,
            },
          });
        }
      } catch (error) {
        console.error('Error monitoring verification loops:', error);
      }
    };

    // Initial check
    checkVerificationLoops();

    // Set up interval monitoring
    const intervalId = setInterval(checkVerificationLoops, checkInterval);

    return () => {
      clearInterval(intervalId);
    };
  }, [reportSecurityEvent]);

  /**
   * Monitor for concurrent security operations
   */
  const monitorConcurrentOperations = useCallback(() => {
    let operationCount = 0;
    const maxConcurrentOps = 3;

    const handleSecurityOperationStart = () => {
      operationCount++;

      if (operationCount > maxConcurrentOps) {
        reportSecurityEvent({
          type: 'concurrent_security_operation',
          severity: 'medium',
          details: {
            operationCount,
            maxAllowed: maxConcurrentOps,
            timestamp: new Date().toISOString(),
          },
        });
      }
    };

    const handleSecurityOperationEnd = () => {
      operationCount = Math.max(0, operationCount - 1);
    };

    // Listen for security operation events
    window.addEventListener(
      'security:operation:start',
      handleSecurityOperationStart
    );
    window.addEventListener(
      'security:operation:end',
      handleSecurityOperationEnd
    );

    return () => {
      window.removeEventListener(
        'security:operation:start',
        handleSecurityOperationStart
      );
      window.removeEventListener(
        'security:operation:end',
        handleSecurityOperationEnd
      );
    };
  }, [reportSecurityEvent]);

  /**
   * Start security monitoring with enhanced verification loop detection
   */
  const startMonitoring = useCallback(() => {
    if (isMonitoring) return;

    setIsMonitoring(true);

    console.log(
      '🔍 Starting enhanced security monitoring with verification loop detection'
    );

    const cleanupFunctions = [
      monitorCSPViolations(),
      monitorDOMChanges(),
      monitorAuthFailures(),
      monitorVerificationLoops(),
      monitorConcurrentOperations(),
    ];

    // Store cleanup functions for later use
    (window as any).__securityCleanup = cleanupFunctions;

    // Report monitoring start event
    reportSecurityEvent({
      type: 'suspicious_activity',
      severity: 'low',
      details: {
        action: 'security_monitoring_started',
        features: [
          'csp_violations',
          'dom_changes',
          'auth_failures',
          'verification_loops',
          'concurrent_operations',
        ],
      },
    });
  }, [
    isMonitoring,
    monitorCSPViolations,
    monitorDOMChanges,
    monitorAuthFailures,
    monitorVerificationLoops,
    monitorConcurrentOperations,
    reportSecurityEvent,
  ]);

  /**
   * Stop security monitoring
   */
  const stopMonitoring = useCallback(() => {
    if (!isMonitoring) return;

    setIsMonitoring(false);

    const cleanupFunctions = (window as any).__securityCleanup;
    if (cleanupFunctions) {
      cleanupFunctions.forEach((cleanup: () => void) => cleanup());
      delete (window as any).__securityCleanup;
    }
  }, [isMonitoring]);

  /**
   * Clear all events
   */
  const clearEvents = useCallback(() => {
    setEvents([]);
  }, []);

  /**
   * Calculate metrics
   */
  const metrics: SecurityMetrics = {
    totalEvents: events.length,
    criticalEvents: events.filter(e => e.severity === 'critical').length,
    recentEvents: events.slice(0, 10),
    threatLevel: calculateThreatLevel(events),
  };

  /**
   * Auto-start monitoring on mount
   */
  useEffect(() => {
    startMonitoring();

    return () => {
      stopMonitoring();
    };
  }, [startMonitoring, stopMonitoring]);

  return {
    metrics,
    reportSecurityEvent,
    clearEvents,
    isMonitoring,
    startMonitoring,
    stopMonitoring,
  };
}
