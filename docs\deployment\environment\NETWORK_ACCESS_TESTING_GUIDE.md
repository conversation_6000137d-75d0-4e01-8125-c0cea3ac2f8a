# Network Access Testing Guide

## 🎯 Overview

This guide provides comprehensive testing procedures for validating cross-origin authentication and WebSocket connectivity when accessing the WorkHub application from different network locations.

## 🔧 Pre-Testing Setup

### 1. Environment Configuration

**For Network Access (**************:9002):**
```bash
# Copy the network environment configuration
cp frontend/.env.network frontend/.env.local

# Or use the environment switcher script
node frontend/scripts/switch-env.js network
```

### 2. Backend Verification

Ensure your backend `.env` file includes the network origin:
```env
FRONTEND_URL=http://localhost:9002,http://localhost:3000,...,http://**************:9002
```

### 3. Service Restart

```bash
# Restart backend service
cd backend && npm run dev

# Restart frontend service  
cd frontend && npm run dev
```

## 🧪 Testing Procedures

### Test 1: Basic Connectivity

**Objective:** Verify basic application loading and API connectivity

**Steps:**
1. Access `http://**************:9002` in browser
2. Open browser DevTools (F12) → Network tab
3. Refresh the page
4. Check for successful API calls to `**************:3001`

**Expected Results:**
- ✅ Application loads without errors
- ✅ API calls target `**************:3001` (not localhost)
- ✅ No CORS errors in console

**Failure Indicators:**
- ❌ API calls still target `localhost:3001`
- ❌ CORS errors in console
- ❌ Network errors or timeouts

### Test 2: Authentication Flow

**Objective:** Verify authentication works across origins

**Steps:**
1. Navigate to login page
2. Enter valid credentials
3. Monitor network requests during login
4. Verify successful authentication

**Expected Results:**
- ✅ Login request goes to `**************:3001/api/auth/login`
- ✅ Authentication succeeds
- ✅ JWT token is properly stored
- ✅ User is redirected to dashboard

**Failure Indicators:**
- ❌ Authentication requests fail
- ❌ Token refresh errors
- ❌ Redirect loops or authentication errors

### Test 3: WebSocket Connection

**Objective:** Verify WebSocket authentication and connectivity

**Steps:**
1. Login successfully
2. Navigate to a page that uses WebSocket (dashboard/real-time features)
3. Open DevTools → Console
4. Look for WebSocket connection logs

**Expected Results:**
- ✅ WebSocket connects to `ws://**************:3001`
- ✅ Authentication successful message
- ✅ Real-time updates work properly
- ✅ No authentication errors in console

**Failure Indicators:**
- ❌ WebSocket connection errors
- ❌ "Authentication error: Invalid token" messages
- ❌ Token refresh failures
- ❌ WebSocket disconnections

### Test 4: Token Refresh

**Objective:** Verify automatic token refresh works across origins

**Steps:**
1. Login and stay on the application
2. Wait for token to approach expiration (or manually trigger refresh)
3. Monitor console for token refresh attempts
4. Verify continued functionality

**Expected Results:**
- ✅ Token refresh requests go to `**************:3001/api/auth/refresh`
- ✅ Refresh succeeds without user intervention
- ✅ WebSocket reconnects with new token
- ✅ Application continues working seamlessly

**Failure Indicators:**
- ❌ Token refresh requests fail (401 errors)
- ❌ User gets logged out unexpectedly
- ❌ WebSocket authentication errors after refresh

## 🔍 Debugging Common Issues

### Issue 1: API Calls Still Target Localhost

**Symptoms:**
- Network tab shows requests to `localhost:3001`
- CORS errors in console

**Solutions:**
1. Verify `.env.local` has correct configuration
2. Restart development server
3. Clear browser cache and cookies
4. Check environment configuration is loaded:
   ```javascript
   // In browser console
   console.log(process.env.NEXT_PUBLIC_API_BASE_URL);
   ```

### Issue 2: WebSocket Authentication Failures

**Symptoms:**
- "Authentication error: Invalid token" in console
- WebSocket connection errors
- Token refresh failures

**Solutions:**
1. Verify backend CORS includes your IP
2. Check WebSocket URL configuration
3. Clear browser storage and re-login
4. Verify token is being passed correctly

### Issue 3: CORS Errors

**Symptoms:**
- "Access to fetch at ... has been blocked by CORS policy"
- Network requests fail with CORS errors

**Solutions:**
1. Add your IP to backend `FRONTEND_URL` environment variable
2. Restart backend service
3. Verify CORS middleware configuration
4. Check for typos in IP addresses

## 📊 Validation Checklist

- [ ] Application loads from network IP
- [ ] API calls target correct backend IP
- [ ] Authentication flow works completely
- [ ] WebSocket connects and authenticates
- [ ] Token refresh works automatically
- [ ] Real-time features function properly
- [ ] No CORS errors in console
- [ ] No authentication errors in logs

## 🚀 Production Deployment Considerations

### Environment Variables
- Set `NEXT_PUBLIC_API_BASE_URL` to production backend URL
- Configure `NEXT_PUBLIC_WS_URL` for production WebSocket
- Update CORS configuration for production domains

### Security
- Use HTTPS/WSS in production
- Implement proper SSL certificates
- Configure secure cookie settings
- Enable production security headers

### Monitoring
- Set up logging for authentication failures
- Monitor WebSocket connection health
- Track token refresh success rates
- Alert on CORS policy violations
