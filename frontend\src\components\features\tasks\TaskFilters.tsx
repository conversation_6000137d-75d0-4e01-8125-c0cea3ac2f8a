/**
 * @file Modern task filters component aligned with design system
 * @module components/tasks/TaskFilters
 */

'use client';

import {
  Filter,
  X,
  Search,
  Calendar,
  CalendarDays,
  User,
  Flag,
  CheckCircle,
  Clock,
  AlertCircle,
  XCircle,
  Users,
} from 'lucide-react';
import React, { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/sheet';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
  PopoverClose,
} from '@/components/ui/popover';
import { Checkbox } from '@/components/ui/checkbox';
import { Calendar as CalendarComponent } from '@/components/ui/calendar';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';
import type { DateRange } from 'react-day-picker';

// Task status options with icons and colors
const TASK_STATUS_OPTIONS = [
  {
    value: 'Pending',
    label: 'Pending',
    icon: Clock,
    color:
      'text-amber-600 bg-amber-50 border-amber-200 dark:text-amber-400 dark:bg-amber-900/20 dark:border-amber-800',
  },
  {
    value: 'Assigned',
    label: 'Assigned',
    icon: User,
    color:
      'text-blue-600 bg-blue-50 border-blue-200 dark:text-blue-400 dark:bg-blue-900/20 dark:border-blue-800',
  },
  {
    value: 'In_Progress',
    label: 'In Progress',
    icon: AlertCircle,
    color:
      'text-purple-600 bg-purple-50 border-purple-200 dark:text-purple-400 dark:bg-purple-900/20 dark:border-purple-800',
  },
  {
    value: 'Completed',
    label: 'Completed',
    icon: CheckCircle,
    color:
      'text-green-600 bg-green-50 border-green-200 dark:text-green-400 dark:bg-green-900/20 dark:border-green-800',
  },
  {
    value: 'Cancelled',
    label: 'Cancelled',
    icon: XCircle,
    color:
      'text-red-600 bg-red-50 border-red-200 dark:text-red-400 dark:bg-red-900/20 dark:border-red-800',
  },
];

// Task priority options
const TASK_PRIORITY_OPTIONS = [
  {
    value: 'Low',
    label: 'Low Priority',
    color:
      'text-green-600 bg-green-50 border-green-200 dark:text-green-400 dark:bg-green-900/20 dark:border-green-800',
  },
  {
    value: 'Medium',
    label: 'Medium Priority',
    color:
      'text-amber-600 bg-amber-50 border-amber-200 dark:text-amber-400 dark:bg-amber-900/20 dark:border-amber-800',
  },
  {
    value: 'High',
    label: 'High Priority',
    color:
      'text-red-600 bg-red-50 border-red-200 dark:text-red-400 dark:bg-red-900/20 dark:border-red-800',
  },
];

// Employee interface for filtering
interface EmployeeOption {
  id: string;
  name: string;
  role: string;
}

// TaskFilters props interface
interface TaskFiltersProps {
  onFiltersChange?: (filters: TaskFilterValues) => void;
  className?: string;
  initialFilters?: Partial<TaskFilterValues>;
  employeesList?: EmployeeOption[];
}

export interface TaskFilterValues {
  search: string;
  status: string[];
  priority: string[];
  assignee: string[];
  dateRange: {
    from?: Date | undefined;
    to?: Date | undefined;
  };
}

/**
 * Modern task filters component with popover and sheet layouts
 */
export const TaskFilters: React.FC<TaskFiltersProps> = ({
  onFiltersChange,
  className,
  initialFilters = {},
  employeesList = [],
}) => {
  const [filters, setFilters] = useState<TaskFilterValues>({
    search: '',
    status: [],
    priority: [],
    assignee: [],
    dateRange: {},
    ...initialFilters,
  });

  const [isSheetOpen, setIsSheetOpen] = useState(false);

  // Update filters and notify parent
  const updateFilters = (newFilters: Partial<TaskFilterValues>) => {
    const updatedFilters = { ...filters, ...newFilters };
    setFilters(updatedFilters);
    onFiltersChange?.(updatedFilters);
  };

  // Clear all filters
  const clearAllFilters = () => {
    const emptyFilters: TaskFilterValues = {
      search: '',
      status: [],
      priority: [],
      assignee: [],
      dateRange: {},
    };
    setFilters(emptyFilters);
    onFiltersChange?.(emptyFilters);
  };

  // Toggle status filter
  const toggleStatus = (status: string) => {
    const newStatus = filters.status.includes(status)
      ? filters.status.filter(s => s !== status)
      : [...filters.status, status];
    updateFilters({ status: newStatus });
  };

  // Toggle priority filter
  const togglePriority = (priority: string) => {
    const newPriority = filters.priority.includes(priority)
      ? filters.priority.filter(p => p !== priority)
      : [...filters.priority, priority];
    updateFilters({ priority: newPriority });
  };

  // Toggle assignee filter
  const toggleAssignee = (assigneeId: string) => {
    const newAssignee = filters.assignee.includes(assigneeId)
      ? filters.assignee.filter(a => a !== assigneeId)
      : [...filters.assignee, assigneeId];
    updateFilters({ assignee: newAssignee });
  };

  // Handle date range selection
  const handleDateRangeSelect = (range: DateRange | undefined) => {
    updateFilters({
      dateRange: {
        from: range?.from ?? undefined,
        to: range?.to ?? undefined,
      },
    });
  };

  // Count active filters
  const activeFiltersCount =
    (filters.search ? 1 : 0) +
    filters.status.length +
    filters.priority.length +
    filters.assignee.length +
    (filters.dateRange.from || filters.dateRange.to ? 1 : 0);

  // Render status filter popover
  const StatusFilterPopover = () => (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="outline" className="gap-2">
          <CheckCircle className="size-4" />
          Status
          {filters.status.length > 0 && (
            <Badge
              variant="secondary"
              className="ml-1 h-5 min-w-5 px-1.5 text-xs"
            >
              {filters.status.length}
            </Badge>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-56 p-3" align="start">
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h4 className="font-medium text-sm">Task Status</h4>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => updateFilters({ status: [] })}
              className="h-auto p-1 text-xs"
            >
              Clear
            </Button>
          </div>
          <Separator />
          <div className="space-y-2">
            {TASK_STATUS_OPTIONS.map(option => {
              const IconComponent = option.icon;
              return (
                <div key={option.value} className="flex items-center gap-2">
                  <Checkbox
                    id={`status-${option.value}`}
                    checked={filters.status.includes(option.value)}
                    onCheckedChange={() => toggleStatus(option.value)}
                  />
                  <Label
                    htmlFor={`status-${option.value}`}
                    className="flex items-center gap-2 cursor-pointer text-sm flex-1"
                  >
                    <IconComponent className="size-3" />
                    <Badge
                      variant="outline"
                      className={cn('text-xs border', option.color)}
                    >
                      {option.label}
                    </Badge>
                  </Label>
                </div>
              );
            })}
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );

  // Render priority filter popover
  const PriorityFilterPopover = () => (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="outline" className="gap-2">
          <Flag className="size-4" />
          Priority
          {filters.priority.length > 0 && (
            <Badge
              variant="secondary"
              className="ml-1 h-5 min-w-5 px-1.5 text-xs"
            >
              {filters.priority.length}
            </Badge>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-48 p-3" align="start">
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h4 className="font-medium text-sm">Priority Level</h4>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => updateFilters({ priority: [] })}
              className="h-auto p-1 text-xs"
            >
              Clear
            </Button>
          </div>
          <Separator />
          <div className="space-y-2">
            {TASK_PRIORITY_OPTIONS.map(option => (
              <div key={option.value} className="flex items-center gap-2">
                <Checkbox
                  id={`priority-${option.value}`}
                  checked={filters.priority.includes(option.value)}
                  onCheckedChange={() => togglePriority(option.value)}
                />
                <Label
                  htmlFor={`priority-${option.value}`}
                  className="flex items-center gap-2 cursor-pointer text-sm flex-1"
                >
                  <Badge
                    variant="outline"
                    className={cn('text-xs border', option.color)}
                  >
                    {option.label}
                  </Badge>
                </Label>
              </div>
            ))}
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );

  // Render assignee filter popover
  const AssigneeFilterPopover = () => (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="outline" className="gap-2">
          <Users className="size-4" />
          Assignee
          {filters.assignee.length > 0 && (
            <Badge
              variant="secondary"
              className="ml-1 h-5 min-w-5 px-1.5 text-xs"
            >
              {filters.assignee.length}
            </Badge>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-64 p-3" align="start">
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h4 className="font-medium text-sm">Assigned Employee</h4>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => updateFilters({ assignee: [] })}
              className="h-auto p-1 text-xs"
            >
              Clear
            </Button>
          </div>
          <Separator />
          <div className="space-y-2 max-h-48 overflow-y-auto">
            {/* Unassigned option */}
            <div className="flex items-center gap-2">
              <Checkbox
                id="assignee-unassigned"
                checked={filters.assignee.includes('unassigned')}
                onCheckedChange={() => toggleAssignee('unassigned')}
              />
              <Label
                htmlFor="assignee-unassigned"
                className="flex items-center gap-2 cursor-pointer text-sm flex-1"
              >
                <User className="size-3 text-muted-foreground" />
                <span className="text-muted-foreground">Unassigned</span>
              </Label>
            </div>

            {/* Employee options */}
            {employeesList?.map(employee => (
              <div key={employee.id} className="flex items-center gap-2">
                <Checkbox
                  id={`assignee-${employee.id}`}
                  checked={filters.assignee.includes(employee.id)}
                  onCheckedChange={() => toggleAssignee(employee.id)}
                />
                <Label
                  htmlFor={`assignee-${employee.id}`}
                  className="flex items-center gap-2 cursor-pointer text-sm flex-1"
                >
                  <User className="size-3" />
                  <div className="flex flex-col">
                    <span>{employee.name}</span>
                    <span className="text-xs text-muted-foreground">
                      {employee.role}
                    </span>
                  </div>
                </Label>
              </div>
            ))}
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );

  // Render date range filter popover
  const DateRangeFilterPopover = () => {
    return (
      <Popover>
        <PopoverTrigger asChild>
          <Button variant="outline" className="gap-2">
            <CalendarDays className="size-4" />
            Date Range
            {(filters.dateRange.from || filters.dateRange.to) && (
              <Badge
                variant="secondary"
                className="ml-1 h-5 min-w-5 px-1.5 text-xs"
              >
                1
              </Badge>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <div className="p-3">
            <div className="flex items-center justify-between mb-3">
              <h4 className="font-medium text-sm">Task Date Range</h4>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => updateFilters({ dateRange: {} })}
                className="h-auto p-1 text-xs"
              >
                Clear
              </Button>
            </div>
            <CalendarComponent
              mode="range"
              selected={{
                from: filters.dateRange.from,
                to: filters.dateRange.to,
              }}
              onSelect={handleDateRangeSelect}
              numberOfMonths={2}
              className="rounded-md border-0"
            />
            {/* Helper text */}
            <div className="mt-3 text-xs text-muted-foreground text-center">
              {filters.dateRange.from && !filters.dateRange.to
                ? 'Select end date to complete range'
                : 'Click start date, then end date'}
            </div>
          </div>
        </PopoverContent>
      </Popover>
    );
  };

  return (
    <div className={cn('flex flex-col gap-4', className)}>
      {/* Search Bar */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 size-4 -translate-y-1/2 text-muted-foreground" />
        <Input
          placeholder="Search tasks..."
          value={filters.search}
          onChange={e => updateFilters({ search: e.target.value })}
          className="pl-10"
        />
      </div>

      {/* Filter Controls */}
      <div className="flex flex-wrap items-center gap-3">
        {/* Desktop Filter Buttons */}
        <div className="hidden md:flex items-center gap-2">
          <StatusFilterPopover />
          <PriorityFilterPopover />
          {employeesList && employeesList.length > 0 && (
            <AssigneeFilterPopover />
          )}
          <DateRangeFilterPopover />
        </div>

        {/* Mobile Filter Sheet */}
        <div className="md:hidden">
          <Sheet open={isSheetOpen} onOpenChange={setIsSheetOpen}>
            <SheetTrigger asChild>
              <Button variant="outline" className="gap-2">
                <Filter className="size-4" />
                Filters
                {activeFiltersCount > 0 && (
                  <Badge
                    variant="secondary"
                    className="ml-1 h-5 min-w-5 px-1.5 text-xs"
                  >
                    {activeFiltersCount}
                  </Badge>
                )}
              </Button>
            </SheetTrigger>
            <SheetContent side="bottom" className="h-[80vh]">
              <SheetHeader>
                <SheetTitle>Filter Tasks</SheetTitle>
                <SheetDescription>
                  Refine your task list with advanced filters
                </SheetDescription>
              </SheetHeader>
              <div className="grid gap-6 py-6">
                {/* Mobile Status Filter */}
                <div className="space-y-3">
                  <Label className="text-sm font-medium">Status</Label>
                  <div className="grid grid-cols-2 gap-2">
                    {TASK_STATUS_OPTIONS.map(option => {
                      const IconComponent = option.icon;
                      return (
                        <div
                          key={option.value}
                          className="flex items-center gap-2 p-2 border rounded-md"
                        >
                          <Checkbox
                            id={`mobile-status-${option.value}`}
                            checked={filters.status.includes(option.value)}
                            onCheckedChange={() => toggleStatus(option.value)}
                          />
                          <Label
                            htmlFor={`mobile-status-${option.value}`}
                            className="flex items-center gap-1 cursor-pointer text-xs flex-1"
                          >
                            <IconComponent className="size-3" />
                            {option.label}
                          </Label>
                        </div>
                      );
                    })}
                  </div>
                </div>

                {/* Mobile Priority Filter */}
                <div className="space-y-3">
                  <Label className="text-sm font-medium">Priority</Label>
                  <div className="grid gap-2">
                    {TASK_PRIORITY_OPTIONS.map(option => (
                      <div
                        key={option.value}
                        className="flex items-center gap-2 p-2 border rounded-md"
                      >
                        <Checkbox
                          id={`mobile-priority-${option.value}`}
                          checked={filters.priority.includes(option.value)}
                          onCheckedChange={() => togglePriority(option.value)}
                        />
                        <Label
                          htmlFor={`mobile-priority-${option.value}`}
                          className="cursor-pointer text-sm flex-1"
                        >
                          {option.label}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Mobile Assignee Filter */}
                {employeesList && employeesList.length > 0 && (
                  <div className="space-y-3">
                    <Label className="text-sm font-medium">Assignee</Label>
                    <div className="grid gap-2 max-h-48 overflow-y-auto">
                      {/* Unassigned option */}
                      <div className="flex items-center gap-2 p-2 border rounded-md">
                        <Checkbox
                          id="mobile-assignee-unassigned"
                          checked={filters.assignee.includes('unassigned')}
                          onCheckedChange={() => toggleAssignee('unassigned')}
                        />
                        <Label
                          htmlFor="mobile-assignee-unassigned"
                          className="cursor-pointer text-sm flex-1"
                        >
                          Unassigned
                        </Label>
                      </div>

                      {/* Employee options */}
                      {employeesList.map(employee => (
                        <div
                          key={employee.id}
                          className="flex items-center gap-2 p-2 border rounded-md"
                        >
                          <Checkbox
                            id={`mobile-assignee-${employee.id}`}
                            checked={filters.assignee.includes(employee.id)}
                            onCheckedChange={() => toggleAssignee(employee.id)}
                          />
                          <Label
                            htmlFor={`mobile-assignee-${employee.id}`}
                            className="cursor-pointer text-sm flex-1"
                          >
                            <div>
                              <div>{employee.name}</div>
                              <div className="text-xs text-muted-foreground">
                                {employee.role}
                              </div>
                            </div>
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Mobile Date Range Filter */}
                <div className="space-y-3">
                  <Label className="text-sm font-medium">Date Range</Label>
                  <div className="border rounded-md p-3">
                    <CalendarComponent
                      mode="range"
                      selected={{
                        from: filters.dateRange.from,
                        to: filters.dateRange.to,
                      }}
                      onSelect={handleDateRangeSelect}
                      numberOfMonths={1}
                      className="rounded-md border-0"
                    />
                  </div>
                </div>

                {/* Clear Filters Button */}
                <Button
                  variant="outline"
                  onClick={clearAllFilters}
                  className="w-full"
                >
                  Clear All Filters
                </Button>
              </div>
            </SheetContent>
          </Sheet>
        </div>

        {/* Clear All Filters (Desktop) */}
        {activeFiltersCount > 0 && (
          <Button
            variant="ghost"
            size="sm"
            onClick={clearAllFilters}
            className="gap-1 text-muted-foreground hover:text-foreground hidden md:flex"
          >
            <X className="size-3" />
            Clear ({activeFiltersCount})
          </Button>
        )}
      </div>

      {/* Active Filters Display */}
      {activeFiltersCount > 0 && (
        <div className="flex flex-wrap gap-2">
          {filters.search && (
            <Badge variant="secondary" className="gap-1">
              Search: "{filters.search}"
              <Button
                variant="ghost"
                size="sm"
                className="h-auto p-0 ml-1 hover:bg-transparent"
                onClick={() => updateFilters({ search: '' })}
              >
                <X className="size-3" />
              </Button>
            </Badge>
          )}

          {filters.status.map(status => {
            const option = TASK_STATUS_OPTIONS.find(
              opt => opt.value === status
            );
            if (!option) return null;
            return (
              <Badge key={status} variant="secondary" className="gap-1">
                {option.label}
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-auto p-0 ml-1 hover:bg-transparent"
                  onClick={() => toggleStatus(status)}
                >
                  <X className="size-3" />
                </Button>
              </Badge>
            );
          })}

          {filters.priority.map(priority => {
            const option = TASK_PRIORITY_OPTIONS.find(
              opt => opt.value === priority
            );
            if (!option) return null;
            return (
              <Badge key={priority} variant="secondary" className="gap-1">
                {option.label}
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-auto p-0 ml-1 hover:bg-transparent"
                  onClick={() => togglePriority(priority)}
                >
                  <X className="size-3" />
                </Button>
              </Badge>
            );
          })}

          {filters.assignee.map(assigneeId => {
            const employee = employeesList?.find(emp => emp.id === assigneeId);
            const displayName =
              assigneeId === 'unassigned'
                ? 'Unassigned'
                : employee?.name || 'Unknown';
            return (
              <Badge key={assigneeId} variant="secondary" className="gap-1">
                Assignee: {displayName}
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-auto p-0 ml-1 hover:bg-transparent"
                  onClick={() => toggleAssignee(assigneeId)}
                >
                  <X className="size-3" />
                </Button>
              </Badge>
            );
          })}

          {(filters.dateRange.from || filters.dateRange.to) && (
            <Badge variant="secondary" className="gap-1">
              Date:{' '}
              {filters.dateRange.from
                ? format(filters.dateRange.from, 'MMM d')
                : '?'}{' '}
              -{' '}
              {filters.dateRange.to
                ? format(filters.dateRange.to, 'MMM d, yyyy')
                : '?'}
              <Button
                variant="ghost"
                size="sm"
                className="h-auto p-0 ml-1 hover:bg-transparent"
                onClick={() => updateFilters({ dateRange: {} })}
              >
                <X className="size-3" />
              </Button>
            </Badge>
          )}
        </div>
      )}
    </div>
  );
};

// Export as default to maintain compatibility
export default TaskFilters;
