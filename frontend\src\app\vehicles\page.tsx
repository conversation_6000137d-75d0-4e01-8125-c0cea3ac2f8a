'use client';

import { Car, PlusCircle, RefreshCw, Search } from 'lucide-react';
import Link from 'next/link';
import React, { useMemo, useState } from 'react';

import type { Vehicle } from '@/lib/types/domain';

import ErrorBoundary from '@/components/error-boundaries/ErrorBoundary';
import VehicleCard from '@/components/features/vehicles/VehicleCard';
import VehicleListContainer from '@/components/features/vehicles/VehicleList';
import { ActionButton } from '@/components/ui/action-button';
import { AppBreadcrumb } from '@/components/ui/app-breadcrumb';
import { Input } from '@/components/ui/input';
import { DataLoader, SkeletonLoader } from '@/components/ui/loading';
import { PageHeader } from '@/components/ui/PageHeader';
// Removed useToast as it's not used for displaying toasts in this component

// Define the type for the render prop arguments
interface VehicleListData {
  error: null | string;
  fetchVehicles: () => void;
  handleDelete: (id: number) => Promise<void>;
  isRefreshing: boolean;
  loading: boolean;
  vehicles: Vehicle[];
}

// Main page component content
const VehiclesPageContent = () => {
  const [searchTerm, setSearchTerm] = useState('');
  // Removed const { toast } = useToast(); as it's not used

  return (
    <VehicleListContainer>
      {({
        error,
        fetchVehicles,
        handleDelete,
        isRefreshing,
        loading,
        vehicles,
      }: VehicleListData) => {
        // Move filtering logic outside the render prop, but still within the component
        const filteredVehicles = useMemo(() => {
          if (loading || error) {
            return [];
          }

          const lowercasedFilter = searchTerm.toLowerCase();
          return vehicles.filter(vehicle => {
            return (
              vehicle.make.toLowerCase().includes(lowercasedFilter) ||
              vehicle.model.toLowerCase().includes(lowercasedFilter) ||
              vehicle.year.toString().includes(lowercasedFilter) ||
              vehicle.licensePlate?.toLowerCase().includes(lowercasedFilter) ||
              vehicle.vin?.toLowerCase().includes(lowercasedFilter) ||
              vehicle.ownerName?.toLowerCase().includes(lowercasedFilter)
            );
          });
        }, [vehicles, searchTerm, loading, error]);

        return (
          <div className="space-y-6">
            <AppBreadcrumb homeHref="/" homeLabel="Dashboard" />
            <PageHeader
              description="Manage, track, and gain insights into your vehicles."
              icon={Car}
              title="My Vehicle Fleet"
            >
              <div className="flex gap-2">
                <ActionButton
                  actionType="primary"
                  asChild
                  icon={<PlusCircle className="size-4" />}
                >
                  <Link href="/vehicles/new">Add New Vehicle</Link>
                </ActionButton>
              </div>
            </PageHeader>

            {/* Search Bar & Status Indicators */}
            <div className="relative mb-6 rounded-lg bg-card p-4 shadow">
              {isRefreshing && !loading && (
                <div className="absolute right-4 top-4 flex items-center text-xs text-muted-foreground">
                  <RefreshCw className="mr-1 size-3 animate-spin" />
                  Updating list...
                </div>
              )}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 size-5 -translate-y-1/2 text-muted-foreground" />
                <Input
                  className="w-full pl-10"
                  onChange={e => setSearchTerm(e.target.value)}
                  placeholder="Search vehicles (Make, Model, Year, VIN, Plate, Owner...)"
                  type="text"
                  value={searchTerm}
                />
              </div>
            </div>

            <DataLoader
              data={filteredVehicles}
              emptyComponent={
                <div className="rounded-lg bg-card py-12 text-center shadow-md">
                  <Car className="mx-auto mb-6 size-16 text-muted-foreground" />
                  <h3 className="mb-2 text-2xl font-semibold text-foreground">
                    {searchTerm
                      ? 'No Vehicles Match Your Search'
                      : 'Your Garage is Empty!'}
                  </h3>
                  <p className="mx-auto mb-6 mt-2 max-w-md text-muted-foreground">
                    {searchTerm
                      ? 'Try adjusting your search terms or add a new vehicle to your fleet.'
                      : "It looks like you haven't added any vehicles yet. Let's get your first one set up."}
                  </p>
                  {!searchTerm && (
                    <ActionButton
                      actionType="primary"
                      asChild
                      icon={<PlusCircle className="size-4" />}
                      size="lg"
                    >
                      <Link href="/vehicles/new">Add Your First Vehicle</Link>
                    </ActionButton>
                  )}
                </div>
              }
              error={error}
              isLoading={loading}
              loadingComponent={<SkeletonLoader count={3} variant="card" />}
              onRetry={fetchVehicles}
            >
              {(
                vehiclesData // Renamed data to vehiclesData to avoid conflict
              ) => (
                <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
                  {vehiclesData.map(vehicle => (
                    <VehicleCard key={vehicle.id} vehicle={vehicle} />
                  ))}
                </div>
              )}
            </DataLoader>
          </div>
        );
      }}
    </VehicleListContainer>
  );
};

// The main export for the page
export default function VehiclesPage() {
  return (
    <ErrorBoundary>
      <VehiclesPageContent />
    </ErrorBoundary>
  );
}
