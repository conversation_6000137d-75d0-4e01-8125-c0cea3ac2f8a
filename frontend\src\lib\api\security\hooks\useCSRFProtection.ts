/**
 * @file CSRF Protection Hook - DRY + Single Responsibility Principle (SRP)
 * @module hooks/useCSRFProtection
 *
 * This hook provides centralized CSRF protection functionality following DRY principles.
 * It handles CSRF token management and request protection for all components.
 *
 * SECURITY NOTE: This is the single source of CSRF protection for all state-changing operations.
 */

'use client';

import { useCallback, useEffect, useState } from 'react';
import { CSRFProtection } from '../../../security/CSRFProtection';
import type {
  CSRFToken,
  RequestConfig,
  CSRFValidationResult,
} from '../../../security/CSRFProtection';

export interface CSRFProtectionState {
  csrfToken: string | null;
  isTokenValid: boolean;
  tokenExpiresAt: Date | null;
  isInitialized: boolean;
}

export interface CSRFProtectionActions {
  attachCSRF: (config: RequestConfig) => RequestConfig;
  validateCSRF: (token: string) => CSRFValidationResult;
  refreshCSRFToken: () => CSRFToken;
  clearCSRFToken: () => void;
  isProtectionRequired: (config: RequestConfig) => boolean;
}

export interface UseCSRFProtectionReturn
  extends CSRFProtectionState,
    CSRFProtectionActions {}

/**
 * CSRF Protection Hook - DRY Pattern for All State-Changing Operations
 *
 * Provides centralized CSRF protection functionality for all components.
 * Ensures all state-changing operations follow the same CSRF protection patterns.
 */
export function useCSRFProtection(): UseCSRFProtectionReturn {
  const [state, setState] = useState<CSRFProtectionState>({
    csrfToken: null,
    isTokenValid: false,
    tokenExpiresAt: null,
    isInitialized: false,
  });

  /**
   * Attach CSRF token to request configuration
   * Single responsibility: Request CSRF attachment only
   */
  const attachCSRF = useCallback(
    (config: RequestConfig): RequestConfig => {
      // Check if CSRF protection is required
      if (!CSRFProtection.isProtectionRequired(config)) {
        return config;
      }

      // Ensure we have a valid token
      const currentToken = CSRFProtection.getCurrentToken();
      if (!currentToken) {
        console.warn('No valid CSRF token available for request');
        return config;
      }

      // Update state if token changed
      if (currentToken.token !== state.csrfToken) {
        setState(prev => ({
          ...prev,
          csrfToken: currentToken.token,
          isTokenValid: currentToken.isValid,
          tokenExpiresAt: currentToken.expiresAt,
        }));
      }

      // Attach token to request
      return CSRFProtection.attachToRequest(config);
    },
    [state.csrfToken]
  );

  /**
   * Validate CSRF token
   * Single responsibility: CSRF token validation only
   */
  const validateCSRF = useCallback((token: string): CSRFValidationResult => {
    return CSRFProtection.validateToken(token);
  }, []);

  /**
   * Refresh CSRF token
   * Single responsibility: CSRF token refresh only
   */
  const refreshCSRFToken = useCallback((): CSRFToken => {
    const newToken = CSRFProtection.refreshToken();

    setState(prev => ({
      ...prev,
      csrfToken: newToken.token,
      isTokenValid: newToken.isValid,
      tokenExpiresAt: newToken.expiresAt,
    }));

    return newToken;
  }, []);

  /**
   * Clear CSRF token
   * Single responsibility: CSRF token clearing only
   */
  const clearCSRFToken = useCallback(() => {
    CSRFProtection.clearToken();

    setState(prev => ({
      ...prev,
      csrfToken: null,
      isTokenValid: false,
      tokenExpiresAt: null,
    }));
  }, []);

  /**
   * Check if CSRF protection is required for request
   * Single responsibility: CSRF requirement check only
   */
  const isProtectionRequired = useCallback((config: RequestConfig): boolean => {
    return CSRFProtection.isProtectionRequired(config);
  }, []);

  // Initialize CSRF protection
  useEffect(() => {
    const initializeCSRF = () => {
      try {
        const initialToken = CSRFProtection.initialize();

        setState({
          csrfToken: initialToken.token,
          isTokenValid: initialToken.isValid,
          tokenExpiresAt: initialToken.expiresAt,
          isInitialized: true,
        });
      } catch (error) {
        console.error('Failed to initialize CSRF protection:', error);
        setState(prev => ({
          ...prev,
          isInitialized: true,
        }));
      }
    };

    initializeCSRF();
  }, []);

  // Auto-refresh token before expiration
  useEffect(() => {
    if (!state.isInitialized || !state.tokenExpiresAt) return;

    const checkAndRefreshToken = () => {
      const now = new Date();
      const timeUntilExpiry = state.tokenExpiresAt!.getTime() - now.getTime();
      const refreshThreshold = 5 * 60 * 1000; // 5 minutes

      if (timeUntilExpiry <= refreshThreshold) {
        refreshCSRFToken();
      }
    };

    // Check every minute
    const interval = setInterval(checkAndRefreshToken, 60 * 1000);

    // Initial check
    checkAndRefreshToken();

    return () => clearInterval(interval);
  }, [state.isInitialized, state.tokenExpiresAt, refreshCSRFToken]);

  // Validate current token periodically
  useEffect(() => {
    if (!state.csrfToken) return;

    const validateCurrentToken = () => {
      const validation = CSRFProtection.validateToken(state.csrfToken!);

      if (!validation.isValid && state.isTokenValid) {
        // Token became invalid, refresh it
        refreshCSRFToken();
      }
    };

    // Validate every 5 minutes
    const interval = setInterval(validateCurrentToken, 5 * 60 * 1000);

    return () => clearInterval(interval);
  }, [state.csrfToken, state.isTokenValid, refreshCSRFToken]);

  return {
    // State
    csrfToken: state.csrfToken,
    isTokenValid: state.isTokenValid,
    tokenExpiresAt: state.tokenExpiresAt,
    isInitialized: state.isInitialized,

    // Actions
    attachCSRF,
    validateCSRF,
    refreshCSRFToken,
    clearCSRFToken,
    isProtectionRequired,
  };
}
