/**
 * @file VehicleUtilizationChart.tsx
 * @description Vehicle utilization chart following existing chart patterns and SOLID principles
 */

import React, { useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Cell,
} from 'recharts';
import { Car, TrendingUp } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useVehicleAnalytics } from '../../hooks/useVehicleAnalytics';
import { DataLoader } from '@/components/ui/data-loader';
import { ErrorDisplay } from '@/components/ui/error-display';
import type { ReportingFilters } from '../../data/types/reporting';
import type { VehicleUtilizationData } from '../../data/types/reporting';

/**
 * Props interface for VehicleUtilizationChart
 */
interface VehicleUtilizationChartProps {
  data?: VehicleUtilizationData[];
  filters?: ReportingFilters;
  className?: string;
  showLegend?: boolean;
  interactive?: boolean;
  height?: number;
}

/**
 * Custom tooltip component for the chart
 */
interface CustomTooltipProps {
  active?: boolean;
  payload?: any[];
  label?: string;
}

const CustomTooltip: React.FC<CustomTooltipProps> = ({
  active,
  payload,
  label,
}) => {
  if (active && payload && payload.length) {
    const data = payload[0].payload;
    return (
      <div className="bg-white p-3 border rounded-lg shadow-lg">
        <p className="font-medium">{data.vehicleName}</p>
        <p className="text-sm text-gray-600">
          Utilization:{' '}
          <span className="font-medium">{data.utilizationRate}%</span>
        </p>
        <p className="text-sm text-gray-600">
          Active Delegations:{' '}
          <span className="font-medium">{data.activeDelegations}</span>
        </p>
        <p className="text-sm text-gray-600">
          Total Delegations:{' '}
          <span className="font-medium">{data.totalDelegations}</span>
        </p>
      </div>
    );
  }
  return null;
};

/**
 * Utility function to get color based on utilization rate
 */
const getUtilizationColor = (rate: number): string => {
  if (rate >= 80) return '#ef4444'; // Red - overutilized
  if (rate >= 60) return '#f59e0b'; // Orange - high utilization
  if (rate >= 40) return '#10b981'; // Green - good utilization
  if (rate >= 20) return '#3b82f6'; // Blue - moderate utilization
  return '#6b7280'; // Gray - low utilization
};

/**
 * VehicleUtilizationChart Component
 *
 * Displays vehicle utilization data in a bar chart format following existing chart patterns.
 *
 * Responsibilities:
 * - Visualize vehicle utilization distribution
 * - Follow existing chart component patterns
 * - Integrate with existing chart utilities
 * - Maintain consistent chart styling
 *
 * SOLID Principles Applied:
 * - SRP: Single responsibility of displaying utilization data
 * - OCP: Open for extension via props configuration
 * - DIP: Depends on chart framework abstractions
 */
export const VehicleUtilizationChart: React.FC<
  VehicleUtilizationChartProps
> = ({
  data: propData,
  filters,
  className = '',
  showLegend = true,
  interactive = true,
  height = 300,
}) => {
  // Use hook if data not provided via props
  const { data: hookData, isLoading, error } = useVehicleAnalytics(filters);
  const utilizationData = propData || hookData?.utilizationMetrics;

  // Transform data for chart display
  const chartData = useMemo(() => {
    if (!utilizationData) return [];

    return utilizationData
      .map(item => ({
        vehicleName: item.vehicleName,
        utilizationRate: Math.round(item.utilizationRate),
        activeDelegations: item.activeDelegations,
        totalDelegations: item.totalDelegations,
        color: getUtilizationColor(item.utilizationRate),
        // Truncate long vehicle names for display
        displayName:
          item.vehicleName.length > 15
            ? `${item.vehicleName.substring(0, 12)}...`
            : item.vehicleName,
      }))
      .sort((a, b) => b.utilizationRate - a.utilizationRate) // Sort by utilization rate
      .slice(0, 10); // Show top 10 vehicles
  }, [utilizationData]);

  // Handle loading state
  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Car className="h-5 w-5" />
            Vehicle Utilization
          </CardTitle>
        </CardHeader>
        <CardContent>
          <DataLoader isLoading={true} data={null} error={null}>
            {() => null}
          </DataLoader>
        </CardContent>
      </Card>
    );
  }

  // Handle error state
  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Car className="h-5 w-5" />
            Vehicle Utilization
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ErrorDisplay error={error} />
        </CardContent>
      </Card>
    );
  }

  // Calculate summary stats
  const avgUtilization =
    chartData.length > 0
      ? Math.round(
          chartData.reduce((sum, item) => sum + item.utilizationRate, 0) /
            chartData.length
        )
      : 0;

  const highUtilizationCount = chartData.filter(
    item => item.utilizationRate >= 80
  ).length;

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Car className="h-5 w-5" />
            Vehicle Utilization
          </CardTitle>
          <div className="flex items-center gap-2">
            <Badge variant="secondary" className="text-xs">
              Avg: {avgUtilization}%
            </Badge>
            {highUtilizationCount > 0 && (
              <Badge variant="destructive" className="text-xs">
                {highUtilizationCount} overutilized
              </Badge>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent>
        {chartData.length === 0 ? (
          <div className="flex items-center justify-center h-64 text-gray-500">
            <div className="text-center">
              <Car className="h-12 w-12 mx-auto mb-2 opacity-50" />
              <p>No vehicle utilization data available</p>
            </div>
          </div>
        ) : (
          <>
            <ResponsiveContainer width="100%" height={height}>
              <BarChart
                data={chartData}
                margin={{
                  top: 20,
                  right: 30,
                  left: 20,
                  bottom: 60,
                }}
              >
                <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                <XAxis
                  dataKey="displayName"
                  angle={-45}
                  textAnchor="end"
                  height={60}
                  fontSize={12}
                />
                <YAxis
                  domain={[0, 100]}
                  tickFormatter={value => `${value}%`}
                  fontSize={12}
                />
                {interactive && <Tooltip content={<CustomTooltip />} />}
                <Bar
                  dataKey="utilizationRate"
                  radius={[4, 4, 0, 0]}
                  name="Utilization Rate"
                >
                  {chartData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Bar>
              </BarChart>
            </ResponsiveContainer>

            {showLegend && (
              <div className="mt-4 flex flex-wrap gap-4 text-xs">
                <div className="flex items-center gap-1">
                  <div className="w-3 h-3 bg-red-500 rounded"></div>
                  <span>Overutilized (80%+)</span>
                </div>
                <div className="flex items-center gap-1">
                  <div className="w-3 h-3 bg-orange-500 rounded"></div>
                  <span>High (60-79%)</span>
                </div>
                <div className="flex items-center gap-1">
                  <div className="w-3 h-3 bg-green-500 rounded"></div>
                  <span>Good (40-59%)</span>
                </div>
                <div className="flex items-center gap-1">
                  <div className="w-3 h-3 bg-blue-500 rounded"></div>
                  <span>Moderate (20-39%)</span>
                </div>
                <div className="flex items-center gap-1">
                  <div className="w-3 h-3 bg-gray-500 rounded"></div>
                  <span>Low (0-19%)</span>
                </div>
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
};

export default VehicleUtilizationChart;
