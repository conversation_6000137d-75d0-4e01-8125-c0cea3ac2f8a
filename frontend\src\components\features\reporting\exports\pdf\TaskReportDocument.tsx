// frontend/src/components/features/reporting/exports/pdf/TaskReportDocument.tsx
import React from 'react';
import { Document, Page, Text, View, StyleSheet } from '@react-pdf/renderer';

const styles = StyleSheet.create({
  page: {
    flexDirection: 'column',
    backgroundColor: '#FFFFFF',
    padding: 30,
    fontSize: 12,
  },
  header: {
    fontSize: 20,
    marginBottom: 20,
    textAlign: 'center',
    color: '#059669',
    fontWeight: 'bold',
  },
  subheader: {
    fontSize: 16,
    marginBottom: 15,
    color: '#374151',
    fontWeight: 'bold',
    borderBottom: '1px solid #e5e7eb',
    paddingBottom: 5,
  },
  section: {
    marginBottom: 20,
  },
  row: {
    flexDirection: 'row',
    marginBottom: 8,
    paddingVertical: 4,
  },
  label: {
    width: '40%',
    fontWeight: 'bold',
    color: '#4b5563',
  },
  value: {
    width: '60%',
    color: '#111827',
  },
  table: {
    marginTop: 10,
  },
  tableHeader: {
    flexDirection: 'row',
    backgroundColor: '#ecfdf5',
    padding: 8,
    fontWeight: 'bold',
  },
  tableRow: {
    flexDirection: 'row',
    padding: 8,
    borderBottom: '1px solid #e5e7eb',
  },
  tableCell: {
    flex: 1,
    fontSize: 10,
  },
  metadata: {
    marginTop: 30,
    padding: 15,
    backgroundColor: '#f0fdf4',
    borderRadius: 5,
  },
  metadataText: {
    fontSize: 10,
    color: '#6b7280',
    marginBottom: 3,
  },
});

interface TaskReportDocumentProps {
  data: any;
  reportTitle: string;
  metadata?: any;
}

export const TaskReportDocument: React.FC<TaskReportDocumentProps> = ({
  data,
  reportTitle,
  metadata,
}) => {
  // Defensive programming: ensure all data is properly defined
  const taskData = React.useMemo(() => {
    if (!data) return {};
    const rawData = data?.data || data;
    if (!rawData || typeof rawData !== 'object') return {};

    return {
      totalCount: rawData.totalCount || 0,
      completedTasks: rawData.completedTasks || 0,
      pendingTasks: rawData.pendingTasks || 0,
      overdueCount: rawData.overdueCount || 0,
      completionRate: rawData.completionRate || 0,
      averageCompletionTime: rawData.averageCompletionTime || 0,
      statusDistribution: Array.isArray(rawData.statusDistribution) ? rawData.statusDistribution : [],
      priorityDistribution: Array.isArray(rawData.priorityDistribution) ? rawData.priorityDistribution : [],
      assignmentMetrics: rawData.assignmentMetrics || {},
      ...rawData,
    };
  }, [data]);
  
  const safeReportTitle = reportTitle || 'Task Report';
  const safeMetadata = metadata || {};

  return (
    <Document>
      <Page size="A4" style={styles.page}>
        <Text style={styles.header}>{safeReportTitle}</Text>

        {/* Task Summary */}
        <View style={styles.section}>
          <Text style={styles.subheader}>Task Summary</Text>
          <View style={styles.row}>
            <Text style={styles.label}>Total Tasks:</Text>
            <Text style={styles.value}>{taskData.totalCount || 0}</Text>
          </View>
          <View style={styles.row}>
            <Text style={styles.label}>Completed Tasks:</Text>
            <Text style={styles.value}>{taskData.completedTasks || 0}</Text>
          </View>
          <View style={styles.row}>
            <Text style={styles.label}>Pending Tasks:</Text>
            <Text style={styles.value}>{taskData.pendingTasks || 0}</Text>
          </View>
          <View style={styles.row}>
            <Text style={styles.label}>Overdue Tasks:</Text>
            <Text style={styles.value}>{taskData.overdueCount || 0}</Text>
          </View>
          <View style={styles.row}>
            <Text style={styles.label}>Completion Rate:</Text>
            <Text style={styles.value}>
              {taskData.completionRate?.toFixed(2) || 0}%
            </Text>
          </View>
          <View style={styles.row}>
            <Text style={styles.label}>Average Completion Time:</Text>
            <Text style={styles.value}>
              {taskData.averageCompletionTime?.toFixed(2) || 0} hours
            </Text>
          </View>
        </View>

        {/* Status Distribution */}
        {taskData.statusDistribution && (
          <View style={styles.section}>
            <Text style={styles.subheader}>Status Distribution</Text>
            <View style={styles.table}>
              <View style={styles.tableHeader}>
                <Text style={styles.tableCell}>Status</Text>
                <Text style={styles.tableCell}>Count</Text>
                <Text style={styles.tableCell}>Percentage</Text>
              </View>
              {taskData.statusDistribution.map((item: any, index: number) => (
                <View key={`status-${index}`} style={styles.tableRow}>
                  <Text style={styles.tableCell}>
                    {item?.status || item?._id || 'Unknown'}
                  </Text>
                  <Text style={styles.tableCell}>
                    {item?.count || item?._count?.status || 0}
                  </Text>
                  <Text style={styles.tableCell}>
                    {item?.percentage
                      ? `${Number(item.percentage).toFixed(1)}%`
                      : 'N/A'}
                  </Text>
                </View>
              ))}
            </View>
          </View>
        )}

        {/* Priority Distribution */}
        {taskData.priorityDistribution && (
          <View style={styles.section}>
            <Text style={styles.subheader}>Priority Distribution</Text>
            <View style={styles.table}>
              <View style={styles.tableHeader}>
                <Text style={styles.tableCell}>Priority</Text>
                <Text style={styles.tableCell}>Count</Text>
                <Text style={styles.tableCell}>Percentage</Text>
              </View>
              {taskData.priorityDistribution.map((item: any, index: number) => (
                <View key={`priority-${index}`} style={styles.tableRow}>
                  <Text style={styles.tableCell}>
                    {item?.priority || item?._id || 'Unknown'}
                  </Text>
                  <Text style={styles.tableCell}>
                    {item?.count || item?._count?.priority || 0}
                  </Text>
                  <Text style={styles.tableCell}>
                    {item?.percentage
                      ? `${Number(item.percentage).toFixed(1)}%`
                      : 'N/A'}
                  </Text>
                </View>
              ))}
            </View>
          </View>
        )}

        {/* Assignment Metrics */}
        {taskData.assignmentMetrics && (
          <View style={styles.section}>
            <Text style={styles.subheader}>Assignment Metrics</Text>
            <View style={styles.row}>
              <Text style={styles.label}>Assigned Tasks:</Text>
              <Text style={styles.value}>
                {taskData.assignmentMetrics.assignedCount || 0}
              </Text>
            </View>
            <View style={styles.row}>
              <Text style={styles.label}>Unassigned Tasks:</Text>
              <Text style={styles.value}>
                {taskData.assignmentMetrics.unassignedCount || 0}
              </Text>
            </View>
            <View style={styles.row}>
              <Text style={styles.label}>Assignment Rate:</Text>
              <Text style={styles.value}>
                {taskData.assignmentMetrics.assignmentRate?.toFixed(2) || 0}%
              </Text>
            </View>
          </View>
        )}

        {/* Report Metadata */}
        {safeMetadata && Object.keys(safeMetadata).length > 0 && (
          <View style={styles.metadata}>
            <Text style={styles.subheader}>Report Information</Text>
            <Text style={styles.metadataText}>
              Report ID: {safeMetadata.id || 'N/A'}
            </Text>
            <Text style={styles.metadataText}>
              Type: {safeMetadata.type || 'N/A'}
            </Text>
            <Text style={styles.metadataText}>
              Entity Type: {safeMetadata.entityType || 'N/A'}
            </Text>
            <Text style={styles.metadataText}>
              Generated:{' '}
              {safeMetadata.generatedAt
                ? new Date(safeMetadata.generatedAt).toLocaleString()
                : 'N/A'}
            </Text>
            <Text style={styles.metadataText}>
              Generated By: {safeMetadata.generatedBy || 'N/A'}
            </Text>
          </View>
        )}
      </Page>
    </Document>
  );
};
