/**
 * @file Reliability monitoring dashboard page.
 * This page provides the main entry point for the reliability monitoring interface.
 * @module app/reliability/page
 */

import type { Metadata } from 'next';

import React from 'react';

import { ProtectedRoute } from '@/components/auth';
import { ReliabilityDashboard } from '@/components/reliability/dashboard';
import { AppBreadcrumb } from '@/components/ui/app-breadcrumb';

/**
 * Metadata for the reliability dashboard page
 */
export const metadata: Metadata = {
  description:
    'Real-time system monitoring and reliability insights for WorkHub',
  keywords: [
    'reliability',
    'monitoring',
    'dashboard',
    'system health',
    'alerts',
  ],
  title: 'Reliability Dashboard | WorkHub',
};

/**
 * Reliability monitoring dashboard page component.
 *
 * This page provides comprehensive system reliability monitoring including:
 * - Real-time health monitoring
 * - Circuit breaker status tracking
 * - Performance metrics visualization
 * - Active alert management
 * - Historical trend analysis
 *
 * Features:
 * - Role-based access control (ADMIN and SUPER_ADMIN only)
 * - Responsive design for all device sizes
 * - Real-time WebSocket integration
 * - Customizable dashboard layout
 * - Comprehensive error handling
 *
 * @returns JSX element representing the reliability dashboard page
 */
export default function ReliabilityPage() {
  return (
    <ProtectedRoute>
      <div className="container mx-auto space-y-6 px-4 py-6 sm:px-6 lg:px-8">
        <AppBreadcrumb />
        <ReliabilityDashboard />
      </div>
    </ProtectedRoute>
  );
}
