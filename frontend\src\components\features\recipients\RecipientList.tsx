'use client';

import { useState } from 'react';

import type { Recipient } from '@/lib/types/domain';

import { DataLoader, SkeletonLoader } from '@/components/ui/loading';
import { cn } from '@/lib/utils';

import { RecipientCard } from './RecipientCard';
import { RecipientFilters } from './RecipientFilters';

interface RecipientListProps {
  recipients: Recipient[];
  isLoading?: boolean;
  error?: Error | null;
  onEdit?: (recipient: Recipient) => void;
  onDelete?: (recipient: Recipient) => void;
  onRetry?: () => void;
  showGifts?: boolean;
  className?: string;
}

interface FilterState {
  search: string;
  hasEmail: string; // 'all', 'yes', 'no'
  hasPhone: string; // 'all', 'yes', 'no'
  hasAddress: string; // 'all', 'yes', 'no'
  giftCount: string; // 'all', 'none', 'some', 'many'
}

const initialFilterState: FilterState = {
  search: '',
  hasEmail: 'all',
  hasPhone: 'all',
  hasAddress: 'all',
  giftCount: 'all',
};

export const RecipientList: React.FC<RecipientListProps> = ({
  recipients,
  isLoading = false,
  error = null,
  onEdit,
  onDelete,
  onRetry,
  showGifts = true,
  className,
}) => {
  const [filters, setFilters] = useState<FilterState>(initialFilterState);

  // Filter recipients based on current filter state
  const filteredRecipients = recipients.filter(recipient => {
    // Search filter (name, email, phone, address, notes)
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      const matchesName = recipient.name.toLowerCase().includes(searchLower);
      const matchesEmail = recipient.email?.toLowerCase().includes(searchLower);
      const matchesPhone = recipient.phone?.toLowerCase().includes(searchLower);
      const matchesAddress = recipient.address?.toLowerCase().includes(searchLower);
      const matchesNotes = recipient.notes?.toLowerCase().includes(searchLower);
      
      if (!matchesName && !matchesEmail && !matchesPhone && !matchesAddress && !matchesNotes) {
        return false;
      }
    }

    // Email filter
    if (filters.hasEmail !== 'all') {
      const hasEmail = Boolean(recipient.email);
      if (filters.hasEmail === 'yes' && !hasEmail) return false;
      if (filters.hasEmail === 'no' && hasEmail) return false;
    }

    // Phone filter
    if (filters.hasPhone !== 'all') {
      const hasPhone = Boolean(recipient.phone);
      if (filters.hasPhone === 'yes' && !hasPhone) return false;
      if (filters.hasPhone === 'no' && hasPhone) return false;
    }

    // Address filter
    if (filters.hasAddress !== 'all') {
      const hasAddress = Boolean(recipient.address);
      if (filters.hasAddress === 'yes' && !hasAddress) return false;
      if (filters.hasAddress === 'no' && hasAddress) return false;
    }

    // Gift count filter
    if (filters.giftCount !== 'all') {
      const giftCount = recipient.gifts?.length || 0;
      switch (filters.giftCount) {
        case 'none':
          if (giftCount > 0) return false;
          break;
        case 'some':
          if (giftCount === 0 || giftCount >= 5) return false;
          break;
        case 'many':
          if (giftCount < 5) return false;
          break;
      }
    }

    return true;
  });

  const handleFilterChange = (newFilters: Partial<FilterState>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  const handleClearFilters = () => {
    setFilters(initialFilterState);
  };

  const hasActiveFilters = Object.entries(filters).some(([key, value]) => {
    if (key === 'search') return value !== '';
    return value !== 'all';
  });

  return (
    <div className={cn('space-y-6', className)}>
      {/* Filters */}
      <RecipientFilters
        filters={filters}
        onFilterChange={handleFilterChange}
        onClearFilters={handleClearFilters}
        hasActiveFilters={hasActiveFilters}
        recipients={recipients} // Pass all recipients for statistics
      />

      {/* Results Summary */}
      <div className="flex items-center justify-between">
        <div className="text-sm text-muted-foreground">
          {hasActiveFilters ? (
            <>
              Showing {filteredRecipients.length} of {recipients.length} recipients
              {filteredRecipients.length !== recipients.length && (
                <span className="ml-2 text-primary">
                  ({recipients.length - filteredRecipients.length} filtered out)
                </span>
              )}
            </>
          ) : (
            `${recipients.length} recipient${recipients.length !== 1 ? 's' : ''} total`
          )}
        </div>
      </div>

      {/* Recipient List */}
      <DataLoader
        data={filteredRecipients}
        isLoading={isLoading}
        error={error}
        loadingComponent={<SkeletonLoader count={6} variant="card" />}
        emptyComponent={
          <div className="text-center py-12">
            <div className="text-muted-foreground">
              {hasActiveFilters ? (
                <>
                  <p className="text-lg font-medium mb-2">No recipients match your filters</p>
                  <p className="text-sm">Try adjusting your search criteria or clearing filters</p>
                </>
              ) : (
                <>
                  <p className="text-lg font-medium mb-2">No recipients found</p>
                  <p className="text-sm">Start by adding your first recipient</p>
                </>
              )}
            </div>
          </div>
        }
        onRetry={onRetry}
      >
        {(recipients) => (
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {recipients.map((recipient) => (
              <RecipientCard
                key={recipient.id}
                recipient={recipient}
                onEdit={onEdit}
                onDelete={onDelete}
                showGifts={showGifts}
              />
            ))}
          </div>
        )}
      </DataLoader>
    </div>
  );
};
