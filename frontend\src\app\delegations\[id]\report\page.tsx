// @ts-nocheck
'use client';

import { format, parseISO } from 'date-fns';
import {
  CalendarDays,
  Clock,
  Info as InfoIcon,
  MapPin,
  Plane,
  Users,
} from 'lucide-react';
import Image from 'next/image';
import { useParams } from 'next/navigation';
import { useEffect } from 'react';

import { ReportActions } from '@/components/reports/ReportActions';
import { Badge } from '@/components/ui/badge';
import { SkeletonLoader } from '@/components/ui/loading';
import { useDelegation } from '@/lib/stores/queries/useDelegations';
import { cn } from '@/lib/utils';
import { formatDelegationStatusForDisplay } from '@/lib/utils/formattingUtils';

import type {
  Delegation,
  Delegate as DomainDelegate, // Alias to avoid conflict if Delegate is defined locally
  FlightDetails,
} from '../../../../lib/types/domain'; // Adjusted path

const formatDate = (
  dateString: string | undefined,
  includeTime = false
): string => {
  if (!dateString) return 'N/A';
  try {
    return format(
      parseISO(dateString),
      includeTime ? 'MMM d, yyyy, HH:mm' : 'MMM d, yyyy'
    );
  } catch {
    return 'Invalid Date';
  }
};

const getStatusColor = (status: Delegation['status']) => {
  switch (status) {
    case 'Cancelled': {
      return 'bg-red-100 text-red-800 border-red-300';
    }
    case 'Completed': {
      return 'bg-purple-100 text-purple-800 border-purple-300';
    }
    case 'Confirmed': {
      return 'bg-green-100 text-green-800 border-green-300';
    }
    case 'In_Progress': {
      return 'bg-yellow-100 text-yellow-800 border-yellow-300';
    }
    case 'Planned': {
      return 'bg-blue-100 text-blue-800 border-blue-300';
    }
    case 'No_details':
    default: {
      return 'bg-gray-100 text-gray-800 border-gray-300';
    }
  }
};

export default function DelegationReportPage() {
  const params = useParams();
  const delegationId = params.id as string;

  const {
    data: delegation,
    error,
    isError,
    isLoading,
  } = useDelegation(delegationId);

  useEffect(() => {
    if (delegation) {
      document.title = `${delegation.eventName} - Delegation Report`;
    }
  }, [delegation]);

  if (isLoading) {
    return (
      <div className="mx-auto max-w-4xl p-4">
        <SkeletonLoader count={1} variant="card" />
        <SkeletonLoader className="mt-6" count={3} variant="table" />
        <SkeletonLoader className="mt-6" count={2} variant="table" />
      </div>
    );
  }

  if (isError || !delegation) {
    return (
      <div className="py-10 text-center">
        Error loading delegation report or delegation not found.{' '}
        {error?.message}
      </div>
    );
  }

  return (
    <div className="mx-auto max-w-4xl bg-white p-2 text-gray-800 sm:p-4">
      <div className="no-print mb-4 text-right">
        <ReportActions
          enableCsv={
            (delegation.delegates && delegation.delegates.length > 0) || // Added null check for delegates
            (delegation.statusHistory && delegation.statusHistory.length > 0)
          }
          fileName={`delegation-report-${delegation.eventName.replaceAll(
            /\s+/g,
            '-'
          )}`}
          reportContentId="#delegation-report-content"
          tableId="#delegates-table"
        />
      </div>

      <div className="report-content" id="delegation-report-content">
        <header className="mb-8 border-b-2 border-gray-300 pb-4 text-center">
          <h1 className="text-3xl font-bold text-gray-800">
            Delegation Report
          </h1>
          <p className="text-xl text-gray-600">{delegation.eventName}</p>
          <Badge
            className={cn(
              'mt-2 text-sm py-1 px-3 font-semibold',
              getStatusColor(delegation.status)
            )}
          >
            Status: {formatDelegationStatusForDisplay(delegation.status)}
          </Badge>
        </header>

        <section className="card-print mb-6 rounded border border-gray-200 p-4">
          <h2 className="mb-3 border-b border-gray-200 pb-2 text-xl font-semibold text-gray-700">
            Delegation Summary
          </h2>
          <div className="grid grid-cols-1 gap-x-4 gap-y-2 text-sm md:grid-cols-2">
            <div>
              <strong>Event Name:</strong> {delegation.eventName}
            </div>
            <div>
              <strong>Location:</strong> {delegation.location}
            </div>
            <div>
              <strong>Duration:</strong> {formatDate(delegation.durationFrom)}{' '}
              to {formatDate(delegation.durationTo)}
            </div>
            {delegation.invitationFrom && (
              <div>
                <strong>Invitation From:</strong> {delegation.invitationFrom}
              </div>
            )}
            {delegation.invitationTo && (
              <div>
                <strong>Invitation To:</strong> {delegation.invitationTo}
              </div>
            )}
          </div>
          {delegation.imageUrl && (
            <div className="no-print relative mx-auto mt-4 aspect-[16/9] w-full max-w-md overflow-hidden rounded">
              <Image
                alt={delegation.eventName}
                data-ai-hint="event placeholder"
                layout="fill"
                objectFit="contain"
                src={delegation.imageUrl}
              />
            </div>
          )}
          {delegation.notes && (
            <div className="mt-3 text-sm">
              <strong>Notes:</strong>{' '}
              <span className="italic">{delegation.notes}</span>
            </div>
          )}
        </section>

        <section className="card-print mb-6 rounded border border-gray-200 p-4">
          <h2 className="mb-3 border-b border-gray-200 pb-2 text-xl font-semibold text-gray-700">
            Delegates ({delegation.delegates?.length || 0})
          </h2>
          {delegation.delegates && delegation.delegates.length > 0 ? (
            <table
              className="w-full text-left text-sm text-gray-600"
              id="delegates-table"
            >
              <thead className="bg-gray-50 text-xs uppercase text-gray-700">
                <tr>
                  <th className="px-3 py-2" scope="col">
                    Name
                  </th>
                  <th className="px-3 py-2" scope="col">
                    Title
                  </th>
                  <th className="px-3 py-2" scope="col">
                    Notes
                  </th>
                </tr>
              </thead>
              <tbody>
                {delegation.delegates.map(
                  (
                    delegate: DomainDelegate // Use aliased DomainDelegate
                  ) => (
                    <tr
                      className="border-b bg-white hover:bg-gray-50"
                      key={delegate.id}
                    >
                      <td className="px-3 py-2 font-medium">{delegate.name}</td>
                      <td className="px-3 py-2">{delegate.title}</td>
                      <td className="px-3 py-2">{delegate.notes || '-'}</td>
                    </tr>
                  )
                )}
              </tbody>
            </table>
          ) : (
            <p className="text-sm text-gray-500">No delegates listed.</p>
          )}
        </section>

        {(delegation.flightArrivalDetails ||
          delegation.flightDepartureDetails) && (
          <section className="card-print mb-6 rounded border border-gray-200 p-4">
            <h2 className="mb-3 border-b border-gray-200 pb-2 text-xl font-semibold text-gray-700">
              Flight Information
            </h2>
            <div className="grid grid-cols-1 gap-6 text-sm md:grid-cols-2">
              {delegation.flightArrivalDetails && (
                <div>
                  <h3 className="text-md mb-1 font-semibold">
                    Arrival Details
                  </h3>
                  <p>
                    <Plane className="mr-1 inline size-4" />
                    <strong>Flight:</strong>{' '}
                    {delegation.flightArrivalDetails.flightNumber}
                  </p>
                  <p>
                    <Clock className="mr-1 inline size-4" />
                    <strong>Time:</strong>{' '}
                    {formatDate(delegation.flightArrivalDetails.dateTime, true)}
                  </p>
                  <p>
                    <MapPin className="mr-1 inline size-4" />
                    <strong>Airport:</strong>{' '}
                    {delegation.flightArrivalDetails.airport}{' '}
                    {delegation.flightArrivalDetails.terminal &&
                      `(Terminal ${delegation.flightArrivalDetails.terminal})`}
                  </p>
                  {delegation.flightArrivalDetails.notes && (
                    <p className="mt-1 text-xs italic">
                      <strong>Notes:</strong>{' '}
                      {delegation.flightArrivalDetails.notes}
                    </p>
                  )}
                </div>
              )}
              {delegation.flightDepartureDetails && (
                <div>
                  <h3 className="text-md mb-1 font-semibold">
                    Departure Details
                  </h3>
                  <p>
                    <Plane className="mr-1 inline size-4" />
                    <strong>Flight:</strong>{' '}
                    {delegation.flightDepartureDetails.flightNumber}
                  </p>
                  <p>
                    <Clock className="mr-1 inline size-4" />
                    <strong>Time:</strong>{' '}
                    {formatDate(
                      delegation.flightDepartureDetails.dateTime,
                      true
                    )}
                  </p>
                  <p>
                    <MapPin className="mr-1 inline size-4" />
                    <strong>Airport:</strong>{' '}
                    {delegation.flightDepartureDetails.airport}{' '}
                    {delegation.flightDepartureDetails.terminal &&
                      `(Terminal ${delegation.flightDepartureDetails.terminal})`}
                  </p>
                  {delegation.flightDepartureDetails.notes && (
                    <p className="mt-1 text-xs italic">
                      <strong>Notes:</strong>{' '}
                      {delegation.flightDepartureDetails.notes}
                    </p>
                  )}
                </div>
              )}
            </div>
            {!delegation.flightArrivalDetails &&
              !delegation.flightDepartureDetails && (
                <p className="text-sm text-gray-500">
                  No flight details logged.
                </p>
              )}
          </section>
        )}

        <section className="card-print rounded border border-gray-200 p-4">
          <h2 className="mb-3 border-b border-gray-200 pb-2 text-xl font-semibold text-gray-700">
            Status History
          </h2>
          {delegation.statusHistory && delegation.statusHistory.length > 0 ? (
            <table
              className="w-full text-left text-sm text-gray-600"
              id="status-history-table"
            >
              <thead className="bg-gray-50 text-xs uppercase text-gray-700">
                <tr>
                  <th className="px-3 py-2" scope="col">
                    Status
                  </th>
                  <th className="px-3 py-2" scope="col">
                    Changed At
                  </th>
                  <th className="px-3 py-2" scope="col">
                    Reason
                  </th>
                </tr>
              </thead>
              <tbody>
                {[...delegation.statusHistory]
                  .sort(
                    (a, b) =>
                      new Date(b.changedAt).getTime() -
                      new Date(a.changedAt).getTime()
                  )
                  .map(entry => (
                    <tr
                      className="border-b bg-white hover:bg-gray-50"
                      key={entry.id}
                    >
                      <td className="px-3 py-2">
                        <Badge
                          className={cn(
                            'text-xs py-0.5 px-1.5',
                            getStatusColor(entry.status)
                          )}
                        >
                          {formatDelegationStatusForDisplay(
                            entry.status as Delegation['status']
                          )}
                        </Badge>
                      </td>
                      <td className="px-3 py-2">
                        {formatDate(entry.changedAt, true)}
                      </td>
                      <td className="px-3 py-2">{entry.reason || '-'}</td>
                    </tr>
                  ))}
              </tbody>
            </table>
          ) : (
            <p className="text-sm text-gray-500">
              No status history available.
            </p>
          )}
        </section>

        <footer className="mt-10 border-t-2 border-gray-300 pt-4 text-center text-xs text-gray-500">
          <p>Report generated on: {new Date().toLocaleDateString()}</p>
          <p>WorkHub - Delegation Management</p>
        </footer>
      </div>
    </div>
  );
}
