/**
 * Admin Module Types
 *
 * Centralized type definitions for the admin module, consolidating
 * types from various admin services for better maintainability.
 */

// Import and re-export existing admin schema types
import type {
  ErrorLogEntry,
  HealthResponse,
  LogLevel,
  PaginatedErrorLogResponse,
  Pagination,
  PerformanceMetrics,
} from '../../../schemas/admin.schema.js';

export type {
  ErrorLogEntry,
  HealthResponse,
  LogLevel,
  PaginatedErrorLogResponse,
  Pagination,
  PerformanceMetrics,
};

// Audit Details for tracking admin operations
export interface AuditDetails {
  ipAddress?: string;
  userAgent?: string;
  userId: string;
}

// Audit Log Types
export interface AuditLog {
  action: string;
  created_at: string;
  details: string;
  id: string;
  ip_address: null | string;
  user_agent: null | string;
  user_id: string;
  auth_user_id: string;
  auth_user: null | {
    email: string;
  };
}

export interface CreateAuditLogRequest {
  action: string;
  details: string;
  ipAddress?: string;
  userAgent?: string;
  userId: string;
}

export interface CreateUserRequest {
  email: string;
  emailVerified?: boolean;
  isActive?: boolean;
  role: string;
}

export interface GetAllUsersResult {
  data: UserProfile[];
  pagination: Pagination;
}

export interface GetAuditLogsResult {
  data: AuditLog[];
  pagination: Pagination;
}

export interface UpdateUserRequest {
  email?: string;
  emailVerified?: boolean;
  isActive?: boolean;
  role?: string;
}

// User Management Types
export interface UserProfile {
  created_at: string;
  employee_id: null | string;
  id: string;
  is_active: boolean;
  role: string;
  updated_at: string;
  users: {
    email: string;
    email_confirmed_at: null | string;
  }[];
}

export interface UserProfileWithEmail {
  created_at: string;
  employee_id: null | string;
  id: string;
  is_active: boolean;
  role: string;
  updated_at: string;
  users: {
    email: string;
    email_confirmed_at: null | string;
  }[];
}

// User Roles
export const VALID_ROLES = ['SUPER_ADMIN', 'ADMIN', 'MANAGER', 'EMPLOYEE', 'USER'] as const;

// Admin Configuration
export interface AdminConfig {
  auditLogRetentionDays: number;
  maxAuditLogsPerPage: number;
  maxConcurrentSessions: number;
  maxErrorLogsPerPage: number;
  maxUsersPerPage: number;
  sessionTimeoutMinutes: number;
}

// Admin Dashboard Data
export interface AdminDashboardData {
  healthStatus: HealthResponse;
  performanceMetrics: PerformanceMetrics;
  recentAuditLogs: AuditLog[];
  systemMetrics: {
    cpuUsage: number;
    diskUsage: number;
    memoryUsage: number;
    uptime: number;
  };
  userStats: {
    activeUsers: number;
    inactiveUsers: number;
    totalUsers: number;
    usersByRole: Record<UserRole, number>;
  };
}

// Admin Event Types for monitoring and logging
export interface AdminEvent {
  details: any;
  error?: string;
  ipAddress?: string;
  success: boolean;
  timestamp: string;
  type: AdminOperation;
  userAgent?: string;
  userId: string;
}

// Admin Module Configuration
export interface AdminModuleConfig {
  cacheTimeoutMs: number;
  enableAuditLogging: boolean;
  enableCircuitBreakers: boolean;
  enableMetricsCollection: boolean;
  enableRequestDeduplication: boolean;
  maxConcurrentOperations: number;
  operationTimeoutMs: number;
}

// Admin Operation Types
export type AdminOperation =
  | 'CREATE_USER'
  | 'DELETE_USER'
  | 'RESET_CIRCUIT_BREAKER'
  | 'TOGGLE_USER_ACTIVATION'
  | 'UPDATE_USER'
  | 'VIEW_AUDIT_LOGS'
  | 'VIEW_ERROR_LOGS'
  | 'VIEW_HEALTH_STATUS'
  | 'VIEW_MONITORING_DATA'
  | 'VIEW_PERFORMANCE_METRICS'
  | 'VIEW_USERS';

// Admin Permissions
export interface AdminPermissions {
  canCreateUsers: boolean;
  canDeleteUsers: boolean;
  canManageRoles: boolean;
  canResetCircuitBreakers: boolean;
  canUpdateUsers: boolean;
  canViewAuditLogs: boolean;
  canViewErrorLogs: boolean;
  canViewSystemMetrics: boolean;
}

// Admin Service Response Types
export interface AdminServiceResponse<T = any> {
  data?: T;
  message?: string;
  status: 'error' | 'success';
  timestamp: string;
}

// Admin Session
export interface AdminSession {
  createdAt: string;
  ipAddress: string;
  lastActivity: string;
  permissions: AdminPermissions;
  role: UserRole;
  sessionId: string;
  userAgent: string;
  userId: string;
}

// Admin Statistics
export interface AdminStatistics {
  auditLogs: {
    byAction: Record<string, number>;
    thisMonth: number;
    thisWeek: number;
    today: number;
    total: number;
  };
  performance: {
    averageResponseTime: number;
    databaseConnections: number;
    errorRate: number;
    requestsPerMinute: number;
  };
  system: {
    environment: string;
    lastRestart: string;
    uptime: number;
    version: string;
  };
  users: {
    active: number;
    byRole: Record<UserRole, number>;
    inactive: number;
    recentlyCreated: number;
    recentlyUpdated: number;
    total: number;
  };
}

export interface AuditLogFilterOptions {
  action?: string;
  endDate?: string;
  limit?: number;
  page?: number;
  search?: string;
  sortBy?: 'action' | 'created_at' | 'user_id';
  sortOrder?: 'asc' | 'desc';
  startDate?: string;
  userId?: string;
}

export interface EmailValidationResult extends ValidationResult {
  email?: string;
}

export interface ErrorLogFilterOptions {
  endDate?: string;
  level?: LogLevel;
  limit?: number;
  page?: number;
  sortBy?: 'level' | 'source' | 'timestamp';
  sortOrder?: 'asc' | 'desc';
  source?: string;
  startDate?: string;
}

export interface RoleValidationResult extends ValidationResult {
  role?: UserRole;
}

// Admin Filter Options
export interface UserFilterOptions {
  isActive?: boolean;
  limit?: number;
  page?: number;
  role?: string;
  search?: string;
  sortBy?: 'created_at' | 'email' | 'role' | 'updated_at';
  sortOrder?: 'asc' | 'desc';
}

export type UserRole = (typeof VALID_ROLES)[number];

// Admin Validation Types
export interface ValidationResult {
  errors: string[];
  isValid: boolean;
}

// Admin Error Types
export class AdminError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode = 500,
    public details?: any,
  ) {
    super(message);
    this.name = 'AdminError';
  }
}

export class AdminNotFoundError extends AdminError {
  constructor(message: string, details?: any) {
    super(message, 'ADMIN_NOT_FOUND_ERROR', 404, details);
    this.name = 'AdminNotFoundError';
  }
}

export class AdminPermissionError extends AdminError {
  constructor(message: string, details?: any) {
    super(message, 'ADMIN_PERMISSION_ERROR', 403, details);
    this.name = 'AdminPermissionError';
  }
}

export class AdminValidationError extends AdminError {
  constructor(message: string, details?: any) {
    super(message, 'ADMIN_VALIDATION_ERROR', 400, details);
    this.name = 'AdminValidationError';
  }
}

export default {
  AdminError,
  AdminNotFoundError,
  AdminPermissionError,
  AdminValidationError,
  VALID_ROLES,
};
