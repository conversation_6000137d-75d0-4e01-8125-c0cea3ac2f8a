// frontend/src/components/features/reporting/dashboard/ReportingDashboard.tsx

'use client';

import {
  BarChart3,
  Car,
  CheckSquare,
  Download,
  FileText,
  Filter,
  Network,
  RefreshCw,
  Settings,
  Table,
  TrendingUp,
  Users,
} from 'lucide-react';
import React from 'react';

import { DashboardPage } from '@/components/dashboard/DashboardLayout';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useDashboardStore } from '@/hooks/domain/useDashboardStore';

import { useRealtimeReportingUpdates } from '../data/hooks/useRealtimeReportingUpdates';
import {
  useReportingFilters,
  useReportingFiltersUI,
} from '../data/stores/useReportingFiltersStore';
import { TaskPriorityFilter, TaskStatusFilter } from '../filters';
import { ReportGenerationPage } from '../generation/ReportGenerationPage';
import { ReportBuilder } from '../management/ReportBuilder';
import { ReportTypeManager } from '../management/ReportTypeManager';
// Note: Using new export system from exports/hooks/useExport
import { TaskReportingTable } from '../tables';
import { reportingDashboardConfig } from './config/reportingDashboardConfig';
import { ReportingFilters } from './filters/ReportingFilters';
import { DashboardGrid } from './layout/DashboardGrid';
import { ReportingLayout } from './layout/ReportingLayout';
import {
  CrossEntityCorrelationWidget,
  DelegationStatusWidget,
  DelegationTrendWidget,
  EmployeeAnalyticsWidget,
  EmployeePerformanceChart,
  EmployeeWorkloadWidget,
  EntityRelationshipNetworkWidget,
  LocationDistributionWidget,
  ReportingTableWidget,
  TaskAssignmentMetrics,
  TaskMetricsWidget,
  TaskPriorityDistribution,
  TaskStatusChart,
  VehicleAnalyticsWidget,
  VehicleCostAnalyticsWidget,
  VehicleMaintenanceWidget,
  VehicleUtilizationChart,
} from './widgets';
// TEMPORARY: Import test function for React-PDF v4 compatibility testing

interface ReportingDashboardProps {
  className?: string;
}

/**
 * @component ReportingDashboard
 * @description Professional reporting dashboard with UX-optimized layout
 *
 * UX Improvements Applied:
 * - Simplified hierarchy: Single dashboard focus, removed redundant cards
 * - Consolidated actions: Single action bar for all dashboard operations
 * - Professional 12-column grid system with proper widget sizing
 * - Improved spacing and alignment throughout
 * - Clean, consistent visual hierarchy
 *
 * Responsibilities:
 * - Orchestrates all reporting dashboard components
 * - Manages clean, professional layout with strict grid alignment
 * - Integrates with existing dashboard framework
 * - Handles real-time updates and data synchronization
 */
export const ReportingDashboard: React.FC<ReportingDashboardProps> = ({
  className = '',
}) => {
  const [activeTab, setActiveTab] = React.useState('overview');
  const [isFilterPanelOpen, setIsFilterPanelOpen] = React.useState(false);

  // Simplified filters to avoid store issues
  const filters = {
    costRange: { max: 10_000, min: 0 },
    dateRange: {
      from: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      to: new Date(),
    },
    employees: [],
    includeServiceHistory: false,
    includeTaskData: false,
    locations: [],
    serviceStatus: [],
    serviceTypes: [],
    status: [],
    vehicles: [],
  };

  const toggleFilterPanel = () => setIsFilterPanelOpen(!isFilterPanelOpen);

  // TODO: Re-enable real-time updates after fixing store
  // useRealtimeReportingUpdates(filters);

  // Professional dashboard tabs configuration
  const dashboardTabs = [
    {
      description: 'High-level metrics and key performance indicators',
      icon: <BarChart3 className="size-4" />,
      id: 'overview',
      label: 'Overview',
    },
    {
      description: 'Detailed analytics and trend analysis',
      icon: <TrendingUp className="size-4" />,
      id: 'analytics',
      label: 'Analytics',
    },
    {
      description: 'Task metrics and performance analysis',
      icon: <CheckSquare className="size-4" />,
      id: 'tasks',
      label: 'Tasks',
    },
    {
      description: 'Vehicle utilization and maintenance analytics',
      icon: <Car className="size-4" />,
      id: 'vehicles',
      label: 'Vehicles',
    },
    {
      description: 'Employee performance and workload analysis',
      icon: <Users className="size-4" />,
      id: 'employees',
      label: 'Employees',
    },
    {
      description: 'Cross-entity relationships and correlations',
      icon: <Network className="size-4" />,
      id: 'correlations',
      label: 'Correlations',
    },
    {
      description: 'Generate comprehensive data reports for all entities',
      icon: <FileText className="size-4" />,
      id: 'generation',
      label: 'Generate Reports',
    },
    {
      description: 'Manage report types and build custom reports',
      icon: <Settings className="size-4" />,
      id: 'management',
      label: 'Management',
    },
    {
      description: 'Raw delegation data in tabular format',
      icon: <Table className="size-4" />,
      id: 'data',
      label: 'Data',
    },
  ];

  // Professional 12-column grid widget configurations
  const getWidgetsForTab = (tabId: string) => {
    switch (tabId) {
      case 'overview': {
        return [
          {
            component: DelegationStatusWidget,
            id: 'status',
            span: 'col-span-12 lg:col-span-4', // 1/3 width on large screens
          },
          {
            component: TaskMetricsWidget,
            id: 'tasks',
            span: 'col-span-12 lg:col-span-8', // 2/3 width on large screens
          },
          {
            component: DelegationTrendWidget,
            id: 'trend',
            span: 'col-span-12 lg:col-span-8', // Full width trend chart
          },
          {
            component: LocationDistributionWidget,
            id: 'location',
            span: 'col-span-12 lg:col-span-4', // 1/3 width location chart
          },
        ];
      }
      case 'analytics': {
        return [
          {
            component: DelegationTrendWidget,
            id: 'trend',
            span: 'col-span-12 lg:col-span-8', // 2/3 width for main trend
          },
          {
            component: LocationDistributionWidget,
            id: 'location',
            span: 'col-span-12 lg:col-span-4', // 1/3 width for location
          },
          {
            component: TaskMetricsWidget,
            id: 'tasks',
            span: 'col-span-12 lg:col-span-6', // Half width task metrics
          },
          {
            component: DelegationStatusWidget,
            id: 'status',
            span: 'col-span-12 lg:col-span-6', // Half width status
          },
        ];
      }
      case 'correlations': {
        return [
          {
            component: CrossEntityCorrelationWidget,
            id: 'cross-entity-correlations',
            span: 'col-span-12', // Full width for complex correlations
          },
          {
            component: EntityRelationshipNetworkWidget,
            id: 'entity-relationships',
            span: 'col-span-12 lg:col-span-6',
          },
          {
            component: TaskMetricsWidget,
            id: 'task-correlations',
            span: 'col-span-12 lg:col-span-6',
          },
        ];
      }
      case 'tasks': {
        return [
          {
            component: TaskMetricsWidget,
            id: 'task-metrics',
            span: 'col-span-12', // Full width for detailed task view
          },
          {
            component: TaskAssignmentMetrics,
            id: 'task-assignments',
            span: 'col-span-12 lg:col-span-6',
          },
          {
            component: TaskStatusChart,
            id: 'task-status-chart',
            span: 'col-span-12 lg:col-span-6',
          },
        ];
      }
      case 'vehicles': {
        return [
          {
            component: VehicleAnalyticsWidget,
            id: 'vehicle-analytics',
            span: 'col-span-12 lg:col-span-6',
          },
          {
            component: VehicleUtilizationChart,
            id: 'vehicle-utilization',
            span: 'col-span-12 lg:col-span-6',
          },
          {
            component: VehicleMaintenanceWidget,
            id: 'vehicle-maintenance',
            span: 'col-span-12 lg:col-span-6',
          },
          {
            component: VehicleCostAnalyticsWidget,
            id: 'vehicle-costs',
            span: 'col-span-12 lg:col-span-6',
          },
        ];
      }
      case 'employees': {
        return [
          {
            component: EmployeeAnalyticsWidget,
            id: 'employee-analytics',
            span: 'col-span-12 lg:col-span-8',
          },
          {
            component: EmployeeWorkloadWidget,
            id: 'employee-workload',
            span: 'col-span-12 lg:col-span-4',
          },
          {
            component: EmployeePerformanceChart,
            id: 'employee-performance',
            span: 'col-span-12',
          },
        ];
      }
      case 'generation': {
        return [
          {
            component: () => <ReportGenerationPage />,
            id: 'report-generation',
            span: 'col-span-12',
          },
        ];
      }
      case 'management': {
        return [
          {
            component: () => <ReportTypeManager />,
            id: 'report-type-manager',
            span: 'col-span-12 lg:col-span-6',
          },
          {
            component: () => <ReportBuilder />,
            id: 'report-builder',
            span: 'col-span-12 lg:col-span-6',
          },
        ];
      }
      case 'data': {
        return [
          {
            component: () => <TaskReportingTable />,
            id: 'data-table',
            span: 'col-span-12',
          },
        ];
      }
      default: {
        return [];
      }
    }
  };

  // Consolidated export functionality
  const handleExportDashboard = async () => {
    try {
      const exportData = {
        activeTab: activeTab,
        filters: filters,
        metadata: {
          exportedBy: 'Reporting Dashboard',
          version: '2.0.0',
        },
        timestamp: new Date().toISOString(),
      };

      const blob = new Blob([JSON.stringify(exportData, null, 2)], {
        type: 'application/json',
      });
      const url = globalThis.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `dashboard-export-${new Date().toISOString().split('T')[0]}.json`;
      document.body.append(a);
      a.click();
      a.remove();
      globalThis.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Export failed:', error);
    }
  };

  const handleRefreshDashboard = () => {
    globalThis.location.reload();
  };

  // Consolidated action bar - single, clean interface
  const renderDashboardActions = () => (
    <div className="flex items-center gap-3">
      <Button
        className={
          isFilterPanelOpen ? 'bg-primary text-primary-foreground' : ''
        }
        onClick={toggleFilterPanel}
        size="sm"
        variant="outline"
      >
        <Filter className="mr-2 size-4" />
        Filters
      </Button>
      <Button onClick={handleRefreshDashboard} size="sm" variant="outline">
        <RefreshCw className="mr-2 size-4" />
        Refresh
      </Button>
      <Button onClick={handleExportDashboard} size="sm" variant="outline">
        <Download className="mr-2 size-4" />
        Export
      </Button>
    </div>
  );

  // Clean filter summary
  const renderFilterSummary = () => {
    const activeFilters = [];
    if (filters.status.length > 0)
      activeFilters.push(`${filters.status.length} status`);
    if (filters.locations.length > 0)
      activeFilters.push(`${filters.locations.length} locations`);
    if (filters.employees.length > 0)
      activeFilters.push(`${filters.employees.length} employees`);
    if (filters.vehicles.length > 0)
      activeFilters.push(`${filters.vehicles.length} vehicles`);

    if (activeFilters.length === 0) return null;

    return (
      <div className="flex items-center gap-3 text-sm text-muted-foreground">
        <span>Active filters:</span>
        {activeFilters.map((filter, index) => (
          <Badge
            className="text-xs"
            key={`filter-${index}-${filter}`}
            variant="secondary"
          >
            {filter}
          </Badge>
        ))}
      </div>
    );
  };

  // Professional widget rendering with 12-column grid
  const renderWidgets = () => {
    const widgets = getWidgetsForTab(activeTab);

    return (
      <div className="grid grid-cols-12 gap-6">
        {widgets.map(({ component: WidgetComponent, id, span }) => (
          <div className={span} key={`${activeTab}-${id}`}>
            <WidgetComponent />
          </div>
        ))}
      </div>
    );
  };

  return (
    <DashboardPage className={className} config={reportingDashboardConfig}>
      <ReportingLayout
        actions={renderDashboardActions()}
        description="Interactive dashboard with real-time metrics and insights"
        filters={isFilterPanelOpen ? <ReportingFilters /> : undefined}
        title="Reporting Dashboard"
      >
        <div className="space-y-8">
          {/* Clean filter summary */}
          {renderFilterSummary()}

          {/* Professional dashboard tabs */}
          <Tabs
            className="w-full"
            onValueChange={setActiveTab}
            value={activeTab}
          >
            <TabsList className="grid w-full grid-cols-9 h-12">
              {dashboardTabs.map(tab => (
                <TabsTrigger
                  className="flex items-center gap-2 text-sm font-medium"
                  key={tab.id}
                  value={tab.id}
                >
                  {tab.icon}
                  <span className="hidden sm:inline">{tab.label}</span>
                </TabsTrigger>
              ))}
            </TabsList>

            {dashboardTabs.map(tab => (
              <TabsContent className="space-y-8" key={tab.id} value={tab.id}>
                <div className="space-y-2">
                  <h2 className="text-2xl font-semibold tracking-tight flex items-center gap-3">
                    {tab.icon}
                    {tab.label}
                  </h2>
                  <p className="text-muted-foreground">{tab.description}</p>
                </div>

                {/* Professional 12-column grid layout */}
                {renderWidgets()}
              </TabsContent>
            ))}
          </Tabs>
        </div>
      </ReportingLayout>
    </DashboardPage>
  );
};

export default ReportingDashboard;
