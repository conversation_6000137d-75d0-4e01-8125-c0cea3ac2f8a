/**
 * @file useReportTypes.ts
 * @description Hook for managing report types following existing patterns
 */

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

import { useApiQuery } from '@/hooks/api';
import { apiClient } from '@/lib/api';

import type { ReportType } from '../data/types/reporting';

/**
 * Hook for managing report types
 *
 * Follows existing patterns from other management hooks.
 * Integrates with the established API and caching patterns.
 *
 * @returns Query and mutation functions for report type management
 */
export const useReportTypes = () => {
  const queryClient = useQueryClient();

  // Query for fetching all report types
  const reportTypesQuery = useApiQuery(
    ['report-types'],
    async (): Promise<ReportType[]> => {
      const result = (await apiClient.get('/reporting/report-types')) as any;
      // Extract the data array from the API response structure
      return result.data?.data || [];
    },
    {
      cacheDuration: 5 * 60 * 1000, // 5 minutes
      enableRetry: true,
    }
  );

  // Mutation for creating a new report type
  const createReportType = useMutation({
    mutationFn: async (
      reportTypeData: Partial<ReportType>
    ): Promise<ReportType> => {
      const result = (await apiClient.post(
        '/reporting/report-types',
        reportTypeData
      )) as any;
      // Extract the data from the API response structure
      return result.data?.data || result.data;
    },
    onSuccess: () => {
      // Invalidate and refetch report types
      queryClient.invalidateQueries({ queryKey: ['report-types'] });
    },
  });

  // Mutation for updating an existing report type
  const updateReportType = useMutation({
    mutationFn: async ({
      id,
      ...reportTypeData
    }: Partial<ReportType> & { id: string }): Promise<ReportType> => {
      const result = (await apiClient.put(
        `/reporting/report-types/${id}`,
        reportTypeData
      )) as any;
      // Extract the data from the API response structure
      return result.data?.data || result.data;
    },
    onSuccess: () => {
      // Invalidate and refetch report types
      queryClient.invalidateQueries({ queryKey: ['report-types'] });
    },
  });

  // Mutation for deleting a report type
  const deleteReportType = useMutation({
    mutationFn: async (id: string): Promise<void> => {
      await apiClient.delete(`/reporting/report-types/${id}`);
    },
    onSuccess: () => {
      // Invalidate and refetch report types
      queryClient.invalidateQueries({ queryKey: ['report-types'] });
    },
  });

  // Mutation for duplicating a report type
  const duplicateReportType = useMutation({
    mutationFn: async (id: string): Promise<ReportType> => {
      const result = (await apiClient.post(
        `/reporting/report-types/${id}/duplicate`
      )) as any;
      // Extract the data from the API response structure
      return result.data?.data || result.data;
    },
    onSuccess: () => {
      // Invalidate and refetch report types
      queryClient.invalidateQueries({ queryKey: ['report-types'] });
    },
  });

  // Mutation for toggling report type active status
  const toggleReportTypeActive = useMutation({
    mutationFn: async ({
      id,
      isActive,
    }: {
      id: string;
      isActive: boolean;
    }): Promise<ReportType> => {
      const result = (await apiClient.patch(
        `/reporting/report-types/${id}/toggle-active`,
        { isActive }
      )) as any;
      // Extract the data from the API response structure
      return result.data?.data || result.data;
    },
    onSuccess: () => {
      // Invalidate and refetch report types
      queryClient.invalidateQueries({ queryKey: ['report-types'] });
    },
  });

  return {
    // Mutations
    createReportType,
    // Query data
    data: reportTypesQuery.data,
    deleteReportType,

    duplicateReportType,
    error: reportTypesQuery.error,
    isLoading: reportTypesQuery.isLoading,
    // Utility functions
    refetch: reportTypesQuery.refetch,
    toggleReportTypeActive,

    updateReportType,
  };
};

/**
 * Hook for fetching a single report type by ID
 *
 * @param id - Report type ID
 * @returns Query result with single report type data
 */
export const useReportType = (id: string) => {
  return useApiQuery(
    ['report-type', id],
    async (): Promise<ReportType> => {
      const result = (await apiClient.get(
        `/reporting/report-types/${id}`
      )) as any;
      // Extract the data from the API response structure
      return result.data?.data || result.data;
    },
    {
      cacheDuration: 5 * 60 * 1000,
      enabled: !!id,
      enableRetry: true,
    }
  );
};

/**
 * Hook for fetching report types by category
 *
 * @param category - Report type category
 * @returns Query result with filtered report types
 */
export const useReportTypesByCategory = (category: string) => {
  return useApiQuery(
    ['report-types', 'category', category],
    async (): Promise<ReportType[]> => {
      const result = (await apiClient.get(
        `/reporting/report-types?category=${encodeURIComponent(category)}`
      )) as any;
      // Extract the data array from the API response structure
      return result.data?.data || [];
    },
    {
      cacheDuration: 5 * 60 * 1000,
      enabled: !!category,
      enableRetry: true,
    }
  );
};
