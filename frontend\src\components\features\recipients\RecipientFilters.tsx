'use client';

import { Filter, Search, X } from 'lucide-react';
import { useMemo } from 'react';

import type { Recipient } from '@/lib/types/domain';

import { ActionButton } from '@/components/ui/action-button';
import { Badge } from '@/components/ui/badge';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface FilterState {
  search: string;
  hasEmail: string; // 'all', 'yes', 'no'
  hasPhone: string; // 'all', 'yes', 'no'
  hasAddress: string; // 'all', 'yes', 'no'
  giftCount: string; // 'all', 'none', 'some', 'many'
}

interface RecipientFiltersProps {
  filters: FilterState;
  onFilterChange: (filters: Partial<FilterState>) => void;
  onClearFilters: () => void;
  hasActiveFilters: boolean;
  recipients: Recipient[]; // For calculating statistics
}

export const RecipientFilters: React.FC<RecipientFiltersProps> = ({
  filters,
  onFilterChange,
  onClearFilters,
  hasActiveFilters,
  recipients,
}) => {
  // Calculate statistics for filter options
  const statistics = useMemo(() => {
    const stats = {
      total: recipients.length,
      withEmail: 0,
      withPhone: 0,
      withAddress: 0,
      withNoGifts: 0,
      withSomeGifts: 0,
      withManyGifts: 0,
    };

    recipients.forEach(recipient => {
      if (recipient.email) stats.withEmail++;
      if (recipient.phone) stats.withPhone++;
      if (recipient.address) stats.withAddress++;

      const giftCount = recipient.gifts?.length || 0;
      if (giftCount === 0) stats.withNoGifts++;
      else if (giftCount < 5) stats.withSomeGifts++;
      else stats.withManyGifts++;
    });

    return stats;
  }, [recipients]);

  const handleSearchChange = (value: string) => {
    onFilterChange({ search: value });
  };

  const handleEmailFilterChange = (value: string) => {
    onFilterChange({ hasEmail: value });
  };

  const handlePhoneFilterChange = (value: string) => {
    onFilterChange({ hasPhone: value });
  };

  const handleAddressFilterChange = (value: string) => {
    onFilterChange({ hasAddress: value });
  };

  const handleGiftCountFilterChange = (value: string) => {
    onFilterChange({ giftCount: value });
  };

  const activeFilterCount = [
    filters.search,
    filters.hasEmail !== 'all' ? filters.hasEmail : '',
    filters.hasPhone !== 'all' ? filters.hasPhone : '',
    filters.hasAddress !== 'all' ? filters.hasAddress : '',
    filters.giftCount !== 'all' ? filters.giftCount : '',
  ].filter(Boolean).length;

  return (
    <Card>
      <Collapsible>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <CollapsibleTrigger asChild>
                <ActionButton variant="ghost" size="sm" className="p-0">
                  <Filter className="h-4 w-4" />
                </ActionButton>
              </CollapsibleTrigger>
              <CardTitle className="text-base">Filters</CardTitle>
              {activeFilterCount > 0 && (
                <Badge variant="secondary" className="ml-2">
                  {activeFilterCount}
                </Badge>
              )}
            </div>
            {hasActiveFilters && (
              <ActionButton
                variant="ghost"
                size="sm"
                onClick={onClearFilters}
                className="text-muted-foreground hover:text-foreground"
              >
                <X className="h-4 w-4 mr-1" />
                Clear All
              </ActionButton>
            )}
          </div>
          <CardDescription>
            Filter recipients by contact information and gift history
          </CardDescription>
        </CardHeader>

        <CollapsibleContent>
          <CardContent className="space-y-4">
            {/* Search */}
            <div className="space-y-2">
              <Label htmlFor="search">Search</Label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                <Input
                  id="search"
                  placeholder="Search recipients by name, email, phone, or notes..."
                  value={filters.search}
                  onChange={(e) => handleSearchChange(e.target.value)}
                  className="pl-9"
                />
              </div>
            </div>

            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              {/* Email Filter */}
              <div className="space-y-2">
                <Label htmlFor="email-filter">Email Address</Label>
                <Select
                  value={filters.hasEmail}
                  onValueChange={handleEmailFilterChange}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All recipients" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All recipients ({statistics.total})</SelectItem>
                    <SelectItem value="yes">Has email ({statistics.withEmail})</SelectItem>
                    <SelectItem value="no">No email ({statistics.total - statistics.withEmail})</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Phone Filter */}
              <div className="space-y-2">
                <Label htmlFor="phone-filter">Phone Number</Label>
                <Select
                  value={filters.hasPhone}
                  onValueChange={handlePhoneFilterChange}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All recipients" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All recipients ({statistics.total})</SelectItem>
                    <SelectItem value="yes">Has phone ({statistics.withPhone})</SelectItem>
                    <SelectItem value="no">No phone ({statistics.total - statistics.withPhone})</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Address Filter */}
              <div className="space-y-2">
                <Label htmlFor="address-filter">Address</Label>
                <Select
                  value={filters.hasAddress}
                  onValueChange={handleAddressFilterChange}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All recipients" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All recipients ({statistics.total})</SelectItem>
                    <SelectItem value="yes">Has address ({statistics.withAddress})</SelectItem>
                    <SelectItem value="no">No address ({statistics.total - statistics.withAddress})</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Gift Count Filter */}
              <div className="space-y-2">
                <Label htmlFor="gift-count-filter">Gift History</Label>
                <Select
                  value={filters.giftCount}
                  onValueChange={handleGiftCountFilterChange}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All recipients" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All recipients ({statistics.total})</SelectItem>
                    <SelectItem value="none">No gifts ({statistics.withNoGifts})</SelectItem>
                    <SelectItem value="some">1-4 gifts ({statistics.withSomeGifts})</SelectItem>
                    <SelectItem value="many">5+ gifts ({statistics.withManyGifts})</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Statistics Summary */}
            <div className="pt-4 border-t">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div className="text-center">
                  <div className="font-semibold text-lg">{statistics.total}</div>
                  <div className="text-muted-foreground">Total Recipients</div>
                </div>
                <div className="text-center">
                  <div className="font-semibold text-lg">{statistics.withEmail}</div>
                  <div className="text-muted-foreground">With Email</div>
                </div>
                <div className="text-center">
                  <div className="font-semibold text-lg">{statistics.withPhone}</div>
                  <div className="text-muted-foreground">With Phone</div>
                </div>
                <div className="text-center">
                  <div className="font-semibold text-lg">{statistics.withAddress}</div>
                  <div className="text-muted-foreground">With Address</div>
                </div>
              </div>
            </div>
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </Card>
  );
};
