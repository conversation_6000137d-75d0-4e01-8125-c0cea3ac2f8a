# WorkHub Environment Configuration - Security Enhanced Staging
# This file contains environment variables for the entire application

# Database Configuration
DATABASE_URL=postgresql://postgres.abylqjnpaegeqwktcukn:<EMAIL>:5432/postgres

# Supabase Configuration
# CRITICAL: Set to 'true' to enable Supabase authentication and database features
USE_SUPABASE=true
SUPABASE_URL=https://abylqjnpaegeqwktcukn.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFieWxxam5wYWVnZXF3a3RjdWtuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcyMTM0NTMsImV4cCI6MjA2Mjc4OTQ1M30.WCzj8fDu7vdxhvbOUuoQHVamy9-XYBr4vtTox52ap2o
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFieWxxam5wYWVnZXF3a3RjdWtuIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NzIxMzQ1MywiZXhwIjoyMDYyNzg5NDUzfQ.yLrGESZvVC6ISrqlcKeR3uvfRqdWPcZqYqLLZkjphU8

# JWT Configuration
JWT_SECRET=JJxeCe52j/9tIaQLW9guDu+pBpSRp//c4PXnj7mV/oyTUdwSBGIOfmpKEAGPY3hA3cBkcu2o2xbw/FiHIKtFUw==

# Frontend Configuration
FRONTEND_URL=http://localhost:3000

# Redis Configuration (for distributed rate limiting and request deduplication)
# REDIS_URL=redis://localhost:6379

# Request Deduplication Configuration - Reliability Enhancement
REQUEST_DEDUP_ENABLED=true
REQUEST_DEDUP_DEFAULT_TTL=300

# Service-Specific Deduplication TTL Settings (in seconds)
ADMIN_DEDUP_TTL=300
API_DEDUP_TTL=60
PERFORMANCE_DEDUP_TTL=30
IDEMPOTENT_DEDUP_TTL=600

# Nginx IP Allowlist Configuration (for reverse proxy support)
# NGINX_PROXY_IPS=10.0.0.0/8,**********/12,***********/16
