// frontend/src/lib/enums/reporting.ts

/**
 * Centralized Reporting Enums
 *
 * Single source of truth for all reporting-related enums and constants.
 * Follows DRY principle by centralizing enum definitions from domain layer.
 */

// Re-export domain types for reporting consistency
export type {
  DelegationStatusPrisma as DelegationStatus,
  TaskStatusPrisma as TaskStatus,
  TaskPriorityPrisma as TaskPriority,
} from '@/lib/types/domain';

// Import types for internal use
import type {
  DelegationStatusPrisma,
  TaskStatusPrisma,
  TaskPriorityPrisma,
} from '@/lib/types/domain';

// Status color mappings for UI consistency
export const DELEGATION_STATUS_COLORS = {
  Planned: '#3b82f6', // Blue
  Confirmed: '#10b981', // Green
  In_Progress: '#f59e0b', // Amber
  Completed: '#22c55e', // Green
  Cancelled: '#ef4444', // Red
  No_details: '#6b7280', // Gray
} as const;

export const TASK_STATUS_COLORS = {
  Pending: '#6b7280', // Gray
  Assigned: '#3b82f6', // Blue
  In_Progress: '#f59e0b', // Amber
  Completed: '#22c55e', // Green
  Cancelled: '#ef4444', // Red
} as const;

export const TASK_PRIORITY_COLORS = {
  Low: '#22c55e', // Green
  Medium: '#f59e0b', // Amber
  High: '#ef4444', // Red
} as const;

// Status display labels for UI
export const DELEGATION_STATUS_LABELS = {
  Planned: 'Planned',
  Confirmed: 'Confirmed',
  In_Progress: 'In Progress',
  Completed: 'Completed',
  Cancelled: 'Cancelled',
  No_details: 'No Details',
} as const;

export const TASK_STATUS_LABELS = {
  Pending: 'Pending',
  Assigned: 'Assigned',
  In_Progress: 'In Progress',
  Completed: 'Completed',
  Cancelled: 'Cancelled',
} as const;

export const TASK_PRIORITY_LABELS = {
  Low: 'Low Priority',
  Medium: 'Medium Priority',
  High: 'High Priority',
} as const;

// Helper functions for enum operations
export const getDelegationStatusColor = (
  status: DelegationStatusPrisma
): string => {
  return DELEGATION_STATUS_COLORS[status] || '#6b7280';
};

export const getTaskStatusColor = (status: TaskStatusPrisma): string => {
  return TASK_STATUS_COLORS[status] || '#6b7280';
};

export const getTaskPriorityColor = (priority: TaskPriorityPrisma): string => {
  return TASK_PRIORITY_COLORS[priority] || '#6b7280';
};

export const getDelegationStatusLabel = (
  status: DelegationStatusPrisma
): string => {
  return DELEGATION_STATUS_LABELS[status] || status;
};

export const getTaskStatusLabel = (status: TaskStatusPrisma): string => {
  return TASK_STATUS_LABELS[status] || status;
};

export const getTaskPriorityLabel = (priority: TaskPriorityPrisma): string => {
  return TASK_PRIORITY_LABELS[priority] || priority;
};

// Validation helpers
export const isValidDelegationStatus = (
  status: string
): status is DelegationStatusPrisma => {
  return Object.keys(DELEGATION_STATUS_COLORS).includes(status);
};

export const isValidTaskStatus = (
  status: string
): status is TaskStatusPrisma => {
  return Object.keys(TASK_STATUS_COLORS).includes(status);
};

export const isValidTaskPriority = (
  priority: string
): priority is TaskPriorityPrisma => {
  return Object.keys(TASK_PRIORITY_COLORS).includes(priority);
};
