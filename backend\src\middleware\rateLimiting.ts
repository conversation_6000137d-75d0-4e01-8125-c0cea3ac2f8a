import type { NextFunction, Request, Response } from 'express';

import { rateLimit } from 'express-rate-limit';
import { RateLimiterMemory, RateLimiterRedis } from 'rate-limiter-flexible';

import { getRedisClient, isRedisAvailable } from '../services/redis.service.js';
import logger from '../utils/logger.js';

/**
 * PHASE 1 SECURITY HARDENING: Rate Limiting Implementation
 *
 * This module provides comprehensive rate limiting protection against:
 * - Brute force attacks
 * - DDoS attacks
 * - API abuse
 * - Resource exhaustion
 *
 * Features:
 * - Multiple rate limiting strategies
 * - Redis support for distributed systems
 * - Endpoint-specific limits
 * - Security headers
 * - Graceful degradation
 */

// Rate limiting configuration
const RATE_LIMIT_CONFIG = {
  // Admin endpoints (environment-specific)
  admin: {
    limit: process.env.NODE_ENV === 'development' ? 100 : 50, // 100 dev, 50 prod
    message: {
      code: 'ADMIN_RATE_LIMIT_EXCEEDED',
      error: 'Admin endpoint rate limit exceeded.',
      retryAfter: process.env.NODE_ENV === 'development' ? '1 minute' : '5 minutes',
    },
    windowMs: process.env.NODE_ENV === 'development' ? 1 * 60 * 1000 : 5 * 60 * 1000, // 1 min dev, 5 min prod
  },

  // API endpoints (moderate) - Increased limits for development
  api: {
    limit: process.env.NODE_ENV === 'development' ? 1000 : 100, // Higher limit for development
    message: {
      code: 'API_RATE_LIMIT_EXCEEDED',
      error: 'API rate limit exceeded, please slow down.',
      retryAfter: '1 minute',
    },
    windowMs: 1 * 60 * 1000, // 1 minute
  },

  // Authentication endpoints (stricter) - SECURITY HARDENING
  auth: {
    limit: 5, // Reduced from 10 to 5 requests per window per IP
    message: {
      code: 'AUTH_RATE_LIMIT_EXCEEDED',
      error: 'Too many authentication attempts, please try again later.',
      retryAfter: '15 minutes',
    },
    windowMs: 15 * 60 * 1000, // 15 minutes
  },

  // Global API rate limits
  global: {
    limit: 1000, // requests per window per IP
    message: {
      code: 'RATE_LIMIT_EXCEEDED',
      error: 'Too many requests from this IP, please try again later.',
      retryAfter: '15 minutes',
    },
    windowMs: 15 * 60 * 1000, // 15 minutes
  },

  // File upload endpoints (strict)
  upload: {
    limit: 5, // requests per window per IP
    message: {
      code: 'UPLOAD_RATE_LIMIT_EXCEEDED',
      error: 'Upload rate limit exceeded, please wait before uploading again.',
      retryAfter: '10 minutes',
    },
    windowMs: 10 * 60 * 1000, // 10 minutes
  },
};

// Redis configuration for distributed rate limiting - Updated to use centralized service
let rateLimiterRedis: null | RateLimiterRedis = null;

/**
 * Initialize Redis rate limiter using centralized Redis service
 *
 * @returns {Promise<void>}
 */
const initializeRateLimiterRedis = async (): Promise<void> => {
  try {
    const redisClient = getRedisClient();
    const redisAvailable = await isRedisAvailable();

    if (redisClient && redisAvailable) {
      rateLimiterRedis = new RateLimiterRedis({
        duration: 60, // Per 60 seconds
        keyPrefix: 'workhub_rl',
        points: 100, // Number of requests
        storeClient: redisClient,
      });

      logger.info('Redis rate limiting initialized successfully', {
        backend: 'redis',
        keyPrefix: 'workhub_rl',
        service: 'rate-limiting',
      });
    } else {
      logger.warn('Redis not available, using memory-based rate limiting', {
        backend: 'memory',
        service: 'rate-limiting',
      });
    }
  } catch (error) {
    logger.error('Redis rate limiting initialization failed', {
      error: error instanceof Error ? error.message : 'Unknown error',
      service: 'rate-limiting',
    });
  }
};

// LEGACY CODE REMOVED: getSharedRedisClient function deprecated
// Use getRedisClient from redis.service.js instead

// Initialize Redis rate limiter on module load
await initializeRateLimiterRedis();

/**
 * Create rate limiter with custom configuration
 */
const createRateLimiter = (config: typeof RATE_LIMIT_CONFIG.global) => {
  return rateLimit({
    // Custom handler for rate limit exceeded
    handler: (req: Request, res: Response, _next: NextFunction) => {
      // Add security headers
      res.setHeader('X-Rate-Limit-Policy', 'PHASE-1-HARDENED');
      res.setHeader('X-Security-Incident', 'RATE_LIMIT_EXCEEDED');
      res.setHeader('X-Security-Timestamp', new Date().toISOString());

      // Log rate limit violation
      logger.warn('Rate limit exceeded', {
        ip: req.ip,
        path: req.path,
        securityEvent: 'RATE_LIMIT_EXCEEDED',
        service: 'rate-limiting',
        userAgent: req.get('User-Agent'),
      });

      // Send rate limit response
      res.status(429).json(config.message);
    },
    // Custom key generator for better IP detection
    keyGenerator: (req: Request): string => {
      // Handle proxy scenarios
      const forwarded = req.headers['x-forwarded-for'];
      if (forwarded && typeof forwarded === 'string') {
        // Take the first IP from the chain
        const ip = forwarded.split(',')[0].trim();
        // Remove port if present (e.g., "***********:8080" -> "***********")
        return ip.replace(/:\d+[^:]*$/, '');
      }

      return req.ip ?? req.socket.remoteAddress ?? 'unknown';
    },
    legacyHeaders: false, // Disable legacy X-RateLimit-* headers
    limit: config.limit,
    message: config.message,

    // Only count failed requests for auth endpoints
    requestWasSuccessful: (req: Request, res: Response): boolean => {
      // For auth endpoints, only count failed attempts
      if (req.path.includes('/auth') || req.path.includes('/login')) {
        return res.statusCode < 400;
      }

      // For other endpoints, count all requests
      return true;
    },

    // Skip function for allowlisted IPs or authenticated admin users
    skip: (req: Request): boolean => {
      // Skip rate limiting for localhost and network IPs in development (both IPv4 and IPv6)
      if (process.env.NODE_ENV === 'development') {
        const ip = req.ip;
        if (
          ip === '127.0.0.1' ||
          ip === '::1' ||
          ip === '::ffff:127.0.0.1' ||
          ip === '**************'
        ) {
          logger.debug('Skipping rate limit for development IP', {
            environment: 'development',
            ip,
            service: 'rate-limiting',
          });
          return true;
        }
      }

      // Skip for allowlisted IPs (if configured)
      const allowlist = process.env.RATE_LIMIT_ALLOWLIST?.split(',') ?? [];
      if (req.ip && allowlist.includes(req.ip)) {
        return true;
      }

      return false;
    },

    standardHeaders: 'draft-8', // Use latest standard headers

    windowMs: config.windowMs,
  });
};

/**
 * Advanced rate limiter using rate-limiter-flexible for complex scenarios
 */
// New type for adaptive limits
interface AdaptiveLimits {
  points: number;
  duration: number;
}

const createAdvancedRateLimiter = (getLimits: (req: Request) => AdaptiveLimits) => {
  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const { points, duration } = getLimits(req); // Get dynamic limits

    // Create a new limiter instance for each request based on dynamic limits
    const limiter =
      rateLimiterRedis ??
      new RateLimiterMemory({
        duration, // Per duration in seconds
        points, // Number of requests
      });

    const key = req.ip ?? 'unknown';

    // Skip rate limiting for localhost in development (both IPv4 and IPv6)
    if (process.env.NODE_ENV === 'development') {
      const ip = req.ip;
      if (ip === '127.0.0.1' || ip === '::1' || ip === '::ffff:127.0.0.1') {
        logger.debug('Skipping adaptive rate limit for localhost', {
          environment: 'development',
          ip,
          service: 'rate-limiting',
          type: 'adaptive',
        });
        next();
        return;
      }
    }

    try {
      await limiter.consume(key);
      next();
    } catch (error) {
      // Rate limit exceeded
      const rateLimiterRes = error as { msBeforeNext: number; remainingPoints?: number };
      const secs = Math.round(rateLimiterRes.msBeforeNext / 1000) || 1;

      res.set({
        'Retry-After': String(secs),
        'X-Rate-Limit-Policy': 'PHASE-2-ADAPTIVE',
        'X-RateLimit-Limit': String(points),
        'X-RateLimit-Remaining': String(rateLimiterRes.remainingPoints ?? 0),
        'X-RateLimit-Reset': String(Math.ceil((Date.now() + rateLimiterRes.msBeforeNext) / 1000)),
      });

      logger.warn('Adaptive rate limit exceeded', {
        ip: req.ip,
        remainingPoints: rateLimiterRes.remainingPoints ?? 0,
        retryAfterSeconds: secs,
        securityEvent: 'ADAPTIVE_RATE_LIMIT_EXCEEDED',
        service: 'rate-limiting',
        type: 'adaptive',
        role: req.userRole || 'anonymous', // Log user role
      });

      res.status(429).json({
        code: 'ADAPTIVE_RATE_LIMIT_EXCEEDED',
        error: 'Rate limit exceeded',
        retryAfter: `${String(secs)} seconds`,
      });
    }
  };
};

// Adaptive limits based on user role
const getAdaptiveLimits = (req: Request): AdaptiveLimits => {
  const role = req.userRole; // Use req.userRole as confirmed from jwtAuth.middleware.ts

  switch (role) {
    case 'admin':
      return { points: 200, duration: 60 }; // 200 requests per minute for admins
    case 'user': // Assuming 'user' is a common role for authenticated users
      return { points: 100, duration: 60 }; // 100 requests per minute for authenticated users
    default:
      return { points: 50, duration: 60 }; // 50 requests per minute for anonymous/unrecognized roles
  }
};

// Pre-configured rate limiters
export const globalRateLimit = createRateLimiter(RATE_LIMIT_CONFIG.global);
export const authRateLimit = createRateLimiter(RATE_LIMIT_CONFIG.auth);
export const apiRateLimit = createRateLimiter(RATE_LIMIT_CONFIG.api);
export const adminRateLimit = createRateLimiter(RATE_LIMIT_CONFIG.admin);
export const uploadRateLimit = createRateLimiter(RATE_LIMIT_CONFIG.upload);

// Advanced rate limiters (keeping existing ones for now, adding adaptive)
export const strictRateLimit = createAdvancedRateLimiter(() => ({ points: 10, duration: 60 })); // 10 requests per minute
export const bruteForceProtection = createAdvancedRateLimiter(() => ({ points: 5, duration: 300 })); // 5 attempts per 5 minutes
export const adaptiveRateLimit = createAdvancedRateLimiter(getAdaptiveLimits); // New adaptive rate limiter

// SECURITY HARDENING: Enhanced authentication protection
export const authBruteForceProtection = createAdvancedRateLimiter(() => ({
  points: 3,
  duration: 900,
})); // 3 attempts per 15 minutes
export const loginAttemptProtection = createAdvancedRateLimiter(() => ({
  points: 5,
  duration: 600,
})); // 5 attempts per 10 minutes

/**
 * Rate limiting security headers middleware
 */
export const addRateLimitSecurityHeaders = (
  req: Request,
  res: Response,
  next: NextFunction,
): void => {
  res.setHeader('X-Rate-Limit-Policy', 'PHASE-1-HARDENED');
  res.setHeader('X-Rate-Limit-Strategy', 'MULTI-LAYER');
  res.setHeader('X-Rate-Limit-Backend', rateLimiterRedis ? 'REDIS' : 'MEMORY');
  next();
};

/**
 * Rate limit status endpoint for monitoring
 */
export const getRateLimitStatus = async (req: Request, res: Response): Promise<void> => {
  const key = req.ip ?? 'unknown';

  try {
    if (rateLimiterRedis) {
      const resRateLimiter = await rateLimiterRedis.get(key);
      res.json({
        backend: 'redis',
        remaining: resRateLimiter?.remainingPoints ?? 0,
        resetTime: resRateLimiter ? new Date(Date.now() + resRateLimiter.msBeforeNext) : null,
        status: 'active',
      });
    } else {
      res.json({
        backend: 'memory',
        message: 'Memory-based rate limiting active',
        status: 'active',
      });
    }
  } catch (error) {
    logger.error('Failed to get rate limit status', {
      error: error instanceof Error ? error.message : 'Unknown error',
      service: 'rate-limiting',
    });
    res.status(500).json({
      message: 'Rate limit status unavailable',
      status: 'error',
    });
  }
};

export default {
  addRateLimitSecurityHeaders,
  adaptiveRateLimit, // Add new adaptive rate limit
  adminRateLimit,
  apiRateLimit,
  authRateLimit,
  bruteForceProtection,
  getRateLimitStatus,
  globalRateLimit,
  strictRateLimit,
  uploadRateLimit,
};
