/**
 * @file Enhanced Task Metrics Widget - UX Optimized Design
 * @description Clean, professional task analytics widget following UX best practices
 *
 * UX Improvements Applied:
 * - Dramatically increased internal padding for breathing room
 * - Simplified layout with clean two-column grid structure
 * - Removed redundant icons and clutter
 * - Enhanced typography hierarchy and readability
 * - Consolidated export actions to main dashboard toolbar
 * - Professional spacing using 8px increments
 *
 * SOLID Principles Applied:
 * - SRP: Single responsibility of displaying task metrics clearly
 * - OCP: Open for extension via props configuration
 * - DIP: Depends on existing widget framework abstractions
 */

'use client';

import React, { useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useTaskAnalytics } from '../../data/hooks/useReportingQueries';
import { useReportingFilters } from '../../data/stores';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import {
  CheckSquare,
  Clock,
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  Users,
  BarChart3,
} from 'lucide-react';
import { TaskStatusChart } from './TaskStatusChart';
import { TaskPriorityDistribution } from './TaskPriorityDistribution';
import { TaskAssignmentMetrics } from './TaskAssignmentMetrics';

interface TaskMetricsWidgetProps {
  className?: string;
  compact?: boolean;
  showCharts?: boolean;
}

/**
 * @component TaskMetricsWidget
 * @description Professional task metrics widget with clean, spacious design
 *
 * Key UX Improvements:
 * - Generous internal padding (8px spacing system)
 * - Simplified metric cards with clear hierarchy
 * - Clean typography with proper contrast
 * - Removed clutter and redundant visual elements
 * - Export actions moved to main dashboard toolbar
 */
export const TaskMetricsWidget: React.FC<TaskMetricsWidgetProps> = ({
  className = '',
  compact = false,
  showCharts = true,
}) => {
  // Use existing filter patterns and data hooks
  const filters = useReportingFilters();
  const {
    data: taskAnalytics,
    isLoading,
    error,
  } = useTaskAnalytics(filters, {
    enabled: true,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });

  // Memoized calculations for performance
  const metricsData = useMemo(() => {
    if (!taskAnalytics) return null;

    return {
      totalTasks: taskAnalytics.totalCount || 0,
      completedTasks:
        taskAnalytics.statusDistribution?.find(s => s.status === 'Completed')
          ?.count || 0,
      completionRate: Math.round((taskAnalytics.completionRate || 0) * 100),
      overdueTasks: taskAnalytics.overdueCount || 0,
      averageCompletionTime: taskAnalytics.averageCompletionTime || 0,
      assignedEmployees: taskAnalytics.assignmentMetrics?.length || 0,
    };
  }, [taskAnalytics]);

  // Loading state with improved skeleton
  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader className="pb-6">
          <Skeleton className="h-7 w-48" />
        </CardHeader>
        <CardContent className="space-y-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, i) => (
              <Skeleton key={i} className="h-32 rounded-lg" />
            ))}
          </div>
          {showCharts && !compact && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <Skeleton className="h-80 rounded-lg" />
              <Skeleton className="h-80 rounded-lg" />
            </div>
          )}
        </CardContent>
      </Card>
    );
  }

  // Clean error state
  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-3">
            <CheckSquare className="h-6 w-6" />
            Task Analytics
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-6">
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Error Loading Task Analytics</AlertTitle>
            <AlertDescription>
              {error.message || 'Failed to load task analytics data'}
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  // Main widget render with professional spacing and clean design
  return (
    <Card className={`${className} overflow-hidden`}>
      {/* Clean header with generous spacing */}
      <CardHeader className="pb-8">
        <CardTitle className="flex items-center gap-3 text-xl">
          <CheckSquare className="h-6 w-6 text-primary" />
          Task Analytics
        </CardTitle>
      </CardHeader>

      <CardContent className="px-8 pb-8 space-y-10">
        {/* Clean metrics grid with generous spacing */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {/* Total Tasks */}
          <div className="group flex flex-col items-center text-center p-8 rounded-xl border-2 border-border hover:border-primary/20 hover:bg-primary/5 transition-all duration-200">
            <div className="p-4 rounded-full bg-blue-100 dark:bg-blue-900/30 mb-6">
              <CheckSquare className="h-8 w-8 text-blue-600" />
            </div>
            <div className="text-4xl font-bold text-blue-600 mb-3">
              {metricsData?.totalTasks || 0}
            </div>
            <div className="text-sm font-medium text-muted-foreground uppercase tracking-wide">
              Total Tasks
            </div>
          </div>

          {/* Completed Tasks */}
          <div className="group flex flex-col items-center text-center p-8 rounded-xl border-2 border-border hover:border-primary/20 hover:bg-primary/5 transition-all duration-200">
            <div className="p-4 rounded-full bg-green-100 dark:bg-green-900/30 mb-6">
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
            <div className="text-4xl font-bold text-green-600 mb-3">
              {metricsData?.completedTasks || 0}
            </div>
            <div className="text-sm font-medium text-muted-foreground uppercase tracking-wide">
              Completed
            </div>
          </div>

          {/* Completion Rate */}
          <div className="group flex flex-col items-center text-center p-8 rounded-xl border-2 border-border hover:border-primary/20 hover:bg-primary/5 transition-all duration-200">
            <div className="p-4 rounded-full bg-emerald-100 dark:bg-emerald-900/30 mb-6">
              <TrendingUp className="h-8 w-8 text-emerald-600" />
            </div>
            <div className="text-4xl font-bold text-emerald-600 mb-3">
              {metricsData?.completionRate || 0}%
            </div>
            <div className="text-sm font-medium text-muted-foreground uppercase tracking-wide">
              Completion Rate
            </div>
          </div>

          {/* Overdue Tasks */}
          <div className="group flex flex-col items-center text-center p-8 rounded-xl border-2 border-border hover:border-primary/20 hover:bg-primary/5 transition-all duration-200">
            <div className="p-4 rounded-full bg-red-100 dark:bg-red-900/30 mb-6">
              <AlertTriangle className="h-8 w-8 text-red-600" />
            </div>
            <div className="text-4xl font-bold text-red-600 mb-3">
              {metricsData?.overdueTasks || 0}
            </div>
            <div className="text-sm font-medium text-muted-foreground uppercase tracking-wide">
              Overdue
            </div>
          </div>

          {/* Average Completion Time */}
          <div className="group flex flex-col items-center text-center p-8 rounded-xl border-2 border-border hover:border-primary/20 hover:bg-primary/5 transition-all duration-200">
            <div className="p-4 rounded-full bg-yellow-100 dark:bg-yellow-900/30 mb-6">
              <Clock className="h-8 w-8 text-yellow-600" />
            </div>
            <div className="text-4xl font-bold text-yellow-600 mb-3">
              {metricsData?.averageCompletionTime || 0}d
            </div>
            <div className="text-sm font-medium text-muted-foreground uppercase tracking-wide">
              Avg. Completion
            </div>
          </div>

          {/* Assigned Staff */}
          <div className="group flex flex-col items-center text-center p-8 rounded-xl border-2 border-border hover:border-primary/20 hover:bg-primary/5 transition-all duration-200">
            <div className="p-4 rounded-full bg-purple-100 dark:bg-purple-900/30 mb-6">
              <Users className="h-8 w-8 text-purple-600" />
            </div>
            <div className="text-4xl font-bold text-purple-600 mb-3">
              {metricsData?.assignedEmployees || 0}
            </div>
            <div className="text-sm font-medium text-muted-foreground uppercase tracking-wide">
              Assigned Staff
            </div>
          </div>
        </div>

        {/* Charts Section with professional spacing */}
        {showCharts && !compact && taskAnalytics && (
          <div className="space-y-10">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <Card className="border-2">
                <CardHeader className="pb-6">
                  <CardTitle className="text-lg">Status Distribution</CardTitle>
                </CardHeader>
                <CardContent>
                  <TaskStatusChart
                    data={taskAnalytics.statusDistribution}
                    className="h-80 w-full"
                    showLegend={true}
                    interactive={true}
                  />
                </CardContent>
              </Card>

              <Card className="border-2">
                <CardHeader className="pb-6">
                  <CardTitle className="text-lg">
                    Priority Distribution
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <TaskPriorityDistribution
                    data={taskAnalytics.priorityDistribution}
                    className="h-80 w-full"
                    showLegend={true}
                    interactive={true}
                  />
                </CardContent>
              </Card>
            </div>

            {/* Assignment Metrics */}
            <TaskAssignmentMetrics
              data={taskAnalytics.assignmentMetrics}
              className="w-full"
            />
          </div>
        )}

        {/* Compact view */}
        {compact && taskAnalytics && (
          <div className="flex items-center justify-between p-6 bg-muted/50 rounded-lg">
            <div className="flex items-center gap-4">
              <div className="text-2xl font-bold text-primary">
                {metricsData?.completionRate}%
              </div>
              <span className="text-muted-foreground">completion rate</span>
            </div>
            <Button variant="ghost" size="sm" className="gap-2">
              <BarChart3 className="h-4 w-4" />
              View Details
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
