/**
 * Tests for enhanced Winston logger functionality
 */

import logger, {
  createContextLogger,
  logSecurityEvent,
  logPerformance,
  logStartup,
} from '../../utils/logger.js';

// Mo<PERSON> winston to capture log calls
jest.mock('winston', () => {
  const mockLogger = {
    error: jest.fn(),
    warn: jest.fn(),
    info: jest.fn(),
    debug: jest.fn(),
    http: jest.fn(),
    log: jest.fn(),
  };

  return {
    createLogger: jest.fn(() => mockLogger),
    format: {
      combine: jest.fn(),
      timestamp: jest.fn(),
      errors: jest.fn(),
      splat: jest.fn(),
      metadata: jest.fn(),
      json: jest.fn(),
      colorize: jest.fn(),
      printf: jest.fn(),
    },
    transports: {
      File: jest.fn(),
      Console: jest.fn(),
    },
    __mockLogger: mockLogger,
  };
});

describe('Enhanced Winston Logger', () => {
  let mockLogger: any;

  beforeEach(() => {
    const winston = require('winston');
    mockLogger = winston.__mockLogger;
    jest.clearAllMocks();
  });

  describe('createContextLogger', () => {
    it('should create a context logger with proper context', () => {
      const context = {
        service: 'test-service',
        requestId: 'req-123456789',
        userId: 'user-987654321',
      };

      const contextLogger = createContextLogger(context);

      // Test error logging
      contextLogger.error('Test error message', { additional: 'data' });

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Test error message',
        expect.objectContaining({
          service: 'test-service',
          requestId: 'req-123456789',
          userId: 'user-987654321',
          additional: 'data',
        }),
      );
    });

    it('should handle missing context gracefully', () => {
      const contextLogger = createContextLogger({});

      contextLogger.info('Test message');

      expect(mockLogger.info).toHaveBeenCalledWith('Test message', {});
    });

    it('should merge additional metadata with context', () => {
      const context = { service: 'test-service' };
      const contextLogger = createContextLogger(context);

      contextLogger.warn('Warning message', {
        errorCode: 'WARN_001',
        details: { field: 'value' },
      });

      expect(mockLogger.warn).toHaveBeenCalledWith(
        'Warning message',
        expect.objectContaining({
          service: 'test-service',
          errorCode: 'WARN_001',
          details: { field: 'value' },
        }),
      );
    });
  });

  describe('logSecurityEvent', () => {
    it('should log security events with proper structure', () => {
      const eventName = 'UNAUTHORIZED_ACCESS_ATTEMPT';
      const details = {
        userId: 'user-123',
        endpoint: '/api/admin',
        ip: '*************',
      };

      logSecurityEvent(eventName, details, 'error');

      expect(mockLogger.log).toHaveBeenCalledWith(
        'error',
        'Security Event: UNAUTHORIZED_ACCESS_ATTEMPT',
        expect.objectContaining({
          securityEvent: true,
          event: eventName,
          details,
          timestamp: expect.any(String),
        }),
      );
    });

    it('should default to warn severity', () => {
      logSecurityEvent('TEST_EVENT', { test: 'data' });

      expect(mockLogger.log).toHaveBeenCalledWith(
        'warn',
        'Security Event: TEST_EVENT',
        expect.any(Object),
      );
    });
  });

  describe('logPerformance', () => {
    it('should log performance metrics with proper structure', () => {
      const operation = 'database_query';
      const duration = 150;
      const metadata = {
        query: 'SELECT * FROM users',
        rows: 25,
      };

      logPerformance(operation, duration, metadata);

      expect(mockLogger.info).toHaveBeenCalledWith(
        'Performance: database_query',
        expect.objectContaining({
          performance: true,
          operation,
          duration,
          query: 'SELECT * FROM users',
          rows: 25,
        }),
      );
    });

    it('should handle performance logging without metadata', () => {
      logPerformance('api_request', 75);

      expect(mockLogger.info).toHaveBeenCalledWith(
        'Performance: api_request',
        expect.objectContaining({
          performance: true,
          operation: 'api_request',
          duration: 75,
        }),
      );
    });
  });

  describe('logStartup', () => {
    it('should log startup messages with proper structure', () => {
      const message = 'Database connection established';
      const metadata = {
        host: 'localhost',
        port: 5432,
        database: 'workhub',
      };

      logStartup(message, metadata);

      expect(mockLogger.info).toHaveBeenCalledWith(
        'Startup: Database connection established',
        expect.objectContaining({
          startup: true,
          host: 'localhost',
          port: 5432,
          database: 'workhub',
        }),
      );
    });

    it('should handle startup logging without metadata', () => {
      logStartup('Application started');

      expect(mockLogger.info).toHaveBeenCalledWith(
        'Startup: Application started',
        expect.objectContaining({
          startup: true,
        }),
      );
    });
  });

  describe('Context Logger Methods', () => {
    let contextLogger: any;

    beforeEach(() => {
      contextLogger = createContextLogger({
        service: 'test-service',
        requestId: 'req-123',
      });
    });

    it('should support all log levels', () => {
      contextLogger.error('Error message');
      contextLogger.warn('Warning message');
      contextLogger.info('Info message');
      contextLogger.debug('Debug message');

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Error message',
        expect.objectContaining({ service: 'test-service' }),
      );
      expect(mockLogger.warn).toHaveBeenCalledWith(
        'Warning message',
        expect.objectContaining({ service: 'test-service' }),
      );
      expect(mockLogger.info).toHaveBeenCalledWith(
        'Info message',
        expect.objectContaining({ service: 'test-service' }),
      );
      expect(mockLogger.debug).toHaveBeenCalledWith(
        'Debug message',
        expect.objectContaining({ service: 'test-service' }),
      );
    });

    it('should preserve context across multiple calls', () => {
      const context = {
        service: 'persistent-service',
        requestId: 'req-persistent',
        userId: 'user-persistent',
      };

      const persistentLogger = createContextLogger(context);

      persistentLogger.info('First message');
      persistentLogger.error('Second message');

      expect(mockLogger.info).toHaveBeenCalledWith(
        'First message',
        expect.objectContaining(context),
      );
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Second message',
        expect.objectContaining(context),
      );
    });
  });
});
