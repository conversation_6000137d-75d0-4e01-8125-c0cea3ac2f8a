{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_dc030637._.js", "server/edge/chunks/[root-of-the-server]__e885b082._.js", "server/edge/chunks/edge-wrapper_df5dcf23.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "+4y0UnvznaGdUrB5ErKZiXmNYD7+AUYbicNS4ko67b0=", "__NEXT_PREVIEW_MODE_ID": "7deb3abe9065a3923315c5c8ca5c2dcd", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "b4fec4a9726f30e84408f12f1f287bf528474fb03bb13b81a303b60f5721b0d1", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "d021f0a048a9f6ce56c95899ab507d64b61ed78a3af8444e5eb539b8696aba78"}}}, "sortedMiddleware": ["/"], "functions": {}}