{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_dc030637._.js", "server/edge/chunks/[root-of-the-server]__e885b082._.js", "server/edge/chunks/edge-wrapper_df5dcf23.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "+4y0UnvznaGdUrB5ErKZiXmNYD7+AUYbicNS4ko67b0=", "__NEXT_PREVIEW_MODE_ID": "eb3f57b531f535c4c58674f5e388e8c9", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "b4692484d8f29840ada63f3fc96c3efd5fc0272f899b155e55e29ca042ecb813", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "4a93897c86d8a03e052e29e06d9d1885840392dc03257d944961b577149cf6a8"}}}, "sortedMiddleware": ["/"], "functions": {}}