'use client';

import type { ColumnDef } from '@tanstack/react-table';
import { format, isPast } from 'date-fns';
import { AlertTriangle, Trash, Archive } from 'lucide-react';
import React from 'react';

import type { Task } from '@/lib/types/domain';

import {
  DataTable,
  createSelectionColumn,
  createTextColumn,
  createStatusColumn,
  createEnhancedActionsColumn,
} from '@/components/ui/tables';
import { formatEmployeeName } from '@/lib/utils/formattingUtils';

/**
 * Props for the TasksTable component
 */
interface TasksTableProps {
  /** Additional CSS class names */
  className?: string;
  /** Array of tasks to display */
  tasks: Task[];
  /** Callback for task deletion */
  onDelete?: (task: Task) => Promise<void>;
  /** Callback for bulk task deletion */
  onBulkDelete?: (tasks: Task[]) => Promise<void>;
  /** Callback for bulk task archiving */
  onBulkArchive?: (tasks: Task[]) => Promise<void>;
}

/**
 * A component that displays tasks in a table using the standardized DataTable component
 *
 * @example
 * ```tsx
 * <TasksTable
 *   tasks={tasks}
 *   onDelete={handleDelete}
 *   onBulkDelete={handleBulkDelete}
 * />
 * ```
 */
export function TasksTable({
  className = '',
  tasks,
  onDelete,
  onBulkDelete,
  onBulkArchive,
}: TasksTableProps) {
  // Helper functions
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'MMM d, yyyy h:mm a');
    } catch {
      return 'Invalid date';
    }
  };

  const formatStatus = (status: string) => {
    return status.replace('_', ' ');
  };

  const isOverdue = (task: Task) => {
    return (
      task.deadline &&
      isPast(new Date(task.deadline)) &&
      task.status !== 'Completed' &&
      task.status !== 'Cancelled'
    );
  };

  const getAssigneeName = (task: Task) => {
    if (!task.staffEmployeeId) {
      return 'Unassigned';
    }

    if (task.staffEmployee) {
      return formatEmployeeName(task.staffEmployee);
    }

    return `Staff ID: ${task.staffEmployeeId}`;
  };

  // Define columns using standardized helpers
  const columns: ColumnDef<Task>[] = [
    // Row selection
    createSelectionColumn<Task>(),

    // Description with truncation
    createTextColumn('description', 'Description', {
      maxLength: 50,
      className: 'max-w-xs',
    }),

    // Status with proper formatting
    createStatusColumn('status', 'Status', {
      Assigned: { variant: 'default', label: 'Assigned' },
      Cancelled: { variant: 'secondary', label: 'Cancelled' },
      Completed: { variant: 'success', label: 'Completed' },
      In_Progress: { variant: 'default', label: 'In Progress' },
      Pending: { variant: 'warning', label: 'Pending' },
    }),

    // Priority with proper formatting
    createStatusColumn('priority', 'Priority', {
      High: { variant: 'destructive', label: 'High' },
      Medium: { variant: 'warning', label: 'Medium' },
      Low: { variant: 'secondary', label: 'Low' },
    }),

    // Assignee with custom formatting
    {
      accessorKey: 'staffEmployeeId',
      header: 'Assignee',
      cell: ({ row }) => {
        const task = row.original;
        return (
          <span className={task.staffEmployeeId ? '' : 'text-muted-foreground'}>
            {getAssigneeName(task)}
          </span>
        );
      },
    },

    // Start time
    {
      accessorKey: 'dateTime',
      header: 'Start Time',
      cell: ({ row }) => {
        const dateTime = row.getValue('dateTime') as string;
        return formatDate(dateTime);
      },
    },

    // Deadline with overdue indicator
    {
      accessorKey: 'deadline',
      header: 'Deadline',
      cell: ({ row }) => {
        const task = row.original;
        const deadline = task.deadline;
        
        if (!deadline) {
          return <span className="text-muted-foreground">No deadline</span>;
        }

        const overdue = isOverdue(task);
        return (
          <div className="flex items-center gap-1">
            <span className={overdue ? 'text-red-600 font-medium' : ''}>
              {formatDate(deadline)}
            </span>
            {overdue && (
              <AlertTriangle
                aria-label="Overdue"
                className="size-4 text-red-600"
              />
            )}
          </div>
        );
      },
    },

    // Location with truncation
    createTextColumn('location', 'Location', {
      maxLength: 30,
      className: 'max-w-xs',
    }),

    // Actions
    createEnhancedActionsColumn({
      viewHref: task => `/tasks/${task.id}`,
      editHref: task => `/tasks/${task.id}/edit`,
      ...(onDelete && {
        onDelete: (task: Task) => {
          onDelete(task);
        },
      }),
      showCopyId: true,
    }),
  ];

  // Bulk actions for selected rows
  const bulkActions = [
    ...(onBulkDelete ? [{
      label: 'Delete Selected',
      icon: ({ className }: { className?: string }) => (
        <Trash className={className} />
      ),
      onClick: async (selectedTasks: Task[]) => {
        await onBulkDelete(selectedTasks);
      },
      variant: 'destructive' as const,
    }] : []),
    ...(onBulkArchive ? [{
      label: 'Archive Selected',
      icon: ({ className }: { className?: string }) => (
        <Archive className={className} />
      ),
      onClick: async (selectedTasks: Task[]) => {
        await onBulkArchive(selectedTasks);
      },
    }] : []),
  ];

  return (
    <DataTable
      data={tasks}
      columns={columns}
      className={className}
      searchPlaceholder="Search tasks by description or location..."
      searchColumn="description"
      emptyMessage="No tasks found. Create your first task to get started."
      pageSize={20}
      // Advanced features
      enableRowSelection={true}
      enableBulkActions={bulkActions.length > 0}
      bulkActions={bulkActions}
      enableColumnVisibility={true}
      // Professional styling with task theme
      tableClassName="shadow-lg"
      headerClassName="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20"
      rowClassName="hover:bg-blue-50/50 dark:hover:bg-blue-900/10"
    />
  );
}
