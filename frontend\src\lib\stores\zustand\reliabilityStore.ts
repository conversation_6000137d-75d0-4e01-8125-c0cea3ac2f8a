/**
 * @file Zustand store for Reliability dashboard state management.
 * This store manages reliability dashboard preferences, UI state, and real-time monitoring controls,
 * complementing the React Query hooks for comprehensive reliability monitoring.
 * @module stores/zustand/reliabilityStore
 */

import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';

import type { AlertSeverity, AlertStatus } from '@/lib/types/domain';

/**
 * Alert filtering configuration
 */
export interface AlertFilters {
  /** Text search filter */
  searchText: string;
  /** Filter by alert severity levels */
  severities: Set<AlertSeverity>;
  /** Filter by alert source/type */
  sources: Set<string>;
  /** Filter by alert status */
  statuses: Set<AlertStatus>;
  /** Time range for alert history */
  timeRange: TimeRange;
}

/**
 * Batch operation loading states
 */
export interface BatchOperations {
  'acknowledge-multiple-alerts': boolean;
  'export-metrics': boolean;
  'refresh-all-data': boolean;
  'resolve-multiple-alerts': boolean;
}

/**
 * WebSocket connection status states
 */
export type ConnectionStatus = 'connected' | 'disconnected' | 'reconnecting';

/**
 * Dashboard layout configuration options
 */
export type DashboardLayout = 'compact' | 'grid' | 'list';

/**
 * Dashboard layout preferences
 */
export interface DashboardLayoutPreferences {
  /** Expanded/collapsed widget states */
  expandedWidgets: Set<WidgetId>;
  /** Grid columns for grid layout */
  gridColumns: number;
  /** Layout type (grid, list, compact) */
  layout: DashboardLayout;
  /** Visible widgets */
  visibleWidgets: Set<WidgetId>;
  /** Widget order/arrangement */
  widgetOrder: WidgetId[];
}

/**
 * Dashboard tab/view identifiers
 */
export type DashboardTab =
  | 'alerts'
  | 'health'
  | 'history'
  | 'metrics'
  | 'overview';

/**
 * Data type identifiers for refresh interval configuration
 */
export type DataType =
  | 'alert-statistics'
  | 'alerts'
  | 'circuit-breakers'
  | 'dependencies'
  | 'detailed-health'
  | 'health'
  | 'metrics';

/**
 * Last refresh timestamps for data freshness tracking
 */
export interface LastRefreshTimestamps {
  'alert-statistics'?: string;
  alerts?: string;
  'circuit-breakers'?: string;
  dependencies?: string;
  'detailed-health'?: string;
  health?: string;
  metrics?: string;
}

/**
 * Notification preferences for alerts
 */
export interface NotificationPreferences {
  /** Auto-dismiss timeout in seconds (0 = no auto-dismiss) */
  autoDismissTimeout: number;
  /** Enable desktop notifications */
  desktopEnabled: boolean;
  /** Minimum severity level for notifications */
  minimumSeverity: AlertSeverity;
  /** Enable sound notifications for alerts */
  soundEnabled: boolean;
}

/**
 * Refresh interval configuration for different data types
 */
export interface RefreshIntervals {
  'alert-statistics': number;
  alerts: number;
  'circuit-breakers': number;
  dependencies: number;
  'detailed-health': number;
  health: number;
  metrics: number;
}

/**
 * Time range options for historical data
 */
export type TimeRange = '1h' | '6h' | '7d' | '24h' | '30d';

/**
 * Dashboard widget identifiers for visibility control
 */
export type WidgetId =
  | 'active-alerts'
  | 'alert-statistics'
  | 'circuit-breaker-alerts'
  | 'circuit-breaker-history'
  | 'circuit-breaker-list'
  | 'circuit-breaker-metrics'
  | 'circuit-breakers'
  | 'deduplication-metrics'
  | 'dependency-health'
  | 'dependency-status'
  | 'health-status-indicators'
  | 'health-trends'
  | 'http-metrics'
  | 'performance-metrics'
  | 'performance-overview'
  | 'system-health'
  | 'system-metrics'
  | 'system-resources';

/**
 * Complete reliability store state interface
 */
interface ReliabilityState {
  clearAlertFilters: () => void;

  clearAlertSelection: () => void;

  getActiveFilters: () => Partial<AlertFilters>;

  getSelectedAlertCount: () => number;
  // Computed selectors
  getVisibleWidgets: () => WidgetId[];
  isDataTypePaused: (dataType: DataType) => boolean;
  // Real-time Monitoring State (ephemeral)
  monitoring: {
    connectionStatus: ConnectionStatus;
    isEnabled: boolean;
    lastRefresh: LastRefreshTimestamps;
    pausedDataTypes: Set<DataType>;
  };
  pauseAllMonitoring: () => void;
  pauseDataType: (dataType: DataType) => void;
  // Preferences (persisted)
  preferences: {
    dashboardLayout: DashboardLayoutPreferences;
    defaultTimeRange: TimeRange;
    notifications: NotificationPreferences;
    refreshIntervals: RefreshIntervals;
  };
  reorderWidgets: (newOrder: WidgetId[]) => void;
  resetPreferencesToDefaults: () => void;

  resumeAllMonitoring: () => void;
  resumeDataType: (dataType: DataType) => void;
  selectAllAlerts: (alertIds: string[]) => void;
  // Actions for UI state
  setActiveTab: (tab: DashboardTab) => void;
  setAlertFilters: (filters: Partial<AlertFilters>) => void;
  setBatchOperationLoading: (
    operation: keyof BatchOperations,
    loading: boolean
  ) => void;
  setConnectionStatus: (status: ConnectionStatus) => void;
  setDashboardLayout: (layout: DashboardLayout) => void;

  setDefaultTimeRange: (timeRange: TimeRange) => void;
  setGridColumns: (columns: number) => void;
  // Actions for monitoring state
  setMonitoringEnabled: (enabled: boolean) => void;
  setNotificationPreferences: (
    preferences: Partial<NotificationPreferences>
  ) => void;
  // Actions for preferences
  setRefreshInterval: (dataType: DataType, interval: number) => void;
  setWidgetExpanded: (widgetId: WidgetId, expanded: boolean) => void;
  toggleAlertSelection: (alertId: string) => void;

  toggleFilterPanel: () => void;
  toggleWidget: (widgetId: WidgetId) => void;
  // UI State (ephemeral)
  ui: {
    activeTab: DashboardTab;
    batchOperations: BatchOperations;
    filters: AlertFilters;
    isFilterPanelOpen: boolean;
    selectedAlerts: Set<string>;
  };
  updateLastRefresh: (dataType: DataType, timestamp?: string) => void;
}

/**
 * Default values for the reliability store
 */
const defaultRefreshIntervals: RefreshIntervals = {
  'alert-statistics': 300_000, // 5 minutes
  alerts: 10_000, // 10 seconds
  'circuit-breakers': 30_000, // 30 seconds
  dependencies: 45_000, // 45 seconds
  'detailed-health': 60_000, // 1 minute
  health: 15_000, // 15 seconds
  metrics: 30_000, // 30 seconds
};

const defaultDashboardLayout: DashboardLayoutPreferences = {
  expandedWidgets: new Set([
    'system-health',
    'health-status-indicators',
    'circuit-breakers',
    'active-alerts',
  ]),
  gridColumns: 3,
  layout: 'grid',
  visibleWidgets: new Set([
    'system-health',
    'health-status-indicators',
    'circuit-breakers',
    'active-alerts',
    'alert-statistics',
    'dependency-status',
    'health-trends',
    'circuit-breaker-metrics',
    'circuit-breaker-history',
    'deduplication-metrics',
    'performance-metrics',
    'performance-overview',
    'system-metrics',
    'http-metrics',
    'dependency-health',
  ]),
  widgetOrder: [
    'system-health',
    'health-status-indicators',
    'circuit-breakers',
    'active-alerts',
    'alert-statistics',
    'dependency-status',
    'health-trends',
    'circuit-breaker-metrics',
    'circuit-breaker-list',
    'circuit-breaker-history',
    'circuit-breaker-alerts',
    'system-resources',
    'performance-overview',
    'system-metrics',
    'http-metrics',
    'deduplication-metrics',
    'performance-metrics',
    'dependency-health',
  ],
};

const defaultNotificationPreferences: NotificationPreferences = {
  autoDismissTimeout: 5000, // 5 seconds
  desktopEnabled: true,
  minimumSeverity: 'medium',
  soundEnabled: true,
};

const defaultAlertFilters: AlertFilters = {
  searchText: '',
  severities: new Set(['critical', 'high', 'low', 'medium']),
  sources: new Set(),
  statuses: new Set(['acknowledged', 'active']),
  timeRange: '24h',
};

const defaultBatchOperations: BatchOperations = {
  'acknowledge-multiple-alerts': false,
  'export-metrics': false,
  'refresh-all-data': false,
  'resolve-multiple-alerts': false,
};

/**
 * Zustand store for managing reliability dashboard state.
 * Enhanced with persistence for user preferences and devtools for development.
 */
export const useReliabilityStore = create<ReliabilityState>()(
  devtools(
    persist(
      (set, get) => ({
        clearAlertFilters: () =>
          set(state => ({
            ui: {
              ...state.ui,
              filters: defaultAlertFilters,
            },
          })),

        clearAlertSelection: () =>
          set(state => ({
            ui: {
              ...state.ui,
              selectedAlerts: new Set(),
            },
          })),

        getActiveFilters: () => {
          const { ui } = get();
          const filters = ui.filters;
          const activeFilters: Partial<AlertFilters> = {};

          // Only include non-default filter values
          if (filters.severities.size !== 4) {
            activeFilters.severities = filters.severities;
          }
          if (filters.statuses.size !== 2) {
            activeFilters.statuses = filters.statuses;
          }
          if (filters.sources.size > 0) {
            activeFilters.sources = filters.sources;
          }
          if (filters.searchText.trim()) {
            activeFilters.searchText = filters.searchText;
          }
          if (filters.timeRange !== '24h') {
            activeFilters.timeRange = filters.timeRange;
          }

          return activeFilters;
        },

        getSelectedAlertCount: () => {
          const { ui } = get();
          return ui.selectedAlerts.size;
        },

        // Computed selectors
        getVisibleWidgets: () => {
          const { preferences } = get();
          return preferences.dashboardLayout.widgetOrder.filter(widgetId =>
            preferences.dashboardLayout.visibleWidgets.has(widgetId)
          );
        },

        isDataTypePaused: (dataType: DataType) => {
          const { monitoring } = get();
          return (
            monitoring.pausedDataTypes.has(dataType) || !monitoring.isEnabled
          );
        },

        monitoring: {
          connectionStatus: 'disconnected',
          isEnabled: true,
          lastRefresh: {},
          pausedDataTypes: new Set(),
        },

        pauseAllMonitoring: () =>
          set(state => ({
            monitoring: {
              ...state.monitoring,
              isEnabled: false,
              pausedDataTypes: new Set([
                'alert-statistics',
                'alerts',
                'circuit-breakers',
                'dependencies',
                'detailed-health',
                'health',
                'metrics',
              ]),
            },
          })),

        pauseDataType: (dataType: DataType) =>
          set(state => {
            const newPausedDataTypes = new Set(
              state.monitoring.pausedDataTypes
            );
            newPausedDataTypes.add(dataType);
            return {
              monitoring: {
                ...state.monitoring,
                pausedDataTypes: newPausedDataTypes,
              },
            };
          }),

        // Initial state
        preferences: {
          dashboardLayout: defaultDashboardLayout,
          defaultTimeRange: '24h',
          notifications: defaultNotificationPreferences,
          refreshIntervals: defaultRefreshIntervals,
        },

        reorderWidgets: (newOrder: WidgetId[]) =>
          set(state => ({
            preferences: {
              ...state.preferences,
              dashboardLayout: {
                ...state.preferences.dashboardLayout,
                widgetOrder: newOrder,
              },
            },
          })),

        resetPreferencesToDefaults: () =>
          set(state => ({
            preferences: {
              dashboardLayout: defaultDashboardLayout,
              defaultTimeRange: '24h',
              notifications: defaultNotificationPreferences,
              refreshIntervals: defaultRefreshIntervals,
            },
          })),

        resumeAllMonitoring: () =>
          set(state => ({
            monitoring: {
              ...state.monitoring,
              isEnabled: true,
              pausedDataTypes: new Set(),
            },
          })),

        resumeDataType: (dataType: DataType) =>
          set(state => {
            const newPausedDataTypes = new Set(
              state.monitoring.pausedDataTypes
            );
            newPausedDataTypes.delete(dataType);
            return {
              monitoring: {
                ...state.monitoring,
                pausedDataTypes: newPausedDataTypes,
              },
            };
          }),

        selectAllAlerts: (alertIds: string[]) =>
          set(state => ({
            ui: {
              ...state.ui,
              selectedAlerts: new Set(alertIds),
            },
          })),

        // UI state actions
        setActiveTab: (tab: DashboardTab) =>
          set(state => ({
            ui: {
              ...state.ui,
              activeTab: tab,
            },
          })),

        setAlertFilters: (filters: Partial<AlertFilters>) =>
          set(state => ({
            ui: {
              ...state.ui,
              filters: {
                ...state.ui.filters,
                ...filters,
                // Handle Set types properly
                severities: filters.severities || state.ui.filters.severities,
                sources: filters.sources || state.ui.filters.sources,
                statuses: filters.statuses || state.ui.filters.statuses,
              },
            },
          })),

        setBatchOperationLoading: (
          operation: keyof BatchOperations,
          loading: boolean
        ) =>
          set(state => ({
            ui: {
              ...state.ui,
              batchOperations: {
                ...state.ui.batchOperations,
                [operation]: loading,
              },
            },
          })),

        setConnectionStatus: (status: ConnectionStatus) =>
          set(state => ({
            monitoring: {
              ...state.monitoring,
              connectionStatus: status,
            },
          })),

        setDashboardLayout: (layout: DashboardLayout) =>
          set(state => ({
            preferences: {
              ...state.preferences,
              dashboardLayout: {
                ...state.preferences.dashboardLayout,
                layout,
              },
            },
          })),

        setDefaultTimeRange: (timeRange: TimeRange) =>
          set(state => ({
            preferences: {
              ...state.preferences,
              defaultTimeRange: timeRange,
            },
          })),

        setGridColumns: (columns: number) =>
          set(state => ({
            preferences: {
              ...state.preferences,
              dashboardLayout: {
                ...state.preferences.dashboardLayout,
                gridColumns: Math.max(1, Math.min(6, columns)), // Clamp between 1-6
              },
            },
          })),

        // Monitoring state actions
        setMonitoringEnabled: (enabled: boolean) =>
          set(state => ({
            monitoring: {
              ...state.monitoring,
              isEnabled: enabled,
            },
          })),

        setNotificationPreferences: (
          preferences: Partial<NotificationPreferences>
        ) =>
          set(state => ({
            preferences: {
              ...state.preferences,
              notifications: {
                ...state.preferences.notifications,
                ...preferences,
              },
            },
          })),

        // Preference actions
        setRefreshInterval: (dataType: DataType, interval: number) =>
          set(state => ({
            preferences: {
              ...state.preferences,
              refreshIntervals: {
                ...state.preferences.refreshIntervals,
                [dataType]: interval,
              },
            },
          })),

        setWidgetExpanded: (widgetId: WidgetId, expanded: boolean) =>
          set(state => {
            const newExpandedWidgets = new Set(
              state.preferences.dashboardLayout.expandedWidgets
            );
            if (expanded) {
              newExpandedWidgets.add(widgetId);
            } else {
              newExpandedWidgets.delete(widgetId);
            }
            return {
              preferences: {
                ...state.preferences,
                dashboardLayout: {
                  ...state.preferences.dashboardLayout,
                  expandedWidgets: newExpandedWidgets,
                },
              },
            };
          }),

        toggleAlertSelection: (alertId: string) =>
          set(state => {
            const newSelectedAlerts = new Set(state.ui.selectedAlerts);
            if (newSelectedAlerts.has(alertId)) {
              newSelectedAlerts.delete(alertId);
            } else {
              newSelectedAlerts.add(alertId);
            }
            return {
              ui: {
                ...state.ui,
                selectedAlerts: newSelectedAlerts,
              },
            };
          }),

        toggleFilterPanel: () =>
          set(state => ({
            ui: {
              ...state.ui,
              isFilterPanelOpen: !state.ui.isFilterPanelOpen,
            },
          })),

        toggleWidget: (widgetId: WidgetId) =>
          set(state => {
            const newVisibleWidgets = new Set(
              state.preferences.dashboardLayout.visibleWidgets
            );
            if (newVisibleWidgets.has(widgetId)) {
              newVisibleWidgets.delete(widgetId);
            } else {
              newVisibleWidgets.add(widgetId);
            }
            return {
              preferences: {
                ...state.preferences,
                dashboardLayout: {
                  ...state.preferences.dashboardLayout,
                  visibleWidgets: newVisibleWidgets,
                },
              },
            };
          }),

        ui: {
          activeTab: 'overview',
          batchOperations: defaultBatchOperations,
          filters: defaultAlertFilters,
          isFilterPanelOpen: false,
          selectedAlerts: new Set(),
        },

        updateLastRefresh: (dataType: DataType, timestamp?: string) =>
          set(state => ({
            monitoring: {
              ...state.monitoring,
              lastRefresh: {
                ...state.monitoring.lastRefresh,
                [dataType]: timestamp || new Date().toISOString(),
              },
            },
          })),
      }),
      {
        // Note: Custom serialization for Set types handled by storage adapter
        merge: (persistedState: any, currentState: ReliabilityState) => {
          const state = persistedState as ReliabilityState;
          // Rehydrate Set objects from arrays
          state.preferences.dashboardLayout.expandedWidgets = new Set(
            state.preferences.dashboardLayout.expandedWidgets
          );
          state.preferences.dashboardLayout.visibleWidgets = new Set(
            state.preferences.dashboardLayout.visibleWidgets
          );
          return { ...currentState, ...state };
        },
        name: 'workhub-reliability-store', // Storage key
        partialize: state => ({
          preferences: {
            dashboardLayout: {
              expandedWidgets: [
                ...state.preferences.dashboardLayout.expandedWidgets,
              ],
              gridColumns: state.preferences.dashboardLayout.gridColumns,
              layout: state.preferences.dashboardLayout.layout,
              visibleWidgets: [
                ...state.preferences.dashboardLayout.visibleWidgets,
              ],
              widgetOrder: state.preferences.dashboardLayout.widgetOrder,
            },
            defaultTimeRange: state.preferences.defaultTimeRange,
            notifications: state.preferences.notifications,
            refreshIntervals: state.preferences.refreshIntervals,
          },
          // Don't persist UI state or monitoring state
        }),
      }
    ),
    {
      name: 'reliability-store', // DevTools name
    }
  )
);
