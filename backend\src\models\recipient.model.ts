import type { Prisma, Recipient } from '../generated/prisma/index.js';
import type { RecipientQuery } from '../schemas/recipient.schema.js';

import { PrismaClientKnownRequestError } from '../generated/prisma/runtime/library.js';
import prisma from './index.js';

/**
 * Create a new recipient
 */
export const createRecipient = async (
  data: Prisma.RecipientCreateInput,
): Promise<null | Recipient> => {
  try {
    return await prisma.recipient.create({
      data,
      include: {
        gifts: {
          orderBy: { createdAt: 'desc' },
        },
      },
    });
  } catch (error) {
    console.error('Error creating recipient:', error);
    if (error instanceof PrismaClientKnownRequestError && error.code === 'P2002') {
      throw new Error('A unique constraint failed while creating the recipient.');
    }
    return null;
  }
};

/**
 * Get all recipients with optional filtering
 */
export const getAllRecipients = async (query: RecipientQuery = {}): Promise<Recipient[]> => {
  try {
    const { limit = 50, offset = 0, role, search, worksite } = query;

    const where: Prisma.RecipientWhereInput = {};

    // Add search filter
    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } },
        { notes: { contains: search, mode: 'insensitive' } },
      ];
    }

    // Add role filter
    if (role) {
      where.role = { contains: role, mode: 'insensitive' };
    }

    // Add worksite filter
    if (worksite) {
      where.worksite = { contains: worksite, mode: 'insensitive' };
    }

    return await prisma.recipient.findMany({
      include: {
        gifts: {
          orderBy: { createdAt: 'desc' },
          take: 5, // Limit to recent gifts for performance
        },
      },
      orderBy: { name: 'asc' },
      skip: offset,
      take: limit,
      where,
    });
  } catch (error) {
    console.error('Error fetching recipients:', error);
    return [];
  }
};

/**
 * Get a single recipient by ID
 */
export const getRecipientById = async (id: string): Promise<null | Recipient> => {
  try {
    return await prisma.recipient.findUnique({
      include: {
        gifts: {
          orderBy: { createdAt: 'desc' },
        },
      },
      where: { id },
    });
  } catch (error) {
    console.error('Error fetching recipient by ID:', error);
    return null;
  }
};

/**
 * Update a recipient
 */
export const updateRecipient = async (
  id: string,
  data: Prisma.RecipientUpdateInput,
): Promise<null | Recipient> => {
  try {
    return await prisma.recipient.update({
      data,
      include: {
        gifts: {
          orderBy: { createdAt: 'desc' },
        },
      },
      where: { id },
    });
  } catch (error) {
    console.error('Error updating recipient:', error);
    if (error instanceof PrismaClientKnownRequestError && error.code === 'P2025') {
      return null; // Record not found
    }
    throw error;
  }
};

/**
 * Delete a recipient
 */
export const deleteRecipient = async (id: string): Promise<null | Recipient> => {
  try {
    return await prisma.recipient.delete({
      where: { id },
    });
  } catch (error) {
    console.error('Error deleting recipient:', error);
    if (error instanceof PrismaClientKnownRequestError && error.code === 'P2025') {
      return null; // Record not found
    }
    throw error;
  }
};

/**
 * Get recipients with their gift counts
 */
export const getRecipientsWithGiftCounts = async (
  query: RecipientQuery = {},
): Promise<(Recipient & { _count: { gifts: number } })[]> => {
  try {
    const { limit = 50, offset = 0, role, search, worksite } = query;

    const where: Prisma.RecipientWhereInput = {};

    // Add search filter
    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } },
        { notes: { contains: search, mode: 'insensitive' } },
      ];
    }

    // Add role filter
    if (role) {
      where.role = { contains: role, mode: 'insensitive' };
    }

    // Add worksite filter
    if (worksite) {
      where.worksite = { contains: worksite, mode: 'insensitive' };
    }

    return await prisma.recipient.findMany({
      include: {
        _count: {
          select: { gifts: true },
        },
      },
      orderBy: { name: 'asc' },
      skip: offset,
      take: limit,
      where,
    });
  } catch (error) {
    console.error('Error fetching recipients with gift counts:', error);
    return [];
  }
};

/**
 * Search recipients by name (for autocomplete/dropdown)
 */
export const searchRecipientsByName = async (searchTerm: string): Promise<Recipient[]> => {
  try {
    return await prisma.recipient.findMany({
      orderBy: { name: 'asc' },
      take: 10, // Limit for autocomplete
      where: {
        name: {
          contains: searchTerm,
          mode: 'insensitive',
        },
      },
    });
  } catch (error) {
    console.error('Error searching recipients by name:', error);
    return [];
  }
};
