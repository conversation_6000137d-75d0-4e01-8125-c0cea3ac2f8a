import type {
  Employee,
  Prisma,
  EmployeeStatus as PrismaEmployeeStatus,
} from '../generated/prisma/index.js';

import { PrismaClientKnownRequestError } from '../generated/prisma/runtime/library.js';
import prisma from './index.js';

export const createEmployee = async (
  data: Prisma.EmployeeCreateInput,
): Promise<Employee | null> => {
  try {
    // Prisma.EmployeeCreateInput already expects status to be PrismaEmployeeStatus if not nullable
    // If data.status is a string from controller, it should be cast like data.status as PrismaEmployeeStatus
    // The controller already does this cast.
    return await prisma.employee.create({
      data,
      include: {
        DelegationDriver: {
          include: { Delegation: true },
          orderBy: { createdAt: 'desc' },
        },
        DelegationEscort: {
          include: { Delegation: true },
          orderBy: { createdAt: 'desc' },
        },
        EmployeeStatusEntry: { orderBy: { changedAt: 'desc' } },
        ServiceRecord: { orderBy: { date: 'desc' } },
        Task_Task_driverEmployeeIdToEmployee: { orderBy: { dateTime: 'desc' } },
        // NEW PHASE 1 RELATIONS
        Task_Task_staffEmployeeIdToEmployee: { orderBy: { dateTime: 'desc' } },
      },
    });
  } catch (error) {
    console.error('Error creating employee:', error);
    if (error instanceof PrismaClientKnownRequestError && error.code === 'P2002') {
      if ((error.meta?.target as string[])?.includes('employeeId')) {
        throw new Error(`Employee with Employee ID ${data.employeeId} already exists.`);
      }
      throw new Error('A unique constraint failed while creating the employee.');
    }
    return null;
  }
};

export const getAllEmployees = async (): Promise<Employee[]> => {
  try {
    return await prisma.employee.findMany({
      include: {
        DelegationDriver: {
          include: { Delegation: true },
          orderBy: { createdAt: 'desc' },
        },
        DelegationEscort: {
          include: { Delegation: true },
          orderBy: { createdAt: 'desc' },
        },
        EmployeeStatusEntry: { orderBy: { changedAt: 'desc' } },
        ServiceRecord: { orderBy: { date: 'desc' } },
        Task_Task_driverEmployeeIdToEmployee: { orderBy: { dateTime: 'desc' } },
        // NEW PHASE 1 RELATIONS
        Task_Task_staffEmployeeIdToEmployee: { orderBy: { dateTime: 'desc' } },
      },
      orderBy: { name: 'asc' },
    });
  } catch (error) {
    console.error('Error fetching all employees:', error);
    return [];
  }
};

// PHASE 3: Get employees filtered by role
export const getEmployeesByRole = async (role: string): Promise<Employee[]> => {
  try {
    return await prisma.employee.findMany({
      include: {
        DelegationDriver: {
          include: { Delegation: true },
          orderBy: { createdAt: 'desc' },
        },
        DelegationEscort: {
          include: { Delegation: true },
          orderBy: { createdAt: 'desc' },
        },
        EmployeeStatusEntry: { orderBy: { changedAt: 'desc' } },
        ServiceRecord: { orderBy: { date: 'desc' } },
        Task_Task_driverEmployeeIdToEmployee: { orderBy: { dateTime: 'desc' } },
        // NEW PHASE 1 RELATIONS
        Task_Task_staffEmployeeIdToEmployee: { orderBy: { dateTime: 'desc' } },
      },
      orderBy: { name: 'asc' },
      where: {
        role: role as any, // Cast to EmployeeRole enum
        status: 'Active', // Only return active employees for assignments
      },
    });
  } catch (error) {
    console.error(`Error fetching employees with role ${role}:`, error);
    return [];
  }
};

export const getEmployeeById = async (id: number): Promise<Employee | null> => {
  try {
    return await prisma.employee.findUnique({
      include: {
        DelegationDriver: {
          include: { Delegation: true },
          orderBy: { createdAt: 'desc' },
        },
        DelegationEscort: {
          include: { Delegation: true },
          orderBy: { createdAt: 'desc' },
        },
        EmployeeStatusEntry: { orderBy: { changedAt: 'desc' } },
        ServiceRecord: { orderBy: { date: 'desc' } },
        Task_Task_driverEmployeeIdToEmployee: { orderBy: { dateTime: 'desc' } },
        // NEW PHASE 1 RELATIONS
        Task_Task_staffEmployeeIdToEmployee: { orderBy: { dateTime: 'desc' } },
      },
      where: { id },
    });
  } catch (error) {
    console.error(`Error fetching employee with ID ${id}:`, error);
    return null;
  }
};

export const updateEmployee = async (
  id: number,
  data: Prisma.EmployeeUpdateInput,
  statusChangeReason?: null | string,
): Promise<Employee | null> => {
  try {
    let updatedEmployee: Employee | null = null;

    await prisma.$transaction(async tx => {
      if (data.status && typeof data.status === 'string') {
        const currentEmployee = await tx.employee.findUnique({
          select: { status: true },
          where: { id },
        });
        if (currentEmployee && currentEmployee.status !== data.status) {
          await tx.employeeStatusEntry.create({
            data: {
              employeeId: id,
              id: `emp_status_${id}_${Date.now()}`, // Generate unique ID
              reason: statusChangeReason || 'Status updated',
              status: data.status, // Ensured by controller
            },
          });
        }
      }
      updatedEmployee = await tx.employee.update({
        data: data,
        include: {
          DelegationDriver: {
            include: { Delegation: true },
            orderBy: { createdAt: 'desc' },
          },
          DelegationEscort: {
            include: { Delegation: true },
            orderBy: { createdAt: 'desc' },
          },
          EmployeeStatusEntry: { orderBy: { changedAt: 'desc' } },
          ServiceRecord: { orderBy: { date: 'desc' } },
          Task_Task_driverEmployeeIdToEmployee: { orderBy: { dateTime: 'desc' } },
          // NEW PHASE 1 RELATIONS
          Task_Task_staffEmployeeIdToEmployee: { orderBy: { dateTime: 'desc' } },
        },
        where: { id },
      });
    });
    return updatedEmployee;
  } catch (error) {
    console.error(`Error updating employee with ID ${id}:`, error);
    if (error instanceof PrismaClientKnownRequestError && error.code === 'P2025') {
      return null;
    }
    if (error instanceof PrismaClientKnownRequestError && error.code === 'P2002') {
      if ((error.meta?.target as string[])?.includes('employeeId') && data.employeeId) {
        throw new Error(`Another employee with Employee ID ${data.employeeId} already exists.`);
      }
    }
    return null;
  }
};

export const deleteEmployee = async (id: number): Promise<Employee | null> => {
  try {
    return await prisma.$transaction(async tx => {
      const employeeToDelete = await tx.employee.findUnique({ where: { id } });
      if (!employeeToDelete) return null;

      await tx.employeeStatusEntry.deleteMany({ where: { employeeId: id } });

      await tx.employee.delete({ where: { id } });
      return employeeToDelete;
    });
  } catch (error) {
    console.error(`Error deleting employee with ID ${id}:`, error);
    if (error instanceof PrismaClientKnownRequestError && error.code === 'P2025') {
      return null;
    }
    return null;
  }
};
