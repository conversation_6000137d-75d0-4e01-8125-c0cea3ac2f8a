# Prisma Database Management Guide

## Environment Setup

1. First, ensure your `DATABASE_URL` is properly set in your environment:

   ```bash
   # Check if DATABASE_URL is set
   echo $DATABASE_URL
   # Or on Windows PowerShell
   echo $env:DATABASE_URL
   ```

2. If needed, temporarily remove D<PERSON><PERSON><PERSON>E_URL from environment:
   ```bash
   # Linux/Mac
   unset DATABASE_URL
   # Windows Command Prompt
   set DATABASE_URL=
   # Windows PowerShell
   Remove-Item Env:DATABASE_URL
   ```

## Common Prisma Commands

### Database Inspection

- `npx prisma db pull --print` - Introspect database and print schema without
  saving
- `npx prisma migrate status` - Check status of migrations

### Schema Management

- `npx prisma migrate dev --name descriptive_name` - Create and apply a new
  migration
- `npx prisma generate` - Generate Prisma Client after schema changes
- `npx prisma db push` - Push schema changes directly (development only)

### Database Visualization

- `npx prisma studio` - Open Prisma Studio to view and edit data

## Migration Workflow Best Practices

1. Make changes to `schema.prisma`
2. Run `npx prisma generate` to update the client
3. Run `npx prisma migrate dev --name descriptive_name` to create a migration
4. Commit both schema changes and migration files to version control

## Troubleshooting

- If migrations fail, check the error message and fix the schema
- For schema/database mismatches, use `npx prisma db pull` to inspect current
  state
- When working in a team, always communicate schema changes

## References

- [Prisma Documentation](https://www.prisma.io/docs/)
- [Migration Guide](https://www.prisma.io/docs/concepts/components/prisma-migrate)
