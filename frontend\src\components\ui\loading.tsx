'use client';

import { <PERSON><PERSON><PERSON><PERSON>cle, Loader2, LucideIcon } from 'lucide-react';
import React from 'react';

import { ActionButton } from '@/components/ui/action-button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Skeleton } from '@/components/ui/skeleton';
import { cn } from '@/lib/utils';

// Size variants for the spinner
export type SpinnerSize = 'lg' | 'md' | 'sm' | 'xl';

// Size mappings for the spinner
const spinnerSizeClasses: Record<SpinnerSize, string> = {
  lg: 'h-8 w-8',
  md: 'h-6 w-6',
  sm: 'h-4 w-4',
  xl: 'h-12 w-12',
};

// Text size mappings for the spinner
const spinnerTextSizeClasses: Record<SpinnerSize, string> = {
  lg: 'text-base',
  md: 'text-sm',
  sm: 'text-xs',
  xl: 'text-lg',
};

export interface DataLoaderProps<T> {
  /**
   * Render function for the data
   */
  children: (data: T) => React.ReactNode;

  /**
   * Custom CSS class names
   */
  className?: string;

  /**
   * The data to render
   */
  data: null | T | undefined;

  /**
   * Custom empty state component
   */
  emptyComponent?: React.ReactNode;

  /**
   * Error message, if any
   */
  error?: null | string;

  /**
   * Custom error component
   */
  errorComponent?: React.ReactNode;

  /**
   * Whether data is currently loading
   */
  isLoading: boolean;

  /**
   * Custom loading component
   */
  loadingComponent?: React.ReactNode;

  /**
   * Function to retry loading data
   */
  onRetry?: () => void;
}

export interface EmptyStateProps {
  /**
   * Custom CSS class names
   */
  className?: string;

  /**
   * Description text to display
   */
  description?: string;

  /**
   * Icon to display (Lucide icon component)
   */
  icon?: LucideIcon;

  /**
   * Primary action button
   */
  primaryAction?: {
    label: string;
    onClick?: () => void;
    href?: string;
    icon?: React.ReactNode;
  };

  /**
   * Secondary action button
   */
  secondaryAction?: {
    label: string;
    onClick?: () => void;
    href?: string;
    icon?: React.ReactNode;
  };

  /**
   * Title text to display
   */
  title: string;
}

export interface ErrorDisplayProps {
  /**
   * Custom CSS class names
   */
  className?: string;

  /**
   * Error message to display
   */
  message: string;

  /**
   * Function to retry the operation
   */
  onRetry?: () => void;
}

export interface LoadingSpinnerProps {
  /**
   * Custom CSS class names
   */
  className?: string;

  /**
   * Whether to display as a full-page overlay
   */
  fullPage?: boolean;

  /**
   * Size of the spinner
   */
  size?: SpinnerSize;

  /**
   * Text to display below the spinner
   */
  text?: string;
}

export interface SkeletonLoaderProps {
  /**
   * Custom CSS class names
   */
  className?: string;

  /**
   * Number of skeleton items to display
   */
  count?: number;

  /**
   * Test ID for testing
   */
  testId?: string;

  /**
   * Type of content to show a skeleton for
   */
  variant?: SkeletonVariant;
}

// Skeleton variants
export type SkeletonVariant = 'card' | 'default' | 'list' | 'stats' | 'table';

/**
 * DataLoader component for handling loading, error, and empty states
 *
 * @example
 * <DataLoader
 *   isLoading={isLoading}
 *   error={error}
 *   data={vehicles}
 *   onRetry={refetch}
 *   loadingComponent={<SkeletonLoader variant="card" count={3} />}
 * >
 *   {(vehicles) => (
 *     <div className="grid grid-cols-3 gap-4">
 *       {vehicles.map(vehicle => (
 *         <VehicleCard key={vehicle.id} vehicle={vehicle} />
 *       ))}
 *     </div>
 *   )}
 * </DataLoader>
 */
export function DataLoader<T>({
  children,
  className,
  data,
  emptyComponent,
  error,
  errorComponent,
  isLoading,
  loadingComponent,
  onRetry,
}: DataLoaderProps<T>) {
  if (isLoading) {
    return (
      loadingComponent || (
        <LoadingSpinner {...(className && { className })} text="Loading..." />
      )
    );
  }

  if (error) {
    return (
      errorComponent || (
        <ErrorDisplay
          {...(className && { className })}
          message={error}
          {...(onRetry && { onRetry })}
        />
      )
    );
  }

  if (!data || (Array.isArray(data) && data.length === 0)) {
    return (
      emptyComponent || (
        <div className={cn('text-center py-8', className)}>
          <p className="text-muted-foreground">No data available</p>
        </div>
      )
    );
  }

  return <div className={className}>{children(data)}</div>;
}

/**
 * Unified empty state component following the design pattern from delegations page
 *
 * @example
 * <EmptyState
 *   title="No Service Records Found"
 *   description="There are no service records matching your current filters."
 *   icon={History}
 *   primaryAction={{
 *     label: "Log New Service",
 *     href: "/vehicles",
 *     icon: <PlusCircle className="size-4" />
 *   }}
 *   secondaryAction={{
 *     label: "Clear Filters",
 *     onClick: clearFilters
 *   }}
 * />
 */
export function EmptyState({
  className,
  description,
  icon: Icon,
  primaryAction,
  secondaryAction,
  title,
}: EmptyStateProps) {
  return (
    <div className={cn('space-y-6 text-center py-12', className)}>
      {Icon && (
        <div className="mx-auto flex h-20 w-20 items-center justify-center rounded-full bg-muted">
          <Icon className="h-10 w-10 text-muted-foreground" />
        </div>
      )}

      <div className="space-y-2">
        <h3 className="text-2xl font-semibold text-foreground">{title}</h3>
        {description && (
          <p className="text-muted-foreground max-w-md mx-auto">
            {description}
          </p>
        )}
      </div>

      <div className="flex flex-col sm:flex-row gap-3 justify-center">
        {primaryAction && (
          <ActionButton
            actionType="primary"
            asChild={!!primaryAction.href}
            icon={primaryAction.icon}
            onClick={primaryAction.onClick}
          >
            {primaryAction.href ? (
              <a href={primaryAction.href}>{primaryAction.label}</a>
            ) : (
              primaryAction.label
            )}
          </ActionButton>
        )}

        {secondaryAction && (
          <ActionButton
            actionType="tertiary"
            asChild={!!secondaryAction.href}
            icon={secondaryAction.icon}
            onClick={secondaryAction.onClick}
          >
            {secondaryAction.href ? (
              <a href={secondaryAction.href}>{secondaryAction.label}</a>
            ) : (
              secondaryAction.label
            )}
          </ActionButton>
        )}
      </div>
    </div>
  );
}

/**
 * Unified error display component
 *
 * @example
 * <ErrorDisplay message="Failed to load data" onRetry={refetch} />
 */
export function ErrorDisplay({
  className,
  message,
  onRetry,
}: ErrorDisplayProps) {
  return (
    <Alert className={cn('my-4', className)} variant="destructive">
      <AlertCircle className="size-4" />
      <AlertTitle>Error</AlertTitle>
      <AlertDescription>
        <div className="mt-2">
          <p className="mb-4 text-sm text-muted-foreground">{message}</p>
          {onRetry && (
            <ActionButton
              actionType="tertiary"
              icon={<Loader2 className="size-4" />}
              onClick={onRetry}
              size="sm"
            >
              Try Again
            </ActionButton>
          )}
        </div>
      </AlertDescription>
    </Alert>
  );
}

/**
 * Unified loading spinner component
 *
 * @example
 * <LoadingSpinner size="md" text="Loading data..." />
 */
export function LoadingSpinner({
  className,
  fullPage = false,
  size = 'md',
  text,
}: LoadingSpinnerProps) {
  return (
    <div
      className={cn(
        'flex items-center justify-center',
        fullPage && 'fixed inset-0 bg-background/80 backdrop-blur-sm z-50',
        className
      )}
    >
      <div className="flex flex-col items-center">
        <Loader2
          className={cn('animate-spin text-primary', spinnerSizeClasses[size])}
        />
        {text && (
          <span
            className={cn(
              'mt-2 text-muted-foreground',
              spinnerTextSizeClasses[size]
            )}
          >
            {text}
          </span>
        )}
      </div>
    </div>
  );
}

/**
 * Unified skeleton loader component
 *
 * @example
 * <SkeletonLoader variant="card" count={3} />
 */
export function SkeletonLoader({
  className,
  count = 1,
  testId = 'loading-skeleton',
  variant = 'default',
}: SkeletonLoaderProps) {
  // Render card skeleton (for entity cards like vehicles, employees)
  if (variant === 'card') {
    return (
      <div
        className={cn(
          'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6',
          className
        )}
        data-testid={testId}
      >
        {new Array(count).fill(0).map((_, i) => (
          <div
            className="overflow-hidden rounded-lg border bg-card shadow-md"
            key={i}
          >
            <Skeleton className="aspect-[16/10] w-full" />
            <div className="p-5">
              <Skeleton className="mb-1 h-7 w-3/4" />
              <Skeleton className="mb-3 h-4 w-1/2" />
              <Skeleton className="my-3 h-px w-full" />
              <div className="space-y-2.5">
                {Array.from({ length: 3 })
                  .fill(0)
                  .map((_, j) => (
                    <div className="flex items-center" key={j}>
                      <Skeleton className="mr-2.5 size-5 rounded-full" />
                      <Skeleton className="h-5 w-2/3" />
                    </div>
                  ))}
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  // Render table skeleton
  if (variant === 'table') {
    return (
      <div className={cn('space-y-3', className)} data-testid={testId}>
        <div className="flex gap-4">
          {Array.from({ length: 3 })
            .fill(0)
            .map((_, i) => (
              <Skeleton className="h-8 flex-1" key={i} />
            ))}
        </div>
        {new Array(count).fill(0).map((_, i) => (
          <div className="flex gap-4" key={i}>
            {Array.from({ length: 3 })
              .fill(0)
              .map((_, j) => (
                <Skeleton className="h-6 flex-1" key={j} />
              ))}
          </div>
        ))}
      </div>
    );
  }

  // Render list skeleton
  if (variant === 'list') {
    return (
      <div className={cn('space-y-3', className)} data-testid={testId}>
        {new Array(count).fill(0).map((_, i) => (
          <div className="flex items-center gap-4" key={i}>
            <Skeleton className="size-12 rounded-full" />
            <div className="flex-1 space-y-2">
              <Skeleton className="h-4 w-1/3" />
              <Skeleton className="h-4 w-full" />
            </div>
          </div>
        ))}
      </div>
    );
  }

  // Render stats skeleton
  if (variant === 'stats') {
    return (
      <div
        className={cn('grid gap-4 md:grid-cols-2 lg:grid-cols-3', className)}
        data-testid={testId}
      >
        {new Array(count).fill(0).map((_, i) => (
          <div className="rounded-lg border bg-card p-5 shadow-sm" key={i}>
            <div className="flex justify-between">
              <Skeleton className="h-5 w-1/3" />
              <Skeleton className="size-5 rounded-full" />
            </div>
            <Skeleton className="mt-3 h-8 w-1/2" />
            <Skeleton className="mt-2 h-4 w-2/3" />
          </div>
        ))}
      </div>
    );
  }

  // Default skeleton
  return (
    <div className={cn('space-y-2', className)} data-testid={testId}>
      {new Array(count).fill(0).map((_, i) => (
        <Skeleton className="h-5 w-full" key={i} />
      ))}
    </div>
  );
}
