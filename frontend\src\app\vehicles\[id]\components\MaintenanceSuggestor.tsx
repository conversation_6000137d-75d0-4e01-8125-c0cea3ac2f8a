'use client';

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Info, Lightbulb, Loader2 } from 'lucide-react';
import { useState } from 'react';

import type {
  SuggestMaintenanceScheduleInput,
  SuggestMaintenanceScheduleOutput,
} from '@/ai/flows/suggest-maintenance-schedule';
import type { ServiceRecord, Vehicle } from '@/lib/types/domain';

import { getMaintenanceSuggestions } from '@/app/actions';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';

interface MaintenanceSuggestorProps {
  vehicle: Vehicle;
}

export default function MaintenanceSuggestor({
  vehicle,
}: MaintenanceSuggestorProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [suggestion, setSuggestion] =
    useState<null | SuggestMaintenanceScheduleOutput>(null);
  const [error, setError] = useState<null | string>(null);
  const [currentOdometer, setCurrentOdometer] = useState<number>(
    (vehicle.serviceHistory?.length ?? 0) > 0
      ? Math.max(...(vehicle.serviceHistory?.map(s => s.odometer) ?? [0]))
      : (vehicle.initialOdometer ?? 0)
  );

  const handleGetSuggestion = async () => {
    setIsLoading(true);
    setSuggestion(null);
    setError(null);

    if (currentOdometer < (vehicle.initialOdometer ?? 0)) {
      setError('Current odometer cannot be less than initial odometer.');
      setIsLoading(false);
      return;
    }
    if (
      vehicle.serviceHistory?.some(record => record.odometer > currentOdometer)
    ) {
      setError(
        'Current odometer cannot be less than a previously logged service odometer.'
      );
      setIsLoading(false);
      return;
    }

    const formattedHistory = formatServiceHistoryForAI(
      vehicle.serviceHistory ?? []
    );
    const input: SuggestMaintenanceScheduleInput = {
      currentOdometer: currentOdometer,
      serviceHistory: formattedHistory,
      vehicleMake: vehicle.make,
      vehicleModel: vehicle.model,
      vehicleYear: vehicle.year,
    };

    const result = await getMaintenanceSuggestions(input);

    if (result.success && result.data) {
      setSuggestion(result.data);
    } else {
      setError(result.error || 'Failed to get suggestions.');
    }
    setIsLoading(false);
  };

  return (
    <Card className="bg-card shadow-lg">
      <CardHeader>
        <CardTitle className="flex items-center text-xl font-semibold text-primary">
          <Lightbulb className="mr-2 size-5 text-accent" />
          AI Maintenance Advisor
        </CardTitle>
        <CardDescription>
          Get AI-powered maintenance schedule suggestions based on your
          vehicle's details and service history.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <Label
            className="mb-1 block text-sm font-medium"
            htmlFor="currentOdometerAi"
          >
            Current Odometer for Suggestion
          </Label>
          <Input
            className="mb-4"
            id="currentOdometerAi"
            onChange={e =>
              setCurrentOdometer(Number.parseInt(e.target.value, 10) || 0)
            }
            placeholder="Enter current mileage"
            type="number"
            value={currentOdometer}
          />
        </div>
        <Button
          className="w-full bg-accent text-accent-foreground hover:bg-accent/90"
          disabled={isLoading}
          onClick={handleGetSuggestion}
        >
          {isLoading ? (
            <>
              <Loader2 className="mr-2 size-4 animate-spin" />
              Generating...
            </>
          ) : (
            'Get Maintenance Suggestions'
          )}
        </Button>

        {error && (
          <Alert className="mt-4" variant="destructive">
            <AlertTriangle className="size-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {suggestion && (
          <div className="mt-6 space-y-4 rounded-lg border border-border bg-background p-4">
            <div>
              <h4 className="mb-2 text-lg font-semibold text-primary">
                Suggested Maintenance Schedule:
              </h4>
              <Textarea
                aria-label="Suggested Maintenance Schedule"
                className="min-h-[150px] bg-muted/50"
                readOnly
                value={suggestion.suggestedMaintenanceSchedule}
              />
            </div>
            <div>
              <h4 className="mb-2 text-lg font-semibold text-primary">
                Reasoning:
              </h4>
              <Textarea
                aria-label="Reasoning for suggestions"
                className="min-h-[100px] bg-muted/50"
                readOnly
                value={suggestion.reasoning}
              />
            </div>
          </div>
        )}
        {!suggestion && !isLoading && !error && (
          <Alert className="mt-4 border-primary/30">
            <Info className="size-4 text-primary" />
            <AlertTitle className="text-primary">Ready for Advice?</AlertTitle>
            <AlertDescription>
              Enter your vehicle's current odometer reading above and click the
              button to receive personalized maintenance suggestions from our AI
              Advisor.
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  );
}

function formatServiceHistoryForAI(serviceHistory: ServiceRecord[]): string {
  if (!serviceHistory || serviceHistory.length === 0) {
    return 'No service history available.';
  }
  return serviceHistory
    .map(
      record =>
        `Date: ${record.date}, Mileage: ${
          record.odometer
        } miles, Service: ${record.servicePerformed.join(', ')}${
          record.notes ? `, Notes: ${record.notes}` : ''
        }${record.cost ? `, Cost: $${record.cost}` : ''}`
    )
    .join('; ');
}
