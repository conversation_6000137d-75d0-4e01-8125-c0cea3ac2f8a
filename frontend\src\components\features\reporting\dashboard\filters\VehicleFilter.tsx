// frontend/src/components/features/reporting/dashboard/filters/VehicleFilter.tsx

import React from 'react';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { cn } from '@/lib/utils';
import { ChevronDown, X, Search, Car } from 'lucide-react';
import {
  useReportingFilters,
  useReportingFiltersActions,
  useReportingFiltersValidation,
} from '../../data/stores/useReportingFiltersStore';

interface VehicleFilterProps {
  compact?: boolean;
  className?: string;
}

export const VehicleFilter: React.FC<VehicleFilterProps> = ({
  compact = false,
  className = '',
}) => {
  const filters = useReportingFilters();
  const { setVehicles } = useReportingFiltersActions();
  const { validationErrors } = useReportingFiltersValidation();

  const [isOpen, setIsOpen] = React.useState(false);
  const [searchTerm, setSearchTerm] = React.useState('');

  // Mock vehicle options - in real app, this would come from an API
  const vehicleOptions = [
    { id: 1, make: 'Toyota', model: 'Camry', year: 2022, licensePlate: 'ABC-123' },
    { id: 2, make: 'Honda', model: 'Accord', year: 2021, licensePlate: 'DEF-456' },
    { id: 3, make: 'Ford', model: 'F-150', year: 2023, licensePlate: 'GHI-789' },
    { id: 4, make: 'Chevrolet', model: 'Silverado', year: 2022, licensePlate: 'JKL-012' },
    { id: 5, make: 'BMW', model: 'X5', year: 2021, licensePlate: 'MNO-345' },
    { id: 6, make: 'Mercedes', model: 'E-Class', year: 2023, licensePlate: 'PQR-678' },
    { id: 7, make: 'Audi', model: 'A4', year: 2022, licensePlate: 'STU-901' },
    { id: 8, make: 'Nissan', model: 'Altima', year: 2021, licensePlate: 'VWX-234' },
  ];

  const filteredVehicles = vehicleOptions.filter(vehicle =>
    vehicle.make.toLowerCase().includes(searchTerm.toLowerCase()) ||
    vehicle.model.toLowerCase().includes(searchTerm.toLowerCase()) ||
    vehicle.licensePlate.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleVehicleToggle = (vehicleId: number) => {
    const currentVehicles = filters.vehicles;
    const isSelected = currentVehicles.includes(vehicleId);
    
    if (isSelected) {
      setVehicles(currentVehicles.filter(id => id !== vehicleId));
    } else {
      setVehicles([...currentVehicles, vehicleId]);
    }
  };

  const handleSelectAll = () => {
    setVehicles(filteredVehicles.map(vehicle => vehicle.id));
  };

  const handleSelectNone = () => {
    setVehicles([]);
  };

  const getDisplayText = () => {
    const selectedCount = filters.vehicles.length;
    if (selectedCount === 0) return 'All vehicles';
    if (selectedCount === 1) {
      const vehicle = vehicleOptions.find(v => v.id === filters.vehicles[0]);
      return vehicle ? `${vehicle.make} ${vehicle.model}` : 'Unknown';
    }
    return `${selectedCount} vehicles`;
  };

  const hasError = validationErrors.vehicles;

  if (compact) {
    return (
      <div className={cn('space-y-1', className)}>
        <Label className="text-xs font-medium">Vehicle</Label>
        <Popover open={isOpen} onOpenChange={setIsOpen}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className={cn(
                'w-full justify-between text-left font-normal',
                hasError && 'border-red-500'
              )}
            >
              <span className="truncate">{getDisplayText()}</span>
              <ChevronDown className="ml-2 h-4 w-4 shrink-0" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-64 p-0" align="start">
            <div className="p-3">
              <div className="relative mb-3">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search vehicles..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8"
                />
              </div>
              <div className="flex justify-between mb-3">
                <Button variant="ghost" size="sm" onClick={handleSelectAll}>
                  All
                </Button>
                <Button variant="ghost" size="sm" onClick={handleSelectNone}>
                  None
                </Button>
              </div>
              <div className="max-h-48 overflow-y-auto space-y-2">
                {filteredVehicles.map((vehicle) => (
                  <div key={vehicle.id} className="flex items-center space-x-2">
                    <Checkbox
                      id={`vehicle-${vehicle.id}`}
                      checked={filters.vehicles.includes(vehicle.id)}
                      onCheckedChange={() => handleVehicleToggle(vehicle.id)}
                    />
                    <Label
                      htmlFor={`vehicle-${vehicle.id}`}
                      className="text-sm font-normal cursor-pointer flex-1"
                    >
                      <div className="flex items-center gap-2">
                        <Car className="h-3 w-3 text-muted-foreground" />
                        <div>
                          <div className="font-medium">{vehicle.make} {vehicle.model}</div>
                          <div className="text-xs text-muted-foreground">{vehicle.licensePlate}</div>
                        </div>
                      </div>
                    </Label>
                  </div>
                ))}
              </div>
            </div>
          </PopoverContent>
        </Popover>
        {hasError && (
          <p className="text-xs text-red-600">{hasError}</p>
        )}
      </div>
    );
  }

  return (
    <div className={cn('space-y-2', className)}>
      <Label className="text-sm font-medium">Vehicles</Label>
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className={cn(
              'w-full justify-between text-left font-normal',
              hasError && 'border-red-500'
            )}
          >
            <span>{getDisplayText()}</span>
            <ChevronDown className="ml-2 h-4 w-4 shrink-0" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-80 p-0" align="start">
          <div className="p-4">
            <div className="flex justify-between items-center mb-4">
              <h4 className="font-medium text-sm">Select Vehicles</h4>
              <div className="flex gap-2">
                <Button variant="ghost" size="sm" onClick={handleSelectAll}>
                  All
                </Button>
                <Button variant="ghost" size="sm" onClick={handleSelectNone}>
                  None
                </Button>
              </div>
            </div>
            
            <div className="relative mb-4">
              <Search className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search vehicles..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-9"
              />
            </div>
            
            <div className="max-h-64 overflow-y-auto space-y-3">
              {filteredVehicles.map((vehicle) => (
                <div key={vehicle.id} className="flex items-center space-x-3">
                  <Checkbox
                    id={`vehicle-${vehicle.id}`}
                    checked={filters.vehicles.includes(vehicle.id)}
                    onCheckedChange={() => handleVehicleToggle(vehicle.id)}
                  />
                  <Label
                    htmlFor={`vehicle-${vehicle.id}`}
                    className="text-sm font-normal cursor-pointer flex-1"
                  >
                    <div className="flex items-center gap-3">
                      <Car className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <div className="font-medium">{vehicle.make} {vehicle.model} ({vehicle.year})</div>
                        <div className="text-xs text-muted-foreground">{vehicle.licensePlate}</div>
                      </div>
                    </div>
                  </Label>
                </div>
              ))}
            </div>
          </div>
        </PopoverContent>
      </Popover>
      
      {/* Selected vehicle badges */}
      {filters.vehicles.length > 0 && (
        <div className="flex flex-wrap gap-1">
          {filters.vehicles.slice(0, 3).map((vehicleId) => {
            const vehicle = vehicleOptions.find(v => v.id === vehicleId);
            if (!vehicle) return null;
            
            return (
              <Badge key={vehicleId} variant="secondary" className="text-xs pr-1">
                {vehicle.make} {vehicle.model}
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-auto p-0 ml-1 hover:bg-transparent"
                  onClick={() => handleVehicleToggle(vehicleId)}
                >
                  <X className="h-3 w-3" />
                </Button>
              </Badge>
            );
          })}
          {filters.vehicles.length > 3 && (
            <Badge variant="secondary" className="text-xs">
              +{filters.vehicles.length - 3} more
            </Badge>
          )}
        </div>
      )}
      
      {hasError && (
        <p className="text-sm text-red-600">{hasError}</p>
      )}
    </div>
  );
};

export default VehicleFilter;
