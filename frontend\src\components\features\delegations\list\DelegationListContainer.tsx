'use client';

import { useCallback, useMemo } from 'react';

import type { Delegation } from '@/lib/types/domain'; // Use domain type directly
import type { DelegationFilterValues } from '@/components/features/delegations/DelegationFilters';

import { useDelegations } from '@/lib/stores/queries/useDelegations'; // Import useDelegations hook

interface DelegationListContainerProps {
  children: (data: {
    delegations: Delegation[];
    error: null | string;
    fetchDelegations: () => Promise<void>;
    loading: boolean;
  }) => React.ReactNode;
  searchTerm?: string;
  filters?: DelegationFilterValues;
}

export default function DelegationListContainer({
  children,
  searchTerm = '',
  filters,
}: DelegationListContainerProps) {
  console.log('DelegationListContainer rendered');
  const {
    data: allDelegations = [],
    error: queryError,
    isLoading: dataIsLoading,
    refetch,
  } = useDelegations();

  const error = queryError ? queryError.message : null;

  // ✅ PRODUCTION-READY FIX: Use useMemo to prevent infinite re-renders
  // Memoize the filtered delegations based on stable dependencies
  const filteredDelegations = useMemo(() => {
    console.log('Filtering delegations', {
      allDelegationsCount: allDelegations.length,
      searchTerm,
      filters,
    });

    let tempDelegations = [...allDelegations];

    // Apply search filter (from filters or legacy searchTerm)
    const searchQuery = filters?.search || searchTerm;
    if (searchQuery) {
      const lowercaseSearchTerm = searchQuery.toLowerCase();
      tempDelegations = tempDelegations.filter(delegation => {
        return (
          delegation.eventName.toLowerCase().includes(lowercaseSearchTerm) ||
          delegation.location.toLowerCase().includes(lowercaseSearchTerm) ||
          delegation.status.toLowerCase().includes(lowercaseSearchTerm) ||
          delegation.delegates?.some(delegate =>
            delegate.name.toLowerCase().includes(lowercaseSearchTerm)
          )
        );
      });
    }

    // Apply status filter
    if (filters?.status && filters.status.length > 0) {
      tempDelegations = tempDelegations.filter(delegation =>
        filters.status.includes(delegation.status)
      );
    }

    // Apply location filter
    if (filters?.location && filters.location.length > 0) {
      tempDelegations = tempDelegations.filter(delegation =>
        filters.location.includes(delegation.location)
      );
    }

    // Apply date range filter
    if (
      filters?.dateRange &&
      (filters.dateRange.from || filters.dateRange.to)
    ) {
      tempDelegations = tempDelegations.filter(delegation => {
        const delegationStartDate = new Date(delegation.durationFrom);
        const delegationEndDate = new Date(delegation.durationTo);

        if (filters.dateRange.from && filters.dateRange.to) {
          // Both dates selected - check if delegation overlaps with range
          return (
            delegationStartDate <= filters.dateRange.to &&
            delegationEndDate >= filters.dateRange.from
          );
        } else if (filters.dateRange.from) {
          // Only start date selected - delegation must end after this date
          return delegationEndDate >= filters.dateRange.from;
        } else if (filters.dateRange.to) {
          // Only end date selected - delegation must start before this date
          return delegationStartDate <= filters.dateRange.to;
        }
        return true;
      });
    }

    // Apply driver filter
    if (filters?.drivers && filters.drivers.length > 0) {
      tempDelegations = tempDelegations.filter(delegation =>
        delegation.drivers?.some(driver =>
          filters.drivers.includes(driver.employee?.id.toString() || '')
        )
      );
    }

    // Apply escort filter
    if (filters?.escorts && filters.escorts.length > 0) {
      tempDelegations = tempDelegations.filter(delegation =>
        delegation.escorts?.some(escort =>
          filters.escorts.includes(escort.employee?.id.toString() || '')
        )
      );
    }

    // Apply vehicle filter
    if (filters?.vehicles && filters.vehicles.length > 0) {
      tempDelegations = tempDelegations.filter(delegation =>
        delegation.vehicles?.some(vehicle =>
          filters.vehicles.includes(vehicle.vehicle?.id.toString() || '')
        )
      );
    }

    return tempDelegations;
  }, [searchTerm, allDelegations, filters]);

  const stableFetchDelegations = useCallback(async () => {
    console.log('Manual fetch triggered via fetchDelegations prop (refetch)');
    await refetch();
  }, [refetch]);

  return (
    <>
      {children({
        delegations: filteredDelegations,
        error: error,
        fetchDelegations: stableFetchDelegations,
        loading: dataIsLoading,
      })}
    </>
  );
}
