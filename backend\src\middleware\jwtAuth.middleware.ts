// ============================================================================
// WorkHub Phase 1.4: Enhanced JWT Validation Middleware
// Implements robust JWT validation following SRP principles
// File: backend/src/middleware/jwtAuth.middleware.ts (Replaced legacy version)
// ============================================================================

import type { NextFunction, Request, Response } from 'express';

import { createHash } from 'crypto';

import type { AuthenticatedUser } from '../types/express.d.js'; // Import AuthenticatedUser

import { supabaseAdmin } from '../lib/supabase.js';
import { logAuditEvent } from '../utils/auditLogger.js';
import HttpError from '../utils/HttpError.js';
import logger, { createContextLogger } from '../utils/logger.js';

// SRP: JWT validation logic
class JwtValidator {
  static extractCustomClaims(user: AuthenticatedUser): {
    employeeId?: string;
    isActive: boolean;
    userRole: string;
  } {
    logger.debug('Extracting custom claims from user:', {
      app_metadata: user.app_metadata,
      user_metadata: user.user_metadata,
    });

    // SECURITY HARDENING: Standardized claims extraction with clear priority order
    // Priority: app_metadata.custom_claims > app_metadata direct > user_metadata > defaults
    const customClaims = user.app_metadata?.custom_claims;

    const employeeId =
      customClaims?.employee_id ||
      user.app_metadata?.employee_id ||
      user.user_metadata?.employee_id ||
      'UNKNOWN_EMPLOYEE_ID';

    const isActive =
      customClaims?.is_active ??
      user.app_metadata?.is_active ??
      user.user_metadata?.is_active ??
      true;

    // Standardized role extraction with clear fallback hierarchy
    const userRole =
      customClaims?.user_role ||
      user.app_metadata?.user_role ||
      user.app_metadata?.role ||
      user.user_metadata?.user_role ||
      user.user_metadata?.role ||
      'USER';

    // SECURITY: Validate extracted role against allowed roles
    const allowedRoles = ['SUPER_ADMIN', 'ADMIN', 'MANAGER', 'USER', 'READONLY'];
    const validatedRole = allowedRoles.includes(userRole) ? userRole : 'USER';

    if (validatedRole !== userRole) {
      logger.warn('Invalid role detected, defaulting to USER', {
        extractedRole: userRole,
        userId: user.id,
        validatedRole,
      });
    }

    logger.debug('Final extracted claims:', { employeeId, isActive, userRole: validatedRole });

    return {
      employeeId,
      isActive,
      userRole: validatedRole,
    };
  }

  static async validateWithSupabase(token: string): Promise<AuthenticatedUser | null> {
    try {
      const {
        data: { user },
        error,
      } = await supabaseAdmin.auth.getUser(token);

      if (error || !user) {
        logger.warn('JWT validation failed with Supabase', {
          error: error?.message,
          tokenHash: TokenSecurityUtils.hashToken(token),
        });
        return null;
      }

      return user as AuthenticatedUser;
    } catch (error) {
      logger.error('JWT validation error', {
        error: error instanceof Error ? error.message : String(error),
        tokenHash: TokenSecurityUtils.hashToken(token),
      });
      return null;
    }
  }
}

// SRP: Security event logging
class SecurityEventLogger {
  static logAuthenticationFailure(
    reason: string,
    errorCode: string,
    req: Request,
    tokenHash?: string,
  ): void {
    logAuditEvent(
      {
        action: 'TOKEN_VALIDATION_FAILURE',
        details: {
          ip: req.ip,
          method: req.method,
          path: req.path,
          tokenHash,
          userAgent: req.get('User-Agent'),
        },
        errorCode,
        eventType: 'AUTH',
        message: reason,
        outcome: 'FAILURE',
        statusCode: 401,
      },
      req,
    );
  }

  static logAuthenticationSuccess(user: AuthenticatedUser, req: Request): void {
    logAuditEvent(
      {
        action: 'AUTHENTICATION_SUCCESS',
        details: {
          email: user.email,
          method: req.method,
          path: req.path,
        },
        eventType: 'AUTH',
        message: 'User authenticated successfully via enhanced JWT middleware.',
        outcome: 'SUCCESS',
        userId: user.id,
        userRole: user.user_role || 'USER',
      },
      req,
    );
  }
}

// SRP: Token security utilities
class TokenSecurityUtils {
  static extractTokenFromCookie(
    cookieHeader: string | undefined,
    cookieName: string,
  ): null | string {
    if (!cookieHeader) {
      return null;
    }
    const nameEQ = cookieName + '=';
    const ca = cookieHeader.split(';');
    for (let i = 0; i < ca.length; i++) {
      let c = ca[i];
      while (c.startsWith(' ')) c = c.substring(1, c.length);
      if (c.startsWith(nameEQ)) return c.substring(nameEQ.length, c.length);
    }
    return null;
  }

  static extractTokenFromHeader(authHeader: string | undefined): null | string {
    if (!authHeader?.startsWith('Bearer ')) {
      return null;
    }
    return authHeader.split(' ')[1];
  }

  static hashToken(token: string): string {
    return createHash('sha256').update(token).digest('hex').substring(0, 8);
  }

  static validateTokenFormat(token: string): boolean {
    const tokenParts = token.split('.');
    return tokenParts.length === 3;
  }
}

// SRP: Main authentication middleware
export const enhancedJwtAuthMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction,
): Promise<void> => {
  const contextLogger = createContextLogger({
    ip: req.ip,
    method: req.method,
    requestId: req.headers['x-request-id'] as string,
    service: 'enhanced-jwt-auth',
    url: req.url,
    userAgent: req.headers['user-agent'],
  });

  try {
    // Prioritize extracting token from httpOnly cookie
    let token = TokenSecurityUtils.extractTokenFromCookie(req.headers.cookie, 'sb-access-token');

    // Fallback to Authorization header if not found in cookie
    if (!token) {
      token = TokenSecurityUtils.extractTokenFromHeader(req.headers.authorization);
    }

    if (!token) {
      SecurityEventLogger.logAuthenticationFailure(
        'Authentication attempt without token.',
        'NO_TOKEN',
        req,
      );

      next(new HttpError('Please provide a valid authorization token', 401, 'NO_TOKEN'));
      return;
    }

    // Validate token format
    if (!TokenSecurityUtils.validateTokenFormat(token)) {
      const tokenHash = TokenSecurityUtils.hashToken(token);
      SecurityEventLogger.logAuthenticationFailure(
        'Invalid JWT format received.',
        'INVALID_TOKEN_FORMAT',
        req,
        tokenHash,
      );

      next(new HttpError('Token must be a valid JWT', 401, 'INVALID_TOKEN_FORMAT'));
      return;
    }

    // Validate token with Supabase
    const user = await JwtValidator.validateWithSupabase(token);

    if (!user) {
      const tokenHash = TokenSecurityUtils.hashToken(token);
      SecurityEventLogger.logAuthenticationFailure(
        'JWT validation failed with provider.',
        'INVALID_TOKEN',
        req,
        tokenHash,
      );

      next(new HttpError('Authentication failed', 401, 'INVALID_TOKEN'));
      return;
    }

    // Verify email confirmation
    if (!user.email_confirmed_at) {
      SecurityEventLogger.logAuthenticationFailure(
        'User authentication failed: Email not verified.',
        'EMAIL_NOT_VERIFIED',
        req,
      );

      next(new HttpError('Please verify your email address', 401, 'EMAIL_NOT_VERIFIED'));
      return;
    }

    // Extract custom claims
    const { employeeId, isActive, userRole } = JwtValidator.extractCustomClaims(user);

    // Check if user is active
    if (!isActive) {
      SecurityEventLogger.logAuthenticationFailure(
        'User account is inactive.',
        'ACCOUNT_INACTIVE',
        req,
      );

      next(new HttpError('Your account has been deactivated', 401, 'ACCOUNT_INACTIVE'));
      return;
    }

    // Attach user data to request
    req.user = user;
    req.userId = user.id;
    req.userRole = userRole;
    req.employeeId = employeeId;
    req.isActive = isActive; // Explicitly set isActive on the request object

    // Log successful authentication
    SecurityEventLogger.logAuthenticationSuccess(user, req);

    contextLogger.info('User authenticated successfully', {
      employeeId,
      hostname: req.hostname, // Add hostname to logs
      ip: req.ip, // Add IP to logs
      userId: user.id,
      userRole,
    });

    next();
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);

    contextLogger.error('Authentication middleware error', {
      error: errorMessage,
      stack: error instanceof Error ? error.stack : undefined,
    });

    logAuditEvent(
      {
        action: 'AUTHENTICATION_MIDDLEWARE_ERROR',
        details: {
          error: errorMessage,
          method: req.method,
          path: req.path,
        },
        errorCode: 'MIDDLEWARE_ERROR',
        eventType: 'SYSTEM_EVENT',
        message: `Authentication middleware error: ${errorMessage}`,
        outcome: 'FAILURE',
        statusCode: 500,
      },
      req,
    );

    next(new HttpError('Unable to process authentication', 500, 'AUTHENTICATION_ERROR'));
  }
};

// Export for backward compatibility with existing route imports
export { enhancedJwtAuthMiddleware as enhancedAuthenticateUser };

// Legacy export name for any remaining old imports
export { enhancedJwtAuthMiddleware as authenticateUser };

// Additional backward compatibility exports that might be needed
export { enhancedJwtAuthMiddleware as jwtAuthMiddleware };
export { enhancedJwtAuthMiddleware as authenticateToken };
