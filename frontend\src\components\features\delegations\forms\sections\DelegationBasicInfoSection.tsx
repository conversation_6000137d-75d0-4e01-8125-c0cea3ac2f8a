/**
 * DelegationBasicInfoSection Component - Enhanced SOLID Principles Implementation
 *
 * Single Responsibility: Handles basic delegation information fields
 * - Event details, duration, status, and invitation information
 * - Enhanced UX with clearer organization and validation feedback
 * - Follows SRP by focusing only on basic info section
 *
 * @module DelegationBasicInfoSection
 */

import React from 'react';
import { useFormContext, type ControllerRenderProps } from 'react-hook-form';
import {
  Calendar,
  MapPin,
  Clock,
  Users2,
  Info,
  CheckCircle,
  AlertCircle,
} from 'lucide-react';

import type { DelegationFormData } from '@/lib/schemas/delegationSchemas';
import type { DelegationStatusPrisma } from '@/lib/types/domain';
import { useDelegationValidation } from '@/hooks/forms/useDelegationValidation';

import { ValidatedField } from '@/components/ui/forms/ValidatedField';
import { FormField } from '@/components/ui/forms/formField';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { DelegationStatusSchema } from '@/lib/schemas/delegationSchemas';
import { formatDelegationStatusForDisplay } from '@/lib/utils/formattingUtils';

// ============================================================================
// INTERFACES
// ============================================================================

export interface DelegationBasicInfoSectionProps {
  /** Whether the form is currently submitting */
  isSubmitting?: boolean;
  /** Additional CSS classes */
  className?: string;
  /** User's role for permission-based UI */
  userRole?: string;
}

// ============================================================================
// HELPER FUNCTIONS
// ============================================================================

/**
 * Get status badge variant based on status
 */
const getStatusBadgeVariant = (
  status: string
): 'default' | 'secondary' | 'destructive' | 'outline' => {
  switch (status) {
    case 'Completed':
      return 'default';
    case 'In_Progress':
      return 'secondary';
    case 'Cancelled':
      return 'destructive';
    default:
      return 'outline';
  }
};

/**
 * Calculate duration in days
 */
const calculateDuration = (from: string, to: string): number => {
  if (!from || !to) return 0;
  const fromDate = new Date(from);
  const toDate = new Date(to);
  const diffTime = Math.abs(toDate.getTime() - fromDate.getTime());
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
};

// ============================================================================
// COMPONENT IMPLEMENTATION
// ============================================================================

/**
 * Enhanced Basic Information Section for Delegation Form
 *
 * Provides improved user experience with:
 * - Clear visual organization with icons and sections
 * - Real-time validation feedback
 * - Helpful duration calculation
 * - Status management with visual indicators
 * - Context-aware help text
 */
export const DelegationBasicInfoSection: React.FC<
  DelegationBasicInfoSectionProps
> = ({ isSubmitting = false, className = '', userRole = 'user' }) => {
  const {
    control,
    watch,
    formState: { errors },
  } = useFormContext<DelegationFormData>();

  const { getSectionValidationState } = useDelegationValidation();
  const watchedValues = watch();
  const duration = calculateDuration(
    watchedValues.durationFrom,
    watchedValues.durationTo
  );
  const hasDateRange = watchedValues.durationFrom && watchedValues.durationTo;

  // Get section validation state
  const sectionValidation = getSectionValidationState('basic');
  const isBasicInfoComplete = sectionValidation.isComplete;

  return (
    <section className={`space-y-6 rounded-lg border bg-card p-6 ${className}`}>
      {/* Section Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="flex items-center justify-center w-10 h-10 rounded-lg bg-accent/10">
            <Calendar className="h-5 w-5 text-accent" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-foreground">
              Event Details
            </h3>
            <p className="text-sm text-muted-foreground">
              Information about the delegation event
            </p>
          </div>
        </div>

        {isBasicInfoComplete && (
          <Badge variant="default" className="flex items-center gap-1">
            <CheckCircle className="h-3 w-3" />
            Complete
          </Badge>
        )}
      </div>

      {/* Event Information */}
      <div className="space-y-4">
        <div className="flex items-center gap-2 text-sm font-medium text-foreground mb-3">
          <Users2 className="h-4 w-4 text-accent" />
          Event Information
        </div>

        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <ValidatedField
            name="eventName"
            label="Event Name"
            disabled={isSubmitting}
            placeholder="e.g., Board Meeting, Conference, Official Visit"
          />

          <ValidatedField
            name="location"
            label="Location"
            disabled={isSubmitting}
            placeholder="e.g., Rabat, Morocco"
          />
        </div>
      </div>

      {/* Duration Information */}
      <div className="space-y-4">
        <div className="flex items-center gap-2 text-sm font-medium text-foreground mb-3">
          <Clock className="h-4 w-4 text-accent" />
          Duration & Timing
        </div>

        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <ValidatedField
            name="durationFrom"
            label="Start Date"
            type="date"
            disabled={isSubmitting}
          />

          <ValidatedField
            name="durationTo"
            label="End Date"
            type="date"
            disabled={isSubmitting}
          />
        </div>

        {/* Duration Display */}
        {hasDateRange && duration > 0 && (
          <Alert>
            <Info className="h-4 w-4" />
            <AlertDescription>
              <div className="flex items-center gap-2">
                <span>Duration:</span>
                <Badge variant="secondary">
                  {duration} {duration === 1 ? 'day' : 'days'}
                </Badge>
                {duration > 7 && (
                  <span className="text-sm text-muted-foreground">
                    (Extended delegation - consider additional planning)
                  </span>
                )}
              </div>
            </AlertDescription>
          </Alert>
        )}

        {/* Date validation error */}
        {errors.durationTo && (
          <Alert variant="destructive">
            <AlertDescription>{errors.durationTo.message}</AlertDescription>
          </Alert>
        )}
      </div>

      {/* Optional Information */}
      <div className="space-y-4">
        <div className="flex items-center gap-2 text-sm font-medium text-foreground mb-3">
          <MapPin className="h-4 w-4 text-accent" />
          Additional Details
        </div>

        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <ValidatedField
            name="invitationFrom"
            label="Invitation From"
            disabled={isSubmitting}
            placeholder="Optional: When invitations should be sent"
            showRequiredIndicator={false}
          />

          <ValidatedField
            name="invitationTo"
            label="Invitation To"
            disabled={isSubmitting}
            placeholder="Optional: RSVP deadline"
            showRequiredIndicator={false}
          />
        </div>
      </div>

      {/* Status Management */}
      <div className="space-y-4">
        <div className="flex items-center gap-2 text-sm font-medium text-foreground mb-3">
          <div
            className={`h-4 w-4 rounded-full ${
              getStatusBadgeVariant(watchedValues.status || 'Planned') ===
              'default'
                ? 'bg-green-500'
                : getStatusBadgeVariant(watchedValues.status || 'Planned') ===
                    'secondary'
                  ? 'bg-blue-500'
                  : getStatusBadgeVariant(watchedValues.status || 'Planned') ===
                      'destructive'
                    ? 'bg-red-500'
                    : 'bg-gray-400'
            }`}
          />
          Status Management
        </div>

        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <FormField
            label="Current Status"
            name="status"
            render={({
              field,
            }: {
              field: ControllerRenderProps<DelegationFormData, 'status'>;
            }) => (
              <div className="space-y-2">
                <Select
                  value={field.value || ''}
                  onValueChange={field.onChange}
                  disabled={isSubmitting}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select delegation status" />
                  </SelectTrigger>
                  <SelectContent>
                    {DelegationStatusSchema.options.map(statusVal => (
                      <SelectItem key={statusVal} value={statusVal}>
                        <div className="flex items-center gap-2">
                          <div
                            className={`h-2 w-2 rounded-full ${
                              getStatusBadgeVariant(statusVal) === 'default'
                                ? 'bg-green-500'
                                : getStatusBadgeVariant(statusVal) ===
                                    'secondary'
                                  ? 'bg-blue-500'
                                  : getStatusBadgeVariant(statusVal) ===
                                      'destructive'
                                    ? 'bg-red-500'
                                    : 'bg-gray-400'
                            }`}
                          />
                          {formatDelegationStatusForDisplay(
                            statusVal as DelegationStatusPrisma
                          )}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {field.value && (
                  <p className="text-xs text-muted-foreground">
                    {field.value === 'Planned' &&
                      'Initial planning phase - details can be modified'}
                    {field.value === 'Confirmed' &&
                      'Event confirmed - notify all stakeholders'}
                    {field.value === 'In_Progress' &&
                      'Event is currently ongoing'}
                    {field.value === 'Completed' &&
                      'Event has been completed successfully'}
                    {field.value === 'Cancelled' &&
                      'Event has been cancelled - notify all parties'}
                    {field.value === 'No_details' &&
                      'Requires additional information'}
                  </p>
                )}
              </div>
            )}
          />

          <ValidatedField
            name="imageUrl"
            label="Supporting Image/Document URL"
            disabled={isSubmitting}
            placeholder="https://example.com/image.jpg"
            showRequiredIndicator={false}
          />
        </div>
      </div>

      {/* Help Section for New Users */}
      {userRole === 'user' && !isBasicInfoComplete && (
        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription>
            <div className="space-y-2">
              <div className="font-medium">Getting Started</div>
              <ul className="text-sm space-y-1">
                <li>• Fill in the event name and location first</li>
                <li>• Set accurate start and end times for proper planning</li>
                <li>• Choose the appropriate status based on planning phase</li>
                <li>• Optional fields can be completed later if needed</li>
              </ul>
            </div>
          </AlertDescription>
        </Alert>
      )}
    </section>
  );
};

export default DelegationBasicInfoSection;
