/**
 * @file Layout component for the reliability monitoring section.
 * This layout provides consistent structure and styling for all reliability pages.
 * @module app/reliability/layout
 */

import { Metadata } from 'next';
import React from 'react';

/**
 * Metadata for the reliability section
 */
export const metadata: Metadata = {
  title: {
    template: '%s | Reliability | WorkHub',
    default: 'Reliability | WorkHub',
  },
  description: 'System reliability monitoring and management for WorkHub',
};

/**
 * Props for the ReliabilityLayout component
 */
interface ReliabilityLayoutProps {
  children: React.ReactNode;
}

/**
 * Layout component for the reliability monitoring section.
 * 
 * This layout provides:
 * - Consistent page structure for reliability pages
 * - Proper spacing and container management
 * - SEO optimization with metadata
 * - Responsive design foundation
 * 
 * @param props - Layout props containing children
 * @returns JSX element representing the reliability layout
 */
export default function ReliabilityLayout({ children }: ReliabilityLayoutProps) {
  return (
    <div className="min-h-screen bg-background">
      <main className="flex-1">
        {children}
      </main>
    </div>
  );
}
