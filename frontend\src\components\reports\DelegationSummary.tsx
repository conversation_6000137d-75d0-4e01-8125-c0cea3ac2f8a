'use client';

import React from 'react';

import type { Delegation, DelegationStatusPrisma } from '@/lib/types/domain';

import { Card, CardContent } from '@/components/ui/card';
import { cn } from '@/lib/utils';

// Status options for dynamic rendering
const STATUS_OPTIONS: DelegationStatusPrisma[] = [
  'Planned',
  'Confirmed',
  'In_Progress',
  'Completed',
  'Cancelled',
  'No_details',
];

// Enhanced status color mapping for better visual hierarchy
const STATUS_COLORS: Record<
  DelegationStatusPrisma,
  { bg: string; border: string; text: string; }
> = {
  Cancelled: {
    bg: 'bg-gradient-to-br from-red-50 to-red-100',
    border: 'border-red-200',
    text: 'text-red-700',
  },
  Completed: {
    bg: 'bg-gradient-to-br from-purple-50 to-purple-100',
    border: 'border-purple-200',
    text: 'text-purple-700',
  },
  Confirmed: {
    bg: 'bg-gradient-to-br from-green-50 to-green-100',
    border: 'border-green-200',
    text: 'text-green-700',
  },
  In_Progress: {
    bg: 'bg-gradient-to-br from-yellow-50 to-yellow-100',
    border: 'border-yellow-200',
    text: 'text-yellow-700',
  },
  No_details: {
    bg: 'bg-gradient-to-br from-gray-50 to-gray-100',
    border: 'border-gray-200',
    text: 'text-gray-700',
  },
  Planned: {
    bg: 'bg-gradient-to-br from-blue-50 to-blue-100',
    border: 'border-blue-200',
    text: 'text-blue-700',
  },
};

// Format status for display (replace underscores with spaces)
const formatStatus = (status: DelegationStatusPrisma): string => {
  return status.replace('_', ' ');
};

/**
 * Props for the DelegationSummary component
 */
interface DelegationSummaryProps {
  /** Additional CSS class names */
  className?: string;
  /** Array of delegations to display summary statistics for */
  delegations: Delegation[];
}

/**
 * Props for the SummaryCard component
 */
interface SummaryCardProps {
  /** Additional CSS class for the card background */
  className?: string;
  /** Whether to use compact layout */
  compact?: boolean;
  /** Label to display below the value */
  label: string;
  /** CSS class for the label text color */
  textColor?: string;
  /** Value to display (typically a number) */
  value: number;
  /** CSS class for the value text color */
  valueColor?: string;
}

/**
 * A component that displays summary statistics for a list of delegations
 *
 * @example
 * ```tsx
 * <DelegationSummary delegations={filteredDelegations} />
 * ```
 */
export function DelegationSummary({
  className,
  delegations,
}: DelegationSummaryProps) {
  // Calculate statistics
  const total = delegations.length;

  // Count delegations by status
  const statusCounts = delegations.reduce(
    (acc, delegation) => {
      const status = delegation.status;
      acc[status] = (acc[status] || 0) + 1;
      return acc;
    },
    {} as Record<DelegationStatusPrisma, number>
  );

  // Calculate total delegates
  const totalDelegates = delegations.reduce((acc, delegation) => {
    return acc + (delegation.delegates?.length || 0);
  }, 0);

  // Get list of statuses that have at least one delegation, sorted by count
  const activeStatuses = Object.entries(statusCounts)
    .sort(([, a], [, b]) => b - a)
    .map(([status]) => status as DelegationStatusPrisma);

  return (
    <div className={cn('mt-6 mb-8', className)}>
      {/* Main summary grid */}
      <div className="mb-6 grid grid-cols-2 gap-4 lg:grid-cols-4">
        {/* Total Delegations Card */}
        <SummaryCard
          className="border-slate-200 bg-gradient-to-br from-slate-50 to-slate-100 shadow-sm transition-shadow hover:shadow-md"
          label="Total Delegations"
          textColor="text-slate-700"
          value={total}
          valueColor="text-slate-800"
        />

        {/* Total Delegates Card */}
        <SummaryCard
          className="border-indigo-200 bg-gradient-to-br from-indigo-50 to-indigo-100 shadow-sm transition-shadow hover:shadow-md"
          label="Total Delegates"
          textColor="text-indigo-700"
          value={totalDelegates}
          valueColor="text-indigo-800"
        />

        {/* Top 2 status cards */}
        {activeStatuses.slice(0, 2).map(status => {
          const statusConfig = STATUS_COLORS[status];
          return (
            <SummaryCard
              className={cn(
                statusConfig.bg,
                statusConfig.border,
                'shadow-sm hover:shadow-md transition-shadow'
              )}
              key={status}
              label={formatStatus(status)}
              textColor={statusConfig.text}
              value={statusCounts[status]}
              valueColor={statusConfig.text}
            />
          );
        })}
      </div>

      {/* Additional status breakdown if there are more than 2 active statuses */}
      {activeStatuses.length > 2 && (
        <div className="grid grid-cols-2 gap-3 sm:grid-cols-3 lg:grid-cols-6">
          {activeStatuses.slice(2).map(status => {
            const statusConfig = STATUS_COLORS[status];
            return (
              <SummaryCard
                className={cn(
                  statusConfig.bg,
                  statusConfig.border,
                  'shadow-sm hover:shadow-md transition-shadow'
                )}
                compact={true}
                key={status}
                label={formatStatus(status)}
                textColor={statusConfig.text}
                value={statusCounts[status]}
                valueColor={statusConfig.text}
              />
            );
          })}
        </div>
      )}
    </div>
  );
}

/**
 * A card component for displaying a summary statistic
 */
function SummaryCard({
  className,
  compact = false,
  label,
  textColor = 'text-gray-600',
  value,
  valueColor = 'text-gray-800',
}: SummaryCardProps) {
  return (
    <Card
      className={cn(
        'overflow-hidden border transition-all duration-200',
        className
      )}
    >
      <CardContent className={cn('text-center', compact ? 'p-3' : 'p-4')}>
        <div
          className={cn(
            'font-bold',
            compact ? 'text-xl mb-1' : 'text-3xl mb-2',
            valueColor
          )}
        >
          {value.toLocaleString()}
        </div>
        <div
          className={cn(
            'font-medium',
            compact ? 'text-xs' : 'text-sm',
            textColor
          )}
        >
          {label}
        </div>
      </CardContent>
    </Card>
  );
}
