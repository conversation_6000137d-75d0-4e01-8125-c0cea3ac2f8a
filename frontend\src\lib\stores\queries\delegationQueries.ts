/**
 * @file Delegation query configurations following Single Responsibility Principle
 * @description Centralized query configurations for delegation-related data fetching
 */

import type { UseQueryOptions } from '@tanstack/react-query';

import type { Delegation } from '../../types/domain';

import {
  delegationApiService,
  employeeApiService,
  vehicleApiService,
} from '../../api/services/apiServiceFactory'; // Use centralized services
import { DelegationTransformer } from '../../transformers/delegationTransformer';

/**
 * Query keys for delegation-related queries
 */
export const delegationQueryKeys = {
  all: ['delegations'] as const,
  detail: (id: string) => ['delegations', id] as const,
  withAssignments: (id: string) =>
    ['delegations', id, 'with-assignments'] as const,
};

/**
 * Creates query configuration for fetching a single delegation
 */
export const createDelegationQuery = (id: string) => ({
  enabled: !!id,
  queryFn: () => delegationApiService.getById(id),
  queryKey: delegationQueryKeys.detail(id),
  staleTime: 5 * 60 * 1000, // 5 minutes
});

/**
 * Creates query configuration for fetching all employees
 */
export const createEmployeesQuery = () => ({
  queryFn: () => employeeApiService.getAll(),
  queryKey: ['employees'] as const,
  staleTime: 10 * 60 * 1000, // 10 minutes - employees change less frequently
});

/**
 * Creates query configuration for fetching all vehicles
 */
export const createVehiclesQuery = () => ({
  queryFn: () => vehicleApiService.getAll(),
  queryKey: ['vehicles'] as const,
  staleTime: 10 * 60 * 1000, // 10 minutes - vehicles change less frequently
});

/**
 * Creates parallel query configurations for delegation with assignments
 */
export const createDelegationWithAssignmentsQueries = (id: string) => [
  createDelegationQuery(id),
  createEmployeesQuery(),
  createVehiclesQuery(),
];

/**
 * Standard query options for delegation queries
 */
export const delegationQueryOptions: Partial<
  UseQueryOptions<Delegation, Error>
> = {
  gcTime: 10 * 60 * 1000, // 10 minutes garbage collection time
  retry: 3,
  retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30_000),
  staleTime: 5 * 60 * 1000,
};
