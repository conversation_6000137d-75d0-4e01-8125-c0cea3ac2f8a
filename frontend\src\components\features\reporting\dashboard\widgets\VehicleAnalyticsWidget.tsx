/**
 * @file VehicleAnalyticsWidget.tsx
 * @description Vehicle analytics widget following existing widget patterns and SOLID principles
 */

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Car,
  TrendingUp,
  AlertTriangle,
  Clock,
  DollarSign,
  Wrench,
  Download,
  MoreHorizontal,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useVehicleAnalytics } from '../../hooks/useVehicleAnalytics';
import { DataLoader } from '@/components/ui/data-loader';
import { ErrorDisplay } from '@/components/ui/error-display';
import type { ReportingFilters } from '../../data/types/reporting';

/**
 * Props interface for VehicleAnalyticsWidget
 */
interface VehicleAnalyticsWidgetProps {
  filters?: ReportingFilters;
  className?: string;
  showExportOptions?: boolean;
  compact?: boolean;
}

/**
 * Metric card component for displaying individual metrics
 */
interface MetricCardProps {
  label: string;
  value: string | number;
  icon: React.ReactNode;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  variant?: 'default' | 'warning' | 'success' | 'destructive';
}

const MetricCard: React.FC<MetricCardProps> = ({
  label,
  value,
  icon,
  trend,
  variant = 'default',
}) => {
  const variantStyles = {
    default: 'border-gray-200',
    warning: 'border-orange-200 bg-orange-50',
    success: 'border-green-200 bg-green-50',
    destructive: 'border-red-200 bg-red-50',
  };

  return (
    <div className={cn('p-4 border rounded-lg', variantStyles[variant])}>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          {icon}
          <span className="text-sm font-medium text-gray-600">{label}</span>
        </div>
        {trend && (
          <Badge
            variant={trend.isPositive ? 'default' : 'destructive'}
            className="text-xs"
          >
            {trend.isPositive ? '+' : ''}
            {trend.value}%
          </Badge>
        )}
      </div>
      <div className="mt-2">
        <span className="text-2xl font-bold">{value}</span>
      </div>
    </div>
  );
};

/**
 * VehicleAnalyticsWidget Component
 *
 * Displays comprehensive vehicle analytics following existing widget patterns.
 *
 * Responsibilities:
 * - Display vehicle analytics in widget format
 * - Integrate with existing dashboard layout
 * - Follow established widget composition patterns
 * - Maintain consistent styling and behavior
 *
 * SOLID Principles Applied:
 * - SRP: Single responsibility of displaying vehicle metrics
 * - OCP: Open for extension via props configuration
 * - DIP: Depends on existing widget framework abstractions
 */
export const VehicleAnalyticsWidget: React.FC<VehicleAnalyticsWidgetProps> = ({
  filters,
  className = '',
  showExportOptions = true,
  compact = false,
}) => {
  // Use existing hook patterns
  const {
    data: vehicleAnalytics,
    isLoading,
    error,
  } = useVehicleAnalytics(filters);

  // Handle loading state
  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Car className="h-5 w-5" />
            Vehicle Analytics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <DataLoader isLoading={true} data={null} error={null}>
            {() => null}
          </DataLoader>
        </CardContent>
      </Card>
    );
  }

  // Handle error state
  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Car className="h-5 w-5" />
            Vehicle Analytics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ErrorDisplay error={error} />
        </CardContent>
      </Card>
    );
  }

  // Calculate derived metrics
  const totalVehicles = vehicleAnalytics?.totalCount || 0;
  const activeVehicles =
    vehicleAnalytics?.utilizationMetrics?.filter(v => v.utilizationRate > 0)
      .length || 0;
  const utilizationRate =
    totalVehicles > 0 ? (activeVehicles / totalVehicles) * 100 : 0;
  const totalCost = vehicleAnalytics?.costAnalysis?.totalCost || 0;
  const avgCostPerService =
    vehicleAnalytics?.costAnalysis?.averageCostPerService || 0;

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Car className="h-5 w-5" />
            Vehicle Analytics
          </CardTitle>
          <div className="flex items-center gap-2">
            <Badge variant="secondary" className="text-xs">
              {totalVehicles} vehicles
            </Badge>
            {showExportOptions && (
              <Button variant="ghost" size="sm">
                <Download className="h-4 w-4" />
              </Button>
            )}
            <Button variant="ghost" size="sm">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Key Metrics Grid - Following existing patterns */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <MetricCard
            label="Total Vehicles"
            value={totalVehicles}
            icon={<Car className="h-4 w-4" />}
          />
          <MetricCard
            label="Utilization Rate"
            value={`${Math.round(utilizationRate)}%`}
            icon={<TrendingUp className="h-4 w-4" />}
            variant={utilizationRate < 70 ? 'warning' : 'success'}
          />
          <MetricCard
            label="Total Service Cost"
            value={`$${totalCost.toLocaleString()}`}
            icon={<DollarSign className="h-4 w-4" />}
          />
          <MetricCard
            label="Avg. Cost/Service"
            value={`$${Math.round(avgCostPerService)}`}
            icon={<Wrench className="h-4 w-4" />}
          />
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="p-4 bg-blue-50 rounded-lg">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-blue-700">
                Active Vehicles
              </span>
              <Car className="h-4 w-4 text-blue-600" />
            </div>
            <span className="text-xl font-bold text-blue-900">
              {activeVehicles}
            </span>
          </div>

          <div className="p-4 bg-green-50 rounded-lg">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-green-700">
                Maintenance Due
              </span>
              <AlertTriangle className="h-4 w-4 text-green-600" />
            </div>
            <span className="text-xl font-bold text-green-900">
              {vehicleAnalytics?.maintenanceSchedule?.filter(
                m => new Date(m.nextMaintenanceDate) <= new Date()
              ).length || 0}
            </span>
          </div>

          <div className="p-4 bg-orange-50 rounded-lg">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-orange-700">
                Avg. Service Time
              </span>
              <Clock className="h-4 w-4 text-orange-600" />
            </div>
            <span className="text-xl font-bold text-orange-900">
              {vehicleAnalytics?.serviceHistory &&
              vehicleAnalytics.serviceHistory.length > 0
                ? Math.round(
                    vehicleAnalytics.serviceHistory.reduce(
                      (acc, service) => acc + (service.cost || 0),
                      0
                    ) / vehicleAnalytics.serviceHistory.length
                  )
                : 0}
              h
            </span>
          </div>
        </div>

        {!compact && (
          <div className="pt-4 border-t">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">
                Last updated: {new Date().toLocaleTimeString()}
              </span>
              <Button variant="outline" size="sm">
                View Details
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default VehicleAnalyticsWidget;
