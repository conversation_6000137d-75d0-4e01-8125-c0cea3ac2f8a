/**
 * @file Delegation enrichment transformer following established patterns
 * @description Handles the enrichment of delegation data with employee and vehicle details
 * @module transformers/delegationEnrichment
 */

import type {
  Delegation,
  DelegationDriver,
  DelegationEscort,
  DelegationVehicleAssignment,
  Employee,
  Vehicle,
} from '../types/domain';

/**
 * Transformer class for enriching delegation data with related entities
 * Follows the same pattern as other transformers in the codebase
 */
export class DelegationEnrichmentTransformer {
  /**
   * Main enrichment method that combines delegation data with employee and vehicle details
   * @param delegation - Base delegation data
   * @param employees - Array of employees
   * @param vehicles - Array of vehicles
   * @returns Fully enriched delegation
   */
  static enrich(
    delegation: Delegation,
    employees: Employee[],
    vehicles: Vehicle[]
  ): Delegation {
    const { employeeMap, vehicleMap } = this.createLookupMaps(
      employees,
      vehicles
    );

    return {
      ...delegation,
      drivers: this.enrichDrivers(delegation.drivers, employeeMap) ?? [],
      escorts: this.enrichEscorts(delegation.escorts, employeeMap) ?? [],
      vehicles: this.enrichVehicles(delegation.vehicles, vehicleMap) ?? [],
    };
  }

  /**
   * Creates optimized lookup maps for O(1) performance
   * @param employees - Array of employees
   * @param vehicles - Array of vehicles
   * @returns Object containing employee and vehicle maps
   */
  private static createLookupMaps(employees: Employee[], vehicles: Vehicle[]) {
    return {
      employeeMap: new Map(employees.map(emp => [emp.id, emp])),
      vehicleMap: new Map(vehicles.map(veh => [veh.id, veh])),
    };
  }

  /**
   * Enriches driver assignments with employee details
   * @param drivers - Array of driver assignments
   * @param employeeMap - Map of employees for O(1) lookup
   * @returns Enriched driver assignments
   */
  private static enrichDrivers(
    drivers: DelegationDriver[] | undefined,
    employeeMap: Map<number, Employee>
  ): DelegationDriver[] | undefined {
    return drivers?.map(driver => {
      const employee =
        driver.employee || employeeMap.get(Number(driver.employeeId));
      return {
        ...driver,
        ...(employee && { employee }),
      };
    });
  }

  /**
   * Enriches escort assignments with employee details
   * @param escorts - Array of escort assignments
   * @param employeeMap - Map of employees for O(1) lookup
   * @returns Enriched escort assignments
   */
  private static enrichEscorts(
    escorts: DelegationEscort[] | undefined,
    employeeMap: Map<number, Employee>
  ): DelegationEscort[] | undefined {
    return escorts?.map(escort => {
      const employee =
        escort.employee || employeeMap.get(Number(escort.employeeId));
      return {
        ...escort,
        ...(employee && { employee }),
      };
    });
  }

  /**
   * Enriches vehicle assignments with vehicle details
   * @param vehicles - Array of vehicle assignments
   * @param vehicleMap - Map of vehicles for O(1) lookup
   * @returns Enriched vehicle assignments
   */
  private static enrichVehicles(
    vehicles: DelegationVehicleAssignment[] | undefined,
    vehicleMap: Map<number, Vehicle>
  ): DelegationVehicleAssignment[] | undefined {
    return vehicles?.map(vehicleAssignment => {
      const vehicle =
        vehicleAssignment.vehicle ||
        vehicleMap.get(vehicleAssignment.vehicleId);
      return {
        ...vehicleAssignment,
        ...(vehicle && { vehicle }),
      };
    });
  }
}

// Export the main enrichment function for backward compatibility
export const enrichDelegation = (
  delegation: Delegation,
  employees: Employee[],
  vehicles: Vehicle[]
): Delegation => {
  return DelegationEnrichmentTransformer.enrich(
    delegation,
    employees,
    vehicles
  );
};
