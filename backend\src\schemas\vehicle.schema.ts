import { z } from 'zod';

const vehicleBaseSchema = {
  color: z.string().optional().nullable(),

  imageUrl: z.string().url('Invalid image URL').optional().nullable().or(z.literal('')),

  initialOdometer: z.number().int().min(0, 'Odometer cannot be negative').optional().nullable(),

  licensePlate: z
    .string({
      invalid_type_error: 'License plate must be a string',
      required_error: 'License plate is required',
    })
    .min(1, 'License plate cannot be empty'),

  make: z
    .string({
      invalid_type_error: 'Vehicle make must be a string',
      required_error: 'Vehicle make is required',
    })
    .min(1, 'Make cannot be empty'),

  model: z
    .string({
      invalid_type_error: 'Vehicle model must be a string',
      required_error: 'Vehicle model is required',
    })
    .min(1, 'Model cannot be empty'),

  ownerContact: z
    .string({
      invalid_type_error: 'Owner contact must be a string',
      required_error: 'Owner contact is required',
    })
    .min(1, 'Owner contact cannot be empty')
    .refine(val => {
      const phoneRegex = /^[\d\s\+\-\(\)]{7,20}$/;
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return phoneRegex.test(val) || emailRegex.test(val);
    }, 'Owner contact must be a valid phone number or email address'),

  ownerName: z
    .string({
      invalid_type_error: 'Owner name must be a string',
      required_error: 'Owner name is required',
    })
    .min(1, 'Owner name cannot be empty'),
  vin: z
    .string({
      invalid_type_error: 'VIN must be a string',
      required_error: 'VIN is required',
    })
    .min(1, 'VIN cannot be empty')
    .regex(/^[A-HJ-NPR-Z0-9]{17}$/, 'VIN must be a valid 17-character VIN format'),
  year: z
    .number({
      invalid_type_error: 'Vehicle year must be a number',
      required_error: 'Vehicle year is required',
    })
    .int('Year must be an integer')
    .min(1900, 'Year must be at least 1900')
    .max(
      new Date().getFullYear() + 1,
      `Year cannot be greater than ${new Date().getFullYear() + 1}`,
    ),
};

export const vehicleCreateSchema = z.object(vehicleBaseSchema);

export const vehicleUpdateSchema = z.object({
  ...Object.entries(vehicleBaseSchema).reduce(
    (acc, [key, validator]) => ({
      ...acc,
      [key]: validator.optional(),
    }),
    {},
  ),
});

export const vehicleIdSchema = z.object({
  id: z
    .string()
    .refine(val => !isNaN(parseInt(val, 10)), {
      message: 'ID must be a valid number',
    })
    .transform(val => parseInt(val, 10)),
});

export type VehicleCreate = z.infer<typeof vehicleCreateSchema>;
export type VehicleIdParam = z.infer<typeof vehicleIdSchema>;
export type VehicleUpdate = z.infer<typeof vehicleUpdateSchema>;
