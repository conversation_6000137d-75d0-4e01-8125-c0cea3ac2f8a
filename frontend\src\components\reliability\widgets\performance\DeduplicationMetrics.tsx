/**
 * @file Deduplication metrics widget component for cache efficiency monitoring.
 * This component provides comprehensive request deduplication monitoring with
 * cache hit rates, efficiency analysis, and performance optimization insights.
 * @module components/reliability/widgets/performance/DeduplicationMetrics
 */

'use client';

import {
  Archive,
  Database,
  Layers,
  Pie<PERSON>hart,
  TrendingUp,
  Zap,
} from 'lucide-react';
import React from 'react';
import {
  Area,
  AreaChart,
  Cell,
  Pie,
  PieChart as RechartsPie<PERSON>hart,
  ResponsiveContainer,
  XAxis,
  YAxis,
} from 'recharts';

import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from '@/components/ui/chart';
import { Progress } from '@/components/ui/progress';
import { Skeleton } from '@/components/ui/skeleton';
import { usePerformanceMetrics } from '@/lib/stores/queries/useReliability';
import { cn } from '@/lib/utils';

/**
 * Props for the DeduplicationMetrics component
 */
export interface DeduplicationMetricsProps {
  /** Optional CSS class name for styling */
  className?: string;
  /** Compact mode for smaller displays */
  compact?: boolean;
  /** Whether to show detailed charts */
  showCharts?: boolean;
  /** Whether to show optimization recommendations */
  showOptimizations?: boolean;
}

/**
 * Cache efficiency metric interface
 */
interface CacheEfficiencyMetric {
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  id: string;
  label: string;
  optimization?: string | undefined;
  status: 'excellent' | 'good' | 'poor' | 'warning';
  unit: string;
  value: number;
}

/**
 * Generate cache trend data for chart visualization
 * Uses stable data points based on current hit rate without random variation
 */
const generateCacheTrendData = (hitRate: number) => {
  const data = [];
  const baseHitRate = hitRate;

  for (let i = 11; i >= 0; i--) {
    const time = new Date(Date.now() - i * 5 * 60 * 1000);
    // Use stable variation based on time pattern instead of random
    const timeBasedVariation = Math.sin((i / 12) * Math.PI * 2) * 3; // ±3% variation
    const currentHitRate = Math.max(
      0,
      Math.min(100, baseHitRate + timeBasedVariation)
    );
    const missRate = 100 - currentHitRate;

    data.push({
      hits: currentHitRate,
      misses: missRate,
      time: time.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
      }),
      timestamp: time.getTime(),
    });
  }

  return data;
};

/**
 * Generate cache distribution data for pie chart
 */
const generateCacheDistribution = (hitRate: number) => {
  const hits = hitRate;
  const misses = 100 - hitRate;

  return [
    { color: '#10b981', name: 'Cache Hits', value: hits },
    { color: '#ef4444', name: 'Cache Misses', value: misses },
  ];
};

/**
 * Get efficiency status based on hit rate
 */
const getEfficiencyStatus = (
  hitRate: number
): 'excellent' | 'good' | 'poor' | 'warning' => {
  if (hitRate >= 90) return 'excellent';
  if (hitRate >= 75) return 'good';
  if (hitRate >= 50) return 'warning';
  return 'poor';
};

/**
 * Get status color classes
 */
const getStatusColor = (
  status: 'excellent' | 'good' | 'poor' | 'warning'
): string => {
  switch (status) {
    case 'excellent': {
      return 'text-green-600 bg-green-50 border-green-200';
    }
    case 'good': {
      return 'text-blue-600 bg-blue-50 border-blue-200';
    }
    case 'poor': {
      return 'text-red-600 bg-red-50 border-red-200';
    }
    case 'warning': {
      return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    }
    default: {
      return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  }
};

/**
 * Format bytes to human readable format
 */
const formatBytes = (bytes: number): string => {
  if (bytes === 0) return '0 B';

  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return `${Number.parseFloat((bytes / Math.pow(k, i)).toFixed(1))} ${sizes[i]}`;
};

/**
 * Deduplication metrics widget component.
 *
 * This component provides:
 * - Request deduplication efficiency monitoring
 * - Cache hit/miss rate analysis and trends
 * - Memory usage optimization for cache operations
 * - Performance impact analysis of deduplication
 * - Cache efficiency recommendations and insights
 * - Real-time deduplication performance tracking
 *
 * Features:
 * - Real-time cache efficiency monitoring
 * - Interactive charts with hit/miss rate trends
 * - Cache distribution visualization with pie charts
 * - Performance optimization recommendations
 * - Memory usage analysis for cache operations
 * - Responsive design with mobile-first approach
 * - Accessibility support with proper ARIA labels
 * - Loading states and comprehensive error handling
 *
 * @param props - Component props
 * @returns JSX element representing the deduplication metrics
 *
 * @example
 * ```tsx
 * <DeduplicationMetrics
 *   showCharts={true}
 *   showOptimizations={true}
 *   compact={false}
 * />
 * ```
 */
export const DeduplicationMetrics: React.FC<DeduplicationMetricsProps> = ({
  className = '',
  compact = false,
  showCharts = true,
  showOptimizations = true,
}) => {
  const { data: metrics, error, isLoading } = usePerformanceMetrics();

  // Process deduplication metrics
  const cacheMetrics: CacheEfficiencyMetric[] = React.useMemo(() => {
    if (!metrics?.deduplicationMetrics) return [];

    const dedup = metrics.deduplicationMetrics;
    const items: CacheEfficiencyMetric[] = [];

    // Cache Hit Rate
    const hitRate = dedup.hitRate || 0;
    const hitStatus = getEfficiencyStatus(hitRate);
    items.push({
      description: 'Percentage of requests served from cache',
      icon: ({ className }: { className?: string }) => (
        <Database className={className} />
      ),
      id: 'hit-rate',
      label: 'Cache Hit Rate',
      optimization:
        hitRate < 75
          ? 'Consider increasing cache TTL or improving cache keys'
          : undefined,
      status: hitStatus,
      unit: '%',
      value: hitRate,
    });

    // Cache Efficiency Score
    const efficiency = Math.min(100, hitRate * 1.2); // Boost score for visualization
    items.push({
      description: 'Overall cache performance and optimization',
      icon: ({ className }: { className?: string }) => (
        <Zap className={className} />
      ),
      id: 'efficiency',
      label: 'Cache Efficiency',
      optimization:
        efficiency < 80
          ? 'Optimize cache strategy and key patterns'
          : undefined,
      status: getEfficiencyStatus(efficiency),
      unit: '%',
      value: efficiency,
    });

    // Memory Savings (calculated from actual cache hits vs total requests)
    const memorySavings =
      dedup.cacheHits && dedup.totalRequests
        ? (dedup.cacheHits / dedup.totalRequests) * 100
        : 0;
    items.push({
      description: 'Memory saved through request deduplication',
      icon: ({ className }: { className?: string }) => (
        <Archive className={className} />
      ),
      id: 'memory-savings',
      label: 'Memory Savings',
      optimization:
        memorySavings < 50
          ? 'Review cache size and eviction policies'
          : undefined,
      status:
        memorySavings > 60
          ? 'excellent'
          : memorySavings > 40
            ? 'good'
            : memorySavings > 20
              ? 'warning'
              : 'poor',
      unit: '%',
      value: memorySavings,
    });

    // Request Deduplication Rate (based on actual cache performance)
    const dedupRate =
      dedup.totalRequests > 0
        ? ((dedup.totalRequests - dedup.cacheMisses) / dedup.totalRequests) *
          100
        : 0;
    items.push({
      description: 'Percentage of duplicate requests eliminated',
      icon: ({ className }: { className?: string }) => (
        <Layers className={className} />
      ),
      id: 'dedup-rate',
      label: 'Deduplication Rate',
      optimization:
        dedupRate < 70 ? 'Improve request fingerprinting algorithm' : undefined,
      status: getEfficiencyStatus(dedupRate),
      unit: '%',
      value: dedupRate,
    });

    return items;
  }, [metrics]);

  // Generate chart data
  const hitRate = metrics?.deduplicationMetrics?.hitRate || 0;
  const cacheTrendData = generateCacheTrendData(hitRate);
  const cacheDistribution = generateCacheDistribution(hitRate);

  // Chart configurations
  const chartConfig = {
    hits: {
      color: '#10b981',
      label: 'Cache Hits',
    },
    misses: {
      color: '#ef4444',
      label: 'Cache Misses',
    },
  };

  // Loading state
  if (isLoading) {
    return (
      <div className={cn('space-y-4', className)}>
        <div className="flex items-center justify-between">
          <Skeleton className="h-6 w-40" />
          <Skeleton className="h-5 w-24" />
        </div>
        <div className="grid grid-cols-1 gap-3 sm:grid-cols-2">
          {Array.from({ length: 4 }).map((_, index) => (
            <Skeleton className="h-24 w-full" key={index} />
          ))}
        </div>
        {showCharts && (
          <div className="grid grid-cols-1 gap-4 lg:grid-cols-2">
            <Skeleton className="h-64 w-full" />
            <Skeleton className="h-64 w-full" />
          </div>
        )}
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className={cn('text-center py-8', className)}>
        <Database className="mx-auto mb-4 size-12 text-red-500" />
        <p className="text-sm font-medium text-red-600">
          Failed to load deduplication metrics
        </p>
        <p className="mt-1 text-xs text-muted-foreground">
          {error.message || 'Unable to retrieve cache performance data'}
        </p>
      </div>
    );
  }

  return (
    <div className={cn('space-y-4', className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Database className="size-5 text-muted-foreground" />
          <h3 className="text-sm font-semibold">Cache & Deduplication</h3>
        </div>
        <Badge
          className={cn(
            'font-medium',
            getStatusColor(getEfficiencyStatus(hitRate))
          )}
          variant="outline"
        >
          {hitRate.toFixed(1)}% Hit Rate
        </Badge>
      </div>

      {/* Cache Metrics Grid */}
      <div
        className={cn(
          'grid gap-3',
          compact ? 'grid-cols-1' : 'grid-cols-1 sm:grid-cols-2'
        )}
      >
        {cacheMetrics.map(metric => {
          const IconComponent = metric.icon;
          const statusColor = getStatusColor(metric.status);

          return (
            <Card
              className={cn(
                'transition-all duration-200 hover:shadow-md',
                statusColor
              )}
              key={metric.id}
            >
              <CardContent className={cn('p-4', compact && 'p-3')}>
                <div className="mb-2 flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <IconComponent className="size-4" />
                    <span
                      className={cn(
                        'font-medium',
                        compact ? 'text-xs' : 'text-sm'
                      )}
                    >
                      {metric.label}
                    </span>
                  </div>
                  <Badge
                    className={cn(
                      'text-xs',
                      metric.status === 'excellent'
                        ? 'text-green-600'
                        : metric.status === 'good'
                          ? 'text-blue-600'
                          : metric.status === 'warning'
                            ? 'text-yellow-600'
                            : 'text-red-600'
                    )}
                    variant="outline"
                  >
                    {metric.status}
                  </Badge>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span
                      className={cn(
                        'font-semibold',
                        compact ? 'text-sm' : 'text-lg'
                      )}
                    >
                      {metric.value.toFixed(1)}
                      {metric.unit}
                    </span>
                  </div>

                  <Progress
                    className="h-2"
                    value={Math.min(metric.value, 100)}
                  />

                  {!compact && (
                    <p className="text-xs text-muted-foreground">
                      {metric.description}
                    </p>
                  )}

                  {showOptimizations && metric.optimization && !compact && (
                    <div className="mt-2 rounded border border-blue-200 bg-blue-50 p-2">
                      <p className="text-xs text-blue-800">
                        💡 {metric.optimization}
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Charts */}
      {showCharts && !compact && (
        <div className="grid grid-cols-1 gap-4 lg:grid-cols-2">
          {/* Cache Hit/Miss Trends */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="size-5" />
                Cache Performance Trends
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ChartContainer className="h-48" config={chartConfig}>
                <ResponsiveContainer height="100%" width="100%">
                  <AreaChart data={cacheTrendData}>
                    <XAxis
                      axisLine={false}
                      dataKey="time"
                      tick={{ fontSize: 12 }}
                      tickLine={false}
                    />
                    <YAxis
                      axisLine={false}
                      domain={[0, 100]}
                      tick={{ fontSize: 12 }}
                      tickLine={false}
                    />
                    <ChartTooltip content={<ChartTooltipContent />} />
                    <Area
                      dataKey="hits"
                      fill={chartConfig.hits.color}
                      fillOpacity={0.6}
                      stackId="1"
                      stroke={chartConfig.hits.color}
                      type="monotone"
                    />
                    <Area
                      dataKey="misses"
                      fill={chartConfig.misses.color}
                      fillOpacity={0.6}
                      stackId="1"
                      stroke={chartConfig.misses.color}
                      type="monotone"
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </ChartContainer>
            </CardContent>
          </Card>

          {/* Cache Distribution */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <PieChart className="size-5" />
                Cache Distribution
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ChartContainer className="h-48" config={chartConfig}>
                <ResponsiveContainer height="100%" width="100%">
                  <RechartsPieChart>
                    <Pie
                      cx="50%"
                      cy="50%"
                      data={cacheDistribution}
                      dataKey="value"
                      innerRadius={40}
                      outerRadius={80}
                      paddingAngle={5}
                    >
                      {cacheDistribution.map((entry, index) => (
                        <Cell fill={entry.color} key={`cell-${index}`} />
                      ))}
                    </Pie>
                    <ChartTooltip
                      content={({ active, payload }) => {
                        if (active && payload?.length && payload[0]?.payload) {
                          const data = payload[0].payload;
                          return (
                            <div className="rounded border bg-white p-2 shadow">
                              <p className="font-medium">{data.name}</p>
                              <p className="text-sm text-muted-foreground">
                                {data.value.toFixed(1)}%
                              </p>
                            </div>
                          );
                        }
                        return null;
                      }}
                    />
                  </RechartsPieChart>
                </ResponsiveContainer>
              </ChartContainer>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Performance Impact Summary */}
      {metrics && !compact && (
        <Card>
          <CardContent className="p-4">
            <div className="grid grid-cols-2 gap-4 text-xs sm:grid-cols-4">
              <div>
                <p className="text-muted-foreground">Total Requests:</p>
                <p className="font-medium">
                  {(
                    metrics.deduplicationMetrics?.totalRequests || 0
                  ).toLocaleString()}
                </p>
              </div>
              <div>
                <p className="text-muted-foreground">Cache Hits:</p>
                <p className="font-medium text-green-600">
                  {Math.round(
                    ((metrics.deduplicationMetrics?.totalRequests || 0) *
                      hitRate) /
                      100
                  ).toLocaleString()}
                </p>
              </div>
              <div>
                <p className="text-muted-foreground">Cache Misses:</p>
                <p className="font-medium text-red-600">
                  {Math.round(
                    ((metrics.deduplicationMetrics?.totalRequests || 0) *
                      (100 - hitRate)) /
                      100
                  ).toLocaleString()}
                </p>
              </div>
              <div>
                <p className="text-muted-foreground">Memory Saved:</p>
                <p className="font-medium text-blue-600">
                  {formatBytes(
                    (metrics.deduplicationMetrics?.totalRequests || 0) *
                      hitRate *
                      0.001 *
                      1024 *
                      1024
                  )}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

/**
 * Default export for the DeduplicationMetrics component
 */
export default DeduplicationMetrics;
