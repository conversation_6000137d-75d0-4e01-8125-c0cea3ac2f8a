/**
 * @file FlightDetailsCard component for displaying flight information
 * @module components/delegations/detail/FlightDetailsCard
 */

import React from 'react';
import {
  Plane,
  PlaneLanding,
  PlaneTakeoff,
  Clock,
  MapPin,
  Info,
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { DetailItem } from '../common/DetailItem';
import type { Delegation } from '@/lib/types/domain';
import { format, parseISO } from 'date-fns';

interface FlightDetailsCardProps {
  delegation: Delegation;
  className?: string;
}

const formatFlightDate = (dateString: string | undefined) => {
  if (!dateString) return 'N/A';
  try {
    return format(parseISO(dateString), 'MMM d, yyyy, HH:mm');
  } catch {
    return 'Invalid Date';
  }
};

/**
 * FlightDetailsCard component for displaying flight information
 * Shows arrival and departure flight details in a professional layout
 */
export function FlightDetailsCard({
  delegation,
  className,
}: FlightDetailsCardProps) {
  // Flight data is now correctly loaded via fixed transformer

  const hasFlights = delegation.arrivalFlight || delegation.departureFlight;

  if (!hasFlights) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Plane className="h-5 w-5 text-blue-600 dark:text-blue-400" />
            <span>Flight Information</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center justify-center py-8 text-center">
            <div className="mx-auto mb-4 h-12 w-12 rounded-full bg-blue-50 dark:bg-blue-900/30 flex items-center justify-center">
              <Plane className="h-6 w-6 text-blue-600 dark:text-blue-400" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              No Flight Details
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              No flight information has been provided for this delegation.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Plane className="h-5 w-5 text-blue-600 dark:text-blue-400" />
            <span>Flight Information</span>
          </div>
          <Badge
            variant="secondary"
            className="bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900/30 dark:text-blue-300 dark:border-blue-800"
          >
            {
              [delegation.arrivalFlight, delegation.departureFlight].filter(
                Boolean
              ).length
            }{' '}
            flight
            {[delegation.arrivalFlight, delegation.departureFlight].filter(
              Boolean
            ).length !== 1
              ? 's'
              : ''}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {delegation.arrivalFlight && (
          <div className="rounded-lg border border-green-200 bg-green-50 p-4 dark:border-green-800 dark:bg-green-900/20">
            <div className="flex items-center space-x-2 mb-4">
              <PlaneLanding className="h-5 w-5 text-green-600 dark:text-green-400" />
              <h3 className="text-lg font-semibold text-green-900 dark:text-green-100">
                Arrival Flight
              </h3>
            </div>
            <div className="grid gap-3 md:grid-cols-2">
              <DetailItem
                icon={Plane}
                label="Flight Number"
                value={delegation.arrivalFlight.flightNumber}
              />
              <DetailItem
                icon={Clock}
                label="Date & Time"
                value={formatFlightDate(delegation.arrivalFlight.dateTime)}
              />
              <DetailItem
                icon={MapPin}
                label="Airport"
                value={delegation.arrivalFlight.airport}
              />
              {delegation.arrivalFlight.terminal && (
                <DetailItem
                  icon={MapPin}
                  label="Terminal"
                  value={delegation.arrivalFlight.terminal}
                />
              )}
            </div>
            {delegation.arrivalFlight.notes && (
              <>
                <Separator className="my-3" />
                <DetailItem
                  icon={Info}
                  label="Notes"
                  value={delegation.arrivalFlight.notes}
                  valueClassName="whitespace-pre-wrap"
                />
              </>
            )}
          </div>
        )}

        {delegation.departureFlight && (
          <div className="rounded-lg border border-orange-200 bg-orange-50 p-4 dark:border-orange-800 dark:bg-orange-900/20">
            <div className="flex items-center space-x-2 mb-4">
              <PlaneTakeoff className="h-5 w-5 text-orange-600 dark:text-orange-400" />
              <h3 className="text-lg font-semibold text-orange-900 dark:text-orange-100">
                Departure Flight
              </h3>
            </div>
            <div className="grid gap-3 md:grid-cols-2">
              <DetailItem
                icon={Plane}
                label="Flight Number"
                value={delegation.departureFlight.flightNumber}
              />
              <DetailItem
                icon={Clock}
                label="Date & Time"
                value={formatFlightDate(delegation.departureFlight.dateTime)}
              />
              <DetailItem
                icon={MapPin}
                label="Airport"
                value={delegation.departureFlight.airport}
              />
              {delegation.departureFlight.terminal && (
                <DetailItem
                  icon={MapPin}
                  label="Terminal"
                  value={delegation.departureFlight.terminal}
                />
              )}
            </div>
            {delegation.departureFlight.notes && (
              <>
                <Separator className="my-3" />
                <DetailItem
                  icon={Info}
                  label="Notes"
                  value={delegation.departureFlight.notes}
                  valueClassName="whitespace-pre-wrap"
                />
              </>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}

export default FlightDetailsCard;
