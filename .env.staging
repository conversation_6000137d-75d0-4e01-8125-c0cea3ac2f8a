# WorkHub Staging Environment Configuration
# Created: January 24, 2025
# Purpose: Staging deployment with 100% functional Hybrid RBAC system

# =============================================================================
# ENVIRONMENT CONFIGURATION
# =============================================================================
NODE_ENV=staging
PORT=3001

# =============================================================================
# SUPABASE CONFIGURATION (Production Database)
# =============================================================================
SUPABASE_URL=https://abylqjnpaegeqwktcukn.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFieWxxam5wYWVnZXF3a3RjdWtuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcyMTM0NTMsImV4cCI6MjA2Mjc4OTQ1M30.WCzj8fDu7vdxhvbOUuoQHVamy9-XYBr4vtTox52ap2o
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFieWxxam5wYWVnZXF3a3RjdWtuIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NzIxMzQ1MywiZXhwIjoyMDYyNzg5NDUzfQ.yLrGESZvVC6ISrqlcKeR3uvfRqdWPcZqYqLLZkjphU8

# Database URL for Prisma
DATABASE_URL=postgresql://postgres.abylqjnpaegeqwktcukn:<EMAIL>:5432/postgres

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# Strong JWT Secret (32+ characters)
JWT_SECRET=JJxeCe52j/9tIaQLW9guDu+pBpSRp//c4PXnj7mV/oyTUdwSBGIOfmpKEAGPY3hA3cBkcu2o2xbw/FiHIKtFUw==

# API Secret for internal services
API_SECRET=GENERATE_32_CHAR_SECRET_FOR_STAGING

# Session Secret (for Express sessions, if used)
SESSION_SECRET=aGVyZWFzZWNyZXRmb3JzZXNzaW9ucw==

# Encryption Key (for data encryption, if used)
ENCRYPTION_KEY=YW5vdGhlcnNlY3JldGZvcmVuY3J5cHRpb24=

# =============================================================================
# CORS CONFIGURATION
# =============================================================================
# Frontend URLs allowed for CORS (staging-specific)
FRONTEND_URL=http://localhost:3000,http://localhost:3001,https://your-staging-frontend.vercel.app

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
LOG_LEVEL=info

# =============================================================================
# FEATURE FLAGS
# =============================================================================
USE_SUPABASE=true

# =============================================================================
# STAGING-SPECIFIC CONFIGURATION
# =============================================================================
# Enable additional logging for staging
DEBUG_MODE=true

# Rate limiting (more permissive for testing)
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=200

# =============================================================================
# REDIS CONFIGURATION (for distributed rate limiting and request deduplication)
# =============================================================================
# Enable Redis for staging environment - Fixed for container connectivity
REDIS_URL=redis://redis:6379

# Redis Connection Configuration - Enhanced for Critical Issues Resolution
REDIS_CONNECTION_TIMEOUT=10000
REDIS_RETRY_ATTEMPTS=5
REDIS_CIRCUIT_BREAKER_THRESHOLD=5
REDIS_CIRCUIT_BREAKER_RESET_TIMEOUT=60000
REDIS_MAX_RECONNECT_ATTEMPTS=10

# Supabase Health Configuration - Enhanced for Critical Issues Resolution
SUPABASE_HEALTH_TIMEOUT=5000
SUPABASE_RETRY_ATTEMPTS=3
SUPABASE_CIRCUIT_BREAKER_THRESHOLD=3

# CORS Configuration - Enhanced for localhost:3001 support
CORS_ALLOW_LOCALHOST_3001=true
CORS_HEALTH_CHECK_ORIGINS=http://localhost:3001,http://127.0.0.1:3001

# Health Check Configuration - Enhanced with retry mechanisms
HEALTH_CHECK_TIMEOUT=5000
HEALTH_CHECK_RETRY_ATTEMPTS=3
HEALTH_CHECK_RETRY_DELAY=1000

# =============================================================================
# HEALTH CHECK CONFIGURATION (Enhanced with retry mechanisms and circuit breaker)
# =============================================================================
# Health check timeout in milliseconds
HEALTH_CHECK_TIMEOUT=5000

# Health check retry configuration
HEALTH_CHECK_RETRY_ATTEMPTS=3
HEALTH_CHECK_RETRY_DELAY=1000

# Circuit breaker configuration for health checks
CIRCUIT_BREAKER_FAILURE_THRESHOLD=5
CIRCUIT_BREAKER_RESET_TIMEOUT=60000

# =============================================================================
# REQUEST DEDUPLICATION CONFIGURATION - RELIABILITY ENHANCEMENT
# =============================================================================
REQUEST_DEDUP_ENABLED=true
REQUEST_DEDUP_DEFAULT_TTL=60

# Optimized Service-Specific Deduplication TTL Settings (in seconds)
ADMIN_DEDUP_TTL=120
API_DEDUP_TTL=30
PERFORMANCE_DEDUP_TTL=15
IDEMPOTENT_DEDUP_TTL=300

# =============================================================================
# NGINX IP ALLOWLIST CONFIGURATION (for reverse proxy support)
# =============================================================================
# NGINX_PROXY_IPS=10.0.0.0/8,**********/12,***********/16

# =============================================================================
# DEPLOYMENT METADATA
# =============================================================================
DEPLOYMENT_ENV=staging
DEPLOYMENT_VERSION=1.0.0-rbac
DEPLOYMENT_DATE=2025-01-24
SECURITY_LEVEL=HIGH
