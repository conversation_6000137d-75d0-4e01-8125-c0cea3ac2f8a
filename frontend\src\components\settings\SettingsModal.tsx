'use client';

import {
  <PERSON>,
  Layout,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>otateCc<PERSON>,
  <PERSON>ting<PERSON>,
  <PERSON>,
  Type,
  X,
} from 'lucide-react';
import { useTheme as useNextTheme } from 'next-themes';
import React from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';
import { useModal } from '@/lib/hooks/useModal';
import { useTheme } from '@/hooks/ui/useTheme';
import { useUiPreferences } from '@/hooks/ui/useUiPreferences';

import { FontSizeSettings } from './FontSizeSettings';
import { ThemeSettings } from './ThemeSettings';

/**
 * Settings Modal Component
 * Comprehensive settings interface using the modal system
 */
export const SettingsModal: React.FC = () => {
  const { closeModal, isModalOpen, modalContent } = useModal();
  const {
    dashboardLayout,
    fontSize,
    getAllPreferences,
    notificationsEnabled,
    resetPreferences,
    setDashboardLayout,
    setFontSize,
    setTableDensity,
    tableDensity,
    toggleNotifications,
  } = useUiPreferences();

  // Theme management
  const { currentTheme, setTheme: setZustandTheme } = useTheme();
  const {
    setTheme: setNextTheme,
    systemTheme,
    theme: nextTheme,
  } = useNextTheme();

  const isOpen = isModalOpen && modalContent === 'settings';

  // Enhanced theme switching that updates both systems
  const handleThemeChange = (newTheme: 'dark' | 'light' | 'system') => {
    setNextTheme(newTheme);
    if (newTheme === 'system') {
      // If system theme is selected, update Zustand with the actual system preference
      const systemPreference = systemTheme || 'light';
      setZustandTheme(systemPreference as 'dark' | 'light');
    } else {
      setZustandTheme(newTheme);
    }
  };

  const handleResetPreferences = () => {
    resetPreferences();
    // Reset theme to system default
    handleThemeChange('system');
    // You could add a toast notification here
  };

  return (
    <Dialog onOpenChange={open => !open && closeModal()} open={isOpen}>
      <DialogContent className="max-h-[90vh] max-w-4xl overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Settings className="size-5" />
            Application Settings
          </DialogTitle>
          <DialogDescription>
            Customize your WorkHub experience with these preferences
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Theme Settings */}
          <div>
            <h3 className="mb-4 flex items-center gap-2 text-lg font-semibold">
              <Palette className="size-5" />
              Theme Preferences
            </h3>
            <ThemeSettings />
          </div>

          <Separator />

          {/* Font Size Settings */}
          <div>
            <h3 className="mb-4 flex items-center gap-2 text-lg font-semibold">
              <Type className="size-5" />
              Display Preferences
            </h3>
            <FontSizeSettings />
          </div>

          <Separator />

          {/* Notification Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Bell className="size-5" />
                Notifications
              </CardTitle>
              <CardDescription>
                Control how you receive notifications and alerts
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium">Enable Notifications</p>
                  <p className="text-sm text-muted-foreground">
                    Receive system notifications and updates
                  </p>
                </div>
                <Switch
                  checked={notificationsEnabled}
                  onCheckedChange={toggleNotifications}
                />
              </div>
            </CardContent>
          </Card>

          {/* Layout Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Layout className="size-5" />
                Layout Preferences
              </CardTitle>
              <CardDescription>
                Customize the layout and density of interface elements
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Table Density */}
              <div>
                <h5 className="mb-3 font-medium">Table Density</h5>
                <div className="flex gap-2">
                  {(['compact', 'comfortable', 'spacious'] as const).map(
                    density => (
                      <Button
                        className="capitalize"
                        key={density}
                        onClick={() => setTableDensity(density)}
                        size="sm"
                        variant={
                          tableDensity === density ? 'default' : 'outline'
                        }
                      >
                        {density}
                      </Button>
                    )
                  )}
                </div>
              </div>

              {/* Dashboard Layout */}
              <div>
                <h5 className="mb-3 font-medium">Dashboard Layout</h5>
                <div className="flex gap-2">
                  {(['grid', 'list', 'cards'] as const).map(layout => (
                    <Button
                      className="capitalize"
                      key={layout}
                      onClick={() => setDashboardLayout(layout)}
                      size="sm"
                      variant={
                        dashboardLayout === layout ? 'default' : 'outline'
                      }
                    >
                      {layout}
                    </Button>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Current Settings Summary */}
          <Card>
            <CardHeader>
              <CardTitle>Current Settings Summary</CardTitle>
              <CardDescription>
                Overview of your current preferences
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4 md:grid-cols-3 lg:grid-cols-4">
                <div className="space-y-1">
                  <p className="text-sm font-medium">Theme</p>
                  <Badge className="capitalize" variant="secondary">
                    {nextTheme || 'system'}
                  </Badge>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium">Font Size</p>
                  <Badge className="capitalize" variant="secondary">
                    {fontSize}
                  </Badge>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium">Notifications</p>
                  <Badge
                    variant={notificationsEnabled ? 'default' : 'secondary'}
                  >
                    {notificationsEnabled ? 'Enabled' : 'Disabled'}
                  </Badge>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium">Table Density</p>
                  <Badge className="capitalize" variant="secondary">
                    {tableDensity}
                  </Badge>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium">Dashboard Layout</p>
                  <Badge className="capitalize" variant="secondary">
                    {dashboardLayout}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <div className="flex items-center justify-between border-t pt-4">
            <Button
              className="flex items-center gap-2"
              onClick={handleResetPreferences}
              variant="outline"
            >
              <RotateCcw className="size-4" />
              Reset to Defaults
            </Button>
            <div className="flex gap-2">
              <Button onClick={closeModal} variant="outline">
                Cancel
              </Button>
              <Button onClick={closeModal}>Save Changes</Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

/**
 * Settings Button Component
 * Trigger button to open the settings modal
 */
export const SettingsButton: React.FC<{
  variant?: 'default' | 'ghost' | 'outline';
}> = ({ variant = 'ghost' }) => {
  const { openSettingsModal } = useModal();

  return (
    <Button
      className="flex items-center gap-2"
      onClick={openSettingsModal}
      size="sm"
      variant={variant}
    >
      <Settings className="size-4" />
      Settings
    </Button>
  );
};
