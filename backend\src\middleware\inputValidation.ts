import type { NextFunction, Request, Response } from 'express';

import DOMPurify from 'dompurify';
import { J<PERSON><PERSON> } from 'jsdom';
import { z } from 'zod';

import { securityMonitoringService } from '../services/securityMonitoring.service.js';

/**
 * REFACTORED INPUT VALIDATION MIDDLEWARE - DRY PRINCIPLE COMPLIANT
 *
 * This refactored version eliminates code duplication and improves maintainability
 * by extracting common patterns into reusable functions.
 */

// Initialize DOMPurify with JSDOM for server-side usage
const window = new JSDOM('').window;
const purify = DOMPurify(window as any);

// Configure DOMPurify for strict sanitization
purify.setConfig({
  ALLOWED_ATTR: [],
  ALLOWED_TAGS: ['b', 'i', 'em', 'strong', 'p', 'br'],
  KEEP_CONTENT: true,
  RETURN_DOM: false,
  RETURN_DOM_FRAGMENT: false,
});

// Configuration constants
const HTML_FIELDS = [
  'description',
  'notes',
  'comments',
  'name',
  'title',
  'content',
  'message',
  'reason',
  'details',
];

const TEXT_ONLY_FIELDS = ['email', 'phone', 'license', 'vin', 'registration', 'code'];

const FIELD_LIMITS = {
  comments: 2000,
  default: 1000,
  description: 5000,
  email: 254,
  license: 50,
  name: 100,
  notes: 10000,
  phone: 20,
  registration: 20,
  title: 200,
  vin: 17,
};

interface SanitizedRequestData {
  body: any;
  params: any;
  query: any;
}

interface ValidationOptions {
  allowEmpty?: boolean;
  customValidator?: (value: any) => boolean;
  maxLength?: number;
  sanitizeHtml?: boolean;
}

// ============================================================================
// EXTRACTED UTILITY FUNCTIONS (DRY PRINCIPLE COMPLIANCE)
// ============================================================================

/**
 * Gets appropriate logger instance based on context
 */
const getLogger = (context?: {
  method?: string;
  path?: string;
  requestId?: string;
  service?: string;
}) => {
  const loggerModule = require('../utils/logger.js');

  if (context) {
    return loggerModule.createContextLogger(context);
  }

  return loggerModule.default;
};

/**
 * Sends standardized validation error response
 */
const sendValidationError = (res: Response, details: any[]): void => {
  res.status(400).json({
    code: 'VALIDATION_ERROR',
    details,
    error: 'Validation failed',
  });
};

/**
 * Sends standardized internal error response
 */
const sendInternalError = (res: Response): void => {
  res.status(500).json({
    code: 'INTERNAL_ERROR',
    error: 'Request processing failed',
  });
};

/**
 * Calculates total request size efficiently
 */
const calculateRequestSize = (req: Request): number => {
  const requestParts = [req.body || {}, req.params || {}, req.query || {}];

  return requestParts.reduce((total, part) => {
    return total + JSON.stringify(part).length;
  }, 0);
};

/**
 * Sanitizes all request data (body, params, query) consistently
 */
const sanitizeRequestData = (req: Request): SanitizedRequestData => {
  const bodyToSanitize = req.body || {};
  return {
    body: sanitizeObject(bodyToSanitize, 'body'),
    params: sanitizeObject(req.params, 'params'),
    query: sanitizeObject(req.query, 'query'),
  };
};

/**
 * Attaches validated data to request object consistently
 */
const attachValidatedData = (req: Request, sanitizedData: SanitizedRequestData): void => {
  req.validatedData = sanitizedData;
  // Only overwrite body as it's mutable in Express
  req.body = sanitizedData.body;
};

/**
 * Handles validation/sanitization errors consistently
 */
const handleValidationError = (
  error: unknown,
  req: Request,
  res: Response,
  context = 'validation',
): void => {
  const logger = getLogger({
    method: req.method,
    path: req.path,
    requestId: req.requestId,
    service: `input-${context}`,
  });

  logger.error(`Input ${context} failed`, {
    error: error instanceof Error ? error.message : String(error),
    stack: error instanceof Error ? error.stack : undefined,
  });

  sendInternalError(res);
};

/**
 * Handles primitive type sanitization
 */
const handlePrimitiveTypes = (obj: any, parentKey: string): any | null => {
  if (typeof obj === 'string') {
    return sanitizeValue(obj, parentKey);
  }

  if (typeof obj === 'number' || typeof obj === 'boolean') {
    return obj;
  }

  return null; // Not a primitive type
};

// ============================================================================
// CORE SANITIZATION FUNCTIONS
// ============================================================================

/**
 * Applies basic XSS prevention to a string
 */
const applyBasicXssPrevention = (input: string): string => {
  return input
    .replace(/javascript:/gi, '')
    .replace(/on\w+\s*=/gi, '')
    .replace(/data:text\/html/gi, '');
};

/**
 * Sanitizes a string value based on field type and options
 */
const sanitizeValue = (value: any, fieldName: string, options: ValidationOptions = {}): string => {
  if (typeof value !== 'string') {
    return String(value ?? '').trim();
  }

  let sanitized = value.trim();

  // Apply length limits
  const maxLength =
    options.maxLength ??
    FIELD_LIMITS[fieldName as keyof typeof FIELD_LIMITS] ??
    FIELD_LIMITS.default;

  if (sanitized.length > maxLength) {
    sanitized = sanitized.slice(0, maxLength);
  }

  // Apply HTML sanitization for specific fields
  if (HTML_FIELDS.includes(fieldName.toLowerCase()) || options.sanitizeHtml) {
    sanitized = purify.sanitize(sanitized);
  } else if (TEXT_ONLY_FIELDS.includes(fieldName.toLowerCase())) {
    sanitized = sanitized.replace(/<[^>]*>/g, '');
  }

  // Apply basic XSS prevention
  sanitized = applyBasicXssPrevention(sanitized);

  return sanitized;
};

/**
 * Recursively sanitizes an object's properties
 */
const sanitizeObject = (obj: any, parentKey = ''): any => {
  if (obj === null || obj === undefined) {
    return obj;
  }

  // Handle primitive types
  const primitiveResult = handlePrimitiveTypes(obj, parentKey);
  if (primitiveResult !== null) {
    return primitiveResult;
  }

  if (Array.isArray(obj)) {
    return obj.slice(0, 1000).map((item, index) => sanitizeObject(item, `${parentKey}[${index}]`));
  }

  if (typeof obj === 'object') {
    const sanitized: any = {};
    const entries = Object.entries(obj).slice(0, 100);

    for (const [key, value] of entries) {
      const sanitizedKey = sanitizeValue(key, 'key', { maxLength: 50 });
      const fullKey = parentKey ? `${parentKey}.${sanitizedKey}` : sanitizedKey;
      sanitized[sanitizedKey] = sanitizeObject(value, fullKey);
    }

    return sanitized;
  }

  return obj;
};

// ============================================================================
// MAIN MIDDLEWARE FUNCTIONS
// ============================================================================

/**
 * Validates request data against a Zod schema
 */
export const validateRequest = (schema: z.ZodSchema) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      // Sanitize all request data using extracted function
      const sanitizedData = sanitizeRequestData(req);

      // Validate with Zod schema
      const validatedData = schema.parse(sanitizedData);

      // Attach validated data using extracted function
      attachValidatedData(req, validatedData);

      next();
    } catch (error) {
      if (error instanceof z.ZodError) {
        const details = error.errors.map(err => ({
          code: err.code,
          field: err.path.join('.'),
          message: err.message,
        }));
        sendValidationError(res, details);
      } else {
        handleValidationError(error, req, res, 'validation');
      }
    }
  };
};

/**
 * Basic input sanitization middleware (without schema validation)
 */
export const sanitizeInput = (req: Request, res: Response, next: NextFunction): void => {
  try {
    // Sanitize all request data using extracted function
    const sanitizedData = sanitizeRequestData(req);

    // Attach sanitized data using extracted function
    attachValidatedData(req, sanitizedData);

    next();
  } catch (error) {
    handleValidationError(error, req, res, 'sanitization');
  }
};

/**
 * Rate limiting helper for input validation
 */
export const validateInputRate = (req: Request): boolean => {
  const totalSize = calculateRequestSize(req);
  return totalSize <= 1024 * 1024; // 1MB limit
};

/**
 * PHASE 2 SECURITY HARDENING: Data Protection Middleware
 * Monitors and protects sensitive data access
 */
export const dataProtectionMiddleware = (dataType: string) => {
  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = (req as any).userId || 'anonymous';
      const operation = req.method === 'GET' ? 'READ' : 'WRITE';

      // Monitor data access
      await securityMonitoringService.monitorDataAccess(userId, dataType, operation, {
        endpoint: req.path,
        method: req.method,
        userAgent: req.get('User-Agent'),
      });

      // Add data protection headers
      res.setHeader('X-Data-Protection', 'ENABLED');
      res.setHeader('X-Data-Classification', securityMonitoringService.classifyDataType(dataType));
      res.setHeader('X-Monitoring-Level', 'HIGH');

      next();
    } catch (error) {
      const logger = getLogger({
        method: req.method,
        path: req.path,
        requestId: req.requestId,
        service: 'data-protection',
      });

      logger.error('Data protection middleware error', {
        dataType,
        error: error instanceof Error ? error.message : String(error),
      });

      next(); // Continue processing even if monitoring fails
    }
  };
};

/**
 * Security headers for input validation responses
 */
export const addValidationSecurityHeaders = (res: Response): void => {
  res.setHeader('X-Input-Validation', 'PHASE-1-HARDENED');
  res.setHeader('X-Sanitization-Level', 'HIGH');
  res.setHeader('X-XSS-Protection', 'DOMPURIFY-ENABLED');
};

export default {
  addValidationSecurityHeaders,
  sanitizeInput,
  sanitizeObject,
  sanitizeValue,
  validateInputRate,
  validateRequest,
};
