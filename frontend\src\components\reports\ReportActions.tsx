'use client';

import {
  Download,
  FileSpreadsheet,
  FileText,
  Loader2,
  Printer,
} from 'lucide-react';
import React, { useState } from 'react';

import { ActionButton } from '@/components/ui/action-button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useFormToast } from '@/hooks/forms/useFormToast';
import { cn } from '@/lib/utils';
import {
  extractTableDataForCsv,
  generateCsvFromData,
} from '@/lib/utils/fileUtils';

export interface ReportActionsProps {
  /** Additional CSS class names */
  className?: string;
  /** Raw data for CSV export (alternative to tableId if CSV is enabled) */
  csvData?: {
    data: any[][];
    headers: string[];
  };
  /** Whether CSV export is available */
  enableCsv?: boolean;
  /** ID of the entity to generate the report for */
  entityId?: string;
  /** Base name for downloaded files (without extension) */
  fileName: string;
  /** ID or selector of the report content element for UI purposes */
  reportContentId: string;
  /** Type of report to download (e.g., 'vehicle', 'service', 'delegation') */
  reportType: string;
  /** ID or selector of the table element (for CSV export from table) */
  tableId?: string;
}

/**
 * Standardized report actions component for print and download functionality
 */
export function ReportActions({
  className,
  csvData,
  enableCsv = false,
  entityId,
  fileName,
  reportContentId,
  reportType,
  tableId,
}: ReportActionsProps) {
  const [isGeneratingPdf, setIsGeneratingPdf] = useState(false);
  const [isGeneratingCsv, setIsGeneratingCsv] = useState(false);
  const { showFormSuccess, showFormError } = useFormToast();

  const handlePrint = () => {
    if (globalThis.window !== undefined) {
      globalThis.print();
    }
  };

  const handleDownloadPdf = async () => {
    setIsGeneratingPdf(true);
    try {
      // Use the backend API to generate and download the PDF
      const apiUrl = `/api/reports/${reportType}${
        entityId ? `/${entityId}` : ''
      }`;

      // Create a link to download the file directly from the API
      const link = document.createElement('a');
      link.href = apiUrl;
      link.download = `${fileName}.pdf`;
      link.target = '_blank'; // Open in a new tab/window
      document.body.append(link);
      link.click();
      link.remove();

      showFormSuccess({
        successTitle: 'PDF Downloaded',
        successDescription: 'Your report is being downloaded as a PDF.',
      });
    } catch (error: any) {
      console.error('Error generating PDF:', error);
      showFormError(
        `PDF download failed: ${error.message || 'Please try again.'}`,
        {
          errorTitle: 'Download Failed',
        }
      );
    } finally {
      setIsGeneratingPdf(false);
    }
  };

  const handleDownloadCsv = async () => {
    if (!enableCsv) return;
    setIsGeneratingCsv(true);
    try {
      if (csvData?.data && csvData.headers) {
        generateCsvFromData(csvData.data, csvData.headers, `${fileName}.csv`);
      } else if (tableId) {
        const extractedData = extractTableDataForCsv(tableId);
        generateCsvFromData(
          extractedData.data,
          extractedData.headers,
          `${fileName}.csv`
        );
      } else {
        throw new Error(
          'CSV export requires either `csvData` or a `tableId` to be provided.'
        );
      }
      showFormSuccess({
        successTitle: 'CSV Downloaded',
        successDescription: 'Your report has been downloaded as a CSV file.',
      });
    } catch (error: any) {
      console.error('Error generating CSV:', error);
      showFormError(
        `CSV generation failed: ${error.message || 'Please try again.'}`,
        {
          errorTitle: 'Download Failed',
        }
      );
    } finally {
      setIsGeneratingCsv(false);
    }
  };

  const isGenerating = isGeneratingPdf || isGeneratingCsv;

  return (
    <div className={cn('flex items-center gap-2 no-print', className)}>
      <ActionButton
        actionType="secondary"
        aria-label="Print report"
        onClick={handlePrint}
        size="icon"
        title="Print Report"
      >
        <Printer className="size-4" />
      </ActionButton>

      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <ActionButton
            actionType="secondary"
            aria-label="Download report"
            disabled={isGenerating}
            size="icon"
            title="Download Report"
          >
            {isGenerating ? (
              <Loader2 className="size-4 animate-spin" />
            ) : (
              <Download className="size-4" />
            )}
          </ActionButton>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem
            disabled={isGeneratingPdf}
            onClick={handleDownloadPdf}
          >
            {isGeneratingPdf ? (
              <Loader2 className="mr-2 size-4 animate-spin" />
            ) : (
              <FileText className="mr-2 size-4" />
            )}
            <span>Download PDF</span>
          </DropdownMenuItem>
          {enableCsv && (
            <DropdownMenuItem
              disabled={isGeneratingCsv}
              onClick={handleDownloadCsv}
            >
              {isGeneratingCsv ? (
                <Loader2 className="mr-2 size-4 animate-spin" />
              ) : (
                <FileSpreadsheet className="mr-2 size-4" />
              )}
              <span>Download CSV</span>
            </DropdownMenuItem>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}
