/**
 * @file Comprehensive test component for all separated hooks
 * @module components/test/HooksIntegrationTest
 */

'use client';

import {
  AlertCircle,
  Bell,
  CheckCircle,
  Info,
  Menu,
  Moon,
  <PERSON>lette,
  Settings,
  Sun,
  // Modal, // Not available in lucide-react
  TestTube,
} from 'lucide-react';
import React from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import {
  useModal,
  useNotifications,
  useSidebar,
  useTheme,
  useUiPreferences,
  useWorkHubNotifications,
} from '@/lib/hooks';

/**
 * Theme testing section
 */
const ThemeTestSection: React.FC = () => {
  const {
    currentTheme,
    isDark,
    isLight,
    setDarkTheme,
    setLightTheme,
    toggleTheme,
  } = useTheme();

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Palette className="size-5" />
          Theme Hook Test
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between">
          <span>Current Theme:</span>
          <Badge variant={isDark ? 'default' : 'secondary'}>
            {currentTheme}
          </Badge>
        </div>

        <div className="flex items-center justify-between">
          <span>Is Dark:</span>
          <Badge variant={isDark ? 'default' : 'outline'}>
            {isDark ? 'Yes' : 'No'}
          </Badge>
        </div>

        <div className="flex items-center justify-between">
          <span>Is Light:</span>
          <Badge variant={isLight ? 'default' : 'outline'}>
            {isLight ? 'Yes' : 'No'}
          </Badge>
        </div>

        <Separator />

        <div className="flex gap-2">
          <Button onClick={toggleTheme} size="sm" variant="outline">
            {isDark ? (
              <Sun className="mr-1 size-4" />
            ) : (
              <Moon className="mr-1 size-4" />
            )}
            Toggle
          </Button>
          <Button onClick={setLightTheme} size="sm" variant="outline">
            <Sun className="mr-1 size-4" />
            Light
          </Button>
          <Button onClick={setDarkTheme} size="sm" variant="outline">
            <Moon className="mr-1 size-4" />
            Dark
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

/**
 * Notifications testing section
 */
const NotificationsTestSection: React.FC = () => {
  const {
    clearAllNotifications,
    showError,
    showInfo,
    showSuccess,
    showWarning,
    unreadCount,
  } = useNotifications();
  const {
    showDelegationUpdate,
    showEmployeeUpdate,
    showTaskAssigned,
    showVehicleMaintenance,
  } = useWorkHubNotifications();

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Bell className="size-5" />
          Notifications Hook Test
          {unreadCount() > 0 && (
            <Badge className="ml-2" variant="destructive">
              {unreadCount()}
            </Badge>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-2">
          <Button
            onClick={() => showSuccess('Success notification!')}
            size="sm"
            variant="outline"
          >
            <CheckCircle className="mr-1 size-4" />
            Success
          </Button>
          <Button
            onClick={() => showError('Error notification!')}
            size="sm"
            variant="outline"
          >
            <AlertCircle className="mr-1 size-4" />
            Error
          </Button>
          <Button
            onClick={() => showWarning('Warning notification!')}
            size="sm"
            variant="outline"
          >
            <AlertCircle className="mr-1 size-4" />
            Warning
          </Button>
          <Button
            onClick={() => showInfo('Info notification!')}
            size="sm"
            variant="outline"
          >
            <Info className="mr-1 size-4" />
            Info
          </Button>
        </div>

        <Separator />

        <div className="grid grid-cols-2 gap-2">
          <Button
            onClick={() => showDelegationUpdate('Delegation updated!')}
            size="sm"
            variant="outline"
          >
            Delegation
          </Button>
          <Button
            onClick={() => showVehicleMaintenance('Vehicle maintenance due!')}
            size="sm"
            variant="outline"
          >
            Vehicle
          </Button>
          <Button
            onClick={() => showTaskAssigned('Task assigned!')}
            size="sm"
            variant="outline"
          >
            Task
          </Button>
          <Button
            onClick={() => showEmployeeUpdate('Employee updated!')}
            size="sm"
            variant="outline"
          >
            Employee
          </Button>
        </div>

        <Separator />

        <Button
          className="w-full"
          onClick={clearAllNotifications}
          size="sm"
          variant="destructive"
        >
          Clear All Notifications
        </Button>
      </CardContent>
    </Card>
  );
};

/**
 * UI Preferences testing section
 */
const UiPreferencesTestSection: React.FC = () => {
  const {
    dashboardLayout,
    fontSize,
    notificationsEnabled,
    resetPreferences,
    setDashboardLayout,
    setFontSize,
    setTableDensity,
    tableDensity,
    toggleNotifications,
  } = useUiPreferences();

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Settings className="size-5" />
          UI Preferences Hook Test
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span>Font Size:</span>
            <Badge>{fontSize}</Badge>
          </div>
          <div className="flex gap-1">
            {(['small', 'medium', 'large'] as const).map(size => (
              <Button
                key={size}
                onClick={() => setFontSize(size)}
                size="sm"
                variant={fontSize === size ? 'default' : 'outline'}
              >
                {size}
              </Button>
            ))}
          </div>
        </div>

        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span>Notifications:</span>
            <Badge variant={notificationsEnabled ? 'default' : 'secondary'}>
              {notificationsEnabled ? 'Enabled' : 'Disabled'}
            </Badge>
          </div>
          <Button onClick={toggleNotifications} size="sm" variant="outline">
            Toggle Notifications
          </Button>
        </div>

        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span>Table Density:</span>
            <Badge>{tableDensity}</Badge>
          </div>
          <div className="flex gap-1">
            {(['compact', 'comfortable', 'spacious'] as const).map(density => (
              <Button
                key={density}
                onClick={() => setTableDensity(density)}
                size="sm"
                variant={tableDensity === density ? 'default' : 'outline'}
              >
                {density}
              </Button>
            ))}
          </div>
        </div>

        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span>Dashboard Layout:</span>
            <Badge>{dashboardLayout}</Badge>
          </div>
          <div className="flex gap-1">
            {(['grid', 'list', 'cards'] as const).map(layout => (
              <Button
                key={layout}
                onClick={() => setDashboardLayout(layout)}
                size="sm"
                variant={dashboardLayout === layout ? 'default' : 'outline'}
              >
                {layout}
              </Button>
            ))}
          </div>
        </div>

        <Separator />

        <Button
          className="w-full"
          onClick={resetPreferences}
          size="sm"
          variant="destructive"
        >
          Reset All Preferences
        </Button>
      </CardContent>
    </Card>
  );
};

/**
 * Sidebar and Modal testing section
 */
const SidebarModalTestSection: React.FC = () => {
  const { closeSidebar, openSidebar, sidebarOpen, toggleSidebar } =
    useSidebar();
  const {
    closeModal,
    isModalOpen,
    modalContent,
    openDelegationFormModal,
    openLoginModal,
    openSettingsModal,
  } = useModal();

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Menu className="size-5" />
          Sidebar & Modal Hook Test
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span>Sidebar:</span>
            <Badge variant={sidebarOpen ? 'default' : 'secondary'}>
              {sidebarOpen ? 'Open' : 'Closed'}
            </Badge>
          </div>
          <div className="flex gap-2">
            <Button onClick={toggleSidebar} size="sm" variant="outline">
              Toggle
            </Button>
            <Button onClick={openSidebar} size="sm" variant="outline">
              Open
            </Button>
            <Button onClick={closeSidebar} size="sm" variant="outline">
              Close
            </Button>
          </div>
        </div>

        <Separator />

        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span>Modal:</span>
            <Badge variant={isModalOpen ? 'default' : 'secondary'}>
              {isModalOpen ? `Open (${modalContent})` : 'Closed'}
            </Badge>
          </div>
          <div className="grid grid-cols-2 gap-2">
            <Button onClick={openLoginModal} size="sm" variant="outline">
              Login Modal
            </Button>
            <Button onClick={openSettingsModal} size="sm" variant="outline">
              Settings Modal
            </Button>
            <Button
              onClick={openDelegationFormModal}
              size="sm"
              variant="outline"
            >
              Delegation Modal
            </Button>
            <Button onClick={closeModal} size="sm" variant="outline">
              Close Modal
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

/**
 * Main hooks integration test component
 */
export const HooksIntegrationTest: React.FC = () => {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TestTube className="size-5" />
            Hooks Integration Test Suite
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">
            This component tests all the separated hooks to ensure they work
            correctly after refactoring. Each section tests a different hook
            with interactive controls.
          </p>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        <ThemeTestSection />
        <NotificationsTestSection />
        <UiPreferencesTestSection />
        <SidebarModalTestSection />
      </div>
    </div>
  );
};
