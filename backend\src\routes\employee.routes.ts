import { Router } from 'express';

import * as employeeController from '../controllers/employee.controller.js';
import {
  addValidationSecurityHeaders,
  sanitizeInput,
  validateRequest,
} from '../middleware/inputValidation.js';
import { adaptiveRateLimit } from '../middleware/rateLimiting.js';
import { apiDeduplication } from '../middleware/requestDeduplication.js'; // Add this import
import { responseWrapper } from '../middleware/responseWrapper.js';
import { enhancedAuthenticateUser } from '../middleware/jwtAuth.middleware.js';
import { requireRole } from '../middleware/supabaseAuth.js';
import { validate } from '../middleware/validation.js';
import {
  employeeCreateSchema,
  employeeIdSchema,
  employeeUpdateSchema,
} from '../schemas/employee.schema.js';
import { employeeSchemas } from '../schemas/validation.js';

const router = Router();

// PHASE 1 SECURITY HARDENING: Apply API rate limiting to all employee routes
router.use(adaptiveRateLimit);
router.use(apiDeduplication); // Apply API deduplication

// Apply response wrapper for standardized API responses
router.use(responseWrapper);

// 🚨 EMERGENCY SECURITY: All employee routes require authentication

// POST /api/employees - Create a new employee (ADMIN only)
// PHASE 1 SECURITY HARDENING: Enhanced input validation and sanitization
// 🔧 FIX: Use the correct employee schema that matches the frontend data structure
router.post(
  '/',
  enhancedAuthenticateUser,
  requireRole(['ADMIN']),
  validate(employeeCreateSchema, 'body'),
  (req, res, next) => {
    addValidationSecurityHeaders(res);
    next();
  },
  employeeController.createEmployee,
);

// GET /api/employees - Get all employees (MANAGER+ can see all, USER sees limited)
// PHASE 1 SECURITY HARDENING: Enhanced input validation for query parameters
router.get(
  '/',
  enhancedAuthenticateUser,
  requireRole(['ADMIN', 'MANAGER']),
  validateRequest(employeeSchemas.list),
  (req, res, next) => {
    addValidationSecurityHeaders(res);
    next();
  },
  employeeController.getAllEmployees,
);

// GET /api/employees/enriched - Get all employees enriched with vehicle information (MANAGER+)
router.get(
  '/enriched',
  enhancedAuthenticateUser,
  requireRole(['MANAGER', 'ADMIN', 'SUPER_ADMIN']),
  sanitizeInput,
  (req, res, next) => {
    addValidationSecurityHeaders(res);
    next();
  },
  employeeController.getEnrichedEmployees,
);

// GET /api/employees/:id - Get a specific employee by ID (authenticated users)
// PHASE 1 SECURITY HARDENING: Enhanced input validation for parameters
// 🔧 FIX: Use the correct employee ID schema that matches the frontend data structure
router.get(
  '/:id',
  enhancedAuthenticateUser,
  validate(employeeIdSchema, 'params'),
  (req, res, next) => {
    addValidationSecurityHeaders(res);
    next();
  },
  employeeController.getEmployeeById,
);

// PUT /api/employees/:id - Update a specific employee by ID (MANAGER+ or own record)
// PHASE 1 SECURITY HARDENING: Enhanced input validation for update operations
// 🔧 FIX: Use the correct employee update schema that matches the frontend data structure
router.put(
  '/:id',
  enhancedAuthenticateUser,
  validate(employeeUpdateSchema, 'body'),
  (req, res, next) => {
    addValidationSecurityHeaders(res);
    next();
  },
  employeeController.updateEmployee,
);

// DELETE /api/employees/:id - Delete a specific employee by ID (ADMIN only)
// PHASE 1 SECURITY HARDENING: Enhanced input validation for delete operations
// 🔧 FIX: Use the correct employee ID schema that matches the frontend data structure
router.delete(
  '/:id',
  enhancedAuthenticateUser,
  requireRole(['ADMIN']),
  validate(employeeIdSchema, 'params'),
  (req, res, next) => {
    addValidationSecurityHeaders(res);
    next();
  },
  employeeController.deleteEmployee,
);

// Get employee tasks (ADMIN or MANAGER or own record) -- This route was causing a linter error and will be removed.
// router.get(
//   '/:id/tasks',
//   enhancedAuthenticateUser, // Changed from authenticateSupabaseUser
//   employeeController.getEmployeeTasks,
// );

export default router;
