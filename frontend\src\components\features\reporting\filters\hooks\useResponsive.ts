// frontend/src/components/features/reporting/filters/hooks/useResponsive.ts
import { useState, useEffect, useCallback } from 'react';

/**
 * @hook useResponsive
 * @description Provides responsive utilities by tracking window dimensions and breakpoint matches.
 * Adheres to SRP by focusing solely on responsive logic.
 * @param {number} [debounceDelay=100] - Delay in milliseconds to debounce window resize events.
 */
export const useResponsive = (debounceDelay: number = 100) => {
  const [windowDimensions, setWindowDimensions] = useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 0,
    height: typeof window !== 'undefined' ? window.innerHeight : 0,
  });

  const getBreakpoint = useCallback(
    (width: number): 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'unknown' => {
      if (width >= 1536) return '2xl';
      if (width >= 1280) return 'xl';
      if (width >= 1024) return 'lg';
      if (width >= 768) return 'md';
      if (width >= 640) return 'sm';
      return 'unknown';
    },
    []
  );

  const currentBreakpoint = getBreakpoint(windowDimensions.width);

  useEffect(() => {
    if (typeof window === 'undefined') return;

    let timeoutId: NodeJS.Timeout;

    const handleResize = () => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        setWindowDimensions({
          width: window.innerWidth,
          height: window.innerHeight,
        });
      }, debounceDelay);
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
      clearTimeout(timeoutId);
    };
  }, [debounceDelay, getBreakpoint]);

  const isMobile = useCallback(
    () => windowDimensions.width < 768,
    [windowDimensions.width]
  );
  const isTablet = useCallback(
    () => windowDimensions.width >= 768 && windowDimensions.width < 1024,
    [windowDimensions.width]
  );
  const isDesktop = useCallback(
    () => windowDimensions.width >= 1024,
    [windowDimensions.width]
  );

  return {
    windowDimensions,
    currentBreakpoint,
    isMobile,
    isTablet,
    isDesktop,
    getBreakpoint,
  };
};
