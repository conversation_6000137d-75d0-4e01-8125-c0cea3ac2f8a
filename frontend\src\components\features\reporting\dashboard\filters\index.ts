// frontend/src/components/features/reporting/dashboard/filters/index.ts

/**
 * Reporting Dashboard Filters Export Index
 *
 * Centralized exports for all reporting filter components following DRY principles.
 * Each filter component integrates with the reporting filters store for state management.
 */

// Main filter panel
export { ReportingFilters } from './ReportingFilters';

// Individual filter components
export { DateRangeFilter } from './DateRangeFilter';
export { StatusFilter } from './StatusFilter';
export { LocationFilter } from './LocationFilter';
export { EmployeeFilter } from './EmployeeFilter';
export { VehicleFilter } from './VehicleFilter';

// Filter presets management
export { FilterPresets } from './FilterPresets';

// ENHANCED: Service history filter components
export { ServiceTypeFilter } from './ServiceTypeFilter';
export { ServiceHistoryToggle } from './ServiceHistoryToggle';
