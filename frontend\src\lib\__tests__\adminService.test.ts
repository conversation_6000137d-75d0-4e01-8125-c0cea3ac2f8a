import type { PaginatedApiResponse } from '@/hooks/api/useApiQuery';
import type {
  ErrorLogEntry,
  HealthCheck,
  SystemMetrics,
} from '@/lib/types/domain';

import { adminService } from '@/lib/api/services/admin';

// Note: This test needs to be updated to use the new BaseApiService pattern
// import { api } from '../services/apiService'; // Removed - use BaseApiService instead
import { withRetry } from '../utils/apiUtils';

// Mock dependencies
jest.mock('../services/apiService', () => ({
  api: {
    get: jest.fn(),
  },
}));

jest.mock('../utils/apiUtils', () => ({
  withRetry: jest.fn(fn => fn()),
}));

describe('Admin Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getHealthStatus', () => {
    it('should call api.get with the correct endpoint', async () => {
      const mockResponse: HealthCheck = {
        environment: 'test',
        status: 'healthy',
        timestamp: '2025-01-01T12:00:00Z',
        uptime: 12_345,
        version: '1.0.0',
      };
      (api.get as jest.Mock).mockResolvedValue(mockResponse);

      const result = await adminService.getHealthStatus();

      expect(withRetry).toHaveBeenCalled();
      expect(api.get).toHaveBeenCalledWith('/admin/health');
      expect(result).toEqual(mockResponse);
    });

    it('should propagate errors from api.get', async () => {
      const error = new Error('Network error');
      (api.get as jest.Mock).mockRejectedValue(error);
      (withRetry as jest.Mock).mockImplementation(fn => fn());

      await expect(adminService.getHealthStatus()).rejects.toThrow(
        'Network error'
      );
    });
  });

  describe('getPerformanceMetrics', () => {
    it('should call api.get with the correct endpoint', async () => {
      const mockResponse: SystemMetrics = {
        deduplicationMetrics: {
          cacheHits: 100,
          cacheMisses: 10,
          errors: 0,
          hitRate: 0.9,
          lastReset: '2025-01-01T00:00:00Z',
          totalRequests: 110,
        },
        httpRequestMetrics: {
          help: 'HTTP request metrics',
          name: 'http_requests_total',
          type: 'counter',
          values: [],
        },
        systemMetrics: {
          connections: { active: 5, total: 10 },
          cpu: { loadAverage: [0.5, 0.4, 0.3], usage: 0.6 },
          memory: { free: 1024, total: 4096, usagePercent: 25, used: 3072 },
        },
      };
      (api.get as jest.Mock).mockResolvedValue(mockResponse);

      const result = await adminService.getPerformanceMetrics();

      expect(withRetry).toHaveBeenCalled();
      expect(api.get).toHaveBeenCalledWith('/admin/performance');
      expect(result).toEqual(mockResponse);
    });
  });

  describe('getRecentErrors', () => {
    it('should call api.get with default parameters', async () => {
      const mockResponse: PaginatedApiResponse<ErrorLogEntry> = {
        data: [
          {
            details: {},
            id: '1',
            level: 'ERROR',
            message: 'Test error 1',
            source: 'frontend',
            timestamp: '2025-01-01T10:00:00Z',
          },
        ],
        pagination: {
          hasNext: false,
          hasPrevious: false,
          limit: 10,
          page: 1,
          total: 1,
          totalPages: 1,
        },
      };
      (api.get as jest.Mock).mockResolvedValue(mockResponse);

      const result = await adminService.getRecentErrors(1, 10);

      expect(withRetry).toHaveBeenCalled();
      expect(api.get).toHaveBeenCalledWith('/admin/errors?page=1&limit=10');
      expect(result).toEqual(mockResponse);
    });

    it('should call api.get with custom parameters', async () => {
      const mockResponse: PaginatedApiResponse<ErrorLogEntry> = {
        data: [
          {
            details: {},
            id: '2',
            level: 'ERROR',
            message: 'Test error 2',
            source: 'frontend',
            timestamp: '2025-01-01T11:00:00Z',
          },
        ],
        pagination: {
          hasNext: false,
          hasPrevious: true,
          limit: 5,
          page: 2,
          total: 1,
          totalPages: 1,
        },
      };
      (api.get as jest.Mock).mockResolvedValue(mockResponse);

      const result = await adminService.getRecentErrors(2, 5, 'ERROR');

      expect(withRetry).toHaveBeenCalled();
      expect(api.get).toHaveBeenCalledWith(
        '/admin/errors?page=2&limit=5&level=ERROR'
      );
      expect(result).toEqual(mockResponse);
    });
  });
});
