/**
 * @file Standardized API Mutation Hook
 * @description Provides consistent API mutation patterns with optimistic updates, error handling, and loading states
 */

import {
  type QueryKey,
  type UseMutationOptions,
  type UseMutationResult,
  useMutation,
  useQueryClient,
} from '@tanstack/react-query';

import { useToast } from '@/hooks/utils/use-toast';

/**
 * Configuration options for API mutations
 */
export interface ApiMutationOptions<TData, TVariables>
  extends Omit<UseMutationOptions<TData, Error, TVariables>, 'mutationFn'> {
  /** Whether to show error toasts automatically */
  showErrorToast?: boolean;
  /** Whether to show success toasts automatically */
  showSuccessToast?: boolean;
  /** Custom success message */
  successMessage?: string;
  /** Custom error message */
  errorMessage?: string;
  /** Query keys to invalidate on success */
  invalidateQueries?: QueryKey[];
  /** Whether to enable optimistic updates */
  enableOptimisticUpdates?: boolean;
  /** Function to generate optimistic update data */
  optimisticUpdateFn?: (variables: TVariables) => TData;
  /** Query key for optimistic updates */
  optimisticQueryKey?: QueryKey;
}

/**
 * Enhanced API mutation result with additional utilities
 */
export interface ApiMutationResult<TData, TVariables> {
  /** Execute mutation with automatic error handling */
  executeAsync: (variables: TVariables) => Promise<TData>;
  /** Check if mutation is in progress */
  isExecuting: boolean;
  /** Last execution timestamp */
  lastExecuted: number | null;
  /** All properties from UseMutationResult */
  mutate: (variables: TVariables, options?: any) => void;
  mutateAsync: (variables: TVariables, options?: any) => Promise<TData>;
  reset: () => void;
  isPending: boolean;
  isError: boolean;
  isSuccess: boolean;
  isIdle: boolean;
  data: TData | undefined;
  error: Error | null;
  variables: TVariables | undefined;
  context: unknown;
  failureCount: number;
  failureReason: Error | null;
  isPaused: boolean;
  status: 'idle' | 'pending' | 'error' | 'success';
  submittedAt: number;
}

/**
 * Standardized API mutation hook with consistent error handling and optimistic updates
 *
 * @example
 * ```typescript
 * const { mutate, isLoading, executeAsync } = useApiMutation(
 *   (userData) => userService.create(userData),
 *   {
 *     showSuccessToast: true,
 *     successMessage: 'User created successfully',
 *     invalidateQueries: [['users']],
 *     enableOptimisticUpdates: true,
 *     optimisticQueryKey: ['users'],
 *     optimisticUpdateFn: (userData) => ({ ...userData, id: 'temp-id' }),
 *   }
 * );
 * ```
 */
export const useApiMutation = <TData, TVariables = void>(
  mutationFn: (variables: TVariables) => Promise<TData>,
  options: ApiMutationOptions<TData, TVariables> = {}
): ApiMutationResult<TData, TVariables> => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const {
    showErrorToast = true,
    showSuccessToast = true,
    successMessage = 'Operation completed successfully',
    errorMessage,
    invalidateQueries = [],
    enableOptimisticUpdates = false,
    optimisticUpdateFn,
    optimisticQueryKey,
    onSuccess,
    onError,
    onMutate,
    ...mutationOptions
  } = options;

  const mutationResult = useMutation({
    mutationFn,
    onMutate: async variables => {
      // Handle optimistic updates
      if (enableOptimisticUpdates && optimisticQueryKey && optimisticUpdateFn) {
        // Cancel any outgoing refetches
        await queryClient.cancelQueries({ queryKey: optimisticQueryKey });

        // Snapshot the previous value
        const previousData = queryClient.getQueryData(optimisticQueryKey);

        // Optimistically update to the new value
        const optimisticData = optimisticUpdateFn(variables);
        queryClient.setQueryData(optimisticQueryKey, optimisticData);

        // Return a context object with the snapshotted value
        const context = { previousData };

        // Call custom onMutate if provided
        const customContext = await onMutate?.(variables);

        return customContext ? { ...context, ...customContext } : context;
      }

      return onMutate?.(variables);
    },
    onSuccess: (data, variables, context) => {
      // Show success toast
      if (showSuccessToast) {
        toast({
          title: 'Success',
          description: successMessage,
        });
      }

      // Invalidate and refetch queries
      invalidateQueries.forEach(queryKey => {
        queryClient.invalidateQueries({ queryKey });
      });

      // Call custom onSuccess if provided
      onSuccess?.(data, variables, context);
    },
    onError: (error, variables, context) => {
      // Rollback optimistic updates
      if (
        enableOptimisticUpdates &&
        optimisticQueryKey &&
        context &&
        typeof context === 'object' &&
        'previousData' in context
      ) {
        queryClient.setQueryData(
          optimisticQueryKey,
          (context as any).previousData
        );
      }

      // Show error toast
      if (showErrorToast) {
        const message = errorMessage || error.message || 'An error occurred';
        toast({
          title: 'Error',
          description: message,
          variant: 'destructive',
        });
      }

      // Call custom onError if provided
      onError?.(error, variables, context);
    },
    ...mutationOptions,
  });

  // Enhanced result with additional utilities
  const enhancedResult: ApiMutationResult<TData, TVariables> = {
    ...mutationResult,
    executeAsync: async (variables: TVariables): Promise<TData> => {
      return new Promise((resolve, reject) => {
        mutationResult.mutate(variables, {
          onSuccess: data => resolve(data),
          onError: error => reject(error),
        });
      });
    },
    isExecuting: mutationResult.isPending,
    lastExecuted: mutationResult.submittedAt || null,
  };

  return enhancedResult;
};

/**
 * Hook for mutations that create new resources
 * Automatically handles cache updates for list queries
 */
export const useCreateMutation = <TData, TVariables = void>(
  mutationFn: (variables: TVariables) => Promise<TData>,
  listQueryKey: QueryKey,
  options: Omit<ApiMutationOptions<TData, TVariables>, 'invalidateQueries'> = {}
): ApiMutationResult<TData, TVariables> => {
  return useApiMutation(mutationFn, {
    ...options,
    invalidateQueries: [listQueryKey],
    successMessage: options.successMessage || 'Item created successfully',
  });
};

/**
 * Hook for mutations that update existing resources
 * Automatically handles cache updates for both list and detail queries
 */
export const useUpdateMutation = <TData, TVariables = void>(
  mutationFn: (variables: TVariables) => Promise<TData>,
  listQueryKey: QueryKey,
  getDetailQueryKey: (data: TData) => QueryKey,
  options: Omit<
    ApiMutationOptions<TData, TVariables>,
    'invalidateQueries' | 'onSuccess'
  > = {}
): ApiMutationResult<TData, TVariables> => {
  const queryClient = useQueryClient();

  return useApiMutation(mutationFn, {
    ...options,
    successMessage: options.successMessage || 'Item updated successfully',
    onSuccess: (data, variables, context) => {
      // Invalidate list query
      queryClient.invalidateQueries({ queryKey: listQueryKey });

      // Update detail query cache
      const detailQueryKey = getDetailQueryKey(data);
      queryClient.setQueryData(detailQueryKey, data);

      // Call custom onSuccess if provided
      (options as any).onSuccess?.(data, variables, context);
    },
  });
};

/**
 * Hook for mutations that delete resources
 * Automatically handles cache cleanup
 */
export const useDeleteMutation = <TVariables = void>(
  mutationFn: (variables: TVariables) => Promise<void>,
  listQueryKey: QueryKey,
  getDetailQueryKey: (variables: TVariables) => QueryKey,
  options: Omit<
    ApiMutationOptions<void, TVariables>,
    'invalidateQueries' | 'onSuccess'
  > = {}
): ApiMutationResult<void, TVariables> => {
  const queryClient = useQueryClient();

  return useApiMutation(mutationFn, {
    ...options,
    successMessage: options.successMessage || 'Item deleted successfully',
    onSuccess: (data, variables, context) => {
      // Invalidate list query
      queryClient.invalidateQueries({ queryKey: listQueryKey });

      // Remove detail query from cache
      const detailQueryKey = getDetailQueryKey(variables);
      queryClient.removeQueries({ queryKey: detailQueryKey });

      // Call custom onSuccess if provided
      (options as any).onSuccess?.(data, variables, context);
    },
  });
};
