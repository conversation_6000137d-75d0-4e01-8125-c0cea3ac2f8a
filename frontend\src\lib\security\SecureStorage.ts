/**
 * @file Secure Storage Service - Single Responsibility Principle (SRP)
 * @module lib/security/SecureStorage
 *
 * This class handles ONLY secure storage operations following SRP principles.
 * It provides a centralized interface for secure cookie management and storage operations.
 *
 * SECURITY NOTE: This class manages httpOnly cookie interactions and secure storage patterns.
 * It does NOT handle token validation or authentication logic.
 */

export interface CookieOptions {
  httpOnly?: boolean;
  secure?: boolean;
  sameSite?: 'strict' | 'lax' | 'none';
  maxAge?: number;
  path?: string;
  domain?: string;
}

export interface StorageResult {
  success: boolean;
  error?: string;
}

/**
 * SecureStorage - Single Responsibility: Secure Storage Operations Only
 *
 * Handles secure cookie management and storage operations.
 * Does NOT handle token validation or authentication logic.
 */
export class SecureStorage {
  private static readonly DEFAULT_COOKIE_OPTIONS: CookieOptions = {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    path: '/',
  };

  /**
   * SECURITY FIX: Renamed from setSecureItem to clarify limitations
   *
   * Sets a CLIENT-SIDE ONLY cookie (NOT httpOnly)
   *
   * ⚠️  CRITICAL SECURITY WARNING:
   * - This method CANNOT set httpOnly cookies (browser security restriction)
   * - This method should NEVER be used for sensitive data like tokens
   * - Sensitive tokens MUST be set by the server as httpOnly cookies
   * - This is only for non-sensitive client-side preferences/settings
   *
   * Single responsibility: Non-httpOnly cookie setting only
   */
  static setClientSideCookie(
    key: string,
    value: string,
    options?: Partial<CookieOptions>
  ): StorageResult {
    try {
      if (typeof window === 'undefined') {
        return {
          success: false,
          error: 'Not available in server-side environment',
        };
      }

      const cookieOptions = { ...this.DEFAULT_COOKIE_OPTIONS, ...options };

      // Build cookie string (for non-httpOnly cookies only)
      let cookieString = `${encodeURIComponent(key)}=${encodeURIComponent(value)}`;

      if (cookieOptions.maxAge) {
        cookieString += `; Max-Age=${cookieOptions.maxAge}`;
      }

      if (cookieOptions.path) {
        cookieString += `; Path=${cookieOptions.path}`;
      }

      if (cookieOptions.domain) {
        cookieString += `; Domain=${cookieOptions.domain}`;
      }

      if (cookieOptions.secure) {
        cookieString += '; Secure';
      }

      if (cookieOptions.sameSite) {
        cookieString += `; SameSite=${cookieOptions.sameSite}`;
      }

      // SECURITY: Only set non-httpOnly cookies from client-side
      // httpOnly cookies MUST be set by the server for security
      if (!cookieOptions.httpOnly) {
        document.cookie = cookieString;
      } else {
        // Prevent misuse by throwing an error if httpOnly is requested
        throw new Error(
          'Cannot set httpOnly cookies from client-side. Use server-side cookie setting.'
        );
      }

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : 'Failed to set client-side cookie',
      };
    }
  }

  /**
   * Get secure item from cookie
   * Single responsibility: Cookie retrieval only
   */
  static getSecureItem(key: string): string | null {
    try {
      if (typeof window === 'undefined') {
        return null;
      }

      const cookies = document.cookie.split(';').reduce(
        (acc, cookie) => {
          const [cookieKey, cookieValue] = cookie.trim().split('=');
          if (cookieKey && cookieValue) {
            acc[decodeURIComponent(cookieKey)] =
              decodeURIComponent(cookieValue);
          }
          return acc;
        },
        {} as Record<string, string>
      );

      return cookies[key] || null;
    } catch (error) {
      console.error('Failed to get secure item:', error);
      return null;
    }
  }

  /**
   * Remove secure item from cookie
   * Single responsibility: Cookie removal only
   */
  static removeSecureItem(
    key: string,
    options?: Partial<CookieOptions>
  ): StorageResult {
    try {
      if (typeof window === 'undefined') {
        return {
          success: false,
          error: 'Not available in server-side environment',
        };
      }

      const cookieOptions = { ...this.DEFAULT_COOKIE_OPTIONS, ...options };

      // Set cookie with past expiration date to remove it
      let cookieString = `${encodeURIComponent(key)}=; expires=Thu, 01 Jan 1970 00:00:00 GMT`;

      if (cookieOptions.path) {
        cookieString += `; Path=${cookieOptions.path}`;
      }

      if (cookieOptions.domain) {
        cookieString += `; Domain=${cookieOptions.domain}`;
      }

      document.cookie = cookieString;

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : 'Failed to remove secure item',
      };
    }
  }

  /**
   * Check if secure storage is available
   * Single responsibility: Storage availability check only
   */
  static isAvailable(): boolean {
    try {
      if (typeof window === 'undefined') {
        return false;
      }

      // Test cookie functionality
      const testKey = '__secure_storage_test__';
      const testValue = 'test';

      this.setClientSideCookie(testKey, testValue, { httpOnly: false });
      const retrieved = this.getSecureItem(testKey);
      this.removeSecureItem(testKey);

      return retrieved === testValue;
    } catch {
      return false;
    }
  }

  /**
   * Clear all non-httpOnly cookies (for logout)
   * Single responsibility: Cookie clearing only
   */
  static clearAllCookies(): StorageResult {
    try {
      if (typeof window === 'undefined') {
        return {
          success: false,
          error: 'Not available in server-side environment',
        };
      }

      const cookies = document.cookie.split(';');

      for (const cookie of cookies) {
        const eqPos = cookie.indexOf('=');
        const name =
          eqPos > -1 ? cookie.substring(0, eqPos).trim() : cookie.trim();

        if (name) {
          // Remove cookie by setting it to expire in the past
          document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/`;
          document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/; domain=${window.location.hostname}`;
        }
      }

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error:
          error instanceof Error ? error.message : 'Failed to clear cookies',
      };
    }
  }

  /**
   * Get all available cookies as key-value pairs
   * Single responsibility: Cookie enumeration only
   */
  static getAllCookies(): Record<string, string> {
    try {
      if (typeof window === 'undefined') {
        return {};
      }

      return document.cookie.split(';').reduce(
        (acc, cookie) => {
          const [key, value] = cookie.trim().split('=');
          if (key && value) {
            acc[decodeURIComponent(key)] = decodeURIComponent(value);
          }
          return acc;
        },
        {} as Record<string, string>
      );
    } catch {
      return {};
    }
  }

  /**
   * Check if a specific cookie exists
   * Single responsibility: Cookie existence check only
   */
  static hasCookie(key: string): boolean {
    return this.getSecureItem(key) !== null;
  }

  /**
   * Get cookie expiration time (if available in cookie value)
   * Single responsibility: Cookie expiration check only
   */
  static getCookieExpiration(_key: string): Date | null {
    // Note: Cookie expiration is not accessible from client-side JavaScript
    // This method is for future extensibility if needed
    return null;
  }
}
