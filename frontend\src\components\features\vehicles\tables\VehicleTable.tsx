/**
 * Advanced Vehicle Table Component
 *
 * Demonstrates the full power of our DataTable component with advanced features
 * inspired by DelegationTable.tsx patterns. Shows how to migrate from legacy
 * table implementations to the new standardized DataTable component.
 */

'use client';

import type { ColumnDef } from '@tanstack/react-table';

import { Archive, Car, Copy, Trash } from 'lucide-react';
import { useRouter } from 'next/navigation';

import type { Vehicle } from '@/lib/types/domain';

import {
  createDateColumn,
  createEnhancedActionsColumn,
  createIconTextColumn,
  createNumericColumn,
  createSelectionColumn,
  createTextColumn,
  createTitleSubtitleColumn,
  DataTable,
} from '@/components/ui/tables';
import { useToast } from '@/hooks/utils/use-toast';

interface VehicleTableProps {
  className?: string;
  onBulkArchive?: (vehicles: Vehicle[]) => Promise<void>;
  onBulkDelete?: (vehicles: Vehicle[]) => Promise<void>;
  onDelete?: (vehicle: Vehicle) => Promise<void>;
  vehicles: Vehicle[];
}

export const VehicleTable = ({
  className = '',
  onBulkArchive,
  onBulkDelete,
  onDelete,
  vehicles,
}: VehicleTableProps) => {
  const router = useRouter();
  const { toast } = useToast();

  // Define advanced columns using helper functions
  const columns: ColumnDef<Vehicle>[] = [
    // Row selection checkbox
    createSelectionColumn<Vehicle>(),

    // Complex title/subtitle column (Make/Model with VIN)
    createTitleSubtitleColumn('make', 'vin', 'Vehicle'),

    // Model as separate column
    createTextColumn('model', 'Model'),

    // Year with custom styling
    createNumericColumn('year', 'Year', { decimals: 0 }),

    // License plate with monospace font
    createTextColumn('licensePlate', 'License Plate', {
      className: 'font-mono text-sm bg-gray-100 px-2 py-1 rounded',
    }),

    // Owner information
    createTextColumn('ownerName', 'Owner'),

    // Odometer with icon
    createIconTextColumn(
      'initialOdometer',
      'Odometer',
      ({ className }: { className?: string }) => <Car className={className} />,
      {
        className: 'text-blue-600',
        formatter: value => `${value || 0} km`,
      }
    ),

    // Created date
    createDateColumn('createdAt', 'Created', 'MMM dd, yyyy'),

    // Enhanced actions with professional styling
    createEnhancedActionsColumn({
      customActions: [
        {
          icon: ({ className }: { className?: string }) => (
            <Copy className={className} />
          ),
          label: 'Duplicate',
          onClick: vehicle => {
            toast({
              description: `Duplicate functionality for ${vehicle.make} ${vehicle.model}`,
              title: 'Feature Coming Soon',
            });
          },
        },
        {
          icon: ({ className }: { className?: string }) => (
            <Archive className={className} />
          ),
          label: 'Archive',
          onClick: vehicle => {
            toast({
              description: `${vehicle.make} ${vehicle.model} has been archived`,
              title: 'Vehicle Archived',
            });
          },
          variant: 'destructive',
        },
      ],
      editHref: vehicle => `/vehicles/${vehicle.id}/edit`,
      ...(onDelete && {
        onDelete: (vehicle: Vehicle) => {
          onDelete(vehicle);
        },
      }),
      showCopyId: true,
      viewHref: vehicle => `/vehicles/${vehicle.id}`,
    }),
  ];

  // Bulk actions for selected rows
  const bulkActions = [
    {
      icon: ({ className }: { className?: string }) => (
        <Trash className={className} />
      ),
      label: 'Delete Selected',
      onClick: async (selectedVehicles: Vehicle[]) => {
        if (onBulkDelete) {
          await onBulkDelete(selectedVehicles);
          toast({
            description: `${selectedVehicles.length} vehicles have been deleted`,
            title: 'Vehicles Deleted',
          });
        }
      },
      variant: 'destructive' as const,
    },
    {
      icon: ({ className }: { className?: string }) => (
        <Archive className={className} />
      ),
      label: 'Archive Selected',
      onClick: async (selectedVehicles: Vehicle[]) => {
        if (onBulkArchive) {
          await onBulkArchive(selectedVehicles);
          toast({
            description: `${selectedVehicles.length} vehicles have been archived`,
            title: 'Vehicles Archived',
          });
        }
      },
    },
  ];

  return (
    <DataTable
      bulkActions={bulkActions}
      className={className}
      columns={columns}
      data={vehicles}
      emptyMessage="No vehicles found. Create your first vehicle to get started."
      enableBulkActions={true}
      enableColumnVisibility={true}
      // Advanced features
      enableRowSelection={true}
      headerClassName="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20"
      onRowClick={vehicle => router.push(`/vehicles/${vehicle.id}`)}
      pageSize={20}
      rowClassName="hover:bg-blue-50/50 dark:hover:bg-blue-900/10"
      searchColumn="make"
      searchPlaceholder="Search vehicles by make, model, or license plate..."
      // Professional styling
      tableClassName="shadow-lg"
    />
  );
};

export default VehicleTable;
