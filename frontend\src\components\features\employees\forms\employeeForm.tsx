'use client';

import { <PERSON><PERSON><PERSON><PERSON>, Save } from 'lucide-react';
import { useRouter } from 'next/navigation';
import React from 'react';
import {
  type ControllerRenderProps,
  type DefaultValues,
  useFormContext,
} from 'react-hook-form'; // Import useFormContext, ControllerRenderProps, and DefaultValues

import type { EmployeeFormData } from '@/lib/schemas/employeeSchemas';
import type { Employee } from '@/lib/types/domain';

import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { BaseForm } from '@/components/ui/forms/baseForm';
import { FormField } from '@/components/ui/forms/formField'; // Correct import for the reusable FormField
import { Input } from '@/components/ui/input'; // Import Input for manual rendering in FormField
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { Textarea } from '@/components/ui/textarea'; // Import Textarea for manual rendering in FormField
import { DriverAvailabilitySchema } from '@/lib/schemas/driverSchemas';
import {
  EmployeeFormSchema,
  EmployeeRoleSchema,
  EmployeeStatusSchema,
} from '@/lib/schemas/employeeSchemas';
import { formatEmployeeStatusForDisplay } from '@/lib/utils/formattingUtils';

interface EmployeeFormProps {
  initialData?: Partial<Employee>;
  isEditing?: boolean;
  isLoading?: boolean; // Keep isLoading for external control if needed
  onSubmit: (data: EmployeeFormData) => Promise<void>;
}

export const EmployeeForm: React.FC<EmployeeFormProps> = ({
  initialData = {},
  isEditing = false,
  onSubmit,
}) => {
  const router = useRouter();
  // Map initialData to form-compatible defaultValues (EmployeeFormData)
  // Ensure skills and generalAssignments are arrays for defaultValues
  const defaultValues: DefaultValues<EmployeeFormData> = {
    // Explicitly type as DefaultValues<EmployeeFormData>
    availability: initialData.availability ?? undefined,
    contactEmail: initialData.contactEmail ?? '',
    contactInfo: initialData.contactInfo ?? initialData.contactEmail ?? '',
    contactMobile: initialData.contactMobile ?? '',
    contactPhone: initialData.contactPhone ?? '',
    currentLocation: initialData.currentLocation ?? '',
    department: initialData.department ?? '',
    employeeId: initialData.employeeId ?? '',
    fullName: initialData.fullName ?? initialData.name ?? '',
    generalAssignments: (initialData.generalAssignments ??
      []) as EmployeeFormData['generalAssignments'], // Ensure it's an array
    hireDate: initialData.hireDate
      ? new Date(initialData.hireDate).toISOString().split('T')[0]
      : '',
    name: initialData.name ?? initialData.fullName ?? '',
    notes: initialData.notes ?? '',
    position: initialData.position ?? '',
    profileImageUrl: initialData.profileImageUrl ?? '',
    role: (initialData.role ?? 'other') as EmployeeFormData['role'],
    shiftSchedule: initialData.shiftSchedule ?? '',
    skills: (initialData.skills ?? []) as EmployeeFormData['skills'], // Ensure it's an array
    status: initialData.status ?? EmployeeStatusSchema.enum.Active, // Removed @ts-expect-error
    workingHours: initialData.workingHours ?? '',
  };

  // The onSubmit function passed to BaseForm will receive the validated data.
  // No manual transformations needed here, as the schema expects arrays.
  const handleFormSubmit = async (data: EmployeeFormData) => {
    await onSubmit(data);
  };

  return (
    <BaseForm
      defaultValues={defaultValues}
      onSubmit={handleFormSubmit}
      schema={EmployeeFormSchema}
    >
      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle className="text-2xl text-primary">
            {isEditing ? 'Edit Employee' : 'Add New Employee'}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Use a wrapper component to access form context for reactive fields */}
          <EmployeeFormFields />
        </CardContent>
        <CardFooter className="flex justify-between gap-2 border-t pt-6">
          <Button onClick={() => router.back()} type="button" variant="outline">
            <ArrowLeft className="mr-2 size-4" />
            Cancel
          </Button>
          <Button
            className="bg-accent text-accent-foreground hover:bg-accent/90"
            type="submit"
          >
            <Save className="mr-2 size-4" />
            {isEditing ? 'Save Changes' : 'Create Employee'}
          </Button>
        </CardFooter>
      </Card>
    </BaseForm>
  );
};

// Separate component to access form context for reactive fields and render form fields
const EmployeeFormFields: React.FC = () => {
  const { watch } = useFormContext<EmployeeFormData>(); // Get watch from form context
  const currentRole = watch('role'); // Watch the role field for dynamic rendering

  return (
    <>
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
        <FormField label="Display Name" name="name" />
        <FormField label="Full Name (Optional)" name="fullName" />
      </div>
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
        <FormField label="Employee ID (Business Key)" name="employeeId" />
        <FormField label="Position/Title" name="position" />
      </div>
      <FormField label="Department" name="department" />

      <h3 className="text-lg font-medium text-foreground">
        Contact Information
      </h3>

      <FormField label="Primary Contact (Email/Phone)" name="contactInfo" />
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
        <FormField
          label="Contact Email (Optional)"
          name="contactEmail"
          type="email"
        />
        <FormField
          label="Contact Phone (Optional)"
          name="contactPhone"
          type="tel"
        />
      </div>
      <FormField
        label="Contact Mobile (Optional)"
        name="contactMobile"
        type="tel"
      />

      <h3 className="text-lg font-medium text-foreground">
        Employment Details
      </h3>

      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
        <FormField label="Hire Date" name="hireDate" type="date" />
        <FormField
          label="Status"
          name="status"
          render={({
            field,
          }: {
            field: ControllerRenderProps<EmployeeFormData, 'status'>;
          }) => (
            <Select onValueChange={field.onChange} value={field.value}>
              <SelectTrigger id="status">
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent>
                {EmployeeStatusSchema.options.map(s => (
                  <SelectItem key={s} value={s}>
                    {formatEmployeeStatusForDisplay(s)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}
        />
      </div>

      <h3 className="text-lg font-medium text-foreground">
        Role & Availability
      </h3>

      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
        <FormField
          label="Role"
          name="role"
          render={({
            field,
          }: {
            field: ControllerRenderProps<EmployeeFormData, 'role'>;
          }) => (
            <Select onValueChange={field.onChange} value={field.value}>
              <SelectTrigger id="role">
                <SelectValue placeholder="Select role" />
              </SelectTrigger>
              <SelectContent>
                {EmployeeRoleSchema.options.map(r => (
                  <SelectItem key={r} value={r}>
                    {r.charAt(0).toUpperCase() + r.slice(1).replace('_', ' ')}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}
        />
        {/* Conditional rendering for driver-specific fields */}
        {currentRole === 'driver' && (
          <FormField
            label="Availability (for Drivers)"
            name="availability"
            render={({
              field,
            }: {
              field: ControllerRenderProps<EmployeeFormData, 'availability'>;
            }) => (
              <Select value={field.value || ''} onValueChange={field.onChange}>
                <SelectTrigger id="availability">
                  <SelectValue placeholder="Select availability" />
                </SelectTrigger>
                <SelectContent>
                  {DriverAvailabilitySchema.options.map(a => (
                    <SelectItem key={a} value={a}>
                      {a.replace('_', ' ')}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          />
        )}
      </div>

      {currentRole === 'driver' && (
        <>
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
            <FormField
              label="Current Location (Optional, for Drivers)"
              name="currentLocation"
            />
            <FormField
              label="Working Hours (Optional, for Drivers)"
              name="workingHours"
            />
          </div>
        </>
      )}

      <h3 className="text-lg font-medium text-foreground">Other Details</h3>

      {/* Custom handling for skills and generalAssignments as comma-separated strings */}
      <FormField
        label="Skills (comma-separated)"
        name="skills"
        render={({
          field,
        }: {
          field: ControllerRenderProps<EmployeeFormData, 'skills'>;
        }) => (
          <Input
            onChange={e => {
              const stringValue = e.target.value;
              const arrayValue = stringValue
                .split(',')
                .map(s => s.trim())
                .filter(Boolean);
              field.onChange(arrayValue); // Pass array to react-hook-form
            }}
            placeholder="e.g., Diesel Engine Repair, HVAC Systems, Welding"
            value={Array.isArray(field.value) ? field.value.join(', ') : ''} // Display as string
          />
        )}
      />
      <FormField label="Shift Schedule (Optional)" name="shiftSchedule" />
      <FormField
        label="General Assignments (comma-separated, Optional)"
        name="generalAssignments"
        render={({
          field,
        }: {
          field: ControllerRenderProps<EmployeeFormData, 'generalAssignments'>;
        }) => (
          <Input
            onChange={e => {
              const stringValue = e.target.value;
              const arrayValue = stringValue
                .split(',')
                .map(s => s.trim())
                .filter(Boolean);
              field.onChange(arrayValue); // Pass array to react-hook-form
            }}
            placeholder="e.g., Workshop Cleanup, Inventory Check"
            value={Array.isArray(field.value) ? field.value.join(', ') : ''} // Display as string
          />
        )}
      />
      <FormField label="Profile Image URL (Optional)" name="profileImageUrl" />
      <FormField label="Notes (Optional)" name="notes" type="textarea" />
    </>
  );
};

export default EmployeeForm;
