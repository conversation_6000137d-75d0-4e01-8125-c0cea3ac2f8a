/**
 * OpenSky Network API Service
 *
 * This service provides functions to interact with the OpenSky Network API
 * for retrieving flight information.
 *
 * API Documentation: https://openskynetwork.github.io/opensky-api/rest.html
 */

import axios from 'axios';

import { logger } from '../utils/logger.js';

// Base URL for OpenSky Network API
const OPENSKY_API_BASE_URL = 'https://opensky-network.org/api';

// Optional credentials for authenticated requests
// These can be moved to environment variables
const OPENSKY_USERNAME = process.env.OPENSKY_USERNAME || '';
const OPENSKY_PASSWORD = process.env.OPENSKY_PASSWORD || '';

// API request timeout in milliseconds
const API_TIMEOUT = 15000; // 15 seconds

// Helper to add delay
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// Simplified flight data for frontend
export interface SimplifiedFlightData {
  altitude?: number;
  arrivalAirport?: string;
  arrivalTime?: number;
  callsign: string;
  departureAirport?: string;
  departureTime?: number;
  heading?: number;
  icao24: string;
  lastSeen?: number;
  latitude?: number;
  longitude?: number;
  onGround?: boolean;
  velocity?: number;
}

interface OpenSkyFlight {
  arrivalAirportCandidatesCount: null | number; // Number of other possible arrival airports. Can be null if no arrival airport candidates have been found
  callsign: null | string; // Callsign of the vehicle (8 chars). Can be null if no callsign has been received
  departureAirportCandidatesCount: null | number; // Number of other possible departure airports. Can be null if no departure airport candidates have been found
  estArrivalAirport: null | string; // ICAO code of the estimated arrival airport. Can be null if the airport could not be identified
  estArrivalAirportHorizDistance: null | number; // Horizontal distance of the last received airborne position to the estimated arrival airport in meters
  estArrivalAirportVertDistance: null | number; // Vertical distance of the last received airborne position to the estimated arrival airport in meters
  estDepartureAirport: null | string; // ICAO code of the estimated departure airport. Can be null if the airport could not be identified
  estDepartureAirportHorizDistance: null | number; // Horizontal distance of the last received airborne position to the estimated departure airport in meters
  estDepartureAirportVertDistance: null | number; // Vertical distance of the last received airborne position to the estimated departure airport in meters
  firstSeen: number; // Estimated time of departure for the flight as Unix time (seconds since epoch)
  icao24: string; // Unique ICAO 24-bit address of the transponder in hex string representation
  lastSeen: number; // Estimated time of arrival for the flight as Unix time (seconds since epoch)
}

interface OpenSkyStates {
  states: null | OpenSkyStateVector[]; // The state vectors
  time: number; // The time which the state vectors in this response are associated with
}

// Types for OpenSky API responses
interface OpenSkyStateVector {
  baro_altitude: null | number; // Barometric altitude in meters. Can be null
  callsign: null | string; // Callsign of the vehicle (8 chars). Can be null if no callsign has been received
  category: null | number; // Aircraft category. Can be null
  geo_altitude: null | number; // Geometric altitude in meters. Can be null
  icao24: string; // ICAO24 address of the transponder (hex string)
  last_contact: number; // Unix timestamp for the last update in general
  latitude: null | number; // WGS-84 latitude in decimal degrees. Can be null
  longitude: null | number; // WGS-84 longitude in decimal degrees. Can be null
  on_ground: boolean; // Boolean indicating if the position was retrieved from a surface position report
  origin_country: string; // Country name inferred from the ICAO24 address
  position_source: number; // Origin of this state's position: 0 = ADS-B, 1 = ASTERIX, 2 = MLAT, 3 = FLARM
  sensors: null | number[]; // IDs of the receivers which contributed to this state vector. Can be null
  spi: boolean; // Whether flight status indicates special purpose indicator
  squawk: null | string; // The transponder code (squawk). Can be null
  time_position: null | number; // Unix timestamp for the last position update. Can be null if no position report was received
  true_track: null | number; // True track in decimal degrees (0-359). Can be null
  velocity: null | number; // Velocity over ground in m/s. Can be null
  vertical_rate: null | number; // Vertical rate in m/s. Can be null
}

/**
 * Get all current state vectors from OpenSky Network API
 *
 * @param time Optional time in seconds since epoch (Unix timestamp). Only returns states that have been updated after the given time.
 * @param icao24 Optional one or more ICAO24 transponder addresses represented by a hex string (e.g. abc9f3)
 * @param bbox Optional bounding box for filtering: [min_latitude, max_latitude, min_longitude, max_longitude]
 * @returns Promise with OpenSkyStates response
 */
export const getAllStates = async (
  time?: number,
  icao24?: string[],
  bbox?: [number, number, number, number],
): Promise<OpenSkyStates> => {
  try {
    const url = `${OPENSKY_API_BASE_URL}/states/all`;
    const params: Record<string, any> = {};

    if (time) params.time = time;
    if (icao24 && icao24.length > 0) params.icao24 = icao24.join(',');
    if (bbox) {
      params.lamin = bbox[0];
      params.lamax = bbox[1];
      params.lomin = bbox[2];
      params.lomax = bbox[3];
    }

    const config: any = {
      params,
      timeout: API_TIMEOUT,
    };

    // Add authentication if credentials are provided
    if (OPENSKY_USERNAME && OPENSKY_PASSWORD) {
      config.auth = {
        password: OPENSKY_PASSWORD,
        username: OPENSKY_USERNAME,
      };
    }

    logger.info(`Fetching OpenSky states from ${url}`, { params });

    const response = await axios.get(url, config);

    // Log response summary
    const statesCount = response.data.states ? response.data.states.length : 0;
    logger.info(`Received ${statesCount} states from OpenSky API`);

    return response.data;
  } catch (error: any) {
    // Enhanced error logging
    if (axios.isAxiosError(error)) {
      const status = error.response?.status;
      const statusText = error.response?.statusText;
      const responseData = error.response?.data;

      logger.error(`OpenSky API error (${status} ${statusText}):`, {
        errorMessage: error.message,
        responseData,
        stack: error.stack,
        url: `${OPENSKY_API_BASE_URL}/states/all`,
      });
    } else {
      logger.error(`Error fetching OpenSky states: ${error.message}`, {
        stack: error.stack,
      });
    }

    throw new Error(`Failed to fetch flight states: ${error.message}`);
  }
};

/**
 * Search for flights by callsign
 *
 * @param callsign The flight callsign to search for (e.g., "SWR123")
 * @returns Promise with simplified flight data
 */
export const findFlightsByCallsignForDate = async (
  callsign: string,
  date: string, // YYYY-MM-DD
): Promise<SimplifiedFlightData[]> => {
  logger.info(`Service: findFlightsByCallsignForDate callsign=${callsign}, date=${date}`);

  const callsignLower = callsign.trim().toLowerCase();
  const uniqueIcao24s = new Set<string>();
  const allMatchingFlights: OpenSkyFlight[] = [];

  // 1. Determine begin and end timestamps for the full day (UTC)
  const dayStart = new Date(`${date}T00:00:00.000Z`);
  const dayEnd = new Date(`${date}T23:59:59.999Z`);
  const fullDayBeginEpoch = Math.floor(dayStart.getTime() / 1000);
  const fullDayEndEpoch = Math.floor(dayEnd.getTime() / 1000);

  // Check if the date is in the future
  const currentDate = new Date();
  if (dayStart > currentDate) {
    logger.warn(`Search for future date rejected: ${date}`, {
      callsign,
      currentDate: currentDate.toISOString(),
      date,
      searchDate: dayStart.toISOString(),
    });
    throw new Error(
      `OpenSky API does not provide data for future dates. The date ${date} is in the future.`,
    );
  }

  // 2. Attempt to Find ICAO24 using /flights/all in 2-hour chunks
  logger.info(
    `Step 1: Finding ICAO24s for callsign '${callsignLower}' on ${date} using /flights/all`,
  );
  try {
    for (let i = 0; i < 12; i++) {
      // 12 * 2-hour chunks = 24 hours
      const intervalBegin = new Date(dayStart.getTime() + i * 2 * 60 * 60 * 1000);
      const intervalEnd = new Date(intervalBegin.getTime() + 2 * 60 * 60 * 1000 - 1000); // -1 sec to avoid overlap

      const beginEpochChunk = Math.floor(intervalBegin.getTime() / 1000);
      // Ensure chunk end does not exceed full day end, and begin is not past end
      const currentEndEpoch = Math.min(Math.floor(intervalEnd.getTime() / 1000), fullDayEndEpoch);

      if (beginEpochChunk >= currentEndEpoch || beginEpochChunk > fullDayEndEpoch) {
        logger.debug(
          `Chunk ${i} skipped or ended: beginEpochChunk=${beginEpochChunk}, currentEndEpoch=${currentEndEpoch}, fullDayEndEpoch=${fullDayEndEpoch}`,
        );
        break;
      }

      const url = `${OPENSKY_API_BASE_URL}/flights/all`;
      const params = { begin: beginEpochChunk, end: currentEndEpoch };
      const config: any = { params, timeout: API_TIMEOUT };
      if (OPENSKY_USERNAME && OPENSKY_PASSWORD) {
        config.auth = { password: OPENSKY_PASSWORD, username: OPENSKY_USERNAME };
      }

      logger.debug(
        `Querying /flights/all for ${callsignLower}: chunk ${i}, begin=${beginEpochChunk}, end=${currentEndEpoch}`,
      );

      try {
        const response = await axios.get<OpenSkyFlight[]>(url, config);
        if (response.data && response.data.length > 0) {
          response.data.forEach(flight => {
            if (flight.callsign && flight.callsign.trim().toLowerCase() === callsignLower) {
              if (flight.icao24) {
                uniqueIcao24s.add(flight.icao24);
                logger.debug(
                  `Found potential ICAO24: ${flight.icao24} for callsign ${callsignLower} in chunk ${i}`,
                );
              }
            }
          });
        }
      } catch (chunkError: any) {
        logger.warn(
          `Error fetching /flights/all chunk ${i} for ${callsignLower}: ${chunkError.message}`,
          {
            begin: beginEpochChunk,
            end: currentEndEpoch,
            error: chunkError.response?.data?.message || chunkError.message,
          },
        );
        if (axios.isAxiosError(chunkError) && chunkError.response?.status === 429) {
          logger.error('Rate limit hit while fetching ICAO24s. Aborting further chunks.');
          await delay(1000);
          break;
        }
      }
      if (i < 11) await delay(process.env.NODE_ENV === 'test' ? 10 : 500); // Shorter delay for tests
    }
  } catch (e: any) {
    logger.error(`Outer error during ICAO24 search loop for ${callsignLower}: ${e.message}`, {
      error: e,
    });
  }

  if (uniqueIcao24s.size === 0) {
    logger.warn(
      `No ICAO24s found for callsign '${callsignLower}' on ${date} after checking /flights/all.`,
    );
    return [];
  }

  logger.info(
    `Step 2: Found ${uniqueIcao24s.size} unique ICAO24s: [${Array.from(uniqueIcao24s).join(
      ', ',
    )}]. Fetching details using /flights/aircraft.`,
  );

  for (const icao24 of uniqueIcao24s) {
    const url = `${OPENSKY_API_BASE_URL}/flights/aircraft`;
    const params = { begin: fullDayBeginEpoch, end: fullDayEndEpoch, icao24 };
    const config: any = { params, timeout: API_TIMEOUT };
    if (OPENSKY_USERNAME && OPENSKY_PASSWORD) {
      config.auth = { password: OPENSKY_PASSWORD, username: OPENSKY_USERNAME };
    }

    logger.debug(
      `Querying /flights/aircraft for ICAO24 ${icao24}, callsign ${callsignLower}, date ${date}`,
    );
    try {
      const response = await axios.get<OpenSkyFlight[]>(url, config);
      if (response.data && response.data.length > 0) {
        response.data.forEach(flight => {
          if (flight.callsign && flight.callsign.trim().toLowerCase() === callsignLower) {
            allMatchingFlights.push(flight);
          }
        });
      }
    } catch (aircraftError: any) {
      logger.warn(
        `Error fetching /flights/aircraft for ICAO24 ${icao24}: ${aircraftError.message}`,
        {
          error: aircraftError.response?.data?.message || aircraftError.message,
        },
      );
      if (axios.isAxiosError(aircraftError) && aircraftError.response?.status === 429) {
        logger.error('Rate limit hit while fetching flight details. Aborting for this ICAO24.');
        await delay(process.env.NODE_ENV === 'test' ? 10 : 1000);
      }
    }
    if (
      uniqueIcao24s.size > 1 &&
      Array.from(uniqueIcao24s).indexOf(icao24) < uniqueIcao24s.size - 1
    ) {
      await delay(process.env.NODE_ENV === 'test' ? 10 : 500); // Delay if checking multiple ICAO24s
    }
  }

  if (allMatchingFlights.length === 0) {
    logger.warn(
      `No flights matched callsign '${callsignLower}' for ICAO24s [${Array.from(uniqueIcao24s).join(
        ', ',
      )}] on ${date}.`,
    );
  } else {
    logger.info(
      `Found ${allMatchingFlights.length} final matching flights for callsign '${callsignLower}' on ${date}.`,
    );
  }

  return allMatchingFlights.map(flight => ({
    arrivalAirport: flight.estArrivalAirport || undefined,
    arrivalTime: flight.lastSeen || undefined,
    callsign: flight.callsign ? flight.callsign.trim() : 'Unknown',
    departureAirport: flight.estDepartureAirport || undefined,
    departureTime: flight.firstSeen || undefined,
    icao24: flight.icao24,
    lastSeen: flight.lastSeen || undefined,
  }));
};

/**
 * Get flights for a specific airport (arrivals or departures)
 *
 * @param airport ICAO code of the airport (e.g., "EDDF" for Frankfurt)
 * @param begin Start time in seconds since epoch (Unix timestamp)
 * @param end End time in seconds since epoch (Unix timestamp)
 * @param isArrival If true, get arrivals; if false, get departures
 * @returns Promise with simplified flight data
 */
export const getFlightsByAirport = async (
  airport: string,
  begin: number,
  end: number,
  isArrival: boolean,
): Promise<SimplifiedFlightData[]> => {
  try {
    const url = `${OPENSKY_API_BASE_URL}/flights/${isArrival ? 'arrival' : 'departure'}`;

    const params = {
      airport,
      begin,
      end,
    };

    const config: any = {
      params,
      timeout: API_TIMEOUT,
    };

    // Add authentication if credentials are provided
    if (OPENSKY_USERNAME && OPENSKY_PASSWORD) {
      config.auth = {
        password: OPENSKY_PASSWORD,
        username: OPENSKY_USERNAME,
      };
    }

    logger.info(
      `Fetching flights for airport ${airport} (${isArrival ? 'arrivals' : 'departures'})`,
      {
        airport,
        begin,
        end,
      },
    );

    const response = await axios.get(url, config);
    const flights: OpenSkyFlight[] = response.data;

    logger.info(`Received ${flights.length} flights for airport ${airport}`, {
      airport,
      flightsCount: flights.length,
    });

    // Transform to simplified format
    return flights.map(flight => ({
      arrivalAirport: flight.estArrivalAirport || undefined,
      arrivalTime: flight.lastSeen,
      callsign: flight.callsign ? flight.callsign.trim() : 'Unknown',
      departureAirport: flight.estDepartureAirport || undefined,
      departureTime: flight.firstSeen,
      icao24: flight.icao24,
    }));
  } catch (error: any) {
    // Enhanced error logging
    if (axios.isAxiosError(error)) {
      const status = error.response?.status;
      const statusText = error.response?.statusText;
      const responseData = error.response?.data;

      logger.error(`OpenSky API error (${status} ${statusText}):`, {
        errorMessage: error.message,
        params: { airport, begin, end },
        responseData,
        stack: error.stack,
        url: `${OPENSKY_API_BASE_URL}/flights/${isArrival ? 'arrival' : 'departure'}`,
      });
    } else {
      logger.error(`Error fetching flights by airport: ${error.message}`, {
        airport,
        begin,
        end,
        isArrival,
        stack: error.stack,
      });
    }

    throw new Error(`Failed to fetch airport flights: ${error.message}`);
  }
};

/**
 * Get all flights in a given time interval
 *
 * @param begin Start time in seconds since epoch (Unix timestamp)
 * @param end End time in seconds since epoch (Unix timestamp)
 * @returns Promise with simplified flight data
 */
export const getFlightsByTimeInterval = async (
  begin: number,
  end: number,
): Promise<SimplifiedFlightData[]> => {
  try {
    const url = `${OPENSKY_API_BASE_URL}/flights/all`;

    const params = {
      begin,
      end,
    };

    const config: any = {
      params,
      timeout: API_TIMEOUT,
    };

    // Add authentication if credentials are provided
    if (OPENSKY_USERNAME && OPENSKY_PASSWORD) {
      config.auth = {
        password: OPENSKY_PASSWORD,
        username: OPENSKY_USERNAME,
      };
    }

    logger.info(`Fetching flights for time interval`, {
      begin,
      beginDate: new Date(begin * 1000).toISOString(),
      end,
      endDate: new Date(end * 1000).toISOString(),
    });

    const response = await axios.get(url, config);
    const flights: OpenSkyFlight[] = response.data;

    logger.info(`Received ${flights.length} flights for time interval`, {
      begin,
      end,
      flightsCount: flights.length,
    });

    // Transform to simplified format
    return flights.map(flight => ({
      arrivalAirport: flight.estArrivalAirport || undefined,
      arrivalTime: flight.lastSeen,
      callsign: flight.callsign ? flight.callsign.trim() : 'Unknown',
      departureAirport: flight.estDepartureAirport || undefined,
      departureTime: flight.firstSeen,
      icao24: flight.icao24,
    }));
  } catch (error: any) {
    // Enhanced error logging
    if (axios.isAxiosError(error)) {
      const status = error.response?.status;
      const statusText = error.response?.statusText;
      const responseData = error.response?.data;

      logger.error(`OpenSky API error (${status} ${statusText}):`, {
        errorMessage: error.message,
        params: { begin, end },
        responseData,
        stack: error.stack,
        url: `${OPENSKY_API_BASE_URL}/flights/all`,
      });
    } else {
      logger.error(`Error fetching flights by time interval: ${error.message}`, {
        begin,
        end,
        stack: error.stack,
      });
    }

    throw new Error(`Failed to fetch flights: ${error.message}`);
  }
};
