import { supabaseAdmin } from '../lib/supabase.js';
import logger from '../utils/logger.js';
import { logGDPRAuditEvent } from '../utils/auditLogger.js';
import { encryptSensitiveData, decryptSensitiveData } from '../config/secrets.js';

/**
 * PHASE 3 SECURITY HARDENING: Incident Response and Continuous Monitoring
 */
interface SecurityIncident {
  id: string;
  type: 'BREACH' | 'ATTACK' | 'VIOLATION' | 'ANOMALY' | 'VULNERABILITY';
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  status: 'DETECTED' | 'INVESTIGATING' | 'CONTAINED' | 'RESOLVED';
  description: string;
  affectedSystems: string[];
  detectedAt: Date;
  resolvedAt?: Date;
  responseActions: string[];
  evidence: any[];
  assignedTo?: string;
}

interface SecurityMetricsInternal {
  totalIncidents: number;
  criticalIncidents: number;
  averageResponseTime: number;
  vulnerabilitiesPatched: number;
  complianceScore: number;
  threatLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
}

export interface SecurityMetrics {
  totalEvents: number;
  authenticationEvents: number;
  authorizationFailures: number;
  securityEvents: number;
  highRiskEvents: number;
  criticalEvents: number;
  uniqueUsers: number;
  suspiciousActivities: number;
}

export interface AuditLogFilter {
  startDate?: string;
  endDate?: string;
  eventType?: string;
  action?: string;
  userId?: string;
  outcome?: string;
  riskLevel?: string;
  limit?: number;
  offset?: number;
}

export interface SuspiciousActivity {
  userId: string;
  activityType: string;
  count: number;
  lastOccurrence: string;
  riskScore: number;
  details: any;
}

/**
 * Security Monitoring Service
 * Provides comprehensive security monitoring and analytics capabilities
 */
export class SecurityMonitoringService {
  /**
   * Get security metrics for dashboard
   */
  async getSecurityMetrics(
    timeframe: '1h' | '24h' | '7d' | '30d' = '24h',
  ): Promise<SecurityMetrics> {
    try {
      const timeframeHours = this.getTimeframeHours(timeframe);
      const startTime = new Date(Date.now() - timeframeHours * 60 * 60 * 1000).toISOString();

      // Get audit logs from the timeframe
      const { data: auditLogs, error } = await supabaseAdmin
        .from('audit_logs')
        .select('*')
        .gte('created_at', startTime);

      if (error) {
        throw new Error(`Failed to fetch audit logs: ${error.message}`);
      }

      // Calculate metrics
      const totalEvents = auditLogs.length;
      const authenticationEvents = auditLogs.filter(
        log => log.action.includes('AUTH') || log.action.includes('LOGIN'),
      ).length;

      const authorizationFailures = auditLogs.filter(
        log => log.action.includes('AUTHZ') && log.details?.outcome === 'FAILURE',
      ).length;

      const securityEvents = auditLogs.filter(log => log.action.includes('SECURITY')).length;

      const highRiskEvents = auditLogs.filter(log => log.details?.riskLevel === 'HIGH').length;

      const criticalEvents = auditLogs.filter(log => log.details?.riskLevel === 'CRITICAL').length;

      const uniqueUsers = new Set(auditLogs.map(log => log.user_id)).size;

      const suspiciousActivities = await this.detectSuspiciousActivities(auditLogs);

      return {
        totalEvents,
        authenticationEvents,
        authorizationFailures,
        securityEvents,
        highRiskEvents,
        criticalEvents,
        uniqueUsers,
        suspiciousActivities: suspiciousActivities.length,
      };
    } catch (error: any) {
      logger.error('Failed to get security metrics:', error);
      throw error;
    }
  }

  /**
   * Get filtered audit logs
   */
  async getAuditLogs(filters: AuditLogFilter = {}): Promise<any[]> {
    try {
      let query = supabaseAdmin
        .from('audit_logs')
        .select('*')
        .order('created_at', { ascending: false });

      // Apply filters
      if (filters.startDate) {
        query = query.gte('created_at', filters.startDate);
      }
      if (filters.endDate) {
        query = query.lte('created_at', filters.endDate);
      }
      if (filters.userId) {
        query = query.eq('user_id', filters.userId);
      }
      if (filters.action) {
        query = query.ilike('action', `%${filters.action}%`);
      }

      // Apply pagination
      if (filters.limit) {
        query = query.limit(filters.limit);
      }
      if (filters.offset) {
        query = query.range(filters.offset, filters.offset + (filters.limit || 50) - 1);
      }

      const { data, error } = await query;

      if (error) {
        throw new Error(`Failed to fetch audit logs: ${error.message}`);
      }

      return data || [];
    } catch (error: any) {
      logger.error('Failed to get audit logs:', error);
      throw error;
    }
  }

  /**
   * Detect suspicious activities
   */
  async detectSuspiciousActivities(auditLogs?: any[]): Promise<SuspiciousActivity[]> {
    try {
      if (!auditLogs) {
        const { data, error } = await supabaseAdmin
          .from('audit_logs')
          .select('*')
          .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString());

        if (error) {
          throw new Error(`Failed to fetch audit logs: ${error.message}`);
        }
        auditLogs = data || [];
      }

      const suspiciousActivities: SuspiciousActivity[] = [];

      // Detect multiple failed login attempts
      const failedLogins = auditLogs.filter(
        log => log.action.includes('LOGIN') && log.details?.outcome === 'FAILURE',
      );

      const failedLoginsByUser = this.groupByUser(failedLogins);
      for (const [userId, attempts] of Object.entries(failedLoginsByUser)) {
        if ((attempts as any[]).length >= 5) {
          suspiciousActivities.push({
            userId,
            activityType: 'MULTIPLE_FAILED_LOGINS',
            count: (attempts as any[]).length,
            lastOccurrence: (attempts as any[])[0].created_at,
            riskScore: Math.min(100, (attempts as any[]).length * 10),
            details: { attempts: (attempts as any[]).length },
          });
        }
      }

      // Detect unusual access patterns
      const accessLogs = auditLogs.filter(
        log => log.action.includes('DATA_ACCESS') || log.action.includes('API_'),
      );

      const accessByUser = this.groupByUser(accessLogs);
      for (const [userId, accesses] of Object.entries(accessByUser)) {
        if ((accesses as any[]).length >= 100) {
          // More than 100 API calls in 24h
          suspiciousActivities.push({
            userId,
            activityType: 'UNUSUAL_ACCESS_PATTERN',
            count: (accesses as any[]).length,
            lastOccurrence: (accesses as any[])[0].created_at,
            riskScore: Math.min(100, (accesses as any[]).length / 2),
            details: { apiCalls: (accesses as any[]).length },
          });
        }
      }

      // Detect privilege escalation attempts
      const authzFailures = auditLogs.filter(
        log => log.action.includes('AUTHZ') && log.details?.outcome === 'FAILURE',
      );

      const authzFailuresByUser = this.groupByUser(authzFailures);
      for (const [userId, failures] of Object.entries(authzFailuresByUser)) {
        if ((failures as any[]).length >= 10) {
          suspiciousActivities.push({
            userId,
            activityType: 'PRIVILEGE_ESCALATION_ATTEMPT',
            count: (failures as any[]).length,
            lastOccurrence: (failures as any[])[0].created_at,
            riskScore: Math.min(100, (failures as any[]).length * 5),
            details: { authzFailures: (failures as any[]).length },
          });
        }
      }

      return suspiciousActivities;
    } catch (error: any) {
      logger.error('Failed to detect suspicious activities:', error);
      return [];
    }
  }

  /**
   * Get real-time security events (last 5 minutes)
   */
  async getRealTimeEvents(): Promise<any[]> {
    try {
      const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000).toISOString();

      const { data, error } = await supabaseAdmin
        .from('audit_logs')
        .select('*')
        .gte('created_at', fiveMinutesAgo)
        .order('created_at', { ascending: false });

      if (error) {
        throw new Error(`Failed to fetch real-time events: ${error.message}`);
      }

      return data || [];
    } catch (error: any) {
      logger.error('Failed to get real-time events:', error);
      return [];
    }
  }

  /**
   * PHASE 2 SECURITY HARDENING: Data Protection Monitoring
   * Monitors sensitive data access and encryption/decryption operations
   */
  async monitorDataAccess(
    userId: string,
    dataType: string,
    operation: 'READ' | 'WRITE' | 'ENCRYPT' | 'DECRYPT',
    details?: any,
  ): Promise<void> {
    const riskLevel = this.assessDataAccessRisk(dataType, operation);

    logGDPRAuditEvent({
      eventType: 'DATA_PROTECTION',
      action: `DATA_${operation}_${dataType.toUpperCase()}`,
      userId,
      outcome: 'SUCCESS',
      message: `Data ${operation.toLowerCase()} operation on ${dataType}`,
      details: {
        dataType,
        operation,
        ...details,
      },
      dataClassification: this.classifyDataType(dataType),
      personalDataInvolved: this.isPersonalData(dataType),
      gdprLawfulBasis: 'legitimate_interests',
      riskLevel,
    });
  }

  /**
   * Monitor encryption/decryption operations
   */
  async monitorEncryptionOperation(
    operation: 'ENCRYPT' | 'DECRYPT',
    dataType: string,
    success: boolean,
    userId?: string,
    error?: string,
  ): Promise<void> {
    logGDPRAuditEvent({
      eventType: 'SECURITY',
      action: `ENCRYPTION_${operation}`,
      userId,
      outcome: success ? 'SUCCESS' : 'FAILURE',
      message: `Data ${operation.toLowerCase()} ${success ? 'successful' : 'failed'}`,
      details: {
        dataType,
        operation,
        error: error ? '[REDACTED]' : undefined, // Don't log actual error details
      },
      dataClassification: 'RESTRICTED',
      riskLevel: success ? 'LOW' : 'HIGH',
      errorCode: error ? 'ENCRYPTION_FAILURE' : undefined,
    });
  }

  /**
   * Detect data protection violations
   */
  async detectDataProtectionViolations(): Promise<SuspiciousActivity[]> {
    try {
      const violations: SuspiciousActivity[] = [];
      const last24Hours = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();

      const { data: auditLogs, error } = await supabaseAdmin
        .from('audit_logs')
        .select('*')
        .gte('created_at', last24Hours)
        .or('action.ilike.%DATA_%,action.ilike.%ENCRYPTION_%');

      if (error) {
        logger.error('Failed to fetch data protection audit logs:', error);
        return violations;
      }

      // Detect excessive data access
      const dataAccessByUser = this.groupByUser(
        auditLogs.filter(log => log.action.includes('DATA_READ')),
      );

      for (const [userId, accesses] of Object.entries(dataAccessByUser)) {
        if ((accesses as any[]).length > 50) {
          // More than 50 data reads in 24h
          violations.push({
            userId,
            activityType: 'EXCESSIVE_DATA_ACCESS',
            count: (accesses as any[]).length,
            lastOccurrence: (accesses as any[])[0].created_at,
            riskScore: Math.min(100, (accesses as any[]).length * 2),
            details: { dataAccesses: (accesses as any[]).length },
          });
        }
      }

      // Detect encryption failures
      const encryptionFailures = auditLogs.filter(
        log => log.action.includes('ENCRYPTION_') && log.details?.outcome === 'FAILURE',
      );

      if (encryptionFailures.length > 5) {
        violations.push({
          userId: 'SYSTEM',
          activityType: 'ENCRYPTION_FAILURES',
          count: encryptionFailures.length,
          lastOccurrence: encryptionFailures[0].created_at,
          riskScore: Math.min(100, encryptionFailures.length * 10),
          details: { encryptionFailures: encryptionFailures.length },
        });
      }

      return violations;
    } catch (error: any) {
      logger.error('Failed to detect data protection violations:', error);
      return [];
    }
  }

  /**
   * Log security monitoring access
   */
  async logMonitoringAccess(userId: string, action: string, details?: any): Promise<void> {
    logGDPRAuditEvent({
      eventType: 'SECURITY',
      action: `MONITORING_${action}`,
      userId,
      outcome: 'SUCCESS',
      message: `Security monitoring action: ${action}`,
      details,
      dataClassification: 'RESTRICTED',
      riskLevel: 'MEDIUM',
    });
  }

  /**
   * Assess risk level for data access operations
   */
  private assessDataAccessRisk(
    dataType: string,
    operation: string,
  ): 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' {
    const sensitiveDataTypes = ['employee', 'vehicle', 'financial', 'personal'];
    const highRiskOperations = ['DECRYPT', 'WRITE'];

    if (
      sensitiveDataTypes.includes(dataType.toLowerCase()) &&
      highRiskOperations.includes(operation)
    ) {
      return 'HIGH';
    }
    if (
      sensitiveDataTypes.includes(dataType.toLowerCase()) ||
      highRiskOperations.includes(operation)
    ) {
      return 'MEDIUM';
    }
    return 'LOW';
  }

  /**
   * Classify data type for GDPR compliance
   */
  classifyDataType(dataType: string): 'PUBLIC' | 'INTERNAL' | 'CONFIDENTIAL' | 'RESTRICTED' {
    const restrictedTypes = ['employee', 'financial', 'personal'];
    const confidentialTypes = ['vehicle', 'service', 'task'];

    if (restrictedTypes.includes(dataType.toLowerCase())) {
      return 'RESTRICTED';
    }
    if (confidentialTypes.includes(dataType.toLowerCase())) {
      return 'CONFIDENTIAL';
    }
    return 'INTERNAL';
  }

  /**
   * Determine if data type contains personal information
   */
  private isPersonalData(dataType: string): boolean {
    const personalDataTypes = ['employee', 'personal', 'contact'];
    return personalDataTypes.includes(dataType.toLowerCase());
  }

  /**
   * PHASE 3 SECURITY HARDENING: Incident Response Management
   * Manages security incidents from detection to resolution
   */
  async createSecurityIncident(
    type: SecurityIncident['type'],
    severity: SecurityIncident['severity'],
    description: string,
    affectedSystems: string[],
    evidence: any[] = [],
  ): Promise<string> {
    const incident: SecurityIncident = {
      id: `INC-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      type,
      severity,
      status: 'DETECTED',
      description,
      affectedSystems,
      detectedAt: new Date(),
      responseActions: [],
      evidence,
    };

    // Log incident creation
    logGDPRAuditEvent({
      eventType: 'SECURITY',
      action: 'INCIDENT_CREATED',
      outcome: 'SUCCESS',
      message: `Security incident created: ${type} - ${severity}`,
      details: {
        incidentId: incident.id,
        type,
        severity,
        affectedSystems,
      },
      riskLevel: severity,
      dataClassification: 'RESTRICTED',
    });

    // Auto-assign based on severity
    if (severity === 'CRITICAL') {
      incident.assignedTo = 'security-team-lead';
      await this.triggerEmergencyResponse(incident);
    } else if (severity === 'HIGH') {
      incident.assignedTo = 'security-team';
      await this.triggerHighPriorityResponse(incident);
    }

    // Store incident (in production, this would be in a dedicated incident management system)
    logger.error('Security incident created', {
      incident,
      service: 'security-monitoring',
    });

    return incident.id;
  }

  /**
   * Trigger emergency response for critical incidents
   */
  private async triggerEmergencyResponse(incident: SecurityIncident): Promise<void> {
    // In production, this would:
    // 1. Send immediate alerts to security team
    // 2. Trigger automated containment procedures
    // 3. Notify management
    // 4. Activate incident response team

    logger.error('CRITICAL SECURITY INCIDENT - Emergency response triggered', {
      incidentId: incident.id,
      type: incident.type,
      description: incident.description,
      service: 'security-monitoring',
    });

    // Log emergency response activation
    logGDPRAuditEvent({
      eventType: 'SECURITY',
      action: 'EMERGENCY_RESPONSE_ACTIVATED',
      outcome: 'SUCCESS',
      message: 'Emergency response activated for critical security incident',
      details: {
        incidentId: incident.id,
        responseTime: 'IMMEDIATE',
      },
      riskLevel: 'CRITICAL',
      dataClassification: 'RESTRICTED',
    });
  }

  /**
   * Trigger high priority response
   */
  private async triggerHighPriorityResponse(incident: SecurityIncident): Promise<void> {
    logger.warn('HIGH PRIORITY SECURITY INCIDENT - Response team notified', {
      incidentId: incident.id,
      type: incident.type,
      description: incident.description,
      service: 'security-monitoring',
    });

    // Log high priority response
    logGDPRAuditEvent({
      eventType: 'SECURITY',
      action: 'HIGH_PRIORITY_RESPONSE_ACTIVATED',
      outcome: 'SUCCESS',
      message: 'High priority response activated for security incident',
      details: {
        incidentId: incident.id,
        responseTime: 'WITHIN_1_HOUR',
      },
      riskLevel: 'HIGH',
      dataClassification: 'RESTRICTED',
    });
  }

  /**
   * Generate security metrics and KPIs
   */
  async generateSecurityMetrics(): Promise<SecurityMetricsInternal> {
    try {
      const last30Days = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString();

      // Get security events from audit logs
      const { data: securityEvents, error } = await supabaseAdmin
        .from('audit_logs')
        .select('*')
        .gte('created_at', last30Days)
        .or('event_type.eq.SECURITY,action.ilike.%INCIDENT%,action.ilike.%VIOLATION%');

      if (error) {
        logger.error('Failed to fetch security events for metrics:', error);
        throw error;
      }

      // Calculate metrics
      const totalIncidents = securityEvents.filter(event =>
        event.action.includes('INCIDENT_CREATED'),
      ).length;

      const criticalIncidents = securityEvents.filter(
        event =>
          event.action.includes('INCIDENT_CREATED') && event.details?.severity === 'CRITICAL',
      ).length;

      const vulnerabilitiesPatched = securityEvents.filter(event =>
        event.action.includes('VULNERABILITY_PATCHED'),
      ).length;

      // Calculate compliance score (simplified)
      const complianceEvents = securityEvents.filter(
        event => event.event_type === 'DATA_PROTECTION' || event.action.includes('COMPLIANCE'),
      );
      const complianceScore = Math.max(0, 100 - criticalIncidents * 10 - totalIncidents * 2);

      // Determine threat level
      let threatLevel: SecurityMetricsInternal['threatLevel'] = 'LOW';
      if (criticalIncidents > 0) threatLevel = 'CRITICAL';
      else if (totalIncidents > 5) threatLevel = 'HIGH';
      else if (totalIncidents > 2) threatLevel = 'MEDIUM';

      const metrics: SecurityMetricsInternal = {
        totalIncidents,
        criticalIncidents,
        averageResponseTime: 15, // minutes (would be calculated from actual response times)
        vulnerabilitiesPatched,
        complianceScore,
        threatLevel,
      };

      // Log metrics generation
      logGDPRAuditEvent({
        eventType: 'SECURITY',
        action: 'SECURITY_METRICS_GENERATED',
        outcome: 'SUCCESS',
        message: 'Security metrics generated for reporting',
        details: metrics,
        riskLevel: 'LOW',
        dataClassification: 'INTERNAL',
      });

      return metrics;
    } catch (error: any) {
      logger.error('Failed to generate security metrics:', error);
      throw new Error(`Security metrics generation failed: ${error.message}`);
    }
  }

  /**
   * Continuous security assessment
   */
  async performContinuousSecurityAssessment(): Promise<{
    riskScore: number;
    recommendations: string[];
    criticalFindings: string[];
  }> {
    const findings: string[] = [];
    const recommendations: string[] = [];
    let riskScore = 0;

    try {
      // Check for recent security violations
      const violations = await this.detectDataProtectionViolations();
      if (violations.length > 0) {
        riskScore += violations.length * 10;
        findings.push(`${violations.length} data protection violations detected`);
        recommendations.push('Review and address data protection violations immediately');
      }

      // Check for suspicious activities
      const suspiciousActivities = await this.detectSuspiciousActivities();
      if (suspiciousActivities.length > 0) {
        riskScore += suspiciousActivities.length * 5;
        findings.push(`${suspiciousActivities.length} suspicious activities detected`);
        recommendations.push(
          'Investigate suspicious activities and implement additional monitoring',
        );
      }

      // Check authentication security
      const authIssues = await this.assessAuthenticationSecurity();
      if (authIssues.length > 0) {
        riskScore += authIssues.length * 15;
        findings.push(...authIssues);
        recommendations.push('Strengthen authentication mechanisms and review access controls');
      }

      // Generate final assessment
      const assessment = {
        riskScore: Math.min(100, riskScore),
        recommendations,
        criticalFindings: findings.filter(
          finding => finding.includes('critical') || finding.includes('violation'),
        ),
      };

      // Log assessment
      logGDPRAuditEvent({
        eventType: 'SECURITY',
        action: 'CONTINUOUS_SECURITY_ASSESSMENT',
        outcome: 'SUCCESS',
        message: 'Continuous security assessment completed',
        details: assessment,
        riskLevel: riskScore > 50 ? 'HIGH' : riskScore > 25 ? 'MEDIUM' : 'LOW',
        dataClassification: 'RESTRICTED',
      });

      return assessment;
    } catch (error: any) {
      logger.error('Continuous security assessment failed:', error);
      throw new Error(`Security assessment failed: ${error.message}`);
    }
  }

  /**
   * Assess authentication security
   */
  private async assessAuthenticationSecurity(): Promise<string[]> {
    const issues: string[] = [];

    try {
      const last24Hours = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();

      const { data: authEvents, error } = await supabaseAdmin
        .from('audit_logs')
        .select('*')
        .gte('created_at', last24Hours)
        .eq('event_type', 'AUTH');

      if (error) {
        logger.error('Failed to fetch auth events for assessment:', error);
        return issues;
      }

      // Check for failed login attempts
      const failedLogins = authEvents.filter(
        event => event.action.includes('LOGIN') && event.outcome === 'FAILURE',
      );

      if (failedLogins.length > 10) {
        issues.push(`High number of failed login attempts: ${failedLogins.length}`);
      }

      // Check for token refresh failures
      const tokenFailures = authEvents.filter(
        event => event.action.includes('TOKEN_REFRESH') && event.outcome === 'FAILURE',
      );

      if (tokenFailures.length > 5) {
        issues.push(`Multiple token refresh failures: ${tokenFailures.length}`);
      }

      return issues;
    } catch (error: any) {
      logger.error('Authentication security assessment failed:', error);
      return ['Authentication security assessment failed'];
    }
  }

  private getTimeframeHours(timeframe: string): number {
    switch (timeframe) {
      case '1h':
        return 1;
      case '24h':
        return 24;
      case '7d':
        return 24 * 7;
      case '30d':
        return 24 * 30;
      default:
        return 24;
    }
  }

  private groupByUser(logs: any[]): Record<string, any[]> {
    return logs.reduce((acc, log) => {
      const userId = log.user_id;
      if (!acc[userId]) {
        acc[userId] = [];
      }
      acc[userId].push(log);
      return acc;
    }, {});
  }
}

export const securityMonitoringService = new SecurityMonitoringService();
