/**
 * @file Generic dashboard layout component
 * @module components/dashboard/DashboardLayout
 */

'use client';

import React from 'react';

import { DashboardProps } from './types';
import ErrorBoundary from '@/components/error-boundaries/ErrorBoundary';
import { cn } from '@/lib/utils';

/**
 * Generic dashboard layout component that provides consistent structure
 * for all dashboard-style pages in the application.
 * 
 * Features:
 * - Consistent spacing and container management
 * - Error boundary protection
 * - Responsive design foundation
 * - Flexible content composition
 * 
 * @param props - Dashboard layout props
 * @returns JSX element representing the dashboard layout
 */
export const DashboardLayout: React.FC<DashboardProps> = ({
  children,
  className = '',
  config,
}) => {
  return (
    <ErrorBoundary>
      <div className={cn('min-h-screen bg-background', className)}>
        <main className="flex-1">
          <div className="container mx-auto space-y-6 px-4 py-6 sm:px-6 lg:px-8">
            {children}
          </div>
        </main>
      </div>
    </ErrorBoundary>
  );
};

/**
 * Dashboard page wrapper that provides the standard page structure
 */
export const DashboardPage: React.FC<DashboardProps> = ({
  children,
  className = '',
  config,
}) => {
  return (
    <DashboardLayout config={config} className={className}>
      <div className="space-y-8">
        {children}
      </div>
    </DashboardLayout>
  );
};

export default DashboardLayout;
