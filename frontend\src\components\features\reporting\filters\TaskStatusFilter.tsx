/**
 * @file Task Status Filter Component - Phase 2 Implementation
 * @description Task status filter component following SOLID principles
 * 
 * SOLID Principles Applied:
 * - SRP: Single responsibility of filtering tasks by status
 * - OCP: Open for extension via additional filter options
 * - DIP: Depends on existing filter store and component abstractions
 * 
 * Architecture Compliance:
 * - Follows existing filter component patterns
 * - Integrates with established filter store
 * - Uses consistent styling and behavior
 * - Maintains component composition principles
 */

'use client';

import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { TaskStatusPrisma } from '@/lib/types/domain';
import { useReportingFilters, useReportingFiltersActions } from '../data/stores';
import { CheckSquare, Filter, X } from 'lucide-react';

interface TaskStatusFilterProps {
  className?: string;
  showLabel?: boolean;
  variant?: 'dropdown' | 'inline';
}

/**
 * @component TaskStatusFilter
 * @description Task status filter following existing filter patterns
 *
 * Responsibilities:
 * - Provide task status filtering interface
 * - Integrate with existing filter store
 * - Follow established filter component patterns
 * - Maintain consistent styling and behavior
 *
 * SOLID Principles Applied:
 * - SRP: Single responsibility of task status filtering
 * - OCP: Open for extension via props configuration
 * - DIP: Depends on filter store abstractions
 */
export const TaskStatusFilter: React.FC<TaskStatusFilterProps> = ({
  className = '',
  showLabel = true,
  variant = 'dropdown',
}) => {
  const filters = useReportingFilters();
  const { setFilters } = useReportingFiltersActions();

  // Available task statuses with display information
  const taskStatuses: Array<{
    value: TaskStatusPrisma;
    label: string;
    color: string;
    description: string;
  }> = [
    {
      value: 'Pending',
      label: 'Pending',
      color: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
      description: 'Tasks waiting to be assigned',
    },
    {
      value: 'Assigned',
      label: 'Assigned',
      color: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
      description: 'Tasks assigned to employees',
    },
    {
      value: 'In_Progress',
      label: 'In Progress',
      color: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300',
      description: 'Tasks currently being worked on',
    },
    {
      value: 'Completed',
      label: 'Completed',
      color: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
      description: 'Successfully completed tasks',
    },
    {
      value: 'Cancelled',
      label: 'Cancelled',
      color: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
      description: 'Cancelled or abandoned tasks',
    },
  ];

  // Get current selected statuses
  const selectedStatuses = filters.taskStatus || [];

  // Handle status selection
  const handleStatusToggle = (status: TaskStatusPrisma) => {
    const currentStatuses = selectedStatuses;
    const isSelected = currentStatuses.includes(status);
    
    let newStatuses: TaskStatusPrisma[];
    if (isSelected) {
      newStatuses = currentStatuses.filter(s => s !== status);
    } else {
      newStatuses = [...currentStatuses, status];
    }
    
    setFilters({
      taskStatus: newStatuses,
    });
  };

  // Clear all status filters
  const handleClearAll = () => {
    setFilters({
      taskStatus: [],
    });
  };

  // Select all statuses
  const handleSelectAll = () => {
    setFilters({
      taskStatus: taskStatuses.map(s => s.value),
    });
  };

  // Render inline variant
  if (variant === 'inline') {
    return (
      <div className={`space-y-3 ${className}`}>
        {showLabel && (
          <div className="flex items-center justify-between">
            <label className="text-sm font-medium">Task Status</label>
            {selectedStatuses.length > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleClearAll}
                className="h-6 px-2 text-xs"
              >
                <X className="h-3 w-3 mr-1" />
                Clear
              </Button>
            )}
          </div>
        )}
        
        <div className="space-y-2">
          {taskStatuses.map((status) => {
            const isSelected = selectedStatuses.includes(status.value);
            
            return (
              <div
                key={status.value}
                className="flex items-center space-x-3 p-2 rounded-lg hover:bg-muted/50 cursor-pointer"
                onClick={() => handleStatusToggle(status.value)}
              >
                <Checkbox
                  checked={isSelected}
                  onChange={() => handleStatusToggle(status.value)}
                />
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2">
                    <Badge className={status.color}>
                      {status.label}
                    </Badge>
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">
                    {status.description}
                  </p>
                </div>
              </div>
            );
          })}
        </div>
        
        {selectedStatuses.length > 0 && (
          <div className="pt-2 border-t">
            <p className="text-xs text-muted-foreground">
              {selectedStatuses.length} status{selectedStatuses.length !== 1 ? 'es' : ''} selected
            </p>
          </div>
        )}
      </div>
    );
  }

  // Render dropdown variant (default)
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" className={className}>
          <Filter className="mr-2 h-4 w-4" />
          Task Status
          {selectedStatuses.length > 0 && (
            <Badge variant="secondary" className="ml-2 h-5 px-1.5 text-xs">
              {selectedStatuses.length}
            </Badge>
          )}
        </Button>
      </DropdownMenuTrigger>
      
      <DropdownMenuContent className="w-64" align="start">
        <DropdownMenuLabel className="flex items-center justify-between">
          <span className="flex items-center gap-2">
            <CheckSquare className="h-4 w-4" />
            Task Status Filter
          </span>
          {selectedStatuses.length > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClearAll}
              className="h-6 px-2 text-xs"
            >
              Clear
            </Button>
          )}
        </DropdownMenuLabel>
        
        <DropdownMenuSeparator />
        
        <div className="p-2">
          <div className="flex gap-2 mb-3">
            <Button
              variant="outline"
              size="sm"
              onClick={handleSelectAll}
              className="flex-1 h-7 text-xs"
            >
              Select All
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleClearAll}
              className="flex-1 h-7 text-xs"
            >
              Clear All
            </Button>
          </div>
          
          <div className="space-y-2">
            {taskStatuses.map((status) => {
              const isSelected = selectedStatuses.includes(status.value);
              
              return (
                <DropdownMenuItem
                  key={status.value}
                  className="flex items-center space-x-3 cursor-pointer"
                  onClick={() => handleStatusToggle(status.value)}
                >
                  <Checkbox
                    checked={isSelected}
                    onChange={() => handleStatusToggle(status.value)}
                  />
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2">
                      <Badge className={status.color}>
                        {status.label}
                      </Badge>
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">
                      {status.description}
                    </p>
                  </div>
                </DropdownMenuItem>
              );
            })}
          </div>
          
          {selectedStatuses.length > 0 && (
            <>
              <DropdownMenuSeparator />
              <div className="p-2 text-xs text-muted-foreground">
                {selectedStatuses.length} status{selectedStatuses.length !== 1 ? 'es' : ''} selected
              </div>
            </>
          )}
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};