'use client';

import * as L from 'leaflet';
import {
  Activity,
  AlertTriangle,
  Car,
  CheckCircle2,
  Clock,
  Fuel,
  MapPin,
  Navigation,
  Phone,
} from 'lucide-react';
import React, { useEffect, useRef, useState } from 'react';

import type { Employee, Vehicle } from '@/lib/types/domain';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/hooks/utils/use-toast';
import { useVehicle } from '@/lib/stores/queries/useVehicles';

interface DriverInformationProps {
  employee: Employee;
  refreshInterval?: number; // in milliseconds
}

// Types for real-time data
interface DriverLocation {
  accuracy?: number;
  heading?: number;
  latitude: number;
  longitude: number;
  speed?: number;
  timestamp: Date;
}

interface DriverStatus {
  currentTask?: string;
  estimatedArrival?: Date;
  lastUpdate: Date;
  status: 'active' | 'break' | 'emergency' | 'offline';
}

// Extended Vehicle interface with additional properties for real-time tracking
interface ExtendedVehicle extends Vehicle {
  currentLocation?: string;
  fuelLevel?: number;
  mileage?: number;
  status?: 'Available' | 'In Use' | 'Maintenance' | 'Out of Service';
}

const DriverInformation: React.FC<DriverInformationProps> = ({
  employee,
  refreshInterval = 30_000, // 30 seconds default
}) => {
  const { toast } = useToast();
  const [vehicle, setVehicle] = useState<ExtendedVehicle | null>(null);
  const [location, setLocation] = useState<DriverLocation | null>(null);
  const [driverStatus, setDriverStatus] = useState<DriverStatus>({
    lastUpdate: new Date(),
    status: 'active',
  });
  const [isTracking, setIsTracking] = useState(false);
  const [lastLocationUpdate, setLastLocationUpdate] = useState<Date | null>(
    null
  );
  const mapContainerRef = useRef<HTMLDivElement>(null);
  const mapRef = useRef<L.Map | null>(null);
  const markerRef = useRef<L.Marker | null>(null);

  // Note: Vehicle assignment is now context-specific (per task/delegation)
  // This component would need to receive vehicle information from the parent
  // or fetch it based on current assignments
  useEffect(() => {
    // For now, we'll show a placeholder or remove vehicle section
    // In a real implementation, this would fetch current vehicle assignments
    // from active tasks/delegations for this driver
    console.log(
      'Vehicle assignment is now context-specific for employee:',
      employee.id
    );
  }, [employee.id, employee.currentLocation]);

  // Initialize Leaflet map
  useEffect(() => {
    const initMap = async () => {
      if (!mapContainerRef.current) return;

      try {
        // Create map
        const map = L.map(mapContainerRef.current).setView(
          [40.7128, -74.006],
          13
        ); // Default to NYC

        // Add tile layer
        L.tileLayer('https://tile.openstreetmap.org/{z}/{x}/{y}.png', {
          attribution:
            '&copy; <a href="http://www.openstreetmap.org/copyright">OpenStreetMap</a>',
          maxZoom: 19,
        }).addTo(map);

        mapRef.current = map;

        // Add driver marker with default icon
        const marker = L.marker([40.7128, -74.006])
          .addTo(map)
          .bindPopup(`${employee.name} - ${employee.role}`);

        markerRef.current = marker;
      } catch (error) {
        console.error('Failed to initialize map:', error);
        toast({
          description: 'Failed to load map component.',
          title: 'Map Error',
          variant: 'destructive',
        });
      }
    };

    initMap();

    return () => {
      if (mapRef.current) {
        mapRef.current.remove();
      }
    };
  }, [employee.name, employee.role, toast]);

  // Simulate real-time location updates
  useEffect(() => {
    if (!isTracking) return;

    const interval = setInterval(() => {
      // Simulate GPS data - in real app, this would come from WebSocket/Socket.IO
      const simulatedLocation: DriverLocation = {
        accuracy: Math.floor(Math.random() * 10) + 5,
        heading: Math.floor(Math.random() * 360),
        latitude: 40.7128 + (Math.random() - 0.5) * 0.01,
        longitude: -74.006 + (Math.random() - 0.5) * 0.01,
        speed: Math.floor(Math.random() * 60) + 20,
        timestamp: new Date(),
      };

      setLocation(simulatedLocation);
      setLastLocationUpdate(new Date());

      // Update map marker position
      if (mapRef.current && markerRef.current) {
        const newLatLng = L.latLng(
          simulatedLocation.latitude,
          simulatedLocation.longitude
        );
        markerRef.current.setLatLng(newLatLng);
        mapRef.current.setView(newLatLng);
      }

      // Simulate status updates
      const statuses: DriverStatus['status'][] = [
        'active',
        'break',
        'active',
        'active',
      ];
      const randomStatus =
        statuses[Math.floor(Math.random() * statuses.length)];
      setDriverStatus(prev => ({
        ...prev,
        currentTask: prev.currentTask || 'No current task',
        lastUpdate: new Date(),
        status: randomStatus as NonNullable<DriverStatus['status']>,
      }));
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [isTracking, refreshInterval]);

  const startTracking = () => {
    setIsTracking(true);
    toast({
      description: `Real-time tracking enabled for ${employee.name}`,
      title: 'Tracking Started',
      variant: 'default',
    });
  };

  const stopTracking = () => {
    setIsTracking(false);
    toast({
      description: `Real-time tracking disabled for ${employee.name}`,
      title: 'Tracking Stopped',
      variant: 'default',
    });
  };

  const getStatusColor = (status: DriverStatus['status']) => {
    switch (status) {
      case 'active': {
        return 'bg-green-500';
      }
      case 'break': {
        return 'bg-yellow-500';
      }
      case 'emergency': {
        return 'bg-red-500';
      }
      case 'offline': {
        return 'bg-gray-500';
      }
      default: {
        return 'bg-gray-500';
      }
    }
  };

  const getStatusIcon = (status: DriverStatus['status']) => {
    switch (status) {
      case 'active': {
        return <CheckCircle2 className="size-4" />;
      }
      case 'break': {
        return <Clock className="size-4" />;
      }
      case 'emergency': {
        return <AlertTriangle className="size-4" />;
      }
      case 'offline': {
        return <Activity className="size-4" />;
      }
      default: {
        return <Activity className="size-4" />;
      }
    }
  };

  return (
    <div className="space-y-6">
      {/* Status and Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center gap-2">
              <Activity className="size-5" />
              Driver Status
            </span>
            <div className="flex gap-2">
              {isTracking ? (
                <Button onClick={stopTracking} size="sm" variant="outline">
                  Stop Tracking
                </Button>
              ) : (
                <Button onClick={startTracking} size="sm">
                  <Navigation className="mr-2 size-4" />
                  Start Tracking
                </Button>
              )}
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
            <div className="flex items-center gap-3">
              <div
                className={`size-3 rounded-full ${getStatusColor(
                  driverStatus.status
                )}`}
              />
              <div>
                <div className="flex items-center gap-2">
                  {getStatusIcon(driverStatus.status)}
                  <span className="font-medium capitalize">
                    {driverStatus.status}
                  </span>
                </div>
                <p className="text-sm text-muted-foreground">
                  Updated: {driverStatus.lastUpdate.toLocaleTimeString()}
                </p>
              </div>
            </div>

            {driverStatus.currentTask && (
              <div className="flex items-center gap-2">
                <MapPin className="size-4 text-blue-500" />
                <div>
                  <p className="font-medium">Current Task</p>
                  <p className="text-sm text-muted-foreground">
                    {driverStatus.currentTask}
                  </p>
                </div>
              </div>
            )}

            {lastLocationUpdate && (
              <div className="flex items-center gap-2">
                <Clock className="size-4 text-green-500" />
                <div>
                  <p className="font-medium">Last Location Update</p>
                  <p className="text-sm text-muted-foreground">
                    {lastLocationUpdate.toLocaleTimeString()}
                  </p>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Real-time Location */}
      {location && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MapPin className="size-5" />
              Live Location
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="mb-4 grid grid-cols-2 gap-4 md:grid-cols-4">
              <div>
                <p className="text-sm font-medium">Coordinates</p>
                <p className="text-sm text-muted-foreground">
                  {location.latitude.toFixed(6)},{' '}
                  {location.longitude.toFixed(6)}
                </p>
              </div>
              {location.speed && (
                <div>
                  <p className="text-sm font-medium">Speed</p>
                  <p className="text-sm text-muted-foreground">
                    {location.speed} mph
                  </p>
                </div>
              )}
              {location.accuracy && (
                <div>
                  <p className="text-sm font-medium">Accuracy</p>
                  <p className="text-sm text-muted-foreground">
                    ±{location.accuracy}m
                  </p>
                </div>
              )}
              {location.heading && (
                <div>
                  <p className="text-sm font-medium">Heading</p>
                  <p className="text-sm text-muted-foreground">
                    {location.heading}°
                  </p>
                </div>
              )}
            </div>

            {/* Map Container */}
            <div
              className="h-64 w-full rounded-lg border"
              ref={mapContainerRef}
              style={{ minHeight: '250px' }}
            />
          </CardContent>
        </Card>
      )}

      {/* Vehicle Information */}
      {vehicle && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Car className="size-5" />
              Assigned Vehicle
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div className="space-y-3">
                <div>
                  <p className="text-sm font-medium">Vehicle</p>
                  <p className="text-lg">
                    {vehicle.make} {vehicle.model} ({vehicle.year})
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium">License Plate</p>
                  <p className="w-fit rounded bg-muted px-2 py-1 font-mono text-sm">
                    {vehicle.licensePlate}
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium">Status</p>
                  <Badge
                    variant={
                      vehicle.status === 'Available' ? 'default' : 'secondary'
                    }
                  >
                    {vehicle.status}
                  </Badge>
                </div>
              </div>

              <div className="space-y-3">
                {vehicle.mileage && (
                  <div>
                    <p className="text-sm font-medium">Mileage</p>
                    <p className="text-sm text-muted-foreground">
                      {vehicle.mileage.toLocaleString()} miles
                    </p>
                  </div>
                )}
                {vehicle.fuelLevel && (
                  <div>
                    <p className="text-sm font-medium">Fuel Level</p>
                    <div className="flex items-center gap-2">
                      <Fuel className="size-4" />
                      <div className="h-2 flex-1 rounded-full bg-muted">
                        <div
                          className="h-2 rounded-full bg-blue-500"
                          style={{ width: `${vehicle.fuelLevel}%` }}
                        />
                      </div>
                      <span className="text-sm">{vehicle.fuelLevel}%</span>
                    </div>
                  </div>
                )}
                <div>
                  <p className="text-sm font-medium">Location</p>
                  <p className="text-sm text-muted-foreground">
                    {vehicle.currentLocation || 'Location unknown'}
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Communication */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Phone className="size-5" />
            Communication
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <Button className="justify-start" variant="outline">
              <Phone className="mr-2 size-4" />
              Call Driver
            </Button>
            <Button className="justify-start" variant="outline">
              <MapPin className="mr-2 size-4" />
              Send Location
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default DriverInformation;
