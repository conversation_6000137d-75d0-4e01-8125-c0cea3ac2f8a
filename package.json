{"name": "work-hub-services-system", "version": "1.0.0", "description": "Work Hub is a comprehensive platform for service teams to efficiently manage projects, track progress, and receive AI-powered insights for optimized workflow.", "scripts": {"docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "docker:restart": "docker-compose restart", "frontend:build": "cd frontend && npm run docker:build", "backend:build": "cd backend && npm run docker:build", "test": "npm run test:frontend && npm run test:backend", "test:frontend": "cd frontend && npm test", "test:backend": "cd backend && npm test", "test:watch:frontend": "cd frontend && npm run test:watch", "test:watch:backend": "cd backend && npm run test:watch", "test:coverage": "npm run test:coverage:frontend && npm run test:coverage:backend", "test:coverage:frontend": "cd frontend && npm run test:coverage", "test:coverage:backend": "cd backend && npm run test:coverage", "security:audit": "npm run security:audit:frontend && npm run security:audit:backend", "security:audit:frontend": "cd frontend && npm audit --audit-level moderate", "security:audit:backend": "cd backend && npm audit --audit-level moderate", "security:fix": "npm run security:fix:frontend && npm run security:fix:backend", "security:fix:frontend": "cd frontend && npm audit fix", "security:fix:backend": "cd backend && npm audit fix", "security:scan": "npm run security:audit && npm run security:outdated", "security:outdated": "npm run security:outdated:frontend && npm run security:outdated:backend", "security:outdated:frontend": "cd frontend && npm outdated", "security:outdated:backend": "cd backend && npm outdated"}, "dependencies": {"@types/jsdom": "^21.1.7", "jsdom": "^26.1.0", "morgan": "^1.10.0", "node-fetch": "^3.3.2", "zod": "^3.25.28"}, "devDependencies": {"@types/morgan": "^1.9.9", "jest": "^29.7.0"}}