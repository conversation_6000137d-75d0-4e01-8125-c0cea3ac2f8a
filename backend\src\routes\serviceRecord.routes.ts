import { Router } from 'express';

import * as serviceRecordController from '../controllers/serviceRecord.controller.js';
import { enhancedAuthenticateUser } from '../middleware/jwtAuth.middleware.js';
import { adaptiveRateLimit } from '../middleware/rateLimiting.js'; // Add this import
import { apiDeduplication } from '../middleware/requestDeduplication.js'; // Add this import
import { responseWrapper } from '../middleware/responseWrapper.js';
import { requireRole } from '../middleware/supabaseAuth.js';
import { validate } from '../middleware/validation.js';
import {
  serviceRecordCreateSchema,
  serviceRecordIdParamSchema,
  serviceRecordUpdateSchema,
  vehicleIdParamSchema,
} from '../schemas/serviceRecord.schema.js';

// mergeParams: true allows access to parent router's params (e.g., :vehicleId from /api/vehicles/:vehicleId/servicerecords)
const router = Router({ mergeParams: true });

// Apply adaptive rate limiting and API deduplication
router.use(adaptiveRateLimit);
router.use(apiDeduplication);

// Apply response wrapper for standardized API responses
router.use(responseWrapper);

/**
 * @openapi
 * /vehicles/{vehicleId}/servicerecords:
 *   post:
 *     tags: [Service Records]
 *     summary: Create a new service record for a specific vehicle
 *     parameters:
 *       - $ref: '#/components/parameters/VehicleIdPath'
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/ServiceRecordCreateInput'
 *     responses:
 *       201: { $ref: '#/components/responses/ServiceRecordResponse' }
 *       400: { $ref: '#/components/responses/BadRequest' }
 *       404: { $ref: '#/components/responses/NotFound' }
 *   get:
 *     tags: [Service Records]
 *     summary: Retrieve all service records for a specific vehicle
 *     parameters:
 *       - $ref: '#/components/parameters/VehicleIdPath'
 *     responses:
 *       200:
 *         description: A list of service records for the vehicle.
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/ServiceRecord'
 *       404: { $ref: '#/components/responses/NotFound' }
 */
// 🚨 EMERGENCY SECURITY: All service record routes require authentication
router.post(
  '/',
  enhancedAuthenticateUser,
  requireRole(['ADMIN', 'MANAGER']),
  validate(vehicleIdParamSchema, 'params'),
  validate(serviceRecordCreateSchema, 'body'), // vehicleId within body is optional here, path param is source of truth
  serviceRecordController.createServiceRecord,
);
router.get(
  '/',
  enhancedAuthenticateUser,
  validate(vehicleIdParamSchema, 'params'),
  serviceRecordController.getAllServiceRecordsForVehicle,
);

/**
 * @openapi
 * /vehicles/{vehicleId}/servicerecords/{serviceRecordId}:
 *   get:
 *     tags: [Service Records]
 *     summary: Retrieve a specific service record by ID for a specific vehicle
 *     parameters:
 *       - $ref: '#/components/parameters/VehicleIdPath'
 *       - $ref: '#/components/parameters/ServiceRecordIdPath'
 *     responses:
 *       200: { $ref: '#/components/responses/ServiceRecordResponse' }
 *       404: { $ref: '#/components/responses/NotFound' }
 *   put:
 *     tags: [Service Records]
 *     summary: Update a specific service record by ID for a specific vehicle
 *     parameters:
 *       - $ref: '#/components/parameters/VehicleIdPath'
 *       - $ref: '#/components/parameters/ServiceRecordIdPath'
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/ServiceRecordUpdateInput'
 *     responses:
 *       200: { $ref: '#/components/responses/ServiceRecordResponse' }
 *       400: { $ref: '#/components/responses/BadRequest' }
 *       404: { $ref: '#/components/responses/NotFound' }
 *   delete:
 *     tags: [Service Records]
 *     summary: Delete a specific service record by ID for a specific vehicle
 *     parameters:
 *       - $ref: '#/components/parameters/VehicleIdPath'
 *       - $ref: '#/components/parameters/ServiceRecordIdPath'
 *     responses:
 *       200:
 *         description: Service record deleted successfully.
 *       404: { $ref: '#/components/responses/NotFound' }
 */
router.get(
  '/:id', // Renamed param to 'id' to match serviceRecordIdSchema standard
  enhancedAuthenticateUser,
  validate(vehicleIdParamSchema, 'params'),
  validate(serviceRecordIdParamSchema, 'params'), // Validates 'id' as serviceRecordId
  serviceRecordController.getServiceRecordById,
);
router.put(
  '/:id',
  enhancedAuthenticateUser,
  requireRole(['ADMIN', 'MANAGER']),
  validate(vehicleIdParamSchema, 'params'),
  validate(serviceRecordIdParamSchema, 'params'),
  validate(serviceRecordUpdateSchema, 'body'),
  serviceRecordController.updateServiceRecord,
);
router.delete(
  '/:id',
  enhancedAuthenticateUser,
  requireRole(['ADMIN']),
  validate(vehicleIdParamSchema, 'params'),
  validate(serviceRecordIdParamSchema, 'params'),
  serviceRecordController.deleteServiceRecord,
);

export default router;
