/**
 * Enhanced CSP Debug Component - 2025 Security Standards
 *
 * Provides comprehensive CSP monitoring and debugging information
 * following the latest security best practices and standards.
 */

'use client';

import React, { useState, useEffect } from 'react';
import { useCSP } from '@/lib/security/CSPProvider';
import { useTheme } from 'next-themes';

interface CSPViolation {
  timestamp: string;
  violatedDirective: string;
  blockedURI: string;
  sourceFile?: string;
  lineNumber?: number;
}

export function EnhancedCSPDebug() {
  const {
    nonce,
    isStrictCSP,
    violationCount,
    isNonceValid,
    resetViolationCount,
  } = useCSP();

  const { theme } = useTheme();
  const [isExpanded, setIsExpanded] = useState(false);
  const [recentViolations, setRecentViolations] = useState<CSPViolation[]>([]);
  const [cspStatus, setCSPStatus] = useState<
    'loading' | 'working' | 'violations' | 'error'
  >('loading');

  useEffect(() => {
    // Determine CSP status based on violations and nonce validity
    if (!nonce) {
      setCSPStatus('error');
    } else if (violationCount === 0) {
      setCSPStatus('working');
    } else {
      setCSPStatus('violations');
    }
  }, [nonce, violationCount]);

  useEffect(() => {
    // Listen for CSP violations and track them
    const handleViolation = (event: SecurityPolicyViolationEvent) => {
      const violation: CSPViolation = {
        timestamp: new Date().toISOString(),
        violatedDirective: event.violatedDirective,
        blockedURI: event.blockedURI,
        sourceFile: event.sourceFile,
        lineNumber: event.lineNumber,
      };

      setRecentViolations(prev => [violation, ...prev.slice(0, 4)]); // Keep last 5
    };

    document.addEventListener('securitypolicyviolation', handleViolation);
    return () =>
      document.removeEventListener('securitypolicyviolation', handleViolation);
  }, []);

  const getStatusIcon = () => {
    switch (cspStatus) {
      case 'working':
        return '✅';
      case 'violations':
        return '⚠️';
      case 'error':
        return '❌';
      default:
        return '🔄';
    }
  };

  const getStatusText = () => {
    switch (cspStatus) {
      case 'working':
        return 'Strict CSP Active';
      case 'violations':
        return `${violationCount} Violations`;
      case 'error':
        return 'CSP Error';
      default:
        return 'Loading...';
    }
  };

  const getStatusColor = () => {
    switch (cspStatus) {
      case 'working':
        return 'text-green-600';
      case 'violations':
        return 'text-yellow-600';
      case 'error':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  // Theme-aware styling
  const isDark = theme === 'dark';

  return (
    <div className="fixed top-4 right-4 z-50">
      {/* Compact Status Display */}
      <div
        className={`${
          isDark
            ? 'bg-gray-800 border-gray-600 text-gray-100'
            : 'bg-white border-gray-300 text-gray-800'
        } border-2 rounded-lg shadow-lg p-3 cursor-pointer transition-all duration-200 ${
          isExpanded
            ? 'border-blue-500'
            : isDark
              ? 'hover:border-gray-500'
              : 'hover:border-gray-400'
        }`}
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center gap-2">
          <span className="text-lg">{getStatusIcon()}</span>
          <div className="text-sm">
            <div className="font-semibold">CSP 2025</div>
            <div className={`text-xs ${getStatusColor()}`}>
              {getStatusText()}
            </div>
          </div>
          <span
            className={`${isDark ? 'text-gray-400' : 'text-gray-400'} text-xs ml-2`}
          >
            {isExpanded ? '▼' : '▶'}
          </span>
        </div>
      </div>

      {/* Expanded Debug Panel */}
      {isExpanded && (
        <div
          className={`mt-2 ${
            isDark
              ? 'bg-gray-800 border-gray-600 text-gray-100'
              : 'bg-white border-gray-300 text-gray-800'
          } border rounded-lg shadow-xl p-4 w-80 max-h-96 overflow-y-auto`}
        >
          <div className="space-y-3">
            {/* CSP Status Section */}
            <div>
              <h3 className="font-semibold mb-2">CSP Status</h3>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span>Nonce:</span>
                  <span
                    className={`font-mono text-xs ${isNonceValid ? 'text-green-600' : 'text-red-600'}`}
                  >
                    {nonce ? `${nonce.substring(0, 12)}...` : 'Missing'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Strict CSP:</span>
                  <span
                    className={
                      isStrictCSP ? 'text-green-600' : 'text-yellow-600'
                    }
                  >
                    {isStrictCSP ? 'Enabled' : 'Development Mode'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Nonce Valid:</span>
                  <span
                    className={isNonceValid ? 'text-green-600' : 'text-red-600'}
                  >
                    {isNonceValid ? 'Yes' : 'No'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Violations:</span>
                  <span
                    className={
                      violationCount === 0 ? 'text-green-600' : 'text-red-600'
                    }
                  >
                    {violationCount}
                  </span>
                </div>
              </div>
            </div>

            {/* Security Features */}
            <div>
              <h3 className="font-semibold mb-2">Security Features</h3>
              <div className="space-y-1 text-xs">
                <div className="flex items-center gap-2">
                  <span className="text-green-600">✓</span>
                  <span>Nonce-based script execution</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-green-600">✓</span>
                  <span>strict-dynamic directive</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-green-600">✓</span>
                  <span>Comprehensive security headers</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-green-600">✓</span>
                  <span>Violation reporting</span>
                </div>
                <div className="flex items-center gap-2">
                  <span
                    className={
                      isStrictCSP ? 'text-green-600' : 'text-yellow-600'
                    }
                  >
                    {isStrictCSP ? '✓' : '⚠'}
                  </span>
                  <span>No unsafe-inline/unsafe-eval</span>
                </div>
              </div>
            </div>

            {/* Recent Violations */}
            {recentViolations.length > 0 && (
              <div>
                <h3 className="font-semibold mb-2">Recent Violations</h3>
                <div className="space-y-2 max-h-32 overflow-y-auto">
                  {recentViolations.map((violation, index) => (
                    <div
                      key={index}
                      className={`text-xs p-2 rounded border ${
                        isDark
                          ? 'bg-red-900/20 border-red-800'
                          : 'bg-red-50 border-red-200'
                      }`}
                    >
                      <div
                        className={`font-semibold ${
                          isDark ? 'text-red-400' : 'text-red-700'
                        }`}
                      >
                        {violation.violatedDirective}
                      </div>
                      <div
                        className={`truncate ${
                          isDark ? 'text-gray-300' : 'text-gray-600'
                        }`}
                      >
                        {violation.blockedURI}
                      </div>
                      <div
                        className={isDark ? 'text-gray-400' : 'text-gray-500'}
                      >
                        {new Date(violation.timestamp).toLocaleTimeString()}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Actions */}
            <div
              className={`flex gap-2 pt-2 border-t ${
                isDark ? 'border-gray-600' : 'border-gray-200'
              }`}
            >
              <button
                onClick={resetViolationCount}
                className="text-xs bg-blue-500 text-white px-2 py-1 rounded hover:bg-blue-600 transition-colors"
              >
                Reset Count
              </button>
              <button
                onClick={() => setRecentViolations([])}
                className={`text-xs px-2 py-1 rounded transition-colors ${
                  isDark
                    ? 'bg-gray-600 text-gray-100 hover:bg-gray-500'
                    : 'bg-gray-500 text-white hover:bg-gray-600'
                }`}
              >
                Clear Violations
              </button>
            </div>

            {/* CSP Policy Info */}
            <div
              className={`text-xs pt-2 border-t ${
                isDark
                  ? 'text-gray-400 border-gray-600'
                  : 'text-gray-500 border-gray-200'
              }`}
            >
              <div>CSP 2025 Standards</div>
              <div>OWASP Compliant</div>
              <div>Nonce Rotation: Active</div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
