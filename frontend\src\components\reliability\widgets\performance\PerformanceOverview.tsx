/**
 * @file Performance overview widget component for comprehensive performance monitoring.
 * This component provides a high-level view of system performance with key metrics,
 * performance scoring, and trend analysis for reliability monitoring.
 * @module components/reliability/widgets/performance/PerformanceOverview
 */

'use client';

import {
  Activity,
  AlertTriangle,
  BarChart3,
  CheckCircle,
  Clock,
  TrendingDown,
  TrendingUp,
  Zap,
} from 'lucide-react';
import React from 'react';

import type { SystemMetrics } from '@/lib/types/domain';

import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Skeleton } from '@/components/ui/skeleton';
import { usePerformanceMetrics } from '@/lib/stores/queries/useReliability';
import { cn } from '@/lib/utils';

/**
 * Props for the PerformanceOverview component
 */
export interface PerformanceOverviewProps {
  /** Optional CSS class name for styling */
  className?: string;
  /** Compact mode for smaller displays */
  compact?: boolean;
  /** Whether to show detailed metrics breakdown */
  showDetails?: boolean;
  /** Whether to show performance trends */
  showTrends?: boolean;
}

/**
 * Performance metric item interface
 */
interface PerformanceMetricItem {
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  id: string;
  label: string;
  recommendation?: string | undefined;
  status: 'excellent' | 'good' | 'poor' | 'warning';
  trend: 'up' | 'down' | 'stable';
  unit: string;
  value: number;
}

/**
 * Calculate overall performance score based on metrics
 */
const calculatePerformanceScore = (metrics: SystemMetrics): number => {
  let score = 100;

  // CPU performance impact (0-30 points)
  const cpuUsage = metrics.systemMetrics?.cpu?.usage || 0;
  if (cpuUsage > 90) score -= 30;
  else if (cpuUsage > 70) score -= 20;
  else if (cpuUsage > 50) score -= 10;

  // Memory performance impact (0-25 points)
  const memoryUsage = metrics.systemMetrics?.memory?.usagePercent || 0;
  if (memoryUsage > 95) score -= 25;
  else if (memoryUsage > 80) score -= 15;
  else if (memoryUsage > 60) score -= 5;

  // Deduplication efficiency impact (0-25 points)
  const hitRate = metrics.deduplicationMetrics?.hitRate || 0;
  if (hitRate < 40) score -= 25;
  else if (hitRate < 60) score -= 15;
  else if (hitRate < 80) score -= 5;

  // HTTP request performance impact (0-20 points)
  const requestCount = metrics.httpRequestMetrics?.values?.length || 0;
  if (requestCount === 0) score -= 10; // No data available

  return Math.max(0, Math.min(100, score));
};

/**
 * Get performance status based on score
 */
const getPerformanceStatus = (
  score: number
): 'excellent' | 'good' | 'poor' | 'warning' => {
  if (score >= 90) return 'excellent';
  if (score >= 75) return 'good';
  if (score >= 50) return 'warning';
  return 'poor';
};

/**
 * Get status color classes
 */
const getStatusColor = (
  status: 'excellent' | 'good' | 'poor' | 'warning'
): string => {
  switch (status) {
    case 'poor': {
      return 'text-red-600 bg-red-50 border-red-200';
    }
    case 'excellent': {
      return 'text-green-600 bg-green-50 border-green-200';
    }
    case 'good': {
      return 'text-blue-600 bg-blue-50 border-blue-200';
    }
    case 'warning': {
      return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    }
    default: {
      return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  }
};

/**
 * Get trend icon component
 */
const getTrendIcon = (
  trend: 'up' | 'down' | 'stable'
): React.ComponentType<{ className?: string }> => {
  switch (trend) {
    case 'down':
      return ({ className }: { className?: string }) => (
        <TrendingDown className={className} />
      );
    case 'up':
      return ({ className }: { className?: string }) => (
        <TrendingUp className={className} />
      );
    default:
      return ({ className }: { className?: string }) => (
        <Activity className={className} />
      );
  }
};

/**
 * Performance overview widget component.
 *
 * This component provides:
 * - Overall performance score calculation and visualization
 * - Key performance metrics with status indicators
 * - Performance trend analysis and recommendations
 * - Real-time performance monitoring with WebSocket updates
 * - Visual performance health indicators
 *
 * Features:
 * - Comprehensive performance scoring algorithm
 * - Color-coded status indicators for quick assessment
 * - Trend analysis with directional indicators
 * - Responsive design with mobile-first approach
 * - Real-time data updates via WebSocket integration
 * - Accessibility support with proper ARIA labels
 * - Loading states and comprehensive error handling
 *
 * @param props - Component props
 * @returns JSX element representing the performance overview
 *
 * @example
 * ```tsx
 * <PerformanceOverview
 *   showDetails={true}
 *   showTrends={true}
 *   compact={false}
 * />
 * ```
 */
export const PerformanceOverview: React.FC<PerformanceOverviewProps> = ({
  className = '',
  compact = false,
  showDetails = true,
  showTrends = true,
}) => {
  const { data: metrics, error, isLoading } = usePerformanceMetrics();

  // Calculate performance metrics
  const performanceMetrics: PerformanceMetricItem[] = React.useMemo(() => {
    if (!metrics) return [];

    const items: PerformanceMetricItem[] = [];

    // Response Time (calculated from HTTP request metrics)
    const avgResponseTime = (() => {
      if (!metrics.httpRequestMetrics?.values?.length) return 200; // Default fallback

      const totalTime = metrics.httpRequestMetrics.values.reduce(
        (sum, item) => sum + item.value,
        0
      );
      const avgTime = totalTime / metrics.httpRequestMetrics.values.length;
      return Math.round(avgTime * 1000); // Convert to milliseconds
    })();
    items.push({
      description: 'Average API response time',
      icon: ({ className }: { className?: string }) => (
        <Clock className={className} />
      ),
      id: 'response-time',
      label: 'Avg Response Time',
      recommendation:
        avgResponseTime < 300
          ? 'Excellent'
          : avgResponseTime < 500
            ? 'Good'
            : avgResponseTime < 1000
              ? 'Warning'
              : 'Critical',
      status:
        avgResponseTime < 200
          ? 'excellent'
          : avgResponseTime < 500
            ? 'good'
            : avgResponseTime < 1000
              ? 'warning'
              : 'poor',
      trend: avgResponseTime < 300 ? 'stable' : 'up',
      unit: 'ms',
      value: avgResponseTime,
    });

    // Throughput (based on connections)
    const throughput = metrics.systemMetrics?.connections?.active || 0;
    items.push({
      description: 'Request processing throughput',
      icon: ({ className }: { className?: string }) => (
        <Zap className={className} />
      ),
      id: 'throughput',
      label: 'Throughput',
      recommendation:
        throughput > 50
          ? 'Excellent'
          : throughput > 20
            ? 'Good'
            : throughput > 5
              ? 'Warning'
              : 'Critical',
      status:
        throughput > 50
          ? 'excellent'
          : throughput > 20
            ? 'good'
            : throughput > 5
              ? 'warning'
              : 'poor',
      trend: 'stable',
      unit: 'req/min',
      value: throughput * 10,
    });

    // Cache Hit Rate
    const hitRate = metrics.deduplicationMetrics?.hitRate || 0;
    items.push({
      description: 'Request deduplication efficiency',
      icon: ({ className }: { className?: string }) => (
        <BarChart3 className={className} />
      ),
      id: 'cache-hit-rate',
      label: 'Cache Hit Rate',
      recommendation:
        hitRate > 80
          ? 'Excellent'
          : hitRate > 60
            ? 'Good'
            : hitRate > 40
              ? 'Warning'
              : 'Critical',
      status:
        hitRate > 80
          ? 'excellent'
          : hitRate > 60
            ? 'good'
            : hitRate > 40
              ? 'warning'
              : 'poor',
      trend: hitRate > 70 ? 'up' : 'down',
      unit: '%',
      value: hitRate,
    });

    // Error Rate (calculated from HTTP request metrics)
    const errorRate = (() => {
      if (!metrics.httpRequestMetrics?.values?.length) return 0; // Default fallback

      const totalRequests = metrics.httpRequestMetrics.values.reduce(
        (sum, item) => sum + item.value,
        0
      );
      const errorRequests = metrics.httpRequestMetrics.values
        .filter(item => parseInt(item.labels.statusCode) >= 400)
        .reduce((sum, item) => sum + item.value, 0);

      return totalRequests > 0 ? (errorRequests / totalRequests) * 100 : 0;
    })();
    items.push({
      description: 'Request error percentage',
      icon: ({ className }: { className?: string }) => (
        <AlertTriangle className={className} />
      ),
      id: 'error-rate',
      label: 'Error Rate',
      recommendation:
        errorRate < 1
          ? 'Excellent'
          : errorRate < 3
            ? 'Good'
            : errorRate < 5
              ? 'Warning'
              : 'Critical',
      status:
        errorRate < 1
          ? 'excellent'
          : errorRate < 3
            ? 'good'
            : errorRate < 5
              ? 'warning'
              : 'poor',
      trend: errorRate < 2 ? 'down' : 'up',
      unit: '%',
      value: errorRate,
    });

    return items;
  }, [metrics]);

  // Calculate overall performance score
  const performanceScore = React.useMemo(() => {
    return metrics ? calculatePerformanceScore(metrics) : 0;
  }, [metrics]);

  const performanceStatus = getPerformanceStatus(performanceScore);

  // Loading state
  if (isLoading) {
    return (
      <div className={cn('space-y-4', className)}>
        <div className="flex items-center justify-between">
          <Skeleton className="h-6 w-40" />
          <Skeleton className="h-8 w-24" />
        </div>
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
          {Array.from({ length: 4 }).map((_, index) => (
            <Skeleton className="h-20 w-full" key={index} />
          ))}
        </div>
        <Skeleton className="h-16 w-full" />
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className={cn('text-center py-8', className)}>
        <AlertTriangle className="mx-auto mb-4 size-12 text-red-500" />
        <p className="text-sm font-medium text-red-600">
          Failed to load performance metrics
        </p>
        <p className="mt-1 text-xs text-muted-foreground">
          {error.message || 'Unable to retrieve performance data'}
        </p>
      </div>
    );
  }

  return (
    <div className={cn('space-y-4', className)}>
      {/* Header with Performance Score */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Activity className="size-5 text-muted-foreground" />
          <h3 className="text-sm font-semibold">Performance Overview</h3>
        </div>
        <Badge
          className={cn('font-medium', getStatusColor(performanceStatus))}
          variant="outline"
        >
          Score: {performanceScore}
        </Badge>
      </div>

      {/* Performance Score Visualization */}
      {!compact && (
        <Card className="p-4">
          <div className="mb-2 flex items-center justify-between">
            <span className="text-sm font-medium">Overall Performance</span>
            <span
              className={cn(
                'text-sm font-semibold',
                performanceStatus === 'excellent'
                  ? 'text-green-600'
                  : performanceStatus === 'good'
                    ? 'text-blue-600'
                    : performanceStatus === 'warning'
                      ? 'text-yellow-600'
                      : 'text-red-600'
              )}
            >
              {performanceStatus.toUpperCase()}
            </span>
          </div>
          <Progress className="h-3" value={performanceScore} />
          <p className="mt-2 text-xs text-muted-foreground">
            Performance score based on CPU, memory, cache efficiency, and
            response times
          </p>
        </Card>
      )}

      {/* Key Metrics Grid */}
      <div
        className={cn(
          'grid gap-3',
          compact
            ? 'grid-cols-2'
            : 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
        )}
      >
        {performanceMetrics.map(metric => {
          const TrendIcon = getTrendIcon(metric.trend);

          return (
            <Card
              className={cn(
                'transition-all duration-200 hover:shadow-md',
                getStatusColor(metric.status)
              )}
              key={metric.id}
            >
              <CardContent className={cn('p-4', compact && 'p-3')}>
                <div className="flex items-center justify-between">
                  <div className="flex flex-wrap items-center gap-2">
                    <metric.icon className="size-4" />
                    <span
                      className={cn(
                        'font-medium',
                        compact ? 'text-xs' : 'text-sm'
                      )}
                    >
                      {metric.label}
                    </span>
                  </div>
                  {showTrends && metric.trend && (
                    <TrendIcon
                      className={cn(
                        compact ? 'h-3 w-3' : 'h-4 w-4',
                        metric.trend === 'up'
                          ? 'text-green-600'
                          : metric.trend === 'down'
                            ? 'text-red-600'
                            : 'text-gray-600'
                      )}
                    />
                  )}
                </div>

                <div className="mt-2">
                  <p
                    className={cn(
                      'font-semibold',
                      compact ? 'text-sm' : 'text-lg'
                    )}
                  >
                    {metric.value.toFixed(metric.unit === '%' ? 1 : 0)}
                    {metric.unit}
                  </p>
                  {showDetails && !compact && (
                    <p className="mt-1 text-xs text-muted-foreground">
                      {metric.description}
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
};

/**
 * Default export for the PerformanceOverview component
 */
export default PerformanceOverview;
