# Project: UI/UX Alignment with Refactored API Service Layer (Leveraging `apiContracts.ts`)

**Document Version:** 1.0 **Date:** May 30, 2025 **Status:** Initial Draft

## Overall Goal

Refactor the frontend UI and data-handling logic to align with the new API
service layer, React Query hooks, and domain models, ensuring type safety and a
consistent user experience. This involves using
`frontend/src/lib/types/apiContracts.ts` as the source of truth for raw API
request/response shapes, `frontend/src/lib/types/domain.ts` for
application-level data models, and transformers in
`frontend/src/lib/transformers/` to bridge between them.

## Core Principles

1.  **Single Source of Truth:**
    - `apiContracts.ts`: Defines exact network request/response payloads.
    - `domain.ts`: Defines data shapes used within the frontend application
      logic (UI, form states, hook inputs/outputs).
2.  **Clear Separation of Concerns:**
    - **UI Components:** Interact with domain types and React Query hooks.
    - **React Query Hooks:** Orchestrate data fetching/mutation, accept/return
      domain types, and use transformers + API services internally.
    - **API Services:** Encapsulate `ApiClient` calls, accept API contract
      request types, receive API contract response types, and use transformers
      to return domain types.
    - **Transformers:** Convert data between API contract shapes and domain
      model shapes.
    - **`ApiClient`:** Handles raw HTTP communication.
3.  **Type Safety:** Enforce strict typing throughout the data flow.

---

## Implementation Plan & Checklist

This plan will be applied iteratively to each core entity: **Vehicles,
Delegations, Tasks, Employees, (and potentially ServiceRecords as a sub-entity
or standalone if it has dedicated CRUD).**

**For Each Entity (e.g., "Vehicles"):**

**Phase 0: Preparation & Review (Conceptual)**

- [ ] **P0.1:** Confirm `apiContracts.ts` definitions for `<Entity>ApiResponse`,
      `Create<Entity>Request`, `Update<Entity>Request` accurately reflect the
      backend API.
- [ ] **P0.2:** Confirm `domain.ts` definitions for `<Entity>`,
      `Create<Entity>Data` are appropriate for frontend application use.
- [ ] **P0.3:** Identify and list key discrepancies (field names, types,
      structure) between `apiContracts.ts` and `domain.ts` for this entity.

**Phase 1: Align `types/api.ts` (if still used for these specific types)**

- [ ] **P1.1:** Ensure request payload types (e.g., `CreateVehicleRequest`) in
      `types/api.ts` are either removed (if API services will directly use types
      from `apiContracts.ts`) or are perfect re-exports/aliases of those in
      `apiContracts.ts`.
  - _Note: The goal is for API Service methods to ultimately expect parameters
    matching `apiContracts.ts` request shapes._
- [ ] **P1.2:** Confirm `...ApiResponse` type aliases in `types/api.ts` (e.g.,
      `type VehicleApiResponse = Vehicle;`) correctly reflect that API _service
      methods_ will return _domain types_ (after transformation).

**Phase 2: Refactor `<Entity>Transformer.ts`**

- [ ] **P2.1:** Update
      `fromApi(apiData: <ContractEntityApiResponse>): <DomainEntity>`:
  - Parameter `apiData` typed from `apiContracts.ts`.
  - Implement all necessary field name mappings, type conversions, and
    structural adjustments.
  - Return type is the domain model from `domain.ts`.
- [ ] **P2.2:** Update
      `toCreateRequest(domainData: Create<DomainEntity>Data): <ContractCreateEntityRequest>`:
  - Input `domainData` typed from `domain.ts`.
  - Return type is the `Create...Request` type from `apiContracts.ts`.
  - Implement all necessary field name mappings and type conversions.
- [ ] **P2.3:** Update
      `toUpdateRequest(domainData: Partial<Create<DomainEntity>Data>): <ContractUpdateEntityRequest>`:
  - Similar to `toCreateRequest`, ensuring output is the `Update...Request` type
    from `apiContracts.ts`.

**Phase 3: Refactor `<Entity>ApiService.ts`**

- [ ] **P3.1:**
      `create(payload: <ContractCreateEntityRequest>): Promise<DomainEntity>`
      method:
  - Parameter `payload` typed from `apiContracts.ts`.
  - `payload` is passed directly to `apiClient.post()`.
  - Raw response (type from `apiContracts.ts`) from `apiClient` is transformed
    using `Transformer.fromApi()`.
  - Returns `Promise<DomainEntity>`.
- [ ] **P3.2:**
      `update(id: ..., payload: <ContractUpdateEntityRequest>): Promise<DomainEntity>`
      method:
  - Parameter `payload` typed from `apiContracts.ts`.
  - Passed directly to `apiClient.put()`.
  - Raw response transformed using `Transformer.fromApi()`.
  - Returns `Promise<DomainEntity>`.
- [ ] **P3.3:** `getAll(): Promise<DomainEntity[]>` method:
  - Receives `ContractEntityApiResponse[]` from `apiClient.get()`.
  - Maps and transforms each item using `Transformer.fromApi()`.
  - Returns `Promise<DomainEntity[]>`.
- [ ] **P3.4:** `getById(id: ...): Promise<DomainEntity>` method:
  - Receives `ContractEntityApiResponse` from `apiClient.get()`.
  - Transforms using `Transformer.fromApi()`.
  - Returns `Promise<DomainEntity>`.
- [ ] **P3.5 (If applicable):** Refactor any other entity-specific methods
      (e.g., `setEscorts` for Delegations) to use appropriate contract types for
      payloads and transform responses.

**Phase 4: Refactor/Verify React Query Hooks (`use<Entity>s.ts`)**

- [ ] **P4.1:** `useCreate<Entity>` hook:
  - Mutation function input type is `Create<DomainEntity>Data` (from
    `domain.ts`).
  - Inside `mutationFn`, calls `Transformer.toCreateRequest()` to get the API
    contract payload.
  - Calls the corresponding `<Entity>ApiService.create()` method with this
    contract payload.
  - Resolved data is `DomainEntity`.
  - Optimistic update logic uses `Create<DomainEntity>Data` and constructs an
    optimistic `DomainEntity`.
- [ ] **P4.2:** `useUpdate<Entity>` hook:
  - Mutation function input type for `data` is `Update<Entity>Request` (from
    `apiContracts.ts`, possibly re-exported via `types/api.ts`).
  - Inside `mutationFn`, calls `<Entity>ApiService.update()` directly with this
    `data`.
  - Resolved data is `DomainEntity`.
  - Optimistic update logic uses `Update<Entity>Request` and the old
    `DomainEntity` to construct an optimistic `DomainEntity`.
- [ ] **P4.3:** `use<Entity>s` (fetch all) and `use<Entity>` (fetch one) hooks:
  - `queryFn` calls the corresponding API service method.
  - Resolved data is `DomainEntity[]` or `DomainEntity`.

**Phase 5: Refactor UI Components**

- [ ] **P5.1 (List/Display Components):**
  - Use `use<Entity>s` or `use<Entity>` hook for data fetching.
  - Props expect domain types (e.g., `vehicle: Vehicle`).
  - Implement `isLoading`, `error` (using `error.getFormattedMessage()`), and
    empty states from hook data.
  - Remove old data fetching and client-side transformations now handled
    elsewhere.
- [ ] **P5.2 (Form Components - Create/Edit):**
  - Form state aligns with `Create<DomainEntity>Data`.
  - `onSubmit` handler prepares `Create<DomainEntity>Data` (for create) or
    `Update<Entity>Request` (for edit, after transforming from form data if
    necessary).
  - Uses `useCreate<Entity>` or `useUpdate<Entity>` mutation hooks.
  - Handles `isPending` for loading states.
  - Handles `error` from hook, displaying formatted messages and potentially
    field-specific validation errors.
- [ ] **P5.3 (Specific Cases):** Address entity-specific UI logic (e.g., subtask
      management for Tasks, assignment UIs for Delegations).

**Phase 6: Testing**

- [ ] **P6.1:** Unit tests for updated Transformers.
- [ ] **P6.2:** Unit/integration tests for updated API Services.
- [ ] **P6.3:** Unit/integration tests for updated React Query hooks.
- [ ] **P6.4:** Manual E2E testing for all refactored UI flows.

---

## Progress Tracking (as of May 30, 2025)

**Entity: Vehicles**

- **Phase 0 (Prep):** [✅]
- **Phase 1 (`types/api.ts`):** [✅]
- **Phase 2 (Transformer - `vehicleTransformer.ts`):**
  - P2.1 (`fromApi`): [✅]
  - P2.2 (`toCreateRequest`): [✅]
  - P2.3 (`toUpdateRequest`): [✅]
- **Phase 3 (API Service - `VehicleApiService.ts`):**
  - P3.1 (`create`): [✅]
  - P3.2 (`update`): [✅]
  - P3.3 (`getAll`): [✅]
  - P3.4 (`getById`): [✅]
- **Phase 4 (Hooks - `useVehicles.ts`):**
  - P4.1 (`useCreateVehicle`): [✅]
  - P4.2 (`useUpdateVehicle`): [✅]
  - P4.3 (Query hooks): [✅]
- **Phase 5 (UI - `vehicles/page.tsx`, `vehicles/new/page.tsx`):**
  - P5.1 (List): [✅]
  - P5.2 (Form): [✅]
- **Phase 6 (Testing):** [ ]

**Entity: Delegations**

- **Phase 0 (Prep):** [✅] (Discrepancies like `title` vs `eventName`, `id`
  types, `delegates` structure noted)
- **Phase 1 (`types/api.ts`):** [✅] **FIXED:** Updated `apiContracts.ts` to
  correctly reflect backend API structure with `flightArrivalDetails` and
  `flightDepartureDetails` instead of single `flightDetails`.
- **Phase 2 (Transformer - `delegationTransformer.ts`):**
  - P2.1 (`fromApi`): [✅] **FIXED:** Now properly maps `DelegationApiResponse`
    from `apiContracts.ts` with correct field mappings (`title`→`eventName`,
    `startDate`→`durationFrom`) and **resolved flight details transformation
    gap** by mapping `flightArrivalDetails`→`arrivalFlight` and
    `flightDepartureDetails`→`departureFlight`.
  - P2.2 (`toCreateRequest`): [✅] **FIXED:** Now outputs
    `CreateDelegationRequest` matching `apiContracts.ts` with proper field
    mappings and flight details structure.
  - P2.3 (`toUpdateRequest`): [✅] **FIXED:** Now outputs
    `UpdateDelegationRequest` matching `apiContracts.ts` with correct field
    mappings.
- **Phase 3 (API Service - `DelegationApiService.ts`):** [✅]
  - P3.1 (`create`): [✅] (Signature expects `CreateDelegationRequest` from
    `apiContracts.ts` or equivalent)
  - P3.2 (`update`): [✅] (Signature expects `UpdateDelegationRequest` from
    `apiContracts.ts` or equivalent)
  - P3.3 (`getAll`): [✅] (Internally needs to ensure it uses transformer with
    raw contract type)
  - P3.4 (`getById`): [✅] (Internally needs to ensure it uses transformer with
    raw contract type)
  - P3.5 (Assignment methods like `setEscorts`): [✅] (Review payloads against
    `apiContracts.ts`)
- **Phase 4 (Hooks - `useDelegations.ts`):** [✅]
  - P4.1 (`useCreateDelegation`): [✅]
  - P4.2 (`useUpdateDelegation`): [✅]
  - P4.3 (Query hooks): [✅]
- **Phase 5 (UI - `delegations/add/page.tsx`,
  `delegations/[id]/edit/page.tsx`):** [✅]
  - P5.1 (List): [✅]
  - P5.2 (Form): [✅]
- **Phase 6 (Testing):** [ ]

**Entity: Tasks**

- **Phase 0 (Prep):** [✅]
- **Phase 1 (`types/api.ts`):** [✅]
- **Phase 2 (Transformer - `taskTransformer.ts`):**
  - P2.1 (`fromApi`): [✅]
  - P2.2 (`toCreateRequest`): [✅]
  - P2.3 (`toUpdateRequest`): [✅]
- **Phase 3 (API Service - `TaskApiService.ts`):**
  - P3.1 (`create`): [✅]
  - P3.2 (`update`): [✅]
  - P3.3 (`getAll`): [✅]
  - P3.4 (`getById`): [✅]
- **Phase 4 (Hooks - `useTasks.ts`):**
  - P4.1 (`useCreateTask`): [✅]
  - P4.2 (`useUpdateTask`): [✅]
  - P4.3 (Query hooks): [✅]
- **Phase 5 (UI):**
  - P5.1 (List): [✅]
  - P5.2 (Form): [✅]
- **Phase 6 (Testing):** [ ]

**Entity: Employees**

- **Phase 0 (Prep):** [✅]
- **Phase 1 (`types/api.ts`):** [✅]
- **Phase 2 (Transformer - `employeeTransformer.ts`):**
  - P2.1 (`fromApi`): [✅]
  - P2.2 (`toCreateRequest`): [✅]
  - P2.3 (`toUpdateRequest`): [✅]
- **Phase 3 (API Service - `EmployeeApiService.ts`):**
  - P3.1 (`create`): [✅]
  - P3.2 (`update`): [✅]
  - P3.3 (`getAll`): [✅]
  - P3.4 (`getById`): [✅]
- **Phase 4 (Hooks - `useEmployees.ts`):**
  - P4.1 (`useCreateEmployee`): [✅]
  - P4.2 (`useUpdateEmployee`): [✅]
  - P4.3 (Query hooks): [✅]
- **Phase 5 (UI):**
  - P5.1 (List): [✅]
  - P5.2 (Form): [✅]
- **Phase 6 (Testing):** [ ]

---

This document will be updated as progress is made on each entity and phase.
