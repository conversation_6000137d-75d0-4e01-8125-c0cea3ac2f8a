/**
 * @file Reporting Table Widget
 * @description A widget that displays paginated delegation data in a table.
 */

'use client';

import type { ColumnDef } from '@tanstack/react-table';

import {
  flexRender,
  getCoreRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { format } from 'date-fns';
import React from 'react';
import { useRouter } from 'next/navigation';
import { Eye, Download, Settings } from 'lucide-react';

import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { useToast } from '@/hooks/utils/use-toast';

import type { Delegation } from '../../data/types';

import { useDelegations } from '../../data/hooks';
import { useReportingFilters } from '../../data/stores';

export const ReportingTableWidget = () => {
  const filters = useReportingFilters();
  const [pagination, setPagination] = React.useState({ page: 1, pageSize: 10 });
  const router = useRouter();
  const { toast } = useToast();

  const { data, error, isLoading } = useDelegations(filters, pagination);

  // Handle view delegation details
  const handleViewDelegation = (delegation: Delegation) => {
    // Navigate to delegation details page
    router.push(`/delegations/${delegation.id}`);
  };

  // Handle export data
  const handleExportData = () => {
    if (!data?.data.length) {
      toast({
        title: 'No Data',
        description: 'No data available to export.',
        variant: 'destructive',
      });
      return;
    }

    // Create CSV content
    const headers = [
      'ID',
      'Customer',
      'Vehicle',
      'Status',
      'Location',
      'Created At',
      'Completed At',
    ];
    const csvContent = [
      headers.join(','),
      ...data.data.map(delegation =>
        [
          delegation.delegationId,
          delegation.customerName,
          delegation.vehicleModel,
          delegation.status,
          delegation.location,
          delegation.createdAt,
          delegation.completedAt || 'N/A',
        ].join(',')
      ),
    ].join('\n');

    // Download CSV file
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `delegation-report-${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);

    toast({
      title: 'Export Successful',
      description: 'Delegation data has been exported to CSV.',
    });
  };

  // Column definitions for the reporting table
  const columns: ColumnDef<Delegation>[] = [
    {
      accessorKey: 'delegationId',
      header: 'Delegation ID',
    },
    {
      accessorKey: 'customerName',
      header: 'Customer',
    },
    {
      accessorKey: 'vehicleModel',
      header: 'Vehicle',
    },
    {
      accessorKey: 'status',
      cell: ({ row }) => <Badge>{row.original.status}</Badge>,
      header: 'Status',
    },
    {
      accessorKey: 'location',
      header: 'Location',
    },
    {
      accessorKey: 'createdAt',
      cell: ({ row }) => format(new Date(row.original.createdAt), 'PPpp'),
      header: 'Date',
    },
    {
      cell: ({ row }) => (
        <Button
          size="sm"
          variant="outline"
          onClick={() => handleViewDelegation(row.original)}
          className="flex items-center gap-1"
        >
          <Eye className="h-3 w-3" />
          View
        </Button>
      ),
      id: 'actions',
      header: 'Actions',
    },
  ];

  const table = useReactTable({
    columns,
    data: data?.data ?? [],
    getCoreRowModel: getCoreRowModel(),
    manualPagination: true,
    rowCount: data?.meta.total ?? 0,
  });

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>{error.message}</AlertDescription>
      </Alert>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Detailed Report</CardTitle>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleExportData}
              disabled={!data?.data.length}
              className="flex items-center gap-1"
            >
              <Download className="h-3 w-3" />
              Export
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              {table.getHeaderGroups().map(headerGroup => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map(header => (
                    <TableHead key={header.id}>
                      {flexRender(
                        header.column.columnDef.header,
                        header.getContext()
                      )}
                    </TableHead>
                  ))}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {isLoading
                ? Array.from({ length: pagination.pageSize }).map((_, i) => (
                    <TableRow key={`loading-row-${i}`}>
                      {columns.map((col, colIndex) => (
                        <TableCell
                          key={`loading-cell-${i}-${col.id ?? colIndex}`}
                        >
                          <Skeleton className="h-6 w-full" />
                        </TableCell>
                      ))}
                    </TableRow>
                  ))
                : table.getRowModel().rows.map(row => (
                    <TableRow key={row.id}>
                      {row.getVisibleCells().map(cell => (
                        <TableCell key={cell.id}>
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext()
                          )}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))}
            </TableBody>
          </Table>
        </div>
        <div className="mt-4 flex items-center justify-between">
          <p className="text-sm text-muted-foreground">
            Page {data?.meta.page} of {data?.meta.totalPages} (
            {data?.meta.total} total records)
          </p>
          <div className="flex items-center gap-2">
            <Button
              disabled={pagination.page === 1}
              onClick={() =>
                setPagination(prev => ({ ...prev, page: prev.page - 1 }))
              }
              size="sm"
              variant="outline"
            >
              Previous
            </Button>
            <Button
              disabled={pagination.page >= (data?.meta.totalPages ?? 1)}
              onClick={() =>
                setPagination(prev => ({ ...prev, page: prev.page + 1 }))
              }
              size="sm"
              variant="outline"
            >
              Next
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
