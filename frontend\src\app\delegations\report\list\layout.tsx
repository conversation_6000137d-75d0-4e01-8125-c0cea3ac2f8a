import type { Metadata } from 'next';

// Font and global styles are handled by the root layout.
// Import '@/app/globals.css' is not needed here.
// Font imports (<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_<PERSON>o) are not needed here.

export const metadata: Metadata = {
  title: 'Delegation List Report',
};

export default function ReportListLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // Report layouts should not render their own html/body structure.
  // They render content *within* the main application layout.
  return <>{children}</>;
}
