'use client';

import { Briefcase } from 'lucide-react';
import { useRouter } from 'next/navigation';

import type { DelegationFormData } from '@/lib/schemas/delegationSchemas';
import type { CreateDelegationData } from '@/lib/types/domain'; // For type safety

import { DelegationFormContainer } from '@/components/features/delegations/forms'; // Modern SOLID implementation
import { PageHeader } from '@/components/ui/PageHeader';
import { usePredefinedEntityToast } from '@/hooks/forms/useFormToast';
import { useCreateDelegation } from '@/lib/stores/queries/useDelegations'; // Added hook

export default function AddDelegationPage() {
  const router = useRouter();
  const { showEntityCreated, showEntityCreationError } =
    usePredefinedEntityToast('delegation');
  const {
    error: submissionError,
    isPending: _isPending, // Renamed to _isPending to ignore unused var ESLint error
    mutateAsync: createDelegation,
  } = useCreateDelegation();

  const handleSubmit = async (data: DelegationFormData) => {
    // The DelegationForm now handles transformations from form data to API data.
    // The data received here should already be in the correct format for CreateDelegationData.
    // However, the status still needs to be converted from "In Progress" to "In_Progress" for the API.
    // Also, dates need to be ISO strings.
    // The DelegationForm handles flight details processing.

    const transformedData: CreateDelegationData = {
      ...data,
      // Ensure nested arrays are correctly mapped to expected API types
      delegates: data.delegates.map(d => ({
        // Removed optional chain, delegates is always array due to schema default
        name: d.name,
        notes: d.notes ?? '',
        title: d.title,
      })),
      drivers: data.driverEmployeeIds.map(id => ({ employeeId: id })), // Removed optional chain
      durationFrom: new Date(data.durationFrom).toISOString(),
      durationTo: new Date(data.durationTo).toISOString(),
      escorts: data.escortEmployeeIds.map(id => ({ employeeId: id })), // Removed optional chain
      // Ensure notes is always a string for CreateDelegationData
      notes: data.notes ?? '',
      status: data.status.replace(' ', '_') as CreateDelegationData['status'], // Ensure status is correct for API
      vehicles: data.vehicleIds.map(id => ({
        // Removed optional chain
        assignedDate: new Date(data.durationFrom).toISOString(), // Default to delegation duration
        returnDate: new Date(data.durationTo).toISOString(), // Default to delegation duration
        vehicleId: id,
      })),
    };

    try {
      const newDelegation = await createDelegation(transformedData);
      // Create a delegation object with the data needed for the toast display
      const delegationForToast = {
        event: data.eventName,
        location: data.location,
      };
      showEntityCreated(delegationForToast);
      router.push('/delegations');
    } catch (error: unknown) {
      // Changed to unknown
      console.error('Error adding delegation:', error);
      let errorMessage = 'Failed to add delegation. Please try again.';
      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (submissionError instanceof Error) {
        // Check submissionError type
        errorMessage = submissionError.message;
      }
      showEntityCreationError(errorMessage);
    }
  };

  return (
    <div className="space-y-6">
      <PageHeader
        description="Enter the details for the new delegation or event."
        icon={Briefcase}
        title="Add New Delegation"
      />
      {submissionError && (
        <p className="rounded-md bg-red-100 p-3 text-red-500">
          Error: {submissionError.message}
        </p>
      )}
      <DelegationFormContainer
        isEditing={false}
        onSubmit={handleSubmit}
        // isSubmitting={_isPending} // Removed as DelegationForm does not accept this prop
      />
    </div>
  );
}
