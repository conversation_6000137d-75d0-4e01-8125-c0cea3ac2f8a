/**
 * @file Form Submission Performance Service
 * @description Handles performance tracking and optimization for form submissions following SRP
 * @version 2.0.0
 * <AUTHOR> Development Team
 */

import type {
  PerformanceConfig,
  PerformanceMetrics,
} from '../types/FormSubmissionTypes';

/**
 * Form submission performance service following SRP
 * Responsible only for performance metrics and optimization
 */
export class FormSubmissionPerformanceService {
  private config: PerformanceConfig;
  private metrics: PerformanceMetrics;
  private submissionStartTime: number | null = null;
  private debounceTimer: NodeJS.Timeout | null = null;

  constructor(config: PerformanceConfig) {
    this.config = config;
    this.metrics = {
      totalSubmissions: 0,
      successfulSubmissions: 0,
      failedSubmissions: 0,
      averageDuration: 0,
    };
  }

  /**
   * Start tracking submission performance
   */
  startTiming(): void {
    this.submissionStartTime = Date.now();
  }

  /**
   * End tracking and update metrics
   */
  endTiming(success: boolean): number {
    if (!this.submissionStartTime) {
      return 0;
    }

    const duration = Date.now() - this.submissionStartTime;
    this.updateMetrics(success, duration);
    this.submissionStartTime = null;

    return duration;
  }

  /**
   * Update performance metrics
   */
  private updateMetrics(success: boolean, duration: number): void {
    const newTotal = this.metrics.totalSubmissions + 1;
    const newSuccessful = success
      ? this.metrics.successfulSubmissions + 1
      : this.metrics.successfulSubmissions;
    const newFailed = success
      ? this.metrics.failedSubmissions
      : this.metrics.failedSubmissions + 1;

    const totalDuration =
      this.metrics.averageDuration * this.metrics.totalSubmissions + duration;

    this.metrics = {
      totalSubmissions: newTotal,
      successfulSubmissions: newSuccessful,
      failedSubmissions: newFailed,
      averageDuration: totalDuration / newTotal,
    };
  }

  /**
   * Get current performance metrics
   */
  getMetrics(): PerformanceMetrics {
    return { ...this.metrics };
  }

  /**
   * Reset performance metrics
   */
  resetMetrics(): void {
    this.metrics = {
      totalSubmissions: 0,
      successfulSubmissions: 0,
      failedSubmissions: 0,
      averageDuration: 0,
    };
  }

  /**
   * Apply debouncing to function execution
   */
  debounce<T extends (...args: any[]) => void>(
    func: T,
    delay: number = this.config.debounceMs
  ): (...args: Parameters<T>) => void {
    return (...args: Parameters<T>) => {
      if (this.debounceTimer) {
        clearTimeout(this.debounceTimer);
      }

      this.debounceTimer = setTimeout(() => {
        func(...args);
      }, delay);
    };
  }

  /**
   * Clear any pending debounced operations
   */
  clearDebounce(): void {
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
      this.debounceTimer = null;
    }
  }

  /**
   * Create timeout promise for request timeout handling
   */
  createTimeoutPromise(
    timeoutMs: number = this.config.timeoutMs
  ): Promise<never> {
    return new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error(`Request timeout after ${timeoutMs}ms`));
      }, timeoutMs);
    });
  }

  /**
   * Wrap a promise with timeout functionality
   */
  async withTimeout<T>(
    promise: Promise<T>,
    timeoutMs: number = this.config.timeoutMs
  ): Promise<T> {
    return Promise.race([promise, this.createTimeoutPromise(timeoutMs)]);
  }

  /**
   * Get success rate as percentage
   */
  getSuccessRate(): number {
    if (this.metrics.totalSubmissions === 0) return 0;
    return (
      (this.metrics.successfulSubmissions / this.metrics.totalSubmissions) * 100
    );
  }

  /**
   * Get failure rate as percentage
   */
  getFailureRate(): number {
    if (this.metrics.totalSubmissions === 0) return 0;
    return (
      (this.metrics.failedSubmissions / this.metrics.totalSubmissions) * 100
    );
  }

  /**
   * Check if performance is within acceptable thresholds
   */
  isPerformanceAcceptable(thresholds?: {
    maxAverageDuration?: number;
    minSuccessRate?: number;
  }): boolean {
    const defaultThresholds = {
      maxAverageDuration: 5000, // 5 seconds
      minSuccessRate: 95, // 95%
    };

    const config = { ...defaultThresholds, ...thresholds };

    return (
      this.metrics.averageDuration <= config.maxAverageDuration &&
      this.getSuccessRate() >= config.minSuccessRate
    );
  }

  /**
   * Generate performance report
   */
  generateReport(): {
    metrics: PerformanceMetrics;
    successRate: number;
    failureRate: number;
    isAcceptable: boolean;
    recommendations: string[];
  } {
    const successRate = this.getSuccessRate();
    const failureRate = this.getFailureRate();
    const isAcceptable = this.isPerformanceAcceptable();

    const recommendations: string[] = [];

    if (this.metrics.averageDuration > 3000) {
      recommendations.push(
        'Consider optimizing form validation or submission logic'
      );
    }

    if (successRate < 90) {
      recommendations.push(
        'High failure rate detected - review error handling'
      );
    }

    if (
      this.metrics.totalSubmissions > 100 &&
      this.metrics.averageDuration > 1000
    ) {
      recommendations.push(
        'Consider implementing caching for better performance'
      );
    }

    return {
      metrics: this.getMetrics(),
      successRate,
      failureRate,
      isAcceptable,
      recommendations,
    };
  }

  /**
   * Cleanup performance tracking resources
   */
  cleanup(): void {
    this.clearDebounce();
    this.submissionStartTime = null;
  }
}
