import type { CustomDelegationUpdatePayload } from '../controllers/delegation.controller.js';
import type { Delegation, Prisma } from '../generated/prisma/index.js';

import {
  DelegationStatus as PrismaDelegationStatus,
  FlightDetails as PrismaFlightDetails,
} from '../generated/prisma/index.js';
import { PrismaClientKnownRequestError } from '../generated/prisma/runtime/library.js';
import prisma from './index.js';

// Define a type for Delegation with all its included relations
type DelegationWithRelations = Prisma.DelegationGetPayload<{
  include: {
    Delegate: true;
    DelegationStatusEntry: { orderBy: { changedAt: 'desc' } };
    drivers: {
      include: {
        Employee: true;
      };
    };
    escorts: {
      include: {
        Employee: true;
      };
    };
    FlightDetails_Delegation_flightArrivalIdToFlightDetails: true;
    FlightDetails_Delegation_flightDepartureIdToFlightDetails: true;
    vehicles: {
      include: {
        Vehicle: true;
      };
    };
  };
}>;

export const createDelegation = async (
  data: Prisma.DelegationCreateInput,
): Promise<DelegationWithRelations | null> => {
  try {
    return await prisma.delegation.create({
      data: {
        ...data,
        status: data.status ? data.status : PrismaDelegationStatus.Planned,
      },
      include: {
        Delegate: true,
        DelegationStatusEntry: { orderBy: { changedAt: 'desc' } },
        drivers: {
          include: {
            Employee: true, // Include the Employee details for each driver
          },
        },
        // NEW MANY-TO-MANY RELATIONS
        escorts: {
          include: {
            Employee: true, // Include the Employee details for each escort
          },
        },
        FlightDetails_Delegation_flightArrivalIdToFlightDetails: true, // Relation name
        FlightDetails_Delegation_flightDepartureIdToFlightDetails: true, // Relation name
        vehicles: {
          include: {
            Vehicle: true, // Include the Vehicle details for each vehicle
          },
        },
      },
    });
  } catch (error) {
    console.error('Error creating delegation:', error);
    return null;
  }
};

export const getAllDelegations = async (): Promise<DelegationWithRelations[]> => {
  try {
    return await prisma.delegation.findMany({
      include: {
        Delegate: true,
        DelegationStatusEntry: { orderBy: { changedAt: 'desc' } },
        drivers: {
          include: {
            Employee: true,
          },
        },
        // NEW MANY-TO-MANY RELATIONS
        escorts: {
          include: {
            Employee: true,
          },
        },
        FlightDetails_Delegation_flightArrivalIdToFlightDetails: true, // Relation name
        FlightDetails_Delegation_flightDepartureIdToFlightDetails: true, // Relation name
        vehicles: {
          include: {
            Vehicle: true,
          },
        },
      },
      orderBy: { durationFrom: 'desc' },
    });
  } catch (error) {
    console.error('Error fetching all delegations:', error);
    return [];
  }
};

export const getDelegationById = async (id: string): Promise<DelegationWithRelations | null> => {
  try {
    return await prisma.delegation.findUnique({
      include: {
        Delegate: true,
        DelegationStatusEntry: { orderBy: { changedAt: 'desc' } },
        drivers: {
          include: {
            Employee: true,
          },
        },
        // NEW MANY-TO-MANY RELATIONS
        escorts: {
          include: {
            Employee: true,
          },
        },
        FlightDetails_Delegation_flightArrivalIdToFlightDetails: true, // Relation name
        FlightDetails_Delegation_flightDepartureIdToFlightDetails: true, // Relation name
        vehicles: {
          include: {
            Vehicle: true,
          },
        },
      },
      where: { id },
    });
  } catch (error) {
    console.error(`Error fetching delegation with ID ${id}:`, error);
    return null;
  }
};

export const updateDelegation = async (
  id: string,
  data: CustomDelegationUpdatePayload,
): Promise<DelegationWithRelations | null> => {
  try {
    const {
      delegates: delegatesData,
      driverEmployeeIds,
      escortEmployeeIds, // Extract new arrays
      flightArrivalDetails,
      flightDepartureDetails,
      status: newStatusInput,
      statusChangeReason,
      vehicleIds,
      ...restOfData
    } = data;

    const newStatus = newStatusInput as PrismaDelegationStatus | undefined;
    const prismaDelegationUpdateData: Prisma.DelegationUpdateInput = {
      ...restOfData,
    };

    if (newStatus !== undefined) {
      prismaDelegationUpdateData.status = { set: newStatus };
    }

    return await prisma.$transaction(async tx => {
      if (newStatus && statusChangeReason !== undefined) {
        const currentDelegation = await tx.delegation.findUnique({
          select: { status: true },
          where: { id },
        });
        if (currentDelegation && newStatus !== currentDelegation.status) {
          await tx.delegationStatusEntry.create({
            data: {
              delegationId: id,
              id: `del_status_${id}_${Date.now()}`, // Generate unique ID
              reason: statusChangeReason,
              status: newStatus,
            },
          });
        }
      }

      // Fetch current delegation to get existing flight IDs for deletion/update
      const currentDelegation = await tx.delegation.findUnique({
        select: { flightArrivalId: true, flightDepartureId: true },
        where: { id },
      });

      // Handle arrival flight details
      if (flightArrivalDetails !== undefined) {
        // Check if it was provided in the payload
        if (flightArrivalDetails === null) {
          // User wants to remove existing arrival flight details
          if (currentDelegation?.flightArrivalId) {
            await tx.flightDetails.delete({ where: { id: currentDelegation.flightArrivalId } });
          }
          prismaDelegationUpdateData.FlightDetails_Delegation_flightArrivalIdToFlightDetails = {
            disconnect: true,
          };
        } else {
          // Create or update arrival flight details
          const arrivalFlight = await tx.flightDetails.upsert({
            create: {
              airport: flightArrivalDetails.airport,
              dateTime: new Date(flightArrivalDetails.dateTime),
              flightNumber: flightArrivalDetails.flightNumber,
              id: flightArrivalDetails.id ?? `flight_arrival_${id}_${Date.now()}`,
              notes: flightArrivalDetails.notes ?? null,
              terminal: flightArrivalDetails.terminal ?? null,
            },
            update: {
              airport: flightArrivalDetails.airport,
              dateTime: new Date(flightArrivalDetails.dateTime),
              flightNumber: flightArrivalDetails.flightNumber,
              notes: flightArrivalDetails.notes ?? null,
              terminal: flightArrivalDetails.terminal ?? null,
            },
            where: { id: flightArrivalDetails.id ?? 'nonexistent' },
          });
          prismaDelegationUpdateData.FlightDetails_Delegation_flightArrivalIdToFlightDetails = {
            connect: { id: arrivalFlight.id },
          };
        }
      }

      // Handle departure flight details
      if (flightDepartureDetails !== undefined) {
        // Check if it was provided in the payload
        if (flightDepartureDetails === null) {
          // User wants to remove existing departure flight details
          if (currentDelegation?.flightDepartureId) {
            await tx.flightDetails.delete({ where: { id: currentDelegation.flightDepartureId } });
          }
          prismaDelegationUpdateData.FlightDetails_Delegation_flightDepartureIdToFlightDetails = {
            disconnect: true,
          };
        } else {
          // Create or update departure flight details
          const departureFlight = await tx.flightDetails.upsert({
            create: {
              airport: flightDepartureDetails.airport,
              dateTime: new Date(flightDepartureDetails.dateTime),
              flightNumber: flightDepartureDetails.flightNumber,
              id: flightDepartureDetails.id ?? `flight_departure_${id}_${Date.now()}`,
              notes: flightDepartureDetails.notes ?? null,
              terminal: flightDepartureDetails.terminal ?? null,
            },
            update: {
              airport: flightDepartureDetails.airport,
              dateTime: new Date(flightDepartureDetails.dateTime),
              flightNumber: flightDepartureDetails.flightNumber,
              notes: flightDepartureDetails.notes ?? null,
              terminal: flightDepartureDetails.terminal ?? null,
            },
            where: { id: flightDepartureDetails.id ?? 'nonexistent' },
          });
          prismaDelegationUpdateData.FlightDetails_Delegation_flightDepartureIdToFlightDetails = {
            connect: { id: departureFlight.id },
          };
        }
      }

      // Update the delegation itself (ensure this happens after FKs might be set for connect)
      await tx.delegation.update({
        data: prismaDelegationUpdateData,
        where: { id },
      });

      if (delegatesData && Array.isArray(delegatesData)) {
        await tx.delegate.deleteMany({ where: { delegationId: id } });
        if (delegatesData.length > 0) {
          await tx.delegate.createMany({
            data: delegatesData.map((d, index) => ({
              ...d,
              delegationId: id,
              id: `delegate_${id}_${index}_${Date.now()}`, // Generate unique ID
            })),
          });
        }
      }

      // --- Handle many-to-many relations for escorts, drivers, vehicles ---
      // Fetch current relations
      const currentRelations = await tx.delegation.findUnique({
        select: {
          drivers: { select: { employeeId: true } },
          escorts: { select: { employeeId: true } },
          vehicles: { select: { vehicleId: true } },
        },
        where: { id },
      });

      const currentEscortIds = currentRelations?.escorts.map(e => e.employeeId) || [];
      const currentDriverIds = currentRelations?.drivers.map(d => d.employeeId) || [];
      const currentVehicleIds = currentRelations?.vehicles.map(v => v.vehicleId) || [];

      // Update Escorts
      if (escortEmployeeIds !== undefined) {
        const newEscortIds = new Set(escortEmployeeIds);
        const escortsToAdd = escortEmployeeIds.filter(newId => !currentEscortIds.includes(newId));
        const escortsToRemove = currentEscortIds.filter(currentId => !newEscortIds.has(currentId));

        if (escortsToRemove.length > 0) {
          await tx.delegationEscort.deleteMany({
            where: {
              delegationId: id,
              employeeId: { in: escortsToRemove },
            },
          });
        }
        if (escortsToAdd.length > 0) {
          await tx.delegationEscort.createMany({
            data: escortsToAdd.map(employeeId => ({
              delegationId: id,
              employeeId,
            })),
          });
        }
      }

      // Update Drivers
      if (driverEmployeeIds !== undefined) {
        const newDriverIds = new Set(driverEmployeeIds);
        const driversToAdd = driverEmployeeIds.filter(newId => !currentDriverIds.includes(newId));
        const driversToRemove = currentDriverIds.filter(currentId => !newDriverIds.has(currentId));

        if (driversToRemove.length > 0) {
          await tx.delegationDriver.deleteMany({
            where: {
              delegationId: id,
              employeeId: { in: driversToRemove },
            },
          });
        }
        if (driversToAdd.length > 0) {
          await tx.delegationDriver.createMany({
            data: driversToAdd.map(employeeId => ({
              delegationId: id,
              employeeId,
            })),
          });
        }
      }

      // Update Vehicles
      if (vehicleIds !== undefined) {
        const newVehicleIds = new Set(vehicleIds);
        const vehiclesToAdd = vehicleIds.filter(newId => !currentVehicleIds.includes(newId));
        const vehiclesToRemove = currentVehicleIds.filter(
          currentId => !newVehicleIds.has(currentId),
        );

        if (vehiclesToRemove.length > 0) {
          await tx.delegationVehicle.deleteMany({
            where: {
              delegationId: id,
              vehicleId: { in: vehiclesToRemove },
            },
          });
        }
        if (vehiclesToAdd.length > 0) {
          await tx.delegationVehicle.createMany({
            data: vehiclesToAdd.map(vehicleId => ({
              delegationId: id,
              vehicleId,
            })),
          });
        }
      }
      // --- End many-to-many relations handling ---

      return tx.delegation.findUnique({
        include: {
          Delegate: true,
          DelegationStatusEntry: { orderBy: { changedAt: 'desc' } },
          drivers: {
            include: {
              Employee: true,
            },
          },
          // NEW MANY-TO-MANY RELATIONS
          escorts: {
            include: {
              Employee: true,
            },
          },
          FlightDetails_Delegation_flightArrivalIdToFlightDetails: true,
          FlightDetails_Delegation_flightDepartureIdToFlightDetails: true,
          vehicles: {
            include: {
              Vehicle: true,
            },
          },
        },
        where: { id },
      });
    });
  } catch (error) {
    console.error(`Error updating delegation with ID ${id}:`, error);
    if (error instanceof PrismaClientKnownRequestError && error.code === 'P2025') {
      return null;
    }
    return null;
  }
};

export const deleteDelegation = async (
  id: string,
  userId: string,
  userRole: string,
): Promise<Delegation | null> => {
  try {
    return await prisma.$transaction(async tx => {
      // Set the RLS context for the current transaction
      // This is crucial for Supabase RLS policies to work correctly
      await tx.$executeRaw`SELECT set_config('request.jwt.claims', ${JSON.stringify({
        role: userRole,
        sub: userId,
      })}, TRUE)`;

      const delegationToDelete = await tx.delegation.findUnique({
        include: {
          Delegate: true,
          DelegationStatusEntry: true,
          drivers: true,
          // NEW MANY-TO-MANY RELATIONS
          escorts: true,
          FlightDetails_Delegation_flightArrivalIdToFlightDetails: true,
          FlightDetails_Delegation_flightDepartureIdToFlightDetails: true,
          vehicles: true,
        },
        where: { id },
      });

      if (!delegationToDelete) {
        return null;
      }

      // Delete related records first to satisfy foreign key constraints
      await tx.delegate.deleteMany({ where: { delegationId: id } });
      await tx.delegationStatusEntry.deleteMany({ where: { delegationId: id } });
      await tx.delegationEscort.deleteMany({ where: { delegationId: id } });
      await tx.delegationDriver.deleteMany({ where: { delegationId: id } });
      await tx.delegationVehicle.deleteMany({ where: { delegationId: id } });

      // Delete flight details if they exist
      if (delegationToDelete.flightArrivalId) {
        await tx.flightDetails.delete({
          where: { id: delegationToDelete.flightArrivalId },
        });
      }
      if (delegationToDelete.flightDepartureId) {
        await tx.flightDetails.delete({
          where: { id: delegationToDelete.flightDepartureId },
        });
      }

      // Finally, delete the delegation itself
      await tx.delegation.delete({ where: { id } });

      return delegationToDelete;
    });
  } catch (error) {
    console.error(`Error deleting delegation with ID ${id}:`, error);
    if (error instanceof PrismaClientKnownRequestError && error.code === 'P2025') {
      return null;
    }
    return null;
  }
};
