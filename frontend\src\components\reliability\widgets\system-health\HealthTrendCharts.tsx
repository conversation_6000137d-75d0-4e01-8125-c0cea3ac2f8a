/**
 * @file Health trend charts widget component for historical health trend visualization.
 * This component provides time-series data visualization for system health trends,
 * allowing users to track health patterns and identify issues over time.
 * @module components/reliability/widgets/system-health/HealthTrendCharts
 */

'use client';

import { BarChart3, Calendar, TrendingDown, TrendingUp } from 'lucide-react';
import React from 'react';
import {
  Area,
  AreaChart,
  Bar,
  BarChart,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from 'recharts';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from '@/components/ui/chart';
import { Skeleton } from '@/components/ui/skeleton';
import { useHealthTrends } from '@/lib/stores/queries/useReliability';
import type { HealthStatus } from '@/lib/types/domain';
import { cn } from '@/lib/utils';

/**
 * Props for the HealthTrendCharts component
 */
export interface HealthTrendChartsProps {
  /** Optional CSS class name for styling */
  className?: string;
  /** Chart type to display */
  chartType?: 'line' | 'area' | 'bar';
  /** Whether to show time range selector */
  showTimeRangeSelector?: boolean;
  /** Height of the chart container */
  height?: number;
  /** Default timeframe for trends */
  defaultTimeframe?: '1h' | '6h' | '24h' | '7d';
}

/**
 * Health trend data point interface
 */
interface HealthTrendDataPoint {
  timestamp: string;
  time: string;
  healthy: number;
  degraded: number;
  unhealthy: number;
  overallScore: number;
}

/**
 * Time range options for the selector
 */
const timeRangeOptions: { value: '1h' | '6h' | '24h' | '7d'; label: string }[] =
  [
    { value: '1h', label: '1 Hour' },
    { value: '6h', label: '6 Hours' },
    { value: '24h', label: '24 Hours' },
    { value: '7d', label: '7 Days' },
  ];

/**
 * Format time label based on time range
 */
const formatTimeLabel = (
  date: Date,
  timeRange: '1h' | '6h' | '24h' | '7d'
): string => {
  switch (timeRange) {
    case '1h':
    case '6h':
      return date.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
      });
    case '24h':
      return date.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
      });
    case '7d':
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
      });
    default:
      return date.toLocaleTimeString();
  }
};

/**
 * Health trend charts widget component.
 *
 * This component provides:
 * - Historical health trend visualization with time-series data
 * - Multiple chart types (line, area, bar) for different visualization needs
 * - Time range selection for different analysis periods
 * - Health status distribution over time
 * - Overall health score trending
 *
 * Features:
 * - Interactive charts with tooltips and hover effects
 * - Time range selector for flexible analysis periods
 * - Multiple chart types for different visualization preferences
 * - Responsive design with mobile-first approach
 * - Color-coded health status indicators
 * - Real-time data updates integration
 * - Accessibility support with proper ARIA labels
 *
 * @param props - Component props
 * @returns JSX element representing the health trend charts
 *
 * @example
 * ```tsx
 * <HealthTrendCharts
 *   chartType="area"
 *   showTimeRangeSelector={true}
 *   height={300}
 * />
 * ```
 */
export const HealthTrendCharts: React.FC<HealthTrendChartsProps> = ({
  className = '',
  chartType = 'area',
  showTimeRangeSelector = true,
  height = 300,
  defaultTimeframe = '24h',
}) => {
  const [selectedTimeRange, setSelectedTimeRange] = React.useState<
    '1h' | '6h' | '24h' | '7d'
  >(defaultTimeframe);

  const {
    data: healthTrendData,
    isLoading,
    error,
  } = useHealthTrends(selectedTimeRange);

  // Process trend data for chart visualization
  const trendData = React.useMemo(() => {
    if (!healthTrendData?.dataPoints) return [];

    return healthTrendData.dataPoints.map((point: any) => ({
      timestamp: point.timestamp,
      time: formatTimeLabel(new Date(point.timestamp), selectedTimeRange),
      healthy: point.summary.healthyChecks,
      degraded: point.summary.degradedChecks,
      unhealthy: point.summary.unhealthyChecks,
      overallScore: Math.round(
        (point.summary.healthyChecks / point.summary.totalChecks) * 100
      ),
    }));
  }, [healthTrendData, selectedTimeRange]);

  // Calculate trend direction
  const trendDirection = React.useMemo(() => {
    if (trendData.length < 2) return 'stable';

    const recent = trendData.slice(-3);
    const earlier = trendData.slice(-6, -3);

    const recentAvg =
      recent.reduce((sum: number, point: any) => sum + point.overallScore, 0) /
      recent.length;
    const earlierAvg =
      earlier.reduce((sum: number, point: any) => sum + point.overallScore, 0) /
      earlier.length;

    const difference = recentAvg - earlierAvg;

    if (difference > 2) return 'improving';
    if (difference < -2) return 'declining';
    return 'stable';
  }, [trendData]);

  // Chart configuration
  const chartConfig = {
    healthy: {
      label: 'Healthy',
      color: '#10b981',
    },
    degraded: {
      label: 'Degraded',
      color: '#f59e0b',
    },
    unhealthy: {
      label: 'Unhealthy',
      color: '#ef4444',
    },
    overallScore: {
      label: 'Overall Score',
      color: '#3b82f6',
    },
  };

  // Loading state
  if (isLoading) {
    return (
      <div className={cn('space-y-4', className)}>
        <div className="flex items-center justify-between">
          <Skeleton className="h-6 w-32" />
          <Skeleton className="h-8 w-24" />
        </div>
        <Skeleton className="w-full" style={{ height: `${height}px` }} />
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className={cn('text-center py-8', className)}>
        <BarChart3 className="h-12 w-12 text-red-500 mx-auto mb-4" />
        <p className="text-sm text-red-600 font-medium">
          Failed to load health trends
        </p>
        <p className="text-xs text-muted-foreground mt-1">
          {error.message || 'Unable to retrieve health trend data'}
        </p>
      </div>
    );
  }

  // Render chart based on type
  const renderChart = () => {
    const commonProps = {
      data: trendData,
      margin: { top: 5, right: 30, left: 20, bottom: 5 },
    };

    switch (chartType) {
      case 'line':
        return (
          <LineChart {...commonProps}>
            <XAxis
              dataKey="time"
              tick={{ fontSize: 12 }}
              tickLine={false}
              axisLine={false}
            />
            <YAxis tick={{ fontSize: 12 }} tickLine={false} axisLine={false} />
            <ChartTooltip content={<ChartTooltipContent />} />
            <Line
              type="monotone"
              dataKey="overallScore"
              stroke={chartConfig.overallScore.color}
              strokeWidth={2}
              dot={false}
            />
          </LineChart>
        );

      case 'bar':
        return (
          <BarChart {...commonProps}>
            <XAxis
              dataKey="time"
              tick={{ fontSize: 12 }}
              tickLine={false}
              axisLine={false}
            />
            <YAxis tick={{ fontSize: 12 }} tickLine={false} axisLine={false} />
            <ChartTooltip content={<ChartTooltipContent />} />
            <Bar
              dataKey="healthy"
              stackId="a"
              fill={chartConfig.healthy.color}
            />
            <Bar
              dataKey="degraded"
              stackId="a"
              fill={chartConfig.degraded.color}
            />
            <Bar
              dataKey="unhealthy"
              stackId="a"
              fill={chartConfig.unhealthy.color}
            />
          </BarChart>
        );

      case 'area':
      default:
        return (
          <AreaChart {...commonProps}>
            <XAxis
              dataKey="time"
              tick={{ fontSize: 12 }}
              tickLine={false}
              axisLine={false}
            />
            <YAxis tick={{ fontSize: 12 }} tickLine={false} axisLine={false} />
            <ChartTooltip content={<ChartTooltipContent />} />
            <Area
              type="monotone"
              dataKey="healthy"
              stackId="1"
              stroke={chartConfig.healthy.color}
              fill={chartConfig.healthy.color}
              fillOpacity={0.6}
            />
            <Area
              type="monotone"
              dataKey="degraded"
              stackId="1"
              stroke={chartConfig.degraded.color}
              fill={chartConfig.degraded.color}
              fillOpacity={0.6}
            />
            <Area
              type="monotone"
              dataKey="unhealthy"
              stackId="1"
              stroke={chartConfig.unhealthy.color}
              fill={chartConfig.unhealthy.color}
              fillOpacity={0.6}
            />
          </AreaChart>
        );
    }
  };

  return (
    <div className={cn('space-y-4', className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <BarChart3 className="h-5 w-5 text-muted-foreground" />
          <h3 className="font-semibold text-sm">Health Trends</h3>
          {trendDirection !== 'stable' && (
            <div className="flex items-center gap-1">
              {trendDirection === 'improving' ? (
                <TrendingUp className="h-4 w-4 text-green-600" />
              ) : (
                <TrendingDown className="h-4 w-4 text-red-600" />
              )}
              <span
                className={cn(
                  'text-xs font-medium',
                  trendDirection === 'improving'
                    ? 'text-green-600'
                    : 'text-red-600'
                )}
              >
                {trendDirection}
              </span>
            </div>
          )}
        </div>

        {/* Time Range Selector */}
        {showTimeRangeSelector && (
          <div className="flex items-center gap-1">
            {timeRangeOptions.map(option => (
              <Button
                key={option.value}
                variant={
                  selectedTimeRange === option.value ? 'default' : 'ghost'
                }
                size="sm"
                className="h-7 px-2 text-xs"
                onClick={() => setSelectedTimeRange(option.value)}
              >
                {option.label}
              </Button>
            ))}
          </div>
        )}
      </div>

      {/* Chart */}
      <ChartContainer
        config={chartConfig}
        className="w-full"
        style={{ height: `${height}px` }}
      >
        <ResponsiveContainer width="100%" height="100%">
          {renderChart()}
        </ResponsiveContainer>
      </ChartContainer>
    </div>
  );
};

/**
 * Default export for the HealthTrendCharts component
 */
export default HealthTrendCharts;
