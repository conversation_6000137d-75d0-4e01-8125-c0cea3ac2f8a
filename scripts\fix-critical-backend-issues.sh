#!/bin/bash

# WorkHub Backend Critical Issues Fix Script
# Addresses: Database Permission, Supabase Connection, Redis Configuration

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# Logging functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
}

info() {
    echo -e "${CYAN}ℹ️  $1${NC}"
}

echo -e "${BLUE}
╔══════════════════════════════════════════════════════════════════════════════╗
║                    WorkHub Backend Critical Issues Fix                       ║
║                                                                              ║
║  🔧 Fixes: Database Permissions, Supabase Connection, Redis Config          ║
║  🎯 Targets: Permission denied, Connection failures, Service availability    ║
╚══════════════════════════════════════════════════════════════════════════════╝
${NC}"

# Verify we're in the correct directory
if [[ ! -f "docker-compose.staging.yml" ]]; then
    error "docker-compose.staging.yml not found. Please run this script from the project root."
    exit 1
fi

# Step 1: Verify environment file fixes
log "🔍 Verifying environment configuration fixes..."

if grep -q "DATABASE_URL=.*schema=public" backend/.env; then
    success "Database URL has schema parameter"
else
    warning "Database URL might need schema parameter"
fi

if grep -q "REDIS_URL=redis://redis:6379" backend/.env; then
    success "Redis URL is correctly configured"
else
    warning "Redis URL configuration might need attention"
fi

# Step 2: Test Supabase connection from host
log "🔗 Testing Supabase connection from host system..."

SUPABASE_URL=$(grep "SUPABASE_URL=" backend/.env | cut -d'=' -f2)
SUPABASE_ANON_KEY=$(grep "SUPABASE_ANON_KEY=" backend/.env | cut -d'=' -f2)

if [[ -n "$SUPABASE_URL" && -n "$SUPABASE_ANON_KEY" ]]; then
    # Test Supabase REST API
    HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" \
        -H "apikey: $SUPABASE_ANON_KEY" \
        -H "Authorization: Bearer $SUPABASE_ANON_KEY" \
        "$SUPABASE_URL/rest/v1/" || echo "000")
    
    if [[ "$HTTP_STATUS" == "200" ]]; then
        success "Supabase REST API is accessible from host"
    else
        warning "Supabase REST API test failed (HTTP $HTTP_STATUS)"
    fi
else
    warning "Supabase credentials not found in environment file"
fi

# Step 3: Check database schema permissions
log "🗄️  Testing database connection and permissions..."

# Extract database connection details for testing
DB_URL=$(grep "DATABASE_URL=" backend/.env | cut -d'=' -f2-)
if [[ -n "$DB_URL" ]]; then
    info "Database URL found: ${DB_URL:0:50}..."
    
    # Test if psql is available for connection testing
    if command -v psql &> /dev/null; then
        log "Testing database connection with psql..."
        if psql "$DB_URL" -c "SELECT current_schema();" &> /dev/null; then
            success "Database connection successful"
        else
            warning "Database connection test failed - this might be expected if psql client is not configured"
        fi
    else
        info "psql not available, skipping direct database test"
    fi
fi

# Step 4: Stop and rebuild services with fixes
log "🛑 Stopping services for rebuild..."
docker-compose -f docker-compose.staging.yml down --remove-orphans

# Clean up any orphaned containers
docker container prune -f

log "🏗️  Rebuilding backend service with fixes..."
docker-compose -f docker-compose.staging.yml build --no-cache backend

# Step 5: Start Redis first (dependency)
log "🔴 Starting Redis service first..."
docker-compose -f docker-compose.staging.yml up -d redis

# Wait for Redis to be healthy
log "⏳ Waiting for Redis to be healthy..."
for i in {1..30}; do
    if docker-compose -f docker-compose.staging.yml ps redis | grep -q "healthy"; then
        success "Redis is healthy"
        break
    fi
    if [[ $i -eq 30 ]]; then
        error "Redis failed to become healthy"
        docker-compose -f docker-compose.staging.yml logs redis
        exit 1
    fi
    echo -n "."
    sleep 2
done

# Test Redis connectivity from host
log "🧪 Testing Redis connectivity..."
if docker exec workhub-redis-staging redis-cli ping 2>/dev/null | grep -q "PONG"; then
    success "Redis responds to PING"
else
    warning "Redis PING test failed"
fi

# Step 6: Start backend service
log "🚀 Starting backend service with fixes..."
docker-compose -f docker-compose.staging.yml up -d backend

# Monitor backend startup closely
log "👀 Monitoring backend startup (first 30 seconds)..."
sleep 5

# Show backend logs in real-time for 30 seconds
timeout 30s docker-compose -f docker-compose.staging.yml logs -f backend &
LOGS_PID=$!

# Wait for backend to be healthy or timeout
log "⏳ Waiting for backend health check..."
for i in {1..60}; do
    if docker-compose -f docker-compose.staging.yml ps backend | grep -q "healthy"; then
        success "Backend is healthy!"
        kill $LOGS_PID 2>/dev/null || true
        break
    fi
    
    if [[ $i -eq 60 ]]; then
        error "Backend failed to become healthy after 2 minutes"
        kill $LOGS_PID 2>/dev/null || true
        
        echo ""
        error "Backend startup failed. Here are the recent logs:"
        docker-compose -f docker-compose.staging.yml logs --tail=50 backend
        
        log "🔍 Checking specific error patterns..."
        
        # Check for specific errors in logs
        if docker-compose -f docker-compose.staging.yml logs backend 2>&1 | grep -q "permission denied"; then
            error "❌ PERMISSION DENIED ERROR DETECTED"
            echo "   - Database user doesn't have proper permissions"
            echo "   - Check if the database URL user has access to 'public' schema"
            echo "   - Verify Supabase project settings and user roles"
        fi
        
        if docker-compose -f docker-compose.staging.yml logs backend 2>&1 | grep -q "Supabase query failed"; then
            error "❌ SUPABASE CONNECTION ERROR DETECTED"
            echo "   - Supabase API keys might be invalid"
            echo "   - Check if Supabase project is active"
            echo "   - Verify network connectivity to Supabase"
        fi
        
        if docker-compose -f docker-compose.staging.yml logs backend 2>&1 | grep -q "redis.*unavailable"; then
            error "❌ REDIS CONNECTION ERROR DETECTED"
            echo "   - Backend cannot connect to Redis service"
            echo "   - Check Redis service name resolution"
            echo "   - Verify Docker network configuration"
        fi
        
        exit 1
    fi
    
    echo -n "."
    sleep 2
done

# Step 7: Test API endpoints
log "🧪 Testing API endpoints..."

# Test health endpoint
sleep 5
for i in {1..10}; do
    if curl -f -s "http://localhost:3001/api/health" > /dev/null; then
        success "Backend health endpoint is responding"
        
        # Show health status
        log "📊 Current health status:"
        curl -s "http://localhost:3001/api/health" | jq '.' || curl -s "http://localhost:3001/api/health"
        break
    fi
    
    if [[ $i -eq 10 ]]; then
        warning "Backend health endpoint is not responding after 10 attempts"
    fi
    
    sleep 2
done

# Step 8: Start remaining services
log "🌐 Starting remaining services..."
docker-compose -f docker-compose.staging.yml up -d

# Step 9: Final verification
log "🎯 Final service status verification..."
docker-compose -f docker-compose.staging.yml ps

echo ""
echo -e "${GREEN}
╔══════════════════════════════════════════════════════════════════════════════╗
║                           🎉 FIX PROCESS COMPLETED! 🎉                      ║
╚══════════════════════════════════════════════════════════════════════════════╝
${NC}"

echo -e "${CYAN}🔍 Verification Commands:${NC}"
echo "  📋 Check health: curl http://localhost:3001/api/health | jq"
echo "  🔴 Test Redis: docker exec workhub-redis-staging redis-cli ping"
echo "  📊 View logs: docker-compose -f docker-compose.staging.yml logs -f backend"
echo "  📈 Service status: docker-compose -f docker-compose.staging.yml ps"

echo ""
echo -e "${CYAN}🎯 Expected Fixes:${NC}"
echo "  ✅ Database permission error should be resolved"
echo "  ✅ Supabase connection should be healthy" 
echo "  ✅ Redis should be available for request deduplication"
echo "  ✅ Circuit breakers should remain healthy"

echo ""
success "Critical issues fix process completed!"

# Keep showing logs for a few more seconds
log "📊 Showing final backend logs for verification..."
docker-compose -f docker-compose.staging.yml logs --tail=20 backend
