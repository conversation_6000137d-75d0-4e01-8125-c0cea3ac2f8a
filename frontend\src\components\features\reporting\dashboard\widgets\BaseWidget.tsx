// frontend/src/components/features/reporting/dashboard/widgets/BaseWidget.tsx
import React from 'react';
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter,
} from '@/components/ui/card'; // Assuming shadcn/ui card components
import { BaseWidgetProps } from '../../data/types'; // Import BaseWidgetProps from types

/**
 * @component BaseWidget
 * @description A foundational component for all dashboard widgets, providing a consistent card-like structure.
 * Adheres to SRP by handling the common widget container UI.
 * Adheres to OCP by being extensible via `children` and `actions`.
 * @param {BaseWidgetProps} props - The component props.
 * @param {string} props.title - The title of the widget.
 * @param {string} [props.description] - An optional description for the widget.
 * @param {boolean} [props.loading] - If true, displays a loading state (e.g., skeleton or spinner).
 * @param {string} [props.error] - If provided, displays an error message.
 * @param {React.ReactNode} [props.actions] - Optional action buttons or components for the widget header.
 * @param {React.ReactNode} props.children - The main content of the widget.
 */
export const BaseWidget: React.FC<
  BaseWidgetProps & { children: React.ReactNode }
> = ({ title, description, loading, error, actions, children }) => {
  return (
    <Card className="h-full flex flex-col">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-lg font-medium">{title}</CardTitle>
        {actions && <div className="flex-shrink-0">{actions}</div>}
      </CardHeader>
      {description && (
        <CardDescription className="px-6 pb-2">{description}</CardDescription>
      )}
      <CardContent className="flex-grow p-6">
        {loading ? (
          <div className="flex items-center justify-center h-full">
            {/* Placeholder for a loading spinner or skeleton */}
            <p>Loading...</p>
          </div>
        ) : error ? (
          <div className="flex items-center justify-center h-full text-red-500">
            <p>Error: {error}</p>
          </div>
        ) : (
          children
        )}
      </CardContent>
      {/* Optional CardFooter for additional actions or info */}
      {/* <CardFooter>
				<p className="text-sm text-muted-foreground">Widget footer content</p>
			</CardFooter> */}
    </Card>
  );
};
