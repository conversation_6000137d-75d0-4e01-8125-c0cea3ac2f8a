/**
 * useDelegationFieldArrays Hook - Field Array Management
 *
 * Manages dynamic field arrays for delegates, drivers, escorts, and vehicles in delegation forms.
 * Provides centralized field array operations following SOLID principles.
 *
 * @module useDelegationFieldArrays
 */

import { useCallback } from 'react';
import { useFieldArray, useFormContext } from 'react-hook-form';
import type { DelegationFormData } from '@/lib/schemas/delegationSchemas';
import {
  delegationFormService,
  type DelegateData,
} from '../services/DelegationFormService';

// ============================================================================
// INTERFACES
// ============================================================================

export interface DelegateField extends DelegateData {
  id: string; // react-hook-form field id
}

export interface UseDelegationFieldArraysReturn {
  // Delegates management
  delegates: DelegateField[];
  appendDelegate: (delegate?: Partial<DelegateData>) => void;
  removeDelegate: (index: number) => void;
  updateDelegate: (index: number, data: Partial<DelegateData>) => void;
  moveDelegate: (from: number, to: number) => void;

  // Drivers management
  selectedDrivers: number[];
  addDriver: (driverId: number) => void;
  removeDriver: (driverId: number) => void;
  toggleDriver: (driverId: number) => void;
  clearDrivers: () => void;

  // Escorts management
  selectedEscorts: number[];
  addEscort: (escortId: number) => void;
  removeEscort: (escortId: number) => void;
  toggleEscort: (escortId: number) => void;
  clearEscorts: () => void;

  // Vehicles management
  selectedVehicles: number[];
  addVehicle: (vehicleId: number) => void;
  removeVehicle: (vehicleId: number) => void;
  toggleVehicle: (vehicleId: number) => void;
  clearVehicles: () => void;

  // Validation
  validateDelegates: () => boolean;
  validateSelections: () => boolean;

  // Utilities
  getDelegateCount: () => number;
  getDriverCount: () => number;
  getEscortCount: () => number;
  getVehicleCount: () => number;
}

export interface DelegationFieldArraysOptions {
  // Validation options
  validateOnChange?: boolean;
  minDelegates?: number;
  maxDelegates?: number;

  // Callbacks
  onDelegateChange?: (delegates: DelegateField[]) => void;
  onDriverChange?: (driverIds: number[]) => void;
  onEscortChange?: (escortIds: number[]) => void;
  onVehicleChange?: (vehicleIds: number[]) => void;
}

// ============================================================================
// HOOK IMPLEMENTATION
// ============================================================================

/**
 * Custom hook for managing delegation form field arrays
 *
 * Provides field array operations for delegates, drivers, escorts, and vehicles.
 * This is a placeholder implementation that will be fully developed in Phase 2.
 *
 * @param options - Configuration options for field arrays
 * @returns Field array management interface
 */
export const useDelegationFieldArrays = (
  options: DelegationFieldArraysOptions = {}
): UseDelegationFieldArraysReturn => {
  const {
    validateOnChange = true,
    minDelegates = 1,
    maxDelegates = 10,
    onDelegateChange,
    onDriverChange,
    onEscortChange,
    onVehicleChange,
  } = options;

  // ============================================================================
  // FORM CONTEXT
  // ============================================================================

  const { control, watch, setValue, getValues } =
    useFormContext<DelegationFormData>();

  // ============================================================================
  // FIELD ARRAYS
  // ============================================================================

  // Delegates field array
  const {
    fields: delegateFields,
    append: appendDelegateField,
    remove: removeDelegateField,
    update: updateDelegateField,
    move: moveDelegateField,
  } = useFieldArray({
    control,
    name: 'delegates',
  });

  // Watch for changes in other arrays
  const selectedDrivers = watch('driverEmployeeIds') || [];
  const selectedEscorts = watch('escortEmployeeIds') || [];
  const selectedVehicles = watch('vehicleIds') || [];

  // ============================================================================
  // DELEGATES MANAGEMENT
  // ============================================================================

  const appendDelegate = useCallback(
    (delegate?: Partial<DelegateData>) => {
      const newDelegate = {
        ...delegationFormService.createDefaultDelegate(),
        ...delegate,
      };

      appendDelegateField(newDelegate);

      if (onDelegateChange) {
        onDelegateChange([...delegateFields, newDelegate as DelegateField]);
      }
    },
    [appendDelegateField, delegateFields, onDelegateChange]
  );

  const removeDelegate = useCallback(
    (index: number) => {
      if (delegateFields.length > minDelegates) {
        removeDelegateField(index);

        if (onDelegateChange) {
          const newDelegates = delegateFields.filter((_, i) => i !== index);
          onDelegateChange(newDelegates);
        }
      }
    },
    [removeDelegateField, delegateFields, minDelegates, onDelegateChange]
  );

  const updateDelegate = useCallback(
    (index: number, data: Partial<DelegateData>) => {
      const currentDelegate = delegateFields[index];
      const updatedDelegate = { ...currentDelegate, ...data };

      updateDelegateField(index, updatedDelegate as any);

      if (onDelegateChange) {
        const newDelegates = delegateFields.map((delegate, i) =>
          i === index ? (updatedDelegate as DelegateField) : delegate
        );
        onDelegateChange(newDelegates);
      }
    },
    [updateDelegateField, delegateFields, onDelegateChange]
  );

  const moveDelegate = useCallback(
    (from: number, to: number) => {
      moveDelegateField(from, to);
    },
    [moveDelegateField]
  );

  // ============================================================================
  // DRIVERS MANAGEMENT
  // ============================================================================

  const addDriver = useCallback(
    (driverId: number) => {
      if (!selectedDrivers.includes(driverId)) {
        const newDrivers = [...selectedDrivers, driverId];
        setValue('driverEmployeeIds', newDrivers);

        if (onDriverChange) {
          onDriverChange(newDrivers);
        }
      }
    },
    [selectedDrivers, setValue, onDriverChange]
  );

  const removeDriver = useCallback(
    (driverId: number) => {
      const newDrivers = selectedDrivers.filter(id => id !== driverId);
      setValue('driverEmployeeIds', newDrivers);

      if (onDriverChange) {
        onDriverChange(newDrivers);
      }
    },
    [selectedDrivers, setValue, onDriverChange]
  );

  const toggleDriver = useCallback(
    (driverId: number) => {
      if (selectedDrivers.includes(driverId)) {
        removeDriver(driverId);
      } else {
        addDriver(driverId);
      }
    },
    [selectedDrivers, addDriver, removeDriver]
  );

  const clearDrivers = useCallback(() => {
    setValue('driverEmployeeIds', []);

    if (onDriverChange) {
      onDriverChange([]);
    }
  }, [setValue, onDriverChange]);

  // ============================================================================
  // ESCORTS MANAGEMENT
  // ============================================================================

  const addEscort = useCallback(
    (escortId: number) => {
      if (!selectedEscorts.includes(escortId)) {
        const newEscorts = [...selectedEscorts, escortId];
        setValue('escortEmployeeIds', newEscorts);

        if (onEscortChange) {
          onEscortChange(newEscorts);
        }
      }
    },
    [selectedEscorts, setValue, onEscortChange]
  );

  const removeEscort = useCallback(
    (escortId: number) => {
      const newEscorts = selectedEscorts.filter(id => id !== escortId);
      setValue('escortEmployeeIds', newEscorts);

      if (onEscortChange) {
        onEscortChange(newEscorts);
      }
    },
    [selectedEscorts, setValue, onEscortChange]
  );

  const toggleEscort = useCallback(
    (escortId: number) => {
      if (selectedEscorts.includes(escortId)) {
        removeEscort(escortId);
      } else {
        addEscort(escortId);
      }
    },
    [selectedEscorts, addEscort, removeEscort]
  );

  const clearEscorts = useCallback(() => {
    setValue('escortEmployeeIds', []);

    if (onEscortChange) {
      onEscortChange([]);
    }
  }, [setValue, onEscortChange]);

  // ============================================================================
  // VEHICLES MANAGEMENT
  // ============================================================================

  const addVehicle = useCallback(
    (vehicleId: number) => {
      if (!selectedVehicles.includes(vehicleId)) {
        const newVehicles = [...selectedVehicles, vehicleId];
        setValue('vehicleIds', newVehicles);

        if (onVehicleChange) {
          onVehicleChange(newVehicles);
        }
      }
    },
    [selectedVehicles, setValue, onVehicleChange]
  );

  const removeVehicle = useCallback(
    (vehicleId: number) => {
      const newVehicles = selectedVehicles.filter(id => id !== vehicleId);
      setValue('vehicleIds', newVehicles);

      if (onVehicleChange) {
        onVehicleChange(newVehicles);
      }
    },
    [selectedVehicles, setValue, onVehicleChange]
  );

  const toggleVehicle = useCallback(
    (vehicleId: number) => {
      if (selectedVehicles.includes(vehicleId)) {
        removeVehicle(vehicleId);
      } else {
        addVehicle(vehicleId);
      }
    },
    [selectedVehicles, addVehicle, removeVehicle]
  );

  const clearVehicles = useCallback(() => {
    setValue('vehicleIds', []);

    if (onVehicleChange) {
      onVehicleChange([]);
    }
  }, [setValue, onVehicleChange]);

  // ============================================================================
  // VALIDATION
  // ============================================================================

  const validateDelegates = useCallback((): boolean => {
    // TODO: Implement delegate validation using service
    return (
      delegateFields.length >= minDelegates &&
      delegateFields.length <= maxDelegates
    );
  }, [delegateFields, minDelegates, maxDelegates]);

  const validateSelections = useCallback((): boolean => {
    // TODO: Implement selection validation
    return true;
  }, []);

  // ============================================================================
  // UTILITIES
  // ============================================================================

  const getDelegateCount = useCallback(
    () => delegateFields.length,
    [delegateFields]
  );
  const getDriverCount = useCallback(
    () => selectedDrivers.length,
    [selectedDrivers]
  );
  const getEscortCount = useCallback(
    () => selectedEscorts.length,
    [selectedEscorts]
  );
  const getVehicleCount = useCallback(
    () => selectedVehicles.length,
    [selectedVehicles]
  );

  // ============================================================================
  // RETURN INTERFACE
  // ============================================================================

  return {
    // Delegates management
    delegates: delegateFields as DelegateField[],
    appendDelegate,
    removeDelegate,
    updateDelegate,
    moveDelegate,

    // Drivers management
    selectedDrivers,
    addDriver,
    removeDriver,
    toggleDriver,
    clearDrivers,

    // Escorts management
    selectedEscorts,
    addEscort,
    removeEscort,
    toggleEscort,
    clearEscorts,

    // Vehicles management
    selectedVehicles,
    addVehicle,
    removeVehicle,
    toggleVehicle,
    clearVehicles,

    // Validation
    validateDelegates,
    validateSelections,

    // Utilities
    getDelegateCount,
    getDriverCount,
    getEscortCount,
    getVehicleCount,
  };
};

// ============================================================================
// EXPORTS
// ============================================================================

export default useDelegationFieldArrays;
