/**
 * @file AggregateReportGenerator.tsx
 * @description Component for generating aggregate entity reports
 */

import { subDays } from 'date-fns';
import {
  BarChart3,
  Calendar,
  Car,
  ClipboardList,
  Download,
  FileText,
  Filter,
  TrendingUp,
  Users,
} from 'lucide-react';
import React, { useState } from 'react';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { toastService } from '@/lib/services/toastService';

import { DateRangePicker } from '../components/DateRangePicker';
import { useExport } from '../exports/hooks/useExport';
import {
  useReportGeneration,
  useReportTemplates,
} from '../hooks/useReportGeneration';

/**
 * Entity type configurations for aggregate reports
 */
const ENTITY_TYPES = [
  {
    color: 'bg-blue-100 text-blue-800',
    description: 'Aggregate delegation analytics and trends',
    icon: ClipboardList,
    id: 'delegations',
    metrics: [
      'Total Count',
      'Completion Rate',
      'Average Duration',
      'Status Distribution',
    ],
    name: 'Delegations',
  },
  {
    color: 'bg-green-100 text-green-800',
    description: 'Task performance and completion analytics',
    icon: FileText,
    id: 'tasks',
    metrics: [
      'Total Tasks',
      'Completion Rate',
      'Average Time',
      'Priority Distribution',
    ],
    name: 'Tasks',
  },
  {
    color: 'bg-orange-100 text-orange-800',
    description: 'Vehicle utilization and maintenance analytics',
    icon: Car,
    id: 'vehicles',
    metrics: [
      'Fleet Size',
      'Utilization Rate',
      'Maintenance Costs',
      'Performance Metrics',
    ],
    name: 'Vehicles',
  },
  {
    color: 'bg-purple-100 text-purple-800',
    description: 'Employee performance and workload analytics',
    icon: Users,
    id: 'employees',
    metrics: [
      'Total Employees',
      'Performance Scores',
      'Workload Distribution',
      'Availability',
    ],
    name: 'Employees',
  },
];

/**
 * Export format options
 */
const EXPORT_FORMATS = [
  { description: 'Formatted analytics report', id: 'pdf', name: 'PDF' },
  { description: 'Spreadsheet with charts', id: 'excel', name: 'Excel' },
  { description: 'Raw data export', id: 'csv', name: 'CSV' },
];

/**
 * Filter options for different entity types
 */
const FILTER_OPTIONS = {
  delegations: [
    {
      id: 'status',
      name: 'Status',
      options: ['Active', 'Completed', 'Pending', 'Cancelled'],
      type: 'select',
    },
    {
      id: 'priority',
      name: 'Priority',
      options: ['High', 'Medium', 'Low'],
      type: 'select',
    },
    { id: 'location', name: 'Location', type: 'text' },
  ],
  employees: [
    { id: 'department', name: 'Department', type: 'text' },
    { id: 'position', name: 'Position', type: 'text' },
    {
      id: 'status',
      name: 'Status',
      options: ['Active', 'Inactive', 'On Leave'],
      type: 'select',
    },
  ],
  tasks: [
    {
      id: 'status',
      name: 'Status',
      options: ['Pending', 'In Progress', 'Completed', 'Cancelled'],
      type: 'select',
    },
    {
      id: 'priority',
      name: 'Priority',
      options: ['High', 'Medium', 'Low'],
      type: 'select',
    },
    { id: 'assignee', name: 'Assignee', type: 'text' },
  ],
  vehicles: [
    {
      id: 'status',
      name: 'Status',
      options: ['Active', 'Maintenance', 'Inactive'],
      type: 'select',
    },
    { id: 'type', name: 'Vehicle Type', type: 'text' },
    { id: 'location', name: 'Location', type: 'text' },
  ],
};

interface AggregateReportGeneratorProps {
  defaultEntityType?: string;
  onReportGenerated?: (result: any) => void;
}

/**
 * AggregateReportGenerator Component
 *
 * Provides interface for generating aggregate analytics reports for entity types.
 */
export const AggregateReportGenerator: React.FC<
  AggregateReportGeneratorProps
> = ({ defaultEntityType = 'delegations', onReportGenerated }) => {
  // State management
  const [selectedEntityType, setSelectedEntityType] =
    useState<string>(defaultEntityType);
  const [selectedTemplate, setSelectedTemplate] = useState<string>('default');
  const [selectedFormat, setSelectedFormat] = useState<string>('pdf');
  const [dateRange, setDateRange] = useState<null | { from: Date; to: Date }>({
    from: subDays(new Date(), 29), // Default to last 30 days
    to: new Date(),
  });
  const [filters, setFilters] = useState<Record<string, any>>({});
  const [includeCharts, setIncludeCharts] = useState<boolean>(true);
  const [includeTrends, setIncludeTrends] = useState<boolean>(true);

  // Hooks
  const { error, generateAggregateReport, isGenerating } =
    useReportGeneration();
  const { isLoading: templatesLoading, templates } = useReportTemplates();
  const { exportReportToExcel, exportReportToPDF, exportToCSV } = useExport();

  /**
   * Handle filter change
   */
  const handleFilterChange = (filterId: string, value: any) => {
    setFilters(prev => ({
      ...prev,
      [filterId]: value,
    }));
  };

  /**
   * Handle report generation and export
   */
  const handleGenerateReport = async () => {
    try {
      const reportFilters = {
        ...filters,
        ...(dateRange && {
          dateRange: {
            from: dateRange.from.toISOString(),
            to: dateRange.to.toISOString(),
          },
        }),
      };

      let result;

      try {
        // Generate the report data from backend
        result = await generateAggregateReport({
          entityType: selectedEntityType,
          filters: reportFilters,
          format: 'json', // Always get JSON data first
          options: {
            includeCharts,
            includeTrends,
          },
          template: selectedTemplate,
        });
      } catch (apiError) {
        console.warn('API call failed, providing fallback data:', apiError);

        // Provide fallback data structure when API fails
        result = {
          data: {
            priorityDistribution: [],
            records: [],
            statusDistribution: [],
            summary: {
              generatedAt: new Date().toISOString(),
              message: `Unable to fetch ${selectedEntityType} data from server. This is a sample report with fallback data.`,
              note: 'Please check your connection and try again.',
            },
            totalCount: 0,
          },
          metadata: {
            entityType: selectedEntityType,
            generatedAt: new Date().toISOString(),
            generatedBy: 'System (Fallback)',
            id: `fallback_${Date.now()}`,
            note: 'Generated with fallback data due to API unavailability',
            status: 'fallback',
          },
        };
      }

      // Validate that we have data to export
      if (!result) {
        throw new Error('No report data received from server');
      }

      // Ensure data structure exists (even if empty)
      if (!result.data) {
        result.data = {
          priorityDistribution: [],
          records: [],
          statusDistribution: [],
          summary: {
            generatedAt: new Date().toISOString(),
            message: 'No data available for the selected criteria',
          },
          totalCount: 0,
        };
      }

      // Now export the data in the selected format
      const reportTitle = `${selectedEntityType.charAt(0).toUpperCase() + selectedEntityType.slice(1)} Analytics Report`;
      const filename = `${selectedEntityType}-analytics-${new Date().toISOString().split('T')[0]}`;

      // Export using the selected format with proper error handling
      try {
        // Show initial feedback to user
        toastService.show({
          description: `Creating ${selectedFormat.toUpperCase()} report for ${selectedEntityType}...`,
          duration: 2000,
          title: 'Generating Report',
        });

        switch (selectedFormat) {
          case 'csv': {
            const csvData = Array.isArray(result.data)
              ? result.data
              : [result.data];
            exportToCSV(csvData, { filename });
            console.log('CSV export completed successfully');
            break;
          }
          case 'excel': {
            exportReportToExcel(result, selectedEntityType, filename);
            console.log('Excel export completed successfully');
            break;
          }
          case 'pdf': {
            console.log('Starting PDF export with data:', result);
            await exportReportToPDF(
              result,
              selectedEntityType as
                | 'delegations'
                | 'employees'
                | 'tasks'
                | 'vehicles',
              reportTitle,
              filename
            );
            console.log('PDF export completed successfully');
            break;
          }
          default: {
            throw new Error(`Unsupported export format: ${selectedFormat}`);
          }
        }

        // Only show success message after export completes successfully
        toastService.success(
          'Report Generated Successfully',
          `${selectedFormat.toUpperCase()} report has been generated and downloaded. ID: ${result.metadata?.id || 'N/A'}`
        );
      } catch (exportError: any) {
        console.error(
          `Failed to export ${selectedFormat} report:`,
          exportError
        );
        toastService.error(
          'Export Failed',
          `Report was generated but ${selectedFormat.toUpperCase()} export failed: ${exportError.message || 'Unknown export error'}`
        );
        throw new Error(
          `Report generated successfully but export failed: ${exportError.message || 'Unknown export error'}`
        );
      }

      onReportGenerated?.(result);
    } catch (error) {
      console.error('Failed to generate aggregate report:', error);

      // Re-throw the error to ensure it's properly handled by the useReportGeneration hook
      // This will set the error state which is displayed in the UI
      throw error;
    }
  };

  /**
   * Get selected entity type configuration
   */
  const selectedEntityConfig = ENTITY_TYPES.find(
    type => type.id === selectedEntityType
  );

  /**
   * Filter templates for selected entity type
   */
  const availableTemplates =
    templates?.filter((template: any) =>
      template.entityTypes.includes(selectedEntityType)
    ) || [];

  /**
   * Get filter options for selected entity type
   */
  const entityFilters =
    FILTER_OPTIONS[selectedEntityType as keyof typeof FILTER_OPTIONS] || [];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-xl font-semibold text-gray-900">
            Aggregate Report Generator
          </h3>
          <p className="mt-1 text-gray-600">
            Generate analytics reports with aggregated data and insights
          </p>
        </div>
        <Badge className="flex items-center gap-2" variant="outline">
          <BarChart3 className="size-4" />
          Aggregate Report
        </Badge>
      </div>

      <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
        {/* Configuration Panel */}
        <div className="space-y-6 lg:col-span-2">
          {/* Entity Type Selection */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="size-5" />
                Entity Type & Analytics
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                {ENTITY_TYPES.map(entity => {
                  const Icon = entity.icon;
                  const isSelected = selectedEntityType === entity.id;

                  return (
                    <div
                      className={`
                        cursor-pointer rounded-lg border p-4 transition-all
                        ${
                          isSelected
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-200 hover:border-gray-300'
                        }
                      `}
                      key={entity.id}
                      onClick={() => setSelectedEntityType(entity.id)}
                    >
                      <div className="flex items-start gap-3">
                        <Icon className="mt-1 size-6 text-gray-600" />
                        <div className="flex-1">
                          <div className="mb-2 flex items-center gap-2">
                            <span className="font-medium">{entity.name}</span>
                            <Badge className={entity.color} variant="secondary">
                              Analytics
                            </Badge>
                          </div>
                          <p className="mb-3 text-sm text-gray-600">
                            {entity.description}
                          </p>
                          <div className="space-y-1">
                            {entity.metrics.map(metric => (
                              <div
                                className="flex items-center gap-1 text-xs text-gray-500"
                                key={metric}
                              >
                                <div className="size-1 rounded-full bg-gray-400" />
                                {metric}
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>

          {/* Filters */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Filter className="size-5" />
                Filters & Date Range
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Date Range */}
              <div>
                <Label className="mb-2 flex items-center gap-2">
                  <Calendar className="size-4" />
                  Date Range
                </Label>
                <DateRangePicker
                  onChange={setDateRange}
                  placeholder="Select date range for analytics"
                  value={dateRange}
                />
              </div>

              {/* Entity-specific filters */}
              {entityFilters.length > 0 && (
                <div className="space-y-3">
                  <Label className="text-sm font-medium">Entity Filters</Label>
                  <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                    {entityFilters.map(filter => (
                      <div key={filter.id}>
                        <Label className="mb-1 block text-sm">
                          {filter.name}
                        </Label>
                        {filter.type === 'select' ? (
                          <Select
                            onValueChange={value => {
                              // Convert 'all' back to empty string for filtering logic
                              const filterValue = value === 'all' ? '' : value;
                              handleFilterChange(filter.id, filterValue);
                            }}
                            value={filters[filter.id] || 'all'}
                          >
                            <SelectTrigger className="h-8">
                              <SelectValue
                                placeholder={`Select ${filter.name.toLowerCase()}`}
                              />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="all">
                                All {filter.name}s
                              </SelectItem>
                              {filter.options?.map(option => (
                                <SelectItem key={option} value={option}>
                                  {option}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        ) : (
                          <input
                            className="h-8 w-full rounded-md border border-gray-300 px-3 text-sm"
                            onChange={e =>
                              handleFilterChange(filter.id, e.target.value)
                            }
                            placeholder={`Filter by ${filter.name.toLowerCase()}`}
                            type="text"
                            value={filters[filter.id] || ''}
                          />
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Report Options */}
              <div className="space-y-3">
                <Label className="text-sm font-medium">Report Options</Label>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      checked={includeCharts}
                      id="includeCharts"
                      onCheckedChange={checked =>
                        setIncludeCharts(checked === true)
                      }
                    />
                    <Label className="text-sm" htmlFor="includeCharts">
                      Include Charts & Visualizations
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      checked={includeTrends}
                      id="includeTrends"
                      onCheckedChange={checked =>
                        setIncludeTrends(checked === true)
                      }
                    />
                    <Label className="text-sm" htmlFor="includeTrends">
                      Include Trend Analysis
                    </Label>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Summary & Generate Panel */}
        <div className="space-y-6">
          {/* Report Configuration */}
          <Card>
            <CardHeader>
              <CardTitle>Report Configuration</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Template Selection */}
              <div>
                <Label className="mb-2 block">Template</Label>
                {templatesLoading ? (
                  <LoadingSpinner />
                ) : (
                  <Select
                    onValueChange={setSelectedTemplate}
                    value={selectedTemplate}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select template" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="default">Default Analytics</SelectItem>
                      {availableTemplates.map((template: any) => (
                        <SelectItem key={template.id} value={template.id}>
                          {template.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              </div>

              {/* Export Format */}
              <div>
                <Label className="mb-2 block">Export Format</Label>
                <Select
                  onValueChange={setSelectedFormat}
                  value={selectedFormat}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {EXPORT_FORMATS.map(format => (
                      <SelectItem key={format.id} value={format.id}>
                        <div>
                          <div className="font-medium">{format.name}</div>
                          <div className="text-sm text-gray-600">
                            {format.description}
                          </div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Summary */}
              <div className="space-y-2 rounded-lg bg-gray-50 p-3">
                <div className="flex items-center gap-2">
                  {selectedEntityConfig && (
                    <>
                      <selectedEntityConfig.icon className="size-4" />
                      <span className="text-sm font-medium">
                        {selectedEntityConfig.name} Analytics
                      </span>
                    </>
                  )}
                </div>
                {dateRange && (
                  <p className="text-xs text-gray-600">
                    {dateRange.from.toLocaleDateString()} -{' '}
                    {dateRange.to.toLocaleDateString()}
                  </p>
                )}
                <div className="flex flex-wrap gap-1">
                  {Object.entries(filters)
                    .filter(([_, value]) => value)
                    .map(([key, value]) => (
                      <Badge className="text-xs" key={key} variant="outline">
                        {key}: {value}
                      </Badge>
                    ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Generate Button */}
          <Card>
            <CardContent className="pt-6">
              {error && (
                <Alert className="mb-4" variant="destructive">
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              <Button
                className="w-full"
                disabled={isGenerating}
                onClick={handleGenerateReport}
                size="lg"
              >
                {isGenerating ? (
                  <>
                    <LoadingSpinner className="mr-2 size-4" />
                    Generating Analytics...
                  </>
                ) : (
                  <>
                    <Download className="mr-2 size-4" />
                    Generate Aggregate Report
                  </>
                )}
              </Button>

              <p className="mt-2 text-center text-xs text-gray-500">
                Report will include aggregated analytics and insights
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};
