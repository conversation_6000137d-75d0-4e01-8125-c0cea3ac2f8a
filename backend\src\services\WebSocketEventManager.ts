/**
 * @file WebSocket Event Manager for the WorkHub backend.
 * This module centralizes the definition of all WebSocket event names used across the application.
 * It helps in maintaining consistency and avoiding magic strings for event handling.
 * @module services/WebSocketEventManager
 */

// CRUD Events (inspired by existing SOCKET_EVENTS)
// These should cover all primary entities and their lifecycle events.
export const CRUD_EVENTS = {
  // Vehicle Events
  VEHICLE_CREATED: 'vehicle:created',
  VEHICLE_UPDATED: 'vehicle:updated',
  VEHICLE_DELETED: 'vehicle:deleted',
  REFRESH_VEHICLES: 'refresh:vehicles', // General refresh event for vehicle list

  // Employee Events
  EMPLOYEE_CREATED: 'employee:created',
  <PERSON><PERSON><PERSON><PERSON>YEE_UPDATED: 'employee:updated',
  EMPLOYEE_DELETED: 'employee:deleted',
  REFRESH_EMPLOYEES: 'refresh:employees', // General refresh event for employee list

  // Task Events
  TASK_CREATED: 'task:created',
  TASK_UPDATED: 'task:updated',
  TASK_DELETED: 'task:deleted',
  REFRESH_TASKS: 'refresh:tasks', // General refresh event for task list

  // Delegation Events
  DELEGATION_CREATED: 'delegation:created',
  DELEGATION_UPDATED: 'delegation:updated',
  DELEGATION_DELETED: 'delegation:deleted',
  REFRESH_DELEGATIONS: 'refresh:delegations', // General refresh event for delegation list

  // Service Record Events (example, expand as needed)
  SERVICE_RECORD_CREATED: 'servicerecord:created',
  SERVICE_RECORD_UPDATED: 'servicerecord:updated',
  SERVICE_RECORD_DELETED: 'servicerecord:deleted',
  REFRESH_SERVICE_RECORDS: 'refresh:servicerecords',
} as const; // Use 'as const' for stricter type checking and autocompletion

// Reliability Monitoring Events (from ReliabilityWebSocketService)
export const RELIABILITY_EVENTS = {
  HEALTH_UPDATE: 'health-update',
  ALERT_CREATED: 'alert-created', // Covers new alerts
  // ALERT_UPDATED: 'alert-updated', // Consider if needed, or if alert-created covers changes
  // ALERT_RESOLVED: 'alert-resolved', // Consider if needed, or if alert-created covers changes
  METRICS_UPDATE: 'metrics-update',
  CIRCUIT_BREAKER_UPDATE: 'circuit-breaker-update',

  // Events for clients joining/leaving reliability monitoring (from original server.ts)
  JOIN_RELIABILITY_MONITORING: 'join-reliability-monitoring',
  LEAVE_RELIABILITY_MONITORING: 'leave-reliability-monitoring',
  RELIABILITY_MESSAGE: 'reliability-message', // Generic message for reliability
  RELIABILITY_ECHO: 'reliability-echo', // Echo for testing
} as const;

// System Events (e.g., global notifications, user-specific messages)
export const SYSTEM_EVENTS = {
  USER_NOTIFICATION: 'system:user-notification', // Example: for direct messages to a user
  GLOBAL_ANNOUNCEMENT: 'system:global-announcement', // Example: for system-wide messages
  // Add other system-level events as needed
} as const;

// Combine all event objects for a comprehensive list if needed, or use them separately.
export const ALL_WEBSOCKET_EVENTS = {
  ...CRUD_EVENTS,
  ...RELIABILITY_EVENTS,
  ...SYSTEM_EVENTS,
};

/**
 * WebSocketEventManager class (Optional - can start with just constants).
 * If more complex event management logic is needed (e.g., validation, payload transformation),
 * this class can be expanded.
 */
export class WebSocketEventManager {
  constructor() {
    console.log('WebSocketEventManager initialized with defined event constants.');
  }

  // Example utility: Validate if an event name is known
  public isValidEvent(eventName: string): boolean {
    return Object.values(ALL_WEBSOCKET_EVENTS).includes(eventName as any);
  }

  // More methods can be added here for event routing, logging, etc.
}

// Optional: Singleton pattern for the manager
let eventManagerInstance: WebSocketEventManager | null = null;

export const getWebSocketEventManager = (): WebSocketEventManager => {
  if (!eventManagerInstance) {
    eventManagerInstance = new WebSocketEventManager();
  }
  return eventManagerInstance;
};

console.log('WebSocket Event Manager loaded with event definitions.'); 