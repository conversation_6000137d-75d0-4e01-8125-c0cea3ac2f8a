/**
 * @file Enhanced Vehicle API service using BaseApiService patterns.
 * @module api/services/vehicleApi
 */

import type {
  CreateVehicleRequest,
  UpdateVehicleRequest,
  VehicleApiResponse,
} from '../../../types/api';
import type { Vehicle } from '../../../types/domain';
import type { ApiClient } from '../../core/apiClient';

import {
  BaseApiService,
  type DataTransformer,
  type ServiceConfig,
} from '../../core/baseApiService';

import { VehicleTransformer as VehicleApiTransformer } from '../../../transformers/vehicleTransformer';

const VehicleTransformer: DataTransformer<Vehicle> = {
  fromApi: (data: VehicleApiResponse) => VehicleApiTransformer.fromApi(data),
  toApi: (data: CreateVehicleRequest | UpdateVehicleRequest) => data,
};

/**
 * Enhanced Vehicle API Service with production-grade patterns
 */
export class VehicleApiService extends BaseApiService<
  Vehicle,
  CreateVehicleRequest,
  UpdateVehicleRequest
> {
  protected endpoint = '/vehicles';
  protected transformer: DataTransformer<Vehicle> = VehicleTransformer;

  constructor(apiClient: ApiClient, config?: ServiceConfig) {
    super(apiClient, {
      cacheDuration: 10 * 60 * 1000, // 10 minutes for vehicles
      circuitBreakerThreshold: 5,
      enableMetrics: true,
      retryAttempts: 3,
      ...config,
    });
  }

  async getAvailableVehicles(
    startDate: Date,
    endDate: Date
  ): Promise<Vehicle[]> {
    const result = await this.getAll({
      available: true,
      endDate: endDate.toISOString(),
      startDate: startDate.toISOString(),
    });
    return result.data;
  }

  // Vehicle-specific methods
  async getByStatus(status: string): Promise<Vehicle[]> {
    const result = await this.getAll({ status });
    return result.data;
  }
}
