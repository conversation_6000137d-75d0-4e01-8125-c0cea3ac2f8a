/**
 * @file IndividualReportGenerator.tsx
 * @description Component for generating individual entity reports
 */

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import {
  FileText,
  Download,
  Search,
  User,
  Car,
  ClipboardList,
  Users,
} from 'lucide-react';
import {
  useReportGeneration,
  useReportTemplates,
} from '../hooks/useReportGeneration';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { Alert, AlertDescription } from '@/components/ui/alert';

/**
 * Entity type configurations
 */
const ENTITY_TYPES = [
  {
    id: 'delegations',
    name: 'Delegation',
    icon: ClipboardList,
    placeholder: 'Enter delegation ID',
    description: 'Generate detailed report for a specific delegation',
  },
  {
    id: 'tasks',
    name: 'Task',
    icon: FileText,
    placeholder: 'Enter task ID',
    description: 'Generate detailed report for a specific task',
  },
  {
    id: 'vehicles',
    name: 'Vehicle',
    icon: Car,
    placeholder: 'Enter vehicle ID or license plate',
    description: 'Generate detailed report for a specific vehicle',
  },
  {
    id: 'employees',
    name: 'Employee',
    icon: Users,
    placeholder: 'Enter employee ID or email',
    description: 'Generate detailed report for a specific employee',
  },
];

/**
 * Export format options
 */
const EXPORT_FORMATS = [
  { id: 'pdf', name: 'PDF', description: 'Formatted document for printing' },
  { id: 'excel', name: 'Excel', description: 'Spreadsheet format' },
  { id: 'csv', name: 'CSV', description: 'Data export format' },
];

interface IndividualReportGeneratorProps {
  defaultEntityType?: string;
  defaultEntityId?: string;
  onReportGenerated?: (result: any) => void;
}

/**
 * IndividualReportGenerator Component
 *
 * Provides interface for generating detailed reports for individual entities.
 */
export const IndividualReportGenerator: React.FC<
  IndividualReportGeneratorProps
> = ({
  defaultEntityType = 'delegations',
  defaultEntityId = '',
  onReportGenerated,
}) => {
  // State management
  const [selectedEntityType, setSelectedEntityType] =
    useState<string>(defaultEntityType);
  const [entityId, setEntityId] = useState<string>(defaultEntityId);
  const [selectedTemplate, setSelectedTemplate] = useState<string>('default');
  const [selectedFormat, setSelectedFormat] = useState<string>('pdf');

  // Hooks
  const { generateIndividualReport, isGenerating, error } =
    useReportGeneration();
  const { templates, isLoading: templatesLoading } = useReportTemplates();

  /**
   * Handle report generation
   */
  const handleGenerateReport = async () => {
    if (!entityId.trim()) {
      return;
    }

    try {
      const result = await generateIndividualReport({
        entityType: selectedEntityType,
        entityId: entityId.trim(),
        template: selectedTemplate,
        format: selectedFormat,
      });

      onReportGenerated?.(result);
    } catch (error) {
      console.error('Failed to generate individual report:', error);
    }
  };

  /**
   * Get selected entity type configuration
   */
  const selectedEntityConfig = ENTITY_TYPES.find(
    type => type.id === selectedEntityType
  );

  /**
   * Filter templates for selected entity type
   */
  const availableTemplates =
    templates?.filter((template: any) =>
      template.entityTypes.includes(selectedEntityType)
    ) || [];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-xl font-semibold text-gray-900">
            Individual Report Generator
          </h3>
          <p className="text-gray-600 mt-1">
            Generate detailed reports for specific entities
          </p>
        </div>
        <Badge variant="outline" className="flex items-center gap-2">
          <User className="h-4 w-4" />
          Individual Report
        </Badge>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Configuration Panel */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Search className="h-5 w-5" />
              Entity Selection
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Entity Type Selection */}
            <div>
              <Label className="mb-2 block">Entity Type</Label>
              <Select
                value={selectedEntityType}
                onValueChange={setSelectedEntityType}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {ENTITY_TYPES.map(type => {
                    const Icon = type.icon;
                    return (
                      <SelectItem key={type.id} value={type.id}>
                        <div className="flex items-center gap-2">
                          <Icon className="h-4 w-4" />
                          <div>
                            <div className="font-medium">{type.name}</div>
                            <div className="text-sm text-gray-600">
                              {type.description}
                            </div>
                          </div>
                        </div>
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
            </div>

            {/* Entity ID Input */}
            <div>
              <Label htmlFor="entityId" className="mb-2 block">
                {selectedEntityConfig?.name} ID
              </Label>
              <Input
                id="entityId"
                value={entityId}
                onChange={e => setEntityId(e.target.value)}
                placeholder={selectedEntityConfig?.placeholder}
                className="w-full"
              />
              <p className="text-sm text-gray-500 mt-1">
                {selectedEntityConfig?.description}
              </p>
            </div>

            {/* Template Selection */}
            <div>
              <Label className="mb-2 block">Report Template</Label>
              {templatesLoading ? (
                <LoadingSpinner />
              ) : (
                <Select
                  value={selectedTemplate}
                  onValueChange={setSelectedTemplate}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a template" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="default">
                      <div>
                        <div className="font-medium">Default Template</div>
                        <div className="text-sm text-gray-600">
                          Standard individual report format
                        </div>
                      </div>
                    </SelectItem>
                    {availableTemplates.map((template: any) => (
                      <SelectItem key={template.id} value={template.id}>
                        <div>
                          <div className="font-medium">{template.name}</div>
                          <div className="text-sm text-gray-600">
                            {template.description}
                          </div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            </div>

            {/* Export Format */}
            <div>
              <Label className="mb-2 block">Export Format</Label>
              <Select value={selectedFormat} onValueChange={setSelectedFormat}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {EXPORT_FORMATS.map(format => (
                    <SelectItem key={format.id} value={format.id}>
                      <div>
                        <div className="font-medium">{format.name}</div>
                        <div className="text-sm text-gray-600">
                          {format.description}
                        </div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Preview & Generate Panel */}
        <Card>
          <CardHeader>
            <CardTitle>Report Preview</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Report Summary */}
            <div className="bg-gray-50 p-4 rounded-lg space-y-3">
              <div>
                <Label className="text-sm font-medium text-gray-600">
                  Entity Type
                </Label>
                <div className="flex items-center gap-2 mt-1">
                  {selectedEntityConfig && (
                    <>
                      <selectedEntityConfig.icon className="h-4 w-4 text-gray-600" />
                      <span className="text-sm">
                        {selectedEntityConfig.name}
                      </span>
                    </>
                  )}
                </div>
              </div>

              <div>
                <Label className="text-sm font-medium text-gray-600">
                  Entity ID
                </Label>
                <p className="mt-1 text-sm font-mono bg-white px-2 py-1 rounded border">
                  {entityId || 'Not specified'}
                </p>
              </div>

              <div>
                <Label className="text-sm font-medium text-gray-600">
                  Template
                </Label>
                <p className="mt-1 text-sm">
                  {availableTemplates.find(
                    (t: any) => t.id === selectedTemplate
                  )?.name || 'Default Template'}
                </p>
              </div>

              <div>
                <Label className="text-sm font-medium text-gray-600">
                  Format
                </Label>
                <p className="mt-1 text-sm">
                  {EXPORT_FORMATS.find(f => f.id === selectedFormat)?.name}
                </p>
              </div>
            </div>

            {/* Error Display */}
            {error && (
              <Alert variant="destructive">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            {/* Generate Button */}
            <Button
              onClick={handleGenerateReport}
              disabled={!entityId.trim() || isGenerating}
              className="w-full"
              size="lg"
            >
              {isGenerating ? (
                <>
                  <LoadingSpinner className="mr-2 h-4 w-4" />
                  Generating Report...
                </>
              ) : (
                <>
                  <Download className="mr-2 h-4 w-4" />
                  Generate Individual Report
                </>
              )}
            </Button>

            <p className="text-xs text-gray-500 text-center">
              Report will include detailed information about the selected{' '}
              {selectedEntityConfig?.name.toLowerCase()}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {ENTITY_TYPES.map(type => {
              const Icon = type.icon;
              return (
                <Button
                  key={type.id}
                  variant="outline"
                  className="h-auto p-4 flex flex-col items-center gap-2"
                  onClick={() => {
                    setSelectedEntityType(type.id);
                    setEntityId('');
                  }}
                >
                  <Icon className="h-6 w-6" />
                  <div className="text-center">
                    <div className="font-medium">{type.name} Report</div>
                    <div className="text-xs text-gray-600">
                      Generate {type.name.toLowerCase()} report
                    </div>
                  </div>
                </Button>
              );
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
