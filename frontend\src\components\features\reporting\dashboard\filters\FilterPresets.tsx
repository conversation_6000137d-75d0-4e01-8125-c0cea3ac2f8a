// frontend/src/components/features/reporting/dashboard/filters/FilterPresets.tsx

import React from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';
import { Save, ChevronDown, Trash2, Star } from 'lucide-react';
import {
  useReportingFiltersPresets,
  useReportingFilters,
} from '../../data/stores/useReportingFiltersStore';

interface FilterPresetsProps {
  className?: string;
}

/**
 * @component FilterPresets
 * @description Filter presets management component for reporting dashboard
 *
 * Responsibilities:
 * - Provides preset save/load/delete functionality
 * - Manages preset storage and retrieval
 * - Displays available presets with quick access
 * - Handles preset validation and naming
 *
 * SOLID Principles Applied:
 * - SRP: Single responsibility of managing filter presets
 * - OCP: Open for extension via props
 * - DIP: Depends on filter store abstractions
 */

// Custom hook to manage presets state efficiently
const usePresets = () => {
  const [presets, setPresets] = React.useState<Record<string, any>>({});
  const [refreshTrigger, setRefreshTrigger] = React.useState(0);

  // Load presets from localStorage
  const loadPresets = React.useCallback(() => {
    try {
      const stored = localStorage.getItem('reporting-filter-presets');
      const loadedPresets = stored ? JSON.parse(stored) : {};
      setPresets(loadedPresets);
    } catch {
      setPresets({});
    }
  }, []);

  // Load presets on mount and when refresh is triggered
  React.useEffect(() => {
    loadPresets();
  }, [loadPresets, refreshTrigger]);

  // Trigger refresh
  const refreshPresets = React.useCallback(() => {
    setRefreshTrigger(prev => prev + 1);
  }, []);

  return { presets, refreshPresets };
};

export const FilterPresets: React.FC<FilterPresetsProps> = ({
  className = '',
}) => {
  const { applyPreset, saveAsPreset, deletePreset } =
    useReportingFiltersPresets();
  const currentFilters = useReportingFilters();
  const { presets, refreshPresets } = usePresets();

  const [isDialogOpen, setIsDialogOpen] = React.useState(false);
  const [presetName, setPresetName] = React.useState('');

  // Handle save preset
  const handleSavePreset = React.useCallback(() => {
    if (presetName.trim()) {
      saveAsPreset(presetName.trim());
      setPresetName('');
      setIsDialogOpen(false);
      refreshPresets(); // Refresh presets after saving
    }
  }, [presetName, saveAsPreset, refreshPresets]);

  // Handle apply preset
  const handleApplyPreset = React.useCallback(
    (name: string) => {
      applyPreset(name);
    },
    [applyPreset]
  );

  // Handle delete preset
  const handleDeletePreset = React.useCallback(
    (name: string) => {
      deletePreset(name);
      refreshPresets(); // Refresh presets after deleting
    },
    [deletePreset, refreshPresets]
  );

  // Get preset summary
  const getPresetSummary = React.useCallback((preset: any) => {
    const parts = [];
    if (preset.status?.length > 0) parts.push(`${preset.status.length} status`);
    if (preset.locations?.length > 0)
      parts.push(`${preset.locations.length} locations`);
    if (preset.employees?.length > 0)
      parts.push(`${preset.employees.length} employees`);
    if (preset.vehicles?.length > 0)
      parts.push(`${preset.vehicles.length} vehicles`);

    return parts.length > 0 ? parts.join(', ') : 'No filters';
  }, []);

  // Default presets
  const defaultPresets = [
    {
      name: 'Last 30 Days',
      description: 'All delegations from the last 30 days',
      action: () => {
        // Apply default 30-day filter
        const filters = {
          dateRange: {
            from: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
            to: new Date(),
          },
          status: [],
          locations: [],
          employees: [],
          vehicles: [],
        };
        // This would need to be implemented in the store
        console.log('Applying 30-day preset', filters);
      },
    },
    {
      name: 'Active Delegations',
      description: 'In progress and approved delegations',
      action: () => {
        console.log('Applying active delegations preset');
      },
    },
    {
      name: 'Completed This Month',
      description: 'Completed delegations from current month',
      action: () => {
        console.log('Applying completed this month preset');
      },
    },
  ];

  const presetNames = Object.keys(presets);

  return (
    <div className={cn('space-y-3', className)}>
      <div className="flex items-center justify-between">
        <Label className="text-sm font-medium">Filter Presets</Label>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button variant="outline" size="sm" className="h-8">
              <Save className="h-4 w-4 mr-2" />
              Save
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>Save Filter Preset</DialogTitle>
              <DialogDescription>
                Save your current filter settings as a preset for quick access
                later.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="preset-name">Preset Name</Label>
                <Input
                  id="preset-name"
                  placeholder="Enter preset name..."
                  value={presetName}
                  onChange={e => setPresetName(e.target.value)}
                  onKeyDown={e => {
                    if (e.key === 'Enter') {
                      handleSavePreset();
                    }
                  }}
                />
              </div>
              <div className="p-3 bg-muted rounded-lg">
                <p className="text-sm text-muted-foreground mb-2">
                  Current filters:
                </p>
                <p className="text-sm">{getPresetSummary(currentFilters)}</p>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleSavePreset} disabled={!presetName.trim()}>
                Save Preset
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {/* Default presets */}
      <div className="space-y-2">
        <p className="text-xs text-muted-foreground">Quick Presets</p>
        <div className="flex flex-wrap gap-2">
          {defaultPresets.map(preset => (
            <Button
              key={preset.name}
              variant="outline"
              size="sm"
              onClick={preset.action}
              className="h-8 text-xs"
            >
              <Star className="h-3 w-3 mr-1" />
              {preset.name}
            </Button>
          ))}
        </div>
      </div>

      {/* Saved presets */}
      {presetNames.length > 0 && (
        <div className="space-y-2">
          <p className="text-xs text-muted-foreground">Saved Presets</p>
          <div className="space-y-1">
            {presetNames.map(name => (
              <div
                key={name}
                className="flex items-center justify-between p-2 rounded-lg border bg-card"
              >
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium truncate">{name}</p>
                  <p className="text-xs text-muted-foreground">
                    {getPresetSummary(presets[name])}
                  </p>
                </div>
                <div className="flex items-center gap-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleApplyPreset(name)}
                    className="h-8 px-2 text-xs"
                  >
                    Apply
                  </Button>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                        <ChevronDown className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => handleApplyPreset(name)}>
                        Apply Preset
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        onClick={() => handleDeletePreset(name)}
                        className="text-red-600"
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default FilterPresets;
