import { EmployeeRole } from '../generated/prisma/index.js';
import prisma from '../models/index.js';
import logger from '../utils/logger.js';

/**
 * Business Logic Validation Service for Assignment Operations
 * Implements role-based validation for Task and Delegation assignments
 * Phase 2: Backend API Enhancement
 */

export interface ValidationResult {
  code?: string;
  error?: string;
  isValid: boolean;
}

/**
 * Validates that an employee has the 'driver' role
 * @param employeeId - The employee ID to validate
 * @returns Promise<ValidationResult>
 */
export const validateDriverRole = async (employeeId: number): Promise<ValidationResult> => {
  try {
    const employee = await prisma.employee.findUnique({
      select: { id: true, name: true, role: true, status: true },
      where: { id: employeeId },
    });

    if (!employee) {
      return {
        code: 'EMPLOYEE_NOT_FOUND',
        error: `Employee with ID ${employeeId} not found`,
        isValid: false,
      };
    }

    if (employee.role !== EmployeeRole.driver) {
      return {
        code: 'INVALID_DRIVER_ROLE',
        error: `Employee ${employee.name} (ID: ${employeeId}) does not have driver role. Current role: ${employee.role}`,
        isValid: false,
      };
    }

    if (employee.status && employee.status !== 'Active') {
      return {
        code: 'EMPLOYEE_NOT_ACTIVE',
        error: `Employee ${employee.name} (ID: ${employeeId}) is not active. Current status: ${employee.status}`,
        isValid: false,
      };
    }

    return { isValid: true };
  } catch (error) {
    logger.error('Error validating driver role:', error);
    return {
      code: 'DATABASE_ERROR',
      error: 'Database error during driver role validation',
      isValid: false,
    };
  }
};

/**
 * Validates that an employee exists and is active
 * @param employeeId - The employee ID to validate
 * @returns Promise<ValidationResult>
 */
export const validateEmployeeExists = async (employeeId: number): Promise<ValidationResult> => {
  try {
    const employee = await prisma.employee.findUnique({
      select: { id: true, name: true, status: true },
      where: { id: employeeId },
    });

    if (!employee) {
      return {
        code: 'EMPLOYEE_NOT_FOUND',
        error: `Employee with ID ${employeeId} not found`,
        isValid: false,
      };
    }

    if (employee.status && employee.status !== 'Active') {
      return {
        code: 'EMPLOYEE_NOT_ACTIVE',
        error: `Employee ${employee.name} (ID: ${employeeId}) is not active. Current status: ${employee.status}`,
        isValid: false,
      };
    }

    return { isValid: true };
  } catch (error) {
    logger.error('Error validating employee existence:', error);
    return {
      code: 'DATABASE_ERROR',
      error: 'Database error during employee validation',
      isValid: false,
    };
  }
};

/**
 * Validates that multiple employees exist and are active.
 * Replaces N+1 queries with a single findMany.
 * @param employeeIds - The array of employee IDs to validate
 * @returns Promise<ValidationResult>
 */
export const validateMultipleEmployeesExist = async (
  employeeIds: number[],
): Promise<ValidationResult> => {
  if (employeeIds.length === 0) return { isValid: true };

  try {
    const employees = await prisma.employee.findMany({
      select: { id: true, name: true, status: true },
      where: {
        id: { in: employeeIds },
        status: 'Active',
      },
    });

    const foundIds = new Set(employees.map(e => e.id));
    const missingOrInactiveEmployees = employeeIds.filter(id => !foundIds.has(id));

    if (missingOrInactiveEmployees.length > 0) {
      return {
        code: 'EMPLOYEES_NOT_FOUND_OR_INACTIVE',
        error: `Employees not found or inactive: ${missingOrInactiveEmployees.join(', ')}`,
        isValid: false,
      };
    }

    return { isValid: true };
  } catch (error) {
    logger.error('Error validating multiple employees existence:', error);
    return {
      code: 'DATABASE_ERROR',
      error: 'Database error during multiple employee validation',
      isValid: false,
    };
  }
};

/**
 * Validates that a vehicle exists and is available
 * @param vehicleId - The vehicle ID to validate
 * @returns Promise<ValidationResult>
 */
export const validateVehicleExists = async (vehicleId: number): Promise<ValidationResult> => {
  try {
    const vehicle = await prisma.vehicle.findUnique({
      select: { id: true, licensePlate: true, make: true, model: true },
      where: { id: vehicleId },
    });

    if (!vehicle) {
      return {
        code: 'VEHICLE_NOT_FOUND',
        error: `Vehicle with ID ${vehicleId} not found`,
        isValid: false,
      };
    }

    return { isValid: true };
  } catch (error) {
    logger.error('Error validating vehicle existence:', error);
    return {
      code: 'DATABASE_ERROR',
      error: 'Database error during vehicle validation',
      isValid: false,
    };
  }
};

/**
 * Validates vehicle availability for a specific time period
 * @param vehicleId - The vehicle ID to check
 * @param startTime - Start time of the assignment
 * @param endTime - End time of the assignment
 * @param excludeTaskId - Task ID to exclude from conflict check (for updates)
 * @param excludeDelegationId - Delegation ID to exclude from conflict check (for updates)
 * @returns Promise<ValidationResult>
 */
export const validateVehicleAvailability = async (
  vehicleId: number,
  startTime: Date,
  endTime: Date,
  excludeTaskId?: string,
  excludeDelegationId?: string,
): Promise<ValidationResult> => {
  try {
    // Check for conflicting task assignments
    const conflictingTasks = await prisma.task.findMany({
      select: {
        dateTime: true,
        description: true,
        estimatedDuration: true,
        id: true,
      },
      where: {
        id: excludeTaskId ? { not: excludeTaskId } : undefined,
        OR: [
          {
            // Task starts during our period
            dateTime: {
              gte: startTime,
              lt: endTime,
            },
          },
          {
            // Task ends during our period (assuming estimatedDuration in minutes)
            AND: [
              { dateTime: { lte: startTime } },
              {
                dateTime: {
                  gte: new Date(startTime.getTime() - 24 * 60 * 60 * 1000), // Check 24 hours back
                },
              },
            ],
          },
        ],
        vehicleId: vehicleId,
      },
    });

    // Check for conflicting delegation assignments
    // This now queries the join table directly or uses `some` on the relation
    const conflictingDelegations = await prisma.delegation.findMany({
      select: {
        durationFrom: true,
        durationTo: true,
        eventName: true,
        id: true,
      },
      where: {
        id: excludeDelegationId ? { not: excludeDelegationId } : undefined,
        OR: [
          {
            // Delegation overlaps with our period
            AND: [{ durationFrom: { lt: endTime } }, { durationTo: { gt: startTime } }],
          },
        ],
        vehicles: {
          some: {
            vehicleId: vehicleId,
          },
        },
      },
    });

    // Check if any tasks actually conflict (considering estimated duration)
    const actualTaskConflicts = conflictingTasks.filter(task => {
      const taskEndTime = new Date(task.dateTime.getTime() + task.estimatedDuration * 60 * 1000);
      return (
        (task.dateTime >= startTime && task.dateTime < endTime) ||
        (taskEndTime > startTime && taskEndTime <= endTime) ||
        (task.dateTime <= startTime && taskEndTime >= endTime)
      );
    });

    if (actualTaskConflicts.length > 0) {
      const conflictDetails = actualTaskConflicts
        .map(task => `Task "${task.description}" (${task.dateTime.toISOString()})`)
        .join(', ');

      return {
        code: 'VEHICLE_CONFLICT_TASK',
        error: `Vehicle is already assigned to conflicting tasks: ${conflictDetails}`,
        isValid: false,
      };
    }

    if (conflictingDelegations.length > 0) {
      const conflictDetails = conflictingDelegations
        .map(
          delegation =>
            `Delegation "${
              delegation.eventName
            }" (${delegation.durationFrom.toISOString()} - ${delegation.durationTo.toISOString()})`,
        )
        .join(', ');

      return {
        code: 'VEHICLE_CONFLICT_DELEGATION',
        error: `Vehicle is already assigned to conflicting delegations: ${conflictDetails}`,
        isValid: false,
      };
    }

    return { isValid: true };
  } catch (error) {
    logger.error('Error validating vehicle availability:', error);
    return {
      code: 'DATABASE_ERROR',
      error: 'Database error during vehicle availability validation',
      isValid: false,
    };
  }
};

/**
 * Validates availability for multiple vehicles for a specific time period.
 * This function iterates through each vehicle and calls validateVehicleAvailability.
 * @param vehicleIds - Array of vehicle IDs to check
 * @param startTime - Start time of the assignment
 * @param endTime - End time of the assignment
 * @param excludeDelegationId - Delegation ID to exclude from conflict check (for updates)
 * @returns Promise<ValidationResult>
 */
export const validateMultipleVehiclesAvailability = async (
  vehicleIds: number[],
  startTime: Date,
  endTime: Date,
  excludeDelegationId?: string,
): Promise<ValidationResult> => {
  for (const vehicleId of vehicleIds) {
    const validation = await validateVehicleAvailability(
      vehicleId,
      startTime,
      endTime,
      undefined, // excludeTaskId (not applicable for delegation context)
      excludeDelegationId,
    );
    if (!validation.isValid) {
      // If any single vehicle is not available, the whole assignment is invalid
      return {
        code: validation.code,
        error: `Vehicle ID ${vehicleId} conflict: ${validation.error}`,
        isValid: false,
      };
    }
  }
  return { isValid: true };
};

/**
 * Validates that multiple vehicles exist.
 * @param vehicleIds - The array of vehicle IDs to validate
 * @returns Promise<ValidationResult>
 */
export const validateMultipleVehiclesExist = async (
  vehicleIds: number[],
): Promise<ValidationResult> => {
  if (vehicleIds.length === 0) return { isValid: true };

  try {
    const vehicles = await prisma.vehicle.findMany({
      select: { id: true, make: true, model: true },
      where: {
        id: { in: vehicleIds },
      },
    });

    const foundIds = new Set(vehicles.map(v => v.id));
    const missingVehicles = vehicleIds.filter(id => !foundIds.has(id));

    if (missingVehicles.length > 0) {
      return {
        code: 'VEHICLES_NOT_FOUND',
        error: `Vehicles not found: ${missingVehicles.join(', ')}`,
        isValid: false,
      };
    }

    return { isValid: true };
  } catch (error) {
    logger.error('Error validating multiple vehicles existence:', error);
    return {
      code: 'DATABASE_ERROR',
      error: 'Database error during multiple vehicle validation',
      isValid: false,
    };
  }
};

/**
 * Validates vehicle assignment logic: vehicle can only be assigned if driver is present
 * Validates vehicle assignment logic: vehicle can only be assigned if driver is present
 * @param driverEmployeeIds - Array of driver employee IDs
 * @param vehicleIds - Array of vehicle IDs
 * @returns ValidationResult
 */
export const validateVehicleAssignmentLogic = (
  driverEmployeeIds: number[],
  vehicleIds: number[],
): ValidationResult => {
  // If vehicles are assigned but no drivers, that's invalid
  if (vehicleIds.length > 0 && driverEmployeeIds.length === 0) {
    return {
      code: 'VEHICLE_WITHOUT_DRIVER',
      error: 'Vehicles cannot be assigned without at least one driver',
      isValid: false,
    };
  }

  // All other combinations are valid:
  // - No drivers, no vehicles (OK)
  // - Drivers with vehicles (OK)
  // - Drivers without vehicles (OK)
  return { isValid: true };
};

/**
 * Comprehensive validation for Task assignment
 * @param staffEmployeeId - Required staff employee ID
 * @param driverEmployeeId - Optional driver employee ID
 * @param vehicleId - Optional vehicle ID
 * @param taskDateTime - Task start date/time
 * @param estimatedDuration - Task duration in minutes
 * @param excludeTaskId - Task ID to exclude from conflict check (for updates)
 * @returns Promise<ValidationResult>
 */
export const validateTaskAssignment = async (
  staffEmployeeId: number,
  driverEmployeeId?: null | number,
  vehicleId?: null | number,
  taskDateTime?: Date,
  estimatedDuration?: number,
  excludeTaskId?: string,
): Promise<ValidationResult> => {
  // Validate staff employee exists
  const staffValidation = await validateEmployeeExists(staffEmployeeId);
  if (!staffValidation.isValid) {
    return staffValidation;
  }

  // Validate driver if provided
  if (driverEmployeeId) {
    const driverValidation = await validateDriverRole(driverEmployeeId);
    if (!driverValidation.isValid) {
      return driverValidation;
    }
  }

  // Validate vehicle if provided
  if (vehicleId) {
    const vehicleValidation = await validateVehicleExists(vehicleId);
    if (!vehicleValidation.isValid) {
      return vehicleValidation;
    }

    // Validate vehicle availability if time information is provided
    if (taskDateTime && estimatedDuration) {
      const taskEndTime = new Date(taskDateTime.getTime() + estimatedDuration * 60 * 1000);
      const availabilityValidation = await validateVehicleAvailability(
        vehicleId,
        taskDateTime,
        taskEndTime,
        excludeTaskId,
      );
      if (!availabilityValidation.isValid) {
        return availabilityValidation;
      }
    }
  }

  // Validate vehicle assignment logic
  // NOTE: This function is designed for single driver/vehicle.
  // For tasks, we still assume single driver/vehicle.
  const logicValidation = validateVehicleAssignmentLogic(
    driverEmployeeId ? [driverEmployeeId] : [],
    vehicleId ? [vehicleId] : [],
  );
  if (!logicValidation.isValid) {
    return logicValidation;
  }

  return { isValid: true };
};

/**
 * Validates that employees assigned as escorts do not have the 'driver' role.
 * @param employeeIds - The array of employee IDs to validate
 * @returns Promise<ValidationResult>
 */
export const validateEscortRoles = async (employeeIds: number[]): Promise<ValidationResult> => {
  if (employeeIds.length === 0) return { isValid: true };

  try {
    const driversAssignedAsEscorts = await prisma.employee.findMany({
      select: { id: true, name: true, role: true },
      where: {
        id: { in: employeeIds },
        role: EmployeeRole.driver,
      },
    });

    if (driversAssignedAsEscorts.length > 0) {
      const driverNames = driversAssignedAsEscorts.map(e => `${e.name} (ID: ${e.id})`).join(', ');
      return {
        code: 'INVALID_ESCORT_ROLE',
        error: `Drivers cannot be assigned as escorts: ${driverNames}`,
        isValid: false,
      };
    }

    return { isValid: true };
  } catch (error) {
    logger.error('Error validating escort roles:', error);
    return {
      code: 'DATABASE_ERROR',
      error: 'Database error during escort role validation',
      isValid: false,
    };
  }
};

/**
 * Comprehensive validation for Delegation assignment
 * @param escortEmployeeIds - Array of escort employee IDs
 * @param driverEmployeeIds - Array of driver employee IDs
 * @param vehicleIds - Array of vehicle IDs
 * @param durationFrom - Delegation start date/time
 * @param durationTo - Delegation end date/time
 * @param excludeDelegationId - Delegation ID to exclude from conflict check (for updates)
 * @returns Promise<ValidationResult>
 */
export const validateDelegationAssignment = async (
  escortEmployeeIds: number[],
  driverEmployeeIds: number[],
  vehicleIds: number[],
  durationFrom?: Date,
  durationTo?: Date,
  excludeDelegationId?: string,
): Promise<ValidationResult> => {
  // Validate all escort employees exist and are active
  const escortExistenceValidation = await validateMultipleEmployeesExist(escortEmployeeIds);
  if (!escortExistenceValidation.isValid) {
    return escortExistenceValidation;
  }

  // Validate that escorts do not have the 'driver' role
  const escortRoleValidation = await validateEscortRoles(escortEmployeeIds);
  if (!escortRoleValidation.isValid) {
    return escortRoleValidation;
  }

  // Validate all driver employees exist and have the 'driver' role
  // This still requires individual checks for roles, as validateMultipleEmployeesExist doesn't check roles.
  // A new batch role validation function could be created for further optimization if needed.
  for (const id of driverEmployeeIds) {
    const driverRoleValidation = await validateDriverRole(id);
    if (!driverRoleValidation.isValid) {
      return driverRoleValidation;
    }
  }

  // Validate all vehicles exist
  const vehicleExistenceValidation = await validateMultipleVehiclesExist(vehicleIds);
  if (!vehicleExistenceValidation.isValid) {
    return vehicleExistenceValidation;
  }

  // Validate vehicle assignment logic (multiple vehicles require at least one driver)
  const logicValidation = validateVehicleAssignmentLogic(driverEmployeeIds, vehicleIds);
  if (!logicValidation.isValid) {
    return logicValidation;
  }

  // Validate vehicle availability for all vehicles if time information is provided
  if (durationFrom && durationTo) {
    const availabilityValidation = await validateMultipleVehiclesAvailability(
      vehicleIds,
      durationFrom,
      durationTo,
      excludeDelegationId,
    );
    if (!availabilityValidation.isValid) {
      return availabilityValidation;
    }
  }

  return { isValid: true };
};
