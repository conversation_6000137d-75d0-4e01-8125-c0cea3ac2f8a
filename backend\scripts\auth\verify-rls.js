#!/usr/bin/env node

/**
 * RLS Verification Script
 * Verifies Row Level Security implementation on Supabase database
 */

import { PrismaClient } from '@prisma/client';
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const prisma = new PrismaClient();
const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_ANON_KEY);

const CORE_TABLES = ['Employee', 'Vehicle', 'ServiceRecord', 'Task', 'Delegation'];

async function verifyRLSStatus() {
  console.log('🔍 COMPREHENSIVE RLS VERIFICATION REPORT');
  console.log('==========================================\n');

  try {
    // Check RLS status for all core tables
    console.log('📊 CORE TABLES RLS STATUS:');
    console.log('---------------------------');

    const rlsQuery = `
      SELECT 
        tablename,
        rowsecurity as rls_enabled,
        (SELECT COUNT(*) FROM pg_policies WHERE schemaname = 'public' AND tablename = t.tablename) as policy_count
      FROM pg_tables t
      WHERE schemaname = 'public'
      AND tablename IN ('Employee', 'Vehicle', 'ServiceRecord', 'Task', 'Delegation')
      ORDER BY tablename;
    `;

    const rlsResults = await prisma.$queryRawUnsafe(rlsQuery);

    let allSecured = true;
    let totalPolicies = 0;

    rlsResults.forEach(table => {
      const status = table.rls_enabled ? '✅ ENABLED' : '❌ DISABLED';
      const securityLevel =
        table.rls_enabled && table.policy_count >= 4
          ? '🟢 FULLY SECURED'
          : table.rls_enabled && table.policy_count > 0
            ? '🟡 PARTIALLY SECURED'
            : table.rls_enabled
              ? '🟠 RLS ON, NO POLICIES'
              : '🔴 NOT SECURED';

      console.log(
        `${table.tablename.padEnd(15)} | ${status.padEnd(12)} | ${table.policy_count} policies | ${securityLevel}`,
      );

      if (!table.rls_enabled || table.policy_count === 0) {
        allSecured = false;
      }
      totalPolicies += parseInt(table.policy_count);
    });

    console.log('\n📋 DETAILED POLICY INFORMATION:');
    console.log('--------------------------------');

    const policiesQuery = `
      SELECT 
        tablename,
        policyname,
        cmd as operation,
        roles,
        qual IS NOT NULL as has_conditions
      FROM pg_policies 
      WHERE schemaname = 'public' 
      AND tablename IN ('Employee', 'Vehicle', 'ServiceRecord', 'Task', 'Delegation')
      ORDER BY tablename, cmd, policyname;
    `;

    const policiesResults = await prisma.$queryRawUnsafe(policiesQuery);

    if (policiesResults.length === 0) {
      console.log('❌ NO POLICIES FOUND FOR CORE TABLES');
    } else {
      policiesResults.forEach(policy => {
        const accessLevel = policy.roles.includes('authenticated')
          ? '🔐 AUTHENTICATED'
          : policy.roles.includes('anon')
            ? '🌐 ANONYMOUS'
            : '👥 CUSTOM';
        const conditions = policy.has_conditions ? '✅ HAS CONDITIONS' : '⚠️ NO CONDITIONS';

        console.log(
          `${policy.tablename.padEnd(15)} | ${policy.policyname.padEnd(25)} | ${policy.operation.padEnd(8)} | ${accessLevel.padEnd(15)} | ${conditions}`,
        );
      });
    }

    console.log('\n🚫 ANONYMOUS ACCESS CHECK:');
    console.log('---------------------------');

    const anonymousQuery = `
      SELECT COUNT(*) as anon_policies
      FROM pg_policies 
      WHERE schemaname = 'public' 
      AND 'anon' = ANY(roles);
    `;

    const anonymousResults = await prisma.$queryRawUnsafe(anonymousQuery);
    const anonCount = parseInt(anonymousResults[0].anon_policies);

    if (anonCount === 0) {
      console.log('✅ NO ANONYMOUS ACCESS POLICIES FOUND - SECURE');
    } else {
      console.log(`❌ ${anonCount} ANONYMOUS ACCESS POLICIES FOUND - SECURITY RISK`);
    }

    console.log('\n📊 SECURITY SUMMARY:');
    console.log('--------------------');
    console.log(`Core Tables: ${CORE_TABLES.length}`);
    console.log(`Tables with RLS: ${rlsResults.filter(t => t.rls_enabled).length}`);
    console.log(`Total Policies: ${totalPolicies}`);
    console.log(`Anonymous Policies: ${anonCount}`);

    const overallStatus =
      allSecured && anonCount === 0
        ? '🟢 FULLY SECURED'
        : allSecured
          ? '🟡 MOSTLY SECURED'
          : '🔴 SECURITY INCOMPLETE';
    console.log(`Overall Status: ${overallStatus}`);

    console.log('\n🎯 VERIFICATION COMPLETE');
    console.log('========================');

    if (allSecured && anonCount === 0) {
      console.log('✅ ALL SECURITY REQUIREMENTS MET');
      console.log('✅ RLS ENABLED ON ALL CORE TABLES');
      console.log('✅ POLICIES CONFIGURED FOR ALL TABLES');
      console.log('✅ NO ANONYMOUS ACCESS DETECTED');
      console.log('🎉 DATABASE IS FULLY SECURED!');
    } else {
      console.log('❌ SECURITY ISSUES DETECTED:');
      if (!allSecured) {
        console.log('   - Some tables missing RLS or policies');
      }
      if (anonCount > 0) {
        console.log('   - Anonymous access policies detected');
      }
    }
  } catch (error) {
    console.error('❌ Error during RLS verification:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

// Run verification
verifyRLSStatus().catch(console.error);
