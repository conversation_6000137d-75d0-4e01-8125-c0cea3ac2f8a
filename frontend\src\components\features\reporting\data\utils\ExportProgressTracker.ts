/**
 * @utility ExportProgressTracker
 * @description Manages export progress tracking and notifications
 *
 * Responsibilities:
 * - Track export progress for multiple concurrent exports
 * - Provide progress updates and status management
 * - Handle export cancellation
 * - Emit progress events for UI updates
 *
 * SOLID Principles Applied:
 * - SRP: Single responsibility of progress tracking
 * - OCP: Open for extension via event system
 */

import type { ExportProgress, ExportStatus } from '../types/export';

export class ExportProgressTracker {
  private progressMap: Map<string, ExportProgress> = new Map();
  private eventListeners: Map<string, ((progress: ExportProgress) => void)[]> =
    new Map();
  private cancelledExports: Set<string> = new Set();

  /**
   * Start tracking an export operation
   */
  start(exportId: string, message: string): void {
    const progress: ExportProgress = {
      id: exportId,
      status: 'processing',
      progress: 0,
      message,
      startedAt: new Date(),
    };

    this.progressMap.set(exportId, progress);
    this.emitProgress(exportId, progress);
  }

  /**
   * Update export progress
   */
  update(exportId: string, progress: number, message?: string): void {
    const currentProgress = this.progressMap.get(exportId);
    if (!currentProgress) {
      throw new Error(`Export ${exportId} not found`);
    }

    // Check if export was cancelled
    if (this.cancelledExports.has(exportId)) {
      this.error(exportId, 'Export was cancelled');
      return;
    }

    const updatedProgress: ExportProgress = {
      ...currentProgress,
      progress: Math.min(100, Math.max(0, progress)),
      message: message || currentProgress.message,
      estimatedCompletion: this.calculateEstimatedCompletion(
        currentProgress,
        progress
      ),
    };

    this.progressMap.set(exportId, updatedProgress);
    this.emitProgress(exportId, updatedProgress);
  }

  /**
   * Mark export as completed
   */
  complete(
    exportId: string,
    message: string = 'Export completed successfully'
  ): void {
    const currentProgress = this.progressMap.get(exportId);
    if (!currentProgress) {
      throw new Error(`Export ${exportId} not found`);
    }

    const completedProgress: ExportProgress = {
      ...currentProgress,
      status: 'completed',
      progress: 100,
      message,
    };

    this.progressMap.set(exportId, completedProgress);
    this.emitProgress(exportId, completedProgress);

    // Clean up after a delay
    setTimeout(() => {
      this.cleanup(exportId);
    }, 30000); // Keep completed exports for 30 seconds
  }

  /**
   * Mark export as failed
   */
  error(exportId: string, errorMessage: string): void {
    const currentProgress = this.progressMap.get(exportId);
    if (!currentProgress) {
      throw new Error(`Export ${exportId} not found`);
    }

    const errorProgress: ExportProgress = {
      ...currentProgress,
      status: 'failed',
      message: 'Export failed',
      error: errorMessage,
    };

    this.progressMap.set(exportId, errorProgress);
    this.emitProgress(exportId, errorProgress);

    // Clean up after a delay
    setTimeout(() => {
      this.cleanup(exportId);
    }, 60000); // Keep failed exports for 60 seconds
  }

  /**
   * Cancel an export operation
   */
  cancel(exportId: string): boolean {
    const currentProgress = this.progressMap.get(exportId);
    if (!currentProgress) {
      return false;
    }

    if (
      currentProgress.status === 'completed' ||
      currentProgress.status === 'failed'
    ) {
      return false; // Cannot cancel completed or failed exports
    }

    this.cancelledExports.add(exportId);

    const cancelledProgress: ExportProgress = {
      ...currentProgress,
      status: 'cancelled',
      message: 'Export cancelled by user',
    };

    this.progressMap.set(exportId, cancelledProgress);
    this.emitProgress(exportId, cancelledProgress);

    // Clean up after a delay
    setTimeout(() => {
      this.cleanup(exportId);
    }, 10000); // Keep cancelled exports for 10 seconds

    return true;
  }

  /**
   * Get current progress for an export
   */
  getProgress(exportId: string): ExportProgress | null {
    return this.progressMap.get(exportId) || null;
  }

  /**
   * Get all active exports
   */
  getActiveExports(): ExportProgress[] {
    return Array.from(this.progressMap.values()).filter(
      progress =>
        progress.status === 'processing' || progress.status === 'pending'
    );
  }

  /**
   * Get all exports (active and completed)
   */
  getAllExports(): ExportProgress[] {
    return Array.from(this.progressMap.values());
  }

  /**
   * Check if an export is cancelled
   */
  isCancelled(exportId: string): boolean {
    return this.cancelledExports.has(exportId);
  }

  /**
   * Subscribe to progress updates for a specific export
   */
  subscribe(
    exportId: string,
    callback: (progress: ExportProgress) => void
  ): () => void {
    if (!this.eventListeners.has(exportId)) {
      this.eventListeners.set(exportId, []);
    }

    this.eventListeners.get(exportId)!.push(callback);

    // Return unsubscribe function
    return () => {
      const listeners = this.eventListeners.get(exportId);
      if (listeners) {
        const index = listeners.indexOf(callback);
        if (index > -1) {
          listeners.splice(index, 1);
        }
      }
    };
  }

  /**
   * Subscribe to all export progress updates
   */
  subscribeToAll(
    callback: (exportId: string, progress: ExportProgress) => void
  ): () => void {
    const globalListeners = this.eventListeners.get('*') || [];
    globalListeners.push((progress: ExportProgress) =>
      callback(progress.id, progress)
    );
    this.eventListeners.set('*', globalListeners);

    // Return unsubscribe function
    return () => {
      const listeners = this.eventListeners.get('*');
      if (listeners) {
        const index = listeners.findIndex(
          listener => listener.toString() === callback.toString()
        );
        if (index > -1) {
          listeners.splice(index, 1);
        }
      }
    };
  }

  /**
   * Clear all progress data
   */
  clear(): void {
    this.progressMap.clear();
    this.eventListeners.clear();
    this.cancelledExports.clear();
  }

  /**
   * Private helper methods
   */
  private emitProgress(exportId: string, progress: ExportProgress): void {
    // Emit to specific export listeners
    const listeners = this.eventListeners.get(exportId);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(progress);
        } catch (error) {
          console.error('Error in export progress callback:', error);
        }
      });
    }

    // Emit to global listeners
    const globalListeners = this.eventListeners.get('*');
    if (globalListeners) {
      globalListeners.forEach(callback => {
        try {
          callback(progress);
        } catch (error) {
          console.error('Error in global export progress callback:', error);
        }
      });
    }
  }

  private calculateEstimatedCompletion(
    currentProgress: ExportProgress,
    newProgress: number
  ): Date | undefined {
    if (newProgress <= currentProgress.progress || newProgress === 0) {
      return undefined;
    }

    const elapsed = Date.now() - currentProgress.startedAt.getTime();
    const progressDelta = newProgress - currentProgress.progress;
    const remainingProgress = 100 - newProgress;

    if (progressDelta <= 0) {
      return undefined;
    }

    const estimatedRemainingTime =
      (elapsed / progressDelta) * remainingProgress;
    return new Date(Date.now() + estimatedRemainingTime);
  }

  private cleanup(exportId: string): void {
    this.progressMap.delete(exportId);
    this.eventListeners.delete(exportId);
    this.cancelledExports.delete(exportId);
  }
}

// Singleton instance for global use
export const exportProgressTracker = new ExportProgressTracker();
