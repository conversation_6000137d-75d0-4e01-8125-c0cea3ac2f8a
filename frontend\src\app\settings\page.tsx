'use client';

import { Palette, Settings } from 'lucide-react';
import { useTheme as useNextTheme } from 'next-themes';
import React from 'react';

import { FontSizeSettings } from '@/components/settings/FontSizeSettings';
import { ThemeSettings } from '@/components/settings/ThemeSettings';
import { AppBreadcrumb } from '@/components/ui/app-breadcrumb';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { useUiPreferences } from '@/hooks/ui/useUiPreferences';

/**
 * Settings Page Component
 * Comprehensive settings interface for WorkHub
 */
export default function SettingsPage() {
  const {
    dashboardLayout,
    fontSize,
    notificationsEnabled,
    resetPreferences,
    tableDensity,
  } = useUiPreferences();

  const { theme: nextTheme } = useNextTheme();

  return (
    <div className="container mx-auto space-y-6 py-8">
      <AppBreadcrumb />
      {/* <PERSON> Header */}
      <div className="flex items-center gap-4">
        <div>
          <h1 className="flex items-center gap-2 text-3xl font-bold">
            <Settings className="size-8" />
            Settings
          </h1>
          <p className="mt-1 text-muted-foreground">
            Customize your WorkHub experience and preferences
          </p>
        </div>
      </div>

      {/* Settings Sections */}
      <div className="grid gap-8">
        {/* Theme Settings */}
        <section>
          <h2 className="mb-4 flex items-center gap-2 text-2xl font-semibold">
            <Palette className="size-6" />
            Theme Preferences
          </h2>
          <ThemeSettings />
        </section>

        {/* Font Size Settings */}
        <section>
          <h2 className="mb-4 text-2xl font-semibold">Display Preferences</h2>
          <FontSizeSettings />
        </section>

        {/* Current Settings Overview */}
        <section>
          <h2 className="mb-4 text-2xl font-semibold">Current Configuration</h2>
          <Card>
            <CardHeader>
              <CardTitle>Settings Summary</CardTitle>
              <CardDescription>
                Overview of your current preferences and settings
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
                <div className="space-y-2">
                  <h4 className="text-sm font-medium uppercase tracking-wide text-muted-foreground">
                    Appearance
                  </h4>
                  <div className="space-y-1">
                    <div className="flex justify-between">
                      <span className="text-sm">Theme:</span>
                      <span className="text-sm font-medium capitalize">
                        {nextTheme || 'system'}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Font Size:</span>
                      <span className="text-sm font-medium capitalize">
                        {fontSize}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <h4 className="text-sm font-medium uppercase tracking-wide text-muted-foreground">
                    Notifications
                  </h4>
                  <div className="space-y-1">
                    <div className="flex justify-between">
                      <span className="text-sm">Enabled:</span>
                      <span className="text-sm font-medium">
                        {notificationsEnabled ? 'Yes' : 'No'}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <h4 className="text-sm font-medium uppercase tracking-wide text-muted-foreground">
                    Layout
                  </h4>
                  <div className="space-y-1">
                    <div className="flex justify-between">
                      <span className="text-sm">Table Density:</span>
                      <span className="text-sm font-medium capitalize">
                        {tableDensity}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Dashboard:</span>
                      <span className="text-sm font-medium capitalize">
                        {dashboardLayout}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="mt-6 border-t pt-6">
                <Button
                  className="flex items-center gap-2"
                  onClick={resetPreferences}
                  variant="outline"
                >
                  <Settings className="size-4" />
                  Reset All to Defaults
                </Button>
              </div>
            </CardContent>
          </Card>
        </section>

        {/* Help Section */}
        <section>
          <h2 className="mb-4 text-2xl font-semibold">Help & Information</h2>
          <Card>
            <CardHeader>
              <CardTitle>About Settings</CardTitle>
              <CardDescription>
                Information about how settings work in WorkHub
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="mb-2 font-medium">Font Size</h4>
                <p className="text-sm text-muted-foreground">
                  Font size settings apply globally across the entire
                  application. Changes are saved automatically and will persist
                  across browser sessions.
                </p>
              </div>
              <div>
                <h4 className="mb-2 font-medium">Accessibility</h4>
                <p className="text-sm text-muted-foreground">
                  Use larger font sizes for better readability. All font size
                  changes maintain proper contrast ratios and accessibility
                  standards.
                </p>
              </div>
              <div>
                <h4 className="mb-2 font-medium">Performance</h4>
                <p className="text-sm text-muted-foreground">
                  Settings are stored locally in your browser and synchronized
                  across tabs. No server requests are made when changing
                  preferences.
                </p>
              </div>
            </CardContent>
          </Card>
        </section>
      </div>
    </div>
  );
}
