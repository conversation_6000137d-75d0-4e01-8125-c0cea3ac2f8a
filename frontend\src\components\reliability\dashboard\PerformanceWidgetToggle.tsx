/**
 * @file Performance Widget Toggle Component
 * @description Provides a way to toggle performance widgets on/off in the reliability dashboard
 * @module components/reliability/dashboard/PerformanceWidgetToggle
 */

'use client';

import React, { useState } from 'react';
import { Bar<PERSON>hart3, Eye, EyeOff, Settings } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { shouldShowPerformanceWidgets } from '@/lib/config/debugConfig';

interface PerformanceWidgetToggleProps {
  onToggle?: (enabled: boolean) => void;
  defaultEnabled?: boolean;
}

/**
 * Performance Widget Toggle Component
 * 
 * Allows toggling performance widgets in the reliability dashboard
 * Only shows in development mode
 */
export function PerformanceWidgetToggle({ 
  onToggle, 
  defaultEnabled = false 
}: PerformanceWidgetToggleProps): JSX.Element | null {
  const [showPerformance, setShowPerformance] = useState(defaultEnabled);
  const [isExpanded, setIsExpanded] = useState(false);

  // Only show in development
  if (process.env.NODE_ENV === 'production') {
    return null;
  }

  const handleToggle = (enabled: boolean) => {
    setShowPerformance(enabled);
    onToggle?.(enabled);
  };

  const performanceWidgets = [
    { id: 'performance-overview', name: 'Performance Overview', description: 'Comprehensive metrics' },
    { id: 'system-metrics', name: 'System Performance', description: 'System monitoring' },
    { id: 'http-metrics', name: 'HTTP Request Metrics', description: 'Request analysis' },
    { id: 'deduplication-metrics', name: 'Cache & Deduplication', description: 'Cache efficiency' },
  ];

  if (!isExpanded) {
    return (
      <div className="fixed bottom-4 left-4 z-50">
        <Button
          variant="outline"
          size="sm"
          onClick={() => setIsExpanded(true)}
          className="bg-white shadow-lg border-gray-300"
        >
          <BarChart3 className="h-4 w-4 mr-2" />
          Performance Tools
          <Badge variant={showPerformance ? "default" : "secondary"} className="ml-2">
            {showPerformance ? "ON" : "OFF"}
          </Badge>
        </Button>
      </div>
    );
  }

  return (
    <div className="fixed bottom-4 left-4 z-50 max-w-sm">
      <Card className="shadow-lg">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              Performance Widgets
            </CardTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(false)}
              className="h-6 w-6 p-0"
            >
              <EyeOff className="h-3 w-3" />
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Main Toggle */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {showPerformance ? (
                <Eye className="h-4 w-4 text-green-600" />
              ) : (
                <EyeOff className="h-4 w-4 text-gray-400" />
              )}
              <span className="text-sm font-medium">
                Show Performance Widgets
              </span>
            </div>
            <Switch
              checked={showPerformance}
              onCheckedChange={handleToggle}
            />
          </div>

          {/* Widget List */}
          <div className="space-y-2">
            <div className="text-xs font-medium text-muted-foreground">
              Available Widgets ({performanceWidgets.length})
            </div>
            {performanceWidgets.map((widget) => (
              <div
                key={widget.id}
                className={`text-xs p-2 rounded border ${
                  showPerformance 
                    ? 'bg-green-50 border-green-200 text-green-800' 
                    : 'bg-gray-50 border-gray-200 text-gray-600'
                }`}
              >
                <div className="font-medium">{widget.name}</div>
                <div className="text-xs opacity-75">{widget.description}</div>
              </div>
            ))}
          </div>

          {/* Environment Info */}
          <div className="text-xs text-muted-foreground border-t pt-2">
            <div>Environment: {process.env.NODE_ENV}</div>
            <div>Config: {shouldShowPerformanceWidgets() ? 'Enabled' : 'Disabled'}</div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
