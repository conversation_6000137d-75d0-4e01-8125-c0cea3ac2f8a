/**
 * Admin Schemas
 *
 * This module defines the schemas for admin-related data structures.
 */

import { z } from 'zod';

/**
 * Log level enum
 */
export const LogLevelEnum = z.enum(['ERROR', 'WARNING', 'INFO']);
export type LogLevel = z.infer<typeof LogLevelEnum>;

/**
 * Schema for error log query parameters
 */
export const errorLogQuerySchema = z.object({
  level: LogLevelEnum.optional(),
  limit: z.coerce.number().int().positive().max(100).default(10),
  page: z.coerce.number().int().positive().default(1),
});

export type ErrorLogQuery = z.infer<typeof errorLogQuerySchema>;

/**
 * Schema for health component status
 */
export const healthComponentStatusSchema = z.object({
  error: z.any().nullable().optional(),
  status: z.string(),
  type: z.string().optional(),
  url: z.string().optional(),
});

export type HealthComponentStatus = z.infer<typeof healthComponentStatusSchema>;

/**
 * Schema for health response
 */
export const healthResponseSchema = z.object({
  components: z.object({
    database: healthComponentStatusSchema,
    supabase: healthComponentStatusSchema.optional(),
  }),
  config: z.object({
    connectionMode: z.string().optional(),
    supabaseConfigured: z.boolean(),
    useSupabase: z.boolean(),
  }),
  message: z.string(),
  status: z.string(),
  timestamp: z.string(),
  uptime: z.number().optional(),
  version: z.string(),
});

export type HealthResponse = z.infer<typeof healthResponseSchema>;

/**
 * Schema for cache hit rate
 */
export const cacheHitRateSchema = z.object({
  indexHitRate: z.number(),
  tableHitRate: z.number(),
});

export type CacheHitRate = z.infer<typeof cacheHitRateSchema>;

/**
 * Schema for performance metrics
 */
export const performanceMetricsSchema = z.object({
  activeQueries: z.number(),
  avgQueryTime: z.number(),
  cacheHitRate: cacheHitRateSchema,
  connectionCount: z.number(),
  timestamp: z.string(),
});

export type PerformanceMetrics = z.infer<typeof performanceMetricsSchema>;

/**
 * Schema for error log entry
 */
export const errorLogEntrySchema = z.object({
  details: z.record(z.any()).optional(),
  id: z.string(),
  level: LogLevelEnum,
  message: z.string(),
  source: z.string().optional(),
  timestamp: z.string(),
});

export type ErrorLogEntry = z.infer<typeof errorLogEntrySchema>;

/**
 * Schema for pagination metadata
 */
export const paginationSchema = z.object({
  limit: z.number(),
  page: z.number(),
  total: z.number(),
  totalPages: z.number(),
});

export type Pagination = z.infer<typeof paginationSchema>;

/**
 * Schema for paginated response
 */
export const paginatedResponseSchema = z.object({
  data: z.array(errorLogEntrySchema),
  pagination: paginationSchema,
});

export type PaginatedErrorLogResponse = z.infer<typeof paginatedResponseSchema>;
