'use client';

import { format, parseISO } from 'date-fns';
import {
  ArrowRight,
  Briefcase,
  Building,
  Car,
  Mail,
  MapPin,
  Phone,
  ShieldCheck,
  UserCircle2,
  Wren<PERSON>,
} from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { useEffect, useState } from 'react';

import type { Employee, Vehicle } from '@/lib/types/domain';

import { ActionButton } from '@/components/ui/action-button';
import { Badge } from '@/components/ui/badge';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';

interface EmployeeCardProps {
  employee: Employee;
}

const getStatusColor = (status: Employee['status']) => {
  switch (status) {
    case 'Active': {
      return 'bg-green-500/20 text-green-700 border-green-500/30 dark:text-green-400 dark:bg-green-500/10 dark:border-green-500/20';
    }
    case 'On_Leave': {
      return 'bg-yellow-500/20 text-yellow-700 border-yellow-500/30 dark:text-yellow-400 dark:bg-yellow-500/10 dark:border-yellow-500/20';
    }
    case 'Terminated': {
      return 'bg-red-500/20 text-red-700 border-red-500/30 dark:text-red-400 dark:bg-red-500/10 dark:border-red-500/20';
    }
    default: {
      return 'bg-gray-500/20 text-gray-700 border-gray-500/30 dark:text-gray-400 dark:bg-gray-500/10 dark:border-gray-500/20';
    }
  }
};

const getAvailabilityColor = (availability: Employee['availability']) => {
  if (!availability) return '';
  switch (availability) {
    case 'Busy': {
      return 'bg-red-500/20 text-red-700 border-red-500/30 dark:text-red-400 dark:bg-red-500/10 dark:border-red-500/20';
    }
    case 'Off_Shift': {
      return 'bg-gray-500/20 text-gray-700 border-gray-500/30 dark:text-gray-400 dark:bg-gray-500/10 dark:border-gray-500/20';
    }
    case 'On_Break': {
      return 'bg-yellow-500/20 text-yellow-700 border-yellow-500/30 dark:text-yellow-400 dark:bg-yellow-500/10 dark:border-yellow-500/20';
    }
    case 'On_Shift': {
      return 'bg-green-500/20 text-green-700 border-green-500/30 dark:text-green-400 dark:bg-green-500/10 dark:border-green-500/20';
    }
    default: {
      return 'bg-gray-500/20 text-gray-700 border-gray-500/30 dark:text-gray-400 dark:bg-gray-500/10 dark:border-gray-500/20';
    }
  }
};

export default function EmployeeCard({ employee }: EmployeeCardProps) {
  return (
    <Card className="flex h-full flex-col overflow-hidden border-border/60 bg-card shadow-md">
      <CardHeader className="p-5">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            <div className="relative flex size-12 items-center justify-center overflow-hidden rounded-full bg-muted ring-2 ring-primary/30">
              {employee.profileImageUrl ? (
                <Image
                  alt={employee.fullName || employee.name}
                  data-ai-hint="employee photo"
                  layout="fill"
                  objectFit="cover"
                  src={employee.profileImageUrl}
                />
              ) : (
                <UserCircle2 className="size-8 text-muted-foreground" />
              )}
            </div>
            <div>
              <CardTitle className="text-xl font-semibold text-primary">
                {employee.fullName || employee.name}
              </CardTitle>
              <CardDescription className="text-sm text-muted-foreground">
                {employee.position} (
                {employee.role
                  ? employee.role.charAt(0).toUpperCase() +
                    employee.role.slice(1).replace('_', ' ')
                  : 'N/A'}
                )
              </CardDescription>
            </div>
          </div>
          <div className="flex flex-col items-end gap-1">
            <Badge
              className={cn(
                'text-xs py-1 px-2 font-semibold',
                getStatusColor(employee.status)
              )}
            >
              {employee.status}
            </Badge>
            {employee.role === 'driver' && employee.availability && (
              <Badge
                className={cn(
                  'text-xs py-1 px-2 font-semibold',
                  getAvailabilityColor(employee.availability)
                )}
              >
                {employee.availability?.replace('_', ' ')}
              </Badge>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent className="flex grow flex-col p-5">
        <Separator className="my-3 bg-border/50" />
        <div className="grow space-y-2.5 text-sm text-foreground">
          <div className="flex items-center">
            <Building className="mr-2.5 size-4 shrink-0 text-accent" />
            <div>
              <span className="text-muted-foreground">Department: </span>
              <strong className="font-semibold">{employee.department}</strong>
            </div>
          </div>
          <div className="flex items-center">
            <Mail className="mr-2.5 size-4 shrink-0 text-accent" />
            <div>
              <span className="text-muted-foreground">Email: </span>
              <strong className="font-semibold">{employee.contactEmail}</strong>
            </div>
          </div>
          {employee.contactMobile && (
            <div className="flex items-center">
              <Phone className="mr-2.5 size-4 shrink-0 text-accent" />
              <div>
                <span className="text-muted-foreground">Mobile: </span>
                <strong className="font-semibold">
                  {employee.contactMobile}
                </strong>
              </div>
            </div>
          )}
          <div className="flex items-center">
            <ShieldCheck className="mr-2.5 size-4 shrink-0 text-accent" />
            <div>
              <span className="text-muted-foreground">Hire Date: </span>
              <strong className="font-semibold">
                {employee.hireDate
                  ? format(parseISO(employee.hireDate), 'MMM d, yyyy')
                  : 'N/A'}
              </strong>
            </div>
          </div>

          {employee.role === 'driver' && (
            <>
              {employee.currentLocation && (
                <div className="flex items-center">
                  <MapPin className="mr-2.5 size-4 shrink-0 text-accent" />
                  <div>
                    <span className="text-muted-foreground">Location: </span>
                    <strong className="font-semibold">
                      {employee.currentLocation}
                    </strong>
                  </div>
                </div>
              )}
              {/* Note: Vehicle assignments are now context-specific (per task/delegation) */}
            </>
          )}
          {employee.skills && employee.skills.length > 0 && (
            <div className="flex items-start pt-1">
              <Wrench className="mr-2.5 mt-0.5 size-4 shrink-0 text-accent" />
              <div>
                <span className="text-muted-foreground">Skills: </span>
                <p className="text-xs font-semibold leading-tight">
                  {employee.skills.join(', ')}
                </p>
              </div>
            </div>
          )}
        </div>
      </CardContent>
      <CardFooter className="border-t border-border/60 bg-muted/20 p-4">
        <ActionButton
          actionType="tertiary"
          asChild
          className="w-full"
          icon={<ArrowRight className="size-4" />}
        >
          <Link href={`/employees/${employee.id}`}>View Details</Link>
        </ActionButton>
      </CardFooter>
    </Card>
  );
}
