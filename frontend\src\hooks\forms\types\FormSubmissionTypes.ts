/**
 * @file Form Submission Types
 * @description Type definitions for form submission functionality following SRP
 * @version 2.0.0
 * <AUTHOR> Development Team
 */

import type React from 'react';
import type { FieldValues } from 'react-hook-form';

/**
 * Submission retry configuration
 */
export interface RetryConfig {
  /** Maximum number of retry attempts */
  maxAttempts: number;
  /** Delay between retries in milliseconds */
  delay: number;
  /** Whether to use exponential backoff */
  exponentialBackoff: boolean;
  /** Conditions under which to retry */
  retryCondition?: (error: Error) => boolean;
}

/**
 * Accessibility configuration for form submission
 */
export interface AccessibilityConfig {
  /** ARIA live region for status announcements */
  announceStatus: boolean;
  /** ARIA describedby for error messages */
  errorDescribedBy?: string;
  /** Focus management after submission */
  focusManagement: 'none' | 'first-error' | 'success-message' | 'next-field';
  /** Screen reader announcements */
  screenReaderAnnouncements: boolean;
}

/**
 * Performance optimization configuration
 */
export interface PerformanceConfig {
  /** Debounce delay for rapid submissions */
  debounceMs: number;
  /** Enable request deduplication */
  enableDeduplication: boolean;
  /** Cache successful submissions */
  cacheResults: boolean;
  /** Timeout for submission requests */
  timeoutMs: number;
}

/**
 * Toast notification configuration - Modern SOLID API
 */
export interface ToastConfig {
  /** Entity type for toast service integration */
  entityType:
    | 'employee'
    | 'vehicle'
    | 'task'
    | 'delegation'
    | 'serviceRecord'
    | 'generic';
  /** Entity instance for specific toast messages */
  entity?: any;
  /** Custom success message (optional - entity service provides defaults) */
  successMessage?: string;
  /** Custom error message (optional - entity service provides defaults) */
  errorMessage?: string;
  /** Whether to show success toast (default: true) */
  showSuccessToast?: boolean;
  /** Whether to show error toast (default: true) */
  showErrorToast?: boolean;
}

/**
 * Comprehensive configuration options for form submission
 */
export interface FormSubmissionOptions<T extends FieldValues> {
  // Basic Configuration
  /** Whether to reset form on success */
  resetOnSuccess?: boolean;

  // Toast Configuration
  toast?: ToastConfig;

  // Callbacks
  /** Custom success callback */
  onSuccess?: (data: T, result?: any) => void | Promise<void>;
  /** Custom error callback */
  onError?: (error: Error, data: T) => void | Promise<void>;
  /** Callback before submission starts */
  onSubmitStart?: (data: T) => void | Promise<void>;
  /** Callback when submission completes (success or error) */
  onSubmitComplete?: (data: T, success: boolean) => void | Promise<void>;

  // Validation
  /** Pre-submission validation */
  preSubmitValidation?: (data: T) => Promise<boolean> | boolean;
  /** Post-submission validation */
  postSubmitValidation?: (result: any) => Promise<boolean> | boolean;

  // Data Processing
  /** Data transformation before submission */
  transformData?: (data: T) => T | Promise<T>;
  /** Result transformation after submission */
  transformResult?: (result: any) => any | Promise<any>;

  // Advanced Features
  /** Retry configuration */
  retry?: Partial<RetryConfig>;
  /** Accessibility configuration */
  accessibility?: Partial<AccessibilityConfig>;
  /** Performance optimization */
  performance?: Partial<PerformanceConfig>;

  // Form Integration
  /** React Hook Form reset function */
  formReset?: () => void;
  /** Form validation trigger */
  formValidate?: () => Promise<boolean>;
  /** Form field focus function */
  formFocus?: (fieldName: string) => void;
}

/**
 * Submission state for tracking progress
 */
export type SubmissionState =
  | 'idle'
  | 'validating'
  | 'submitting'
  | 'success'
  | 'error'
  | 'retrying';

/**
 * Performance metrics tracking
 */
export interface PerformanceMetrics {
  totalSubmissions: number;
  successfulSubmissions: number;
  failedSubmissions: number;
  averageDuration: number;
}

/**
 * ARIA attributes for accessibility
 */
export interface AriaAttributes {
  'aria-busy': boolean;
  'aria-invalid': boolean;
  'aria-describedby': string | undefined;
  'aria-live': 'polite' | 'assertive' | 'off';
}

/**
 * Comprehensive form submission result with enhanced state management
 */
export interface FormSubmissionResult<T extends FieldValues> {
  // Core State
  /** Whether submission is in progress */
  isLoading: boolean;
  /** Current submission state */
  state: SubmissionState;
  /** Current error state */
  error: string | null;
  /** Error object for detailed error handling */
  errorObject: Error | null;
  /** Whether submission was successful */
  isSuccess: boolean;
  /** Whether currently validating */
  isValidating: boolean;
  /** Whether currently retrying */
  isRetrying: boolean;

  // Submission Data
  /** Last submitted data */
  lastSubmittedData: T | null;
  /** Last submission timestamp */
  lastSubmitted: number | null;
  /** Last successful result */
  lastResult: any;
  /** Current retry attempt number */
  retryAttempt: number;

  // Actions
  /** Submit handler function */
  handleSubmit: (data: T, event?: React.BaseSyntheticEvent) => Promise<void>;
  /** Clear error state */
  clearError: () => void;
  /** Reset all state */
  reset: () => void;
  /** Retry last submission */
  retry: () => Promise<void>;
  /** Cancel current submission */
  cancel: () => void;

  // Accessibility
  /** ARIA attributes for form elements */
  ariaAttributes: AriaAttributes;

  // Performance Metrics
  /** Submission duration in milliseconds */
  submissionDuration: number | null;
  /** Performance metrics */
  metrics: PerformanceMetrics;
}
