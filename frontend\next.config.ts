import createNextIntlPlugin from 'next-intl/plugin';
import type { NextConfig } from 'next';

const withNextIntl = createNextIntlPlugin();

const nextConfig: NextConfig = {
  /* config options here */
  typescript: {
    ignoreBuildErrors: false,
  },
  eslint: {
    ignoreDuringBuilds: false,
  },
  // Transpile specific modules that might cause issues
  transpilePackages: [
    'papaparse',
    '@supabase/supabase-js',
    // Removed: 'html2canvas', 'jspdf' - migrated to @react-pdf/renderer
  ],
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'picsum.photos',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'images.app.goo.gl',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'as1.ftcdn.net',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 't4.ftcdn.net',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'media.cdntoyota.co.za',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'site.toyota.ly',
        port: '',
        pathname: '/**',
      },
    ],
  },
  // Add webpack configuration to properly resolve modules
  webpack: (config, { isServer }) => {
    // Fixes npm packages that depend on `fs` module
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        path: false,
        os: false,
        crypto: false,
        stream: false,
        http: false,
        https: false,
        zlib: false,
        url: false,
      };
    }

    // Handle Supabase and other external dependencies
    config.externals = config.externals || [];
    if (!isServer) {
      config.externals.push({
        'utf-8-validate': 'commonjs utf-8-validate',
        bufferutil: 'commonjs bufferutil',
      });
    }

    // Handle Genkit AI and OpenTelemetry dependencies
    if (!isServer) {
      config.externals.push({
        '@opentelemetry/exporter-jaeger':
          'commonjs @opentelemetry/exporter-jaeger',
        '@opentelemetry/instrumentation':
          'commonjs @opentelemetry/instrumentation',
        '@opentelemetry/sdk-node': 'commonjs @opentelemetry/sdk-node',
        '@genkit-ai/core': 'commonjs @genkit-ai/core',
        genkit: 'commonjs genkit',
      });
    }

    // Ignore handlebars require.extensions warnings and OpenTelemetry dynamic imports
    config.ignoreWarnings = [
      ...(config.ignoreWarnings || []),
      {
        module: /handlebars/,
        message: /require\.extensions/,
      },
      {
        module: /@opentelemetry\/instrumentation/,
        message:
          /Critical dependency: the request of a dependency is an expression/,
      },
      {
        module: /@genkit-ai/,
        message:
          /Critical dependency: the request of a dependency is an expression/,
      },
    ];

    // Add module rules for OpenTelemetry packages
    config.module = config.module || {};
    config.module.rules = config.module.rules || [];

    // Exclude OpenTelemetry from client-side bundling
    if (!isServer) {
      config.module.rules.push({
        test: /@opentelemetry/,
        use: 'null-loader',
      });
      config.module.rules.push({
        test: /@genkit-ai/,
        use: 'null-loader',
      });
      config.module.rules.push({
        test: /genkit/,
        use: 'null-loader',
      });
    }

    return config;
  },
  turbopack: {
    // Turbopack configuration options
    resolveAlias: {
      // Map aliased imports to their actual modules
      '@components': './src/components',
      '@utils': './src/utils',
      '@hooks': './src/hooks',
      '@styles': './src/styles',
    },
    resolveExtensions: ['.tsx', '.ts', '.jsx', '.js', '.json', '.css'],
    // Add rules for specific file types if needed
    rules: {
      '*.svg': {
        loaders: ['@svgr/webpack'],
        as: '*.js',
      },
    },
  },

  async rewrites() {
    // Use environment-aware backend URL for Docker compatibility
    // Note: This runs at build time, so we use environment variables
    // For runtime environment detection, the frontend uses getEnvironmentConfig()
    const backendUrl =
      process.env.BACKEND_URL ||
      process.env.NEXT_PUBLIC_API_URL ||
      'http://localhost:3001';

    return [
      {
        source: '/api/:path*',
        destination: `${backendUrl}/api/:path*`, // Proxy /api requests to backend, Docker-aware
      },
    ];
  },

  // CSP headers moved to middleware.ts for 2025 best practices
  // This eliminates conflicts and enables proper nonce-based CSP
};

export default withNextIntl(nextConfig);
