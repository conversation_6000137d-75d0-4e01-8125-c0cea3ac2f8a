{"name": "workhub-frontend", "version": "0.1.0", "private": true, "type": "module", "overrides": {"glob": "^10.4.5", "inflight": "npm:lru-cache@^7.14.1", "react": "^18.3.1", "react-dom": "^18.3.1"}, "scripts": {"dev": "next dev --turbopack -p 9002", "genkit:dev": "genkit start -- tsx src/ai/dev.ts", "genkit:watch": "genkit start -- tsx --watch src/ai/dev.ts", "build": "next build", "start": "next start", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit", "docker:build": "docker build -t workhub-frontend .", "docker:run": "docker run -p 3000:3000 workhub-frontend", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:debug": "node --inspect-brk ./node_modules/jest/bin/jest.js --runInBand", "env:localhost": "node scripts/switch-env.js localhost", "env:local": "node scripts/switch-env.js localhost", "env:network": "node scripts/switch-env.js network", "env:production": "node scripts/switch-env.js production"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@genkit-ai/googleai": "^1.8.0", "@genkit-ai/next": "^1.8.0", "@hookform/resolvers": "^4.1.3", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.1.8", "@react-pdf/renderer": "^4.3.0", "@supabase/supabase-js": "^2.49.4", "@tanstack-query-firebase/react": "^1.0.5", "@tanstack/react-query": "^5.79.0", "@tanstack/react-table": "^8.21.3", "@types/leaflet": "^1.9.18", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.6.0", "dotenv": "^16.5.0", "firebase": "^11.7.0", "genkit": "^1.8.0", "jest": "^29.7.0", "jwt-decode": "^4.0.0", "leaflet": "^1.9.4", "lucide-react": "^0.475.0", "next": "^15.3.2", "next-intl": "^4.3.1", "next-themes": "^0.3.0", "papaparse": "^5.5.2", "patch-package": "^8.1.0-canary.1", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.57.0", "react-router-dom": "^6.0.0", "recharts": "^2.15.1", "socket.io-client": "^4.8.1", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "xlsx": "^0.18.5", "zod": "^3.25.64", "zustand": "^5.0.5"}, "devDependencies": {"@babel/preset-env": "^7.27.2", "@babel/preset-react": "^7.27.1", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.28.0", "@opentelemetry/exporter-jaeger": "^2.0.1", "@playwright/test": "^1.53.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^14.2.1", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.14", "@types/node": "^20", "@types/papaparse": "^5.3.16", "@types/react": "^18.3.3", "@types/react-dom": "^18", "@types/react-router-dom": "^5.3.3", "@typescript-eslint/eslint-plugin": "^8.33.1", "@typescript-eslint/parser": "^8.33.1", "eslint": "^9.27.0", "eslint-config-next": "15.1.8", "eslint-config-prettier": "^10.1.5", "eslint-plugin-perfectionist": "^4.13.0", "eslint-plugin-prettier": "^5.4.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-tailwindcss": "^3.18.0", "eslint-plugin-unicorn": "^59.0.1", "genkit-cli": "^1.8.0", "identity-obj-proxy": "^3.0.0", "jest-environment-jsdom": "^29.7.0", "msw": "^2.8.6", "null-loader": "^4.0.1", "postcss": "^8", "prettier": "^3.5.3", "tailwindcss": "^3.4.1", "ts-jest": "^29.3.4", "typescript": "^5", "typescript-eslint": "^8.33.1"}}