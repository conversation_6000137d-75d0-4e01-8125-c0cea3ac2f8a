/**
 * @file Core interfaces for the API layer
 * @module api/core/interfaces
 */

import type { RequestConfig } from './types';

/**
 * Core HTTP client interface for making API requests
 * This interface defines the contract for all HTTP clients in the system
 */
export interface IHttpClient {
  /**
   * Performs a GET request
   * @template T - The expected response data type
   * @param endpoint - The API endpoint (e.g., '/users')
   * @param config - Optional request configuration
   * @returns A Promise that resolves with the response data
   */
  get<T>(endpoint: string, config?: RequestConfig): Promise<T>;

  /**
   * Performs a POST request
   * @template T - The expected response data type
   * @param endpoint - The API endpoint
   * @param data - The request body
   * @param config - Optional request configuration
   * @returns A Promise that resolves with the response data
   */
  post<T>(endpoint: string, data?: any, config?: RequestConfig): Promise<T>;

  /**
   * Performs a PUT request
   * @template T - The expected response data type
   * @param endpoint - The API endpoint
   * @param data - The request body
   * @param config - Optional request configuration
   * @returns A Promise that resolves with the response data
   */
  put<T>(endpoint: string, data?: any, config?: RequestConfig): Promise<T>;

  /**
   * Performs a PATCH request
   * @template T - The expected response data type
   * @param endpoint - The API endpoint
   * @param data - The request body
   * @param config - Optional request configuration
   * @returns A Promise that resolves with the response data
   */
  patch<T>(endpoint: string, data?: any, config?: RequestConfig): Promise<T>;

  /**
   * Performs a DELETE request
   * @template T - The expected response data type (can be void if no content)
   * @param endpoint - The API endpoint
   * @param config - Optional request configuration
   * @returns A Promise that resolves when the request is complete
   */
  delete<T = void>(endpoint: string, config?: RequestConfig): Promise<T>;
}

/**
 * Security context interface for middleware
 */
export interface SecurityContext {
  isAuthenticated: boolean;
  hasValidToken: boolean;
  user?: any;
  session?: any;
  timestamp: Date;
}

/**
 * Security middleware interface for composable security features
 */
export interface ISecurityMiddleware {
  readonly name: string;
  readonly priority: number;
  
  /**
   * Process a request configuration with security enhancements
   * @param config - The request configuration to process
   * @param context - The current security context
   * @returns The processed request configuration
   */
  process(config: RequestConfig, context: SecurityContext): Promise<RequestConfig>;
  
  /**
   * Handle errors that occur during request processing
   * @param error - The error that occurred
   * @param context - The current security context
   */
  handleError?(error: unknown, context: SecurityContext): Promise<void>;
}

/**
 * Security configuration interface
 */
export interface SecurityConfig {
  csrf: {
    enabled: boolean;
    tokenHeader: string;
    excludePaths: string[];
  };
  tokenValidation: {
    enabled: boolean;
    refreshThreshold: number;
    autoRefresh: boolean;
  };
  inputSanitization: {
    enabled: boolean;
    sanitizers: string[];
  };
  authentication: {
    enabled: boolean;
    autoLogout: boolean;
    redirectOnFailure: boolean;
  };
  http: {
    baseURL: string;
    timeout: number;
    retryAttempts: number;
  };
}

/**
 * Security status interface
 */
export interface SecurityStatus {
  isAuthenticated: boolean;
  hasValidToken: boolean;
  securityFeaturesEnabled: Partial<SecurityConfig>;
  lastSecurityCheck: Date;
  threatLevel: 'low' | 'medium' | 'high' | 'critical';
}
