# Database Configuration
# Set USE_SUPABASE to 'true' to use Supabase, 'false' to use local PostgreSQL
USE_SUPABASE=true

# Database URL - This will be used by Prisma
# For local development with Docker:
# DATABASE_URL=postgresql://postgres:postgres@localhost:5432/car_service_db?schema=public

# For Supabase (uncomment and update when switching to Supabase):
DATABASE_URL=postgresql://postgres.abylqjnpaegeqwktcukn:<EMAIL>:5432/postgres



# Supabase Configuration - EMERGENCY SECURITY IMPLEMENTATION
SUPABASE_URL=https://abylqjnpaegeqwktcukn.supabase.co
SUPABASE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFieWxxam5wYWVnZXF3a3RjdWtuIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NzIxMzQ1MywiZXhwIjoyMDYyNzg5NDUzfQ.yLrGESZvVC6ISrqlcKeR3uvfRqdWPcZqYqLLZkjphU8
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFieWxxam5wYWVnZXF3a3RjdWtuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcyMTM0NTMsImV4cCI6MjA2Mjc4OTQ1M30.WCzj8fDu7vdxhvbOUuoQHVamy9-XYBr4vtTox52ap2o
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFieWxxam5wYWVnZXF3a3RjdWtuIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NzIxMzQ1MywiZXhwIjoyMDYyNzg5NDUzfQ.yLrGESZvVC6ISrqlcKeR3uvfRqdWPcZqYqLLZkjphU8

# Server configuration
PORT=3001
NODE_ENV=development

# CORS configuration
FRONTEND_URL=http://localhost:3000,http://localhost:9002,https://9000-firebase-studio-1746987210702.cluster-c3a7z3wnwzapkx3rfr5kz62dac.cloudworkstations.dev,https://6000-firebase-studio-1746987210702.cluster-c3a7z3wnwzapkx3rfr5kz62dac.cloudworkstations.dev,https://6000-firebase-studio-1747070456093.cluster-3gc7bglotjgwuxlqpiut7yyqt4.cloudworkstations.dev

# Third-party API keys
# OpenSky Network API doesn't require an API key for basic access
# For higher rate limits, you can register at https://opensky-network.org/

# Alerting Configuration (optional - for development, alerting can be disabled)
ALERTS_ENABLED=true
# ALERT_WEBHOOK_URL=https://your-webhook-url.com/alerts
# ALERT_SLACK_WEBHOOK=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
# ALERT_EMAIL_RECIPIENTS=<EMAIL>,<EMAIL>

# Alert Thresholds
RESPONSE_TIME_ALERT_THRESHOLD=2000
ERROR_RATE_ALERT_THRESHOLD=5
CPU_USAGE_ALERT_THRESHOLD=80
MEMORY_USAGE_ALERT_THRESHOLD=85
# JWT Secret (for authentication)
JWT_SECRET=JJxeCe52j/9tIaQLW9guDu+pBpSRp//c4PXnj7mV/oyTUdwSBGIOfmpKEAGPY3hA3cBkcu2o2xbw/FiHIKtFUw==
# Logging
LOG_LEVEL=info

# =============================================================================
# REDIS CONFIGURATION (for distributed rate limiting and request deduplication)
# =============================================================================
# Enable Redis for staging environment - Fixed for container connectivity
REDIS_URL=redis://redis:6379

# Redis Connection Configuration - Enhanced for Critical Issues Resolution
REDIS_CONNECTION_TIMEOUT=10000
REDIS_RETRY_ATTEMPTS=5
REDIS_CIRCUIT_BREAKER_THRESHOLD=5
REDIS_CIRCUIT_BREAKER_RESET_TIMEOUT=60000
REDIS_MAX_RECONNECT_ATTEMPTS=10

# Supabase Health Configuration - Enhanced for Critical Issues Resolution
SUPABASE_HEALTH_TIMEOUT=5000
SUPABASE_RETRY_ATTEMPTS=3
SUPABASE_CIRCUIT_BREAKER_THRESHOLD=3

# CORS Configuration - Enhanced for localhost:3001 support
CORS_ALLOW_LOCALHOST_3001=true
CORS_HEALTH_CHECK_ORIGINS=http://localhost:3001,http://127.0.0.1:3001

# Health Check Configuration - Enhanced with retry mechanisms
HEALTH_CHECK_TIMEOUT=5000
HEALTH_CHECK_RETRY_ATTEMPTS=3
HEALTH_CHECK_RETRY_DELAY=1000

# =============================================================================
# HEALTH CHECK CONFIGURATION (Enhanced with retry mechanisms and circuit breaker)
# =============================================================================
# Health check timeout in milliseconds
HEALTH_CHECK_TIMEOUT=5000

# Health check retry configuration
HEALTH_CHECK_RETRY_ATTEMPTS=3
HEALTH_CHECK_RETRY_DELAY=1000

# Circuit breaker configuration for health checks
CIRCUIT_BREAKER_FAILURE_THRESHOLD=5
CIRCUIT_BREAKER_RESET_TIMEOUT=60000

# =============================================================================
# REQUEST DEDUPLICATION CONFIGURATION - RELIABILITY ENHANCEMENT
# =============================================================================
REQUEST_DEDUP_ENABLED=true
REQUEST_DEDUP_DEFAULT_TTL=60

# Optimized Service-Specific Deduplication TTL Settings (in seconds)
ADMIN_DEDUP_TTL=120
API_DEDUP_TTL=30
PERFORMANCE_DEDUP_TTL=15
IDEMPOTENT_DEDUP_TTL=300

# =============================================================================
# NGINX IP ALLOWLIST CONFIGURATION (for reverse proxy support)
# =============================================================================
NGINX_PROXY_IPS=127.0.0.1


API_SECRET=7Jtspy+Ugzy4Tr9gqHswNV2K6PkDnce2FFZRFNHJ5Q0=
SESSION_SECRET=nlGWfMwrnqsYwxZ0TYm7QtMyEsNn5l7tgaCaBmV7KQU=
ENCRYPTION_KEY=JpuAMAw+VWllziHGxCxKdSDcrcmA8n688av1HCLIXQI=
l
API_SECRET=GENERATE_YOUR_OWN_API_SECRET_HERE
SESSION_SECRET=GENERATE_YOUR_OWN_SESSION_SECRET_HERE
ENCRYPTION_KEY=GENERATE_YOUR_OWN_ENCRYPTION_KEY_HERE

# =============================================================================
# PHASE 3 SECURITY HARDENING: Additional Security Configuration
# =============================================================================

# Production Domain Configuration (REQUIRED for production)
# This ensures cookies are only sent to the correct domain
PRODUCTION_DOMAIN=your-production-domain.com

# CSP Configuration (Optional - for enhanced Content Security Policy)
# Comma-separated list of allowed connect sources for CSP
# NEXT_PUBLIC_ALLOWED_CSP_CONNECT_SRC=https://api.example.com,https://cdn.example.com

# Docker Environment Detection (for CSP configuration)
# NEXT_PUBLIC_DOCKER_ENV=false

# Database Credentials (for Docker)
DB_USER=your_db_user
DB_PASSWORD=your_secure_db_password
DB_NAME=your_db_name

# PgAdmin Credentials (for Docker)
PGADMIN_EMAIL=<EMAIL>
PGADMIN_PASSWORD=your_secure_admin_password
