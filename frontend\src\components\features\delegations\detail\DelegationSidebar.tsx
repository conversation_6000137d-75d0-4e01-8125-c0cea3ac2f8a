/**
 * @file DelegationSidebar component for delegation detail page sidebar
 * @module components/delegations/detail/DelegationSidebar
 */

import React from 'react';
import Link from 'next/link';
import { Edit, FileText, Printer } from 'lucide-react';
import { DelegationMetrics } from '../common/DelegationMetrics';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import type { Delegation } from '@/lib/types/domain';
import { format, parseISO } from 'date-fns';

interface DelegationSidebarProps {
  delegation: Delegation;
  className?: string;
}

/**
 * DelegationSidebar component for delegation detail page sidebar
 * Provides quick actions and key metrics
 */
export function DelegationSidebar({
  delegation,
  className,
}: DelegationSidebarProps) {
  const handlePrint = () => {
    if (typeof window !== 'undefined') {
      window.print();
    }
  };

  return (
    <div className={className}>
      <div className="space-y-6">
        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Quick Actions</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <Button
              variant="outline"
              className="w-full justify-start"
              size="sm"
              asChild
            >
              <Link href={`/delegations/${delegation.id}/edit`}>
                <Edit className="mr-2 h-4 w-4" />
                Edit Delegation
              </Link>
            </Button>
            <Button
              variant="outline"
              className="w-full justify-start"
              size="sm"
              asChild
            >
              <Link
                href={`/delegations/${delegation.id}/report`}
                target="_blank"
                rel="noopener noreferrer"
              >
                <FileText className="mr-2 h-4 w-4" />
                View Report
              </Link>
            </Button>
            <Button
              variant="outline"
              className="w-full justify-start"
              size="sm"
              onClick={handlePrint}
            >
              <Printer className="mr-2 h-4 w-4" />
              Print Details
            </Button>
          </CardContent>
        </Card>

        {/* Delegation Metrics */}
        <DelegationMetrics delegation={delegation} />

        {/* Status Information */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Status Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900 dark:text-white mb-1">
                {delegation.status?.replace('_', ' ') || 'No Status'}
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Current Status
              </p>
            </div>

            {delegation.durationFrom && (
              <>
                <Separator />
                <div className="text-center">
                  <div className="text-sm font-medium text-gray-900 dark:text-white mb-1">
                    {format(parseISO(delegation.durationFrom), 'MMM d, yyyy')}
                  </div>
                  <p className="text-xs text-gray-600 dark:text-gray-400">
                    Start Date
                  </p>
                </div>
              </>
            )}

            {delegation.durationTo && (
              <>
                <Separator />
                <div className="text-center">
                  <div className="text-sm font-medium text-gray-900 dark:text-white mb-1">
                    {format(parseISO(delegation.durationTo), 'MMM d, yyyy')}
                  </div>
                  <p className="text-xs text-gray-600 dark:text-gray-400">
                    End Date
                  </p>
                </div>
              </>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

export default DelegationSidebar;
