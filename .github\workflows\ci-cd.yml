name: 🚀 WorkHub CI/CD Pipeline

on:
  push:
    branches: [ main, security/*, feat/*, fix/*, hotfix/* ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:

env:
  NODE_VERSION: '20'
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # 🔍 Security and Code Quality Checks
  security-scan:
    name: 🔒 Security Scan
    runs-on: ubuntu-latest
    permissions:
      security-events: write
      contents: read
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🔍 Run CodeQL Analysis
        uses: github/codeql-action/init@v3
        with:
          languages: javascript, typescript

      - name: 🔍 Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v3

      - name: 🛡️ Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'

      - name: 📤 Upload Trivy scan results
        uses: github/codeql-action/upload-sarif@v3
        if: always()
        with:
          sarif_file: 'trivy-results.sarif'

  # 🧪 Frontend Tests
  frontend-tests:
    name: 🎨 Frontend Tests
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./frontend
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json

      - name: 📦 Install dependencies
        run: npm ci

      - name: 🔍 Type check
        run: npm run type-check

      - name: 🧹 Lint check
        run: npm run lint

      - name: 🎨 Format check
        run: npm run format:check

      - name: 🧪 Run tests
        run: npm run test:ci

      - name: 📊 Upload coverage reports
        uses: codecov/codecov-action@v3
        with:
          file: ./frontend/coverage/lcov.info
          flags: frontend

  # 🔧 Backend Tests
  backend-tests:
    name: ⚙️ Backend Tests
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./backend
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: test_password
          POSTGRES_USER: test_user
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: backend/package-lock.json

      - name: 📦 Install dependencies
        run: npm ci

      - name: 🔍 Type check
        run: npm run type-check

      - name: 🧹 Lint check
        run: npm run lint

      - name: 🗄️ Setup test database
        run: |
          npx prisma migrate deploy
          npx prisma db seed
        env:
          DATABASE_URL: postgresql://test_user:test_password@localhost:5432/test_db

      - name: 🧪 Run tests
        run: npm run test:ci
        env:
          DATABASE_URL: postgresql://test_user:test_password@localhost:5432/test_db

      - name: 📊 Upload coverage reports
        uses: codecov/codecov-action@v3
        with:
          file: ./backend/coverage/lcov.info
          flags: backend

  # 🏗️ Build and Test Integration
  build-and-integration:
    name: 🏗️ Build & Integration Tests
    runs-on: ubuntu-latest
    needs: [security-scan, frontend-tests, backend-tests]
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 🏗️ Build frontend
        working-directory: ./frontend
        run: |
          npm ci
          npm run build

      - name: 🏗️ Build backend
        working-directory: ./backend
        run: |
          npm ci
          npm run build

      - name: 🧪 Run E2E tests
        run: |
          # Start backend in background
          cd backend && npm start &
          # Wait for backend to be ready
          sleep 30
          # Run E2E tests
          cd frontend && npm run test:e2e

  # 🚀 Deploy to Staging
  deploy-staging:
    name: 🚀 Deploy to Staging
    runs-on: ubuntu-latest
    needs: [build-and-integration]
    if: github.ref == 'refs/heads/main' || startsWith(github.ref, 'refs/heads/security/')
    environment:
      name: staging
      url: https://staging.workhub.com
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🔑 Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-west-2

      - name: 🚀 Deploy to staging
        run: |
          echo "🚀 Deploying to staging environment..."
          # Add your deployment commands here
          # Example: aws ecs update-service --cluster staging --service workhub --force-new-deployment

  # 🏭 Deploy to Production
  deploy-production:
    name: 🏭 Deploy to Production
    runs-on: ubuntu-latest
    needs: [deploy-staging]
    if: github.ref == 'refs/heads/main'
    environment:
      name: production
      url: https://workhub.com
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🔑 Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-west-2

      - name: 🏭 Deploy to production
        run: |
          echo "🏭 Deploying to production environment..."
          # Add your production deployment commands here

  # 📊 Notify Results
  notify:
    name: 📊 Notify Results
    runs-on: ubuntu-latest
    needs: [deploy-staging]
    if: always()
    steps:
      - name: 📊 Notify deployment status
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          channel: '#deployments'
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}
        if: always()
