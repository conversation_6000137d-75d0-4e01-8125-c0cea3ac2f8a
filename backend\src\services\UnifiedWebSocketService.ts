/**
 * @file Unified WebSocket Service for the WorkHub backend.
 * This service centralizes WebSocket communication, managing different domains (namespaces)
 * like CRUD operations, reliability monitoring, and system notifications.
 * It aims to provide a consistent and authenticated way to handle real-time events.
 * @module services/UnifiedWebSocketService
 */

import type { Server as SocketIOServer, Socket } from 'socket.io';
import { RELIABILITY_EVENTS, CRUD_EVENTS } from './WebSocketEventManager.js'; // Added CRUD_EVENTS
import logger from '../utils/logger.js'; // Added for logging
import { supabaseAdmin } from '../lib/supabase.js'; // Added for authentication
import { supabase, getDatabaseConfig } from './database.service.js'; // Added supabase client and getConfig
import type { User } from '@supabase/supabase-js'; // Import User type
import type {
  Vehicle, // Assuming base Vehicle type for payload mapping
  Employee, // Assuming base Employee type for payload mapping
  VehicleCreatedPayload,
  VehicleUpdatedPayload,
  VehicleDeletedPayload,
  EmployeeCreatedPayload,
  EmployeeUpdatedPayload,
  EmployeeDeletedPayload,
} from '../types/websocketEvents.js'; // Added payload types
import { logAuditEvent } from '../utils/auditLogger.js'; // Added import

// Placeholder types for namespaces - these will be fleshed out later
// It's expected these might become classes themselves, potentially in separate files.

// Define AuthenticatedSocket interface as per the plan
interface AuthenticatedSocket extends Socket {
  data: {
    user: User; // Use imported User type
    userRole?: string;
    employeeId?: string; // employeeId is often a number, but plan uses string for JWT claims
    // Add ip address for logging if available from handshake
    ipAddress?: string;
  };
}

export interface CrudWebSocketNamespace {
  // TODO: Define methods for CRUD event emissions (e.g., emitEntityChange, emitRefresh)
  // Example: emitVehicleCreated(data: Vehicle): void;
}

export interface ReliabilityWebSocketNamespace {
  // TODO: Define methods for reliability event emissions
  // Example: emitHealthUpdate(healthData: HealthCheck): void;
}

export interface SystemWebSocketNamespace {
  // TODO: Define methods for system-level event emissions (e.g., notifications to users)
  // Example: emitNotification(userId: string, message: string): void;
}

const CRUD_ROOM = 'entity-updates'; // Define if not already present or import from a central place

// Helper to map Supabase Vehicle payload
const mapSupabaseVehicleToPayload = (supaPayload: any): Vehicle => {
  // Basic mapping, adjust fields as necessary to match 'Vehicle' type from websocketEvents.ts
  return {
    id: supaPayload.id.toString(), // Ensure ID is string
    make: supaPayload.make,
    model: supaPayload.model,
    year: supaPayload.year,
    vin: supaPayload.vin,
    // ... other fields from Vehicle type if needed
  };
};

// Helper to map Supabase Employee payload
const mapSupabaseEmployeeToPayload = (supaPayload: any): Employee => {
  // Basic mapping, adjust fields as necessary to match 'Employee' type from websocketEvents.ts
  const fullName = supaPayload.name || supaPayload.fullName || '';
  const nameParts = fullName.split(' ');
  return {
    id: supaPayload.id.toString(), // Ensure ID is string
    firstName: nameParts[0] || '',
    lastName: nameParts.slice(1).join(' ') || '',
    email: supaPayload.contactEmail || supaPayload.email || '',
    // ... other fields like role, department, position, profileImageUrl from Employee type
    // Supabase payload might have these directly or nested
    role: supaPayload.role,
    department: supaPayload.department,
    position: supaPayload.position,
    profileImageUrl: supaPayload.profileImageUrl,
    fullName: fullName,
    name: fullName, // if 'name' is expected by Employee type
    contactEmail: supaPayload.contactEmail || supaPayload.email,
  };
};

export class UnifiedWebSocketService {
  private io: SocketIOServer;
  private subscriptions: any[] = []; // For Supabase subscriptions cleanup

  // Domain-specific namespaces
  public crud: CrudWebSocketNamespace;
  public reliability: ReliabilityWebSocketNamespace;
  public system: SystemWebSocketNamespace;

  constructor(io: SocketIOServer) {
    this.io = io;

    // Add authentication middleware
    this.io.use(async (socket: AuthenticatedSocket, next) => {
      const ipAddress = socket.handshake.address; // Get IP address
      socket.data = { ...socket.data, ipAddress }; // Store IP for later logging

      // Try to get token from auth object first (explicit token)
      let token = socket.handshake.auth.token as string | undefined;

      // If no explicit token, try to extract from cookies (httpOnly cookie support)
      if (!token) {
        const cookieHeader = socket.handshake.headers.cookie;
        if (cookieHeader) {
          // Use the same cookie extraction logic as HTTP middleware
          const nameEQ = 'sb-access-token=';
          const ca = cookieHeader.split(';');
          for (let i = 0; i < ca.length; i++) {
            let c = ca[i];
            while (c.charAt(0) === ' ') c = c.substring(1, c.length);
            if (c.indexOf(nameEQ) === 0) {
              token = c.substring(nameEQ.length, c.length);
              break;
            }
          }
        }
      }

      if (!token) {
        logAuditEvent({
          eventType: 'AUTH',
          action: 'WEBSOCKET_AUTH_FAILURE',
          outcome: 'FAILURE',
          errorCode: 'NO_TOKEN_WS',
          message: 'WebSocket authentication failed: No token provided in auth object or cookies.',
          details: { socketId: socket.id },
          ipAddress: ipAddress,
        });
        // logger.warn already exists
        return next(new Error('Authentication error: No token provided'));
      }

      try {
        const {
          data: { user },
          error,
        } = await supabaseAdmin.auth.getUser(token);

        if (error || !user) {
          logAuditEvent({
            eventType: 'AUTH',
            action: 'WEBSOCKET_AUTH_FAILURE',
            outcome: 'FAILURE',
            errorCode: 'INVALID_TOKEN_WS',
            message: `WebSocket authentication failed: Invalid token. Provider error: ${error?.message || 'User not found'}`,
            details: { socketId: socket.id, providerError: error?.message },
            ipAddress: ipAddress,
          });
          // logger.warn already exists
          return next(new Error('Authentication error: Invalid token'));
        }

        const userRole = (user as any).user_role || 'USER';
        const employeeId = (user as any).employee_id;

        socket.data = { ...socket.data, user, userRole, employeeId }; // user and ipAddress already set

        logAuditEvent({
          eventType: 'AUTH',
          action: 'WEBSOCKET_AUTH_SUCCESS',
          outcome: 'SUCCESS',
          userId: user.id,
          userRole: userRole,
          employeeId: employeeId,
          message: 'WebSocket connection authenticated successfully.',
          details: { socketId: socket.id },
          ipAddress: ipAddress,
        });
        // logger.info already exists
        next();
      } catch (e: any) {
        logAuditEvent({
          eventType: 'AUTH',
          action: 'WEBSOCKET_AUTH_EXCEPTION',
          outcome: 'FAILURE',
          errorCode: 'TOKEN_VERIFICATION_EXCEPTION_WS',
          message: 'WebSocket authentication failed: Token verification exception.',
          details: { socketId: socket.id, error: e.message, stack: e.stack?.substring(0, 200) },
          ipAddress: ipAddress,
        });
        // logger.error already exists
        return next(new Error('Authentication error: Token verification failed'));
      }
    });

    // Initialize namespaces
    this.crud = this.initializeCrudNamespace();
    this.reliability = this.initializeReliabilityNamespace();
    this.system = this.initializeSystemNamespace();

    this.initializeGlobalEventHandlers();
    this.initializeSupabaseRealtimeSubscriptions();
  }

  private initializeCrudNamespace(): CrudWebSocketNamespace {
    // TODO: Instantiate and return the actual CRUD namespace implementation
    // This might involve passing `this.io` to the namespace constructor
    console.log('CRUD WebSocket Namespace Initialized (Placeholder)');
    return {
      // Placeholder implementation
    } as CrudWebSocketNamespace;
  }

  private initializeReliabilityNamespace(): ReliabilityWebSocketNamespace {
    // TODO: Instantiate and return the actual Reliability namespace implementation
    console.log('Reliability WebSocket Namespace Initialized (Placeholder)');
    return {
      // Placeholder implementation
    } as ReliabilityWebSocketNamespace;
  }

  private initializeSystemNamespace(): SystemWebSocketNamespace {
    // TODO: Instantiate and return the actual System namespace implementation
    console.log('System WebSocket Namespace Initialized (Placeholder)');
    return {
      // Placeholder implementation
    } as SystemWebSocketNamespace;
  }

  private initializeGlobalEventHandlers(): void {
    this.io.on('connection', (socket: AuthenticatedSocket) => {
      // Use AuthenticatedSocket
      // At this point, socket.data.user should be populated if authentication was successful
      logger.info(
        `New authenticated client connected to UnifiedWebSocketService: ${socket.id}, User ID: ${socket.data.user?.id}, Role: ${socket.data.userRole}, EmployeeID: ${socket.data.employeeId || 'N/A'}`,
      );

      logAuditEvent({
        eventType: 'SYSTEM_EVENT',
        action: 'WEBSOCKET_CLIENT_CONNECT',
        outcome: 'INFO',
        userId: socket.data.user?.id, // user might not be populated if auth failed, but we log that separately
        userRole: socket.data.userRole,
        employeeId: socket.data.employeeId,
        message: 'New client connected to WebSocket service.',
        details: { socketId: socket.id },
        ipAddress: socket.data.ipAddress,
      });

      // Handle reliability monitoring room join
      socket.on(RELIABILITY_EVENTS.JOIN_RELIABILITY_MONITORING, () => {
        socket.join('reliability-monitoring');
        logger.info(
          `Client ${socket.id} joined reliability-monitoring room via UnifiedWebSocketService`,
        );
        // Send initial connection confirmation
        socket.emit('reliability-connected', {
          // Consider making 'reliability-connected' a formal event in RELIABILITY_EVENTS
          status: 'connected',
          timestamp: new Date().toISOString(),
          message: 'Successfully joined reliability monitoring',
        });
      });

      // Handle reliability monitoring room leave
      socket.on(RELIABILITY_EVENTS.LEAVE_RELIABILITY_MONITORING, () => {
        socket.leave('reliability-monitoring');
        logger.info(
          `Client ${socket.id} left reliability-monitoring room via UnifiedWebSocketService`,
        );
      });

      // Handle reliability messages from client
      socket.on(RELIABILITY_EVENTS.RELIABILITY_MESSAGE, (message: any) => {
        logger.debug(
          `Reliability message received by UnifiedWebSocketService from ${socket.id}:`,
          message,
        );
        // Echo back for testing purposes or handle as needed
        // This could be routed to the reliability namespace if it needs to process it
        socket.emit(RELIABILITY_EVENTS.RELIABILITY_ECHO, {
          // RELIABILITY_ECHO should be defined in RELIABILITY_EVENTS
          ...message,
          timestamp: new Date().toISOString(),
          echo: true,
          processedBy: 'UnifiedWebSocketService',
        });
      });

      // --- Role-based event authorization as per plan (Corrected) ---

      // Higher-order function to wrap event handlers with role-based authorization.
      // Defined inside the connection handler to have access to the specific `socket` instance.
      const requireRoleAndWrapHandler = (
        allowedRoles: string[],
        eventName: string, // Added eventName for logging
        handler: (data: any, callback?: (response: any) => void) => void,
      ) => {
        return (data: any, callback?: (response: any) => void) => {
          const userRole = socket.data.userRole || 'USER';
          const userId = socket.data.user?.id;

          if (!allowedRoles.includes(userRole)) {
            logAuditEvent({
              eventType: 'AUTHZ',
              action: 'WEBSOCKET_EVENT_AUTHZ_FAILURE',
              outcome: 'FAILURE',
              userId: userId,
              userRole: userRole,
              errorCode: 'INSUFFICIENT_PERMISSIONS_WS_EVENT',
              message: `WebSocket event authorization failed for event '${eventName}'. Role '${userRole}' not in [${allowedRoles.join(', ')}].`,
              details: {
                socketId: socket.id,
                eventName,
                requiredRoles: allowedRoles,
                eventData: data,
              },
              ipAddress: socket.data.ipAddress,
            });
            // logger.warn already exists
            if (callback) {
              callback({ error: 'Insufficient permissions', code: 'FORBIDDEN_EVENT' });
            }
            return; // Important to stop further processing
          }

          logAuditEvent({
            eventType: 'AUTHZ',
            action: 'WEBSOCKET_EVENT_AUTHZ_SUCCESS',
            outcome: 'SUCCESS',
            userId: userId,
            userRole: userRole,
            message: `WebSocket event authorization successful for event '${eventName}'.`,
            details: {
              socketId: socket.id,
              eventName,
              allowedRoles: allowedRoles,
              eventData: data,
            },
            ipAddress: socket.data.ipAddress,
          });
          handler(data, callback);
        };
      };

      // Example usage update:
      socket.on(
        'admin-action',
        requireRoleAndWrapHandler(['ADMIN', 'SUPER_ADMIN'], 'admin-action', (data, cb) => {
          logger.info(`Admin action '${data.command}' received from ${socket.data.user.email}`);
          // ... handle admin-action ...
          if (cb) cb({ status: 'Admin action processed' });
        }),
      );

      socket.on(
        'manager-action',
        requireRoleAndWrapHandler(
          ['MANAGER', 'ADMIN', 'SUPER_ADMIN'],
          'manager-action',
          (data, cb) => {
            logger.info(`Manager action '${data.task}' received from ${socket.data.user.email}`);
            // ... handle manager-action ...
            if (cb) cb({ status: 'Manager action processed' });
          },
        ),
      );

      // --- End of role-based event authorization ---

      socket.on('disconnect', (reason: string) => {
        logger.info(
          `Client disconnected from UnifiedWebSocketService: ${socket.id}, reason: ${reason}`,
        );
        logAuditEvent({
          eventType: 'SYSTEM_EVENT',
          action: 'WEBSOCKET_CLIENT_DISCONNECT',
          outcome: 'INFO',
          userId: socket.data.user?.id,
          userRole: socket.data.userRole,
          message: 'Client disconnected from WebSocket service.',
          details: { socketId: socket.id, reason: reason },
          ipAddress: socket.data.ipAddress,
        });
      });

      // Handle token refresh event
      socket.on(
        'refresh-token',
        async (
          newToken: string,
          callback: (response: { success: boolean; message?: string; userRole?: string }) => void,
        ) => {
          const ipAddress = socket.data.ipAddress; // Use stored IP for logging

          if (!newToken) {
            logAuditEvent({
              eventType: 'AUTH',
              action: 'WEBSOCKET_TOKEN_REFRESH_FAILURE',
              outcome: 'FAILURE',
              errorCode: 'NO_TOKEN_REFRESH',
              message: 'WebSocket token refresh failed: No new token provided.',
              details: { socketId: socket.id },
              ipAddress: ipAddress,
            });
            logger.warn('WebSocket token refresh failed: No new token provided for socket', {
              socketId: socket.id,
            });
            if (callback) callback({ success: false, message: 'No new token provided' });
            return;
          }

          try {
            const {
              data: { user },
              error,
            } = await supabaseAdmin.auth.getUser(newToken);

            if (error || !user) {
              logAuditEvent({
                eventType: 'AUTH',
                action: 'WEBSOCKET_TOKEN_REFRESH_FAILURE',
                outcome: 'FAILURE',
                errorCode: 'INVALID_TOKEN_REFRESH',
                message: `WebSocket token refresh failed: Invalid token. Provider error: ${error?.message || 'User not found'}`,
                details: { socketId: socket.id, providerError: error?.message },
                ipAddress: ipAddress,
              });
              logger.warn('WebSocket token refresh failed: Invalid token for socket', {
                socketId: socket.id,
              });
              if (callback) callback({ success: false, message: 'Invalid or expired token' });
              return;
            }

            const userRole = (user as any).user_role || 'USER';
            const employeeId = (user as any).employee_id;

            // Update socket.data with new user information
            socket.data.user = user;
            socket.data.userRole = userRole;
            socket.data.employeeId = employeeId;

            logAuditEvent({
              eventType: 'AUTH',
              action: 'WEBSOCKET_TOKEN_REFRESH_SUCCESS',
              outcome: 'SUCCESS',
              userId: user.id,
              userRole: userRole,
              employeeId: employeeId,
              message: 'WebSocket token refreshed successfully.',
              details: { socketId: socket.id },
              ipAddress: ipAddress,
            });
            logger.info(
              `WebSocket token refreshed successfully for client ${socket.id}, User ID: ${user.id}, Role: ${userRole}`,
            );
            if (callback)
              callback({ success: true, message: 'Token refreshed successfully', userRole });
          } catch (e: any) {
            logAuditEvent({
              eventType: 'AUTH',
              action: 'WEBSOCKET_TOKEN_REFRESH_EXCEPTION',
              outcome: 'FAILURE',
              errorCode: 'TOKEN_REFRESH_EXCEPTION_WS',
              message: 'WebSocket token refresh failed: Exception during verification.',
              details: { socketId: socket.id, error: e.message, stack: e.stack?.substring(0, 200) },
              ipAddress: ipAddress,
            });
            logger.error('WebSocket token refresh error for socket', {
              socketId: socket.id,
              error: e.message,
            });
            if (callback)
              callback({ success: false, message: 'Token refresh failed due to server error' });
          }
        },
      );

      // TODO: Handle other global events or pass socket to namespaces for specific handling
    });

    logger.info('UnifiedWebSocketService initialized and listening for connections.');
  }

  private initializeSupabaseRealtimeSubscriptions(): void {
    const dbConfig = getDatabaseConfig();
    if (!dbConfig.useSupabase || !supabase) {
      logger.warn(
        'Supabase client not available or not configured, Supabase real-time subscriptions not set up for UnifiedWebSocketService.',
      );
      return;
    }

    logger.info('Setting up Supabase real-time subscriptions for UnifiedWebSocketService...');

    try {
      const vehiclesChannel = supabase
        .channel('unified-vehicles-changes') // Use a unique channel name
        .on(
          'postgres_changes',
          { event: 'INSERT', schema: 'public', table: 'vehicles' },
          payloadSupa => {
            try {
              logger.info('Supabase: Vehicle created:', payloadSupa.new?.id);
              const wsPayload: VehicleCreatedPayload = mapSupabaseVehicleToPayload(payloadSupa.new);
              this.emitToRoom(CRUD_ROOM, CRUD_EVENTS.VEHICLE_CREATED, wsPayload);
              this.emitToRoom(CRUD_ROOM, CRUD_EVENTS.REFRESH_VEHICLES, {});
            } catch (mapError) {
              logger.error('Error mapping/emitting Supabase vehicle insert:', {
                error: mapError,
                payload: payloadSupa.new,
              });
            }
          },
        )
        .on(
          'postgres_changes',
          { event: 'UPDATE', schema: 'public', table: 'vehicles' },
          payloadSupa => {
            try {
              logger.info('Supabase: Vehicle updated:', payloadSupa.new?.id);
              const wsPayload: VehicleUpdatedPayload = mapSupabaseVehicleToPayload(payloadSupa.new);
              this.emitToRoom(CRUD_ROOM, CRUD_EVENTS.VEHICLE_UPDATED, wsPayload);
              this.emitToRoom(CRUD_ROOM, CRUD_EVENTS.REFRESH_VEHICLES, {});
            } catch (mapError) {
              logger.error('Error mapping/emitting Supabase vehicle update:', {
                error: mapError,
                payload: payloadSupa.new,
              });
            }
          },
        )
        .on(
          'postgres_changes',
          { event: 'DELETE', schema: 'public', table: 'vehicles' },
          payloadSupa => {
            try {
              logger.info('Supabase: Vehicle deleted:', payloadSupa.old?.id);
              const wsPayload: VehicleDeletedPayload = { id: payloadSupa.old.id.toString() };
              this.emitToRoom(CRUD_ROOM, CRUD_EVENTS.VEHICLE_DELETED, wsPayload);
              this.emitToRoom(CRUD_ROOM, CRUD_EVENTS.REFRESH_VEHICLES, {});
            } catch (mapError) {
              logger.error('Error mapping/emitting Supabase vehicle delete:', {
                error: mapError,
                payload: payloadSupa.old,
              });
            }
          },
        )
        .subscribe((status, err) => {
          if (err) {
            logger.error('Error subscribing to Supabase vehicles changes', { error: err, status });
          } else {
            logger.info('Successfully subscribed to Supabase vehicles changes', { status });
          }
        });

      const employeesChannel = supabase
        .channel('unified-employees-changes')
        .on(
          'postgres_changes',
          { event: 'INSERT', schema: 'public', table: 'employees' },
          payloadSupa => {
            try {
              logger.info('Supabase: Employee created:', payloadSupa.new?.id);
              const wsPayload: EmployeeCreatedPayload = mapSupabaseEmployeeToPayload(
                payloadSupa.new,
              );
              this.emitToRoom(CRUD_ROOM, CRUD_EVENTS.EMPLOYEE_CREATED, wsPayload);
              this.emitToRoom(CRUD_ROOM, CRUD_EVENTS.REFRESH_EMPLOYEES, {});
            } catch (mapError) {
              logger.error('Error mapping/emitting Supabase employee insert:', {
                error: mapError,
                payload: payloadSupa.new,
              });
            }
          },
        )
        .on(
          'postgres_changes',
          { event: 'UPDATE', schema: 'public', table: 'employees' },
          payloadSupa => {
            try {
              logger.info('Supabase: Employee updated:', payloadSupa.new?.id);
              const wsPayload: EmployeeUpdatedPayload = mapSupabaseEmployeeToPayload(
                payloadSupa.new,
              );
              this.emitToRoom(CRUD_ROOM, CRUD_EVENTS.EMPLOYEE_UPDATED, wsPayload);
              this.emitToRoom(CRUD_ROOM, CRUD_EVENTS.REFRESH_EMPLOYEES, {});
            } catch (mapError) {
              logger.error('Error mapping/emitting Supabase employee update:', {
                error: mapError,
                payload: payloadSupa.new,
              });
            }
          },
        )
        .on(
          'postgres_changes',
          { event: 'DELETE', schema: 'public', table: 'employees' },
          payloadSupa => {
            try {
              logger.info('Supabase: Employee deleted:', payloadSupa.old?.id);
              const wsPayload: EmployeeDeletedPayload = { id: payloadSupa.old.id.toString() };
              this.emitToRoom(CRUD_ROOM, CRUD_EVENTS.EMPLOYEE_DELETED, wsPayload);
              this.emitToRoom(CRUD_ROOM, CRUD_EVENTS.REFRESH_EMPLOYEES, {});
            } catch (mapError) {
              logger.error('Error mapping/emitting Supabase employee delete:', {
                error: mapError,
                payload: payloadSupa.old,
              });
            }
          },
        )
        .subscribe((status, err) => {
          if (err) {
            logger.error('Error subscribing to Supabase employees changes', { error: err, status });
          } else {
            logger.info('Successfully subscribed to Supabase employees changes', { status });
          }
        });

      this.subscriptions.push(vehiclesChannel, employeesChannel);
      logger.info(
        'Supabase real-time subscriptions set up successfully in UnifiedWebSocketService',
      );
    } catch (error) {
      logger.error(
        'Error setting up Supabase real-time subscriptions in UnifiedWebSocketService:',
        error,
      );
    }
  }

  public cleanupSupabaseSubscriptions(): void {
    if (this.subscriptions.length === 0) {
      return;
    }
    // Ensure supabase client is available before proceeding
    if (!supabase) {
      logger.warn('Supabase client not available, cannot cleanup subscriptions.');
      // Clear local subscriptions array anyway if supabase client is gone unexpectedly
      this.subscriptions = [];
      return;
    }

    logger.info('Cleaning up Supabase real-time subscriptions from UnifiedWebSocketService...');
    const currentSupabaseClient = supabase; // Create a non-null reference for the current scope

    this.subscriptions.forEach(channel => {
      currentSupabaseClient
        .removeChannel(channel)
        .then(status => {
          logger.info('Supabase channel removed', { status });
        })
        .catch(err => {
          logger.error('Error removing Supabase channel', { error: err });
        });
    });
    this.subscriptions = [];
    logger.info('Supabase real-time subscriptions cleaned up from UnifiedWebSocketService.');
  }

  /**
   * Emits an event to all clients in a specific room.
   * @param room - The name of the room.
   * @param event - The event name.
   * @param data - The data to send with the event.
   */
  public emitToRoom(room: string, event: string, data: any): void {
    this.io.to(room).emit(event, data);
    console.log(`Event [${event}] emitted to room [${room}] with data:`, data);
  }

  /**
   * Emits an event to a specific user (identified by their socket ID).
   * More robust user identification (e.g., user ID from auth) should be implemented.
   * @param socketId - The socket ID of the user.
   * @param event - The event name.
   * @param data - The data to send with the event.
   */
  public emitToUser(socketId: string, event: string, data: any): void {
    this.io.to(socketId).emit(event, data);
    console.log(`Event [${event}] emitted to user [${socketId}] with data:`, data);
  }

  /**
   * Emits an event to all connected clients (global broadcast).
   * Use with caution, prefer targeted emissions via rooms or user IDs.
   * @param event - The event name.
   * @param data - The data to send with the event.
   */
  public emitGlobal(event: string, data: any): void {
    this.io.emit(event, data);
    console.log(`Event [${event}] emitted globally with data:`, data);
  }

  // TODO: Add methods for room management (joinRoom, leaveRoom)
  // TODO: Add methods for authentication and authorization on socket connections/events
}

// Optional: Singleton pattern for the service if needed application-wide
let unifiedWebSocketServiceInstance: UnifiedWebSocketService | null = null;

export const getUnifiedWebSocketService = (io?: SocketIOServer): UnifiedWebSocketService => {
  if (!unifiedWebSocketServiceInstance) {
    if (!io) {
      throw new Error(
        'SocketIOServer instance must be provided to initialize UnifiedWebSocketService for the first time.',
      );
    }
    unifiedWebSocketServiceInstance = new UnifiedWebSocketService(io);
  }
  return unifiedWebSocketServiceInstance;
};

// Example usage (typically in your main server setup file like server.ts):
// import { Server as SocketIOServer } from 'socket.io';
// import { getUnifiedWebSocketService } from './services/UnifiedWebSocketService';
// const io = new SocketIOServer(httpServer, { cors: { origin: '*' } });
// const unifiedSocketService = getUnifiedWebSocketService(io);
// Now `unifiedSocketService.crud`, `unifiedSocketService.reliability` etc. can be used by controllers/services.
