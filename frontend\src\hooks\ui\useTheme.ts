/**
 * @file Theme management hook using Zustand AppStore
 * @module hooks/useTheme
 */

import { useCallback } from 'react';

import { useAppStore } from '@/lib/stores/zustand/appStore';

/**
 * Hook for theme management with additional utilities
 * Provides convenient methods for theme switching and state checking
 */
export const useTheme = () => {
  const currentTheme = useAppStore(state => state.currentTheme);
  const setTheme = useAppStore(state => state.setTheme);

  /**
   * Toggle between light and dark themes
   */
  const toggleTheme = useCallback(() => {
    setTheme(currentTheme === 'light' ? 'dark' : 'light');
  }, [currentTheme, setTheme]);

  /**
   * Check if current theme is dark
   */
  const isDark = currentTheme === 'dark';

  /**
   * Check if current theme is light
   */
  const isLight = currentTheme === 'light';

  /**
   * Set theme to light
   */
  const setLightTheme = useCallback(() => {
    setTheme('light');
  }, [setTheme]);

  /**
   * Set theme to dark
   */
  const setDarkTheme = useCallback(() => {
    setTheme('dark');
  }, [setTheme]);

  /**
   * Get theme-specific CSS classes
   */
  const getThemeClasses = useCallback(() => {
    return {
      background: isDark ? 'bg-gray-900' : 'bg-white',
      border: isDark ? 'border-gray-700' : 'border-gray-200',
      isDark,
      isLight,
      root: currentTheme,
      text: isDark ? 'text-white' : 'text-gray-900',
    };
  }, [currentTheme, isDark, isLight]);

  return {
    // State
    currentTheme,
    // Utilities
    getThemeClasses,
    isDark,

    isLight,
    setDarkTheme,
    setLightTheme,
    // Actions
    setTheme,

    toggleTheme,
  };
};
