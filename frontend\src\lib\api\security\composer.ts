/**
 * @file Security Composer - Orchestrates security hooks directly
 * @module api/security/composer
 *
 * This composer uses the moved security hooks directly without wrapper layers,
 * following DRY principles by leveraging existing security infrastructure.
 *
 * Phase 2 Enhancement: Now integrates with SecurityUtils and SECURITY_CONSTANTS
 */

import type {
  ISecurityMiddleware,
  SecurityContext,
  SecurityConfig,
} from '../core/interfaces';
import type { RequestConfig } from '../core/types';
import { SECURITY_CONSTANTS } from '../../security';

// Extended RequestConfig for security processing that includes method, url, and body
interface SecurityRequestConfig extends RequestConfig {
  method?: string;
  url?: string;
  body?: any;
}

// Import the moved security hooks directly
import type { UseCSRFProtectionReturn } from './hooks/useCSRFProtection';
import type { UseTokenManagementReturn } from './hooks/useTokenManagement';
import type { UseInputValidationReturn } from './hooks/useInputValidation';
import type { UseSessionSecurityReturn } from './hooks/useSessionSecurity';

/**
 * Security features interface that wraps the moved hooks
 */
export interface SecurityFeatures {
  csrfProtection?: UseCSRFProtectionReturn;
  tokenManagement?: UseTokenManagementReturn;
  inputValidation?: UseInputValidationReturn;
  sessionSecurity?: UseSessionSecurityReturn;
}

/**
 * Security Composer that orchestrates moved security hooks directly
 *
 * This composer follows DRY principles by using the original security hooks
 * that were moved from hooks/security/ to lib/api/security/hooks/
 */
export class SecurityComposer {
  private securityFeatures: SecurityFeatures;
  private config: Partial<SecurityConfig>;

  constructor(
    securityFeatures: SecurityFeatures,
    config: Partial<SecurityConfig> = {}
  ) {
    this.securityFeatures = securityFeatures;
    this.config = {
      csrf: { enabled: true, tokenHeader: 'X-CSRF-Token', excludePaths: [] },
      tokenValidation: {
        enabled: true,
        refreshThreshold:
          SECURITY_CONSTANTS.TOKEN_EXPIRY_THRESHOLD_MINUTES * 60, // Convert to seconds
        autoRefresh: true,
      },
      inputSanitization: { enabled: true, sanitizers: ['xss', 'sql'] },
      authentication: {
        enabled: true,
        autoLogout: true,
        redirectOnFailure: true,
      },
      ...config,
    };
  }

  /**
   * Process request with all enabled security features
   * Uses the original security hooks directly - no wrapper layers
   */
  async processRequest(
    config: SecurityRequestConfig,
    context: SecurityContext
  ): Promise<SecurityRequestConfig> {
    let processedConfig = { ...config };

    try {
      // 1. Authentication validation using moved hooks
      if (
        this.config.authentication?.enabled &&
        this.securityFeatures.sessionSecurity
      ) {
        if (!this.securityFeatures.sessionSecurity.isSessionActive) {
          throw new Error('Authentication required for secure API calls');
        }
      }

      // 2. Token validation & refresh using moved hooks
      if (
        this.config.tokenValidation?.enabled &&
        this.securityFeatures.tokenManagement
      ) {
        const { isTokenValid, isTokenExpired, refreshToken } =
          this.securityFeatures.tokenManagement;

        if (!isTokenValid || isTokenExpired) {
          console.log(
            '🔄 SecurityComposer: Token invalid/expired, attempting refresh...'
          );
          const refreshSuccess = await refreshToken();
          if (!refreshSuccess) {
            throw new Error('Token refresh failed - authentication required');
          }
          console.log('✅ SecurityComposer: Token refreshed successfully');
        }
      }

      // 3. Input sanitization using moved hooks
      if (
        this.config.inputSanitization?.enabled &&
        this.securityFeatures.inputValidation &&
        processedConfig.body
      ) {
        const { sanitizeInput } = this.securityFeatures.inputValidation;
        processedConfig.body = sanitizeInput(processedConfig.body);
        console.debug('🧹 SecurityComposer: Input sanitized using moved hooks');
      }

      // 4. CSRF protection using moved hooks (for state-changing operations)
      if (this.config.csrf?.enabled && this.securityFeatures.csrfProtection) {
        const method = processedConfig.method?.toUpperCase();
        if (['POST', 'PUT', 'PATCH', 'DELETE'].includes(method || '')) {
          const { attachCSRF } = this.securityFeatures.csrfProtection;
          // Convert to CSRF RequestConfig format
          const csrfConfig = {
            url: processedConfig.url || '',
            method: processedConfig.method || 'GET',
            headers: processedConfig.headers || {},
            body: processedConfig.body,
          };
          const csrfResult = attachCSRF(csrfConfig);
          processedConfig = { ...processedConfig, ...csrfResult };
          console.debug(
            '🛡️ SecurityComposer: CSRF protection applied using moved hooks'
          );
        }
      }

      return processedConfig;
    } catch (error) {
      console.error('SecurityComposer: Error processing request:', error);
      throw error;
    }
  }

  /**
   * Handle errors using moved security hooks
   */
  async handleError(error: unknown, context: SecurityContext): Promise<void> {
    try {
      // Auto-logout on authentication errors using moved hooks
      if (
        this.config.authentication?.autoLogout &&
        this.securityFeatures.sessionSecurity
      ) {
        if (
          error instanceof Error &&
          (error.message.includes('401') ||
            error.message.includes('Authentication') ||
            error.message.includes('Unauthorized'))
        ) {
          console.warn(
            '🔐 SecurityComposer: Authentication error detected, clearing session...'
          );
          this.securityFeatures.sessionSecurity.clearSession();
        }
      }

      // Update activity timestamp using moved hooks
      if (this.securityFeatures.sessionSecurity) {
        this.securityFeatures.sessionSecurity.updateActivity();
      }
    } catch (handlingError) {
      console.error(
        'SecurityComposer: Error in error handling:',
        handlingError
      );
    }
  }

  /**
   * Get current security status using moved hooks
   */
  getSecurityStatus(): {
    isAuthenticated: boolean;
    hasValidToken: boolean;
    sessionActive: boolean;
    securityFeaturesEnabled: Partial<SecurityConfig>;
    securityFeaturesInitialized: boolean;
  } {
    return {
      isAuthenticated:
        this.securityFeatures.sessionSecurity?.isSessionActive ?? false,
      hasValidToken:
        this.securityFeatures.tokenManagement?.isTokenValid ?? false,
      sessionActive:
        this.securityFeatures.sessionSecurity?.isSessionActive ?? false,
      securityFeaturesEnabled: this.config,
      securityFeaturesInitialized: !!(
        this.securityFeatures.csrfProtection ||
        this.securityFeatures.tokenManagement ||
        this.securityFeatures.inputValidation ||
        this.securityFeatures.sessionSecurity
      ),
    };
  }

  /**
   * Update security configuration
   */
  updateConfig(newConfig: Partial<SecurityConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * Update security features (when hooks change)
   */
  updateSecurityFeatures(newFeatures: Partial<SecurityFeatures>): void {
    this.securityFeatures = { ...this.securityFeatures, ...newFeatures };
  }
}

/**
 * Factory function to create SecurityComposer with moved hooks
 */
export function createSecurityComposer(
  securityFeatures: SecurityFeatures,
  config?: Partial<SecurityConfig>
): SecurityComposer {
  return new SecurityComposer(securityFeatures, config);
}
