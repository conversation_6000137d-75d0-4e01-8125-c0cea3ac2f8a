{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/i18n/config.ts"], "sourcesContent": ["/**\n * @file i18n Configuration\n * @module i18n/config\n *\n * Central configuration for internationalization settings.\n * Defines supported locales, default locale, and locale-specific settings.\n */\n\n/**\n * Supported locales in the application\n */\nexport const locales = ['en-US', 'ar-IQ'] as const;\n\n/**\n * Default locale for the application\n */\nexport const defaultLocale = 'en-US' as const;\n\n/**\n * Type for supported locales\n */\nexport type Locale = (typeof locales)[number];\n\n/**\n * RTL (Right-to-Left) locales\n */\nexport const rtlLocales = ['ar-IQ'] as const;\n\n/**\n * Check if a locale is RTL\n */\nexport function isRTLLocale(locale: string): boolean {\n  return rtlLocales.includes(locale as any);\n}\n\n/**\n * Locale display names for UI\n */\nexport const localeNames: Record<Locale, string> = {\n  'en-US': 'English',\n  'ar-IQ': 'العربية',\n};\n\n/**\n * Locale configuration for next-intl\n */\nexport const localeConfig = {\n  locales,\n  defaultLocale,\n  localePrefix: 'always' as const,\n};\n"], "names": [], "mappings": "AAAA;;;;;;CAMC,GAED;;CAEC;;;;;;;;AACM,MAAM,UAAU;IAAC;IAAS;CAAQ;AAKlC,MAAM,gBAAgB;AAUtB,MAAM,aAAa;IAAC;CAAQ;AAK5B,SAAS,YAAY,MAAc;IACxC,OAAO,WAAW,QAAQ,CAAC;AAC7B;AAKO,MAAM,cAAsC;IACjD,SAAS;IACT,SAAS;AACX;AAKO,MAAM,eAAe;IAC1B;IACA;IACA,cAAc;AAChB"}}, {"offset": {"line": 65, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/i18n/routing.ts"], "sourcesContent": ["/**\n * @file i18n Routing Configuration\n * @module i18n/routing\n *\n * Centralized routing configuration for next-intl.\n * Defines routing behavior, locale prefixes, and navigation settings.\n */\n\nimport { defineRouting } from 'next-intl/routing';\n\nimport { defaultLocale, locales } from './config';\n\n/**\n * Routing configuration for next-intl\n * \n * This configuration is shared between middleware and navigation APIs\n * to ensure consistent locale handling throughout the application.\n */\nexport const routing = defineRouting({\n  // List of all supported locales\n  locales,\n  \n  // Default locale used when no locale matches\n  defaultLocale,\n  \n  // Always show locale prefix in URLs for consistency\n  localePrefix: 'always',\n});\n"], "names": [], "mappings": "AAAA;;;;;;CAMC;;;AAED;AAEA;;;AAQO,MAAM,UAAU,CAAA,GAAA,0OAAA,CAAA,gBAAa,AAAD,EAAE;IACnC,gCAAgC;IAChC,SAAA,6HAAA,CAAA,UAAO;IAEP,6CAA6C;IAC7C,eAAA,6HAAA,CAAA,gBAAa;IAEb,oDAAoD;IACpD,cAAc;AAChB"}}, {"offset": {"line": 92, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/middleware.ts"], "sourcesContent": ["import type { NextRequest } from 'next/server';\n\nimport createIntlMiddleware from 'next-intl/middleware';\nimport { NextResponse } from 'next/server';\n\nimport { routing } from '@/i18n/routing';\n\n// Create the intl middleware using the routing configuration\nconst handleI18nRouting = createIntlMiddleware(routing);\n\n// Public routes that don't require authentication\nconst publicRoutes = new Set([\n  '/',\n  '/forgot-password',\n  '/login',\n  '/reset-password',\n  '/signup',\n]);\n\n// Protected routes that require authentication\nconst protectedRoutes = [\n  '/admin',\n  '/delegations',\n  '/tasks',\n  '/vehicles',\n  '/employees',\n  '/reports',\n  '/reporting',\n  '/settings',\n  '/reliability',\n  '/profile',\n  '/gifts',\n  '/recipients',\n];\n\n// Define JWT payload interface for type safety\ninterface JWTPayload {\n  exp?: number;\n  role?: string;\n  sub?: string;\n  user_role?: string;\n}\n\n/**\n * Main middleware function following next-intl best practices\n */\nexport default function middleware(request: NextRequest) {\n  const pathname = request.nextUrl.pathname;\n\n  // Skip middleware for static files and API routes\n  if (\n    pathname.startsWith('/api/') ||\n    pathname.startsWith('/_next/') ||\n    pathname.startsWith('/favicon.ico') ||\n    pathname.includes('.')\n  ) {\n    return handleI18nRouting(request);\n  }\n\n  // Extract locale and path without locale prefix\n  const localeMatch = pathname.match(/^\\/([a-z]{2}-[A-Z]{2})(\\/.*)?$/);\n  const locale = localeMatch?.[1];\n  const pathWithoutLocale = localeMatch?.[2] || '/';\n\n  // Check if it's a public route\n  const isPublicRoute = publicRoutes.has(pathWithoutLocale);\n\n  // Check if it's a protected route\n  const isProtectedRoute = protectedRoutes.some(route =>\n    pathWithoutLocale.startsWith(route)\n  );\n\n  // Handle authentication for protected routes\n  if (isProtectedRoute && !isPublicRoute) {\n    const accessToken = request.cookies.get('sb-access-token')?.value;\n\n    if (!accessToken) {\n      // Redirect to login with current locale\n      const loginUrl = new URL(\n        `/${locale || routing.defaultLocale}/login`,\n        request.url\n      );\n      loginUrl.searchParams.set('redirect', pathname);\n      return NextResponse.redirect(loginUrl);\n    }\n\n    // Basic token validation (signature validation should be done server-side)\n    try {\n      const tokenParts = accessToken.split('.');\n      if (tokenParts.length === 3) {\n        const payload = JSON.parse(atob(tokenParts[1])) as JWTPayload;\n        const now = Math.floor(Date.now() / 1000);\n\n        if (payload.exp && payload.exp < now) {\n          // Token expired, redirect to login\n          const loginUrl = new URL(\n            `/${locale || routing.defaultLocale}/login`,\n            request.url\n          );\n          loginUrl.searchParams.set('redirect', pathname);\n          return NextResponse.redirect(loginUrl);\n        }\n      }\n    } catch {\n      // Invalid token, redirect to login\n      const loginUrl = new URL(\n        `/${locale || routing.defaultLocale}/login`,\n        request.url\n      );\n      loginUrl.searchParams.set('redirect', pathname);\n      return NextResponse.redirect(loginUrl);\n    }\n  }\n\n  // Handle i18n routing\n  return handleI18nRouting(request);\n}\n\n/**\n * Enhanced CSP implementation now handled by cspConfig.ts\n * This provides strict 2025 security standards with comprehensive directives\n */\n\n/**\n * Generate cryptographically secure nonce\n */\nfunction generateNonce(): string {\n  // Generate 32 random bytes for a secure nonce\n  const bytes = new Uint8Array(32);\n  crypto.getRandomValues(bytes);\n  return Buffer.from(bytes).toString('base64');\n}\n\n/**\n * Authentication middleware\n */\nasync function handleAuthentication(\n  request: NextRequest\n): Promise<NextResponse | null> {\n  const path = request.nextUrl.pathname;\n\n  // Extract the path without locale prefix for route matching\n  const pathWithoutLocale = path.replace(/^\\/[a-z]{2}-[A-Z]{2}/, '') || '/';\n\n  const isProtectedRoute = protectedRoutes.some(route =>\n    pathWithoutLocale.startsWith(route)\n  );\n  const isPublicRoute = publicRoutes.has(pathWithoutLocale);\n  const isAdminRoute = adminRoutes.some(route =>\n    pathWithoutLocale.startsWith(route)\n  );\n\n  // Skip auth for public routes and API routes\n  if (isPublicRoute || path.startsWith('/api/') || path.startsWith('/_next/')) {\n    return null;\n  }\n\n  // SECURITY FIX: Use proper JWT token validation instead of insecure session decryption\n  // Get JWT token from secure httpOnly cookie (set by backend)\n  const accessToken = request.cookies.get('sb-access-token')?.value;\n\n  if (!accessToken && isProtectedRoute) {\n    // Extract locale from path for proper redirect\n    const localeMatch = path.match(/^\\/([a-z]{2}-[A-Z]{2})/);\n    const locale = localeMatch ? localeMatch[1] : defaultLocale;\n    const loginUrl = new URL(`/${locale}/login`, request.url);\n    loginUrl.searchParams.set('redirect', path);\n    return NextResponse.redirect(loginUrl);\n  }\n\n  if (accessToken) {\n    try {\n      // Basic JWT format validation (3 parts separated by dots)\n      const tokenParts = accessToken.split('.');\n      if (tokenParts.length !== 3) {\n        throw new Error('Invalid JWT format');\n      }\n\n      // Decode JWT payload (this is safe as it's just base64 decoding for reading claims)\n      // Note: This does NOT validate the signature - that must be done server-side\n      const payloadPart = tokenParts[1];\n      if (!payloadPart) {\n        throw new Error('Invalid JWT payload');\n      }\n      const payload = JSON.parse(atob(payloadPart)) as JWTPayload;\n\n      // Check if token is expired\n      const now = Math.floor(Date.now() / 1000);\n      if (payload.exp && payload.exp < now) {\n        throw new Error('Token expired');\n      }\n\n      // Extract user role for admin route protection\n      const userRole = payload.user_role ?? payload.role ?? 'USER';\n\n      // Admin route protection\n      if (isAdminRoute && !['ADMIN', 'SUPER_ADMIN'].includes(userRole)) {\n        // Extract locale for proper redirect\n        const localeMatch = path.match(/^\\/([a-z]{2}-[A-Z]{2})/);\n        const locale = localeMatch ? localeMatch[1] : defaultLocale;\n        return NextResponse.redirect(\n          new URL(`/${locale}/unauthorized`, request.url)\n        );\n      }\n\n      // Redirect authenticated users from auth pages\n      if (\n        payload.sub &&\n        (pathWithoutLocale === '/login' || pathWithoutLocale === '/signup')\n      ) {\n        // Extract locale for proper redirect\n        const localeMatch = path.match(/^\\/([a-z]{2}-[A-Z]{2})/);\n        const locale = localeMatch ? localeMatch[1] : defaultLocale;\n        return NextResponse.redirect(new URL(`/${locale}`, request.url));\n      }\n    } catch {\n      // Invalid or expired token, redirect to login for protected routes\n      if (isProtectedRoute) {\n        // Extract locale for proper redirect\n        const localeMatch = path.match(/^\\/([a-z]{2}-[A-Z]{2})/);\n        const locale = localeMatch ? localeMatch[1] : defaultLocale;\n        const loginUrl = new URL(`/${locale}/login`, request.url);\n        loginUrl.searchParams.set('redirect', path);\n        return NextResponse.redirect(loginUrl);\n      }\n    }\n  }\n\n  return null;\n}\n\n/**\n * CORS middleware for API routes\n */\nfunction handleCORS(request: NextRequest): NextResponse | null {\n  // Only apply CORS to API routes\n  if (!request.nextUrl.pathname.startsWith('/api/')) {\n    return null;\n  }\n\n  const allowedOrigins = [\n    'https://workhub.company.com',\n    'https://staging.workhub.company.com',\n    ...(process.env.NODE_ENV === 'development'\n      ? ['http://localhost:9002', 'http://localhost:3000']\n      : []),\n  ];\n\n  const origin = request.headers.get('origin') ?? '';\n  const isAllowedOrigin = allowedOrigins.includes(origin);\n\n  // Handle preflight requests\n  if (request.method === 'OPTIONS') {\n    const preflightHeaders = {\n      'Access-Control-Allow-Credentials': 'true',\n      'Access-Control-Allow-Headers':\n        'Content-Type, Authorization, X-CSRF-Token, X-Requested-With',\n      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',\n      'Access-Control-Max-Age': '86400',\n      ...(isAllowedOrigin && { 'Access-Control-Allow-Origin': origin }),\n    };\n\n    return new NextResponse(null, { headers: preflightHeaders, status: 200 });\n  }\n\n  return null;\n}\n\n/**\n * Middleware configuration\n */\nexport const config = {\n  matcher: [\n    /*\n     * Match all request paths except for the ones starting with:\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     */\n    '/((?!_next/static|_next/image|favicon.ico).*)',\n  ],\n};\n"], "names": [], "mappings": ";;;;AAkIS;AAhIT;AACA;AAAA;AAEA;;;;AAEA,6DAA6D;AAC7D,MAAM,oBAAoB,CAAA,GAAA,8LAAA,CAAA,UAAoB,AAAD,EAAE,8HAAA,CAAA,UAAO;AAEtD,kDAAkD;AAClD,MAAM,eAAe,IAAI,IAAI;IAC3B;IACA;IACA;IACA;IACA;CACD;AAED,+CAA+C;AAC/C,MAAM,kBAAkB;IACtB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAac,SAAS,WAAW,OAAoB;IACrD,MAAM,WAAW,QAAQ,OAAO,CAAC,QAAQ;IAEzC,kDAAkD;IAClD,IACE,SAAS,UAAU,CAAC,YACpB,SAAS,UAAU,CAAC,cACpB,SAAS,UAAU,CAAC,mBACpB,SAAS,QAAQ,CAAC,MAClB;QACA,OAAO,kBAAkB;IAC3B;IAEA,gDAAgD;IAChD,MAAM,cAAc,SAAS,KAAK,CAAC;IACnC,MAAM,SAAS,aAAa,CAAC,EAAE;IAC/B,MAAM,oBAAoB,aAAa,CAAC,EAAE,IAAI;IAE9C,+BAA+B;IAC/B,MAAM,gBAAgB,aAAa,GAAG,CAAC;IAEvC,kCAAkC;IAClC,MAAM,mBAAmB,gBAAgB,IAAI,CAAC,CAAA,QAC5C,kBAAkB,UAAU,CAAC;IAG/B,6CAA6C;IAC7C,IAAI,oBAAoB,CAAC,eAAe;QACtC,MAAM,cAAc,QAAQ,OAAO,CAAC,GAAG,CAAC,oBAAoB;QAE5D,IAAI,CAAC,aAAa;YAChB,wCAAwC;YACxC,MAAM,WAAW,IAAI,IACnB,CAAC,CAAC,EAAE,UAAU,8HAAA,CAAA,UAAO,CAAC,aAAa,CAAC,MAAM,CAAC,EAC3C,QAAQ,GAAG;YAEb,SAAS,YAAY,CAAC,GAAG,CAAC,YAAY;YACtC,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;QAC/B;QAEA,2EAA2E;QAC3E,IAAI;YACF,MAAM,aAAa,YAAY,KAAK,CAAC;YACrC,IAAI,WAAW,MAAM,KAAK,GAAG;gBAC3B,MAAM,UAAU,KAAK,KAAK,CAAC,KAAK,UAAU,CAAC,EAAE;gBAC7C,MAAM,MAAM,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK;gBAEpC,IAAI,QAAQ,GAAG,IAAI,QAAQ,GAAG,GAAG,KAAK;oBACpC,mCAAmC;oBACnC,MAAM,WAAW,IAAI,IACnB,CAAC,CAAC,EAAE,UAAU,8HAAA,CAAA,UAAO,CAAC,aAAa,CAAC,MAAM,CAAC,EAC3C,QAAQ,GAAG;oBAEb,SAAS,YAAY,CAAC,GAAG,CAAC,YAAY;oBACtC,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;gBAC/B;YACF;QACF,EAAE,OAAM;YACN,mCAAmC;YACnC,MAAM,WAAW,IAAI,IACnB,CAAC,CAAC,EAAE,UAAU,8HAAA,CAAA,UAAO,CAAC,aAAa,CAAC,MAAM,CAAC,EAC3C,QAAQ,GAAG;YAEb,SAAS,YAAY,CAAC,GAAG,CAAC,YAAY;YACtC,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;QAC/B;IACF;IAEA,sBAAsB;IACtB,OAAO,kBAAkB;AAC3B;AAEA;;;CAGC,GAED;;CAEC,GACD,SAAS;IACP,8CAA8C;IAC9C,MAAM,QAAQ,IAAI,WAAW;IAC7B,OAAO,eAAe,CAAC;IACvB,OAAO,qHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,OAAO,QAAQ,CAAC;AACrC;AAEA;;CAEC,GACD,eAAe,qBACb,OAAoB;IAEpB,MAAM,OAAO,QAAQ,OAAO,CAAC,QAAQ;IAErC,4DAA4D;IAC5D,MAAM,oBAAoB,KAAK,OAAO,CAAC,wBAAwB,OAAO;IAEtE,MAAM,mBAAmB,gBAAgB,IAAI,CAAC,CAAA,QAC5C,kBAAkB,UAAU,CAAC;IAE/B,MAAM,gBAAgB,aAAa,GAAG,CAAC;IACvC,MAAM,eAAe,YAAY,IAAI,CAAC,CAAA,QACpC,kBAAkB,UAAU,CAAC;IAG/B,6CAA6C;IAC7C,IAAI,iBAAiB,KAAK,UAAU,CAAC,YAAY,KAAK,UAAU,CAAC,YAAY;QAC3E,OAAO;IACT;IAEA,uFAAuF;IACvF,6DAA6D;IAC7D,MAAM,cAAc,QAAQ,OAAO,CAAC,GAAG,CAAC,oBAAoB;IAE5D,IAAI,CAAC,eAAe,kBAAkB;QACpC,+CAA+C;QAC/C,MAAM,cAAc,KAAK,KAAK,CAAC;QAC/B,MAAM,SAAS,cAAc,WAAW,CAAC,EAAE,GAAG;QAC9C,MAAM,WAAW,IAAI,IAAI,CAAC,CAAC,EAAE,OAAO,MAAM,CAAC,EAAE,QAAQ,GAAG;QACxD,SAAS,YAAY,CAAC,GAAG,CAAC,YAAY;QACtC,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;IAC/B;IAEA,IAAI,aAAa;QACf,IAAI;YACF,0DAA0D;YAC1D,MAAM,aAAa,YAAY,KAAK,CAAC;YACrC,IAAI,WAAW,MAAM,KAAK,GAAG;gBAC3B,MAAM,IAAI,MAAM;YAClB;YAEA,oFAAoF;YACpF,6EAA6E;YAC7E,MAAM,cAAc,UAAU,CAAC,EAAE;YACjC,IAAI,CAAC,aAAa;gBAChB,MAAM,IAAI,MAAM;YAClB;YACA,MAAM,UAAU,KAAK,KAAK,CAAC,KAAK;YAEhC,4BAA4B;YAC5B,MAAM,MAAM,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK;YACpC,IAAI,QAAQ,GAAG,IAAI,QAAQ,GAAG,GAAG,KAAK;gBACpC,MAAM,IAAI,MAAM;YAClB;YAEA,+CAA+C;YAC/C,MAAM,WAAW,QAAQ,SAAS,IAAI,QAAQ,IAAI,IAAI;YAEtD,yBAAyB;YACzB,IAAI,gBAAgB,CAAC;gBAAC;gBAAS;aAAc,CAAC,QAAQ,CAAC,WAAW;gBAChE,qCAAqC;gBACrC,MAAM,cAAc,KAAK,KAAK,CAAC;gBAC/B,MAAM,SAAS,cAAc,WAAW,CAAC,EAAE,GAAG;gBAC9C,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAC1B,IAAI,IAAI,CAAC,CAAC,EAAE,OAAO,aAAa,CAAC,EAAE,QAAQ,GAAG;YAElD;YAEA,+CAA+C;YAC/C,IACE,QAAQ,GAAG,IACX,CAAC,sBAAsB,YAAY,sBAAsB,SAAS,GAClE;gBACA,qCAAqC;gBACrC,MAAM,cAAc,KAAK,KAAK,CAAC;gBAC/B,MAAM,SAAS,cAAc,WAAW,CAAC,EAAE,GAAG;gBAC9C,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,CAAC,EAAE,QAAQ,EAAE,QAAQ,GAAG;YAChE;QACF,EAAE,OAAM;YACN,mEAAmE;YACnE,IAAI,kBAAkB;gBACpB,qCAAqC;gBACrC,MAAM,cAAc,KAAK,KAAK,CAAC;gBAC/B,MAAM,SAAS,cAAc,WAAW,CAAC,EAAE,GAAG;gBAC9C,MAAM,WAAW,IAAI,IAAI,CAAC,CAAC,EAAE,OAAO,MAAM,CAAC,EAAE,QAAQ,GAAG;gBACxD,SAAS,YAAY,CAAC,GAAG,CAAC,YAAY;gBACtC,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;YAC/B;QACF;IACF;IAEA,OAAO;AACT;AAEA;;CAEC,GACD,SAAS,WAAW,OAAoB;IACtC,gCAAgC;IAChC,IAAI,CAAC,QAAQ,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,UAAU;QACjD,OAAO;IACT;IAEA,MAAM,iBAAiB;QACrB;QACA;WACI,uCACA;YAAC;YAAyB;SAAwB;KAEvD;IAED,MAAM,SAAS,QAAQ,OAAO,CAAC,GAAG,CAAC,aAAa;IAChD,MAAM,kBAAkB,eAAe,QAAQ,CAAC;IAEhD,4BAA4B;IAC5B,IAAI,QAAQ,MAAM,KAAK,WAAW;QAChC,MAAM,mBAAmB;YACvB,oCAAoC;YACpC,gCACE;YACF,gCAAgC;YAChC,0BAA0B;YAC1B,GAAI,mBAAmB;gBAAE,+BAA+B;YAAO,CAAC;QAClE;QAEA,OAAO,IAAI,6LAAA,CAAA,eAAY,CAAC,MAAM;YAAE,SAAS;YAAkB,QAAQ;QAAI;IACzE;IAEA,OAAO;AACT;AAKO,MAAM,SAAS;IACpB,SAAS;QACP;;;;;KAKC,GACD;KACD;AACH"}}]}