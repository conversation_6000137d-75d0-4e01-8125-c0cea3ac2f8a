# Font Size & Theme Settings - User Guide

**Document Version:** 1.0  
**Created:** January 2025  
**Status:** Ready for Use  
**Priority:** High

## 🎯 **Overview**

This guide explains how to use the new font size and theme settings in WorkHub, providing users with comprehensive customization options for their interface preferences.

## 🎨 **Theme Settings**

### **Accessing Theme Controls**

#### **1. Navigation Bar (Quick Access)**

- Click the **theme icon** (sun/moon) in the top navigation
- Select from: Light, Dark, or System
- Changes apply immediately

#### **2. Settings Modal**

- Click the **Type icon** (A) in navigation → "All Settings"
- Navigate to "Theme Preferences" section
- Full theme management interface with previews

#### **3. Settings Page**

- Visit `/settings` for comprehensive theme controls
- Detailed theme information and system integration

### **Theme Options**

#### **🌞 Light Theme**

- Clean, bright interface
- Best for well-lit environments
- High contrast for readability

#### **🌙 Dark Theme**

- Easy on the eyes in low light
- Reduces eye strain
- Modern, sleek appearance

#### **🖥️ System Theme**

- Automatically follows your device settings
- Switches between light/dark based on system preference
- Seamless integration with OS

### **Theme Features**

- ✅ **Instant Application** - Changes apply immediately
- ✅ **Persistent Storage** - Settings saved across sessions
- ✅ **System Integration** - Respects OS preferences
- ✅ **Accessibility** - Maintains proper contrast ratios

## 📝 **Font Size Settings**

### **Accessing Font Size Controls**

#### **1. Navigation Bar (Quick Access)**

- Click the **Type icon** (A) in the top navigation
- Use quick buttons: Small, Medium, Large
- See current selection with badge indicator

#### **2. Settings Modal**

- Click "All Settings" from navigation
- Navigate to "Display Preferences" section
- Full font size interface with live preview

#### **3. Settings Page**

- Visit `/settings` for comprehensive font controls
- Detailed preview and accessibility information

### **Font Size Options**

#### **📏 Small (text-sm)**

- Compact text for more content
- Ideal for data-heavy interfaces
- Efficient screen space usage

#### **📏 Medium (text-base)**

- Standard readable text
- Default setting for most users
- Balanced readability and space

#### **📏 Large (text-lg)**

- Larger text for better accessibility
- Easier reading for visual impairments
- Improved readability on large screens

### **Font Size Features**

- ✅ **Global Application** - Affects entire application
- ✅ **Live Preview** - See changes in real-time
- ✅ **Accessibility Focused** - Improves readability
- ✅ **Responsive Design** - Works on all screen sizes

## 🚀 **How to Use**

### **Quick Setup (2 minutes)**

1. **Set Your Theme**

   ```
   Navigation → Theme Icon → Select Preference
   ```

2. **Adjust Font Size**

   ```
   Navigation → Type Icon → Choose Size
   ```

3. **Verify Settings**
   ```
   Settings Page → Review Current Configuration
   ```

### **Advanced Configuration**

#### **1. Open Settings Modal**

- Click Type icon (A) in navigation
- Select "All Settings"
- Comprehensive interface opens

#### **2. Theme Configuration**

- Choose color scheme (Light/Dark/System)
- Preview changes in real-time
- System theme auto-syncs with OS

#### **3. Font Size Configuration**

- Select from three size options
- View live preview of changes
- Test with sample content

#### **4. Save and Apply**

- Changes save automatically
- No manual save required
- Settings persist across sessions

### **Testing Your Settings**

#### **Font Size Demo Page**

- Visit `/font-size-demo`
- Test all font sizes with sample content
- See how changes affect different text types

#### **Settings Overview**

- Visit `/settings`
- Review all current preferences
- Reset to defaults if needed

## 🎛️ **Settings Management**

### **Current Settings Summary**

View your active preferences:

- **Theme**: Light/Dark/System
- **Font Size**: Small/Medium/Large
- **Notifications**: Enabled/Disabled
- **Layout**: Table density, Dashboard layout

### **Reset to Defaults**

- Open Settings Modal or Settings Page
- Click "Reset to Defaults" button
- Confirms before applying changes
- Restores original WorkHub settings

### **Import/Export** (Future Feature)

- Settings backup and restore
- Share preferences across devices
- Team-wide setting templates

## 🔧 **Troubleshooting**

### **Common Issues**

#### **Theme Not Changing**

1. Check browser compatibility
2. Clear browser cache
3. Disable browser extensions
4. Try incognito/private mode

#### **Font Size Not Applying**

1. Refresh the page
2. Check for browser zoom settings
3. Verify JavaScript is enabled
4. Clear localStorage if needed

#### **Settings Not Persisting**

1. Check browser storage permissions
2. Ensure cookies are enabled
3. Verify localStorage is available
4. Try different browser

### **Browser Support**

- ✅ Chrome 90+
- ✅ Firefox 88+
- ✅ Safari 14+
- ✅ Edge 90+

### **Performance Tips**

- Settings changes are instant
- No page reload required
- Minimal performance impact
- Works offline once loaded

## 📱 **Mobile Experience**

### **Responsive Design**

- Touch-friendly controls
- Optimized for mobile screens
- Swipe gestures supported
- Accessible on all devices

### **Mobile-Specific Features**

- Larger touch targets
- Simplified navigation
- Auto-hide on scroll
- Battery-efficient animations

## 🎯 **Best Practices**

### **For Better Readability**

1. **Use Large Font** if you have visual difficulties
2. **Dark Theme** for extended screen time
3. **System Theme** for automatic adaptation
4. **Test Settings** with actual work content

### **For Productivity**

1. **Small Font** for data-heavy tasks
2. **Light Theme** for detailed work
3. **Consistent Settings** across devices
4. **Regular Review** of preferences

### **For Accessibility**

1. **High Contrast** theme combinations
2. **Larger Font Sizes** for better visibility
3. **System Integration** for OS accessibility features
4. **Keyboard Navigation** support

## 🔮 **Future Enhancements**

### **Planned Features**

- **Custom Font Families** - Additional font options
- **Color Customization** - Custom theme colors
- **Layout Density** - More spacing options
- **Animation Controls** - Motion preferences

### **Advanced Settings**

- **Per-Page Preferences** - Different settings per section
- **Time-Based Themes** - Automatic day/night switching
- **Team Themes** - Organization-wide preferences
- **Accessibility Profiles** - Predefined accessibility settings

## 📞 **Support**

### **Getting Help**

- Check this guide first
- Visit the Settings Demo page
- Contact system administrator
- Submit feedback through the app

### **Feedback**

Your feedback helps improve WorkHub:

- Report bugs or issues
- Suggest new features
- Share usage patterns
- Rate your experience

---

**Need Help?** Visit the Settings page or contact your system administrator for assistance with customizing your WorkHub experience.
