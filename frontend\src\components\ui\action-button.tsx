'use client';

import { Slot } from '@radix-ui/react-slot';
import { Loader2 } from 'lucide-react';
import React from 'react';

import type { ButtonProps } from '@/components/ui/button';

import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

export interface ActionButtonProps extends Omit<ButtonProps, 'variant'> {
  /**
   * The type of action this button represents
   * - primary: Main actions (Create, Save, Submit)
   * - secondary: Alternative actions (View, Edit)
   * - tertiary: Optional actions (Cancel, Back)
   * - danger: Destructive actions (Delete, Remove)
   */
  actionType?: ActionType;

  /**
   * Icon to display before the button text
   * Should be a Lucide icon with consistent sizing (h-4 w-4)
   */
  icon?: React.ReactNode;

  /**
   * Whether the button is in a loading state
   */
  isLoading?: boolean;

  /**
   * Text to display when button is loading
   * If not provided, will use children
   */
  loadingText?: string;
}

export type ActionType = 'danger' | 'primary' | 'secondary' | 'tertiary';

/**
 * ActionButton component for consistent action styling across the application
 *
 * @example
 * <ActionButton actionType="primary" icon={<PlusCircle />}>
 *   Add New
 * </ActionButton>
 */
export const ActionButton = React.forwardRef<
  HTMLButtonElement,
  ActionButtonProps
>(
  (
    {
      actionType = 'primary',
      asChild = false,
      children,
      className,
      disabled,
      icon,
      isLoading = false,
      loadingText,
      ...props
    },
    ref
  ) => {
    // Map action types to shadcn/ui button variants and additional styling
    const actionStyles: Record<
      ActionType,
      { className: string; variant: ButtonProps['variant']; }
    > = {
      danger: {
        className: 'shadow-md',
        variant: 'destructive',
      },
      primary: {
        className: 'shadow-md',
        variant: 'default',
      },
      secondary: {
        className: '',
        variant: 'secondary',
      },
      tertiary: {
        className: '',
        variant: 'outline',
      },
    };

    const { className: actionClassName, variant } = actionStyles[actionType];

    // const Comp = asChild ? Slot : "button"; // This was for an older structure, Button handles asChild now

    return (
      <Button
        asChild={asChild} // This is passed to the underlying shadcn Button
        className={cn(actionClassName, className)}
        disabled={isLoading || disabled}
        ref={ref}
        variant={variant}
        {...props}
      >
        {isLoading ? (
          <span className="inline-flex items-center">
            {' '}
            {/* Replaced Fragment with span */}
            <Loader2 className="mr-2 size-4 animate-spin" />
            {loadingText || children}
          </span>
        ) : (
          <span className="inline-flex items-center">
            {' '}
            {/* Replaced Fragment with span */}
            {icon && <span className="mr-2">{icon}</span>}
            {children}
          </span>
        )}
      </Button>
    );
  }
);

ActionButton.displayName = 'ActionButton';
