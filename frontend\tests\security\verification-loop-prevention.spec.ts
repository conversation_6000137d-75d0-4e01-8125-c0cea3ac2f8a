/**
 * @file Verification Loop Prevention E2E Tests
 * @description End-to-end tests for authentication verification loop prevention
 */

import { test, expect, Page } from '@playwright/test';

// Test configuration
const TEST_CONFIG = {
  baseURL: 'http://localhost:9002',
  timeout: 30000,
  retries: 2,
};

// Helper function to simulate authentication failure
async function simulateAuthFailure(page: Page, count: number = 1) {
  for (let i = 0; i < count; i++) {
    await page.evaluate(() => {
      // Trigger authentication failure event
      window.dispatchEvent(
        new CustomEvent('auth:failure', {
          detail: { reason: 'Token validation failed' },
        })
      );
    });
    await page.waitForTimeout(100);
  }
}

// Helper function to check circuit breaker state
async function getCircuitBreakerState(page: Page) {
  return await page.evaluate(() => {
    // Access SecurityUtils from window if available
    const SecurityUtils = (window as any).SecurityUtils;
    if (
      SecurityUtils &&
      typeof SecurityUtils.getCircuitBreakerState === 'function'
    ) {
      return SecurityUtils.getCircuitBreakerState();
    }
    return null;
  });
}

// Helper function to inject SecurityUtils for testing
async function injectSecurityUtils(page: Page) {
  await page.addInitScript(() => {
    // Mock SecurityUtils for testing
    (window as any).SecurityUtils = {
      attempts: 0,
      isOpen: false,
      activeOperations: new Set(),

      recordSecurityAttempt() {
        this.attempts++;
        if (this.attempts >= 5) {
          this.isOpen = true;
          console.error(
            '🚨 Security verification loop detected - circuit breaker activated'
          );
        }
      },

      recordSecuritySuccess() {
        this.attempts = 0;
        this.isOpen = false;
      },

      canPerformSecurityCheck() {
        return !this.isOpen;
      },

      getCircuitBreakerState() {
        return {
          attemptCount: this.attempts,
          isOpen: this.isOpen,
          activeOperations: Array.from(this.activeOperations),
          lastAttempt: Date.now(),
          lastOperationTime: Date.now(),
        };
      },

      startSecurityOperation(operationId) {
        if (this.isOpen) return false;
        if (this.activeOperations.has(operationId)) return false;
        this.activeOperations.add(operationId);
        return true;
      },

      endSecurityOperation(operationId) {
        this.activeOperations.delete(operationId);
      },

      resetCircuitBreakerForTesting() {
        this.attempts = 0;
        this.isOpen = false;
        this.activeOperations.clear();
      },
    };
  });
}

test.describe('Verification Loop Prevention', () => {
  test.beforeEach(async ({ page }) => {
    // Inject SecurityUtils mock
    await injectSecurityUtils(page);

    // Navigate to the application
    await page.goto(TEST_CONFIG.baseURL);

    // Wait for the page to load
    await page.waitForLoadState('networkidle');
  });

  test('should prevent authentication verification loops', async ({ page }) => {
    // Simulate multiple authentication failures
    await simulateAuthFailure(page, 6);

    // Check circuit breaker state
    const state = await getCircuitBreakerState(page);
    expect(state).toBeTruthy();
    expect(state.isOpen).toBe(true);
    expect(state.attemptCount).toBeGreaterThanOrEqual(5);

    // Verify error message in console
    const logs = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        logs.push(msg.text());
      }
    });

    await page.waitForTimeout(1000);
    expect(logs.some(log => log.includes('circuit breaker activated'))).toBe(
      true
    );
  });

  test('should block security operations when circuit is open', async ({
    page,
  }) => {
    // Open the circuit breaker
    await simulateAuthFailure(page, 5);

    // Try to perform a security operation
    const operationResult = await page.evaluate(() => {
      const SecurityUtils = (window as any).SecurityUtils;
      return SecurityUtils.startSecurityOperation('test-operation');
    });

    expect(operationResult).toBe(false);
  });

  test('should allow operations when circuit is closed', async ({ page }) => {
    // Ensure circuit is closed
    await page.evaluate(() => {
      (window as any).SecurityUtils.resetCircuitBreakerForTesting();
    });

    // Try to perform a security operation
    const operationResult = await page.evaluate(() => {
      const SecurityUtils = (window as any).SecurityUtils;
      return SecurityUtils.startSecurityOperation('test-operation');
    });

    expect(operationResult).toBe(true);
  });

  test('should prevent concurrent security operations', async ({ page }) => {
    // Start multiple operations with the same ID
    const results = await page.evaluate(() => {
      const SecurityUtils = (window as any).SecurityUtils;
      const operationId = 'concurrent-test';

      return [
        SecurityUtils.startSecurityOperation(operationId),
        SecurityUtils.startSecurityOperation(operationId),
        SecurityUtils.startSecurityOperation(operationId),
      ];
    });

    // Only the first operation should succeed
    expect(results[0]).toBe(true);
    expect(results[1]).toBe(false);
    expect(results[2]).toBe(false);
  });

  test('should reset circuit breaker on success', async ({ page }) => {
    // Record some failures
    await simulateAuthFailure(page, 3);

    // Record a success
    await page.evaluate(() => {
      (window as any).SecurityUtils.recordSecuritySuccess();
    });

    // Check state is reset
    const state = await getCircuitBreakerState(page);
    expect(state.attemptCount).toBe(0);
    expect(state.isOpen).toBe(false);
  });

  test('should handle rapid authentication attempts gracefully', async ({
    page,
  }) => {
    // Simulate rapid authentication attempts
    const promises = Array.from({ length: 20 }, (_, i) =>
      page.evaluate(index => {
        const SecurityUtils = (window as any).SecurityUtils;
        const operationId = `rapid-auth-${index}`;

        if (SecurityUtils.startSecurityOperation(operationId)) {
          // Simulate some work
          setTimeout(() => {
            SecurityUtils.endSecurityOperation(operationId);
          }, Math.random() * 100);
          return true;
        }
        return false;
      }, i)
    );

    const results = await Promise.all(promises);

    // Some operations should succeed, some should be blocked
    const successCount = results.filter(Boolean).length;
    expect(successCount).toBeGreaterThan(0);
    expect(successCount).toBeLessThan(20); // Not all should succeed due to coordination
  });

  test('should maintain session state during circuit breaker activation', async ({
    page,
  }) => {
    // Check initial session state
    const initialSession = await page.evaluate(() => {
      return localStorage.getItem('workhub-session-state');
    });

    // Trigger circuit breaker
    await simulateAuthFailure(page, 5);

    // Session state should still exist
    const finalSession = await page.evaluate(() => {
      return localStorage.getItem('workhub-session-state');
    });

    expect(finalSession).toBeTruthy();
  });

  test('should log security events appropriately', async ({ page }) => {
    const consoleLogs = [];

    page.on('console', msg => {
      consoleLogs.push({
        type: msg.type(),
        text: msg.text(),
        timestamp: Date.now(),
      });
    });

    // Trigger various security events
    await simulateAuthFailure(page, 3);

    await page.evaluate(() => {
      (window as any).SecurityUtils.recordSecuritySuccess();
    });

    await simulateAuthFailure(page, 5);

    await page.waitForTimeout(1000);

    // Check for appropriate log messages
    const errorLogs = consoleLogs.filter(log => log.type === 'error');
    const debugLogs = consoleLogs.filter(
      log => log.type === 'log' || log.type === 'debug'
    );

    expect(errorLogs.length).toBeGreaterThan(0);
    expect(errorLogs.some(log => log.text.includes('circuit breaker'))).toBe(
      true
    );
  });

  test('should handle page refresh without losing circuit breaker state', async ({
    page,
  }) => {
    // Trigger some failures
    await simulateAuthFailure(page, 3);

    // Get state before refresh
    const stateBefore = await getCircuitBreakerState(page);

    // Refresh the page
    await page.reload();
    await page.waitForLoadState('networkidle');

    // Re-inject SecurityUtils after refresh
    await injectSecurityUtils(page);

    // State should be maintained (in a real implementation, this would be persisted)
    // For this test, we'll verify the system can handle the refresh gracefully
    const stateAfter = await getCircuitBreakerState(page);
    expect(stateAfter).toBeTruthy();
  });

  test('should handle network errors without triggering circuit breaker', async ({
    page,
  }) => {
    // Mock network failure
    await page.route('**/api/**', route => {
      route.abort('failed');
    });

    // Try to make API calls that will fail due to network
    await page.evaluate(async () => {
      try {
        await fetch('/api/auth/refresh', { method: 'POST' });
      } catch (error) {
        // Expected to fail
      }
    });

    // Circuit breaker should not be triggered by network errors
    const state = await getCircuitBreakerState(page);
    expect(state.isOpen).toBe(false);
  });

  test('should provide visual feedback for security state', async ({
    page,
  }) => {
    // This test would check for any UI indicators of security state
    // For now, we'll just verify the page remains functional

    await simulateAuthFailure(page, 5);

    // Page should still be responsive
    const title = await page.title();
    expect(title).toBeTruthy();

    // Navigation should still work
    const currentUrl = page.url();
    expect(currentUrl).toContain(TEST_CONFIG.baseURL);
  });
});

test.describe('Security Monitoring Integration', () => {
  test.beforeEach(async ({ page }) => {
    await injectSecurityUtils(page);
    await page.goto(TEST_CONFIG.baseURL);
    await page.waitForLoadState('networkidle');
  });

  test('should detect and report security events', async ({ page }) => {
    const securityEvents = [];

    // Mock security event reporting
    await page.addInitScript(() => {
      (window as any).securityEvents = [];
      (window as any).reportSecurityEvent = event => {
        (window as any).securityEvents.push(event);
      };
    });

    // Trigger security events
    await simulateAuthFailure(page, 5);

    // Check reported events
    const events = await page.evaluate(() => (window as any).securityEvents);
    expect(events.length).toBeGreaterThan(0);
  });

  test('should calculate threat levels correctly', async ({ page }) => {
    // This would test the threat level calculation
    // Based on the number and severity of security events

    await simulateAuthFailure(page, 2);

    // Threat level should be low to medium
    const threatLevel = await page.evaluate(() => {
      // Mock threat level calculation
      const events = (window as any).securityEvents || [];
      if (events.length >= 5) return 'high';
      if (events.length >= 2) return 'medium';
      return 'low';
    });

    expect(['low', 'medium']).toContain(threatLevel);
  });
});
