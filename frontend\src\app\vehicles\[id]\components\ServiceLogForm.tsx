// @ts-nocheck - Keeping for now due to potential type complexities with Vehicle/ServiceRecord from old vs new
'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2, PlusCircle } from 'lucide-react';
import { useState } from 'react'; // Keep for local form state if not using RHF for everything
import { type SubmitHandler, useForm } from 'react-hook-form';

import type {
  ServiceRecord as DomainServiceRecord,
  Vehicle as DomainVehicle,
  // Assuming a CreateServiceRecordData type exists or will be created
  // For now, we'll construct the payload based on DomainServiceRecord
} from '@/lib/types/domain';

import { ActionButton } from '@/components/ui/action-button';
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { useCreateServiceRecord } from '@/lib/stores/queries/useServiceRecords';
import { serviceRecordToast } from '@/lib/services/toastService'; // Assumed hook
import { formatDateForApi } from '@/lib/utils/dateUtils';

// Validation schemas
import {
  ServiceRecordCreateSchema,
  ServiceRecordSchemaUtils,
  type ServiceRecordCreateData,
} from '@/lib/schemas/serviceRecordSchemas';

const commonServices = [
  'Oil Change',
  'Tire Rotation',
  'Brake Service',
  'Battery Replacement',
  'Air Filter Replacement',
  'Cabin Air Filter Replacement',
  'Wiper Blade Replacement',
  'Fluid Check/Top-up',
  'Spark Plug Replacement',
  'Coolant Flush',
  'Transmission Service',
  'Wheel Alignment',
  'State Inspection',
  'Other',
];

// Using centralized validation schema for consistency

interface ServiceLogFormProps {
  currentOdometerReading?: number;
  onServiceRecordAdded: () => void; // Simplified callback
  vehicle: DomainVehicle; // Full vehicle object for better toast messages
}

export default function ServiceLogForm({
  currentOdometerReading = 0,
  onServiceRecordAdded,
  vehicle,
}: ServiceLogFormProps) {
  const form = useForm<ServiceRecordCreateData>({
    defaultValues: {
      cost: 0,
      date: new Date().toISOString().split('T')[0],
      notes: '',
      odometer: currentOdometerReading || 0,
      servicePerformed: [],
      vehicleId: vehicle.id,
    },
    resolver: zodResolver(ServiceRecordCreateSchema),
  });

  const {
    error: submissionError,
    isPending,
    mutateAsync: createServiceRecord,
  } = useCreateServiceRecord();

  const handleFormSubmit: SubmitHandler<
    ServiceRecordCreateData
  > = async data => {
    // Use the transformer to properly handle optional fields and type compatibility
    const formDataWithFormattedDate = {
      ...data,
      date: formatDateForApi(data.date), // Convert date to ISO datetime format for API
    };

    const payload = ServiceRecordSchemaUtils.transformToApiPayload(
      formDataWithFormattedDate,
      vehicle.id
    );

    try {
      await createServiceRecord(payload); // API call via hook

      onServiceRecordAdded(); // Signal success, parent component should refetch/update

      // Use standardized toast service with vehicle and service information
      const vehicleName = `${vehicle.make} ${vehicle.model}`;
      const serviceType =
        data.servicePerformed.length === 1
          ? data.servicePerformed[0]
          : `${data.servicePerformed.length} services`;

      serviceRecordToast.serviceRecordCreated(vehicleName, serviceType);

      form.reset({
        cost: 0, // Ensure consistency with defaultValues and schema
        date: new Date().toISOString().split('T')[0],
        notes: '',
        odometer: data.odometer, // Keep last odometer reading
        servicePerformed: [],
        vehicleId: vehicle.id,
      });
    } catch (error: any) {
      console.error('Error adding service record:', error);

      // Use standardized toast service for errors
      const errorMessage =
        error.message ||
        submissionError?.message ||
        'An error occurred. Please try again.';
      serviceRecordToast.serviceRecordCreationError(errorMessage);
    }
  };

  return (
    <Card className="bg-card shadow-md">
      <CardHeader className="p-5">
        <CardTitle className="text-xl font-semibold text-primary">
          Log New Service
        </CardTitle>
      </CardHeader>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleFormSubmit)}>
          <CardContent className="space-y-4 p-5">
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <FormField
                control={form.control}
                name="date"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Date of Service</FormLabel>
                    <FormControl>
                      <Input type="date" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="odometer"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Odometer (miles)</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="e.g., 25000"
                        type="number"
                        {...field}
                        onChange={e =>
                          field.onChange(Number(e.target.value) || 0)
                        }
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <FormField
              control={form.control}
              name="servicePerformed"
              render={() => (
                <FormItem>
                  <FormLabel>Services Performed</FormLabel>
                  <div className="grid grid-cols-2 gap-x-4 gap-y-2 sm:grid-cols-3">
                    {commonServices.map(service => (
                      <FormField
                        control={form.control}
                        key={service}
                        name="servicePerformed"
                        render={({ field }) => {
                          return (
                            <FormItem className="flex flex-row items-center space-x-2 space-y-0">
                              <FormControl>
                                <Checkbox
                                  checked={field.value?.includes(service)}
                                  onCheckedChange={checked => {
                                    const currentValue = field.value || [];
                                    if (checked) {
                                      field.onChange([
                                        ...currentValue,
                                        service,
                                      ]);
                                    } else {
                                      field.onChange(
                                        currentValue.filter(
                                          value => value !== service
                                        )
                                      );
                                    }
                                  }}
                                />
                              </FormControl>
                              <FormLabel className="text-sm font-normal">
                                {service}
                              </FormLabel>
                            </FormItem>
                          );
                        }}
                      />
                    ))}
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Notes (Optional)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="e.g., Used synthetic oil, checked tire pressure."
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="cost"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Cost (Optional)</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="e.g., 75.50"
                      step="0.01"
                      type="number"
                      {...field}
                      onChange={e =>
                        field.onChange(Number(e.target.value) || 0)
                      }
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
          <CardFooter className="p-5">
            <ActionButton
              actionType="primary"
              className="w-full"
              icon={<PlusCircle className="size-4" />}
              isLoading={isPending}
              loadingText="Saving..."
              type="submit"
            >
              Add Service Record
            </ActionButton>
          </CardFooter>
        </form>
      </Form>
    </Card>
  );
}
