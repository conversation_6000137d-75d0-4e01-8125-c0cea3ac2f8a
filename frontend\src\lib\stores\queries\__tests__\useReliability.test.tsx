/**
 * @file Unit tests for Reliability React Query hooks
 * @module stores/queries/__tests__/useReliability
 */

import { renderHook, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import type { ReactNode } from 'react';

import { reliabilityApiService } from '@/lib/api/services/apiServiceFactory';
import type {
  Alert,
  AlertHistory,
  AlertStatistics,
  CircuitBreakerStatus,
  DeduplicationMetrics,
  DependencyHealth,
  DetailedHealthCheck,
  HealthCheck,
  SystemMetrics,
  TestAlertsResult,
} from '@/lib/types/domain';

import {
  useSystemHealth,
  useDetailedSystemHealth,
  useDependencyHealth,
  useCircuitBreakerStatus,
  usePerformanceMetrics,
  useDeduplicationMetrics,
  useAlerts,
  useAlertHistory,
  useAlertStatistics,
  useReliabilityDashboard,
  useSystemHealthStatus,
  useCriticalAlertCount,
  useResolveAlert,
  useAcknowledgeAlert,
  useTestAlerts,
} from '../useReliability';

// Mock the reliability API service
jest.mock('@/lib/api/services/apiServiceFactory', () => ({
  reliabilityApiService: {
    getSystemHealth: jest.fn(),
    getDetailedHealth: jest.fn(),
    getDependencyHealth: jest.fn(),
    getCircuitBreakerStatus: jest.fn(),
    getMetrics: jest.fn(),
    getDeduplicationMetrics: jest.fn(),
    getActiveAlerts: jest.fn(),
    getAlertHistory: jest.fn(),
    getAlertStatistics: jest.fn(),
    getReliabilityDashboardData: jest.fn(),
    isSystemHealthy: jest.fn(),
    getCriticalAlertCount: jest.fn(),
    resolveAlert: jest.fn(),
    acknowledgeAlert: jest.fn(),
    testAlerts: jest.fn(),
  },
}));

// Mock the notifications hook
jest.mock('@/hooks/ui/useNotifications', () => ({
  useNotifications: () => ({
    showError: jest.fn(),
    showSuccess: jest.fn(),
    showInfo: jest.fn(),
  }),
}));

const mockReliabilityApiService = reliabilityApiService as jest.Mocked<typeof reliabilityApiService>;

// Test wrapper with QueryClient
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        gcTime: 0,
      },
      mutations: {
        retry: false,
      },
    },
  });

  return ({ children }: { children: ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );
};

describe('useReliability hooks', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Query Hooks', () => {
    describe('useSystemHealth', () => {
      it('should fetch system health data successfully', async () => {
        const mockHealthData: HealthCheck = {
          status: 'healthy',
          timestamp: '2024-01-15T10:30:00.000Z',
          uptime: 3600,
          version: '1.0.0',
          environment: 'test',
        };

        mockReliabilityApiService.getSystemHealth.mockResolvedValue(mockHealthData);

        const { result } = renderHook(() => useSystemHealth(), {
          wrapper: createWrapper(),
        });

        await waitFor(() => {
          expect(result.current.isSuccess).toBe(true);
        });

        expect(result.current.data).toEqual(mockHealthData);
        expect(mockReliabilityApiService.getSystemHealth).toHaveBeenCalledTimes(1);
      });

      it('should handle errors gracefully', async () => {
        const mockError = new Error('Health check failed');
        mockReliabilityApiService.getSystemHealth.mockRejectedValue(mockError);

        const { result } = renderHook(() => useSystemHealth(), {
          wrapper: createWrapper(),
        });

        await waitFor(() => {
          expect(result.current.isError).toBe(true);
        });

        expect(result.current.error).toEqual(mockError);
      });
    });

    describe('useCircuitBreakerStatus', () => {
      it('should fetch circuit breaker status successfully', async () => {
        const mockCircuitBreakerData: CircuitBreakerStatus = {
          circuitBreakers: [
            {
              name: 'database-circuit-breaker',
              state: 'CLOSED',
              failureCount: 0,
              successCount: 100,
              timeout: 5000,
            },
          ],
          summary: {
            total: 1,
            closed: 1,
            open: 0,
            halfOpen: 0,
          },
        };

        mockReliabilityApiService.getCircuitBreakerStatus.mockResolvedValue(mockCircuitBreakerData);

        const { result } = renderHook(() => useCircuitBreakerStatus(), {
          wrapper: createWrapper(),
        });

        await waitFor(() => {
          expect(result.current.isSuccess).toBe(true);
        });

        expect(result.current.data).toEqual(mockCircuitBreakerData);
        expect(mockReliabilityApiService.getCircuitBreakerStatus).toHaveBeenCalledTimes(1);
      });
    });

    describe('useAlerts', () => {
      it('should fetch active alerts successfully', async () => {
        const mockAlerts: Alert[] = [
          {
            id: 'alert-1',
            type: 'system-health',
            severity: 'high',
            message: 'Database connection degraded',
            timestamp: '2024-01-15T10:30:00.000Z',
            status: 'active',
            source: 'health-monitor',
          },
        ];

        mockReliabilityApiService.getActiveAlerts.mockResolvedValue(mockAlerts);

        const { result } = renderHook(() => useAlerts(), {
          wrapper: createWrapper(),
        });

        await waitFor(() => {
          expect(result.current.isSuccess).toBe(true);
        });

        expect(result.current.data).toEqual(mockAlerts);
        expect(mockReliabilityApiService.getActiveAlerts).toHaveBeenCalledTimes(1);
      });
    });

    describe('useAlertHistory', () => {
      it('should fetch alert history with pagination', async () => {
        const mockAlertHistory: AlertHistory = {
          alerts: [
            {
              id: 'alert-1',
              type: 'system-health',
              severity: 'medium',
              message: 'Memory usage high',
              timestamp: '2024-01-15T09:30:00.000Z',
              status: 'resolved',
              source: 'system-monitor',
              resolvedAt: '2024-01-15T09:35:00.000Z',
            },
          ],
          pagination: {
            page: 1,
            limit: 50,
            total: 1,
            totalPages: 1,
            hasNext: false,
            hasPrevious: false,
          },
        };

        mockReliabilityApiService.getAlertHistory.mockResolvedValue(mockAlertHistory);

        const { result } = renderHook(() => useAlertHistory(1, 50), {
          wrapper: createWrapper(),
        });

        await waitFor(() => {
          expect(result.current.isSuccess).toBe(true);
        });

        expect(result.current.data).toEqual(mockAlertHistory);
        expect(mockReliabilityApiService.getAlertHistory).toHaveBeenCalledWith(1, 50);
      });
    });
  });

  describe('Mutation Hooks', () => {
    describe('useResolveAlert', () => {
      it('should resolve alert successfully', async () => {
        const mockResolvedAlert: Alert = {
          id: 'alert-1',
          type: 'system-health',
          severity: 'high',
          message: 'Database connection degraded',
          timestamp: '2024-01-15T10:30:00.000Z',
          status: 'resolved',
          source: 'health-monitor',
          resolvedAt: '2024-01-15T10:35:00.000Z',
          resolvedBy: 'admin',
        };

        mockReliabilityApiService.resolveAlert.mockResolvedValue(mockResolvedAlert);

        const { result } = renderHook(() => useResolveAlert(), {
          wrapper: createWrapper(),
        });

        await waitFor(() => {
          expect(result.current.mutate).toBeDefined();
        });

        result.current.mutate({
          alertId: 'alert-1',
          reason: 'Database connection restored',
          resolvedBy: 'admin',
        });

        await waitFor(() => {
          expect(result.current.isSuccess).toBe(true);
        });

        expect(mockReliabilityApiService.resolveAlert).toHaveBeenCalledWith(
          'alert-1',
          'Database connection restored',
          'admin'
        );
      });
    });

    describe('useTestAlerts', () => {
      it('should test alert system successfully', async () => {
        const mockTestResult: TestAlertsResult = {
          success: true,
          message: 'Alert system test completed successfully',
          testAlertId: 'test-alert-123',
        };

        mockReliabilityApiService.testAlerts.mockResolvedValue(mockTestResult);

        const { result } = renderHook(() => useTestAlerts(), {
          wrapper: createWrapper(),
        });

        await waitFor(() => {
          expect(result.current.mutate).toBeDefined();
        });

        result.current.mutate();

        await waitFor(() => {
          expect(result.current.isSuccess).toBe(true);
        });

        expect(result.current.data).toEqual(mockTestResult);
        expect(mockReliabilityApiService.testAlerts).toHaveBeenCalledTimes(1);
      });
    });
  });

  describe('Convenience Hooks', () => {
    describe('useSystemHealthStatus', () => {
      it('should return boolean health status', async () => {
        mockReliabilityApiService.isSystemHealthy.mockResolvedValue(true);

        const { result } = renderHook(() => useSystemHealthStatus(), {
          wrapper: createWrapper(),
        });

        await waitFor(() => {
          expect(result.current.isSuccess).toBe(true);
        });

        expect(result.current.data).toBe(true);
        expect(mockReliabilityApiService.isSystemHealthy).toHaveBeenCalledTimes(1);
      });
    });

    describe('useCriticalAlertCount', () => {
      it('should return critical alert count', async () => {
        mockReliabilityApiService.getCriticalAlertCount.mockResolvedValue(3);

        const { result } = renderHook(() => useCriticalAlertCount(), {
          wrapper: createWrapper(),
        });

        await waitFor(() => {
          expect(result.current.isSuccess).toBe(true);
        });

        expect(result.current.data).toBe(3);
        expect(mockReliabilityApiService.getCriticalAlertCount).toHaveBeenCalledTimes(1);
      });
    });
  });
});
