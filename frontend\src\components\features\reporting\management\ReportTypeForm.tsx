/**
 * @file ReportTypeForm.tsx
 * @description Report type form component following established form patterns and SOLID principles
 */

import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  FileText, 
  X,
  Plus,
  Trash2
} from 'lucide-react';
import type { ReportType } from '../data/types/reporting';

/**
 * Form validation schema
 */
const reportTypeSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name must be less than 100 characters'),
  description: z.string().optional(),
  category: z.string().min(1, 'Category is required'),
  dataSource: z.string().min(1, 'Data source is required'),
  widgets: z.array(z.string()).min(1, 'At least one widget is required'),
  filters: z.array(z.string()).optional(),
  isActive: z.boolean().default(true),
  isPublic: z.boolean().default(false),
  refreshInterval: z.number().min(1).max(1440).optional(), // 1 minute to 24 hours
  tags: z.array(z.string()).optional(),
});

type ReportTypeFormData = z.infer<typeof reportTypeSchema>;

/**
 * Props interface for ReportTypeForm
 */
interface ReportTypeFormProps {
  reportType?: ReportType | null;
  onSubmit: (data: ReportTypeFormData) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
}

/**
 * Available widget types
 */
const WIDGET_TYPES = [
  { value: 'analytics', label: 'Analytics Widget' },
  { value: 'chart', label: 'Chart Widget' },
  { value: 'table', label: 'Data Table' },
  { value: 'metrics', label: 'Metrics Widget' },
  { value: 'correlation', label: 'Correlation Widget' },
];

/**
 * Available data sources
 */
const DATA_SOURCES = [
  { value: 'delegations', label: 'Delegations' },
  { value: 'tasks', label: 'Tasks' },
  { value: 'vehicles', label: 'Vehicles' },
  { value: 'employees', label: 'Employees' },
  { value: 'cross-entity', label: 'Cross-Entity' },
];

/**
 * Available categories
 */
const CATEGORIES = [
  { value: 'operational', label: 'Operational' },
  { value: 'performance', label: 'Performance' },
  { value: 'financial', label: 'Financial' },
  { value: 'compliance', label: 'Compliance' },
  { value: 'analytics', label: 'Analytics' },
];

/**
 * ReportTypeForm Component
 * 
 * Form for creating and editing report types following established form patterns.
 * 
 * Responsibilities:
 * - Handle report type creation and editing
 * - Validate form data using Zod schema
 * - Follow established form component patterns
 * - Maintain consistent styling and behavior
 * 
 * SOLID Principles Applied:
 * - SRP: Single responsibility of handling report type form
 * - OCP: Open for extension via props configuration
 * - DIP: Depends on established form framework abstractions
 */
export const ReportTypeForm: React.FC<ReportTypeFormProps> = ({
  reportType,
  onSubmit,
  onCancel,
  isLoading = false
}) => {
  // Initialize form with existing data or defaults
  const form = useForm<ReportTypeFormData>({
    resolver: zodResolver(reportTypeSchema),
    defaultValues: {
      name: reportType?.name || '',
      description: reportType?.description || '',
      category: reportType?.category || '',
      dataSource: reportType?.dataSource || '',
      widgets: reportType?.widgets || [],
      filters: reportType?.filters || [],
      isActive: reportType?.isActive ?? true,
      isPublic: reportType?.isPublic ?? false,
      refreshInterval: reportType?.refreshInterval || 60,
      tags: reportType?.tags || [],
    },
  });

  // Handle form submission
  const handleSubmit = async (data: ReportTypeFormData) => {
    try {
      await onSubmit(data);
    } catch (error) {
      console.error('Form submission error:', error);
    }
  };

  // Handle tag management
  const addTag = (tag: string) => {
    const currentTags = form.getValues('tags') || [];
    if (tag && !currentTags.includes(tag)) {
      form.setValue('tags', [...currentTags, tag]);
    }
  };

  const removeTag = (tagToRemove: string) => {
    const currentTags = form.getValues('tags') || [];
    form.setValue('tags', currentTags.filter(tag => tag !== tagToRemove));
  };

  return (
    <Dialog open={true} onOpenChange={onCancel}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            {reportType ? 'Edit Report Type' : 'Create Report Type'}
          </DialogTitle>
          <DialogDescription>
            {reportType 
              ? 'Update the report type configuration and settings.'
              : 'Create a new report type with custom widgets and data sources.'
            }
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            {/* Basic Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Basic Information</h3>
              
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Name *</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter report type name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="Describe what this report type shows"
                        rows={3}
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="category"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Category *</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select category" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {CATEGORIES.map((category) => (
                            <SelectItem key={category.value} value={category.value}>
                              {category.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="dataSource"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Data Source *</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select data source" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {DATA_SOURCES.map((source) => (
                            <SelectItem key={source.value} value={source.value}>
                              {source.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Widget Configuration */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Widget Configuration</h3>
              
              <FormField
                control={form.control}
                name="widgets"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Widgets *</FormLabel>
                    <FormDescription>
                      Select the widgets to include in this report type
                    </FormDescription>
                    <div className="grid grid-cols-2 gap-2">
                      {WIDGET_TYPES.map((widget) => (
                        <div key={widget.value} className="flex items-center space-x-2">
                          <Checkbox
                            id={widget.value}
                            checked={field.value?.includes(widget.value)}
                            onCheckedChange={(checked) => {
                              const currentWidgets = field.value || [];
                              if (checked) {
                                field.onChange([...currentWidgets, widget.value]);
                              } else {
                                field.onChange(currentWidgets.filter(w => w !== widget.value));
                              }
                            }}
                          />
                          <label htmlFor={widget.value} className="text-sm">
                            {widget.label}
                          </label>
                        </div>
                      ))}
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Settings */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Settings</h3>
              
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="isActive"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                      <div className="space-y-0.5">
                        <FormLabel>Active</FormLabel>
                        <FormDescription>
                          Enable this report type for use
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="isPublic"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                      <div className="space-y-0.5">
                        <FormLabel>Public</FormLabel>
                        <FormDescription>
                          Make available to all users
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="refreshInterval"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Refresh Interval (minutes)</FormLabel>
                    <FormControl>
                      <Input 
                        type="number" 
                        min="1" 
                        max="1440"
                        placeholder="60"
                        {...field}
                        onChange={(e) => field.onChange(parseInt(e.target.value) || 60)}
                      />
                    </FormControl>
                    <FormDescription>
                      How often the report data should refresh (1-1440 minutes)
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={onCancel}>
                Cancel
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? 'Saving...' : reportType ? 'Update' : 'Create'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default ReportTypeForm;
