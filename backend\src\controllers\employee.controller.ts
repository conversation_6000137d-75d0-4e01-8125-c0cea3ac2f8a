import type { NextFunction, Request, Response } from 'express';

import type { Prisma } from '../generated/prisma/index.js';
import type { EmployeeCreate, EmployeeUpdate } from '../schemas/employee.schema.js';
// Import event payload types
import type {
  EmployeeCreatedPayload,
  EmployeeDeletedPayload,
  EmployeeUpdatedPayload,
} from '../types/websocketEvents.js'; // Added

import { EmployeeStatus as PrismaEmployeeStatus } from '../generated/prisma/index.js';
import * as employeeModel from '../models/employee.model.js';
// Removed: import { emitEmployeeChange, SOCKET_EVENTS } from '../services/socketService.js';
import { getUnifiedWebSocketService } from '../services/UnifiedWebSocketService.js'; // Added
import { CRUD_EVENTS } from '../services/WebSocketEventManager.js'; // Added
import logger from '../utils/logger.js';
import HttpError from '../utils/HttpError.js';

const CRUD_ROOM = 'entity-updates'; // General room for CRUD updates

// Helper to split full name into first and last
const splitFullName = (
  fullName: string | null | undefined,
): { firstName: string; lastName: string } => {
  if (!fullName) return { firstName: '', lastName: '' };
  const parts = fullName.split(' ');
  const firstName = parts[0] || '';
  const lastName = parts.slice(1).join(' ') || '';
  return { firstName, lastName };
};

export const createEmployee = async (
  req: Request,
  res: Response,
  next: NextFunction,
): Promise<void> => {
  try {
    const validatedBody = req.body as EmployeeCreate;

    const employeeData: Prisma.EmployeeCreateInput = {
      availability: validatedBody.availability,
      contactEmail: validatedBody.contactEmail,
      contactInfo: validatedBody.contactInfo,
      contactMobile: validatedBody.contactMobile,
      contactPhone: validatedBody.contactPhone,
      currentLocation: validatedBody.currentLocation,
      department: validatedBody.department,
      employeeId: validatedBody.employeeId,
      fullName: validatedBody.fullName || validatedBody.name, // Ensure fullName is populated
      generalAssignments: validatedBody.generalAssignments,
      hireDate: validatedBody.hireDate ? new Date(validatedBody.hireDate) : null,
      name: validatedBody.name,
      notes: validatedBody.notes,
      position: validatedBody.position,
      profileImageUrl: validatedBody.profileImageUrl,
      role: validatedBody.role,
      shiftSchedule: validatedBody.shiftSchedule,
      skills: validatedBody.skills,
      status: validatedBody.status
        ? (validatedBody.status as PrismaEmployeeStatus)
        : PrismaEmployeeStatus.Active,
      updatedAt: new Date(),
      workingHours: validatedBody.workingHours,
    };

    const newEmployee = await employeeModel.createEmployee(employeeData);
    if (newEmployee) {
      try {
        const unifiedSocketService = getUnifiedWebSocketService();
        const { firstName, lastName } = splitFullName(newEmployee.fullName);
        const payload: EmployeeCreatedPayload = {
          ...newEmployee,
          id: newEmployee.id.toString(), // Ensure ID is string if expected by payload
          firstName,
          lastName,
          email: newEmployee.contactEmail || '', // Assuming contactEmail maps to email
          // Map other fields from newEmployee to EmployeeCreatedPayload if necessary
        };
        unifiedSocketService.emitToRoom(CRUD_ROOM, CRUD_EVENTS.EMPLOYEE_CREATED, payload);
      } catch (wsError) {
        logger.error('Failed to emit WebSocket event for employee creation', { wsError });
      }
      res.status(201).json(newEmployee);
    } else {
      return next(
        new HttpError(
          'Could not create employee, input may be invalid or another error occurred.',
          400,
          'EMPLOYEE_CREATION_FAILED',
        ),
      );
    }
  } catch (error: any) {
    if (error.message.includes('already exists')) {
      return next(new HttpError(error.message, 409, 'EMPLOYEE_ALREADY_EXISTS'));
    } else if (error.message.includes('not found')) {
      return next(new HttpError(error.message, 409, 'EMPLOYEE_DEPENDENCY_NOT_FOUND'));
    } else {
      return next(
        new HttpError('Error creating employee', 500, 'EMPLOYEE_CREATION_ERROR', {
          originalError: error.message,
        }),
      );
    }
  }
};

export const getAllEmployees = async (
  req: Request,
  res: Response,
  next: NextFunction,
): Promise<void> => {
  try {
    const { role } = req.query;
    if (role && typeof role === 'string') {
      const validRoles = [
        'driver',
        'mechanic',
        'administrator',
        'office_staff',
        'manager',
        'service_advisor',
        'technician',
        'other',
      ];
      if (!validRoles.includes(role)) {
        return next(
          new HttpError('Invalid role parameter', 400, 'INVALID_ROLE_PARAMETER', { validRoles }),
        );
      }
      const employees = await employeeModel.getEmployeesByRole(role as any);
      res.status(200).json(employees);
    } else {
      const employees = await employeeModel.getAllEmployees();
      res.status(200).json(employees);
    }
  } catch (error: any) {
    return next(
      new HttpError('Error fetching employees', 500, 'EMPLOYEE_FETCH_ERROR', {
        originalError: error.message,
      }),
    );
  }
};

export const getEmployeeById = async (
  req: Request,
  res: Response,
  next: NextFunction,
): Promise<void> => {
  try {
    const id = parseInt(req.params.id, 10);
    if (isNaN(id)) {
      return next(new HttpError('Invalid employee ID format', 400, 'INVALID_EMPLOYEE_ID'));
    }
    const employee = await employeeModel.getEmployeeById(id);
    if (employee) {
      res.status(200).json(employee);
    } else {
      return next(new HttpError('Employee not found', 404, 'EMPLOYEE_NOT_FOUND'));
    }
  } catch (error: any) {
    return next(
      new HttpError('Error fetching employee', 500, 'EMPLOYEE_FETCH_ERROR', {
        originalError: error.message,
      }),
    );
  }
};

export const updateEmployee = async (
  req: Request,
  res: Response,
  next: NextFunction,
): Promise<void> => {
  try {
    const id = parseInt(req.params.id, 10);
    if (isNaN(id)) {
      return next(new HttpError('Invalid employee ID format', 400, 'INVALID_EMPLOYEE_ID'));
    }

    const validatedBody = req.body as EmployeeUpdate;
    const { statusChangeReason, ...updatePayloadFromSchema } = validatedBody;

    const employeeUpdateData: Prisma.EmployeeUpdateInput = {
      ...updatePayloadFromSchema,
      fullName: validatedBody.fullName || validatedBody.name, // Ensure fullName is populated if changed
    };

    if (updatePayloadFromSchema.hireDate) {
      employeeUpdateData.hireDate = new Date(updatePayloadFromSchema.hireDate);
    }
    if (updatePayloadFromSchema.status) {
      employeeUpdateData.status = updatePayloadFromSchema.status as PrismaEmployeeStatus;
    }

    const updatedEmployee = await employeeModel.updateEmployee(
      id,
      employeeUpdateData,
      statusChangeReason,
    );

    if (updatedEmployee) {
      try {
        const unifiedSocketService = getUnifiedWebSocketService();
        const { firstName, lastName } = splitFullName(updatedEmployee.fullName);
        const payload: EmployeeUpdatedPayload = {
          ...updatedEmployee,
          id: updatedEmployee.id.toString(), // Ensure ID is string if expected by payload
          firstName,
          lastName,
          email: updatedEmployee.contactEmail || '', // Assuming contactEmail maps to email
          // Map other fields from updatedEmployee to EmployeeUpdatedPayload if necessary
        };
        unifiedSocketService.emitToRoom(CRUD_ROOM, CRUD_EVENTS.EMPLOYEE_UPDATED, payload);
      } catch (wsError) {
        logger.error('Failed to emit WebSocket event for employee update', { wsError });
      }
      res.status(200).json(updatedEmployee);
    } else {
      const exists = await employeeModel.getEmployeeById(id);
      if (!exists) {
        return next(new HttpError('Employee not found to update', 404, 'EMPLOYEE_NOT_FOUND'));
      } else {
        return next(
          new HttpError(
            'Could not update employee, input may be invalid or another error occurred.',
            400,
            'EMPLOYEE_UPDATE_FAILED',
          ),
        );
      }
    }
  } catch (error: any) {
    if (error.message.includes('already exists')) {
      return next(new HttpError(error.message, 409, 'EMPLOYEE_ALREADY_EXISTS'));
    } else if (error.message.includes('not found')) {
      return next(new HttpError(error.message, 404, 'EMPLOYEE_NOT_FOUND'));
    } else {
      return next(
        new HttpError('Error updating employee', 500, 'EMPLOYEE_UPDATE_ERROR', {
          originalError: error.message,
        }),
      );
    }
  }
};

export const deleteEmployee = async (
  req: Request,
  res: Response,
  next: NextFunction,
): Promise<void> => {
  try {
    const id = parseInt(req.params.id, 10);
    if (isNaN(id)) {
      return next(new HttpError('Invalid employee ID format', 400, 'INVALID_EMPLOYEE_ID'));
    }
    const deletedEmployee = await employeeModel.deleteEmployee(id);
    if (deletedEmployee) {
      try {
        const unifiedSocketService = getUnifiedWebSocketService();
        const payload: EmployeeDeletedPayload = { id: deletedEmployee.id.toString() }; // Ensure ID is string
        unifiedSocketService.emitToRoom(CRUD_ROOM, CRUD_EVENTS.EMPLOYEE_DELETED, payload);
      } catch (wsError) {
        logger.error('Failed to emit WebSocket event for employee deletion', { wsError });
      }
      res.status(200).json({
        employee: deletedEmployee,
        message: 'Employee deleted successfully',
      });
    } else {
      return next(
        new HttpError('Employee not found or could not be deleted', 404, 'EMPLOYEE_NOT_FOUND'),
      );
    }
  } catch (error: any) {
    return next(
      new HttpError('Error deleting employee', 500, 'EMPLOYEE_DELETE_ERROR', {
        originalError: error.message,
      }),
    );
  }
};

/**
 * Get all employees enriched with vehicle information
 * This is used for the /api/employees/enriched endpoint
 */
export const getEnrichedEmployees = async (
  req: Request,
  res: Response,
  next: NextFunction,
): Promise<void> => {
  try {
    logger.info('Fetching enriched employees', {
      ip: req.ip,
      query: req.query,
      userAgent: req.headers['user-agent'],
    });

    const employees = await employeeModel.getAllEmployees();

    if (!employees || !Array.isArray(employees) || employees.length === 0) {
      logger.info('No employees found for enrichment');
      res.status(200).json([]);
      return;
    }

    const enrichedEmployees = employees.map(employee => ({
      ...employee,
      assignedVehicleDetails: null,
    }));

    logger.debug('Enriched employees successfully', {
      count: enrichedEmployees.length,
      query: req.query,
    });

    res.status(200).json(enrichedEmployees);
  } catch (error: any) {
    logger.error('Error fetching enriched employees', {
      error: error.message,
      query: req.query,
      stack: error.stack,
    });
    return next(
      new HttpError('Error fetching enriched employees', 500, 'ENRICHED_EMPLOYEES_FETCH_ERROR', {
        originalError: error.message,
      }),
    );
  }
};
