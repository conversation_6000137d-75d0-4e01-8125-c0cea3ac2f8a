import { jest } from '@jest/globals';
import request from 'supertest';
import { initializeAndGetApp } from '../../app.js'; // The Express app
import prisma from '../../utils/prisma.js'; // Prisma client
import type { Employee, Vehicle } from '../../generated/prisma/index.js';
import { afterAll, beforeAll, beforeEach, describe, expect, it } from '@jest/globals';

let app;

beforeAll(async () => {
  app = await initializeAndGetApp();
});

// Ensure Prisma client disconnects after all tests are done
afterAll(async () => {
  await prisma.$disconnect();
  await prisma.serviceRecord.deleteMany({});
  await prisma.task.deleteMany({});
  await prisma.employeeStatusEntry.deleteMany({});
  await prisma.employee.deleteMany({});
  await prisma.vehicle.deleteMany({}); // Clean vehicles if drivers are assigned to them
});

// Clean up relevant tables before each test run in this suite
beforeEach(async () => {
  // Delete records in a way that respects foreign key constraints or model delete logic
  // Assuming Task and ServiceRecord might be related to Employee and need to be cleared if not cascaded
  // Employee model's delete logic handles status entries and task disassociation.
  await prisma.serviceRecord.deleteMany({});
  await prisma.task.deleteMany({});
  await prisma.employeeStatusEntry.deleteMany({});
  await prisma.employee.deleteMany({});
  await prisma.vehicle.deleteMany({}); // Clean vehicles if drivers are assigned to them
});

describe('Employee API Routes', () => {
  let createdEmployeeId: number;
  let createdVehicleId: number;

  const newEmployeeData = {
    name: 'Test Employee',
    role: 'mechanic',
    employeeId: `EMP-${Date.now()}`, // Business key, ensure unique
    contactInfo: '<EMAIL>',
    position: 'Senior Mechanic',
    department: 'Maintenance',
    hireDate: new Date().toISOString(),
    status: 'Active',
    skills: ['Engine Repair', 'Diagnostics'],
  };

  const newDriverData = {
    name: 'Test Driver',
    role: 'driver',
    employeeId: `DRV-${Date.now()}`,
    contactInfo: '<EMAIL>',
    position: 'Delivery Driver',
    department: 'Logistics',
    hireDate: new Date().toISOString(),
    status: 'Active',
    availability: 'On Shift',
    currentLocation: 'Main Garage',
    workingHours: 'Mon-Fri 9am-4pm',
    assignedVehicleId: null as number | null, // Will be set after vehicle creation
  };

  it('should create a new employee (mechanic) via POST /api/employees', async () => {
    const response = await request(app).post('/api/employees').send(newEmployeeData);

    expect(response.statusCode).toBe(201);
    expect(response.body.status).toBe('success');
    expect(response.body.data).toHaveProperty('id');
    expect(response.body.data.name).toBe(newEmployeeData.name);
    expect(response.body.data.employeeId).toBe(newEmployeeData.employeeId);
    expect(response.body.data.role).toBe('mechanic');
    createdEmployeeId = response.body.data.id; // Save for later tests
  });

  it('should create a new employee (driver) and assign a vehicle via POST /api/employees', async () => {
    // First, create a vehicle to assign
    const vehicleRes = await request(app)
      .post('/api/vehicles')
      .send({
        make: 'TestTruck',
        model: 'Runner',
        year: 2022,
        vin: `TRUCKDRIVERVIN${Date.now()}`,
        licensePlate: 'DRVTRK',
        ownerName: 'Company',
        ownerContact: '<EMAIL>',
      });
    expect(vehicleRes.statusCode).toBe(201);
    createdVehicleId = vehicleRes.body.data.id;

    const driverDataWithVehicle = {
      ...newDriverData,
      assignedVehicleId: createdVehicleId,
    };
    const response = await request(app).post('/api/employees').send(driverDataWithVehicle);

    expect(response.statusCode).toBe(201);
    expect(response.body.status).toBe('success');
    expect(response.body.data).toHaveProperty('id');
    expect(response.body.data.name).toBe(driverDataWithVehicle.name);
    expect(response.body.data.employeeId).toBe(driverDataWithVehicle.employeeId);
    expect(response.body.data.role).toBe('driver');
    expect(response.body.data.assignedVehicleId).toBe(createdVehicleId);
    expect(response.body.data.assignedVehicle).toBeDefined();
    expect(response.body.data.assignedVehicle.make).toBe('TestTruck');
  });

  it('should not create an employee with a duplicate Employee ID (business key)', async () => {
    await request(app).post('/api/employees').send(newEmployeeData);
    const response = await request(app).post('/api/employees').send(newEmployeeData);

    expect(response.statusCode).toBe(409);
    expect(response.body.status).toBe('error');
    expect(response.body.code).toBe('CONFLICT');
    expect(response.body.message).toContain('already exists');
    expect(response.body.error).toBeDefined();
  });

  it('should get all employees via GET /api/employees', async () => {
    await request(app).post('/api/employees').send(newEmployeeData);
    const response = await request(app).get('/api/employees');
    expect(response.statusCode).toBe(200);
    expect(response.body.status).toBe('success');
    expect(Array.isArray(response.body.data)).toBe(true);
    expect(response.body.data.length).toBeGreaterThanOrEqual(1);
    expect(
      response.body.data.some((e: Employee) => e.employeeId === newEmployeeData.employeeId),
    ).toBe(true);
  });

  it('should get a specific employee by ID via GET /api/employees/:id', async () => {
    const creationResponse = await request(app).post('/api/employees').send(newEmployeeData);
    const employeeIdToFetch = creationResponse.body.data.id;
    const response = await request(app).get(`/api/employees/${employeeIdToFetch}`);
    expect(response.statusCode).toBe(200);
    expect(response.body.status).toBe('success');
    expect(response.body.data).toHaveProperty('id', employeeIdToFetch);
    expect(response.body.data.employeeId).toBe(newEmployeeData.employeeId);
  });

  it('should return 404 for a non-existent employee ID', async () => {
    const response = await request(app).get('/api/employees/999999');
    expect(response.statusCode).toBe(404);
    expect(response.body.status).toBe('error');
    expect(response.body.code).toBe('NOT_FOUND');
    expect(response.body.message).toContain('not found');
  });

  it('should update an employee via PUT /api/employees/:id', async () => {
    const creationResponse = await request(app).post('/api/employees').send(newEmployeeData);
    const employeeIdToUpdate = creationResponse.body.data.id;
    const updates = {
      name: 'Updated Test Employee',
      position: 'Lead Mechanic',
      status: 'On Leave',
      statusChangeReason: 'Vacation',
    };
    const response = await request(app).put(`/api/employees/${employeeIdToUpdate}`).send(updates);

    expect(response.statusCode).toBe(200);
    expect(response.body.status).toBe('success');
    expect(response.body.data.name).toBe(updates.name);
    expect(response.body.data.position).toBe(updates.position);
    expect(response.body.data.status).toBe(updates.status);
    expect(response.body.data.statusHistory.length).toBeGreaterThanOrEqual(2);
    expect(response.body.data.statusHistory[0].status).toBe(updates.status);
    expect(response.body.data.statusHistory[0].reason).toBe(updates.statusChangeReason);
  });

  it('should not update an employee to an existing Employee ID', async () => {
    const firstEmp = await request(app).post('/api/employees').send(newEmployeeData);
    const anotherEmpData = {
      ...newEmployeeData,
      name: 'Another Employee',
      employeeId: `EMP-${Date.now() + 1000}`,
    };
    const anotherEmpRes = await request(app).post('/api/employees').send(anotherEmpData);
    expect(anotherEmpRes.statusCode).toBe(201);

    const updates = { employeeId: firstEmp.body.data.employeeId };

    const response = await request(app)
      .put(`/api/employees/${anotherEmpRes.body.data.id}`)
      .send(updates);

    expect(response.statusCode).toBe(409);
    expect(response.body.status).toBe('error');
    expect(response.body.code).toBe('CONFLICT');
    expect(response.body.message).toContain('already exists');
    expect(response.body.error).toBeDefined();
  });

  it('should delete an employee via DELETE /api/employees/:id', async () => {
    const creationResponse = await request(app).post('/api/employees').send(newEmployeeData);
    const employeeIdToDelete = creationResponse.body.data.id;
    const response = await request(app).delete(`/api/employees/${employeeIdToDelete}`);
    expect(response.statusCode).toBe(200);
    expect(response.body.status).toBe('success');
    expect(response.body.data.message).toBe('Employee deleted successfully');

    // Verify it's actually deleted
    const getResponse = await request(app).get(`/api/employees/${employeeIdToDelete}`);
    expect(getResponse.statusCode).toBe(404);
  });
});
