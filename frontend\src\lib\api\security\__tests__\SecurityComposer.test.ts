/**
 * @file SecurityComposer Tests
 * @module api/security/__tests__/SecurityComposer.test
 *
 * Phase 5: Testing & Validation
 * Comprehensive tests for SecurityComposer
 */

import type { SecurityConfig, SecurityContext } from '../../core/interfaces';
import type { SecurityFeatures } from '../composer';

import { SECURITY_CONSTANTS } from '../../../security';
import { createSecurityComposer, SecurityComposer } from '../composer';

// Mock security features with complete interfaces
const mockSecurityFeatures: SecurityFeatures = {
  csrfProtection: {
    // CSRFProtectionActions
    attachCSRF: jest.fn(config => ({
      ...config,
      headers: { ...config.headers, 'X-CSRF-Token': 'mock-csrf-token' },
    })),
    clearCSRFToken: jest.fn(),
    // CSRFProtectionState
    csrfToken: 'mock-csrf-token',
    isInitialized: true,
    isProtectionRequired: jest.fn().mockReturnValue(true),
    isTokenValid: true,
    refreshCSRFToken: jest.fn().mockReturnValue({
      expiresAt: new Date(Date.now() + 3_600_000),
      isValid: true,
      token: 'new-mock-token',
    }),
    tokenExpiresAt: new Date(Date.now() + 3_600_000),
    validateCSRF: jest.fn().mockReturnValue({ isValid: true }),
  },
  inputValidation: {
    clearAllErrors: jest.fn(),
    clearFieldErrors: jest.fn(),
    errors: {},
    // UseInputValidationReturn specific methods
    getFieldError: jest.fn().mockReturnValue(null),
    hasFieldError: jest.fn().mockReturnValue(false),
    isFieldTouched: jest.fn().mockReturnValue(false),
    // ValidationState
    isValid: true,
    isValidating: false,
    resetValidation: jest.fn(),
    sanitizeInput: jest.fn(input => input),
    setFieldTouched: jest.fn(),
    touched: {},
    // ValidationActions
    validateField: jest.fn().mockReturnValue({ errors: [], isValid: true }),
    validateForm: jest.fn().mockReturnValue({ errors: [], isValid: true }),
  },
  sessionSecurity: {
    // SessionSecurityActions
    clearSession: jest.fn(),
    // SessionSecurityState
    concurrentSessions: [],
    handleCrossTabLogout: jest.fn(),
    isSessionActive: true,
    isSessionExpired: false,
    lastActivity: new Date(),
    refreshSession: jest.fn(),
    sessionId: 'mock-session-id',
    sessionWarning: false,
    updateActivity: jest.fn(),
  },
  tokenManagement: {
    // TokenManagementActions
    checkTokenExpiry: jest.fn().mockReturnValue(false),
    clearToken: jest.fn(),
    getTokenExpiration: jest
      .fn()
      .mockReturnValue(new Date(Date.now() + 3_600_000)),
    isTokenExpired: false,
    // TokenManagementState
    isTokenValid: true,
    lastValidation: new Date(),
    refreshToken: jest.fn().mockResolvedValue(true),
    tokenError: null,
    validateCurrentToken: jest.fn().mockReturnValue({ isValid: true }),
    willExpireSoon: false,
  },
};

const mockSecurityConfig: SecurityConfig = {
  authentication: {
    autoLogout: true,
    enabled: true,
    redirectOnFailure: true,
  },
  csrf: {
    enabled: true,
    excludePaths: [],
    tokenHeader: 'X-CSRF-Token',
  },
  http: {
    baseURL: '/api',
    retryAttempts: 3,
    timeout: 10_000,
  },
  inputSanitization: {
    enabled: true,
    sanitizers: ['xss', 'sql'],
  },
  tokenValidation: {
    autoRefresh: true,
    enabled: true,
    refreshThreshold: SECURITY_CONSTANTS.TOKEN_EXPIRY_THRESHOLD_MINUTES * 60,
  },
};

const mockSecurityContext: SecurityContext = {
  hasValidToken: true,
  isAuthenticated: true,
  session: { id: 'session-123' },
  timestamp: new Date(),
  user: { id: '123', role: 'user' },
};

describe('SecurityComposer', () => {
  let composer: SecurityComposer;

  beforeEach(() => {
    jest.clearAllMocks();
    composer = new SecurityComposer(mockSecurityFeatures, mockSecurityConfig);
  });

  describe('Initialization', () => {
    it('should initialize with security features and config', () => {
      expect(composer).toBeInstanceOf(SecurityComposer);
    });

    it('should use SECURITY_CONSTANTS for default configuration', () => {
      const defaultComposer = new SecurityComposer(mockSecurityFeatures);
      const status = defaultComposer.getSecurityStatus();

      // Should use SECURITY_CONSTANTS for token refresh threshold
      expect(status).toBeDefined();
    });

    it('should merge custom config with defaults', () => {
      const customConfig = {
        csrf: {
          enabled: false,
          excludePaths: [],
          tokenHeader: 'X-CSRF-Token',
        },
      };

      const customComposer = new SecurityComposer(
        mockSecurityFeatures,
        customConfig
      );
      const status = customComposer.getSecurityStatus();

      expect(status).toBeDefined();
    });
  });

  describe('Request Processing', () => {
    it('should process request with all security features', async () => {
      const requestConfig = {
        body: { data: 'test' },
        headers: {},
        method: 'POST',
        url: '/api/test',
      };

      const processedConfig = await composer.processRequest(
        requestConfig,
        mockSecurityContext
      );

      // Should apply CSRF protection
      expect(
        mockSecurityFeatures.csrfProtection?.attachCSRF
      ).toHaveBeenCalledWith(
        expect.objectContaining({
          method: 'POST',
          url: '/api/test',
        })
      );

      // Should include CSRF token in headers
      expect(processedConfig.headers).toHaveProperty(
        'X-CSRF-Token',
        'mock-csrf-token'
      );
    });

    it('should sanitize input data', async () => {
      const requestConfig = {
        body: { data: '<script>alert("xss")</script>' },
        headers: {},
        method: 'POST',
        url: '/api/test',
      };

      await composer.processRequest(requestConfig, mockSecurityContext);

      expect(
        mockSecurityFeatures.inputValidation?.sanitizeInput
      ).toHaveBeenCalledWith(requestConfig.body);
    });

    it('should skip CSRF for excluded paths', async () => {
      const configWithExclusions = {
        ...mockSecurityConfig,
        csrf: {
          ...mockSecurityConfig.csrf,
          excludePaths: ['/api/public'],
        },
      };

      const composerWithExclusions = new SecurityComposer(
        mockSecurityFeatures,
        configWithExclusions
      );

      const requestConfig = {
        body: { data: 'test' },
        headers: {},
        method: 'POST',
        url: '/api/public/test',
      };

      await composerWithExclusions.processRequest(
        requestConfig,
        mockSecurityContext
      );

      // CSRF should not be applied for excluded paths
      expect(
        mockSecurityFeatures.csrfProtection?.attachCSRF
      ).not.toHaveBeenCalled();
    });

    it('should handle disabled security features', async () => {
      const disabledConfig = {
        ...mockSecurityConfig,
        csrf: { ...mockSecurityConfig.csrf, enabled: false },
        inputSanitization: {
          ...mockSecurityConfig.inputSanitization,
          enabled: false,
        },
      };

      const disabledComposer = new SecurityComposer(
        mockSecurityFeatures,
        disabledConfig
      );

      const requestConfig = {
        body: { data: 'test' },
        headers: {},
        method: 'POST',
        url: '/api/test',
      };

      await disabledComposer.processRequest(requestConfig, mockSecurityContext);

      expect(
        mockSecurityFeatures.csrfProtection?.attachCSRF
      ).not.toHaveBeenCalled();
      expect(
        mockSecurityFeatures.inputValidation?.sanitizeInput
      ).not.toHaveBeenCalled();
    });
  });

  describe('Error Handling', () => {
    it('should handle authentication errors', async () => {
      const authError = new Error('Authentication failed');
      authError.name = 'AuthenticationError';

      await composer.handleError(authError, mockSecurityContext);

      // Should call session clear for auth errors
      expect(
        mockSecurityFeatures.sessionSecurity?.clearSession
      ).toHaveBeenCalled();
    });

    it('should handle CSRF errors', async () => {
      const csrfError = new Error('CSRF token invalid');
      csrfError.name = 'CSRFError';

      await composer.handleError(csrfError, mockSecurityContext);

      // Should handle CSRF errors appropriately
      expect(composer.handleError).toBeDefined();
    });

    it('should handle generic errors', async () => {
      const genericError = new Error('Generic error');

      await composer.handleError(genericError, mockSecurityContext);

      // Should not throw and handle gracefully
      expect(composer.handleError).toBeDefined();
    });
  });

  describe('Security Status', () => {
    it('should provide comprehensive security status', () => {
      const status = composer.getSecurityStatus();

      expect(status).toEqual({
        hasValidToken: true,
        isAuthenticated: true,
        securityFeaturesEnabled: expect.any(Object),
        securityFeaturesInitialized: true,
        sessionActive: true,
      });
    });

    it('should reflect disabled features in status', () => {
      const disabledConfig = {
        ...mockSecurityConfig,
        csrf: { ...mockSecurityConfig.csrf, enabled: false },
      };

      const disabledComposer = new SecurityComposer(
        mockSecurityFeatures,
        disabledConfig
      );
      const status = disabledComposer.getSecurityStatus();

      expect(status.securityFeaturesEnabled?.csrf?.enabled).toBe(false);
    });
  });

  describe('Configuration Updates', () => {
    it('should update security configuration', () => {
      const newConfig = {
        csrf: {
          enabled: false,
          excludePaths: [],
          tokenHeader: 'X-CSRF-Token',
        },
      };

      composer.updateConfig(newConfig);

      const status = composer.getSecurityStatus();
      expect(status.securityFeaturesEnabled?.csrf?.enabled).toBe(false);
    });

    it('should update security features', () => {
      const newFeatures: SecurityFeatures = {
        ...mockSecurityFeatures,
        tokenManagement: {
          ...mockSecurityFeatures.tokenManagement!,
          isTokenValid: false,
        },
      };

      composer.updateSecurityFeatures(newFeatures);

      const status = composer.getSecurityStatus();
      expect(status.hasValidToken).toBe(false);
    });
  });
});

describe('createSecurityComposer', () => {
  it('should create SecurityComposer with factory function', () => {
    const composer = createSecurityComposer(
      mockSecurityFeatures,
      mockSecurityConfig
    );

    expect(composer).toBeInstanceOf(SecurityComposer);
  });

  it('should create SecurityComposer with default config', () => {
    const composer = createSecurityComposer(mockSecurityFeatures);

    expect(composer).toBeInstanceOf(SecurityComposer);
    expect(composer.getSecurityStatus()).toBeDefined();
  });

  it('should log creation in development mode', () => {
    const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

    createSecurityComposer(mockSecurityFeatures, mockSecurityConfig);

    expect(consoleSpy).toHaveBeenCalledWith(
      expect.stringContaining('SecurityComposer created')
    );

    consoleSpy.mockRestore();
  });
});
