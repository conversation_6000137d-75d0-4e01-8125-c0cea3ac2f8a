/**
 * @file Circuit breaker metrics widget component with charts and performance data.
 * This component provides visual metrics for circuit breaker performance including
 * failure rates, success rates, and state distribution charts.
 * @module components/reliability/widgets/circuit-breakers/CircuitBreakerMetrics
 */

'use client';

import { Alert<PERSON><PERSON>gle, BarChart3, PieChart, TrendingDown, TrendingUp } from 'lucide-react';
import React from 'react';
import { <PERSON>, Pie, Pie<PERSON>hart as Recharts<PERSON>ie<PERSON><PERSON>, ResponsiveContainer, Tooltip } from 'recharts';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';
import { Progress } from '@/components/ui/progress';
import { useCircuitBreakerStatus } from '@/lib/stores/queries/useReliability';
import { cn } from '@/lib/utils';

/**
 * Props for the CircuitBreakerMetrics component
 */
export interface CircuitBreakerMetricsProps {
  /** Optional CSS class name for styling */
  className?: string;
  /** Whether to show detailed charts */
  showCharts?: boolean;
}

/**
 * Circuit breaker metrics widget component.
 * 
 * This component provides:
 * - Circuit breaker state distribution charts
 * - Failure rate and success rate metrics
 * - Performance trends and statistics
 * - Visual representation of circuit breaker health
 * 
 * Features:
 * - Interactive charts with tooltips
 * - Real-time data updates
 * - Responsive design
 * - Accessibility support
 * - Color-coded status indicators
 * 
 * @param props - Component props
 * @returns JSX element representing the circuit breaker metrics
 */
export const CircuitBreakerMetrics: React.FC<CircuitBreakerMetricsProps> = ({
  className = '',
  showCharts = true,
}) => {
  const { data: circuitBreakerData, isLoading, error } = useCircuitBreakerStatus();

  /**
   * Prepare chart data for state distribution
   */
  const getStateDistributionData = () => {
    if (!circuitBreakerData?.summary) return [];

    const { closed, halfOpen, open } = circuitBreakerData.summary;
    
    return [
      {
        name: 'Healthy',
        value: closed,
        color: '#10b981', // green-500
        percentage: circuitBreakerData.summary.total > 0 ? Math.round((closed / circuitBreakerData.summary.total) * 100) : 0,
      },
      {
        name: 'Testing',
        value: halfOpen,
        color: '#f59e0b', // yellow-500
        percentage: circuitBreakerData.summary.total > 0 ? Math.round((halfOpen / circuitBreakerData.summary.total) * 100) : 0,
      },
      {
        name: 'Failed',
        value: open,
        color: '#ef4444', // red-500
        percentage: circuitBreakerData.summary.total > 0 ? Math.round((open / circuitBreakerData.summary.total) * 100) : 0,
      },
    ].filter(item => item.value > 0);
  };

  /**
   * Calculate overall metrics
   */
  const getOverallMetrics = () => {
    if (!circuitBreakerData?.circuitBreakers) {
      return {
        totalFailures: 0,
        totalSuccesses: 0,
        averageFailureRate: 0,
        healthScore: 100,
      };
    }

    const totalFailures = circuitBreakerData.circuitBreakers.reduce(
      (sum, breaker) => sum + breaker.failureCount, 0
    );

    const totalSuccesses = circuitBreakerData.circuitBreakers.reduce(
      (sum, breaker) => sum + (breaker.successCount || 0), 0
    );

    const totalRequests = totalFailures + totalSuccesses;
    const averageFailureRate = totalRequests > 0 ? (totalFailures / totalRequests) * 100 : 0;
    
    const healthScore = circuitBreakerData.summary.total > 0 
      ? Math.round((circuitBreakerData.summary.closed / circuitBreakerData.summary.total) * 100)
      : 100;

    return {
      totalFailures,
      totalSuccesses,
      averageFailureRate,
      healthScore,
    };
  };

  // Handle loading state
  if (isLoading) {
    return (
      <div className={cn('space-y-4', className)}>
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          {Array.from({ length: 4 }).map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-4 w-24 bg-muted rounded"></div>
              </CardHeader>
              <CardContent>
                <div className="h-20 bg-muted rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  // Handle error state
  if (error || !circuitBreakerData) {
    return (
      <div className={cn('flex items-center justify-center py-8', className)}>
        <div className="text-center">
          <AlertTriangle className="mx-auto h-8 w-8 text-muted-foreground mb-2" />
          <p className="text-sm text-muted-foreground">
            Failed to load circuit breaker metrics
          </p>
        </div>
      </div>
    );
  }

  const stateDistributionData = getStateDistributionData();
  const metrics = getOverallMetrics();

  return (
    <div className={cn('space-y-4', className)}>
      {/* Metrics Cards */}
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
        {/* Health Score */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Health Score</CardTitle>
            <TrendingUp className={cn(
              'h-4 w-4',
              metrics.healthScore >= 90 ? 'text-green-600' :
              metrics.healthScore >= 70 ? 'text-yellow-600' : 'text-red-600'
            )} />
          </CardHeader>
          <CardContent>
            <div className={cn(
              'text-2xl font-bold',
              metrics.healthScore >= 90 ? 'text-green-600' :
              metrics.healthScore >= 70 ? 'text-yellow-600' : 'text-red-600'
            )}>
              {metrics.healthScore}%
            </div>
            <Progress 
              value={metrics.healthScore} 
              className="mt-2 h-2"
              aria-label={`Health score: ${metrics.healthScore}%`}
            />
          </CardContent>
        </Card>

        {/* Failure Rate */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Failure Rate</CardTitle>
            <TrendingDown className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {metrics.averageFailureRate.toFixed(1)}%
            </div>
            <p className="text-xs text-muted-foreground">
              {metrics.totalFailures} failures
            </p>
          </CardContent>
        </Card>

        {/* Success Count */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Successes</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {metrics.totalSuccesses.toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              Successful requests
            </p>
          </CardContent>
        </Card>

        {/* Active Breakers */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {circuitBreakerData.summary.total}
            </div>
            <p className="text-xs text-muted-foreground">
              Circuit breakers
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Charts */}
      {showCharts && stateDistributionData.length > 0 && (
        <div className="grid grid-cols-1 gap-4 lg:grid-cols-2">
          {/* State Distribution Chart */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <PieChart className="h-5 w-5" />
                State Distribution
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ChartContainer
                config={{
                  healthy: {
                    label: 'Healthy',
                    color: '#10b981',
                  },
                  testing: {
                    label: 'Testing',
                    color: '#f59e0b',
                  },
                  failed: {
                    label: 'Failed',
                    color: '#ef4444',
                  },
                }}
                className="h-[200px]"
              >
                <ResponsiveContainer width="100%" height="100%">
                  <RechartsPieChart>
                    <Pie
                      data={stateDistributionData}
                      cx="50%"
                      cy="50%"
                      innerRadius={40}
                      outerRadius={80}
                      paddingAngle={2}
                      dataKey="value"
                    >
                      {stateDistributionData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <ChartTooltip
                      content={
                        <ChartTooltipContent
                          formatter={(value, name) => [
                            `${value} (${stateDistributionData.find(d => d.name === name)?.percentage}%)`,
                            name
                          ]}
                        />
                      }
                    />
                  </RechartsPieChart>
                </ResponsiveContainer>
              </ChartContainer>
              
              {/* Legend */}
              <div className="flex justify-center gap-4 mt-4">
                {stateDistributionData.map((item) => (
                  <div key={item.name} className="flex items-center gap-2">
                    <div 
                      className="w-3 h-3 rounded-full"
                      style={{ backgroundColor: item.color }}
                    />
                    <span className="text-sm text-muted-foreground">
                      {item.name} ({item.percentage}%)
                    </span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Performance Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Performance Summary
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Success Rate */}
                <div>
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm font-medium">Success Rate</span>
                    <span className="text-sm text-green-600 font-medium">
                      {(100 - metrics.averageFailureRate).toFixed(1)}%
                    </span>
                  </div>
                  <Progress 
                    value={100 - metrics.averageFailureRate} 
                    className="h-2"
                    aria-label={`Success rate: ${(100 - metrics.averageFailureRate).toFixed(1)}%`}
                  />
                </div>

                {/* Failure Rate */}
                <div>
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm font-medium">Failure Rate</span>
                    <span className="text-sm text-red-600 font-medium">
                      {metrics.averageFailureRate.toFixed(1)}%
                    </span>
                  </div>
                  <Progress 
                    value={metrics.averageFailureRate} 
                    className="h-2"
                    aria-label={`Failure rate: ${metrics.averageFailureRate.toFixed(1)}%`}
                  />
                </div>

                {/* Statistics */}
                <div className="grid grid-cols-2 gap-4 pt-2 border-t">
                  <div className="text-center">
                    <div className="text-lg font-bold text-green-600">
                      {metrics.totalSuccesses.toLocaleString()}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      Total Successes
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-bold text-red-600">
                      {metrics.totalFailures.toLocaleString()}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      Total Failures
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};

/**
 * Default export for the CircuitBreakerMetrics component
 */
export default CircuitBreakerMetrics;
