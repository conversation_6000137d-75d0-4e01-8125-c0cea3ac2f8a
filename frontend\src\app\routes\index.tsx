// frontend/src/app/routes/index.tsx (Example for a centralized routing file in Next.js App Router or React Router)
import React from 'react';
import {
  BrowserRouter as Router,
  Routes,
  Route,
  Outlet,
} from 'react-router-dom'; // Example for React Router
import { ReportingLayout } from '@/components/features/reporting/dashboard/layout/ReportingLayout';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute'; // Assuming an existing ProtectedRoute component

// Placeholder components for different report types
const DelegationAnalyticsPage = () => (
  <ReportingLayout
    title="Delegation Analytics"
    description="Overview of delegation performance."
  >
    <div>Delegation Analytics Content Here</div>
  </ReportingLayout>
);
const TaskMetricsPage = () => (
  <ReportingLayout
    title="Task Metrics"
    description="Detailed insights into task completion."
  >
    <div>Task Metrics Content Here</div>
  </ReportingLayout>
);
const ReportingDashboardPage = () => (
  <ReportingLayout
    title="Reporting Dashboard"
    description="Customizable dashboard for key reports."
  >
    <div>Reporting Dashboard Content Here</div>
  </ReportingLayout>
);

console.log('Rendering ReportingDashboardPage');

// Main application router
const AppRouter: React.FC = () => {
  return (
    <Router>
      <Routes>
        {/* Existing application routes */}
        <Route path="/" element={<div>Home Page</div>} />
        <Route path="/settings" element={<div>Settings Page</div>} />

        {/* Reporting Module Routes - Protected */}
        {/* Reporting Module Routes - Protected */}
        <Route
          path="/reporting"
          element={
            <ProtectedRoute allowedRoles={['admin', 'reporter']}>
              <Outlet />
            </ProtectedRoute>
          }
        >
          <Route index element={<ReportingDashboardPage />} />
          <Route path="analytics" element={<DelegationAnalyticsPage />} />
          <Route path="tasks" element={<TaskMetricsPage />} />
          {/* Nested routing for specific report IDs or types */}
          <Route
            path=":reportType/:reportId"
            element={
              <ReportingLayout
                title="Dynamic Report"
                description="Viewing a specific report."
              >
                <Outlet /> {/* Renders nested report components */}
              </ReportingLayout>
            }
          >
            {/* Example of a nested route for a specific report detail */}
            <Route path="detail" element={<div>Report Detail View</div>} />
          </Route>
        </Route>

        {/* Fallback for unknown routes */}
        <Route path="*" element={<div>404 Not Found</div>} />
      </Routes>
    </Router>
  );
};

export default AppRouter;

// Example of how navigation menu items would be updated (e.g., in Navbar.tsx)
/*
// frontend/src/components/global/Navbar.tsx (Conceptual update)
import Link from 'next/link'; // or from 'react-router-dom'

const Navbar = () => {
    return (
        <nav>
            <ul>
                <li><Link href="/">Home</Link></li>
                <li><Link href="/settings">Settings</Link></li>
                <li><Link href="/reporting">Reporting</Link></li>
                <li><Link href="/reporting/analytics">Analytics</Link></li>
                <li><Link href="/reporting/tasks">Task Metrics</Link></li>
            </ul>
        </nav>
    );
};
*/
