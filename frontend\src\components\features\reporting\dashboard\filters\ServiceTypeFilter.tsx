// frontend/src/components/features/reporting/dashboard/filters/ServiceTypeFilter.tsx

import React from 'react';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { cn } from '@/lib/utils';
import { ChevronDown, X, Wrench } from 'lucide-react';
import {
  useReportingFilters,
  useReportingFiltersActions,
  useReportingFiltersValidation,
} from '../../data/stores/useReportingFiltersStore';
import { ServiceTypePrisma } from '../../data/types/vehicleService';

interface ServiceTypeFilterProps {
  compact?: boolean;
  className?: string;
}

/**
 * @component ServiceTypeFilter
 * @description Service type filter component following existing filter patterns
 * 
 * Responsibilities:
 * - Provides service type selection interface
 * - Integrates with existing reporting filters store
 * - Follows same patterns as StatusFilter and other filters
 * - Handles validation and error display
 * 
 * SOLID Principles Applied:
 * - SRP: Single responsibility of service type filtering
 * - OCP: Open for extension via props
 * - DIP: Depends on existing filter store abstractions
 */
export const ServiceTypeFilter: React.FC<ServiceTypeFilterProps> = ({
  compact = false,
  className = '',
}) => {
  const filters = useReportingFilters();
  const { setServiceTypes } = useReportingFiltersActions();
  const { validationErrors } = useReportingFiltersValidation();

  const [isOpen, setIsOpen] = React.useState(false);

  // Available service type options
  const serviceTypeOptions = [
    { value: 'MAINTENANCE' as ServiceTypePrisma, label: 'Maintenance', color: 'bg-blue-100 text-blue-800', icon: '🔧' },
    { value: 'REPAIR' as ServiceTypePrisma, label: 'Repair', color: 'bg-red-100 text-red-800', icon: '🛠️' },
    { value: 'INSPECTION' as ServiceTypePrisma, label: 'Inspection', color: 'bg-green-100 text-green-800', icon: '🔍' },
    { value: 'CLEANING' as ServiceTypePrisma, label: 'Cleaning', color: 'bg-cyan-100 text-cyan-800', icon: '🧽' },
    { value: 'FUEL' as ServiceTypePrisma, label: 'Fuel', color: 'bg-yellow-100 text-yellow-800', icon: '⛽' },
    { value: 'EMERGENCY' as ServiceTypePrisma, label: 'Emergency', color: 'bg-orange-100 text-orange-800', icon: '🚨' },
    { value: 'UPGRADE' as ServiceTypePrisma, label: 'Upgrade', color: 'bg-purple-100 text-purple-800', icon: '⬆️' },
    { value: 'INSURANCE' as ServiceTypePrisma, label: 'Insurance', color: 'bg-indigo-100 text-indigo-800', icon: '🛡️' },
  ];

  // Handle service type selection
  const handleServiceTypeToggle = (serviceType: ServiceTypePrisma) => {
    const currentTypes = filters.serviceTypes || [];
    const isSelected = currentTypes.includes(serviceType);
    
    if (isSelected) {
      setServiceTypes(currentTypes.filter(t => t !== serviceType));
    } else {
      setServiceTypes([...currentTypes, serviceType]);
    }
  };

  // Handle select all/none
  const handleSelectAll = () => {
    setServiceTypes(serviceTypeOptions.map(option => option.value));
  };

  const handleSelectNone = () => {
    setServiceTypes([]);
  };

  // Get display text
  const getDisplayText = () => {
    const selectedCount = filters.serviceTypes?.length || 0;
    if (selectedCount === 0) return 'All service types';
    if (selectedCount === 1) {
      const selected = serviceTypeOptions.find(option => option.value === filters.serviceTypes?.[0]);
      return selected?.label || 'Unknown';
    }
    return `${selectedCount} service types`;
  };

  const hasError = validationErrors.serviceTypes;

  if (compact) {
    return (
      <div className={cn('space-y-1', className)}>
        <Label className="text-xs font-medium">Service Type</Label>
        <Popover open={isOpen} onOpenChange={setIsOpen}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className={cn(
                'w-full justify-between text-left font-normal',
                hasError && 'border-red-500'
              )}
            >
              <span className="truncate">{getDisplayText()}</span>
              <ChevronDown className="ml-2 h-4 w-4 shrink-0" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-64 p-0" align="start">
            <div className="p-3">
              <div className="flex justify-between mb-3">
                <Button variant="ghost" size="sm" onClick={handleSelectAll}>
                  All
                </Button>
                <Button variant="ghost" size="sm" onClick={handleSelectNone}>
                  None
                </Button>
              </div>
              <div className="space-y-2">
                {serviceTypeOptions.map((option) => (
                  <div key={option.value} className="flex items-center space-x-2">
                    <Checkbox
                      id={`service-type-${option.value}`}
                      checked={filters.serviceTypes?.includes(option.value) || false}
                      onCheckedChange={() => handleServiceTypeToggle(option.value)}
                    />
                    <Label
                      htmlFor={`service-type-${option.value}`}
                      className="text-sm font-normal cursor-pointer flex-1 flex items-center gap-2"
                    >
                      <span>{option.icon}</span>
                      <Badge variant="secondary" className={cn('text-xs', option.color)}>
                        {option.label}
                      </Badge>
                    </Label>
                  </div>
                ))}
              </div>
            </div>
          </PopoverContent>
        </Popover>
        {hasError && (
          <p className="text-xs text-red-600">{hasError}</p>
        )}
      </div>
    );
  }

  return (
    <div className={cn('space-y-2', className)}>
      <Label className="text-sm font-medium">Service Types</Label>
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className={cn(
              'w-full justify-between text-left font-normal',
              hasError && 'border-red-500'
            )}
          >
            <span>{getDisplayText()}</span>
            <ChevronDown className="ml-2 h-4 w-4 shrink-0" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-80 p-0" align="start">
          <div className="p-4">
            <div className="flex justify-between items-center mb-4">
              <h4 className="font-medium text-sm flex items-center gap-2">
                <Wrench className="h-4 w-4" />
                Select Service Types
              </h4>
              <div className="flex gap-2">
                <Button variant="ghost" size="sm" onClick={handleSelectAll}>
                  All
                </Button>
                <Button variant="ghost" size="sm" onClick={handleSelectNone}>
                  None
                </Button>
              </div>
            </div>
            <div className="space-y-3">
              {serviceTypeOptions.map((option) => (
                <div key={option.value} className="flex items-center space-x-3">
                  <Checkbox
                    id={`service-type-${option.value}`}
                    checked={filters.serviceTypes?.includes(option.value) || false}
                    onCheckedChange={() => handleServiceTypeToggle(option.value)}
                  />
                  <Label
                    htmlFor={`service-type-${option.value}`}
                    className="text-sm font-normal cursor-pointer flex-1 flex items-center gap-3"
                  >
                    <span className="text-lg">{option.icon}</span>
                    <Badge variant="secondary" className={cn('text-sm', option.color)}>
                      {option.label}
                    </Badge>
                  </Label>
                </div>
              ))}
            </div>
          </div>
        </PopoverContent>
      </Popover>
      
      {/* Selected service type badges */}
      {filters.serviceTypes && filters.serviceTypes.length > 0 && (
        <div className="flex flex-wrap gap-1">
          {filters.serviceTypes.map((serviceType) => {
            const option = serviceTypeOptions.find(opt => opt.value === serviceType);
            if (!option) return null;
            
            return (
              <Badge
                key={serviceType}
                variant="secondary"
                className={cn('text-xs pr-1', option.color)}
              >
                <span className="mr-1">{option.icon}</span>
                {option.label}
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-auto p-0 ml-1 hover:bg-transparent"
                  onClick={() => handleServiceTypeToggle(serviceType)}
                >
                  <X className="h-3 w-3" />
                </Button>
              </Badge>
            );
          })}
        </div>
      )}
      
      {hasError && (
        <p className="text-sm text-red-600">{hasError}</p>
      )}
    </div>
  );
};

export default ServiceTypeFilter;
