#!/bin/bash

# WorkHub Backend Critical Issues Resolution Script
# Fixes: Supabase connection, Redis connectivity, Circuit breaker initialization

set -e

echo "🔧 WorkHub Backend Critical Issues Resolution"
echo "=============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if docker-compose is available
if ! command -v docker-compose &> /dev/null; then
    print_error "docker-compose is not installed or not in PATH"
    exit 1
fi

print_status "Step 1: Stopping existing services..."
docker-compose -f docker-compose.staging.yml down --remove-orphans

print_status "Step 2: Removing old containers and networks..."
docker container prune -f
docker network prune -f

print_status "Step 3: Building updated backend image..."
docker-compose -f docker-compose.staging.yml build backend --no-cache

print_status "Step 4: Starting Redis service first..."
docker-compose -f docker-compose.staging.yml up -d redis

print_status "Step 5: Waiting for Redis to be healthy..."
timeout=60
counter=0
while [ $counter -lt $timeout ]; do
    if docker-compose -f docker-compose.staging.yml ps redis | grep -q "healthy"; then
        print_success "Redis is healthy"
        break
    fi
    
    if [ $counter -eq $((timeout-1)) ]; then
        print_error "Redis failed to become healthy within $timeout seconds"
        docker-compose -f docker-compose.staging.yml logs redis
        exit 1
    fi
    
    echo -n "."
    sleep 1
    counter=$((counter+1))
done

print_status "Step 6: Starting backend service..."
docker-compose -f docker-compose.staging.yml up -d backend

print_status "Step 7: Waiting for backend to be healthy..."
timeout=120
counter=0
while [ $counter -lt $timeout ]; do
    if docker-compose -f docker-compose.staging.yml ps backend | grep -q "healthy"; then
        print_success "Backend is healthy"
        break
    fi
    
    # Check for specific errors in logs
    if docker-compose -f docker-compose.staging.yml logs backend 2>&1 | grep -q "Supabase.*failed"; then
        print_warning "Supabase connection issues detected - checking logs..."
    fi
    
    if docker-compose -f docker-compose.staging.yml logs backend 2>&1 | grep -q "redis.*unavailable"; then
        print_warning "Redis connection issues detected - checking logs..."
    fi
    
    if [ $counter -eq $((timeout-1)) ]; then
        print_error "Backend failed to become healthy within $timeout seconds"
        print_error "Backend logs:"
        docker-compose -f docker-compose.staging.yml logs backend --tail=50
        exit 1
    fi
    
    echo -n "."
    sleep 2
    counter=$((counter+2))
done

print_status "Step 8: Starting frontend service..."
docker-compose -f docker-compose.staging.yml up -d frontend

print_status "Step 9: Final health check..."
sleep 10

# Test backend health endpoint
print_status "Testing backend health endpoint..."
if curl -f -s http://localhost:3001/api/health > /dev/null; then
    print_success "Backend health endpoint is responding"
else
    print_error "Backend health endpoint is not responding"
    exit 1
fi

# Test Redis connectivity
print_status "Testing Redis connectivity..."
if docker exec workhub-redis-staging redis-cli ping | grep -q "PONG"; then
    print_success "Redis is responding to ping"
else
    print_error "Redis is not responding to ping"
    exit 1
fi

print_status "Step 10: Checking service logs for critical issues..."

# Check for resolved issues
if ! docker-compose -f docker-compose.staging.yml logs backend --since=2m | grep -q "Supabase.*failed"; then
    print_success "✅ Supabase connection issues resolved"
else
    print_warning "⚠️ Supabase connection issues still present"
fi

if ! docker-compose -f docker-compose.staging.yml logs backend --since=2m | grep -q "redis.*unavailable"; then
    print_success "✅ Redis connection issues resolved"
else
    print_warning "⚠️ Redis connection issues still present"
fi

if docker-compose -f docker-compose.staging.yml logs backend --since=2m | grep -q "Circuit breakers initialized"; then
    print_success "✅ Circuit breakers initialized successfully"
else
    print_warning "⚠️ Circuit breaker initialization issues may persist"
fi

echo ""
print_success "🎉 Backend critical issues resolution completed!"
echo ""
print_status "Service Status:"
docker-compose -f docker-compose.staging.yml ps

echo ""
print_status "To monitor logs in real-time, run:"
echo "docker-compose -f docker-compose.staging.yml logs -f backend"

echo ""
print_status "To test the health endpoint:"
echo "curl http://localhost:3001/api/health"
