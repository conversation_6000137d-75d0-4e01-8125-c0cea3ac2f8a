/**
 * Consolidated Admin Controller
 *
 * This controller provides unified endpoints for all admin operations,
 * including user management, system monitoring, audit logging, and
 * health checks.
 *
 * Features:
 * - Centralized admin endpoint management
 * - Consistent request/response patterns
 * - Comprehensive input validation
 * - Audit logging for all admin operations
 * - Circuit breaker protection
 * - Request deduplication support
 */

import type { NextFunction, Request, Response } from 'express';

import type {
  AuditDetails,
  AuditLogFilterOptions,
  CreateUserRequest,
  ErrorLogFilterOptions,
  LogLevel,
  UpdateUserRequest,
  UserFilterOptions,
} from '../types/admin.types.js';

import { businessMetrics } from '../../../services/metrics.service.js';
import logger from '../../../utils/logger.js';
import { adminService } from '../services/admin.service.js';
import { AdminError, AdminPermissionError, AdminValidationError } from '../types/admin.types.js';

/**
 * Extract audit details from request
 */
function extractAuditDetails(req: Request): AuditDetails {
  return {
    ipAddress: req.ip || req.connection.remoteAddress,
    userAgent: req.headers['user-agent'],
    userId: (req as any).user?.id || 'unknown',
  };
}

/**
 * Handle admin errors consistently
 */
function handleAdminError(error: any, res: Response, operation: string): void {
  logger.error(`Admin operation failed: ${operation}`, {
    error: error.message,
    fullError: error, // Log the full error object
    service: 'admin-controller',
    stack: error.stack,
  });

  if (error instanceof AdminValidationError) {
    res.status(400).json({
      code: error.code,
      message: error.message,
      status: 'error',
      timestamp: new Date().toISOString(),
    });
  } else if (error instanceof AdminPermissionError) {
    res.status(403).json({
      code: error.code,
      message: error.message,
      status: 'error',
      timestamp: new Date().toISOString(),
    });
  } else if (error instanceof AdminError) {
    res.status(error.statusCode).json({
      code: error.code,
      message: error.message,
      status: 'error',
      timestamp: new Date().toISOString(),
    });
  } else {
    res.status(500).json({
      code: 'INTERNAL_ERROR',
      message: 'Internal server error',
      status: 'error',
      timestamp: new Date().toISOString(),
    });
  }
}

/**
 * Admin Dashboard Controller
 * GET /api/admin/dashboard
 */
export const getDashboard = async (
  req: Request,
  res: Response,
  next: NextFunction,
): Promise<void> => {
  try {
    const auditDetails = extractAuditDetails(req);
    const result = await adminService.getDashboardData(auditDetails);

    res.json(result);
  } catch (error) {
    handleAdminError(error, res, 'GET_DASHBOARD');
  }
};

/**
 * Health Status Controller
 * GET /api/admin/health
 */
export const getHealthStatus = async (
  req: Request,
  res: Response,
  next: NextFunction,
): Promise<void> => {
  try {
    const healthStatus = await adminService.getHealthStatus();

    res.json({
      data: healthStatus,
      status: 'success',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    handleAdminError(error, res, 'GET_HEALTH_STATUS');
  }
};

/**
 * Performance Metrics Controller
 * GET /api/admin/performance
 */
export const getPerformanceMetrics = async (
  req: Request,
  res: Response,
  next: NextFunction,
): Promise<void> => {
  try {
    const performanceMetrics = await adminService.getPerformanceMetrics();

    res.json({
      data: performanceMetrics,
      status: 'success',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    handleAdminError(error, res, 'GET_PERFORMANCE_METRICS');
  }
};

/**
 * Error Logs Controller
 * GET /api/admin/logs/errors
 */
export const getErrorLogs = async (
  req: Request,
  res: Response,
  next: NextFunction,
): Promise<void> => {
  try {
    const auditDetails = extractAuditDetails(req);
    const options: ErrorLogFilterOptions = {
      endDate: req.query.endDate as string,
      level: req.query.level as LogLevel,
      limit: parseInt(req.query.limit as string) || 10,
      page: parseInt(req.query.page as string) || 1,
      sortBy: (req.query.sortBy as any) || 'timestamp',
      sortOrder: (req.query.sortOrder as any) || 'desc',
      source: req.query.source as string,
      startDate: req.query.startDate as string,
    };

    const result = await adminService.getErrorLogs(options, auditDetails);
    res.json(result);
  } catch (error) {
    handleAdminError(error, res, 'GET_ERROR_LOGS');
  }
};

/**
 * Users List Controller
 * GET /api/admin/users
 */
export const getUsers = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const auditDetails = extractAuditDetails(req);
    const options: UserFilterOptions = {
      isActive: req.query.isActive ? req.query.isActive === 'true' : undefined,
      limit: parseInt(req.query.limit as string) || 10,
      page: parseInt(req.query.page as string) || 1,
      role: req.query.role as string,
      search: req.query.search as string,
      sortBy: (req.query.sortBy as any) || 'created_at',
      sortOrder: (req.query.sortOrder as any) || 'desc',
    };

    const result = await adminService.getAllUsers(options, auditDetails);
    res.json(result);
  } catch (error) {
    handleAdminError(error, res, 'GET_USERS');
  }
};

/**
 * Create User Controller
 * POST /api/admin/users
 */
export const createUser = async (
  req: Request,
  res: Response,
  next: NextFunction,
): Promise<void> => {
  try {
    const auditDetails = extractAuditDetails(req);
    const userData: CreateUserRequest = {
      email: req.body.email,
      emailVerified: req.body.emailVerified ?? false,
      isActive: req.body.isActive ?? true,
      role: req.body.role,
    };

    const result = await adminService.createUser(userData, auditDetails);
    res.status(201).json(result);
  } catch (error) {
    handleAdminError(error, res, 'CREATE_USER');
  }
};

/**
 * Update User Controller
 * PUT /api/admin/users/:id
 */
export const updateUser = async (
  req: Request,
  res: Response,
  next: NextFunction,
): Promise<void> => {
  try {
    const auditDetails = extractAuditDetails(req);
    const userId = req.params.id;
    const userData: UpdateUserRequest = {
      email: req.body.email,
      emailVerified: req.body.emailVerified,
      isActive: req.body.isActive,
      role: req.body.role,
    };

    const result = await adminService.updateUser(userId, userData, auditDetails);
    res.json(result);
  } catch (error) {
    handleAdminError(error, res, 'UPDATE_USER');
  }
};

/**
 * Delete User Controller
 * DELETE /api/admin/users/:id
 */
export const deleteUser = async (
  req: Request,
  res: Response,
  next: NextFunction,
): Promise<void> => {
  try {
    const auditDetails = extractAuditDetails(req);
    const userId = req.params.id;

    // Import the delete function from the user service
    const { deleteUser: deleteUserService } = await import(
      '../../../services/userManagement.service.js'
    );
    await deleteUserService(userId, auditDetails);

    businessMetrics.recordAdminOperation('DELETE_USER', auditDetails.userId, 'success');

    res.json({
      message: `User ${userId} deleted successfully`,
      status: 'success',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    const auditDetails = extractAuditDetails(req);
    businessMetrics.recordAdminOperation('DELETE_USER', auditDetails.userId, 'error');
    handleAdminError(error, res, 'DELETE_USER');
  }
};

/**
 * Toggle User Activation Controller
 * PATCH /api/admin/users/:id/toggle-activation
 */
export const toggleUserActivation = async (
  req: Request,
  res: Response,
  next: NextFunction,
): Promise<void> => {
  try {
    const auditDetails = extractAuditDetails(req);
    const userId = req.params.id;
    const isActive = req.body.isActive;

    if (typeof isActive !== 'boolean') {
      throw new AdminValidationError('isActive must be a boolean value');
    }

    // Import the toggle function from the user service
    const { toggleUserActivation: toggleUserActivationService } = await import(
      '../../../services/userManagement.service.js'
    );
    const user = await toggleUserActivationService(userId, isActive, auditDetails);

    businessMetrics.recordAdminOperation('TOGGLE_USER_ACTIVATION', auditDetails.userId, 'success');

    res.json({
      data: user,
      message: `User ${userId} ${isActive ? 'activated' : 'deactivated'} successfully`,
      status: 'success',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    const auditDetails = extractAuditDetails(req);
    businessMetrics.recordAdminOperation('TOGGLE_USER_ACTIVATION', auditDetails.userId, 'error');
    handleAdminError(error, res, 'TOGGLE_USER_ACTIVATION');
  }
};

/**
 * Audit Logs Controller
 * GET /api/admin/audit-logs
 */
export const getAuditLogs = async (
  req: Request,
  res: Response,
  next: NextFunction,
): Promise<void> => {
  try {
    const auditDetails = extractAuditDetails(req);
    const options: AuditLogFilterOptions = {
      action: req.query.action as string,
      endDate: req.query.endDate as string,
      limit: parseInt(req.query.limit as string) || 10,
      page: parseInt(req.query.page as string) || 1,
      search: req.query.search as string,
      sortBy: (req.query.sortBy as any) || 'created_at',
      sortOrder: (req.query.sortOrder as any) || 'desc',
      startDate: req.query.startDate as string,
      userId: req.query.userId as string,
    };

    const result = await adminService.getAuditLogs(options, auditDetails);
    res.json(result);
  } catch (error) {
    handleAdminError(error, res, 'GET_AUDIT_LOGS');
  }
};

/**
 * System Statistics Controller
 * GET /api/admin/statistics
 */
export const getSystemStatistics = async (
  req: Request,
  res: Response,
  next: NextFunction,
): Promise<void> => {
  try {
    const auditDetails = extractAuditDetails(req);

    // Get basic system statistics
    const statistics = {
      performance: {
        memoryUsage: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
        nodeVersion: process.version,
        pid: process.pid,
        platform: process.platform,
      },
      system: {
        environment: process.env.NODE_ENV || 'development',
        lastRestart: new Date(Date.now() - process.uptime() * 1000).toISOString(),
        uptime: Math.floor(process.uptime()),
        version: process.env.npm_package_version || '1.0.0',
      },
    };

    businessMetrics.recordAdminOperation('VIEW_STATISTICS', auditDetails.userId, 'success');

    res.json({
      data: statistics,
      status: 'success',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    const auditDetails = extractAuditDetails(req);
    businessMetrics.recordAdminOperation('VIEW_STATISTICS', auditDetails.userId, 'error');
    handleAdminError(error, res, 'GET_SYSTEM_STATISTICS');
  }
};

export default {
  createUser,
  deleteUser,
  getAuditLogs,
  getDashboard,
  getErrorLogs,
  getHealthStatus,
  getPerformanceMetrics,
  getSystemStatistics,
  getUsers,
  toggleUserActivation,
  updateUser,
};
