/**
 * @file ReportHistory.tsx
 * @description Component for viewing and managing report generation history
 */

import { formatDistanceToNow } from 'date-fns';
import {
  Calendar,
  Download,
  Eye,
  FileText,
  Filter,
  History,
  RefreshCw,
  Search,
} from 'lucide-react';
import React, { useState } from 'react';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

import {
  useReportDownload,
  useReportHistory,
} from '../hooks/useReportGeneration';

/**
 * Report type filter options
 */
const REPORT_TYPE_FILTERS = [
  { label: 'All Types', value: '' },
  { label: 'Comprehensive', value: 'comprehensive' },
  { label: 'Individual', value: 'individual' },
  { label: 'Aggregate', value: 'aggregate' },
];

/**
 * Entity type filter options
 */
const ENTITY_TYPE_FILTERS = [
  { label: 'All Entities', value: '' },
  { label: 'Delegations', value: 'delegations' },
  { label: 'Tasks', value: 'tasks' },
  { label: 'Vehicles', value: 'vehicles' },
  { label: 'Employees', value: 'employees' },
];

/**
 * Status badge configurations
 */
const STATUS_CONFIGS = {
  completed: { color: 'bg-green-100 text-green-800', label: 'Completed' },
  failed: { color: 'bg-red-100 text-red-800', label: 'Failed' },
  processing: { color: 'bg-yellow-100 text-yellow-800', label: 'Processing' },
};

/**
 * Format badge configurations
 */
const FORMAT_CONFIGS = {
  csv: { color: 'bg-blue-100 text-blue-800', label: 'CSV' },
  excel: { color: 'bg-green-100 text-green-800', label: 'Excel' },
  pdf: { color: 'bg-red-100 text-red-800', label: 'PDF' },
};

interface ReportHistoryProps {
  onReportSelect?: (report: any) => void;
}

/**
 * ReportHistory Component
 *
 * Displays history of generated reports with filtering and download capabilities.
 */
export const ReportHistory: React.FC<ReportHistoryProps> = ({
  onReportSelect,
}) => {
  // State management
  const [typeFilter, setTypeFilter] = useState<string>('');
  const [entityFilter, setEntityFilter] = useState<string>('');
  const [searchTerm, setSearchTerm] = useState<string>('');

  // Hooks
  const { error, isLoading, pagination, refetch, reports } = useReportHistory({
    ...(typeFilter && { type: typeFilter }),
    ...(entityFilter && { entityType: entityFilter }),
  });
  const { downloadError, downloadReport, isDownloading } = useReportDownload();

  /**
   * Handle report download
   */
  const handleDownload = async (reportId: string) => {
    try {
      await downloadReport(reportId);
    } catch (error) {
      console.error('Failed to download report:', error);
    }
  };

  /**
   * Filter reports based on search term
   */
  const filteredReports = reports.filter((report: any) => {
    if (!searchTerm) return true;

    const searchLower = searchTerm.toLowerCase();
    return (
      report.id.toLowerCase().includes(searchLower) ||
      report.type.toLowerCase().includes(searchLower) ||
      report.entityType?.toLowerCase().includes(searchLower) ||
      report.entityTypes?.some((type: any) =>
        type.toLowerCase().includes(searchLower)
      )
    );
  });

  /**
   * Get entity types display for comprehensive reports
   */
  const getEntityTypesDisplay = (report: any) => {
    if (report.type === 'individual') {
      return report.entityType;
    }
    if (report.type === 'aggregate') {
      return report.entityType;
    }
    if (report.entityTypes) {
      return report.entityTypes.join(', ');
    }
    return 'N/A';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-xl font-semibold text-gray-900">
            Report History
          </h3>
          <p className="mt-1 text-gray-600">
            View and manage your generated reports
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            disabled={isLoading}
            onClick={() => refetch()}
            size="sm"
            variant="outline"
          >
            <RefreshCw
              className={`mr-2 size-4 ${isLoading ? 'animate-spin' : ''}`}
            />
            Refresh
          </Button>
          <Badge className="flex items-center gap-2" variant="outline">
            <History className="size-4" />
            {reports.length} Reports
          </Badge>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="size-5" />
            Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
            {/* Search */}
            <div>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 size-4 -translate-y-1/2 text-gray-400" />
                <Input
                  className="pl-10"
                  onChange={e => setSearchTerm(e.target.value)}
                  placeholder="Search reports..."
                  value={searchTerm}
                />
              </div>
            </div>

            {/* Report Type Filter */}
            <div>
              <Select onValueChange={setTypeFilter} value={typeFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Report Type" />
                </SelectTrigger>
                <SelectContent>
                  {REPORT_TYPE_FILTERS.map(option => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Entity Type Filter */}
            <div>
              <Select onValueChange={setEntityFilter} value={entityFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Entity Type" />
                </SelectTrigger>
                <SelectContent>
                  {ENTITY_TYPE_FILTERS.map(option => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Clear Filters */}
            <div>
              <Button
                className="w-full"
                onClick={() => {
                  setTypeFilter('');
                  setEntityFilter('');
                  setSearchTerm('');
                }}
                variant="outline"
              >
                Clear Filters
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Error Display */}
      {(error || downloadError) && (
        <Alert variant="destructive">
          <AlertDescription>{String(error || downloadError)}</AlertDescription>
        </Alert>
      )}

      {/* Reports List */}
      <Card>
        <CardHeader>
          <CardTitle>Generated Reports</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <LoadingSpinner className="size-8" />
            </div>
          ) : filteredReports.length === 0 ? (
            <div className="py-8 text-center">
              <FileText className="mx-auto mb-4 size-12 text-gray-400" />
              <h3 className="mb-2 text-lg font-medium text-gray-900">
                No Reports Found
              </h3>
              <p className="text-gray-600">
                {reports.length === 0
                  ? 'No reports have been generated yet.'
                  : 'No reports match your current filters.'}
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredReports.map((report: any) => {
                const statusConfig =
                  STATUS_CONFIGS[report.status as keyof typeof STATUS_CONFIGS];
                const formatConfig =
                  FORMAT_CONFIGS[report.format as keyof typeof FORMAT_CONFIGS];

                return (
                  <div
                    className="rounded-lg border p-4 transition-colors hover:bg-gray-50"
                    key={report.id}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="mb-2 flex items-center gap-3">
                          <h4 className="font-medium text-gray-900">
                            {report.id}
                          </h4>
                          <Badge
                            className={statusConfig?.color}
                            variant="secondary"
                          >
                            {statusConfig?.label || report.status}
                          </Badge>
                          <Badge
                            className={formatConfig?.color}
                            variant="secondary"
                          >
                            {formatConfig?.label || report.format}
                          </Badge>
                          <Badge variant="outline">{report.type}</Badge>
                        </div>

                        <div className="grid grid-cols-1 gap-4 text-sm text-gray-600 md:grid-cols-3">
                          <div>
                            <span className="font-medium">Entity Types:</span>
                            <p className="mt-1">
                              {getEntityTypesDisplay(report)}
                            </p>
                          </div>
                          <div>
                            <span className="font-medium">Generated:</span>
                            <p className="mt-1 flex items-center gap-1">
                              <Calendar className="size-3" />
                              {formatDistanceToNow(
                                new Date(report.generatedAt),
                                { addSuffix: true }
                              )}
                            </p>
                          </div>
                          <div>
                            <span className="font-medium">File Size:</span>
                            <p className="mt-1">{report.fileSize || 'N/A'}</p>
                          </div>
                        </div>

                        {report.type === 'individual' && report.entityId && (
                          <div className="mt-2 text-sm text-gray-600">
                            <span className="font-medium">Entity ID:</span>{' '}
                            {report.entityId}
                          </div>
                        )}
                      </div>

                      <div className="ml-4 flex items-center gap-2">
                        {onReportSelect && (
                          <Button
                            onClick={() => onReportSelect(report)}
                            size="sm"
                            variant="outline"
                          >
                            <Eye className="mr-1 size-4" />
                            View
                          </Button>
                        )}

                        {report.status === 'completed' && (
                          <Button
                            disabled={isDownloading}
                            onClick={() => handleDownload(report.id)}
                            size="sm"
                            variant="outline"
                          >
                            {isDownloading ? (
                              <LoadingSpinner className="mr-1 size-4" />
                            ) : (
                              <Download className="mr-1 size-4" />
                            )}
                            Download
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Pagination */}
      {pagination && pagination.totalPages > 1 && (
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <p className="text-sm text-gray-600">
                Showing {(pagination.page - 1) * pagination.limit + 1} to{' '}
                {Math.min(pagination.page * pagination.limit, pagination.total)}{' '}
                of {pagination.total} reports
              </p>
              <div className="flex items-center gap-2">
                <Button
                  disabled={pagination.page <= 1}
                  size="sm"
                  variant="outline"
                >
                  Previous
                </Button>
                <span className="text-sm text-gray-600">
                  Page {pagination.page} of {pagination.totalPages}
                </span>
                <Button
                  disabled={pagination.page >= pagination.totalPages}
                  size="sm"
                  variant="outline"
                >
                  Next
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
