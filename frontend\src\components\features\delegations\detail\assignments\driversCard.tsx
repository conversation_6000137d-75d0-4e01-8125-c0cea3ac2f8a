/**
 * @file DriversCard component for displaying delegation drivers
 * @module components/delegations/detail/assignments/DriversCard
 */

import { User } from 'lucide-react';
import React from 'react';

import type { Delegation } from '@/lib/types/domain';

import {
  formatEmployeeName,
  formatEmployeeRole,
} from '@/lib/utils/formattingUtils';

import { AssignmentSection } from './AssignmentSection';

interface DriversCardProps {
  className?: string;
  delegation: Delegation;
}

/**
 * DriversCard component for displaying delegation drivers
 * Uses the generic AssignmentSection for consistent styling
 */
export function DriversCard({ className, delegation }: DriversCardProps) {
  return (
    <AssignmentSection
      className={className ?? ''}
      emptyMessage="No drivers assigned to this delegation."
      icon={User}
      items={delegation.drivers ?? []}
      renderItem={(driver, index) => (
        <div
          className="space-y-2 rounded-lg border border-gray-200 bg-white p-4 transition-shadow hover:shadow-sm dark:border-gray-700 dark:bg-gray-800"
          key={driver.employeeId || index}
        >
          <h4 className="font-semibold text-gray-900 dark:text-white">
            {driver.employee
              ? formatEmployeeName(driver.employee)
              : `Employee ID: ${driver.employeeId}`}
          </h4>
          {driver.employee?.role && (
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {formatEmployeeRole(driver.employee.role)}
            </p>
          )}
          {driver.employee?.contactEmail && (
            <p className="rounded bg-gray-50 p-2 text-xs text-gray-500 dark:bg-gray-700 dark:text-gray-500">
              📧 {driver.employee.contactEmail}
            </p>
          )}
        </div>
      )}
      title="Drivers"
    />
  );
}

export default DriversCard;
