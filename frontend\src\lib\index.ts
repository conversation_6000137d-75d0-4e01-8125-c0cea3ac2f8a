/**
 * @file Lib Index
 * @description Centralized exports for all lib modules following architectural excellence principles
 */

// ===== CORE INFRASTRUCTURE =====

// API Layer - HTTP communication and service layer
export * from './api';

// Security Layer - Authentication, authorization, and security utilities
export * from './security';

// ===== DATA LAYER =====

// Schemas - Validation and type definitions (specific exports to avoid conflicts)
export {
  // Delegation schemas
  DelegationStatusSchema,
  StatusHistoryEntrySchema,
  DelegateSchema,
  FlightDetailsSchema,
  DelegationFormSchema,
  type DelegationFormData,
  type DelegationStatus,
  type StatusHistoryEntry,
  type Delegate,
  type FlightDetails,

  // Employee schemas
  EmployeeStatusSchema,
  EmployeeRoleSchema,
  EmployeeFormSchema,
  type EmployeeFormData,
  type EmployeeStatus,
  type EmployeeRole,

  // Driver schemas
  DriverAvailabilitySchema,
  type DriverAvailability,

  // Task schemas
  TaskStatusSchema,
  TaskPrioritySchema,
  SubTaskSchema,
  TaskFormSchema,
  type TaskFormData,
  type TaskStatus,
  type TaskPriority,
  type SubTask,

  // Vehicle schemas
  VehicleStatusSchema,
  VehicleFuelTypeSchema,
  VehicleFormSchema,
  type VehicleFormData,
  type VehicleStatus,
  type VehicleFuelType,

  // Schema utilities
  SchemaUtils,
  CommonSchemas,
} from './schemas';

// Transformers - Data transformation between API and domain models
export * from './transformers';

// Types - TypeScript type definitions (specific exports to avoid conflicts)
export type {
  // API types (legacy ApiResponse and PaginationResponse removed - use standardized types)
  ApiError,
  ApiValidationError,

  // API contract types
  CreateEmployeeRequest,
  UpdateEmployeeRequest,
  CreateVehicleRequest,
  UpdateVehicleRequest,
  CreateTaskRequest,
  UpdateTaskRequest,
  CreateDelegationRequest,
  UpdateDelegationRequest,

  // API response types
  EmployeeApiResponse,
  VehicleApiResponse,
  TaskApiResponse,
  DelegationApiResponse,
} from './types/api';

// Domain types
export type {
  // Core domain entities
  Employee,
  Vehicle,
  Task,
  Delegation,

  // Supporting types
  CircuitBreakerState,
  HealthStatus,
  AuditLog,
  UserProfile,

  // Create data types
  CreateEmployeeData,
  CreateVehicleData,
  CreateTaskData,
  CreateDelegationData,

  // Enum types
  EmployeeRolePrisma,
  EmployeeStatusPrisma,
  TaskPriorityPrisma,
  TaskStatusPrisma,
  DelegationStatusPrisma,
  DriverAvailabilityPrisma,
} from './types/domain';

// ===== UTILITY LAYER =====

// Utils - Pure utility functions
export * from './utils';

// ===== BUSINESS LOGIC LAYER =====

// Services - Business logic services
export {
  WebSocketManager,
  getWebSocketManager,
} from './services/WebSocketManager';
export { TokenRefreshService } from './services/TokenRefreshService';

// ===== STATE MANAGEMENT =====

// Stores - State management
export { queryClient, prefetchUtils } from './stores/queryClient';
export * from './stores/zustand/appStore';
export * from './stores/zustand/reliabilityStore';
export * from './stores/zustand/uiStore';

// ===== EXTERNAL INTEGRATIONS =====

// Supabase client
export { supabase } from './supabase.js';

// ===== CONVENIENCE EXPORTS =====

// Commonly used utilities for easy access
export const LibUtils = {
  // From utils
  cn: require('./utils').cn,
  formatDateForDisplay: require('./utils').formatDateForDisplay,
  formatCurrency: require('./utils').formatCurrency,

  // From transformers
  TransformerUtils: require('./transformers').TransformerUtils,

  // From schemas
  SchemaUtils: require('./schemas').SchemaUtils,

  // From security
  SecurityUtils: require('./security').SecurityUtils,
} as const;

// Library constants for consistent usage
export const LIB_CONSTANTS = {
  API_VERSION: 'v1',
  DEFAULT_PAGE_SIZE: 10,
  MAX_PAGE_SIZE: 100,
  DEFAULT_TIMEOUT: 30000,
  CACHE_DURATION: 5 * 60 * 1000, // 5 minutes
} as const;
