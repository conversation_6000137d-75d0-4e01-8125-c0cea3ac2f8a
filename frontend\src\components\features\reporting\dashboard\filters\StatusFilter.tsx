// frontend/src/components/features/reporting/dashboard/filters/StatusFilter.tsx

import React from 'react';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { cn } from '@/lib/utils';
import { ChevronDown, X } from 'lucide-react';
import {
  useReportingFilters,
  useReportingFiltersActions,
  useReportingFiltersValidation,
} from '../../data/stores/useReportingFiltersStore';
import { DelegationStatusPrisma } from '../../data/types/reporting';

interface StatusFilterProps {
  compact?: boolean;
  className?: string;
}

/**
 * @component StatusFilter
 * @description Multi-select status filter component for reporting dashboard
 * 
 * Responsibilities:
 * - Provides status selection interface
 * - Integrates with reporting filters store
 * - Handles validation and error display
 * - Supports compact and full layouts
 * 
 * SOLID Principles Applied:
 * - SRP: Single responsibility of status filtering
 * - OCP: Open for extension via props
 * - DIP: Depends on filter store abstractions
 */
export const StatusFilter: React.FC<StatusFilterProps> = ({
  compact = false,
  className = '',
}) => {
  const filters = useReportingFilters();
  const { setStatus } = useReportingFiltersActions();
  const { validationErrors } = useReportingFiltersValidation();

  const [isOpen, setIsOpen] = React.useState(false);

  // Available status options
  const statusOptions = [
    { value: 'DRAFT' as DelegationStatusPrisma, label: 'Draft', color: 'bg-gray-100 text-gray-800' },
    { value: 'PENDING' as DelegationStatusPrisma, label: 'Pending', color: 'bg-yellow-100 text-yellow-800' },
    { value: 'APPROVED' as DelegationStatusPrisma, label: 'Approved', color: 'bg-blue-100 text-blue-800' },
    { value: 'IN_PROGRESS' as DelegationStatusPrisma, label: 'In Progress', color: 'bg-purple-100 text-purple-800' },
    { value: 'COMPLETED' as DelegationStatusPrisma, label: 'Completed', color: 'bg-green-100 text-green-800' },
    { value: 'CANCELLED' as DelegationStatusPrisma, label: 'Cancelled', color: 'bg-red-100 text-red-800' },
  ];

  // Handle status selection
  const handleStatusToggle = (status: DelegationStatusPrisma) => {
    const currentStatuses = filters.status;
    const isSelected = currentStatuses.includes(status);
    
    if (isSelected) {
      setStatus(currentStatuses.filter(s => s !== status));
    } else {
      setStatus([...currentStatuses, status]);
    }
  };

  // Handle select all/none
  const handleSelectAll = () => {
    setStatus(statusOptions.map(option => option.value));
  };

  const handleSelectNone = () => {
    setStatus([]);
  };

  // Get display text
  const getDisplayText = () => {
    const selectedCount = filters.status.length;
    if (selectedCount === 0) return 'All statuses';
    if (selectedCount === 1) {
      const selected = statusOptions.find(option => option.value === filters.status[0]);
      return selected?.label || 'Unknown';
    }
    return `${selectedCount} statuses`;
  };

  const hasError = validationErrors.status;

  if (compact) {
    return (
      <div className={cn('space-y-1', className)}>
        <Label className="text-xs font-medium">Status</Label>
        <Popover open={isOpen} onOpenChange={setIsOpen}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className={cn(
                'w-full justify-between text-left font-normal',
                hasError && 'border-red-500'
              )}
            >
              <span className="truncate">{getDisplayText()}</span>
              <ChevronDown className="ml-2 h-4 w-4 shrink-0" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-56 p-0" align="start">
            <div className="p-3">
              <div className="flex justify-between mb-3">
                <Button variant="ghost" size="sm" onClick={handleSelectAll}>
                  All
                </Button>
                <Button variant="ghost" size="sm" onClick={handleSelectNone}>
                  None
                </Button>
              </div>
              <div className="space-y-2">
                {statusOptions.map((option) => (
                  <div key={option.value} className="flex items-center space-x-2">
                    <Checkbox
                      id={`status-${option.value}`}
                      checked={filters.status.includes(option.value)}
                      onCheckedChange={() => handleStatusToggle(option.value)}
                    />
                    <Label
                      htmlFor={`status-${option.value}`}
                      className="text-sm font-normal cursor-pointer flex-1"
                    >
                      <Badge variant="secondary" className={cn('text-xs', option.color)}>
                        {option.label}
                      </Badge>
                    </Label>
                  </div>
                ))}
              </div>
            </div>
          </PopoverContent>
        </Popover>
        {hasError && (
          <p className="text-xs text-red-600">{hasError}</p>
        )}
      </div>
    );
  }

  return (
    <div className={cn('space-y-2', className)}>
      <Label className="text-sm font-medium">Status</Label>
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className={cn(
              'w-full justify-between text-left font-normal',
              hasError && 'border-red-500'
            )}
          >
            <span>{getDisplayText()}</span>
            <ChevronDown className="ml-2 h-4 w-4 shrink-0" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-64 p-0" align="start">
          <div className="p-4">
            <div className="flex justify-between items-center mb-4">
              <h4 className="font-medium text-sm">Select Statuses</h4>
              <div className="flex gap-2">
                <Button variant="ghost" size="sm" onClick={handleSelectAll}>
                  All
                </Button>
                <Button variant="ghost" size="sm" onClick={handleSelectNone}>
                  None
                </Button>
              </div>
            </div>
            <div className="space-y-3">
              {statusOptions.map((option) => (
                <div key={option.value} className="flex items-center space-x-3">
                  <Checkbox
                    id={`status-${option.value}`}
                    checked={filters.status.includes(option.value)}
                    onCheckedChange={() => handleStatusToggle(option.value)}
                  />
                  <Label
                    htmlFor={`status-${option.value}`}
                    className="text-sm font-normal cursor-pointer flex-1"
                  >
                    <Badge variant="secondary" className={cn('text-sm', option.color)}>
                      {option.label}
                    </Badge>
                  </Label>
                </div>
              ))}
            </div>
          </div>
        </PopoverContent>
      </Popover>
      
      {/* Selected status badges */}
      {filters.status.length > 0 && (
        <div className="flex flex-wrap gap-1">
          {filters.status.map((status) => {
            const option = statusOptions.find(opt => opt.value === status);
            if (!option) return null;
            
            return (
              <Badge
                key={status}
                variant="secondary"
                className={cn('text-xs pr-1', option.color)}
              >
                {option.label}
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-auto p-0 ml-1 hover:bg-transparent"
                  onClick={() => handleStatusToggle(status)}
                >
                  <X className="h-3 w-3" />
                </Button>
              </Badge>
            );
          })}
        </div>
      )}
      
      {hasError && (
        <p className="text-sm text-red-600">{hasError}</p>
      )}
    </div>
  );
};

export default StatusFilter;
