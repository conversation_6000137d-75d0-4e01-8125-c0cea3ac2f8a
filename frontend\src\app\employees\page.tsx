'use client';

import {
  ClipboardCheck, // Added from tasks page, might be useful
  PlusCircle,
  RefreshCw,
  Search,
  UsersRound,
} from 'lucide-react';
import Link from 'next/link';
import React, { useCallback, useEffect, useMemo, useState } from 'react';

import type { Employee } from '@/lib/types/domain'; // Use domain type directly

import EmployeeCard from '@/components/features/employees/EmployeeCard';
import ErrorBoundary from '@/components/error-boundaries/ErrorBoundary';
import { ActionButton } from '@/components/ui/action-button';
import { AppBreadcrumb } from '@/components/ui/app-breadcrumb';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { DataLoader, SkeletonLoader } from '@/components/ui/loading';
import { PageHeader } from '@/components/ui/PageHeader';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { useFormToast } from '@/hooks/forms/useFormToast';
import {
  EmployeeRoleSchema,
  EmployeeStatusSchema,
} from '@/lib/schemas/employeeSchemas';
import { useEmployees } from '@/lib/stores/queries/useEmployees'; // Import useEmployees hook
import { formatEmployeeStatusForDisplay } from '@/lib/utils/formattingUtils';

function EmployeeCardSkeleton() {
  return (
    <div className="flex h-full flex-col overflow-hidden rounded-lg border-border/60 bg-card shadow-lg">
      <div className="flex grow flex-col p-5">
        <div className="mb-2 flex items-start justify-between">
          <Skeleton className="h-8 w-3/5 bg-muted/50" />
          <Skeleton className="h-5 w-1/4 rounded-full bg-muted/50" />
        </div>
        <Skeleton className="mb-1 h-4 w-1/2 bg-muted/50" />
        <Skeleton className="mb-3 h-4 w-1/3 bg-muted/50" />
        <Skeleton className="my-3 h-px w-full bg-border/50" />
        <div className="grow space-y-2.5">
          {Array.from({ length: 3 }).map((_, i) => (
            <div className="flex items-center" key={i}>
              <Skeleton className="mr-2.5 size-5 rounded-full bg-muted/50" />
              <Skeleton className="h-5 w-2/3 bg-muted/50" />
            </div>
          ))}
        </div>
      </div>
      <div className="border-t border-border/60 bg-muted/20 p-4">
        <Skeleton className="h-10 w-full bg-muted/50" />
      </div>
    </div>
  );
}

const EmployeesPageContent = () => {
  const { showFormSuccess, showFormError } = useFormToast();

  const {
    data: employees = [],
    error: employeesError,
    isLoading: isLoadingEmployees,
    refetch: refetchEmployees,
  } = useEmployees();

  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [departmentFilter, setDepartmentFilter] = useState<string>('all');
  const [roleFilter, setRoleFilter] = useState<string>('all');

  const uniqueDepartments = useMemo(() => {
    return [
      ...new Set(employees.map(e => e.department).filter(Boolean)),
    ].sort();
  }, [employees]);

  // 🔧 FIX: Replace useEffect + useState with useMemo to prevent infinite loop
  // This prevents the "Maximum update depth exceeded" error by avoiding unstable array references
  const filteredEmployees = useMemo(() => {
    let tempEmployees = [...employees];
    const lowercasedSearch = searchTerm.toLowerCase();

    if (statusFilter !== 'all') {
      tempEmployees = tempEmployees.filter(emp => emp.status === statusFilter);
    }
    if (departmentFilter !== 'all') {
      tempEmployees = tempEmployees.filter(
        emp => emp.department === departmentFilter
      );
    }
    if (roleFilter !== 'all') {
      tempEmployees = tempEmployees.filter(emp => emp.role === roleFilter);
    }

    if (lowercasedSearch) {
      tempEmployees = tempEmployees.filter(employee => {
        return (
          (employee.name || '').toLowerCase().includes(lowercasedSearch) ||
          (employee.contactInfo || '')
            .toLowerCase()
            .includes(lowercasedSearch) ||
          (employee.position || '').toLowerCase().includes(lowercasedSearch) ||
          (employee.department || '')
            .toLowerCase()
            .includes(lowercasedSearch) ||
          (employee.role || '').toLowerCase().includes(lowercasedSearch) ||
          (employee.contactEmail || '')
            .toLowerCase()
            .includes(lowercasedSearch) ||
          (employee.contactPhone || '')
            .toLowerCase()
            .includes(lowercasedSearch) ||
          (employee.contactMobile || '')
            .toLowerCase()
            .includes(lowercasedSearch) ||
          (employee.skills &&
            employee.skills.some(skill =>
              (skill || '').toLowerCase().includes(lowercasedSearch)
            )) ||
          (employee.role === 'driver' &&
            employee.availability &&
            (employee.availability || '')
              .toLowerCase()
              .includes(lowercasedSearch)) ||
          (employee.role === 'driver' &&
            employee.currentLocation &&
            (employee.currentLocation || '')
              .toLowerCase()
              .includes(lowercasedSearch))
        );
      });
    }

    return tempEmployees;
  }, [searchTerm, employees, statusFilter, departmentFilter, roleFilter]);

  const handleRefresh = useCallback(async () => {
    try {
      await refetchEmployees();
      showFormSuccess({
        successTitle: 'Refresh Complete',
        successDescription: 'Employee list has been updated.',
      });
    } catch (error) {
      console.error('Error refreshing employees:', error);
      showFormError(error as Error, {
        errorTitle: 'Refresh Failed',
        errorDescription: 'Could not update employee list. Please try again.',
      });
    }
  }, [refetchEmployees, showFormSuccess, showFormError]);

  const activeFilters =
    searchTerm ||
    statusFilter !== 'all' ||
    departmentFilter !== 'all' ||
    roleFilter !== 'all';

  return (
    <div className="space-y-6">
      <AppBreadcrumb homeHref="/" homeLabel="Dashboard" />
      <PageHeader
        description="Oversee employee profiles, roles, status, and assignments."
        icon={UsersRound}
        title="Manage Employees"
      >
        <div className="flex gap-2">
          <ActionButton
            actionType="tertiary"
            icon={
              <RefreshCw
                className={`size-4 ${isLoadingEmployees ? 'animate-spin' : ''}`}
              />
            }
            isLoading={isLoadingEmployees} // Use isLoadingEmployees
            loadingText={'Refreshing...'}
            onClick={handleRefresh}
          >
            {'Refresh'}
          </ActionButton>
          <ActionButton
            actionType="primary"
            asChild
            icon={<PlusCircle className="size-4" />}
          >
            <Link href="/employees/new">Add New Employee</Link>
          </ActionButton>
        </div>
      </PageHeader>

      <Card className="relative mb-6 p-4 shadow">
        {isLoadingEmployees && (
          <div className="absolute right-4 top-4 flex items-center text-xs text-muted-foreground">
            <RefreshCw className="mr-1 size-3 animate-spin" />
            {'Refreshing...'}
          </div>
        )}
        {/* Real-time status indicators removed as they are not directly from React Query */}
        <CardContent className="pt-4">
          <div className="grid grid-cols-1 items-end gap-4 md:grid-cols-2 lg:grid-cols-4">
            <div className="relative lg:col-span-1">
              <Label
                className="mb-1 block text-sm font-medium text-muted-foreground"
                htmlFor="search-employees"
              >
                Search Employees
              </Label>
              <Search className="absolute left-3 top-[calc(50%_-_0.5rem_+_12px)] size-5 text-muted-foreground" />
              <Input
                className="w-full pl-10"
                id="search-employees"
                onChange={e => setSearchTerm(e.target.value)}
                placeholder="Name, Role, Dept, Skill..."
                type="text"
                value={searchTerm}
              />
            </div>
            <div>
              <Label
                className="mb-1 block text-sm font-medium text-muted-foreground"
                htmlFor="status-filter"
              >
                Filter by Status
              </Label>
              <Select onValueChange={setStatusFilter} value={statusFilter}>
                <SelectTrigger id="status-filter">
                  <SelectValue placeholder="All Statuses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  {EmployeeStatusSchema.options.map(s => (
                    <SelectItem key={s} value={s}>
                      {formatEmployeeStatusForDisplay(s)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label
                className="mb-1 block text-sm font-medium text-muted-foreground"
                htmlFor="department-filter"
              >
                Filter by Department
              </Label>
              <Select
                onValueChange={setDepartmentFilter}
                value={departmentFilter}
              >
                <SelectTrigger id="department-filter">
                  <SelectValue placeholder="All Departments" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Departments</SelectItem>
                  {uniqueDepartments.map(dep => (
                    <SelectItem key={dep || ''} value={dep || ''}>
                      {dep}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label
                className="mb-1 block text-sm font-medium text-muted-foreground"
                htmlFor="role-filter"
              >
                Filter by Role
              </Label>
              <Select onValueChange={setRoleFilter} value={roleFilter}>
                <SelectTrigger id="role-filter">
                  <SelectValue placeholder="All Roles" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Roles</SelectItem>
                  {EmployeeRoleSchema.options.map(role => (
                    <SelectItem key={role} value={role}>
                      {role.charAt(0).toUpperCase() +
                        role.slice(1).replace('_', ' ')}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>
      <DataLoader
        data={filteredEmployees}
        emptyComponent={
          <div className="rounded-lg bg-card py-12 text-center shadow-md">
            <UsersRound className="mx-auto mb-6 size-16 text-muted-foreground" />
            <h3 className="mb-2 text-2xl font-semibold text-foreground">
              {activeFilters
                ? 'No Employees Match Your Filters'
                : 'No Employees Registered Yet'}
            </h3>
            <p className="mx-auto mb-6 mt-2 max-w-md text-muted-foreground">
              {activeFilters
                ? 'Try adjusting your search or filter criteria.'
                : 'Get started by adding an employee.'}
            </p>
            {!activeFilters && (
              <ActionButton
                actionType="primary"
                asChild
                icon={<PlusCircle className="size-4" />}
                size="lg"
              >
                <Link href="/employees/add">Add Your First Employee</Link>
              </ActionButton>
            )}
          </div>
        }
        error={employeesError ? employeesError.message : null} // Use employeesError
        isLoading={isLoadingEmployees} // Use isLoadingEmployees
        loadingComponent={
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
            {Array.from({ length: 3 }).map((_, i) => (
              <EmployeeCardSkeleton key={i} />
            ))}
          </div>
        }
        onRetry={handleRefresh} // Use handleRefresh for retry
      >
        {employeeData => (
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
            {employeeData.map(employee => (
              <EmployeeCard employee={employee} key={employee.id} />
            ))}
          </div>
        )}
      </DataLoader>
    </div>
  );
};

export default function EmployeesPage() {
  return (
    <ErrorBoundary>
      <EmployeesPageContent />
    </ErrorBoundary>
  );
}
