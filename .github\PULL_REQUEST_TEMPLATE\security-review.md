---
name: 🔒 Security Review PR Template
about: Template for security-sensitive pull requests
title: 'security: [Brief description of security changes]'
labels: ['security-review', 'needs-security-approval']
assignees: []
---

# 🔒 Security Review Required

## 📋 Description

Brief description of the security-related changes in this PR.

## 🔍 Type of Security Change

- [ ] Authentication/Authorization changes
- [ ] JWT/Token management updates
- [ ] Database security modifications
- [ ] API security enhancements
- [ ] Input validation improvements
- [ ] Encryption/Decryption changes
- [ ] Session management updates
- [ ] RBAC/Permission system changes
- [ ] Security vulnerability fix
- [ ] Other: _______________

## 🛡️ Security Impact Assessment

### High Impact Areas
- [ ] User authentication flow
- [ ] Admin privilege escalation
- [ ] Data access controls
- [ ] API endpoint security
- [ ] Database access patterns

### Medium Impact Areas
- [ ] Session management
- [ ] Input validation
- [ ] Error handling
- [ ] Logging and monitoring
- [ ] Configuration changes

### Low Impact Areas
- [ ] UI/UX security improvements
- [ ] Documentation updates
- [ ] Test security enhancements

## 🧪 Testing Completed

### Security Tests
- [ ] Authentication flow tests
- [ ] Authorization tests
- [ ] JWT token validation tests
- [ ] RBAC permission tests
- [ ] Input validation tests
- [ ] SQL injection prevention tests
- [ ] XSS prevention tests

### Integration Tests
- [ ] End-to-end security flow tests
- [ ] API security tests
- [ ] Database security tests
- [ ] Session management tests

### Manual Testing
- [ ] Security penetration testing
- [ ] User role testing
- [ ] Edge case security testing
- [ ] Error handling security testing

## 🔒 Security Checklist

### Authentication & Authorization
- [ ] JWT tokens properly validated and signed
- [ ] Refresh token mechanism is secure
- [ ] Token expiration properly handled
- [ ] RBAC properly implemented and tested
- [ ] No privilege escalation vulnerabilities
- [ ] Session management is secure

### Input Validation & Sanitization
- [ ] All user inputs are validated
- [ ] SQL injection prevention implemented
- [ ] XSS prevention measures in place
- [ ] CSRF protection enabled
- [ ] File upload security (if applicable)

### Data Protection
- [ ] Sensitive data is encrypted at rest
- [ ] Sensitive data is encrypted in transit
- [ ] No sensitive data in logs
- [ ] Proper data access controls
- [ ] PII handling compliance

### Infrastructure Security
- [ ] Security headers properly configured
- [ ] CORS settings are restrictive
- [ ] Rate limiting implemented
- [ ] Error messages don't leak sensitive info
- [ ] Audit logging is comprehensive

### Code Security
- [ ] No hardcoded credentials or secrets
- [ ] Dependencies are up to date
- [ ] Security linting passes
- [ ] Code follows security best practices
- [ ] Proper error handling

## 📊 Security Scan Results

- [ ] CodeQL security scan passed
- [ ] Dependency vulnerability scan passed
- [ ] SAST (Static Application Security Testing) passed
- [ ] Secret scanning passed
- [ ] Container security scan passed (if applicable)

## 🔍 Code Review Requirements

### Required Reviewers
- [ ] Security team member
- [ ] Senior developer
- [ ] DevOps/Infrastructure team (if applicable)

### Review Focus Areas
- [ ] Authentication logic
- [ ] Authorization checks
- [ ] Input validation
- [ ] Error handling
- [ ] Logging and monitoring
- [ ] Configuration security

## 📚 Documentation Updates

- [ ] Security documentation updated
- [ ] API documentation reflects security changes
- [ ] Deployment documentation updated
- [ ] Security incident response plan updated (if needed)
- [ ] User documentation updated (if needed)

## 🚀 Deployment Considerations

### Pre-deployment
- [ ] Security configuration reviewed
- [ ] Environment variables secured
- [ ] Database migration security reviewed
- [ ] Backup and rollback plan prepared

### Post-deployment
- [ ] Security monitoring alerts configured
- [ ] Audit logging verified
- [ ] Performance impact assessed
- [ ] Security metrics baseline established

## 🔗 Related Issues/PRs

- Closes #[issue_number]
- Related to #[issue_number]
- Depends on #[pr_number]

## 📝 Additional Notes

Any additional security considerations, known limitations, or future security improvements planned.

---

**⚠️ Security Review Mandatory**: This PR cannot be merged without explicit approval from the security team.

**🔒 Confidentiality**: If this PR contains sensitive security information, ensure it's marked as confidential and limit access appropriately.
