/**
 * @file Notification display component for Zustand notifications
 * @module components/ui/NotificationDisplay
 */

'use client';

import {
  AlertCircle,
  AlertTriangle,
  Briefcase,
  Car,
  CheckCircle,
  Info,
  Settings,
  Users,
  X,
} from 'lucide-react';
import React from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { useAppStore } from '@/lib/stores/zustand/appStore';
import { cn } from '@/lib/utils';

/**
 * Get icon for notification type
 */
const getNotificationIcon = (type: string) => {
  switch (type) {
    case 'delegation-update': {
      return <Briefcase className="size-4" />;
    }
    case 'employee-update': {
      return <Users className="size-4" />;
    }
    case 'error': {
      return <AlertCircle className="size-4" />;
    }
    case 'success': {
      return <CheckCircle className="size-4" />;
    }
    case 'task-assigned': {
      return <CheckCircle className="size-4" />;
    }
    case 'vehicle-maintenance': {
      return <Car className="size-4" />;
    }
    case 'warning': {
      return <AlertTriangle className="size-4" />;
    }
    default: {
      return <Info className="size-4" />;
    }
  }
};

/**
 * Get notification styling based on type
 */
const getNotificationStyles = (type: string) => {
  switch (type) {
    case 'delegation-update': {
      return 'border-blue-200 bg-blue-50 text-blue-800 dark:border-blue-800 dark:bg-blue-950 dark:text-blue-200';
    }
    case 'employee-update': {
      return 'border-teal-200 bg-teal-50 text-teal-800 dark:border-teal-800 dark:bg-teal-950 dark:text-teal-200';
    }
    case 'error': {
      return 'border-red-200 bg-red-50 text-red-800 dark:border-red-800 dark:bg-red-950 dark:text-red-200';
    }
    case 'success': {
      return 'border-green-200 bg-green-50 text-green-800 dark:border-green-800 dark:bg-green-950 dark:text-green-200';
    }
    case 'task-assigned': {
      return 'border-indigo-200 bg-indigo-50 text-indigo-800 dark:border-indigo-800 dark:bg-indigo-950 dark:text-indigo-200';
    }
    case 'vehicle-maintenance': {
      return 'border-purple-200 bg-purple-50 text-purple-800 dark:border-purple-800 dark:bg-purple-950 dark:text-purple-200';
    }
    case 'warning': {
      return 'border-yellow-200 bg-yellow-50 text-yellow-800 dark:border-yellow-800 dark:bg-yellow-950 dark:text-yellow-200';
    }
    default: {
      return 'border-gray-200 bg-gray-50 text-gray-800 dark:border-gray-800 dark:bg-gray-950 dark:text-gray-200';
    }
  }
};

/**
 * Get badge variant for notification type
 */
const getBadgeVariant = (type: string) => {
  switch (type) {
    case 'error': {
      return 'destructive';
    }
    case 'success': {
      return 'default';
    }
    case 'warning': {
      return 'secondary';
    }
    default: {
      return 'outline';
    }
  }
};

/**
 * Notification display component
 */
export const NotificationDisplay: React.FC = () => {
  const {
    clearAllNotifications,
    markNotificationAsRead,
    notifications,
    removeNotification,
    unreadNotificationCount,
  } = useAppStore();

  if (notifications.length === 0) {
    return null;
  }

  return (
    <div className="fixed right-4 top-4 z-50 w-96 max-w-sm space-y-2">
      {/* Header with clear all button */}
      {notifications.length > 1 && (
        <div className="mb-2 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium">Notifications</span>
            {unreadNotificationCount() > 0 && (
              <Badge className="text-xs" variant="destructive">
                {unreadNotificationCount()}
              </Badge>
            )}
          </div>
          <Button
            className="text-xs"
            onClick={clearAllNotifications}
            size="sm"
            variant="ghost"
          >
            Clear All
          </Button>
        </div>
      )}

      {/* Notification list */}
      <div className="max-h-96 space-y-2 overflow-y-auto">
        {notifications.slice(-5).map(notification => (
          <Card
            className={cn(
              'relative transition-all duration-300 hover:shadow-md',
              getNotificationStyles(notification.type),
              !notification.read &&
                'ring-2 ring-offset-2 ring-offset-background'
            )}
            key={notification.id}
          >
            <CardContent className="p-4">
              <div className="flex items-start gap-3">
                {/* Icon */}
                <div className="mt-0.5 shrink-0">
                  {getNotificationIcon(notification.type)}
                </div>

                {/* Content */}
                <div className="min-w-0 flex-1">
                  <div className="flex items-start justify-between gap-2">
                    <div className="flex-1">
                      {/* Type badge */}
                      <Badge
                        className="mb-1 text-xs"
                        variant={getBadgeVariant(notification.type)}
                      >
                        {notification.type.replace('-', ' ')}
                      </Badge>

                      {/* Message */}
                      <p className="text-sm font-medium leading-tight">
                        {notification.message}
                      </p>

                      {/* Timestamp */}
                      <p className="mt-1 text-xs opacity-75">
                        {new Date(notification.timestamp).toLocaleString()}
                      </p>

                      {/* Category and action URL */}
                      {(notification.category || notification.actionUrl) && (
                        <div className="mt-2 flex items-center gap-2">
                          {notification.category && (
                            <Badge className="text-xs" variant="outline">
                              {notification.category}
                            </Badge>
                          )}
                          {notification.actionUrl && (
                            <Button
                              className="h-auto p-0 text-xs"
                              onClick={() =>
                                window.open(notification.actionUrl, '_blank')
                              }
                              size="sm"
                              variant="link"
                            >
                              View Details
                            </Button>
                          )}
                        </div>
                      )}
                    </div>

                    {/* Actions */}
                    <div className="flex flex-col gap-1">
                      <Button
                        className="size-6 p-0"
                        onClick={() => removeNotification(notification.id)}
                        size="sm"
                        variant="ghost"
                      >
                        <X className="size-3" />
                      </Button>

                      {!notification.read && (
                        <Button
                          className="size-6 p-0"
                          onClick={() =>
                            markNotificationAsRead(notification.id)
                          }
                          size="sm"
                          title="Mark as read"
                          variant="ghost"
                        >
                          <CheckCircle className="size-3" />
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

/**
 * Notification toast component for temporary notifications
 */
export const NotificationToast: React.FC<{
  notification: {
    id: string;
    message: string;
    timestamp: string;
    type: string;
  };
  onDismiss: (id: string) => void;
}> = ({ notification, onDismiss }) => {
  // Auto-dismiss after 5 seconds
  React.useEffect(() => {
    const timer = setTimeout(() => {
      onDismiss(notification.id);
    }, 5000);

    return () => clearTimeout(timer);
  }, [notification.id, onDismiss]);

  return (
    <Card
      className={cn(
        'fixed bottom-4 right-4 z-50 w-80 transition-all duration-300 animate-in slide-in-from-right',
        getNotificationStyles(notification.type)
      )}
    >
      <CardContent className="p-4">
        <div className="flex items-start gap-3">
          <div className="shrink-0">
            {getNotificationIcon(notification.type)}
          </div>
          <div className="flex-1">
            <p className="text-sm font-medium">{notification.message}</p>
          </div>
          <Button
            className="size-6 p-0"
            onClick={() => onDismiss(notification.id)}
            size="sm"
            variant="ghost"
          >
            <X className="size-3" />
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
