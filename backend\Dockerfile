# PHASE 1 SECURITY HARDENING: Backend Dockerfile - SECURITY HARDENED VERSION
FROM node:18-alpine AS builder

# Security: Install security updates
RUN apk update && apk upgrade && \
    apk add --no-cache dumb-init openssl && \
    rm -rf /var/cache/apk/*

# Security: Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S workhub -u 1001

WORKDIR /app

# Copy package files with proper ownership
COPY --chown=workhub:nodejs package*.json ./

# Install ALL dependencies (including dev) for build process with retry logic
RUN npm config set fetch-retry-mintimeout 20000 && \
    npm config set fetch-retry-maxtimeout 120000 && \
    npm config set fetch-retries 5 && \
    npm ci && \
    npm cache clean --force

# Copy source code with proper ownership
COPY --chown=workhub:nodejs . .

# Generate Prisma client
RUN npx prisma generate

# Build the application
RUN npm run build

# Remove dev dependencies after build to reduce image size
RUN npm prune --omit=dev

# Production stage
FROM node:18-alpine AS runtime

# Security: Install security updates and dumb-init
RUN apk update && apk upgrade && \
    apk add --no-cache dumb-init openssl wget && \
    rm -rf /var/cache/apk/*

# Security: Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S workhub -u 1001

WORKDIR /app

# Security: Copy files with proper ownership
COPY --from=builder --chown=workhub:nodejs /app/dist ./dist
COPY --from=builder --chown=workhub:nodejs /app/node_modules ./node_modules
COPY --from=builder --chown=workhub:nodejs /app/package.json ./package.json
COPY --from=builder --chown=workhub:nodejs /app/prisma ./prisma
COPY --from=builder --chown=workhub:nodejs /app/src/generated ./dist/generated

# Security: Create required directories with proper ownership
RUN mkdir -p /app/logs /app/src && \
    chown -R workhub:nodejs /app/logs /app/src

# Security: Remove package manager tools to reduce attack surface
RUN apk del apk-tools

# Security: Switch to non-root user
USER workhub

# Expose the port the app runs on
EXPOSE 3001

# Security: Use dumb-init for proper signal handling
ENTRYPOINT ["dumb-init", "--"]

# Security: Don't expose sensitive information in logs - removed DATABASE_URL echo
# Skip prisma db push for staging/production - database should already be configured
CMD ["sh", "-c", "node dist/server.js"]

# Security: Add labels for scanning and compliance
LABEL security.scan="enabled"
LABEL security.last-updated="2025-05-24"
LABEL security.non-root="true"
LABEL security.phase="PHASE-1-HARDENED"
LABEL maintainer="WorkHub Security Team"
