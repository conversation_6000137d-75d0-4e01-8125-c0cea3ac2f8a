'use client';

import { useRouter } from 'next/navigation';
import React from 'react';

import { LoginForm } from '../../components/auth/loginForm';
import { AuthProvider } from '../../contexts/AuthContext';

/**
 * Modern Login Page
 *
 * A dedicated page for user authentication featuring:
 * - Modern, minimalist design
 * - Responsive layout for all devices
 * - Enhanced user experience
 * - Security-focused UI
 */
export default function LoginPage() {
  const router = useRouter();

  const handleLoginSuccess = () => {
    // Redirect to dashboard after successful login
    router.push('/');
  };

  const handleForgotPassword = () => {
    // TODO: Implement forgot password flow
    console.log('Forgot password clicked');
    // router.push('/forgot-password');
  };

  const handleSignUp = () => {
    // TODO: Implement sign up flow
    console.log('Sign up clicked');
    // router.push('/signup');
  };

  return (
    <AuthProvider>
      <LoginForm
        onForgotPassword={handleForgotPassword}
        onSignUp={handleSignUp}
        onSuccess={handleLoginSuccess}
      />
    </AuthProvider>
  );
}
