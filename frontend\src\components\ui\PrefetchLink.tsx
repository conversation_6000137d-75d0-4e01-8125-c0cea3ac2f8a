/**
 * @file Enhanced Link component with intelligent prefetching
 * @module components/ui/PrefetchLink
 */

import type { LinkProps } from 'next/link';

import Link from 'next/link';
import React, { useCallback, useRef } from 'react';

import { useNavigationPrefetch } from '@/hooks/api/useNavigationPrefetch';

interface PrefetchLinkProps extends Omit<LinkProps, 'href'> {
  children: React.ReactNode;
  className?: string;
  href: string;
  onPrefetchComplete?: () => void;
  onPrefetchError?: (error: Error) => void;
  onPrefetchStart?: () => void;
  prefetchDelay?: number;
  prefetchStrategy?: 'hover' | 'immediate' | 'none' | 'visible';
}

/**
 * Enhanced Link component with intelligent prefetching capabilities
 *
 * Features:
 * - Hover prefetching (default)
 * - Intersection Observer prefetching (when link becomes visible)
 * - Immediate prefetching
 * - Configurable prefetch strategies
 * - Error handling and callbacks
 */
export const PrefetchLink: React.FC<PrefetchLinkProps> = ({
  children,
  className,
  href,
  onPrefetchComplete,
  onPrefetchError,
  onPrefetchStart,
  prefetchDelay = 100,
  prefetchStrategy = 'hover',
  ...linkProps
}) => {
  const { prefetchRoute } = useNavigationPrefetch();
  const linkRef = useRef<HTMLAnchorElement>(null);
  const prefetchTimeoutRef = useRef<NodeJS.Timeout>();
  const hasPrefetched = useRef(false);
  const observerRef = useRef<IntersectionObserver>();

  /**
   * Execute prefetch with error handling and callbacks
   */
  const executePrefetch = useCallback(async () => {
    if (hasPrefetched.current) return;

    try {
      hasPrefetched.current = true;
      onPrefetchStart?.();

      await prefetchRoute(href);

      onPrefetchComplete?.();
    } catch (error) {
      hasPrefetched.current = false; // Allow retry on error
      onPrefetchError?.(error as Error);
      console.warn(`Prefetch failed for ${href}:`, error);
    }
  }, [
    href,
    prefetchRoute,
    onPrefetchStart,
    onPrefetchComplete,
    onPrefetchError,
  ]);

  /**
   * Handle hover prefetching
   */
  const handleMouseEnter = useCallback(() => {
    if (prefetchStrategy !== 'hover') return;

    // Clear any existing timeout
    if (prefetchTimeoutRef.current) {
      clearTimeout(prefetchTimeoutRef.current);
    }

    // Debounce prefetch to avoid excessive requests
    prefetchTimeoutRef.current = setTimeout(() => {
      executePrefetch();
    }, prefetchDelay);
  }, [prefetchStrategy, prefetchDelay, executePrefetch]);

  /**
   * Handle mouse leave (cleanup)
   */
  const handleMouseLeave = useCallback(() => {
    if (prefetchTimeoutRef.current) {
      clearTimeout(prefetchTimeoutRef.current);
      prefetchTimeoutRef.current = undefined;
    }
  }, []);

  /**
   * Set up intersection observer for visible prefetching
   */
  const setupIntersectionObserver = useCallback(() => {
    if (prefetchStrategy !== 'visible' || !linkRef.current) return;

    observerRef.current = new IntersectionObserver(
      entries => {
        for (const entry of entries) {
          if (entry.isIntersecting) {
            executePrefetch();
            // Disconnect after first prefetch
            observerRef.current?.disconnect();
          }
        }
      },
      {
        rootMargin: '50px', // Start prefetching 50px before link becomes visible
        threshold: 0.1,
      }
    );

    observerRef.current.observe(linkRef.current);
  }, [prefetchStrategy, executePrefetch]);

  /**
   * Handle immediate prefetching
   */
  React.useEffect(() => {
    if (prefetchStrategy === 'immediate') {
      executePrefetch();
    }
  }, [prefetchStrategy, executePrefetch]);

  /**
   * Set up intersection observer
   */
  React.useEffect(() => {
    setupIntersectionObserver();

    return () => {
      observerRef.current?.disconnect();
    };
  }, [setupIntersectionObserver]);

  /**
   * Cleanup on unmount
   */
  React.useEffect(() => {
    return () => {
      if (prefetchTimeoutRef.current) {
        clearTimeout(prefetchTimeoutRef.current);
      }
      observerRef.current?.disconnect();
    };
  }, []);

  return (
    <Link
      className={className}
      href={href}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      ref={linkRef}
      {...linkProps}
    >
      {children}
    </Link>
  );
};

/**
 * Specialized link components for common use cases
 */

/**
 * Navigation link with hover prefetching (for main navigation)
 */
export const NavLink: React.FC<
  Omit<PrefetchLinkProps, 'prefetchStrategy'>
> = props => (
  <PrefetchLink {...props} prefetchDelay={150} prefetchStrategy="hover" />
);

/**
 * Card link with visible prefetching (for card grids/lists)
 */
export const CardLink: React.FC<
  Omit<PrefetchLinkProps, 'prefetchStrategy'>
> = props => <PrefetchLink {...props} prefetchStrategy="visible" />;

/**
 * Action link with immediate prefetching (for critical actions)
 */
export const ActionLink: React.FC<
  Omit<PrefetchLinkProps, 'prefetchStrategy'>
> = props => <PrefetchLink {...props} prefetchStrategy="immediate" />;

/**
 * Simple link without prefetching (for external links or when prefetching is not desired)
 */
export const SimpleLink: React.FC<
  Omit<PrefetchLinkProps, 'prefetchStrategy'>
> = props => <PrefetchLink {...props} prefetchStrategy="none" />;

/**
 * Hook for programmatic prefetching in components
 */
export const usePrefetchOnMount = (routes: string[], enabled = true) => {
  const { prefetchUserJourney } = useNavigationPrefetch();

  React.useEffect(() => {
    if (!enabled || routes.length === 0) return;

    const prefetchRoutes = async () => {
      try {
        await prefetchUserJourney(routes);
      } catch (error) {
        console.warn('Failed to prefetch routes on mount:', error);
      }
    };

    // Delay prefetching slightly to not block initial render
    const timeoutId = setTimeout(prefetchRoutes, 100);

    return () => clearTimeout(timeoutId);
  }, [routes, enabled, prefetchUserJourney]);
};

/**
 * Performance monitoring component for prefetch links
 */
export const PrefetchPerformanceMonitor: React.FC<{
  children: React.ReactNode;
}> = ({ children }) => {
  const [prefetchStats, setPrefetchStats] = React.useState({
    failed: 0,
    successful: 0,
    total: 0,
  });

  const handlePrefetchStart = useCallback(() => {
    setPrefetchStats(prev => ({ ...prev, total: prev.total + 1 }));
  }, []);

  const handlePrefetchComplete = useCallback(() => {
    setPrefetchStats(prev => ({ ...prev, successful: prev.successful + 1 }));
  }, []);

  const handlePrefetchError = useCallback(() => {
    setPrefetchStats(prev => ({ ...prev, failed: prev.failed + 1 }));
  }, []);

  // Log stats in development
  React.useEffect(() => {
    if (process.env.NODE_ENV === 'development' && prefetchStats.total > 0) {
      console.log('Prefetch Stats:', prefetchStats);
    }
  }, [prefetchStats]);

  return (
    <div>
      {React.Children.map(children, child => {
        if (React.isValidElement(child) && child.type === PrefetchLink) {
          return React.cloneElement(
            child as React.ReactElement<PrefetchLinkProps>,
            {
              onPrefetchComplete: handlePrefetchComplete,
              onPrefetchError: handlePrefetchError,
              onPrefetchStart: handlePrefetchStart,
            }
          );
        }
        return child;
      })}
    </div>
  );
};
