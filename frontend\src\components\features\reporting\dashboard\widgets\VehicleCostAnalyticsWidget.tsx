/**
 * @file VehicleCostAnalyticsWidget.tsx
 * @description Vehicle cost analytics widget following existing patterns and SOLID principles
 */

import React, { useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import {
  DollarSign,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  PieChart,
  BarChart3,
  MoreHorizontal,
} from 'lucide-react';
import {
  <PERSON><PERSON><PERSON> as Recharts<PERSON>ie<PERSON>hart,
  Pie,
  Cell,
  ResponsiveContainer,
  Tooltip,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
} from 'recharts';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';
import { useVehicleAnalytics } from '../../hooks/useVehicleAnalytics';
import { DataLoader } from '@/components/ui/data-loader';
import { ErrorDisplay } from '@/components/ui/error-display';
import type { ReportingFilters } from '../../data/types/reporting';

/**
 * Props interface for VehicleCostAnalyticsWidget
 */
interface VehicleCostAnalyticsWidgetProps {
  filters?: ReportingFilters;
  className?: string;
  showTrend?: boolean;
  compact?: boolean;
}

/**
 * Cost metric card component
 */
interface CostMetricCardProps {
  label: string;
  value: string;
  icon: React.ReactNode;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  variant?: 'default' | 'warning' | 'success' | 'destructive';
}

const CostMetricCard: React.FC<CostMetricCardProps> = ({
  label,
  value,
  icon,
  trend,
  variant = 'default',
}) => {
  const variantStyles = {
    default: 'border-gray-200',
    warning: 'border-orange-200 bg-orange-50',
    success: 'border-green-200 bg-green-50',
    destructive: 'border-red-200 bg-red-50',
  };

  return (
    <div className={cn('p-4 border rounded-lg', variantStyles[variant])}>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          {icon}
          <span className="text-sm font-medium text-gray-600">{label}</span>
        </div>
        {trend && (
          <div className="flex items-center gap-1">
            {trend.isPositive ? (
              <TrendingUp className="h-3 w-3 text-green-600" />
            ) : (
              <TrendingDown className="h-3 w-3 text-red-600" />
            )}
            <Badge
              variant={trend.isPositive ? 'default' : 'destructive'}
              className="text-xs"
            >
              {trend.isPositive ? '+' : ''}
              {trend.value}%
            </Badge>
          </div>
        )}
      </div>
      <div className="mt-2">
        <span className="text-2xl font-bold">{value}</span>
      </div>
    </div>
  );
};

/**
 * Custom tooltip for pie chart
 */
const CustomPieTooltip = ({ active, payload }: any) => {
  if (active && payload && payload.length) {
    const data = payload[0].payload;
    return (
      <div className="bg-white p-3 border rounded-lg shadow-lg">
        <p className="font-medium">{data.name}</p>
        <p className="text-sm text-gray-600">
          Cost:{' '}
          <span className="font-medium">${data.value.toLocaleString()}</span>
        </p>
        <p className="text-sm text-gray-600">
          Percentage: <span className="font-medium">{data.percentage}%</span>
        </p>
      </div>
    );
  }
  return null;
};

/**
 * VehicleCostAnalyticsWidget Component
 *
 * Displays vehicle cost analytics and trends following existing widget patterns.
 *
 * Responsibilities:
 * - Display cost analytics in widget format
 * - Show cost breakdown and trends
 * - Follow established widget composition patterns
 * - Maintain consistent styling and behavior
 *
 * SOLID Principles Applied:
 * - SRP: Single responsibility of displaying cost analytics
 * - OCP: Open for extension via props configuration
 * - DIP: Depends on existing widget framework abstractions
 */
export const VehicleCostAnalyticsWidget: React.FC<
  VehicleCostAnalyticsWidgetProps
> = ({ filters, className = '', showTrend = true, compact = false }) => {
  // Use existing hook patterns
  const {
    data: vehicleAnalytics,
    isLoading,
    error,
  } = useVehicleAnalytics(filters);
  const costAnalysis = vehicleAnalytics?.costAnalysis;

  // Transform data for charts
  const pieChartData = useMemo(() => {
    if (!costAnalysis?.costByType) return [];

    const colors = ['#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6'];

    return costAnalysis.costByType.map((item, index) => ({
      name: item.type,
      value: item.cost,
      percentage: Math.round((item.cost / costAnalysis.totalCost) * 100),
      color: colors[index % colors.length],
    }));
  }, [costAnalysis]);

  const trendData = useMemo(() => {
    if (!costAnalysis?.monthlyTrend) return [];

    return costAnalysis.monthlyTrend.map((item, index) => ({
      month: format(new Date(item.month), 'MMM'),
      cost: item.cost,
      services: index + 1, // Placeholder since serviceCount doesn't exist
    }));
  }, [costAnalysis]);

  // Handle loading state
  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            Cost Analytics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <DataLoader isLoading={true} data={null} error={null}>
            {() => null}
          </DataLoader>
        </CardContent>
      </Card>
    );
  }

  // Handle error state
  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            Cost Analytics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ErrorDisplay error={error} />
        </CardContent>
      </Card>
    );
  }

  // Calculate derived metrics
  const totalCost = costAnalysis?.totalCost || 0;
  const avgCostPerService = costAnalysis?.averageCostPerService || 0;
  // These properties don't exist in ServiceCostSummary, so we'll calculate them
  const budgetUtilization =
    totalCost > 0 ? Math.min((totalCost / 10000) * 100, 100) : 0; // Assuming 10k budget
  const projectedAnnualCost = totalCost * 12; // Simple projection

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            Cost Analytics
          </CardTitle>
          <div className="flex items-center gap-2">
            <Badge variant="secondary" className="text-xs">
              ${totalCost.toLocaleString()} total
            </Badge>
            {budgetUtilization > 90 && (
              <Badge variant="destructive" className="text-xs">
                Budget Alert
              </Badge>
            )}
            <Button variant="ghost" size="sm">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Key Metrics Grid */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <CostMetricCard
            label="Total Cost"
            value={`$${totalCost.toLocaleString()}`}
            icon={<DollarSign className="h-4 w-4" />}
          />
          <CostMetricCard
            label="Avg/Service"
            value={`$${Math.round(avgCostPerService)}`}
            icon={<BarChart3 className="h-4 w-4" />}
          />
          <CostMetricCard
            label="Budget Used"
            value={`${Math.round(budgetUtilization)}%`}
            icon={<PieChart className="h-4 w-4" />}
            variant={
              budgetUtilization > 90
                ? 'destructive'
                : budgetUtilization > 75
                  ? 'warning'
                  : 'success'
            }
          />
          <CostMetricCard
            label="Projected Annual"
            value={`$${Math.round(projectedAnnualCost).toLocaleString()}`}
            icon={<TrendingUp className="h-4 w-4" />}
          />
        </div>

        {!compact && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Cost Breakdown Pie Chart */}
            <div>
              <h4 className="text-sm font-medium mb-3">Cost by Service Type</h4>
              {pieChartData.length > 0 ? (
                <ResponsiveContainer width="100%" height={200}>
                  <RechartsPieChart>
                    <Pie
                      data={pieChartData}
                      cx="50%"
                      cy="50%"
                      outerRadius={80}
                      dataKey="value"
                      label={({ name, percentage }) =>
                        `${name}: ${percentage}%`
                      }
                      labelLine={false}
                    >
                      {pieChartData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip content={<CustomPieTooltip />} />
                  </RechartsPieChart>
                </ResponsiveContainer>
              ) : (
                <div className="h-48 flex items-center justify-center text-gray-500">
                  No cost breakdown data available
                </div>
              )}
            </div>

            {/* Cost Trend Chart */}
            {showTrend && (
              <div>
                <h4 className="text-sm font-medium mb-3">Monthly Cost Trend</h4>
                {trendData.length > 0 ? (
                  <ResponsiveContainer width="100%" height={200}>
                    <LineChart data={trendData}>
                      <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                      <XAxis dataKey="month" fontSize={12} />
                      <YAxis
                        tickFormatter={value => `$${value}`}
                        fontSize={12}
                      />
                      <Tooltip
                        formatter={(value: number) => [
                          `$${value.toLocaleString()}`,
                          'Cost',
                        ]}
                        labelFormatter={label => `Month: ${label}`}
                      />
                      <Line
                        type="monotone"
                        dataKey="cost"
                        stroke="#3b82f6"
                        strokeWidth={2}
                        dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                ) : (
                  <div className="h-48 flex items-center justify-center text-gray-500">
                    No trend data available
                  </div>
                )}
              </div>
            )}
          </div>
        )}

        {/* Budget Alert */}
        {budgetUtilization > 90 && (
          <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-red-600" />
              <span className="font-medium text-red-800">Budget Alert</span>
            </div>
            <p className="text-sm text-red-700 mt-1">
              You've used {Math.round(budgetUtilization)}% of your maintenance
              budget. Consider reviewing upcoming expenses.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default VehicleCostAnalyticsWidget;
