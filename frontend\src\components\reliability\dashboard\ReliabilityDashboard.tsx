/**
 * @file Main reliability monitoring dashboard component.
 * This component serves as the primary container for the reliability monitoring interface,
 * integrating real-time data streams, user preferences, and responsive layout management.
 * @module components/reliability/dashboard/ReliabilityDashboard
 */

'use client';

import React, { useEffect } from 'react';

import { DashboardGrid } from './DashboardGrid';
import { DashboardHeader } from './DashboardHeader';
import { DashboardSettings } from './DashboardSettings';
import ErrorBoundary from '@/components/error-boundaries/ErrorBoundary';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import LoadingSpinner from '@/components/ui/LoadingSpinner';
import { useReliabilityStore } from '@/lib/hooks';
import { useReliabilityDashboard } from '@/lib/stores/queries/useReliability';

/**
 * Props for the ReliabilityDashboard component
 */
export interface ReliabilityDashboardProps {
  /** Optional CSS class name for styling */
  className?: string;
  /** Whether to show the settings panel initially */
  showSettings?: boolean;
}

/**
 * Main reliability monitoring dashboard component.
 *
 * This component provides a comprehensive view of system reliability including:
 * - Real-time health monitoring
 * - Circuit breaker status
 * - Performance metrics
 * - Active alerts and notifications
 * - User-configurable dashboard layout
 *
 * Features:
 * - Responsive design with mobile-first approach
 * - Real-time WebSocket integration for live updates
 * - Customizable widget layout and preferences
 * - Comprehensive error handling and loading states
 * - Accessibility compliance with ARIA labels and keyboard navigation
 *
 * @param props - Component props
 * @returns JSX element representing the reliability dashboard
 *
 * @example
 * ```tsx
 * function ReliabilityPage() {
 *   return (
 *     <div className="container mx-auto p-4">
 *       <ReliabilityDashboard />
 *     </div>
 *   );
 * }
 * ```
 */
export const ReliabilityDashboard: React.FC<ReliabilityDashboardProps> = ({
  className = '',
  showSettings = false,
}) => {
  // Initialize WebSocket connection management
  // useReliabilityWebSocketManager();

  // Get dashboard state and preferences
  const isMonitoringEnabled = useReliabilityStore(
    state => state.monitoring.isEnabled
  );
  const connectionStatus = useReliabilityStore(
    state => state.monitoring.connectionStatus
  );
  const activeTab = useReliabilityStore(state => state.ui.activeTab);
  const setActiveTab = useReliabilityStore(state => state.setActiveTab);

  // Initialize reliability data queries
  const {
    data: dashboardData,
    isLoading: isDataLoading,
    error: dataError,
  } = useReliabilityDashboard();

  // Extract individual data from dashboard data
  const systemHealth = { data: dashboardData?.systemHealth };
  const circuitBreakers = { data: dashboardData?.circuitBreakers };

  // Set initial tab if not set
  useEffect(() => {
    if (!activeTab) {
      setActiveTab('overview');
    }
  }, [activeTab, setActiveTab]);

  // Handle monitoring disabled state
  if (!isMonitoringEnabled) {
    return (
      <ErrorBoundary>
        <div className={`space-y-6 ${className}`}>
          <DashboardHeader />

          <Alert className="mx-auto max-w-2xl">
            <AlertTitle>Monitoring Disabled</AlertTitle>
            <AlertDescription>
              Reliability monitoring is currently disabled. Enable monitoring in
              the dashboard settings to view real-time system health data.
            </AlertDescription>
          </Alert>

          <DashboardSettings />
        </div>
      </ErrorBoundary>
    );
  }

  // Handle initial loading state
  if (isDataLoading && !systemHealth.data && !circuitBreakers.data) {
    return (
      <ErrorBoundary>
        <div className={`space-y-6 ${className}`}>
          <DashboardHeader />

          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <LoadingSpinner size="lg" />
              <p className="mt-4 text-sm text-muted-foreground">
                Loading reliability dashboard...
              </p>
            </div>
          </div>
        </div>
      </ErrorBoundary>
    );
  }

  // Handle critical data error
  if (dataError && !systemHealth.data && !circuitBreakers.data) {
    return (
      <ErrorBoundary>
        <div className={`space-y-6 ${className}`}>
          <DashboardHeader />

          <Alert variant="destructive" className="mx-auto max-w-2xl">
            <AlertTitle>Dashboard Error</AlertTitle>
            <AlertDescription>
              Failed to load reliability dashboard data. Please check your
              connection and try again.
              {dataError && (
                <details className="mt-2">
                  <summary className="cursor-pointer text-sm">
                    Error details
                  </summary>
                  <pre className="mt-1 text-xs">{dataError.message}</pre>
                </details>
              )}
            </AlertDescription>
          </Alert>
        </div>
      </ErrorBoundary>
    );
  }

  return (
    <ErrorBoundary>
      <div
        className={`space-y-6 ${className}`}
        role="main"
        aria-label="Reliability monitoring dashboard"
      >
        {/* Dashboard Header with navigation and controls */}
        <DashboardHeader />

        {/* Connection status warning */}
        {connectionStatus === 'disconnected' && (
          <Alert
            variant={
              process.env.NODE_ENV === 'development' ? 'default' : 'destructive'
            }
            className="mx-auto max-w-4xl"
          >
            <AlertTitle>
              {process.env.NODE_ENV === 'development'
                ? 'Development Mode - Polling Active'
                : 'Real-time Connection Lost'}
            </AlertTitle>
            <AlertDescription>
              {process.env.NODE_ENV === 'development'
                ? 'Real-time WebSocket connection is not available (authentication required). Dashboard is using polling fallback to fetch data every 10-30 seconds.'
                : 'The real-time connection to the monitoring system has been lost. Data may not be current. The system will automatically attempt to reconnect.'}
            </AlertDescription>
          </Alert>
        )}

        {/* Main dashboard content */}
        <div className="space-y-6">
          {/* Dashboard Grid with widgets */}
          <DashboardGrid />

          {/* Settings panel (conditionally shown) */}
          {showSettings && <DashboardSettings />}
        </div>
      </div>
    </ErrorBoundary>
  );
};

/**
 * Default export for the ReliabilityDashboard component
 */
export default ReliabilityDashboard;
