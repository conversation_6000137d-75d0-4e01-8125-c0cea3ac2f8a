/**
 * @file Circuit breaker alerts widget component.
 * This component displays active alerts related to circuit breaker failures,
 * state changes, and recovery issues with action buttons for alert management.
 * @module components/reliability/widgets/circuit-breakers/CircuitBreakerAlerts
 */

'use client';

import {
  AlertTriangle,
  Bell,
  BellOff,
  CheckCircle,
  Clock,
  Shield,
  ShieldAlert,
  X,
} from 'lucide-react';
import React, { useState } from 'react';

import { ActionButton } from '@/components/ui/action-button';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { useAlerts } from '@/lib/stores/queries/useReliability';
import type { AlertSeverity } from '@/lib/types/domain';
import { cn } from '@/lib/utils';

/**
 * Props for the CircuitBreakerAlerts component
 */
export interface CircuitBreakerAlertsProps {
  /** Optional CSS class name for styling */
  className?: string;
  /** Maximum number of alerts to display */
  maxAlerts?: number;
}

/**
 * Circuit breaker alerts widget component.
 *
 * This component provides:
 * - Active alerts related to circuit breaker failures
 * - Alert severity indicators and filtering
 * - Quick action buttons for alert management
 * - Real-time alert updates
 *
 * Features:
 * - Severity-based filtering
 * - Alert acknowledgment and resolution
 * - Real-time WebSocket updates
 * - Responsive design
 * - Accessibility support
 *
 * @param props - Component props
 * @returns JSX element representing the circuit breaker alerts
 */
export const CircuitBreakerAlerts: React.FC<CircuitBreakerAlertsProps> = ({
  className = '',
  maxAlerts = 10,
}) => {
  const { data: alertsData, isLoading, error } = useAlerts();
  const [severityFilter, setSeverityFilter] = useState<AlertSeverity | 'all'>(
    'all'
  );

  /**
   * Get severity configuration
   */
  const getSeverityConfig = (severity: AlertSeverity) => {
    switch (severity) {
      case 'critical':
        return {
          icon: ShieldAlert,
          color: 'text-red-600 dark:text-red-400',
          bgColor: 'bg-red-100 dark:bg-red-900/20',
          variant: 'destructive' as const,
          label: 'Critical',
        };
      case 'high':
        return {
          icon: AlertTriangle,
          color: 'text-orange-600 dark:text-orange-400',
          bgColor: 'bg-orange-100 dark:bg-orange-900/20',
          variant: 'destructive' as const,
          label: 'High',
        };
      case 'medium':
        return {
          icon: Shield,
          color: 'text-yellow-600 dark:text-yellow-400',
          bgColor: 'bg-yellow-100 dark:bg-yellow-900/20',
          variant: 'secondary' as const,
          label: 'Medium',
        };
      case 'low':
        return {
          icon: Bell,
          color: 'text-blue-600 dark:text-blue-400',
          bgColor: 'bg-blue-100 dark:bg-blue-900/20',
          variant: 'outline' as const,
          label: 'Low',
        };
    }
  };

  /**
   * Format relative time
   */
  const formatRelativeTime = (timestamp: string) => {
    const now = new Date();
    const time = new Date(timestamp);
    const diffMs = now.getTime() - time.getTime();
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMinutes / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffDays > 0) return `${diffDays}d ago`;
    if (diffHours > 0) return `${diffHours}h ago`;
    if (diffMinutes > 0) return `${diffMinutes}m ago`;
    return 'Just now';
  };

  /**
   * Filter alerts by circuit breaker type and severity
   */
  const filteredAlerts = React.useMemo(() => {
    if (!alertsData) return [];

    return alertsData
      .filter(alert => {
        // Filter for circuit breaker related alerts
        const isCircuitBreakerAlert =
          alert.type === 'circuit-breaker' ||
          alert.source?.includes('circuit') ||
          alert.message.toLowerCase().includes('circuit');

        // Filter by severity
        const matchesSeverity =
          severityFilter === 'all' || alert.severity === severityFilter;

        // Only show active alerts
        const isActive = alert.status === 'active';

        return isCircuitBreakerAlert && matchesSeverity && isActive;
      })
      .slice(0, maxAlerts);
  }, [alertsData, severityFilter, maxAlerts]);

  /**
   * Handle alert acknowledgment
   */
  const handleAcknowledgeAlert = async (alertId: string) => {
    // In a real implementation, this would call the acknowledge mutation
    console.log('Acknowledging alert:', alertId);
  };

  /**
   * Handle alert resolution
   */
  const handleResolveAlert = async (alertId: string) => {
    // In a real implementation, this would call the resolve mutation
    console.log('Resolving alert:', alertId);
  };

  // Handle loading state
  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>Circuit Breaker Alerts</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {Array.from({ length: 3 }).map((_, i) => (
              <div
                key={i}
                className="flex items-center space-x-4 animate-pulse"
              >
                <div className="h-10 w-10 bg-muted rounded-full"></div>
                <div className="space-y-2 flex-1">
                  <div className="h-4 bg-muted rounded w-2/3"></div>
                  <div className="h-3 bg-muted rounded w-1/2"></div>
                </div>
                <div className="h-6 w-16 bg-muted rounded"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  // Handle error state
  if (error || !alertsData) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>Circuit Breaker Alerts</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="text-center">
              <AlertTriangle className="mx-auto h-8 w-8 text-muted-foreground mb-2" />
              <p className="text-sm text-muted-foreground">
                Failed to load circuit breaker alerts
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <TooltipProvider>
      <Card className={className}>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Bell className="h-5 w-5" />
              Circuit Breaker Alerts
              {filteredAlerts.length > 0 && (
                <Badge variant="destructive" className="ml-2">
                  {filteredAlerts.length}
                </Badge>
              )}
            </CardTitle>

            <Select
              value={severityFilter}
              onValueChange={value => setSeverityFilter(value as any)}
            >
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Levels</SelectItem>
                <SelectItem value="critical">Critical</SelectItem>
                <SelectItem value="high">High</SelectItem>
                <SelectItem value="medium">Medium</SelectItem>
                <SelectItem value="low">Low</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardHeader>

        <CardContent>
          {filteredAlerts.length === 0 ? (
            <div className="text-center py-8">
              <CheckCircle className="mx-auto h-8 w-8 text-green-600 mb-2" />
              <p className="text-sm text-muted-foreground">
                No active circuit breaker alerts
              </p>
              <p className="text-xs text-muted-foreground mt-1">
                All circuit breakers are operating normally
              </p>
            </div>
          ) : (
            <div className="space-y-3">
              {filteredAlerts.map(alert => {
                const config = getSeverityConfig(alert.severity);
                const Icon = config.icon;

                return (
                  <div
                    key={alert.id}
                    className={cn(
                      'flex items-start gap-3 p-3 rounded-lg border',
                      config.bgColor
                    )}
                  >
                    {/* Alert Icon */}
                    <div className={cn('p-2 rounded-full bg-background/50')}>
                      <Icon className={cn('h-4 w-4', config.color)} />
                    </div>

                    {/* Alert Content */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between gap-2 mb-2">
                        <div>
                          <p className="font-medium text-sm">{alert.message}</p>
                          {alert.source && (
                            <p className="text-xs text-muted-foreground">
                              Source: {alert.source}
                            </p>
                          )}
                        </div>
                        <Badge variant={config.variant} className="text-xs">
                          {config.label}
                        </Badge>
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-1 text-xs text-muted-foreground">
                          <Clock className="h-3 w-3" />
                          {formatRelativeTime(alert.timestamp)}
                        </div>

                        {/* Action Buttons */}
                        <div className="flex items-center gap-1">
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleAcknowledgeAlert(alert.id)}
                                className="h-7 w-7 p-0"
                              >
                                <BellOff className="h-3 w-3" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>Acknowledge alert</TooltipContent>
                          </Tooltip>

                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleResolveAlert(alert.id)}
                                className="h-7 w-7 p-0"
                              >
                                <CheckCircle className="h-3 w-3" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>Resolve alert</TooltipContent>
                          </Tooltip>
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}

              {alertsData.filter(
                alert =>
                  (alert.type === 'circuit-breaker' ||
                    alert.source?.includes('circuit')) &&
                  alert.status === 'active'
              ).length > maxAlerts && (
                <div className="text-center pt-3 border-t">
                  <p className="text-sm text-muted-foreground">
                    +
                    {alertsData.filter(
                      alert =>
                        (alert.type === 'circuit-breaker' ||
                          alert.source?.includes('circuit')) &&
                        alert.status === 'active'
                    ).length - maxAlerts}{' '}
                    more alerts
                  </p>
                  <Button variant="outline" size="sm" className="mt-2">
                    View All Alerts
                  </Button>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </TooltipProvider>
  );
};

/**
 * Default export for the CircuitBreakerAlerts component
 */
export default CircuitBreakerAlerts;
