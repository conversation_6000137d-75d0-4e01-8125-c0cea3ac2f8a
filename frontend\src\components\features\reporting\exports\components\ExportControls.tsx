// frontend/src/components/features/reporting/exports/components/ExportControls.tsx

import {
  ChevronDown,
  Download,
  FileText,
  Image,
  Loader2,
  Printer,
} from 'lucide-react';
import React, { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

import type { ExportFormat } from '../../data/types/export';
import type { ExportOptions } from '../../data/types/reporting';

import { useExport } from '../hooks/useExport';

/**
 * Props for ExportControls component following Interface Segregation Principle
 */
interface ExportControlsProps {
  chartElementId?: string;
  className?: string;
  data?: any[];
  disabled?: boolean;
  filename?: string;
  onExportComplete?: (format: ExportFormat) => void;
  onExportError?: (format: ExportFormat, error: string) => void;
  onExportStart?: (format: ExportFormat) => void;
  showChart?: boolean;
  showPrint?: boolean;
}

/**
 * ExportControls Component
 *
 * A comprehensive export controls component following SOLID principles.
 * Integrates with existing export hooks and maintains separation of concerns.
 *
 * Features:
 * - Multiple export formats (CSV, Excel, PDF)
 * - Integration with existing useExport hook
 * - Progress indicators and error handling
 * - Print functionality
 * - Follows established folder structure
 *
 * @param props - Component props
 * @returns JSX element
 */
export const ExportControls: React.FC<ExportControlsProps> = ({
  chartElementId,
  className = '',
  data = [],
  disabled = false,
  filename = 'export',
  onExportComplete,
  onExportError,
  onExportStart,
  showChart = false, // Disabled for now since chart export needs html2canvas
  showPrint = true,
}) => {
  const [exportingFormat, setExportingFormat] = useState<ExportFormat | null>(
    null
  );

  // Use existing export hook following DRY principles
  const {
    exportError,
    exportReportToPDF,
    exportToCSV,
    exportToExcel,
    exportToPDF,
    isExporting,
  } = useExport(filename);

  // Handle export with proper error handling
  const handleExport = async (
    format: ExportFormat,
    options: ExportOptions = {}
  ) => {
    if (disabled || isExporting) return;

    setExportingFormat(format);
    onExportStart?.(format);

    try {
      switch (format) {
        case 'csv': {
          if (!data || data.length === 0) {
            throw new Error('No data available for CSV export');
          }
          const csvOptions: ExportOptions = {
            filename,
            format: 'csv',
            includeCharts: false,
          };
          exportToCSV(data, csvOptions);
          break;
        }

        case 'excel': {
          if (!data || data.length === 0) {
            throw new Error('No data available for Excel export');
          }
          const excelOptions: ExportOptions = {
            filename,
            format: 'excel',
            includeCharts: false,
          };
          exportToExcel(data, excelOptions);
          break;
        }

        case 'pdf': {
          // Use the working PDF export from useExport hook
          if (!data || data.length === 0) {
            throw new Error('No data available for PDF export');
          }

          // Create a basic analytics structure from the data
          const analyticsData = {
            data: {
              records: data,
              summary: {
                exportedAt: new Date().toISOString(),
                totalRecords: data.length,
              },
              totalCount: data.length,
            },
            metadata: {
              format: 'pdf',
              generatedAt: new Date().toISOString(),
              generatedBy: 'system',
              id: `export_${Date.now()}`,
              type: 'data_export',
            },
          };

          // Use the working exportReportToPDF method from the hook
          await exportReportToPDF(
            analyticsData,
            'delegations', // Default entity type for generic exports
            'Data Export Report',
            filename
          );
          break;
        }

        case 'png': {
          throw new Error(
            'Chart export is not available. Please install html2canvas dependency.'
          );
        }

        default: {
          throw new Error(`Unsupported export format: ${format}`);
        }
      }

      onExportComplete?.(format);
    } catch (error: any) {
      console.error(`${format.toUpperCase()} export failed:`, error);
      onExportError?.(format, error.message);
    } finally {
      setExportingFormat(null);
    }
  };

  // Handle print functionality
  const handlePrint = () => {
    if (disabled) return;
    globalThis.print();
  };

  // Get export status
  const getExportStatus = (format: ExportFormat) => {
    if (exportingFormat === format && isExporting) {
      return 'exporting';
    }
    return 'idle';
  };

  // Render export button with status
  const renderExportButton = (
    format: ExportFormat,
    icon: React.ReactNode,
    label: string
  ) => {
    const status = getExportStatus(format);
    const isCurrentlyExporting = status === 'exporting';

    return (
      <DropdownMenuItem
        className="flex items-center gap-2"
        disabled={disabled || isExporting}
        onClick={() => handleExport(format)}
      >
        {isCurrentlyExporting ? (
          <Loader2 className="size-4 animate-spin" />
        ) : (
          icon
        )}
        <span>{isCurrentlyExporting ? `Exporting ${label}...` : label}</span>
        {isCurrentlyExporting && (
          <Badge className="ml-auto" variant="secondary">
            Processing
          </Badge>
        )}
      </DropdownMenuItem>
    );
  };

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            className="h-8"
            disabled={disabled || isExporting}
            size="sm"
            variant="outline"
          >
            <Download className="mr-1 size-4" />
            Export
            <ChevronDown className="ml-1 size-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-48">
          {/* Data exports */}
          {renderExportButton(
            'csv',
            <FileText className="size-4" />,
            'Export CSV'
          )}
          {renderExportButton(
            'excel',
            <FileText className="size-4" />,
            'Export Excel'
          )}
          {renderExportButton(
            'pdf',
            <FileText className="size-4" />,
            'Export PDF'
          )}

          {/* Chart export (if enabled) */}
          {showChart && (
            <>
              <DropdownMenuSeparator />
              {renderExportButton(
                'png',
                <Image className="size-4" />,
                'Export Chart'
              )}
            </>
          )}

          {/* Print option */}
          {showPrint && (
            <>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                className="flex items-center gap-2"
                disabled={disabled}
                onClick={handlePrint}
              >
                <Printer className="size-4" />
                <span>Print</span>
              </DropdownMenuItem>
            </>
          )}
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Error display */}
      {exportError && (
        <Badge className="text-xs" variant="destructive">
          Export failed
        </Badge>
      )}
    </div>
  );
};
