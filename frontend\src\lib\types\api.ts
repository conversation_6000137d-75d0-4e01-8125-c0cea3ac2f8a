/**
 * API response and error types
 * Re-exports from apiContracts.ts for API wire format types
 */

import type {
  AcknowledgeAlertRequest,
  AlertApiResponse,
  AlertHistoryApiResponse,
  AlertStatisticsApiResponse,
  CircuitBreakerStatusApiResponse,
  DeduplicationMetricsApiResponse,
  DelegationApiResponse,
  DependencyHealthApiResponse,
  DetailedHealthApiResponse,
  EmployeeApiResponse,
  FlightDetailsApiResponse,
  HealthCheckApiResponse,
  MetricsApiResponse,
  ResolveAlertRequest,
  TaskApiResponse,
  TestAlertsApiResponse,
  VehicleApiResponse,
} from './apiContracts';

import {
  Alert,
  AlertHistory,
  AlertSeverity,
  AlertStatistics,
  AlertStatus,
  CircuitBreakerState,
  CircuitBreakerStatus,
  ComponentHealth,
  DeduplicationMetrics,
  Delegation,
  DelegationStatusPrisma,
  DependencyHealth,
  DetailedHealthCheck,
  Employee,
  EmployeeRolePrisma,
  EmployeeStatusPrisma,
  ErrorLogEntry, // Now from domain
  FlightDetails,
  HealthCheck,
  HealthStatus,
  LogLevel, // Now from domain
  ServiceRecord,
  SystemMetrics,
  Task,
  TaskPriorityPrisma,
  TaskStatusPrisma,
  TestAlertsResult,
  Vehicle,
} from './domain';

// Re-export API contract types for wire format
export type {
  AcknowledgeAlertRequest,
  AlertApiResponse,
  AlertHistoryApiResponse,
  AlertStatisticsApiResponse,
  CircuitBreakerStatusApiResponse,
  CreateDelegateRequest,
  CreateDelegationRequest,
  CreateDriverRequest,
  CreateEmployeeRequest,
  CreateEscortRequest,
  CreateFlightDetailsRequest,
  CreateSubtaskRequest,
  CreateTaskRequest,
  CreateVehicleAssignmentRequest,
  CreateVehicleRequest,
  DeduplicationMetricsApiResponse,
  DelegateApiResponse,
  // Delegation API contracts
  DelegationApiResponse,
  DelegationDriverApiResponse,
  DelegationEscortApiResponse,
  DelegationVehicleApiResponse,
  DependencyHealthApiResponse,
  DetailedHealthApiResponse,
  // Employee API contracts
  EmployeeApiResponse,
  FlightDetailsApiResponse,
  // Reliability API contracts
  HealthCheckApiResponse,
  MetricsApiResponse,
  ResolveAlertRequest,
  ServiceRecordApiResponse,
  SubtaskApiResponse,
  // Task API contracts
  TaskApiResponse,
  TestAlertsApiResponse,
  UpdateDelegationRequest,
  UpdateEmployeeRequest,
  UpdateTaskRequest,
  UpdateVehicleRequest,
  // Vehicle API contracts
  VehicleApiResponse,
} from './apiContracts';

// Re-export domain types that are also used as API types
export type {
  ErrorLogEntry,
  FlightDetails,
  HealthCheck,
  LogLevel,
} from './domain';

/**
 * API Error Types for categorization and handling
 */
export enum ApiErrorType {
  AUTHENTICATION_ERROR = 'authentication_error',
  AUTHORIZATION_ERROR = 'authorization_error',

  // Client errors
  CLIENT_ERROR = 'client_error',
  // Network-related errors
  NETWORK_ERROR = 'network_error',

  NOT_FOUND = 'not_found',
  // Parsing errors
  PARSING_ERROR = 'parsing_error',
  RATE_LIMIT = 'rate_limit',
  // Server errors
  SERVER_ERROR = 'server_error',
  TIMEOUT = 'timeout',

  // Other
  UNKNOWN = 'unknown',

  VALIDATION_ERROR = 'validation_error',
}
// Request options type
export interface ApiRequestOptions extends RequestInit {
  retries?: number;
  retryDelay?: number;
  skipRetryLogging?: boolean;
  timeout?: number;
}
// Legacy ApiResponse interface removed - use standardized backend response format
// Validation error type
export interface ApiValidationError {
  code?: string;
  message: string;
  path: string;
  received?: any;
}

export type DelegationsApiResponse = DelegationApiResponse[];

export type EmployeesApiResponse = EmployeeApiResponse[];

// Filter parameters (generic)
export type FilterParams = Record<
  string,
  boolean | number | string | undefined
>;

// Pagination parameters
export interface PaginationParams {
  limit?: number;
  offset?: number;
  page?: number;
}

// Legacy PaginationResponse interface removed - use standardized pagination format

// Combined query parameters
export interface QueryParams
  extends FilterParams,
    PaginationParams,
    SortingParams {}

// Sorting parameters
export interface SortingParams {
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export type TasksApiResponse = TaskApiResponse[];

// Convenience type aliases for common patterns
export type VehiclesApiResponse = VehicleApiResponse[];

/**
 * Enhanced API error with additional context and helper methods
 */
export class ApiError extends Error {
  code?: string;
  details?: any;
  endpoint?: string; // Make optional
  errorType: ApiErrorType;
  receivedData?: any;
  retryable: boolean;
  status: number;
  validationErrors?: ApiValidationError[]; // Make optional

  constructor(
    message: string,
    options: {
      code?: string;
      details?: any;
      endpoint?: string;
      errorType?: ApiErrorType;
      receivedData?: any;
      status: number;
      validationErrors?: ApiValidationError[];
    }
  ) {
    super(message);
    this.name = 'ApiError';
    this.status = options.status;
    if (options.code) this.code = options.code;
    if (options.endpoint) this.endpoint = options.endpoint;
    if (options.validationErrors)
      this.validationErrors = options.validationErrors;
    this.receivedData = options.receivedData;
    this.details = options.details;

    // Determine error type based on status code if not provided
    this.errorType = options.errorType || this.determineErrorType();

    // Determine if error is retryable
    this.retryable = this.isRetryable();

    // Ensure instanceof works correctly
    Object.setPrototypeOf(this, ApiError.prototype);
  }

  // Legacy constructor support for backward compatibility
  static create(
    message: string,
    status: number,
    options?: {
      details?: any;
      errorType?: ApiErrorType;
      receivedData?: any;
      validationErrors?: ApiValidationError[];
    }
  ): ApiError {
    return new ApiError(message, {
      details: options?.details,
      ...(options?.errorType && { errorType: options.errorType }),
      receivedData: options?.receivedData,
      status,
      ...(options?.validationErrors && {
        validationErrors: options.validationErrors,
      }),
    });
  }

  /**
   * Get a user-friendly formatted message for display
   */
  getFormattedMessage(): string {
    if (this.isValidationError()) {
      const errorDetails = this.validationErrors!.map(
        err => `${err.path}: ${err.message}`
      ).join('; ');

      return `Validation failed: ${errorDetails}`;
    }

    switch (this.errorType) {
      case ApiErrorType.AUTHENTICATION_ERROR: {
        return 'Authentication required. Please log in and try again.';
      }

      case ApiErrorType.AUTHORIZATION_ERROR: {
        return 'You do not have permission to perform this action.';
      }

      case ApiErrorType.NETWORK_ERROR: {
        return 'Network error: Unable to connect to the server. Please check your internet connection.';
      }

      case ApiErrorType.NOT_FOUND: {
        return `Resource not found: ${
          this.endpoint || 'The requested resource'
        } could not be found.`;
      }

      case ApiErrorType.PARSING_ERROR: {
        return 'Could not parse the server response. Please try again or contact support.';
      }

      case ApiErrorType.RATE_LIMIT: {
        return 'Too many requests. Please try again later.';
      }

      case ApiErrorType.SERVER_ERROR: {
        return `Server error (${this.status}): ${this.message}. Please try again later.`;
      }

      case ApiErrorType.TIMEOUT: {
        return 'Request timed out. The server is taking too long to respond.';
      }

      default: {
        return this.message;
      }
    }
  }

  /**
   * Get technical details for logging
   */
  getTechnicalDetails(): string {
    const details = [
      `Status: ${this.status}`,
      `Type: ${this.errorType}`,
      `Message: ${this.message}`,
    ];

    if (this.details) {
      details.push(`Details: ${JSON.stringify(this.details)}`);
    }

    if (this.validationErrors) {
      details.push(
        `Validation Errors: ${JSON.stringify(this.validationErrors)}`
      );
    }

    return details.join('\n');
  }

  /**
   * Check if this is a validation error
   */
  isValidationError(): boolean {
    return (
      this.status === 400 &&
      Array.isArray(this.validationErrors) &&
      this.validationErrors.length > 0
    );
  }

  /**
   * Determine error type based on status code
   */
  private determineErrorType(): ApiErrorType {
    if (this.status >= 500) {
      return ApiErrorType.SERVER_ERROR;
    }

    if (this.status === 429) {
      return ApiErrorType.RATE_LIMIT;
    }

    if (this.status === 401) {
      return ApiErrorType.AUTHENTICATION_ERROR;
    }

    if (this.status === 403) {
      return ApiErrorType.AUTHORIZATION_ERROR;
    }

    if (this.status === 400 && this.isValidationError()) {
      return ApiErrorType.VALIDATION_ERROR;
    }

    if (this.status >= 400 && this.status < 500) {
      return ApiErrorType.CLIENT_ERROR;
    }

    return ApiErrorType.UNKNOWN;
  }

  /**
   * Determine if error is retryable
   */
  private isRetryable(): boolean {
    return (
      this.errorType === ApiErrorType.SERVER_ERROR ||
      this.errorType === ApiErrorType.NETWORK_ERROR ||
      this.errorType === ApiErrorType.TIMEOUT ||
      this.errorType === ApiErrorType.RATE_LIMIT
    );
  }
}
