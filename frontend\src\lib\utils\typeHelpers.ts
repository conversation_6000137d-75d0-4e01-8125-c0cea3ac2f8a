/**
 * @file Type Helper Utilities
 * @description Utilities to handle exactOptionalPropertyTypes issues
 */

/**
 * Converts undefined to null for properties that expect null
 */
export function undefinedToNull<T>(value: T | undefined): T | null {
  return value === undefined ? null : value;
}

/**
 * Converts null to undefined for properties that expect undefined
 */
export function nullToUndefined<T>(value: T | null): T | undefined {
  return value === null ? undefined : value;
}

/**
 * Ensures a value is not undefined (converts to null if needed)
 */
export function ensureNotUndefined<T>(value: T | undefined): T | null {
  return value === undefined ? null : value;
}

/**
 * Safely handles optional properties for exactOptionalPropertyTypes
 */
export function safeOptional<T>(value: T | undefined | null): T | undefined {
  return value === null ? undefined : value;
}

/**
 * Creates a partial object with proper undefined handling
 */
export function createPartial<T extends Record<string, any>>(
  obj: Partial<T>
): Partial<T> {
  const result: Partial<T> = {};

  for (const [key, value] of Object.entries(obj)) {
    if (value !== undefined) {
      (result as any)[key] = value;
    }
  }

  return result;
}

/**
 * Merges objects while handling undefined values properly
 */
export function mergeWithUndefined<T extends Record<string, any>>(
  target: T,
  source: Partial<T>
): T {
  const result = { ...target };

  for (const [key, value] of Object.entries(source)) {
    if (value !== undefined) {
      (result as any)[key] = value;
    }
  }

  return result;
}

/**
 * Type guard to check if a value is not undefined
 */
export function isDefined<T>(value: T | undefined): value is T {
  return value !== undefined;
}

/**
 * Type guard to check if a value is not null
 */
export function isNotNull<T>(value: T | null): value is T {
  return value !== null;
}

/**
 * Type guard to check if a value is not null or undefined
 */
export function isNotNullOrUndefined<T>(
  value: T | null | undefined
): value is T {
  return value !== null && value !== undefined;
}
