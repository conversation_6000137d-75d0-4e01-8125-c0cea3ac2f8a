// circuitBreaker.service.test.ts - Unit tests for Circuit Breaker Service

import { describe, test, expect, beforeEach, afterEach, jest } from '@jest/globals';

// Note: We're using the real opossum circuit breaker for integration-style testing
// This provides better coverage of the actual behavior

jest.mock('src/utils/logger.ts', () => ({
  default: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
}));

// Import after mocking
import {
  executeWithCircuitBreaker,
  getCircuitBreakerStatus,
  circuitBreakerRegistry,
} from '../../../services/circuitBreaker.service';

describe('CircuitBreakerRegistry', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterEach(() => {
    // Reset all circuit breakers
    circuitBreakerRegistry.reset();
  });

  describe('Circuit Breaker Creation', () => {
    test('should create circuit breaker with default configuration', () => {
      const breaker = circuitBreakerRegistry.getBreaker('test-service');

      expect(breaker).toBeDefined();
      expect(breaker.fire).toBeDefined();
      expect(breaker.on).toBeDefined();
    });

    test('should create circuit breaker with custom configuration', () => {
      const customConfig = {
        timeout: 5000,
        errorThresholdPercentage: 60,
        resetTimeout: 20000,
      };

      const breaker = circuitBreakerRegistry.getBreaker('custom-service', customConfig);

      expect(breaker).toBeDefined();
    });

    test('should reuse existing circuit breaker for same service', () => {
      const breaker1 = circuitBreakerRegistry.getBreaker('same-service');
      const breaker2 = circuitBreakerRegistry.getBreaker('same-service');

      expect(breaker1).toBe(breaker2);
    });

    test('should create different circuit breakers for different services', () => {
      const breaker1 = circuitBreakerRegistry.getBreaker('service-1');
      const breaker2 = circuitBreakerRegistry.getBreaker('service-2');

      expect(breaker1).not.toBe(breaker2);
    });
  });

  describe('Service Type Configurations', () => {
    test('should apply database service configuration', () => {
      const breaker = circuitBreakerRegistry.getBreaker('test-db');

      expect(breaker).toBeDefined();
      // Configuration is applied during creation
    });

    test('should apply supabase service configuration', () => {
      const breaker = circuitBreakerRegistry.getBreaker('test-supabase');

      expect(breaker).toBeDefined();
    });

    test('should apply redis service configuration', () => {
      const breaker = circuitBreakerRegistry.getBreaker('test-redis');

      expect(breaker).toBeDefined();
    });

    test('should apply external API service configuration', () => {
      const breaker = circuitBreakerRegistry.getBreaker('test-api');

      expect(breaker).toBeDefined();
    });
  });

  describe('Circuit Breaker Status', () => {
    test('should return status for specific circuit breaker', () => {
      const breaker = circuitBreakerRegistry.getBreaker('status-test');
      const status = circuitBreakerRegistry.getStatus('status-test');

      expect(status).toBeDefined();
      expect(status.name).toBe('status-test');
      expect(status.state).toBeDefined();
    });

    test('should return status for all circuit breakers', () => {
      circuitBreakerRegistry.getBreaker('service-1');
      circuitBreakerRegistry.getBreaker('service-2');

      const allStatus = circuitBreakerRegistry.getStatus();

      expect(typeof allStatus).toBe('object');
      expect(Object.keys(allStatus).length).toBeGreaterThanOrEqual(2);
    });

    test('should return null for non-existent circuit breaker', () => {
      const status = circuitBreakerRegistry.getStatus('non-existent');

      expect(status).toBeNull();
    });
  });

  describe('Circuit Breaker Reset', () => {
    test('should reset specific circuit breaker', () => {
      const breaker = circuitBreakerRegistry.getBreaker('reset-test') as any;

      // Open the circuit breaker first
      breaker.open();
      expect(breaker.opened).toBe(true);

      // Reset should close it
      circuitBreakerRegistry.reset('reset-test');

      // Verify it's closed after reset
      expect(breaker.opened).toBe(false);
    });

    test('should reset all circuit breakers', () => {
      const breaker1 = circuitBreakerRegistry.getBreaker('service-1') as any;
      const breaker2 = circuitBreakerRegistry.getBreaker('service-2') as any;

      // Open both circuit breakers
      breaker1.open();
      breaker2.open();
      expect(breaker1.opened).toBe(true);
      expect(breaker2.opened).toBe(true);

      // Reset all
      circuitBreakerRegistry.reset();

      // Verify both are closed
      expect(breaker1.opened).toBe(false);
      expect(breaker2.opened).toBe(false);
    });
  });

  // Circuit breaker clearing is handled internally

  describe('Event Handling', () => {
    test('should set up event listeners on circuit breaker creation', () => {
      const breaker = circuitBreakerRegistry.getBreaker('event-test') as any;

      // Verify that the circuit breaker has event handling capabilities
      expect(breaker.on).toBeDefined();
      expect(typeof breaker.on).toBe('function');

      // Test that we can add event listeners
      const mockListener = jest.fn();
      breaker.on('open', mockListener);
      expect(mockListener).not.toHaveBeenCalled(); // Should not be called immediately
    });

    test('should handle circuit breaker open event', () => {
      const breaker = circuitBreakerRegistry.getBreaker('event-test-2') as any;
      const openListener = jest.fn();

      // Add event listener
      breaker.on('open', openListener);

      // Force the circuit breaker to open state
      breaker.open();

      // Verify the event listener was called
      expect(openListener).toHaveBeenCalled();
    });

    test('should handle circuit breaker close event', () => {
      const breaker = circuitBreakerRegistry.getBreaker('event-test-3') as any;
      const closeListener = jest.fn();

      // Add event listener
      breaker.on('close', closeListener);

      // First open the circuit breaker, then close it to trigger the event
      breaker.open(); // This should trigger an 'open' event
      breaker.close(); // This should trigger a 'close' event

      // Verify the event listener was called
      expect(closeListener).toHaveBeenCalled();
    });
  });
});

describe('executeWithCircuitBreaker', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('should execute function successfully', async () => {
    const testFunction = jest.fn().mockResolvedValue('success');

    const result = await executeWithCircuitBreaker('test-service', testFunction);

    expect(result).toBe('success');
    expect(testFunction).toHaveBeenCalled();
  });

  test('should handle function failure', async () => {
    const testFunction = jest.fn().mockRejectedValue(new Error('Test error'));

    await expect(executeWithCircuitBreaker('test-service', testFunction)).rejects.toThrow(
      'Test error',
    );

    expect(testFunction).toHaveBeenCalled();
  });

  test('should use custom configuration', async () => {
    const testFunction = jest.fn().mockResolvedValue('success');
    const customConfig = { timeout: 1000 };

    const result = await executeWithCircuitBreaker('custom-service', testFunction, customConfig);

    expect(result).toBe('success');
  });

  test('should handle circuit breaker open state', async () => {
    const testFunction = jest.fn().mockResolvedValue('success');

    // Get the circuit breaker and set it to open state
    const breaker = circuitBreakerRegistry.getBreaker('open-service') as any;
    breaker.open(); // Force the circuit breaker to open state

    // When circuit breaker is open, it should reject immediately
    await expect(executeWithCircuitBreaker('open-service', testFunction)).rejects.toThrow();

    // Function should not be called when circuit breaker is open
    expect(testFunction).not.toHaveBeenCalled();
  });
});

describe('getCircuitBreakerStatus', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('should return status for specific circuit breaker', () => {
    circuitBreakerRegistry.getBreaker('status-service');

    const status = getCircuitBreakerStatus('status-service');

    expect(status).toBeDefined();
    expect(status.name).toBe('status-service');
  });

  test('should return status for all circuit breakers', () => {
    circuitBreakerRegistry.getBreaker('service-1');
    circuitBreakerRegistry.getBreaker('service-2');

    const allStatus = getCircuitBreakerStatus();

    expect(typeof allStatus).toBe('object');
    expect(allStatus).not.toBeNull();
    expect(Object.keys(allStatus).length).toBeGreaterThanOrEqual(2);
  });
});

describe('Circuit Breaker State Transitions', () => {
  let breaker: any;

  beforeEach(() => {
    breaker = circuitBreakerRegistry.getBreaker('state-test');
    jest.clearAllMocks();
    // Ensure circuit breaker starts in closed state
    breaker.close();
  });

  test('should transition from CLOSED to OPEN on failures', async () => {
    const failingFunction = jest.fn().mockRejectedValue(new Error('Failure'));

    // Verify initial state
    expect(breaker.opened).toBe(false);

    // Simulate multiple failures to trigger circuit breaker
    // The default error threshold is 60%, so we need enough failures
    for (let i = 0; i < 10; i++) {
      try {
        await executeWithCircuitBreaker('state-test', failingFunction);
      } catch (error) {
        // Expected to fail
      }
    }

    // The circuit breaker should transition to OPEN after enough failures
    // Note: The exact threshold depends on the configuration
    expect(breaker.opened).toBe(true);
  });

  test('should reset failure count on successful execution', async () => {
    // Reset the circuit breaker to closed state first
    breaker.close();
    expect(breaker.opened).toBe(false);

    const successFunction = jest.fn().mockResolvedValue('success');

    await executeWithCircuitBreaker('state-test', successFunction);

    // Verify the function was called successfully
    expect(successFunction).toHaveBeenCalled();
    expect(breaker.opened).toBe(false); // Should remain closed
  });
});

describe('Circuit Breaker Configuration Validation', () => {
  test('should handle invalid configuration gracefully', () => {
    const invalidConfig = {
      timeout: -1000,
      errorThresholdPercentage: 150,
    };

    // Should not throw error, should use defaults or sanitized values
    expect(() => {
      circuitBreakerRegistry.getBreaker('invalid-config', invalidConfig);
    }).not.toThrow();
  });

  test('should merge custom config with defaults', () => {
    const partialConfig = {
      timeout: 2000,
    };

    const breaker = circuitBreakerRegistry.getBreaker('partial-config', partialConfig);

    expect(breaker).toBeDefined();
    // Other config values should use defaults
  });
});
