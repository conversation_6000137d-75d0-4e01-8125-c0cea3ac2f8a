/**
 * @file Universal Form Submission Hook - SOLID Architecture
 * @description Modern form submission hook following SOLID principles
 * @version 4.0.0 - Legacy-free implementation
 * <AUTHOR> Development Team
 *
 * SOLID Principles Applied:
 * ✅ Single Responsibility Principle (SRP): Each service handles one concern
 * ✅ Open/Closed Principle: Extensible through configuration and services
 * ✅ Liskov Substitution Principle: Compatible service implementations
 * ✅ Interface Segregation Principle: Focused service interfaces
 * ✅ Dependency Inversion Principle: Abstraction-based dependencies
 *
 * Modern Features:
 * 🚀 Specialized service architecture with clean separation
 * 🎯 Integrated toast service with entity-specific messaging
 * 🔄 Built-in retry logic with exponential backoff
 * ♿ Comprehensive accessibility support
 * 📊 Performance tracking and optimization
 * 🛡️ Type-safe implementation with full TypeScript support
 */

import type React from 'react';
import type { FieldValues } from 'react-hook-form';
import { useCallback, useState, useRef, useEffect } from 'react';

// Import types
import type {
  FormSubmissionOptions,
  FormSubmissionResult,
  SubmissionState,
} from './types/FormSubmissionTypes';

// Import services following SRP
import { FormSubmissionConfigService } from './services/FormSubmissionConfig';
import { FormSubmissionToastService } from './services/FormSubmissionToastService';
import { FormSubmissionAccessibilityService } from './services/FormSubmissionAccessibilityService';
import { FormSubmissionRetryService } from './services/FormSubmissionRetryService';
import { FormSubmissionPerformanceService } from './services/FormSubmissionPerformanceService';

/**
 * Universal Form Submission Hook - Modern SOLID Architecture
 *
 * Coordinates specialized services following SOLID principles for clean,
 * maintainable, and accessible form submission handling.
 *
 * @example Entity-Specific Form Submission
 * ```typescript
 * const { handleSubmit, isLoading, error, state } = useFormSubmission(
 *   async (data) => await api.createEmployee(data),
 *   {
 *     toast: {
 *       entityType: 'employee',
 *       entity: { name: data.name },
 *     },
 *     preSubmitValidation: (data) => data.name && data.email,
 *     transformData: (data) => ({
 *       ...data,
 *       email: data.email.toLowerCase().trim(),
 *     }),
 *   }
 * );
 * ```
 *
 * @example Production-Ready Form with All Features
 * ```typescript
 * const {
 *   handleSubmit,
 *   state,
 *   retry,
 *   ariaAttributes,
 *   metrics
 * } = useFormSubmission(
 *   async (data) => await api.createVehicle(data),
 *   {
 *     toast: {
 *       entityType: 'vehicle',
 *       entity: { make: data.make, model: data.model },
 *     },
 *     preSubmitValidation: async (data) => {
 *       return data.make && data.model && data.licensePlate;
 *     },
 *     transformData: (data) => ({
 *       ...data,
 *       year: parseInt(data.year, 10),
 *       licensePlate: data.licensePlate.toUpperCase(),
 *     }),
 *     retry: {
 *       maxAttempts: 3,
 *       exponentialBackoff: true,
 *       retryCondition: (error) => error.message.includes('network'),
 *     },
 *     accessibility: {
 *       focusManagement: 'first-error',
 *       announceStatus: true,
 *     },
 *     performance: {
 *       debounceMs: 300,
 *       timeoutMs: 30000,
 *     },
 *     onSuccess: (data, result) => {
 *       router.push(`/vehicles/${result.id}`);
 *     },
 *   }
 * );
 * ```
 */
export const useFormSubmission = <T extends FieldValues>(
  onSubmit: (data: T) => Promise<any>,
  options: FormSubmissionOptions<T> = {}
): FormSubmissionResult<T> => {
  // Merge configurations using config service
  const retryConfig = FormSubmissionConfigService.mergeRetryConfig(
    options.retry
  );
  const accessibilityConfig =
    FormSubmissionConfigService.mergeAccessibilityConfig(options.accessibility);
  const performanceConfig = FormSubmissionConfigService.mergePerformanceConfig(
    options.performance
  );
  const toastConfig = FormSubmissionConfigService.mergeToastConfig(
    options.toast
  );

  // Initialize services following SRP
  const accessibilityService = useRef(
    new FormSubmissionAccessibilityService(accessibilityConfig)
  ).current;
  const retryService = useRef(
    new FormSubmissionRetryService(retryConfig)
  ).current;
  const performanceService = useRef(
    new FormSubmissionPerformanceService(performanceConfig)
  ).current;

  // Core state management
  const [state, setState] = useState<SubmissionState>('idle');
  const [error, setError] = useState<string | null>(null);
  const [errorObject, setErrorObject] = useState<Error | null>(null);
  const [lastSubmittedData, setLastSubmittedData] = useState<T | null>(null);
  const [lastSubmitted, setLastSubmitted] = useState<number | null>(null);
  const [lastResult, setLastResult] = useState<any>(null);
  const [submissionDuration, setSubmissionDuration] = useState<number | null>(
    null
  );

  // Refs for cleanup and cancellation
  const abortControllerRef = useRef<AbortController | null>(null);

  // Derived state
  const isLoading = state === 'submitting' || state === 'validating';
  const isSuccess = state === 'success';
  const isValidating = state === 'validating';
  const isRetrying = state === 'retrying';
  const retryAttempt = retryService.getCurrentAttempt();

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      performanceService.cleanup();
      accessibilityService.cleanup();
    };
  }, [performanceService, accessibilityService]);

  // Utility Functions
  const clearError = useCallback(() => {
    setError(null);
    setErrorObject(null);
    accessibilityService.updateErrorMessage(null);
    if (state === 'error') {
      setState('idle');
    }
  }, [state, accessibilityService]);

  const reset = useCallback(() => {
    setState('idle');
    setError(null);
    setErrorObject(null);
    setLastSubmittedData(null);
    setLastSubmitted(null);
    setLastResult(null);
    setSubmissionDuration(null);
    retryService.resetAttempts();
    performanceService.resetMetrics();
    accessibilityService.updateErrorMessage(null);
  }, [retryService, performanceService, accessibilityService]);

  const cancel = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    setState('idle');
    accessibilityService.announceStatus('Form submission cancelled');
  }, [accessibilityService]);

  // Core submission logic using coordinated services
  const performSubmission = useCallback(
    async (data: T, isRetryAttempt = false): Promise<void> => {
      try {
        // Start performance tracking
        performanceService.startTiming();

        // Create abort controller for cancellation
        abortControllerRef.current = new AbortController();

        // Set appropriate state and announce
        const currentState = isRetryAttempt ? 'retrying' : 'submitting';
        setState(currentState);

        const statusMessage = accessibilityService.getStatusMessage(
          currentState,
          retryAttempt,
          retryConfig.maxAttempts
        );
        accessibilityService.announceStatus(statusMessage);

        // Call onSubmitStart callback
        if (options.onSubmitStart) {
          await options.onSubmitStart(data);
        }

        // Pre-submission validation
        if (options.preSubmitValidation) {
          setState('validating');
          accessibilityService.announceStatus('Validating form data...');

          const isValid = await options.preSubmitValidation(data);
          if (!isValid) {
            throw new Error('Validation failed');
          }
        }

        setState(currentState);

        // Transform data if needed
        let submissionData = data;
        if (options.transformData) {
          submissionData = await options.transformData(data);
        }

        // Perform submission with timeout
        const submissionPromise = onSubmit(submissionData);
        const result = await performanceService.withTimeout(submissionPromise);

        // Transform result if needed
        let finalResult = result;
        if (options.transformResult) {
          finalResult = await options.transformResult(result);
        }

        // Post-submission validation
        if (options.postSubmitValidation) {
          const isValid = await options.postSubmitValidation(finalResult);
          if (!isValid) {
            throw new Error('Post-submission validation failed');
          }
        }

        // Success handling
        const duration = performanceService.endTiming(true);
        setState('success');
        setLastResult(finalResult);
        setLastSubmitted(Date.now());
        setLastSubmittedData(data);
        setSubmissionDuration(duration);
        retryService.resetAttempts();

        // Show success toast using integrated toast service
        FormSubmissionToastService.showSuccessToast(
          toastConfig,
          data,
          finalResult
        );

        // Announce success
        accessibilityService.announceStatus(
          'Form submitted successfully',
          'assertive'
        );
        accessibilityService.manageFocus('success', options.formFocus);

        // Reset form if configured
        if (options.resetOnSuccess && options.formReset) {
          options.formReset();
        }

        // Call success callback
        if (options.onSuccess) {
          await options.onSuccess(data, finalResult);
        }

        // Call completion callback
        if (options.onSubmitComplete) {
          await options.onSubmitComplete(data, true);
        }
      } catch (error_: unknown) {
        const errorObj =
          error_ instanceof Error ? error_ : new Error(String(error_));
        const duration = performanceService.endTiming(false);

        // Check if we should retry
        const shouldRetry =
          !isRetryAttempt && retryService.shouldRetry(errorObj);

        if (shouldRetry) {
          setState('retrying');
          const delay = retryService.getRetryDelay();
          retryService.incrementAttempt();

          accessibilityService.announceStatus(
            `Retrying in ${delay}ms... (Attempt ${retryService.getCurrentAttempt()}/${retryConfig.maxAttempts})`
          );

          await retryService.sleep(delay);
          return performSubmission(data, true);
        }

        // Handle error
        setState('error');
        const errorMessage =
          errorObj.message ||
          toastConfig.errorMessage ||
          'An unexpected error occurred';
        setError(errorMessage);
        setErrorObject(errorObj);
        setSubmissionDuration(duration);

        // Show error toast using integrated toast service
        FormSubmissionToastService.showErrorToast(toastConfig, errorObj, data);

        // Update accessibility
        accessibilityService.updateErrorMessage(errorMessage);
        accessibilityService.announceStatus(
          `Error: ${errorMessage}`,
          'assertive'
        );
        accessibilityService.manageFocus('error', options.formFocus);

        // Call error callback
        if (options.onError) {
          await options.onError(errorObj, data);
        }

        // Call completion callback
        if (options.onSubmitComplete) {
          await options.onSubmitComplete(data, false);
        }
      }
    },
    [
      onSubmit,
      options,
      retryService,
      performanceService,
      accessibilityService,
      toastConfig,
      retryConfig.maxAttempts,
      retryAttempt,
    ]
  );

  // Debounced submit handler using performance service
  const handleSubmit = useCallback(
    async (data: T, event?: React.BaseSyntheticEvent): Promise<void> => {
      // Prevent default form submission
      if (event) {
        event.preventDefault();
      }

      // Apply debouncing using performance service
      const debouncedSubmit = performanceService.debounce(
        () => performSubmission(data),
        performanceConfig.debounceMs
      );

      debouncedSubmit();
    },
    [performSubmission, performanceService, performanceConfig.debounceMs]
  );

  // Retry handler
  const retry = useCallback(async (): Promise<void> => {
    if (lastSubmittedData) {
      retryService.resetAttempts(); // Reset retry count for manual retry
      await performSubmission(lastSubmittedData);
    }
  }, [lastSubmittedData, performSubmission, retryService]);

  // ARIA attributes using accessibility service
  const ariaAttributes = accessibilityService.generateAriaAttributes(
    isLoading,
    !!error,
    state
  );

  // Return comprehensive result object
  return {
    // Core State
    isLoading,
    state,
    error,
    errorObject,
    isSuccess,
    isValidating,
    isRetrying,

    // Submission Data
    lastSubmittedData,
    lastSubmitted,
    lastResult,
    retryAttempt,

    // Actions
    handleSubmit,
    clearError,
    reset,
    retry,
    cancel,

    // Accessibility
    ariaAttributes,

    // Performance Metrics
    submissionDuration,
    metrics: performanceService.getMetrics(),
  };
};
