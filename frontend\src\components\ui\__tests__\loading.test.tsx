import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import React from 'react';

import {
  DataLoader,
  ErrorDisplay,
  LoadingSpinner,
  SkeletonLoader,
} from '../loading';

describe('LoadingSpinner', () => {
  it('renders with default size', () => {
    render(<LoadingSpinner />);

    const spinner = document.querySelector('.animate-spin');
    expect(spinner).toBeInTheDocument();
    expect(spinner?.className).toContain('h-6 w-6'); // Default md size
  });

  it('renders with custom size', () => {
    render(<LoadingSpinner size="lg" />);

    const spinner = document.querySelector('.animate-spin');
    expect(spinner).toBeInTheDocument();
    expect(spinner?.className).toContain('h-8 w-8'); // lg size
  });

  it('renders with text when provided', () => {
    render(<LoadingSpinner text="Loading data..." />);

    expect(screen.getByText('Loading data...')).toBeInTheDocument();
  });

  it('renders as full page overlay when fullPage is true', () => {
    render(<LoadingSpinner fullPage />);

    const overlay = document.querySelector('.fixed.inset-0');
    expect(overlay).toBeInTheDocument();
  });
});

describe('SkeletonLoader', () => {
  it('renders default skeleton', () => {
    render(<SkeletonLoader count={3} />);

    const skeletons = document.querySelectorAll('.animate-pulse');
    expect(skeletons.length).toBe(3);
  });

  it('renders card skeleton', () => {
    render(<SkeletonLoader count={2} variant="card" />);

    // Check for card-specific elements
    const aspectRatios = document.querySelectorAll(String.raw`.aspect-\[16\/10\]`);
    expect(aspectRatios.length).toBe(2);
  });

  it('renders table skeleton', () => {
    render(<SkeletonLoader count={2} variant="table" />);

    // Check for table header row and data rows
    const rows = document.querySelectorAll('.flex.gap-4');
    // Header row + 2 data rows
    expect(rows.length).toBe(3);
  });

  it('renders list skeleton', () => {
    render(<SkeletonLoader count={2} variant="list" />);

    // Check for list items with avatar and content
    const listItems = document.querySelectorAll('.flex.items-center.gap-4');
    expect(listItems.length).toBe(2);

    // Check for rounded avatars
    const avatars = document.querySelectorAll('.rounded-full');
    expect(avatars.length).toBe(2);
  });

  it('renders stats skeleton', () => {
    render(<SkeletonLoader count={2} variant="stats" />);

    // Check for stats cards
    const cards = document.querySelectorAll('.rounded-lg.border.bg-card');
    expect(cards.length).toBe(2);
  });

  it('applies custom test ID', () => {
    render(<SkeletonLoader testId="custom-skeleton" />);

    expect(screen.getByTestId('custom-skeleton')).toBeInTheDocument();
  });
});

describe('ErrorDisplay', () => {
  it('renders error message', () => {
    render(<ErrorDisplay message="Failed to load data" />);

    expect(screen.getByText('Failed to load data')).toBeInTheDocument();
    expect(screen.getByText('Error')).toBeInTheDocument();
  });

  it('renders retry button when onRetry is provided', () => {
    const handleRetry = jest.fn();
    render(
      <ErrorDisplay message="Failed to load data" onRetry={handleRetry} />
    );

    const retryButton = screen.getByRole('button', { name: /try again/i });
    expect(retryButton).toBeInTheDocument();
  });

  it('calls onRetry when retry button is clicked', async () => {
    const handleRetry = jest.fn();
    const user = userEvent.setup();

    render(
      <ErrorDisplay message="Failed to load data" onRetry={handleRetry} />
    );

    const retryButton = screen.getByRole('button', { name: /try again/i });
    await user.click(retryButton);

    expect(handleRetry).toHaveBeenCalledTimes(1);
  });

  it('does not render retry button when onRetry is not provided', () => {
    render(<ErrorDisplay message="Failed to load data" />);

    const retryButton = screen.queryByRole('button', { name: /try again/i });
    expect(retryButton).not.toBeInTheDocument();
  });
});

describe('DataLoader', () => {
  it('renders loading component when isLoading is true', () => {
    render(
      <DataLoader
        data={null}
        isLoading={true}
        loadingComponent={<div data-testid="custom-loader">Custom Loader</div>}
      >
        {() => <div>Content</div>}
      </DataLoader>
    );

    expect(screen.getByTestId('custom-loader')).toBeInTheDocument();
    expect(screen.queryByText('Content')).not.toBeInTheDocument();
  });

  it('renders default loading spinner when isLoading is true and no custom loader', () => {
    render(
      <DataLoader data={null} isLoading={true}>
        {() => <div>Content</div>}
      </DataLoader>
    );

    const spinner = document.querySelector('.animate-spin');
    expect(spinner).toBeInTheDocument();
    expect(screen.queryByText('Content')).not.toBeInTheDocument();
  });

  it('renders error component when error is provided', () => {
    render(
      <DataLoader
        data={null}
        error="Failed to load"
        errorComponent={<div data-testid="custom-error">Custom Error</div>}
        isLoading={false}
      >
        {() => <div>Content</div>}
      </DataLoader>
    );

    expect(screen.getByTestId('custom-error')).toBeInTheDocument();
    expect(screen.queryByText('Content')).not.toBeInTheDocument();
  });

  it('renders default error display when error is provided and no custom error component', () => {
    render(
      <DataLoader data={null} error="Failed to load" isLoading={false}>
        {() => <div>Content</div>}
      </DataLoader>
    );

    expect(screen.getByText('Failed to load')).toBeInTheDocument();
    expect(screen.queryByText('Content')).not.toBeInTheDocument();
  });

  it('renders empty component when data is null', () => {
    render(
      <DataLoader
        data={null}
        emptyComponent={<div data-testid="custom-empty">No data</div>}
        isLoading={false}
      >
        {() => <div>Content</div>}
      </DataLoader>
    );

    expect(screen.getByTestId('custom-empty')).toBeInTheDocument();
    expect(screen.queryByText('Content')).not.toBeInTheDocument();
  });

  it('renders empty component when data is an empty array', () => {
    render(
      <DataLoader
        data={[]}
        emptyComponent={<div data-testid="custom-empty">No data</div>}
        isLoading={false}
      >
        {() => <div>Content</div>}
      </DataLoader>
    );

    expect(screen.getByTestId('custom-empty')).toBeInTheDocument();
    expect(screen.queryByText('Content')).not.toBeInTheDocument();
  });

  it('renders default empty state when data is null and no custom empty component', () => {
    render(
      <DataLoader data={null} isLoading={false}>
        {() => <div>Content</div>}
      </DataLoader>
    );

    expect(screen.getByText('No data available')).toBeInTheDocument();
    expect(screen.queryByText('Content')).not.toBeInTheDocument();
  });

  it('renders children with data when data is provided', () => {
    const testData = { name: 'Test' };

    render(
      <DataLoader data={testData} isLoading={false}>
        {data => <div>Hello, {data.name}</div>}
      </DataLoader>
    );

    expect(screen.getByText('Hello, Test')).toBeInTheDocument();
  });
});
