/**
 * Enhanced BaseApiService - Production-Ready Service Foundation
 *
 * This service provides a standardized foundation for all API services with:
 * - Circuit breaker protection (from AdminService patterns)
 * - Request caching and deduplication
 * - Comprehensive error handling with retry logic
 * - Performance monitoring and metrics
 * - Data transformation and validation
 *
 * Built upon proven patterns from the consolidated AdminService.
 */

import type { PaginatedResponse } from '@/types';

import type { ApiClient } from './apiClient';

import { ApiError, ServiceError } from './errors';

/**
 * Data transformer interface for consistent data transformation
 */
export interface DataTransformer<T> {
  fromApi?: (data: any) => T;
  toApi?: (data: any) => any;
}

// PaginatedResponse interface now imported from @/types for consistency

/**
 * Service configuration interface
 */
export interface ServiceConfig {
  cacheDuration?: number;
  circuitBreakerThreshold?: number;
  enableMetrics?: boolean;
  retryAttempts?: number;
}

/**
 * Service metrics interface
 */
export interface ServiceMetrics {
  averageResponseTime: number;
  cacheHitRatio: number;
  errorCount: number;
  requestCount: number;
}

/**
 * Simple circuit breaker implementation
 */
class CircuitBreaker {
  private failures = 0;
  private lastFailureTime = 0;
  private state: 'CLOSED' | 'HALF_OPEN' | 'OPEN' = 'CLOSED';

  constructor(
    private readonly name: string,
    private readonly threshold = 5,
    private readonly timeout = 60_000
  ) {}

  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === 'OPEN') {
      if (Date.now() - this.lastFailureTime > this.timeout) {
        this.state = 'HALF_OPEN';
      } else {
        throw new ServiceError(
          'Circuit breaker is OPEN',
          'CIRCUIT_BREAKER_OPEN'
        );
      }
    }

    try {
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }

  getState() {
    return {
      failures: this.failures,
      lastFailureTime: this.lastFailureTime,
      name: this.name,
      state: this.state,
    };
  }

  private onFailure() {
    this.failures++;
    this.lastFailureTime = Date.now();
    if (this.failures >= this.threshold) {
      this.state = 'OPEN';
    }
  }

  private onSuccess() {
    this.failures = 0;
    this.state = 'CLOSED';
  }
}

/**
 * Simple request cache implementation
 */
class RequestCache {
  private readonly cache = new Map<string, { data: any; expiry: number }>();

  clear(): void {
    this.cache.clear();
  }

  get<T>(key: string): null | T {
    const item = this.cache.get(key);
    if (!item) return null;

    if (Date.now() > item.expiry) {
      this.cache.delete(key);
      return null;
    }

    return item.data as T;
  }

  getStats() {
    return {
      keys: [...this.cache.keys()],
      size: this.cache.size,
    };
  }

  invalidate(key: string): void {
    this.cache.delete(key);
  }

  invalidatePattern(pattern: RegExp): void {
    for (const key of this.cache.keys()) {
      if (pattern.test(key)) {
        this.cache.delete(key);
      }
    }
  }

  set<T>(key: string, data: T, duration = 300_000): void {
    this.cache.set(key, {
      data,
      expiry: Date.now() + duration,
    });
  }
}

/**
 * Enhanced BaseApiService with production-grade patterns
 */
export abstract class BaseApiService<
  T,
  CreateT = Partial<T>,
  UpdateT = Partial<T>,
> {
  protected cache: RequestCache;
  // Service infrastructure (enhanced from AdminService patterns)
  protected circuitBreaker: CircuitBreaker;

  protected config: ServiceConfig;
  protected abstract endpoint: string;
  protected metrics: ServiceMetrics;
  protected abstract transformer: DataTransformer<T>;

  constructor(
    protected apiClient: ApiClient,
    config: ServiceConfig = {}
  ) {
    this.config = {
      cacheDuration: 5 * 60 * 1000, // 5 minutes default
      circuitBreakerThreshold: 5,
      enableMetrics: true,
      retryAttempts: 3,
      ...config,
    };

    // Initialize service infrastructure
    this.circuitBreaker = new CircuitBreaker(
      `${this.constructor.name}`,
      this.config.circuitBreakerThreshold
    );

    this.cache = new RequestCache();

    this.metrics = {
      averageResponseTime: 0,
      cacheHitRatio: 0,
      errorCount: 0,
      requestCount: 0,
    };
  }

  /**
   * Clear service cache
   */
  clearCache() {
    this.cache.clear();
  }

  /**
   * Create new entity
   */
  async create(data: CreateT): Promise<T> {
    return this.executeWithInfrastructure(
      null, // No caching for create operations
      async () => {
        const transformedData = this.transformer.toApi
          ? this.transformer.toApi(data)
          : data;
        const response = await this.apiClient.post<any>(
          this.endpoint,
          transformedData
        );

        // Invalidate related caches
        this.cache.invalidatePattern(new RegExp(`^${this.endpoint}:`));

        return this.transformer.fromApi
          ? this.transformer.fromApi(response)
          : response;
      }
    );
  }

  /**
   * Delete entity
   */
  async delete(id: number | string): Promise<void> {
    return this.executeWithInfrastructure(
      null, // No caching for delete operations
      async () => {
        await this.apiClient.delete(`${this.endpoint}/${id}`);

        // Invalidate related caches
        this.cache.invalidatePattern(new RegExp(`^${this.endpoint}:`));
        this.cache.invalidate(`${this.endpoint}:getById:${id}`);
      }
    );
  }

  /**
   * Get all entities with pagination and filtering
   */
  async getAll(filters?: Record<string, any>): Promise<PaginatedResponse<T>> {
    const cacheKey = `${this.endpoint}:getAll:${JSON.stringify(filters || {})}`;

    return this.executeWithInfrastructure(cacheKey, async () => {
      const params = new URLSearchParams();
      if (filters) {
        for (const [key, value] of Object.entries(filters)) {
          if (value !== undefined && value !== null) {
            params.append(key, String(value));
          }
        }
      }

      const queryString = params.toString();
      const url = queryString
        ? `${this.endpoint}?${queryString}`
        : this.endpoint;

      const response = await this.apiClient.get<any>(url);

      // Handle different response formats from backend
      let responseData: any[];
      let paginationInfo: any = {};

      // Handle wrapped response format from responseWrapper middleware
      if (response && response.status === 'success' && response.data) {
        // Backend returns wrapped response: {status: 'success', data: {...}, pagination?: {...}}
        const wrappedData = response.data;

        if (Array.isArray(wrappedData)) {
          // Direct array in wrapped response
          responseData = wrappedData;
          // Check for pagination at the top level of wrapped response
          if (response.pagination) {
            paginationInfo = {
              pagination: {
                hasNext: response.pagination.hasNext ?? false,
                hasPrevious: response.pagination.hasPrevious ?? false,
                limit: response.pagination.limit,
                page: response.pagination.page,
                total: response.pagination.total,
                totalPages:
                  response.pagination.totalPages ??
                  Math.ceil(
                    response.pagination.total / response.pagination.limit
                  ),
              },
            };
          }
        } else if (wrappedData && Array.isArray(wrappedData.data)) {
          // Nested data structure: {status: 'success', data: {data: [], pagination: {}}}
          responseData = wrappedData.data;

          // Handle nested pagination object
          if (wrappedData.pagination) {
            paginationInfo = {
              pagination: {
                hasNext: wrappedData.pagination.hasNext ?? false,
                hasPrevious: wrappedData.pagination.hasPrevious ?? false,
                limit: wrappedData.pagination.limit,
                page: wrappedData.pagination.page,
                total: wrappedData.pagination.total,
                totalPages:
                  wrappedData.pagination.totalPages ??
                  Math.ceil(
                    wrappedData.pagination.total / wrappedData.pagination.limit
                  ),
              },
            };
          } else if (response.pagination) {
            // Pagination at wrapper level
            paginationInfo = {
              pagination: {
                hasNext: response.pagination.hasNext ?? false,
                hasPrevious: response.pagination.hasPrevious ?? false,
                limit: response.pagination.limit,
                page: response.pagination.page,
                total: response.pagination.total,
                totalPages:
                  response.pagination.totalPages ??
                  Math.ceil(
                    response.pagination.total / response.pagination.limit
                  ),
              },
            };
          }
        } else {
          // Single item wrapped response - convert to array for consistency
          responseData = [wrappedData];
        }
      } else if (Array.isArray(response)) {
        // Direct array response (common backend pattern)
        responseData = response;
      } else if (response && (response.error || response.status === 'error')) {
        // Backend returned an error response
        throw new Error(
          response.message || response.error || 'API request failed'
        );
      } else if (response && typeof response === 'object') {
        // Single object response - convert to array for consistency
        responseData = [response];
      } else {
        // Unexpected response format
        throw new Error(
          `Invalid response format from API: ${JSON.stringify(response)}`
        );
      }

      // Transform data using the service's transformer
      const transformedData = responseData.map(item =>
        this.transformer.fromApi ? this.transformer.fromApi(item) : item
      );

      return {
        data: transformedData,
        ...paginationInfo,
      };
    });
  }

  /**
   * Get entity by ID
   */
  async getById(id: number | string): Promise<T> {
    const cacheKey = `${this.endpoint}:getById:${id}`;

    return this.executeWithInfrastructure(cacheKey, async () => {
      const response = await this.apiClient.get<any>(`${this.endpoint}/${id}`);
      return this.transformer.fromApi
        ? this.transformer.fromApi(response)
        : response;
    });
  }

  /**
   * Get service health status
   */
  getHealthStatus() {
    return {
      cacheStats: this.cache.getStats(),
      circuitBreakerState: this.circuitBreaker.getState(),
      endpoint: this.endpoint,
      metrics: this.metrics,
      service: this.constructor.name,
    };
  }

  /**
   * Reset service metrics
   */
  resetMetrics() {
    this.metrics = {
      averageResponseTime: 0,
      cacheHitRatio: 0,
      errorCount: 0,
      requestCount: 0,
    };
  }

  /**
   * Update existing entity
   */
  async update(id: number | string, data: UpdateT): Promise<T> {
    return this.executeWithInfrastructure(
      null, // No caching for update operations
      async () => {
        const transformedData = this.transformer.toApi
          ? this.transformer.toApi(data)
          : data;
        const response = await this.apiClient.put<any>(
          `${this.endpoint}/${id}`,
          transformedData
        );

        // Invalidate related caches
        this.cache.invalidatePattern(new RegExp(`^${this.endpoint}:`));
        this.cache.invalidate(`${this.endpoint}:getById:${id}`);

        return this.transformer.fromApi
          ? this.transformer.fromApi(response)
          : response;
      }
    );
  }

  /**
   * Execute operation with full service infrastructure
   * (circuit breaker, caching, error handling, metrics)
   */
  protected async executeWithInfrastructure<R>(
    cacheKey: null | string,
    operation: () => Promise<R>
  ): Promise<R> {
    const startTime = Date.now();

    try {
      this.metrics.requestCount++;

      // Try cache first if cacheKey provided
      if (cacheKey) {
        const cached = this.cache.get(cacheKey);
        if (cached) {
          this.metrics.cacheHitRatio =
            (this.metrics.cacheHitRatio * (this.metrics.requestCount - 1) + 1) /
            this.metrics.requestCount;
          return cached as R;
        }
      }

      // Execute with circuit breaker and retry logic
      const result = await this.circuitBreaker.execute(async () => {
        return withRetry(operation, this.config.retryAttempts);
      });

      // Cache result if cacheKey provided
      if (cacheKey && result) {
        this.cache.set(cacheKey, result, this.config.cacheDuration);
      }

      // Update metrics
      const responseTime = Date.now() - startTime;
      this.metrics.averageResponseTime =
        (this.metrics.averageResponseTime * (this.metrics.requestCount - 1) +
          responseTime) /
        this.metrics.requestCount;

      return result;
    } catch (error) {
      this.metrics.errorCount++;

      // Log the error for debugging with better error serialization
      console.error(`Service error in ${this.constructor.name}:`, {
        endpoint: this.endpoint,
        errorDetails:
          error instanceof Error
            ? {
                message: error.message,
                name: error.name,
                stack: error.stack,
              }
            : error,
        errorMessage: error instanceof Error ? error.message : String(error),
        errorType: error?.constructor?.name || typeof error,
        timestamp: new Date().toISOString(),
      });

      // Transform error to ServiceError
      if (error instanceof ServiceError) {
        throw error;
      }

      // Handle specific API errors
      if (error instanceof Error) {
        // Check if it's a network connectivity issue
        if (
          error.message.includes('fetch') ||
          error.message.includes('network')
        ) {
          throw new ServiceError(
            'Network connection failed. Please check your internet connection and try again.',
            'NETWORK_ERROR',
            undefined,
            { endpoint: this.endpoint, service: this.constructor.name }
          );
        }

        // Check if it's a backend server error
        if (
          error.message.includes('500') ||
          error.message.includes('Internal Server Error')
        ) {
          throw new ServiceError(
            'Server error occurred. Please try again later.',
            'SERVER_ERROR',
            undefined,
            { endpoint: this.endpoint, service: this.constructor.name }
          );
        }
      }

      throw new ServiceError(
        error instanceof Error ? error.message : 'Unknown service error',
        'SERVICE_ERROR',
        undefined,
        { endpoint: this.endpoint, service: this.constructor.name }
      );
    }
  }
}

/**
 * Simple retry utility
 */
async function withRetry<T>(
  operation: () => Promise<T>,
  maxAttempts = 3,
  delay = 1000
): Promise<T> {
  let lastError: Error;

  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error instanceof Error ? error : new Error('Unknown error');

      if (attempt === maxAttempts) {
        throw lastError;
      }

      // Wait before retry
      await new Promise(resolve => setTimeout(resolve, delay * attempt));
    }
  }

  throw lastError!;
}
