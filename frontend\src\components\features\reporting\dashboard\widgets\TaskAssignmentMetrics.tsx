/**
 * @file Task Assignment Metrics Component - Phase 2 Implementation
 * @description Task assignment analytics component following existing patterns
 *
 * SOLID Principles Applied:
 * - SRP: Single responsibility of displaying task assignment metrics
 * - OCP: Open for extension via props configuration and styling options
 * - DIP: Depends on existing component and utility abstractions
 *
 * Architecture Compliance:
 * - Follows existing component patterns
 * - Uses established styling and layout patterns
 * - Integrates with existing data structures
 * - Maintains consistent visual design
 */

'use client';

import React, { useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { TaskAssignmentMetrics as TaskAssignmentMetricsType } from '../../data/types/reporting';
import { Users, Download, TrendingUp, Clock, CheckCircle } from 'lucide-react';

interface TaskAssignmentMetricsProps {
  data?: TaskAssignmentMetricsType[];
  className?: string;
  showExportOptions?: boolean;
  maxDisplayItems?: number;
}

/**
 * @component TaskAssignmentMetrics
 * @description Task assignment analytics component following existing patterns
 *
 * Responsibilities:
 * - Display task assignment metrics for employees
 * - Show completion rates and performance indicators
 * - Provide export functionality
 * - Follow established component patterns
 * - Maintain consistent styling and behavior
 *
 * SOLID Principles Applied:
 * - SRP: Single responsibility of displaying assignment metrics
 * - OCP: Open for extension via props configuration
 * - DIP: Depends on existing component abstractions
 */
export const TaskAssignmentMetrics: React.FC<TaskAssignmentMetricsProps> = ({
  data = [],
  className = '',
  showExportOptions = true,
  maxDisplayItems = 10,
}) => {
  // Process and sort data for display
  const processedData = useMemo(() => {
    return data
      .sort((a, b) => b.completionRate - a.completionRate)
      .slice(0, maxDisplayItems);
  }, [data, maxDisplayItems]);

  // Calculate summary statistics
  const summaryStats = useMemo(() => {
    if (!data || data.length === 0) {
      return {
        totalEmployees: 0,
        totalAssignedTasks: 0,
        totalCompletedTasks: 0,
        averageCompletionRate: 0,
        averageCompletionTime: 0,
      };
    }

    const totalAssignedTasks = data.reduce(
      (sum, item) => sum + item.assignedTasks,
      0
    );
    const totalCompletedTasks = data.reduce(
      (sum, item) => sum + item.completedTasks,
      0
    );
    const averageCompletionRate =
      data.reduce((sum, item) => sum + item.completionRate, 0) / data.length;
    const averageCompletionTime =
      data.reduce((sum, item) => sum + item.averageCompletionTime, 0) /
      data.length;

    return {
      totalEmployees: data.length,
      totalAssignedTasks,
      totalCompletedTasks,
      averageCompletionRate: Math.round(averageCompletionRate),
      averageCompletionTime: Math.round(averageCompletionTime * 10) / 10,
    };
  }, [data]);

  // Export functionality following existing patterns
  const handleExportCSV = async () => {
    try {
      if (!data || data.length === 0) {
        console.warn('No data to export');
        return;
      }

      const csvData = data.map(item => ({
        'Employee Name': item.employeeName,
        'Assigned Tasks': item.assignedTasks,
        'Completed Tasks': item.completedTasks,
        'Completion Rate (%)': Math.round(item.completionRate),
        'Average Completion Time (days)': item.averageCompletionTime,
      }));

      if (csvData.length === 0) {
        console.warn('No data to export');
        return;
      }

      const csvContent = [
        Object.keys(csvData[0]!).join(','),
        ...csvData.map(row => Object.values(row).join(',')),
      ].join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `task-assignment-metrics-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Export failed:', error);
    }
  };

  // Get performance badge variant based on completion rate
  const getPerformanceBadge = (completionRate: number) => {
    if (completionRate >= 90)
      return { variant: 'default' as const, label: 'Excellent' };
    if (completionRate >= 75)
      return { variant: 'secondary' as const, label: 'Good' };
    if (completionRate >= 60)
      return { variant: 'outline' as const, label: 'Average' };
    return { variant: 'destructive' as const, label: 'Needs Improvement' };
  };

  // Handle empty data state
  if (!data || data.length === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Task Assignment Metrics
          </CardTitle>
        </CardHeader>
        <CardContent className="flex items-center justify-center py-12">
          <div className="text-center text-muted-foreground">
            <Users className="h-12 w-12 mx-auto mb-2 opacity-50" />
            <p>No task assignment data available</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
        <div>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Task Assignment Metrics
          </CardTitle>
          <p className="text-sm text-muted-foreground mt-1">
            Employee task performance and completion rates
          </p>
        </div>
        {showExportOptions && (
          <Button
            variant="outline"
            size="sm"
            onClick={handleExportCSV}
            className="h-8"
          >
            <Download className="h-3 w-3 mr-1" />
            Export CSV
          </Button>
        )}
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Summary Statistics */}
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
          <div className="flex flex-col items-center justify-center p-3 bg-blue-50 rounded-lg border">
            <div className="flex items-center gap-2 mb-2">
              <Users className="h-5 w-5 text-blue-600" />
              <span className="text-2xl font-bold text-blue-600">
                {summaryStats.totalEmployees}
              </span>
            </div>
            <p className="text-sm font-medium text-blue-700">Employees</p>
          </div>

          <div className="flex flex-col items-center justify-center p-3 bg-purple-50 rounded-lg border">
            <div className="flex items-center gap-2 mb-2">
              <TrendingUp className="h-5 w-5 text-purple-600" />
              <span className="text-2xl font-bold text-purple-600">
                {summaryStats.totalAssignedTasks}
              </span>
            </div>
            <p className="text-sm font-medium text-purple-700">Assigned</p>
          </div>

          <div className="flex flex-col items-center justify-center p-3 bg-green-50 rounded-lg border">
            <div className="flex items-center gap-2 mb-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <span className="text-2xl font-bold text-green-600">
                {summaryStats.totalCompletedTasks}
              </span>
            </div>
            <p className="text-sm font-medium text-green-700">Completed</p>
          </div>

          <div className="flex flex-col items-center justify-center p-3 bg-emerald-50 rounded-lg border">
            <div className="flex items-center gap-2 mb-2">
              <TrendingUp className="h-5 w-5 text-emerald-600" />
              <span className="text-2xl font-bold text-emerald-600">
                {summaryStats.averageCompletionRate}%
              </span>
            </div>
            <p className="text-sm font-medium text-emerald-700">Avg. Rate</p>
          </div>

          <div className="flex flex-col items-center justify-center p-3 bg-orange-50 rounded-lg border">
            <div className="flex items-center gap-2 mb-2">
              <Clock className="h-5 w-5 text-orange-600" />
              <span className="text-2xl font-bold text-orange-600">
                {summaryStats.averageCompletionTime}d
              </span>
            </div>
            <p className="text-sm font-medium text-orange-700">Avg. Time</p>
          </div>
        </div>

        {/* Employee Assignment Details */}
        <div className="space-y-4">
          <h4 className="text-sm font-medium text-muted-foreground">
            Employee Performance ({processedData.length} of {data.length})
          </h4>

          <div className="space-y-3">
            {processedData.map((employee, index) => {
              const performanceBadge = getPerformanceBadge(
                employee.completionRate
              );

              return (
                <div
                  key={employee.employeeId}
                  className="flex flex-col md:flex-row md:items-center justify-between p-4 border rounded-lg bg-muted/20 gap-4"
                >
                  <div className="flex-1 min-w-0">
                    <div className="flex flex-col sm:flex-row sm:items-center gap-2 mb-3">
                      <h5 className="font-medium truncate">
                        {employee.employeeName}
                      </h5>
                      <Badge
                        variant={performanceBadge.variant}
                        className="text-xs w-fit"
                      >
                        {performanceBadge.label}
                      </Badge>
                    </div>

                    <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 text-sm">
                      <div className="flex flex-col">
                        <span className="text-muted-foreground text-xs">
                          Assigned
                        </span>
                        <span className="font-medium text-base">
                          {employee.assignedTasks}
                        </span>
                      </div>
                      <div className="flex flex-col">
                        <span className="text-muted-foreground text-xs">
                          Completed
                        </span>
                        <span className="font-medium text-base">
                          {employee.completedTasks}
                        </span>
                      </div>
                      <div className="flex flex-col">
                        <span className="text-muted-foreground text-xs">
                          Rate
                        </span>
                        <span className="font-medium text-base">
                          {Math.round(employee.completionRate)}%
                        </span>
                      </div>
                      <div className="flex flex-col">
                        <span className="text-muted-foreground text-xs">
                          Avg. Time
                        </span>
                        <span className="font-medium text-base">
                          {employee.averageCompletionTime}d
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="w-full md:w-32 flex flex-col items-center">
                    <Progress
                      value={employee.completionRate}
                      className="h-3 w-full"
                    />
                    <p className="text-sm font-medium mt-2 text-center">
                      {Math.round(employee.completionRate)}%
                    </p>
                  </div>
                </div>
              );
            })}
          </div>

          {data.length > maxDisplayItems && (
            <div className="text-center pt-2">
              <p className="text-sm text-muted-foreground">
                Showing top {maxDisplayItems} employees by completion rate
              </p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
