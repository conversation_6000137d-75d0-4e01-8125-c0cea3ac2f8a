-- Migration: Update Auth Hook to Use auth.users.user_metadata
-- This updates the custom_access_token_hook to read from auth.users.user_metadata instead of user_profiles

-- Update the custom access token hook function to use auth.users.user_metadata
CREATE OR REPLACE FUNCTION public.custom_access_token_hook(event JSONB)
RETURNS JSONB
LANGUAGE plpgsql
STABLE
SECURITY DEFINER
AS $$
DECLARE
    claims JSONB;
    user_role TEXT;
    is_active BOOLEAN;
    employee_id INTEGER;
    user_metadata JSONB;
BEGIN
    -- Get the claims from the event
    claims := event->'claims';

    -- Fetch user metadata from auth.users instead of user_profiles
    SELECT user_metadata 
    INTO user_metadata
    FROM auth.users 
    WHERE id = (event->>'user_id')::UUID;

    -- Extract role, status, and employee_id from user_metadata
    IF user_metadata IS NOT NULL THEN
        user_role := user_metadata->>'role';
        is_active := (user_metadata->>'is_active')::BOOLEAN;
        employee_id := (user_metadata->>'employee_id')::INTEGER;
    END IF;

    -- Set custom claims in the JWT
    IF user_role IS NOT NULL THEN
        claims := jsonb_set(claims, '{custom_claims}', jsonb_build_object(
            'user_role', user_role,
            'is_active', COALESCE(is_active, true),
            'employee_id', employee_id
        ));
    ELSE
        -- Default claims for users without metadata
        claims := jsonb_set(claims, '{custom_claims}', jsonb_build_object(
            'user_role', 'USER',
            'is_active', true,
            'employee_id', null
        ));
    END IF;

    -- Update the event with the new claims
    event := jsonb_set(event, '{claims}', claims);
    
    RETURN event;
END;
$$;

-- Grant necessary permissions for the updated function
GRANT EXECUTE ON FUNCTION public.custom_access_token_hook(JSONB) TO supabase_auth_admin;
GRANT EXECUTE ON FUNCTION public.custom_access_token_hook(JSONB) TO service_role;

-- Update helper functions to read from auth.users.user_metadata instead of user_profiles

-- Update get_user_role function
CREATE OR REPLACE FUNCTION public.get_user_role(user_id UUID)
RETURNS TEXT
LANGUAGE plpgsql
STABLE
SECURITY DEFINER
AS $$
DECLARE
    user_role TEXT;
    user_metadata JSONB;
BEGIN
    SELECT user_metadata INTO user_metadata
    FROM auth.users
    WHERE id = user_id;
    
    user_role := user_metadata->>'role';
    
    RETURN COALESCE(user_role, 'USER');
END;
$$;

-- Update get_user_employee_id function
CREATE OR REPLACE FUNCTION public.get_user_employee_id(user_id UUID)
RETURNS INTEGER
LANGUAGE plpgsql
STABLE
SECURITY DEFINER
AS $$
DECLARE
    employee_id INTEGER;
    user_metadata JSONB;
BEGIN
    SELECT user_metadata INTO user_metadata
    FROM auth.users
    WHERE id = user_id;
    
    employee_id := (user_metadata->>'employee_id')::INTEGER;
    
    RETURN employee_id;
END;
$$;

-- Update is_admin function
CREATE OR REPLACE FUNCTION public.is_admin(user_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
STABLE
SECURITY DEFINER
AS $$
DECLARE
    user_role TEXT;
    user_metadata JSONB;
    is_active BOOLEAN;
BEGIN
    SELECT user_metadata INTO user_metadata
    FROM auth.users
    WHERE id = user_id;
    
    user_role := user_metadata->>'role';
    is_active := COALESCE((user_metadata->>'is_active')::BOOLEAN, true);
    
    RETURN user_role IN ('ADMIN', 'SUPER_ADMIN') AND is_active;
END;
$$;

-- Update is_manager_or_above function
CREATE OR REPLACE FUNCTION public.is_manager_or_above(user_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
STABLE
SECURITY DEFINER
AS $$
DECLARE
    user_role TEXT;
    user_metadata JSONB;
    is_active BOOLEAN;
BEGIN
    SELECT user_metadata INTO user_metadata
    FROM auth.users
    WHERE id = user_id;
    
    user_role := user_metadata->>'role';
    is_active := COALESCE((user_metadata->>'is_active')::BOOLEAN, true);
    
    RETURN user_role IN ('MANAGER', 'ADMIN', 'SUPER_ADMIN') AND is_active;
END;
$$;

-- Grant permissions for updated helper functions
GRANT EXECUTE ON FUNCTION public.get_user_role(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.is_admin(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.is_manager_or_above(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_user_employee_id(UUID) TO authenticated;

-- Remove the trigger function that creates user_profiles (no longer needed)
DROP FUNCTION IF EXISTS public.handle_new_user() CASCADE;

-- Comment: The auth hook now reads from auth.users.user_metadata instead of user_profiles table
-- This eliminates the need for cross-schema queries and improves performance
