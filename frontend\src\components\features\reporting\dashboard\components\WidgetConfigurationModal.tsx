// frontend/src/components/features/reporting/dashboard/components/WidgetConfigurationModal.tsx

import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Settings, Eye, Database } from 'lucide-react';
import { WidgetConfig, useDashboardLayoutActions } from '../stores/useDashboardCustomizationStore';

interface WidgetConfigurationModalProps {
  widget: WidgetConfig | null;
  isOpen: boolean;
  onClose: () => void;
}

/**
 * Widget Configuration Modal
 * 
 * ENHANCED: Single Responsibility - Handles widget configuration UI
 * Integrates with existing dashboard customization store
 * Follows established modal patterns from the existing codebase
 */
export const WidgetConfigurationModal: React.FC<WidgetConfigurationModalProps> = ({
  widget,
  isOpen,
  onClose,
}) => {
  const { updateWidget } = useDashboardLayoutActions();
  const [localConfig, setLocalConfig] = useState<WidgetConfig | null>(widget);

  React.useEffect(() => {
    setLocalConfig(widget);
  }, [widget]);

  const handleSave = () => {
    if (localConfig) {
      updateWidget(localConfig.id, localConfig);
      onClose();
    }
  };

  const updateLocalConfig = (updates: Partial<WidgetConfig>) => {
    if (localConfig) {
      setLocalConfig({ ...localConfig, ...updates });
    }
  };

  if (!localConfig) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Configure Widget
          </DialogTitle>
          <DialogDescription>
            Customize the appearance and behavior of your widget.
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="general" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="general" className="flex items-center gap-1">
              <Settings className="h-3 w-3" />
              General
            </TabsTrigger>
            <TabsTrigger value="display" className="flex items-center gap-1">
              <Eye className="h-3 w-3" />
              Display
            </TabsTrigger>
            <TabsTrigger value="data" className="flex items-center gap-1">
              <Database className="h-3 w-3" />
              Data
            </TabsTrigger>
          </TabsList>

          <TabsContent value="general" className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="widget-title">Title</Label>
              <Input
                id="widget-title"
                value={localConfig.title}
                onChange={(e) => updateLocalConfig({ title: e.target.value })}
                placeholder="Enter widget title"
              />
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="widget-visible"
                checked={localConfig.visible}
                onCheckedChange={(visible) => updateLocalConfig({ visible })}
              />
              <Label htmlFor="widget-visible">Visible</Label>
            </div>

            <div className="space-y-2">
              <Label htmlFor="widget-type">Widget Type</Label>
              <Input
                id="widget-type"
                value={localConfig.type}
                onChange={(e) => updateLocalConfig({ type: e.target.value })}
                placeholder="chart, table, metric"
              />
            </div>
          </TabsContent>

          <TabsContent value="display" className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="widget-width">Width</Label>
                <Input
                  id="widget-width"
                  type="number"
                  value={localConfig.size.width}
                  onChange={(e) => updateLocalConfig({
                    size: { ...localConfig.size, width: parseInt(e.target.value) || 0 }
                  })}
                  min="1"
                  max="12"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="widget-height">Height</Label>
                <Input
                  id="widget-height"
                  type="number"
                  value={localConfig.size.height}
                  onChange={(e) => updateLocalConfig({
                    size: { ...localConfig.size, height: parseInt(e.target.value) || 0 }
                  })}
                  min="1"
                  max="12"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="widget-x">X Position</Label>
                <Input
                  id="widget-x"
                  type="number"
                  value={localConfig.position.x}
                  onChange={(e) => updateLocalConfig({
                    position: { ...localConfig.position, x: parseInt(e.target.value) || 0 }
                  })}
                  min="0"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="widget-y">Y Position</Label>
                <Input
                  id="widget-y"
                  type="number"
                  value={localConfig.position.y}
                  onChange={(e) => updateLocalConfig({
                    position: { ...localConfig.position, y: parseInt(e.target.value) || 0 }
                  })}
                  min="0"
                />
              </div>
            </div>
          </TabsContent>

          <TabsContent value="data" className="space-y-4">
            <div className="space-y-2">
              <Label>Widget-specific settings</Label>
              <p className="text-sm text-muted-foreground">
                Configure data sources and display options specific to this widget type.
              </p>
              
              {/* Widget-specific configuration based on type */}
              {localConfig.type === 'chart' && (
                <div className="space-y-3 p-3 border rounded">
                  <h4 className="font-medium">Chart Settings</h4>
                  <div className="space-y-2">
                    <Label htmlFor="chart-type">Chart Type</Label>
                    <Input
                      id="chart-type"
                      value={localConfig.settings?.chartType || ''}
                      onChange={(e) => updateLocalConfig({
                        settings: { ...localConfig.settings, chartType: e.target.value }
                      })}
                      placeholder="bar, line, pie, doughnut"
                    />
                  </div>
                </div>
              )}

              {localConfig.type === 'table' && (
                <div className="space-y-3 p-3 border rounded">
                  <h4 className="font-medium">Table Settings</h4>
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="table-pagination"
                      checked={localConfig.settings?.pagination || false}
                      onCheckedChange={(pagination) => updateLocalConfig({
                        settings: { ...localConfig.settings, pagination }
                      })}
                    />
                    <Label htmlFor="table-pagination">Enable Pagination</Label>
                  </div>
                </div>
              )}

              {localConfig.type === 'metric' && (
                <div className="space-y-3 p-3 border rounded">
                  <h4 className="font-medium">Metric Settings</h4>
                  <div className="space-y-2">
                    <Label htmlFor="metric-format">Number Format</Label>
                    <Input
                      id="metric-format"
                      value={localConfig.settings?.format || ''}
                      onChange={(e) => updateLocalConfig({
                        settings: { ...localConfig.settings, format: e.target.value }
                      })}
                      placeholder="number, currency, percentage"
                    />
                  </div>
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleSave}>
            Save Changes
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default WidgetConfigurationModal;
