import React from 'react';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReportingDashboard } from '../../../dashboard/ReportingDashboard';
import { ReportingDataService } from '../../../data/services/ReportingDataService';
import { reportingDataService } from '../../../data/services/ReportingDataService';
import type {
  DelegationStatusPrisma,
  TaskStatusPrisma,
  TaskPriorityPrisma,
} from '@/lib/types/domain';
import type { ServiceTypePrisma } from '../../../data/types/vehicleService';

// Mock the data service
jest.mock('../../../data/services/ReportingDataService');

const mockReportingDataService =
  reportingDataService as jest.Mocked<ReportingDataService>;

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        gcTime: 0,
      },
    },
  });

  return (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );
};

describe('ReportingDashboard Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Dashboard Loading and Data Flow', () => {
    it('should load dashboard with all widgets and display data correctly', async () => {
      // Arrange
      const mockDelegationAnalytics = {
        totalCount: 150,
        statusDistribution: [
          {
            status: 'PENDING' as DelegationStatusPrisma,
            count: 45,
            percentage: 30,
            color: '#orange',
          },
          {
            status: 'APPROVED' as DelegationStatusPrisma,
            count: 75,
            percentage: 50,
            color: '#green',
          },
          {
            status: 'COMPLETED' as DelegationStatusPrisma,
            count: 30,
            percentage: 20,
            color: '#blue',
          },
        ],
        trendData: [
          { date: '2024-06-01', created: 10, completed: 8, inProgress: 5 },
          { date: '2024-06-02', created: 12, completed: 10, inProgress: 7 },
        ],
        locationMetrics: [
          {
            location: 'New York',
            delegationCount: 50,
            averageDuration: 4.5,
            completionRate: 85,
          },
          {
            location: 'London',
            delegationCount: 40,
            averageDuration: 3.8,
            completionRate: 90,
          },
        ],
        summary: {
          totalDelegations: 150,
          activeDelegations: 45,
          completedDelegations: 105,
          totalDelegates: 25,
          averageDuration: 4.2,
          completionRate: 0.7,
        },
      };

      const mockTaskAnalytics = {
        totalCount: 200,
        statusDistribution: [
          {
            status: 'PENDING' as TaskStatusPrisma,
            count: 60,
            percentage: 30,
            color: '#orange',
          },
          {
            status: 'IN_PROGRESS' as TaskStatusPrisma,
            count: 80,
            percentage: 40,
            color: '#blue',
          },
          {
            status: 'COMPLETED' as TaskStatusPrisma,
            count: 60,
            percentage: 30,
            color: '#green',
          },
        ],
        priorityDistribution: [
          {
            priority: 'HIGH' as TaskPriorityPrisma,
            count: 50,
            percentage: 25,
            color: '#red',
          },
          {
            priority: 'MEDIUM' as TaskPriorityPrisma,
            count: 100,
            percentage: 50,
            color: '#yellow',
          },
          {
            priority: 'LOW' as TaskPriorityPrisma,
            count: 50,
            percentage: 25,
            color: '#green',
          },
        ],
        completionRate: 0.3,
        overdueCount: 15,
        averageCompletionTime: 2.5,
        assignmentMetrics: [
          {
            employeeId: 1,
            employeeName: 'John Doe',
            assignedTasks: 20,
            completedTasks: 15,
            completionRate: 0.75,
            averageCompletionTime: 2.2,
          },
        ],
      };

      const mockVehicleAnalytics = {
        totalCount: 50,
        serviceHistory: [],
        costAnalysis: {
          totalCost: 25000,
          averageCostPerService: 500,
          costByType: [
            {
              type: 'MAINTENANCE' as ServiceTypePrisma,
              cost: 15000,
              count: 30,
            },
            { type: 'REPAIR' as ServiceTypePrisma, cost: 10000, count: 20 },
          ],
          monthlyTrend: [
            { month: '2024-05', cost: 8000 },
            { month: '2024-06', cost: 9000 },
          ],
        },
        utilizationMetrics: [],
        maintenanceSchedule: [],
      };

      const mockEmployeeAnalytics = {
        totalCount: 25,
        performanceMetrics: [
          {
            employeeId: 1,
            employeeName: 'John Doe',
            completedDelegations: 15,
            completedTasks: 20,
            averageRating: 4.5,
            onTimePerformance: 0.9,
            workloadScore: 0.8,
          },
        ],
        delegationHistory: [],
        taskAssignments: [],
        availabilityMetrics: [],
      };

      // Mock service responses
      mockReportingDataService.getDelegationAnalytics.mockResolvedValue(
        mockDelegationAnalytics
      );
      mockReportingDataService.getTaskAnalytics.mockResolvedValue(
        mockTaskAnalytics
      );
      mockReportingDataService.getVehicleAnalytics.mockResolvedValue(
        mockVehicleAnalytics
      );
      mockReportingDataService.getEmployeeAnalytics.mockResolvedValue(
        mockEmployeeAnalytics
      );

      // Act
      render(
        <TestWrapper>
          <ReportingDashboard />
        </TestWrapper>
      );

      // Assert - Check loading states initially
      expect(screen.getByText(/loading/i)).toBeInTheDocument();

      // Wait for data to load and check dashboard content
      await waitFor(() => {
        expect(screen.getByText('150')).toBeInTheDocument(); // Total delegations
        expect(screen.getByText('200')).toBeInTheDocument(); // Total tasks
        expect(screen.getByText('50')).toBeInTheDocument(); // Total vehicles
        expect(screen.getByText('25')).toBeInTheDocument(); // Total employees
      });

      // Check that all widgets are rendered
      expect(screen.getByText(/delegation analytics/i)).toBeInTheDocument();
      expect(screen.getByText(/task analytics/i)).toBeInTheDocument();
      expect(screen.getByText(/vehicle analytics/i)).toBeInTheDocument();
      expect(screen.getByText(/employee analytics/i)).toBeInTheDocument();

      // Verify service calls were made with correct filters
      expect(
        mockReportingDataService.getDelegationAnalytics
      ).toHaveBeenCalledWith(
        expect.objectContaining({
          dateRange: expect.any(Object),
          status: expect.any(Array),
          locations: expect.any(Array),
          employees: expect.any(Array),
          vehicles: expect.any(Array),
        })
      );
    });

    it('should handle filter changes and update all widgets', async () => {
      // Arrange
      const initialMockData = {
        totalCount: 100,
        statusDistribution: [],
        trendData: [],
        locationMetrics: [],
        summary: {
          totalDelegations: 100,
          activeDelegations: 30,
          completedDelegations: 70,
          totalDelegates: 20,
          averageDuration: 3.5,
          completionRate: 0.7,
        },
      };

      const filteredMockData = {
        totalCount: 50,
        statusDistribution: [],
        trendData: [],
        locationMetrics: [],
        summary: {
          totalDelegations: 50,
          activeDelegations: 15,
          completedDelegations: 35,
          totalDelegates: 10,
          averageDuration: 3.2,
          completionRate: 0.7,
        },
      };

      mockReportingDataService.getDelegationAnalytics
        .mockResolvedValueOnce(initialMockData)
        .mockResolvedValueOnce(filteredMockData);

      mockReportingDataService.getTaskAnalytics.mockResolvedValue({
        totalCount: 0,
        statusDistribution: [],
        priorityDistribution: [],
        completionRate: 0,
        overdueCount: 0,
        averageCompletionTime: 0,
        assignmentMetrics: [],
      });

      mockReportingDataService.getVehicleAnalytics.mockResolvedValue({
        totalCount: 0,
        serviceHistory: [],
        costAnalysis: {
          totalCost: 0,
          averageCostPerService: 0,
          costByType: [],
          monthlyTrend: [],
        },
        utilizationMetrics: [],
        maintenanceSchedule: [],
      });

      mockReportingDataService.getEmployeeAnalytics.mockResolvedValue({
        totalCount: 0,
        performanceMetrics: [],
        delegationHistory: [],
        taskAssignments: [],
        availabilityMetrics: [],
      });

      // Act
      render(
        <TestWrapper>
          <ReportingDashboard />
        </TestWrapper>
      );

      // Wait for initial load
      await waitFor(() => {
        expect(screen.getByText('100')).toBeInTheDocument();
      });

      // Apply filters
      const statusFilter = screen.getByLabelText(/status filter/i);
      fireEvent.change(statusFilter, { target: { value: 'PENDING' } });

      const applyFiltersButton = screen.getByText(/apply filters/i);
      fireEvent.click(applyFiltersButton);

      // Assert - Check that data updated after filter change
      await waitFor(() => {
        expect(screen.getByText('50')).toBeInTheDocument();
      });

      // Verify service was called again with new filters
      expect(
        mockReportingDataService.getDelegationAnalytics
      ).toHaveBeenCalledTimes(2);
      expect(
        mockReportingDataService.getDelegationAnalytics
      ).toHaveBeenLastCalledWith(
        expect.objectContaining({
          status: expect.arrayContaining(['PENDING']),
        })
      );
    });
  });

  describe('Error Handling Integration', () => {
    it('should display error states when services fail', async () => {
      // Arrange
      const errorMessage = 'Failed to fetch delegation analytics';
      mockReportingDataService.getDelegationAnalytics.mockRejectedValue(
        new Error(errorMessage)
      );

      mockReportingDataService.getTaskAnalytics.mockResolvedValue({
        totalCount: 0,
        statusDistribution: [],
        priorityDistribution: [],
        completionRate: 0,
        overdueCount: 0,
        averageCompletionTime: 0,
        assignmentMetrics: [],
      });

      mockReportingDataService.getVehicleAnalytics.mockResolvedValue({
        totalCount: 0,
        serviceHistory: [],
        costAnalysis: {
          totalCost: 0,
          averageCostPerService: 0,
          costByType: [],
          monthlyTrend: [],
        },
        utilizationMetrics: [],
        maintenanceSchedule: [],
      });

      mockReportingDataService.getEmployeeAnalytics.mockResolvedValue({
        totalCount: 0,
        performanceMetrics: [],
        delegationHistory: [],
        taskAssignments: [],
        availabilityMetrics: [],
      });

      // Act
      render(
        <TestWrapper>
          <ReportingDashboard />
        </TestWrapper>
      );

      // Assert
      await waitFor(() => {
        expect(
          screen.getByText(/error loading delegation data/i)
        ).toBeInTheDocument();
      });

      // Check that other widgets still load successfully
      expect(
        screen.queryByText(/error loading task data/i)
      ).not.toBeInTheDocument();
    });

    it('should allow retry when errors occur', async () => {
      // Arrange
      mockReportingDataService.getDelegationAnalytics
        .mockRejectedValueOnce(new Error('Network error'))
        .mockResolvedValueOnce({
          totalCount: 100,
          statusDistribution: [],
          trendData: [],
          locationMetrics: [],
          summary: {
            totalDelegations: 100,
            activeDelegations: 30,
            completedDelegations: 70,
            totalDelegates: 20,
            averageDuration: 3.5,
            completionRate: 0.7,
          },
        });

      // Mock other services to return empty data
      mockReportingDataService.getTaskAnalytics.mockResolvedValue({
        totalCount: 0,
        statusDistribution: [],
        priorityDistribution: [],
        completionRate: 0,
        overdueCount: 0,
        averageCompletionTime: 0,
        assignmentMetrics: [],
      });

      mockReportingDataService.getVehicleAnalytics.mockResolvedValue({
        totalCount: 0,
        serviceHistory: [],
        costAnalysis: {
          totalCost: 0,
          averageCostPerService: 0,
          costByType: [],
          monthlyTrend: [],
        },
        utilizationMetrics: [],
        maintenanceSchedule: [],
      });

      mockReportingDataService.getEmployeeAnalytics.mockResolvedValue({
        totalCount: 0,
        performanceMetrics: [],
        delegationHistory: [],
        taskAssignments: [],
        availabilityMetrics: [],
      });

      // Act
      render(
        <TestWrapper>
          <ReportingDashboard />
        </TestWrapper>
      );

      // Wait for error state
      await waitFor(() => {
        expect(
          screen.getByText(/error loading delegation data/i)
        ).toBeInTheDocument();
      });

      // Click retry button
      const retryButton = screen.getByText(/retry/i);
      fireEvent.click(retryButton);

      // Assert - Check that data loads successfully after retry
      await waitFor(() => {
        expect(screen.getByText('100')).toBeInTheDocument();
        expect(
          screen.queryByText(/error loading delegation data/i)
        ).not.toBeInTheDocument();
      });

      expect(
        mockReportingDataService.getDelegationAnalytics
      ).toHaveBeenCalledTimes(2);
    });
  });

  describe('Real-time Updates Integration', () => {
    it('should handle real-time data updates via WebSocket', async () => {
      // Arrange
      const initialData = {
        totalCount: 100,
        statusDistribution: [
          {
            status: 'PENDING' as DelegationStatusPrisma,
            count: 30,
            percentage: 30,
            color: '#orange',
          },
          {
            status: 'COMPLETED' as DelegationStatusPrisma,
            count: 70,
            percentage: 70,
            color: '#green',
          },
        ],
        trendData: [],
        locationMetrics: [],
        summary: {
          totalDelegations: 100,
          activeDelegations: 30,
          completedDelegations: 70,
          totalDelegates: 20,
          averageDuration: 3.5,
          completionRate: 0.7,
        },
      };

      const updatedData = {
        totalCount: 105,
        statusDistribution: [
          {
            status: 'PENDING' as DelegationStatusPrisma,
            count: 32,
            percentage: 30.5,
            color: '#orange',
          },
          {
            status: 'COMPLETED' as DelegationStatusPrisma,
            count: 73,
            percentage: 69.5,
            color: '#green',
          },
        ],
        trendData: [],
        locationMetrics: [],
        summary: {
          totalDelegations: 105,
          activeDelegations: 32,
          completedDelegations: 73,
          totalDelegates: 20,
          averageDuration: 3.4,
          completionRate: 0.695,
        },
      };

      mockReportingDataService.getDelegationAnalytics
        .mockResolvedValueOnce(initialData)
        .mockResolvedValueOnce(updatedData);

      // Mock other services
      mockReportingDataService.getTaskAnalytics.mockResolvedValue({
        totalCount: 0,
        statusDistribution: [],
        priorityDistribution: [],
        completionRate: 0,
        overdueCount: 0,
        averageCompletionTime: 0,
        assignmentMetrics: [],
      });

      mockReportingDataService.getVehicleAnalytics.mockResolvedValue({
        totalCount: 0,
        serviceHistory: [],
        costAnalysis: {
          totalCost: 0,
          averageCostPerService: 0,
          costByType: [],
          monthlyTrend: [],
        },
        utilizationMetrics: [],
        maintenanceSchedule: [],
      });

      mockReportingDataService.getEmployeeAnalytics.mockResolvedValue({
        totalCount: 0,
        performanceMetrics: [],
        delegationHistory: [],
        taskAssignments: [],
        availabilityMetrics: [],
      });

      // Act
      render(
        <TestWrapper>
          <ReportingDashboard />
        </TestWrapper>
      );

      // Wait for initial data
      await waitFor(() => {
        expect(screen.getByText('100')).toBeInTheDocument();
      });

      // Simulate real-time update (this would normally come from WebSocket)
      const refreshButton = screen.getByText(/refresh/i);
      fireEvent.click(refreshButton);

      // Assert - Check that data updated
      await waitFor(() => {
        expect(screen.getByText('105')).toBeInTheDocument();
      });

      expect(
        mockReportingDataService.getDelegationAnalytics
      ).toHaveBeenCalledTimes(2);
    });
  });

  describe('Performance Integration', () => {
    it('should handle large datasets efficiently', async () => {
      // Arrange
      const largeDataset = {
        totalCount: 10000,
        statusDistribution: Array.from({ length: 5 }, (_, i) => ({
          status: `STATUS_${i}` as DelegationStatusPrisma,
          count: 2000,
          percentage: 20,
          color: `#color${i}`,
        })),
        trendData: Array.from({ length: 365 }, (_, i) => ({
          date: `2024-${String(Math.floor(i / 30) + 1).padStart(2, '0')}-${String((i % 30) + 1).padStart(2, '0')}`,
          created: Math.floor(Math.random() * 50),
          completed: Math.floor(Math.random() * 40),
          inProgress: Math.floor(Math.random() * 20),
        })),
        locationMetrics: Array.from({ length: 100 }, (_, i) => ({
          location: `Location ${i}`,
          delegationCount: Math.floor(Math.random() * 100),
          averageDuration: Math.random() * 10,
          completionRate: Math.random(),
        })),
        summary: {
          totalDelegations: 10000,
          activeDelegations: 3000,
          completedDelegations: 7000,
          totalDelegates: 500,
          averageDuration: 4.2,
          completionRate: 0.7,
        },
      };

      mockReportingDataService.getDelegationAnalytics.mockResolvedValue(
        largeDataset
      );

      // Mock other services with empty data for this test
      mockReportingDataService.getTaskAnalytics.mockResolvedValue({
        totalCount: 0,
        statusDistribution: [],
        priorityDistribution: [],
        completionRate: 0,
        overdueCount: 0,
        averageCompletionTime: 0,
        assignmentMetrics: [],
      });

      mockReportingDataService.getVehicleAnalytics.mockResolvedValue({
        totalCount: 0,
        serviceHistory: [],
        costAnalysis: {
          totalCost: 0,
          averageCostPerService: 0,
          costByType: [],
          monthlyTrend: [],
        },
        utilizationMetrics: [],
        maintenanceSchedule: [],
      });

      mockReportingDataService.getEmployeeAnalytics.mockResolvedValue({
        totalCount: 0,
        performanceMetrics: [],
        delegationHistory: [],
        taskAssignments: [],
        availabilityMetrics: [],
      });

      const startTime = performance.now();

      // Act
      render(
        <TestWrapper>
          <ReportingDashboard />
        </TestWrapper>
      );

      // Assert
      await waitFor(() => {
        expect(screen.getByText('10000')).toBeInTheDocument();
      });

      const endTime = performance.now();
      const renderTime = endTime - startTime;

      // Performance assertion - should render within reasonable time
      expect(renderTime).toBeLessThan(5000); // 5 seconds max for large dataset
    });
  });
});
