/**
 * @file Data transformer for Gift domain models.
 * @module transformers/giftTransformer
 */

import type {
  CreateGiftRequest,
  GiftApiResponse,
  UpdateGiftRequest,
} from '../types/apiContracts';
import type { CreateGiftData, Gift, UpdateGiftData } from '../types/domain';

import { formatDateForApi } from '../utils/dateUtils';

/**
 * Transforms gift data between API response formats and frontend domain models.
 */
export const GiftTransformer = {
  /**
   * Converts an API Gift response into a frontend Gift domain model.
   * @param apiData - The data received from the API.
   * @returns The Gift domain model.
   */
  fromApi(apiData: GiftApiResponse): Gift {
    return {
      id: apiData.id,
      itemDescription: apiData.itemDescription,
      recipientId: apiData.recipientId,
      dateSent: apiData.dateSent,
      senderName: apiData.senderName,
      occasion: apiData.occasion ?? null,
      notes: apiData.notes ?? null,
      createdAt: apiData.createdAt,
      updatedAt: apiData.updatedAt,
      recipient: apiData.recipient
        ? {
            id: apiData.recipient.id,
            name: apiData.recipient.name,
            email: apiData.recipient.email ?? null,
            phone: apiData.recipient.phone ?? null,
            address: apiData.recipient.address ?? null,
            notes: apiData.recipient.notes ?? null,
            createdAt: apiData.recipient.createdAt,
            updatedAt: apiData.recipient.updatedAt,
          }
        : undefined,
    };
  },

  /**
   * Converts frontend CreateGiftData into an API request payload.
   * @param domainData - The domain data to transform.
   * @returns The API request payload.
   */
  toCreateRequest(domainData: CreateGiftData): CreateGiftRequest {
    return {
      itemDescription: domainData.itemDescription,
      recipientId: domainData.recipientId,
      dateSent: formatDateForApi(domainData.dateSent),
      senderName: domainData.senderName,
      occasion: domainData.occasion ?? null,
      notes: domainData.notes ?? null,
    };
  },

  /**
   * Converts frontend UpdateGiftData into an API request payload.
   * @param domainData - The domain data to transform.
   * @returns The API request payload.
   */
  toUpdateRequest(domainData: UpdateGiftData): UpdateGiftRequest {
    const request: UpdateGiftRequest = {};

    if (domainData.itemDescription !== undefined) {
      request.itemDescription = domainData.itemDescription;
    }
    if (domainData.recipientId !== undefined) {
      request.recipientId = domainData.recipientId;
    }
    if (domainData.dateSent !== undefined) {
      request.dateSent = formatDateForApi(domainData.dateSent);
    }
    if (domainData.senderName !== undefined) {
      request.senderName = domainData.senderName;
    }
    if (domainData.occasion !== undefined) {
      request.occasion = domainData.occasion;
    }
    if (domainData.notes !== undefined) {
      request.notes = domainData.notes;
    }

    return request;
  },

  /**
   * Transforms an array of API Gift responses into domain models.
   * @param apiDataArray - Array of API responses.
   * @returns Array of Gift domain models.
   */
  fromApiArray(apiDataArray: GiftApiResponse[]): Gift[] {
    return apiDataArray.map(this.fromApi);
  },
};
