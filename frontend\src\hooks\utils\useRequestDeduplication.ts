import { useCallback, useRef } from 'react';

/**
 * Global request deduplication instance
 * Use this for API calls that should be deduplicated across the entire app
 */
class GlobalRequestDeduplicator {
  private readonly pendingRequests = new Map<string, Promise<any>>();

  clear(): void {
    this.pendingRequests.clear();
  }

  async deduplicate<T>(key: string, requestFn: () => Promise<T>): Promise<T> {
    // If there's already a pending request for this key, return it
    if (this.pendingRequests.has(key)) {
      console.log(`🔄 Global Request DEDUPLICATED for ${key}`);
      return this.pendingRequests.get(key)!;
    }

    // Create new request and store it
    const request = requestFn().finally(() => {
      // Remove from pending requests when completed
      this.pendingRequests.delete(key);
    });

    this.pendingRequests.set(key, request);
    console.log(`🔄 Global Request STARTED for ${key}`);

    return request;
  }

  getPendingCount(): number {
    return this.pendingRequests.size;
  }

  getPendingKeys(): string[] {
    return [...this.pendingRequests.keys()];
  }
}

/**
 * Hook to prevent duplicate API requests when multiple components
 * try to fetch the same data simultaneously
 */
export function useRequestDeduplication() {
  const pendingRequests = useRef(new Map<string, Promise<any>>());

  const deduplicateRequest = useCallback(
    <T>(key: string, requestFn: () => Promise<T>): Promise<T> => {
      // If there's already a pending request for this key, return it
      if (pendingRequests.current.has(key)) {
        console.log(`🔄 Request DEDUPLICATED for ${key}`);
        return pendingRequests.current.get(key)!;
      }

      // Create new request and store it
      const request = requestFn().finally(() => {
        // Remove from pending requests when completed
        pendingRequests.current.delete(key);
      });

      pendingRequests.current.set(key, request);
      console.log(`🔄 Request STARTED for ${key}`);

      return request;
    },
    []
  );

  const clearPendingRequests = useCallback(() => {
    pendingRequests.current.clear();
  }, []);

  const getPendingRequestsCount = useCallback(() => {
    return pendingRequests.current.size;
  }, []);

  return {
    clearPendingRequests,
    deduplicateRequest,
    getPendingRequestsCount,
  };
}

export const globalRequestDeduplicator = new GlobalRequestDeduplicator();
