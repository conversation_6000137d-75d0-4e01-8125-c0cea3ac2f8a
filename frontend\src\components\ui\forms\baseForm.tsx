'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import {
  type DefaultValues,
  type FieldValues,
  type SubmitHandler,
  useForm,
} from 'react-hook-form';
import { type ZodSchema } from 'zod';

import { Form } from '@/components/ui/form';

export interface BaseFormProps<T extends FieldValues> {
  children: React.ReactNode;
  defaultValues?: DefaultValues<T>;
  onSubmit: (data: T) => Promise<void>;
  schema: ZodSchema<T>;
  className?: string;
  ariaAttributes?: Record<string, any>;
}

export const BaseForm = <T extends FieldValues>({
  children,
  defaultValues,
  onSubmit,
  schema,
  className = '',
  ariaAttributes = {},
}: BaseFormProps<T>) => {
  const form = useForm<T>({
    ...(defaultValues && { defaultValues }),
    resolver: zodResolver(schema),
  });

  const handleSubmit: SubmitHandler<T> = async data => {
    await onSubmit(data);
  };

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(handleSubmit)}
        className={className}
        {...ariaAttributes}
      >
        {children}
      </form>
    </Form>
  );
};
