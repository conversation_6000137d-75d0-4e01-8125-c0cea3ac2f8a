/**
 * @file Modern delegation card component aligned with design system
 * @module components/delegations/common/DelegationCard
 */

'use client';

import {
  AlertTriangle,
  ArrowRight,
  CalendarDays,
  Car,
  Clock,
  Eye,
  Info,
  MapPin,
  Plane,
  User,
  Users,
} from 'lucide-react';
import Link from 'next/link';

import type { Delegation } from '@/lib/types/domain';

import { ActionButton } from '@/components/ui/action-button';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { StatusBadge } from '@/components/ui/status-badge';
import { useDelegationInfo } from '@/hooks/domain/useDelegationInfo';
import {
  formatDelegationDate,
  getInfoIconColor,
} from '@/lib/utils/delegationUtils';
import { formatEmployeeName } from '@/lib/utils/formattingUtils';
import { cn } from '@/lib/utils';

interface DelegationCardProps {
  delegation: Delegation;
  className?: string;
}

/**
 * Modern delegation card component aligned with the established design system.
 *
 * Features:
 * - Consistent spacing and typography matching VehicleCard and StatCard
 * - Modern hover states and interactions
 * - Design system color usage with p-5 padding standard
 * - Responsive layout with proper information hierarchy
 * - Accessibility compliance
 * - Clean, professional appearance
 */
export default function DelegationCard({
  delegation,
  className,
}: DelegationCardProps) {
  // Extract delegation info using custom hook
  const {
    driverInfo,
    escortInfo,
    hasFlightDetails,
    isActive,
    needsEscortAssignment,
    vehicleInfo,
  } = useDelegationInfo(delegation);

  return (
    <Card
      className={cn(
        'flex h-full flex-col overflow-hidden border-border/60 bg-card shadow-md',
        'transition-all duration-200 ease-in-out',
        'hover:shadow-lg hover:border-primary/30',
        'group',
        className
      )}
    >
      {/* Status Indicator Line - Aligned with design system */}
      <div
        className={cn(
          'h-1 w-full transition-all duration-200',
          isActive
            ? 'bg-gradient-to-r from-primary to-accent'
            : delegation.status === 'Completed'
              ? 'bg-green-500'
              : delegation.status === 'Cancelled'
                ? 'bg-destructive'
                : 'bg-muted'
        )}
      />

      {/* Header - Consistent with VehicleCard p-5 padding */}
      <CardHeader className="p-5 pb-3">
        <div className="flex items-start justify-between gap-3">
          <div className="min-w-0 flex-1">
            <CardTitle className="text-xl font-semibold text-primary mb-1 line-clamp-2">
              {delegation.eventName}
            </CardTitle>
            <CardDescription className="flex items-center gap-2 text-sm text-muted-foreground">
              <MapPin className="size-4 text-muted-foreground shrink-0" />
              <span className="truncate">{delegation.location}</span>
            </CardDescription>
          </div>

          {/* Status Badge */}
          <StatusBadge
            size="sm"
            status={delegation.status}
            className="shrink-0"
          />
        </div>

        {/* Active Indicator - Refined styling */}
        {isActive && (
          <div className="flex w-fit items-center gap-2 rounded-full bg-primary/10 px-3 py-1.5 border border-primary/20 mt-3">
            <div className="size-2 animate-pulse rounded-full bg-primary" />
            <span className="text-xs font-medium text-primary">
              Currently Active
            </span>
          </div>
        )}
      </CardHeader>

      {/* Content - Consistent p-5 padding */}
      <CardContent className="flex-1 p-5 pt-0">
        {/* Duration Section - Simplified and clean */}
        <div className="mb-4">
          <div className="flex items-center gap-2 mb-2">
            <CalendarDays className="size-4 text-muted-foreground" />
            <span className="text-sm font-medium text-muted-foreground">
              Duration
            </span>
          </div>
          <p className="text-sm font-medium">
            {formatDelegationDate(delegation.durationFrom)} -{' '}
            {formatDelegationDate(delegation.durationTo)}
          </p>
        </div>

        <Separator className="my-4" />

        {/* Key Information - Clean grid layout */}
        <div className="space-y-3">
          {/* Delegates Count */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Users className="size-4 text-muted-foreground" />
              <span className="text-sm font-medium">Delegates</span>
            </div>
            <Badge variant="secondary" className="text-xs">
              {delegation.delegates?.length ?? 0}
            </Badge>
          </div>

          {/* Flight Details */}
          {hasFlightDetails && (
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Plane className="size-4 text-blue-600" />
                <span className="text-sm font-medium">Flight Details</span>
              </div>
              <Badge
                variant="outline"
                className="text-xs text-blue-600 border-blue-200"
              >
                Available
              </Badge>
            </div>
          )}

          {/* Assignments - Clean, minimal display */}
          {(escortInfo || driverInfo || vehicleInfo) && (
            <>
              <Separator className="my-3" />
              <div className="space-y-2">
                <span className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
                  Assignments
                </span>
                <div className="space-y-2">
                  {escortInfo && (
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <User className="size-4 text-muted-foreground" />
                        <span className="text-sm">Escort</span>
                      </div>
                      <span className="text-sm font-medium truncate max-w-32">
                        {formatEmployeeName(escortInfo)}
                      </span>
                    </div>
                  )}

                  {driverInfo && (
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <User className="size-4 text-muted-foreground" />
                        <span className="text-sm">Driver</span>
                      </div>
                      <span className="text-sm font-medium truncate max-w-32">
                        {formatEmployeeName(driverInfo)}
                      </span>
                    </div>
                  )}

                  {vehicleInfo && (
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Car className="size-4 text-muted-foreground" />
                        <span className="text-sm">Vehicle</span>
                      </div>
                      <span className="text-sm font-medium truncate max-w-32">
                        {vehicleInfo.make} {vehicleInfo.model}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </>
          )}

          {/* Warning for unassigned escort - Simplified */}
          {needsEscortAssignment && (
            <div className="flex items-center gap-2 p-3 rounded-lg bg-destructive/10 border border-destructive/20 mt-3">
              <AlertTriangle className="size-4 text-destructive shrink-0" />
              <div>
                <p className="text-sm font-medium text-destructive">
                  Escort Required
                </p>
                <p className="text-xs text-destructive/80">
                  No escort assigned
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Notes Section - Simplified */}
        {delegation.notes && (
          <div className="mt-4 p-3 rounded-lg bg-muted/30">
            <div className="flex items-start gap-2">
              <Info className="size-4 text-muted-foreground mt-0.5 shrink-0" />
              <div className="min-w-0 flex-1">
                <p className="text-xs font-medium text-muted-foreground mb-1">
                  Notes
                </p>
                <p className="text-sm line-clamp-2 text-muted-foreground">
                  {delegation.notes}
                </p>
              </div>
            </div>
          </div>
        )}
      </CardContent>

      {/* Footer - Consistent with VehicleCard styling */}
      <CardFooter className="border-t bg-muted/20 p-4">
        <ActionButton
          actionType="primary"
          asChild
          className="w-full"
          icon={<Eye className="size-4" />}
        >
          <Link href={`/delegations/${delegation.id}`}>View Details</Link>
        </ActionButton>
      </CardFooter>
    </Card>
  );
}
