#!/usr/bin/env node

/**
 * Migration Script: user_profiles to auth.users.user_metadata
 *
 * This script migrates all user role and status data from the user_profiles table
 * to auth.users.user_metadata, enabling the switch to auth.users-only architecture.
 *
 * Usage: node migrate-to-auth-users.js [--dry-run]
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables:');
  console.error('   SUPABASE_URL:', supabaseUrl ? '✅ Set' : '❌ Missing');
  console.error('   SUPABASE_SERVICE_ROLE_KEY:', supabaseServiceKey ? '✅ Set' : '❌ Missing');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Check for dry-run flag
const isDryRun = process.argv.includes('--dry-run');

async function migrateToAuthUsers() {
  console.log('🔄 Starting migration from user_profiles to auth.users.user_metadata...');

  if (isDryRun) {
    console.log('🔍 DRY RUN MODE - No changes will be made');
  }

  try {
    // Step 1: Get all user_profiles data
    console.log('📋 Step 1: Fetching user_profiles data...');
    const { data: userProfiles, error: profilesError } = await supabase
      .from('user_profiles')
      .select('*')
      .order('created_at', { ascending: true });

    if (profilesError) {
      console.error('❌ Error fetching user_profiles:', profilesError);
      return false;
    }

    console.log(`✅ Found ${userProfiles.length} user profiles to migrate`);

    // Step 2: Get current auth.users data
    console.log('📋 Step 2: Fetching auth.users data...');
    const { data: authUsersResponse, error: authError } = await supabase.auth.admin.listUsers();

    if (authError) {
      console.error('❌ Error fetching auth users:', authError);
      return false;
    }

    const authUsers = authUsersResponse.users;
    console.log(`✅ Found ${authUsers.length} auth users`);

    // Step 3: Validate data consistency
    console.log('📋 Step 3: Validating data consistency...');
    const validationResult = validateDataConsistency(userProfiles, authUsers);

    if (!validationResult.success) {
      console.error('❌ Data validation failed:', validationResult.errors);
      return false;
    }

    console.log('✅ Data validation passed');

    // Step 4: Prepare migration data
    console.log('📋 Step 4: Preparing migration data...');
    const migrationPlan = prepareMigrationPlan(userProfiles, authUsers);

    console.log(`📊 Migration Plan:`);
    console.log(`   - Users to update: ${migrationPlan.updates.length}`);
    console.log(`   - Users with existing metadata: ${migrationPlan.existingMetadata.length}`);
    console.log(`   - Users without auth records: ${migrationPlan.orphaned.length}`);

    if (migrationPlan.orphaned.length > 0) {
      console.warn('⚠️  Found orphaned user_profiles (no corresponding auth.users):');
      migrationPlan.orphaned.forEach(profile => {
        console.warn(`     - ${profile.id} (role: ${profile.role})`);
      });
    }

    // Step 5: Execute migration (or simulate if dry-run)
    if (isDryRun) {
      console.log('🔍 DRY RUN - Would execute the following updates:');
      migrationPlan.updates.forEach((update, index) => {
        console.log(`   ${index + 1}. User ${update.userId}:`);
        console.log(`      - Role: ${update.metadata.role}`);
        console.log(`      - Active: ${update.metadata.is_active}`);
        console.log(`      - Employee ID: ${update.metadata.employee_id || 'null'}`);
      });

      return {
        success: true,
        dryRun: true,
        plan: migrationPlan,
      };
    } else {
      console.log('📋 Step 5: Executing migration...');
      const migrationResult = await executeMigration(migrationPlan.updates);

      if (!migrationResult.success) {
        console.error('❌ Migration failed:', migrationResult.errors);
        return false;
      }

      console.log('✅ Migration completed successfully');

      // Step 6: Verify migration
      console.log('📋 Step 6: Verifying migration...');
      const verificationResult = await verifyMigration(userProfiles);

      if (!verificationResult.success) {
        console.error('❌ Migration verification failed:', verificationResult.errors);
        console.error('🚨 Consider running rollback script!');
        return false;
      }

      console.log('✅ Migration verification passed');

      return {
        success: true,
        migrated: migrationResult.migrated,
        verified: verificationResult.verified,
      };
    }
  } catch (error) {
    console.error('❌ Migration failed with error:', error);
    return false;
  }
}

function validateDataConsistency(userProfiles, authUsers) {
  const errors = [];
  const authUserIds = new Set(authUsers.map(user => user.id));

  // Check if all user_profiles have corresponding auth.users
  userProfiles.forEach(profile => {
    if (!authUserIds.has(profile.id)) {
      errors.push(`user_profiles entry ${profile.id} has no corresponding auth.users record`);
    }
  });

  // Check for valid roles
  const validRoles = ['SUPER_ADMIN', 'ADMIN', 'MANAGER', 'USER', 'READONLY'];
  userProfiles.forEach(profile => {
    if (!validRoles.includes(profile.role)) {
      errors.push(`Invalid role "${profile.role}" for user ${profile.id}`);
    }
  });

  return {
    success: errors.length === 0,
    errors,
  };
}

function prepareMigrationPlan(userProfiles, authUsers) {
  const authUserMap = new Map(authUsers.map(user => [user.id, user]));
  const updates = [];
  const existingMetadata = [];
  const orphaned = [];

  userProfiles.forEach(profile => {
    const authUser = authUserMap.get(profile.id);

    if (!authUser) {
      orphaned.push(profile);
      return;
    }

    // Check if user already has role metadata
    const existingRole = authUser.user_metadata?.role;
    if (existingRole) {
      existingMetadata.push({
        userId: profile.id,
        existingRole,
        profileRole: profile.role,
      });
    }

    // Prepare update
    const newMetadata = {
      ...authUser.user_metadata,
      role: profile.role,
      is_active: profile.is_active,
      employee_id: profile.employee_id,
    };

    updates.push({
      userId: profile.id,
      currentMetadata: authUser.user_metadata,
      metadata: newMetadata,
    });
  });

  return {
    updates,
    existingMetadata,
    orphaned,
  };
}

async function executeMigration(updates) {
  const migrated = [];
  const errors = [];

  console.log(`🔄 Updating ${updates.length} users...`);

  for (let i = 0; i < updates.length; i++) {
    const update = updates[i];

    try {
      const { error } = await supabase.auth.admin.updateUserById(update.userId, {
        user_metadata: update.metadata,
      });

      if (error) {
        errors.push(`Failed to update user ${update.userId}: ${error.message}`);
      } else {
        migrated.push(update.userId);
        if ((i + 1) % 10 === 0) {
          console.log(`   ✅ Updated ${i + 1}/${updates.length} users`);
        }
      }
    } catch (error) {
      errors.push(`Exception updating user ${update.userId}: ${error.message}`);
    }
  }

  console.log(`✅ Migration completed: ${migrated.length} successful, ${errors.length} failed`);

  return {
    success: errors.length === 0,
    migrated,
    errors,
  };
}

async function verifyMigration(originalProfiles) {
  console.log('🔍 Verifying migrated data...');

  const { data: authUsersResponse, error } = await supabase.auth.admin.listUsers();

  if (error) {
    return {
      success: false,
      errors: [`Failed to fetch auth users for verification: ${error.message}`],
    };
  }

  const authUsers = authUsersResponse.users;
  const verified = [];
  const errors = [];

  originalProfiles.forEach(profile => {
    const authUser = authUsers.find(user => user.id === profile.id);

    if (!authUser) {
      errors.push(`Auth user not found for profile ${profile.id}`);
      return;
    }

    const metadata = authUser.user_metadata;

    if (metadata?.role !== profile.role) {
      errors.push(
        `Role mismatch for user ${profile.id}: expected ${profile.role}, got ${metadata?.role}`,
      );
    }

    if (metadata?.is_active !== profile.is_active) {
      errors.push(
        `Active status mismatch for user ${profile.id}: expected ${profile.is_active}, got ${metadata?.is_active}`,
      );
    }

    if (metadata?.employee_id !== profile.employee_id) {
      errors.push(
        `Employee ID mismatch for user ${profile.id}: expected ${profile.employee_id}, got ${metadata?.employee_id}`,
      );
    }

    if (errors.length === 0) {
      verified.push(profile.id);
    }
  });

  console.log(
    `✅ Verification completed: ${verified.length} verified, ${errors.length} mismatches`,
  );

  return {
    success: errors.length === 0,
    verified,
    errors,
  };
}

// Run migration if this script is executed directly
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const isMainModule = process.argv[1] === __filename;

if (isMainModule) {
  migrateToAuthUsers()
    .then(result => {
      if (result && result.success) {
        if (result.dryRun) {
          console.log('🔍 Dry run completed successfully!');
          console.log('💡 Run without --dry-run flag to execute the migration');
        } else {
          console.log('🎉 Migration completed successfully!');
          console.log(`📊 Results: ${result.migrated?.length || 0} users migrated`);
        }
        process.exit(0);
      } else {
        console.error('💥 Migration failed!');
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('💥 Migration failed with error:', error);
      process.exit(1);
    });
}

export { migrateToAuthUsers };
