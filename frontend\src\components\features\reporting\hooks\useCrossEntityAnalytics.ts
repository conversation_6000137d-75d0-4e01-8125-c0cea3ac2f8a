/**
 * @file useCrossEntityAnalytics.ts
 * @description Hook for fetching cross-entity analytics data following existing patterns
 */

import { useQuery } from '@tanstack/react-query';
import { useApiQuery } from '@/hooks/api';
import { apiClient } from '@/lib/api';
import type {
  ReportingFilters,
  CrossEntityAnalytics,
} from '../data/types/reporting';

/**
 * Hook for fetching cross-entity analytics data
 *
 * Follows existing patterns from other analytics hooks.
 * Integrates with the established API and caching patterns.
 *
 * @param filters - Optional reporting filters to apply
 * @returns Query result with cross-entity analytics data
 */
export const useCrossEntityAnalytics = (filters?: ReportingFilters) => {
  return useApiQuery(
    ['cross-entity-analytics', filters],
    async (): Promise<CrossEntityAnalytics> => {
      const queryParams = new URLSearchParams();

      if (filters?.dateRange) {
        // Defensive programming: Ensure dates are Date objects
        const fromDate =
          filters.dateRange.from instanceof Date
            ? filters.dateRange.from
            : new Date(filters.dateRange.from);
        const toDate =
          filters.dateRange.to instanceof Date
            ? filters.dateRange.to
            : new Date(filters.dateRange.to);

        queryParams.append(
          'dateRange.from',
          fromDate.toISOString().split('T')[0] || fromDate.toISOString()
        );
        queryParams.append(
          'dateRange.to',
          toDate.toISOString().split('T')[0] || toDate.toISOString()
        );
      }

      if (filters?.employees) {
        filters.employees.forEach(employee =>
          queryParams.append('employees', employee.toString())
        );
      }

      if (filters?.vehicles) {
        filters.vehicles.forEach(vehicle =>
          queryParams.append('vehicles', vehicle.toString())
        );
      }

      if (filters?.locations) {
        filters.locations.forEach(location =>
          queryParams.append('locations', location)
        );
      }

      // Note: includeCorrelations doesn't exist in ReportingFilters type
      // if (filters?.includeCorrelations) {
      //   queryParams.append('includeCorrelations', 'true');
      // }

      const url = `/reporting/cross-entity/analytics${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
      const result = (await apiClient.get(url)) as any;
      return result.data || result;
    },
    {
      cacheDuration: 5 * 60 * 1000, // 5 minutes
      enableRetry: true,
      retryAttempts: 3,
    }
  );
};

/**
 * Hook for fetching employee-vehicle correlations specifically
 *
 * @param filters - Optional reporting filters to apply
 * @returns Query result with employee-vehicle correlation data
 */
export const useEmployeeVehicleCorrelations = (filters?: ReportingFilters) => {
  return useApiQuery(
    ['employee-vehicle-correlations', filters],
    async () => {
      const queryParams = new URLSearchParams();

      if (filters?.dateRange) {
        queryParams.append('from', filters.dateRange.from.toISOString());
        queryParams.append('to', filters.dateRange.to.toISOString());
      }

      if (filters?.employees) {
        filters.employees.forEach(employee =>
          queryParams.append('employees', employee.toString())
        );
      }

      if (filters?.vehicles) {
        filters.vehicles.forEach(vehicle =>
          queryParams.append('vehicles', vehicle.toString())
        );
      }

      const url = `/reporting/employee-vehicle-correlations${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
      const result = (await apiClient.get(url)) as any;
      return result.data || result;
    },
    {
      cacheDuration: 5 * 60 * 1000,
      enableRetry: true,
    }
  );
};

/**
 * Hook for fetching task-delegation correlations
 *
 * @param filters - Optional reporting filters to apply
 * @returns Query result with task-delegation correlation data
 */
export const useTaskDelegationCorrelations = (filters?: ReportingFilters) => {
  return useApiQuery(
    ['task-delegation-correlations', filters],
    async () => {
      const queryParams = new URLSearchParams();

      if (filters?.dateRange) {
        queryParams.append('from', filters.dateRange.from.toISOString());
        queryParams.append('to', filters.dateRange.to.toISOString());
      }

      const url = `/reporting/task-delegation-correlations${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
      const result = (await apiClient.get(url)) as any;
      return result.data || result;
    },
    {
      cacheDuration: 5 * 60 * 1000,
      enableRetry: true,
    }
  );
};

/**
 * Hook for fetching performance-workload correlations
 *
 * @param filters - Optional reporting filters to apply
 * @returns Query result with performance-workload correlation data
 */
export const usePerformanceWorkloadCorrelations = (
  filters?: ReportingFilters
) => {
  return useApiQuery(
    ['performance-workload-correlations', filters],
    async () => {
      const queryParams = new URLSearchParams();

      if (filters?.dateRange) {
        queryParams.append('from', filters.dateRange.from.toISOString());
        queryParams.append('to', filters.dateRange.to.toISOString());
      }

      if (filters?.employees) {
        filters.employees.forEach(employee =>
          queryParams.append('employees', employee.toString())
        );
      }

      const url = `/reporting/performance-workload-correlations${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
      const result = (await apiClient.get(url)) as any;
      return result.data || result;
    },
    {
      cacheDuration: 5 * 60 * 1000,
      enableRetry: true,
    }
  );
};
