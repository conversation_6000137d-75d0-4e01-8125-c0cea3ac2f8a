/**
 * Middleware to sanitize task response data before it's sent to the frontend
 * Ensures that array fields are always arrays, even if they're null or undefined
 */

import type { NextFunction, Request, Response } from 'express';

import logger, { createContextLogger } from '../utils/logger.js';

/**
 * Sanitizes a single task object to ensure all array fields are arrays
 * @param task The task object to sanitize
 * @param requestInfo Additional request information for logging
 * @returns The sanitized task object
 */
const sanitizeTaskObject = (
  task: any,
  requestInfo: { path: string; taskId?: string } = { path: 'unknown' },
  contextLogger: any,
): any => {
  if (!task) return task;

  // Create a copy of the task to avoid modifying the original
  const sanitizedTask = { ...task };
  const taskId = task.id || requestInfo.taskId || 'unknown';

  // Ensure assignedEmployees is always an array
  if (!sanitizedTask.assignedEmployees || !Array.isArray(sanitizedTask.assignedEmployees)) {
    const originalValue =
      sanitizedTask.assignedEmployees === null ? 'null' : typeof sanitizedTask.assignedEmployees;

    contextLogger.warn('Task response sanitized: assignedEmployees', {
      field: 'assignedEmployees',
      originalValue,
      path: requestInfo.path,
      sanitizedTo: '[]',
      taskId,
    });
    sanitizedTask.assignedEmployees = [];
  }

  // Ensure requiredSkills is always an array
  if (!sanitizedTask.requiredSkills || !Array.isArray(sanitizedTask.requiredSkills)) {
    const originalValue =
      sanitizedTask.requiredSkills === null ? 'null' : typeof sanitizedTask.requiredSkills;

    contextLogger.warn('Task response sanitized: requiredSkills', {
      field: 'requiredSkills',
      originalValue,
      path: requestInfo.path,
      sanitizedTo: '[]',
      taskId,
    });
    sanitizedTask.requiredSkills = [];
  }

  // Ensure subTasks is always an array
  if (!sanitizedTask.subTasks || !Array.isArray(sanitizedTask.subTasks)) {
    const originalValue = sanitizedTask.subTasks === null ? 'null' : typeof sanitizedTask.subTasks;

    contextLogger.warn('Task response sanitized: subTasks', {
      field: 'subTasks',
      originalValue,
      path: requestInfo.path,
      sanitizedTo: '[]',
      taskId,
    });
    sanitizedTask.subTasks = [];
  }

  // Ensure statusHistory is always an array
  if (!sanitizedTask.statusHistory || !Array.isArray(sanitizedTask.statusHistory)) {
    const originalValue =
      sanitizedTask.statusHistory === null ? 'null' : typeof sanitizedTask.statusHistory;

    contextLogger.warn('Task response sanitized: statusHistory', {
      field: 'statusHistory',
      originalValue,
      path: requestInfo.path,
      sanitizedTo: '[]',
      taskId,
    });
    sanitizedTask.statusHistory = [];
  }

  return sanitizedTask;
};

/**
 * Intercepts response data to ensure all task objects have proper array fields
 * This prevents "findIndex is not a function" errors in the frontend
 */
export const sanitizeTaskResponse = (req: Request, res: Response, next: NextFunction): void => {
  // Create context logger for this middleware
  const contextLogger = createContextLogger({
    method: req.method,
    path: req.path,
    requestId: req.headers['x-request-id'] as string,
    service: 'task-response-sanitizer',
    userId: (req as any).userId,
  });

  // Store the original res.json method
  const originalJson = res.json;

  // Create request info object for logging
  const requestInfo = {
    path: req.path,
    taskId: req.params.id || 'unknown',
  };

  // Override res.json to sanitize the response data
  res.json = function (data: any): Response {
    try {
      // If the response is an array of tasks
      if (Array.isArray(data)) {
        data = data.map(item => sanitizeTaskObject(item, requestInfo, contextLogger));
        contextLogger.debug('Sanitized task response array', {
          taskCount: data.length,
          taskId: requestInfo.taskId,
        });
      }
      // If the response is a single task
      else if (data && typeof data === 'object' && (data.description || data.assignedEmployees)) {
        data = sanitizeTaskObject(data, requestInfo, contextLogger);
        contextLogger.debug('Sanitized single task response', {
          taskId: data.id || requestInfo.taskId,
        });
      }
    } catch (error) {
      contextLogger.error('Error in task response sanitizer middleware', {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        taskId: requestInfo.taskId,
      });
      // Continue with the original data if sanitization fails
    }

    // Call the original json method with the sanitized data
    return originalJson.call(this, data);
  };

  next();
};
