// reliability.integration.test.ts - Integration tests for reliability components

import { describe, test, expect, beforeEach, afterEach, jest } from '@jest/globals';
import express from 'express';
import request from 'supertest';
import {
  createMockRedis,
  createMockCircuitBreaker,
  createMockPrometheusClient,
  PerformanceMeasurer,
} from '../../reliabilityTestUtils.js';

// Mock dependencies
const mockRedis = createMockRedis();
const mockPrometheusClient = createMockPrometheusClient();

jest.mock('ioredis', () => ({
  default: jest.fn().mockImplementation(() => mockRedis),
}));

jest.mock('prom-client', () => mockPrometheusClient);

jest.mock('opossum', () => ({
  default: jest.fn().mockImplementation(() => createMockCircuitBreaker()),
}));

jest.mock('../../../utils/logger.js', () => ({
  default: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
}));

jest.mock('../../../utils/prisma.js', () => ({
  default: {
    $queryRaw: jest.fn().mockResolvedValue([{ result: 1 }]),
    $disconnect: jest.fn(),
  },
}));

// Import after mocking
import { metricsMiddleware } from '../../../services/metrics.service.js';
import { createDeduplicationMiddleware } from '../../../middleware/requestDeduplication.js';
import { createCircuitBreakerMiddleware } from '../../../middleware/circuitBreaker.js';

describe('Reliability Integration Tests', () => {
  let app: express.Application;
  let performanceMeasurer: PerformanceMeasurer;

  beforeEach(() => {
    app = express();
    app.use(express.json());
    performanceMeasurer = new PerformanceMeasurer();

    jest.clearAllMocks();
    mockRedis._getStore().clear();
    mockPrometheusClient._getMetrics().clear();
  });

  describe('Middleware Integration', () => {
    test('should integrate metrics, deduplication, and circuit breaker middleware', async () => {
      // Set up middleware stack
      app.use(metricsMiddleware);
      app.use(createDeduplicationMiddleware('api'));
      app.use(createCircuitBreakerMiddleware({ serviceName: 'test-api' }));

      // Add test route
      app.get('/api/test', (req, res) => {
        res.json({ message: 'success', timestamp: Date.now() });
      });

      // First request - should go through all middleware
      const response1 = await request(app).get('/api/test').expect(200);

      expect(response1.body.message).toBe('success');
      expect(response1.headers['x-cache']).toBe('MISS');

      // Second identical request - should hit cache
      const response2 = await request(app).get('/api/test').expect(200);

      expect(response2.headers['x-cache']).toBe('HIT');

      // Verify metrics were collected
      const metricsMap = mockPrometheusClient._getMetrics();
      expect(metricsMap.has('workhub_http_request_duration_seconds')).toBe(true);
    });

    test('should handle circuit breaker protection with deduplication', async () => {
      let requestCount = 0;

      app.use(metricsMiddleware);
      app.use(createDeduplicationMiddleware('api'));
      app.use(
        createCircuitBreakerMiddleware({
          serviceName: 'failing-service',
          fallbackStatusCode: 503,
        }),
      );

      // Add failing route
      app.get('/api/failing', (req, res) => {
        requestCount++;
        if (requestCount <= 3) {
          throw new Error('Service failure');
        }
        res.json({ message: 'success after recovery' });
      });

      // First few requests should fail and trigger circuit breaker
      await request(app).get('/api/failing').expect(503);
      await request(app).get('/api/failing').expect(503);
      await request(app).get('/api/failing').expect(503);

      // Circuit breaker should now be open
      const response = await request(app).get('/api/failing').expect(503);
      expect(response.body.error).toContain('temporarily unavailable');
    });

    test('should measure performance impact of middleware stack', async () => {
      app.use(metricsMiddleware);
      app.use(createDeduplicationMiddleware('performance'));

      app.get('/api/performance', (req, res) => {
        res.json({ data: 'performance test' });
      });

      // Measure performance of multiple requests
      const measurements = [];
      for (let i = 0; i < 10; i++) {
        const result = await performanceMeasurer.measure('middleware-stack', async () => {
          return request(app).get('/api/performance').expect(200);
        });
        measurements.push(result);
      }

      const stats = performanceMeasurer.getStats('middleware-stack');
      expect(stats).toBeDefined();
      expect(stats!.mean).toBeLessThan(100); // Should be under 100ms
      expect(stats!.p95).toBeLessThan(200); // 95th percentile under 200ms
    });
  });

  describe('End-to-End Reliability Workflows', () => {
    test('should handle complete request lifecycle with all reliability features', async () => {
      // Set up complete middleware stack
      app.use(metricsMiddleware);
      app.use(createDeduplicationMiddleware('api'));
      app.use(createCircuitBreakerMiddleware({ serviceName: 'complete-test' }));

      // Add route that simulates database operation
      app.post('/api/users', async (req, res) => {
        // Simulate database operation
        await new Promise(resolve => setTimeout(resolve, 50));
        res.status(201).json({
          id: 'user-123',
          name: req.body.name,
          created: new Date().toISOString(),
        });
      });

      // Test complete workflow
      const userData = { name: 'Test User' };

      const response = await request(app).post('/api/users').send(userData).expect(201);

      expect(response.body.id).toBe('user-123');
      expect(response.body.name).toBe('Test User');
      expect(response.headers['x-cache']).toBe('MISS');

      // Verify metrics were collected
      const metricsMap = mockPrometheusClient._getMetrics();
      expect(metricsMap.has('workhub_http_request_duration_seconds')).toBe(true);
      expect(metricsMap.has('workhub_http_requests_total')).toBe(true);
    });

    test('should handle error scenarios across all components', async () => {
      app.use(metricsMiddleware);
      app.use(createDeduplicationMiddleware('api'));
      app.use(
        createCircuitBreakerMiddleware({
          serviceName: 'error-test',
          fallbackStatusCode: 500,
        }),
      );

      // Add route that always fails
      app.get('/api/error', (req, res) => {
        throw new Error('Simulated service error');
      });

      // Multiple requests to trigger circuit breaker
      for (let i = 0; i < 5; i++) {
        await request(app).get('/api/error').expect(500);
      }

      // Circuit breaker should now be open
      const response = await request(app).get('/api/error').expect(500);
      expect(response.body.error).toContain('temporarily unavailable');

      // Verify error metrics were collected
      const metricsMap = mockPrometheusClient._getMetrics();
      const httpRequestsMetric = metricsMap.get('workhub_http_requests_total');
      expect(httpRequestsMetric.labels).toHaveBeenCalledWith(
        'GET',
        '/api/error',
        '500',
        'anonymous',
      );
    });

    test('should handle high concurrency scenarios', async () => {
      app.use(metricsMiddleware);
      app.use(createDeduplicationMiddleware('api'));

      app.get('/api/concurrent', (req, res) => {
        res.json({
          message: 'concurrent test',
          requestId: req.headers['x-request-id'] || 'unknown',
        });
      });

      // Send multiple concurrent requests
      const concurrentRequests = Array.from({ length: 20 }, (_, i) =>
        request(app).get('/api/concurrent').set('x-request-id', `req-${i}`).expect(200),
      );

      const responses = await Promise.all(concurrentRequests);

      // All requests should succeed
      expect(responses).toHaveLength(20);
      responses.forEach(response => {
        expect(response.body.message).toBe('concurrent test');
      });

      // Some requests should hit cache (identical requests)
      const cacheHits = responses.filter(r => r.headers['x-cache'] === 'HIT').length;
      const cacheMisses = responses.filter(r => r.headers['x-cache'] === 'MISS').length;

      expect(cacheHits + cacheMisses).toBe(20);
    });
  });

  describe('Monitoring Endpoints Integration', () => {
    test('should provide comprehensive monitoring data', async () => {
      // Set up monitoring endpoints
      app.get('/api/metrics', async (req, res) => {
        const { getMetrics } = await import('../../../services/metrics.service.js');
        const metrics = await getMetrics();
        res.set('Content-Type', 'text/plain');
        res.send(metrics);
      });

      app.get('/api/monitoring/circuit-breakers', async (req, res) => {
        const { getCircuitBreakerStatus } = await import(
          '../../../services/circuitBreaker.service.js'
        );
        const status = getCircuitBreakerStatus();
        res.json(status);
      });

      app.get('/api/monitoring/deduplication', async (req, res) => {
        const { getDeduplicationMetrics } = await import(
          '../../../middleware/requestDeduplication.js'
        );
        const metrics = getDeduplicationMetrics();
        res.json(metrics);
      });

      // Test metrics endpoint
      const metricsResponse = await request(app).get('/api/metrics').expect(200);

      expect(metricsResponse.text).toBe('# Mock metrics data');
      expect(metricsResponse.headers['content-type']).toContain('text/plain');

      // Test circuit breaker status endpoint
      const cbResponse = await request(app).get('/api/monitoring/circuit-breakers').expect(200);

      expect(Array.isArray(cbResponse.body)).toBe(true);

      // Test deduplication metrics endpoint
      const dedupResponse = await request(app).get('/api/monitoring/deduplication').expect(200);

      expect(dedupResponse.body).toHaveProperty('totalRequests');
      expect(dedupResponse.body).toHaveProperty('cacheHits');
      expect(dedupResponse.body).toHaveProperty('cacheMisses');
    });

    test('should handle monitoring endpoint errors gracefully', async () => {
      // Mock metrics export to fail
      mockPrometheusClient.register.metrics.mockRejectedValueOnce(
        new Error('Metrics export failed'),
      );

      app.get('/api/metrics', async (req, res) => {
        try {
          const { getMetrics } = await import('../../../services/metrics.service.js');
          const metrics = await getMetrics();
          res.set('Content-Type', 'text/plain');
          res.send(metrics);
        } catch (error) {
          res.status(500).json({ error: 'Failed to export metrics' });
        }
      });

      await request(app).get('/api/metrics').expect(500);
    });
  });

  describe('Performance Validation', () => {
    test('should meet performance targets under normal load', async () => {
      app.use(metricsMiddleware);
      app.use(createDeduplicationMiddleware('performance'));

      app.get('/api/performance-test', (req, res) => {
        res.json({ data: 'performance test data' });
      });

      // Measure performance under normal load
      const normalLoadResults = [];
      for (let i = 0; i < 50; i++) {
        const result = await performanceMeasurer.measure('normal-load', async () => {
          return request(app).get('/api/performance-test').expect(200);
        });
        normalLoadResults.push(result);
      }

      const stats = performanceMeasurer.getStats('normal-load');

      // Performance targets from reliability plan
      expect(stats!.p95).toBeLessThan(2000); // 95% under 2 seconds
      expect(stats!.mean).toBeLessThan(500); // Average under 500ms
      expect(stats!.max).toBeLessThan(5000); // Max under 5 seconds
    });

    test('should maintain performance with cache hits', async () => {
      app.use(createDeduplicationMiddleware('performance'));

      app.get('/api/cache-performance', (req, res) => {
        res.json({ cached: true, timestamp: Date.now() });
      });

      // First request - cache miss
      await performanceMeasurer.measure('cache-miss', async () => {
        return request(app).get('/api/cache-performance').expect(200);
      });

      // Subsequent requests - cache hits
      for (let i = 0; i < 10; i++) {
        await performanceMeasurer.measure('cache-hit', async () => {
          return request(app).get('/api/cache-performance').expect(200);
        });
      }

      const missStats = performanceMeasurer.getStats('cache-miss');
      const hitStats = performanceMeasurer.getStats('cache-hit');

      // Cache hits should be significantly faster
      expect(hitStats!.mean).toBeLessThan(missStats!.mean);
      expect(hitStats!.mean).toBeLessThan(50); // Cache hits under 50ms
    });
  });

  describe('Error Recovery and Resilience', () => {
    test('should recover from temporary failures', async () => {
      let failureCount = 0;
      const maxFailures = 3;

      app.use(
        createCircuitBreakerMiddleware({
          serviceName: 'recovery-test',
          fallbackStatusCode: 503,
        }),
      );

      app.get('/api/recovery', (req, res) => {
        failureCount++;
        if (failureCount <= maxFailures) {
          throw new Error('Temporary failure');
        }
        res.json({ message: 'recovered', attempt: failureCount });
      });

      // Initial failures
      for (let i = 0; i < maxFailures; i++) {
        await request(app).get('/api/recovery').expect(503);
      }

      // Circuit breaker should be open
      await request(app).get('/api/recovery').expect(503);

      // Simulate circuit breaker reset (would happen automatically in real scenario)
      const { circuitBreakerRegistry } = await import(
        '../../../services/circuitBreaker.service.js'
      );
      circuitBreakerRegistry.resetBreaker('recovery-test');

      // Should now succeed
      const response = await request(app).get('/api/recovery').expect(200);
      expect(response.body.message).toBe('recovered');
    });

    test('should handle cascading failures gracefully', async () => {
      // Simulate multiple services failing
      app.use(
        createCircuitBreakerMiddleware({
          serviceName: 'service-a',
          fallbackStatusCode: 503,
        }),
      );

      app.get('/api/cascade-test', (req, res) => {
        // Simulate dependency on multiple services
        throw new Error('Service A failed');
      });

      // Multiple failures should be handled gracefully
      for (let i = 0; i < 10; i++) {
        const response = await request(app).get('/api/cascade-test').expect(503);
        expect(response.body.error).toContain('temporarily unavailable');
      }

      // System should remain responsive
      expect(true).toBe(true); // Test passes if no hanging or crashes
    });
  });
});
