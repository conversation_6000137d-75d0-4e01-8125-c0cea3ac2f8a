/**
 * @file Reporting Data Types - Centralized Exports
 * @description Single source of truth for all reporting-related types following SOLID principles
 */

// ===== CORE INTERFACES (SRP: Single Responsibility) =====

// Filters and configuration
export type { ReportingFilters, ExportOptions } from './reporting';

// Analytics and metrics
export type {
  DelegationAnalytics,
  TaskMetrics,
  TrendData,
  LocationMetrics,
  StatusDistributionData,
  SummaryMetrics,
  ServiceHistoryData,
  ServiceCostSummary,
  TaskAnalyticsData,
} from './reporting';

// Data models
export type {
  Delegation,
  DelegationReportData,
  PaginatedDelegationsResponse,
} from './reporting';

// Chart and widget interfaces
export type { BaseChartProps, BaseWidgetProps } from './reporting';

// Service interfaces
export type { IReportingDataService } from './reporting';

// ===== RAW DATA TYPES (ISP: Interface Segregation) =====

// Raw API response types for transformers
export type {
  RawDelegationAnalytics,
  RawTaskMetrics,
  RawTrendData,
  RawLocationMetrics,
  RawServiceHistoryData,
  RawServiceCostSummary,
  RawTaskAnalyticsData,
} from './reporting';

// ===== ENUM RE-EXPORTS (DRY: Don't Repeat Yourself) =====

// Prisma enum types
export type {
  DelegationStatusPrisma,
  TaskStatusPrisma,
  TaskPriorityPrisma,
} from './reporting';

// Vehicle service types
export type { ServiceTypePrisma, ServiceStatusPrisma } from './vehicleService';

// ===== ANALYTICS TYPES =====

// Analytics-specific types
export type * from './analytics';

// ===== EXPORT TYPES =====

// Export-specific types
export type * from './export';

// ===== TYPE UTILITIES =====

// Import types for utility definitions
import type {
  DelegationReportData,
  TaskMetrics,
  ServiceHistoryData,
} from './reporting';

/**
 * Utility type for filtering interfaces
 */
export type FilterableEntity = 'delegation' | 'task' | 'vehicle' | 'employee';

/**
 * Utility type for exportable data
 */
export type ExportableData =
  | DelegationReportData
  | TaskMetrics
  | ServiceHistoryData;

/**
 * Utility type for chart data
 */
export type ChartDataType = 'status' | 'trend' | 'location' | 'metrics';
