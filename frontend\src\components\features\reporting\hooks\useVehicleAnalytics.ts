/**
 * @file useVehicleAnalytics.ts
 * @description Hook for fetching vehicle analytics data following existing patterns
 */

import { useQuery } from '@tanstack/react-query';
import { useApiQuery } from '@/hooks/api';
import { apiClient } from '@/lib/api';
import type {
  ReportingFilters,
  VehicleAnalytics,
} from '../data/types/reporting';

/**
 * Hook for fetching vehicle analytics data
 *
 * Follows existing patterns from useTaskAnalytics and other reporting hooks.
 * Integrates with the established API and caching patterns.
 *
 * @param filters - Optional reporting filters to apply
 * @returns Query result with vehicle analytics data
 */
export const useVehicleAnalytics = (filters?: ReportingFilters) => {
  return useApiQuery(
    ['vehicle-analytics', filters],
    async (): Promise<VehicleAnalytics> => {
      const queryParams = new URLSearchParams();

      if (filters?.dateRange) {
        // Defensive programming: Ensure dates are Date objects
        const fromDate =
          filters.dateRange.from instanceof Date
            ? filters.dateRange.from
            : new Date(filters.dateRange.from);
        const toDate =
          filters.dateRange.to instanceof Date
            ? filters.dateRange.to
            : new Date(filters.dateRange.to);

        queryParams.append(
          'dateRange.from',
          fromDate.toISOString().split('T')[0] || fromDate.toISOString()
        );
        queryParams.append(
          'dateRange.to',
          toDate.toISOString().split('T')[0] || toDate.toISOString()
        );
      }

      if (filters?.vehicles) {
        filters.vehicles.forEach(vehicle =>
          queryParams.append('vehicles', vehicle.toString())
        );
      }

      if (filters?.serviceTypes) {
        filters.serviceTypes.forEach(type =>
          queryParams.append('serviceTypes', type)
        );
      }

      if (filters?.serviceStatus) {
        filters.serviceStatus.forEach(status =>
          queryParams.append('serviceStatus', status)
        );
      }

      const url = `/reporting/vehicles/analytics${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
      const result = (await apiClient.get(url)) as any;
      return result.data || result;
    },
    {
      cacheDuration: 5 * 60 * 1000, // 5 minutes
      enableRetry: true,
      retryAttempts: 3,
    }
  );
};

/**
 * Hook for fetching vehicle utilization metrics specifically
 *
 * @param filters - Optional reporting filters to apply
 * @returns Query result with vehicle utilization data
 */
export const useVehicleUtilization = (filters?: ReportingFilters) => {
  return useApiQuery(
    ['vehicle-utilization', filters],
    async () => {
      // Get utilization data from vehicle analytics endpoint
      const queryParams = new URLSearchParams();

      if (filters?.dateRange) {
        queryParams.append('from', filters.dateRange.from.toISOString());
        queryParams.append('to', filters.dateRange.to.toISOString());
      }

      if (filters?.vehicles) {
        filters.vehicles.forEach(vehicle =>
          queryParams.append('vehicles', vehicle.toString())
        );
      }

      const url = `/reporting/vehicles/analytics${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
      const result = (await apiClient.get(url)) as any;
      const vehicleAnalytics = result.data || result;

      // Return the utilization metrics from vehicle analytics
      return vehicleAnalytics.utilizationMetrics || [];
    },
    {
      cacheDuration: 5 * 60 * 1000,
      enableRetry: true,
    }
  );
};

/**
 * Hook for fetching vehicle maintenance schedule
 *
 * @param filters - Optional reporting filters to apply
 * @returns Query result with maintenance schedule data
 */
export const useVehicleMaintenanceSchedule = (filters?: ReportingFilters) => {
  return useApiQuery(
    ['vehicle-maintenance-schedule', filters],
    async () => {
      // Get maintenance data from vehicle analytics endpoint
      const queryParams = new URLSearchParams();

      if (filters?.dateRange) {
        queryParams.append('from', filters.dateRange.from.toISOString());
        queryParams.append('to', filters.dateRange.to.toISOString());
      }

      if (filters?.vehicles) {
        filters.vehicles.forEach(vehicle =>
          queryParams.append('vehicles', vehicle.toString())
        );
      }

      const url = `/reporting/vehicles/analytics${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
      const result = (await apiClient.get(url)) as any;
      const vehicleAnalytics = result.data || result;

      // Return the maintenance schedule from vehicle analytics
      return vehicleAnalytics.maintenanceSchedule || [];
    },
    {
      cacheDuration: 5 * 60 * 1000,
      enableRetry: true,
    }
  );
};

/**
 * Hook for fetching vehicle cost analytics
 *
 * @param filters - Optional reporting filters to apply
 * @returns Query result with cost analytics data
 */
export const useVehicleCostAnalytics = (filters?: ReportingFilters) => {
  return useApiQuery(
    ['vehicle-cost-analytics', filters],
    async () => {
      const queryParams = new URLSearchParams();

      if (filters?.dateRange) {
        queryParams.append('from', filters.dateRange.from.toISOString());
        queryParams.append('to', filters.dateRange.to.toISOString());
      }

      if (filters?.vehicles) {
        filters.vehicles.forEach(vehicle =>
          queryParams.append('vehicles', vehicle.toString())
        );
      }

      if (filters?.serviceTypes) {
        filters.serviceTypes.forEach(type =>
          queryParams.append('serviceTypes', type)
        );
      }

      const url = `/reporting/vehicle-costs${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
      const result = (await apiClient.get(url)) as any;
      return result.data || result;
    },
    {
      cacheDuration: 5 * 60 * 1000,
      enableRetry: true,
    }
  );
};
