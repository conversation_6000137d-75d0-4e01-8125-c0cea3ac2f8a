/**
 * @file Delegation utility functions
 * @module lib/utils/delegationUtils
 */

import { format } from 'date-fns';
import type { Delegation } from '@/lib/types/domain';

/**
 * Get status color classes for delegation status
 * @param status - Delegation status
 * @returns CSS classes for status styling
 */
export const getStatusColor = (status: Delegation['status']) => {
  switch (status) {
    case 'Cancelled': {
      return 'bg-red-500/20 text-red-700 border-red-500/30 dark:text-red-400 dark:bg-red-500/10 dark:border-red-500/20';
    }
    case 'Completed': {
      return 'bg-purple-500/20 text-purple-700 border-purple-500/30 dark:text-purple-400 dark:bg-purple-500/10 dark:border-purple-500/20';
    }
    case 'Confirmed': {
      return 'bg-green-500/20 text-green-700 border-green-500/30 dark:text-green-400 dark:bg-green-500/10 dark:border-green-500/20';
    }
    case 'In_Progress': {
      return 'bg-yellow-500/20 text-yellow-700 border-yellow-500/30 dark:text-yellow-400 dark:bg-yellow-500/10 dark:border-yellow-500/20';
    }
    case 'Planned': {
      return 'bg-blue-500/20 text-blue-700 border-blue-500/30 dark:text-blue-400 dark:bg-blue-500/10 dark:border-blue-500/20';
    }
    default: {
      return 'bg-gray-500/20 text-gray-700 border-gray-500/30 dark:text-gray-400 dark:bg-gray-500/10 dark:border-gray-500/20';
    }
  }
};

/**
 * Format date for display in delegation cards
 * @param dateString - Date string or Date object
 * @returns Formatted date string
 */
export const formatDelegationDate = (dateString: Date | string): string => {
  const date = new Date(dateString);
  if (Number.isNaN(date.getTime())) {
    return 'N/A';
  }
  return format(date, 'MMM d, yyyy');
};

/**
 * Format time for display in delegation cards
 * @param dateString - Date string or Date object
 * @returns Formatted time string
 */
export const formatDelegationTime = (dateString: Date | string): string => {
  const date = new Date(dateString);
  if (Number.isNaN(date.getTime())) {
    return 'N/A';
  }
  return format(date, 'HH:mm');
};

/**
 * Get icon color classes for different info types
 * @param type - Type of information
 * @returns CSS classes for icon styling
 */
export const getInfoIconColor = (type: string) => {
  const colorMap: Record<string, string> = {
    duration: 'bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400',
    delegates:
      'bg-green-50 dark:bg-green-900/30 text-green-600 dark:text-green-400',
    flight:
      'bg-purple-50 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400',
    escort:
      'bg-amber-50 dark:bg-amber-900/30 text-amber-600 dark:text-amber-400',
    driver: 'bg-cyan-50 dark:bg-cyan-900/30 text-cyan-600 dark:text-cyan-400',
    vehicle: 'bg-slate-50 dark:bg-slate-800 text-slate-600 dark:text-slate-400',
    notes:
      'bg-indigo-50 dark:bg-indigo-900/30 text-indigo-600 dark:text-indigo-400',
  };

  return (
    colorMap[type] ||
    'bg-gray-50 dark:bg-gray-800 text-gray-600 dark:text-gray-400'
  );
};

/**
 * Check if delegation needs attention (missing escort)
 * @param delegation - Delegation object
 * @returns Boolean indicating if attention is needed
 */
export const needsAttention = (delegation: Delegation): boolean => {
  const hasEscort =
    delegation.escorts &&
    delegation.escorts.length > 0 &&
    delegation.escorts[0]?.employee;
  return (
    !hasEscort &&
    delegation.status !== 'Completed' &&
    delegation.status !== 'Cancelled'
  );
};

/**
 * Get delegation priority level
 * @param delegation - Delegation object
 * @returns Priority level string
 */
export const getDelegationPriority = (
  delegation: Delegation
): 'high' | 'medium' | 'low' => {
  if (delegation.status === 'In_Progress') return 'high';
  if (delegation.status === 'Confirmed') return 'medium';
  return 'low';
};
