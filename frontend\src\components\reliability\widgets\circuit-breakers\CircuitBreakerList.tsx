/**
 * @file Circuit breaker detailed list widget component.
 * This component provides a comprehensive list view of all circuit breakers with detailed
 * status information, failure counts, and recovery timing.
 * @module components/reliability/widgets/circuit-breakers/CircuitBreakerList
 */

'use client';

import {
  AlertTriangle,
  Clock,
  RefreshCw,
  Shield,
  ShieldAlert,
  ShieldCheck,
  Timer,
} from 'lucide-react';
import React, { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { useCircuitBreakerStatus } from '@/lib/stores/queries/useReliability';
import type { CircuitBreakerState } from '@/lib/types/domain';
import { cn } from '@/lib/utils';

/**
 * Props for the CircuitBreakerList component
 */
export interface CircuitBreakerListProps {
  /** Optional CSS class name for styling */
  className?: string;
  /** Maximum number of items to display */
  maxItems?: number;
}

/**
 * Circuit breaker detailed list widget component.
 * 
 * This component provides:
 * - Detailed list of all circuit breakers
 * - Real-time status updates
 * - Filtering and search capabilities
 * - Failure count and timing information
 * - Recovery status and next attempt timing
 * 
 * Features:
 * - Real-time WebSocket updates
 * - Search and filter functionality
 * - Sortable columns
 * - Responsive design
 * - Accessibility support
 * 
 * @param props - Component props
 * @returns JSX element representing the circuit breaker list
 */
export const CircuitBreakerList: React.FC<CircuitBreakerListProps> = ({
  className = '',
  maxItems = 10,
}) => {
  const { data: circuitBreakerData, isLoading, error, refetch } = useCircuitBreakerStatus();
  
  // Local state for filtering and search
  const [searchTerm, setSearchTerm] = useState('');
  const [stateFilter, setStateFilter] = useState<CircuitBreakerState | 'all'>('all');

  /**
   * Get status configuration for circuit breaker states
   */
  const getStateConfig = (state: CircuitBreakerState) => {
    switch (state) {
      case 'CLOSED':
        return {
          icon: ShieldCheck,
          color: 'text-green-600 dark:text-green-400',
          bgColor: 'bg-green-100 dark:bg-green-900/20',
          label: 'Healthy',
          variant: 'default' as const,
        };
      case 'HALF_OPEN':
        return {
          icon: Shield,
          color: 'text-yellow-600 dark:text-yellow-400',
          bgColor: 'bg-yellow-100 dark:bg-yellow-900/20',
          label: 'Testing',
          variant: 'secondary' as const,
        };
      case 'OPEN':
        return {
          icon: ShieldAlert,
          color: 'text-red-600 dark:text-red-400',
          bgColor: 'bg-red-100 dark:bg-red-900/20',
          label: 'Failed',
          variant: 'destructive' as const,
        };
    }
  };

  /**
   * Format time duration
   */
  const formatDuration = (timestamp?: string) => {
    if (!timestamp) return 'Never';
    
    const now = new Date();
    const time = new Date(timestamp);
    const diffMs = now.getTime() - time.getTime();
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMinutes / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffDays > 0) return `${diffDays}d ago`;
    if (diffHours > 0) return `${diffHours}h ago`;
    if (diffMinutes > 0) return `${diffMinutes}m ago`;
    return 'Just now';
  };

  /**
   * Format next attempt time
   */
  const formatNextAttempt = (timestamp?: string) => {
    if (!timestamp) return null;
    
    const now = new Date();
    const nextAttempt = new Date(timestamp);
    const diffMs = nextAttempt.getTime() - now.getTime();
    
    if (diffMs <= 0) return 'Ready to test';
    
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    const diffSeconds = Math.floor((diffMs % (1000 * 60)) / 1000);

    if (diffMinutes > 0) return `${diffMinutes}m ${diffSeconds}s`;
    return `${diffSeconds}s`;
  };

  /**
   * Filter circuit breakers based on search and state
   */
  const filteredCircuitBreakers = React.useMemo(() => {
    if (!circuitBreakerData?.circuitBreakers) return [];

    return circuitBreakerData.circuitBreakers
      .filter(breaker => {
        const matchesSearch = breaker.name.toLowerCase().includes(searchTerm.toLowerCase());
        const matchesState = stateFilter === 'all' || breaker.state === stateFilter;
        return matchesSearch && matchesState;
      })
      .slice(0, maxItems);
  }, [circuitBreakerData?.circuitBreakers, searchTerm, stateFilter, maxItems]);

  // Handle loading state
  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>Circuit Breakers</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="flex items-center space-x-4 animate-pulse">
                <div className="h-10 w-10 bg-muted rounded-full"></div>
                <div className="space-y-2 flex-1">
                  <div className="h-4 bg-muted rounded w-1/3"></div>
                  <div className="h-3 bg-muted rounded w-1/2"></div>
                </div>
                <div className="h-6 w-16 bg-muted rounded"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  // Handle error state
  if (error || !circuitBreakerData) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>Circuit Breakers</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="text-center">
              <AlertTriangle className="mx-auto h-8 w-8 text-muted-foreground mb-2" />
              <p className="text-sm text-muted-foreground mb-3">
                Failed to load circuit breaker data
              </p>
              <Button variant="outline" size="sm" onClick={() => refetch()}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Retry
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <TooltipProvider>
      <Card className={className}>
        <CardHeader>
          <CardTitle>Circuit Breakers</CardTitle>
          
          {/* Filters */}
          <div className="flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
            <div className="flex gap-2">
              <Input
                placeholder="Search circuit breakers..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-64"
              />
              <Select value={stateFilter} onValueChange={(value) => setStateFilter(value as any)}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All States</SelectItem>
                  <SelectItem value="CLOSED">Healthy</SelectItem>
                  <SelectItem value="HALF_OPEN">Testing</SelectItem>
                  <SelectItem value="OPEN">Failed</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <Button variant="outline" size="sm" onClick={() => refetch()}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>
        </CardHeader>
        
        <CardContent>
          {filteredCircuitBreakers.length === 0 ? (
            <div className="text-center py-8">
              <Shield className="mx-auto h-8 w-8 text-muted-foreground mb-2" />
              <p className="text-sm text-muted-foreground">
                {searchTerm || stateFilter !== 'all' 
                  ? 'No circuit breakers match your filters'
                  : 'No circuit breakers found'
                }
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Failures</TableHead>
                    <TableHead>Last Failure</TableHead>
                    <TableHead>Next Attempt</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredCircuitBreakers.map((breaker) => {
                    const config = getStateConfig(breaker.state);
                    const Icon = config.icon;
                    const nextAttempt = formatNextAttempt(breaker.nextAttempt);
                    
                    return (
                      <TableRow key={breaker.name}>
                        <TableCell>
                          <div className="flex items-center gap-3">
                            <div className={cn('p-2 rounded-full', config.bgColor)}>
                              <Icon className={cn('h-4 w-4', config.color)} />
                            </div>
                            <div>
                              <p className="font-medium">{breaker.name}</p>
                              {breaker.timeout && (
                                <p className="text-xs text-muted-foreground">
                                  Timeout: {breaker.timeout}ms
                                </p>
                              )}
                            </div>
                          </div>
                        </TableCell>
                        
                        <TableCell>
                          <Badge variant={config.variant}>
                            {config.label}
                          </Badge>
                        </TableCell>
                        
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <span className={cn(
                              'font-medium',
                              breaker.failureCount > 0 ? 'text-red-600' : 'text-muted-foreground'
                            )}>
                              {breaker.failureCount}
                            </span>
                            {breaker.successCount !== undefined && (
                              <Tooltip>
                                <TooltipTrigger>
                                  <span className="text-xs text-green-600">
                                    +{breaker.successCount}
                                  </span>
                                </TooltipTrigger>
                                <TooltipContent>
                                  {breaker.successCount} successful requests
                                </TooltipContent>
                              </Tooltip>
                            )}
                          </div>
                        </TableCell>
                        
                        <TableCell>
                          <div className="flex items-center gap-1 text-sm text-muted-foreground">
                            <Clock className="h-3 w-3" />
                            {formatDuration(breaker.lastFailureTime)}
                          </div>
                        </TableCell>
                        
                        <TableCell>
                          {nextAttempt ? (
                            <div className="flex items-center gap-1 text-sm">
                              <Timer className="h-3 w-3 text-yellow-600" />
                              <span className="text-yellow-600">{nextAttempt}</span>
                            </div>
                          ) : (
                            <span className="text-sm text-muted-foreground">—</span>
                          )}
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </div>
          )}
          
          {circuitBreakerData.circuitBreakers.length > maxItems && (
            <div className="text-center pt-4 border-t">
              <p className="text-sm text-muted-foreground">
                Showing {Math.min(filteredCircuitBreakers.length, maxItems)} of {circuitBreakerData.circuitBreakers.length} circuit breakers
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </TooltipProvider>
  );
};

/**
 * Default export for the CircuitBreakerList component
 */
export default CircuitBreakerList;
