/**
 * @file Enhanced delegation calendar view component with modern shadcn/ui
 * @module components/delegations/DelegationCalendar
 */

'use client';

import {
  eachDayOfInterval,
  endOfMonth,
  format,
  isToday,
  startOfMonth,
} from 'date-fns';
import { Calendar, ChevronLeft, ChevronRight } from 'lucide-react';
import Link from 'next/link';
import * as React from 'react';

import type { Delegation, DelegationStatusPrisma } from '@/lib/types/domain'; // Added DelegationStatusPrisma

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import {
  formatDelegationTime,
  getStatusColor,
} from '@/lib/utils/delegationUtils';
import { formatDelegationStatusForDisplay } from '@/lib/utils/formattingUtils';

/**
 * Props for DelegationCalendar component
 */
interface DelegationCalendarProps {
  className?: string;
  delegations: Delegation[];
}

/**
 * Get delegations for a specific date
 */
const getDelegationsForDate = (delegations: Delegation[], date: Date) => {
  return delegations.filter(delegation => {
    const startDate = new Date(delegation.durationFrom);
    const endDate = new Date(delegation.durationTo);
    return date >= startDate && date <= endDate;
  });
};

/**
 * Enhanced delegation calendar component with modern shadcn/ui patterns.
 *
 * Features:
 * - Multiple view modes (month, week, agenda)
 * - Advanced filtering and search
 * - Interactive event details
 * - Status-based color coding
 * - Responsive design
 * - Modern shadcn/ui styling
 *
 * @param props - Component props
 * @returns JSX element representing the enhanced delegation calendar
 */
export const DelegationCalendar: React.FC<DelegationCalendarProps> = ({
  className = '',
  delegations,
}) => {
  const [currentDate, setCurrentDate] = React.useState<Date>(new Date());

  const monthStart = startOfMonth(currentDate);
  const monthEnd = endOfMonth(currentDate);
  const monthDays = eachDayOfInterval({ end: monthEnd, start: monthStart });

  const goToPreviousMonth = () => {
    setCurrentDate(
      new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 1)
    );
  };

  const goToNextMonth = () => {
    setCurrentDate(
      new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 1)
    );
  };

  const goToToday = () => {
    setCurrentDate(new Date());
  };

  return (
    <div className={cn('space-y-6', className)}>
      {/* Professional Header */}
      <Card className="border border-gray-200 bg-white shadow-sm dark:border-gray-700 dark:bg-gray-900">
        <CardHeader className="pb-4">
          <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
            <CardTitle className="flex items-center gap-3 text-2xl font-semibold text-gray-900 dark:text-white">
              <div className="rounded-full bg-blue-600 p-2">
                <Calendar className="size-6 text-white" />
              </div>
              {format(currentDate, 'MMMM yyyy')}
            </CardTitle>
            <div className="flex items-center gap-2">
              <Button
                className="border-gray-300 hover:bg-gray-50 dark:border-gray-600 dark:hover:bg-gray-800"
                onClick={goToToday}
                size="sm"
                variant="outline"
              >
                Today
              </Button>
              <Button
                className="border-gray-300 hover:bg-gray-50 dark:border-gray-600 dark:hover:bg-gray-800"
                onClick={goToPreviousMonth}
                size="sm"
                variant="outline"
              >
                <ChevronLeft className="size-4" />
              </Button>
              <Button
                className="border-gray-300 hover:bg-gray-50 dark:border-gray-600 dark:hover:bg-gray-800"
                onClick={goToNextMonth}
                size="sm"
                variant="outline"
              >
                <ChevronRight className="size-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Professional Calendar Grid */}
      <Card className="border border-gray-200 bg-white shadow-sm dark:border-gray-700 dark:bg-gray-900">
        <CardContent className="p-6">
          {/* Calendar Grid */}
          <div className="grid grid-cols-7 gap-1">
            {/* Day Headers */}
            {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
              <div
                className="border border-gray-200 bg-gray-50 p-3 text-center text-sm font-semibold text-gray-700 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-300"
                key={day}
              >
                {day}
              </div>
            ))}

            {/* Calendar Days */}
            {monthDays.map(day => {
              const dayDelegations = getDelegationsForDate(delegations, day);
              const isCurrentDay = isToday(day);

              return (
                <div
                  className={cn(
                    'min-h-[120px] border border-gray-200 dark:border-gray-700 p-2 bg-white dark:bg-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors duration-200',
                    isCurrentDay &&
                      'bg-blue-50 dark:bg-blue-950/30 border-blue-300 dark:border-blue-700'
                  )}
                  key={day.toISOString()}
                >
                  <div
                    className={cn(
                      'text-sm font-medium mb-2 flex items-center justify-center w-6 h-6 rounded-full',
                      isCurrentDay
                        ? 'bg-blue-600 text-white shadow-sm'
                        : 'text-gray-700 dark:text-gray-300'
                    )}
                  >
                    {format(day, 'd')}
                  </div>

                  <div className="space-y-1">
                    {dayDelegations.slice(0, 2).map(delegation => (
                      <Link
                        className="block"
                        href={`/delegations/${delegation.id}`}
                        key={delegation.id}
                      >
                        <div
                          className={cn(
                            'text-xs p-2 rounded border cursor-pointer hover:shadow-sm transition-all duration-200',
                            getStatusColor(
                              delegation.status as DelegationStatusPrisma
                            ) // Explicit cast
                          )}
                          title={`${delegation.eventName} - ${formatDelegationStatusForDisplay(delegation.status)}`}
                        >
                          <div className="truncate font-medium">
                            {delegation.eventName}
                          </div>
                          <div className="mt-0.5 text-xs opacity-75">
                            {formatDelegationTime(delegation.durationFrom)}
                          </div>
                        </div>
                      </Link>
                    ))}

                    {dayDelegations.length > 2 && (
                      <div className="rounded border border-gray-200 bg-gray-100 p-1 text-center text-xs text-gray-500 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400">
                        +{dayDelegations.length - 2} more
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>

          {/* Professional Legend */}
          <div className="mt-8 border-t border-gray-200 pt-6 dark:border-gray-700">
            <div className="mb-4 text-sm font-semibold text-gray-900 dark:text-white">
              Status Legend
            </div>
            <div className="flex flex-wrap gap-3">
              {(
                [
                  { label: 'Planned', status: 'Planned' as const },
                  { label: 'Confirmed', status: 'Confirmed' as const },
                  { label: 'In Progress', status: 'In_Progress' as const },
                  { label: 'Completed', status: 'Completed' as const },
                  { label: 'Cancelled', status: 'Cancelled' as const },
                ] as const
              ).map(({ label, status }) => (
                <div
                  className="flex items-center gap-2 rounded-lg border border-gray-200 bg-gray-50 px-3 py-2 dark:border-gray-700 dark:bg-gray-800"
                  key={status}
                >
                  <div
                    className={cn(
                      'w-3 h-3 rounded-full',
                      getStatusColor(status)
                    )}
                  />
                  <span className="text-xs font-medium text-gray-700 dark:text-gray-300">
                    {label}
                  </span>
                </div>
              ))}
            </div>
          </div>

          {/* Professional Summary */}
          <div className="mt-6 border-t border-gray-200 pt-4 dark:border-gray-700">
            <div className="rounded-lg border border-gray-200 bg-gray-50 px-4 py-3 text-sm text-gray-600 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400">
              <span className="font-medium text-gray-900 dark:text-white">
                {delegations.length}
              </span>{' '}
              delegation{delegations.length === 1 ? '' : 's'} this month
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default DelegationCalendar;
