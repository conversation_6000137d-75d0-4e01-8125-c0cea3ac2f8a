// frontend/src/components/features/reporting/charts/LocationDistributionChart.tsx

'use client';

import React, { useMemo, useState } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell,
  Legend,
} from 'recharts';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { LocationMetrics } from '../data/types/reporting';

/**
 * Props for LocationDistributionChart component
 */
interface LocationDistributionChartProps {
  data: LocationMetrics[];
  loading?: boolean;
  error?: string | undefined; // FIXED: Allow undefined for strict mode
  height?: number;
  showLegend?: boolean;
  interactive?: boolean;
  className?: string;
}

/**
 * Custom tooltip for location chart
 */
const LocationTooltip = ({ active, payload }: any) => {
  if (active && payload && payload.length) {
    const data = payload[0].payload;

    return (
      <div className="bg-white p-4 border border-gray-200 rounded-lg shadow-lg min-w-[200px]">
        <p className="font-semibold text-gray-900 mb-2">{data.location}</p>
        <div className="space-y-1">
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-600">Delegations:</span>
            <Badge variant="secondary">{data.delegationCount}</Badge>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-600">Completion Rate:</span>
            <Badge
              variant={
                data.completionRate >= 80
                  ? 'default'
                  : data.completionRate >= 60
                    ? 'secondary'
                    : 'destructive'
              }
            >
              {data.completionRate.toFixed(1)}%
            </Badge>
          </div>
        </div>
      </div>
    );
  }
  return null;
};

/**
 * Loading skeleton for location chart
 */
const LocationLoadingSkeleton = ({ height }: { height: number }) => (
  <div className="animate-pulse">
    <div
      className="flex items-end justify-between mb-4"
      style={{ height: height - 50 }}
    >
      {[1, 2, 3, 4, 5].map(i => (
        <div
          key={i}
          className="bg-gray-200 rounded-t"
          style={{
            width: '15%',
            height: `${Math.random() * 80 + 20}%`,
          }}
        />
      ))}
    </div>
    <div className="flex justify-center gap-4">
      {[1, 2, 3].map(i => (
        <div key={i} className="flex items-center gap-2">
          <div className="w-3 h-3 bg-gray-200 rounded-full"></div>
          <div className="w-20 h-4 bg-gray-200 rounded"></div>
        </div>
      ))}
    </div>
  </div>
);

/**
 * Error display for location chart
 */
const LocationErrorDisplay = ({ error }: { error: string }) => (
  <div className="flex flex-col items-center justify-center h-64 text-gray-500">
    <div className="text-4xl mb-2">🗺️</div>
    <p className="text-sm">Failed to load location data</p>
    <p className="text-xs text-gray-400 mt-1">{error}</p>
  </div>
);

/**
 * LocationDistributionChart Component
 *
 * Displays delegation distribution across different locations with completion rates.
 * Supports both bar chart and pie chart views with interactive features.
 *
 * Features:
 * - Bar and pie chart views
 * - Completion rate visualization
 * - Interactive tooltips
 * - Responsive design
 * - Loading and error states
 * - Color coding based on performance
 *
 * @param props - Component props
 * @returns JSX element
 */
export const LocationDistributionChart: React.FC<
  LocationDistributionChartProps
> = ({
  data,
  loading = false,
  error,
  height = 400,
  showLegend = true,
  interactive = true,
  className = '',
}) => {
  const [selectedLocation, setSelectedLocation] = useState<string | null>(null);

  // Process data with color coding based on completion rate
  const processedData = useMemo(() => {
    return data.map(item => {
      // Color coding based on completion rate
      let color = '#ef4444'; // Red for low completion
      if (item.completionRate && item.completionRate >= 80) {
        color = '#10b981'; // Green for high completion
      } else if (item.completionRate && item.completionRate >= 60) {
        color = '#f59e0b'; // Yellow for medium completion
      }

      return {
        ...item,
        color,
        // Truncate long location names for display
        displayName:
          item.location.length > 15
            ? `${item.location.substring(0, 15)}...`
            : item.location,
      };
    });
  }, [data]);

  // Sort data by delegation count for better visualization
  const sortedData = useMemo(() => {
    return [...processedData].sort(
      (a, b) => b.delegationCount - a.delegationCount
    );
  }, [processedData]);

  // Handle bar click for interactivity
  const handleBarClick = (data: any) => {
    if (interactive) {
      setSelectedLocation(
        selectedLocation === data.location ? null : data.location
      );
    }
  };

  // Custom legend for pie chart
  const CustomPieLegend = ({ payload }: any) => (
    <div className="grid grid-cols-2 gap-2 mt-4 max-h-32 overflow-y-auto">
      {payload?.map((entry: any, index: number) => (
        <div
          key={`legend-${index}`}
          className="flex items-center gap-2 text-xs cursor-pointer hover:opacity-80"
          onClick={() => handleBarClick(entry.payload)}
        >
          <div
            className="w-3 h-3 rounded-full flex-shrink-0"
            style={{ backgroundColor: entry.color }}
          />
          <span className="truncate" title={entry.payload.location}>
            {entry.payload.displayName}
          </span>
          <Badge variant="outline" className="ml-auto text-xs">
            {entry.payload.delegationCount}
          </Badge>
        </div>
      ))}
    </div>
  );

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>Location Distribution</CardTitle>
          <CardDescription>Loading location data...</CardDescription>
        </CardHeader>
        <CardContent>
          <LocationLoadingSkeleton height={height} />
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>Location Distribution</CardTitle>
          <CardDescription>Location data unavailable</CardDescription>
        </CardHeader>
        <CardContent>
          <LocationErrorDisplay error={error} />
        </CardContent>
      </Card>
    );
  }

  if (!data || data.length === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>Location Distribution</CardTitle>
          <CardDescription>No location data available</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center justify-center h-64 text-gray-500">
            <div className="text-4xl mb-2">🗺️</div>
            <p className="text-sm">No location data to display</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>Location Distribution</CardTitle>
        <CardDescription>
          Delegation distribution and completion rates by location
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="bar" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="bar">Bar Chart</TabsTrigger>
            <TabsTrigger value="pie">Pie Chart</TabsTrigger>
          </TabsList>

          <TabsContent value="bar" className="mt-4">
            <ResponsiveContainer width="100%" height={height}>
              <BarChart
                data={sortedData}
                margin={{ top: 20, right: 30, left: 20, bottom: 60 }}
              >
                <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                <XAxis
                  dataKey="displayName"
                  angle={-45}
                  textAnchor="end"
                  height={80}
                  fontSize={11}
                  interval={0}
                />
                <YAxis fontSize={12} tickLine={false} axisLine={false} />
                {interactive && <Tooltip content={<LocationTooltip />} />}
                <Bar
                  dataKey="delegationCount"
                  fill="#8884d8"
                  onClick={handleBarClick}
                  cursor={interactive ? 'pointer' : 'default'}
                  animationDuration={800}
                >
                  {sortedData.map((entry, index) => (
                    <Cell
                      key={`cell-${index}`}
                      fill={
                        selectedLocation === entry.location
                          ? '#3b82f6'
                          : entry.color
                      }
                    />
                  ))}
                </Bar>
              </BarChart>
            </ResponsiveContainer>

            {/* Performance Legend */}
            <div className="flex justify-center gap-6 mt-4 text-sm">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <span>High Performance (≥80%)</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                <span>Medium Performance (60-79%)</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                <span>Low Performance (&lt;60%)</span>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="pie" className="mt-4">
            <ResponsiveContainer width="100%" height={height}>
              <PieChart>
                <Pie
                  data={processedData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ delegationCount, percent }) =>
                    `${delegationCount} (${(percent * 100).toFixed(1)}%)`
                  }
                  outerRadius={120}
                  fill="#8884d8"
                  dataKey="delegationCount"
                  animationDuration={800}
                >
                  {processedData.map((entry, index) => (
                    <Cell
                      key={`pie-cell-${index}`}
                      fill={entry.color}
                      onClick={() => handleBarClick(entry)}
                      style={{ cursor: interactive ? 'pointer' : 'default' }}
                    />
                  ))}
                </Pie>
                {interactive && <Tooltip content={<LocationTooltip />} />}
                {showLegend && <Legend content={<CustomPieLegend />} />}
              </PieChart>
            </ResponsiveContainer>
          </TabsContent>
        </Tabs>

        {selectedLocation && (
          <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
            <p className="text-sm text-blue-800">
              <strong>Selected:</strong> {selectedLocation}
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
