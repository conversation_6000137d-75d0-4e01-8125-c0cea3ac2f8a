/**
 * @file TanStack Query hooks for Reliability-related data.
 * These hooks manage fetching, caching, and mutating reliability monitoring data,
 * integrating with the ReliabilityApiService for system health, circuit breakers,
 * metrics, and alert management.
 * @module stores/queries/useReliability
 */

import type {
  UseQueryOptions,
  UseMutationOptions,
} from '@tanstack/react-query';

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

import type { ApiError } from '@/lib/types/api';
import type {
  Alert,
  AlertHistory,
  AlertStatistics,
  CircuitBreakerStatus,
  DeduplicationMetrics,
  DependencyHealth,
  DetailedHealthCheck,
  HealthCheck,
  SystemMetrics,
  TestAlertsResult,
} from '@/lib/types/domain';

import { useNotifications } from '@/hooks/ui/useNotifications';
import { useReliabilityQuery } from '@/hooks/api/useSmartQuery';
import { reliabilityApiService } from '@/lib/api/services/apiServiceFactory';

/**
 * Centralized query keys for reliability data to ensure consistency.
 * Organized hierarchically for efficient cache invalidation.
 */
export const reliabilityQueryKeys = {
  all: ['reliability'] as const,
  health: () => [...reliabilityQueryKeys.all, 'health'] as const,
  healthDetailed: () => [...reliabilityQueryKeys.health(), 'detailed'] as const,
  healthDependencies: () =>
    [...reliabilityQueryKeys.health(), 'dependencies'] as const,
  healthTrends: () => [...reliabilityQueryKeys.health(), 'trends'] as const,
  circuitBreakers: () =>
    [...reliabilityQueryKeys.all, 'circuit-breakers'] as const,
  circuitBreakerHistory: () =>
    [...reliabilityQueryKeys.circuitBreakers(), 'history'] as const,
  httpRequestMetrics: () =>
    [...reliabilityQueryKeys.metrics(), 'http-requests'] as const,
  metrics: () => [...reliabilityQueryKeys.all, 'metrics'] as const,
  deduplication: () =>
    [...reliabilityQueryKeys.metrics(), 'deduplication'] as const,
  alerts: () => [...reliabilityQueryKeys.all, 'alerts'] as const,
  alertsHistory: (page: number, limit: number) =>
    [...reliabilityQueryKeys.alerts(), 'history', page, limit] as const,
  alertsStatistics: () =>
    [...reliabilityQueryKeys.alerts(), 'statistics'] as const,
};

// =============================================================================
// QUERY HOOKS - Health Monitoring
// =============================================================================

/**
 * Hook for basic system health monitoring.
 * Uses smart WebSocket integration with fallback polling.
 * @param options - Optional React Query options
 * @returns Query result containing basic HealthCheck data with WebSocket state
 */
export const useSystemHealth = (
  options?: Omit<UseQueryOptions<HealthCheck, ApiError>, 'queryFn' | 'queryKey'>
) => {
  return useReliabilityQuery(
    [...reliabilityQueryKeys.health()],
    () => reliabilityApiService.getSystemHealth(),
    'health',
    options
  );
};

/**
 * Hook for detailed system health with component-level information.
 * Less frequent updates due to larger data volume.
 * @param options - Optional React Query options
 * @returns Query result containing DetailedHealthCheck data
 */
export const useDetailedSystemHealth = (
  options?: Omit<
    UseQueryOptions<DetailedHealthCheck, ApiError>,
    'queryFn' | 'queryKey'
  >
) => {
  return useReliabilityQuery(
    [...reliabilityQueryKeys.healthDetailed()],
    () => reliabilityApiService.getDetailedHealth(),
    'health', // WebSocket event key for detailed health
    options
  );
};

/**
 * Hook for external dependency health monitoring.
 * @param options - Optional React Query options
 * @returns Query result containing DependencyHealth data
 */
export const useDependencyHealth = (
  options?: Omit<
    UseQueryOptions<DependencyHealth, ApiError>,
    'queryFn' | 'queryKey'
  >
) => {
  return useReliabilityQuery(
    [...reliabilityQueryKeys.healthDependencies()],
    () => reliabilityApiService.getDependencyHealth(),
    'health', // WebSocket event key for dependency health
    options
  );
};

// =============================================================================
// QUERY HOOKS - Circuit Breakers & Metrics
// =============================================================================

/**
 * Hook for circuit breaker status monitoring.
 * Provides real-time circuit breaker state for system resilience monitoring.
 * @param options - Optional React Query options
 * @returns Query result containing CircuitBreakerStatus data
 */
export const useCircuitBreakerStatus = (
  options?: Omit<
    UseQueryOptions<CircuitBreakerStatus, ApiError>,
    'queryFn' | 'queryKey'
  >
) => {
  return useReliabilityQuery(
    [...reliabilityQueryKeys.circuitBreakers()],
    () => reliabilityApiService.getCircuitBreakerStatus(),
    'circuit-breakers', // WebSocket event key for circuit breaker status
    options
  );
};

/**
 * Hook for system performance metrics.
 * Includes CPU, memory, HTTP request metrics, and deduplication data.
 * @param options - Optional React Query options
 * @returns Query result containing SystemMetrics data
 */
export const usePerformanceMetrics = (
  options?: Omit<
    UseQueryOptions<SystemMetrics, ApiError>,
    'queryFn' | 'queryKey'
  >
) => {
  return useReliabilityQuery(
    [...reliabilityQueryKeys.metrics()],
    () => reliabilityApiService.getMetrics(),
    'metrics', // WebSocket event key for performance metrics
    options
  );
};

/**
 * Hook for health trend data.
 * Provides time-series health data for trend analysis and visualization.
 * @param timeframe - Time range for trend data ('1h', '6h', '24h', '7d')
 * @param options - Optional React Query options
 * @returns Query result containing health trend data
 */
export const useHealthTrends = (
  timeframe: '1h' | '6h' | '24h' | '7d' = '24h',
  options?: Omit<UseQueryOptions<any, ApiError>, 'queryFn' | 'queryKey'>
) => {
  return useReliabilityQuery(
    [...reliabilityQueryKeys.healthTrends(), timeframe],
    () => reliabilityApiService.getHealthTrends(timeframe),
    'health', // WebSocket event key for health trends
    {
      ...options,
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes
    }
  );
};

/**
 * Hook for circuit breaker history data.
 * Provides historical circuit breaker state changes and metrics.
 * @param timeframe - Time range for history data ('1h', '6h', '24h', '7d')
 * @param breakerName - Optional specific circuit breaker name to filter by
 * @param options - Optional React Query options
 * @returns Query result containing circuit breaker history data
 */
export const useCircuitBreakerHistory = (
  timeframe: '1h' | '6h' | '24h' | '7d' = '24h',
  breakerName?: string,
  options?: Omit<UseQueryOptions<any, ApiError>, 'queryFn' | 'queryKey'>
) => {
  return useReliabilityQuery(
    [
      ...reliabilityQueryKeys.circuitBreakerHistory(),
      timeframe,
      breakerName || 'all',
    ],
    () =>
      reliabilityApiService.getCircuitBreakerHistory(timeframe, breakerName),
    'circuit-breakers', // WebSocket event key for circuit breaker history
    {
      ...options,
      staleTime: 2 * 60 * 1000, // 2 minutes
      gcTime: 5 * 60 * 1000, // 5 minutes
    }
  );
};

/**
 * Hook for HTTP request metrics data.
 * Provides HTTP request performance metrics for monitoring and analysis.
 * @param options - Optional React Query options
 * @returns Query result containing HTTP request metrics data
 */
export const useHttpRequestMetrics = (
  options?: Omit<UseQueryOptions<any, ApiError>, 'queryFn' | 'queryKey'>
) => {
  return useReliabilityQuery(
    [...reliabilityQueryKeys.httpRequestMetrics()],
    () => reliabilityApiService.getHttpRequestMetrics(),
    'metrics', // WebSocket event key for HTTP request metrics
    {
      ...options,
      staleTime: 1 * 60 * 1000, // 1 minute
      gcTime: 3 * 60 * 1000, // 3 minutes
    }
  );
};

/**
 * Hook for request deduplication metrics.
 * Monitors cache performance and request optimization.
 * @param options - Optional React Query options
 * @returns Query result containing DeduplicationMetrics data
 */
export const useDeduplicationMetrics = (
  options?: Omit<
    UseQueryOptions<DeduplicationMetrics, ApiError>,
    'queryFn' | 'queryKey'
  >
) => {
  return useReliabilityQuery(
    [...reliabilityQueryKeys.deduplication()], // Spread for mutable array
    () => reliabilityApiService.getDeduplicationMetrics(),
    'metrics', // WebSocket event key for metrics (covers deduplication)
    options
  );
};

// =============================================================================
// QUERY HOOKS - Alert Management
// =============================================================================

/**
 * Hook for active alerts monitoring.
 * Provides real-time alert updates with frequent polling for critical alerts.
 * @param options - Optional React Query options
 * @returns Query result containing array of active Alert data
 */
export const useAlerts = (
  options?: Omit<UseQueryOptions<Alert[], ApiError>, 'queryFn' | 'queryKey'>
) => {
  return useReliabilityQuery(
    [...reliabilityQueryKeys.alerts()], // Spread for mutable array
    () => reliabilityApiService.getActiveAlerts(),
    'alerts', // WebSocket event key for alerts
    options
  );
};

/**
 * Hook for paginated alert history.
 * Supports pagination with keepPreviousData for smooth UX.
 * @param page - Page number for pagination (default: 1)
 * @param limit - Number of alerts per page (default: 50)
 * @param options - Optional React Query options
 * @returns Query result containing AlertHistory with pagination
 */
export const useAlertHistory = (
  page: number = 1,
  limit: number = 50,
  options?: Omit<
    UseQueryOptions<AlertHistory, ApiError>,
    'queryFn' | 'queryKey'
  >
) => {
  return useReliabilityQuery<AlertHistory, ApiError>(
    [...reliabilityQueryKeys.alertsHistory(page, limit)],
    () => reliabilityApiService.getAlertHistory(page, limit),
    'alerts', // WebSocket event key for alerts (covers history)
    {
      staleTime: 2 * 60 * 1000, // 2 minutes - history is less time-sensitive
      placeholderData: previousData => previousData, // Smooth pagination experience
      ...options,
    }
  );
};

/**
 * Hook for alert statistics and analytics.
 * Less frequent updates as statistics change slowly.
 * @param options - Optional React Query options
 * @returns Query result containing AlertStatistics data
 */
export const useAlertStatistics = (
  options?: Omit<
    UseQueryOptions<AlertStatistics, ApiError>,
    'queryFn' | 'queryKey'
  >
) => {
  return useReliabilityQuery(
    [...reliabilityQueryKeys.alertsStatistics()], // Spread for mutable array
    () => reliabilityApiService.getAlertStatistics(),
    'alerts', // WebSocket event key for alerts (covers statistics)
    options
  );
};

// =============================================================================
// CONVENIENCE HOOKS
// =============================================================================

/**
 * Hook for comprehensive reliability dashboard data.
 * Combines multiple queries for dashboard overview with parallel fetching.
 * @param options - Optional React Query options
 * @returns Query result containing complete dashboard data
 */
export const useReliabilityDashboard = (
  options?: Omit<
    UseQueryOptions<
      {
        systemHealth: HealthCheck;
        detailedHealth: DetailedHealthCheck;
        circuitBreakers: CircuitBreakerStatus;
        metrics: SystemMetrics;
        activeAlerts: Alert[];
        alertStatistics: AlertStatistics;
      },
      ApiError
    >,
    'queryFn' | 'queryKey'
  >
) => {
  return useReliabilityQuery(
    [...reliabilityQueryKeys.all, 'dashboard'],
    () => reliabilityApiService.getReliabilityDashboardData(),
    'metrics', // Using 'metrics' as the event key for dashboard updates
    {
      staleTime: 30 * 1000, // 30 seconds
      ...options,
    }
  );
};

/**
 * Hook for checking if system is healthy.
 * Simple boolean check for system status indicators.
 * @param options - Optional React Query options
 * @returns Query result containing boolean health status
 */
export const useSystemHealthStatus = (
  options?: Omit<UseQueryOptions<boolean, ApiError>, 'queryFn' | 'queryKey'>
) => {
  return useReliabilityQuery<boolean, ApiError>(
    [...reliabilityQueryKeys.health(), 'status'],
    () => reliabilityApiService.isSystemHealthy(),
    'health', // WebSocket event key for health status
    {
      staleTime: 15 * 1000, // 15 seconds
      ...options,
    }
  );
};

/**
 * Hook for critical alert count.
 * Quick access to critical alert numbers for badges/indicators.
 * @param options - Optional React Query options
 * @returns Query result containing number of critical alerts
 */
export const useCriticalAlertCount = (
  options?: Omit<UseQueryOptions<number, ApiError>, 'queryFn' | 'queryKey'>
) => {
  return useReliabilityQuery<number, ApiError>(
    [...reliabilityQueryKeys.alerts(), 'critical-count'],
    () => reliabilityApiService.getCriticalAlertCount(),
    'alerts', // WebSocket event key for critical alerts
    {
      staleTime: 10 * 1000, // 10 seconds
      ...options,
    }
  );
};

// =============================================================================
// MUTATION HOOKS - Alert Management Actions
// =============================================================================

/**
 * Hook for resolving alerts.
 * Includes optimistic updates for immediate UI feedback and proper error handling.
 * @param options - Optional React Query mutation options
 * @returns Mutation result for resolving an alert
 */
export const useResolveAlert = (
  options?: Omit<
    UseMutationOptions<
      Alert,
      ApiError,
      { alertId: string; reason?: string; resolvedBy?: string },
      { previousAlerts: Alert[] | undefined }
    >,
    'mutationFn'
  >
) => {
  const queryClient = useQueryClient();
  const { showError, showSuccess } = useNotifications();

  return useMutation<
    Alert,
    ApiError,
    { alertId: string; reason?: string; resolvedBy?: string },
    { previousAlerts: Alert[] | undefined }
  >({
    mutationFn: async ({ alertId, reason, resolvedBy }) => {
      return reliabilityApiService.resolveAlert(alertId, reason, resolvedBy);
    },
    onMutate: async ({ alertId }) => {
      // Cancel outgoing queries to prevent optimistic update conflicts
      await queryClient.cancelQueries({
        queryKey: reliabilityQueryKeys.alerts(),
      });

      // Snapshot previous alerts for rollback
      const previousAlerts = queryClient.getQueryData<Alert[]>(
        reliabilityQueryKeys.alerts()
      );

      // Optimistically update alert status
      queryClient.setQueryData<Alert[]>(
        reliabilityQueryKeys.alerts(),
        (old = []) =>
          old.map(alert =>
            alert.id === alertId
              ? {
                  ...alert,
                  status: 'resolved' as const,
                  resolvedAt: new Date().toISOString(),
                }
              : alert
          )
      );

      return { previousAlerts };
    },
    onError: (error, variables, context) => {
      // Rollback optimistic update
      if (context?.previousAlerts) {
        queryClient.setQueryData(
          reliabilityQueryKeys.alerts(),
          context.previousAlerts
        );
      }
      showError(
        `Failed to resolve alert: ${error.message || 'Unknown error occurred'}`
      );
    },
    onSuccess: data => {
      showSuccess(`Alert "${data.type}" has been resolved successfully!`);
    },
    onSettled: () => {
      // Invalidate and refetch related queries
      queryClient.invalidateQueries({
        queryKey: reliabilityQueryKeys.alerts(),
      });
      queryClient.invalidateQueries({
        queryKey: reliabilityQueryKeys.alertsStatistics(),
      });
    },
    ...options,
  });
};

/**
 * Hook for acknowledging alerts.
 * Includes optimistic updates and proper error handling.
 * @param options - Optional React Query mutation options
 * @returns Mutation result for acknowledging an alert
 */
export const useAcknowledgeAlert = (
  options?: Omit<
    UseMutationOptions<
      Alert,
      ApiError,
      { alertId: string; note?: string; acknowledgedBy?: string },
      { previousAlerts: Alert[] | undefined }
    >,
    'mutationFn'
  >
) => {
  const queryClient = useQueryClient();
  const { showError, showSuccess } = useNotifications();

  return useMutation<
    Alert,
    ApiError,
    { alertId: string; note?: string; acknowledgedBy?: string },
    { previousAlerts: Alert[] | undefined }
  >({
    mutationFn: async ({ alertId, note, acknowledgedBy }) => {
      return reliabilityApiService.acknowledgeAlert(
        alertId,
        note,
        acknowledgedBy
      );
    },
    onMutate: async ({ alertId, acknowledgedBy }) => {
      // Cancel outgoing queries
      await queryClient.cancelQueries({
        queryKey: reliabilityQueryKeys.alerts(),
      });

      // Snapshot previous alerts
      const previousAlerts = queryClient.getQueryData<Alert[]>(
        reliabilityQueryKeys.alerts()
      );

      // Optimistically update alert status
      queryClient.setQueryData<Alert[]>(
        reliabilityQueryKeys.alerts(),
        (old = []) =>
          old.map(alert =>
            alert.id === alertId
              ? {
                  ...alert,
                  status: 'acknowledged' as const,
                  acknowledgedAt: new Date().toISOString(),
                  acknowledgedBy: acknowledgedBy || 'Unknown',
                }
              : alert
          )
      );

      return { previousAlerts };
    },
    onError: (error, variables, context) => {
      // Rollback optimistic update
      if (context?.previousAlerts) {
        queryClient.setQueryData(
          reliabilityQueryKeys.alerts(),
          context.previousAlerts
        );
      }
      showError(
        `Failed to acknowledge alert: ${error.message || 'Unknown error occurred'}`
      );
    },
    onSuccess: data => {
      showSuccess(`Alert "${data.type}" has been acknowledged successfully!`);
    },
    onSettled: () => {
      // Invalidate and refetch related queries
      queryClient.invalidateQueries({
        queryKey: reliabilityQueryKeys.alerts(),
      });
      queryClient.invalidateQueries({
        queryKey: reliabilityQueryKeys.alertsStatistics(),
      });
    },
    ...options,
  });
};

/**
 * Hook for testing the alert system.
 * Admin-only functionality for verifying alert system operation.
 * @param options - Optional React Query mutation options
 * @returns Mutation result for testing alerts
 */
export const useTestAlerts = (
  options?: Omit<
    UseMutationOptions<TestAlertsResult, ApiError, void>,
    'mutationFn'
  >
) => {
  const queryClient = useQueryClient();
  const { showError, showSuccess, showInfo } = useNotifications();

  return useMutation<TestAlertsResult, ApiError, void>({
    mutationFn: async () => {
      return reliabilityApiService.testAlerts();
    },
    onMutate: () => {
      showInfo('Testing alert system...');
    },
    onError: error => {
      showError(
        `Failed to test alert system: ${error.message || 'Unknown error occurred'}`
      );
    },
    onSuccess: data => {
      if (data.success) {
        showSuccess(
          `Alert system test successful! ${data.testAlertId ? `Test alert ID: ${data.testAlertId}` : ''}`
        );
      } else {
        showError(`Alert system test failed: ${data.message}`);
      }
    },
    onSettled: () => {
      // Refresh alerts to show any test alerts that were created
      queryClient.invalidateQueries({
        queryKey: reliabilityQueryKeys.alerts(),
      });
    },
    ...options,
  });
};
