import type { CreateVehicleInput, UpdateVehicleInput, Vehicle } from '../types/vehicle.ts';

import { PrismaClient } from '../generated/prisma/index.js';

const prisma: PrismaClient = new PrismaClient();

export async function createVehicle(data: CreateVehicleInput): Promise<Vehicle> {
  const vehicle = await prisma.vehicle.create({
    data,
  });
  return vehicle;
}

export async function deleteVehicle(id: number): Promise<void> {
  await prisma.vehicle.delete({
    where: { id },
  });
}

export async function getAllVehicles(): Promise<{ data: Vehicle[]; pagination: any }> {
  const vehicles = await prisma.vehicle.findMany();
  return {
    data: vehicles,
    pagination: {
      page: 1,
      pageSize: vehicles.length,
      total: vehicles.length,
      totalPages: 1,
    },
  };
}

export async function getVehicleById(id: number): Promise<null | Vehicle> {
  const vehicle = await prisma.vehicle.findUnique({
    where: { id },
  });
  return vehicle;
}

export async function updateVehicle(id: number, data: UpdateVehicleInput): Promise<Vehicle> {
  const vehicle = await prisma.vehicle.update({
    data,
    where: { id },
  });
  return vehicle;
}
