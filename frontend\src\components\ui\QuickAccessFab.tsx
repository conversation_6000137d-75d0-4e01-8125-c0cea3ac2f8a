'use client';

import React, { useState } from 'react';
import <PERSON> from 'next/link';
import { usePathname } from 'next/navigation';
import {
  BarChart3,
  TrendingUp,
  FileText,
  Download,
  Filter,
  X,
  ChevronUp,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

interface QuickAccessFabProps {
  className?: string;
}

/**
 * Floating Action Button for Quick Access to Reporting Dashboard
 *
 * Provides quick access to different sections of the reporting dashboard
 * from anywhere in the application.
 */
export const QuickAccessFab: React.FC<QuickAccessFabProps> = ({
  className = '',
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const pathname = usePathname();

  // Don't show on reports pages to avoid redundancy
  if (pathname?.startsWith('/reports')) {
    return null;
  }

  const quickAccessItems = [
    {
      href: '/reports',
      icon: BarChart3,
      label: 'Dashboard',
      description: 'Main Analytics',
      color: 'bg-blue-600 hover:bg-blue-700',
    },
    {
      href: '/reports/analytics',
      icon: TrendingUp,
      label: 'Analytics',
      description: 'Advanced Insights',
      color: 'bg-purple-600 hover:bg-purple-700',
    },
    {
      href: '/reports/data',
      icon: FileText,
      label: 'Data Tables',
      description: 'Raw Data View',
      color: 'bg-green-600 hover:bg-green-700',
    },
  ];

  const toggleExpanded = () => setIsExpanded(!isExpanded);

  return (
    <div className={cn('fixed bottom-6 right-6 z-50', className)}>
      {/* Expanded Menu Items */}
      {isExpanded && (
        <div className="mb-4 space-y-3">
          {quickAccessItems.map((item, index) => (
            <div
              key={item.href}
              className="flex items-center justify-end space-x-3 animate-in slide-in-from-bottom-2 duration-200"
              style={{ animationDelay: `${index * 50}ms` }}
            >
              {/* Label */}
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg border px-3 py-2 text-sm">
                <div className="font-medium text-gray-900 dark:text-gray-100">
                  {item.label}
                </div>
                <div className="text-xs text-gray-500 dark:text-gray-400">
                  {item.description}
                </div>
              </div>

              {/* Action Button */}
              <Button
                asChild
                size="lg"
                className={cn(
                  'h-12 w-12 rounded-full shadow-lg border-0 text-white',
                  item.color
                )}
              >
                <Link href={item.href}>
                  <item.icon className="h-5 w-5" />
                  <span className="sr-only">{item.label}</span>
                </Link>
              </Button>
            </div>
          ))}
        </div>
      )}

      {/* Main FAB Button */}
      <Button
        onClick={toggleExpanded}
        size="lg"
        className={cn(
          'h-14 w-14 rounded-full shadow-lg transition-all duration-200',
          isExpanded
            ? 'bg-red-600 hover:bg-red-700 rotate-45'
            : 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700'
        )}
      >
        {isExpanded ? (
          <X className="h-6 w-6 text-white" />
        ) : (
          <div className="flex flex-col items-center">
            <BarChart3 className="h-5 w-5 text-white" />
            <ChevronUp className="h-3 w-3 text-white -mt-1" />
          </div>
        )}
        <span className="sr-only">
          {isExpanded
            ? 'Close quick access menu'
            : 'Open reporting quick access'}
        </span>
      </Button>

      {/* New Feature Badge */}
      {!isExpanded && (
        <Badge
          variant="destructive"
          className="absolute -top-2 -right-2 text-xs px-1.5 py-0.5 animate-pulse"
        >
          NEW
        </Badge>
      )}
    </div>
  );
};

export default QuickAccessFab;
