/**
 * @file Hook for monitoring token refresh status and providing user feedback
 * @module hooks/useTokenRefreshStatus
 */

import { useEffect, useState } from 'react';
import { getTokenRefreshService } from '../../../services/TokenRefreshService';

/**
 * Token refresh status types
 */
export type TokenRefreshStatus =
  | 'idle'
  | 'refreshing'
  | 'success'
  | 'failed'
  | 'scheduled';

/**
 * Token refresh status information
 */
export interface TokenRefreshStatusInfo {
  status: TokenRefreshStatus;
  lastRefresh: Date | null;
  nextRefresh: Date | null;
  error: string | null;
  retryAttempts: number;
}

/**
 * Hook for monitoring token refresh status
 *
 * Provides real-time information about token refresh operations
 * and can be used to show user feedback or handle authentication issues.
 */
export function useTokenRefreshStatus(): TokenRefreshStatusInfo {
  const [statusInfo, setStatusInfo] = useState<TokenRefreshStatusInfo>({
    status: 'idle',
    lastRefresh: null,
    nextRefresh: null,
    error: null,
    retryAttempts: 0,
  });

  useEffect(() => {
    const tokenRefreshService = getTokenRefreshService();

    const unsubscribe = tokenRefreshService.subscribe((event, data) => {
      switch (event) {
        case 'refresh_scheduled':
          setStatusInfo(prev => ({
            ...prev,
            status: 'scheduled',
            nextRefresh: data?.refreshAt ? new Date(data.refreshAt) : null,
            error: null,
          }));
          break;

        case 'refresh_success':
          setStatusInfo(prev => ({
            ...prev,
            status: 'success',
            lastRefresh: new Date(),
            error: null,
            retryAttempts: 0,
          }));

          // Reset to idle after a short delay
          setTimeout(() => {
            setStatusInfo(prev => ({
              ...prev,
              status: 'idle',
            }));
          }, 2000);
          break;

        case 'refresh_failed':
          setStatusInfo(prev => ({
            ...prev,
            status: 'failed',
            error: data?.error || 'Token refresh failed',
            retryAttempts: data?.attempts || 0,
          }));
          break;
      }
    });

    return unsubscribe;
  }, []);

  return statusInfo;
}

/**
 * Hook for getting user-friendly status messages
 */
export function useTokenRefreshMessages(): {
  message: string | null;
  type: 'info' | 'success' | 'warning' | 'error' | null;
} {
  const statusInfo = useTokenRefreshStatus();

  switch (statusInfo.status) {
    case 'scheduled':
      return {
        message: statusInfo.nextRefresh
          ? `Authentication will refresh at ${statusInfo.nextRefresh.toLocaleTimeString()}`
          : 'Authentication refresh scheduled',
        type: 'info',
      };

    case 'success':
      return {
        message: 'Authentication refreshed successfully',
        type: 'success',
      };

    case 'failed':
      if (statusInfo.retryAttempts >= 3) {
        return {
          message: 'Authentication refresh failed. Please log in again.',
          type: 'error',
        };
      }
      return {
        message: `Authentication refresh failed (attempt ${statusInfo.retryAttempts}/3). Retrying...`,
        type: 'warning',
      };

    default:
      return {
        message: null,
        type: null,
      };
  }
}

/**
 * Hook for detecting when user needs to re-authenticate
 */
export function useAuthenticationRequired(): boolean {
  const statusInfo = useTokenRefreshStatus();

  return statusInfo.status === 'failed' && statusInfo.retryAttempts >= 3;
}
