/**
 * @file Root Layout - Server Component Entry Point
 * @module app/layout
 *
 * Main layout entry point that delegates to server layout wrapper
 * for proper CSP nonce handling following 2025 security standards.
 */

import type { Metadata } from 'next';
import type { ReactNode } from 'react';

import ServerLayout from './layout-server';
import './globals.css';

import 'leaflet/dist/leaflet.css';

export const metadata: Metadata = {
  authors: [{ name: 'WorkHub Team' }],
  description: 'Professional delegation and task management platform',
  keywords: ['delegation', 'task management', 'workflow', 'productivity'],
  openGraph: {
    description: 'Professional delegation and task management platform',
    locale: 'en_US',
    title: 'WorkHub - Delegation Management System',
    type: 'website',
  },
  robots: 'index, follow',
  title: 'WorkHub - Delegation Management System',
};

export const viewport = {
  width: 'device-width',
  initialScale: 1,
};

interface RootLayoutProps {
  children: ReactNode;
}

/**
 * Root Layout Component
 *
 * Server component that delegates to ServerLayout for CSP nonce handling.
 * Single responsibility: Layout structure and metadata definition.
 */
export default function RootLayout({ children }: RootLayoutProps) {
  return (
    <html className="h-full" lang="en" suppressHydrationWarning>
      <body className="flex min-h-screen flex-col font-sans antialiased">
        <ServerLayout>{children}</ServerLayout>
      </body>
    </html>
  );
}
