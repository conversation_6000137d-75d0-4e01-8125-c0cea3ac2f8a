/**
 * @file Tests for useFormValidation hook
 */

import { renderHook, act } from '@testing-library/react';
import { z } from 'zod';

import { useFormValidation } from '../forms/useFormValidation';

// Test schema
const TestSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  email: z.string().email('Invalid email'),
  age: z.number().min(18, 'Must be at least 18'),
});

type TestFormData = z.infer<typeof TestSchema>;

describe('useFormValidation', () => {
  it('should initialize with default values', () => {
    const defaultValues: Partial<TestFormData> = {
      name: '<PERSON>',
      email: '<EMAIL>',
      age: 25,
    };

    const { result } = renderHook(() =>
      useFormValidation({
        schema: TestSchema,
        defaultValues,
      })
    );

    expect(result.current.form.getValues()).toEqual(defaultValues);
    expect(result.current.isDirty).toBe(false);
  });

  it('should validate individual fields', async () => {
    const { result } = renderHook(() =>
      useFormValidation({
        schema: TestSchema,
        defaultValues: { name: '', email: '', age: 0 },
      })
    );

    // Test invalid field
    await act(async () => {
      result.current.form.setValue('name', '');
      const isValid = await result.current.validateField('name');
      expect(isValid).toBe(false);
    });

    // Test valid field
    await act(async () => {
      result.current.form.setValue('name', 'John');
      const isValid = await result.current.validateField('name');
      expect(isValid).toBe(true);
    });
  });

  it('should validate entire form', async () => {
    const { result } = renderHook(() =>
      useFormValidation({
        schema: TestSchema,
        defaultValues: { name: '', email: '', age: 0 },
      })
    );

    // Test invalid form
    await act(async () => {
      const isValid = await result.current.validateForm();
      expect(isValid).toBe(false);
    });

    // Test valid form
    await act(async () => {
      result.current.form.setValue('name', 'John');
      result.current.form.setValue('email', '<EMAIL>');
      result.current.form.setValue('age', 25);
      const isValid = await result.current.validateForm();
      expect(isValid).toBe(true);
    });
  });

  it('should provide error checking utilities', async () => {
    const { result } = renderHook(() =>
      useFormValidation({
        schema: TestSchema,
        defaultValues: { name: '', email: '', age: 0 },
      })
    );

    await act(async () => {
      result.current.form.setValue('email', 'invalid-email');
      await result.current.validateField('email');
    });

    expect(result.current.hasFieldError('email')).toBe(true);
    expect(result.current.getFieldError('email')).toBe('Invalid email');
  });

  it('should clear errors', async () => {
    const { result } = renderHook(() =>
      useFormValidation({
        schema: TestSchema,
        defaultValues: { name: '', email: '', age: 0 },
      })
    );

    // Create an error
    await act(async () => {
      result.current.form.setValue('email', 'invalid-email');
      await result.current.validateField('email');
    });

    expect(result.current.hasFieldError('email')).toBe(true);

    // Clear specific field error
    await act(async () => {
      result.current.clearFieldError('email');
    });

    expect(result.current.hasFieldError('email')).toBe(false);
  });

  it('should reset form to default values', async () => {
    const defaultValues: Partial<TestFormData> = {
      name: 'John',
      email: '<EMAIL>',
      age: 25,
    };

    const { result } = renderHook(() =>
      useFormValidation({
        schema: TestSchema,
        defaultValues,
      })
    );

    // Change values
    await act(async () => {
      result.current.form.setValue('name', 'Jane');
      result.current.form.setValue('email', '<EMAIL>');
    });

    expect(result.current.form.getValues().name).toBe('Jane');

    // Reset form
    await act(async () => {
      result.current.resetForm();
    });

    expect(result.current.form.getValues()).toEqual(defaultValues);
  });

  it('should use custom error messages', async () => {
    const customErrorMessages = {
      email: 'Please enter a valid email address',
    };

    const { result } = renderHook(() =>
      useFormValidation({
        schema: TestSchema,
        defaultValues: { name: '', email: '', age: 0 },
        customErrorMessages,
      })
    );

    await act(async () => {
      result.current.form.setValue('email', 'invalid-email');
      await result.current.validateField('email');
    });

    expect(result.current.getFieldError('email')).toBe(
      'Please enter a valid email address'
    );
  });
});
