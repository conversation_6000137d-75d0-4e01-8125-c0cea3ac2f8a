/**
 * @file Task Status Chart Component - Phase 2 Implementation
 * @description Task status distribution chart following existing chart patterns
 *
 * SOLID Principles Applied:
 * - SRP: Single responsibility of visualizing task status distribution
 * - OCP: Open for extension via props configuration and styling options
 * - DIP: Depends on existing chart utilities and component abstractions
 *
 * Architecture Compliance:
 * - Follows existing chart component patterns
 * - Uses established chart utilities and styling
 * - Integrates with existing chart framework
 * - Maintains consistent visual design
 */

'use client';

import { BarChart3 } from 'lucide-react';
import React, { useMemo } from 'react';
import {
  Cell,
  Legend,
  Pie,
  PieChart,
  ResponsiveContainer,
  Tooltip,
} from 'recharts';

import type { TaskStatusPrisma } from '@/lib/types/domain';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

import type { TaskStatusDistributionData } from '../../data/types/reporting';

interface TaskStatusChartProps {
  className?: string;
  data?: TaskStatusDistributionData[];
  height?: number;
  interactive?: boolean;
  showLegend?: boolean;
}

/**
 * @component TaskStatusChart
 * @description Task status distribution chart following existing chart patterns
 *
 * Responsibilities:
 * - Visualize task status distribution using pie chart
 * - Follow existing chart component patterns
 * - Integrate with existing chart utilities
 * - Maintain consistent chart styling
 * - Provide interactive features when enabled
 *
 * SOLID Principles Applied:
 * - SRP: Single responsibility of displaying task status chart
 * - OCP: Open for extension via props configuration
 * - DIP: Depends on chart library abstractions
 */
export const TaskStatusChart: React.FC<TaskStatusChartProps> = ({
  className = '',
  data = [],
  height = 300,
  interactive = true,
  showLegend = true,
}) => {
  // Transform data for chart consumption following existing patterns
  const chartData = useMemo(() => {
    return data.map(item => ({
      color: item.color || getTaskStatusColor(item.status),
      name: formatStatusLabel(item.status),
      percentage: Math.round(item.percentage),
      status: item.status,
      value: item.count,
    }));
  }, [data]);

  // Calculate total for display
  const totalTasks = useMemo(() => {
    return chartData.reduce((sum, item) => sum + item.value, 0);
  }, [chartData]);

  // Custom tooltip component following existing patterns
  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload?.length) {
      const data = payload[0].payload;
      return (
        <div className="rounded-lg border bg-white p-3 shadow-lg dark:bg-gray-800">
          <p className="font-medium">{data.name}</p>
          <p className="text-sm text-muted-foreground">
            Count: <span className="font-medium">{data.value}</span>
          </p>
          <p className="text-sm text-muted-foreground">
            Percentage: <span className="font-medium">{data.percentage}%</span>
          </p>
        </div>
      );
    }
    return null;
  };

  // Custom legend component following existing patterns
  const CustomLegend = ({ payload }: any) => {
    if (!showLegend || !payload) return null;

    return (
      <div className="mt-4 flex flex-wrap justify-center gap-4">
        {payload.map((entry: any, index: number) => (
          <div className="flex items-center gap-2" key={index}>
            <div
              className="size-3 rounded-full"
              style={{ backgroundColor: entry.color }}
            />
            <span className="text-sm text-muted-foreground">
              {entry.value} ({entry.payload.percentage}%)
            </span>
          </div>
        ))}
      </div>
    );
  };

  // Handle empty data state
  if (!data || data.length === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="size-5" />
            Task Status Distribution
          </CardTitle>
        </CardHeader>
        <CardContent
          className="flex items-center justify-center"
          style={{ height }}
        >
          <div className="text-center text-muted-foreground">
            <BarChart3 className="mx-auto mb-2 size-12 opacity-50" />
            <p>No task status data available</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <BarChart3 className="size-5" />
          Task Status Distribution
        </CardTitle>
        <p className="text-sm text-muted-foreground">
          Total Tasks: {totalTasks}
        </p>
      </CardHeader>

      <CardContent>
        <ResponsiveContainer height={height} width="100%">
          <PieChart>
            <Pie
              cx="50%"
              cy="50%"
              data={chartData}
              dataKey="value"
              fill="#8884d8"
              label={({ name, percentage }) => `${name}: ${percentage}%`}
              labelLine={false}
              outerRadius={Math.min(height * 0.3, 100)}
            >
              {chartData.map((entry, index) => (
                <Cell fill={entry.color} key={`cell-${index}`} />
              ))}
            </Pie>
            {interactive && <Tooltip content={<CustomTooltip />} />}
            {showLegend && <Legend content={<CustomLegend />} />}
          </PieChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
};

/**
 * Utility function to format status labels for display
 * Following existing patterns for status formatting
 */
const formatStatusLabel = (status: TaskStatusPrisma): string => {
  const statusLabels: Record<TaskStatusPrisma, string> = {
    Assigned: 'Assigned',
    Cancelled: 'Cancelled',
    Completed: 'Completed',
    In_Progress: 'In Progress',
    Pending: 'Pending',
  };
  return statusLabels[status] || status;
};

/**
 * Utility function to get consistent colors for task statuses
 * Following existing color patterns and accessibility guidelines
 */
const getTaskStatusColor = (status: TaskStatusPrisma): string => {
  const colorMap: Record<TaskStatusPrisma, string> = {
    Assigned: '#3b82f6', // Blue - assigned/active
    Cancelled: '#ef4444', // Red - cancelled/error
    Completed: '#10b981', // Green - completed/success
    In_Progress: '#8b5cf6', // Purple - in progress
    Pending: '#f59e0b', // Amber - waiting/pending
  };
  return colorMap[status] || '#6b7280'; // Gray fallback
};
