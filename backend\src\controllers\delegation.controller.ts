import type { Request, Response, NextFunction } from 'express';

import { v4 as uuidv4 } from 'uuid';

import type {
  DelegationDriver as PrismaDelegationDriver,
  DelegationEscort as PrismaDelegationEscort,
  DelegationVehicle as PrismaDelegationVehicle,
  Employee as PrismaEmployee,
  Prisma,
  Delegation as PrismaDelegation,
  Vehicle as PrismaVehicle,
  FlightDetails as PrismaFlightDetails,
} from '../generated/prisma/index.js';
import type {
  DelegationStatusEnumType,
  DelegationCreate,
  DelegationUpdate,
} from '../schemas/delegation.schema.js';
// Import event payload types
import type {
  DelegationCreatedPayload,
  DelegationDeletedPayload,
  DelegationUpdatedPayload,
  Delegation as WebSocketDelegationPayload, // Alias our payload Delegation type
} from '../types/websocketEvents.js'; // Added

import { DelegationStatus as PrismaDelegationStatus } from '../generated/prisma/index.js';
import * as delegationModel from '../models/delegation.model.js';
import { validateDelegationAssignment } from '../services/assignmentValidation.js';
// Removed: import { emitDelegationChange, SOCKET_EVENTS } from '../services/socketService.js';
import { getUnifiedWebSocketService } from '../services/UnifiedWebSocketService.js'; // Added
import { CRUD_EVENTS } from '../services/WebSocketEventManager.js'; // Added
import logger, { createContextLogger } from '../utils/logger.js'; // Corrected logger import
import HttpError from '../utils/HttpError.js'; // Import HttpError

const CRUD_ROOM = 'entity-updates'; // General room for CRUD updates

// Helper function to map Prisma Delegation to our WebSocket Delegation Payload
const mapPrismaDelegationToPayload = (
  prismaDelegation: PrismaDelegation & {
    vehicles?: Partial<PrismaDelegationVehicle>[]; // Assuming vehicles might be partially loaded
    drivers?: Partial<PrismaDelegationDriver>[]; // Assuming drivers might be partially loaded
    escorts?: Partial<PrismaDelegationEscort>[]; // Assuming escorts might be partially loaded
  },
): WebSocketDelegationPayload => {
  return {
    id: prismaDelegation.id,
    eventName: prismaDelegation.eventName,
    location: prismaDelegation.location,
    durationFrom: prismaDelegation.durationFrom.toISOString(),
    durationTo: prismaDelegation.durationTo.toISOString(),
    status: prismaDelegation.status,
    notes: prismaDelegation.notes,
    vehicleIds:
      prismaDelegation.vehicles?.map(dv => dv.vehicleId as number).filter(id => id !== undefined) ||
      [],
    driverEmployeeIds:
      prismaDelegation.drivers?.map(dd => dd.employeeId as number).filter(id => id !== undefined) ||
      [],
    escortEmployeeIds:
      prismaDelegation.escorts?.map(de => de.employeeId as number).filter(id => id !== undefined) ||
      [],
  };
};

// Helper function to transform Prisma delegation response to API response format
const transformPrismaDelegationToApiResponse = (prismaDelegation: any): any => {
  return {
    ...prismaDelegation,
    // Transform drivers: extract Employee objects from join table
    drivers: prismaDelegation.drivers?.map((driver: any) => driver.Employee).filter(Boolean) || [],
    // Transform escorts: extract Employee objects from join table
    escorts: prismaDelegation.escorts?.map((escort: any) => escort.Employee).filter(Boolean) || [],
    // Transform vehicles: extract Vehicle objects from join table
    vehicles:
      prismaDelegation.vehicles?.map((vehicle: any) => vehicle.Vehicle).filter(Boolean) || [],
    // Transform delegates: use Delegate array directly (already in correct format)
    delegates: prismaDelegation.Delegate || [],
    // Transform flight details: map Prisma relation names to API field names
    flightArrivalDetails:
      prismaDelegation.FlightDetails_Delegation_flightArrivalIdToFlightDetails || null,
    flightDepartureDetails:
      prismaDelegation.FlightDetails_Delegation_flightDepartureIdToFlightDetails || null,
    // Transform status history: use DelegationStatusEntry array directly
    statusHistory: prismaDelegation.DelegationStatusEntry || [],
  };
};

// Define a more specific type for what the model's updateDelegation expects
// This type does NOT extend Prisma.DelegationUpdateInput directly,
// as it contains fields for manual processing by the model layer.
interface CustomDelegationUpdatePayload {
  delegates?: { name: string; notes?: string | null; title: string }[]; // For update, delegates are replaced, ID is not needed from client here
  driverEmployeeIds?: number[];
  durationFrom?: Date | Prisma.DateTimeFieldUpdateOperationsInput | string;
  durationTo?: Date | Prisma.DateTimeFieldUpdateOperationsInput | string;
  // NEW PHASE 1 ASSIGNMENT FIELDS
  escortEmployeeIds?: number[];
  // Fields that can be directly part of Prisma.DelegationUpdateInput
  eventName?: Prisma.StringFieldUpdateOperationsInput | string;
  // Fields for manual processing by the model layer
  flightArrivalDetails?: null | (Prisma.FlightDetailsCreateInput & { id?: string });
  flightDepartureDetails?: null | (Prisma.FlightDetailsCreateInput & { id?: string });
  imageUrl?: null | Prisma.NullableStringFieldUpdateOperationsInput | string;

  invitationFrom?: null | Prisma.NullableStringFieldUpdateOperationsInput | string;
  invitationTo?: null | Prisma.NullableStringFieldUpdateOperationsInput | string;
  location?: Prisma.StringFieldUpdateOperationsInput | string;
  notes?: null | Prisma.NullableStringFieldUpdateOperationsInput | string;

  status?: Prisma.EnumDelegationStatusFieldUpdateOperationsInput | PrismaDelegationStatus; // Use imported Prisma enum
  statusChangeReason?: string; // For status history logic in model
  vehicleIds?: number[];
}

interface DelegationDriverWithEmployee extends PrismaDelegationDriver {
  Employee: PrismaEmployee;
}

// Define interfaces for populated many-to-many relations
interface DelegationEscortWithEmployee extends PrismaDelegationEscort {
  Employee: PrismaEmployee;
}

interface DelegationVehicleWithVehicle extends PrismaDelegationVehicle {
  Vehicle: PrismaVehicle;
}

// Refactored: Assumes basic field presence, format, and date logic (like from > to) is handled by Zod validation middleware
const processDelegationData = (
  data: DelegationCreate,
  contextLogger: any,
): Prisma.DelegationCreateInput => {
  contextLogger.debug('Processing validated delegation data for creation', {
    dataKeys: Object.keys(data || {}),
  });

  // Zod has already validated required fields and date formats/logic (durationFrom <= durationTo)
  // Type casting for dates as Zod schema ensures they are valid date strings or Date objects if preprocessed
  const durationFrom = new Date(data.durationFrom as string | Date);
  const durationTo = new Date(data.durationTo as string | Date);

  // Status has a default in Zod schema, so it will always be present
  const status = data.status as PrismaDelegationStatus;

  let flightArrivalDetailsDbInput:
    | Prisma.FlightDetailsCreateNestedOneWithoutDelegation_Delegation_flightArrivalIdToFlightDetailsInput
    | undefined = undefined;
  if (data.flightArrivalDetails) {
    // Zod schema for FlightDetails ensures required fields if the object is present
    flightArrivalDetailsDbInput = {
      create: {
        airport: data.flightArrivalDetails.airport,
        flightNumber: data.flightArrivalDetails.flightNumber,
        terminal: data.flightArrivalDetails.terminal,
        notes: data.flightArrivalDetails.notes,
        dateTime: new Date(data.flightArrivalDetails.dateTime as string | Date), // Ensure it's a Date object for Prisma
        id: uuidv4(), // Always generate new ID for new flight details
      },
    };
  }

  let flightDepartureDetailsDbInput:
    | Prisma.FlightDetailsCreateNestedOneWithoutDelegation_Delegation_flightDepartureIdToFlightDetailsInput
    | undefined = undefined;
  if (data.flightDepartureDetails) {
    flightDepartureDetailsDbInput = {
      create: {
        airport: data.flightDepartureDetails.airport,
        flightNumber: data.flightDepartureDetails.flightNumber,
        terminal: data.flightDepartureDetails.terminal,
        notes: data.flightDepartureDetails.notes,
        dateTime: new Date(data.flightDepartureDetails.dateTime as string | Date),
        id: uuidv4(), // Always generate new ID for new flight details
      },
    };
  }

  let delegatesInput: Prisma.DelegateCreateNestedManyWithoutDelegationInput | undefined = undefined;
  if (data.delegates && data.delegates.length > 0) {
    // Zod schema for Delegate ensures required fields (name, title)
    delegatesInput = {
      create: data.delegates.map(d => ({
        name: d.name,
        title: d.title,
        notes: d.notes,
        id: uuidv4(), // DelegateSchema.omit({ id: true }) for create, so always generate new ID
      })),
    };
  }

  const statusHistoryInput: Prisma.DelegationStatusEntryCreateNestedManyWithoutDelegationInput = {
    create: [{ id: uuidv4(), reason: 'Delegation created', status }],
  };

  const { eventName, imageUrl, invitationFrom, invitationTo, location, notes } = data;
  const createInput: Prisma.DelegationCreateInput = {
    id: uuidv4(),
    eventName,
    location,
    durationFrom,
    durationTo,
    status,
    imageUrl: imageUrl || null, // Ensure null if empty string or undefined from Zod optional
    invitationFrom: invitationFrom || null,
    invitationTo: invitationTo || null,
    notes: notes || null,
    createdAt: new Date(),
    updatedAt: new Date(),
    Delegate: delegatesInput,
    FlightDetails_Delegation_flightArrivalIdToFlightDetails: flightArrivalDetailsDbInput,
    FlightDetails_Delegation_flightDepartureIdToFlightDetails: flightDepartureDetailsDbInput,
    DelegationStatusEntry: statusHistoryInput,
  };

  // Zod schema's refine handles vehicle/driver assignment logic, so no need to re-check here.
  // IDs are also validated as positive integers by Zod.
  if (data.driverEmployeeIds && data.driverEmployeeIds.length > 0) {
    createInput.drivers = {
      create: data.driverEmployeeIds.map((id: number) => ({ employeeId: id })),
    };
  }
  if (data.escortEmployeeIds && data.escortEmployeeIds.length > 0) {
    createInput.escorts = {
      create: data.escortEmployeeIds.map((id: number) => ({ employeeId: id })),
    };
  }
  if (data.vehicleIds && data.vehicleIds.length > 0) {
    createInput.vehicles = { create: data.vehicleIds.map((id: number) => ({ vehicleId: id })) };
  }
  return createInput;
};

// Refactored: Assumes Zod validation middleware handles optional fields and formats
const processDelegationUpdateData = (
  data: DelegationUpdate, // Use Zod inferred type for update payload
  contextLogger: any,
): CustomDelegationUpdatePayload => {
  contextLogger.debug('Processing validated delegation data for update', {
    dataKeys: Object.keys(data || {}),
  });
  const updatePayload: CustomDelegationUpdatePayload = {};

  // Assign fields if they exist in the validated data (Zod handles partial nature)
  if (data.eventName !== undefined) updatePayload.eventName = data.eventName;
  if (data.location !== undefined) updatePayload.location = data.location;
  if (data.durationFrom !== undefined)
    updatePayload.durationFrom = new Date(data.durationFrom as string | Date);
  if (data.durationTo !== undefined)
    updatePayload.durationTo = new Date(data.durationTo as string | Date);
  if (data.status !== undefined) updatePayload.status = data.status as PrismaDelegationStatus;
  if (data.statusChangeReason !== undefined)
    updatePayload.statusChangeReason =
      data.statusChangeReason === null ? undefined : data.statusChangeReason;
  if (data.notes !== undefined) {
    updatePayload.notes = data.notes === null ? null : data.notes;
  }
  if (data.imageUrl !== undefined) {
    updatePayload.imageUrl = data.imageUrl === null ? null : data.imageUrl;
  }
  if (data.invitationFrom !== undefined) {
    updatePayload.invitationFrom = data.invitationFrom === null ? null : data.invitationFrom;
  }
  if (data.invitationTo !== undefined) {
    updatePayload.invitationTo = data.invitationTo === null ? null : data.invitationTo;
  }

  if (data.flightArrivalDetails !== undefined) {
    if (data.flightArrivalDetails === null) {
      updatePayload.flightArrivalDetails = null;
    } else {
      // Zod FlightDetailsSchema ensures required fields if object is not null
      updatePayload.flightArrivalDetails = {
        airport: data.flightArrivalDetails.airport,
        flightNumber: data.flightArrivalDetails.flightNumber,
        terminal: data.flightArrivalDetails.terminal,
        notes: data.flightArrivalDetails.notes,
        dateTime: new Date(data.flightArrivalDetails.dateTime as string | Date),
        id: data.flightArrivalDetails.id || uuidv4(),
      };
    }
  }
  if (data.flightDepartureDetails !== undefined) {
    if (data.flightDepartureDetails === null) {
      updatePayload.flightDepartureDetails = null;
    } else {
      updatePayload.flightDepartureDetails = {
        airport: data.flightDepartureDetails.airport,
        flightNumber: data.flightDepartureDetails.flightNumber,
        terminal: data.flightDepartureDetails.terminal,
        notes: data.flightDepartureDetails.notes,
        dateTime: new Date(data.flightDepartureDetails.dateTime as string | Date),
        id: data.flightDepartureDetails.id || uuidv4(),
      };
    }
  }

  // Model layer is expected to handle the logic for upserting/replacing delegates array
  if (data.delegates !== undefined)
    updatePayload.delegates = data.delegates.map(d => ({
      name: d.name,
      title: d.title,
      notes: d.notes,
    }));
  // Zod schema handles numeric arrays
  if (data.vehicleIds !== undefined) updatePayload.vehicleIds = data.vehicleIds;
  if (data.driverEmployeeIds !== undefined)
    updatePayload.driverEmployeeIds = data.driverEmployeeIds;
  if (data.escortEmployeeIds !== undefined)
    updatePayload.escortEmployeeIds = data.escortEmployeeIds;

  return updatePayload;
};

export const createDelegation = async (
  req: Request,
  res: Response,
  next: NextFunction,
): Promise<void> => {
  const contextLogger = createContextLogger({
    operation: 'createDelegation',
    method: req.method,
    url: req.originalUrl,
    ip: req.ip,
    body: req.body,
  });
  try {
    // req.body is now of type DelegationCreate, validated by Zod middleware
    const validatedBody = req.body as DelegationCreate;

    const assignmentValidation = await validateDelegationAssignment(
      validatedBody.escortEmployeeIds || [],
      validatedBody.driverEmployeeIds || [],
      validatedBody.vehicleIds || [],
      validatedBody.durationFrom
        ? new Date(validatedBody.durationFrom as string | Date)
        : undefined,
      validatedBody.durationTo ? new Date(validatedBody.durationTo as string | Date) : undefined,
    );

    if (!assignmentValidation.isValid) {
      contextLogger.warn('Delegation assignment validation failed', {
        validation: assignmentValidation,
      });
      // Use HttpError for consistent error responses
      throw new HttpError(
        assignmentValidation.error || 'Assignment validation failed',
        400,
        assignmentValidation.code,
      );
    }

    const delegationData = processDelegationData(validatedBody, contextLogger);
    const newDelegation = await delegationModel.createDelegation(delegationData);

    if (newDelegation) {
      try {
        const unifiedSocketService = getUnifiedWebSocketService();
        const payload = mapPrismaDelegationToPayload(newDelegation as any);
        unifiedSocketService.emitToRoom(CRUD_ROOM, CRUD_EVENTS.DELEGATION_CREATED, payload);
      } catch (wsError: any) {
        logger.error('Failed to emit WebSocket event for delegation creation', {
          error: wsError.message,
          stack: wsError.stack,
        });
      }
      // Transform Prisma response to API response format
      const transformedDelegation = transformPrismaDelegationToApiResponse(newDelegation);
      res.status(201).json(transformedDelegation);
    } else {
      contextLogger.error('Delegation creation returned null from model');
      throw new HttpError('Could not create delegation.', 400, 'DELEGATION_CREATION_FAILED');
    }
  } catch (error: any) {
    contextLogger.error('Error creating delegation', { error: error.message, stack: error.stack });
    // Use centralized error handling - pass error to error handler middleware
    next(
      error instanceof HttpError
        ? error
        : new HttpError(
            'Internal server error during delegation creation',
            500,
            'DELEGATION_CREATE_ERROR',
          ),
    );
  }
};

export const getAllDelegations = async (
  req: Request,
  res: Response,
  next: NextFunction,
): Promise<void> => {
  const contextLogger = createContextLogger({
    operation: 'getAllDelegations',
    method: req.method,
    url: req.originalUrl,
    ip: req.ip,
  });
  try {
    const delegations = await delegationModel.getAllDelegations(); // Model does not take contextLogger
    // Transform Prisma response to API response format
    const transformedDelegations = delegations.map(transformPrismaDelegationToApiResponse);
    res.status(200).json(transformedDelegations);
  } catch (error: any) {
    contextLogger.error('Error fetching all delegations', {
      error: error.message,
      stack: error.stack,
    });
    // Use centralized error handling
    next(
      error instanceof HttpError
        ? error
        : new HttpError('Error fetching delegations', 500, 'DELEGATION_FETCH_ERROR'),
    );
  }
};

export const getDelegationById = async (
  req: Request,
  res: Response,
  next: NextFunction,
): Promise<void> => {
  const contextLogger = createContextLogger({
    operation: 'getDelegationById',
    method: req.method,
    url: req.originalUrl,
    ip: req.ip,
    params: req.params,
  });
  try {
    const { id } = req.params;
    if (!id) {
      contextLogger.warn('Delegation ID not provided in params');
      throw new HttpError('Delegation ID is required', 400, 'DELEGATION_ID_REQUIRED');
    }
    const delegation = await delegationModel.getDelegationById(id); // Model does not take contextLogger
    if (delegation) {
      // Transform Prisma response to API response format
      const transformedDelegation = transformPrismaDelegationToApiResponse(delegation);
      res.status(200).json(transformedDelegation);
    } else {
      contextLogger.info('Delegation not found', { delegationId: id });
      throw new HttpError('Delegation not found', 404, 'DELEGATION_NOT_FOUND');
    }
  } catch (error: any) {
    contextLogger.error('Error fetching delegation by ID', {
      error: error.message,
      stack: error.stack,
    });
    // Use centralized error handling
    next(
      error instanceof HttpError
        ? error
        : new HttpError('Error fetching delegation', 500, 'DELEGATION_FETCH_BY_ID_ERROR'),
    );
  }
};

export const updateDelegation = async (
  req: Request,
  res: Response,
  next: NextFunction,
): Promise<void> => {
  const contextLogger = createContextLogger({
    operation: 'updateDelegation',
    method: req.method,
    url: req.originalUrl,
    ip: req.ip,
    params: req.params,
    body: req.body,
  });
  try {
    const { id } = req.params;
    // req.body is now of type DelegationUpdate, validated by Zod middleware
    const validatedBody = req.body as DelegationUpdate;

    const currentDelegation = await delegationModel.getDelegationById(id);
    if (!currentDelegation) {
      throw new HttpError('Delegation not found to update.', 404, 'DELEGATION_NOT_FOUND');
    }

    const escortIdsForValidation =
      validatedBody.escortEmployeeIds !== undefined
        ? validatedBody.escortEmployeeIds
        : currentDelegation.escorts?.map(e => e.employeeId) || [];
    const driverIdsForValidation =
      validatedBody.driverEmployeeIds !== undefined
        ? validatedBody.driverEmployeeIds
        : currentDelegation.drivers?.map(d => d.employeeId) || [];
    const vehicleIdsForValidation =
      validatedBody.vehicleIds !== undefined
        ? validatedBody.vehicleIds
        : currentDelegation.vehicles?.map(v => v.vehicleId) || [];

    const durationFromForValidation = validatedBody.durationFrom
      ? new Date(validatedBody.durationFrom as string | Date)
      : currentDelegation.durationFrom;
    const durationToForValidation = validatedBody.durationTo
      ? new Date(validatedBody.durationTo as string | Date)
      : currentDelegation.durationTo;

    const assignmentValidation = await validateDelegationAssignment(
      escortIdsForValidation,
      driverIdsForValidation,
      vehicleIdsForValidation,
      durationFromForValidation,
      durationToForValidation,
      id,
    );

    if (!assignmentValidation.isValid) {
      contextLogger.warn('Delegation assignment validation failed for update', {
        validation: assignmentValidation,
      });
      throw new HttpError(
        assignmentValidation.error || 'Assignment validation failed',
        400,
        assignmentValidation.code,
      );
    }

    const delegationUpdateData = processDelegationUpdateData(validatedBody, contextLogger);
    const updatedDelegation = await delegationModel.updateDelegation(id, delegationUpdateData);

    if (updatedDelegation) {
      try {
        const unifiedSocketService = getUnifiedWebSocketService();
        const payload = mapPrismaDelegationToPayload(updatedDelegation as any);
        unifiedSocketService.emitToRoom(CRUD_ROOM, CRUD_EVENTS.DELEGATION_UPDATED, payload);
      } catch (wsError: any) {
        logger.error('Failed to emit WebSocket event for delegation update', {
          error: wsError.message,
          stack: wsError.stack,
        });
      }
      // Transform Prisma response to API response format
      const transformedDelegation = transformPrismaDelegationToApiResponse(updatedDelegation);
      res.status(200).json(transformedDelegation);
    } else {
      contextLogger.error(
        'Delegation update returned null from model despite prior existence check',
        { delegationId: id },
      );
      throw new HttpError('Could not update delegation.', 400, 'DELEGATION_UPDATE_FAILED');
    }
  } catch (error: any) {
    contextLogger.error('Error updating delegation', { error: error.message, stack: error.stack });
    // Use centralized error handling
    next(
      error instanceof HttpError
        ? error
        : new HttpError(
            'Internal server error during delegation update',
            500,
            'DELEGATION_UPDATE_ERROR',
          ),
    );
  }
};

export const deleteDelegation = async (
  req: Request,
  res: Response,
  next: NextFunction,
): Promise<void> => {
  const contextLogger = createContextLogger({
    operation: 'deleteDelegation',
    method: req.method,
    url: req.originalUrl,
    ip: req.ip,
    params: req.params,
  });
  try {
    const { id } = req.params;
    if (!id) {
      contextLogger.warn('Delegation ID not provided in params for delete');
      throw new HttpError('Delegation ID is required for delete', 400, 'DELEGATION_ID_REQUIRED');
    }
    // For deleteDelegation, the model expects userId and userRole for RLS or audit.
    // Assuming these are on req.user, which should be populated by auth middleware.
    const userId = (req as any).user?.id;
    const userRole = (req as any).user?.user_role;

    if (!userId || !userRole) {
      contextLogger.warn('User ID or role not found on request for deleteDelegation', {
        userId,
        userRole,
      });
      // Depending on policy, this might be an error or just a warning if RLS is not strict.
      // For now, let it proceed, but this is a point of attention.
    }

    // Ensure userId and userRole are strings if they exist, otherwise pass undefined or handle as per model's expectation.
    // The model deleteDelegation(id: string, userId: string, userRole: string) expects strings.
    // If req.user might not exist, or these fields might not, this needs careful handling.
    // Forcing a placeholder if not present, or throwing error if strictly required by model.
    // Let's assume for now the auth middleware guarantees req.user.id and req.user.user_role.
    // If not, this call to deleteDelegation will fail or need adjustment.

    const deletedDelegation = await delegationModel.deleteDelegation(
      id,
      String(userId || 'system'),
      String(userRole || 'system'),
    );

    if (deletedDelegation) {
      try {
        const unifiedSocketService = getUnifiedWebSocketService();
        const payload: DelegationDeletedPayload = { id: deletedDelegation.id };
        unifiedSocketService.emitToRoom(CRUD_ROOM, CRUD_EVENTS.DELEGATION_DELETED, payload);
      } catch (wsError: any) {
        logger.error('Failed to emit WebSocket event for delegation deletion', {
          error: wsError.message,
          stack: wsError.stack,
        });
      }
      res
        .status(200)
        .json({ message: 'Delegation deleted successfully', delegation: deletedDelegation });
    } else {
      contextLogger.info('Delegation not found for deletion or could not be deleted', {
        delegationId: id,
      });
      throw new HttpError(
        'Delegation not found or could not be deleted',
        404,
        'DELEGATION_NOT_FOUND_OR_CANNOT_DELETE',
      );
    }
  } catch (error: any) {
    contextLogger.error('Error deleting delegation', { error: error.message, stack: error.stack });
    // Use centralized error handling
    next(
      error instanceof HttpError
        ? error
        : new HttpError('Error deleting delegation', 500, 'DELEGATION_DELETE_ERROR'),
    );
  }
};

// Exporting the custom type for model usage
export type { CustomDelegationUpdatePayload };
