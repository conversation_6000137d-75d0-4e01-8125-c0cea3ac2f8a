'use client';

import { Car, History, PlusCircle, Search, Wrench, X } from 'lucide-react';
import Link from 'next/link';
import { useCallback, useEffect, useMemo, useState } from 'react';

import type { Vehicle } from '@/lib/types/domain';
import type { EnrichedServiceRecord } from '@/lib/types/domain'; // Assuming this type is what useEnrichedServiceRecords returns

import ErrorBoundary from '@/components/error-boundaries/ErrorBoundary';
import ServiceRecordsErrorBoundary from '@/components/error-boundaries/ServiceRecordsErrorBoundary';
import { ReportActions } from '@/components/reports/ReportActions';
import { EnhancedServiceHistoryContainer } from '@/components/service-history/EnhancedServiceHistoryContainer';
import { ActionButton } from '@/components/ui/action-button';
import { AppBreadcrumb } from '@/components/ui/app-breadcrumb';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { EmptyState } from '@/components/ui/loading';
import { PageHeader } from '@/components/ui/PageHeader';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { useAuthContext } from '@/contexts/AuthContext';
import { useEnrichedServiceRecords } from '@/lib/stores/queries/useServiceRecords'; // Using the new hook
import { useVehicles } from '@/lib/stores/queries/useVehicles';

const commonServicesList = [
  'Oil Change',
  'Tire Rotation',
  'Brake Service',
  'Battery Replacement',
  'Air Filter Replacement',
  'Cabin Air Filter Replacement',
  'Wiper Blade Replacement',
  'Fluid Check/Top-up',
  'Spark Plug Replacement',
  'Coolant Flush',
  'Transmission Service',
  'Wheel Alignment',
  'State Inspection',
  'Other',
];

export default function ServiceHistoryPage() {
  const { loading: authLoading, session, user } = useAuthContext();
  const isAuthenticated = !!user && !!session?.access_token;

  const {
    data: allVehiclesData,
    error: vehiclesError,
    isLoading: rqIsLoadingVehicles,
    refetch: refetchVehicles,
  } = useVehicles({ enabled: isAuthenticated && !authLoading });
  const allVehicles = useMemo(() => allVehiclesData || [], [allVehiclesData]);

  const {
    data: serviceRecordsData,
    error: recordsError,
    isLoading: isLoadingRecords,
    refetch: refetchServiceRecords,
  } = useEnrichedServiceRecords({ enabled: isAuthenticated && !authLoading }); // Pass enabled option

  const serviceRecords = useMemo(
    () => serviceRecordsData || [],
    [serviceRecordsData]
  );

  const [selectedVehicleId, setSelectedVehicleId] = useState('all');
  const [selectedServiceType, setSelectedServiceType] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 300);
    return () => clearTimeout(timer);
  }, [searchTerm]);

  const uniqueServiceTypes = useMemo(() => {
    const types = new Set<string>();
    serviceRecords.forEach((record: EnrichedServiceRecord) => {
      record.servicePerformed.forEach((service: string) => types.add(service));
    });
    commonServicesList.forEach((service: string) => types.add(service));
    return [...types].sort();
  }, [serviceRecords]);

  // 🔧 FIX: Replace useEffect + useState with useMemo to prevent infinite loop
  // This prevents the "Maximum update depth exceeded" error by avoiding unstable array references
  const filteredRecords = useMemo(() => {
    let tempRecords = [...serviceRecords];
    if (selectedVehicleId !== 'all') {
      tempRecords = tempRecords.filter(
        record => String(record.vehicleId) === selectedVehicleId
      );
    }
    if (selectedServiceType !== 'all') {
      tempRecords = tempRecords.filter(record =>
        record.servicePerformed.includes(selectedServiceType)
      );
    }
    if (debouncedSearchTerm) {
      const lowerSearchTerm = debouncedSearchTerm.toLowerCase();
      tempRecords = tempRecords.filter(
        record =>
          `${record.vehicleMake} ${record.vehicleModel}`
            .toLowerCase()
            .includes(lowerSearchTerm) ||
          record.servicePerformed
            .join(' ')
            .toLowerCase()
            .includes(lowerSearchTerm) ||
          record.notes?.toLowerCase().includes(lowerSearchTerm) ||
          record.licensePlate?.toLowerCase().includes(lowerSearchTerm) ||
          record.odometer.toString().includes(lowerSearchTerm)
      );
    }
    return tempRecords;
  }, [
    serviceRecords,
    selectedVehicleId,
    selectedServiceType,
    debouncedSearchTerm,
  ]);

  const handleRetry = useCallback(() => {
    if (typeof refetchVehicles === 'function') refetchVehicles();
    if (typeof refetchServiceRecords === 'function') refetchServiceRecords();
  }, [refetchVehicles, refetchServiceRecords]);

  const pageIsLoading = authLoading || rqIsLoadingVehicles || isLoadingRecords;

  const errorMessage = useMemo(() => {
    const errors = [];
    if (vehiclesError)
      errors.push(`Vehicles: ${(vehiclesError as Error).message}`);
    if (recordsError)
      errors.push(`Service Records: ${(recordsError as Error).message}`);
    return errors.length > 0 ? errors.join('; ') : null;
  }, [vehiclesError, recordsError]);

  if (pageIsLoading) {
    return (
      <ErrorBoundary>
        <div className="space-y-6">
          <PageHeader icon={History} title="Service History">
            <div className="flex gap-2">
              <Skeleton className="h-10 w-32" />
              <Skeleton className="h-10 w-32" />
            </div>
          </PageHeader>
          <Card>
            <CardContent className="pt-6">
              <div className="mb-6 grid grid-cols-1 gap-4 md:grid-cols-3">
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-10 w-full" />
              </div>
            </CardContent>
          </Card>
          <div className="space-y-4">
            {Array.from({ length: 5 }).map((_, i) => (
              <div className="flex items-center space-x-4 border-b p-4" key={i}>
                <Skeleton className="h-6 w-1/6" />
                <Skeleton className="h-6 w-1/6" />
                <Skeleton className="h-6 w-2/6" />
                <Skeleton className="h-6 w-1/6" />
                <Skeleton className="h-6 w-1/6" />
              </div>
            ))}
          </div>
        </div>
      </ErrorBoundary>
    );
  }

  if (!authLoading && !isAuthenticated) {
    return (
      <ErrorBoundary>
        <div className="space-y-6 text-center">
          <PageHeader icon={History} title="Access Denied" />
          <p>Please sign in to view service history.</p>
        </div>
      </ErrorBoundary>
    );
  }

  // NEW: Unified Empty Page for no records
  if (
    !pageIsLoading &&
    isAuthenticated &&
    filteredRecords.length === 0 &&
    !errorMessage
  ) {
    const hasActiveFilters =
      selectedVehicleId !== 'all' ||
      selectedServiceType !== 'all' ||
      searchTerm;

    return (
      <ErrorBoundary>
        <div className="space-y-6">
          <AppBreadcrumb />
          <PageHeader
            description="View and manage all service records for your vehicles."
            icon={History}
            title="Service History Report"
          >
            <div className="no-print flex gap-2">
              <ActionButton
                actionType="tertiary"
                asChild
                icon={<PlusCircle className="size-4" />}
              >
                <Link href="/vehicles">Log New Service</Link>
              </ActionButton>
            </div>
          </PageHeader>

          <EmptyState
            description={
              hasActiveFilters
                ? 'There are no service records matching your current filters. You can log a new service record or adjust your filters.'
                : 'No service records have been logged yet. Get started by logging your first service record.'
            }
            icon={History}
            primaryAction={{
              href: '/vehicles',
              icon: <PlusCircle className="size-4" />,
              label: 'Log New Service',
            }}
            title="No Service Records Found"
            {...(hasActiveFilters && {
              secondaryAction: {
                icon: <X className="size-4" />,
                label: 'Reset Filters',
                onClick: () => {
                  setSelectedVehicleId('all');
                  setSelectedServiceType('all');
                  setSearchTerm('');
                },
              },
            })}
          />
        </div>
      </ErrorBoundary>
    );
  }

  return (
    <ErrorBoundary>
      <div className="print-container space-y-6">
        <AppBreadcrumb />
        <PageHeader
          description="View and manage all service records for your vehicles."
          icon={History}
          title="Service History Report"
        >
          <div className="no-print flex gap-2">
            <ActionButton
              actionType="tertiary"
              asChild
              icon={<PlusCircle className="size-4" />}
            >
              <Link href="/vehicles">Log New Service</Link>
            </ActionButton>
            <ReportActions
              enableCsv={filteredRecords.length > 0}
              fileName={`service-history-report-${
                new Date().toISOString().split('T')[0]
              }`}
              reportContentId="#service-history-report-content"
              reportType="service-history"
              tableId="#service-history-table"
            />
          </div>
        </PageHeader>

        <Card className="no-print shadow-sm">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Filter Options</CardTitle>
            <CardDescription>
              Filter service records by vehicle, service type, or search terms
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="filter-grid grid grid-cols-1 items-end gap-6 md:grid-cols-2 lg:grid-cols-3">
              <div>
                <Label htmlFor="vehicle-filter">Filter by Vehicle</Label>
                <Select
                  aria-label="Filter by vehicle"
                  onValueChange={setSelectedVehicleId}
                  value={selectedVehicleId}
                >
                  <SelectTrigger className="mt-1.5 w-full" id="vehicle-filter">
                    <SelectValue placeholder="All Vehicles" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Vehicles</SelectItem>
                    {allVehicles.map(vehicle => (
                      <SelectItem key={vehicle.id} value={String(vehicle.id)}>
                        {vehicle.make} {vehicle.model} ({vehicle.year})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="service-filter">Filter by Service Type</Label>
                <Select
                  aria-label="Filter by service type"
                  onValueChange={setSelectedServiceType}
                  value={selectedServiceType}
                >
                  <SelectTrigger className="mt-1.5 w-full" id="service-filter">
                    <SelectValue placeholder="All Services" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Services</SelectItem>
                    {uniqueServiceTypes.map(service => (
                      <SelectItem key={service} value={service}>
                        {service}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="search-records">Search Records</Label>
                <div className="relative mt-1.5">
                  <Input
                    aria-label="Search service records"
                    className="px-10"
                    id="search-records"
                    onChange={e => setSearchTerm(e.target.value)}
                    placeholder="Search by keyword, notes, plate..."
                    type="text"
                    value={searchTerm}
                  />
                  <Search
                    aria-hidden="true"
                    className="absolute left-3 top-1/2 size-4 -translate-y-1/2 text-muted-foreground"
                  />
                  {searchTerm && (
                    <Button
                      aria-label="Clear search"
                      className="absolute right-1 top-1/2 size-7 -translate-y-1/2"
                      onClick={() => setSearchTerm('')}
                      size="icon"
                      variant="ghost"
                    >
                      <X className="size-4" />
                      <span className="sr-only">Clear search</span>
                    </Button>
                  )}
                </div>
              </div>
            </div>

            {(selectedVehicleId !== 'all' ||
              selectedServiceType !== 'all' ||
              searchTerm) && (
              <div className="mt-6 flex flex-wrap items-center justify-between rounded-md border border-border p-3">
                <div className="flex flex-wrap items-center gap-2">
                  <span className="text-sm font-medium">Active Filters:</span>
                  {selectedVehicleId !== 'all' && (
                    <Badge
                      className="flex items-center gap-1"
                      variant="outline"
                    >
                      <Car className="size-3" />
                      {
                        allVehicles.find(
                          v => String(v.id) === selectedVehicleId
                        )?.make
                      }{' '}
                      {
                        allVehicles.find(
                          v => String(v.id) === selectedVehicleId
                        )?.model
                      }
                    </Badge>
                  )}
                  {selectedServiceType !== 'all' && (
                    <Badge
                      className="flex items-center gap-1"
                      variant="outline"
                    >
                      <Wrench className="size-3" />
                      {selectedServiceType}
                    </Badge>
                  )}
                  {searchTerm && (
                    <Badge
                      className="flex items-center gap-1"
                      variant="outline"
                    >
                      <Search className="size-3" />"{searchTerm}"
                    </Badge>
                  )}
                </div>
                <Button
                  aria-label="Reset all filters"
                  className="mt-2 sm:mt-0"
                  onClick={() => {
                    setSelectedVehicleId('all');
                    setSelectedServiceType('all');
                    setSearchTerm('');
                  }}
                  size="sm"
                  variant="outline"
                >
                  <X className="mr-1 size-3" />
                  Reset Filters
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

        <div className="report-content" id="service-history-report-content">
          <ServiceRecordsErrorBoundary>
            <EnhancedServiceHistoryContainer
              error={errorMessage}
              isLoading={isLoadingRecords && !serviceRecordsData}
              onRetry={handleRetry}
              records={filteredRecords}
              showVehicleInfo={true}
              vehicleSpecific={false}
            />
          </ServiceRecordsErrorBoundary>

          <footer className="mt-10 border-t-2 border-gray-300 pt-4 text-center text-xs text-gray-500">
            <p>Report generated on: {new Date().toLocaleDateString()}</p>
            <p>WorkHub - Vehicle Service Management</p>
          </footer>
        </div>

        <style global jsx>{`
          /* Styles remain the same */
        `}</style>
      </div>
    </ErrorBoundary>
  );
}
