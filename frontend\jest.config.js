/** @type {import('ts-jest').JestConfigWithTsJest} */
export default {
  displayName: 'frontend',
  preset: 'ts-jest',
  testEnvironment: 'jsdom',
  moduleNameMapper: {
    // Handle module aliases (from tsconfig.json)
    '^@/(.*)$': '<rootDir>/src/$1',
    // Handle CSS imports (if you're using CSS modules)
    '\\.(css|less|scss|sass)$': 'identity-obj-proxy',
    // Mock vitest with jest equivalents
    '^vitest$': '<rootDir>/src/test/vitestMock.js',
  },
  transform: {
    '^.+\\.(ts|tsx|js|jsx|mjs)$': [
      'ts-jest',
      {
        tsconfig: 'tsconfig.json',
        jsx: 'react-jsx',
        isolatedModules: true,
        diagnostics: {
          ignoreCodes: [151001],
        },
        useESM: false,
        babelConfig: {
          presets: [
            ['@babel/preset-react', { runtime: 'automatic' }],
            '@babel/preset-env',
          ],
        },
      },
    ],
  },
  transformIgnorePatterns: [
    '/node_modules/(?!(@testing-library|react-dom|react|next|@next|@radix-ui|lucide-react))',
  ],
  // Test file patterns - expanded to include more patterns
  testMatch: [
    '**/__tests__/**/*.test.ts',
    '**/__tests__/**/*.test.tsx',
    '**/src/**/*.test.ts',
    '**/src/**/*.test.tsx',
    '**/tests/**/*.test.ts',
    '**/tests/**/*.test.tsx',
  ],
  // Collect coverage from src directory
  collectCoverageFrom: [
    'src/**/*.ts',
    'src/**/*.tsx',
    '!src/**/*.d.ts',
    '!**/node_modules/**',
    '!src/app/layout.tsx', // Exclude Next.js app layout
    '!src/app/page.tsx', // Exclude Next.js app page
  ],
  // Specify the root directory
  rootDir: '.',
  // Specify test directory
  roots: ['<rootDir>/src', '<rootDir>/tests'],
  // Automatically clear mock calls between tests
  clearMocks: true,
  // Setup files
  setupFilesAfterEnv: ['<rootDir>/src/setupTests.ts'],
  // Set coverage thresholds
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
  },
  // Verbose output for better debugging
  verbose: true,
};
