// frontend/src/components/features/reporting/dashboard/filters/EmployeeFilter.tsx

import React from 'react';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { cn } from '@/lib/utils';
import { ChevronDown, X, Search, User } from 'lucide-react';
import {
  useReportingFilters,
  useReportingFiltersActions,
  useReportingFiltersValidation,
} from '../../data/stores/useReportingFiltersStore';

interface EmployeeFilterProps {
  compact?: boolean;
  className?: string;
}

export const EmployeeFilter: React.FC<EmployeeFilterProps> = ({
  compact = false,
  className = '',
}) => {
  const filters = useReportingFilters();
  const { setEmployees } = useReportingFiltersActions();
  const { validationErrors } = useReportingFiltersValidation();

  const [isOpen, setIsOpen] = React.useState(false);
  const [searchTerm, setSearchTerm] = React.useState('');

  // Mock employee options - in real app, this would come from an API
  const employeeOptions = [
    { id: 1, name: 'John Smith', department: 'Operations' },
    { id: 2, name: 'Sarah Johnson', department: 'Sales' },
    { id: 3, name: 'Mike Davis', department: 'Engineering' },
    { id: 4, name: 'Emily Brown', department: 'Marketing' },
    { id: 5, name: 'David Wilson', department: 'Operations' },
    { id: 6, name: 'Lisa Anderson', department: 'HR' },
    { id: 7, name: 'Tom Miller', department: 'Finance' },
    { id: 8, name: 'Anna Garcia', department: 'Sales' },
    { id: 9, name: 'Chris Taylor', department: 'Engineering' },
    { id: 10, name: 'Jessica Lee', department: 'Marketing' },
  ];

  const filteredEmployees = employeeOptions.filter(employee =>
    employee.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    employee.department.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleEmployeeToggle = (employeeId: number) => {
    const currentEmployees = filters.employees;
    const isSelected = currentEmployees.includes(employeeId);
    
    if (isSelected) {
      setEmployees(currentEmployees.filter(id => id !== employeeId));
    } else {
      setEmployees([...currentEmployees, employeeId]);
    }
  };

  const handleSelectAll = () => {
    setEmployees(filteredEmployees.map(emp => emp.id));
  };

  const handleSelectNone = () => {
    setEmployees([]);
  };

  const getDisplayText = () => {
    const selectedCount = filters.employees.length;
    if (selectedCount === 0) return 'All employees';
    if (selectedCount === 1) {
      const employee = employeeOptions.find(emp => emp.id === filters.employees[0]);
      return employee?.name || 'Unknown';
    }
    return `${selectedCount} employees`;
  };

  const hasError = validationErrors.employees;

  if (compact) {
    return (
      <div className={cn('space-y-1', className)}>
        <Label className="text-xs font-medium">Employee</Label>
        <Popover open={isOpen} onOpenChange={setIsOpen}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className={cn(
                'w-full justify-between text-left font-normal',
                hasError && 'border-red-500'
              )}
            >
              <span className="truncate">{getDisplayText()}</span>
              <ChevronDown className="ml-2 h-4 w-4 shrink-0" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-64 p-0" align="start">
            <div className="p-3">
              <div className="relative mb-3">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search employees..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8"
                />
              </div>
              <div className="flex justify-between mb-3">
                <Button variant="ghost" size="sm" onClick={handleSelectAll}>
                  All
                </Button>
                <Button variant="ghost" size="sm" onClick={handleSelectNone}>
                  None
                </Button>
              </div>
              <div className="max-h-48 overflow-y-auto space-y-2">
                {filteredEmployees.map((employee) => (
                  <div key={employee.id} className="flex items-center space-x-2">
                    <Checkbox
                      id={`employee-${employee.id}`}
                      checked={filters.employees.includes(employee.id)}
                      onCheckedChange={() => handleEmployeeToggle(employee.id)}
                    />
                    <Label
                      htmlFor={`employee-${employee.id}`}
                      className="text-sm font-normal cursor-pointer flex-1"
                    >
                      <div className="flex items-center gap-2">
                        <User className="h-3 w-3 text-muted-foreground" />
                        <div>
                          <div className="font-medium">{employee.name}</div>
                          <div className="text-xs text-muted-foreground">{employee.department}</div>
                        </div>
                      </div>
                    </Label>
                  </div>
                ))}
              </div>
            </div>
          </PopoverContent>
        </Popover>
        {hasError && (
          <p className="text-xs text-red-600">{hasError}</p>
        )}
      </div>
    );
  }

  return (
    <div className={cn('space-y-2', className)}>
      <Label className="text-sm font-medium">Employees</Label>
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className={cn(
              'w-full justify-between text-left font-normal',
              hasError && 'border-red-500'
            )}
          >
            <span>{getDisplayText()}</span>
            <ChevronDown className="ml-2 h-4 w-4 shrink-0" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-80 p-0" align="start">
          <div className="p-4">
            <div className="flex justify-between items-center mb-4">
              <h4 className="font-medium text-sm">Select Employees</h4>
              <div className="flex gap-2">
                <Button variant="ghost" size="sm" onClick={handleSelectAll}>
                  All
                </Button>
                <Button variant="ghost" size="sm" onClick={handleSelectNone}>
                  None
                </Button>
              </div>
            </div>
            
            <div className="relative mb-4">
              <Search className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search employees..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-9"
              />
            </div>
            
            <div className="max-h-64 overflow-y-auto space-y-3">
              {filteredEmployees.map((employee) => (
                <div key={employee.id} className="flex items-center space-x-3">
                  <Checkbox
                    id={`employee-${employee.id}`}
                    checked={filters.employees.includes(employee.id)}
                    onCheckedChange={() => handleEmployeeToggle(employee.id)}
                  />
                  <Label
                    htmlFor={`employee-${employee.id}`}
                    className="text-sm font-normal cursor-pointer flex-1"
                  >
                    <div className="flex items-center gap-3">
                      <User className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <div className="font-medium">{employee.name}</div>
                        <div className="text-xs text-muted-foreground">{employee.department}</div>
                      </div>
                    </div>
                  </Label>
                </div>
              ))}
            </div>
          </div>
        </PopoverContent>
      </Popover>
      
      {/* Selected employee badges */}
      {filters.employees.length > 0 && (
        <div className="flex flex-wrap gap-1">
          {filters.employees.slice(0, 3).map((employeeId) => {
            const employee = employeeOptions.find(emp => emp.id === employeeId);
            if (!employee) return null;
            
            return (
              <Badge key={employeeId} variant="secondary" className="text-xs pr-1">
                {employee.name}
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-auto p-0 ml-1 hover:bg-transparent"
                  onClick={() => handleEmployeeToggle(employeeId)}
                >
                  <X className="h-3 w-3" />
                </Button>
              </Badge>
            );
          })}
          {filters.employees.length > 3 && (
            <Badge variant="secondary" className="text-xs">
              +{filters.employees.length - 3} more
            </Badge>
          )}
        </div>
      )}
      
      {hasError && (
        <p className="text-sm text-red-600">{hasError}</p>
      )}
    </div>
  );
};

export default EmployeeFilter;
