/**
 * @file TanStack Query hooks for Delegation-related data.
 * These hooks manage fetching, caching, and mutating delegation data,
 * integrating with the DelegationApiService and DelegationTransformer.
 * @module stores/queries/useDelegations
 */

import type { UseQueryOptions } from '@tanstack/react-query';

import { useMutation, useQueries, useQueryClient } from '@tanstack/react-query';
import { useCallback, useMemo } from 'react';

import { undefinedToNull } from '@/lib/utils/typeHelpers';

// import { DelegationFormData } from '../../schemas/delegationSchemas'; // Not directly used by hooks' public API
import type { UpdateDelegationRequest } from '../../types/api'; // For useUpdateDelegation
import type {
  CreateDelegationData,
  Delegation,
  DelegationStatusPrisma,
  FlightDetails,
  // DelegationEscort, // Not directly used in optimistic updates in a way that needs separate import here
  // DelegationDriver,
  // DelegationVehicleAssignment,
} from '../../types/domain';

import { useCrudQuery } from '../../../hooks/api/useSmartQuery'; // Adjusted import path
import { delegationApiService } from '../../api/services/apiServiceFactory'; // Use centralized service
import { enrichDelegation } from '../../transformers/delegationEnrichment';
import { DelegationTransformer } from '../../transformers/delegationTransformer';
import {
  createDelegationWithAssignmentsQueries,
  delegationQueryKeys,
} from './delegationQueries';

export const useDelegations = (
  options?: Omit<
    UseQueryOptions<Delegation[], Error>,
    'queryFn' | 'queryKey'
  > & { enabled?: boolean }
) => {
  return useCrudQuery<Delegation[], Error>(
    [...delegationQueryKeys.all], // queryKey - spread for mutability
    async () => {
      const result = await delegationApiService.getAll();
      return result.data;
    },
    'delegation', // entityType
    {
      staleTime: 0, // Existing staleTime
      ...options, // Spread additional options
    }
  );
};

export const useDelegation = (id: string) => {
  return useCrudQuery<Delegation, Error>(
    [...delegationQueryKeys.detail(id)],
    async () => {
      return await delegationApiService.getById(id);
    },
    'delegation', // entityType for WebSocket events
    {
      enabled: !!id,
      staleTime: 5 * 60 * 1000,
    }
  );
};

// ✅ OPTIMIZED: Fast parallel data fetching for delegation with assignments
export const useDelegationWithAssignments = (id: string) => {
  // Execute all queries in parallel using useQueries for maximum performance
  const results = useQueries({
    queries: createDelegationWithAssignmentsQueries(id),
  });

  const [delegationQuery, employeesQuery, vehiclesQuery] = results;

  // Compute enriched delegation when all data is available
  const enrichedDelegation = useMemo(() => {
    if (
      !delegationQuery?.data ||
      !employeesQuery?.data ||
      !vehiclesQuery?.data
    ) {
      return;
    }

    try {
      // ✅ PRODUCTION FIX: delegationQuery.data is already transformed by the service layer
      // No need to apply DelegationTransformer.fromApi() again
      const delegation = delegationQuery.data as Delegation;
      return enrichDelegation(
        delegation,
        employeesQuery.data as any,
        vehiclesQuery.data as any
      );
    } catch (error) {
      console.error('Error enriching delegation data:', error);
      throw error;
    }
  }, [delegationQuery?.data, employeesQuery?.data, vehiclesQuery?.data]);

  // ✅ PRODUCTION FIX: Memoize refetch function to prevent infinite re-renders
  const refetch = useCallback(() => {
    delegationQuery?.refetch();
    employeesQuery?.refetch();
    vehiclesQuery?.refetch();
  }, [
    delegationQuery?.refetch,
    employeesQuery?.refetch,
    vehiclesQuery?.refetch,
  ]);

  // Return combined state with optimized loading states
  return {
    data: enrichedDelegation,
    error:
      delegationQuery?.error || employeesQuery?.error || vehiclesQuery?.error,
    isError:
      delegationQuery?.isError ||
      employeesQuery?.isError ||
      vehiclesQuery?.isError,
    isLoading:
      delegationQuery?.isLoading ||
      employeesQuery?.isLoading ||
      vehiclesQuery?.isLoading,
    isPending:
      delegationQuery?.isPending ||
      employeesQuery?.isPending ||
      vehiclesQuery?.isPending,
    refetch,
  };
};

// ✅ BACKWARD COMPATIBILITY: Alias for the optimized hook
export const useDelegationEnriched = useDelegationWithAssignments;

export const useCreateDelegation = () => {
  const queryClient = useQueryClient();
  interface CreateContext {
    previousDelegations: Delegation[] | undefined;
  }

  return useMutation<Delegation, Error, CreateDelegationData, CreateContext>({
    mutationFn: async (delegationData: CreateDelegationData) => {
      const apiPayload = DelegationTransformer.toCreateRequest(delegationData);
      // ✅ PRODUCTION FIX: delegationApiService.create() already applies transformation
      return await delegationApiService.create(apiPayload);
    },
    onError: (err, _delegationData, context) => {
      if (context?.previousDelegations) {
        queryClient.setQueryData(
          delegationQueryKeys.all,
          context.previousDelegations
        );
      }
      console.error('Failed to create delegation:', err);
      // Invalidate to refetch correct data on error
      queryClient.invalidateQueries({ queryKey: delegationQueryKeys.all });
    },
    onMutate: async (delegationData: CreateDelegationData) => {
      await queryClient.cancelQueries({ queryKey: delegationQueryKeys.all });
      const previousDelegations = queryClient.getQueryData<Delegation[]>(
        delegationQueryKeys.all
      );

      queryClient.setQueryData<Delegation[]>(
        delegationQueryKeys.all,
        (old = []) => {
          const tempId = `optimistic-${Date.now()}`;
          const now = new Date().toISOString();

          const optimisticArrivalFlight: FlightDetails | null =
            delegationData.flightArrivalDetails
              ? {
                  id: `optimistic-flight-arr-${Date.now()}`,
                  ...delegationData.flightArrivalDetails,
                }
              : null;

          const optimisticDepartureFlight: FlightDetails | null =
            delegationData.flightDepartureDetails
              ? {
                  id: `optimistic-flight-dep-${Date.now() + 1}`,
                  ...delegationData.flightDepartureDetails,
                }
              : null;

          const optimisticDelegates: Delegation['delegates'] =
            delegationData.delegates?.map((d, index) => ({
              id: `optimistic-delegate-${tempId}-${index}`,
              name: d.name, // Use d.name directly
              notes: d.notes ?? null,
              title: d.title, // Use d.title directly
            })) || [];

          const optimisticDelegation: Delegation = {
            arrivalFlight: optimisticArrivalFlight ?? null,
            createdAt: now,
            delegates: optimisticDelegates,
            departureFlight: optimisticDepartureFlight ?? null,
            drivers:
              delegationData.drivers?.map(d => ({
                createdAt: now, // Placeholder timestamp
                createdBy: null, // Placeholder
                delegationId: tempId, // Link to optimistic delegation
                employeeId: d.employeeId, // Keep as number
                id: `optimistic-driver-${tempId}-${d.employeeId}`, // Placeholder ID
                notes: d.notes ?? null, // Include notes if available in CreateDelegationData
                updatedAt: now, // Placeholder timestamp
              })) || [],
            durationFrom: delegationData.durationFrom,
            durationTo: delegationData.durationTo,
            escorts:
              delegationData.escorts?.map(e => ({
                createdAt: now, // Placeholder timestamp
                createdBy: null, // Placeholder
                delegationId: tempId, // Link to optimistic delegation
                employeeId: e.employeeId, // Keep as number
                id: `optimistic-escort-${tempId}-${e.employeeId}`, // Placeholder ID
                notes: e.notes ?? null, // Include notes if available in CreateDelegationData
                updatedAt: now, // Placeholder timestamp
              })) || [],
            eventName: delegationData.eventName,
            id: tempId,
            imageUrl: delegationData.imageUrl ?? null,
            invitationFrom: delegationData.invitationFrom ?? null,
            invitationTo: delegationData.invitationTo ?? null,
            location: delegationData.location,
            notes: delegationData.notes ?? null,
            status: delegationData.status || 'Planned',
            statusHistory: [],
            updatedAt: now,
            vehicles:
              delegationData.vehicles?.map(v => ({
                assignedDate: v.assignedDate,
                createdAt: now, // Placeholder timestamp
                createdBy: null, // Placeholder
                delegationId: tempId, // Link to optimistic delegation
                id: `optimistic-vehicle-${tempId}-${v.vehicleId}`, // Placeholder ID
                notes: v.notes ?? null,
                returnDate: v.returnDate ?? null,
                updatedAt: now, // Placeholder timestamp
                vehicleId: v.vehicleId,
              })) || [],
          };
          return [...old, optimisticDelegation];
        }
      );
      return { previousDelegations };
    },
    onSettled: () => {
      // Invalidate to ensure consistency after success or failure
      queryClient.invalidateQueries({ queryKey: delegationQueryKeys.all });
    },
  });
};

export const useUpdateDelegation = () => {
  const queryClient = useQueryClient();
  interface UpdateContext {
    previousDelegation: Delegation | undefined;
    previousDelegationsList: Delegation[] | undefined;
  }

  return useMutation<
    Delegation,
    Error,
    { data: UpdateDelegationRequest; id: string }, // Corrected: data is UpdateDelegationRequest
    UpdateContext
  >({
    mutationFn: async ({ data, id }) => {
      // ✅ PRODUCTION FIX: delegationApiService.update() already applies transformation
      return await delegationApiService.update(id, data);
    },
    onError: (err, variables, context) => {
      if (context?.previousDelegation) {
        queryClient.setQueryData(
          delegationQueryKeys.detail(variables.id),
          context.previousDelegation
        );
      }
      if (context?.previousDelegationsList) {
        queryClient.setQueryData(
          delegationQueryKeys.all,
          context.previousDelegationsList
        );
      }
      console.error('Failed to update delegation:', err);
      // Invalidate to refetch correct data on error
      queryClient.invalidateQueries({
        queryKey: delegationQueryKeys.detail(variables.id),
      });
      queryClient.invalidateQueries({ queryKey: delegationQueryKeys.all });
    },
    onMutate: async ({ data, id }) => {
      // data is UpdateDelegationRequest
      await queryClient.cancelQueries({ queryKey: delegationQueryKeys.all });
      await queryClient.cancelQueries({
        queryKey: delegationQueryKeys.detail(id),
      });

      const previousDelegation = queryClient.getQueryData<Delegation>(
        delegationQueryKeys.detail(id)
      );
      const previousDelegationsList = queryClient.getQueryData<Delegation[]>(
        delegationQueryKeys.all
      );

      queryClient.setQueryData<Delegation>(
        delegationQueryKeys.detail(id),
        old => {
          if (!old) return;
          const now = new Date().toISOString();

          // ✅ PRODUCTION FIX: Use correct field mappings for UpdateDelegationRequest
          const updatedOptimistic: Delegation = {
            ...old,
            // Handle flight details updates
            arrivalFlight: undefinedToNull(
              data.flightArrivalDetails === null
                ? null // Explicitly set to null if requested
                : data.flightArrivalDetails === undefined
                  ? old.arrivalFlight
                  : {
                      airport:
                        data.flightArrivalDetails.airport ||
                        old.arrivalFlight?.airport ||
                        '',
                      dateTime:
                        data.flightArrivalDetails.dateTime ||
                        old.arrivalFlight?.dateTime ||
                        '',
                      flightNumber:
                        data.flightArrivalDetails.flightNumber ||
                        old.arrivalFlight?.flightNumber ||
                        '',
                      id:
                        old.arrivalFlight?.id || `optimistic-arr-${Date.now()}`, // Keep old ID or generate new
                      notes:
                        data.flightArrivalDetails.notes ??
                        old.arrivalFlight?.notes ??
                        null,
                      terminal:
                        data.flightArrivalDetails.terminal ??
                        old.arrivalFlight?.terminal ??
                        null,
                    } // Keep old value if not in request
            ),
            departureFlight: undefinedToNull(
              data.flightDepartureDetails === null
                ? null // Explicitly set to null if requested
                : data.flightDepartureDetails === undefined
                  ? old.departureFlight
                  : {
                      airport:
                        data.flightDepartureDetails.airport ||
                        old.departureFlight?.airport ||
                        '',
                      dateTime:
                        data.flightDepartureDetails.dateTime ||
                        old.departureFlight?.dateTime ||
                        '',
                      flightNumber:
                        data.flightDepartureDetails.flightNumber ||
                        old.departureFlight?.flightNumber ||
                        '',
                      id:
                        old.departureFlight?.id ||
                        `optimistic-dep-${Date.now()}`, // Keep old ID or generate new
                      notes:
                        data.flightDepartureDetails.notes ??
                        old.departureFlight?.notes ??
                        null,
                      terminal:
                        data.flightDepartureDetails.terminal ??
                        old.departureFlight?.terminal ??
                        null,
                    } // Keep old value if not in request
            ),
            durationFrom: data.durationFrom ?? old.durationFrom, // ✅ Direct mapping
            durationTo: data.durationTo ?? old.durationTo, // ✅ Direct mapping
            // Direct field mappings (no transformation needed)
            eventName: data.eventName ?? old.eventName, // ✅ Direct mapping
            imageUrl: undefinedToNull(data.imageUrl ?? old.imageUrl),
            invitationFrom: undefinedToNull(
              data.invitationFrom ?? old.invitationFrom
            ),
            invitationTo: undefinedToNull(
              data.invitationTo ?? old.invitationTo
            ),
            location: data.location ?? old.location,
            notes: undefinedToNull(data.notes ?? old.notes),
            status: (data.status as DelegationStatusPrisma) ?? old.status, // Cast status
            updatedAt: now,
            // Note: Nested assignments (escorts, drivers, vehicles) are typically managed via separate mutations,
            // so they are not included in the main delegation update optimistic logic here.
          };
          return updatedOptimistic;
        }
      );

      queryClient.setQueryData<Delegation[]>(
        delegationQueryKeys.all,
        (oldList = []) =>
          oldList.map(delegation =>
            delegation.id === id
              ? queryClient.getQueryData<Delegation>(
                  delegationQueryKeys.detail(id)
                ) || delegation
              : delegation
          )
      );

      return { previousDelegation, previousDelegationsList };
    },
    onSettled: (_data, _error, variables) => {
      // Always refetch after error or success
      queryClient.invalidateQueries({
        queryKey: delegationQueryKeys.detail(variables.id),
      });
      queryClient.invalidateQueries({ queryKey: delegationQueryKeys.all });
    },
  });
};

export const useUpdateDelegationStatus = () => {
  const queryClient = useQueryClient();
  interface StatusUpdateContext {
    previousDelegation: Delegation | undefined;
  }

  return useMutation<
    Delegation,
    Error,
    { id: string; status: DelegationStatusPrisma; statusChangeReason?: string },
    StatusUpdateContext
  >({
    mutationFn: async ({ id, status, statusChangeReason }) => {
      const response = await delegationApiService.updateStatus(
        id,
        status,
        statusChangeReason
      );
      return response;
    },
    onError: (err, variables, context) => {
      if (context?.previousDelegation) {
        queryClient.setQueryData(
          delegationQueryKeys.detail(variables.id),
          context.previousDelegation
        );
      }
      console.error('Failed to update delegation status:', err);
    },
    onMutate: async ({ id, status }) => {
      await queryClient.cancelQueries({
        queryKey: delegationQueryKeys.detail(id),
      });
      const previousDelegation = queryClient.getQueryData<Delegation>(
        delegationQueryKeys.detail(id)
      );
      queryClient.setQueryData<Delegation>(
        delegationQueryKeys.detail(id),
        old => (old ? { ...old, status: status } : undefined)
      );
      return { previousDelegation };
    },
    onSettled: (_data, _error, variables) => {
      queryClient.invalidateQueries({
        queryKey: delegationQueryKeys.detail(variables.id),
      });
      queryClient.invalidateQueries({ queryKey: delegationQueryKeys.all });
    },
  });
};

export const useManageDelegationFlightDetails = () => {
  const queryClient = useQueryClient();
  interface FlightDetailsContext {
    previousDelegation: Delegation | undefined;
  }

  return useMutation<
    Delegation,
    Error,
    { flightDetails: FlightDetails; id: string },
    FlightDetailsContext
  >({
    mutationFn: async ({ flightDetails, id }) => {
      // This service method might need adjustment if flightDetails from form is Omit<FlightDetails, 'id'>
      // For now, assuming it expects full FlightDetails (including ID for existing, or will generate for new)
      const response = await delegationApiService.manageFlightDetails(
        id,
        flightDetails
      );
      return response;
    },
    onError: (err, variables, context) => {
      if (context?.previousDelegation) {
        queryClient.setQueryData(
          delegationQueryKeys.detail(variables.id),
          context.previousDelegation
        );
      }
      console.error('Failed to manage delegation flight details:', err);
    },
    onMutate: async ({ flightDetails, id }) => {
      await queryClient.cancelQueries({
        queryKey: delegationQueryKeys.detail(id),
      });
      const previousDelegation = queryClient.getQueryData<Delegation>(
        delegationQueryKeys.detail(id)
      );
      queryClient.setQueryData<Delegation>(
        delegationQueryKeys.detail(id),
        old => {
          if (!old) return;
          // This optimistic update assumes flightDetails is for arrival.
          // A more robust solution would need to know if it's arrival or departure.
          return { ...old, arrivalFlight: flightDetails };
        }
      );
      return { previousDelegation };
    },
    onSettled: (_data, _error, variables) => {
      queryClient.invalidateQueries({
        queryKey: delegationQueryKeys.detail(variables.id),
      });
      queryClient.invalidateQueries({ queryKey: delegationQueryKeys.all });
    },
  });
};

export const useDeleteDelegation = () => {
  const queryClient = useQueryClient();
  interface DeleteContext {
    previousDelegationsList: Delegation[] | undefined;
  }

  return useMutation<string, Error, string, DeleteContext>({
    mutationFn: async (id: string) => {
      await delegationApiService.delete(id);
      return id;
    },
    onError: (err, _id, context) => {
      if (context?.previousDelegationsList) {
        queryClient.setQueryData(
          delegationQueryKeys.all,
          context.previousDelegationsList
        );
      }
      console.error('Failed to delete delegation:', err);
    },
    onMutate: async id => {
      await queryClient.cancelQueries({ queryKey: delegationQueryKeys.all });
      await queryClient.cancelQueries({
        queryKey: delegationQueryKeys.detail(id),
      });

      const previousDelegationsList = queryClient.getQueryData<Delegation[]>(
        delegationQueryKeys.all
      );

      queryClient.setQueryData<Delegation[]>(
        delegationQueryKeys.all,
        (old = []) => old.filter(delegation => delegation.id !== id)
      );

      queryClient.removeQueries({ queryKey: delegationQueryKeys.detail(id) });

      return { previousDelegationsList };
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: delegationQueryKeys.all });
    },
  });
};
