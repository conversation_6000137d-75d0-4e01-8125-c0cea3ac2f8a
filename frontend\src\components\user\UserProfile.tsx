'use client';

import {
  Activity,
  Calendar,
  CheckCircle,
  Clock,
  Globe,
  Loader2,
  LogOut,
  Mail,
  Shield,
  ShieldCheck,
  Smartphone,
  User,
  XCircle,
} from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import React, { useState } from 'react';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { useAuthContext } from '@/contexts/AuthContext';

interface UserProfileProps {
  showSignOut?: boolean;
  variant?: 'card' | 'dropdown' | 'detailed';
}

// Helper function to get role badge color
const getRoleBadgeColor = (role: null | string) => {
  switch (role) {
    case 'ADMIN': {
      return 'bg-purple-500 hover:bg-purple-600 text-white';
    }
    case 'EMPLOYEE': {
      return 'bg-green-500 hover:bg-green-600 text-white';
    }
    case 'MANAGER': {
      return 'bg-blue-500 hover:bg-blue-600 text-white';
    }
    case 'SUPER_ADMIN': {
      return 'bg-red-500 hover:bg-red-600 text-white';
    }
    default: {
      return 'bg-gray-500 hover:bg-gray-600 text-white';
    }
  }
};

export function UserProfile({
  showSignOut = true,
  variant = 'dropdown',
}: UserProfileProps) {
  const { signOut, user, userRole } = useAuthContext();
  const router = useRouter();
  const [isSigningOut, setIsSigningOut] = useState(false);

  if (!user) {
    return null;
  }

  const handleSignOut = async () => {
    setIsSigningOut(true);
    try {
      await signOut();
      router.push('/login');
    } catch (error) {
      console.error('Sign out error:', error);
    } finally {
      setIsSigningOut(false);
    }
  };

  // Helper functions
  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const formatDateTime = (dateString: string | null | undefined) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getTimeAgo = (dateString: string | null | undefined) => {
    if (!dateString) return 'Never';
    const now = new Date();
    const date = new Date(dateString);
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) return 'Just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
    if (diffInSeconds < 86400)
      return `${Math.floor(diffInSeconds / 3600)}h ago`;
    if (diffInSeconds < 2592000)
      return `${Math.floor(diffInSeconds / 86400)}d ago`;
    return formatDate(dateString);
  };

  const getUserRole = () => {
    // Debug logging to see what data we have
    console.log('🔍 Role Debug Info:', {
      'user.user_metadata?.role': user.user_metadata?.role,
      'userRole (from context)': userRole,
      'user.user_metadata': user.user_metadata,
      'user.app_metadata': user.app_metadata,
      'user.email': user.email,
    });

    // TEMPORARY FIX: If user_metadata has role but context doesn't, show a refresh button
    if (user.user_metadata?.role && user.user_metadata.role !== userRole) {
      console.warn(
        '🚨 Role mismatch detected! User needs to refresh their session.',
        {
          metadataRole: user.user_metadata.role,
          contextRole: userRole,
        }
      );
    }

    return user.user_metadata?.role || userRole || 'USER';
  };

  const getUserStatus = () => {
    return user.user_metadata?.is_active !== false ? 'Active' : 'Inactive';
  };

  const getRoleBadgeVariant = (role: string) => {
    switch (role.toUpperCase()) {
      case 'SUPER_ADMIN':
        return 'destructive';
      case 'ADMIN':
        return 'destructive';
      case 'MANAGER':
        return 'default';
      case 'USER':
        return 'secondary';
      case 'READONLY':
        return 'outline';
      default:
        return 'secondary';
    }
  };

  const getStatusBadgeVariant = (status: string) => {
    return status === 'Active' ? 'default' : 'destructive';
  };

  const userInitials = user.email ? user.email.charAt(0).toUpperCase() : 'U';
  const userEmail = user.email || 'N/A';
  const isEmailVerified = user.email_confirmed_at !== null;

  // Use consistent role source - prioritize user_metadata over context
  const currentUserRole = getUserRole();
  const roleDisplayName = currentUserRole
    ? currentUserRole.replace('_', ' ')
    : 'N/A';
  const roleBadgeClass = getRoleBadgeColor(currentUserRole);

  if (variant === 'dropdown') {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button className="relative size-8 rounded-full" variant="ghost">
            <Avatar className="size-8">
              <AvatarImage
                alt={userInitials}
                src={user.user_metadata?.avatar_url || ''}
              />
              <AvatarFallback>{userInitials}</AvatarFallback>
            </Avatar>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-56" forceMount>
          <DropdownMenuLabel className="font-normal">
            <div className="flex flex-col space-y-1">
              <p className="text-sm font-medium leading-none">{userEmail}</p>
              <p className="text-xs leading-none text-muted-foreground">
                {user.id}
              </p>
            </div>
          </DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem>
            <User className="mr-2 size-4" />
            <Link href="/profile">Profile</Link>
          </DropdownMenuItem>
          <DropdownMenuItem>
            <Mail className="mr-2 size-4" />
            <span>
              {isEmailVerified ? 'Email Verified' : 'Email Not Verified'}
            </span>
            {isEmailVerified ? (
              <CheckCircle className="ml-auto size-4 text-green-500" />
            ) : (
              <XCircle className="ml-auto size-4 text-red-500" />
            )}
          </DropdownMenuItem>
          <DropdownMenuItem>
            <ShieldCheck className="mr-2 size-4" />
            <Badge className={roleBadgeClass}>{roleDisplayName}</Badge>
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={handleSignOut}>
            <LogOut className="mr-2 size-4" />
            <span>Log out</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    );
  }

  if (variant === 'card') {
    return (
      <Card className="mx-auto w-full max-w-md">
        <CardHeader className="flex flex-row items-center space-x-4 p-6">
          <Avatar className="size-16">
            <AvatarImage
              alt={userInitials}
              src={user.user_metadata?.avatar_url || ''}
            />
            <AvatarFallback className="text-2xl">{userInitials}</AvatarFallback>
          </Avatar>
          <div className="flex flex-col space-y-1">
            <CardTitle className="text-2xl font-bold">{userEmail}</CardTitle>
            <CardDescription className="text-sm text-muted-foreground">
              User ID: {user.id}
            </CardDescription>
            <Badge className={`${roleBadgeClass} px-2 py-1 text-sm`}>
              {roleDisplayName}
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="space-y-4 p-6 pt-0">
          <div className="flex items-center">
            <Mail className="mr-2 size-5 text-muted-foreground" />
            <span className="text-base">Email: {userEmail}</span>
            {isEmailVerified ? (
              <CheckCircle className="ml-2 size-5 text-green-500" />
            ) : (
              <XCircle className="ml-2 size-5 text-red-500" />
            )}
          </div>
          <div className="flex items-center">
            <ShieldCheck className="mr-2 size-5 text-muted-foreground" />
            <span className="text-base">Role: </span>
            <Badge className={`${roleBadgeClass} ml-1`}>
              {roleDisplayName}
            </Badge>
          </div>
          {/* Add more account details here as needed */}
          {showSignOut && (
            <div className="flex justify-end">
              <Button
                onClick={handleSignOut}
                variant="outline"
                disabled={isSigningOut}
              >
                {isSigningOut ? (
                  <>
                    <Loader2 className="mr-2 size-4 animate-spin" />
                    Signing out...
                  </>
                ) : (
                  <>
                    <LogOut className="mr-2 size-4" />
                    Log out
                  </>
                )}
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    );
  }

  // Detailed variant with comprehensive information
  if (variant === 'detailed') {
    return (
      <div className="w-full max-w-5xl mx-auto space-y-6">
        {/* Header Card */}
        <Card>
          <CardHeader className="pb-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <Avatar className="size-20">
                  <AvatarImage
                    alt={user.email || ''}
                    src={user.user_metadata?.avatar_url}
                  />
                  <AvatarFallback className="text-xl">
                    {userInitials}
                  </AvatarFallback>
                </Avatar>
                <div className="space-y-2">
                  <div>
                    <h1 className="text-3xl font-bold">
                      {user.user_metadata?.full_name || 'User Profile'}
                    </h1>
                    <p className="text-muted-foreground text-lg">
                      {user.email}
                    </p>
                  </div>
                  <div className="flex items-center gap-3 flex-wrap">
                    <Badge
                      className="text-sm px-3 py-1"
                      variant={getRoleBadgeVariant(getUserRole())}
                    >
                      {getUserRole()}
                    </Badge>
                    <Badge
                      className="text-sm px-3 py-1"
                      variant={getStatusBadgeVariant(getUserStatus())}
                    >
                      <Activity className="mr-1 size-3" />
                      {getUserStatus()}
                    </Badge>
                    {user.email_confirmed_at && (
                      <Badge className="text-sm px-3 py-1" variant="outline">
                        <Shield className="mr-1 size-3" />
                        Verified
                      </Badge>
                    )}
                  </div>
                </div>
              </div>
              {showSignOut && (
                <Button
                  disabled={isSigningOut}
                  onClick={handleSignOut}
                  variant="outline"
                  size="sm"
                >
                  {isSigningOut ? (
                    <>
                      <Loader2 className="mr-2 size-4 animate-spin" />
                      Signing out...
                    </>
                  ) : (
                    <>
                      <LogOut className="mr-2 size-4" />
                      Sign out
                    </>
                  )}
                </Button>
              )}
            </div>
          </CardHeader>
        </Card>

        {/* Detailed Information Tabs */}
        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="security">Security</TabsTrigger>
            <TabsTrigger value="activity">Activity</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              {/* Account Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-lg">
                    <User className="size-5" />
                    Account Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">
                        User ID:
                      </span>
                      <span className="font-mono text-xs bg-muted px-2 py-1 rounded">
                        {user.id.slice(0, 8)}...
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">
                        Email:
                      </span>
                      <span className="text-sm">{user.email}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">
                        Role:
                      </span>
                      <Badge variant={getRoleBadgeVariant(getUserRole())}>
                        {getUserRole()}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">
                        Status:
                      </span>
                      <Badge variant={getStatusBadgeVariant(getUserStatus())}>
                        {getUserStatus()}
                      </Badge>
                    </div>
                    {user.user_metadata?.employee_id && (
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-muted-foreground">
                          Employee ID:
                        </span>
                        <span className="text-sm">
                          {user.user_metadata.employee_id}
                        </span>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Contact & Verification */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-lg">
                    <Mail className="size-5" />
                    Contact & Verification
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">
                        Email Verified:
                      </span>
                      <div className="flex items-center gap-2">
                        {user.email_confirmed_at ? (
                          <>
                            <Shield className="size-4 text-green-600" />
                            <span className="text-sm text-green-600">
                              Verified
                            </span>
                          </>
                        ) : (
                          <>
                            <Shield className="size-4 text-red-600" />
                            <span className="text-sm text-red-600">
                              Not Verified
                            </span>
                          </>
                        )}
                      </div>
                    </div>
                    {user.email_confirmed_at && (
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-muted-foreground">
                          Verified On:
                        </span>
                        <span className="text-sm">
                          {formatDate(user.email_confirmed_at)}
                        </span>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="security" className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              {/* Security Status */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-lg">
                    <Shield className="size-5" />
                    Security Status
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Alert>
                    <Shield className="size-4" />
                    <AlertDescription>
                      Your account is protected by enterprise-grade security
                      protocols. All activities are monitored and logged for
                      security purposes.
                    </AlertDescription>
                  </Alert>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">
                        Account Type:
                      </span>
                      <span className="text-sm">Standard User</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">
                        SSO User:
                      </span>
                      <span className="text-sm">
                        {user.is_sso_user ? 'Yes' : 'No'}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">
                        Anonymous:
                      </span>
                      <span className="text-sm">
                        {user.is_anonymous ? 'Yes' : 'No'}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Session Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-lg">
                    <Globe className="size-5" />
                    Session Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">
                        Current Session:
                      </span>
                      <Badge variant="default">Active</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">
                        Session ID:
                      </span>
                      <span className="font-mono text-xs bg-muted px-2 py-1 rounded">
                        {user.id.slice(-8)}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">
                        App Metadata:
                      </span>
                      <span className="text-sm">
                        {user.app_metadata ? 'Present' : 'None'}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="activity" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                  <Activity className="size-5" />
                  Account Activity
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
                    <div className="flex items-center gap-3">
                      <Clock className="size-5 text-muted-foreground" />
                      <div>
                        <p className="text-sm font-medium">Last Sign In</p>
                        <p className="text-xs text-muted-foreground">
                          {formatDateTime(user.last_sign_in_at)}
                        </p>
                      </div>
                    </div>
                    <span className="text-sm text-muted-foreground">
                      {getTimeAgo(user.last_sign_in_at)}
                    </span>
                  </div>

                  <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
                    <div className="flex items-center gap-3">
                      <Calendar className="size-5 text-muted-foreground" />
                      <div>
                        <p className="text-sm font-medium">Account Created</p>
                        <p className="text-xs text-muted-foreground">
                          {formatDateTime(user.created_at)}
                        </p>
                      </div>
                    </div>
                    <span className="text-sm text-muted-foreground">
                      {getTimeAgo(user.created_at)}
                    </span>
                  </div>

                  <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
                    <div className="flex items-center gap-3">
                      <User className="size-5 text-muted-foreground" />
                      <div>
                        <p className="text-sm font-medium">Last Updated</p>
                        <p className="text-xs text-muted-foreground">
                          {formatDateTime(user.updated_at)}
                        </p>
                      </div>
                    </div>
                    <span className="text-sm text-muted-foreground">
                      {getTimeAgo(user.updated_at)}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    );
  }

  return null;
}
