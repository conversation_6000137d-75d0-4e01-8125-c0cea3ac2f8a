/**
 * @file EscortsCard component for displaying delegation escorts
 * @module components/delegations/detail/assignments/EscortsCard
 */

import React from 'react';
import { Shield } from 'lucide-react';
import { AssignmentSection } from './AssignmentSection';
import {
  formatEmployeeName,
  formatEmployeeRole,
} from '@/lib/utils/formattingUtils';
import type { Delegation } from '@/lib/types/domain';

interface EscortsCardProps {
  delegation: Delegation;
  className?: string;
}

/**
 * EscortsCard component for displaying delegation escorts
 * Uses the generic AssignmentSection for consistent styling
 */
export function EscortsCard({ delegation, className }: EscortsCardProps) {
  return (
    <AssignmentSection
      title="Escorts"
      icon={Shield}
      items={delegation.escorts ?? []}
      renderItem={(escort, index) => (
        <div
          key={escort.employeeId || index}
          className="rounded-lg border border-gray-200 bg-white p-4 space-y-2 hover:shadow-sm transition-shadow dark:border-gray-700 dark:bg-gray-800"
        >
          <h4 className="font-semibold text-gray-900 dark:text-white">
            {escort.employee
              ? formatEmployeeName(escort.employee)
              : `Employee ID: ${escort.employeeId}`}
          </h4>
          {escort.employee?.role && (
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {formatEmployeeRole(escort.employee.role)}
            </p>
          )}
          {escort.employee?.contactEmail && (
            <p className="text-xs text-gray-500 dark:text-gray-500 bg-gray-50 dark:bg-gray-700 p-2 rounded">
              📧 {escort.employee.contactEmail}
            </p>
          )}
        </div>
      )}
      emptyMessage="No escorts assigned to this delegation."
      className={className || undefined}
    />
  );
}

export default EscortsCard;
