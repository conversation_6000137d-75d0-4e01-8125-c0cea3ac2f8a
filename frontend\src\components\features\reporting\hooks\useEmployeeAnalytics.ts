/**
 * @file useEmployeeAnalytics.ts
 * @description Hook for fetching employee analytics data following existing patterns
 */

import { useQuery } from '@tanstack/react-query';
import { useApiQuery } from '@/hooks/api';
import { apiClient } from '@/lib/api';
import type {
  ReportingFilters,
  EmployeeAnalytics,
} from '../data/types/reporting';

/**
 * Hook for fetching employee analytics data
 *
 * Follows existing patterns from useTaskAnalytics and useVehicleAnalytics.
 * Integrates with the established API and caching patterns.
 *
 * @param filters - Optional reporting filters to apply
 * @returns Query result with employee analytics data
 */
export const useEmployeeAnalytics = (filters?: ReportingFilters) => {
  return useApiQuery(
    ['employee-analytics', filters],
    async (): Promise<EmployeeAnalytics> => {
      const queryParams = new URLSearchParams();

      if (filters?.dateRange) {
        // Defensive programming: Ensure dates are Date objects
        const fromDate =
          filters.dateRange.from instanceof Date
            ? filters.dateRange.from
            : new Date(filters.dateRange.from);
        const toDate =
          filters.dateRange.to instanceof Date
            ? filters.dateRange.to
            : new Date(filters.dateRange.to);

        queryParams.append(
          'dateRange.from',
          fromDate.toISOString().split('T')[0] || fromDate.toISOString()
        );
        queryParams.append(
          'dateRange.to',
          toDate.toISOString().split('T')[0] || toDate.toISOString()
        );
      }

      if (filters?.employees) {
        filters.employees.forEach(employee =>
          queryParams.append('employees', employee.toString())
        );
      }

      if (filters?.locations) {
        filters.locations.forEach(location =>
          queryParams.append('locations', location)
        );
      }

      if (filters?.includeEmployeeMetrics) {
        queryParams.append('includeEmployeeMetrics', 'true');
      }

      const url = `/reporting/employees/analytics${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
      const result = (await apiClient.get(url)) as any;
      return result.data || result;
    },
    {
      cacheDuration: 5 * 60 * 1000, // 5 minutes
      enableRetry: true,
      retryAttempts: 3,
    }
  );
};

/**
 * Hook for fetching employee performance metrics specifically
 *
 * @param filters - Optional reporting filters to apply
 * @returns Query result with employee performance data
 */
export const useEmployeePerformance = (filters?: ReportingFilters) => {
  return useApiQuery(
    ['employee-performance', filters],
    async () => {
      const queryParams = new URLSearchParams();

      if (filters?.dateRange) {
        queryParams.append('from', filters.dateRange.from.toISOString());
        queryParams.append('to', filters.dateRange.to.toISOString());
      }

      if (filters?.employees) {
        filters.employees.forEach(employee =>
          queryParams.append('employees', employee.toString())
        );
      }

      const url = `/api/reporting/employee-performance${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(
          `Failed to fetch employee performance: ${response.statusText}`
        );
      }
      return response.json();
    },
    {
      cacheDuration: 5 * 60 * 1000,
      enableRetry: true,
    }
  );
};

/**
 * Hook for fetching employee workload distribution
 *
 * @param filters - Optional reporting filters to apply
 * @returns Query result with workload distribution data
 */
export const useEmployeeWorkload = (filters?: ReportingFilters) => {
  return useApiQuery(
    ['employee-workload', filters],
    async () => {
      const queryParams = new URLSearchParams();

      if (filters?.dateRange) {
        queryParams.append('from', filters.dateRange.from.toISOString());
        queryParams.append('to', filters.dateRange.to.toISOString());
      }

      if (filters?.employees) {
        filters.employees.forEach(employee =>
          queryParams.append('employees', employee.toString())
        );
      }

      const url = `/api/reporting/employee-workload${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(
          `Failed to fetch employee workload: ${response.statusText}`
        );
      }
      return response.json();
    },
    {
      cacheDuration: 5 * 60 * 1000,
      enableRetry: true,
    }
  );
};

/**
 * Hook for fetching employee task assignments
 *
 * @param filters - Optional reporting filters to apply
 * @returns Query result with task assignment data
 */
export const useEmployeeTaskAssignments = (filters?: ReportingFilters) => {
  return useApiQuery(
    ['employee-task-assignments', filters],
    async () => {
      const queryParams = new URLSearchParams();

      if (filters?.dateRange) {
        queryParams.append('from', filters.dateRange.from.toISOString());
        queryParams.append('to', filters.dateRange.to.toISOString());
      }

      if (filters?.employees) {
        filters.employees.forEach(employee =>
          queryParams.append('employees', employee.toString())
        );
      }

      const url = `/api/reporting/employee-tasks${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(
          `Failed to fetch employee task assignments: ${response.statusText}`
        );
      }
      return response.json();
    },
    {
      cacheDuration: 5 * 60 * 1000,
      enableRetry: true,
    }
  );
};

/**
 * Hook for fetching employee availability metrics
 *
 * @param filters - Optional reporting filters to apply
 * @returns Query result with availability metrics data
 */
export const useEmployeeAvailability = (filters?: ReportingFilters) => {
  return useApiQuery(
    ['employee-availability', filters],
    async () => {
      const queryParams = new URLSearchParams();

      if (filters?.dateRange) {
        queryParams.append('from', filters.dateRange.from.toISOString());
        queryParams.append('to', filters.dateRange.to.toISOString());
      }

      if (filters?.employees) {
        filters.employees.forEach(employee =>
          queryParams.append('employees', employee.toString())
        );
      }

      const url = `/api/reporting/employee-availability${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(
          `Failed to fetch employee availability: ${response.statusText}`
        );
      }
      return response.json();
    },
    {
      cacheDuration: 5 * 60 * 1000,
      enableRetry: true,
    }
  );
};
