/**
 * Admin Module Index
 *
 * Centralized exports for the admin module, providing a clean
 * interface for importing admin functionality throughout the application.
 *
 * This module consolidates:
 * - Admin services
 * - Admin controllers
 * - Admin routes
 * - Admin types
 * - Admin utilities
 */

// Export admin controller
export { default as adminController } from './controllers/admin.controller.js';

// Export admin routes
export { default as adminRoutes } from './routes/admin.routes.js';

// Export admin service
export { adminService, AdminService } from './services/admin.service.js';

// Export admin types
export * from './types/admin.types.js';

// Export admin module configuration
export const ADMIN_MODULE_CONFIG = {
  dependencies: [
    'circuitBreaker.service',
    'metrics.service',
    'admin.service',
    'userManagement.service',
    'auditLog.service',
  ],
  description: 'Consolidated admin module with reliability enhancements',
  endpoints: {
    auditLogs: '/api/admin/audit-logs',
    dashboard: '/api/admin/dashboard',
    errorLogs: '/api/admin/logs/errors',
    health: '/api/admin/health',
    performance: '/api/admin/performance',
    statistics: '/api/admin/statistics',
    users: '/api/admin/users',
  },
  features: [
    'Circuit breaker protection',
    'Request deduplication',
    'Comprehensive audit logging',
    'Performance monitoring',
    'Centralized user management',
    'System health monitoring',
    'Error log management',
  ],
  middleware: [
    'supabaseAuth',
    'validateAdminRole',
    'requestDeduplication',
    'circuitBreakerMiddleware',
    'inputValidation',
  ],
  name: 'admin',
  version: '2.0.0',
};

// Export admin module metadata
export const ADMIN_MODULE_METADATA = {
  createdAt: new Date().toISOString(),
  performance: {
    caching: true,
    filtering: true,
    pagination: true,
    sorting: true,
  },
  phase: 'Phase 2 - Admin Services Consolidation',
  reliability: {
    auditLogging: true,
    circuitBreakers: true,
    metricsCollection: true,
    requestDeduplication: true,
  },
  security: {
    auditTrail: true,
    authentication: true,
    authorization: true,
    inputValidation: true,
  },
  status: 'active',
};

// Default export for convenience
export default {
  ADMIN_MODULE_CONFIG,
  ADMIN_MODULE_METADATA,
};
