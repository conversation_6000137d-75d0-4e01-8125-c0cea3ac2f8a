#!/usr/bin/env node

/**
 * Test script to verify employeeSchemas.list validation works correctly
 * This tests the schema independently before re-enabling in the route
 */

import { z } from 'zod';

// Recreate the schema locally for testing
const emailSchema = z.string().email().max(254);
const phoneSchema = z.string().max(20);
const uuidSchema = z.string().uuid();
const dateSchema = z.string().datetime().or(z.date());

const employeeSchemas = {
  list: z.object({
    body: z.object({}),
    params: z.object({}),
    query: z
      .object({
        page: z.string().regex(/^\d+$/).transform(Number).optional(),
        limit: z.string().regex(/^\d+$/).transform(Number).optional(),
        search: z.string().max(100).optional(),
        department: z.string().max(100).optional(),
        isActive: z
          .string()
          .regex(/^(true|false)$/)
          .transform(val => val === 'true')
          .optional(),
      })
      .passthrough(), // Allow additional query parameters
  }),
};

console.log('🧪 Testing employeeSchemas.list validation...\n');

// Test cases
const testCases = [
  {
    name: 'Empty request (typical GET)',
    data: {
      body: {},
      params: {},
      query: {},
    },
    shouldPass: true,
  },
  {
    name: 'Valid query parameters',
    data: {
      body: {},
      params: {},
      query: {
        page: '1',
        limit: '10',
        search: 'john',
        department: 'IT',
        isActive: 'true',
      },
    },
    shouldPass: true,
  },
  {
    name: 'Additional query parameters (passthrough)',
    data: {
      body: {},
      params: {},
      query: {
        page: '1',
        sort: 'name',
        order: 'asc',
      },
    },
    shouldPass: true,
  },
  {
    name: 'Invalid page parameter',
    data: {
      body: {},
      params: {},
      query: {
        page: 'invalid',
      },
    },
    shouldPass: false,
  },
  {
    name: 'Invalid isActive parameter',
    data: {
      body: {},
      params: {},
      query: {
        isActive: 'maybe',
      },
    },
    shouldPass: false,
  },
  {
    name: 'Search too long',
    data: {
      body: {},
      params: {},
      query: {
        search: 'a'.repeat(101),
      },
    },
    shouldPass: false,
  },
];

let passed = 0;
let failed = 0;

testCases.forEach((testCase, index) => {
  try {
    const result = employeeSchemas.list.parse(testCase.data);

    if (testCase.shouldPass) {
      console.log(`✅ Test ${index + 1}: ${testCase.name} - PASSED`);
      if (testCase.data.query.page) {
        console.log(
          `   Transformed page: ${result.query.page} (type: ${typeof result.query.page})`,
        );
      }
      if (testCase.data.query.isActive) {
        console.log(
          `   Transformed isActive: ${result.query.isActive} (type: ${typeof result.query.isActive})`,
        );
      }
      passed++;
    } else {
      console.log(
        `❌ Test ${index + 1}: ${testCase.name} - FAILED (should have failed but passed)`,
      );
      failed++;
    }
  } catch (error) {
    if (!testCase.shouldPass) {
      console.log(`✅ Test ${index + 1}: ${testCase.name} - PASSED (correctly failed)`);
      console.log(`   Error: ${error.errors?.[0]?.message || error.message}`);
      passed++;
    } else {
      console.log(`❌ Test ${index + 1}: ${testCase.name} - FAILED (should have passed)`);
      console.log(`   Error: ${error.errors?.[0]?.message || error.message}`);
      failed++;
    }
  }
  console.log('');
});

console.log('📊 Test Results:');
console.log(`   Passed: ${passed}`);
console.log(`   Failed: ${failed}`);
console.log(`   Total: ${testCases.length}`);

if (failed === 0) {
  console.log('\n🎉 All tests passed! Schema is working correctly.');
  console.log('✅ Safe to re-enable validation middleware.');
} else {
  console.log('\n⚠️ Some tests failed. Review schema before re-enabling validation.');
}
