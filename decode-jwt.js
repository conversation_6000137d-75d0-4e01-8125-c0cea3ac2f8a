/**
 * Decode JWT token to see the structure
 */

const jwt = 'eyJhbGciOiJIUzI1NiIsImtpZCI6IjAyU3J0UTcvQ3lVcGxudTMiLCJ0eXAiOiJKV1QifQ.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.OaxHaXEw0BZVTo9k0rF7-2FtaxRLAnAwrmdGbGDmKkY';

// Decode the payload (second part)
const payload = jwt.split('.')[1];
const decoded = JSON.parse(Buffer.from(payload, 'base64').toString());

console.log('JWT Payload:');
console.log(JSON.stringify(decoded, null, 2));
