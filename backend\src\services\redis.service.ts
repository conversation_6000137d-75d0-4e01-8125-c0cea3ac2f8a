/**
 * Centralized Redis Client Service
 *
 * Provides robust Redis client management with comprehensive error handling,
 * configurable reconnection strategy, and graceful degradation to memory cache.
 *
 * Features:
 * - Exponential backoff reconnection strategy (following ioredis best practices)
 * - Circuit breaker pattern for connection failures
 * - Comprehensive logging for connection events
 * - Graceful degradation when <PERSON>is is unavailable
 * - Connection pooling and health monitoring
 * - DNS resolution handling for Docker environments
 *
 * <AUTHOR> Development Team
 * @version 2.0 - Critical Issues Resolution with 2025 Best Practices
 */

import type { Redis } from 'ioredis';

import IORedis from 'ioredis';

import logger from '../utils/logger.js';

/**
 * Redis connection states for comprehensive monitoring
 */
export enum RedisConnectionState {
  CIRCUIT_OPEN = 'circuit_open',
  CONNECTED = 'connected',
  CONNECTING = 'connecting',
  DISCONNECTED = 'disconnected',
  ERROR = 'error',
  RECONNECTING = 'reconnecting',
}

/**
 * Circuit breaker configuration interface
 */
interface CircuitBreakerConfig {
  failureThreshold: number;
  monitoringPeriod: number;
  resetTimeout: number;
}

/**
 * Redis client configuration interface
 */
interface RedisClientConfig {
  connectTimeout: number;
  enableReadyCheck: boolean;
  lazyConnect: boolean;
  maxRetriesPerRequest: number;
  retryDelayOnFailover: number;
}

/**
 * Centralized Redis client manager with enterprise-grade reliability patterns
 */
class RedisClientManager {
  private client: Redis | null = null;
  private connectionState: RedisConnectionState = RedisConnectionState.DISCONNECTED;
  private reconnectAttempts = 0;
  private circuitBreakerFailures = 0;
  private lastCircuitBreakerReset = Date.now();

  // Configuration from environment variables with sensible defaults
  private readonly maxReconnectAttempts = parseInt(
    process.env.REDIS_MAX_RECONNECT_ATTEMPTS || '10',
    10,
  );

  private readonly circuitBreakerConfig: CircuitBreakerConfig = {
    failureThreshold: parseInt(process.env.REDIS_CIRCUIT_BREAKER_THRESHOLD || '5', 10),
    monitoringPeriod: parseInt(process.env.REDIS_CIRCUIT_BREAKER_MONITORING_PERIOD || '300000', 10), // 5 minutes
    resetTimeout: parseInt(process.env.REDIS_CIRCUIT_BREAKER_RESET_TIMEOUT || '60000', 10), // 1 minute
  };

  /**
   * Initialize Redis client with robust configuration following ioredis best practices
   *
   * @returns {Promise<Redis | null>} Redis client instance or null if unavailable
   */
  async initialize(): Promise<Redis | null> {
    const redisUrl = process.env.REDIS_URL;

    if (!redisUrl) {
      logger.warn('Redis URL not configured, using memory fallback', {
        service: 'redis-client-manager',
        state: RedisConnectionState.DISCONNECTED,
      });
      return null;
    }

    if (this.isCircuitBreakerOpen()) {
      logger.warn('Redis circuit breaker is open, skipping connection attempt', {
        failures: this.circuitBreakerFailures,
        service: 'redis-client-manager',
        state: RedisConnectionState.CIRCUIT_OPEN,
      });
      return null;
    }

    try {
      this.connectionState = RedisConnectionState.CONNECTING;

      // Enhanced Redis configuration following 2025 best practices
      const config: RedisClientConfig = {
        connectTimeout: parseInt(process.env.REDIS_CONNECTION_TIMEOUT || '10000', 10),
        enableReadyCheck: false, // Disable for better performance in Docker
        lazyConnect: true, // Connect only when needed
        maxRetriesPerRequest: parseInt(process.env.REDIS_RETRY_ATTEMPTS || '3', 10),
        retryDelayOnFailover: 100, // Fast failover
      };

      // Enhanced retry strategy with exponential backoff and jitter
      const retryStrategy = (times: number): number | null => {
        if (times > this.maxReconnectAttempts) {
          logger.error('Redis max reconnection attempts exceeded', {
            attempts: times,
            maxAttempts: this.maxReconnectAttempts,
            service: 'redis-client-manager',
          });
          return null; // Stop retrying
        }

        // Exponential backoff with jitter (following ioredis best practices)
        const delay = Math.min(times * 50 + Math.random() * 100, 2000);
        logger.info('Redis retry strategy: scheduling reconnection', {
          attempt: times,
          delay,
          service: 'redis-client-manager',
        });
        return delay;
      };

      this.client = new IORedis.default(redisUrl, {
        ...config,
        retryStrategy,
        // Enhanced reconnection on specific errors (following ioredis patterns)
        reconnectOnError: (err: Error) => {
          const targetErrors = ['READONLY', 'ECONNRESET', 'ENOTFOUND', 'ETIMEDOUT'];
          const shouldReconnect = targetErrors.some(target => err.message.includes(target));

          if (shouldReconnect) {
            logger.info('Redis reconnecting due to recoverable error', {
              error: err.message,
              service: 'redis-client-manager',
            });
          }

          return shouldReconnect;
        },
      });

      // Set up comprehensive event handlers
      this.setupEventHandlers();

      // Test connection with timeout
      const isConnected = await this.testConnection();

      if (isConnected) {
        this.connectionState = RedisConnectionState.CONNECTED;
        this.reconnectAttempts = 0;
        this.resetCircuitBreaker();

        logger.info('Redis client initialized successfully', {
          service: 'redis-client-manager',
          state: this.connectionState,
          url: redisUrl.replace(/\/\/.*?@/, '//****@'), // Hide credentials
        });

        return this.client;
      } else {
        throw new Error('Redis connection test failed');
      }
    } catch (error) {
      this.handleConnectionError(error instanceof Error ? error : new Error('Unknown Redis error'));
      return null;
    }
  }

  /**
   * Get the current Redis client instance
   *
   * @returns {Redis | null} Current Redis client or null
   */
  getClient(): Redis | null {
    if (this.connectionState === RedisConnectionState.CONNECTED && this.client) {
      return this.client;
    }
    return null;
  }

  /**
   * Check if Redis is available and connected
   *
   * @returns {Promise<boolean>} True if Redis is available
   */
  async isAvailable(): Promise<boolean> {
    if (!this.client || this.connectionState !== RedisConnectionState.CONNECTED) {
      return false;
    }

    try {
      await this.client.ping();
      return true;
    } catch (error) {
      logger.warn('Redis ping failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        service: 'redis-client-manager',
        state: this.connectionState,
      });
      return false;
    }
  }

  /**
   * Get current connection state
   *
   * @returns {RedisConnectionState} Current connection state
   */
  getConnectionState(): RedisConnectionState {
    return this.connectionState;
  }

  /**
   * Gracefully disconnect Redis client
   *
   * @returns {Promise<void>}
   */
  async disconnect(): Promise<void> {
    if (this.client) {
      try {
        await this.client.quit();
        logger.info('Redis client disconnected gracefully', {
          service: 'redis-client-manager',
        });
      } catch (error) {
        logger.error('Error during Redis disconnect', {
          error: error instanceof Error ? error.message : 'Unknown error',
          service: 'redis-client-manager',
        });
      } finally {
        this.client = null;
        this.connectionState = RedisConnectionState.DISCONNECTED;
      }
    }
  }

  /**
   * Check if circuit breaker is open
   *
   * @private
   * @returns {boolean} True if circuit breaker is open
   */
  private isCircuitBreakerOpen(): boolean {
    const now = Date.now();

    // Reset circuit breaker if enough time has passed
    if (now - this.lastCircuitBreakerReset > this.circuitBreakerConfig.resetTimeout) {
      this.resetCircuitBreaker();
      return false;
    }

    return this.circuitBreakerFailures >= this.circuitBreakerConfig.failureThreshold;
  }

  /**
   * Reset circuit breaker state
   *
   * @private
   */
  private resetCircuitBreaker(): void {
    this.circuitBreakerFailures = 0;
    this.lastCircuitBreakerReset = Date.now();
  }

  /**
   * Handle connection errors with circuit breaker logic
   *
   * @private
   * @param error - Connection error
   */
  private handleConnectionError(error: Error): void {
    this.connectionState = RedisConnectionState.ERROR;
    this.circuitBreakerFailures++;

    logger.error('Redis connection error', {
      error: error.message,
      failures: this.circuitBreakerFailures,
      threshold: this.circuitBreakerConfig.failureThreshold,
      service: 'redis-client-manager',
      state: this.connectionState,
    });

    if (this.circuitBreakerFailures >= this.circuitBreakerConfig.failureThreshold) {
      logger.warn('Redis circuit breaker opened due to repeated failures', {
        failures: this.circuitBreakerFailures,
        service: 'redis-client-manager',
      });
    }
  }

  /**
   * Setup comprehensive event handlers for Redis client
   *
   * @private
   */
  private setupEventHandlers(): void {
    if (!this.client) return;

    this.client.on('connect', () => {
      this.connectionState = RedisConnectionState.CONNECTED;
      logger.info('Redis client connected', {
        service: 'redis-client-manager',
        state: this.connectionState,
      });
    });

    this.client.on('ready', () => {
      this.connectionState = RedisConnectionState.CONNECTED;
      this.reconnectAttempts = 0;
      logger.info('Redis client ready for operations', {
        service: 'redis-client-manager',
        state: this.connectionState,
      });
    });

    this.client.on('error', (error: Error) => {
      this.handleConnectionError(error);
    });

    this.client.on('close', () => {
      this.connectionState = RedisConnectionState.DISCONNECTED;
      logger.warn('Redis connection closed', {
        service: 'redis-client-manager',
        state: this.connectionState,
      });
    });

    this.client.on('reconnecting', (times: number) => {
      this.connectionState = RedisConnectionState.RECONNECTING;
      this.reconnectAttempts = times;
      logger.info('Redis client reconnecting', {
        attempt: times,
        maxAttempts: this.maxReconnectAttempts,
        service: 'redis-client-manager',
        state: this.connectionState,
      });
    });

    this.client.on('end', () => {
      this.connectionState = RedisConnectionState.DISCONNECTED;
      logger.warn('Redis connection ended', {
        service: 'redis-client-manager',
        state: this.connectionState,
      });
    });
  }

  /**
   * Test Redis connection with timeout
   *
   * @private
   * @returns {Promise<boolean>} True if connection test passes
   */
  private async testConnection(): Promise<boolean> {
    if (!this.client) return false;

    try {
      const result = await Promise.race([
        this.client.ping(),
        new Promise<string>((_, reject) =>
          setTimeout(() => {
            reject(new Error('Connection test timeout'));
          }, 5000),
        ),
      ]);

      return result === 'PONG';
    } catch (error) {
      logger.error('Redis connection test failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        service: 'redis-client-manager',
      });
      return false;
    }
  }
}

// Singleton instance
const redisClientManager = new RedisClientManager();

/**
 * Initialize Redis client (call once at application startup)
 *
 * @returns {Promise<Redis | null>} Redis client instance or null
 */
export const initializeRedisClient = async (): Promise<Redis | null> => {
  return await redisClientManager.initialize();
};

/**
 * Get shared Redis client instance
 *
 * @returns {Redis | null} Redis client or null if unavailable
 */
export const getRedisClient = (): Redis | null => {
  return redisClientManager.getClient();
};

/**
 * Check if Redis is available
 *
 * @returns {Promise<boolean>} True if Redis is available
 */
export const isRedisAvailable = async (): Promise<boolean> => {
  return await redisClientManager.isAvailable();
};

/**
 * Get Redis connection state
 *
 * @returns {RedisConnectionState} Current connection state
 */
export const getRedisConnectionState = (): RedisConnectionState => {
  return redisClientManager.getConnectionState();
};

/**
 * Gracefully disconnect Redis client
 *
 * @returns {Promise<void>}
 */
export const disconnectRedis = async (): Promise<void> => {
  await redisClientManager.disconnect();
};

// Export the manager instance for advanced usage
export { redisClientManager };
