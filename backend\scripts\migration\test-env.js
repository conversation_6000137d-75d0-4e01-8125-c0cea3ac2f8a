#!/usr/bin/env node

/**
 * Test Environment Variables
 * Simple script to test if environment variables are loaded correctly
 */

import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

console.log('🔍 Testing environment variables...');
console.log('SUPABASE_URL:', process.env.SUPABASE_URL ? '✅ Set' : '❌ Missing');
console.log('SUPABASE_SERVICE_ROLE_KEY:', process.env.SUPABASE_SERVICE_ROLE_KEY ? '✅ Set' : '❌ Missing');

if (process.env.SUPABASE_URL) {
  console.log('URL starts with:', process.env.SUPABASE_URL.substring(0, 20) + '...');
}

if (process.env.SUPABASE_SERVICE_ROLE_KEY) {
  console.log('Service key starts with:', process.env.SUPABASE_SERVICE_ROLE_KEY.substring(0, 20) + '...');
}

console.log('✅ Environment test completed');
