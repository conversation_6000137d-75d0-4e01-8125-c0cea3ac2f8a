/**
 * @file ReportGenerationPage.tsx
 * @description Main page for data report generation with tabbed interface
 */

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  FileText,
  BarChart3,
  User,
  History,
  Download,
  Settings,
  Info,
} from 'lucide-react';
import { DataReportGenerator } from './DataReportGenerator';
import { IndividualReportGenerator } from './IndividualReportGenerator';
import { AggregateReportGenerator } from './AggregateReportGenerator';
import { ReportHistory } from './ReportHistory';
import { Alert, AlertDescription } from '@/components/ui/alert';

/**
 * Tab configurations
 */
const TABS = [
  {
    id: 'comprehensive',
    label: 'Comprehensive Reports',
    icon: FileText,
    description: 'Generate reports across multiple entity types',
    component: DataReportGenerator,
  },
  {
    id: 'individual',
    label: 'Individual Reports',
    icon: User,
    description: 'Generate detailed reports for specific entities',
    component: IndividualReportGenerator,
  },
  {
    id: 'aggregate',
    label: 'Aggregate Analytics',
    icon: BarChart3,
    description: 'Generate analytics reports with aggregated data',
    component: AggregateReportGenerator,
  },
  {
    id: 'history',
    label: 'Report History',
    icon: History,
    description: 'View and manage generated reports',
    component: ReportHistory,
  },
];

/**
 * ReportGenerationPage Component
 *
 * Main interface for all report generation capabilities.
 * Provides tabbed interface for different report types.
 */
export const ReportGenerationPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<string>('comprehensive');
  const [recentReports, setRecentReports] = useState<any[]>([]);

  /**
   * Handle successful report generation
   */
  const handleReportGenerated = (result: any) => {
    console.log('Report generated:', result);

    // Extract metadata from the correct location in the response
    const metadata = result?.data?.metadata || result?.metadata;

    if (!metadata) {
      console.error('No metadata found in report result:', result);
      alert('Report generated, but metadata is missing');
      return;
    }

    // Create a normalized report object for storage
    const normalizedReport = {
      ...result,
      metadata,
    };

    // Add to recent reports
    setRecentReports(prev => [normalizedReport, ...prev.slice(0, 4)]);

    // Show success message
    // In a real app, you might want to show a toast notification
    alert(`Report generated successfully! ID: ${metadata.id}`);
  };

  /**
   * Handle report selection from history
   */
  const handleReportSelect = (report: any) => {
    console.log('Report selected:', report);
    // In a real app, you might want to open a report viewer
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            Data Report Generation
          </h1>
          <p className="text-gray-600 mt-2">
            Generate comprehensive reports for delegations, tasks, vehicles, and
            employees
          </p>
        </div>
        <div className="flex items-center gap-3">
          <Badge variant="outline" className="flex items-center gap-2">
            <Download className="h-4 w-4" />
            Report Builder
          </Badge>
          <Button variant="outline" size="sm">
            <Settings className="h-4 w-4 mr-2" />
            Settings
          </Button>
        </div>
      </div>

      {/* Quick Stats */}
      {recentReports.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Info className="h-5 w-5" />
              Recent Activity
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {recentReports.map((report, index) => {
                // Safely extract metadata
                const metadata = report?.metadata;
                if (!metadata) {
                  return null; // Skip reports without metadata
                }

                return (
                  <div key={index} className="bg-gray-50 p-3 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <Badge variant="outline" className="text-xs">
                        {metadata.type || 'Unknown'}
                      </Badge>
                      <span className="text-xs text-gray-500">
                        {metadata.generatedAt
                          ? new Date(metadata.generatedAt).toLocaleTimeString()
                          : 'Unknown time'}
                      </span>
                    </div>
                    <p className="text-sm font-medium truncate">
                      {metadata.id || 'No ID'}
                    </p>
                    <p className="text-xs text-gray-600">
                      {metadata.entityTypes?.join(', ') ||
                        metadata.entityType ||
                        'Unknown entity'}
                    </p>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Information Alert */}
      <Alert>
        <Info className="h-4 w-4" />
        <AlertDescription>
          <strong>Report Generation System:</strong> Generate individual entity
          reports, aggregate analytics, or comprehensive reports across multiple
          data sources. All reports support PDF, Excel, and CSV export formats.
        </AlertDescription>
      </Alert>

      {/* Main Tabs Interface */}
      <Tabs
        value={activeTab}
        onValueChange={setActiveTab}
        className="space-y-6"
      >
        {/* Tab Navigation */}
        <Card>
          <CardContent className="pt-6">
            <TabsList className="grid w-full grid-cols-4">
              {TABS.map(tab => {
                const Icon = tab.icon;
                return (
                  <TabsTrigger
                    key={tab.id}
                    value={tab.id}
                    className="flex items-center gap-2 data-[state=active]:bg-blue-50"
                  >
                    <Icon className="h-4 w-4" />
                    <span className="hidden sm:inline">{tab.label}</span>
                  </TabsTrigger>
                );
              })}
            </TabsList>

            {/* Tab Description */}
            <div className="mt-4 p-4 bg-gray-50 rounded-lg">
              {TABS.map(tab => {
                if (tab.id !== activeTab) return null;
                const Icon = tab.icon;
                return (
                  <div key={tab.id} className="flex items-center gap-3">
                    <Icon className="h-5 w-5 text-gray-600" />
                    <div>
                      <h3 className="font-medium text-gray-900">{tab.label}</h3>
                      <p className="text-sm text-gray-600">{tab.description}</p>
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>

        {/* Tab Content */}
        {TABS.map(tab => {
          const Component = tab.component;
          return (
            <TabsContent key={tab.id} value={tab.id} className="space-y-6">
              <Component
                onReportGenerated={handleReportGenerated}
                onReportSelect={handleReportSelect}
              />
            </TabsContent>
          );
        })}
      </Tabs>

      {/* Help Section */}
      <Card>
        <CardHeader>
          <CardTitle>Report Generation Guide</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <FileText className="h-4 w-4 text-blue-600" />
                <span className="font-medium">Comprehensive</span>
              </div>
              <p className="text-sm text-gray-600">
                Generate reports that include data from multiple entity types
                with cross-entity analytics.
              </p>
            </div>

            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <User className="h-4 w-4 text-green-600" />
                <span className="font-medium">Individual</span>
              </div>
              <p className="text-sm text-gray-600">
                Create detailed reports for specific delegations, tasks,
                vehicles, or employees.
              </p>
            </div>

            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <BarChart3 className="h-4 w-4 text-orange-600" />
                <span className="font-medium">Aggregate</span>
              </div>
              <p className="text-sm text-gray-600">
                Generate analytics reports with aggregated metrics and trend
                analysis.
              </p>
            </div>

            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <History className="h-4 w-4 text-purple-600" />
                <span className="font-medium">History</span>
              </div>
              <p className="text-sm text-gray-600">
                View, download, and manage all your previously generated
                reports.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
