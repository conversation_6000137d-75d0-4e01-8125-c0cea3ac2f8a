// @ts-nocheck - Keeping this for now due to potential complexities with Delegate type
'use client';

import { format, parseISO } from 'date-fns';
import {
  AlertTriangle,
  ArrowLeft,
  Briefcase,
  CalendarDays,
  Car,
  Clock,
  Edit,
  Info,
  MapPin,
  Plane,
  PlaneLanding,
  PlaneTakeoff,
  Printer,
  RefreshCw,
  Shield,
  Trash2,
  User,
  Users,
} from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { useParams, useRouter } from 'next/navigation';
import { useCallback, useEffect, useState } from 'react'; // useState will be removed for hook state

import type {
  DelegationStatusPrisma,
  Delegation as DomainDelegation,
  FlightDetails as DomainFlightDetails, // Renamed for clarity
  DelegationStatusUpdate as DomainStatusHistoryEntry, // Renamed for clarity
} from '@/lib/types/domain'; // Updated types

import {
  DelegationDetailHeader,
  DelegationSidebar,
  DelegationTabs,
} from '@/components/features/delegations/detail'; // Import the new components
import { ViewReportButton } from '@/components/reports/ViewReportButton';
import { ActionButton } from '@/components/ui/action-button';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { AppBreadcrumb } from '@/components/ui/app-breadcrumb';
import { Badge } from '@/components/ui/badge';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { DataLoader, SkeletonLoader } from '@/components/ui/loading'; // ErrorDisplay removed as DataLoader handles it
import { PageHeader } from '@/components/ui/PageHeader';
import { Separator } from '@/components/ui/separator';
import { usePredefinedEntityToast } from '@/hooks/forms/useFormToast';
import {
  useDelegation,
  useDeleteDelegation,
  useUpdateDelegationStatus, // Import the new hook
} from '@/lib/stores/queries/useDelegations'; // Updated hooks
import { cn } from '@/lib/utils';
import {
  formatDelegationStatusForDisplay,
  formatEmployeeName,
  formatEmployeeRole,
} from '@/lib/utils/formattingUtils';
import { getSafeDelegationImageUrl } from '@/lib/utils/imageUtils';

// getStatusColor now uses DelegationStatusPrisma
const getStatusColor = (status: DelegationStatusPrisma | undefined) => {
  switch (status) {
    case 'Cancelled': {
      return 'bg-red-500/20 text-red-700 border-red-500/30 dark:text-red-400 dark:bg-red-500/10 dark:border-red-500/20';
    }
    case 'Completed': {
      return 'bg-purple-500/20 text-purple-700 border-purple-500/30 dark:text-purple-400 dark:bg-purple-500/10 dark:border-purple-500/20';
    }
    case 'Confirmed': {
      return 'bg-green-500/20 text-green-700 border-green-500/30 dark:text-green-400 dark:bg-green-500/10 dark:border-green-500/20';
    }
    case 'In_Progress': {
      return 'bg-yellow-500/20 text-yellow-700 border-yellow-500/30 dark:text-yellow-400 dark:bg-yellow-500/10 dark:border-yellow-500/20';
    }
    case 'Planned': {
      return 'bg-blue-500/20 text-blue-700 border-blue-500/30 dark:text-blue-400 dark:bg-blue-500/10 dark:border-blue-500/20';
    }
    case 'No_details':
    default: {
      return 'bg-gray-500/20 text-gray-700 border-gray-500/30 dark:text-gray-400 dark:bg-gray-500/10 dark:border-gray-500/20';
    }
  }
};

const formatDate = (dateString: string | undefined, includeTime = false) => {
  if (!dateString) return 'N/A'; // Added check for undefined dateString
  try {
    return format(
      parseISO(dateString),
      includeTime ? 'MMM d, yyyy, HH:mm' : 'MMM d, yyyy'
    );
  } catch {
    return 'Invalid Date';
  }
};

interface DetailItemProps {
  children?: React.ReactNode;
  icon: React.ElementType;
  label: string;
  value?: null | number | string;
  valueClassName?: string;
}

export default function DelegationDetailPage() {
  const params = useParams();
  const router = useRouter();
  const {
    showEntityDeleted,
    showEntityDeletionError,
    showEntityUpdated,
    showEntityUpdateError,
  } = usePredefinedEntityToast('delegation');

  const delegationId = params.id as string;

  const {
    data: delegation, // This is DomainDelegation | undefined
    error,
    isLoading,
    refetch,
  } = useDelegation(delegationId);

  const deleteDelegationMutation = useDeleteDelegation();
  const updateDelegationStatusMutation = useUpdateDelegationStatus(); // Initialize the new mutation hook

  const handleDeleteDelegation = async () => {
    if (delegation) {
      try {
        await deleteDelegationMutation.mutateAsync(delegation.id);
        const delegationForToast = {
          event: delegation.eventName,
          location: delegation.location,
        };
        showEntityDeleted(delegationForToast);
        router.push('/delegations');
      } catch (deleteError: any) {
        console.error('Error deleting delegation:', deleteError);
        showEntityDeletionError(
          deleteError.message ||
            'Failed to delete delegation. Please try again.'
        );
      }
    }
  };

  const handleStatusUpdateConfirm = async (
    status: DelegationStatusPrisma,
    reason: string
  ) => {
    if (delegation) {
      try {
        await updateDelegationStatusMutation.mutateAsync({
          id: delegation.id,
          status: status,
          statusChangeReason: reason,
        });
        const delegationForToast = {
          event: delegation.eventName,
          location: delegation.location,
        };
        showEntityUpdated(delegationForToast);
        // Modal state is managed by StatusHistoryCard component
        // No need to refetch explicitly, react-query's onSettled in useUpdateDelegationStatus handles invalidation
      } catch (updateError: any) {
        console.error('Error updating delegation status:', updateError);
        showEntityUpdateError(
          updateError.message ||
            'Failed to update delegation status. Please try again.'
        );
      }
    }
  };

  return (
    <DataLoader
      data={delegation}
      emptyComponent={
        <div className="py-10 text-center">
          <PageHeader icon={AlertTriangle} title="Delegation Not Found" />
          <p className="mb-4">The requested delegation could not be found.</p>
        </div>
      }
      error={error ? (error as Error).message : null}
      isLoading={isLoading}
      loadingComponent={
        <div className="space-y-6">
          <PageHeader icon={Briefcase} title="Loading Delegation..." />
          <SkeletonLoader count={1} variant="card" />
          <div className="grid items-start gap-6 md:grid-cols-3">
            <SkeletonLoader
              className="md:col-span-2"
              count={1}
              variant="card"
            />
            <SkeletonLoader count={1} variant="card" />
          </div>
        </div>
      }
      onRetry={refetch}
    >
      {(loadedDelegation: DomainDelegation) => (
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
          {/* Breadcrumb Container */}
          <div className="container mx-auto px-4 pt-6">
            <AppBreadcrumb />
          </div>

          {/* Modern Header */}
          <DelegationDetailHeader
            delegation={loadedDelegation}
            onDelete={handleDeleteDelegation}
          />

          {/* Main Content */}
          <div className="container mx-auto px-4 py-8">
            <div className="grid gap-8 lg:grid-cols-4">
              {/* Main Content Area */}
              <div className="lg:col-span-3">
                <DelegationTabs
                  delegation={loadedDelegation}
                  onStatusUpdate={handleStatusUpdateConfirm}
                />
              </div>

              {/* Sidebar */}
              <div className="lg:col-span-1">
                <DelegationSidebar delegation={loadedDelegation} />
              </div>
            </div>
          </div>
        </div>
      )}
    </DataLoader>
  );
}
