// use server'
'use server';

/**
 * @fileOverview Suggests a maintenance schedule based on vehicle history and mileage.
 *
 * - suggestMaintenanceSchedule - A function that suggests a maintenance schedule.
 * - SuggestMaintenanceScheduleInput - The input type for the suggestMaintenanceSchedule function.
 * - SuggestMaintenanceScheduleOutput - The return type for the suggestMaintenanceSchedule function.
 */

import { z } from 'genkit';

import { ai } from '@/ai/genkit';

const SuggestMaintenanceScheduleInputSchema = z.object({
  currentOdometer: z
    .number()
    .describe('The current odometer reading of the vehicle in miles.'),
  serviceHistory: z
    .string()
    .describe(
      'A detailed service history of the vehicle, including dates, mileage at service, and services performed.'
    ),
  vehicleMake: z.string().describe('The make of the vehicle.'),
  vehicleModel: z.string().describe('The model of the vehicle.'),
  vehicleYear: z.number().describe('The year of the vehicle.'),
});
export type SuggestMaintenanceScheduleInput = z.infer<
  typeof SuggestMaintenanceScheduleInputSchema
>;

const SuggestMaintenanceScheduleOutputSchema = z.object({
  reasoning: z
    .string()
    .describe(
      'Explanation of the factors for determining the suggested maintenance schedule.'
    ),
  suggestedMaintenanceSchedule: z
    .string()
    .describe('The suggested maintenance schedule for the vehicle.'),
});
export type SuggestMaintenanceScheduleOutput = z.infer<
  typeof SuggestMaintenanceScheduleOutputSchema
>;

export async function suggestMaintenanceSchedule(
  input: SuggestMaintenanceScheduleInput
): Promise<SuggestMaintenanceScheduleOutput> {
  return suggestMaintenanceScheduleFlow(input);
}

const prompt = ai.definePrompt({
  input: { schema: SuggestMaintenanceScheduleInputSchema },
  name: 'suggestMaintenanceSchedulePrompt',
  output: { schema: SuggestMaintenanceScheduleOutputSchema },
  prompt: `You are an expert mechanic who is giving maintenance advice to a vehicle owner. Based on the
following vehicle information and service history, suggest a maintenance schedule. Be sure to consider
common maintenance items, mileage, and time-based recommendations.

Vehicle Make: {{{vehicleMake}}}
Vehicle Model: {{{vehicleModel}}}
Vehicle Year: {{{vehicleYear}}}
Current Odometer: {{{currentOdometer}}} miles
Service History: {{{serviceHistory}}}

Provide a detailed maintenance schedule, including mileage or time intervals for each service. Explain the reasoning behind the suggested schedule.
`, // added explanation
});

const suggestMaintenanceScheduleFlow = ai.defineFlow(
  {
    inputSchema: SuggestMaintenanceScheduleInputSchema,
    name: 'suggestMaintenanceScheduleFlow',
    outputSchema: SuggestMaintenanceScheduleOutputSchema,
  },
  async input => {
    const { output } = await prompt(input);
    return output!;
  }
);
