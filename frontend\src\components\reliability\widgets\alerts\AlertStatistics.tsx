/**
 * @file Alert statistics widget component.
 * This component displays alert trends and statistical analysis.
 * @module components/reliability/widgets/alerts/AlertStatistics
 */

'use client';

import { BarChart3, TrendingDown, TrendingUp, Clock } from 'lucide-react';
import React from 'react';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { useAlertStatistics } from '@/lib/stores/queries/useReliability';
import { cn } from '@/lib/utils';

/**
 * Props for the AlertStatistics component
 */
export interface AlertStatisticsProps {
  /** Optional CSS class name for styling */
  className?: string;
}

/**
 * Alert statistics widget component.
 * 
 * This component provides:
 * - Alert count statistics by severity
 * - Resolution time metrics
 * - Trend analysis over time periods
 * - Visual progress indicators
 * 
 * Features:
 * - Real-time statistics updates
 * - Severity breakdown visualization
 * - Trend indicators
 * - Responsive design
 * 
 * @param props - Component props
 * @returns JSX element representing the alert statistics widget
 */
export const AlertStatistics: React.FC<AlertStatisticsProps> = ({
  className = '',
}) => {
  const { data: statistics, isLoading, error } = useAlertStatistics();

  /**
   * Format duration in human-readable format
   */
  const formatDuration = (minutes: number) => {
    if (minutes < 60) return `${Math.round(minutes)}m`;
    if (minutes < 1440) return `${Math.round(minutes / 60)}h`;
    return `${Math.round(minutes / 1440)}d`;
  };

  /**
   * Get severity color
   */
  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'bg-red-500';
      case 'high': return 'bg-orange-500';
      case 'medium': return 'bg-yellow-500';
      case 'low': return 'bg-blue-500';
      default: return 'bg-gray-500';
    }
  };

  // Handle loading state
  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Alert Statistics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="h-4 bg-muted rounded mb-2"></div>
                <div className="h-2 bg-muted rounded"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  // Handle error state
  if (error || !statistics) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Alert Statistics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-4">
            <BarChart3 className="mx-auto h-8 w-8 text-muted-foreground mb-2" />
            <p className="text-sm text-muted-foreground">
              Failed to load statistics
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const { total, active, resolved, bySeverity, averageResolutionTime, recentTrends } = statistics;
  const resolutionRate = total > 0 ? Math.round((resolved / total) * 100) : 0;

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <BarChart3 className="h-5 w-5" />
          Alert Statistics
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Overview Stats */}
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold">{total}</div>
              <p className="text-xs text-muted-foreground">Total Alerts</p>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{resolved}</div>
              <p className="text-xs text-muted-foreground">Resolved</p>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">{active}</div>
              <p className="text-xs text-muted-foreground">Active</p>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">{resolutionRate}%</div>
              <p className="text-xs text-muted-foreground">Resolution Rate</p>
            </div>
          </div>

          {/* Resolution Rate Progress */}
          <div>
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm font-medium">Resolution Rate</span>
              <span className="text-sm text-muted-foreground">{resolutionRate}%</span>
            </div>
            <Progress value={resolutionRate} className="h-2" />
          </div>

          {/* Severity Breakdown */}
          <div>
            <h4 className="text-sm font-medium mb-3">By Severity</h4>
            <div className="space-y-2">
              {Object.entries(bySeverity).map(([severity, count]) => {
                const percentage = total > 0 ? Math.round((count / total) * 100) : 0;
                return (
                  <div key={severity} className="flex items-center gap-3">
                    <div className={cn('w-3 h-3 rounded-full', getSeverityColor(severity))} />
                    <div className="flex-1">
                      <div className="flex justify-between items-center">
                        <span className="text-sm capitalize">{severity}</span>
                        <span className="text-sm text-muted-foreground">
                          {count} ({percentage}%)
                        </span>
                      </div>
                      <Progress value={percentage} className="h-1 mt-1" />
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Average Resolution Time */}
          <div className="flex items-center gap-3 p-3 rounded-lg bg-muted/50">
            <Clock className="h-5 w-5 text-blue-600" />
            <div>
              <p className="font-medium">Avg. Resolution Time</p>
              <p className="text-sm text-muted-foreground">
                {formatDuration(averageResolutionTime)}
              </p>
            </div>
          </div>

          {/* Recent Trends */}
          <div>
            <h4 className="text-sm font-medium mb-3">Recent Trends</h4>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm">Last 24 Hours</span>
                <div className="flex items-center gap-1">
                  <span className="text-sm font-medium">{recentTrends.last24Hours}</span>
                  {recentTrends.last24Hours > recentTrends.last7Days / 7 ? (
                    <TrendingUp className="h-3 w-3 text-red-500" />
                  ) : (
                    <TrendingDown className="h-3 w-3 text-green-500" />
                  )}
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Last 7 Days</span>
                <div className="flex items-center gap-1">
                  <span className="text-sm font-medium">{recentTrends.last7Days}</span>
                  {recentTrends.last7Days > recentTrends.last30Days / 4 ? (
                    <TrendingUp className="h-3 w-3 text-red-500" />
                  ) : (
                    <TrendingDown className="h-3 w-3 text-green-500" />
                  )}
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Last 30 Days</span>
                <span className="text-sm font-medium">{recentTrends.last30Days}</span>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

/**
 * Default export for the AlertStatistics component
 */
export default AlertStatistics;
