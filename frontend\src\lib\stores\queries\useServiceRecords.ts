import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

import type { EnrichedServiceRecord } from '@/lib/types/domain';
import type { ServiceRecord as DomainServiceRecord } from '@/lib/types/domain';
import type { ServiceRecordApiResponse } from '@/lib/types/apiContracts';

import { ApiClient } from '@/lib/api/core/apiClient';
import {
  BaseApiService,
  type DataTransformer,
  type ServiceConfig,
} from '@/lib/api/core/baseApiService';
import { useCrudQuery } from '@/hooks/api/useSmartQuery';

export interface CreateServiceRecordPayload {
  cost?: number | undefined; // Allow undefined explicitly due to exactOptionalPropertyTypes
  date: string;
  employeeId?: null | number;
  notes?: string;
  odometer: number;
  servicePerformed: string[];
  vehicleId: number;
}

const ServiceRecordTransformer: DataTransformer<DomainServiceRecord> = {
  fromApi(apiData: any): DomainServiceRecord {
    // Handle both regular service records and enriched service records
    const baseRecord = {
      cost: apiData.cost,
      createdAt: apiData.createdAt,
      date: apiData.date,
      employeeId: apiData.employeeId,
      id: apiData.id,
      notes: apiData.notes,
      odometer: apiData.odometer,
      servicePerformed: Array.isArray(apiData.servicePerformed)
        ? apiData.servicePerformed
        : [],
      updatedAt: apiData.updatedAt,
      vehicleId: apiData.vehicleId,
    };

    // Add enriched fields if available (for EnrichedServiceRecord)
    if (apiData.vehicleMake || apiData.vehicleModel || apiData.vehicleYear) {
      return {
        ...baseRecord,
        vehicleMake: apiData.vehicleMake || 'Unknown',
        vehicleModel: apiData.vehicleModel || 'Unknown',
        vehicleYear: apiData.vehicleYear || new Date().getFullYear(),
        licensePlate: apiData.licensePlate || null,
        employeeName: apiData.employeeName || null,
      } as any; // Cast to handle both ServiceRecord and EnrichedServiceRecord
    }

    return baseRecord;
  },
  toApi: (data: any) => data,
};

class ServiceRecordApiService extends BaseApiService<
  DomainServiceRecord,
  CreateServiceRecordPayload,
  Partial<CreateServiceRecordPayload>
> {
  protected endpoint = '/servicerecords';
  protected transformer: DataTransformer<DomainServiceRecord> =
    ServiceRecordTransformer;

  constructor(apiClient: ApiClient, config?: ServiceConfig) {
    super(apiClient, {
      cacheDuration: 5 * 60 * 1000, // 5 minutes for service records
      retryAttempts: 3,
      circuitBreakerThreshold: 5,
      enableMetrics: true,
      ...config,
    });
  }

  async getById(id: string): Promise<EnrichedServiceRecord> {
    return this.executeWithInfrastructure(
      `${this.endpoint}:${id}`,
      async () => {
        const response = await this.apiClient.get<EnrichedServiceRecord>(
          `${this.endpoint}/${id}`
        );

        // Apply transformer if available
        const transformedResponse = this.transformer.fromApi
          ? this.transformer.fromApi(response)
          : response;

        return transformedResponse as EnrichedServiceRecord;
      }
    );
  }

  // Custom update method for service records, handling vehicleId in path
  async updateRecord(
    id: string,
    vehicleId: number, // Keep vehicleId as a separate parameter for clarity in this specific method
    data: Partial<CreateServiceRecordPayload>
  ): Promise<DomainServiceRecord> {
    return this.executeWithInfrastructure(null, async () => {
      // Remove vehicleId from data if present to avoid conflicts, use the parameter instead
      const { vehicleId: _, ...updateData } = data;
      const response = await this.apiClient.put<any>(
        `/vehicles/${vehicleId}/servicerecords/${id}`,
        updateData // Send only the actual update data
      );
      this.cache.invalidatePattern(new RegExp(`^${this.endpoint}:`)); // Invalidate all service records
      this.cache.invalidatePattern(new RegExp(`^${this.endpoint}:${id}`)); // Invalidate specific record
      this.cache.invalidatePattern(new RegExp(`^vehicles:${vehicleId}:`)); // Invalidate vehicle-specific caches
      return this.transformer.fromApi
        ? this.transformer.fromApi(response)
        : response;
    });
  }

  // Custom delete method for service records, handling vehicleId in path
  async deleteRecord(id: string, vehicleId: number): Promise<void> {
    return this.executeWithInfrastructure(null, async () => {
      await this.apiClient.delete(
        `/vehicles/${vehicleId}/servicerecords/${id}`
      );
      this.cache.invalidatePattern(new RegExp(`^${this.endpoint}:`)); // Invalidate all service records
      this.cache.invalidatePattern(new RegExp(`^${this.endpoint}:${id}`)); // Invalidate specific record
      this.cache.invalidatePattern(new RegExp(`^vehicles:${vehicleId}:`)); // Invalidate vehicle-specific caches
    });
  }

  async getVehicleServiceRecords(
    vehicleId: number
  ): Promise<EnrichedServiceRecord[]> {
    return this.executeWithInfrastructure(
      `vehicles:${vehicleId}:servicerecords`,
      async () => {
        const response = await this.apiClient.get<EnrichedServiceRecord[]>(
          `/vehicles/${vehicleId}/servicerecords`
        );
        return response;
      }
    );
  }

  async createVehicleServiceRecord(
    vehicleId: number,
    data: CreateServiceRecordPayload
  ): Promise<DomainServiceRecord> {
    return this.executeWithInfrastructure(null, async () => {
      const response = await this.apiClient.post<any>(
        `/vehicles/${vehicleId}/servicerecords`,
        data
      );

      this.cache.invalidatePattern(new RegExp(`^${this.endpoint}:`));
      this.cache.invalidatePattern(new RegExp(`^vehicles:${vehicleId}:`));

      return this.transformer.fromApi
        ? this.transformer.fromApi(response)
        : response;
    });
  }

  // Method to fetch enriched service records
  async getAllEnriched(): Promise<EnrichedServiceRecord[]> {
    return this.executeWithInfrastructure(
      `${this.endpoint}:enriched`,
      async () => {
        const response = await this.apiClient.get<EnrichedServiceRecord[]>(
          `${this.endpoint}/enriched`
        );

        // Apply transformer to each record
        return response.map(record => {
          const transformed = this.transformer.fromApi
            ? this.transformer.fromApi(record)
            : record;
          return transformed as EnrichedServiceRecord;
        });
      }
    );
  }
}

import { apiClient } from '../../api';
const serviceRecordApiService = new ServiceRecordApiService(apiClient);

export const SERVICE_RECORD_QUERY_KEY = 'serviceRecords';

export const useServiceRecord = (
  id: string,
  options?: { enabled?: boolean }
) => {
  return useCrudQuery<EnrichedServiceRecord, Error>(
    [SERVICE_RECORD_QUERY_KEY, id],
    () => serviceRecordApiService.getById(id),
    'serviceRecord',
    {
      enabled: options?.enabled ?? !!id, // Only enable if ID is present
      staleTime: 1000 * 60 * 5,
    }
  );
};

export const useEnrichedServiceRecords = (options?: { enabled?: boolean }) => {
  return useCrudQuery<EnrichedServiceRecord[], Error>(
    [SERVICE_RECORD_QUERY_KEY, 'allEnriched'],
    () => serviceRecordApiService.getAllEnriched(),
    'serviceRecord',
    {
      enabled: options?.enabled ?? true,
      staleTime: 1000 * 60 * 5,
    }
  );
};

export const useVehicleServiceRecords = (
  vehicleId: number,
  options?: { enabled?: boolean }
) => {
  return useCrudQuery<EnrichedServiceRecord[], Error>(
    [SERVICE_RECORD_QUERY_KEY, 'forVehicle', vehicleId],
    () => serviceRecordApiService.getVehicleServiceRecords(vehicleId),
    'serviceRecord',
    {
      enabled: options?.enabled ?? true,
      staleTime: 1000 * 60 * 5,
    }
  );
};

export const useCreateServiceRecord = () => {
  const queryClient = useQueryClient();
  return useMutation<DomainServiceRecord, Error, CreateServiceRecordPayload>({
    mutationFn: async (
      newRecordData: CreateServiceRecordPayload
    ): Promise<DomainServiceRecord> => {
      const { vehicleId } = newRecordData;
      return serviceRecordApiService.createVehicleServiceRecord(
        vehicleId,
        newRecordData
      );
    },
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({
        queryKey: [SERVICE_RECORD_QUERY_KEY, 'allEnriched'],
      });
      queryClient.invalidateQueries({
        queryKey: [SERVICE_RECORD_QUERY_KEY, 'forVehicle', variables.vehicleId],
      });
      queryClient.invalidateQueries({
        queryKey: ['vehicle', variables.vehicleId],
      });
    },
  });
};

export const useUpdateServiceRecord = () => {
  const queryClient = useQueryClient();
  return useMutation<
    DomainServiceRecord,
    Error,
    { id: string; vehicleId: number; data: Partial<CreateServiceRecordPayload> }
  >({
    mutationFn: async ({
      id,
      vehicleId,
      data,
    }): Promise<DomainServiceRecord> => {
      // Call the custom updateRecord method
      return serviceRecordApiService.updateRecord(id, vehicleId, data);
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: [SERVICE_RECORD_QUERY_KEY, 'allEnriched'],
      });
      queryClient.invalidateQueries({
        queryKey: [SERVICE_RECORD_QUERY_KEY, variables.id],
      }); // Invalidate specific record
      // Use variables.vehicleId directly as it's now part of the mutation variables
      queryClient.invalidateQueries({
        queryKey: [SERVICE_RECORD_QUERY_KEY, 'forVehicle', variables.vehicleId],
      });
      queryClient.invalidateQueries({
        queryKey: ['vehicle', variables.vehicleId],
      });
    },
  });
};

export const useDeleteServiceRecord = () => {
  const queryClient = useQueryClient();
  return useMutation<void, Error, { id: string; vehicleId: number }>({
    mutationFn: async ({ id, vehicleId }): Promise<void> => {
      // Call the custom deleteRecord method
      return serviceRecordApiService.deleteRecord(id, vehicleId);
    },
    onSuccess: (_, variables) => {
      // Use variables instead of id for consistency
      queryClient.invalidateQueries({
        queryKey: [SERVICE_RECORD_QUERY_KEY, 'allEnriched'],
      });
      queryClient.invalidateQueries({
        queryKey: [SERVICE_RECORD_QUERY_KEY, variables.id],
      });
      // Invalidate all vehicle-specific caches since we don't know which vehicle this record belonged to
      queryClient.invalidateQueries({
        queryKey: [SERVICE_RECORD_QUERY_KEY, 'forVehicle'],
      });
      queryClient.invalidateQueries({
        queryKey: ['vehicle'],
      });
    },
  });
};
