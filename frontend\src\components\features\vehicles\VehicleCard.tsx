'use client';

import {
  <PERSON>R<PERSON>,
  CalendarDays,
  Gauge,
  Info,
  Palette,
  Tag,
  Wrench,
} from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';

import type { Vehicle } from '@/lib/types/domain';

import { ActionButton } from '@/components/ui/action-button';
import { Badge } from '@/components/ui/badge';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { getSafeVehicleImageUrl } from '@/lib/utils/imageUtils';

interface InfoPillProps {
  icon: React.ElementType;
  label: string;
  value: number | string;
}

interface VehicleCardProps {
  vehicle: Vehicle;
}

export default function VehicleCard({ vehicle }: VehicleCardProps) {
  // Calculate latest odometer, keeping track of whether we have real data
  const hasServiceHistory = (vehicle.serviceHistory?.length || 0) > 0;
  const hasInitialOdometer = vehicle.initialOdometer !== null;

  const latestOdometer = hasServiceHistory
    ? Math.max(
        vehicle.initialOdometer || 0,
        ...(vehicle.serviceHistory || []).map(s => s.odometer)
      )
    : vehicle.initialOdometer || 0;

  const formatOdometerDisplay = (): string => {
    if (hasServiceHistory) {
      // If we have service history, we can always show the latest reading
      return `${latestOdometer.toLocaleString()} miles`;
    } else if (hasInitialOdometer) {
      // Only initial odometer, show it
      return `${latestOdometer.toLocaleString()} miles`;
    } else {
      // No odometer data at all
      return 'Not recorded';
    }
  };

  return (
    <Card className="flex h-full flex-col overflow-hidden border-border/60 bg-card shadow-md">
      <CardHeader className="relative p-0">
        <div className="relative aspect-[16/10] w-full">
          <Image
            alt={`${vehicle.make} ${vehicle.model}`}
            className="bg-muted object-cover"
            data-ai-hint="luxury car"
            fill
            priority={true}
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            src={getSafeVehicleImageUrl(vehicle.imageUrl, vehicle.id)}
          />
        </div>
      </CardHeader>
      <CardContent className="flex grow flex-col p-5">
        <CardTitle className="mb-1 text-xl font-semibold text-primary">
          {vehicle.make} {vehicle.model}
        </CardTitle>
        <CardDescription className="mb-3 text-sm text-muted-foreground">
          {vehicle.year} {vehicle.color && `• ${vehicle.color}`}{' '}
          {vehicle.licensePlate && `• Plate: ${vehicle.licensePlate}`}
        </CardDescription>
        <Separator className="my-3 bg-border/50" />
        <div className="grow space-y-2.5 text-sm text-foreground">
          <div className="flex items-center">
            <Gauge className="mr-2.5 size-4 shrink-0 text-accent" />
            <div>
              <span className="text-muted-foreground">Latest Odometer: </span>
              <strong className="font-semibold">
                {formatOdometerDisplay()}
              </strong>
            </div>
          </div>
          <div className="flex items-center">
            <Wrench className="mr-2.5 size-4 shrink-0 text-accent" />
            <div>
              <span className="text-muted-foreground">Services Logged: </span>
              <strong className="font-semibold">
                {vehicle.serviceHistory?.length || 0}
              </strong>
            </div>
          </div>
        </div>
      </CardContent>
      <CardFooter className="border-t border-border/60 bg-muted/20 p-4">
        <ActionButton
          actionType="tertiary"
          asChild
          className="w-full"
          icon={<ArrowRight className="size-4" />}
        >
          <Link href={`/vehicles/${vehicle.id}`}>Manage Vehicle</Link>
        </ActionButton>
      </CardFooter>
    </Card>
  );
}

function InfoPill({ icon: Icon, label, value }: InfoPillProps) {
  return (
    <div className="flex items-center space-x-2 rounded-full bg-muted/50 px-3 py-1 text-xs text-foreground">
      <Icon className="size-3.5 text-accent" />
      <span>
        {label}: <strong>{value}</strong>
      </span>
    </div>
  );
}
