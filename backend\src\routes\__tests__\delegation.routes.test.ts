import { jest } from '@jest/globals';
import request from 'supertest';
import { initializeAndGetApp } from '../../app.js';
import { afterAll, beforeAll, beforeEach, describe, expect, it } from '@jest/globals';
import prisma from '../../utils/prisma.js';

let app;
let createdDelegationId;

// Mock middleware
jest.mock('../../middleware/jwtAuth.middleware.js', () => ({
  enhancedAuthenticateUser: (req, res, next) => {
    req.user = { id: 'mock-user-id', role: 'ADMIN' }; // Mock user
    next();
  },
}));

jest.mock('../../middleware/supabaseAuth.js', () => ({
  requireRole: roles => (req, res, next) => {
    if (roles.includes(req.user.role)) {
      return next();
    }
    return res.status(403).json({ message: 'Forbidden' });
  },
}));

beforeAll(async () => {
  app = await initializeAndGetApp();
});

afterAll(async () => {
  await prisma.$disconnect();
});

beforeEach(async () => {
  await prisma.delegation.deleteMany({});
  await prisma.employee.deleteMany({});
  await prisma.vehicle.deleteMany({});
});

describe('Delegation API Routes', () => {
  let employee;
  let vehicle;

  beforeEach(async () => {
    employee = await prisma.employee.create({
      data: {
        name: 'Test Employee for Delegation',
        role: 'driver',
        employeeId: `EMP-DEL-${Date.now()}`,
        contactInfo: '<EMAIL>',
        status: 'Active',
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    });

    vehicle = await prisma.vehicle.create({
      data: {
        make: 'TestMake',
        model: 'TestModel',
        year: 2023,
        vin: `DEL-VIN-${Date.now()}`,
        licensePlate: 'DEL-LP',
        ownerName: 'Test Owner',
        ownerContact: '<EMAIL>',
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    });
  });

  const newDelegationData = () => ({
    employeeId: employee.id,
    vehicleId: vehicle.id,
    startDate: new Date().toISOString(),
    endDate: new Date(new Date().setDate(new Date().getDate() + 5)).toISOString(),
    status: 'SCHEDULED',
  });

  it('should create a new delegation', async () => {
    const response = await request(app).post('/api/delegations').send(newDelegationData());

    expect(response.statusCode).toBe(201);
    expect(response.body.status).toBe('success');
    expect(response.body.data).toHaveProperty('id');
    expect(response.body.data.employeeId).toBe(employee.id);
    createdDelegationId = response.body.data.id;
  });

  it('should get all delegations', async () => {
    await request(app).post('/api/delegations').send(newDelegationData());
    const response = await request(app).get('/api/delegations');

    expect(response.statusCode).toBe(200);
    expect(response.body.status).toBe('success');
    expect(Array.isArray(response.body.data)).toBe(true);
    expect(response.body.data.length).toBe(1);
  });

  it('should get a delegation by ID', async () => {
    const creationResponse = await request(app).post('/api/delegations').send(newDelegationData());
    const delegationId = creationResponse.body.data.id;
    const response = await request(app).get(`/api/delegations/${delegationId}`);

    expect(response.statusCode).toBe(200);
    expect(response.body.status).toBe('success');
    expect(response.body.data.id).toBe(delegationId);
  });

  it('should update a delegation', async () => {
    const creationResponse = await request(app).post('/api/delegations').send(newDelegationData());
    const delegationId = creationResponse.body.data.id;
    const updates = { status: 'ACTIVE' };
    const response = await request(app).put(`/api/delegations/${delegationId}`).send(updates);

    expect(response.statusCode).toBe(200);
    expect(response.body.status).toBe('success');
    expect(response.body.data.status).toBe('ACTIVE');
  });

  it('should delete a delegation', async () => {
    const creationResponse = await request(app).post('/api/delegations').send(newDelegationData());
    const delegationId = creationResponse.body.data.id;
    const response = await request(app).delete(`/api/delegations/${delegationId}`);

    expect(response.statusCode).toBe(200);
    expect(response.body.status).toBe('success');
    expect(response.body.data.message).toBe('Delegation deleted successfully');
  });

  it('should return 404 for a non-existent delegation ID', async () => {
    const response = await request(app).get('/api/delegations/non-existent-id');
    expect(response.statusCode).toBe(404);
    expect(response.body.status).toBe('error');
  });
});
