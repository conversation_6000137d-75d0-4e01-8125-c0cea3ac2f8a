/**
 * Circuit Breaker Pattern Implementation
 *
 * Prevents cascading failures by stopping requests to failing services
 * and allowing them time to recover.
 */

enum CircuitState {
  CLOSED = 'CLOSED', // Normal operation
  HALF_OPEN = 'HALF_OPEN', // Testing if service has recovered
  OPEN = 'OPEN', // Failing, reject all requests
}

interface CircuitBreakerOptions {
  /** Number of failures before opening the circuit */
  failureThreshold: number;
  /** Function to determine if an error should count as a failure */
  isFailure?: (error: any) => boolean;
  /** Time in milliseconds to wait before attempting to close the circuit */
  recoveryTimeout: number;
  /** Time in milliseconds to consider a request as timeout */
  requestTimeout: number;
}

class CircuitBreaker {
  private failureCount = 0;
  private lastFailureTime = 0;
  private readonly options: Required<CircuitBreakerOptions>;
  private state: CircuitState = CircuitState.CLOSED;
  private successCount = 0;

  constructor(options: Partial<CircuitBreakerOptions> = {}) {
    this.options = {
      failureThreshold: 5,
      isFailure: (error: any) => {
        // Consider 429 (rate limit) and 5xx errors as failures
        return (
          error?.status === 429 || (error?.status >= 500 && error?.status < 600)
        );
      },
      recoveryTimeout: 60_000, // 1 minute
      requestTimeout: 10_000, // 10 seconds
      ...options,
    };
  }

  /**
   * Execute a function with circuit breaker protection
   */
  async execute<T>(fn: () => Promise<T>): Promise<T> {
    if (this.state === CircuitState.OPEN) {
      if (this.shouldAttemptReset()) {
        this.state = CircuitState.HALF_OPEN;
        this.successCount = 0;
        console.log('🔄 Circuit Breaker: Attempting to reset (HALF_OPEN)');
      } else {
        const timeUntilRetry = Math.round(
          (this.lastFailureTime + this.options.recoveryTimeout - Date.now()) /
            1000
        );
        throw new Error(
          `Circuit breaker is OPEN. Retry in ${timeUntilRetry} seconds.`
        );
      }
    }

    try {
      const result = await this.executeWithTimeout(fn);
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure(error);
      throw error;
    }
  }

  /**
   * Get current circuit breaker status
   */
  getStatus(): {
    failureCount: number;
    lastFailureTime: number;
    state: CircuitState;
    timeUntilRetry?: number;
  } {
    const status = {
      failureCount: this.failureCount,
      lastFailureTime: this.lastFailureTime,
      state: this.state,
    };

    if (this.state === CircuitState.OPEN) {
      const timeUntilRetry = Math.max(
        0,
        this.lastFailureTime + this.options.recoveryTimeout - Date.now()
      );
      return { ...status, timeUntilRetry };
    }

    return status;
  }

  /**
   * Manually reset the circuit breaker
   */
  reset(): void {
    this.state = CircuitState.CLOSED;
    this.failureCount = 0;
    this.lastFailureTime = 0;
    this.successCount = 0;
    console.log('🔄 Circuit Breaker: Manually reset (CLOSED)');
  }

  /**
   * Execute function with timeout
   */
  private async executeWithTimeout<T>(fn: () => Promise<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      const timeoutId = setTimeout(() => {
        reject(new Error('Request timeout'));
      }, this.options.requestTimeout);

      fn()
        .then(resolve)
        .catch(reject)
        .finally(() => clearTimeout(timeoutId));
    });
  }

  /**
   * Handle failed execution
   */
  private onFailure(error: any): void {
    if (!this.options.isFailure(error)) {
      return; // Don't count this as a failure
    }

    this.failureCount++;
    this.lastFailureTime = Date.now();

    if (this.state === CircuitState.HALF_OPEN) {
      this.state = CircuitState.OPEN;
      console.log('🔄 Circuit Breaker: Reset failed, opening circuit (OPEN)');
    } else if (this.failureCount >= this.options.failureThreshold) {
      this.state = CircuitState.OPEN;
      console.log(
        `🔄 Circuit Breaker: Failure threshold reached (${this.failureCount}), opening circuit (OPEN)`
      );
    }
  }

  /**
   * Handle successful execution
   */
  private onSuccess(): void {
    this.failureCount = 0;

    if (this.state === CircuitState.HALF_OPEN) {
      this.successCount++;
      if (this.successCount >= 3) {
        // Require 3 successes to fully close
        this.state = CircuitState.CLOSED;
        console.log('🔄 Circuit Breaker: Reset successful (CLOSED)');
      }
    }
  }

  /**
   * Check if we should attempt to reset the circuit
   */
  private shouldAttemptReset(): boolean {
    return Date.now() - this.lastFailureTime >= this.options.recoveryTimeout;
  }
}

// Create circuit breakers for different admin endpoints
export const adminCircuitBreakers = {
  audit: new CircuitBreaker({
    failureThreshold: 3,
    recoveryTimeout: 60_000, // 1 minute for audit logs
    requestTimeout: 8000, // 8 seconds timeout
  }),

  errors: new CircuitBreaker({
    failureThreshold: 5,
    recoveryTimeout: 30_000, // 30 seconds for error logs
    requestTimeout: 8000, // 8 seconds timeout
  }),

  health: new CircuitBreaker({
    failureThreshold: 3,
    recoveryTimeout: 30_000, // 30 seconds for health checks
    requestTimeout: 5000, // 5 seconds timeout
  }),

  performance: new CircuitBreaker({
    failureThreshold: 3,
    recoveryTimeout: 60_000, // 1 minute for performance metrics
    requestTimeout: 10_000, // 10 seconds timeout
  }),

  users: new CircuitBreaker({
    failureThreshold: 3,
    recoveryTimeout: 60_000, // 1 minute for user management
    requestTimeout: 10_000, // 10 seconds timeout
  }),
};

/**
 * Get status of all circuit breakers
 */
export const getCircuitBreakerStatus = () => {
  return Object.entries(adminCircuitBreakers).reduce(
    (acc, [key, breaker]) => {
      acc[key] = breaker.getStatus();
      return acc;
    },
    {} as Record<
      string,
      ReturnType<typeof adminCircuitBreakers.health.getStatus>
    >
  );
};

/**
 * Reset all circuit breakers
 */
export const resetAllCircuitBreakers = () => {
  for (const breaker of Object.values(adminCircuitBreakers)) breaker.reset();
  console.log('🔄 Circuit Breaker: All breakers reset');
};

export { CircuitBreaker, CircuitState };
export type { CircuitBreakerOptions };
