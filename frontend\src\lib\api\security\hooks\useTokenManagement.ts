/**
 * @file Token Management Hook - Single Responsibility Principle (SRP)
 * @module hooks/useTokenManagement
 *
 * This hook handles ONLY token lifecycle operations following SRP principles.
 * It provides token validation, refresh, and management functionality.
 *
 * SECURITY NOTE: Works with httpOnly cookies and provides token utility functions.
 */

'use client';

import { useCallback, useEffect, useMemo, useState } from 'react';

import type { TokenValidationResult } from '../../../security';

import {
  SECURITY_CONSTANTS,
  SecurityUtils,
  SessionManager,
  TokenManager,
} from '../../../security';
import { getTokenRefreshService } from '../../../services/TokenRefreshService';

export interface TokenManagementActions {
  checkTokenExpiry: () => boolean;
  clearToken: () => void;
  getTokenExpiration: () => Date | null;
  refreshToken: () => Promise<boolean>;
  validateCurrentToken: () => null | TokenValidationResult;
}

export interface TokenManagementState {
  isTokenExpired: boolean;
  isTokenValid: boolean;
  lastValidation: Date | null;
  tokenError: null | string;
  willExpireSoon: boolean;
}

export interface UseTokenManagementReturn
  extends TokenManagementActions,
    TokenManagementState {}

/**
 * Token Management Hook - Single Responsibility: Token Operations Only
 *
 * Manages token lifecycle, validation, and refresh operations.
 * Does NOT handle authentication state or user session management.
 */
export function useTokenManagement(
  currentToken?: null | string
): UseTokenManagementReturn {
  const [state, setState] = useState<TokenManagementState>({
    isTokenExpired: false,
    isTokenValid: false,
    lastValidation: null,
    tokenError: null,
    willExpireSoon: false,
  });

  const tokenRefreshService = useMemo(() => getTokenRefreshService(), []);

  /**
   * Validate current token with circuit breaker coordination
   * Single responsibility: Token validation only
   */
  const validateCurrentToken = useCallback((): null | TokenValidationResult => {
    if (!currentToken) {
      return null;
    }

    // Circuit breaker check for validation
    if (!SecurityUtils.canPerformSecurityCheck()) {
      console.debug('🔒 Token validation blocked by circuit breaker');
      return null;
    }

    const operationId = 'token-validation';
    if (!SecurityUtils.startSecurityOperation(operationId)) {
      console.debug('🔄 Token validation already in progress');
      return null;
    }

    try {
      const validation = TokenManager.validateToken(currentToken);

      if (validation.isValid) {
        SecurityUtils.recordSecuritySuccess();

        // Notify SessionManager of valid session
        SessionManager.handleSessionValidation(true);
      } else {
        console.warn('❌ Token validation failed:', validation.error);
        SecurityUtils.recordSecurityAttempt();

        // Notify SessionManager of invalid session
        SessionManager.handleSessionValidation(false, {
          error: validation.error,
        });
      }

      setState(prev => ({
        ...prev,
        isTokenExpired: validation.isExpired,
        isTokenValid: validation.isValid,
        lastValidation: new Date(),
        tokenError: validation.error || null,
        willExpireSoon: validation.isValid
          ? TokenManager.willExpireSoon(currentToken)
          : false,
      }));

      return validation;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Token validation failed';
      console.error('❌ Token validation error:', errorMessage);
      SecurityUtils.recordSecurityAttempt();

      setState(prev => ({
        ...prev,
        isTokenValid: false,
        tokenError: errorMessage,
        lastValidation: new Date(),
      }));

      return null;
    } finally {
      SecurityUtils.endSecurityOperation(operationId);
    }
  }, [currentToken]);

  /**
   * Refresh token using token refresh service with circuit breaker coordination
   * Single responsibility: Token refresh only
   */
  const refreshToken = useCallback(async (): Promise<boolean> => {
    // Circuit breaker check - prevent verification loops
    if (!SecurityUtils.canPerformSecurityCheck()) {
      console.debug('🔒 Token refresh blocked by circuit breaker');
      return false;
    }

    const operationId = 'token-refresh';
    if (!SecurityUtils.startSecurityOperation(operationId)) {
      console.debug('🔄 Token refresh already in progress');
      return false;
    }

    try {
      setState(prev => ({ ...prev, tokenError: null }));
      console.log('🔄 Starting token refresh...');

      const success = await tokenRefreshService.refreshNow();

      if (success) {
        console.log('✅ Token refresh successful');
        SecurityUtils.recordSecuritySuccess();

        // Notify SessionManager of successful token refresh
        SessionManager.handleTokenRefresh(true);

        setState(prev => ({
          ...prev,
          isTokenValid: true,
          isTokenExpired: false,
          tokenError: null,
          lastValidation: new Date(),
        }));
      } else {
        console.warn('❌ Token refresh failed');
        SecurityUtils.recordSecurityAttempt();

        // Notify SessionManager of failed token refresh
        SessionManager.handleTokenRefresh(false);

        setState(prev => ({
          ...prev,
          isTokenValid: false,
          tokenError: 'Token refresh failed',
        }));
      }

      return success;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Token refresh failed';

      console.error('❌ Token refresh error:', errorMessage);
      SecurityUtils.recordSecurityAttempt();

      // Notify SessionManager of failed token refresh
      SessionManager.handleTokenRefresh(false, { error: errorMessage });

      setState(prev => ({
        ...prev,
        isTokenValid: false,
        tokenError: errorMessage,
      }));

      return false;
    } finally {
      SecurityUtils.endSecurityOperation(operationId);
    }
  }, [tokenRefreshService]);

  /**
   * Clear token state
   * Single responsibility: Token clearing only
   */
  const clearToken = useCallback(() => {
    setState({
      isTokenExpired: false,
      isTokenValid: false,
      lastValidation: null,
      tokenError: null,
      willExpireSoon: false,
    });
  }, []);

  /**
   * Check if token will expire soon
   * Single responsibility: Expiry check only
   */
  const checkTokenExpiry = useCallback((): boolean => {
    if (!currentToken) return true;

    return TokenManager.willExpireSoon(
      currentToken,
      SECURITY_CONSTANTS.TOKEN_EXPIRY_THRESHOLD_MINUTES
    );
  }, [currentToken]);

  /**
   * Get token expiration date
   * Single responsibility: Expiration date retrieval only
   */
  const getTokenExpiration = useCallback((): Date | null => {
    if (!currentToken) return null;

    return TokenManager.getTokenExpiration(currentToken);
  }, [currentToken]);

  // Auto-validate token when it changes
  useEffect(() => {
    if (currentToken) {
      validateCurrentToken();
    } else {
      clearToken();
    }
  }, [currentToken, validateCurrentToken, clearToken]);

  // Auto-refresh token when it's about to expire with circuit breaker coordination
  useEffect(() => {
    if (!currentToken || !state.isTokenValid) return;

    const checkAndRefresh = () => {
      // Circuit breaker check before auto-refresh
      if (!SecurityUtils.canPerformSecurityCheck()) {
        console.debug('🔒 Auto token refresh blocked by circuit breaker');
        return;
      }

      const operationId = 'auto-token-refresh-check';
      if (!SecurityUtils.startSecurityOperation(operationId)) {
        console.debug('🔄 Auto token refresh check already in progress');
        return;
      }

      try {
        if (TokenManager.willExpireSoon(currentToken, 2)) {
          // 2 minutes before expiry
          console.log('⏰ Token will expire soon, triggering auto-refresh');
          refreshToken();
        }
      } catch (error) {
        console.error('❌ Auto token refresh check failed:', error);
        SecurityUtils.recordSecurityAttempt();
      } finally {
        SecurityUtils.endSecurityOperation(operationId);
      }
    };

    // Reduced frequency to prevent excessive checking (every 2 minutes instead of 1)
    const interval = setInterval(checkAndRefresh, 120 * 1000);

    // Initial check with delay to allow other security operations to complete
    setTimeout(checkAndRefresh, 5000);

    return () => clearInterval(interval);
  }, [currentToken, state.isTokenValid, refreshToken]);

  return {
    checkTokenExpiry,
    clearToken,
    getTokenExpiration,
    isTokenExpired: state.isTokenExpired,
    // State
    isTokenValid: state.isTokenValid,

    lastValidation: state.lastValidation,
    refreshToken,
    tokenError: state.tokenError,
    // Actions
    validateCurrentToken,
    willExpireSoon: state.willExpireSoon,
  };
}
