/**
 * @file Response Wrapper Middleware Tests
 * @description Comprehensive test suite for response wrapper middleware
 */

import type { Request, Response } from 'express';
import { createResponseWrapper, responseWrapper, wrapResponse } from '../responseWrapper.js';
import { isStandardApiResponse } from '../../types/response.types.js';

// Mock logger to avoid console output during tests
jest.mock('../../utils/logger.js', () => ({
  debug: jest.fn(),
  error: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
}));

describe('Response Wrapper Middleware', () => {
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let mockNext: jest.Mock;
  let originalJson: jest.Mock;

  beforeEach(() => {
    originalJson = jest.fn();
    mockRequest = {
      path: '/api/vehicles',
      method: 'GET',
      headers: {
        'x-request-id': 'test-request-id',
      },
    };
    mockResponse = {
      json: originalJson,
      statusCode: 200,
    };
    mockNext = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createResponseWrapper', () => {
    it('should create middleware with default config', () => {
      const middleware = createResponseWrapper();
      expect(typeof middleware).toBe('function');
    });

    it('should create middleware with custom config', () => {
      const middleware = createResponseWrapper({
        excludePaths: ['/custom-exclude'],
        includeRequestId: false,
      });
      expect(typeof middleware).toBe('function');
    });
  });

  describe('Response Wrapping', () => {
    it('should wrap successful responses in standard format', () => {
      const middleware = createResponseWrapper();
      const testData = { id: 1, name: 'Test Vehicle' };

      middleware(mockRequest as Request, mockResponse as Response, mockNext);

      // Simulate calling res.json()
      (mockResponse as any).json(testData);

      expect(originalJson).toHaveBeenCalledWith({
        status: 'success',
        data: testData,
        timestamp: expect.any(String),
        requestId: 'test-request-id',
      });
    });

    it('should include request ID when enabled and available', () => {
      const middleware = createResponseWrapper({ includeRequestId: true });
      const testData = { id: 1 };

      middleware(mockRequest as Request, mockResponse as Response, mockNext);
      (mockResponse as any).json(testData);

      expect(originalJson).toHaveBeenCalledWith(
        expect.objectContaining({
          requestId: 'test-request-id',
        }),
      );
    });

    it('should exclude request ID when disabled', () => {
      const middleware = createResponseWrapper({ includeRequestId: false });
      const testData = { id: 1 };

      middleware(mockRequest as Request, mockResponse as Response, mockNext);
      (mockResponse as any).json(testData);

      const calledWith = originalJson.mock.calls[0][0];
      expect(calledWith).not.toHaveProperty('requestId');
    });

    it('should handle missing request ID gracefully', () => {
      mockRequest.headers = {};
      const middleware = createResponseWrapper({ includeRequestId: true });
      const testData = { id: 1 };

      middleware(mockRequest as Request, mockResponse as Response, mockNext);
      (mockResponse as any).json(testData);

      const calledWith = originalJson.mock.calls[0][0];
      expect(calledWith).not.toHaveProperty('requestId');
    });

    it('should include timestamp in wrapped response', () => {
      const middleware = createResponseWrapper();
      const testData = { id: 1 };

      middleware(mockRequest as Request, mockResponse as Response, mockNext);
      (mockResponse as any).json(testData);

      expect(originalJson).toHaveBeenCalledWith(
        expect.objectContaining({
          timestamp: expect.stringMatching(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/),
        }),
      );
    });
  });

  describe('Pagination Handling', () => {
    it('should extract and wrap pagination metadata', () => {
      const middleware = createResponseWrapper();
      const testData = {
        items: [{ id: 1 }, { id: 2 }],
        pagination: {
          page: 1,
          limit: 10,
          total: 25,
        },
      };

      middleware(mockRequest as Request, mockResponse as Response, mockNext);
      (mockResponse as any).json(testData);

      expect(originalJson).toHaveBeenCalledWith({
        status: 'success',
        data: { items: [{ id: 1 }, { id: 2 }] },
        pagination: {
          page: 1,
          limit: 10,
          total: 25,
          totalPages: 3,
          hasNext: true,
          hasPrevious: false,
        },
        timestamp: expect.any(String),
        requestId: 'test-request-id',
      });
    });

    it('should handle pagination with hasNext and hasPrevious correctly', () => {
      const middleware = createResponseWrapper();
      const testData = {
        items: [],
        pagination: {
          page: 2,
          limit: 10,
          total: 25,
        },
      };

      middleware(mockRequest as Request, mockResponse as Response, mockNext);
      (mockResponse as any).json(testData);

      const calledWith = originalJson.mock.calls[0][0];
      expect(calledWith.pagination).toEqual({
        page: 2,
        limit: 10,
        total: 25,
        totalPages: 3,
        hasNext: true,
        hasPrevious: true,
      });
    });

    it('should handle last page pagination correctly', () => {
      const middleware = createResponseWrapper();
      const testData = {
        items: [],
        pagination: {
          page: 3,
          limit: 10,
          total: 25,
        },
      };

      middleware(mockRequest as Request, mockResponse as Response, mockNext);
      (mockResponse as any).json(testData);

      const calledWith = originalJson.mock.calls[0][0];
      expect(calledWith.pagination).toEqual({
        page: 3,
        limit: 10,
        total: 25,
        totalPages: 3,
        hasNext: false,
        hasPrevious: true,
      });
    });
  });

  describe('Path Exclusion', () => {
    it('should skip wrapping for excluded paths', () => {
      const middleware = createResponseWrapper({
        excludePaths: ['/api/health', '/metrics'],
      });

      mockRequest.path = '/api/health';
      middleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockNext).toHaveBeenCalled();
      expect((mockResponse as any).json).toBe(originalJson);
    });

    it('should wrap responses for non-excluded paths', () => {
      const middleware = createResponseWrapper({
        excludePaths: ['/api/health'],
      });

      mockRequest.path = '/api/vehicles';
      middleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockNext).toHaveBeenCalled();
      expect((mockResponse as any).json).not.toBe(originalJson);
    });

    it('should handle prefix-based exclusion', () => {
      const middleware = createResponseWrapper({
        excludePaths: ['/swagger'],
      });

      mockRequest.path = '/swagger/docs';
      middleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockNext).toHaveBeenCalled();
      expect((mockResponse as any).json).toBe(originalJson);
    });
  });

  describe('Already Wrapped Responses', () => {
    it('should skip wrapping for already wrapped responses', () => {
      const middleware = createResponseWrapper();
      const wrappedData = {
        status: 'success',
        data: { id: 1 },
        timestamp: new Date().toISOString(),
      };

      middleware(mockRequest as Request, mockResponse as Response, mockNext);
      (mockResponse as any).json(wrappedData);

      expect(originalJson).toHaveBeenCalledWith(wrappedData);
    });
  });

  describe('Error Response Handling', () => {
    it('should skip wrapping for error status codes', () => {
      const middleware = createResponseWrapper();
      const errorData = { message: 'Not found' };

      mockResponse.statusCode = 404;
      middleware(mockRequest as Request, mockResponse as Response, mockNext);
      (mockResponse as any).json(errorData);

      expect(originalJson).toHaveBeenCalledWith(errorData);
    });

    it('should skip wrapping for 5xx status codes', () => {
      const middleware = createResponseWrapper();
      const errorData = { message: 'Internal server error' };

      mockResponse.statusCode = 500;
      middleware(mockRequest as Request, mockResponse as Response, mockNext);
      (mockResponse as any).json(errorData);

      expect(originalJson).toHaveBeenCalledWith(errorData);
    });
  });

  describe('Error Handling', () => {
    it('should fallback to original response on wrapping error', () => {
      const middleware = createResponseWrapper();
      const testData = { id: 1 };

      // Mock a scenario that could cause an error
      jest.spyOn(Date.prototype, 'toISOString').mockImplementationOnce(() => {
        throw new Error('Date error');
      });

      middleware(mockRequest as Request, mockResponse as Response, mockNext);
      (mockResponse as any).json(testData);

      expect(originalJson).toHaveBeenCalledWith(testData);
    });
  });

  describe('Type Guards', () => {
    it('should correctly identify standard API responses', () => {
      const standardResponse = {
        status: 'success',
        data: { id: 1 },
        timestamp: new Date().toISOString(),
      };

      expect(isStandardApiResponse(standardResponse)).toBe(true);
    });

    it('should correctly identify non-standard responses', () => {
      expect(isStandardApiResponse({ id: 1 })).toBe(false);
      expect(isStandardApiResponse(null)).toBe(false);
      expect(isStandardApiResponse(undefined)).toBe(false);
      expect(isStandardApiResponse('string')).toBe(false);
    });
  });

  describe('Utility Functions', () => {
    describe('wrapResponse', () => {
      it('should manually wrap response data', () => {
        const data = { id: 1, name: 'Test' };
        const wrapped = wrapResponse(data);

        expect(wrapped).toEqual({
          status: 'success',
          data,
          timestamp: expect.any(String),
        });
      });

      it('should include request ID when provided', () => {
        const data = { id: 1 };
        const requestId = 'test-request-id';
        const wrapped = wrapResponse(data, requestId);

        expect(wrapped).toEqual({
          status: 'success',
          data,
          timestamp: expect.any(String),
          requestId,
        });
      });

      it('should include pagination when provided', () => {
        const data = { items: [] };
        const pagination = {
          page: 1,
          limit: 10,
          total: 0,
          totalPages: 0,
          hasNext: false,
          hasPrevious: false,
        };
        const wrapped = wrapResponse(data, undefined, pagination);

        expect(wrapped).toEqual({
          status: 'success',
          data,
          timestamp: expect.any(String),
          pagination,
        });
      });
    });
  });

  describe('Performance', () => {
    it('should have minimal overhead for response wrapping', () => {
      const middleware = createResponseWrapper();
      const testData = { id: 1 };

      const startTime = process.hrtime.bigint();

      middleware(mockRequest as Request, mockResponse as Response, mockNext);
      (mockResponse as any).json(testData);

      const endTime = process.hrtime.bigint();
      const durationMs = Number(endTime - startTime) / 1_000_000;

      // Should complete within 5ms as specified in requirements
      expect(durationMs).toBeLessThan(5);
    });
  });
});
