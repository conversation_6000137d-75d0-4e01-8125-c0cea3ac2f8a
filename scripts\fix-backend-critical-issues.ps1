# WorkHub Critical Issues Resolution Script v2.0 (PowerShell)
# Fixes: Frontend-Backend communication, Database permissions, Supabase connection, Redis connectivity

Write-Host "🔧 WorkHub Backend Critical Issues Resolution" -ForegroundColor Blue
Write-Host "==============================================" -ForegroundColor Blue

function Write-Status {
    param($Message)
    Write-Host "[INFO] $Message" -ForegroundColor Cyan
}

function Write-Success {
    param($Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param($Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param($Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

# Check if docker-compose is available
if (!(Get-Command docker-compose -ErrorAction SilentlyContinue)) {
    Write-Error "docker-compose is not installed or not in PATH"
    exit 1
}

Write-Status "Step 1: Stopping existing services..."
docker-compose -f docker-compose.staging.yml down --remove-orphans

Write-Status "Step 2: Removing old containers and networks..."
docker container prune -f
docker network prune -f

Write-Status "Step 3: Building updated images with fixes..."
Write-Status "  - Building backend with database connection fixes..."
docker-compose -f docker-compose.staging.yml build backend --no-cache
Write-Status "  - Building frontend with Docker network communication fixes..."
docker-compose -f docker-compose.staging.yml build frontend --no-cache

Write-Status "Step 4: Starting Redis service first..."
docker-compose -f docker-compose.staging.yml up -d redis

Write-Status "Step 5: Waiting for Redis to be healthy..."
$timeout = 60
$counter = 0
while ($counter -lt $timeout) {
    $redisStatus = docker-compose -f docker-compose.staging.yml ps redis
    if ($redisStatus -match "healthy") {
        Write-Success "Redis is healthy"
        break
    }
    
    if ($counter -eq ($timeout - 1)) {
        Write-Error "Redis failed to become healthy within $timeout seconds"
        docker-compose -f docker-compose.staging.yml logs redis
        exit 1
    }
    
    Write-Host "." -NoNewline
    Start-Sleep 1
    $counter++
}

Write-Status "Step 6: Starting backend service..."
docker-compose -f docker-compose.staging.yml up -d backend

Write-Status "Step 7: Waiting for backend to be healthy..."
$timeout = 120
$counter = 0
while ($counter -lt $timeout) {
    $backendStatus = docker-compose -f docker-compose.staging.yml ps backend
    if ($backendStatus -match "healthy") {
        Write-Success "Backend is healthy"
        break
    }
    
    # Check for specific errors in logs
    $backendLogs = docker-compose -f docker-compose.staging.yml logs backend 2>&1
    if ($backendLogs -match "Supabase.*failed") {
        Write-Warning "Supabase connection issues detected - checking logs..."
    }
    
    if ($backendLogs -match "redis.*unavailable") {
        Write-Warning "Redis connection issues detected - checking logs..."
    }
    
    if ($counter -eq ($timeout - 1)) {
        Write-Error "Backend failed to become healthy within $timeout seconds"
        Write-Error "Backend logs:"
        docker-compose -f docker-compose.staging.yml logs backend --tail=50
        exit 1
    }
    
    Write-Host "." -NoNewline
    Start-Sleep 2
    $counter += 2
}

Write-Status "Step 8: Starting frontend service..."
docker-compose -f docker-compose.staging.yml up -d frontend

Write-Status "Step 9: Final health check..."
Start-Sleep 10

# Test backend health endpoint
Write-Status "Testing backend health endpoint..."
try {
    $response = Invoke-WebRequest -Uri "http://localhost:3001/api/health" -UseBasicParsing -TimeoutSec 10
    if ($response.StatusCode -eq 200) {
        Write-Success "Backend health endpoint is responding"
    } else {
        Write-Error "Backend health endpoint returned status: $($response.StatusCode)"
        exit 1
    }
} catch {
    Write-Error "Backend health endpoint is not responding: $($_.Exception.Message)"
    exit 1
}

# Test Redis connectivity
Write-Status "Testing Redis connectivity..."
$redisPing = docker exec workhub-redis-staging redis-cli ping
if ($redisPing -match "PONG") {
    Write-Success "Redis is responding to ping"
} else {
    Write-Error "Redis is not responding to ping"
    exit 1
}

Write-Status "Step 10: Comprehensive issue verification..."

# Check backend logs
$backendLogs = docker-compose -f docker-compose.staging.yml logs backend --since=2m

# Check frontend logs
$frontendLogs = docker-compose -f docker-compose.staging.yml logs frontend --since=2m

Write-Status "Checking resolved issues:"

# Database permission check
if ($backendLogs -notmatch "permission denied for schema") {
    Write-Success "✅ Database permission issues resolved"
} else {
    Write-Warning "⚠️ Database permission issues still present"
}

# Supabase connection check
if ($backendLogs -notmatch "Supabase.*failed") {
    Write-Success "✅ Supabase connection issues resolved"
} else {
    Write-Warning "⚠️ Supabase connection issues still present"
}

# Redis connection check
if ($backendLogs -notmatch "redis.*unavailable") {
    Write-Success "✅ Redis connection issues resolved"
} else {
    Write-Warning "⚠️ Redis connection issues still present"
}

# Frontend-Backend communication check
if ($frontendLogs -notmatch "ECONNREFUSED.*3001") {
    Write-Success "✅ Frontend-Backend communication issues resolved"
} else {
    Write-Warning "⚠️ Frontend-Backend communication issues still present"
}

# Circuit breaker check
if ($backendLogs -match "Circuit breakers initialized") {
    Write-Success "✅ Circuit breakers initialized successfully"
} else {
    Write-Warning "⚠️ Circuit breaker initialization issues may persist"
}

Write-Host ""
Write-Success "🎉 Backend critical issues resolution completed!"
Write-Host ""
Write-Status "Service Status:"
docker-compose -f docker-compose.staging.yml ps

Write-Host ""
Write-Status "To monitor logs in real-time, run:"
Write-Host "docker-compose -f docker-compose.staging.yml logs -f backend"

Write-Host ""
Write-Status "To test the health endpoint:"
Write-Host "curl http://localhost:3001/api/health"
