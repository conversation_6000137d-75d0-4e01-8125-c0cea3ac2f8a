'use client';

import { History, PlusCircle, RefreshCw } from 'lucide-react';
import React from 'react';

import type { EnrichedServiceRecord } from '@/lib/types/domain';

import { ServiceHistorySummary } from '@/components/reports/ServiceHistorySummary';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { ErrorHandler } from '@/components/ui/error-handler';
import { EmptyState, SkeletonLoader } from '@/components/ui/loading';
import { useToast } from '@/hooks/utils/use-toast';
import { useDeleteServiceRecord } from '@/lib/stores/queries/useServiceRecords';

import { ServiceHistoryTable } from './ServiceHistoryTable';

/**
 * Props for the EnhancedServiceHistoryContainer component
 */
interface EnhancedServiceHistoryContainerProps {
  /** Additional CSS class names */
  className?: string;
  /** Error message, if any */
  error: null | string;
  /** Whether data is currently loading */
  isLoading: boolean;
  /** Function to retry loading data */
  onRetry: () => void;
  /** Array of service records to display */
  records: EnrichedServiceRecord[];
  /** Whether to show vehicle information column */
  showVehicleInfo?: boolean;
  /** Whether this is a vehicle-specific view */
  vehicleSpecific?: boolean;
}

/**
 * A container component that handles sorting, pagination, and display of service history records
 *
 * @example
 * ```tsx
 * <EnhancedServiceHistoryContainer
 *   records={filteredRecords}
 *   isLoading={isLoading}
 *   error={error}
 *   onRetry={handleRetry}
 *   showVehicleInfo={true}
 *   vehicleSpecific={false}
 * />
 * ```
 */
export function EnhancedServiceHistoryContainer({
  className,
  error,
  isLoading,
  onRetry,
  records,
  showVehicleInfo = true,
  vehicleSpecific = false,
}: EnhancedServiceHistoryContainerProps) {
  const { toast } = useToast();
  const deleteMutation = useDeleteServiceRecord();

  // Handle single record deletion
  const handleDelete = async (record: EnrichedServiceRecord) => {
    try {
      // Pass the required object with id and vehicleId
      await deleteMutation.mutateAsync({
        id: record.id,
        vehicleId: record.vehicleId,
      });

      toast({
        title: 'Deleted!',
        description: 'Service record deleted successfully.',
        variant: 'default',
      });

      // Trigger a refetch of the data
      onRetry();
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to delete service record. Please try again.',
        variant: 'destructive',
      });
    }
  };

  // Handle bulk deletion
  const handleBulkDelete = async (selectedRecords: EnrichedServiceRecord[]) => {
    try {
      // Delete all selected records
      await Promise.all(
        selectedRecords.map(record =>
          // Pass the required object with id and vehicleId for each record
          deleteMutation.mutateAsync({
            id: record.id,
            vehicleId: record.vehicleId,
          })
        )
      );

      toast({
        title: 'Deleted!',
        description: `${selectedRecords.length} service records deleted successfully.`,
        variant: 'default',
      });

      // Trigger a refetch of the data
      onRetry();
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to delete some service records. Please try again.',
        variant: 'destructive',
      });
    }
  };

  // Since DataTable handles sorting and pagination internally,
  // we only need to manage the data display logic here

  // Render loading state
  if (isLoading) {
    return (
      <div className="space-y-4" data-testid="loading-skeleton">
        <SkeletonLoader count={5} variant="table" />
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div className="space-y-4">
        <ErrorHandler context="Loading Service Records" error={error} />
        <div className="flex justify-center">
          <Button
            aria-label="Try loading service records again"
            onClick={onRetry}
            size="sm"
            variant="outline"
          >
            <RefreshCw className="mr-2 size-4" />
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  // Render empty state
  if (records.length === 0) {
    return (
      <EmptyState
        title="No Service Records Found"
        description={
          vehicleSpecific
            ? 'No service records available for this vehicle. You can log a new service record to get started.'
            : 'There are no service records matching your current filters. You can log a new service record or adjust your filters.'
        }
        icon={History}
        primaryAction={{
          label: 'Log New Service',
          href: '/vehicles',
          icon: <PlusCircle className="size-4" />,
        }}
      />
    );
  }

  return (
    <div className="space-y-6">
      {/* Summary Statistics */}
      <ServiceHistorySummary
        records={records}
        vehicleSpecific={vehicleSpecific}
      />

      {/* Service Records Table */}
      <Card className="card-print shadow-md">
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <ServiceHistoryTable
              records={records}
              showVehicleInfo={showVehicleInfo}
              onDelete={handleDelete}
              onBulkDelete={handleBulkDelete}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
