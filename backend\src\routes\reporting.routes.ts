// backend/src/routes/reporting.routes.ts

import { Router } from 'express';
import { enhancedAuthenticateUser } from '../middleware/jwtAuth.middleware.js';
import { requireRole } from '../middleware/supabaseAuth.js';
import { apiRateLimit } from '../middleware/rateLimiting.js';
import { reportingController } from '../controllers/reporting.controller.js';
import logger from '../utils/logger.js';

const router = Router();

// Apply common middleware to all reporting routes
router.use(enhancedAuthenticateUser);
router.use(requireRole(['ADMIN', 'MANAGER', 'OFFICE_STAFF'])); // Allow reporting access to relevant roles
router.use(apiRateLimit);

// Log all reporting route access
router.use((req, res, next) => {
  logger.info('Reporting route accessed', {
    ip: req.ip,
    method: req.method,
    path: req.path,
    service: 'reporting-routes',
    userId: (req as any).user?.id,
  });
  next();
});

/**
 * Delegation Analytics Routes
 */

// GET /api/reporting/delegations/analytics - Get delegation analytics data
router.get('/delegations/analytics', reportingController.getDelegationAnalytics);

/**
 * Task Metrics Routes
 */

// GET /api/reporting/tasks/metrics - Get task metrics data
router.get('/tasks/metrics', reportingController.getTaskMetrics);

// GET /api/reporting/tasks/analytics - Get task analytics data
router.get('/tasks/analytics', reportingController.getTaskAnalytics);

/**
 * Trend Data Routes
 */

// GET /api/reporting/trends - Get trend data
router.get('/trends', reportingController.getTrendData);

/**
 * Location Metrics Routes
 */

// GET /api/reporting/locations/metrics - Get location metrics
router.get('/locations/metrics', reportingController.getLocationMetrics);

/**
 * Service History Routes (Enhanced)
 */

// GET /api/reporting/services/history - Get service history data
router.get('/services/history', reportingController.getServiceHistory);

// GET /api/reporting/services/costs - Get service cost summary
router.get('/services/costs', reportingController.getServiceCostSummary);

/**
 * Vehicle Analytics Routes
 */

// GET /api/reporting/vehicles/analytics - Get vehicle analytics data
router.get('/vehicles/analytics', reportingController.getVehicleAnalytics);

/**
 * Employee Analytics Routes
 */

// GET /api/reporting/employees/analytics - Get employee analytics data
router.get('/employees/analytics', reportingController.getEmployeeAnalytics);

/**
 * Report Types Routes
 */

// GET /api/reporting/report-types - Get all report types
router.get('/report-types', reportingController.getReportTypes);

// POST /api/reporting/report-types - Create a new report type
router.post('/report-types', reportingController.createReportType);

// GET /api/reporting/report-types/:id - Get a specific report type
router.get('/report-types/:id', reportingController.getReportType);

// PUT /api/reporting/report-types/:id - Update a report type
router.put('/report-types/:id', reportingController.updateReportType);

// DELETE /api/reporting/report-types/:id - Delete a report type
router.delete('/report-types/:id', reportingController.deleteReportType);

// POST /api/reporting/report-types/:id/duplicate - Duplicate a report type
router.post('/report-types/:id/duplicate', reportingController.duplicateReportType);

// PATCH /api/reporting/report-types/:id/toggle-active - Toggle report type active status
router.patch('/report-types/:id/toggle-active', reportingController.toggleReportTypeActive);

/**
 * Cross-Entity Analytics Routes
 */

// GET /api/reporting/cross-entity/analytics - Get cross-entity analytics
router.get('/cross-entity/analytics', reportingController.getCrossEntityAnalytics);

/**
 * Data Report Generation Routes
 */

// POST /api/reporting/reports/generate - Generate comprehensive data report
router.post('/reports/generate', reportingController.generateDataReport);

// GET /api/reporting/reports/templates - Get available report templates
router.get('/reports/templates', reportingController.getReportTemplates);

// POST /api/reporting/reports/individual/:entityType/:entityId - Generate individual entity report
router.post(
  '/reports/individual/:entityType/:entityId',
  reportingController.generateIndividualReport,
);

// POST /api/reporting/reports/aggregate/:entityType - Generate aggregate entity report
router.post('/reports/aggregate/:entityType', reportingController.generateAggregateReport);

// POST /api/reporting/reports/raw-data - Generate detailed raw data report
router.post('/reports/raw-data', reportingController.generateDataReport);

// GET /api/reporting/reports/history - Get report generation history
router.get('/reports/history', reportingController.getReportHistory);

// GET /api/reporting/reports/:reportId/download - Download generated report
router.get('/reports/:reportId/download', reportingController.downloadReport);

/**
 * Export Routes
 */

// POST /api/reporting/export/pdf - Export report as PDF
router.post('/export/pdf', reportingController.exportToPDF);

// POST /api/reporting/export/excel - Export report as Excel
router.post('/export/excel', reportingController.exportToExcel);

// POST /api/reporting/export/csv - Export report as CSV
router.post('/export/csv', reportingController.exportToCSV);

/**
 * Health Check Route
 */

// GET /api/reporting/health - Health check for reporting service
router.get('/health', (req, res) => {
  res.json({
    status: 'success',
    service: 'reporting',
    timestamp: new Date().toISOString(),
    message: 'Reporting service is operational',
  });
});

export default router;
