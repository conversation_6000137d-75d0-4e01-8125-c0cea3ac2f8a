import { z } from 'zod';

// Base schema for common fields
const serviceRecordBase = {
  cost: z.preprocess(
    val => (val === '' || val === null || val === undefined ? undefined : Number(val)),
    z.number().min(0, 'Cost cannot be negative').optional().nullable(),
  ),
  date: z
    .string()
    .datetime({ message: 'Invalid date format. Expected ISO string.' })
    .refine(val => !isNaN(Date.parse(val)), {
      message: 'Service date is required',
    }),
  employeeId: z
    .number()
    .int()
    .positive('Employee ID must be a positive integer')
    .optional()
    .nullable(),
  notes: z.string().optional().nullable(),
  odometer: z.number().int().min(0, 'Odometer reading cannot be negative'),
  servicePerformed: z
    .array(z.string().min(1, 'Service description cannot be empty'))
    .min(1, 'At least one service must be described'),
};

/**
 * @openapi
 * components:
 *   schemas:
 *     ServiceRecordCreateInput:
 *       type: object
 *       required:
 *         - date
 *         - odometer
 *         - servicePerformed
 *       properties:
 *         employeeId:
 *           type: integer
 *           description: ID of the employee who performed the service (optional).
 *         date:
 *           type: string
 *           format: date-time
 *           description: Date the service was performed.
 *         odometer:
 *           type: integer
 *           description: Odometer reading at the time of service.
 *           minimum: 0
 *         servicePerformed:
 *           type: array
 *           items:
 *             type: string
 *           description: List of services performed.
 *           minItems: 1
 *         notes:
 *           type: string
 *           description: Additional notes about the service.
 *         cost:
 *           type: number
 *           format: float
 *           description: Cost of the service (optional).
 *           minimum: 0
 *     ServiceRecordUpdateInput:
 *       type: object
 *       properties:
 *         employeeId:
 *           type: integer
 *           description: ID of the employee who performed the service (optional).
 *         date:
 *           type: string
 *           format: date-time
 *           description: Date the service was performed.
 *         odometer:
 *           type: integer
 *           description: Odometer reading at the time of service.
 *           minimum: 0
 *         servicePerformed:
 *           type: array
 *           items:
 *             type: string
 *           description: List of services performed.
 *         notes:
 *           type: string
 *           description: Additional notes about the service.
 *         cost:
 *           type: number
 *           format: float
 *           description: Cost of the service (optional).
 *           minimum: 0
 *     ServiceRecord:
 *       allOf:
 *         - $ref: '#/components/schemas/ServiceRecordCreateInput'
 *         - type: object
 *           properties:
 *             id:
 *               type: string
 *               format: uuid
 *               description: Unique identifier for the service record.
 *             vehicleId:
 *               type: integer
 *               description: ID of the vehicle this service record belongs to.
 *             createdAt:
 *               type: string
 *               format: date-time
 *             updatedAt:
 *               type: string
 *               format: date-time
 *   parameters:
 *     VehicleIdPath:
 *       name: vehicleId
 *       in: path
 *       required: true
 *       description: ID of the vehicle.
 *       schema:
 *         type: integer
 *     ServiceRecordIdPath:
 *       name: id
 *       in: path
 *       required: true
 *       description: ID of the service record.
 *       schema:
 *         type: string
 *         format: uuid
 *   responses:
 *     ServiceRecordResponse:
 *       description: A single service record.
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/ServiceRecord'
 *     BadRequest:
 *       description: Invalid input.
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *                status: { type: string, example: "error" }
 *                message: { type: string }
 *                errors: { type: array, items: { type: object } }
 *     NotFound:
 *        description: Resource not found.
 *        content:
 *          application/json:
 *            schema:
 *              type: object
 *              properties:
 *                message: { type: string }
 */
export const serviceRecordCreateSchema = z.object({
  // vehicleId is handled by the path param in the controller for nested routes.
  // If creating a service record directly (not nested), then vehicleId would be required here.
  ...serviceRecordBase,
});

export const serviceRecordUpdateSchema = z
  .object({
    ...serviceRecordBase,
  })
  .partial(); // All fields are optional for update

export const vehicleIdParamSchema = z.object({
  vehicleId: z
    .string()
    .refine(val => !isNaN(parseInt(val, 10)), {
      message: 'Vehicle ID path parameter must be a valid number',
    })
    .transform(val => parseInt(val, 10)),
});

export const serviceRecordIdParamSchema = z.object({
  id: z
    .string()
    .refine(
      val =>
        val === 'enriched' ||
        /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(val) ||
        /^service_\d+_\d+$/.test(val),
      {
        message: 'Service Record ID path parameter must be a UUID, custom service ID format (service_vehicleId_timestamp), or the special value "enriched"',
      },
    ),
});

export type ServiceRecordCreate = z.infer<typeof serviceRecordCreateSchema>;
export type ServiceRecordUpdate = z.infer<typeof serviceRecordUpdateSchema>;
