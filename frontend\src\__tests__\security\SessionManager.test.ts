/**
 * @file SessionManager State Coordination Tests
 * @description Tests for session integrity validation and state recovery
 */

import { SessionManager } from '../../lib/security/SessionManager';

// Mock localStorage
const mockLocalStorage = (() => {
  let store: Record<string, string> = {};
  return {
    getItem: jest.fn((key: string) => store[key] || null),
    setItem: jest.fn((key: string, value: string) => {
      store[key] = value;
    }),
    removeItem: jest.fn((key: string) => {
      delete store[key];
    }),
    clear: jest.fn(() => {
      store = {};
    }),
  };
})();

// Mock console
const mockConsole = {
  log: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
  debug: jest.fn(),
};

describe('SessionManager State Coordination', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockLocalStorage.clear();
    
    Object.defineProperty(window, 'localStorage', {
      value: mockLocalStorage,
      writable: true,
    });
    
    Object.defineProperty(global, 'console', {
      value: mockConsole,
      writable: true,
    });

    // Initialize session manager
    SessionManager.initialize();
  });

  afterEach(() => {
    SessionManager.cleanup();
  });

  describe('Session State Management', () => {
    test('should initialize with default session state', () => {
      const state = SessionManager.getSessionState();
      
      expect(state).toBeDefined();
      expect(state?.isActive).toBe(true);
      expect(state?.sessionId).toBeDefined();
      expect(state?.lastActivity).toBeInstanceOf(Date);
    });

    test('should update activity timestamp', () => {
      const initialState = SessionManager.getSessionState();
      const initialTime = initialState?.lastActivity?.getTime();
      
      // Wait a bit and update activity
      setTimeout(() => {
        SessionManager.updateActivity();
        
        const updatedState = SessionManager.getSessionState();
        const updatedTime = updatedState?.lastActivity?.getTime();
        
        expect(updatedTime).toBeGreaterThan(initialTime!);
      }, 10);
    });

    test('should detect session timeout correctly', () => {
      // Mock old activity time (31 minutes ago)
      const oldTime = new Date(Date.now() - 31 * 60 * 1000);
      SessionManager.updateActivity();
      
      // Manually set old time in localStorage
      const sessionState = SessionManager.getSessionState();
      if (sessionState) {
        sessionState.lastActivity = oldTime;
        mockLocalStorage.setItem(
          'workhub-session-state',
          JSON.stringify({
            ...sessionState,
            lastActivity: oldTime.toISOString(),
          })
        );
      }

      const isTimeout = SessionManager.detectTimeout();
      expect(isTimeout).toBe(true);
    });

    test('should not detect timeout for recent activity', () => {
      SessionManager.updateActivity();
      
      const isTimeout = SessionManager.detectTimeout();
      expect(isTimeout).toBe(false);
    });
  });

  describe('Session Integrity Validation', () => {
    test('should validate consistent session state', () => {
      SessionManager.updateActivity();
      
      const isConsistent = SessionManager.validateSessionConsistency();
      expect(isConsistent).toBe(true);
    });

    test('should detect inconsistent session state', () => {
      // Create inconsistent state by manually corrupting localStorage
      mockLocalStorage.setItem(
        'workhub-session-state',
        '{"invalid": "json"}'
      );

      const isConsistent = SessionManager.validateSessionConsistency();
      expect(isConsistent).toBe(false);
    });

    test('should perform comprehensive integrity check', async () => {
      SessionManager.updateActivity();
      
      const integrityCheck = await SessionManager.performIntegrityCheck();
      expect(integrityCheck).toBe(true);
    });

    test('should fail integrity check for corrupted state', async () => {
      // Corrupt the session state
      mockLocalStorage.setItem(
        'workhub-session-state',
        'corrupted-data'
      );

      const integrityCheck = await SessionManager.performIntegrityCheck();
      expect(integrityCheck).toBe(false);
    });
  });

  describe('State Recovery Mechanisms', () => {
    test('should recover from corrupted state', () => {
      // Corrupt the session state
      mockLocalStorage.setItem(
        'workhub-session-state',
        'invalid-json'
      );

      const recovered = SessionManager.recoverFromCorruptedState();
      expect(recovered).toBe(true);

      // Verify state is restored
      const state = SessionManager.getSessionState();
      expect(state).toBeDefined();
      expect(state?.isActive).toBe(true);
      expect(state?.sessionId).toBeDefined();
    });

    test('should preserve valid data during recovery', () => {
      const originalState = SessionManager.getSessionState();
      const originalSessionId = originalState?.sessionId;

      // Simulate partial corruption
      const corruptedState = {
        sessionId: originalSessionId,
        isActive: true,
        lastActivity: 'invalid-date', // This will cause parsing issues
      };
      
      mockLocalStorage.setItem(
        'workhub-session-state',
        JSON.stringify(corruptedState)
      );

      const recovered = SessionManager.recoverFromCorruptedState();
      expect(recovered).toBe(true);

      const recoveredState = SessionManager.getSessionState();
      expect(recoveredState?.sessionId).toBe(originalSessionId);
      expect(recoveredState?.isActive).toBe(true);
      expect(recoveredState?.lastActivity).toBeInstanceOf(Date);
    });

    test('should handle recovery failure gracefully', () => {
      // Mock localStorage to always throw errors
      mockLocalStorage.setItem.mockImplementation(() => {
        throw new Error('Storage error');
      });
      mockLocalStorage.getItem.mockImplementation(() => {
        throw new Error('Storage error');
      });

      const recovered = SessionManager.recoverFromCorruptedState();
      expect(recovered).toBe(false);
      
      expect(mockConsole.error).toHaveBeenCalledWith(
        expect.stringContaining('Failed to recover session state')
      );
    });
  });

  describe('Session Event Handling', () => {
    test('should handle token refresh success', () => {
      SessionManager.handleTokenRefresh(true);
      
      // Should update activity
      const state = SessionManager.getSessionState();
      expect(state?.isActive).toBe(true);
    });

    test('should handle token refresh failure', () => {
      SessionManager.handleTokenRefresh(false, { error: 'Token expired' });
      
      // Should log the failure but maintain session
      expect(mockConsole.warn).toHaveBeenCalledWith(
        expect.stringContaining('Token refresh failed')
      );
    });

    test('should handle session validation success', () => {
      SessionManager.handleSessionValidation(true);
      
      const state = SessionManager.getSessionState();
      expect(state?.isActive).toBe(true);
    });

    test('should handle session validation failure', () => {
      SessionManager.handleSessionValidation(false, { error: 'Invalid session' });
      
      expect(mockConsole.warn).toHaveBeenCalledWith(
        expect.stringContaining('Session validation failed')
      );
    });
  });

  describe('Session Event Listeners', () => {
    test('should add and remove event listeners', () => {
      const mockHandler = jest.fn();
      
      const cleanup = SessionManager.addSessionEventListener(mockHandler);
      expect(typeof cleanup).toBe('function');
      
      // Cleanup should not throw
      expect(() => cleanup()).not.toThrow();
    });

    test('should trigger event listeners on session events', () => {
      const mockHandler = jest.fn();
      
      SessionManager.addSessionEventListener(mockHandler);
      
      // Trigger a session event
      SessionManager.handleTokenRefresh(false);
      
      // Note: This test might need adjustment based on actual event emission implementation
      // The current implementation might not emit events synchronously
    });
  });

  describe('Cross-Tab Coordination', () => {
    test('should clear session state', () => {
      SessionManager.updateActivity();
      
      SessionManager.clearSessionState();
      
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith(
        'workhub-session-state'
      );
    });

    test('should generate unique session IDs', () => {
      const sessionId1 = SessionManager.getCurrentSessionId();
      
      // Clear and reinitialize
      SessionManager.cleanup();
      SessionManager.initialize();
      
      const sessionId2 = SessionManager.getCurrentSessionId();
      
      expect(sessionId1).not.toBe(sessionId2);
    });
  });

  describe('Error Handling and Edge Cases', () => {
    test('should handle missing localStorage gracefully', () => {
      // Mock localStorage to be undefined
      Object.defineProperty(window, 'localStorage', {
        value: undefined,
        writable: true,
      });

      expect(() => {
        SessionManager.initialize();
      }).not.toThrow();
    });

    test('should handle localStorage quota exceeded', () => {
      mockLocalStorage.setItem.mockImplementation(() => {
        throw new Error('QuotaExceededError');
      });

      expect(() => {
        SessionManager.updateActivity();
      }).not.toThrow();
      
      expect(mockConsole.warn).toHaveBeenCalledWith(
        expect.stringContaining('Failed to update session state')
      );
    });

    test('should handle concurrent state modifications', () => {
      // Simulate concurrent modifications
      const promises = Array.from({ length: 10 }, (_, i) => 
        Promise.resolve().then(() => SessionManager.updateActivity())
      );

      return Promise.all(promises).then(() => {
        const state = SessionManager.getSessionState();
        expect(state).toBeDefined();
        expect(state?.isActive).toBe(true);
      });
    });
  });

  describe('Integration with Circuit Breaker', () => {
    test('should coordinate with security operations', () => {
      // This test would verify integration with SecurityUtils
      // but requires mocking the SecurityUtils module
      
      const state = SessionManager.getSessionState();
      expect(state).toBeDefined();
      
      // Verify session state is maintained during security operations
      SessionManager.updateActivity();
      const updatedState = SessionManager.getSessionState();
      expect(updatedState?.lastActivity).toBeInstanceOf(Date);
    });
  });
});
