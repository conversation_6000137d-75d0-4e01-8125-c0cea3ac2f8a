import type { NextFunction, Request, Response } from 'express';

import { createHmac, randomBytes } from 'crypto';
import { Router } from 'express';
import { z } from 'zod';

import { supabaseAdmin } from '../lib/supabase.js';
import { validateRequest } from '../middleware/inputValidation.js';
import { enhancedAuthenticateUser } from '../middleware/jwtAuth.middleware.js';
import {
  authBruteForceProtection,
  authRateLimit,
  loginAttemptProtection,
} from '../middleware/rateLimiting.js';
import { authSecurityMiddleware } from '../middleware/security.js';
import { logAuditEvent } from '../utils/auditLogger.js';
import HttpError from '../utils/HttpError.js';
import logger from '../utils/logger.js';
import { TOKEN_SECURITY_CONFIG, TokenSecurityManager } from '../utils/tokenSecurity.js';

const router = Router();

// SECURITY ENHANCEMENT: Advanced cookie security configuration
// Enhanced with HMAC signatures, stricter SameSite policy, and production-grade security
interface EnhancedCookieOptions {
  domain: string;
  httpOnly: boolean;
  maxAge?: number;
  path: string;
  // Additional security headers
  priority?: 'high' | 'low' | 'medium';
  sameSite: 'lax' | 'none' | 'strict';
  secure: boolean;
}

// SECURITY: Cookie integrity verification with HMAC
const COOKIE_SECRET =
  process.env.COOKIE_SECRET ||
  (() => {
    if (process.env.NODE_ENV === 'production') {
      logger.error('COOKIE_SECRET not set in production environment', {
        security: 'CRITICAL_CONFIG_MISSING',
        service: 'auth-routes',
      });
      throw new Error('COOKIE_SECRET must be explicitly set in production');
    }
    // Generate a random secret for development (not recommended for production)
    return randomBytes(32).toString('hex');
  })();

/**
 * Create a signed cookie value with HMAC integrity verification
 */
function createSignedCookieValue(value: string): string {
  const timestamp = Date.now();
  const signature = generateCookieSignature(value, timestamp);
  return `${value}.${timestamp}.${signature}`;
}

/**
 * Generate HMAC signature for cookie integrity verification
 */
function generateCookieSignature(value: string, timestamp: number): string {
  const payload = `${value}:${timestamp}`;
  return createHmac('sha256', COOKIE_SECRET).update(payload).digest('hex');
}

/**
 * Verify HMAC signature for cookie integrity
 */
function verifyCookieSignature(value: string, timestamp: number, signature: string): boolean {
  const expectedSignature = generateCookieSignature(value, timestamp);
  return (
    createHmac('sha256', COOKIE_SECRET).update(expectedSignature).digest('hex') ===
    createHmac('sha256', COOKIE_SECRET).update(signature).digest('hex')
  );
}

/**
 * Verify and extract value from signed cookie
 */
function verifySignedCookieValue(signedValue: string): null | string {
  try {
    const parts = signedValue.split('.');
    if (parts.length !== 3) {
      logger.warn('Invalid signed cookie format', { service: 'auth-routes' });
      return null;
    }

    const [value, timestampStr, signature] = parts;
    const timestamp = parseInt(timestampStr, 10);

    if (isNaN(timestamp)) {
      logger.warn('Invalid timestamp in signed cookie', { service: 'auth-routes' });
      return null;
    }

    // Check if cookie is expired (24 hours for access tokens, 7 days for refresh tokens)
    const maxAge = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
    if (Date.now() - timestamp > maxAge) {
      logger.warn('Signed cookie expired', { service: 'auth-routes' });
      return null;
    }

    if (!verifyCookieSignature(value, timestamp, signature)) {
      logger.warn('Cookie signature verification failed', { service: 'auth-routes' });
      return null;
    }

    return value;
  } catch (error) {
    logger.error('Error verifying signed cookie', {
      error: error instanceof Error ? error.message : String(error),
      service: 'auth-routes',
    });
    return null;
  }
}

/**
 * Enhanced secure cookie configuration with HMAC signatures and production-grade security
 */
const getCookieOptions = (
  req: Request<any, any, any, any>,
  cookieType: 'access' | 'refresh' = 'access',
): EnhancedCookieOptions => {
  const isProduction = process.env.NODE_ENV === 'production';
  const isSecureConnection = req.secure || req.get('X-Forwarded-Proto') === 'https';

  // Enhanced domain configuration
  const domain = isProduction
    ? process.env.PRODUCTION_DOMAIN ||
      (() => {
        logger.error('PRODUCTION_DOMAIN not set in production environment', {
          security: 'CRITICAL_CONFIG_MISSING',
          service: 'auth-routes',
        });
        throw new Error('PRODUCTION_DOMAIN must be explicitly set in production');
      })()
    : req.hostname;

  // Enhanced SameSite policy based on environment and cookie type
  let sameSite: 'lax' | 'none' | 'strict' = 'lax';

  if (isProduction) {
    // Stricter policy for production
    sameSite = cookieType === 'refresh' ? 'strict' : 'lax';
  } else {
    // More permissive for development
    sameSite = 'lax';
  }

  // Force secure in production or when HTTPS is detected
  const secure = isProduction || isSecureConnection;

  const options: EnhancedCookieOptions = {
    domain,
    httpOnly: true,
    path: '/',
    priority: cookieType === 'refresh' ? 'high' : 'medium',
    sameSite,
    secure,
  };

  // Log security configuration for audit
  logger.debug('Cookie security configuration', {
    cookieType,
    domain,
    isProduction,
    isSecureConnection,
    sameSite,
    secure,
    service: 'auth-routes',
  });

  return options;
};

interface LoginRequestBody {
  email?: string;
  password?: string;
}

// SECURITY HARDENING: Input validation schemas for authentication endpoints
const loginSchema = z.object({
  body: z.object({
    email: z
      .string()
      .email('Invalid email format')
      .min(5, 'Email must be at least 5 characters')
      .max(254, 'Email must not exceed 254 characters')
      .toLowerCase()
      .trim(),
    password: z
      .string()
      .min(8, 'Password must be at least 8 characters')
      .max(128, 'Password must not exceed 128 characters')
      .regex(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
        'Password must contain at least one lowercase letter, one uppercase letter, and one number',
      ),
  }),
  params: z.object({}),
  query: z.object({}),
});

/**
 * Extract and verify refresh token from signed cookie
 */
function extractRefreshTokenFromCookie(cookieHeader?: string): null | string {
  if (!cookieHeader) return null;

  const cookies = cookieHeader.split(';').reduce<Record<string, string>>((acc, cookie) => {
    const [key, value] = cookie.trim().split('=');
    if (key && value) {
      acc[key] = value;
    }
    return acc;
  }, {});

  const signedRefreshToken = cookies['sb-refresh-token'];
  if (!signedRefreshToken) return null;

  // Verify and extract the actual token from the signed cookie
  const actualToken = verifySignedCookieValue(signedRefreshToken);
  if (!actualToken) {
    logger.warn('Failed to verify signed refresh token cookie', { service: 'auth-routes' });
    return null;
  }

  return actualToken;
}

router.post(
  '/login',
  authSecurityMiddleware, // SECURITY HARDENING: Add auth-specific security headers
  validateRequest(loginSchema), // SECURITY HARDENING: Input validation
  authRateLimit,
  loginAttemptProtection, // SECURITY HARDENING: Enhanced brute force protection
  async (
    req: Request<unknown, unknown, LoginRequestBody>,
    res: Response,
    next: NextFunction,
  ): Promise<void> => {
    const { email, password } = req.body;
    if (!email || !password) {
      next(new HttpError('Email and password are required for login.', 400, 'MISSING_CREDENTIALS'));
      return;
    }
    try {
      const { data, error } = await supabaseAdmin.auth.signInWithPassword({ email, password });
      if (error || !data.session) {
        logger.warn('Supabase login failed', { email, error: error?.message });
        next(
          new HttpError(
            error?.message ?? 'Authentication failed. Please check your email and password.',
            401,
            'INVALID_CREDENTIALS',
          ),
        );
        return;
      }
      const expiresInSeconds = data.session.expires_in;

      // SECURITY ENHANCEMENT: Use enhanced cookie options with HMAC signatures
      const accessCookieOptions = getCookieOptions(req, 'access');
      const refreshCookieOptions = getCookieOptions(req, 'refresh');

      // Set access token cookie with enhanced security
      res.cookie('sb-access-token', createSignedCookieValue(data.session.access_token), {
        ...accessCookieOptions,
        maxAge: expiresInSeconds * 1000,
      });

      // Set refresh token cookie with enhanced security
      res.cookie('sb-refresh-token', createSignedCookieValue(data.session.refresh_token), {
        ...refreshCookieOptions,
        maxAge: TOKEN_SECURITY_CONFIG.REFRESH_TOKEN_MAX_AGE, // Reduced from 30 days to 7 days
      });

      // Log security-enhanced cookie setting
      logger.info('Enhanced secure cookies set successfully', {
        accessCookieSecure: accessCookieOptions.secure,
        accessSameSite: accessCookieOptions.sameSite,
        email,
        refreshCookieSecure: refreshCookieOptions.secure,
        refreshSameSite: refreshCookieOptions.sameSite,
        service: 'auth-routes',
        userId: data.user.id,
      });
      logger.info('User logged in successfully and httpOnly cookies set', {
        email,
        userId: data.user.id,
      });
      res
        .status(200)
        .json({ message: 'Login successful', user: { email: data.user.email, id: data.user.id } });
    } catch (error: unknown) {
      logger.error('Login endpoint error', {
        error: error instanceof Error ? error.message : String(error),
      });
      next(new HttpError('An unexpected error occurred during login.', 500, 'SERVER_ERROR'));
    }
  },
);

router.post('/logout', (req: Request, res: Response) => {
  // SECURITY ENHANCEMENT: Use enhanced cookie options for clearing cookies
  const accessCookieOptions = getCookieOptions(req, 'access');
  const refreshCookieOptions = getCookieOptions(req, 'refresh');

  res.clearCookie('sb-access-token', accessCookieOptions);
  res.clearCookie('sb-refresh-token', refreshCookieOptions);

  logger.info('User logged out successfully, secure cookies cleared', {
    ip: req.ip,
    service: 'auth-routes',
    userAgent: req.get('User-Agent'),
  });

  res.status(200).json({ message: 'Logout successful' });
});

router.post(
  '/refresh',
  authSecurityMiddleware, // SECURITY HARDENING: Add auth-specific security headers
  authRateLimit,
  authBruteForceProtection, // SECURITY HARDENING: Enhanced protection for token refresh
  async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      // SECURITY ENHANCEMENT: Enhanced refresh token extraction with type safety
      interface RefreshTokenBody {
        refresh_token?: string;
      }

      const body = req.body as RefreshTokenBody;

      // Try to get refresh token from multiple sources (OAuth2 standard + httpOnly cookie)
      let refreshToken: null | string = body?.refresh_token || null; // OAuth2 standard: from request body

      // Use nullish coalescing for cleaner code
      refreshToken ??= extractRefreshTokenFromCookie(req.headers.cookie); // Fallback: from httpOnly cookie

      if (!refreshToken) {
        logAuditEvent(
          {
            action: 'TOKEN_REFRESH_NO_TOKEN',
            details: {
              bodyHasRefreshToken: !!body?.refresh_token,
              cookieHasRefreshToken: !!extractRefreshTokenFromCookie(req.headers.cookie),
              userAgent: req.get('User-Agent'),
            },
            errorCode: 'NO_REFRESH_TOKEN',
            eventType: 'AUTH',
            message: 'Token refresh attempt without refresh token',
            outcome: 'FAILURE',
          },
          req,
        );

        next(new HttpError('No refresh token provided', 401, 'NO_REFRESH_TOKEN'));
        return;
      }

      // Use enhanced token security manager for rotation
      const rotationResult = await TokenSecurityManager.rotateRefreshToken(refreshToken, req);

      if (!rotationResult.success) {
        // SECURITY ENHANCEMENT: Clear cookies on failure with enhanced options
        const accessCookieOptions = getCookieOptions(req, 'access');
        const refreshCookieOptions = getCookieOptions(req, 'refresh');

        res.clearCookie('sb-access-token', accessCookieOptions);
        res.clearCookie('sb-refresh-token', refreshCookieOptions);

        next(
          new HttpError(
            'Unable to refresh authentication token',
            401,
            rotationResult.error || 'REFRESH_FAILED',
          ),
        );
        return;
      }

      const { newTokens } = rotationResult;
      if (!newTokens) {
        next(new HttpError('Token rotation failed', 500, 'ROTATION_ERROR'));
        return;
      }

      // SECURITY ENHANCEMENT: Set new cookies with enhanced security and HMAC signatures
      const accessCookieOptions = getCookieOptions(req, 'access');
      const refreshCookieOptions = getCookieOptions(req, 'refresh');

      res.cookie('sb-access-token', createSignedCookieValue(newTokens.accessToken), {
        ...accessCookieOptions,
        maxAge: newTokens.expiresIn * 1000,
      });

      res.cookie('sb-refresh-token', createSignedCookieValue(newTokens.refreshToken), {
        ...refreshCookieOptions,
        maxAge: TOKEN_SECURITY_CONFIG.REFRESH_TOKEN_MAX_AGE,
      });

      logger.info('Token refreshed successfully with rotation', {
        deviceFingerprint: TokenSecurityManager.generateDeviceFingerprint(req),
        ip: req.ip,
      });

      res.status(200).json({
        expiresIn: newTokens.expiresIn,
        message: 'Token refreshed successfully',
        // Note: User info not included for security - client should use /auth/test if needed
      });
    } catch (error: unknown) {
      logger.error('Token refresh endpoint unexpected error', {
        error: error instanceof Error ? error.message : String(error),
        ip: req.ip,
        stack: error instanceof Error ? error.stack : undefined,
      });
      next(new HttpError('An unexpected error occurred during token refresh', 500, 'SERVER_ERROR'));
    }
  },
);

router.get(
  '/test',
  enhancedAuthenticateUser,
  (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user) {
      logger.warn(
        'User object not found on request after enhancedAuthenticateUser middleware for /auth/test',
      );
      next(new HttpError('Authentication failed unexpectedly.', 401, 'NO_USER_UNEXPECTED'));
      return;
    }
    res.status(200).json({
      message: '✅ Authentication test successful with enhanced middleware.',
      user: {
        email: req.user.email,
        employeeIdFromMiddleware: req.employeeId,
        id: req.user.id,
        roleFromMiddleware: req.userRole,
        ...(req.user.app_metadata && {
          app_metadata: req.user.app_metadata,
        }),
        ...(req.user.user_metadata && {
          user_metadata: req.user.user_metadata,
        }),
        ...(req.user.aud && { aud: req.user.aud }),
        ...(req.user.created_at && { created_at: req.user.created_at }),
        ...(req.user.email_confirmed_at && { email_confirmed_at: req.user.email_confirmed_at }),
        ...(req.user.last_sign_in_at && { last_sign_in_at: req.user.last_sign_in_at }),
      },
    });
  },
);

export default router;
