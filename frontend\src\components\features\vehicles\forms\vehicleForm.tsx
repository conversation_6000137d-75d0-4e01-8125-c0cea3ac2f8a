/**
 * @file Vehicle Form Component
 * @description Enhanced vehicle form using BaseForm pattern with comprehensive fields
 */

import React from 'react';
import { Car, Calendar, Palette, Hash, FileText, Wrench } from 'lucide-react';

import type { CreateVehicleData } from '@/lib/types/domain';

import { BaseForm } from '@/components/ui/forms/baseForm';
import { FormField } from '@/components/ui/forms/formField';
import { VehicleFormSchema } from '@/lib/schemas/vehicleSchemas';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface VehicleFormProps {
  initialData?: Partial<CreateVehicleData>;
  isEditing?: boolean;
  isLoading?: boolean;
  onSubmit: (data: CreateVehicleData) => Promise<void>;
}

export const VehicleForm = ({
  initialData,
  isEditing = false,
  isLoading = false,
  onSubmit,
}: VehicleFormProps) => {
  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Car className="h-5 w-5" />
          {isEditing ? 'Edit Vehicle' : 'Add New Vehicle'}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <BaseForm
          defaultValues={{
            status: 'active',
            ...initialData,
          }}
          onSubmit={onSubmit}
          schema={VehicleFormSchema}
          className="space-y-6"
        >
          {/* Basic Information Section */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">
              Basic Information
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                label="Make"
                name="make"
                placeholder="e.g., Toyota, Ford, BMW"
                icon={Car}
                required
              />
              <FormField
                label="Model"
                name="model"
                placeholder="e.g., Camry, F-150, X3"
                icon={Car}
                required
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                label="Year"
                name="year"
                type="number"
                placeholder="e.g., 2023"
                icon={Calendar}
                min={1900}
                max={new Date().getFullYear() + 1}
                required
              />
              <FormField
                label="Color"
                name="color"
                placeholder="e.g., Red, Blue, Silver"
                icon={Palette}
              />
            </div>
          </div>

          {/* Identification Section */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">
              Identification
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                label="License Plate"
                name="licensePlate"
                placeholder="e.g., ABC-1234"
                icon={Hash}
                required
              />
              <FormField
                label="VIN (Optional)"
                name="vin"
                placeholder="17-character VIN (auto-generated if empty)"
                icon={Hash}
                maxLength={17}
              />
            </div>
          </div>

          {/* Additional Information Section */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">
              Additional Information
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                label="Mileage"
                name="mileage"
                type="number"
                placeholder="Current mileage"
                icon={Wrench}
                min={0}
              />
              <FormField
                label="Status"
                name="status"
                type="select"
                placeholder="Select status"
                options={[
                  { value: 'active', label: 'Active' },
                  { value: 'maintenance', label: 'In Maintenance' },
                  { value: 'inactive', label: 'Inactive' },
                ]}
                defaultValue="active"
              />
            </div>

            <FormField
              label="Notes"
              name="notes"
              type="textarea"
              placeholder="Additional notes about the vehicle..."
              icon={FileText}
              rows={3}
            />
          </div>

          {/* Submit Button */}
          <div className="flex justify-end pt-6 border-t">
            <Button
              type="submit"
              disabled={isLoading}
              className="min-w-[120px]"
            >
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                  {isEditing ? 'Updating...' : 'Creating...'}
                </>
              ) : (
                <>
                  <Car className="h-4 w-4 mr-2" />
                  {isEditing ? 'Update Vehicle' : 'Create Vehicle'}
                </>
              )}
            </Button>
          </div>
        </BaseForm>
      </CardContent>
    </Card>
  );
};
