/**
 * @file API Hooks Index
 * @description Centralized exports for all API-related hooks
 */

// Core API hooks
export {
  useApiQuery,
  useDependentApiQuery,
  usePaginatedApiQuery,
  type ApiQueryOptions,
  type ApiQueryResult,
  type PaginatedQueryOptions,
  type PaginatedQueryResult,
} from './useApiQuery';

export {
  useApiMutation,
  useCreateMutation,
  useUpdateMutation,
  useDeleteMutation,
  type ApiMutationOptions,
  type ApiMutationResult,
} from './useApiMutation';

// Advanced API hooks (moved from lib/hooks)
export {
  useNavigationPrefetch,
  useJourneyPrefetch,
  USER_JOURNEYS,
} from './useNavigationPrefetch';
export {
  useQueryOptimization,
  useQueryPerformanceMonitor,
} from './useQueryOptimization';
export { useSmartQuery } from './useSmartQuery';

// Re-export commonly used React Query types for convenience
export type {
  QueryKey,
  UseQueryOptions,
  UseQueryResult,
  UseMutationOptions,
  UseMutationResult,
} from '@tanstack/react-query';
