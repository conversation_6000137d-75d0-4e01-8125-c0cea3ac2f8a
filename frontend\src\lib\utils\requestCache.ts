/**
 * Request Cache Utility
 *
 * Provides intelligent caching for API requests to reduce server load
 * and improve user experience by avoiding unnecessary API calls.
 */

interface CacheEntry<T> {
  data: T;
  expiresAt: number;
  timestamp: number;
}

interface CacheOptions {
  /** Cache duration in milliseconds */
  duration: number;
  /** Maximum number of entries to keep in cache */
  maxEntries?: number;
  /** Whether to return stale data while refreshing */
  staleWhileRevalidate?: boolean;
  /** Minimum time between requests in milliseconds (throttling) */
  throttleTime?: number;
}

class RequestCache {
  private readonly cache = new Map<string, CacheEntry<any>>();
  private readonly defaultOptions: Required<CacheOptions> = {
    duration: 30_000, // 30 seconds
    maxEntries: 100,
    staleWhileRevalidate: true,
    throttleTime: 1000, // 1 second minimum between requests
  };
  private readonly lastRequestTime = new Map<string, number>();

  /**
   * Clear all cache entries
   */
  clear(): void {
    const size = this.cache.size;
    this.cache.clear();
    console.log(`🔄 Cache: Cleared ${size} entries`);
  }

  /**
   * Get cached data or execute the request function
   */
  async get<T>(
    key: string,
    requestFn: () => Promise<T>,
    options: Partial<CacheOptions> = {}
  ): Promise<T> {
    const opts = { ...this.defaultOptions, ...options };
    const now = Date.now();
    const cached = this.cache.get(key);

    // Return fresh cached data
    if (cached && now < cached.expiresAt) {
      const timeLeft = Math.round((cached.expiresAt - now) / 1000);
      console.log(`🔄 Cache HIT for ${key} (expires in ${timeLeft}s)`);
      return cached.data;
    }

    // Check throttling - if we made a request too recently, return stale data if available
    const lastRequest = this.lastRequestTime.get(key) || 0;
    if (now - lastRequest < opts.throttleTime && cached) {
      const throttleTimeLeft = Math.round(
        (opts.throttleTime - (now - lastRequest)) / 1000
      );
      console.log(
        `🔄 Cache THROTTLED for ${key}, returning stale data (throttle ends in ${throttleTimeLeft}s)`
      );
      return cached.data;
    }

    // Return stale data while revalidating in background
    if (
      cached &&
      opts.staleWhileRevalidate &&
      now < cached.expiresAt + opts.duration
    ) {
      console.log(`🔄 Cache STALE for ${key}, revalidating in background`);

      // Revalidate in background
      this.revalidateInBackground(key, requestFn, opts);

      return cached.data;
    }

    // Cache miss or expired - fetch fresh data
    console.log(`🔄 Cache MISS for ${key}, fetching fresh data`);
    return this.fetchAndCache(key, requestFn, opts);
  }

  /**
   * Get cache statistics
   */
  getStats(): {
    entries: { age: number; expiresIn: number; key: string }[];
    size: number;
  } {
    const now = Date.now();
    const entries = [...this.cache.entries()].map(([key, entry]) => ({
      age: now - entry.timestamp,
      expiresIn: entry.expiresAt - now,
      key,
    }));

    return {
      entries,
      size: this.cache.size,
    };
  }

  /**
   * Invalidate specific cache entry
   */
  invalidate(key: string): void {
    this.cache.delete(key);
    console.log(`🔄 Cache: Invalidated ${key}`);
  }

  /**
   * Invalidate all cache entries matching pattern
   */
  invalidatePattern(pattern: RegExp): void {
    const keysToDelete: string[] = [];

    for (const key of this.cache.keys()) {
      if (pattern.test(key)) {
        keysToDelete.push(key);
      }
    }

    for (const key of keysToDelete) this.cache.delete(key);
    console.log(
      `🔄 Cache: Invalidated ${keysToDelete.length} entries matching pattern`
    );
  }

  /**
   * Cleanup old cache entries
   */
  private cleanup(maxEntries: number): void {
    if (this.cache.size <= maxEntries) return;

    // Sort by timestamp and remove oldest entries
    const entries = [...this.cache.entries()].sort(
      ([, a], [, b]) => a.timestamp - b.timestamp
    );

    const toRemove = entries.slice(0, this.cache.size - maxEntries);
    for (const [key] of toRemove) {
      this.cache.delete(key);
    }

    console.log(`🔄 Cache: Cleaned up ${toRemove.length} old entries`);
  }

  /**
   * Fetch data and store in cache
   */
  private async fetchAndCache<T>(
    key: string,
    requestFn: () => Promise<T>,
    options: Required<CacheOptions>
  ): Promise<T> {
    try {
      const now = Date.now();

      // Track request time for throttling
      this.lastRequestTime.set(key, now);

      const data = await requestFn();

      this.cache.set(key, {
        data,
        expiresAt: now + options.duration,
        timestamp: now,
      });

      // Cleanup old entries if cache is too large
      this.cleanup(options.maxEntries);

      return data;
    } catch (error) {
      // If we have stale data, return it on error
      const cached = this.cache.get(key);
      if (cached) {
        console.warn(
          `🔄 Cache: Returning stale data for ${key} due to error:`,
          error
        );
        return cached.data;
      }
      throw error;
    }
  }

  /**
   * Revalidate cache entry in background
   */
  private async revalidateInBackground<T>(
    key: string,
    requestFn: () => Promise<T>,
    options: Required<CacheOptions>
  ): Promise<void> {
    try {
      await this.fetchAndCache(key, requestFn, options);
      console.log(`🔄 Cache: Background revalidation completed for ${key}`);
    } catch (error) {
      console.warn(
        `🔄 Cache: Background revalidation failed for ${key}:`,
        error
      );
    }
  }
}

// Export singleton instance
export const requestCache = new RequestCache();

// Export cache options for different types of requests
export const CACHE_DURATIONS = {
  AUDIT_LOGS: 60_000, // 1 minute - audit logs need regular updates
  ERROR_LOGS: 30_000, // 30 seconds - errors need more frequent updates
  HEALTH_STATUS: 60_000, // 1 minute - health doesn't change frequently
  PERFORMANCE_METRICS: 120_000, // 2 minutes - performance data is relatively stable
  USER_DATA: 300_000, // 5 minutes - user data changes infrequently
} as const;

export { RequestCache };
export type { CacheOptions, CacheEntry };
