import { z } from 'zod';

export const EmployeeRoleSchema = z.enum([
  'driver',
  'mechanic',
  'administrator',
  'office_staff',
  'manager',
  'service_advisor',
  'technician',
  'other',
]);
export type EmployeeRoleType = z.infer<typeof EmployeeRoleSchema>;

export const EmployeeStatusSchema = z.enum(['Active', 'On_Leave', 'Terminated', 'Inactive']);
export type EmployeeStatusType = z.infer<typeof EmployeeStatusSchema>;

export const DriverAvailabilitySchema = z.enum(['On_Shift', 'Off_Shift', 'On_Break', 'Busy']);
export type DriverAvailabilityType = z.infer<typeof DriverAvailabilitySchema>;

const employeeBaseFields = {
  // Driver-specific, made optional
  availability: DriverAvailabilitySchema.optional().nullable(),
  contactEmail: z.string().email('Invalid email address').optional().nullable(),
  contactInfo: z
    .string()
    .min(1, 'Contact information is required')
    .refine(val => {
      const phoneRegex = /^[\d\s\+\-\(\)]{7,20}$/;
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return phoneRegex.test(val) || emailRegex.test(val);
    }, 'Contact information must be a valid phone number or email address'),
  contactMobile: z.string().optional().nullable(),
  contactPhone: z.string().optional().nullable(),
  currentLocation: z.string().optional().nullable(),
  department: z.string().optional().nullable(),
  employeeId: z.string().min(1, 'Employee ID (business key) is required'),
  fullName: z.string().optional().nullable(), // Optional, can be derived or same as name
  generalAssignments: z.array(z.string()).optional().default([]),
  hireDate: z
    .string()
    .optional()
    .nullable()
    .refine(
      val => {
        if (!val || val.trim() === '') return true; // Allow empty
        return !isNaN(Date.parse(val)); // Allow any valid date string
      },
      {
        message: 'Invalid hire date format',
      },
    ),
  name: z.string().min(1, 'Name is required'),

  notes: z.string().optional().nullable(),
  position: z.string().optional().nullable(),
  profileImageUrl: z
    .string()
    .optional()
    .nullable()
    .refine(
      val => {
        // Allow empty string, null, or undefined (optional)
        if (!val || val.trim() === '') return true;
        // If value exists, validate it as URL
        try {
          new URL(val);
          return true;
        } catch {
          return false;
        }
      },
      {
        message: 'Invalid URL for profile image',
      },
    ),

  role: EmployeeRoleSchema,
  shiftSchedule: z.string().optional().nullable(),
  skills: z.array(z.string()).optional().default([]),
  status: EmployeeStatusSchema.optional(),
  workingHours: z.string().optional().nullable(),
};

export const employeeCreateSchema = z.object({
  ...employeeBaseFields,
  // 🔧 FIX: Now that frontend sends proper ISO format, we can require datetime format
  hireDate: z
    .string()
    .datetime({ message: 'Invalid hire date format. Expected ISO string.' })
    .optional()
    .nullable(),
});

export const employeeUpdateSchema = z
  .object({
    ...employeeBaseFields,
    statusChangeReason: z.string().optional().nullable(), // For logging status changes during update
  })
  .partial(); // All fields are optional on update

export const employeeIdSchema = z.object({
  id: z
    .string()
    .refine(val => val === 'enriched' || !isNaN(parseInt(val, 10)), {
      message: 'ID must be a valid number or the special value "enriched"',
    })
    .transform(val => (val === 'enriched' ? val : parseInt(val, 10))),
});

export type EmployeeCreate = z.infer<typeof employeeCreateSchema>;
export type EmployeeIdParam = z.infer<typeof employeeIdSchema>;
export type EmployeeUpdate = z.infer<typeof employeeUpdateSchema>;
