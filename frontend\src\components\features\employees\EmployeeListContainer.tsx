'use client';

import React from 'react';

import type { Employee } from '@/lib/types/domain';
import {
  useDeleteEmployee,
  useEmployees,
} from '@/lib/stores/queries/useEmployees';

interface EmployeeListContainerProps {
  children: (data: {
    employees: Employee[];
    error: null | string;
    fetchEmployees: () => Promise<any>;
    handleDelete: (id: number) => Promise<void>;
    isConnected: boolean;
    isRefreshing: boolean;
    loading: boolean;
    socketTriggered: boolean;
  }) => React.ReactNode;
}

const EmployeeListContainer: React.FC<EmployeeListContainerProps> = ({
  children,
}) => {
  const {
    data: employeesData,
    error: queryError,
    fetchStatus,
    isError,
    isFetching,
    isLoading,
    isWebSocketConnected,
    refetch,
  } = useEmployees();

  const { mutateAsync: deleteEmployeeMutation } = useDeleteEmployee();

  const handleDelete = async (id: number) => {
    try {
      await deleteEmployeeMutation(String(id));
    } catch (error_: any) {
      console.error('Error deleting employee:', error_);
      throw error_;
    }
  };

  const currentEmployees = employeesData || [];
  const currentLoading = isLoading || isFetching;
  const currentError = isError
    ? queryError?.message || 'Failed to fetch employees.'
    : null;

  return (
    <>
      {children({
        employees: currentEmployees,
        error: currentError,
        fetchEmployees: async () => refetch(),
        handleDelete,
        isConnected: isWebSocketConnected,
        isRefreshing: isFetching,
        loading: currentLoading,
        socketTriggered: false,
      })}
    </>
  );
};

export default EmployeeListContainer;
