# Authentication & Authorization Scripts

This directory contains scripts for managing Supabase authentication, RBAC
(Role-Based Access Control), and security-related operations.

## Scripts Overview

### 🔧 Setup & Configuration

- **`apply-auth-helpers.js`** - Applies authentication helper functions to
  Supabase
- **`setup-test-users.js`** - Creates test users for development and testing
- **`verify-auth-hook-setup.js`** - Verifies that auth hooks are properly
  configured

### 🔍 Verification & Testing

- **`check-user-roles.js`** - Checks and displays user role assignments
- **`test-auth-hook.js`** - Tests the authentication hook functionality
- **`test-rbac-infrastructure.js`** - Tests the RBAC infrastructure
- **`test-rbac-system.js`** - Comprehensive RBAC system testing
- **`verify-jwt-claims.js`** - Verifies JWT token claims and custom data
- **`verify-rbac-setup.js`** - Verifies RBAC configuration
- **`verify-rls.js`** - Tests Row Level Security policies

### 🐛 Debugging & Troubleshooting

- **`debug-auth-hook.js`** - Debug authentication hook issues
- **`final-rbac-verification.js`** - Final verification of RBAC implementation
- **`phase2-verify-user-profiles.js`** - Verifies user profiles after migration

### 🔧 Maintenance & Fixes

- **`set-user-role.js`** - Manually set user roles
- **`URGENT_FIX_AUTH_HOOK.sql`** - Emergency SQL fix for auth hooks
- **`fix-auth-hook.sql`** - SQL script to fix auth hook issues

## Usage Examples

### Check User Roles

```bash
node scripts/auth/check-user-roles.js
```

### Test RBAC System

```bash
node scripts/auth/test-rbac-system.js
```

### Verify JWT Claims

```bash
node scripts/auth/verify-jwt-claims.js
```

### Set User Role

```bash
node scripts/auth/set-user-role.js <EMAIL> ADMIN
```

## Important Notes

⚠️ **Security Warning**: These scripts have administrative privileges and can
modify user permissions and authentication settings.

### Prerequisites

- Supabase service role key must be configured
- Backend must be built (`npm run build`)
- Proper environment variables set

### Safety Guidelines

1. **Never run in production** without thorough testing
2. **Backup database** before running modification scripts
3. **Verify environment** to ensure you're targeting correct database
4. **Test with non-admin users** first when possible

## RBAC Implementation Status

The current RBAC implementation uses:

- **Hybrid approach**: JWT custom claims + user_profiles table
- **Auth hooks**: Automatically inject role claims into JWT tokens
- **RLS policies**: Database-level security based on JWT claims
- **User profiles**: Dedicated table for role management

## Troubleshooting

### Common Issues

1. **Auth hook not firing**: Check `verify-auth-hook-setup.js`
2. **JWT claims missing**: Run `verify-jwt-claims.js`
3. **RLS policies failing**: Use `verify-rls.js`
4. **Role assignment issues**: Check `check-user-roles.js`

### Debug Process

1. Run verification scripts to identify issues
2. Use debug scripts for detailed troubleshooting
3. Apply fixes using maintenance scripts
4. Re-verify with testing scripts
