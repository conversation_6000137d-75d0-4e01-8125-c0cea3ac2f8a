/**
 * Request Deduplication Middleware
 *
 * Prevents redundant processing of identical requests by caching responses
 * and returning cached results for duplicate requests within a TTL window.
 *
 * Features:
 * - Redis-based distributed deduplication (with in-memory fallback)
 * - Configurable TTL per endpoint type via environment variables
 * - Request fingerprinting based on method, path, body, and user
 * - Metrics collection for monitoring
 * - Support for idempotent operations
 * - Nginx IP allowlist support for reverse proxy configurations
 *
 * <AUTHOR> Development Team
 * @version 2.0 - Enhanced with Redis integration and environment configuration
 */

import type { NextFunction, Request, Response } from 'express';
import type { Redis } from 'ioredis';

import crypto from 'crypto';
import NodeCache from 'node-cache';

import { getRedisClient, isRedisAvailable } from '../services/redis.service.js';
import logger from '../utils/logger.js';

/**
 * Shared Redis client instance - Updated to use centralized Redis service
 */
let redisClient: null | Redis = null;

/**
 * Initialize Redis client for request deduplication using centralized service
 *
 * @returns {Promise<void>}
 */
const initializeRedisClient = async (): Promise<void> => {
  try {
    redisClient = getRedisClient();

    if (redisClient) {
      // Test Redis connectivity with timeout
      const isConnected = await Promise.race([
        isRedisAvailable(),
        new Promise<boolean>(resolve =>
          setTimeout(() => {
            resolve(false);
          }, 5000),
        ), // 5s timeout
      ]);

      if (isConnected) {
        logger.info('Request deduplication using centralized Redis service', {
          backend: 'redis',
          redisStatus: 'connected',
          service: 'request-deduplication',
        });
      } else {
        logger.warn('Redis connectivity test failed, using in-memory cache fallback', {
          backend: 'memory',
          redisStatus: 'timeout',
          service: 'request-deduplication',
        });
        redisClient = null; // Force fallback to memory cache
      }
    } else {
      logger.warn('Redis client not available for request deduplication, using in-memory cache', {
        backend: 'memory',
        redisStatus: 'unavailable',
        service: 'request-deduplication',
      });
    }
  } catch (error) {
    logger.error('Failed to initialize Redis client for request deduplication', {
      error: error instanceof Error ? error.message : 'Unknown error',
      service: 'request-deduplication',
    });
    redisClient = null; // Ensure fallback to memory cache
  }
};

// Initialize Redis client
await initializeRedisClient();

// In-memory cache fallback with optimized default TTL
const memoryCache = new NodeCache({
  checkperiod: 60, // Check for expired keys every 60 seconds
  stdTTL: parseInt(process.env.REQUEST_DEDUP_DEFAULT_TTL || '60', 10), // Reduced default TTL from 300s to 60s
  useClones: false, // Don't clone objects for better performance
});

/**
 * Deduplication configuration interface
 */
interface DeduplicationConfig {
  enabled: boolean; // Whether deduplication is enabled
  includeBody: boolean; // Whether to include request body in fingerprint
  includeHeaders: string[]; // Headers to include in fingerprint
  includeQuery: boolean; // Whether to include query parameters
  keyPrefix: string; // Redis key prefix
  ttl: number; // Time to live in seconds
}

/**
 * Environment-based TTL configuration with fallback defaults
 */
const getTTLFromEnvironment = (configType: string): number => {
  const envVarMap: Record<string, string> = {
    admin: 'ADMIN_DEDUP_TTL',
    api: 'API_DEDUP_TTL',
    idempotent: 'IDEMPOTENT_DEDUP_TTL',
    performance: 'PERFORMANCE_DEDUP_TTL',
  };

  const envVar = envVarMap[configType];
  const defaultTTL = process.env.REQUEST_DEDUP_DEFAULT_TTL ?? '300';

  if (envVar && process.env[envVar]) {
    const ttl = parseInt(process.env[envVar], 10);
    if (!isNaN(ttl) && ttl > 0) {
      return ttl;
    }
  }

  // Optimized fallback TTL values for better performance
  const fallbackTTLs: Record<string, number> = {
    admin: 120, // 2 minutes - Reduced from 5 minutes
    api: 30, // 30 seconds - Reduced from 1 minute
    idempotent: 300, // 5 minutes - Reduced from 10 minutes
    performance: 15, // 15 seconds - Reduced from 30 seconds
  };

  return fallbackTTLs[configType] || parseInt(defaultTTL, 10);
};

/**
 * Check if deduplication is enabled globally
 *
 * @returns {boolean} True if deduplication is enabled
 */
const isDeduplicationEnabled = (): boolean => {
  const enabled = process.env.REQUEST_DEDUP_ENABLED;
  return enabled === 'true' || enabled === '1';
};

/**
 * Parse Nginx proxy IP allowlist from environment variable
 *
 * @returns {string[]} Array of allowed IP ranges/addresses
 */
const getNginxProxyIPs = (): string[] => {
  const nginxIPs = process.env.NGINX_PROXY_IPS;
  if (!nginxIPs) {
    return [];
  }
  return nginxIPs
    .split(',')
    .map(ip => ip.trim())
    .filter(ip => ip.length > 0);
};

/**
 * Get the real client IP address, considering Nginx proxy headers
 *
 * @param {Request} req - Express request object
 * @returns {string} The real client IP address
 */
const getRealClientIP = (req: Request): string => {
  const nginxProxyIPs = getNginxProxyIPs();

  // If no Nginx proxy IPs configured, use standard IP detection
  if (nginxProxyIPs.length === 0) {
    return req.ip ?? req.socket.remoteAddress ?? 'unknown';
  }

  // Check for X-Forwarded-For header (set by Nginx)
  const xForwardedFor = req.headers['x-forwarded-for'];
  if (xForwardedFor) {
    const ips = Array.isArray(xForwardedFor)
      ? xForwardedFor[0].split(',')
      : xForwardedFor.split(',');

    // Return the first (original client) IP
    const clientIP = ips[0]?.trim();
    if (clientIP) {
      return clientIP;
    }
  }

  // Check for X-Real-IP header (alternative Nginx header)
  const xRealIP = req.headers['x-real-ip'];
  if (xRealIP && typeof xRealIP === 'string') {
    return xRealIP;
  }

  // Fallback to standard IP detection
  return req.ip ?? req.socket.remoteAddress ?? 'unknown';
};

// Default configurations for different endpoint types with environment variable support
const DEFAULT_CONFIGS: Record<string, DeduplicationConfig> = {
  admin: {
    enabled: isDeduplicationEnabled(),
    includeBody: true,
    includeHeaders: ['authorization'],
    includeQuery: true,
    keyPrefix: 'dedup:admin',
    ttl: getTTLFromEnvironment('admin'),
  },

  api: {
    enabled: isDeduplicationEnabled(),
    includeBody: true,
    includeHeaders: ['authorization'],
    includeQuery: true,
    keyPrefix: 'dedup:api',
    ttl: getTTLFromEnvironment('api'),
  },

  idempotent: {
    enabled: isDeduplicationEnabled(),
    includeBody: true,
    includeHeaders: ['authorization', 'content-type'],
    includeQuery: true,
    keyPrefix: 'dedup:idem',
    ttl: getTTLFromEnvironment('idempotent'),
  },

  performance: {
    enabled: isDeduplicationEnabled(),
    includeBody: false,
    includeHeaders: ['authorization'],
    includeQuery: true,
    keyPrefix: 'dedup:perf',
    ttl: getTTLFromEnvironment('performance'),
  },
};

// Metrics tracking
interface DeduplicationMetrics {
  cacheHits: number;
  cacheMisses: number;
  errors: number;
  lastReset: Date;
  totalRequests: number;
}

const metrics: DeduplicationMetrics = {
  cacheHits: 0,
  cacheMisses: 0,
  errors: 0,
  lastReset: new Date(),
  totalRequests: 0,
};

/**
 * Create request deduplication middleware
 */
export function createDeduplicationMiddleware(
  configType: keyof typeof DEFAULT_CONFIGS = 'api',
  customConfig?: Partial<DeduplicationConfig>,
) {
  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    // Get configuration
    const config = {
      ...DEFAULT_CONFIGS[configType],
      ...customConfig,
    };

    // Skip if deduplication is disabled
    if (!config.enabled) {
      next();
      return;
    }

    // Skip for non-idempotent methods by default (unless explicitly configured)
    const idempotentMethods = ['GET', 'PUT', 'DELETE', 'HEAD', 'OPTIONS'];
    if (!idempotentMethods.includes(req.method) && configType !== 'idempotent') {
      next();
      return;
    }

    metrics.totalRequests++;

    try {
      // Generate request fingerprint
      const fingerprint = generateRequestFingerprint(req, config);

      // Enhanced logging for debugging cross-contamination issues
      const fullPath = req.originalUrl || req.url || req.path;
      const pathSegments = fullPath.split('/').filter(Boolean);
      const resourceType = pathSegments[1] || 'unknown';

      logger.debug('Request deduplication fingerprint generated', {
        configType: config.keyPrefix,
        fingerprint,
        fullPath: fullPath,
        method: req.method,
        path: req.path,
        resourceType: resourceType,
        service: 'request-deduplication',
        userId: req.userId ?? 'anonymous',
      });

      // Check cache for existing response
      const cachedResponse = await getResponse(fingerprint);

      if (cachedResponse) {
        // Cache hit - return cached response
        metrics.cacheHits++;

        logger.info('Request deduplication cache hit', {
          cacheAge:
            new Date().getTime() -
            (cachedResponse.timestamp ? new Date(cachedResponse.timestamp as string).getTime() : 0),
          configType: config.keyPrefix,
          fingerprint,
          fullPath: fullPath,
          method: req.method,
          path: req.path,
          resourceType: resourceType,
          service: 'request-deduplication',
          userId: req.userId ?? 'anonymous',
        });

        // Set headers to indicate cached response
        res.set('X-Cache', 'HIT');
        res.set('X-Cache-Timestamp', cachedResponse.timestamp);
        res.set('X-Deduplication-Key', fingerprint);

        // Send cached response
        res.status(cachedResponse.statusCode).json(cachedResponse.body);
        return;
      }

      // Cache miss - continue with request processing
      metrics.cacheMisses++;

      // Intercept response to cache it
      const originalSend = res.send;
      const originalJson = res.json;
      let responseSent = false;

      // Override res.send
      res.send = function (body: any) {
        if (!responseSent) {
          responseSent = true;

          // Cache successful responses (2xx status codes)
          if (res.statusCode >= 200 && res.statusCode < 300) {
            storeResponse(
              fingerprint,
              {
                body: body,
                headers: res.getHeaders(),
                statusCode: res.statusCode,
              },
              config.ttl,
            );
          }

          // Set headers to indicate cache miss
          res.set('X-Cache', 'MISS');
          res.set('X-Deduplication-Key', fingerprint);
        }

        return originalSend.call(this, body);
      };

      // Override res.json
      res.json = function (body: any) {
        if (!responseSent) {
          responseSent = true;

          // Cache successful responses (2xx status codes)
          if (res.statusCode >= 200 && res.statusCode < 300) {
            storeResponse(
              fingerprint,
              {
                body: body,
                headers: res.getHeaders(),
                statusCode: res.statusCode,
              },
              config.ttl,
            );
          }

          // Set headers to indicate cache miss
          res.set('X-Cache', 'MISS');
          res.set('X-Deduplication-Key', fingerprint);
        }

        return originalJson.call(this, body);
      };

      next();
    } catch (error) {
      logger.error('Request deduplication middleware error', {
        error: error instanceof Error ? error.message : 'Unknown error',
        method: req.method,
        path: req.path,
        service: 'request-deduplication',
      });
      metrics.errors++;
      next(); // Continue without deduplication on error
    }
  };
}

/**
 * Generate a unique fingerprint for the request
 * Enhanced to prevent cross-contamination between different resource types
 *
 * @param {Request} req - Express request object
 * @param {DeduplicationConfig} config - Deduplication configuration
 * @returns {string} Unique fingerprint for the request
 */
export function generateRequestFingerprint(req: Request, config: DeduplicationConfig): string {
  // Extract resource type from URL for explicit differentiation
  // Use originalUrl or url as fallback since req.path might be modified by middleware
  const fullPath = req.originalUrl || req.url || req.path;
  const pathSegments = fullPath.split('/').filter(Boolean);
  const resourceType = pathSegments[1] || 'unknown'; // e.g., 'tasks', 'delegations', 'admin'

  // Start with method, resource type, and full path for maximum differentiation
  const components: string[] = [req.method, `resource:${resourceType}`, fullPath];

  // Enhanced user identification with multiple fallback strategies
  const userId =
    req.userId ||
    (req as any).user?.id ||
    (req as any).user?.sub ||
    (req as any).auth?.userId ||
    'anonymous';
  components.push(`user:${userId}`);

  // Add origin information for better request differentiation
  const origin =
    req.headers.origin ||
    req.headers.referer ||
    req.headers['x-forwarded-host'] ||
    req.get('host') ||
    'no-origin';
  components.push(`origin:${origin}`);

  // Add session-based differentiation for additional uniqueness
  // Use authorization header as session identifier, fallback to real client IP
  const authHeader = req.headers.authorization;
  const sessionId = authHeader
    ? crypto.createHash('md5').update(authHeader).digest('hex').substring(0, 8)
    : getRealClientIP(req) || 'no-session';
  components.push(`session:${sessionId}`);

  // Include query parameters if configured
  if (config.includeQuery && Object.keys(req.query).length > 0) {
    const sortedQuery = Object.keys(req.query)
      .sort()
      .map(key => `${key}=${req.query[key]}`)
      .join('&');
    components.push(`query:${sortedQuery}`);
  }

  // Include request body if configured
  if (config.includeBody && req.body && Object.keys(req.body).length > 0) {
    const bodyString = JSON.stringify(req.body);
    components.push(`body:${bodyString}`);
  }

  // Include specified headers (excluding authorization which is already used for session)
  for (const headerName of config.includeHeaders) {
    if (headerName.toLowerCase() === 'authorization') continue; // Skip, already used for session
    const headerValue = req.headers[headerName.toLowerCase()];
    if (headerValue) {
      components.push(`header:${headerName}:${headerValue}`);
    }
  }

  // Create hash of all components
  const fingerprint = crypto.createHash('sha256').update(components.join('|')).digest('hex');

  return `${config.keyPrefix}:${resourceType}:${fingerprint}`;
}

/**
 * Retrieve response from cache with enhanced Redis integration
 *
 * @param {string} key - The cache key to retrieve
 * @returns {Promise<any | null>} The cached response data or null if not found
 */
async function getResponse(key: string): Promise<any | null> {
  try {
    let responseData: any = null;
    let cacheBackend = 'memory';

    // Try Redis first if available
    const currentRedisClient = getRedisClient();
    if (currentRedisClient && (await isRedisAvailable())) {
      try {
        const cached = await currentRedisClient.get(key);
        if (cached) {
          responseData = JSON.parse(cached);
          cacheBackend = 'redis';
        }
        logger.debug('Redis cache lookup', {
          backend: 'redis',
          found: !!cached,
          key,
          service: 'request-deduplication',
        });
      } catch (redisError) {
        logger.warn('Redis cache lookup failed, falling back to memory cache', {
          error: redisError instanceof Error ? redisError.message : 'Unknown error',
          key,
          service: 'request-deduplication',
        });
        // Fall through to memory cache
      }
    }

    // Fallback to memory cache if Redis is unavailable or failed
    if (!responseData) {
      responseData = memoryCache.get(key);
      logger.debug('Memory cache lookup', {
        backend: 'memory',
        found: !!responseData,
        key,
        service: 'request-deduplication',
      });
    }

    if (responseData) {
      logger.debug('Response retrieved from deduplication cache', {
        backend: cacheBackend,
        key,
        service: 'request-deduplication',
        timestamp: responseData.timestamp,
      });
      return responseData;
    }

    return null;
  } catch (error) {
    logger.error('Failed to retrieve response from deduplication cache', {
      error: error instanceof Error ? error.message : 'Unknown error',
      key,
      service: 'request-deduplication',
    });
    metrics.errors++;
    return null;
  }
}

/**
 * Store response in cache with enhanced Redis integration
 *
 * @param {string} key - The cache key to store under
 * @param {any} response - The response data to cache
 * @param {number} ttl - Time to live in seconds
 * @returns {Promise<void>}
 */
async function storeResponse(key: string, response: any, ttl: number): Promise<void> {
  try {
    const responseData = {
      body: response.body,
      headers: response.headers,
      statusCode: response.statusCode,
      timestamp: new Date().toISOString(),
    };

    let cacheBackend = 'memory';
    let stored = false;

    // Try Redis first if available
    const currentRedisClient = getRedisClient();
    if (currentRedisClient && (await isRedisAvailable())) {
      try {
        await currentRedisClient.setex(key, ttl, JSON.stringify(responseData));
        cacheBackend = 'redis';
        stored = true;
        logger.debug('Response stored in Redis cache', {
          backend: 'redis',
          key,
          service: 'request-deduplication',
          ttl,
        });
      } catch (redisError) {
        logger.warn('Redis cache storage failed, falling back to memory cache', {
          error: redisError instanceof Error ? redisError.message : 'Unknown error',
          key,
          service: 'request-deduplication',
        });
        // Fall through to memory cache
      }
    }

    // Fallback to memory cache if Redis is unavailable or failed
    if (!stored) {
      memoryCache.set(key, responseData, ttl);
      logger.debug('Response stored in memory cache', {
        backend: 'memory',
        key,
        service: 'request-deduplication',
        ttl,
      });
    }

    logger.debug('Response stored in deduplication cache', {
      backend: cacheBackend,
      key,
      service: 'request-deduplication',
      ttl,
    });
  } catch (error) {
    logger.error('Failed to store response in deduplication cache', {
      error: error instanceof Error ? error.message : 'Unknown error',
      key,
      service: 'request-deduplication',
    });
    metrics.errors++;
  }
}

/**
 * Pre-configured middleware instances
 */
export const adminDeduplication = createDeduplicationMiddleware('admin');
export const apiDeduplication = createDeduplicationMiddleware('api');
export const performanceDeduplication = createDeduplicationMiddleware('performance');
export const idempotentDeduplication = createDeduplicationMiddleware('idempotent');

/**
 * Clear deduplication cache with enhanced Redis integration
 *
 * @param {string} [pattern] - Optional pattern to match keys for deletion
 * @returns {Promise<void>}
 */
export async function clearDeduplicationCache(pattern?: string): Promise<void> {
  try {
    let cacheBackend = 'memory';
    let keysCleared = 0;

    // Try Redis first if available
    const currentRedisClient = getRedisClient();
    if (currentRedisClient && (await isRedisAvailable())) {
      try {
        if (pattern) {
          // Clear keys matching pattern
          const keys = await currentRedisClient.keys(pattern);
          if (keys.length > 0) {
            keysCleared = await currentRedisClient.del(...keys);
          }
        } else {
          // Clear all deduplication keys
          const keys = await currentRedisClient.keys('dedup:*');
          if (keys.length > 0) {
            keysCleared = await currentRedisClient.del(...keys);
          }
        }
        cacheBackend = 'redis';
        logger.debug('Redis cache cleared', {
          backend: 'redis',
          keysCleared,
          pattern: pattern || 'dedup:*',
          service: 'request-deduplication',
        });
      } catch (redisError) {
        logger.warn('Redis cache clear failed, falling back to memory cache', {
          error: redisError instanceof Error ? redisError.message : 'Unknown error',
          pattern: pattern || 'all',
          service: 'request-deduplication',
        });
        // Fall through to memory cache
      }
    }

    // Also clear memory cache (or fallback if Redis failed)
    if (pattern) {
      // Clear memory cache keys matching pattern (simplified)
      const keys = memoryCache.keys().filter(key => key.includes(pattern));
      memoryCache.del(keys);
      if (cacheBackend === 'memory') {
        keysCleared = keys.length;
      }
    } else {
      const allKeys = memoryCache.keys();
      memoryCache.flushAll();
      if (cacheBackend === 'memory') {
        keysCleared = allKeys.length;
      }
    }

    logger.info('Request deduplication cache cleared', {
      backend: cacheBackend,
      keysCleared,
      pattern: pattern || 'all',
      service: 'request-deduplication',
    });
  } catch (error) {
    logger.error('Failed to clear deduplication cache', {
      error: error instanceof Error ? error.message : 'Unknown error',
      pattern: pattern || 'all',
      service: 'request-deduplication',
    });
  }
}

/**
 * Get deduplication metrics
 */
export function getDeduplicationMetrics(): DeduplicationMetrics & {
  hitRate: number;
} {
  const hitRate = metrics.totalRequests > 0 ? (metrics.cacheHits / metrics.totalRequests) * 100 : 0;

  return {
    ...metrics,
    hitRate: Math.round(hitRate * 100) / 100, // Round to 2 decimal places
  };
}

/**
 * Reset deduplication metrics
 */
export function resetDeduplicationMetrics(): void {
  metrics.totalRequests = 0;
  metrics.cacheHits = 0;
  metrics.cacheMisses = 0;
  metrics.errors = 0;
  metrics.lastReset = new Date();

  logger.info('Request deduplication metrics reset', {
    service: 'request-deduplication',
  });
}

// Export middleware instances with test-compatible names
export const adminDeduplicationMiddleware = adminDeduplication;
export const apiDeduplicationMiddleware = apiDeduplication;
export const performanceDeduplicationMiddleware = performanceDeduplication;
export const idempotentDeduplicationMiddleware = idempotentDeduplication;

export default {
  adminDeduplication,
  apiDeduplication,
  clearDeduplicationCache,
  createDeduplicationMiddleware,
  generateRequestFingerprint,
  getDeduplicationMetrics,
  idempotentDeduplication,
  performanceDeduplication,
  resetDeduplicationMetrics,
};
