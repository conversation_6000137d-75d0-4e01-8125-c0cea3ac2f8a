'use client';

import { <PERSON><PERSON><PERSON>riangle, Filter, Info, RefreshCw, XCircle } from 'lucide-react';
import React, { useEffect, useState } from 'react';

import type { ErrorLogEntry, LogLevel } from '@/types';

import ErrorBoundary from '@/components/error-boundaries/ErrorBoundary';
import { ActionButton } from '@/components/ui/action-button';
import { Badge } from '@/components/ui/badge';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { LoadingError } from '@/components/ui/loading-states';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { usePaginatedApiQuery } from '@/hooks/api/useApiQuery';
import { adminService } from '@/lib/api/services/admin'; // Import adminService directly
import { SessionManager } from '@/lib/security/SessionManager';
import { getTokenRefreshService } from '@/lib/services/TokenRefreshService';

/**
 * Component that displays recent system error logs
 */
export function ErrorLog() {
  const [level, setLevel] = useState<LogLevel | undefined>();
  const [refreshing, setRefreshing] = useState(false);
  const [authError, setAuthError] = useState<null | string>(null);

  const {
    currentPage: page,
    data: errors,
    error,
    hasNextPage,
    hasPrevPage,
    isLoading,
    nextPage,
    prevPage,
    refetch,
    totalPages,
  } = usePaginatedApiQuery<ErrorLogEntry>(
    ['adminErrors', level], // Add query key
    (currentPage: number, pageSize: number) =>
      adminService.getRecentErrors(currentPage, pageSize, level),
    {
      pageSize: 10,
    }
  );

  // Handle authentication errors using SessionManager and TokenRefreshService
  useEffect(() => {
    if (error) {
      const handleAuthError = async () => {
        // Check if this is an authentication error
        const isAuthError =
          ('status' in error &&
            (error.status === 401 || error.status === 500)) ||
          ('code' in error &&
            (error.code === 'NO_TOKEN' || error.code === 'INVALID_TOKEN')) ||
          error.message.includes('Authentication failed') ||
          error.message.includes('Not Found'); // Backend returns 500 but frontend sees 404

        if (isAuthError) {
          // Check session state using SessionManager
          const sessionState = SessionManager.getSessionState();
          const isSessionTimeout = SessionManager.detectTimeout();

          if (isSessionTimeout || !sessionState?.isActive) {
            setAuthError(
              'Your session has expired. Click "Refresh Authentication" to renew your session.'
            );
            return;
          }

          // Try to validate and refresh the session using TokenRefreshService
          try {
            const tokenService = getTokenRefreshService();
            const sessionInfo = await tokenService.getSessionInfo();

            if (sessionInfo.isValid) {
              // Session seems valid but API call failed - might be a server issue
              setAuthError(
                'Server error occurred. This might be a temporary issue. Try refreshing.'
              );
            } else {
              if (sessionInfo.isExpired) {
                setAuthError(
                  'Your session has expired. Click "Refresh Authentication" to renew your session.'
                );
              } else {
                setAuthError(
                  'Authentication failed. Please refresh the page to sign in again.'
                );
              }
            }
          } catch {
            setAuthError(
              'Authentication system error. Please refresh the page to sign in again.'
            );
          }
        }
      };

      handleAuthError();
    }
  }, [error]);

  const handleRefresh = async () => {
    setRefreshing(true);
    setAuthError(null); // Clear any previous auth errors
    await refetch();
    setRefreshing(false);
  };

  const handleLevelChange = (value: string) => {
    setLevel(value === 'all' ? undefined : (value as LogLevel));
  };

  const handleAuthRefresh = async () => {
    setRefreshing(true);
    setAuthError(null);

    try {
      const tokenService = getTokenRefreshService();
      const refreshSuccess = await tokenService.refreshNow();

      if (refreshSuccess) {
        // Token refreshed successfully, retry the API call
        await refetch();
      } else {
        setAuthError('Failed to refresh authentication. Please sign in again.');
      }
    } catch {
      setAuthError('Authentication refresh failed. Please sign in again.');
    } finally {
      setRefreshing(false);
    }
  };

  const handlePageRefresh = () => {
    globalThis.location.reload();
  };

  const getLevelIcon = (level: string) => {
    if (level === 'ERROR') return <XCircle className="size-4 text-red-500" />;
    if (level === 'WARNING')
      return <AlertTriangle className="size-4 text-yellow-500" />;
    return <Info className="size-4 text-blue-500" />;
  };

  const getLevelBadge = (level: string) => {
    if (level === 'ERROR')
      return (
        <Badge
          className="border-red-500/30 bg-red-500/20 text-red-700"
          variant="outline"
        >
          Error
        </Badge>
      );
    if (level === 'WARNING')
      return (
        <Badge
          className="border-yellow-500/30 bg-yellow-500/20 text-yellow-700"
          variant="outline"
        >
          Warning
        </Badge>
      );
    return (
      <Badge
        className="border-blue-500/30 bg-blue-500/20 text-blue-700"
        variant="outline"
      >
        Info
      </Badge>
    );
  };

  return (
    <ErrorBoundary>
      <Card className="shadow-md">
        <CardHeader className="p-5 pb-2">
          <CardTitle className="text-xl font-semibold text-primary">
            Recent Errors & Warnings
          </CardTitle>
          <CardDescription>Latest system errors and warnings</CardDescription>
        </CardHeader>

        <div className="px-5 pb-2">
          <div className="flex items-center space-x-2">
            <Filter className="size-4 text-muted-foreground" />
            <span className="text-sm font-medium">Filter by level:</span>
            <Select onValueChange={handleLevelChange} value={level ?? 'all'}>
              <SelectTrigger className="w-[140px]">
                <SelectValue placeholder="All levels" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All levels</SelectItem>
                <SelectItem value="ERROR">Errors only</SelectItem>
                <SelectItem value="WARNING">Warnings only</SelectItem>
                <SelectItem value="INFO">Info only</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <CardContent className="p-5">
          {authError ? (
            <div className="rounded-md border border-red-200 bg-red-50 p-4">
              <div className="flex items-center space-x-2">
                <XCircle className="size-5 text-red-500" />
                <div>
                  <h3 className="font-medium text-red-800">
                    Authentication Error
                  </h3>
                  <p className="text-sm text-red-700">{authError}</p>
                  <div className="mt-3 flex space-x-2">
                    <ActionButton
                      actionType="primary"
                      isLoading={refreshing}
                      loadingText="Refreshing..."
                      onClick={handleAuthRefresh}
                      size="sm"
                    >
                      Refresh Authentication
                    </ActionButton>
                    <ActionButton
                      actionType="tertiary"
                      onClick={handlePageRefresh}
                      size="sm"
                    >
                      Refresh Page
                    </ActionButton>
                  </div>
                </div>
              </div>
            </div>
          ) : isLoading || refreshing ? (
            <div className="space-y-3">
              <Skeleton className="h-6 w-full" />
              <Skeleton className="h-6 w-full" />
              <Skeleton className="h-6 w-full" />
              <Skeleton className="h-6 w-full" />
              <Skeleton className="h-6 w-full" />
            </div>
          ) : error ? (
            <LoadingError message={error.message} onRetry={refetch} />
          ) : errors && errors.length > 0 ? (
            <>
              <ScrollArea className="h-[300px] pr-4">
                <div className="space-y-3">
                  {errors.map((entry: ErrorLogEntry) => (
                    <div
                      className="rounded-md border p-3 transition-colors hover:bg-accent/50"
                      key={entry.id}
                    >
                      <div className="mb-1 flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          {getLevelIcon(entry.level)}
                          <span className="font-medium">{entry.message}</span>
                        </div>
                        {getLevelBadge(entry.level)}
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="text-xs text-muted-foreground">
                          {new Date(entry.timestamp).toLocaleString()}
                        </div>
                        {entry.source && (
                          <div className="text-xs text-muted-foreground">
                            Source: {entry.source}
                          </div>
                        )}
                      </div>
                      {entry.details &&
                        Object.keys(entry.details).length > 0 && (
                          <div className="mt-2 rounded bg-muted p-2 text-xs">
                            <pre className="whitespace-pre-wrap">
                              {JSON.stringify(entry.details, null, 2)}
                            </pre>
                          </div>
                        )}
                    </div>
                  ))}
                </div>
              </ScrollArea>

              {totalPages > 1 && (
                <div className="mt-4 flex items-center justify-between">
                  <ActionButton
                    actionType="tertiary"
                    disabled={!hasPrevPage || isLoading || refreshing}
                    onClick={prevPage}
                    size="sm"
                  >
                    Previous
                  </ActionButton>
                  <span className="text-sm text-muted-foreground">
                    Page {page} of {totalPages}
                  </span>
                  <ActionButton
                    actionType="tertiary"
                    disabled={!hasNextPage || isLoading || refreshing}
                    onClick={nextPage}
                    size="sm"
                  >
                    Next
                  </ActionButton>
                </div>
              )}
            </>
          ) : (
            <div className="p-8 text-center text-muted-foreground">
              <AlertTriangle className="mx-auto mb-2 size-8 text-muted-foreground/50" />
              <p>No errors or warnings found for the selected filter.</p>
              {level && (
                <p className="mt-2 text-sm">
                  Try changing the filter to see more results.
                </p>
              )}
            </div>
          )}
        </CardContent>
        <CardFooter className="p-5">
          <ActionButton
            actionType="tertiary"
            className="w-full"
            icon={<RefreshCw className="size-4" />}
            isLoading={refreshing || isLoading}
            loadingText="Refreshing..."
            onClick={handleRefresh}
            size="sm"
          >
            Refresh Logs
          </ActionButton>
        </CardFooter>
      </Card>
    </ErrorBoundary>
  );
}
