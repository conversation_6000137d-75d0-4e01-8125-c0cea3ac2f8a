import { jest } from '@jest/globals';
import request from 'supertest';
import { initializeAndGetApp } from '../../app';
import { afterAll, beforeAll, beforeEach, describe, expect, it } from '@jest/globals';
import prisma from '../../utils/prisma';

let app;
let vehicle;
let employee;
let serviceRecord;

beforeAll(async () => {
  app = await initializeAndGetApp();
});

afterAll(async () => {
  await prisma.$disconnect();
});

beforeEach(async () => {
  await prisma.task.deleteMany({});
  await prisma.serviceRecord.deleteMany({});
  await prisma.vehicle.deleteMany({});
  await prisma.employee.deleteMany({});

  vehicle = await prisma.vehicle.create({
    data: {
      make: 'TestMake',
      model: 'TestModel',
      year: 2023,
      vin: `SR-VIN-${Date.now()}`,
      licensePlate: 'SR-LP',
      ownerName: 'Test Owner',
      ownerContact: '<EMAIL>',
      createdAt: new Date(),
      updatedAt: new Date(),
    },
  });

  employee = await prisma.employee.create({
    data: {
      name: 'Test Employee for SR',
      role: 'mechanic',
      employeeId: `EMP-SR-${Date.now()}`,
      contactInfo: '<EMAIL>',
      status: 'Active',
      createdAt: new Date(),
      updatedAt: new Date(),
    },
  });

  serviceRecord = await prisma.serviceRecord.create({
    data: {
      Vehicle: { connect: { id: vehicle.id } },
      Employee: { connect: { id: employee.id } },
      servicePerformed: ['Initial service'],
      cost: 150.0,
      date: new Date(),
      odometer: 15000,
      updatedAt: new Date(),
    },
  });
});

describe('Service Record API Routes', () => {
  describe('Direct Service Record Routes (/api/servicerecords)', () => {
    it('should get enriched service records', async () => {
      const response = await request(app).get('/api/servicerecords/enriched');
      expect(response.statusCode).toBe(200);
      expect(response.body.status).toBe('success');
      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.data[0]).toHaveProperty('vehicleMake', vehicle.make);
    });

    it('should get a service record by ID', async () => {
      const response = await request(app).get(`/api/servicerecords/${serviceRecord.id}`);
      expect(response.statusCode).toBe(200);
      expect(response.body.status).toBe('success');
      expect(response.body.data.id).toBe(serviceRecord.id);
    });

    it('should return 404 for a non-existent service record ID', async () => {
      const response = await request(app).get('/api/servicerecords/non-existent-id');
      expect(response.statusCode).toBe(404);
      expect(response.body.status).toBe('error');
    });
  });

  describe('Nested Service Record Routes (/api/vehicles/:vehicleId/servicerecords)', () => {
    const newServiceRecordData = () => ({
      employeeId: employee.id,
      date: new Date().toISOString(),
      odometer: 16000,
      servicePerformed: ['Oil change'],
      notes: 'Performed oil change.',
      cost: 75.0,
    });

    it('should create a new service record for a vehicle', async () => {
      const response = await request(app)
        .post(`/api/vehicles/${vehicle.id}/servicerecords`)
        .send(newServiceRecordData());
      expect(response.statusCode).toBe(201);
      expect(response.body.status).toBe('success');
      expect(response.body.data).toHaveProperty('id');
      expect(response.body.data.vehicleId).toBe(vehicle.id);
    });

    it('should get all service records for a vehicle', async () => {
      const response = await request(app).get(`/api/vehicles/${vehicle.id}/servicerecords`);
      expect(response.statusCode).toBe(200);
      expect(response.body.status).toBe('success');
      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.data.length).toBe(1);
    });

    it('should update a service record', async () => {
      const updates = { notes: 'Updated notes.', cost: 80.0 };
      const response = await request(app)
        .put(`/api/servicerecords/${serviceRecord.id}`)
        .send(updates);
      expect(response.statusCode).toBe(200);
      expect(response.body.status).toBe('success');
      expect(response.body.data.notes).toBe('Updated notes.');
    });

    it('should delete a service record', async () => {
      const response = await request(app).delete(`/api/servicerecords/${serviceRecord.id}`);
      expect(response.statusCode).toBe(200);
      expect(response.body.status).toBe('success');
      expect(response.body.data.message).toContain('deleted successfully');
    });
  });
});
