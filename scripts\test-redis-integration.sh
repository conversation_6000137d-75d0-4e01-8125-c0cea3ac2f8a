#!/bin/bash

# WorkHub Redis Integration Test Script
# Tests Redis connectivity and request deduplication functionality
# Version: 1.0 - Reliability Enhancement Testing

set -euo pipefail  # Exit on error, undefined vars, pipe failures

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
}

# Script header
echo -e "${BLUE}
╔══════════════════════════════════════════════════════════════════════════════╗
║                    WorkHub Redis Integration Test                            ║
║                     Reliability Enhancement Verification                    ║
║                                                                              ║
║  🔧 Tests: Redis Connectivity, Request Deduplication, TTL Configuration     ║
║  ⚡ Features: Environment Variables, Fallback Mechanisms                     ║
╚══════════════════════════════════════════════════════════════════════════════╝
${NC}"

# Check if backend is running
log "🔍 Checking if backend service is running..."
BACKEND_URL="http://localhost:3001"

if ! curl -f -s "$BACKEND_URL/api/health" > /dev/null; then
    error "Backend service is not running at $BACKEND_URL"
    echo "Please start the backend service first:"
    echo "  docker-compose up backend"
    echo "  or"
    echo "  npm run dev (in backend directory)"
    exit 1
fi
success "Backend service is running"

# Test Redis connectivity if Redis URL is configured
log "🔍 Testing Redis connectivity..."
if [[ -n "${REDIS_URL:-}" ]]; then
    # Extract Redis host and port from URL
    REDIS_HOST=$(echo "$REDIS_URL" | sed -n 's|redis://\([^:]*\).*|\1|p')
    REDIS_PORT=$(echo "$REDIS_URL" | sed -n 's|redis://[^:]*:\([0-9]*\).*|\1|p')
    
    if [[ -z "$REDIS_PORT" ]]; then
        REDIS_PORT="6379"
    fi
    
    if command -v nc &> /dev/null; then
        if nc -z "$REDIS_HOST" "$REDIS_PORT" 2>/dev/null; then
            success "Redis connectivity test passed ($REDIS_HOST:$REDIS_PORT)"
        else
            warning "Redis connectivity test failed ($REDIS_HOST:$REDIS_PORT)"
            warning "Request deduplication will use memory fallback"
        fi
    else
        warning "netcat (nc) not available, skipping Redis connectivity test"
    fi
else
    warning "REDIS_URL not configured, testing memory fallback mode"
fi

# Test request deduplication functionality
log "🧪 Testing request deduplication functionality..."

# Test endpoint that should be deduplicated
TEST_ENDPOINT="$BACKEND_URL/api/employees"
AUTH_HEADER="Authorization: Bearer test-token"

# Make first request
log "Making first request to $TEST_ENDPOINT..."
RESPONSE1=$(curl -s -H "$AUTH_HEADER" "$TEST_ENDPOINT" || echo "ERROR")

if [[ "$RESPONSE1" == "ERROR" ]]; then
    warning "First request failed - endpoint may require authentication"
    warning "Testing with public health endpoint instead"
    TEST_ENDPOINT="$BACKEND_URL/api/health"
    RESPONSE1=$(curl -s "$TEST_ENDPOINT")
fi

# Make second request immediately (should be deduplicated if TTL > 0)
log "Making second request immediately..."
RESPONSE2=$(curl -s -H "$AUTH_HEADER" "$TEST_ENDPOINT" 2>/dev/null || curl -s "$TEST_ENDPOINT")

# Check if responses are identical (indicating deduplication worked)
if [[ "$RESPONSE1" == "$RESPONSE2" ]]; then
    success "Request deduplication appears to be working (identical responses)"
else
    warning "Responses differ - deduplication may not be active or TTL expired"
fi

# Test environment variable configuration
log "🔧 Testing environment variable configuration..."

ENV_VARS=(
    "REQUEST_DEDUP_ENABLED"
    "REQUEST_DEDUP_DEFAULT_TTL"
    "ADMIN_DEDUP_TTL"
    "API_DEDUP_TTL"
    "PERFORMANCE_DEDUP_TTL"
    "IDEMPOTENT_DEDUP_TTL"
)

for var in "${ENV_VARS[@]}"; do
    if [[ -n "${!var:-}" ]]; then
        success "$var is configured: ${!var}"
    else
        warning "$var is not configured (using defaults)"
    fi
done

# Test TTL value validation
log "🔧 Validating TTL values..."
TTL_VARS=("REQUEST_DEDUP_DEFAULT_TTL" "ADMIN_DEDUP_TTL" "API_DEDUP_TTL" "PERFORMANCE_DEDUP_TTL" "IDEMPOTENT_DEDUP_TTL")

for var in "${TTL_VARS[@]}"; do
    if [[ -n "${!var:-}" ]]; then
        if [[ "${!var}" =~ ^[0-9]+$ ]] && [[ "${!var}" -gt 0 ]]; then
            success "$var is valid: ${!var} seconds"
        else
            error "$var must be a positive integer: ${!var}"
        fi
    fi
done

# Test Nginx proxy configuration
log "🔧 Testing Nginx proxy configuration..."
if [[ -n "${NGINX_PROXY_IPS:-}" ]]; then
    success "NGINX_PROXY_IPS is configured: ${NGINX_PROXY_IPS}"
    
    # Validate IP format (basic check)
    IFS=',' read -ra IPS <<< "$NGINX_PROXY_IPS"
    for ip in "${IPS[@]}"; do
        ip=$(echo "$ip" | xargs)  # trim whitespace
        if [[ "$ip" =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+(/[0-9]+)?$ ]]; then
            success "Valid IP/CIDR format: $ip"
        else
            warning "Potentially invalid IP/CIDR format: $ip"
        fi
    done
else
    warning "NGINX_PROXY_IPS not configured (using standard IP detection)"
fi

# Performance test (optional)
log "⚡ Running basic performance test..."
START_TIME=$(date +%s%N)

for i in {1..5}; do
    curl -s "$BACKEND_URL/api/health" > /dev/null
done

END_TIME=$(date +%s%N)
DURATION=$(( (END_TIME - START_TIME) / 1000000 ))  # Convert to milliseconds

success "5 requests completed in ${DURATION}ms (avg: $((DURATION / 5))ms per request)"

# Summary
echo -e "${GREEN}
╔══════════════════════════════════════════════════════════════════════════════╗
║                        🎉 REDIS INTEGRATION TEST COMPLETE! 🎉              ║
╚══════════════════════════════════════════════════════════════════════════════╝
${NC}"

echo -e "${BLUE}📊 Test Summary:${NC}"
echo "  🔧 Backend Service: Running"
echo "  ⚡ Request Deduplication: Tested"
echo "  🔧 Environment Variables: Validated"
echo "  🌐 Nginx Configuration: Checked"
echo "  ⚡ Performance: Measured"
echo ""

echo -e "${BLUE}📋 Next Steps:${NC}"
echo "  1. Monitor application logs for deduplication cache hits/misses"
echo "  2. Test with actual load to verify Redis performance benefits"
echo "  3. Configure Redis URL for production deployment"
echo "  4. Set up monitoring for cache hit rates and Redis connectivity"
echo ""

success "Redis integration test completed successfully!"
