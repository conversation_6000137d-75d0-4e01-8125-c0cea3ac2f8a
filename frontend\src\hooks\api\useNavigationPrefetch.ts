/**
 * @file Navigation prefetching hook for performance optimization
 * @module hooks/useNavigationPrefetch
 */

import { useRouter } from 'next/navigation';
import { useCallback, useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';

import {
  delegationApiService,
  employeeApiService,
  taskApiService,
  vehicleApiService,
} from '../../lib/api/services/apiServiceFactory';
import { prefetchUtils, queryClient } from '../../lib/stores/queryClient';
import { useAuthContext } from '../../contexts/AuthContext';

/**
 * Create authentication-aware prefetch patterns
 * @param isAuthReady - Whether authentication system is ready for API calls
 */
const createPrefetchPatterns = (isAuthReady: boolean) => ({
  // Dashboard route - prefetch all critical data
  '/': () => prefetchUtils.prefetchDashboardData(isAuthReady),

  // Admin routes - Updated to use new reliability API service
  '/admin': () => {
    if (!isAuthReady) {
      console.warn('Authentication not ready, deferring admin data prefetch.');
      return Promise.resolve();
    }
    return Promise.all([
      queryClient.prefetchQuery({
        queryFn: () => employeeApiService.getAll(), // Admin users are employees
        queryKey: ['admin', 'users'],
        staleTime: 5 * 60 * 1000,
      }),
      // Note: Performance metrics now available in reliability dashboard
      // Legacy admin performance metrics kept for backward compatibility
    ]);
  },

  '/admin/audit': () => {
    if (!isAuthReady) {
      console.warn(
        'Authentication not ready, deferring admin audit data prefetch.'
      );
      return Promise.resolve();
    }
    return queryClient.prefetchQuery({
      queryFn: async () => {
        const { adminService } = await import('@/lib/api/services/admin');
        return adminService.getRecentErrors();
      },
      queryKey: ['admin', 'audit'],
      staleTime: 1 * 60 * 1000, // 1 minute for audit logs
    });
  },

  // Reliability dashboard routes - New comprehensive monitoring
  '/reliability': () => {
    if (!isAuthReady) {
      console.warn(
        'Authentication not ready, deferring reliability data prefetch.'
      );
      return Promise.resolve();
    }
    return Promise.all([
      queryClient.prefetchQuery({
        queryFn: async () => {
          const { reliabilityApiService } = await import(
            '../../lib/api/services/apiServiceFactory'
          );
          // Prefetch system health data
          return reliabilityApiService.getSystemHealth();
        },
        queryKey: ['reliability', 'health'],
        staleTime: 15 * 1000, // 15 seconds for real-time data
      }),
      queryClient.prefetchQuery({
        queryFn: async () => {
          const { reliabilityApiService } = await import(
            '../../lib/api/services/apiServiceFactory'
          );
          // Prefetch circuit breaker data
          return reliabilityApiService.getCircuitBreakerStatus();
        },
        queryKey: ['reliability', 'circuit-breakers'],
        staleTime: 30 * 1000, // 30 seconds for circuit breaker data
      }),
    ]);
  },

  '/admin/users': () => {
    if (!isAuthReady) {
      console.warn(
        'Authentication not ready, deferring admin users data prefetch.'
      );
      return Promise.resolve();
    }
    return queryClient.prefetchQuery({
      queryFn: () => employeeApiService.getAll(),
      queryKey: ['admin', 'users'],
      staleTime: 5 * 60 * 1000,
    });
  },

  // Delegation routes
  '/delegations': () => {
    if (!isAuthReady) {
      console.warn(
        'Authentication not ready, deferring delegations data prefetch.'
      );
      return Promise.resolve();
    }
    return Promise.all([
      queryClient.prefetchQuery({
        queryFn: () => delegationApiService.getAll(),
        queryKey: ['delegations'],
        staleTime: 5 * 60 * 1000,
      }),
      queryClient.prefetchQuery({
        queryFn: () => employeeApiService.getAll(),
        queryKey: ['employees'],
        staleTime: 10 * 60 * 1000,
      }),
      queryClient.prefetchQuery({
        queryFn: () => vehicleApiService.getAll(),
        queryKey: ['vehicles'],
        staleTime: 10 * 60 * 1000,
      }),
    ]);
  },

  '/delegations/add': () => {
    if (!isAuthReady) {
      console.warn(
        'Authentication not ready, deferring delegation add data prefetch.'
      );
      return Promise.resolve();
    }
    return Promise.all([
      queryClient.prefetchQuery({
        queryFn: () => employeeApiService.getAll(),
        queryKey: ['employees'],
        staleTime: 10 * 60 * 1000,
      }),
      queryClient.prefetchQuery({
        queryFn: () => vehicleApiService.getAll(),
        queryKey: ['vehicles'],
        staleTime: 10 * 60 * 1000,
      }),
    ]);
  },

  // Employee routes
  '/employees': () => {
    if (!isAuthReady) {
      console.warn(
        'Authentication not ready, deferring employees data prefetch.'
      );
      return Promise.resolve();
    }
    return queryClient.prefetchQuery({
      queryFn: () => employeeApiService.getAll(),
      queryKey: ['employees'],
      staleTime: 10 * 60 * 1000,
    });
  },

  '/employees/new': () => {
    if (!isAuthReady) {
      console.warn(
        'Authentication not ready, deferring employees new data prefetch.'
      );
      return Promise.resolve();
    }
    return queryClient.prefetchQuery({
      queryFn: () => employeeApiService.getAll(),
      queryKey: ['employees'],
      staleTime: 10 * 60 * 1000,
    });
  },

  '/supabase-diagnostics': () => {
    if (!isAuthReady) {
      console.warn(
        'Authentication not ready, deferring diagnostics data prefetch.'
      );
      return Promise.resolve();
    }
    return queryClient.prefetchQuery({
      queryFn: async () => {
        // Diagnostics now redirect to reliability dashboard
        // Keep minimal prefetch for backward compatibility
        return { redirectTo: '/reliability' };
      },
      queryKey: ['admin', 'diagnostics'],
      staleTime: 30 * 1000, // 30 seconds for diagnostics
    });
  },

  // Task routes
  '/tasks': () => prefetchUtils.prefetchTaskManagementData(isAuthReady),

  '/tasks/new': () => prefetchUtils.prefetchTaskManagementData(isAuthReady),

  // Vehicle routes
  '/vehicles': () => {
    if (!isAuthReady) {
      console.warn(
        'Authentication not ready, deferring vehicles data prefetch.'
      );
      return Promise.resolve();
    }
    return queryClient.prefetchQuery({
      queryFn: () => vehicleApiService.getAll(),
      queryKey: ['vehicles'],
      staleTime: 10 * 60 * 1000,
    });
  },

  '/vehicles/new': () => {
    if (!isAuthReady) {
      console.warn(
        'Authentication not ready, deferring vehicles new data prefetch.'
      );
      return Promise.resolve();
    }
    return Promise.all([
      queryClient.prefetchQuery({
        queryFn: () => vehicleApiService.getAll(),
        queryKey: ['vehicles'],
        staleTime: 10 * 60 * 1000,
      }),
    ]);
  },
});

/**
 * Hook for intelligent navigation prefetching
 * Prefetches data for routes before user navigates to them
 */
export const useNavigationPrefetch = () => {
  const router = useRouter();
  const { isInitialized, loading } = useAuthContext();

  // Determine if authentication system is ready for API calls
  const isAuthReady = isInitialized && !loading;

  /**
   * Prefetch data for a specific route
   */
  const prefetchRoute = useCallback(
    async (route: string) => {
      try {
        // Get authentication-aware prefetch patterns
        const PREFETCH_PATTERNS = createPrefetchPatterns(isAuthReady);

        // Find exact match first
        const exactPattern =
          PREFETCH_PATTERNS[route as keyof typeof PREFETCH_PATTERNS];
        if (exactPattern) {
          await exactPattern();
          return;
        }

        // Check for dynamic routes (e.g., /vehicles/[id])
        if (route.includes('/vehicles/') && route !== '/vehicles/new') {
          const vehicleId = route.split('/vehicles/')[1]?.split('/')[0];
          if (vehicleId && !isNaN(Number(vehicleId))) {
            await prefetchUtils.prefetchVehicleDetails(
              Number(vehicleId),
              isAuthReady
            );
          }
          return;
        }

        // Check for edit routes
        if (route.includes('/edit')) {
          const basePath = route.split('/edit')[0];
          const basePattern =
            PREFETCH_PATTERNS[basePath as keyof typeof PREFETCH_PATTERNS];
          if (basePattern) {
            await basePattern();
          }
          return;
        }

        // Fallback: prefetch common data for unknown routes
        console.log(
          `No specific prefetch pattern for route: ${route}, using fallback`
        );
      } catch (error) {
        console.warn(`Failed to prefetch data for route ${route}:`, error);
      }
    },
    [isAuthReady]
  );

  /**
   * Enhanced navigation with prefetching
   */
  const navigateWithPrefetch = useCallback(
    async (route: string) => {
      // Start prefetching immediately
      const prefetchPromise = prefetchRoute(route);

      // Navigate immediately (don't wait for prefetch)
      router.push(route);

      // Let prefetch complete in background
      prefetchPromise.catch(error => {
        console.warn(`Background prefetch failed for ${route}:`, error);
      });
    },
    [router, prefetchRoute]
  );

  /**
   * Prefetch on hover (for link components)
   */
  const handleLinkHover = useCallback(
    (route: string) => {
      // Debounce hover prefetching to avoid excessive requests
      const timeoutId = setTimeout(() => {
        prefetchRoute(route);
      }, 100);

      return () => clearTimeout(timeoutId);
    },
    [prefetchRoute]
  );

  /**
   * Prefetch multiple routes (for anticipated user journeys)
   */
  const prefetchUserJourney = useCallback(
    async (routes: readonly string[]) => {
      const prefetchPromises = routes.map(route => prefetchRoute(route));
      await Promise.allSettled(prefetchPromises);
    },
    [prefetchRoute]
  );

  return {
    handleLinkHover,
    navigateWithPrefetch,
    prefetchRoute,
    prefetchUserJourney,
  };
};

/**
 * Common user journey patterns for bulk prefetching
 */
export const USER_JOURNEYS = {
  // Admin workflow
  ADMIN_WORKFLOW: [
    '/admin',
    '/admin/users',
    '/admin/audit',
    '/supabase-diagnostics',
  ],

  // Dashboard to detail views
  DASHBOARD_DRILL_DOWN: [
    '/',
    '/vehicles',
    '/tasks',
    '/delegations',
    '/employees',
  ],

  // Delegation workflow
  DELEGATION_WORKFLOW: [
    '/delegations',
    '/delegations/add',
    '/employees',
    '/vehicles',
  ],

  // Task management workflow
  TASK_MANAGEMENT: ['/tasks', '/tasks/new', '/employees'],

  // Vehicle management workflow
  VEHICLE_MANAGEMENT: ['/vehicles', '/vehicles/new'],
} as const;

/**
 * Hook for prefetching common user journeys
 */
export const useJourneyPrefetch = () => {
  const { prefetchUserJourney } = useNavigationPrefetch();

  const prefetchVehicleManagement = useCallback(() => {
    return prefetchUserJourney(USER_JOURNEYS.VEHICLE_MANAGEMENT);
  }, [prefetchUserJourney]);

  const prefetchTaskManagement = useCallback(() => {
    return prefetchUserJourney(USER_JOURNEYS.TASK_MANAGEMENT);
  }, [prefetchUserJourney]);

  const prefetchDelegationWorkflow = useCallback(() => {
    return prefetchUserJourney(USER_JOURNEYS.DELEGATION_WORKFLOW);
  }, [prefetchUserJourney]);

  const prefetchDashboardDrillDown = useCallback(() => {
    return prefetchUserJourney(USER_JOURNEYS.DASHBOARD_DRILL_DOWN);
  }, [prefetchUserJourney]);

  const prefetchAdminWorkflow = useCallback(() => {
    return prefetchUserJourney(USER_JOURNEYS.ADMIN_WORKFLOW);
  }, [prefetchUserJourney]);

  return {
    prefetchAdminWorkflow,
    prefetchDashboardDrillDown,
    prefetchDelegationWorkflow,
    prefetchTaskManagement,
    prefetchVehicleManagement,
  };
};

const getRecentErrors = () => {
  return [];
};
