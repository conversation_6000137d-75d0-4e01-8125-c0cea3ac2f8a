/**
 * @file Domain Hooks Index
 * @description Centralized exports for all domain-specific hooks
 */

// Delegation domain hooks
export { useDelegationInfo } from './useDelegationInfo';

// Dashboard domain hooks (moved from lib/hooks)
export { useDashboardStore } from './useDashboardStore';

// Future domain hooks can be added here:
// export { useTaskInfo } from './useTaskInfo';
// export { useVehicleInfo } from './useVehicleInfo';
// export { useEmployeeInfo } from './useEmployeeInfo';
