/**
 * @file Zustand store for global application UI state.
 * This store manages UI-related states such as sidebar visibility, theme, and notifications.
 * @module stores/zustand/appStore
 */

import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';

/**
 * Interface for a notification object.
 */
export interface Notification {
  actionUrl?: string; // Optional URL for notification actions
  category?: 'delegation' | 'employee' | 'system' | 'task' | 'vehicle';
  id: string;
  message: string;
  read: boolean;
  timestamp: string;
  type:
    | 'delegation-update'
    | 'employee-update'
    | 'error'
    | 'info'
    | 'success'
    | 'task-assigned'
    | 'vehicle-maintenance'
    | 'warning';
}

/**
 * Interface for the AppStore state and actions.
 */
interface AppStore {
  addNotification: (
    notification: Omit<Notification, 'id' | 'read' | 'timestamp'>
  ) => void;
  clearAllNotifications: () => void;
  currentTheme: 'dark' | 'light';

  markNotificationAsRead: (id: string) => void;
  notifications: Notification[];
  removeNotification: (id: string) => void;
  setTheme: (theme: 'dark' | 'light') => void;
  // UI State
  sidebarOpen: boolean;
  // Actions
  toggleSidebar: () => void;

  // Computed values (selectors can be derived directly from state in components or using middleware)
  unreadNotificationCount: () => number;
}

/**
 * Zustand store for managing global application UI state.
 * Enhanced with persistence and devtools for better development experience.
 */
export const useAppStore = create<AppStore>()(
  devtools(
    persist(
      (set, get) => ({
        addNotification: (
          notification: Omit<Notification, 'id' | 'read' | 'timestamp'>
        ) =>
          set(state => ({
            notifications: [
              ...state.notifications,
              {
                ...notification,
                id: crypto.randomUUID(),
                read: false,
                timestamp: new Date().toISOString(),
              },
            ],
          })),
        clearAllNotifications: () => set({ notifications: [] }),
        currentTheme: 'light', // Always start with light to avoid hydration mismatch

        markNotificationAsRead: (id: string) =>
          set(state => ({
            notifications: state.notifications.map(n =>
              n.id === id ? { ...n, read: true } : n
            ),
          })),

        notifications: [],

        removeNotification: (id: string) =>
          set(state => ({
            notifications: state.notifications.filter(n => n.id !== id),
          })),

        setTheme: (theme: 'dark' | 'light') => {
          set({ currentTheme: theme });
          // Note: localStorage is handled by persist middleware
        },

        // Initial state
        sidebarOpen: false,

        // Actions
        toggleSidebar: () =>
          set(state => ({ sidebarOpen: !state.sidebarOpen })),

        // Computed values
        unreadNotificationCount: () => {
          const { notifications } = get();
          return notifications.filter(n => !n.read).length;
        },
      }),
      {
        name: 'workhub-app-store', // Storage key
        partialize: state => ({
          currentTheme: state.currentTheme,
          // Don't persist notifications or sidebar state
        }),
      }
    ),
    {
      name: 'app-store', // DevTools name
    }
  )
);
