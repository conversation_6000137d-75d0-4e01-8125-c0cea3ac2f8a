'use client';

import { Car, History } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { useEffect, useState } from 'react';

import type { ServiceRecord, Vehicle } from '@/lib/types/domain';

import { ReportActions } from '@/components/reports/ReportActions';
import { ActionButton } from '@/components/ui/action-button';
import { Button } from '@/components/ui/button';
import { DataLoader, SkeletonLoader } from '@/components/ui/loading';
import { useVehicle } from '@/lib/stores/queries/useVehicles'; // Import useVehicle hook
import { getErrorMessage } from '@/lib/utils/errorHandling'; // Import getErrorMessage

// Helper for dynamic metadata. This ideally is done on server for SEO.
// For client-side dynamic title, we use useEffect.
// export async function generateMetadata({ params }: { params: { id: string } }): Promise<Metadata> {
//   // This won't work here as getVehicleById is client-side.
//   // For true server-side metadata, data fetching must be server-compatible.
//   // const vehicle = getVehicleById(params.id);
//   // return { title: vehicle ? `${vehicle.make} ${vehicle.model} - Report` : 'Vehicle Report' };
//   return { title: 'Vehicle Report' };
// }

export default function VehicleReportPage() {
  const params = useParams();
  const vehicleId = params?.id as string;

  const {
    data: vehicle,
    error,
    isLoading,
    refetch,
  } = useVehicle(Number(vehicleId), {
    enabled: !!vehicleId, // Only fetch if vehicleId is available
  });

  useEffect(() => {
    if (vehicle) {
      // Sort service history for display: oldest first for chronological report
      vehicle.serviceHistory?.sort(
        (a: ServiceRecord, b: ServiceRecord) =>
          new Date(a.date).getTime() - new Date(b.date).getTime() ||
          a.odometer - b.odometer
      );
      document.title = `${vehicle.make} ${vehicle.model} - Maintenance Report`;
    }
  }, [vehicle]);

  const handleRetry = () => {
    refetch();
  };

  return (
    <div className="mx-auto max-w-4xl bg-white p-2 text-gray-800 sm:p-4">
      <DataLoader
        data={vehicle}
        emptyComponent={
          <div className="py-10 text-center">
            Vehicle not found or could not be loaded.
          </div>
        }
        error={getErrorMessage(error)} // Use getErrorMessage
        isLoading={isLoading}
        loadingComponent={
          <div className="space-y-6">
            <h1 className="text-center text-3xl font-bold text-gray-800">
              Loading Report...
            </h1>
            <SkeletonLoader count={1} variant="card" />
            <SkeletonLoader className="mt-6" count={3} variant="table" />
          </div>
        }
        onRetry={handleRetry}
      >
        {loadedVehicle => (
          <>
            <div className="no-print mb-4 flex items-center justify-between">
              <ActionButton
                actionType="tertiary"
                asChild
                icon={<History className="size-4" />}
              >
                <Link href={`/vehicles/${vehicleId}/report/service-history`}>
                  Detailed Service History
                </Link>
              </ActionButton>

              <ReportActions
                enableCsv={(loadedVehicle.serviceHistory?.length || 0) > 0}
                entityId={vehicleId}
                fileName={`vehicle-report-${loadedVehicle.make}-${loadedVehicle.model}`}
                reportContentId="#vehicle-report-content"
                reportType="vehicle"
                tableId="#service-history-table"
              />
            </div>

            <div className="report-content" id="vehicle-report-content">
              <header className="report-header mb-8 border-b-2 border-gray-300 pb-4 text-center">
                <h1 className="text-3xl font-bold text-gray-800">
                  Maintenance Report
                </h1>
                <p className="text-xl text-gray-600">
                  {loadedVehicle.make} {loadedVehicle.model} (
                  {loadedVehicle.year})
                </p>
                {loadedVehicle.licensePlate && (
                  <p className="text-md text-gray-500">
                    Plate: {loadedVehicle.licensePlate}
                  </p>
                )}
              </header>

              <section className="card-print mb-8 rounded border border-gray-200 p-4">
                <h2 className="mb-4 border-b border-gray-200 pb-2 text-2xl font-semibold text-gray-700">
                  Vehicle Details
                </h2>
                <div className="grid grid-cols-1 gap-4 text-sm md:grid-cols-2">
                  <div>
                    <strong>Make:</strong> {loadedVehicle.make}
                  </div>
                  <div>
                    <strong>Model:</strong> {loadedVehicle.model}
                  </div>
                  <div>
                    <strong>Year:</strong> {loadedVehicle.year}
                  </div>
                  {loadedVehicle.licensePlate && (
                    <div>
                      <strong>Plate Number:</strong>{' '}
                      {loadedVehicle.licensePlate}
                    </div>
                  )}
                  {loadedVehicle.color && (
                    <div>
                      <strong>Color:</strong> {loadedVehicle.color}
                    </div>
                  )}
                  <div>
                    <strong>Initial Odometer:</strong>{' '}
                    {loadedVehicle.initialOdometer !== null &&
                    loadedVehicle.initialOdometer !== undefined
                      ? `${loadedVehicle.initialOdometer.toLocaleString()} miles`
                      : 'Not recorded'}
                  </div>
                </div>
                {loadedVehicle.imageUrl && (
                  <div className="no-print relative mx-auto mt-4 aspect-[16/9] w-full max-w-md overflow-hidden rounded">
                    <Image
                      alt={`${loadedVehicle.make} ${loadedVehicle.model}`}
                      data-ai-hint="car side"
                      layout="fill"
                      objectFit="contain"
                      src={loadedVehicle.imageUrl}
                    />
                  </div>
                )}
              </section>

              <section className="card-print rounded border border-gray-200 p-4">
                <div className="mb-4 flex items-center justify-between border-b border-gray-200 pb-2">
                  <h2 className="text-2xl font-semibold text-gray-700">
                    Service History
                  </h2>
                  <p className="no-print text-sm text-gray-500">
                    <Link
                      className="text-blue-600 hover:underline"
                      href={`/vehicles/${vehicleId}/report/service-history`}
                    >
                      View detailed service history
                    </Link>
                  </p>
                </div>
                {loadedVehicle.serviceHistory?.length === 0 ? (
                  <p className="text-gray-500">
                    No service records available for this vehicle.
                  </p>
                ) : (
                  <table
                    className="w-full text-left text-sm text-gray-600"
                    id="service-history-table"
                  >
                    <thead className="bg-gray-50 text-xs uppercase text-gray-700">
                      <tr>
                        <th className="px-3 py-2" scope="col">
                          Date
                        </th>
                        <th className="px-3 py-2" scope="col">
                          Odometer
                        </th>
                        <th className="px-3 py-2" scope="col">
                          Service Performed
                        </th>
                        <th className="px-3 py-2" scope="col">
                          Notes
                        </th>
                        <th className="px-3 py-2 text-right" scope="col">
                          Cost
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {loadedVehicle.serviceHistory?.map(record => (
                        <tr
                          className="border-b bg-white hover:bg-gray-50"
                          key={record.id}
                        >
                          <td className="px-3 py-2">
                            {new Date(record.date).toLocaleDateString()}
                          </td>
                          <td className="px-3 py-2">
                            {record.odometer.toLocaleString()}
                          </td>
                          <td className="px-3 py-2">
                            {record.servicePerformed.join(', ')}
                          </td>
                          <td className="px-3 py-2">{record.notes || '-'}</td>
                          <td className="px-3 py-2 text-right">
                            {record.cost
                              ? `$${Number(record.cost).toFixed(2)}`
                              : '-'}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                )}
              </section>

              <footer className="report-footer mt-12 border-t-2 border-gray-300 pt-4 text-center text-xs text-gray-500">
                <p>Report generated on: {new Date().toLocaleDateString()}</p>
                <p>WorkHub</p>
              </footer>
            </div>
          </>
        )}
      </DataLoader>
    </div>
  );
}
