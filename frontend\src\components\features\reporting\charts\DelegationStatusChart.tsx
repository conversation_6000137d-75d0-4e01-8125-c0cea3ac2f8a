// frontend/src/components/features/reporting/charts/DelegationStatusChart.tsx

import React, { useMemo } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell,
  ResponsiveContainer,
  Tooltip,
  Legend,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  TooltipProps,
  LegendProps,
} from 'recharts';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { <PERSON><PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { StatusDistributionData } from '../data/types/reporting';
import { LoadingSkeleton, ErrorDisplay, NoDataDisplay } from './shared/ChartStateComponents';

/**
 * Props for DelegationStatusChart component
 *
 * Follows Interface Segregation Principle by defining focused props
 */
interface DelegationStatusChartProps {
  data: StatusDistributionData[];
  loading?: boolean;
  error?: string | undefined; // FIXED: Allow undefined for strict mode
  height?: number;
  showLegend?: boolean;
  interactive?: boolean;
  className?: string;
}

/**
 * Custom tooltip component for status chart
 *
 * Follows SRP: Only responsible for rendering tooltip content
 */
const CustomTooltip = ({ active, payload }: TooltipProps<number, string>) => {
  const data = payload?.[0]?.payload;

  if (active && data) {
    return (
      <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
        <p className="font-semibold text-gray-900">{data.status}</p>
        <p className="text-sm text-gray-600">
          Count: <span className="font-medium">{data.count}</span>
        </p>
        <p className="text-sm text-gray-600">
          Percentage:{' '}
          <span className="font-medium">{data.percentage.toFixed(1)}%</span>
        </p>
      </div>
    );
  }

  return null;
};

/**
 * Custom legend component with interactive features
 *
 * Follows SRP: Only responsible for rendering legend
 */
const CustomLegend = ({ payload, onLegendClick }: LegendProps & { onLegendClick?: (entry: any) => void }) => {
  return (
    <div className="flex flex-wrap justify-center gap-4 mt-4">
      {payload?.map((entry: any, index: number) => (
        <div
          key={`legend-${index}`}
          className="flex items-center gap-2 cursor-pointer hover:opacity-80 transition-opacity"
          onClick={() => onLegendClick?.(entry)}
        >
          <div
            className="w-3 h-3 rounded-full"
            style={{ backgroundColor: entry.color }}
          />
          <span className="text-sm text-gray-700">{entry.value}</span>
        </div>
      ))}
    </div>
  );
};

/**
 * Loading skeleton component
 *
 * Follows SRP: Only responsible for loading state display
 */


/**
 * DelegationStatusChart Component
 *
 * A comprehensive chart component for displaying delegation status distribution
 * with both pie and bar chart views, following SOLID principles.
 *
 * Features:
 * - Dual view modes (pie chart and bar chart)
 * - Interactive tooltips and legends
 * - Responsive design
 * - Loading and error states
 * - Accessibility support
 *
 * @param props - Component props
 * @returns JSX element
 */
export const DelegationStatusChart: React.FC<DelegationStatusChartProps> = ({
  data,
  loading = false,
  error,
  height = 400,
  showLegend = true,
  interactive = true,
  className = '',
}) => {
  // Memoize processed data for performance
  const processedData = useMemo(() => {
    return data.map((item, index) => ({
      ...item,
      // Ensure consistent color assignment
      color: item.color || `hsl(${(index * 360) / data.length}, 70%, 50%)`,
    }));
  }, [data]);

  // Handle legend click for interactivity
  const handleLegendClick = (entry: any) => {
    if (interactive) {
      // Could implement filtering or highlighting logic here
      console.log('Legend clicked:', entry);
    }
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>Delegation Status Distribution</CardTitle>
          <CardDescription>Loading chart data...</CardDescription>
        </CardHeader>
        <CardContent>
          <LoadingSkeleton height={height} />
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>Delegation Status Distribution</CardTitle>
          <CardDescription>Chart data unavailable</CardDescription>
        </CardHeader>
        <CardContent>
          <ErrorDisplay error={error} />
        </CardContent>
      </Card>
    );
  }

    if (!data || data.length === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>Delegation Status Distribution</CardTitle>
          <CardDescription>No data available</CardDescription>
        </CardHeader>
        <CardContent>
          <NoDataDisplay message="No delegation data to display" />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>Delegation Status Distribution</CardTitle>
        <CardDescription>
          Overview of delegation statuses across your organization
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="pie" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="pie">Pie Chart</TabsTrigger>
            <TabsTrigger value="bar">Bar Chart</TabsTrigger>
          </TabsList>

          <TabsContent value="pie" className="mt-4">
            <ResponsiveContainer width="100%" height={height}>
              <PieChart>
                <Pie
                  data={processedData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ percentage }) => `${percentage.toFixed(1)}%`}
                  outerRadius={120}
                  fill="#8884d8"
                  dataKey="count"
                  animationBegin={0}
                  animationDuration={800}
                >
                  {processedData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                {interactive && <Tooltip content={<CustomTooltip />} />}
                {showLegend && (
                  <Legend
                    content={<CustomLegend onLegendClick={handleLegendClick} />}
                  />
                )}
              </PieChart>
            </ResponsiveContainer>
          </TabsContent>

          <TabsContent value="bar" className="mt-4">
            <ResponsiveContainer width="100%" height={height}>
              <BarChart
                data={processedData}
                margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="status"
                  angle={-45}
                  textAnchor="end"
                  height={80}
                  fontSize={12}
                />
                <YAxis />
                {interactive && <Tooltip content={<CustomTooltip />} />}
                <Bar dataKey="count" fill="#8884d8" animationDuration={800}>
                  {processedData.map((entry, index) => (
                    <Cell key={`bar-cell-${index}`} fill={entry.color} />
                  ))}
                </Bar>
              </BarChart>
            </ResponsiveContainer>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};
