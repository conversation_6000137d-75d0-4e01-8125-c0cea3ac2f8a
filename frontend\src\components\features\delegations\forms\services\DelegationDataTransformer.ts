/**
 * Delegation Data Transformer - Data Transformation Layer
 *
 * Handles data transformation between API and form formats for delegation operations.
 * Provides clean separation between external API contracts and internal form data structures.
 *
 * @module DelegationDataTransformer
 */

import type { DelegationFormData } from '@/lib/schemas/delegationSchemas';
import type {
  Delegation,
  Employee,
  FlightDetails,
  Vehicle,
} from '@/lib/types/domain';

// ============================================================================
// INTERFACES
// ============================================================================

export interface DelegationApiRequest {
  // Additional request fields
  delegates: any[];
  driverEmployeeIds: number[];
  durationFrom: string;
  durationTo: string;
  escortEmployeeIds: number[];
  eventLocation: string;
  eventName: string;
  flightArrivalDetails?: ProcessedFlightDetails | undefined;
  flightDepartureDetails?: ProcessedFlightDetails | undefined;
  status: string;
  vehicleIds: number[];
}

export interface DelegationApiResponse {
  // Additional API fields
  delegates?: any[];
  drivers?: any[];
  durationFrom: string;
  durationTo: string;
  escorts?: any[];
  eventLocation: string;
  eventName: string;
  flightDetails?: any;
  id: number;
  notes?: string;
  status: string;
  vehicles?: any[];
}

export interface EmployeeAssignment {
  assignedAt: string;
  employeeId: number;
  role: string;
}

export interface EmployeeAssignment {
  assignedAt: string;
  employeeId: number;
  permissions?: string[];
  role: string;
  status?: string;
}

export interface EmployeeAssignment {
  assignedAt: string;
  employeeId: number;
  permissions?: string[];
  role: string;
  status?: string;
}

// ============================================================================
// SERVICE INTERFACE
// ============================================================================

export interface IDelegationDataTransformer {
  // API transformation
  fromApi(apiData: DelegationApiResponse): DelegationFormData;
  // Data normalization
  normalizeFormData(data: DelegationFormData): DelegationFormData;

  processFlightDateTime(dateTime: string): string;
  sanitizeApiData(data: DelegationApiResponse): DelegationApiResponse;

  toApi(formData: DelegationFormData): DelegationApiRequest;
  // Employee/Vehicle transformation
  transformEmployeeAssignments(employees: Employee[]): EmployeeAssignment[];

  // Flight details transformation
  transformFlightDetails(details: FlightDetails): ProcessedFlightDetails;
  transformVehicleAssignments(vehicles: Vehicle[]): VehicleAssignment[];

  // Validation helpers
  validateApiResponse(data: any): data is DelegationApiResponse;
  validateFormData(data: any): data is DelegationFormData;
}

export interface ProcessedFlightDetails {
  airport: string;
  airportCode?: string;
  dateTime: string;
  flightNumber: string;
  // Additional processed fields
  formattedDateTime?: string;
  isProcessed?: boolean;
  notes?: string;
  terminal?: string;
}

export interface VehicleAssignment {
  assignedAt: string;
  estimatedUsage?: {
    endTime: string;
    estimatedMileage: number;
    startTime: string;
  };
  purpose?: string;
  status?: string;
  vehicleId: number;
}

// ============================================================================
// IMPLEMENTATION
// ============================================================================

/**
 * Delegation Data Transformer Implementation
 *
 * Provides data transformation between API and form formats.
 * This is a placeholder implementation that will be fully developed in Phase 2.
 */
export class DelegationDataTransformer implements IDelegationDataTransformer {
  // API transformation methods
  fromApi(apiData: DelegationApiResponse): DelegationFormData {
    return {
      delegates: this.transformApiDelegates(apiData.delegates || []),
      driverEmployeeIds: this.extractEmployeeIds(
        apiData.drivers || [],
        'driver'
      ),
      durationFrom: apiData.durationFrom || '',
      durationTo: apiData.durationTo || '',
      escortEmployeeIds: this.extractEmployeeIds(
        apiData.escorts || [],
        'escort'
      ),
      eventName: apiData.eventName || '',
      flightArrivalDetails: this.transformApiFlightDetails(
        apiData.flightDetails?.arrival
      ),
      flightDepartureDetails: this.transformApiFlightDetails(
        apiData.flightDetails?.departure
      ),
      location: apiData.eventLocation || '',
      notes: apiData.notes || undefined,
      status: this.mapApiStatusToFormStatus(apiData.status),
      vehicleIds: this.extractVehicleIds(apiData.vehicles || []),
    };
  }

  // Data normalization
  normalizeFormData(data: DelegationFormData): DelegationFormData {
    return {
      ...data,
      // Normalize arrays
      delegates: Array.isArray(data.delegates) ? data.delegates : [],
      driverEmployeeIds: Array.isArray(data.driverEmployeeIds)
        ? data.driverEmployeeIds
        : [],
      // Normalize dates
      durationFrom: data.durationFrom?.trim() || '',
      durationTo: data.durationTo?.trim() || '',
      escortEmployeeIds: Array.isArray(data.escortEmployeeIds)
        ? data.escortEmployeeIds
        : [],
      // Normalize string fields
      eventName: data.eventName?.trim() || '',
      location: data.location?.trim() || '',
      notes: data.notes?.trim() || undefined,
      // Normalize status
      status: data.status || 'Planned',
      vehicleIds: Array.isArray(data.vehicleIds) ? data.vehicleIds : [],
    };
  }

  processFlightDateTime(dateTime: string): string {
    if (!dateTime) return '';

    try {
      // Ensure consistent ISO format
      const date = new Date(dateTime);
      if (isNaN(date.getTime())) {
        return dateTime; // Return original if invalid
      }
      return date.toISOString();
    } catch {
      return dateTime; // Return original if processing fails
    }
  }

  sanitizeApiData(data: DelegationApiResponse): DelegationApiResponse {
    // Remove any potentially harmful or unnecessary fields
    const sanitized = { ...data };

    // Remove internal fields that shouldn't be exposed
    delete (sanitized as any).__internal;
    delete (sanitized as any).__meta;
    delete (sanitized as any).password;
    delete (sanitized as any).token;

    // Sanitize string fields to prevent XSS
    if (sanitized.eventName) {
      sanitized.eventName = this.sanitizeString(sanitized.eventName);
    }
    if (sanitized.eventLocation) {
      sanitized.eventLocation = this.sanitizeString(sanitized.eventLocation);
    }
    if (sanitized.notes) {
      sanitized.notes = this.sanitizeString(sanitized.notes);
    }

    return sanitized;
  }

  toApi(formData: DelegationFormData): DelegationApiRequest {
    return {
      delegates: this.transformFormDelegates(formData.delegates || []),
      driverEmployeeIds: formData.driverEmployeeIds || [],
      durationFrom: formData.durationFrom,
      durationTo: formData.durationTo,
      escortEmployeeIds: formData.escortEmployeeIds || [],
      eventLocation: formData.location, // Note: API uses eventLocation, form uses location
      eventName: formData.eventName,
      flightArrivalDetails: formData.flightArrivalDetails
        ? this.transformFlightDetails(formData.flightArrivalDetails)
        : undefined,
      flightDepartureDetails: formData.flightDepartureDetails
        ? this.transformFlightDetails(formData.flightDepartureDetails)
        : undefined,
      status: this.mapFormStatusToApiStatus(formData.status),
      vehicleIds: formData.vehicleIds || [],
    };
  }

  // Employee/Vehicle transformation
  transformEmployeeAssignments(employees: Employee[]): EmployeeAssignment[] {
    return employees.map(emp => ({
      assignedAt: new Date().toISOString(),
      employeeId: emp.id,
      permissions: this.getDefaultPermissionsForRole(emp.role || 'STAFF'),
      role: emp.role || 'STAFF', // Default to STAFF role if not specified
      status: 'ASSIGNED', // Default assignment status
    }));
  }

  // Flight details transformation
  transformFlightDetails(details: FlightDetails): ProcessedFlightDetails {
    // TODO: Implement flight details transformation
    const result: ProcessedFlightDetails = {
      airport: details.airport || '',
      dateTime: this.processFlightDateTime(details.dateTime || ''),
      flightNumber: details.flightNumber || '',
      isProcessed: true,
    };

    // Only add optional fields if they have values
    if (details.terminal) {
      result.terminal = details.terminal;
    }
    if (details.notes) {
      result.notes = details.notes;
    }

    return result;
  }

  transformVehicleAssignments(vehicles: Vehicle[]): VehicleAssignment[] {
    return vehicles.map(vehicle => ({
      assignedAt: new Date().toISOString(),
      estimatedUsage: {
        endTime: new Date(Date.now() + 8 * 60 * 60 * 1000).toISOString(), // 8 hours default
        estimatedMileage: 0, // To be updated based on delegation requirements
        startTime: new Date().toISOString(),
      },
      purpose: 'DELEGATION_TRANSPORT', // Default purpose
      status: 'ASSIGNED',
      vehicleId: vehicle.id,
    }));
  }

  // Validation helpers
  validateApiResponse(data: any): data is DelegationApiResponse {
    // TODO: Implement API response validation
    return typeof data === 'object' && data !== null;
  }

  validateFormData(data: any): data is DelegationFormData {
    // TODO: Implement form data validation
    return typeof data === 'object' && data !== null;
  }

  private extractEmployeeIds(employees: any[], role: string): number[] {
    return employees
      .filter(emp => emp.role === role)
      .map(emp => emp.employeeId || emp.id)
      .filter(id => typeof id === 'number');
  }

  private extractVehicleIds(vehicles: any[]): number[] {
    return vehicles
      .map(vehicle => vehicle.vehicleId || vehicle.id)
      .filter(id => typeof id === 'number');
  }

  private getDefaultPermissionsForRole(role: string): string[] {
    switch (role.toUpperCase()) {
      case 'ADMIN': {
        return ['READ', 'WRITE', 'DELETE', 'MANAGE'];
      }
      case 'DRIVER': {
        return ['READ', 'UPDATE_STATUS'];
      }
      case 'MANAGER': {
        return ['READ', 'WRITE', 'MANAGE'];
      }
      case 'STAFF':
      default: {
        return ['READ', 'UPDATE_STATUS'];
      }
    }
  }

  private mapApiStatusToFormStatus(status: string): any {
    // Map API status values to form status values
    const statusMap: Record<string, string> = {
      cancelled: 'Cancelled',
      completed: 'Completed',
      in_progress: 'In_Progress',
      planned: 'Planned',
    };
    return statusMap[status.toLowerCase()] || status;
  }

  private mapFormStatusToApiStatus(status: string): string {
    // Map form status values to API status values
    const statusMap: Record<string, string> = {
      Cancelled: 'cancelled',
      Completed: 'completed',
      In_Progress: 'in_progress',
      Planned: 'planned',
    };
    return statusMap[status] || status.toLowerCase();
  }

  private sanitizeString(str: string): string {
    return str
      .replaceAll(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // Remove script tags
      .replaceAll(/javascript:/gi, '') // Remove javascript: protocols
      .replaceAll(/on\w+\s*=/gi, '') // Remove event handlers
      .trim();
  }

  private transformApiDelegates(apiDelegates: any[]): any[] {
    return apiDelegates.map(delegate => ({
      name: delegate.name || '',
      notes: delegate.notes || undefined,
      title: delegate.title || '',
    }));
  }

  private transformApiFlightDetails(apiFlightDetails: any): any {
    if (!apiFlightDetails) return null;

    return {
      airport: apiFlightDetails.airport || '',
      dateTime: apiFlightDetails.dateTime || '',
      flightNumber: apiFlightDetails.flightNumber || '',
      notes: apiFlightDetails.notes || undefined,
      terminal: apiFlightDetails.terminal || undefined,
    };
  }

  private transformFormDelegates(formDelegates: any[]): any[] {
    return formDelegates.map(delegate => ({
      name: delegate.name || '',
      notes: delegate.notes || null, // API expects null for empty notes
      title: delegate.title || '',
    }));
  }
}

// ============================================================================
// SINGLETON INSTANCE
// ============================================================================

/**
 * Singleton instance of the delegation data transformer
 * Use this instance throughout the application for consistency
 */
export const delegationDataTransformer = new DelegationDataTransformer();

// ============================================================================
// EXPORTS
// ============================================================================

export default delegationDataTransformer;
