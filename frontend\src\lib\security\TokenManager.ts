/**
 * @file Token Management Service - Single Responsibility Principle (SRP)
 * @module lib/security/TokenManager
 *
 * This class handles ONLY token-related operations following SRP principles.
 * It provides a centralized interface for token extraction, validation, and management.
 *
 * SECURITY NOTE: With httpOnly cookies, tokens are NOT accessible via JavaScript.
 * This class primarily serves as a validation and utility layer.
 */

import { jwtDecode } from 'jwt-decode';
import { undefinedToNull } from '../utils/typeHelpers';

export interface TokenPayload {
  sub: string;
  email: string;
  user_role?: string;
  employee_id?: number;
  is_active?: boolean;
  exp: number;
  iat: number;
  // Supabase custom claims structure
  custom_claims?: {
    user_role?: string;
    employee_id?: number;
    is_active?: boolean;
  };
}

export interface TokenValidationResult {
  isValid: boolean;
  isExpired: boolean;
  payload: TokenPayload | null;
  error?: string;
}

/**
 * TokenManager - Single Responsibility: Token Operations Only
 *
 * Handles token extraction, validation, and utility operations.
 * Does NOT handle storage (delegated to SecureStorage) or authentication logic.
 */
export class TokenManager {
  /**
   * Extract token from Authorization header
   * Single responsibility: Token extraction only
   */
  static extractTokenFromHeader(authHeader?: string): string | null {
    if (!authHeader) return null;

    const parts = authHeader.split(' ');
    if (parts.length !== 2 || parts[0] !== 'Bearer') {
      return null;
    }

    return undefinedToNull(parts[1]);
  }

  /**
   * Extract token from cookie string
   * Single responsibility: Cookie token extraction only
   */
  static extractTokenFromCookie(
    cookieString?: string,
    cookieName = 'sb-access-token'
  ): string | null {
    if (!cookieString) return null;

    const cookies: Record<string, string> = {};
    cookieString.split(';').forEach(cookie => {
      const [key, value] = cookie.trim().split('=');
      if (key && value) {
        cookies[key] = decodeURIComponent(value);
      }
    });

    return cookies[cookieName] ?? null;
  }

  /**
   * Validate JWT token structure and expiration
   * Single responsibility: Token validation only
   */
  static validateToken(token: string): TokenValidationResult {
    try {
      if (!token || typeof token !== 'string') {
        return {
          isValid: false,
          isExpired: true,
          payload: null,
          error: 'Invalid token format',
        };
      }

      const payload = jwtDecode<TokenPayload>(token);
      const currentTime = Math.floor(Date.now() / 1000);
      const isExpired = payload.exp < currentTime;

      const result: TokenValidationResult = {
        isValid: !isExpired,
        isExpired,
        payload,
      };

      if (isExpired) {
        result.error = 'Token expired';
      }

      return result;
    } catch (error) {
      return {
        isValid: false,
        isExpired: true,
        payload: null,
        error:
          error instanceof Error ? error.message : 'Token validation failed',
      };
    }
  }

  /**
   * Extract user role from token payload
   * Single responsibility: Role extraction only
   *
   * Checks multiple locations for role data:
   * 1. custom_claims.user_role (Supabase auth hook structure)
   * 2. user_role (direct property)
   * 3. Defaults to 'USER'
   */
  static extractUserRole(token: string): string | null {
    const validation = this.validateToken(token);
    if (!validation.isValid || !validation.payload) {
      return null;
    }

    // Check custom_claims first (Supabase auth hook structure)
    const customClaimsRole = validation.payload.custom_claims?.user_role;
    if (customClaimsRole) {
      return customClaimsRole;
    }

    // Fallback to direct property
    return validation.payload.user_role ?? 'USER';
  }

  /**
   * Extract employee ID from token payload
   * Single responsibility: Employee ID extraction only
   */
  static extractEmployeeId(token: string): number | null {
    const validation = this.validateToken(token);
    if (!validation.isValid || !validation.payload) {
      return null;
    }

    return validation.payload.custom_claims?.employee_id ?? validation.payload.employee_id ?? null;
  }

  /**
   * Check if user is active from token payload
   * Single responsibility: Active status check only
   */
  static isUserActive(token: string): boolean {
    const validation = this.validateToken(token);
    if (!validation.isValid || !validation.payload) {
      return false;
    }

    return validation.payload.custom_claims?.is_active ?? validation.payload.is_active ?? true;
  }

  /**
   * Get token expiration time
   * Single responsibility: Expiration time extraction only
   */
  static getTokenExpiration(token: string): Date | null {
    const validation = this.validateToken(token);
    if (!validation.isValid || !validation.payload) {
      return null;
    }

    return new Date(validation.payload.exp * 1000);
  }

  /**
   * Check if token will expire within specified minutes
   * Single responsibility: Expiration proximity check only
   */
  static willExpireSoon(token: string, minutesThreshold = 5): boolean {
    const expiration = this.getTokenExpiration(token);
    if (!expiration) return true;

    const thresholdTime = new Date(Date.now() + minutesThreshold * 60 * 1000);
    return expiration <= thresholdTime;
  }

  /**
   * Generate a secure hash of token for logging purposes
   * Single responsibility: Token hashing for security logging only
   */
  static hashTokenForLogging(token: string): string {
    if (!token) return 'no-token';

    // Create a simple hash for logging (first 8 chars + last 8 chars)
    if (token.length < 16) return 'short-token';

    return `${token.substring(0, 8)}...${token.substring(token.length - 8)}`;
  }

  /**
   * Validate token format without decoding
   * Single responsibility: Format validation only
   */
  static isValidTokenFormat(token: string): boolean {
    if (!token || typeof token !== 'string') return false;

    // JWT tokens have 3 parts separated by dots
    const parts = token.split('.');
    return parts.length === 3 && parts.every(part => part.length > 0);
  }
}