'use client';

import { Check, Type } from 'lucide-react';
import React from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { useUiPreferences } from '@/hooks/ui/useUiPreferences';

/**
 * Font size options with display labels and descriptions
 */
const FONT_SIZE_OPTIONS = [
  {
    className: 'text-sm',
    description: 'Compact text for more content',
    example: 'The quick brown fox jumps over the lazy dog',
    label: 'Small',
    value: 'small' as const,
  },
  {
    className: 'text-base',
    description: 'Standard readable text',
    example: 'The quick brown fox jumps over the lazy dog',
    label: 'Medium',
    value: 'medium' as const,
  },
  {
    className: 'text-lg',
    description: 'Larger text for better accessibility',
    example: 'The quick brown fox jumps over the lazy dog',
    label: 'Large',
    value: 'large' as const,
  },
];

/**
 * Font Size Settings Component
 * Provides UI controls for changing application font size
 */
export const FontSizeSettings: React.FC = () => {
  const { fontSize, getFontSizeClass, setFontSize } = useUiPreferences();

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Type className="size-5" />
          Font Size Preferences
        </CardTitle>
        <CardDescription>
          Choose your preferred font size for better readability and
          accessibility
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Current Selection Display */}
        <div className="flex items-center justify-between rounded-lg bg-muted/50 p-4">
          <div>
            <p className="font-medium">Current Font Size</p>
            <p className="text-sm text-muted-foreground">
              Applied across the entire application
            </p>
          </div>
          <Badge className="capitalize" variant="secondary">
            {fontSize}
          </Badge>
        </div>

        {/* Font Size Options */}
        <div className="space-y-3">
          <h4 className="text-sm font-medium uppercase tracking-wide text-muted-foreground">
            Available Options
          </h4>
          {FONT_SIZE_OPTIONS.map(option => (
            <div
              className={`
                relative cursor-pointer rounded-lg border p-4 transition-all
                ${
                  fontSize === option.value
                    ? 'border-primary bg-primary/5 ring-1 ring-primary/20'
                    : 'border-border hover:border-primary/50 hover:bg-muted/30'
                }
              `}
              key={option.value}
              onClick={() => setFontSize(option.value)}
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="mb-2 flex items-center gap-2">
                    <h5 className="font-medium">{option.label}</h5>
                    {fontSize === option.value && (
                      <Check className="size-4 text-primary" />
                    )}
                  </div>
                  <p className="mb-3 text-sm text-muted-foreground">
                    {option.description}
                  </p>
                  <div
                    className={`rounded border bg-background p-3 ${option.className}`}
                  >
                    {option.example}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Quick Action Buttons */}
        <div className="flex items-center justify-between border-t pt-4">
          <div className="flex gap-2">
            {FONT_SIZE_OPTIONS.map(option => (
              <Button
                className="capitalize"
                key={option.value}
                onClick={() => setFontSize(option.value)}
                size="sm"
                variant={fontSize === option.value ? 'default' : 'outline'}
              >
                {option.label}
              </Button>
            ))}
          </div>
          <Button
            className="text-muted-foreground"
            onClick={() => setFontSize('medium')}
            size="sm"
            variant="ghost"
          >
            Reset to Default
          </Button>
        </div>

        {/* Live Preview */}
        <div className="rounded-lg border bg-background p-4">
          <h5 className="mb-2 font-medium">Live Preview</h5>
          <div className={`space-y-2 ${getFontSizeClass()}`}>
            <p className="font-semibold">Heading Text</p>
            <p>
              This is how regular paragraph text will appear with your selected
              font size. The setting applies to all text content throughout the
              WorkHub application.
            </p>
            <p className="text-muted-foreground">
              Secondary text and descriptions will also scale accordingly.
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

/**
 * Compact Font Size Selector for toolbars or settings panels
 */
export const FontSizeSelector: React.FC = () => {
  const { fontSize, setFontSize } = useUiPreferences();

  return (
    <div className="flex items-center gap-2">
      <Type className="size-4 text-muted-foreground" />
      <select
        className="
          rounded border border-input bg-background px-2 py-1 text-sm
          focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2
        "
        onChange={e =>
          setFontSize(e.target.value as 'large' | 'medium' | 'small')
        }
        value={fontSize}
      >
        <option value="small">Small</option>
        <option value="medium">Medium</option>
        <option value="large">Large</option>
      </select>
    </div>
  );
};

/**
 * Icon-based Font Size Toggle for minimal UI
 */
export const FontSizeToggle: React.FC = () => {
  const { fontSize, setFontSize } = useUiPreferences();

  const cycleSize = () => {
    const sizes: ('large' | 'medium' | 'small')[] = [
      'small',
      'medium',
      'large',
    ];
    const currentIndex = sizes.indexOf(fontSize);
    const nextIndex = (currentIndex + 1) % sizes.length;
    const nextSize = sizes[nextIndex];
    if (nextSize) {
      setFontSize(nextSize);
    }
  };

  return (
    <Button
      className="flex items-center gap-2"
      onClick={cycleSize}
      size="sm"
      title={`Current: ${fontSize}. Click to cycle.`}
      variant="ghost"
    >
      <Type className="size-4" />
      <span className="text-xs capitalize">{fontSize}</span>
    </Button>
  );
};
