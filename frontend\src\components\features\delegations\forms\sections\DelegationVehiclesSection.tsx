/**
 * DelegationVehiclesSection Component - SOLID Principles Implementation
 *
 * Single Responsibility: Handles vehicle selection and management
 * - Multi-select vehicle functionality with filtering
 * - Follows SRP by focusing only on vehicle selection
 *
 * @module DelegationVehiclesSection
 */

import React from 'react';
import { useFormContext } from 'react-hook-form';
import { Truck, Trash2 } from 'lucide-react';

import type { DelegationFormData } from '@/lib/schemas/delegationSchemas';

import { Badge } from '@/components/ui/badge';
import { FormMessage } from '@/components/ui/form';
import { useVehicles } from '@/lib/stores/queries/useVehicles';

// ============================================================================
// INTERFACES
// ============================================================================

export interface DelegationVehiclesSectionProps {
  /** Whether the form is currently submitting */
  isSubmitting?: boolean;
  /** Additional CSS classes */
  className?: string;
}

// ============================================================================
// COMPONENT IMPLEMENTATION
// ============================================================================

/**
 * Vehicles Section for Delegation Form
 *
 * Manages vehicle selection with multi-select functionality and filtering.
 * This component follows SRP by focusing solely on vehicle selection.
 */
export const DelegationVehiclesSection: React.FC<
  DelegationVehiclesSectionProps
> = ({ isSubmitting = false, className = '' }) => {
  const { watch, setValue } = useFormContext<DelegationFormData>();

  // Watch selected vehicles and drivers
  const selectedVehicleIds = watch('vehicleIds') || [];
  const selectedDriverIds = watch('driverEmployeeIds') || [];

  // Fetch available vehicles
  const {
    data: vehicles = [],
    isLoading: vehiclesLoading,
    error: vehiclesError,
  } = useVehicles();

  // Filter out already selected vehicles for the dropdown
  const availableVehicles = vehicles.filter(
    vehicle => !selectedVehicleIds.includes(vehicle.id)
  );

  // Get selected vehicle details
  const selectedVehicles = vehicles.filter(vehicle =>
    selectedVehicleIds.includes(vehicle.id)
  );

  const handleAddVehicle = (vehicleId: number) => {
    if (!selectedVehicleIds.includes(vehicleId)) {
      const newVehicleIds = [...selectedVehicleIds, vehicleId];
      setValue('vehicleIds', newVehicleIds);
    }
  };

  const handleRemoveVehicle = (vehicleId: number) => {
    const newVehicleIds = selectedVehicleIds.filter(id => id !== vehicleId);
    setValue('vehicleIds', newVehicleIds);
  };

  const getVehicleDisplayName = (vehicle: any) => {
    const parts = [];
    if (vehicle.make) parts.push(vehicle.make);
    if (vehicle.model) parts.push(vehicle.model);
    if (vehicle.year) parts.push(`(${vehicle.year})`);
    if (vehicle.licensePlate) parts.push(`- ${vehicle.licensePlate}`);
    return parts.length > 0 ? parts.join(' ') : `Vehicle ${vehicle.id}`;
  };

  return (
    <section className={`space-y-4 rounded-lg border bg-card p-6 ${className}`}>
      <h3 className="flex items-center text-lg font-semibold text-foreground">
        <Truck className="mr-2 size-5 text-accent" />
        Vehicles
      </h3>

      {/* Vehicle Selection Dropdown */}
      <div className="space-y-2">
        <label className="text-sm font-medium text-foreground">
          Select Vehicles{' '}
          {selectedDriverIds.length === 0 && '(Requires Driver)'}
        </label>
        {selectedDriverIds.length === 0 && (
          <p className="text-sm text-muted-foreground">
            Please select at least one driver before adding vehicles.
          </p>
        )}
        <select
          className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
          onChange={e => {
            const vehicleId = parseInt(e.target.value, 10);
            if (vehicleId) {
              handleAddVehicle(vehicleId);
              e.target.value = ''; // Reset selection
            }
          }}
          disabled={
            isSubmitting || vehiclesLoading || selectedDriverIds.length === 0
          }
        >
          <option value="">
            {vehiclesLoading
              ? 'Loading vehicles...'
              : selectedDriverIds.length === 0
                ? 'Select drivers first'
                : 'Select a vehicle to add'}
          </option>
          {selectedDriverIds.length > 0 &&
            availableVehicles.map(vehicle => (
              <option key={vehicle.id} value={vehicle.id}>
                {getVehicleDisplayName(vehicle)}
              </option>
            ))}
        </select>
      </div>

      {/* Selected Vehicles Display */}
      {selectedVehicles.length > 0 && (
        <div className="space-y-2">
          <label className="text-sm font-medium text-foreground">
            Selected Vehicles ({selectedVehicles.length})
          </label>
          <div className="flex flex-wrap gap-2">
            {selectedVehicles.map(vehicle => (
              <Badge
                key={vehicle.id}
                variant="secondary"
                className="inline-flex items-center gap-1 px-3 py-1"
              >
                <div className="flex flex-col">
                  <span className="font-medium">
                    {vehicle.make} {vehicle.model}
                  </span>
                  <span className="text-xs text-muted-foreground">
                    {vehicle.year} • {vehicle.licensePlate || 'No License'}
                  </span>
                </div>
                {!isSubmitting && (
                  <button
                    type="button"
                    onClick={() => handleRemoveVehicle(vehicle.id)}
                    className="ml-1 text-muted-foreground hover:text-destructive"
                    aria-label={`Remove ${getVehicleDisplayName(vehicle)}`}
                  >
                    <Trash2 className="size-3" />
                  </button>
                )}
              </Badge>
            ))}
          </div>
        </div>
      )}

      {/* No vehicles selected message */}
      {selectedVehicles.length === 0 && (
        <p className="text-sm text-muted-foreground">
          No vehicles selected. Select vehicles from the dropdown above.
        </p>
      )}

      {/* No available vehicles message */}
      {!vehiclesLoading && vehicles.length === 0 && (
        <p className="text-sm text-muted-foreground">
          No vehicles available. Please add vehicles to the system first.
        </p>
      )}

      <FormMessage />
    </section>
  );
};

export default DelegationVehiclesSection;
