-- User Profiles <PERSON><PERSON>
-- Generated: 2025-06-18T21:13:17.717Z
-- This script restores the user_profiles table from backup

-- Recreate user_profiles table
CREATE TABLE IF NOT EXISTS public.user_profiles (
    id UUID PRIMARY KEY,
    role TEXT DEFAULT 'USER' CHECK (role IN ('SUPER_ADMIN', 'ADMIN', '<PERSON>NAGE<PERSON>', 'USER', 'READONLY')),
    employee_id INTEGER UNIQUE,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert backup data
INSERT INTO public.user_profiles (id, role, employee_id, is_active, created_at, updated_at) 
VALUES ('85a1fb69-7875-4014-9d25-a65f444dbc73', 'ADMIN', NULL, true, '2025-06-09T11:51:00.104351+00:00', '2025-06-09T11:51:00.104351+00:00');
INSERT INTO public.user_profiles (id, role, employee_id, is_active, created_at, updated_at) 
VALUES ('4b7987fd-a6a8-442f-881c-99d3a01e92b1', 'ADMIN', NULL, true, '2025-06-09T11:57:48.895778+00:00', '2025-06-09T11:57:48.895778+00:00');
INSERT INTO public.user_profiles (id, role, employee_id, is_active, created_at, updated_at) 
VALUES ('ef5132bb-f8ab-47c3-9dca-a259dbc51d85', 'ADMIN', NULL, true, '2025-06-14T21:42:09.496098+00:00', '2025-06-14T21:42:09.496098+00:00');
INSERT INTO public.user_profiles (id, role, employee_id, is_active, created_at, updated_at) 
VALUES ('639fcc6f-8b77-4a4c-962b-dfa10a94441e', 'ADMIN', NULL, true, '2025-06-18T20:37:37.058752+00:00', '2025-06-18T20:37:37.058752+00:00');

-- Verify restore
SELECT COUNT(*) as restored_count FROM public.user_profiles;
SELECT role, COUNT(*) as count FROM public.user_profiles GROUP BY role;
