'use client';

import type React from 'react'; // Removed useEffect, useState, useCallback

import { Edit, PlusCircle, Trash2 } from 'lucide-react';
import { CircleAlert } from 'lucide-react';
import Link from 'next/link';

import type { Vehicle } from '@/lib/types/domain'; // Changed import path

import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
// import { useSocketRefresh } from '@/hooks/useSocketRefresh'; // Removed
// import { SOCKET_EVENTS } from '@/hooks/useSocket'; // Removed
import { usePredefinedEntityToast } from '@/hooks/forms/useFormToast';
// import {
//   getVehicles,
//   deleteVehicle as deleteVehicleFromStore,
// } from '@/lib/store'; // Removed
import {
  useDeleteVehicle,
  useVehicles,
} from '@/lib/stores/queries/useVehicles'; // Added

interface VehicleListProps {
  children: (data: {
    error: null | string; // Error from fetching vehicles
    fetchVehicles: () => void; // Manual refetch trigger
    handleDelete: (id: number) => Promise<void>;
    isRefreshing: boolean; // True if a background refetch is happening
    loading: boolean; // Represents initial fetch loading
    vehicles: Vehicle[];
    // isConnected and socketTriggered are removed as useSocketRefresh is removed
  }) => React.ReactNode;
}

const VehicleListContainer: React.FC<VehicleListProps> = ({ children }) => {
  const { showEntityDeleted, showEntityDeletionError } =
    usePredefinedEntityToast('vehicle');
  const {
    data: vehiclesData,
    error: vehiclesError,
    isFetching: isVehiclesFetching, // To indicate background refresh
    isLoading: isVehiclesLoading,
    refetch: refetchVehicles,
  } = useVehicles();

  const { isPending: isDeletingVehicle, mutateAsync: deleteVehicleMutation } =
    useDeleteVehicle();

  const handleDelete = async (id: number) => {
    if (globalThis.confirm('Are you sure you want to delete this vehicle?')) {
      try {
        await deleteVehicleMutation(id); // useDeleteVehicle expects number ID
        // Note: We need the vehicle object to show proper toast, but since it's deleted,
        // we'll use a generic message. In a real app, you might want to get the vehicle
        // data before deletion to show the proper name.
        showEntityDeleted({ make: 'Vehicle', model: '' }); // Generic fallback
        // No need to manually update local state, React Query handles cache invalidation/update via useDeleteVehicle's onSettled/onSuccess
      } catch (error: any) {
        console.error('Error deleting vehicle:', error);
        showEntityDeletionError(
          error.message || 'Could not delete the vehicle.'
        );
        // Optionally rethrow if the caller needs to handle it, though often not needed with RQ
        // throw err;
      }
    }
  };

  // Note: Real-time updates via sockets (previously useSocketRefresh) would need
  // a different implementation with React Query, e.g., listening to socket events
  // and calling queryClient.invalidateQueries({ queryKey: vehicleQueryKeys.all }).

  return children({
    error: vehiclesError?.message || null,
    fetchVehicles: refetchVehicles,
    handleDelete,
    isRefreshing: isVehiclesFetching, // Indicates background refetches
    loading: isVehiclesLoading, // Initial load state
    vehicles: vehiclesData || [],
    // isConnected and socketTriggered are removed
  });
};

export default VehicleListContainer;
