// ============================================================================
// WorkHub Security Hardening: Token Security Utilities
// Implements secure token management following security best practices
// File: backend/src/utils/tokenSecurity.ts
// ============================================================================

import { createHash, randomBytes, scrypt } from 'crypto';
import { promisify } from 'util';
import type { Request } from 'express';
import { supabaseAdmin } from '../lib/supabase.js';
import { logAuditEvent } from './auditLogger.js';
import logger from './logger.js';

// Promisify scrypt for async usage
const scryptAsync = promisify(scrypt);

/**
 * Security configuration for token management
 */
export const TOKEN_SECURITY_CONFIG = {
  // Refresh token rotation settings
  REFRESH_TOKEN_MAX_AGE: 7 * 24 * 60 * 60 * 1000, // 7 days (reduced from 30)
  ACCESS_TOKEN_MAX_AGE: 15 * 60 * 1000, // 15 minutes

  // Token validation settings
  TOKEN_HASH_LENGTH: 8,
  ROTATION_GRACE_PERIOD: 5 * 60 * 1000, // 5 minutes grace period for rotation

  // Security thresholds
  MAX_REFRESH_ATTEMPTS: 3,
  SUSPICIOUS_ACTIVITY_THRESHOLD: 5,
} as const;

/**
 * Interface for refresh token metadata
 */
interface RefreshTokenMetadata {
  tokenId: string;
  userId: string;
  issuedAt: number;
  expiresAt: number;
  rotationCount: number;
  lastUsed: number;
  deviceFingerprint?: string;
  ipAddress?: string;
}

/**
 * Enhanced token security utilities following SRP
 */
export class TokenSecurityManager {
  /**
   * Generate a secure token hash for logging and tracking
   */
  static generateTokenHash(token: string): string {
    return createHash('sha256')
      .update(token)
      .digest('hex')
      .substring(0, TOKEN_SECURITY_CONFIG.TOKEN_HASH_LENGTH);
  }

  /**
   * Generate a unique token identifier
   */
  static generateTokenId(): string {
    return randomBytes(16).toString('hex');
  }

  /**
   * Validate token format (JWT structure)
   */
  static validateTokenFormat(token: string): boolean {
    if (!token || typeof token !== 'string') {
      return false;
    }

    const parts = token.split('.');
    return parts.length === 3 && parts.every(part => part.length > 0);
  }

  /**
   * Extract device fingerprint from request for token binding
   */
  static generateDeviceFingerprint(req: Request): string {
    const components = [
      req.get('User-Agent') || '',
      req.get('Accept-Language') || '',
      req.get('Accept-Encoding') || '',
      req.ip || '',
    ];

    return createHash('sha256').update(components.join('|')).digest('hex').substring(0, 16);
  }

  /**
   * Validate refresh token and check for suspicious activity
   */
  static async validateRefreshToken(
    refreshToken: string,
    req: Request,
  ): Promise<{ valid: boolean; suspicious: boolean; reason?: string }> {
    try {
      // Validate token format
      if (!this.validateTokenFormat(refreshToken)) {
        return { valid: false, suspicious: false, reason: 'INVALID_FORMAT' };
      }

      // Validate with Supabase
      const { data, error } = await supabaseAdmin.auth.refreshSession({
        refresh_token: refreshToken,
      });

      if (error || !data.session) {
        // Check if this is a suspicious pattern
        const tokenHash = this.generateTokenHash(refreshToken);
        const suspicious = await this.checkSuspiciousActivity(req.ip || '', tokenHash);

        return {
          valid: false,
          suspicious,
          reason: error?.message || 'REFRESH_FAILED',
        };
      }

      return { valid: true, suspicious: false };
    } catch (error) {
      logger.error('Token validation error', {
        error: error instanceof Error ? error.message : String(error),
        service: 'token-security',
      });

      return { valid: false, suspicious: true, reason: 'VALIDATION_ERROR' };
    }
  }

  /**
   * Check for suspicious refresh token activity
   */
  private static async checkSuspiciousActivity(
    ipAddress: string,
    tokenHash: string,
  ): Promise<boolean> {
    // This would typically check against a rate limiting store
    // For now, we'll implement basic suspicious activity detection

    // In a production system, you would:
    // 1. Check Redis/database for recent failed attempts from this IP
    // 2. Check for rapid successive refresh attempts
    // 3. Check for token reuse patterns
    // 4. Implement geolocation-based anomaly detection

    return false; // Placeholder - implement based on your requirements
  }

  /**
   * Rotate refresh token with security logging
   */
  static async rotateRefreshToken(
    currentRefreshToken: string,
    req: Request,
  ): Promise<{
    success: boolean;
    newTokens?: { accessToken: string; refreshToken: string; expiresIn: number };
    error?: string;
  }> {
    try {
      const tokenHash = this.generateTokenHash(currentRefreshToken);

      // Validate current token
      const validation = await this.validateRefreshToken(currentRefreshToken, req);

      if (!validation.valid) {
        // Log suspicious activity
        if (validation.suspicious) {
          logAuditEvent(
            {
              eventType: 'SECURITY',
              action: 'SUSPICIOUS_TOKEN_REFRESH',
              outcome: 'FAILURE',
              message: 'Suspicious refresh token activity detected',
              errorCode: validation.reason || 'UNKNOWN',
              details: {
                tokenHash,
                reason: validation.reason,
                deviceFingerprint: this.generateDeviceFingerprint(req),
              },
              riskLevel: 'HIGH',
            },
            req,
          );
        }

        return { success: false, error: validation.reason || 'INVALID_TOKEN' };
      }

      // Perform token refresh with Supabase
      const { data, error } = await supabaseAdmin.auth.refreshSession({
        refresh_token: currentRefreshToken,
      });

      if (error || !data.session) {
        logAuditEvent(
          {
            eventType: 'AUTH',
            action: 'TOKEN_ROTATION_FAILED',
            outcome: 'FAILURE',
            message: 'Failed to rotate refresh token',
            errorCode: error?.message || 'ROTATION_FAILED',
            details: { tokenHash },
          },
          req,
        );

        return { success: false, error: error?.message || 'ROTATION_FAILED' };
      }

      // Log successful rotation
      logAuditEvent(
        {
          eventType: 'AUTH',
          action: 'TOKEN_ROTATION_SUCCESS',
          outcome: 'SUCCESS',
          message: 'Refresh token rotated successfully',
          userId: data.user?.id,
          details: {
            oldTokenHash: tokenHash,
            newTokenHash: this.generateTokenHash(data.session.refresh_token),
            deviceFingerprint: this.generateDeviceFingerprint(req),
          },
        },
        req,
      );

      return {
        success: true,
        newTokens: {
          accessToken: data.session.access_token,
          refreshToken: data.session.refresh_token,
          expiresIn: data.session.expires_in || 3600,
        },
      };
    } catch (error) {
      logger.error('Token rotation error', {
        error: error instanceof Error ? error.message : String(error),
        service: 'token-security',
      });

      return { success: false, error: 'ROTATION_ERROR' };
    }
  }

  /**
   * Invalidate all refresh tokens for a user (security breach response)
   */
  static async invalidateAllUserTokens(userId: string, reason: string): Promise<void> {
    try {
      // This would typically involve:
      // 1. Marking all user tokens as invalid in your token store
      // 2. Adding the user to a "force re-auth" list
      // 3. Logging the security event

      logAuditEvent({
        eventType: 'SECURITY',
        action: 'ALL_TOKENS_INVALIDATED',
        outcome: 'SUCCESS',
        message: `All tokens invalidated for user: ${reason}`,
        userId,
        details: { reason },
        riskLevel: 'HIGH',
      });

      logger.warn('All tokens invalidated for user', {
        userId,
        reason,
        service: 'token-security',
      });
    } catch (error) {
      logger.error('Failed to invalidate user tokens', {
        error: error instanceof Error ? error.message : String(error),
        userId,
        service: 'token-security',
      });
    }
  }
}

/**
 * Legacy compatibility exports
 */
export const hashToken = TokenSecurityManager.generateTokenHash;
export const validateTokenFormat = TokenSecurityManager.validateTokenFormat;
export const rotateRefreshToken = TokenSecurityManager.rotateRefreshToken;
