/**
 * @file Tests for widget fixes - verifying real API data usage
 * @description Tests to ensure widgets use real API data instead of mock/simulated data
 */

import React from 'react';
import { render } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { DeduplicationMetrics } from '../DeduplicationMetrics';
import { PerformanceOverview } from '../PerformanceOverview';

// Mock the reliability hooks
jest.mock('@/lib/stores/queries/useReliability', () => ({
  usePerformanceMetrics: jest.fn(),
}));

const { usePerformanceMetrics } = require('@/lib/stores/queries/useReliability');

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('Widget Fixes - Real API Data Usage', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('DeduplicationMetrics Widget', () => {
    it('should use real API data for memory savings calculation', () => {
      const mockMetrics = {
        deduplicationMetrics: {
          cacheHits: 800,
          cacheMisses: 200,
          totalRequests: 1000,
          hitRate: 80,
          errors: 5,
          lastReset: '2024-01-01T00:00:00Z',
        },
        httpRequestMetrics: { values: [] },
        systemMetrics: {
          cpu: { usage: 50, loadAverage: [1.0, 1.1, 1.2] },
          memory: { used: **********, total: **********, free: **********, usagePercent: 50 },
          connections: { active: 25, total: 100 },
        },
      };

      usePerformanceMetrics.mockReturnValue({
        data: mockMetrics,
        isLoading: false,
        error: null,
      });

      const { container } = render(
        <TestWrapper>
          <DeduplicationMetrics />
        </TestWrapper>
      );

      // Verify the component renders without errors
      expect(container).toBeTruthy();
      
      // The memory savings should be calculated from real cache hits (800/1000 = 80%)
      // instead of simulated hitRate * 0.8
      expect(usePerformanceMetrics).toHaveBeenCalled();
    });

    it('should handle missing metrics gracefully', () => {
      usePerformanceMetrics.mockReturnValue({
        data: null,
        isLoading: false,
        error: null,
      });

      const { container } = render(
        <TestWrapper>
          <DeduplicationMetrics />
        </TestWrapper>
      );

      expect(container).toBeTruthy();
    });
  });

  describe('PerformanceOverview Widget', () => {
    it('should use real HTTP metrics for response time calculation', () => {
      const mockMetrics = {
        deduplicationMetrics: {
          cacheHits: 900,
          cacheMisses: 100,
          totalRequests: 1000,
          hitRate: 90,
          errors: 2,
          lastReset: '2024-01-01T00:00:00Z',
        },
        httpRequestMetrics: {
          name: 'http_request_duration_seconds',
          help: 'HTTP request duration',
          type: 'histogram',
          values: [
            { labels: { method: 'GET', route: '/api/test', statusCode: '200', userRole: 'user' }, value: 0.150 },
            { labels: { method: 'POST', route: '/api/data', statusCode: '201', userRole: 'admin' }, value: 0.250 },
            { labels: { method: 'GET', route: '/api/health', statusCode: '200', userRole: 'system' }, value: 0.050 },
          ],
        },
        systemMetrics: {
          cpu: { usage: 40, loadAverage: [0.7, 0.8, 0.9] },
          memory: { used: **********, total: **********, free: **********, usagePercent: 40 },
          connections: { active: 20, total: 100 },
        },
      };

      usePerformanceMetrics.mockReturnValue({
        data: mockMetrics,
        isLoading: false,
        error: null,
      });

      const { container } = render(
        <TestWrapper>
          <PerformanceOverview />
        </TestWrapper>
      );

      // Response time should be calculated from real HTTP metrics:
      // Average of [0.150, 0.250, 0.050] = 0.150 seconds = 150ms
      // instead of 150 + (cpu * 5)
      expect(container).toBeTruthy();
      expect(usePerformanceMetrics).toHaveBeenCalled();
    });

    it('should handle API errors gracefully', () => {
      usePerformanceMetrics.mockReturnValue({
        data: null,
        isLoading: false,
        error: { message: 'API Error' },
      });

      const { container } = render(
        <TestWrapper>
          <PerformanceOverview />
        </TestWrapper>
      );

      expect(container).toBeTruthy();
    });
  });
});
