'use client';
import {
  Settings as AdminIcon,
  BarChart3,
  Briefcase,
  ClipboardList,
  UsersRound as EmployeesIcon,
  History,
  LayoutDashboard,
  Monitor,
  Car as MyVehiclesIcon,
  Type,
  Building2 as WorkHubLogoIcon,
} from 'lucide-react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

import { ThemeToggle } from '@/components/theme-toggle';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { UserProfile } from '@/components/user/UserProfile'; // Import UserProfile
import { useModal } from '@/lib/hooks/useModal';
import { useUiPreferences } from '@/hooks/ui/useUiPreferences';
import { cn } from '@/lib/utils';

export default function Navbar() {
  const pathname = usePathname();
  const { fontSize, setFontSize } = useUiPreferences();
  const { openSettingsModal } = useModal();

  const navItems = [
    { href: '/', icon: LayoutDashboard, label: 'Dashboard' },
    { href: '/vehicles', icon: MyVehiclesIcon, label: 'Assets' },
    { href: '/service-history', icon: History, label: 'Maintenance' },
    { href: '/delegations', icon: Briefcase, label: 'Projects' },
    { href: '/tasks', icon: ClipboardList, label: 'Tasks' },
    { href: '/employees', icon: EmployeesIcon, label: 'Team' },
    {
      href: '/reports',
      icon: BarChart3,
      label: 'Analytics',
      isHighlighted: true,
    },
    { href: '/reliability', icon: Monitor, label: 'Reliability' },
    { href: '/admin', icon: AdminIcon, label: 'Admin' },
  ];

  return (
    <header className="no-print border-b border-border bg-card text-card-foreground shadow-md">
      <nav className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex h-16 items-center justify-between">
          <div className="flex items-center">
            <Link
              className="flex items-center space-x-2 text-xl font-semibold transition-opacity hover:opacity-80"
              href="/"
            >
              <WorkHubLogoIcon className="size-7 text-primary" />
              <span>WorkHub</span>
            </Link>
          </div>
          <div className="flex items-center space-x-1 md:space-x-2">
            {navItems.map(item => (
              <Button
                asChild
                className={cn(
                  'hover:bg-accent hover:text-accent-foreground px-2 md:px-3',
                  pathname === item.href ||
                    (item.href !== '/' && pathname?.startsWith(item.href))
                    ? 'bg-accent text-accent-foreground'
                    : 'text-foreground',
                  item.isHighlighted &&
                    'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0'
                )}
                key={item.href}
                variant={item.isHighlighted ? 'default' : 'ghost'}
              >
                <Link className="flex items-center" href={item.href}>
                  <item.icon className="mr-0 size-4 md:mr-2" />
                  <span className="hidden md:inline">{item.label}</span>
                  {item.isHighlighted && (
                    <span className="ml-1 text-xs">📊</span>
                  )}
                </Link>
              </Button>
            ))}
            {/* Font Size Quick Controls */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  aria-label={`Current font size: ${fontSize}. Click to change font size`}
                  className="text-foreground hover:bg-accent hover:text-accent-foreground"
                  size="icon"
                  variant="ghost"
                >
                  <Type className="size-[1.2rem]" />
                  <span className="sr-only">Change font size</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="min-w-48">
                <DropdownMenuLabel>Font Size</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <div className="px-2 py-1.5">
                  <div className="mb-2 flex items-center justify-between">
                    <span className="text-sm font-medium">Current</span>
                    <Badge className="text-xs capitalize" variant="secondary">
                      {fontSize}
                    </Badge>
                  </div>
                  <div className="flex gap-1">
                    {(['small', 'medium', 'large'] as const).map(size => (
                      <Button
                        className="flex-1 text-xs capitalize"
                        key={size}
                        onClick={() => setFontSize(size)}
                        size="sm"
                        variant={fontSize === size ? 'default' : 'outline'}
                      >
                        {size}
                      </Button>
                    ))}
                  </div>
                </div>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={openSettingsModal}>
                  <AdminIcon className="mr-2 size-4" />
                  All Settings
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
            <ThemeToggle />
            <UserProfile variant="dropdown" /> {/* Add UserProfile dropdown */}
          </div>
        </div>
      </nav>
    </header>
  );
}
