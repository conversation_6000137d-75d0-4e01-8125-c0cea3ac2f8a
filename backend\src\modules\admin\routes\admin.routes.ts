/**
 * Consolidated Admin Routes
 *
 * This module provides unified routing for all admin operations,
 * consolidating previously scattered admin endpoints into a
 * cohesive admin API structure.
 *
 * Features:
 * - Centralized admin routing
 * - Consistent middleware application
 * - Request deduplication for admin operations
 * - Circuit breaker protection
 * - Comprehensive input validation
 * - Role-based access control
 */

import { Router } from 'express';

import { circuitBreakerResetMiddleware } from '../../../middleware/circuitBreaker.js';
import inputValidationModule from '../../../middleware/inputValidation.js';
import requestDeduplicationModule from '../../../middleware/requestDeduplication.js';
import { enhancedAuthenticateUser } from '../../../middleware/jwtAuth.middleware.js';
import { requireRole } from '../../../middleware/supabaseAuth.js';
import { validate } from '../../../middleware/validation.js';
import logger from '../../../utils/logger.js';
import adminController from '../controllers/admin.controller.js';
import HttpError from '../../../utils/HttpError.js';

// Extract the specific middleware functions we need
const { adminDeduplication } = requestDeduplicationModule;
const inputValidation = inputValidationModule;

const router = Router();

// Apply common middleware to all admin routes
router.use(enhancedAuthenticateUser);
router.use(requireRole(['ADMIN', 'SUPER_ADMIN']));

// Log all admin route access
router.use((req, res, next) => {
  logger.info('Admin route accessed', {
    ip: req.ip,
    method: req.method,
    path: req.path,
    service: 'admin-routes',
    userId: (req as any).user?.id,
  });
  next();
});

/**
 * Admin Dashboard Routes
 */

// GET /api/admin/dashboard - Get comprehensive dashboard data
router.get('/dashboard', adminDeduplication, adminController.getDashboard);

/**
 * System Health & Monitoring Routes
 */

// GET /api/admin/health - Get system health status
router.get('/health', adminDeduplication, adminController.getHealthStatus);

// GET /api/admin/performance - Get performance metrics
router.get('/performance', adminDeduplication, adminController.getPerformanceMetrics);

// GET /api/admin/statistics - Get system statistics
router.get('/statistics', adminDeduplication, adminController.getSystemStatistics);

/**
 * Error Logs Routes
 */

// GET /api/admin/logs/errors - Get error logs with filtering
router.get('/logs/errors', adminDeduplication, adminController.getErrorLogs);

/**
 * User Management Routes
 */

// GET /api/admin/users - Get all users with filtering and pagination
router.get('/users', adminDeduplication, adminController.getUsers);

// POST /api/admin/users - Create new user
router.post('/users', adminDeduplication, adminController.createUser);

// PUT /api/admin/users/:id - Update user
router.put('/users/:id', adminDeduplication, adminController.updateUser);

// DELETE /api/admin/users/:id - Delete user (Super Admin only)
router.delete(
  '/users/:id',
  requireRole(['SUPER_ADMIN']),
  adminDeduplication,
  adminController.deleteUser,
);

// PATCH /api/admin/users/:id/toggle-activation - Toggle user activation
router.patch(
  '/users/:id/toggle-activation',
  adminDeduplication,
  adminController.toggleUserActivation,
);

/**
 * Audit Logs Routes
 */

// GET /api/admin/audit-logs - Get audit logs with filtering
router.get('/audit-logs', adminDeduplication, adminController.getAuditLogs);

/**
 * Legacy Route Compatibility
 *
 * These routes maintain compatibility with existing admin endpoints
 * while redirecting to the new consolidated structure.
 */

// Legacy health check route
router.get('/health-status', (req, res) => {
  logger.info('Legacy admin health route accessed, redirecting', {
    originalPath: req.path,
    service: 'admin-routes',
    userId: (req as any).user?.id,
  });
  res.redirect(301, '/api/admin/health');
});

// Legacy performance route
router.get('/performance-metrics', (req, res) => {
  logger.info('Legacy admin performance route accessed, redirecting', {
    originalPath: req.path,
    service: 'admin-routes',
    userId: (req as any).user?.id,
  });
  res.redirect(301, '/api/admin/performance');
});

// Legacy error logs route
router.get('/error-logs', (req, res) => {
  logger.info('Legacy admin error logs route accessed, redirecting', {
    originalPath: req.path,
    service: 'admin-routes',
    userId: (req as any).user?.id,
  });
  res.redirect(301, '/api/admin/logs/errors');
});

/**
 * Error handling middleware for admin routes
 */
router.use((error: any, req: any, res: any, next: any) => {
  logger.error('Admin route error', {
    error: error.message,
    method: req.method,
    path: req.path,
    service: 'admin-routes',
    stack: error.stack,
    userId: req.user?.id,
  });

  res.status(error.statusCode || 500).json({
    code: error.code || 'ADMIN_ERROR',
    message: error.message || 'Internal server error',
    status: 'error',
    timestamp: new Date().toISOString(),
  });
});

/**
 * 404 handler for admin routes
 */
router.use((req, res, next) => {
  logger.warn('Admin route not found', {
    method: req.method,
    path: req.path,
    service: 'admin-routes',
    userId: (req as any).user?.id,
  });

  const error = new HttpError('Admin endpoint not found', 404, 'ADMIN_ENDPOINT_NOT_FOUND');
  next(error);
});

export default router;
