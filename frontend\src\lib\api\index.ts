/**
 * @file Centralized exports for the API service layer.
 * @module api
 */

// Core infrastructure
export * from './core/apiClient';
export * from './core/baseApiService';
export * from './core/errors';
export * from './core/interfaces';
export * from './core/types';

import { getEnvironmentConfig } from '../config/environment';
// Create and export a configured API client instance
import { ApiClient } from './core/apiClient';

/**
 * Secure Authentication Token Provider
 * Single source of truth for authentication tokens across the entire application
 * Consistent naming convention: "Secure" prefix for all authentication functions
 */
let secureAuthTokenProvider: (() => null | string) | null = null;

/**
 * Legacy compatibility - maintains backward compatibility
 * @deprecated Use getSecureAuthTokenProvider instead
 */
export function getGlobalAuthTokenProvider(): (() => null | string) | null {
  console.warn(
    '⚠️ getGlobalAuthTokenProvider is deprecated. Use getSecureAuthTokenProvider instead.'
  );
  return getSecureAuthTokenProvider();
}

/**
 * Get the secure token provider function (for debugging/testing)
 */
export function getSecureAuthTokenProvider(): (() => null | string) | null {
  return secureAuthTokenProvider;
}

/**
 * Legacy compatibility - maintains backward compatibility
 * @deprecated Use getSecureAuthTokenProvider instead
 */
export function getUnifiedAuthTokenProvider(): (() => null | string) | null {
  console.warn(
    '⚠️ getUnifiedAuthTokenProvider is deprecated. Use getSecureAuthTokenProvider instead.'
  );
  return getSecureAuthTokenProvider();
}

/**
 * Legacy compatibility - maintains backward compatibility
 * @deprecated Use setSecureAuthTokenProvider instead
 */
export function setGlobalAuthTokenProvider(
  provider: () => null | string
): void {
  console.warn(
    '⚠️ setGlobalAuthTokenProvider is deprecated. Use setSecureAuthTokenProvider instead.'
  );
  setSecureAuthTokenProvider(provider);
}

/**
 * Set the secure authentication token provider
 * This should ONLY be called by the AuthContext
 * Replaces legacy setGlobalAuthTokenProvider and setFactoryAuthTokenProvider
 */
export function setSecureAuthTokenProvider(
  provider: () => null | string
): void {
  secureAuthTokenProvider = provider;

  if (process.env.NODE_ENV === 'development') {
    console.log('🔐 Secure Auth Token Provider initialized');
  }
}

/**
 * Legacy compatibility - maintains backward compatibility
 * @deprecated Use setSecureAuthTokenProvider instead
 */
export function setUnifiedAuthTokenProvider(
  provider: () => null | string
): void {
  console.warn(
    '⚠️ setUnifiedAuthTokenProvider is deprecated. Use setSecureAuthTokenProvider instead.'
  );
  setSecureAuthTokenProvider(provider);
}

/**
 * Get the current authentication token from the secure provider
 * This is used by ALL API clients throughout the application
 */
function getSecureAuthToken(): null | string {
  if (!secureAuthTokenProvider) {
    if (process.env.NODE_ENV === 'development') {
      console.warn('⚠️ Secure Auth Token Provider not initialized');
    }
    return null;
  }

  try {
    return secureAuthTokenProvider();
  } catch (error) {
    console.error('❌ Error getting auth token from secure provider:', error);
    return null;
  }
}

// Get environment-aware configuration
const envConfig = getEnvironmentConfig();

export const apiClient = new ApiClient({
  baseURL: envConfig.apiBaseUrl, // Use environment-aware configuration
  getAuthToken: getSecureAuthToken, // Use consistent secure naming
  headers: {
    'Content-Type': 'application/json',
  },
  retryAttempts: 3,
  timeout: 10_000,
});

// Security architecture (selective exports to avoid conflicts)
export {
  createSecureApiClient,
  createSecurityComposer,
  // Secure API client
  SecureApiClient,
  // Security composer
  SecurityComposer,
  // Security providers
  SecurityConfigProvider,
  useCSRFProtection,
  useInputValidation,
  // Security hooks
  useSecureApiClient,
  useSecureApiReplacement,
  useSecureHttpClient,
  useSecurityConfig,
  useSecurityConfigValue,
  useSecurityMonitoring,
  useSessionSecurity,
  useTokenManagement,
} from './security';

export type {
  SecureApiClientConfig,
  SecureApiRequestConfig,
  SecurityFeatures,
  // Security types (avoid RequestConfig conflict)
  UseSecureApiClientReturn,
  UseSecureHttpClientReturn,
} from './security';

// Domain-specific API services
export * from './services/domain/delegationApi';
export * from './services/domain/employeeApi';
export * from './services/domain/taskApi';
export * from './services/domain/vehicleApi';

// External API services
export * from './services/external/flightApi';
export * from './services/external/flightDetailsApi';
