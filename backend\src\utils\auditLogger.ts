import type { Request } from 'express'; // For capturing IP, user-agent etc.

import crypto from 'crypto'; // Added for cryptographic operations
import fs from 'fs'; // Added for file system operations
import path from 'path'; // Added for path resolution
import winston from 'winston';

// Define the structure of an audit event with GDPR compliance
export interface AuditEvent {
  action: string; // e.g., 'LOGIN_SUCCESS', 'ROLE_CHECK_FAILURE', 'SESSION_INVALIDATE_REQUEST'
  alertTriggered?: boolean; // Whether this event triggered an alert
  // Enhanced Phase 3 fields
  correlationId?: string; // Request correlation ID for tracing
  // GDPR Compliance fields
  dataClassification?: 'CONFIDENTIAL' | 'INTERNAL' | 'PUBLIC' | 'RESTRICTED';
  dataSubjectId?: string; // ID of the data subject (for GDPR)
  details?: Record<string, any>; // Any additional context-specific data
  employeeId?: string; // Consistent with other middleware, string is safer from JWT
  errorCode?: string; // Internal error code, e.g., 'INVALID_TOKEN'
  eventType:
    | 'AUTH'
    | 'AUTHZ'
    | 'COMPLIANCE'
    | 'DATA_ACCESS'
    | 'DATA_PROTECTION'
    | 'SECURITY'
    | 'SESSION'
    | 'SYSTEM_EVENT'
    | 'WEBSOCKET_SECURITY';
  gdprLawfulBasis?:
    | 'consent'
    | 'contract'
    | 'legal_obligation'
    | 'legitimate_interests'
    | 'public_task'
    | 'vital_interests';
  immutableHash?: string; // Cryptographic hash for audit trail integrity
  impersonatorUserId?: string; // For admin actions on behalf of users
  ipAddress?: string;
  message: string; // Human-readable description of the event
  outcome: 'FAILURE' | 'INFO' | 'PENDING' | 'SUCCESS' | 'WARNING';
  personalDataInvolved?: boolean; // Whether personal data was accessed/modified

  requestId?: string; // Unique request identifier
  retentionPeriod?: number; // Days to retain this audit log
  // Security enhancement fields
  riskLevel?: 'CRITICAL' | 'HIGH' | 'LOW' | 'MEDIUM';

  sessionId?: string; // Session identifier
  statusCode?: number; // HTTP status code if applicable
  targetResource?: string; // e.g., 'vehicle:123', 'user_profile:abc'
  targetUserId?: string; // If the action targets another user
  timestamp: string;

  userAgent?: string;
  userId?: string;
  userRole?: string;
}

// Configure the audit logger
const auditLogger = winston.createLogger({
  exitOnError: false, // Do not exit on logger errors
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json(), // Log in JSON format for easier parsing
  ),
  level: 'info', // Log all audit events
  transports: [
    // In a production environment, this would likely write to:
    // - A dedicated audit log file with rotation (e.g., new winston.transports.File({ filename: 'audit.log' }))
    // - A centralized logging service (e.g., ELK, Splunk, Supabase table via a custom transport)
    // For now, logging to console for simplicity during development, but structured.
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.printf(info => {
          // Check if info.message is an object (from direct JSON logging) or string
          let logMessage = info.message;
          if (typeof info.message === 'object') {
            // If the primary message is an object, it's likely from a direct JSON log
            // and we want to stringify its 'eventDetails' if that's where our data is.
            // This part might need adjustment based on how winston internally handles things
            // when json() formatter is also used globally.
            logMessage = JSON.stringify((info.message as any).eventDetails || info.message);
          } else if (info.eventDetails) {
            // If info.message is a string and eventDetails exists, combine them or just use eventDetails
            logMessage = `Message: "${info.message}", Details: ${JSON.stringify(info.eventDetails)}`;
          }
          return `[AUDIT_LOG] ${info.timestamp} ${info.level}: ${logMessage}`;
        }),
      ),
    }),
    // File transport for production-grade audit logging
    new winston.transports.File({
      filename: path.join(process.cwd(), 'logs', 'audit.log'), // Use path.join for cross-platform compatibility
      level: 'info',
      maxFiles: 5,
      maxsize: 5242880, // 5MB
      tailable: true, // Keep the latest logs
      zippedArchive: true, // Compress rotated files
    }),
  ],
});

// Ensure the logs directory exists
const logsDir = path.join(process.cwd(), 'logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

/**
 * Generates a correlation ID for request tracing
 */
export function generateCorrelationId(): string {
  return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Generates an immutable hash for audit trail integrity
 */
export function generateImmutableHash(event: Partial<AuditEvent>): string {
  const eventString = JSON.stringify({
    action: event.action,
    eventType: event.eventType,
    message: event.message,
    outcome: event.outcome,
    timestamp: event.timestamp,
    userId: event.userId,
  });
  return crypto.createHash('sha256').update(eventString).digest('hex');
}

/**
 * Logs an audit event.
 *
 * @param event Partial<AuditEvent> - The core event details. Common fields like timestamp
 *                                  and ipAddress can be auto-populated if a request object is provided.
 * @param req Optional Express Request object to enrich log with IP, User-Agent etc.
 */
export function logAuditEvent(
  event: Omit<AuditEvent, 'ipAddress' | 'timestamp' | 'userAgent'> &
    Partial<Pick<AuditEvent, 'ipAddress' | 'userAgent'>>,
  req?: Request,
): void {
  const fullEvent: AuditEvent = {
    ipAddress: req?.ip || event.ipAddress,
    timestamp: new Date().toISOString(),
    userAgent: req?.get('User-Agent') || event.userAgent,
    ...event,
  };

  // Pass the message and the full event as metadata. The printf function will access eventDetails from the metadata.
  auditLogger.info(fullEvent.message, { eventDetails: fullEvent });
}

/**
 * Enhanced GDPR-compliant audit event logging
 */
export async function logGDPRAuditEvent(
  event: Omit<
    AuditEvent,
    'correlationId' | 'immutableHash' | 'ipAddress' | 'timestamp' | 'userAgent'
  > &
    Partial<Pick<AuditEvent, 'correlationId' | 'ipAddress' | 'userAgent'>>,
  req?: Request,
): Promise<void> {
  const correlationId =
    event.correlationId || (req?.headers['x-correlation-id'] as string) || generateCorrelationId();

  const fullEvent: AuditEvent = {
    alertTriggered: event.alertTriggered || false,
    correlationId,
    // Set default GDPR compliance values
    dataClassification: event.dataClassification || 'INTERNAL',
    gdprLawfulBasis: event.gdprLawfulBasis || 'legitimate_interests',
    ipAddress: req?.ip || event.ipAddress,
    personalDataInvolved: event.personalDataInvolved || false,

    requestId: req?.headers['x-request-id'] as string,
    retentionPeriod: event.retentionPeriod || 2555, // 7 years default
    riskLevel: event.riskLevel || 'LOW',
    sessionId: req?.headers['x-session-id'] as string,
    timestamp: new Date().toISOString(),
    userAgent: req?.get('User-Agent') || event.userAgent,

    ...event,
  };

  // Generate immutable hash for audit trail integrity
  fullEvent.immutableHash = generateImmutableHash(fullEvent);

  auditLogger.info(fullEvent.message, { eventDetails: fullEvent });

  // Process event for automated alerting (Phase 3)
  try {
    // Import alerting service dynamically to avoid circular dependencies
    const { processAuditEventForAlerting } = await import('../services/alerting.service.js');
    await processAuditEventForAlerting(fullEvent);
  } catch (error: any) {
    // Don't fail audit logging if alerting fails
    auditLogger.error('Failed to process audit event for alerting:', error);
  }
}

// --- Example Usage (to be removed or moved to tests) ---
// logAuditEvent({
//   eventType: 'AUTH',
//   action: 'LOGIN_ATTEMPT',
//   userId: 'test-user-id',
//   outcome: 'FAILURE',
//   errorCode: 'INVALID_CREDENTIALS',
//   message: 'User login failed due to invalid credentials.',
//   details: { username: 'testuser' },
// }, { ip: '*************', get: () => 'TestAgent/1.0' } as any as Request);

// logAuditEvent({
//   eventType: 'SECURITY',
//   action: 'PASSWORD_RESET_REQUEST',
//   userId: 'another-user',
//   outcome: 'SUCCESS',
//   message: 'Password reset initiated for user.',
//   details: { email: '<EMAIL>' },
// });

export default auditLogger; // Export the logger instance if direct use is needed, though logAuditEvent is preferred.
