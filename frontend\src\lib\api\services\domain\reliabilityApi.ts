import type {
  AlertApiResponse,
  AlertHistoryApiResponse,
  AlertStatisticsApiResponse,
  CircuitBreakerStatusApiResponse,
  DeduplicationMetricsApiResponse,
  DependencyHealthApiResponse,
  DetailedHealthApiResponse,
  HealthCheckApiResponse,
  MetricsApiResponse,
  TestAlertsApiResponse,
} from '../../../types/api';
import type {
  Alert,
  AlertHistory,
  AlertStatistics,
  CircuitBreakerStatus,
  DeduplicationMetrics,
  DependencyHealth,
  DetailedHealthCheck,
  HealthCheck,
  SystemMetrics,
  TestAlertsResult,
} from '../../../types/domain';
import type { ApiClient } from '../../core/apiClient';

import logger from '../../../utils/logger';
import {
  BaseApiService,
  type DataTransformer,
  type ServiceConfig,
} from '../../core/baseApiService';

const ReliabilityTransformer: DataTransformer<any> = {
  fromApi: (data: any) => data,
  toApi: (data: any) => data,
};

export class ReliabilityApiService extends BaseApiService<any, any, any> {
  protected endpoint = '/reliability';
  protected transformer: DataTransformer<any> = ReliabilityTransformer;

  constructor(apiClient: ApiClient, config?: ServiceConfig) {
    super(apiClient, {
      cacheDuration: 1 * 60 * 1000, // 1 minute for reliability data
      circuitBreakerThreshold: 5,
      enableMetrics: true,
      retryAttempts: 3,
      ...config,
    });
  }

  async acknowledgeAlert(
    alertId: string,
    note?: string,
    acknowledgedBy?: string
  ): Promise<Alert> {
    return this.executeWithInfrastructure(null, async () => {
      const response = await this.apiClient.post<AlertApiResponse>(
        `/alerts/${alertId}/acknowledge`,
        {
          acknowledgedBy,
          note,
        }
      );

      this.cache.invalidatePattern(new RegExp('^alerts:'));

      return response;
    });
  }

  async getActiveAlerts(): Promise<Alert[]> {
    return this.executeWithInfrastructure('alerts:active', async () => {
      try {
        const apiResponse = await this.apiClient.get<any>('/alerts');

        return apiResponse?.alerts || [];
      } catch (error) {
        console.error('Failed to get active alerts:', error);
        return [];
      }
    });
  }

  async getAlertHistory(page = 1, limit = 50): Promise<AlertHistory> {
    return this.executeWithInfrastructure(
      `alerts:history:${page}:${limit}`,
      async () => {
        const queryParams = new URLSearchParams({
          limit: limit.toString(),
          page: page.toString(),
        });
        const response = await this.apiClient.get<AlertHistoryApiResponse>(
          `/alerts/history?${queryParams.toString()}`
        );
        return response;
      }
    );
  }

  async getAlertStatistics(): Promise<AlertStatistics> {
    return this.executeWithInfrastructure('alerts:statistics', async () => {
      try {
        const response =
          await this.apiClient.get<AlertStatisticsApiResponse>(
            '/alerts/statistics'
          );
        return response;
      } catch (error) {
        console.error('Failed to get alert statistics:', error);
        return {
          acknowledged: 0,
          active: 0,
          averageResolutionTime: 0,
          bySeverity: { critical: 0, high: 0, low: 0, medium: 0 },
          recentTrends: { last7Days: 0, last24Hours: 0, last30Days: 0 },
          resolved: 0,
          total: 0,
        };
      }
    });
  }

  async getCircuitBreakerHistory(
    timeframe: '1h' | '6h' | '7d' | '24h' = '24h',
    breakerName?: string
  ): Promise<any> {
    return this.executeWithInfrastructure(
      `circuit-breakers:history:${timeframe}:${breakerName || 'all'}`,
      async () => {
        const params = new URLSearchParams({ timeframe });
        if (breakerName) {
          params.append('breakerName', breakerName);
        }
        const response = await this.apiClient.get<any>(
          `/monitoring/circuit-breakers/history?${params.toString()}`
        );
        return response;
      }
    );
  }

  async getCircuitBreakerStatus(): Promise<CircuitBreakerStatus> {
    return this.executeWithInfrastructure(
      'monitoring:circuit-breakers',
      async () => {
        try {
          const apiResponse =
            await this.apiClient.get<any>('/circuit-breakers');

          const circuitBreakers = apiResponse?.circuitBreakers || [];

          return {
            circuitBreakers: circuitBreakers || [],
            summary: {
              closed:
                circuitBreakers?.filter((cb: any) => cb.state === 'CLOSED')
                  .length || 0,
              halfOpen:
                circuitBreakers?.filter((cb: any) => cb.state === 'HALF_OPEN')
                  .length || 0,
              open:
                circuitBreakers?.filter((cb: any) => cb.state === 'OPEN')
                  .length || 0,
              total: circuitBreakers?.length || 0,
            },
          };
        } catch (error) {
          console.error('Failed to get circuit breaker status:', error);
          return {
            circuitBreakers: [],
            summary: { closed: 0, halfOpen: 0, open: 0, total: 0 },
          };
        }
      }
    );
  }

  async getCriticalAlertCount(): Promise<number> {
    try {
      const statistics = await this.getAlertStatistics();
      return statistics.bySeverity.critical;
    } catch {
      return 0;
    }
  }

  async getDeduplicationMetrics(): Promise<DeduplicationMetrics> {
    return this.executeWithInfrastructure(
      'monitoring:deduplication',
      async () => {
        const response =
          await this.apiClient.get<DeduplicationMetricsApiResponse>(
            '/monitoring/deduplication'
          );
        return response as any;
      }
    );
  }

  async getDependencyHealth(): Promise<DependencyHealth> {
    return this.executeWithInfrastructure('health:dependencies', async () => {
      const response = await this.apiClient.get<DependencyHealthApiResponse>(
        '/health/dependencies'
      );
      return response as any;
    });
  }

  async getDetailedHealth(): Promise<DetailedHealthCheck> {
    return this.executeWithInfrastructure('health:detailed', async () => {
      const response =
        await this.apiClient.get<DetailedHealthApiResponse>('/health/detailed');
      return response as any;
    });
  }

  async getHealthTrends(
    timeframe: '1h' | '6h' | '7d' | '24h' = '24h'
  ): Promise<any> {
    return this.executeWithInfrastructure(
      `health:trends:${timeframe}`,
      async () => {
        const response = await this.apiClient.get<any>(
          `/health/trends?timeframe=${timeframe}`
        );
        return response;
      }
    );
  }

  async getHttpRequestMetrics(): Promise<any> {
    return this.executeWithInfrastructure('http:metrics', async () => {
      const response = await this.apiClient.get<any>('/http-request-metrics');
      return response;
    });
  }

  async getMetrics(): Promise<SystemMetrics> {
    return this.executeWithInfrastructure('metrics:system', async () => {
      const response = await this.apiClient.get<MetricsApiResponse>(
        '/metrics',
        {
          headers: { Accept: 'application/json' },
        }
      );
      return response as any;
    });
  }

  async getReliabilityDashboardData(): Promise<{
    activeAlerts: Alert[];
    alertStatistics: AlertStatistics;
    circuitBreakers: CircuitBreakerStatus;
    detailedHealth: DetailedHealthCheck;
    metrics: SystemMetrics;
    systemHealth: HealthCheck;
  }> {
    const [
      systemHealth,
      detailedHealth,
      circuitBreakers,
      metrics,
      activeAlerts,
      alertStatistics,
    ] = await Promise.all([
      this.getSystemHealth(),
      this.getDetailedHealth(),
      this.getCircuitBreakerStatus(),
      this.getMetrics(),
      this.getActiveAlerts(),
      this.getAlertStatistics(),
    ]);

    return {
      activeAlerts,
      alertStatistics,
      circuitBreakers,
      detailedHealth,
      metrics,
      systemHealth,
    };
  }

  async getSystemHealth(): Promise<HealthCheck> {
    return this.executeWithInfrastructure('health:system', async () => {
      const response =
        await this.apiClient.get<HealthCheckApiResponse>('/health');
      return response as any;
    });
  }

  async isSystemHealthy(): Promise<boolean> {
    try {
      const health = await this.getSystemHealth();
      return health.status === 'healthy';
    } catch {
      return false;
    }
  }

  async resolveAlert(
    alertId: string,
    reason?: string,
    resolvedBy?: string
  ): Promise<Alert> {
    return this.executeWithInfrastructure(null, async () => {
      const response = await this.apiClient.post<AlertApiResponse>(
        `/alerts/${alertId}/resolve`,
        {
          reason,
          resolvedBy,
        }
      );

      this.cache.invalidatePattern(new RegExp('^alerts:'));

      return response;
    });
  }

  async testAlerts(): Promise<TestAlertsResult> {
    return this.executeWithInfrastructure(null, async () => {
      const response = await this.apiClient.post<any>('/alerts/test');
      return {
        message: response?.message || 'Test alert triggered',
        success: response?.status === 'success',
        testAlertId: response?.data?.id,
      };
    });
  }
}
